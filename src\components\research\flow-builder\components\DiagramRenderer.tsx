/**
 * Modern Diagram Renderer Component
 * Beautiful, full-page rendering experience for Mermaid diagrams
 */

import React, { useEffect, useRef, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Download,
  Maximize2,
  Copy,
  RefreshCw,
  AlertTriangle,
  Loader2,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Fullscreen,
  Minimize2,
  Edit3
} from "lucide-react";
import { DiagramRendererProps } from '../types';
import { loadMermaid, initializeMermaid, getMermaidInstance } from '../utils/mermaid-loader';

export function DiagramRenderer({
  diagram,
  config,
  onError,
  onFixErrors,
  className = ""
}: DiagramRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [svgContent, setSvgContent] = useState<string>('');
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const [mermaidLoaded, setMermaidLoaded] = useState(false);

  useEffect(() => {
    loadMermaidLib();
  }, []);

  useEffect(() => {
    if (mermaidLoaded && containerRef.current && diagram.mermaidCode) {
      // Add a small delay to ensure DOM is ready
      const timeoutId = setTimeout(() => {
        renderDiagram();
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [diagram.mermaidCode, diagram.id, config, mermaidLoaded]);

  // Keyboard shortcuts for zoom
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '=':
          case '+':
            e.preventDefault();
            handleZoomIn();
            break;
          case '-':
            e.preventDefault();
            handleZoomOut();
            break;
          case '0':
            e.preventDefault();
            handleResetZoom();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [zoom]);

  const loadMermaidLib = async () => {
    try {
      console.log('🔄 DiagramRenderer: Loading Mermaid library...');

      // Use the mermaid loader utility
      await loadMermaid();
      initializeMermaid(config);

      console.log('✅ DiagramRenderer: Mermaid loaded and initialized successfully');
      setMermaidLoaded(true);
      setIsLoading(false);
    } catch (error: any) {
      console.error('❌ DiagramRenderer: Failed to load Mermaid:', error);
      const errorMessage = `Failed to load Mermaid library: ${error.message}`;
      setError(errorMessage);
      setIsLoading(false);
      setMermaidLoaded(false);
      if (onError) {
        onError(errorMessage);
      }
    }
  };

  const renderDiagram = async () => {
    const container = containerRef.current;
    const mermaid = getMermaidInstance();

    console.log('🎨 DiagramRenderer: Starting render', {
      hasMermaid: !!mermaid,
      hasContainer: !!container,
      hasCode: !!diagram.mermaidCode,
      diagramId: diagram.id,
      codeLength: diagram.mermaidCode?.length
    });

    if (!mermaid || !container || !diagram.mermaidCode) {
      console.warn('⚠️ DiagramRenderer: Missing dependencies for rendering');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Clear previous content and any existing mermaid elements
      container.innerHTML = '';

      // Remove any existing mermaid elements with the same ID to prevent conflicts
      const existingElements = document.querySelectorAll(`[id^="mermaid-${diagram.id}"]`);
      existingElements.forEach(el => el.remove());

      // Generate unique ID for this diagram
      const diagramId = `mermaid-${diagram.id}-${Date.now()}`;

      console.log('🔍 DiagramRenderer: Validating Mermaid code', {
        diagramId,
        codePreview: diagram.mermaidCode.substring(0, 100) + '...'
      });

      // Validate the diagram syntax (Mermaid v10.x uses different API)
      try {
        // In v10.x, parse returns a boolean or throws
        const parseResult = await mermaid.parse(diagram.mermaidCode);
        console.log('✅ DiagramRenderer: Validation successful', { parseResult });
      } catch (parseError: any) {
        console.error('❌ DiagramRenderer: Parse error:', parseError);
        throw new Error(`Invalid Mermaid syntax: ${parseError.message}`);
      }

      console.log('🎨 DiagramRenderer: Rendering diagram');
      // Render the diagram
      const { svg } = await mermaid.render(diagramId, diagram.mermaidCode);
      console.log('✅ DiagramRenderer: Render complete', { svgLength: svg.length });

      // Insert the SVG into the container
      container.innerHTML = svg;
      setSvgContent(svg);

      // Enhanced SVG styling for better responsiveness and compact display
      const svgElement = container.querySelector('svg');
      if (svgElement) {
        // Get original dimensions
        const originalWidth = svgElement.getAttribute('width') || svgElement.viewBox?.baseVal?.width || 800;
        const originalHeight = svgElement.getAttribute('height') || svgElement.viewBox?.baseVal?.height || 600;

        // Set responsive styling
        svgElement.style.maxWidth = '100%';
        svgElement.style.maxHeight = '100%';
        svgElement.style.height = 'auto';
        svgElement.style.width = 'auto';
        svgElement.style.transform = `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`;
        svgElement.style.transformOrigin = 'center center';
        svgElement.style.cursor = isPanning ? 'grabbing' : 'grab';
        svgElement.style.display = 'block';
        svgElement.style.margin = '0 auto';

        // Ensure proper viewBox for scaling
        if (!svgElement.getAttribute('viewBox') && originalWidth && originalHeight) {
          svgElement.setAttribute('viewBox', `0 0 ${originalWidth} ${originalHeight}`);
        }

        // Remove fixed dimensions to allow responsive scaling
        svgElement.removeAttribute('width');
        svgElement.removeAttribute('height');

        // Set preserveAspectRatio for better scaling
        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
      }

      console.log('✅ DiagramRenderer: Rendering successful');
      setIsLoading(false);
    } catch (error: any) {
      console.error('❌ DiagramRenderer: Render error:', error);
      console.error('❌ DiagramRenderer: Failed code:', diagram.mermaidCode);

      const errorMessage = error.message || 'Failed to render diagram';
      setError(errorMessage);
      setIsLoading(false);

      if (onError) {
        onError(errorMessage);
      }
    }
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(diagram.mermaidCode);
      toast.success('Diagram code copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  const handleDownloadSVG = () => {
    if (!svgContent) return;

    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${diagram.metadata.title.replace(/[^a-zA-Z0-9]/g, '_')}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('SVG downloaded successfully');
  };

  const updateTransform = (newZoom: number, newPan: { x: number; y: number }) => {
    const svgElement = containerRef.current?.querySelector('svg');
    if (svgElement) {
      svgElement.style.transform = `scale(${newZoom}) translate(${newPan.x}px, ${newPan.y}px)`;
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom * 1.2, 5);
    setZoom(newZoom);
    updateTransform(newZoom, pan);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom / 1.2, 0.1);
    setZoom(newZoom);
    updateTransform(newZoom, pan);
  };

  const handleResetZoom = () => {
    const newZoom = 1;
    const newPan = { x: 0, y: 0 };
    setZoom(newZoom);
    setPan(newPan);
    updateTransform(newZoom, newPan);
  };

  const handleFitToScreen = () => {
    const container = containerRef.current;
    const svgElement = container?.querySelector('svg');
    if (!container || !svgElement) return;

    const containerRect = container.getBoundingClientRect();
    const svgRect = svgElement.getBoundingClientRect();

    const scaleX = (containerRect.width - 32) / svgRect.width;
    const scaleY = (containerRect.height - 32) / svgRect.height;
    const newZoom = Math.min(scaleX, scaleY, 1);

    const newPan = { x: 0, y: 0 };
    setZoom(newZoom);
    setPan(newPan);
    updateTransform(newZoom, newPan);
  };

  // Pan functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // Left mouse button
      setIsPanning(true);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isPanning) {
      const deltaX = (e.clientX - lastPanPoint.x) / zoom;
      const deltaY = (e.clientY - lastPanPoint.y) / zoom;

      const newPan = {
        x: pan.x + deltaX,
        y: pan.y + deltaY
      };

      setPan(newPan);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
      updateTransform(zoom, newPan);
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  // Wheel zoom
  const handleWheel = (e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? 0.9 : 1.1;
      const newZoom = Math.min(Math.max(zoom * delta, 0.1), 5);
      setZoom(newZoom);
      updateTransform(newZoom, pan);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-96 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 rounded-2xl border border-gray-200/50 ${className}`}>
        <div className="text-center space-y-6 max-w-sm">
          {/* Enhanced Loading Animation */}
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
              <Loader2 className="h-10 w-10 animate-spin text-white" />
            </div>
            <div className="absolute inset-0 rounded-full bg-blue-400 opacity-75 animate-ping"></div>
          </div>

          {/* Loading Text */}
          <div className="space-y-2">
            <p className="text-lg font-semibold text-gray-900">Rendering your diagram...</p>
            <p className="text-sm text-gray-600">Processing Mermaid code and generating visualization</p>
          </div>

          {/* Progress Indicator */}
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 bg-gradient-to-br from-red-50 via-orange-50/50 to-red-50 border border-red-200/50 rounded-2xl shadow-sm ${className}`}>
        <Alert className="border-none bg-transparent">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 space-y-4">
              <div>
                <h3 className="font-semibold text-red-900 text-lg mb-1">Failed to render diagram</h3>
                <p className="text-sm text-red-700">There was an issue processing your diagram code</p>
              </div>

              <div className="flex flex-wrap gap-3">
                {onFixErrors && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onFixErrors(error)}
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 rounded-xl transition-all duration-200 hover:scale-105"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Fix with AI
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={renderDiagram}
                  className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50 rounded-xl transition-all duration-200 hover:scale-105"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Render
                </Button>
              </div>

              <div className="bg-red-100/50 border border-red-200/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="text-sm font-medium text-red-800 mb-2">Error Details:</h4>
                <pre className="text-xs text-red-700 whitespace-pre-wrap font-mono bg-red-50 p-3 rounded-lg border border-red-200/50 overflow-x-auto">
                  {error}
                </pre>
              </div>
            </div>
          </div>
        </Alert>
      </div>
    );
  }

  const containerClassName = isFullscreen
    ? "fixed inset-0 z-50 bg-white p-6"
    : className;

  return (
    <div className={containerClassName}>
      {/* Enhanced Modern Controls Bar */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-6 p-5 bg-gradient-to-r from-slate-50 via-blue-50/50 to-indigo-50/50 rounded-2xl border border-gray-200/50 shadow-sm backdrop-blur-sm">
        <div className="flex flex-wrap items-center gap-4">
          {/* Zoom Controls Group */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <ZoomIn className="h-4 w-4 text-blue-600" />
              Zoom:
            </span>
            <div className="flex items-center bg-white rounded-xl border border-gray-200/50 shadow-sm overflow-hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                title="Zoom Out (Ctrl + -)"
                className="hover:bg-blue-50 transition-colors rounded-none border-r border-gray-200/50"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <div className="px-4 py-2 text-sm font-bold text-gray-900 min-w-[60px] text-center bg-gray-50/50">
                {Math.round(zoom * 100)}%
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                title="Zoom In (Ctrl + +)"
                className="hover:bg-blue-50 transition-colors rounded-none border-l border-gray-200/50"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* View Controls Group */}
          <div className="flex items-center gap-2 bg-white rounded-xl border border-gray-200/50 shadow-sm p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetZoom}
              title="Reset Zoom (Ctrl + 0)"
              className="hover:bg-gray-50 transition-all duration-200 rounded-lg"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFitToScreen}
              title="Fit to Screen"
              className="hover:bg-gray-50 transition-all duration-200 rounded-lg"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
              className="hover:bg-gray-50 transition-all duration-200 rounded-lg"
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Fullscreen className="h-4 w-4" />}
            </Button>
          </div>

          {/* Help Tip */}
          <div className="hidden lg:flex items-center gap-2 text-xs text-gray-500 bg-white/80 px-3 py-2 rounded-xl border border-gray-200/50 backdrop-blur-sm">
            <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
            <span>Ctrl+Wheel to zoom • Drag to pan • Double-click to fit</span>
          </div>
        </div>

        {/* Action Buttons Group */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyCode}
            className="bg-white/80 hover:bg-white border-gray-300/50 hover:border-blue-400 transition-all duration-200 rounded-xl"
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy Code
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadSVG}
            className="bg-white/80 hover:bg-white border-gray-300/50 hover:border-green-400 transition-all duration-200 rounded-xl"
          >
            <Download className="h-4 w-4 mr-2" />
            Download SVG
          </Button>
        </div>
      </div>

      {/* Enhanced Modern Diagram Container */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 overflow-hidden shadow-lg relative">
        {/* Container Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-blue-50/30 border-b border-gray-200/50">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">Live Preview</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>Pan: {Math.round(pan.x)}, {Math.round(pan.y)}</span>
            <span>•</span>
            <span>Scale: {Math.round(zoom * 100)}%</span>
          </div>
        </div>

        {/* Enhanced Responsive Diagram Area */}
        <div
          ref={containerRef}
          className="w-full flex items-center justify-center p-4 overflow-hidden relative bg-gradient-to-br from-white via-slate-50/30 to-blue-50/20"
          style={{
            height: isFullscreen ? 'calc(100vh - 200px)' : 'clamp(400px, 60vh, 500px)',
            maxHeight: isFullscreen ? 'calc(100vh - 200px)' : '500px',
            cursor: isPanning ? 'grabbing' : 'grab',
            backgroundImage: `
              radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.1) 2px, transparent 0),
              radial-gradient(circle at 75px 75px, rgba(139, 92, 246, 0.1) 2px, transparent 0)
            `,
            backgroundSize: '100px 100px'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onWheel={handleWheel}
          onDoubleClick={handleFitToScreen}
        >
          {/* Zoom Indicator */}
          {zoom !== 1 && (
            <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm">
              {Math.round(zoom * 100)}%
            </div>
          )}

          {/* Pan Indicator */}
          {(pan.x !== 0 || pan.y !== 0) && (
            <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm">
              Offset: {Math.round(pan.x)}, {Math.round(pan.y)}
            </div>
          )}
        </div>

        {/* Quick Actions Overlay */}
        <div className="absolute bottom-4 right-4 flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetZoom}
            className="bg-white/90 backdrop-blur-sm border-gray-300/50 hover:border-blue-400 transition-all duration-200 rounded-xl shadow-lg"
            title="Reset View"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleFitToScreen}
            className="bg-white/90 backdrop-blur-sm border-gray-300/50 hover:border-blue-400 transition-all duration-200 rounded-xl shadow-lg"
            title="Fit to Screen"
          >
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
