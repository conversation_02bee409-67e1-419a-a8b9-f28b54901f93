import React, { useState, useEffect, useCallback } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  X, 
  Play, 
  Pause, 
  RotateCcw,
  Maximize,
  Minimize
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

import { Presentation } from '../types';
import { SlideRenderer } from './SlideRenderer';
import { usePresentationStore } from '../stores/presentation.store';

interface PresentationModeProps {
  presentation: Presentation;
  onExit: () => void;
}

export function PresentationMode({ presentation, onExit }: PresentationModeProps) {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [timeElapsed, setTimeElapsed] = useState(0);

  const { selectedTheme } = usePresentationStore();

  const currentSlide = presentation.slides[currentSlideIndex];
  const totalSlides = presentation.slides.length;
  const progress = ((currentSlideIndex + 1) / totalSlides) * 100;

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      if (currentSlideIndex < totalSlides - 1) {
        setCurrentSlideIndex(prev => prev + 1);
      } else {
        setIsAutoPlay(false);
      }
    }, 5000); // 5 seconds per slide

    return () => clearInterval(interval);
  }, [isAutoPlay, currentSlideIndex, totalSlides]);

  // Timer
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Keyboard navigation
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowRight':
      case ' ':
        event.preventDefault();
        nextSlide();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        previousSlide();
        break;
      case 'Escape':
        event.preventDefault();
        onExit();
        break;
      case 'f':
      case 'F':
        event.preventDefault();
        toggleFullscreen();
        break;
    }
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  const nextSlide = () => {
    if (currentSlideIndex < totalSlides - 1) {
      setCurrentSlideIndex(prev => prev + 1);
    }
  };

  const previousSlide = () => {
    if (currentSlideIndex > 0) {
      setCurrentSlideIndex(prev => prev - 1);
    }
  };

  const goToSlide = (index: number) => {
    setCurrentSlideIndex(index);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  const resetPresentation = () => {
    setCurrentSlideIndex(0);
    setTimeElapsed(0);
    setIsAutoPlay(false);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Header Controls */}
      <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-lg font-semibold">{presentation.metadata.title}</h1>
          <Badge variant="secondary">
            {currentSlideIndex + 1} / {totalSlides}
          </Badge>
          <span className="text-sm text-gray-300">
            {formatTime(timeElapsed)}
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleAutoPlay}
            className="text-white hover:bg-gray-700"
          >
            {isAutoPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={resetPresentation}
            className="text-white hover:bg-gray-700"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullscreen}
            className="text-white hover:bg-gray-700"
          >
            {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onExit}
            className="text-white hover:bg-gray-700"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <Progress value={progress} className="h-1" />

      {/* Main Slide Area */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gray-100">
        <div className="w-full max-w-6xl aspect-video bg-white rounded-lg shadow-2xl overflow-hidden">
          {currentSlide && (
            <SlideRenderer
              slide={currentSlide}
              theme={selectedTheme}
              className="h-full"
            />
          )}
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={previousSlide}
          disabled={currentSlideIndex === 0}
          className="text-white hover:bg-gray-700 disabled:opacity-50"
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>

        {/* Slide Thumbnails */}
        <div className="flex items-center gap-2 max-w-md overflow-x-auto">
          {presentation.slides.map((slide, index) => (
            <button
              key={slide.id}
              onClick={() => goToSlide(index)}
              className={`flex-shrink-0 w-12 h-8 rounded border-2 transition-all ${
                index === currentSlideIndex
                  ? 'border-blue-500 bg-blue-100'
                  : 'border-gray-600 bg-gray-700 hover:border-gray-400'
              }`}
            >
              <span className="text-xs">{index + 1}</span>
            </button>
          ))}
        </div>

        <Button
          variant="ghost"
          onClick={nextSlide}
          disabled={currentSlideIndex === totalSlides - 1}
          className="text-white hover:bg-gray-700 disabled:opacity-50"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-2" />
        </Button>
      </div>

      {/* Speaker Notes */}
      {currentSlide?.notes && (
        <div className="bg-gray-800 text-white p-4 max-h-32 overflow-y-auto">
          <h4 className="text-sm font-semibold mb-2">Speaker Notes:</h4>
          <p className="text-sm text-gray-300">{currentSlide.notes}</p>
        </div>
      )}
    </div>
  );
}
