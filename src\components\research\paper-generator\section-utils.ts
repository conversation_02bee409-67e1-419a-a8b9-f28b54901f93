import { GeneratedSection, Citation } from './types';

interface SeparatedContent {
  mainContent: string;
  extractedReferences: string[];
}

/**
 * Extracts references from section content and separates it into main content and references
 */
export function separateSectionReferences(content: string): SeparatedContent {
  // Default return values
  const result: SeparatedContent = {
    mainContent: content,
    extractedReferences: []
  };
  
  // Look for common reference section headers
  const referenceSectionPatterns = [
    /references\s*(:|\n)/i,
    /reference\s*list\s*(:|\n)/i,
    /bibliography\s*(:|\n)/i,
    /works\s*cited\s*(:|\n)/i
  ];
  
  // Try to find a references section
  let referencesStart = -1;
  
  for (const pattern of referenceSectionPatterns) {
    const match = content.match(pattern);
    if (match) {
      referencesStart = match.index!;
      break;
    }
  }
  
  // If we found a references section, separate it from main content
  if (referencesStart >= 0) {
    // The main content is everything before the references section
    result.mainContent = content.substring(0, referencesStart).trim();
    
    // Extract references text (everything after the reference header)
    const referencesText = content.substring(referencesStart);
    
    // Split into individual references
    // First, remove the "References" header
    const withoutHeader = referencesText.replace(/^(references|reference\s*list|bibliography|works\s*cited)\s*(:|\n)/i, '').trim();
    
    // Identify individual references using multiple strategies
    
    // Try numbered references pattern (e.g., "1. Author...")
    const numberedRefs = withoutHeader.match(/\d+\.\s+[A-Z][^0-9]+?(?=\n\d+\.|$)/gs);
    
    if (numberedRefs && numberedRefs.length > 0) {
      result.extractedReferences = numberedRefs.map(ref => ref.replace(/^\d+\.\s*/, '').trim());
    } else {
      // Try author-year pattern (e.g., "Author, A. (2023)...")
      const authorYearRefs = withoutHeader.split(/\n\s*\n/).filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 30 &&
               (trimmed.match(/[A-Z][a-z]+,\s+[A-Z]\.\s*\(\d{4}\)/) ||
                trimmed.match(/[A-Z][a-z]+\s+et\s+al\.\s*\(\d{4}\)/) ||
                trimmed.match(/[A-Z][a-z]+,\s+[A-Z]\.\s*[A-Z]\.\s*\(\d{4}\)/) ||
                trimmed.match(/[A-Z][a-z]+,\s+[A-Z]\.\s*,\s*&\s*[A-Z][a-z]+,\s*[A-Z]\.\s*\(\d{4}\)/)) &&
               !trimmed.match(/^(reference|bibliograph)/i);
      });

      if (authorYearRefs.length > 0) {
        result.extractedReferences = authorYearRefs.map(ref => ref.trim());
      } else {
        // Fallback: try simple line breaks if each line looks like a reference
        const lineRefs = withoutHeader.split(/\n/).filter(line => {
          const trimmed = line.trim();
          return trimmed.length > 30 &&
                 trimmed.match(/\(\d{4}\)/) &&
                 !trimmed.match(/^(reference|bibliograph)/i) &&
                 (trimmed.includes('.') || trimmed.includes(','));
        });

        result.extractedReferences = lineRefs.map(ref => ref.trim());
      }
    }
  }
  
  return result;
}

/**
 * Format section content to hide references in the main content view
 */
export function formatSectionContentForDisplay(section: GeneratedSection): {
  displayContent: string;
  hasHiddenReferences: boolean;
} {
  if (!section.content) {
    return { displayContent: '', hasHiddenReferences: false };
  }
  
  // Skip references section or sections without content
  if (section.id === 'references') {
    return { displayContent: section.content, hasHiddenReferences: false };
  }
  
  // Separate references from main content
  const { mainContent, extractedReferences } = separateSectionReferences(section.content);
  
  return {
    displayContent: mainContent,
    hasHiddenReferences: extractedReferences.length > 0
  };
}

/**
 * Clean up a reference string
 */
export function cleanReferenceString(refString: string): string {
  // Remove common numbering patterns
  return refString
    .replace(/^\d+\.\s+/, '')  // Remove leading numbers like "1. "
    .trim();
}
