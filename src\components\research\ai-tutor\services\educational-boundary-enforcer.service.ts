/**
 * Educational Boundary Enforcer Service
 * Ensures AI tutor stays within educational scope and doesn't interfere with other tools
 */

import { educationalContextDetector, EducationalContext, IntentClassification } from './educational-context-detector.service';

export interface BoundaryCheckResult {
  allowed: boolean;
  reason: string;
  suggestedAction: 'proceed' | 'redirect' | 'block' | 'warn';
  alternativeMode?: 'research' | 'general';
  warningMessage?: string;
}

export interface SessionContext {
  mode: 'tutor' | 'research' | 'general';
  educationLevel?: string;
  subject?: string;
  previousQueries: string[];
  sessionDuration: number;
  userPreferences?: {
    strictMode: boolean;
    allowCrossModeQueries: boolean;
  };
}

class EducationalBoundaryEnforcerService {
  private readonly EDUCATIONAL_CONFIDENCE_THRESHOLD = 60;
  private readonly STRICT_MODE_THRESHOLD = 80;
  
  // Topics that should be redirected to research mode
  private researchTopics = [
    'current events', 'news', 'recent developments', 'latest research',
    'market analysis', 'stock prices', 'financial data', 'business trends',
    'technical documentation', 'api reference', 'software documentation',
    'product reviews', 'comparisons', 'recommendations'
  ];

  // Topics that should be blocked or warned about
  private restrictedTopics = [
    'medical diagnosis', 'legal advice', 'financial advice', 'investment advice',
    'personal information', 'private data', 'passwords', 'security credentials',
    'harmful content', 'inappropriate material', 'violence', 'illegal activities'
  ];

  // Educational topics that are always allowed
  private alwaysAllowedTopics = [
    'mathematics', 'science', 'history', 'literature', 'language learning',
    'programming concepts', 'academic subjects', 'study techniques',
    'homework help', 'exam preparation', 'learning strategies'
  ];

  /**
   * Checks if a query is within educational boundaries for the AI tutor
   */
  async checkEducationalBoundary(
    query: string, 
    sessionContext: SessionContext
  ): Promise<BoundaryCheckResult> {
    // Get educational context
    const educationalContext = await educationalContextDetector.detectEducationalContext(
      query, 
      this.buildContextString(sessionContext)
    );

    // Get intent classification
    const intentClassification = await educationalContextDetector.classifyIntent(query);

    // Check for restricted content
    const restrictedCheck = this.checkRestrictedContent(query);
    if (!restrictedCheck.allowed) {
      return restrictedCheck;
    }

    // Apply boundary rules based on mode
    switch (sessionContext.mode) {
      case 'tutor':
        return this.checkTutorBoundaries(query, educationalContext, intentClassification, sessionContext);
      
      case 'research':
        return this.checkResearchBoundaries(query, educationalContext, sessionContext);
      
      case 'general':
        return this.checkGeneralBoundaries(query, educationalContext, sessionContext);
      
      default:
        return {
          allowed: true,
          reason: 'No specific boundaries defined for this mode',
          suggestedAction: 'proceed'
        };
    }
  }

  /**
   * Suggests the most appropriate mode for a given query
   */
  async suggestOptimalMode(
    query: string, 
    currentMode: 'tutor' | 'research' | 'general'
  ): Promise<'tutor' | 'research' | 'general' | null> {
    const intentClassification = await educationalContextDetector.classifyIntent(query);
    
    // If current mode is optimal, return null (no change needed)
    if (intentClassification.suggestedMode === currentMode) {
      return null;
    }

    // Return suggested mode if confidence is high enough
    if (intentClassification.confidence >= 70) {
      return intentClassification.suggestedMode;
    }

    return null;
  }

  private checkTutorBoundaries(
    query: string,
    educationalContext: EducationalContext,
    intentClassification: IntentClassification,
    sessionContext: SessionContext
  ): BoundaryCheckResult {
    const strictMode = sessionContext.userPreferences?.strictMode ?? false;
    const threshold = strictMode ? this.STRICT_MODE_THRESHOLD : this.EDUCATIONAL_CONFIDENCE_THRESHOLD;

    // Always allow clearly educational content
    if (this.isAlwaysAllowedTopic(query)) {
      return {
        allowed: true,
        reason: 'Query is clearly educational',
        suggestedAction: 'proceed'
      };
    }

    // Check educational confidence
    if (educationalContext.confidence < threshold) {
      // Check if it should be redirected to research mode
      if (this.shouldRedirectToResearch(query)) {
        return {
          allowed: false,
          reason: 'Query is better suited for research mode',
          suggestedAction: 'redirect',
          alternativeMode: 'research',
          warningMessage: 'This query seems to be research-oriented. Would you like to switch to Research mode for better results?'
        };
      }

      // Check if it should be redirected to general mode
      if (intentClassification.category === 'general' || intentClassification.category === 'system') {
        return {
          allowed: false,
          reason: 'Query is not educational in nature',
          suggestedAction: 'redirect',
          alternativeMode: 'general',
          warningMessage: 'This doesn\'t seem to be an educational query. Would you like to switch to General Assistant mode?'
        };
      }

      // Warn but allow if not in strict mode
      if (!strictMode) {
        return {
          allowed: true,
          reason: 'Query has low educational confidence but allowed in non-strict mode',
          suggestedAction: 'warn',
          warningMessage: 'This query doesn\'t seem very educational. The AI Tutor works best with learning-related questions.'
        };
      }

      // Block in strict mode
      return {
        allowed: false,
        reason: 'Query does not meet educational confidence threshold in strict mode',
        suggestedAction: 'block',
        warningMessage: 'This query is not educational enough for the AI Tutor. Please ask a learning-related question or switch to a different mode.'
      };
    }

    // Allow educational queries
    return {
      allowed: true,
      reason: `Educational query with ${educationalContext.confidence}% confidence`,
      suggestedAction: 'proceed'
    };
  }

  private checkResearchBoundaries(
    query: string,
    educationalContext: EducationalContext,
    sessionContext: SessionContext
  ): BoundaryCheckResult {
    // Research mode is more permissive but still has some boundaries
    
    // Check if it's clearly educational and might be better in tutor mode
    if (educationalContext.confidence >= 80 && educationalContext.intent === 'learning') {
      return {
        allowed: true,
        reason: 'Research query allowed, but might be better in tutor mode',
        suggestedAction: 'warn',
        alternativeMode: 'tutor',
        warningMessage: 'This seems like a learning question. Would you like to switch to AI Tutor mode for a more educational approach?'
      };
    }

    // Allow most research queries
    return {
      allowed: true,
      reason: 'Query is appropriate for research mode',
      suggestedAction: 'proceed'
    };
  }

  private checkGeneralBoundaries(
    query: string,
    educationalContext: EducationalContext,
    sessionContext: SessionContext
  ): BoundaryCheckResult {
    // General mode is most permissive
    
    // Suggest tutor mode for clearly educational queries
    if (educationalContext.confidence >= 75) {
      return {
        allowed: true,
        reason: 'General query allowed, but educational content detected',
        suggestedAction: 'warn',
        alternativeMode: 'tutor',
        warningMessage: 'This looks like an educational question! Would you like to switch to AI Tutor mode for a better learning experience?'
      };
    }

    return {
      allowed: true,
      reason: 'Query is appropriate for general mode',
      suggestedAction: 'proceed'
    };
  }

  private checkRestrictedContent(query: string): BoundaryCheckResult {
    const lowerQuery = query.toLowerCase();

    // Check for restricted topics
    for (const topic of this.restrictedTopics) {
      if (lowerQuery.includes(topic.toLowerCase())) {
        return {
          allowed: false,
          reason: `Query contains restricted content: ${topic}`,
          suggestedAction: 'block',
          warningMessage: 'I cannot provide assistance with this type of request. Please ask about educational topics or general knowledge instead.'
        };
      }
    }

    return {
      allowed: true,
      reason: 'No restricted content detected',
      suggestedAction: 'proceed'
    };
  }

  private isAlwaysAllowedTopic(query: string): boolean {
    const lowerQuery = query.toLowerCase();
    return this.alwaysAllowedTopics.some(topic => 
      lowerQuery.includes(topic.toLowerCase())
    );
  }

  private shouldRedirectToResearch(query: string): boolean {
    const lowerQuery = query.toLowerCase();
    return this.researchTopics.some(topic => 
      lowerQuery.includes(topic.toLowerCase())
    );
  }

  private buildContextString(sessionContext: SessionContext): string {
    const parts = [
      `Mode: ${sessionContext.mode}`,
      sessionContext.educationLevel ? `Education Level: ${sessionContext.educationLevel}` : '',
      sessionContext.subject ? `Subject: ${sessionContext.subject}` : '',
      `Session Duration: ${sessionContext.sessionDuration} minutes`,
      `Previous Queries: ${sessionContext.previousQueries.length}`
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Validates if a mode switch is appropriate
   */
  async validateModeSwitch(
    fromMode: 'tutor' | 'research' | 'general',
    toMode: 'tutor' | 'research' | 'general',
    currentQuery?: string
  ): Promise<{ valid: boolean; reason: string; warning?: string }> {
    // Always allow manual mode switches
    if (!currentQuery) {
      return {
        valid: true,
        reason: 'Manual mode switch requested'
      };
    }

    // Check if the switch makes sense for the current query
    const intentClassification = await educationalContextDetector.classifyIntent(currentQuery);
    
    if (intentClassification.suggestedMode === toMode) {
      return {
        valid: true,
        reason: 'Mode switch aligns with query intent'
      };
    }

    // Warn if switching to a less optimal mode
    return {
      valid: true,
      reason: 'Mode switch allowed but may not be optimal',
      warning: `The ${intentClassification.suggestedMode} mode might be better suited for this type of query.`
    };
  }

  /**
   * Gets boundary enforcement statistics
   */
  getBoundaryStats(sessionContext: SessionContext): {
    totalQueries: number;
    allowedQueries: number;
    redirectedQueries: number;
    blockedQueries: number;
    warningQueries: number;
  } {
    // This would typically be implemented with actual session tracking
    // For now, return mock data
    return {
      totalQueries: sessionContext.previousQueries.length,
      allowedQueries: Math.floor(sessionContext.previousQueries.length * 0.8),
      redirectedQueries: Math.floor(sessionContext.previousQueries.length * 0.1),
      blockedQueries: Math.floor(sessionContext.previousQueries.length * 0.05),
      warningQueries: Math.floor(sessionContext.previousQueries.length * 0.05)
    };
  }
}

export const educationalBoundaryEnforcer = new EducationalBoundaryEnforcerService();
