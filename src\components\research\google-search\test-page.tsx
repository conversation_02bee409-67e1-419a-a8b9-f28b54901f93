/**
 * Google Search Test Page
 * Simple page to test Google Search functionality
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  TestTube, 
  Stethoscope, 
  AlertTriangle,
  ExternalLink 
} from 'lucide-react';

import { GoogleSearchInterface } from './components/GoogleSearchInterface';
import { GoogleSearchValidation } from './components/GoogleSearchValidation';
import { GoogleSearchDiagnostic } from './components/GoogleSearchDiagnostic';
import { runGoogleSearchDiagnostic } from './diagnostics/google-search-diagnostic';

export function GoogleSearchTestPage() {
  const [quickTestResult, setQuickTestResult] = useState<string | null>(null);
  const [isRunningQuickTest, setIsRunningQuickTest] = useState(false);

  const runQuickTest = async () => {
    setIsRunningQuickTest(true);
    setQuickTestResult(null);

    try {
      console.log('🚀 Running Quick Google Search Test...');
      
      const results = await runGoogleSearchDiagnostic();
      const passedTests = results.filter(r => r.status === 'pass').length;
      const totalTests = results.length;
      const successRate = (passedTests / totalTests) * 100;

      let resultMessage = '';
      if (successRate >= 80) {
        resultMessage = `✅ Google Search is working correctly! (${passedTests}/${totalTests} tests passed)`;
      } else if (successRate >= 50) {
        resultMessage = `⚠️ Google Search has some issues. (${passedTests}/${totalTests} tests passed)`;
      } else {
        resultMessage = `❌ Google Search is NOT working properly. (${passedTests}/${totalTests} tests passed)`;
      }

      setQuickTestResult(resultMessage);

      // Log detailed results to console
      console.log('\n📊 Quick Test Results:');
      results.forEach(result => {
        const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        console.log(`${icon} ${result.test}: ${result.message}`);
      });

    } catch (error) {
      console.error('❌ Quick test failed:', error);
      setQuickTestResult(`❌ Quick test failed: ${error.message}`);
    } finally {
      setIsRunningQuickTest(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">Google Search Test Center</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive testing suite to verify Google Search integration, detect fake URLs, 
            and ensure real search results are being used instead of AI training data.
          </p>
        </div>

        {/* Quick Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TestTube className="h-5 w-5" />
              <span>Quick Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Run a quick diagnostic to check if Google Search is working properly.
            </p>
            <div className="flex items-center space-x-4">
              <Button 
                onClick={runQuickTest}
                disabled={isRunningQuickTest}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isRunningQuickTest ? 'Running Test...' : 'Run Quick Test'}
              </Button>
              <Button 
                variant="outline"
                onClick={() => window.open('https://console.developers.google.com/', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Google API Console
              </Button>
            </div>
            {quickTestResult && (
              <Alert className={quickTestResult.includes('✅') ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {quickTestResult}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Main Testing Interface */}
        <Tabs defaultValue="interface" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="interface" className="flex items-center space-x-2">
              <Search className="h-4 w-4" />
              <span>Search Interface</span>
            </TabsTrigger>
            <TabsTrigger value="diagnostic" className="flex items-center space-x-2">
              <Stethoscope className="h-4 w-4" />
              <span>Diagnostic</span>
            </TabsTrigger>
            <TabsTrigger value="validation" className="flex items-center space-x-2">
              <TestTube className="h-4 w-4" />
              <span>Validation</span>
            </TabsTrigger>
            <TabsTrigger value="help" className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Help</span>
            </TabsTrigger>
          </TabsList>

          {/* Search Interface Tab */}
          <TabsContent value="interface">
            <Card>
              <CardHeader>
                <CardTitle>Google Search Interface</CardTitle>
                <p className="text-gray-600">
                  Test the actual search interface. Try both Standard and Deep Research modes.
                  Check if the URLs and sources are real and working.
                </p>
              </CardHeader>
              <CardContent>
                <div className="h-96 border rounded-lg overflow-hidden">
                  <GoogleSearchInterface userId="test-user" />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Diagnostic Tab */}
          <TabsContent value="diagnostic">
            <GoogleSearchDiagnostic />
          </TabsContent>

          {/* Validation Tab */}
          <TabsContent value="validation">
            <GoogleSearchValidation />
          </TabsContent>

          {/* Help Tab */}
          <TabsContent value="help">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>How to Check if Google Search is Working</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-green-700 mb-3">✅ Signs Google Search is Working:</h4>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li>• Real URLs from actual websites (.edu, .gov, academic journals)</li>
                        <li>• Current year information and recent dates</li>
                        <li>• Specific article titles and author names</li>
                        <li>• Working links that you can click and visit</li>
                        <li>• Academic sources like PubMed, arXiv, Nature, Science</li>
                        <li>• Recent news and current events</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-red-700 mb-3">❌ Signs Using Training Data:</h4>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li>• Fake URLs like example.com, placeholder.com</li>
                        <li>• Generic article titles like "Study on AI"</li>
                        <li>• No current year information</li>
                        <li>• Broken or non-existent links</li>
                        <li>• Vague references without specific details</li>
                        <li>• Outdated information only</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Troubleshooting Steps</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium">1. Check API Configuration</h4>
                      <p className="text-sm text-gray-600">
                        Ensure VITE_GEMINI_API_KEY is set in your .env file and the API key has Google Search access enabled.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium">2. Verify Google Search Tool</h4>
                      <p className="text-sm text-gray-600">
                        The Gemini API must have the Google Search tool enabled. Check your Google Cloud Console settings.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium">3. Test with Simple Queries</h4>
                      <p className="text-sm text-gray-600">
                        Start with simple queries like "current date" or "latest news" to verify real-time search capability.
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium">4. Check Network Connection</h4>
                      <p className="text-sm text-gray-600">
                        Ensure your application can make external API calls to Google's services.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Console Commands</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 font-mono text-sm bg-gray-50 p-4 rounded-lg">
                    <div>// Run diagnostic in browser console:</div>
                    <div className="text-blue-600">runGoogleSearchDiagnostic()</div>
                    <div></div>
                    <div>// Quick health check:</div>
                    <div className="text-blue-600">quickHealthCheck()</div>
                    <div></div>
                    <div>// Test specific functionality:</div>
                    <div className="text-blue-600">runIndividualTest('url')</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
