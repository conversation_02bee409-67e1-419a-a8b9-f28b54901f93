# AI Change Tracking System

A comprehensive change tracking and review system for AI-powered text modifications in the research platform's editor.

## Features

### Core Functionality
- **Real-time Change Tracking**: Automatically tracks all AI modifications during editing sessions
- **Visual Diff Interface**: Shows changes with red (deleted) and green (added) highlighting
- **Granular Change Management**: Accept or reject individual changes or all at once
- **Change Navigation**: Navigate between changes with next/previous controls
- **Session-based Tracking**: Changes are tracked per editing session

### Visual Interface
- **Inline Diff View**: Shows changes directly in context
- **Change Highlighting**: Clear visual indicators for additions and deletions
- **Change Counter**: Shows current change position (e.g., "Change 2 of 5")
- **Status Indicators**: Real-time tracking status and pending changes count

### User Controls
- **Start/Stop Tracking**: Toggle change tracking on/off
- **View Mode Toggle**: Switch between normal and diff view
- **Bulk Actions**: Accept all or reject all changes at once
- **Individual Actions**: Accept or reject specific changes
- **Navigation**: Move between changes sequentially

## Components

### `ChangeTrackingService`
Core service that manages change tracking state and operations.

**Key Methods:**
- `startTracking(initialContent)` - Begin tracking changes
- `stopTracking()` - Stop tracking and clear changes
- `recordChange(...)` - Record a new AI modification
- `acceptChange(changeId)` - Accept a specific change
- `rejectChange(changeId)` - Reject a specific change
- `acceptAllChanges()` - Accept all pending changes
- `rejectAllChanges()` - Reject all and revert to original

### `useChangeTracking`
React hook that provides easy access to change tracking functionality.

**Returns:**
- State: `isTrackingEnabled`, `viewMode`, `pendingChangesCount`, `hasChanges`
- Actions: `startTracking`, `stopTracking`, `recordChange`, etc.
- Navigation: `navigateToNextChange`, `navigateToPreviousChange`

### `ChangeTrackingControls`
UI component for change tracking controls and status display.

**Props:**
- `onToggleTracking` - Handler for enabling/disabling tracking
- `onToggleDiffView` - Handler for switching view modes
- `onAcceptAllChanges` - Handler for accepting all changes
- `onRejectAllChanges` - Handler for rejecting all changes

### `DiffViewer`
Visual diff component that displays changes with highlighting and controls.

**Props:**
- `onAcceptChange` - Handler for accepting individual changes
- `onRejectChange` - Handler for rejecting individual changes
- `onAcceptAllChanges` - Handler for accepting all changes
- `onRejectAllChanges` - Handler for rejecting all changes
- `onNavigateChange` - Handler for change navigation

## Integration

### In EnhancedMainEditor

The change tracking system is integrated into the main editor component:

```typescript
import { useChangeTracking, ChangeTrackingControls, DiffViewer } from './change-tracking';

// In component
const changeTracking = useChangeTracking();

// In AI action handler
const applyAIAction = async (responseContent, actionType, originalText, prompt) => {
  // Record change if tracking is enabled
  if (changeTracking.isTrackingEnabled && actionType !== 'display') {
    changeTracking.recordChange(
      originalText,
      responseContent,
      startPosition,
      endPosition,
      actionType,
      prompt
    );
  }
  // ... apply the change
};
```

### UI Integration

```jsx
{/* Change Tracking Controls */}
<ChangeTrackingControls
  onToggleTracking={handleToggleTracking}
  onToggleDiffView={handleToggleDiffView}
  onAcceptAllChanges={handleAcceptAllChanges}
  onRejectAllChanges={handleRejectAllChanges}
/>

{/* Diff Viewer - shown when in diff mode */}
{changeTracking.viewMode === 'diff' && changeTracking.hasChanges && (
  <DiffViewer
    onAcceptChange={handleAcceptChange}
    onRejectChange={handleRejectChange}
    onAcceptAllChanges={handleAcceptAllChanges}
    onRejectAllChanges={handleRejectAllChanges}
    onNavigateChange={handleNavigateChange}
  />
)}
```

## Data Flow

1. **Start Tracking**: User enables tracking, system stores original content
2. **AI Modification**: AI makes changes, system records before/after states
3. **Change Review**: User can see changes in diff view with highlighting
4. **Change Management**: User accepts/rejects individual or all changes
5. **Session End**: User can stop tracking or continue with new changes

## Types

### `ChangeRecord`
```typescript
interface ChangeRecord {
  id: string;
  timestamp: Date;
  originalText: string;
  newText: string;
  startPosition: number;
  endPosition: number;
  aiActionType: 'replace' | 'insert' | 'display';
  status: 'pending' | 'accepted' | 'rejected';
  prompt?: string;
}
```

### `ChangeTrackingState`
```typescript
interface ChangeTrackingState {
  originalContent: string;
  currentContent: string;
  changes: ChangeRecord[];
  isTrackingEnabled: boolean;
  currentChangeIndex: number;
  viewMode: 'normal' | 'diff';
}
```

## Dependencies

- `diff` - For computing text differences
- `react-diff-viewer-continued` - For visual diff display
- `nanoid` - For generating unique change IDs
- `sonner` - For toast notifications

## Usage Example

See `ChangeTrackingDemo.tsx` for a complete working example of the change tracking system.

## Future Enhancements

- **Persistent Storage**: Save change history across sessions
- **Change Comments**: Allow users to add notes to changes
- **Change Categories**: Categorize changes by type (grammar, style, content)
- **Undo/Redo**: Full undo/redo support for change operations
- **Export Changes**: Export change summary for review
- **Collaborative Review**: Multi-user change review workflow
