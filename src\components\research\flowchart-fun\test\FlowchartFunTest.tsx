/**
 * Comprehensive Test Component for Flowchart Fun
 * Tests all major features and functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Play, FileText, Eye, Palette, BookOpen } from 'lucide-react';
import { FlowchartFun } from '../FlowchartFun';
import Settings from '../components/Settings';
import Examples from '../components/Examples';
import EnhancedGraphViewer from '../components/EnhancedGraphViewer';
import { defaultTheme, themePresets } from '../themes/defaultTheme';
import { flowchartExamples } from '../examples/flowchartExamples';
import { FFTheme, FlowchartTemplate } from '../types';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

const FlowchartFunTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<FFTheme>(defaultTheme);
  const [testText, setTestText] = useState(`Start
  Process Data
    Valid Data?
      Yes: Save to Database
        Success?
          Yes: Send Confirmation
            End
          No: Log Error
            Retry?
              Yes: Save to Database
              No: End
      No: Show Error Message
        End`);

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: Theme System
    try {
      const testTheme = { ...defaultTheme, nodeBackground: '#ff0000' };
      setCurrentTheme(testTheme);
      results.push({
        name: 'Theme System',
        status: 'pass',
        message: 'Theme system working correctly'
      });
    } catch (error) {
      results.push({
        name: 'Theme System',
        status: 'fail',
        message: `Theme system failed: ${error}`
      });
    }

    // Test 2: Examples Loading
    try {
      if (flowchartExamples.length > 0) {
        results.push({
          name: 'Examples Loading',
          status: 'pass',
          message: `${flowchartExamples.length} examples loaded successfully`
        });
      } else {
        results.push({
          name: 'Examples Loading',
          status: 'warning',
          message: 'No examples found'
        });
      }
    } catch (error) {
      results.push({
        name: 'Examples Loading',
        status: 'fail',
        message: `Examples loading failed: ${error}`
      });
    }

    // Test 3: Theme Presets
    try {
      if (themePresets.length > 0) {
        results.push({
          name: 'Theme Presets',
          status: 'pass',
          message: `${themePresets.length} theme presets available`
        });
      } else {
        results.push({
          name: 'Theme Presets',
          status: 'warning',
          message: 'No theme presets found'
        });
      }
    } catch (error) {
      results.push({
        name: 'Theme Presets',
        status: 'fail',
        message: `Theme presets failed: ${error}`
      });
    }

    // Test 4: Text Parsing
    try {
      if (testText.includes('Start') && testText.includes('End')) {
        results.push({
          name: 'Text Parsing',
          status: 'pass',
          message: 'Text parsing structure looks correct'
        });
      } else {
        results.push({
          name: 'Text Parsing',
          status: 'warning',
          message: 'Text parsing may have issues'
        });
      }
    } catch (error) {
      results.push({
        name: 'Text Parsing',
        status: 'fail',
        message: `Text parsing failed: ${error}`
      });
    }

    // Test 5: Component Rendering
    try {
      // Test if key components can be instantiated
      const settingsTest = React.createElement(Settings, {
        theme: currentTheme,
        onThemeChange: () => {}
      });
      
      const examplesTest = React.createElement(Examples, {
        onSelectExample: () => {}
      });

      results.push({
        name: 'Component Rendering',
        status: 'pass',
        message: 'All components can be rendered'
      });
    } catch (error) {
      results.push({
        name: 'Component Rendering',
        status: 'fail',
        message: `Component rendering failed: ${error}`
      });
    }

    // Test 6: Layout Algorithms
    try {
      const layouts = ['dagre', 'klay', 'cose', 'breadthfirst', 'concentric', 'circle'];
      const availableLayouts = layouts.filter(layout => 
        Object.values(defaultTheme).includes(layout) || layout === 'dagre'
      );
      
      results.push({
        name: 'Layout Algorithms',
        status: 'pass',
        message: `Layout algorithms available: ${layouts.join(', ')}`
      });
    } catch (error) {
      results.push({
        name: 'Layout Algorithms',
        status: 'fail',
        message: `Layout algorithms test failed: ${error}`
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-50 border-green-200';
      case 'fail':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
    }
  };

  const passedTests = testResults.filter(r => r.status === 'pass').length;
  const failedTests = testResults.filter(r => r.status === 'fail').length;
  const warningTests = testResults.filter(r => r.status === 'warning').length;

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Flowchart Fun - Comprehensive Test Suite
          </CardTitle>
          <CardDescription>
            Test all major features and functionality of the enhanced Flow Builder module
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            
            {testResults.length > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  {passedTests} Passed
                </Badge>
                {failedTests > 0 && (
                  <Badge variant="destructive">
                    {failedTests} Failed
                  </Badge>
                )}
                {warningTests > 0 && (
                  <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                    {warningTests} Warnings
                  </Badge>
                )}
              </div>
            )}
          </div>

          {testResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Test Results:</h3>
              {testResults.map((result, index) => (
                <div 
                  key={index} 
                  className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.name}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="demo" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="demo">Live Demo</TabsTrigger>
          <TabsTrigger value="settings">Settings Test</TabsTrigger>
          <TabsTrigger value="examples">Examples Test</TabsTrigger>
          <TabsTrigger value="themes">Themes Test</TabsTrigger>
        </TabsList>

        <TabsContent value="demo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Live Flowchart Fun Demo</CardTitle>
              <CardDescription>
                Test the complete Flow Builder functionality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96 border rounded-lg">
                <FlowchartFun
                  initialText={testText}
                  onTextChange={setTestText}
                  className="h-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Settings Panel Test</CardTitle>
              <CardDescription>
                Test the comprehensive settings interface
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Settings
                theme={currentTheme}
                onThemeChange={(updates) => setCurrentTheme(prev => ({ ...prev, ...updates }))}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Examples Gallery Test</CardTitle>
              <CardDescription>
                Test the examples showcase and template loading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Examples
                onSelectExample={(template: FlowchartTemplate) => {
                  setTestText(template.text);
                  alert(`Loaded example: ${template.name}`);
                }}
                onPreviewExample={(template: FlowchartTemplate) => {
                  alert(`Previewing: ${template.name}`);
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="themes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Theme System Test</CardTitle>
              <CardDescription>
                Test theme presets and customization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {themePresets.map((preset) => (
                  <Card 
                    key={preset.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => setCurrentTheme(preset.theme)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">{preset.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {preset.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div 
                        className="w-full h-16 rounded border-2 p-2"
                        style={{ backgroundColor: preset.theme.background }}
                      >
                        <div
                          className="w-12 h-6 rounded text-xs flex items-center justify-center"
                          style={{
                            backgroundColor: preset.theme.nodeBackground,
                            color: preset.theme.nodeForeground,
                            borderWidth: `${preset.theme.borderWidth}px`,
                            borderColor: preset.theme.borderColor,
                            borderStyle: 'solid',
                          }}
                        >
                          Node
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {testResults.length > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Test suite completed! {passedTests} tests passed, {failedTests} failed, {warningTests} warnings.
            {failedTests === 0 && warningTests === 0 && " All systems operational! 🎉"}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default FlowchartFunTest;
