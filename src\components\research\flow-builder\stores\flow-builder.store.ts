/**
 * Flow Builder Zustand Store
 * State management for the Flow Chart Builder module
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { nanoid } from 'nanoid';
import {
  FlowBuilderStore,
  GeneratedDiagram,
  DiagramGenerationRequest,
  DiagramMetadata,
  DiagramValidationResult,
  DiagramExportOptions,
  MermaidConfig,
  DiagramTemplate,
  FlowBuilderError
} from '../types';
import {
  DEFAULT_MERMAID_CONFIG,
  DEFAULT_EXPORT_OPTIONS,
  GENERATION_CONFIG,
  DEFAULT_TEMPLATES,
  STORAGE_KEYS,
  VAL<PERSON>ATION_RULES,
  ERROR_MESSAGES
} from '../constants';
import { diagramHistoryService } from '../services/diagram-history.service';

// Validation function (extracted to avoid circular references)
const validateDiagramCode = (code: string): DiagramValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Basic validation
  if (!code.trim()) {
    errors.push('Diagram code cannot be empty');
    return { isValid: false, errors, warnings, suggestions };
  }

  if (code.length > VALIDATION_RULES.MAX_MERMAID_CODE_LENGTH) {
    errors.push(`Diagram code is too long (max ${VALIDATION_RULES.MAX_MERMAID_CODE_LENGTH} characters)`);
  }

  // Check for basic Mermaid syntax
  const lines = code.split('\n').map(line => line.trim()).filter(line => line);
  if (lines.length === 0) {
    errors.push('Diagram must contain at least one line of code');
    return { isValid: false, errors, warnings, suggestions };
  }

  // Check for diagram type declaration
  const firstLine = lines[0]?.toLowerCase() || '';
  const validStarters = ['flowchart', 'graph', 'sequencediagram', 'gantt', 'pie', 'mindmap', 'timeline', 'journey', 'erdiagram', 'statediagram', 'classDiagram'];
  const hasValidStarter = validStarters.some(starter => firstLine.startsWith(starter));

  if (!hasValidStarter) {
    // This is just a warning, not an error - allow preview
    warnings.push('Diagram should start with a valid diagram type declaration');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions
  };
};

// Create example diagram
const createExampleDiagram = (): GeneratedDiagram => ({
  id: 'example-diagram',
  metadata: {
    title: 'Research Methodology Example',
    description: 'This is an example research methodology flowchart. You can edit this code or generate a new diagram using AI.',
    type: 'flowchart',
    keywords: ['research', 'methodology', 'example'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  mermaidCode: `flowchart TD
    A[Research Question] --> B[Literature Review]
    B --> C{Sufficient Background}
    C -->|No| B
    C -->|Yes| D[Hypothesis Formation]
    D --> E[Research Design]
    E --> F[Data Collection]
    F --> G[Data Analysis]
    G --> H[Results Interpretation]
    H --> I{Hypothesis Supported}
    I -->|Yes| J[Accept Hypothesis]
    I -->|No| K[Reject Hypothesis]
    J --> L[Publication]
    K --> L
    L --> M[Future Research]`,
  status: 'ready',
  generatedAt: new Date(),
  model: 'example'
});

// Initial state
const initialState = {
  // Core state
  metadata: null,
  currentDiagram: createExampleDiagram(),
  history: {
    diagrams: [createExampleDiagram()],
    totalGenerated: 1,
    lastGenerated: new Date()
  },
  
  // Generation state
  workflow: {
    currentStep: 'input' as const,
    isGenerating: false,
    progress: 0,
    estimatedTimeRemaining: undefined,
    error: undefined
  },
  selectedModel: GENERATION_CONFIG.DEFAULT_MODEL,
  
  // Editing state
  editing: {
    isEditing: false,
    originalCode: '',
    editedCode: '',
    hasUnsavedChanges: false,
    validationErrors: []
  },
  
  // Configuration
  mermaidConfig: DEFAULT_MERMAID_CONFIG,
  exportOptions: DEFAULT_EXPORT_OPTIONS,
  
  // Templates
  templates: DEFAULT_TEMPLATES,
  selectedTemplate: undefined
};

export const useFlowBuilderStore = create<FlowBuilderStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Diagram generation
        generateDiagram: async (request: DiagramGenerationRequest) => {
          const { flowAIService } = await import('../services/flow-ai.service');
          
          set((state) => {
            state.workflow.isGenerating = true;
            state.workflow.currentStep = 'generating';
            state.workflow.progress = 0;
            state.workflow.error = undefined;
          });

          try {
            const response = await flowAIService.generateDiagram(request);
            
            if (response.success && response.diagram) {
              set((state) => {
                state.currentDiagram = response.diagram!;
                state.history.diagrams.unshift(response.diagram!);
                state.history.totalGenerated += 1;
                state.history.lastGenerated = new Date();
                state.workflow.isGenerating = false;
                state.workflow.currentStep = 'review';
                state.workflow.progress = 100;

                // Limit history size
                if (state.history.diagrams.length > 50) {
                  state.history.diagrams = state.history.diagrams.slice(0, 50);
                }
              });

              // Save to Supabase history (async, don't block UI)
              try {
                const selectedModel = get().selectedModel;
                await diagramHistoryService.saveDiagram(response.diagram!, request.input, selectedModel);
              } catch (error) {
                console.warn('Failed to save diagram to history:', error);
                // Don't throw - this shouldn't break the main flow
              }
            } else {
              throw new FlowBuilderError(
                response.error || ERROR_MESSAGES.GENERATION_FAILED,
                'GENERATION_FAILED'
              );
            }
          } catch (error: any) {
            set((state) => {
              state.workflow.isGenerating = false;
              state.workflow.currentStep = 'input';
              state.workflow.error = error.message || ERROR_MESSAGES.GENERATION_FAILED;
              state.workflow.progress = 0;
            });
            throw error;
          }
        },

        regenerateDiagram: async (feedback?: string) => {
          const currentDiagram = get().currentDiagram;
          if (!currentDiagram) return;

          const request: DiagramGenerationRequest = {
            input: {
              description: currentDiagram.metadata.description + (feedback ? `\n\nAdditional feedback: ${feedback}` : ''),
              type: currentDiagram.metadata.type,
              direction: currentDiagram.metadata.direction
            },
            model: get().selectedModel
          };

          await get().generateDiagram(request);
        },

        editDiagramWithAI: async (editPrompt: string) => {
          const currentDiagram = get().currentDiagram;
          if (!currentDiagram) return;

          const enhancedDescription = `${currentDiagram.metadata.description}

EDIT INSTRUCTIONS: ${editPrompt}

Please modify the existing diagram based on the edit instructions above. Keep the same general structure but incorporate the requested changes.`;

          const request: DiagramGenerationRequest = {
            input: {
              description: enhancedDescription,
              type: currentDiagram.metadata.type,
              direction: currentDiagram.metadata.direction
            },
            model: get().selectedModel
          };

          await get().generateDiagram(request);
        },

        fixDiagramErrors: async (errorMessage: string) => {
          const currentDiagram = get().currentDiagram;
          if (!currentDiagram) return;

          const fixDescription = `Fix the following Mermaid diagram that has errors:

ORIGINAL DESCRIPTION: ${currentDiagram.metadata.description}
DIAGRAM TYPE: ${currentDiagram.metadata.type}
ERROR MESSAGE: ${errorMessage}
CURRENT CODE:
${currentDiagram.mermaidCode}

Please generate a corrected version of this diagram that:
1. Fixes all syntax errors
2. Maintains the original intent and structure
3. Uses proper Mermaid.js syntax for ${currentDiagram.metadata.type} diagrams
4. Follows all syntax rules (no special characters in labels, proper spacing, etc.)
5. Ensures the diagram renders correctly

Generate only the corrected Mermaid code without any explanations.`;

          const request: DiagramGenerationRequest = {
            input: {
              description: fixDescription,
              type: currentDiagram.metadata.type,
              direction: currentDiagram.metadata.direction
            },
            model: get().selectedModel
          };

          await get().generateDiagram(request);
        },

        // Diagram management
        setCurrentDiagram: (diagram: GeneratedDiagram) => {
          set((state) => {
            state.currentDiagram = diagram;
            state.workflow.currentStep = 'review';
          });
        },

        saveDiagram: (diagram: GeneratedDiagram) => {
          set((state) => {
            const existingIndex = state.history.diagrams.findIndex(d => d.id === diagram.id);
            if (existingIndex >= 0) {
              state.history.diagrams[existingIndex] = diagram;
            } else {
              state.history.diagrams.unshift(diagram);
              state.history.totalGenerated += 1;
            }
            state.history.lastGenerated = new Date();
          });
        },

        deleteDiagram: (id: string) => {
          set((state) => {
            state.history.diagrams = state.history.diagrams.filter(d => d.id !== id);
            if (state.currentDiagram?.id === id) {
              state.currentDiagram = null;
              state.workflow.currentStep = 'input';
            }
          });
        },

        loadDiagramHistory: () => {
          // History is automatically loaded from persistence
          // This method can be used for manual refresh if needed
        },

        // Editing
        startEditing: () => {
          const currentDiagram = get().currentDiagram;
          if (!currentDiagram) return;

          set((state) => {
            state.editing.isEditing = true;
            state.editing.originalCode = currentDiagram.mermaidCode;
            state.editing.editedCode = currentDiagram.mermaidCode;
            state.editing.hasUnsavedChanges = false;
            state.editing.validationErrors = [];
            state.workflow.currentStep = 'editing';
          });
        },

        updateDiagramCode: (code: string) => {
          set((state) => {
            state.editing.editedCode = code;
            state.editing.hasUnsavedChanges = code !== state.editing.originalCode;

            // Validate the code using the validation function directly
            const validation = validateDiagramCode(code);
            state.editing.validationErrors = validation.errors;
          });
        },

        saveEdits: () => {
          const { currentDiagram, editing } = get();
          if (!currentDiagram || !editing.isEditing) return;

          const validation = validateDiagramCode(editing.editedCode);
          if (!validation.isValid) {
            set((state) => {
              state.editing.validationErrors = validation.errors;
            });
            return;
          }

          const updatedDiagram: GeneratedDiagram = {
            ...currentDiagram,
            mermaidCode: editing.editedCode,
            metadata: {
              ...currentDiagram.metadata,
              updatedAt: new Date()
            }
          };

          set((state) => {
            state.currentDiagram = updatedDiagram;
            state.editing.isEditing = false;
            state.editing.originalCode = '';
            state.editing.editedCode = '';
            state.editing.hasUnsavedChanges = false;
            state.editing.validationErrors = [];
            state.workflow.currentStep = 'review';
          });

          get().saveDiagram(updatedDiagram);
        },

        cancelEditing: () => {
          set((state) => {
            state.editing.isEditing = false;
            state.editing.originalCode = '';
            state.editing.editedCode = '';
            state.editing.hasUnsavedChanges = false;
            state.editing.validationErrors = [];
            state.workflow.currentStep = 'review';
          });
        },

        validateDiagram: (code: string): DiagramValidationResult => {
          return validateDiagramCode(code);
        },

        // Export
        exportDiagram: async (options: DiagramExportOptions) => {
          const currentDiagram = get().currentDiagram;
          if (!currentDiagram) {
            throw new FlowBuilderError('No diagram to export', 'NO_DIAGRAM');
          }

          try {
            const { diagramExportService } = await import('../services/diagram-export.service');
            await diagramExportService.exportDiagram(currentDiagram, options);
          } catch (error: any) {
            throw new FlowBuilderError(
              error.message || ERROR_MESSAGES.EXPORT_FAILED,
              'EXPORT_FAILED',
              error
            );
          }
        },

        // Configuration
        updateMermaidConfig: (config: Partial<MermaidConfig>) => {
          set((state) => {
            state.mermaidConfig = { ...state.mermaidConfig, ...config };
          });
        },

        setSelectedModel: (model: string) => {
          set((state) => {
            state.selectedModel = model;
          });
        },

        // Templates
        loadTemplates: () => {
          // Templates are loaded from constants by default
          // This method can be extended to load custom templates from API
        },

        applyTemplate: (templateId: string, variables?: Record<string, string>) => {
          const template = get().templates.find(t => t.id === templateId);
          if (!template) return;

          let mermaidCode = template.mermaidCode;
          
          // Replace variables if provided
          if (variables) {
            Object.entries(variables).forEach(([key, value]) => {
              mermaidCode = mermaidCode.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value);
            });
          }

          const diagram: GeneratedDiagram = {
            id: nanoid(),
            metadata: {
              title: template.name,
              description: template.description,
              type: template.type,
              keywords: [],
              createdAt: new Date(),
              updatedAt: new Date()
            },
            mermaidCode,
            status: 'ready',
            generatedAt: new Date(),
            model: 'template'
          };

          set((state) => {
            state.currentDiagram = diagram;
            state.selectedTemplate = templateId;
            state.workflow.currentStep = 'review';
          });

          get().saveDiagram(diagram);
        },

        // Utility
        resetState: () => {
          set((state) => {
            Object.assign(state, initialState);
          });
        },

        setError: (error: string) => {
          set((state) => {
            state.workflow.error = error;
          });
        },

        clearError: () => {
          set((state) => {
            state.workflow.error = undefined;
          });
        }
      })),
      {
        name: STORAGE_KEYS.DIAGRAM_HISTORY,
        partialize: (state) => ({
          history: state.history,
          mermaidConfig: state.mermaidConfig,
          exportOptions: state.exportOptions,
          selectedModel: state.selectedModel
        })
      }
    ),
    { name: 'flow-builder-store' }
  )
);
