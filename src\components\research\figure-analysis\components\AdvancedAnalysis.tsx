/**
 * Advanced Analysis Component
 * Displays innovative AI-powered analysis features
 */

import React, { useState } from 'react';
import { 
  Shield, Search, BarChart3, Microscope, 
  AlertTriangle, TrendingUp, Database, Award,
  ChevronDown, ChevronRight, ExternalLink,
  CheckCircle, XCircle, Info, Eye, Brain
} from 'lucide-react';
import { AdvancedAnalysis as AdvancedAnalysisType } from '../types';

interface AdvancedAnalysisProps {
  analysis: AdvancedAnalysisType;
  className?: string;
}

export const AdvancedAnalysis: React.FC<AdvancedAnalysisProps> = ({
  analysis,
  className = '',
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['authenticity']));

  /**
   * Toggle section expansion
   */
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  /**
   * Get risk level color
   */
  const getRiskColor = (level: number) => {
    if (level >= 80) return 'text-red-600 bg-red-100 border-red-200';
    if (level >= 60) return 'text-orange-600 bg-orange-100 border-orange-200';
    if (level >= 40) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    return 'text-green-600 bg-green-100 border-green-200';
  };

  /**
   * Get impact level color
   */
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'very-high': return 'text-purple-600 bg-purple-100';
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  /**
   * Render collapsible section
   */
  const renderSection = (
    title: string,
    icon: React.ReactNode,
    content: React.ReactNode,
    sectionKey: string,
    badge?: React.ReactNode
  ) => {
    const isExpanded = expandedSections.has(sectionKey);
    
    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
        >
          <div className="flex items-center space-x-3">
            {icon}
            <span className="font-medium text-gray-900">{title}</span>
            {badge}
          </div>
          {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </button>
        
        {isExpanded && (
          <div className="p-4 bg-white">
            {content}
          </div>
        )}
      </div>
    );
  };

  /**
   * Render progress bar
   */
  const renderProgressBar = (value: number, max: number = 100, colorClass?: string) => {
    const percentage = (value / max) * 100;
    const defaultColor = value >= 70 ? 'bg-green-500' : value >= 40 ? 'bg-yellow-500' : 'bg-red-500';
    
    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${colorClass || defaultColor}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Authenticity Check */}
      {analysis.authenticityCheck && renderSection(
        'Figure Authenticity Detection',
        <Shield className="w-5 h-5 text-red-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Manipulation Likelihood</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                    getRiskColor(analysis.authenticityCheck.manipulationLikelihood)
                  }`}>
                    {analysis.authenticityCheck.manipulationLikelihood}%
                  </span>
                </div>
                {renderProgressBar(analysis.authenticityCheck.manipulationLikelihood, 100, 
                  analysis.authenticityCheck.manipulationLikelihood >= 60 ? 'bg-red-500' : 'bg-green-500')}
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Analysis Confidence</span>
                  <span className="text-sm font-bold">{analysis.authenticityCheck.confidence}%</span>
                </div>
                {renderProgressBar(analysis.authenticityCheck.confidence)}
              </div>
            </div>

            {analysis.authenticityCheck.suspiciousAreas.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Suspicious Areas Detected</h4>
                <div className="space-y-2">
                  {analysis.authenticityCheck.suspiciousAreas.map((area, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg border border-red-200">
                      <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-red-800">{area.type} detected</p>
                        <p className="text-xs text-red-600 mt-1">{area.description}</p>
                        <p className="text-xs text-red-500 mt-1">Confidence: {area.confidence}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800 font-medium">AI Recommendation:</p>
              <p className="text-sm text-blue-700 mt-1">{analysis.authenticityCheck.recommendation}</p>
            </div>
          </div>
        ),
        'authenticity',
        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
          getRiskColor(analysis.authenticityCheck.manipulationLikelihood)
        }`}>
          {analysis.authenticityCheck.manipulationLikelihood < 30 ? 'Authentic' : 
           analysis.authenticityCheck.manipulationLikelihood < 70 ? 'Questionable' : 'High Risk'}
        </span>
      )}

      {/* Cross-Reference Analysis */}
      {analysis.crossReference && renderSection(
        'Cross-Reference & Novelty Analysis',
        <Search className="w-5 h-5 text-blue-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Novelty Score</span>
                  <span className="text-sm font-bold">{analysis.crossReference.noveltyScore}/100</span>
                </div>
                {renderProgressBar(analysis.crossReference.noveltyScore)}
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Similar Figures Found:</span>
                <span className="text-sm font-medium">{analysis.crossReference.similarFigures.length}</span>
              </div>
            </div>

            {analysis.crossReference.similarFigures.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Similar Figures in Literature</h4>
                <div className="space-y-2">
                  {analysis.crossReference.similarFigures.slice(0, 3).map((figure, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{figure.source}</p>
                        <p className="text-xs text-gray-600">Similarity: {figure.similarity}% | Type: {figure.type}</p>
                      </div>
                      {figure.url && (
                        <button
                          onClick={() => window.open(figure.url, '_blank')}
                          className="p-1 text-blue-500 hover:text-blue-700"
                          title="View source"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analysis.crossReference.potentialDuplicates.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <h4 className="font-medium text-yellow-800 mb-2">⚠️ Potential Duplicates Detected</h4>
                {analysis.crossReference.potentialDuplicates.map((duplicate, index) => (
                  <div key={index} className="text-sm text-yellow-700">
                    <p className="font-medium">{duplicate.source}</p>
                    <p className="text-xs mt-1">{duplicate.recommendation}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        ),
        'crossReference',
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          analysis.crossReference.noveltyScore >= 70 ? 'bg-green-100 text-green-800' :
          analysis.crossReference.noveltyScore >= 40 ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {analysis.crossReference.noveltyScore >= 70 ? 'Novel' :
           analysis.crossReference.noveltyScore >= 40 ? 'Moderate' : 'Low Novelty'}
        </span>
      )}

      {/* Statistical Validation */}
      {analysis.statisticalValidation && renderSection(
        'Statistical Validation',
        <BarChart3 className="w-5 h-5 text-green-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Contains Statistics:</span>
                {analysis.statisticalValidation.hasStatistics ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-gray-500" />
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Claims Found:</span>
                <span className="text-sm font-medium">{analysis.statisticalValidation.statisticalClaims.length}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overall Validity:</span>
                <span className={`text-sm font-medium ${
                  analysis.statisticalValidation.overallValidity === 'valid' ? 'text-green-600' :
                  analysis.statisticalValidation.overallValidity === 'questionable' ? 'text-yellow-600' :
                  'text-red-600'
                }`}>
                  {analysis.statisticalValidation.overallValidity}
                </span>
              </div>
            </div>

            {analysis.statisticalValidation.statisticalClaims.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Statistical Claims Analysis</h4>
                <div className="space-y-2">
                  {analysis.statisticalValidation.statisticalClaims.map((claim, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{claim.claim}</p>
                          <p className="text-xs text-gray-600 mt-1">Type: {claim.type}</p>
                        </div>
                        <span className="text-xs font-medium text-gray-700">{claim.confidence}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ),
        'statistical',
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          analysis.statisticalValidation.overallValidity === 'valid' ? 'bg-green-100 text-green-800' :
          analysis.statisticalValidation.overallValidity === 'questionable' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {analysis.statisticalValidation.overallValidity}
        </span>
      )}

      {/* Bias Detection */}
      {analysis.biasDetection && renderSection(
        'Bias Detection & Neutrality Analysis',
        <Eye className="w-5 h-5 text-purple-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Bias Score</span>
                  <span className="text-sm font-bold">{analysis.biasDetection.overallBiasScore}/100</span>
                </div>
                {renderProgressBar(100 - analysis.biasDetection.overallBiasScore, 100, 
                  analysis.biasDetection.overallBiasScore <= 30 ? 'bg-green-500' : 
                  analysis.biasDetection.overallBiasScore <= 60 ? 'bg-yellow-500' : 'bg-red-500')}
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Neutrality Score</span>
                  <span className="text-sm font-bold">{analysis.biasDetection.neutralityScore}/100</span>
                </div>
                {renderProgressBar(analysis.biasDetection.neutralityScore)}
              </div>
            </div>

            {analysis.biasDetection.detectedBiases.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Detected Biases</h4>
                <div className="space-y-2">
                  {analysis.biasDetection.detectedBiases.map((bias, index) => (
                    <div key={index} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="flex items-start justify-between mb-2">
                        <p className="text-sm font-medium text-orange-800">{bias.type} bias</p>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          bias.severity === 'high' ? 'bg-red-100 text-red-800' :
                          bias.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {bias.severity}
                        </span>
                      </div>
                      <p className="text-sm text-orange-700">{bias.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ),
        'bias',
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          analysis.biasDetection.overallBiasScore <= 30 ? 'bg-green-100 text-green-800' :
          analysis.biasDetection.overallBiasScore <= 60 ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {analysis.biasDetection.overallBiasScore <= 30 ? 'Low Bias' :
           analysis.biasDetection.overallBiasScore <= 60 ? 'Moderate Bias' : 'High Bias'}
        </span>
      )}

      {/* Impact Prediction */}
      {analysis.impactPrediction && renderSection(
        'Research Impact Prediction',
        <Award className="w-5 h-5 text-yellow-500" />,
        (
          <div className="space-y-4">
            <div className="text-center">
              <div className={`inline-flex items-center px-4 py-2 rounded-lg font-medium ${
                getImpactColor(analysis.impactPrediction.overallImpact)
              }`}>
                <TrendingUp className="w-5 h-5 mr-2" />
                {analysis.impactPrediction.overallImpact.replace('-', ' ').toUpperCase()} IMPACT
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{analysis.impactPrediction.citationPotential}</div>
                <div className="text-xs text-gray-600">Citation Potential</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{analysis.impactPrediction.visualAppeal}</div>
                <div className="text-xs text-gray-600">Visual Appeal</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{analysis.impactPrediction.clarity}</div>
                <div className="text-xs text-gray-600">Clarity</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{analysis.impactPrediction.novelty}</div>
                <div className="text-xs text-gray-600">Novelty</div>
              </div>
            </div>

            {analysis.impactPrediction.factors.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Impact Factors</h4>
                <div className="space-y-2">
                  {analysis.impactPrediction.factors.map((factor, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex-1">
                        <span className="text-sm font-medium text-gray-900">{factor.name}</span>
                        <p className="text-xs text-gray-600">{factor.description}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-bold text-gray-900">{factor.score}</div>
                        <div className="text-xs text-gray-500">Weight: {factor.weight}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ),
        'impact',
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          getImpactColor(analysis.impactPrediction.overallImpact)
        }`}>
          {analysis.impactPrediction.overallImpact.replace('-', ' ')}
        </span>
      )}

      {/* Methodology Analysis */}
      {analysis.methodologyExtraction && renderSection(
        'Methodology & Best Practices Analysis',
        <Microscope className="w-5 h-5 text-indigo-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-700">Visualization Type:</span>
                <p className="text-sm text-gray-900 mt-1">{analysis.methodologyExtraction.dataVisualizationType}</p>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Appropriateness</span>
                  <span className="text-sm font-bold">{analysis.methodologyExtraction.appropriateness}/100</span>
                </div>
                {renderProgressBar(analysis.methodologyExtraction.appropriateness)}
              </div>
            </div>

            {analysis.methodologyExtraction.detectedMethods.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Detected Methods</h4>
                <div className="space-y-2">
                  {analysis.methodologyExtraction.detectedMethods.map((method, index) => (
                    <div key={index} className="p-3 bg-indigo-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-indigo-900">{method.name}</span>
                        <span className="text-xs text-indigo-700">{method.confidence}% confidence</span>
                      </div>
                      <p className="text-sm text-indigo-800">{method.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analysis.methodologyExtraction.alternatives.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Alternative Methods</h4>
                <div className="space-y-2">
                  {analysis.methodologyExtraction.alternatives.slice(0, 3).map((alt, index) => (
                    <div key={index} className="p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-green-900">{alt.name}</span>
                        <span className="text-xs text-green-700">Suitability: {alt.suitability}%</span>
                      </div>
                      <p className="text-sm text-green-800">{alt.implementation}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ),
        'methodology'
      )}

      {/* Data Extraction */}
      {analysis.dataExtraction && renderSection(
        'Data Extraction & Structure Analysis',
        <Database className="w-5 h-5 text-cyan-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Data Type:</span>
                <span className="text-sm font-medium">{analysis.dataExtraction.dataType}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Data Points:</span>
                <span className="text-sm font-medium">{analysis.dataExtraction.extractedData.length}</span>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Confidence</span>
                  <span className="text-sm font-bold">{analysis.dataExtraction.confidence}%</span>
                </div>
                {renderProgressBar(analysis.dataExtraction.confidence)}
              </div>
            </div>

            {analysis.dataExtraction.structure.axes.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Detected Structure</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Axes</h5>
                    {analysis.dataExtraction.structure.axes.map((axis, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {axis.type.toUpperCase()}: {axis.label} ({axis.scale})
                      </div>
                    ))}
                  </div>
                  
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-1">Data Series</h5>
                    {analysis.dataExtraction.structure.series.map((series, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {series.name}: {series.dataPoints} points ({series.type})
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ),
        'dataExtraction'
      )}
    </div>
  );
};
