import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Edit, 
  Save, 
  X, 
  Plus, 
  Trash2, 
  GripVertical,
  FileText,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { GeneratedOutline, GeneratedOutlineSection } from '../types';

interface OutlineEditorProps {
  outline: GeneratedOutline;
  chapterIndex: number;
  onSave: (updatedOutline: GeneratedOutline) => void;
  onRegenerate: () => void;
  isRegenerating?: boolean;
}

export const OutlineEditor: React.FC<OutlineEditorProps> = ({
  outline,
  chapterIndex,
  onSave,
  onRegenerate,
  isRegenerating = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedOutline, setEditedOutline] = useState<GeneratedOutline>(outline);

  const handleStartEdit = () => {
    setEditedOutline({ ...outline });
    setIsEditing(true);
  };

  const handleSave = () => {
    onSave(editedOutline);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedOutline(outline);
    setIsEditing(false);
  };

  const updateOutlineField = (field: keyof GeneratedOutline, value: any) => {
    setEditedOutline(prev => ({ ...prev, [field]: value }));
  };

  const addSection = () => {
    const newSection: GeneratedOutlineSection = {
      id: `section-${Date.now()}`,
      title: 'New Section',
      description: 'Section description...',
      level: 1,
      order: editedOutline.sections.length + 1,
      estimatedWordCount: 500,
      keyPoints: []
    };

    setEditedOutline(prev => ({
      ...prev,
      sections: [...prev.sections, newSection]
    }));
  };

  const updateSection = (sectionId: string, updates: Partial<GeneratedOutlineSection>) => {
    setEditedOutline(prev => ({
      ...prev,
      sections: prev.sections.map(section =>
        section.id === sectionId ? { ...section, ...updates } : section
      )
    }));
  };

  const removeSection = (sectionId: string) => {
    setEditedOutline(prev => ({
      ...prev,
      sections: prev.sections.filter(section => section.id !== sectionId)
    }));
  };

  const moveSection = (sectionId: string, direction: 'up' | 'down') => {
    const sections = [...editedOutline.sections];
    const index = sections.findIndex(s => s.id === sectionId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= sections.length) return;
    
    [sections[index], sections[newIndex]] = [sections[newIndex], sections[index]];
    
    // Update order values
    sections.forEach((section, idx) => {
      section.order = idx + 1;
    });
    
    setEditedOutline(prev => ({ ...prev, sections }));
  };

  const addKeyPoint = (sectionId: string, keyPoint: string) => {
    if (!keyPoint.trim()) return;
    
    updateSection(sectionId, {
      keyPoints: [...(editedOutline.sections.find(s => s.id === sectionId)?.keyPoints || []), keyPoint.trim()]
    });
  };

  const removeKeyPoint = (sectionId: string, pointIndex: number) => {
    const section = editedOutline.sections.find(s => s.id === sectionId);
    if (!section) return;
    
    const newKeyPoints = section.keyPoints.filter((_, index) => index !== pointIndex);
    updateSection(sectionId, { keyPoints: newKeyPoints });
  };

  const totalEstimatedWords = editedOutline.sections.reduce((sum, section) => sum + section.estimatedWordCount, 0);

  return (
    <Card className="shadow-lg border-2 border-blue-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-600" />
            Chapter {chapterIndex + 1}: {isEditing ? editedOutline.title : outline.title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {totalEstimatedWords.toLocaleString()} words
            </Badge>
            <Badge 
              variant={outline.status === 'approved' ? 'default' : 'secondary'} 
              className="text-sm"
            >
              {outline.status === 'approved' ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Approved
                </>
              ) : (
                <>
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Needs Review
                </>
              )}
            </Badge>
            {!isEditing && (
              <div className="flex gap-2">
                <Button onClick={handleStartEdit} variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button 
                  onClick={onRegenerate} 
                  variant="outline" 
                  size="sm"
                  disabled={isRegenerating}
                >
                  <RefreshCw className={`h-4 w-4 mr-1 ${isRegenerating ? 'animate-spin' : ''}`} />
                  Regenerate
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Chapter Overview */}
        <div className="space-y-4">
          <h4 className="font-semibold">Chapter Overview</h4>
          
          {isEditing ? (
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium mb-1 block">Chapter Title</label>
                <Input
                  value={editedOutline.title}
                  onChange={(e) => updateOutlineField('title', e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">Chapter Description</label>
                <Textarea
                  value={editedOutline.description}
                  onChange={(e) => updateOutlineField('description', e.target.value)}
                  rows={3}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">Key Points (comma-separated)</label>
                <Input
                  value={editedOutline.keyPoints.join(', ')}
                  onChange={(e) => updateOutlineField('keyPoints', e.target.value.split(',').map(p => p.trim()).filter(p => p))}
                  placeholder="Main takeaway 1, Main takeaway 2, ..."
                />
              </div>
            </div>
          ) : (
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-700 mb-2">{outline.description}</p>
              <div className="flex flex-wrap gap-1">
                {outline.keyPoints.map((point, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {point}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sections */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold">Chapter Sections</h4>
            {isEditing && (
              <Button onClick={addSection} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Section
              </Button>
            )}
          </div>

          <div className="space-y-3">
            {(isEditing ? editedOutline.sections : outline.sections).map((section, index) => (
              <div key={section.id} className="border rounded-lg p-4 bg-gray-50">
                {isEditing ? (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-4 w-4 text-gray-400" />
                      <Input
                        value={section.title}
                        onChange={(e) => updateSection(section.id, { title: e.target.value })}
                        className="font-medium"
                      />
                      <Input
                        type="number"
                        value={section.estimatedWordCount}
                        onChange={(e) => updateSection(section.id, { estimatedWordCount: parseInt(e.target.value) || 0 })}
                        className="w-24"
                        min="100"
                        max="2000"
                      />
                      <div className="flex gap-1">
                        <Button
                          onClick={() => moveSection(section.id, 'up')}
                          variant="ghost"
                          size="sm"
                          disabled={index === 0}
                        >
                          ↑
                        </Button>
                        <Button
                          onClick={() => moveSection(section.id, 'down')}
                          variant="ghost"
                          size="sm"
                          disabled={index === editedOutline.sections.length - 1}
                        >
                          ↓
                        </Button>
                        <Button
                          onClick={() => removeSection(section.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Textarea
                      value={section.description}
                      onChange={(e) => updateSection(section.id, { description: e.target.value })}
                      placeholder="Describe what this section covers..."
                      rows={2}
                    />
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Key Points</label>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {section.keyPoints.map((point, pointIndex) => (
                          <Badge key={pointIndex} variant="outline" className="text-xs">
                            {point}
                            <X 
                              className="h-3 w-3 ml-1 cursor-pointer hover:text-red-500" 
                              onClick={() => removeKeyPoint(section.id, pointIndex)}
                            />
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add key point..."
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              addKeyPoint(section.id, e.currentTarget.value);
                              e.currentTarget.value = '';
                            }
                          }}
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">{section.order}. {section.title}</div>
                      <Badge variant="outline" className="text-xs">
                        {section.estimatedWordCount} words
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{section.description}</div>
                    {section.keyPoints.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {section.keyPoints.map((point, pointIndex) => (
                          <Badge key={pointIndex} variant="outline" className="text-xs">
                            {point}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className="flex gap-3 pt-4 border-t">
            <Button onClick={handleSave} size="lg">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            <Button onClick={handleCancel} variant="outline" size="lg">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
