/**
 * Research Planning Service
 * Handles intelligent research planning, type selection, and outline generation
 */

import { 
  ResearchType, 
  ResearchTypeTemplate, 
  ResearchPlan, 
  DeepResearchOutline,
  ResearchPoint,
  ResearchSubpoint
} from '../types';

export class ResearchPlanningService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
  }

  /**
   * Get available research type templates
   */
  getResearchTypeTemplates(): ResearchTypeTemplate[] {
    return [
      {
        id: 'quick_research',
        name: 'Quick Research',
        description: 'Fast overview with key insights and basic citations',
        icon: 'Search',
        color: 'blue',
        examples: ['Market overview', 'Technology introduction', 'Basic concept explanation'],
        config: {
          id: 'quick_research',
          name: 'Quick Research',
          description: 'Rapid research for basic understanding',
          minSections: 3,
          maxSections: 6,
          minWordsPerSection: 300,
          maxWordsPerSection: 600,
          totalWordTarget: 2000,
          requiredSections: ['Introduction', 'Key Findings', 'Conclusion'],
          optionalSections: ['Background', 'Current Trends', 'Implications'],
          citationDensity: 'light',
          academicLevel: 'undergraduate'
        }
      },
      {
        id: 'literature_review',
        name: 'Literature Review',
        description: 'Comprehensive review of existing academic literature with extensive citations',
        icon: 'BookOpen',
        color: 'green',
        examples: ['Systematic literature review', 'Meta-analysis', 'Research gap analysis'],
        config: {
          id: 'literature_review',
          name: 'Literature Review',
          description: 'Systematic review of academic literature with 20+ citations per section',
          minSections: 6,
          maxSections: 12,
          minWordsPerSection: 1000,
          maxWordsPerSection: 1500,
          totalWordTarget: 10000,
          requiredSections: ['Abstract', 'Introduction', 'Methodology', 'Literature Analysis', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Research Gaps', 'Future Directions', 'Limitations'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'research_paper',
        name: 'Research Paper',
        description: 'Full academic research paper with methodology and analysis',
        icon: 'FileText',
        color: 'purple',
        examples: ['Empirical study', 'Case study analysis', 'Experimental research'],
        config: {
          id: 'research_paper',
          name: 'Research Paper',
          description: 'Complete academic research paper',
          minSections: 8,
          maxSections: 15,
          minWordsPerSection: 1000,
          maxWordsPerSection: 2000,
          totalWordTarget: 12000,
          requiredSections: ['Abstract', 'Introduction', 'Literature Review', 'Methodology', 'Results', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Data Analysis', 'Limitations', 'Future Work', 'Acknowledgments', 'Appendices'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'academic_book',
        name: 'Academic Book',
        description: 'Comprehensive book-length academic work (50,000+ words)',
        icon: 'Book',
        color: 'orange',
        examples: ['Textbook chapter', 'Monograph', 'Comprehensive guide'],
        config: {
          id: 'academic_book',
          name: 'Academic Book',
          description: 'Book-length comprehensive academic work',
          minSections: 15,
          maxSections: 30,
          minWordsPerSection: 2000,
          maxWordsPerSection: 4000,
          totalWordTarget: 50000,
          requiredSections: ['Preface', 'Introduction', 'Literature Review', 'Theoretical Framework', 'Methodology', 'Analysis', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Abstract', 'Executive Summary', 'Background', 'Case Studies', 'Applications', 'Future Directions', 'Glossary', 'Index', 'Appendices'],
          citationDensity: 'heavy',
          academicLevel: 'doctoral'
        }
      },
      {
        id: 'systematic_review',
        name: 'Systematic Review',
        description: 'Comprehensive systematic review following PRISMA guidelines with meta-analysis',
        icon: 'BookOpen',
        color: 'teal',
        examples: ['PRISMA systematic review', 'Cochrane review', 'Meta-analysis study'],
        config: {
          id: 'systematic_review',
          name: 'Systematic Review',
          description: 'Systematic review following PRISMA methodology with comprehensive analysis',
          minSections: 8,
          maxSections: 15,
          minWordsPerSection: 1200,
          maxWordsPerSection: 2000,
          totalWordTarget: 15000,
          requiredSections: ['Abstract', 'Introduction', 'Methods', 'Search Strategy', 'Study Selection', 'Data Extraction', 'Results', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'PRISMA Checklist', 'Quality Assessment', 'Meta-Analysis', 'Subgroup Analysis', 'Sensitivity Analysis', 'Limitations', 'Implications'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'narrative_review',
        name: 'Narrative Review',
        description: 'Comprehensive narrative review article with thematic analysis and synthesis',
        icon: 'FileText',
        color: 'cyan',
        examples: ['Narrative literature review', 'Thematic review', 'Conceptual review'],
        config: {
          id: 'narrative_review',
          name: 'Narrative Review',
          description: 'Comprehensive narrative review with thematic synthesis',
          minSections: 7,
          maxSections: 12,
          minWordsPerSection: 1000,
          maxWordsPerSection: 1800,
          totalWordTarget: 12000,
          requiredSections: ['Abstract', 'Introduction', 'Search Strategy', 'Thematic Analysis', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Key Themes', 'Synthesis', 'Implications', 'Future Directions', 'Limitations'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'scoping_review',
        name: 'Scoping Review',
        description: 'Scoping review to map research landscape and identify knowledge gaps',
        icon: 'Search',
        color: 'violet',
        examples: ['Scoping study', 'Mapping review', 'Evidence mapping'],
        config: {
          id: 'scoping_review',
          name: 'Scoping Review',
          description: 'Scoping review to map existing literature and identify research gaps',
          minSections: 6,
          maxSections: 10,
          minWordsPerSection: 1000,
          maxWordsPerSection: 1500,
          totalWordTarget: 10000,
          requiredSections: ['Abstract', 'Introduction', 'Methods', 'Results', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Search Strategy', 'Data Charting', 'Knowledge Gaps', 'Implications', 'Limitations'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'integrative_review',
        name: 'Integrative Review',
        description: 'Integrative review combining diverse methodologies and evidence types',
        icon: 'BookMarked',
        color: 'rose',
        examples: ['Mixed-methods review', 'Integrative analysis', 'Multi-perspective review'],
        config: {
          id: 'integrative_review',
          name: 'Integrative Review',
          description: 'Integrative review synthesizing diverse research methodologies',
          minSections: 7,
          maxSections: 12,
          minWordsPerSection: 1100,
          maxWordsPerSection: 1600,
          totalWordTarget: 11000,
          requiredSections: ['Abstract', 'Introduction', 'Methods', 'Literature Analysis', 'Integration', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Quality Assessment', 'Synthesis', 'Implications', 'Limitations'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'policy_brief',
        name: 'Policy Brief',
        description: 'Government and policy-focused research with official sources',
        icon: 'Shield',
        color: 'red',
        examples: ['Policy analysis', 'Government report', 'Regulatory review'],
        config: {
          id: 'policy_brief',
          name: 'Policy Brief',
          description: 'Policy-focused research with government sources',
          minSections: 5,
          maxSections: 10,
          minWordsPerSection: 600,
          maxWordsPerSection: 1200,
          totalWordTarget: 6000,
          requiredSections: ['Executive Summary', 'Background', 'Policy Analysis', 'Recommendations', 'References'],
          optionalSections: ['Current Status', 'Stakeholder Analysis', 'Implementation', 'Cost-Benefit Analysis', 'Risk Assessment'],
          citationDensity: 'moderate',
          academicLevel: 'professional'
        }
      },
      {
        id: 'complete_report',
        name: 'Complete Research Report',
        description: 'Comprehensive research report with extensive analysis and 100+ citations',
        icon: 'FileText',
        color: 'indigo',
        examples: ['Technical report', 'Industry analysis', 'Comprehensive study'],
        config: {
          id: 'complete_report',
          name: 'Complete Research Report',
          description: 'Comprehensive research report with detailed analysis',
          minSections: 8,
          maxSections: 15,
          minWordsPerSection: 1000,
          maxWordsPerSection: 1500,
          totalWordTarget: 12000,
          requiredSections: ['Executive Summary', 'Introduction', 'Literature Review', 'Methodology', 'Analysis', 'Results', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Case Studies', 'Implications', 'Limitations', 'Future Research'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'academic_article',
        name: 'Academic Article',
        description: 'Publication-ready academic article with journal-quality citations',
        icon: 'GraduationCap',
        color: 'emerald',
        examples: ['Journal article', 'Conference paper', 'Academic publication'],
        config: {
          id: 'academic_article',
          name: 'Academic Article',
          description: 'Publication-ready academic article',
          minSections: 7,
          maxSections: 12,
          minWordsPerSection: 1000,
          maxWordsPerSection: 1500,
          totalWordTarget: 8000,
          requiredSections: ['Abstract', 'Introduction', 'Literature Review', 'Methodology', 'Results', 'Discussion', 'Conclusion', 'References'],
          optionalSections: ['Background', 'Theoretical Framework', 'Implications', 'Limitations', 'Future Work'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      },
      {
        id: 'research_plan',
        name: 'Research Plan',
        description: 'Detailed research proposal and planning document',
        icon: 'Target',
        color: 'amber',
        examples: ['Research proposal', 'Grant application', 'Study design'],
        config: {
          id: 'research_plan',
          name: 'Research Plan',
          description: 'Comprehensive research planning document',
          minSections: 6,
          maxSections: 10,
          minWordsPerSection: 800,
          maxWordsPerSection: 1200,
          totalWordTarget: 7000,
          requiredSections: ['Abstract', 'Background', 'Literature Review', 'Methodology', 'Timeline', 'References'],
          optionalSections: ['Objectives', 'Theoretical Framework', 'Budget', 'Risk Assessment', 'Expected Outcomes'],
          citationDensity: 'heavy',
          academicLevel: 'graduate'
        }
      }
    ];
  }

  /**
   * Analyze query and suggest appropriate research type
   */
  async analyzeQueryAndSuggestType(query: string, model: string): Promise<ResearchTypeTemplate[]> {
    try {
      const templates = this.getResearchTypeTemplates();
      const prompt = this.buildTypeAnalysisPrompt(query, templates);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Research Planning Platform'
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'system',
              content: this.getTypeAnalysisSystemPrompt()
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 2000,
          temperature: 0.3,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`AI API error: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse = data.choices?.[0]?.message?.content || '';

      // Parse AI response to get recommended types
      return this.parseTypeRecommendations(aiResponse, templates);

    } catch (error) {
      console.error('Error analyzing query for research type:', error);
      // Return default suggestions
      return this.getResearchTypeTemplates().slice(0, 3);
    }
  }

  /**
   * Generate intelligent outline based on research type and query
   */
  async generateIntelligentOutline(
    query: string, 
    researchType: ResearchType, 
    model: string
  ): Promise<DeepResearchOutline> {
    try {
      const prompt = this.buildIntelligentOutlinePrompt(query, researchType);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Research Planning Platform'
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'system',
              content: this.getIntelligentOutlineSystemPrompt(researchType)
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 6000,
          temperature: 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`AI API error: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse = data.choices?.[0]?.message?.content || '';

      return this.parseIntelligentOutlineResponse(aiResponse, query, researchType);

    } catch (error) {
      console.error('Error generating intelligent outline:', error);
      return this.createFallbackOutline(query, researchType);
    }
  }

  /**
   * Build type analysis prompt
   */
  private buildTypeAnalysisPrompt(query: string, templates: ResearchTypeTemplate[]): string {
    return `Analyze this research query and recommend the most appropriate research types:

Query: "${query}"

Available Research Types:
${templates.map(t => `
${t.name}: ${t.description}
- Word Target: ${t.config.totalWordTarget}
- Academic Level: ${t.config.academicLevel}
- Examples: ${t.examples.join(', ')}
`).join('\n')}

Recommend the top 3 most suitable research types for this query, considering:
1. Query complexity and scope
2. Required depth of analysis
3. Expected word count
4. Academic rigor needed
5. Time investment

Respond with JSON:
{
  "recommendations": [
    {
      "typeId": "research_type_id",
      "confidence": 0.95,
      "reasoning": "Why this type fits the query"
    }
  ]
}`;
  }

  /**
   * Parse type recommendations from AI response
   */
  private parseTypeRecommendations(response: string, templates: ResearchTypeTemplate[]): ResearchTypeTemplate[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return templates.slice(0, 3);
      }

      const parsed = JSON.parse(jsonMatch[0]);
      const recommendations = parsed.recommendations || [];

      return recommendations
        .map((rec: any) => templates.find(t => t.id === rec.typeId))
        .filter(Boolean)
        .slice(0, 3);

    } catch (error) {
      console.error('Failed to parse type recommendations:', error);
      return templates.slice(0, 3);
    }
  }

  /**
   * Get type analysis system prompt
   */
  private getTypeAnalysisSystemPrompt(): string {
    return `You are an expert research planning assistant. Your role is to analyze research queries and recommend the most appropriate research methodology and structure.

You excel at:
1. Understanding research scope and complexity
2. Matching queries to appropriate academic formats
3. Estimating research requirements
4. Recommending optimal research approaches

Always respond with properly formatted JSON.`;
  }

  /**
   * Build intelligent outline prompt
   */
  private buildIntelligentOutlinePrompt(query: string, researchType: ResearchType): string {
    return `Create an intelligent research outline for this ${researchType.name}:

Query: "${query}"

Research Type Requirements:
- Sections: ${researchType.minSections}-${researchType.maxSections}
- Words per section: ${researchType.minWordsPerSection}-${researchType.maxWordsPerSection}
- Total target: ${researchType.totalWordTarget} words
- Academic level: ${researchType.academicLevel}
- Citation density: ${researchType.citationDensity}

Required Sections: ${researchType.requiredSections.join(', ')}
Optional Sections: ${researchType.optionalSections.join(', ')}

CRITICAL REQUIREMENTS FOR SECTION TITLES:
1. DO NOT use generic titles like "Point 1", "Section 1", "Part A", "Chapter 1"
2. Each section title must be SPECIFIC and directly related to "${query}"
3. Use descriptive, academic titles that clearly indicate the content
4. Avoid numbered or generic prefixes

EXAMPLE OF GOOD SECTION TITLES (for AI in Healthcare):
- "Current Applications of Machine Learning in Medical Diagnosis"
- "Ethical Considerations in AI-Driven Patient Care"
- "Integration Challenges in Hospital Information Systems"

EXAMPLE OF BAD SECTION TITLES (AVOID THESE):
- "Point 1: Overview"
- "Section 2: Analysis"
- "Part 3: Discussion"
- "Introduction"
- "Background"

Create a logical, comprehensive outline that:
1. Covers the topic thoroughly with SPECIFIC section titles
2. Follows academic standards for ${researchType.name}
3. Includes appropriate number of sections
4. Has 2-4 subsections per main section with specific titles
5. Builds knowledge progressively
6. Includes proper academic structure

Format as JSON with sections array containing title, description, and subsections. Ensure ALL titles are specific to "${query}".`;
  }

  /**
   * Parse intelligent outline response
   */
  private parseIntelligentOutlineResponse(
    response: string, 
    query: string, 
    researchType: ResearchType
  ): DeepResearchOutline {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in outline response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      const sections = parsed.sections || parsed.points || [];

      const points: ResearchPoint[] = sections.map((section: any, index: number) => ({
        id: `point_${Date.now()}_${index}`,
        pointNumber: index + 1,
        title: section.title,
        description: section.description,
        status: 'pending' as const,
        subpoints: (section.subsections || section.subpoints || []).map((sub: any, subIndex: number) => ({
          id: `subpoint_${Date.now()}_${index}_${subIndex}`,
          subpointNumber: subIndex + 1,
          title: sub.title,
          description: sub.description
        }))
      }));

      return {
        id: `outline_${Date.now()}`,
        title: parsed.title || `${researchType.name}: ${query}`,
        description: parsed.description || `Comprehensive ${researchType.name.toLowerCase()} on ${query}`,
        researchType,
        estimatedWordCount: researchType.totalWordTarget,
        estimatedTimeHours: Math.ceil(researchType.totalWordTarget / 500), // Rough estimate
        points,
        totalPoints: points.length,
        createdAt: new Date(),
        abstract: parsed.abstract,
        keywords: parsed.keywords || [],
        targetAudience: parsed.targetAudience || researchType.academicLevel
      };

    } catch (error) {
      console.error('Failed to parse intelligent outline:', error);
      return this.createFallbackOutline(query, researchType);
    }
  }

  /**
   * Create fallback outline if parsing fails
   */
  private createFallbackOutline(query: string, researchType: ResearchType): DeepResearchOutline {
    const sections = Math.min(researchType.maxSections, Math.max(researchType.minSections, 8));
    
    const points: ResearchPoint[] = researchType.requiredSections.slice(0, sections).map((sectionName, index) => ({
      id: `point_${Date.now()}_${index}`,
      pointNumber: index + 1,
      title: sectionName,
      description: `Comprehensive analysis of ${sectionName.toLowerCase()} for ${query}`,
      status: 'pending' as const,
      subpoints: [
        {
          id: `sub_${Date.now()}_${index}_1`,
          subpointNumber: 1,
          title: `Key Concepts`,
          description: `Fundamental concepts and definitions`
        },
        {
          id: `sub_${Date.now()}_${index}_2`,
          subpointNumber: 2,
          title: `Current Research`,
          description: `Latest research and findings`
        }
      ]
    }));

    return {
      id: `outline_${Date.now()}`,
      title: `${researchType.name}: ${query}`,
      description: `Comprehensive ${researchType.name.toLowerCase()} covering ${query}`,
      researchType,
      estimatedWordCount: researchType.totalWordTarget,
      estimatedTimeHours: Math.ceil(researchType.totalWordTarget / 500),
      points,
      totalPoints: points.length,
      createdAt: new Date()
    };
  }

  /**
   * Get intelligent outline system prompt
   */
  private getIntelligentOutlineSystemPrompt(researchType: ResearchType): string {
    return `You are an expert academic research planner specializing in ${researchType.name} development.

Your expertise includes:
1. ${researchType.name} structure and methodology
2. Academic writing standards for ${researchType.academicLevel} level
3. Research planning and organization
4. Citation and reference requirements
5. Content depth and scope planning

Create comprehensive, well-structured outlines that meet academic standards and research objectives.

Always respond with properly formatted JSON.`;
  }
}

// Export singleton instance
export const researchPlanningService = new ResearchPlanningService();
