/**
 * Diagram History Service
 * Manages diagram storage and retrieval with Supabase integration
 */

import { supabase } from '@/lib/supabase';
import { GeneratedDiagram, DiagramInput, DiagramType } from '../types';

// Database types
export interface UserDiagram {
  id: string;
  user_id: string;
  title: string;
  description: string;
  diagram_type: DiagramType;
  direction: string;
  mermaid_code: string;
  ai_model: string;
  generation_prompt: string;
  additional_requirements?: string;
  context?: string;
  status: 'completed' | 'error' | 'generating';
  error_message?: string;
  tags: string[];
  is_favorite: boolean;
  view_count: number;
  export_count: number;
  created_at: string;
  updated_at: string;
}

export interface DiagramCollection {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  diagram_count: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface DiagramHistoryFilters {
  type?: DiagramType;
  search?: string;
  tags?: string[];
  collection_id?: string;
  is_favorite?: boolean;
  limit?: number;
  offset?: number;
}

export class DiagramHistoryService {
  private currentUserId: string | null = null;

  constructor() {
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  private async initializeAuth() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.warn('Auth session error during initialization:', error);
        this.currentUserId = null;
      } else {
        this.currentUserId = session?.user?.id || null;
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange((event, session) => {
        this.currentUserId = session?.user?.id || null;
        console.log('Auth state changed in diagram history service:', event, this.currentUserId ? 'User logged in' : 'User logged out');
      });
    } catch (error) {
      console.warn('Failed to initialize auth in diagram history service:', error);
      this.currentUserId = null;
    }
  }

  /**
   * Save a generated diagram to history
   */
  async saveDiagram(diagram: GeneratedDiagram, input: DiagramInput, model: string): Promise<{ success: boolean; error?: string; diagram?: UserDiagram }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      const diagramData = {
        user_id: user.id,
        title: diagram.metadata.title,
        description: diagram.metadata.description,
        diagram_type: diagram.metadata.type,
        direction: diagram.metadata.direction || 'TD',
        mermaid_code: diagram.mermaidCode,
        ai_model: model,
        generation_prompt: input.description,
        additional_requirements: input.additionalRequirements,
        context: input.context,
        status: 'completed' as const,
        tags: [],
        is_favorite: false,
        view_count: 0,
        export_count: 0
      };

      const { data, error } = await supabase
        .from('user_diagrams')
        .insert(diagramData)
        .select()
        .single();

      if (error) {
        console.error('Error saving diagram:', error);
        return { success: false, error: error.message };
      }

      // Log activity
      await this.logActivity(data.id, 'created', { model, type: diagram.metadata.type });

      return { success: true, diagram: data };
    } catch (error: any) {
      console.error('Error saving diagram:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's diagram history with filters
   */
  async getUserDiagrams(filters: DiagramHistoryFilters = {}): Promise<{ diagrams: UserDiagram[]; error?: string; total?: number }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { diagrams: [], error: 'User not authenticated' };
      }

      let query = supabase
        .from('user_diagrams')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id);

      // Apply filters
      if (filters.type) {
        query = query.eq('diagram_type', filters.type);
      }
      if (filters.is_favorite !== undefined) {
        query = query.eq('is_favorite', filters.is_favorite);
      }
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      // Apply pagination
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;
      query = query.range(offset, offset + limit - 1);

      // Order by creation date (newest first)
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching diagrams:', error);
        return { diagrams: [], error: error.message };
      }

      return { diagrams: data || [], total: count || 0 };
    } catch (error: any) {
      console.error('Error fetching diagrams:', error);
      return { diagrams: [], error: error.message };
    }
  }

  /**
   * Get a specific diagram by ID
   */
  async getDiagram(id: string): Promise<{ diagram?: UserDiagram; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('user_diagrams')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching diagram:', error);
        return { error: error.message };
      }

      // Increment view count
      await this.incrementViewCount(id);
      await this.logActivity(id, 'viewed');

      return { diagram: data };
    } catch (error: any) {
      console.error('Error fetching diagram:', error);
      return { error: error.message };
    }
  }

  /**
   * Update diagram metadata
   */
  async updateDiagram(id: string, updates: Partial<Pick<UserDiagram, 'title' | 'description' | 'tags' | 'is_favorite'>>): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await supabase
        .from('user_diagrams')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating diagram:', error);
        return { success: false, error: error.message };
      }

      // Log activity
      const activityType = updates.is_favorite !== undefined ? 'favorited' : 'edited';
      await this.logActivity(id, activityType, updates);

      return { success: true };
    } catch (error: any) {
      console.error('Error updating diagram:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a diagram
   */
  async deleteDiagram(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await supabase
        .from('user_diagrams')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting diagram:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      console.error('Error deleting diagram:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Toggle favorite status of a diagram
   */
  async toggleFavorite(id: string, isFavorite: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await supabase
        .from('user_diagrams')
        .update({ is_favorite: isFavorite })
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error toggling favorite:', error);
        return { success: false, error: error.message };
      }

      // Log activity
      await this.logActivity(id, isFavorite ? 'favorited' : 'unfavorited');

      return { success: true };
    } catch (error: any) {
      console.error('Error toggling favorite:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's diagram collections
   */
  async getUserCollections(): Promise<{ collections: DiagramCollection[]; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { collections: [], error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('diagram_collections')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching collections:', error);
        return { collections: [], error: error.message };
      }

      return { collections: data || [] };
    } catch (error: any) {
      console.error('Error fetching collections:', error);
      return { collections: [], error: error.message };
    }
  }

  /**
   * Log user activity
   */
  private async logActivity(diagramId: string, activityType: string, details: any = {}) {
    try {
      if (!this.currentUserId) return;

      await supabase
        .from('diagram_activity')
        .insert({
          diagram_id: diagramId,
          user_id: this.currentUserId,
          activity_type: activityType,
          activity_details: details
        });
    } catch (error) {
      console.warn('Failed to log activity:', error);
    }
  }

  /**
   * Increment view count for a diagram
   */
  private async incrementViewCount(diagramId: string) {
    try {
      await supabase.rpc('increment_diagram_view_count', { diagram_id: diagramId });
    } catch (error) {
      console.warn('Failed to increment view count:', error);
    }
  }

  /**
   * Get diagram statistics
   */
  async getDiagramStats(): Promise<{ stats: any; error?: string }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { stats: {}, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('user_diagrams')
        .select('diagram_type, status')
        .eq('user_id', user.id);

      if (error) {
        return { stats: {}, error: error.message };
      }

      const stats = {
        total: data.length,
        byType: data.reduce((acc: any, diagram) => {
          acc[diagram.diagram_type] = (acc[diagram.diagram_type] || 0) + 1;
          return acc;
        }, {}),
        byStatus: data.reduce((acc: any, diagram) => {
          acc[diagram.status] = (acc[diagram.status] || 0) + 1;
          return acc;
        }, {})
      };

      return { stats };
    } catch (error: any) {
      return { stats: {}, error: error.message };
    }
  }
}

// Export singleton instance
export const diagramHistoryService = new DiagramHistoryService();
