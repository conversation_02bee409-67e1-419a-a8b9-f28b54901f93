/**
 * Text Parser Service
 * Converts flowchart text syntax to graph data structure
 */

import { 
  FlowchartGraph, 
  FlowchartNode, 
  FlowchartEdge, 
  ParseResult, 
  ParseError, 
  ParsedLine,
  TextParserService 
} from '../types';
import { PARSER_CONFIG } from '../constants';

class TextParserServiceImpl implements TextParserService {
  
  /**
   * Parse flowchart text into graph structure
   */
  parse(text: string): ParseResult {
    try {
      const lines = this.parseLines(text);
      const errors = this.validateLines(lines);
      
      if (errors.length > 0) {
        return {
          success: false,
          errors
        };
      }

      const graph = this.buildGraph(lines);
      
      return {
        success: true,
        graph,
        errors: [],
        warnings: []
      };
    } catch (error) {
      return {
        success: false,
        errors: [{
          line: 0,
          message: error instanceof Error ? error.message : 'Unknown parsing error',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Validate syntax without full parsing
   */
  validateSyntax(text: string): ParseError[] {
    try {
      const lines = this.parseLines(text);
      return this.validateLines(lines);
    } catch (error) {
      return [{
        line: 0,
        message: error instanceof Error ? error.message : 'Syntax validation error',
        severity: 'error'
      }];
    }
  }

  /**
   * Format text with proper indentation
   */
  formatText(text: string): string {
    const lines = text.split('\n');
    const formatted: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed) {
        const indentLevel = this.getIndentLevel(line);
        const indent = ' '.repeat(indentLevel * PARSER_CONFIG.indentSize);
        formatted.push(indent + trimmed);
      } else {
        formatted.push('');
      }
    }
    
    return formatted.join('\n');
  }

  /**
   * Get node at specific line
   */
  getNodeAtLine(text: string, line: number): FlowchartNode | null {
    const lines = this.parseLines(text);
    const parsedLine = lines.find(l => l.lineNumber === line);
    
    if (!parsedLine || !parsedLine.nodeId) {
      return null;
    }

    return {
      id: parsedLine.nodeId,
      label: this.extractNodeLabel(parsedLine.content),
      data: { line: parsedLine.lineNumber }
    };
  }

  /**
   * Parse text into structured lines
   */
  private parseLines(text: string): ParsedLine[] {
    const lines = text.split('\n');
    const parsedLines: ParsedLine[] = [];
    
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      
      // Skip empty lines
      if (!trimmed) return;
      
      // Handle comments
      if (trimmed.startsWith(PARSER_CONFIG.commentPrefix)) {
        parsedLines.push({
          content: trimmed,
          indentLevel: 0,
          lineNumber: index + 1,
          isComment: true
        });
        return;
      }

      const indentLevel = this.getIndentLevel(line);
      const nodeId = this.generateNodeId(trimmed, index);
      const connections = this.extractConnections(trimmed);
      
      parsedLines.push({
        content: trimmed,
        indentLevel,
        lineNumber: index + 1,
        nodeId,
        connections
      });
    });
    
    return parsedLines;
  }

  /**
   * Get indentation level of a line
   */
  private getIndentLevel(line: string): number {
    const match = line.match(/^(\s*)/);
    const spaces = match ? match[1].length : 0;
    return Math.floor(spaces / PARSER_CONFIG.indentSize);
  }

  /**
   * Generate unique node ID
   */
  private generateNodeId(content: string, lineIndex: number): string {
    const label = this.extractNodeLabel(content);
    const sanitized = label.toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    
    return `${PARSER_CONFIG.nodeIdPrefix}${sanitized}_${lineIndex}`;
  }

  /**
   * Extract node label from content
   */
  private extractNodeLabel(content: string): string {
    // Remove connection keywords and extract the main label
    const parts = content.split(':');
    return parts[0].trim();
  }

  /**
   * Extract connections from content
   */
  private extractConnections(content: string): string[] {
    const connections: string[] = [];
    
    // Look for connection patterns
    const parts = content.split(':');
    if (parts.length > 1) {
      const connectionPart = parts.slice(1).join(':').trim();
      
      // Split by connection keywords
      for (const keyword of PARSER_CONFIG.connectionKeywords) {
        if (connectionPart.includes(keyword)) {
          const targets = connectionPart.split(keyword)
            .map(t => t.trim())
            .filter(t => t.length > 0);
          connections.push(...targets);
        }
      }
      
      // If no keywords found, treat the whole part as a connection
      if (connections.length === 0 && connectionPart) {
        connections.push(connectionPart);
      }
    }
    
    return connections;
  }

  /**
   * Validate parsed lines
   */
  private validateLines(lines: ParsedLine[]): ParseError[] {
    const errors: ParseError[] = [];
    const nodeCount = lines.filter(l => !l.isComment).length;
    
    // Check node limit
    if (nodeCount > PARSER_CONFIG.maxNodes) {
      errors.push({
        line: 0,
        message: `Too many nodes (${nodeCount}). Maximum allowed: ${PARSER_CONFIG.maxNodes}`,
        severity: 'error'
      });
    }

    // Check indentation consistency
    let prevIndent = 0;
    for (const line of lines) {
      if (line.isComment) continue;
      
      if (line.indentLevel > prevIndent + 1) {
        errors.push({
          line: line.lineNumber,
          message: 'Invalid indentation. Cannot skip indentation levels.',
          severity: 'error'
        });
      }
      
      if (line.indentLevel > PARSER_CONFIG.maxDepth) {
        errors.push({
          line: line.lineNumber,
          message: `Indentation too deep. Maximum depth: ${PARSER_CONFIG.maxDepth}`,
          severity: 'error'
        });
      }
      
      prevIndent = line.indentLevel;
    }

    return errors;
  }

  /**
   * Build graph from parsed lines
   */
  private buildGraph(lines: ParsedLine[]): FlowchartGraph {
    const nodes: FlowchartNode[] = [];
    const edges: FlowchartEdge[] = [];
    const nodeStack: ParsedLine[] = [];
    
    for (const line of lines) {
      if (line.isComment || !line.nodeId) continue;
      
      // Create node
      const node: FlowchartNode = {
        id: line.nodeId,
        label: this.extractNodeLabel(line.content),
        data: {
          line: line.lineNumber,
          indentLevel: line.indentLevel,
          originalContent: line.content
        }
      };
      nodes.push(node);
      
      // Manage node stack based on indentation
      while (nodeStack.length > line.indentLevel) {
        nodeStack.pop();
      }
      
      // Create edge to parent if exists
      if (nodeStack.length > 0) {
        const parent = nodeStack[nodeStack.length - 1];
        if (parent.nodeId) {
          const edge: FlowchartEdge = {
            id: `${PARSER_CONFIG.edgeIdPrefix}${parent.nodeId}_to_${line.nodeId}`,
            source: parent.nodeId,
            target: line.nodeId,
            data: {
              type: 'hierarchy'
            }
          };
          edges.push(edge);
        }
      }
      
      // Add current node to stack
      nodeStack.push(line);
      
      // Create edges for explicit connections
      if (line.connections && line.connections.length > 0) {
        for (const connection of line.connections) {
          const targetNode = this.findNodeByLabel(nodes, connection);
          if (targetNode) {
            const edge: FlowchartEdge = {
              id: `${PARSER_CONFIG.edgeIdPrefix}${line.nodeId}_to_${targetNode.id}`,
              source: line.nodeId,
              target: targetNode.id,
              label: connection,
              data: {
                type: 'explicit'
              }
            };
            edges.push(edge);
          }
        }
      }
    }

    return {
      nodes,
      edges,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0'
      }
    };
  }

  /**
   * Find node by label
   */
  private findNodeByLabel(nodes: FlowchartNode[], label: string): FlowchartNode | null {
    const cleanLabel = label.replace(/[()]/g, '').trim();
    return nodes.find(node => 
      node.label.toLowerCase() === cleanLabel.toLowerCase()
    ) || null;
  }
}

// Export singleton instance
export const textParserService = new TextParserServiceImpl();
