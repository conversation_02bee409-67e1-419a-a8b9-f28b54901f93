import { <PERSON>act<PERSON><PERSON><PERSON> } from '@tiptap/react'
import tippy from 'tippy.js'
import { CommandList } from './CommandList'
import { slashCommands } from './suggestion-plugin'
import 'tippy.js/dist/tippy.css'

interface SuggestionProps {
  editor: any
  clientRect: () => DOMRect
}

// Function to render the command items popup
export const renderItems = () => {
  let component: ReactRenderer<any>
  let popup: any

  return {
    onStart: (props: SuggestionProps) => {
      component = new ReactRenderer(CommandList, {
        props: {
          items: slashCommands,
          command: (command: any) => {
            command.command(props)
            popup[0].hide()
          },
        },
        editor: props.editor,
      })

      // Mount the popup at the cursor position
      popup = tippy('body', {
        getReferenceClientRect: props.clientRect,
        appendTo: () => document.body,
        content: component.element,
        showOnCreate: true,
        interactive: true,
        trigger: 'manual',
        placement: 'bottom-start',
        arrow: false,
        theme: 'light-border',
        animation: 'shift-away',
        maxWidth: '400px',
      })
    },

    onUpdate: (props: SuggestionProps) => {
      component.updateProps({
        items: slashCommands,
        command: (command: any) => {
          command.command(props)
          popup[0].hide()
        },
      })

      // Update position of popup
      popup[0].setProps({
        getReferenceClientRect: props.clientRect,
      })
    },

    onKeyDown: (props: { event: KeyboardEvent }) => {
      if (props.event.key === 'Escape') {
        popup[0].hide()
        return true
      }

      return component.ref?.onKeyDown(props)
    },

    onExit: () => {
      popup[0].destroy()
      component.destroy()
    },
  }
}
