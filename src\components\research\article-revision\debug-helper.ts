/**
 * Debug helper for article revision system
 * Use this to understand what's happening with document parsing and section extraction
 */

import { DocumentParserService } from './services/document-parser.service';
import { useRevisionWorkflowStore } from './stores/revision-workflow.store';

export function debugArticleRevision() {
  const store = useRevisionWorkflowStore.getState();
  
  console.log('=== ARTICLE REVISION DEBUG ===');
  
  // Check original article
  if (store.originalArticle) {
    console.log('📄 Original Article:');
    console.log('- Title:', store.originalArticle.title);
    console.log('- File name:', store.originalArticle.fileName);
    console.log('- File type:', store.originalArticle.fileType);
    console.log('- Word count:', store.originalArticle.wordCount);
    console.log('- Full text length:', store.originalArticle.fullText?.length || 0);
    
    console.log('\n📋 Section Mapping:');
    Object.entries(store.originalArticle.sectionMapping).forEach(([section, content]) => {
      if (content && content.trim().length > 0) {
        console.log(`- ${section}: ${content.length} characters`);
        console.log(`  Preview: "${content.substring(0, 100)}..."`);
      } else {
        console.log(`- ${section}: EMPTY`);
      }
    });
  } else {
    console.log('❌ No original article found');
  }
  
  // Check reviewer comments
  if (store.reviewerComments && store.reviewerComments.length > 0) {
    console.log('\n💬 Reviewer Comments:');
    store.reviewerComments.forEach((rc, index) => {
      console.log(`- Reviewer ${rc.reviewerNumber}: ${rc.comments.length} comments`);
      rc.comments.forEach((comment, idx) => {
        console.log(`  ${idx + 1}. "${comment.comment.substring(0, 100)}..."`);
        console.log(`     Target: ${comment.targetSection || 'overall'}`);
        console.log(`     Category: ${comment.category || 'uncategorized'}`);
      });
    });
  } else {
    console.log('❌ No reviewer comments found');
  }
  
  // Check progress
  console.log('\n⚡ Current Progress:');
  console.log('- Phase:', store.progress.phase);
  console.log('- Current step:', store.progress.currentStep);
  console.log('- Percentage:', store.progress.percentage);
  console.log('- Errors:', store.progress.errors);
  console.log('- Warnings:', store.progress.warnings);
  
  console.log('\n=== END DEBUG ===');
}

export function testSectionExtraction(text: string) {
  console.log('=== TESTING SECTION EXTRACTION ===');
  console.log('Input text length:', text.length);
  console.log('Input preview:', text.substring(0, 200) + '...');
  
  const sections = DocumentParserService.extractSections(text);
  
  console.log('\nExtracted sections:');
  Object.entries(sections).forEach(([section, content]) => {
    if (content && content.trim().length > 0) {
      console.log(`✅ ${section}: ${content.length} characters`);
      console.log(`   Preview: "${content.substring(0, 100)}..."`);
    } else {
      console.log(`❌ ${section}: EMPTY`);
    }
  });
  
  console.log('=== END SECTION TEST ===');
  return sections;
}

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).debugArticleRevision = debugArticleRevision;
  (window as any).testSectionExtraction = testSectionExtraction;
}
