import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { BarChart3, Info, TrendingUp } from "lucide-react";
import { VisualizationConfig } from '../types';

interface FallbackChartProps {
  visualization: VisualizationConfig;
  className?: string;
}

/**
 * Fallback chart component that displays data in a simple format
 * when <PERSON>lotly fails to render
 */
export const FallbackChart: React.FC<FallbackChartProps> = ({
  visualization,
  className = ""
}) => {
  const renderSimpleChart = () => {
    const data = visualization.data[0];
    
    if (!data || !data.x || !data.y) {
      return (
        <div className="text-center py-8 text-gray-500">
          <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No data available for visualization</p>
        </div>
      );
    }

    switch (visualization.type) {
      case 'bar_chart':
        return renderBarChart(data);
      case 'histogram':
        return renderHistogram(data);
      case 'scatter':
        return renderScatterPlot(data);
      default:
        return renderDataTable(data);
    }
  };

  const renderBarChart = (data: any) => {
    const maxValue = Math.max(...data.y);
    
    return (
      <div className="space-y-2">
        {data.x.map((label: string, index: number) => {
          const value = data.y[index];
          const percentage = (value / maxValue) * 100;
          
          return (
            <div key={index} className="flex items-center gap-3">
              <div className="w-20 text-sm text-gray-600 truncate">{label}</div>
              <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                <div 
                  className="bg-blue-500 h-6 rounded-full flex items-center justify-end pr-2"
                  style={{ width: `${percentage}%` }}
                >
                  <span className="text-xs text-white font-medium">
                    {typeof value === 'number' ? value.toFixed(1) : value}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderHistogram = (data: any) => {
    // Group data into bins for histogram
    const values = data.x || data.y || [];
    const numBins = Math.min(10, Math.max(5, Math.floor(Math.sqrt(values.length))));
    const min = Math.min(...values);
    const max = Math.max(...values);
    const binSize = (max - min) / numBins;
    
    const bins = Array(numBins).fill(0);
    const binLabels = [];
    
    for (let i = 0; i < numBins; i++) {
      const binStart = min + i * binSize;
      const binEnd = min + (i + 1) * binSize;
      binLabels.push(`${binStart.toFixed(1)}-${binEnd.toFixed(1)}`);
    }
    
    values.forEach((value: number) => {
      const binIndex = Math.min(Math.floor((value - min) / binSize), numBins - 1);
      bins[binIndex]++;
    });
    
    const maxCount = Math.max(...bins);
    
    return (
      <div className="space-y-2">
        {bins.map((count, index) => {
          const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;
          
          return (
            <div key={index} className="flex items-center gap-3">
              <div className="w-24 text-xs text-gray-600">{binLabels[index]}</div>
              <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                <div 
                  className="bg-green-500 h-4 rounded-full flex items-center justify-end pr-2"
                  style={{ width: `${percentage}%` }}
                >
                  <span className="text-xs text-white font-medium">{count}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderScatterPlot = (data: any) => {
    const points = data.x.map((x: number, index: number) => ({
      x,
      y: data.y[index]
    })).slice(0, 20); // Show first 20 points
    
    return (
      <div className="space-y-3">
        <div className="grid grid-cols-4 gap-2 text-xs font-medium text-gray-600 border-b pb-2">
          <div>Point</div>
          <div>X Value</div>
          <div>Y Value</div>
          <div>Ratio</div>
        </div>
        {points.map((point, index) => (
          <div key={index} className="grid grid-cols-4 gap-2 text-sm">
            <div className="text-gray-500">#{index + 1}</div>
            <div>{typeof point.x === 'number' ? point.x.toFixed(2) : point.x}</div>
            <div>{typeof point.y === 'number' ? point.y.toFixed(2) : point.y}</div>
            <div className="text-gray-500">
              {typeof point.x === 'number' && typeof point.y === 'number' 
                ? (point.y / point.x).toFixed(2) 
                : '-'
              }
            </div>
          </div>
        ))}
        {data.x.length > 20 && (
          <div className="text-xs text-gray-500 text-center pt-2 border-t">
            ... and {data.x.length - 20} more points
          </div>
        )}
      </div>
    );
  };

  const renderDataTable = (data: any) => {
    const keys = Object.keys(data);
    const firstKey = keys[0];
    const dataLength = Array.isArray(data[firstKey]) ? data[firstKey].length : 0;
    
    return (
      <div className="space-y-3">
        <div className="grid gap-2 text-xs font-medium text-gray-600 border-b pb-2"
             style={{ gridTemplateColumns: `repeat(${keys.length}, 1fr)` }}>
          {keys.map(key => (
            <div key={key} className="truncate">{key}</div>
          ))}
        </div>
        {Array.from({ length: Math.min(10, dataLength) }, (_, index) => (
          <div key={index} className="grid gap-2 text-sm"
               style={{ gridTemplateColumns: `repeat(${keys.length}, 1fr)` }}>
            {keys.map(key => (
              <div key={key} className="truncate">
                {Array.isArray(data[key]) ? data[key][index] : data[key]}
              </div>
            ))}
          </div>
        ))}
        {dataLength > 10 && (
          <div className="text-xs text-gray-500 text-center pt-2 border-t">
            ... and {dataLength - 10} more rows
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-orange-500" />
            <div>
              <CardTitle className="text-lg">{visualization.title}</CardTitle>
              {visualization.description && (
                <p className="text-sm text-gray-500 mt-1">{visualization.description}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">
              Simplified View
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Alert className="mb-4">
          <Info className="h-4 w-4" />
          <AlertDescription className="text-sm">
            Interactive chart unavailable. Showing simplified data view.
          </AlertDescription>
        </Alert>
        
        <div className="max-h-96 overflow-y-auto">
          {renderSimpleChart()}
        </div>
      </CardContent>
    </Card>
  );
};
