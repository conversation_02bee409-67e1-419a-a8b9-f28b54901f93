import { Presentation, PresentationTheme } from '../types';

export class ExportService {
  /**
   * Export presentation as PDF
   */
  static async exportToPDF(presentation: Presentation, theme: PresentationTheme): Promise<void> {
    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      // Generate HTML content for PDF
      const htmlContent = this.generatePrintableHTML(presentation, theme);
      
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      
      // Wait for content to load then print
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    } catch (error) {
      console.error('Failed to export PDF:', error);
      throw new Error('Failed to export presentation as PDF');
    }
  }

  /**
   * Export presentation as PowerPoint (PPTX)
   */
  static async exportToPPTX(presentation: Presentation, theme: PresentationTheme): Promise<void> {
    try {
      // For now, we'll create a simple text-based export
      // In a real implementation, you'd use a library like PptxGenJS
      const content = this.generatePPTXContent(presentation, theme);
      
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${presentation.metadata.title}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export PPTX:', error);
      throw new Error('Failed to export presentation as PowerPoint');
    }
  }

  /**
   * Export presentation as HTML
   */
  static async exportToHTML(presentation: Presentation, theme: PresentationTheme): Promise<void> {
    try {
      const htmlContent = this.generateStandaloneHTML(presentation, theme);
      
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${presentation.metadata.title}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export HTML:', error);
      throw new Error('Failed to export presentation as HTML');
    }
  }

  /**
   * Export presentation as Markdown
   */
  static async exportToMarkdown(presentation: Presentation): Promise<void> {
    try {
      const markdownContent = this.generateMarkdownContent(presentation);
      
      const blob = new Blob([markdownContent], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${presentation.metadata.title}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export Markdown:', error);
      throw new Error('Failed to export presentation as Markdown');
    }
  }

  /**
   * Generate printable HTML for PDF export
   */
  private static generatePrintableHTML(presentation: Presentation, theme: PresentationTheme): string {
    const slides = presentation.slides.map((slide, index) => {
      const content = this.renderSlideContent(slide.content);
      
      return `
        <div class="slide" style="
          page-break-after: always;
          width: 100%;
          height: 100vh;
          padding: ${theme.layout.padding};
          background-color: ${theme.colors.background};
          color: ${theme.colors.text};
          font-family: ${theme.fonts.body};
          display: flex;
          flex-direction: column;
          justify-content: center;
        ">
          <div class="slide-content">
            ${content}
          </div>
          <div class="slide-number" style="
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 14px;
            color: ${theme.colors.muted};
          ">
            ${index + 1}
          </div>
        </div>
      `;
    }).join('');

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${presentation.metadata.title}</title>
          <style>
            @page { margin: 0; }
            body { margin: 0; font-family: ${theme.fonts.body}; }
            .slide { position: relative; }
            h1 { font-family: ${theme.fonts.heading}; color: ${theme.colors.heading}; }
            h2 { font-family: ${theme.fonts.heading}; color: ${theme.colors.heading}; }
            h3 { font-family: ${theme.fonts.heading}; color: ${theme.colors.heading}; }
            ul { padding-left: 30px; }
            li { margin-bottom: 10px; }
            blockquote { 
              border-left: 4px solid ${theme.colors.accent}; 
              padding-left: 20px; 
              font-style: italic; 
              color: ${theme.colors.primary};
            }
          </style>
        </head>
        <body>
          ${slides}
        </body>
      </html>
    `;
  }

  /**
   * Generate standalone HTML file
   */
  private static generateStandaloneHTML(presentation: Presentation, theme: PresentationTheme): string {
    // Similar to printable HTML but with navigation controls
    return this.generatePrintableHTML(presentation, theme);
  }

  /**
   * Generate PPTX content (simplified text format)
   */
  private static generatePPTXContent(presentation: Presentation, theme: PresentationTheme): string {
    let content = `${presentation.metadata.title}\n`;
    content += `Generated on ${new Date().toLocaleDateString()}\n`;
    content += `Theme: ${theme.displayName}\n\n`;
    content += '='.repeat(50) + '\n\n';

    presentation.slides.forEach((slide, index) => {
      content += `Slide ${index + 1}: ${slide.title}\n`;
      content += '-'.repeat(30) + '\n';
      
      const slideText = this.extractTextFromContent(slide.content);
      content += slideText + '\n\n';
      
      if (slide.notes) {
        content += `Speaker Notes: ${slide.notes}\n\n`;
      }
      
      content += '='.repeat(50) + '\n\n';
    });

    return content;
  }

  /**
   * Generate Markdown content
   */
  private static generateMarkdownContent(presentation: Presentation): string {
    let content = `# ${presentation.metadata.title}\n\n`;
    content += `Generated on ${new Date().toLocaleDateString()}\n\n`;
    content += `---\n\n`;

    presentation.slides.forEach((slide, index) => {
      content += `## Slide ${index + 1}: ${slide.title}\n\n`;
      
      const slideText = this.extractTextFromContent(slide.content);
      content += slideText + '\n\n';
      
      if (slide.notes) {
        content += `> **Speaker Notes:** ${slide.notes}\n\n`;
      }
      
      content += `---\n\n`;
    });

    return content;
  }

  /**
   * Render slide content as HTML
   */
  private static renderSlideContent(content: any[]): string {
    if (!content || content.length === 0) return '<p>No content</p>';

    return content.map(item => {
      switch (item.type) {
        case 'h1':
          return `<h1>${item.children?.[0]?.text || ''}</h1>`;
        case 'h2':
          return `<h2>${item.children?.[0]?.text || ''}</h2>`;
        case 'h3':
          return `<h3>${item.children?.[0]?.text || ''}</h3>`;
        case 'p':
          return `<p>${item.children?.[0]?.text || ''}</p>`;
        case 'ul':
          const listItems = item.children?.map((li: any) => 
            `<li>${li.children?.[0]?.text || ''}</li>`
          ).join('') || '';
          return `<ul>${listItems}</ul>`;
        case 'blockquote':
          return `<blockquote>${item.children?.[0]?.text || ''}</blockquote>`;
        default:
          return `<p>${item.children?.[0]?.text || JSON.stringify(item)}</p>`;
      }
    }).join('');
  }

  /**
   * Extract plain text from slide content
   */
  private static extractTextFromContent(content: any[]): string {
    if (!content || content.length === 0) return 'No content';

    return content.map(item => {
      if (item.children?.[0]?.text) {
        return item.children[0].text;
      }
      return JSON.stringify(item);
    }).join('\n');
  }
}
