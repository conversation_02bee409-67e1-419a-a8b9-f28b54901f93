/**
 * <PERSON><PERSON> Chat Component - Enhanced
 * Interactive educational chat interface with comprehension tracking and engagement features
 */

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Send,
  Bot,
  User,
  BookOpen,
  ExternalLink,
  Clock,
  MessageSquare,
  Lightbulb,
  ArrowUp,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  Sparkles,
  Brain,
  Target,
  TrendingUp,
  Award,
  CheckCircle,
  HelpCircle,
  Zap,
  Star,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  Volume2,
  VolumeX,
  Bookmark,
  Share
} from "lucide-react";
import ReactMarkdown from 'react-markdown';
import { TutorSession, TutorChatProps, EnhancedTutorMessage } from '../types';
import { TutorSources } from './TutorSources';

import { useTutorStore } from '../stores/tutor.store';

export function TutorChat({
  session,
  onSendMessage,
  onEndSession,
  isLoading,
  isStreaming
}: TutorChatProps) {
  const [message, setMessage] = useState('');
  const [showResources, setShowResources] = useState(true);

  const [comprehensionLevel, setComprehensionLevel] = useState(75); // Mock comprehension tracking
  const [engagementScore, setEngagementScore] = useState(85); // Mock engagement tracking
  const [learningProgress, setLearningProgress] = useState(60); // Mock learning progress
  const [showProgressPanel, setShowProgressPanel] = useState(false);
  const [messageRatings, setMessageRatings] = useState<{[key: string]: 'helpful' | 'not-helpful' | null}>({});
  const [isListening, setIsListening] = useState(false);
  const [savedMessages, setSavedMessages] = useState<Set<string>>(new Set());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);



  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [session.messages]);

  const handleSendMessage = () => {
    if (!message.trim() || isLoading || isStreaming) return;

    onSendMessage(message.trim());
    setMessage('');

    // Mock engagement tracking
    setEngagementScore(prev => Math.min(100, prev + 2));
    setLearningProgress(prev => Math.min(100, prev + 1));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleMessageRating = (messageId: string, rating: 'helpful' | 'not-helpful') => {
    setMessageRatings(prev => ({ ...prev, [messageId]: rating }));

    // Update comprehension based on feedback
    if (rating === 'helpful') {
      setComprehensionLevel(prev => Math.min(100, prev + 3));
    } else {
      setComprehensionLevel(prev => Math.max(0, prev - 2));
    }
  };

  const toggleSaveMessage = (messageId: string) => {
    setSavedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const getComprehensionColor = (level: number) => {
    if (level >= 80) return 'text-green-600 bg-green-100';
    if (level >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getEngagementIcon = (score: number) => {
    if (score >= 80) return <Star className="w-4 h-4 text-yellow-500" />;
    if (score >= 60) return <TrendingUp className="w-4 h-4 text-blue-500" />;
    return <Target className="w-4 h-4 text-gray-500" />;
  };





  return (
    <TooltipProvider>
      <div className={`grid gap-6 h-[calc(100vh-200px)] transition-all duration-300 ${
        showResources ? 'grid-cols-1 lg:grid-cols-4' : 'grid-cols-1'
      }`}>
        {/* Chat Area */}
        <div className={`flex flex-col ${showResources ? 'lg:col-span-3' : 'col-span-1'}`}>
          <Card className="flex-1 flex flex-col shadow-2xl border-0 bg-gradient-to-br from-white to-blue-50/30">
            {/* Enhanced Chat Header */}
            <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="relative"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                      <Bot className="w-7 h-7 text-white" />
                    </div>
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"
                    />
                  </motion.div>

                  <div>
                    <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {session.title}
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <div className="flex items-center space-x-1">
                        <MessageSquare className="w-3 h-3" />
                        <span>{session.messages.length} messages</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{formatTimestamp(session.createdAt)}</span>
                      </div>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                        {session.educationLevel.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Header Controls */}
                <div className="flex items-center space-x-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowProgressPanel(!showProgressPanel)}
                        className="bg-white/80 backdrop-blur-sm"
                      >
                        <Brain className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Learning Analytics</TooltipContent>
                  </Tooltip>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowResources(!showResources)}
                    className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm"
                  >
                    {showResources ? (
                      <>
                        <EyeOff className="w-4 h-4" />
                        <span className="hidden sm:inline">Hide Resources</span>
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4" />
                        <span className="hidden sm:inline">Show Resources</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* Learning Progress Indicators */}
              <AnimatePresence>
                {showProgressPanel && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-lg"
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <Brain className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium text-gray-700">Comprehension</span>
                      </div>
                      <div className="relative">
                        <Progress value={comprehensionLevel} className="h-2" />
                        <span className={`absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs px-2 py-1 rounded-full ${getComprehensionColor(comprehensionLevel)}`}>
                          {comprehensionLevel}%
                        </span>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        {getEngagementIcon(engagementScore)}
                        <span className="text-sm font-medium text-gray-700">Engagement</span>
                      </div>
                      <div className="relative">
                        <Progress value={engagementScore} className="h-2" />
                        <span className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700">
                          {engagementScore}%
                        </span>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-2 mb-2">
                        <TrendingUp className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium text-gray-700">Progress</span>
                      </div>
                      <div className="relative">
                        <Progress value={learningProgress} className="h-2" />
                        <span className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs px-2 py-1 rounded-full bg-green-100 text-green-700">
                          {learningProgress}%
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardHeader>

          <Separator />

          {/* Enhanced Messages Area */}
          <CardContent className="flex-1 p-0">
            <ScrollArea ref={scrollAreaRef} className="h-full p-6">
              <div className="space-y-6">
                {session.messages.map((msg, index) => (
                  <React.Fragment key={msg.id}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`group relative max-w-[85%] ${msg.role === 'user' ? 'ml-12' : 'mr-12'}`}>
                        {/* Message Bubble */}
                        <div
                          className={`relative ${
                            msg.role === 'user'
                              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl rounded-br-md shadow-lg'
                              : 'bg-white text-gray-900 rounded-2xl rounded-bl-md shadow-lg border border-gray-100'
                          } px-5 py-4 transition-all duration-200 hover:shadow-xl`}
                        >
                          {/* Message Header */}
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                msg.role === 'user'
                                  ? 'bg-white/20'
                                  : 'bg-gradient-to-r from-blue-500 to-purple-500'
                              }`}>
                                {msg.role === 'user' ? (
                                  <User className="w-3 h-3" />
                                ) : (
                                  <Bot className="w-3 h-3 text-white" />
                                )}
                              </div>
                              <span className={`text-xs font-medium ${
                                msg.role === 'user' ? 'text-white/80' : 'text-gray-500'
                              }`}>
                                {formatTimestamp(msg.timestamp)}
                              </span>
                              {msg.metadata?.confidence && (
                                <Badge
                                  variant="outline"
                                  className={`text-xs ${
                                    msg.role === 'user'
                                      ? 'border-white/30 text-white/90 bg-white/10'
                                      : 'border-blue-200 text-blue-700 bg-blue-50'
                                  }`}
                                >
                                  {Math.round(msg.metadata.confidence * 100)}% confidence
                                </Badge>
                              )}
                            </div>

                            {/* Message Actions */}
                            {msg.role === 'assistant' && (
                              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleMessageRating(msg.id, 'helpful')}
                                      className={`h-6 w-6 p-0 hover:bg-green-100 ${
                                        messageRatings[msg.id] === 'helpful' ? 'bg-green-100 text-green-600' : 'text-gray-400'
                                      }`}
                                    >
                                      <ThumbsUp className="w-3 h-3" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Helpful</TooltipContent>
                                </Tooltip>

                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleMessageRating(msg.id, 'not-helpful')}
                                      className={`h-6 w-6 p-0 hover:bg-red-100 ${
                                        messageRatings[msg.id] === 'not-helpful' ? 'bg-red-100 text-red-600' : 'text-gray-400'
                                      }`}
                                    >
                                      <ThumbsDown className="w-3 h-3" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Not helpful</TooltipContent>
                                </Tooltip>

                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => toggleSaveMessage(msg.id)}
                                      className={`h-6 w-6 p-0 hover:bg-yellow-100 ${
                                        savedMessages.has(msg.id) ? 'bg-yellow-100 text-yellow-600' : 'text-gray-400'
                                      }`}
                                    >
                                      <Bookmark className="w-3 h-3" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Save message</TooltipContent>
                                </Tooltip>
                              </div>
                            )}
                          </div>

                          {/* Message Content */}
                          <div className={`prose prose-sm max-w-none ${
                            msg.role === 'user' ? 'prose-invert' : 'prose-gray'
                          }`}>
                            <ReactMarkdown>{msg.content}</ReactMarkdown>
                          </div>

                          {/* Message Metadata */}
                          {msg.metadata && (
                            <div className={`mt-3 pt-2 border-t text-xs ${
                              msg.role === 'user'
                                ? 'border-white/20 text-white/70'
                                : 'border-gray-200 text-gray-500'
                            }`}>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  {msg.metadata.model && (
                                    <span className="flex items-center space-x-1">
                                      <Brain className="w-3 h-3" />
                                      <span>{msg.metadata.model}</span>
                                    </span>
                                  )}
                                  {msg.metadata.tokens && (
                                    <span className="flex items-center space-x-1">
                                      <Zap className="w-3 h-3" />
                                      <span>{msg.metadata.tokens} tokens</span>
                                    </span>
                                  )}
                                </div>

                                {msg.role === 'assistant' && messageRatings[msg.id] === 'helpful' && (
                                  <div className="flex items-center space-x-1 text-green-600">
                                    <CheckCircle className="w-3 h-3" />
                                    <span>Marked as helpful</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Comprehension Indicator for Assistant Messages */}
                        {msg.role === 'assistant' && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.5 }}
                            className="absolute -bottom-2 -left-2 w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg"
                          >
                            <CheckCircle className="w-3 h-3 text-white" />
                          </motion.div>
                        )}
                      </div>
                    </motion.div>


                  </React.Fragment>
                ))}

                {/* Enhanced Streaming Indicator */}
                {isStreaming && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-start"
                  >
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl rounded-bl-md px-5 py-4 shadow-lg border border-blue-100">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex space-x-1">
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                              className="w-2 h-2 bg-blue-500 rounded-full"
                            />
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                              className="w-2 h-2 bg-purple-500 rounded-full"
                            />
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                              className="w-2 h-2 bg-pink-500 rounded-full"
                            />
                          </div>
                          <span className="text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Crafting your personalized response...
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>

          <Separator className="bg-gradient-to-r from-blue-200 to-purple-200" />

          {/* Enhanced Input Area */}
          <div className="p-6 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
            <div className="flex space-x-3 mb-4">
              <div className="flex-1 relative">
                <Input
                  placeholder="Ask me anything! I'm here to help you learn..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isLoading || isStreaming}
                  className="pr-12 py-3 text-base border-2 border-gray-200 focus:border-blue-400 rounded-xl shadow-sm bg-white/80 backdrop-blur-sm"
                />
                {message && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  >
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </motion.div>
                )}
              </div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim() || isLoading || isStreaming}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                  size="lg"
                >
                  {isLoading || isStreaming ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full" />
                    </motion.div>
                  ) : (
                    <motion.div whileHover={{ x: 2 }}>
                      <Send className="w-5 h-5" />
                    </motion.div>
                  )}
                </Button>
              </motion.div>
            </div>

            {/* Enhanced Quick Actions */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Sparkles className="w-4 h-4 text-blue-500" />
                <span className="font-medium">Quick Actions:</span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {[
                  { icon: Lightbulb, label: "Explain More", message: "Can you explain this in more detail?", color: "yellow" },
                  { icon: BookOpen, label: "Examples", message: "Can you give me some examples?", color: "green" },

                  { icon: ExternalLink, label: "Real World", message: "How does this relate to real life?", color: "blue" },
                  { icon: HelpCircle, label: "Quiz Me", message: "Can you quiz me on this topic?", color: "red" },
                  { icon: Target, label: "Practice", message: "Give me practice problems", color: "indigo" },
                  { icon: RotateCcw, label: "Simplify", message: "Can you explain this more simply?", color: "orange" },
                  { icon: TrendingUp, label: "Next Level", message: "What should I learn next?", color: "pink" }
                ].map((action, index) => (
                  <motion.div
                    key={action.label}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setMessage(action.message)}
                      disabled={isLoading || isStreaming}
                      className={`w-full justify-start text-left h-auto py-2 px-3 bg-white/60 backdrop-blur-sm hover:bg-${action.color}-50 hover:border-${action.color}-300 transition-all duration-200`}
                    >
                      <action.icon className={`w-3 h-3 mr-2 text-${action.color}-500`} />
                      <span className="text-xs font-medium">{action.label}</span>
                    </Button>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Enhanced Collapsible Resources Sidebar */}
      <AnimatePresence>
        {showResources && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
            className="lg:col-span-1"
          >
            <TutorSources
              sources={session.sources}
              isLoading={false}
              onSourceClick={(source) => {
                window.open(source.url, '_blank');
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
    </TooltipProvider>
  );
}
