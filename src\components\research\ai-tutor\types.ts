import { LucideIcon } from "lucide-react";
import { ComponentType } from "react";

// Core tutoring types
export interface TutorSession {
  id: string;
  title: string;
  topic: string;
  educationLevel: EducationLevel;
  messages: TutorMessage[];
  sources: TutorSource[];
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  isActive: boolean;
  metadata?: {
    totalMessages: number;
    duration: number; // in minutes
    concepts: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  };
}

export interface TutorMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  sources?: string[]; // IDs of sources referenced in this message
  isStreaming?: boolean;
  metadata?: {
    tokens?: number;
    model?: string;
    confidence?: number;
  };
}

export interface TutorSource {
  id: string;
  title: string;
  url: string;
  snippet: string;
  domain: string;
  publishedDate?: string;
  score: number;
  type: 'academic' | 'educational' | 'web' | 'video' | 'book';
  relevanceScore: number;
  educationLevel?: EducationLevel;
}

// Education levels - Research-focused difficulty system
export type EducationLevel =
  | 'basic'
  | 'intermediate'
  | 'advanced'
  | 'expert';

export interface EducationLevelOption {
  id: EducationLevel;
  name: string;
  description: string;
  icon: LucideIcon;
  ageRange: string;
  complexity: number; // 1-10 scale
}

// AI model configuration
export interface TutorAIModel {
  id: string;
  name: string;
  provider: string;
  apiProvider: 'gemini' | 'openrouter'; // Which API service to use
  description: string;
  maxTokens: number;
  supportsStreaming: boolean;
  cost: 'low' | 'medium' | 'high';
  strengths: string[];
  bestFor: string[];
  educationLevels: EducationLevel[];
}

// Tutoring settings
export interface TutorSettings {
  preferredModel: string;
  educationLevel: EducationLevel;
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  pace: 'slow' | 'normal' | 'fast';
  includeExamples: boolean;
  includeQuizzes: boolean;
  sourcesEnabled: boolean;
  maxSources: number;
  language: string;
  notifications: {
    sessionReminders: boolean;
    progressUpdates: boolean;
  };
}

// Topic suggestions
export interface TopicSuggestion {
  id: string;
  name: string;
  category: string;
  icon: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string; // e.g., "30 minutes"
  prerequisites?: string[];
  relatedTopics: string[];
}

// Learning progress
export interface LearningProgress {
  sessionId: string;
  topic: string;
  conceptsLearned: string[];
  questionsAsked: number;
  timeSpent: number; // in minutes
  comprehensionLevel: number; // 1-10 scale
  nextSteps: string[];
  achievements: string[];
}

// Export options
export interface TutorExportOptions {
  format: 'pdf' | 'docx' | 'txt' | 'html';
  includeMessages: boolean;
  includeSources: boolean;
  includeProgress: boolean;
  includeMetadata: boolean;
  title?: string;
  author?: string;
}

// Search and source options
export interface TutorSearchOptions {
  maxResults: number;
  searchDepth: 'basic' | 'comprehensive' | 'advanced';
  includeImages: boolean;
  includeVideos: boolean;
  educationLevel: EducationLevel;
  sourceTypes: ('academic' | 'educational' | 'web' | 'video' | 'book')[];
  timeFilter?: 'day' | 'week' | 'month' | 'year' | 'all';
}

// API response types
export interface TutorResponse {
  content: string;
  sources: TutorSource[];
  confidence: number;
  tokens: number;
  model: string;
  suggestions?: string[];
}

export interface StreamingTutorResponse {
  chunk: string;
  isComplete: boolean;
  sources?: TutorSource[];
  metadata?: {
    tokens: number;
    model: string;
  };
}

// Database types for Supabase integration
export interface TutorSessionDB {
  id: string;
  user_id: string;
  title: string;
  topic: string;
  education_level: EducationLevel;
  messages: TutorMessage[];
  sources: TutorSource[];
  created_at: string;
  updated_at: string;
  is_active: boolean;
  metadata?: any;
}

export interface TutorProgressDB {
  id: string;
  user_id: string;
  session_id: string;
  concepts_learned: string[];
  questions_asked: number;
  time_spent: number;
  comprehension_level: number;
  created_at: string;
  updated_at: string;
}

// Component props
export interface TutorHeroProps {
  onStartSession: (topic: string, educationLevel: EducationLevel) => void;
  isLoading: boolean;
  suggestions: TopicSuggestion[];
}

export interface TutorChatProps {
  session: TutorSession;
  onSendMessage: (message: string) => void;
  onEndSession: () => void;
  isLoading: boolean;
  isStreaming: boolean;
}

export interface TutorSourcesProps {
  sources: TutorSource[];
  isLoading: boolean;
  onSourceClick: (source: TutorSource) => void;
}

export interface TutorHistoryProps {
  sessions: TutorSession[];
  onSessionSelect: (session: TutorSession) => void;
  onSessionDelete: (sessionId: string) => void;
  onCreateNew: () => void;
}

export type GradeLevel = 'elementary' | 'middle' | 'high' | 'college';
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

// Enhanced Tutor Message
export interface EnhancedTutorMessage extends TutorMessage {
  // Future: Add document-related metadata here
}

// Store state interface
export interface TutorStoreState {
  // Current session
  currentSession: TutorSession | null;
  isSessionActive: boolean;

  // UI state
  currentView: 'hero' | 'chat' | 'history' | 'settings';
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;

  // Settings
  settings: TutorSettings;

  // History
  sessions: TutorSession[];

  // Sources
  isLoadingSources: boolean;

  // Actions
  startSession: (topic: string, educationLevel: EducationLevel) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  endSession: () => void;
  loadSession: (sessionId: string) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  updateSettings: (settings: Partial<TutorSettings>) => void;
  setCurrentView: (view: 'hero' | 'chat' | 'history' | 'settings') => void;
  clearError: () => void;
}

// Research Comprehension Platform Types

// Document types
export interface ResearchDocument {
  id: string;
  title: string;
  authors: string[];
  abstract: string;
  content: string;
  sections: DocumentSection[];
  metadata: DocumentMetadata;
  uploadedAt: Date;
  processedAt: Date;
  userId: string;
  filePath?: string; // Path to original file in storage
  fileSize?: number;
  fileType?: string;
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  startPage?: number;
  endPage?: number;
  type: 'abstract' | 'introduction' | 'methodology' | 'results' | 'discussion' | 'conclusion' | 'references' | 'other';
  orderIndex: number;
}

export interface DocumentMetadata {
  extractedAt: Date;
  confidence: number; // 0-1 scale for extraction confidence
  sources: string[]; // Extraction methods used
  language: string;
  keywords?: string[];
  doi?: string;
  journal?: string;
  publicationYear?: number;
  citationCount?: number;
}

// Quiz types
export interface Quiz {
  id: string;
  documentId: string;
  userId: string;
  title: string;
  questions: QuizQuestion[];
  difficulty: DifficultyLevel;
  estimatedTime: number; // in minutes
  createdAt: Date;
  updatedAt: Date;
}

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  question: string;
  options?: string[]; // For multiple choice
  correctAnswer: string | string[];
  explanation: string;
  relatedSection: string; // Section ID this question relates to
  difficulty: number; // 1-5 scale
  points: number;
}

export interface QuizResult {
  id: string;
  quizId: string;
  userId: string;
  answers: QuizAnswer[];
  score: number; // 0-100
  totalPoints: number;
  earnedPoints: number;
  timeSpent: number; // in seconds
  completedAt: Date;
  feedback?: string;
}

export interface QuizAnswer {
  questionId: string;
  userAnswer: string | string[];
  isCorrect: boolean;
  timeSpent: number; // in seconds
  attempts: number;
}

// Educational Game types
export interface EducationalGame {
  id: string;
  documentId: string;
  userId: string;
  type: 'terminology-match' | 'concept-map' | 'methodology-puzzle' | 'citation-challenge' | 'sequence-order';
  title: string;
  description: string;
  gameData: TerminologyMatchData | ConceptMapData | MethodologyPuzzleData | CitationChallengeData | SequenceOrderData;
  difficulty: number; // 1-5 scale
  estimatedTime: number; // in minutes
  createdAt: Date;
  updatedAt: Date;
}

export interface TerminologyMatchData {
  terms: Array<{
    term: string;
    definition: string;
    category?: string;
  }>;
  distractors: string[]; // Incorrect definitions
}

export interface ConceptMapData {
  concepts: Array<{
    id: string;
    name: string;
    description: string;
    x?: number;
    y?: number;
  }>;
  relationships: Array<{
    from: string;
    to: string;
    label: string;
    type: 'causes' | 'leads-to' | 'part-of' | 'related-to' | 'opposite-of';
  }>;
}

export interface MethodologyPuzzleData {
  steps: Array<{
    id: string;
    description: string;
    order: number;
    category: 'data-collection' | 'analysis' | 'interpretation' | 'validation';
  }>;
  correctSequence: string[]; // Array of step IDs in correct order
}

export interface CitationChallengeData {
  citations: Array<{
    id: string;
    text: string;
    format: 'APA' | 'MLA' | 'Chicago' | 'IEEE';
    isCorrect: boolean;
    errors?: string[]; // Description of errors if incorrect
  }>;
}

export interface SequenceOrderData {
  items: Array<{
    id: string;
    content: string;
    correctPosition: number;
    category?: string;
  }>;
  context: string; // Description of what should be ordered
}

export interface GameResult {
  id: string;
  gameId: string;
  userId: string;
  score: number; // 0-100
  timeSpent: number; // in seconds
  moves: number; // Number of actions taken
  hintsUsed: number;
  completedAt: Date;
  gameState: any; // Final state of the game
  feedback?: string;
}

// RAG (Retrieval-Augmented Generation) types
export interface DocumentChunk {
  id: string;
  documentId: string;
  sectionId: string;
  content: string;
  embedding?: number[]; // Vector embedding for similarity search
  metadata: ChunkMetadata;
  chunkIndex: number;
  startPosition: number;
  endPosition: number;
}

export interface ChunkMetadata {
  pageNumber?: number;
  sectionTitle?: string;
  chunkType: 'paragraph' | 'heading' | 'list' | 'table' | 'figure-caption';
  wordCount: number;
  keyTerms: string[];
  importance: number; // 1-5 scale
}

export interface RAGQuery {
  query: string;
  documentId?: string; // If specified, search only in this document
  maxResults: number;
  threshold: number; // Minimum similarity threshold (0-1)
  filters?: {
    sectionTypes?: DocumentSection['type'][];
    dateRange?: {
      start: Date;
      end: Date;
    };
    authors?: string[];
  };
}

export interface RAGResult {
  chunk: DocumentChunk;
  similarity: number; // 0-1 similarity score
  relevantText: string; // Highlighted relevant portion
  context: string; // Surrounding context
  citation: string; // Formatted citation for this chunk
}

export interface RAGResponse {
  query: string;
  results: RAGResult[];
  totalResults: number;
  searchTime: number; // in milliseconds
  generatedAnswer?: string; // AI-generated answer based on retrieved chunks
  confidence: number; // 0-1 confidence in the answer
  sources: string[]; // Document IDs used in the response
}

// Progress Tracking and Learning Analytics types
export interface LearningProgress {
  id: string;
  userId: string;
  documentId: string;
  comprehensionLevel: number; // 0-100 scale
  conceptsLearned: string[];
  conceptsMastered: string[];
  quizzesCompleted: string[]; // Quiz IDs
  gamesCompleted: string[]; // Game IDs
  timeSpent: number; // Total time in seconds
  sessionsCount: number;
  lastActivity: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface StudySession {
  id: string;
  userId: string;
  documentId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  activitiesCompleted: SessionActivity[];
  questionsAsked: number;
  conceptsExplored: string[];
  satisfactionRating?: number; // 1-5 scale
  notes?: string;
}

export interface SessionActivity {
  type: 'reading' | 'quiz' | 'game' | 'question' | 'search';
  activityId?: string; // ID of quiz, game, etc.
  startTime: Date;
  endTime: Date;
  duration: number;
  outcome?: 'completed' | 'partial' | 'abandoned';
  score?: number;
}

export interface LearningObjective {
  id: string;
  documentId: string;
  title: string;
  description: string;
  category: 'comprehension' | 'analysis' | 'synthesis' | 'evaluation';
  difficulty: DifficultyLevel;
  estimatedTime: number; // in minutes
  prerequisites: string[]; // Other objective IDs
  assessmentCriteria: string[];
  isCompleted: boolean;
  completedAt?: Date;
}

export interface ConceptMastery {
  conceptId: string;
  conceptName: string;
  masteryLevel: number; // 0-100 scale
  evidenceCount: number; // Number of correct demonstrations
  lastDemonstrated: Date;
  strengthAreas: string[];
  improvementAreas: string[];
}

export interface LearningAnalytics {
  userId: string;
  documentId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'all-time';
  metrics: {
    totalTimeSpent: number;
    averageSessionDuration: number;
    comprehensionGrowth: number; // Percentage improvement
    quizAccuracy: number; // Average quiz score
    gamePerformance: number; // Average game score
    conceptsMastered: number;
    engagementScore: number; // 1-10 scale
    consistencyScore: number; // Based on regular usage
  };
  trends: {
    timeSpentTrend: number[]; // Daily/weekly values
    comprehensionTrend: number[];
    engagementTrend: number[];
  };
  recommendations: string[];
  generatedAt: Date;
}

// Search Integration types
export interface ResearchSearchQuery {
  query: string;
  documentContext?: string; // Current document being studied
  searchType: 'academic' | 'supplementary' | 'definition' | 'example';
  maxResults: number;
  filters?: {
    dateRange?: string;
    domains?: string[];
    language?: string;
  };
}

export interface ResearchSearchResult {
  id: string;
  title: string;
  url: string;
  snippet: string;
  content?: string;
  domain: string;
  publishedDate?: string;
  relevanceScore: number;
  type: 'academic' | 'educational' | 'web' | 'video';
  citationFormat?: {
    apa: string;
    mla: string;
    chicago: string;
  };
}

// UI Component Props types
export interface DocumentUploaderProps {
  onDocumentUploaded: (document: ResearchDocument) => void;
  onUploadProgress: (progress: number) => void;
  onError: (error: string) => void;
  acceptedFileTypes: string[];
  maxFileSize: number; // in bytes
  isLoading: boolean;
}

export interface DocumentViewerProps {
  document: ResearchDocument;
  highlightedSections?: string[]; // Section IDs to highlight
  onSectionClick: (sectionId: string) => void;
  showMetadata: boolean;
  showOutline: boolean;
}

export interface QuizInterfaceProps {
  quiz: Quiz;
  onQuizComplete: (result: QuizResult) => void;
  onQuestionAnswer: (questionId: string, answer: QuizAnswer) => void;
  allowReview: boolean;
  timeLimit?: number; // in seconds
  showProgress: boolean;
}

export interface GameInterfaceProps {
  game: EducationalGame;
  onGameComplete: (result: GameResult) => void;
  onGameStateChange: (state: any) => void;
  showHints: boolean;
  allowPause: boolean;
}

export interface RAGChatProps {
  document: ResearchDocument;
  onMessageSend: (message: string) => void;
  onRAGQuery: (query: RAGQuery) => Promise<RAGResponse>;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    sources?: RAGResult[];
    timestamp: Date;
  }>;
  isLoading: boolean;
}

export interface ProgressDashboardProps {
  userId: string;
  documents: ResearchDocument[];
  progress: LearningProgress[];
  analytics: LearningAnalytics[];
  onDocumentSelect: (documentId: string) => void;
  timeRange: 'week' | 'month' | 'quarter' | 'year';
}

// Updated Store State for Research Comprehension Platform
export interface ResearchComprehensionStoreState {
  // Current session and documents
  currentDocument: ResearchDocument | null;
  uploadedDocuments: ResearchDocument[];
  currentSession: StudySession | null;

  // UI state
  currentView: 'library' | 'study' | 'quiz' | 'games' | 'progress' | 'search' | 'settings' | 'history';
  isLoading: boolean;
  isProcessingDocument: boolean;
  error: string | null;

  // Settings
  settings: ResearchComprehensionSettings;

  // Learning state
  learningProgress: LearningProgress[];
  currentQuiz: Quiz | null;
  currentGame: EducationalGame | null;

  // RAG state
  ragResults: RAGResult[];
  isSearching: boolean;

  // Actions
  uploadDocument: (file: File) => Promise<ResearchDocument>;
  selectDocument: (documentId: string) => void;
  startStudySession: (documentId: string) => Promise<void>;
  endStudySession: () => void;
  generateQuiz: (documentId: string, options: QuizGenerationOptions) => Promise<Quiz>;
  generateGame: (documentId: string, gameType: EducationalGame['type']) => Promise<EducationalGame>;
  performRAGQuery: (query: RAGQuery) => Promise<RAGResponse>;
  searchOnline: (query: ResearchSearchQuery) => Promise<ResearchSearchResult[]>;
  updateProgress: (progress: Partial<LearningProgress>) => void;
  updateSettings: (settings: Partial<ResearchComprehensionSettings>) => void;
  setCurrentView: (view: ResearchComprehensionStoreState['currentView']) => void;
  clearError: () => void;
}

export interface ResearchComprehensionSettings {
  // AI and Processing
  preferredModel: string;
  educationLevel: EducationLevel;

  // Document Processing
  autoGenerateQuizzes: boolean;
  autoGenerateGames: boolean;
  chunkSize: number; // For RAG processing

  // Learning Preferences
  quizDifficulty: DifficultyLevel;
  gameDifficulty: number; // 1-5 scale
  showHints: boolean;
  allowRetries: boolean;

  // Search and RAG
  maxSearchResults: number;
  searchTimeout: number; // in seconds
  ragThreshold: number; // Similarity threshold

  // Progress Tracking
  trackDetailedAnalytics: boolean;
  shareProgressWithInstructors: boolean;

  // UI Preferences
  theme: 'light' | 'dark' | 'auto';
  showProgressIndicators: boolean;
  enableNotifications: boolean;
  autoSaveInterval: number; // in minutes
}

export interface QuizGenerationOptions {
  questionCount: number;
  questionTypes: QuizQuestion['type'][];
  difficulty: DifficultyLevel;
  focusSections?: string[]; // Section IDs to focus on
  includeDefinitions: boolean;
  includeConceptual: boolean;
  includeAnalytical: boolean;
}
