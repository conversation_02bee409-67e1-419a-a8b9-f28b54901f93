import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  BookMetadata,
  UserChapter,
  GeminiBookOutline,
  GeminiGenerationState,
  GeminiGeneratedChapter,
  GeminiBookContext
} from '../types';
import { GEMINI_CONFIG } from '../constants';

interface GeminiBookState extends GeminiGenerationState {
  // Core state
  bookMetadata: BookMetadata | null;
  userChapters: UserChapter[];
  
  // Generation progress
  startTime: Date | null;
  endTime: Date | null;
  
  // Actions
  initializeGeneration: (metadata: BookMetadata, userChapters: UserChapter[]) => void;
  setOutline: (outline: GeminiBookOutline) => void;
  approveOutline: () => void;
  rejectOutline: (feedback?: string) => void;
  startChapterGeneration: () => void;
  addGeneratedChapter: (chapter: GeminiGeneratedChapter) => void;
  updateProgress: (chapterIndex: number) => void;
  completeGeneration: () => void;
  setError: (error: string) => void;
  resetState: () => void;
  
  // Context management
  getBookContext: () => GeminiBookContext | null;
  getRollingContext: () => string[];
  
  // Utility
  canProceedToNextChapter: () => boolean;
  getEstimatedTimeRemaining: () => string | null;
}

const initialState: Omit<GeminiBookState, 
  'initializeGeneration' | 'setOutline' | 'approveOutline' | 'rejectOutline' | 
  'startChapterGeneration' | 'addGeneratedChapter' | 'updateProgress' | 
  'completeGeneration' | 'setError' | 'resetState' | 'getBookContext' | 
  'getRollingContext' | 'canProceedToNextChapter' | 'getEstimatedTimeRemaining'
> = {
  // Core state
  bookMetadata: null,
  userChapters: [],
  
  // Generation state
  phase: 'outline-generation',
  outline: undefined,
  outlineApproved: false,
  currentChapterIndex: 0,
  generatedChapters: [],
  chapterSummaries: [],
  isGenerating: false,
  lastRequestTime: undefined,
  totalProgress: 0,
  estimatedTimeRemaining: undefined,
  error: undefined,
  
  // Timing
  startTime: null,
  endTime: null
};

export const useGeminiBookStore = create<GeminiBookState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      initializeGeneration: (metadata: BookMetadata, userChapters: UserChapter[]) => {
        set({
          bookMetadata: metadata,
          userChapters,
          phase: 'outline-generation',
          outline: undefined,
          outlineApproved: false,
          currentChapterIndex: 0,
          generatedChapters: [],
          chapterSummaries: [],
          isGenerating: false,
          totalProgress: 0,
          startTime: new Date(),
          endTime: null,
          error: undefined
        });
      },

      setOutline: (outline: GeminiBookOutline) => {
        set({
          outline,
          phase: 'outline-approval',
          isGenerating: false
        });
      },

      approveOutline: () => {
        set({
          outlineApproved: true,
          phase: 'chapter-generation'
        });
      },

      rejectOutline: (feedback?: string) => {
        set({
          outlineApproved: false,
          phase: 'outline-generation',
          error: feedback
        });
      },

      startChapterGeneration: () => {
        set({
          phase: 'chapter-generation',
          isGenerating: true,
          currentChapterIndex: 0,
          totalProgress: 0
        });
      },

      addGeneratedChapter: (chapter: GeminiGeneratedChapter) => {
        const state = get();

        console.log(`=== STORE: Adding generated chapter ===`);
        console.log(`Chapter being added:`, { id: chapter.id, title: chapter.title, order: chapter.order });
        console.log(`Current state before adding:`, {
          currentChapterIndex: state.currentChapterIndex,
          generatedChaptersCount: state.generatedChapters.length,
          existingChapterIds: state.generatedChapters.map(ch => ch.id)
        });

        const newChapters = [...state.generatedChapters, chapter];
        const newSummaries = [...state.chapterSummaries, chapter.summary];

        // Keep only the most recent summaries to manage context size
        const maxSummaries = GEMINI_CONFIG.CONTEXT.MAX_PREVIOUS_CHAPTERS;
        const trimmedSummaries = newSummaries.slice(-maxSummaries);

        // Calculate progress
        const totalChapters = state.outline?.chapters.length || 1;
        const progress = Math.round((newChapters.length / totalChapters) * 100);

        // Set the next chapter index for generation
        const nextChapterIndex = newChapters.length; // This will be the index of the next chapter to generate

        console.log(`STORE: Setting new state:`, {
          newChaptersCount: newChapters.length,
          nextChapterIndex,
          totalChapters,
          progress
        });

        set({
          generatedChapters: newChapters,
          chapterSummaries: trimmedSummaries,
          currentChapterIndex: nextChapterIndex,
          totalProgress: progress,
          lastRequestTime: new Date()
        });

        console.log(`=== STORE: Chapter added successfully ===`);
      },

      updateProgress: (chapterIndex: number) => {
        const state = get();
        const totalChapters = state.outline?.chapters.length || 1;
        const progress = Math.round((chapterIndex / totalChapters) * 100);

        console.log(`=== STORE: updateProgress called ===`);
        console.log(`Setting currentChapterIndex to: ${chapterIndex}`);
        console.log(`Progress: ${progress}%`);

        set({
          currentChapterIndex: chapterIndex,
          totalProgress: progress
        });
      },

      completeGeneration: () => {
        set({
          phase: 'completed',
          isGenerating: false,
          endTime: new Date(),
          totalProgress: 100
        });
      },

      setError: (error: string) => {
        set({
          error,
          phase: 'error',
          isGenerating: false
        });
      },

      resetState: () => {
        set(initialState);
      },

      getBookContext: (): GeminiBookContext | null => {
        const state = get();
        if (!state.bookMetadata || !state.outline) {
          return null;
        }

        return {
          bookMetadata: state.bookMetadata,
          bookOutline: state.outline,
          previousChapterSummaries: state.chapterSummaries,
          currentChapterIndex: state.currentChapterIndex,
          totalChapters: state.outline.chapters.length,
          rollingContextWordCount: state.chapterSummaries.join(' ').split(' ').length
        };
      },

      getRollingContext: (): string[] => {
        const state = get();
        return state.chapterSummaries.slice(-GEMINI_CONFIG.CONTEXT.MAX_PREVIOUS_CHAPTERS);
      },

      canProceedToNextChapter: (): boolean => {
        const state = get();
        return state.outlineApproved && 
               !state.isGenerating && 
               state.currentChapterIndex < (state.outline?.chapters.length || 0);
      },

      getEstimatedTimeRemaining: (): string | null => {
        const state = get();
        if (!state.startTime || !state.outline || state.generatedChapters.length === 0) {
          return null;
        }

        const elapsedTime = Date.now() - state.startTime.getTime();
        const chaptersCompleted = state.generatedChapters.length;
        const totalChapters = state.outline.chapters.length;
        const remainingChapters = totalChapters - chaptersCompleted;

        if (remainingChapters <= 0) {
          return '0 minutes';
        }

        const averageTimePerChapter = elapsedTime / chaptersCompleted;
        const estimatedRemainingTime = averageTimePerChapter * remainingChapters;

        // Add rate limiting delays
        const rateLimitDelay = remainingChapters * GEMINI_CONFIG.RATE_LIMIT.DELAY_BETWEEN_REQUESTS;
        const totalEstimatedTime = estimatedRemainingTime + rateLimitDelay;

        const minutes = Math.ceil(totalEstimatedTime / (1000 * 60));
        
        if (minutes < 60) {
          return `${minutes} minutes`;
        } else {
          const hours = Math.floor(minutes / 60);
          const remainingMinutes = minutes % 60;
          return `${hours}h ${remainingMinutes}m`;
        }
      }
    }),
    {
      name: 'gemini-book-store',
      partialize: (state) => ({
        // Only persist essential state, not temporary generation state
        bookMetadata: state.bookMetadata,
        userChapters: state.userChapters,
        outline: state.outline,
        outlineApproved: state.outlineApproved,
        generatedChapters: state.generatedChapters,
        phase: state.phase === 'completed' ? state.phase : 'outline-generation'
      })
    }
  )
);
