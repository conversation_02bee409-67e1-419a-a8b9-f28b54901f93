/**
 * Flowchart Fun Constants
 * Configuration and default values for the Flowchart Fun module
 */

import { ExportFormat, FlowchartConfig, FlowchartTemplate } from './types';

// Default flowchart text examples
export const DEFAULT_FLOWCHART_TEXT = `Research Process
  Literature Review: Systematic Review
    Search Strategy: Database Search
    Inclusion Criteria: Peer-reviewed articles
    Exclusion Criteria: Non-English articles
  Data Collection: Survey Design
    Questionnaire: Validated instruments
    Sampling: Random sampling
    Ethics: IRB approval
  Data Analysis: Statistical Analysis
    Descriptive: Mean, SD, frequencies
    Inferential: t-tests, ANOVA
    Software: SPSS, R
  Results: Findings
    Significant: p < 0.05
    Effect Size: Cohen's d
  Discussion: Interpretation
    Limitations: Sample size
    Future Research: Longitudinal study
  Conclusion: Summary`;

// Export format configurations
export const EXPORT_FORMATS: ExportFormat[] = [
  'png',
  'svg', 
  'pdf',
  'word',
  'json',
  'txt'
];

export const EXPORT_FORMAT_LABELS: Record<ExportFormat, string> = {
  png: 'PNG Image',
  svg: 'SVG Vector',
  pdf: 'PDF Document',
  word: 'Word Document',
  json: 'JSON Data',
  cytoscape: 'Cytoscape Format',
  txt: 'Text Format'
};

// Cytoscape layout algorithms
export const LAYOUT_ALGORITHMS = {
  dagre: {
    name: 'dagre',
    rankDir: 'TB',
    align: 'UL',
    nodeSep: 50,
    edgeSep: 10,
    rankSep: 50
  },
  cose: {
    name: 'cose',
    idealEdgeLength: 100,
    nodeOverlap: 20,
    refresh: 20,
    fit: true,
    padding: 30,
    randomize: false,
    componentSpacing: 100,
    nodeRepulsion: 400000,
    edgeElasticity: 100,
    nestingFactor: 5,
    gravity: 80,
    numIter: 1000,
    initialTemp: 200,
    coolingFactor: 0.95,
    minTemp: 1.0
  },
  grid: {
    name: 'grid',
    fit: true,
    padding: 30,
    boundingBox: undefined,
    avoidOverlap: true,
    avoidOverlapPadding: 10,
    nodeDimensionsIncludeLabels: false,
    spacingFactor: undefined,
    condense: false,
    rows: undefined,
    cols: undefined,
    position: function(node: any) { return undefined; },
    sort: undefined,
    animate: false
  },
  circle: {
    name: 'circle',
    fit: true,
    padding: 30,
    boundingBox: undefined,
    avoidOverlap: true,
    nodeDimensionsIncludeLabels: false,
    spacingFactor: undefined,
    radius: undefined,
    startAngle: 3 / 2 * Math.PI,
    sweep: undefined,
    clockwise: true,
    sort: undefined,
    animate: false
  }
};

// Cytoscape styling
export const DEFAULT_NODE_STYLE = {
  'background-color': '#3b82f6',
  'border-color': '#1e40af',
  'border-width': 2,
  'color': '#ffffff',
  'font-size': '12px',
  'font-family': 'Inter, system-ui, sans-serif',
  'text-valign': 'center',
  'text-halign': 'center',
  'text-wrap': 'wrap',
  'text-max-width': '120px',
  'width': 'label',
  'height': 'label',
  'padding': '8px',
  'shape': 'roundrectangle'
};

export const DEFAULT_EDGE_STYLE = {
  'width': 2,
  'line-color': '#6b7280',
  'target-arrow-color': '#6b7280',
  'target-arrow-shape': 'triangle',
  'curve-style': 'bezier',
  'font-size': '10px',
  'font-family': 'Inter, system-ui, sans-serif',
  'color': '#374151',
  'text-background-color': '#ffffff',
  'text-background-opacity': 0.8,
  'text-background-padding': '2px'
};

// Theme configurations
export const THEMES = {
  light: {
    background: '#ffffff',
    node: {
      ...DEFAULT_NODE_STYLE,
      'background-color': '#3b82f6',
      'border-color': '#1e40af',
      'color': '#ffffff'
    },
    edge: {
      ...DEFAULT_EDGE_STYLE,
      'line-color': '#6b7280',
      'target-arrow-color': '#6b7280',
      'color': '#374151'
    }
  },
  dark: {
    background: '#1f2937',
    node: {
      ...DEFAULT_NODE_STYLE,
      'background-color': '#4f46e5',
      'border-color': '#3730a3',
      'color': '#ffffff'
    },
    edge: {
      ...DEFAULT_EDGE_STYLE,
      'line-color': '#9ca3af',
      'target-arrow-color': '#9ca3af',
      'color': '#d1d5db'
    }
  }
};

// Parser configuration
export const PARSER_CONFIG = {
  maxDepth: 10,
  maxNodes: 1000,
  maxEdges: 2000,
  autoParseDelay: 500,
  indentSize: 2,
  connectionKeywords: ['goes to', 'leads to', 'connects to', 'flows to', 'points to', 'and', 'then', 'next'],
  commentPrefix: '//',
  nodeIdPrefix: 'node_',
  edgeIdPrefix: 'edge_'
};

// Export default options
export const DEFAULT_EXPORT_OPTIONS = {
  png: {
    quality: 1.0,
    width: 1200,
    height: 800,
    backgroundColor: '#ffffff'
  },
  svg: {
    width: 1200,
    height: 800,
    backgroundColor: '#ffffff'
  },
  pdf: {
    width: 1200,
    height: 800,
    backgroundColor: '#ffffff',
    format: 'A4' as const
  },
  word: {
    includeMetadata: true,
    includeGraph: true,
    includeText: true
  }
};

// Template categories
export const TEMPLATE_CATEGORIES = [
  'Research Methodology',
  'Business Process',
  'Software Development',
  'Decision Making',
  'Project Management',
  'Academic Writing',
  'Data Analysis',
  'System Design'
];

// Default templates
export const DEFAULT_TEMPLATES: FlowchartTemplate[] = [
  {
    id: 'research-methodology',
    name: 'Research Methodology',
    description: 'Standard research process flowchart',
    category: 'Research Methodology',
    tags: ['research', 'methodology', 'academic'],
    text: DEFAULT_FLOWCHART_TEXT
  },
  {
    id: 'literature-review',
    name: 'Literature Review Process',
    description: 'Systematic literature review workflow',
    category: 'Research Methodology',
    tags: ['literature', 'review', 'systematic'],
    text: `Literature Review Process
  Planning: Define scope
    Research Question: PICO framework
    Inclusion Criteria: Peer-reviewed
    Exclusion Criteria: Non-English
  Search Strategy: Database search
    Databases: PubMed, Scopus, Web of Science
    Keywords: Boolean operators
    Filters: Date range, study type
  Screening: Title and abstract
    Initial Screening: Remove duplicates
    Full-text Review: Detailed assessment
    Quality Assessment: Risk of bias
  Data Extraction: Standardized form
    Study Characteristics: Author, year, design
    Outcomes: Primary and secondary
    Quality Metrics: Methodological quality
  Analysis: Synthesis
    Narrative Synthesis: Qualitative summary
    Meta-analysis: Quantitative pooling
  Reporting: PRISMA guidelines
    Flow Diagram: Study selection process
    Results Table: Study characteristics
    Discussion: Implications and limitations`
  },
  {
    id: 'data-analysis',
    name: 'Data Analysis Workflow',
    description: 'Statistical data analysis process',
    category: 'Data Analysis',
    tags: ['data', 'analysis', 'statistics'],
    text: `Data Analysis Workflow
  Data Preparation: Clean and organize
    Import Data: Load from files
    Data Cleaning: Handle missing values
    Variable Coding: Categorical variables
  Exploratory Analysis: Understand data
    Descriptive Statistics: Mean, median, mode
    Data Visualization: Histograms, boxplots
    Correlation Analysis: Relationships
  Statistical Testing: Hypothesis testing
    Normality Tests: Shapiro-Wilk
    Parametric Tests: t-test, ANOVA
    Non-parametric Tests: Mann-Whitney U
  Results Interpretation: Draw conclusions
    Effect Sizes: Cohen's d, eta-squared
    Confidence Intervals: 95% CI
    Statistical Significance: p-values
  Reporting: Present findings
    Tables: Descriptive statistics
    Figures: Graphs and charts
    Text: Results narrative`
  }
];

// Error messages
export const ERROR_MESSAGES = {
  PARSE_ERROR: 'Failed to parse flowchart text',
  EXPORT_ERROR: 'Failed to export flowchart',
  INVALID_SYNTAX: 'Invalid flowchart syntax',
  MAX_NODES_EXCEEDED: 'Maximum number of nodes exceeded',
  MAX_EDGES_EXCEEDED: 'Maximum number of edges exceeded',
  NETWORK_ERROR: 'Network connection error',
  FILE_TOO_LARGE: 'File size too large',
  UNSUPPORTED_FORMAT: 'Unsupported export format'
};

// UI Configuration
export const UI_CONFIG = {
  splitPaneDefaultSize: 50,
  minPaneSize: 200,
  maxPaneSize: 80,
  toolbarHeight: 60,
  statusBarHeight: 30,
  sidebarWidth: 300,
  exportDialogWidth: 500,
  exportDialogHeight: 400
};

// Storage keys
export const STORAGE_KEYS = {
  FLOWCHART_TEXT: 'flowchart_fun_text',
  THEME: 'flowchart_fun_theme',
  LAYOUT: 'flowchart_fun_layout',
  AUTO_SAVE: 'flowchart_fun_auto_save',
  HISTORY: 'flowchart_fun_history'
};

// Main configuration object
export const FLOWCHART_CONFIG: FlowchartConfig = {
  defaultText: DEFAULT_FLOWCHART_TEXT,
  maxNodes: PARSER_CONFIG.maxNodes,
  maxEdges: PARSER_CONFIG.maxEdges,
  autoParseDelay: PARSER_CONFIG.autoParseDelay,
  exportFormats: EXPORT_FORMATS,
  themes: THEMES,
  layouts: LAYOUT_ALGORITHMS
};
