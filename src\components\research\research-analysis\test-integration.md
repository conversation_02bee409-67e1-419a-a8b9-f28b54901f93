# Research Analysis Platform Integration Test Guide

## Pre-requisites

1. **Database Setup**: Ensure the research analysis schema has been applied to your Supabase database
2. **Authentication**: User must be logged in to test database operations
3. **Environment**: Supabase environment variables must be configured

## Test Scenarios

### 1. Document Upload and Storage Test

**Steps:**
1. Navigate to the Research Analysis Platform
2. Go to the "Upload" tab
3. Upload a PDF research document
4. Verify the document appears in the library
5. Check the "History" tab to see if the document is saved

**Expected Results:**
- Document is processed and analyzed by AI
- Document metadata is extracted and displayed
- Document is saved to Supabase database
- File is uploaded to Supabase storage
- Document appears in history dashboard

**Verification:**
```sql
-- Check if document was saved
SELECT * FROM research_documents WHERE user_id = 'your-user-id';

-- Check if sections were saved
SELECT * FROM research_document_sections WHERE document_id = 'document-id';
```

### 2. Literature Review Generation and Storage Test

**Steps:**
1. Upload at least 2 research documents
2. Select the documents in the library
3. Go to "Literature Review" tab
4. Generate a literature review
5. Check the "History" tab for the saved review

**Expected Results:**
- Literature review is generated successfully
- Review is saved to database with all sections
- Review appears in history dashboard
- Review can be viewed in detail modal

**Verification:**
```sql
-- Check if literature review was saved
SELECT * FROM literature_reviews WHERE user_id = 'your-user-id';

-- Check if review sections were saved
SELECT * FROM literature_review_sections WHERE review_id = 'review-id';
```

### 3. Gap Analysis and Storage Test

**Steps:**
1. Ensure you have documents uploaded
2. Select documents and go to "Research Gaps" tab
3. Generate a gap analysis
4. Check the "History" tab for the saved analysis

**Expected Results:**
- Gap analysis is completed successfully
- Analysis is saved with gaps, themes, and opportunities
- Analysis appears in history dashboard
- Detailed view shows all components

**Verification:**
```sql
-- Check if gap analysis was saved
SELECT * FROM research_gap_analyses WHERE user_id = 'your-user-id';

-- Check if gaps were saved
SELECT * FROM research_gaps WHERE analysis_id = 'analysis-id';

-- Check if themes were saved
SELECT * FROM research_themes WHERE analysis_id = 'analysis-id';
```

### 4. Hypothesis Generation and Storage Test

**Steps:**
1. Complete a gap analysis first
2. Go to "Hypotheses" tab
3. Generate research hypotheses
4. Check the "History" tab for saved hypotheses

**Expected Results:**
- Hypotheses are generated successfully
- Hypotheses are saved to database
- Hypotheses appear in history dashboard
- Detailed view shows variables and methodology

**Verification:**
```sql
-- Check if hypotheses were saved
SELECT * FROM research_hypotheses WHERE user_id = 'your-user-id';
```

### 5. History Dashboard Test

**Steps:**
1. Navigate to the "History" tab
2. Test search functionality
3. Test filtering by status and date
4. Click "View" on different items
5. Test delete functionality

**Expected Results:**
- All saved items appear in appropriate tabs
- Search works across titles and authors
- Filters work correctly
- Detail modals display complete information
- Delete operations work and update the display

### 6. Activity Logging Test

**Steps:**
1. Perform various actions (upload, generate, delete)
2. Check the "Activity" tab in history dashboard

**Expected Results:**
- All user actions are logged
- Activity shows correct timestamps
- Activity descriptions are meaningful

**Verification:**
```sql
-- Check activity logs
SELECT * FROM research_analysis_activity WHERE user_id = 'your-user-id' ORDER BY created_at DESC;
```

## Error Scenarios to Test

### 1. Network Failure During Upload
- Disconnect internet during document upload
- Verify graceful error handling
- Check that partial data isn't saved

### 2. Large File Upload
- Upload a file larger than 50MB
- Verify proper error message
- Check file validation works

### 3. Invalid File Type
- Upload an unsupported file type
- Verify proper error message
- Check file type validation

### 4. Database Connection Issues
- Test behavior when database is unavailable
- Verify fallback to local storage
- Check error messages are user-friendly

## Performance Tests

### 1. Multiple Document Upload
- Upload 5-10 documents simultaneously
- Monitor processing time and memory usage
- Verify all documents are processed correctly

### 2. Large Document Processing
- Upload a large PDF (20+ pages)
- Monitor processing time
- Verify complete content extraction

### 3. History Dashboard Loading
- With 50+ items in history
- Test loading performance
- Verify pagination works if implemented

## Security Tests

### 1. User Data Isolation
- Create two user accounts
- Upload documents with each account
- Verify users can't see each other's data

### 2. File Access Control
- Try to access another user's file URL
- Verify proper access control

### 3. SQL Injection Prevention
- Test search fields with SQL injection attempts
- Verify proper input sanitization

## Browser Compatibility

Test the enhanced platform on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Mobile Responsiveness

Test the history dashboard on:
- Mobile phones (portrait/landscape)
- Tablets (portrait/landscape)
- Different screen sizes

## Cleanup After Testing

```sql
-- Clean up test data (be careful with this!)
DELETE FROM research_analysis_activity WHERE user_id = 'your-test-user-id';
DELETE FROM research_hypotheses WHERE user_id = 'your-test-user-id';
DELETE FROM research_gap_analyses WHERE user_id = 'your-test-user-id';
DELETE FROM literature_reviews WHERE user_id = 'your-test-user-id';
DELETE FROM research_documents WHERE user_id = 'your-test-user-id';
```

## Success Criteria

✅ All document uploads save to database
✅ All analysis results save to database  
✅ History dashboard loads and displays data correctly
✅ Search and filtering work properly
✅ Detail modals display complete information
✅ Delete operations work correctly
✅ Activity logging captures all actions
✅ Error handling is graceful and user-friendly
✅ Performance is acceptable for typical usage
✅ Security policies prevent data leakage
✅ UI is responsive and works across browsers

## Troubleshooting

### Common Issues:

1. **"User not authenticated" errors**
   - Ensure user is logged in
   - Check Supabase auth configuration

2. **Database connection errors**
   - Verify Supabase URL and keys
   - Check network connectivity
   - Verify database schema is applied

3. **File upload failures**
   - Check storage bucket exists
   - Verify storage policies are correct
   - Check file size and type limits

4. **Missing data in history**
   - Check browser console for errors
   - Verify RLS policies are correct
   - Check user ID matches in database

5. **UI not loading properly**
   - Check for JavaScript errors
   - Verify all imports are correct
   - Check component dependencies
