/**
 * Career Explorer Module Index
 * Main export file for the Career Explorer module
 */

// Main component
export { CareerExplorer } from './CareerExplorer';

// Types
export type {
  CareerMetadata,
  CareerPath,
  CareerRoadmapStep,
  CareerAnalysis,
  ResumeData,
  CareerExplorationState,
  CareerVisualizationNode,
  CareerVisualizationEdge,
  AIModelOption,
  CareerGenerationRequest,
  CareerGenerationResponse,
  CareerExportOptions,
  CareerExportData,
  CareerExploration,
  CareerExplorerProps,
  ResumeUploadFormProps,
  CareerVisualizationProps,
  CareerDetailModalProps,
  CareerHistoryPanelProps,
  DifficultyLevel,
  CareerField,
  ExportFormat
} from './types';

// Components
export {
  ResumeUploadForm,
  CareerVisualization,
  CareerDetailModal,
  CareerExportDialog,
  CareerHistoryPanel
} from './components';

// Services
export { careerAIService } from './services/career-ai.service';
export { pdfParserService } from './services/pdf-parser.service';

// Store
export { useCareerExplorerStore } from './stores/career-explorer.store';

// Constants
export {
  AI_MODELS,
  DIFFICULTY_LEVELS,
  CAREER_FIELDS,
  VISUALIZATION_CONFIG,
  EXPORT_FORMATS,
  DEFAULT_GENERATION_OPTIONS,
  CAREER_PROMPTS,
  UPLOAD_CONFIG,
  UI_CONSTANTS,
  CAREER_COLORS,
  CAREER_CATEGORIES
} from './constants';
