/**
 * Quick validation script to ensure all imports and basic functionality work
 */

// Test all imports
export async function validateImports(): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  
  try {
    // Test core system imports
    const { agenticEditingService } = await import('./AgenticEditingService');
    const { changeTrackingAdapter } = await import('./ChangeTrackingAdapter');
    const { errorHandler } = await import('./ErrorHandler');
    
    // Test tool imports
    const { SearchTool, AnalysisTool, EditTool, ReviewTool, ValidationTool, WriteTool } = await import('./tools');
    
    // Test types import
    const types = await import('./types');
    
    console.log('✅ All imports successful');
    
    // Test basic initialization
    agenticEditingService.initialize();
    agenticEditingService.setChangeTrackingIntegration(changeTrackingAdapter);
    
    console.log('✅ Basic initialization successful');
    
    return { success: true, errors: [] };
    
  } catch (error: any) {
    const errorMessage = `Import/initialization failed: ${error.message}`;
    errors.push(errorMessage);
    console.error('❌', errorMessage);
    return { success: false, errors };
  }
}

// Test basic functionality
export async function validateBasicFunctionality(): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  
  try {
    const { agenticEditingService } = await import('./AgenticEditingService');
    
    // Test with a simple document and request
    const testDocument = 'This is a test document with some content that can be improved.';
    const testRequest = 'Improve the clarity of this text';
    
    const result = await agenticEditingService.executeTargetedEdit(
      testRequest,
      testDocument,
      {
        editMode: 'conservative',
        enableChangeTracking: false,
        maxChanges: 1,
        confidenceThreshold: 0.5
      }
    );
    
    if (result.success || result.changes.length > 0) {
      console.log('✅ Basic functionality test passed');
      return { success: true, errors: [] };
    } else {
      const errorMessage = 'Basic functionality test failed - no changes generated';
      errors.push(errorMessage);
      console.warn('⚠️', errorMessage);
      return { success: false, errors };
    }
    
  } catch (error: any) {
    const errorMessage = `Basic functionality test failed: ${error.message}`;
    errors.push(errorMessage);
    console.error('❌', errorMessage);
    return { success: false, errors };
  }
}

// Test UI component imports
export async function validateUIComponents(): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  
  try {
    // Test Enhanced Agent Mode import
    const { EnhancedAgentMode } = await import('../EnhancedAgentMode');
    
    // Test Integration Service import
    const { useAgenticEditing } = await import('../AgenticIntegrationService');
    
    console.log('✅ UI component imports successful');
    return { success: true, errors: [] };
    
  } catch (error: any) {
    const errorMessage = `UI component import failed: ${error.message}`;
    errors.push(errorMessage);
    console.error('❌', errorMessage);
    return { success: false, errors };
  }
}

// Run all validations
export async function runQuickValidation(): Promise<{
  overall: boolean;
  results: {
    imports: { success: boolean; errors: string[] };
    functionality: { success: boolean; errors: string[] };
    ui: { success: boolean; errors: string[] };
  };
}> {
  console.log('🔍 Running quick validation of agentic system...');
  
  const results = {
    imports: await validateImports(),
    functionality: await validateBasicFunctionality(),
    ui: await validateUIComponents()
  };
  
  const overall = results.imports.success && results.functionality.success && results.ui.success;
  
  console.log('\n📊 Quick Validation Results:');
  console.log(`Imports: ${results.imports.success ? '✅' : '❌'}`);
  console.log(`Functionality: ${results.functionality.success ? '✅' : '❌'}`);
  console.log(`UI Components: ${results.ui.success ? '✅' : '❌'}`);
  console.log(`Overall: ${overall ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (!overall) {
    console.log('\n❌ Errors found:');
    Object.values(results).forEach(result => {
      result.errors.forEach(error => console.log(`  • ${error}`));
    });
  }
  
  return { overall, results };
}

// Export for easy testing
export default {
  validateImports,
  validateBasicFunctionality,
  validateUIComponents,
  runQuickValidation
};
