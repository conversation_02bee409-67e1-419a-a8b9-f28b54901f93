/**
 * Test Enhanced Deep Research System
 * Comprehensive test for the new academic research functionality
 */

import { researchPlanningService } from './services/research-planning.service';
import { referenceManagementService } from './services/reference-management.service';
import { academicFormattingService } from './services/academic-formatting.service';
import { deepResearchService } from './services/deep-research.service';

// Test data
const testQueries = [
  {
    query: "Impact of artificial intelligence on healthcare diagnostics",
    expectedTypes: ['literature_review', 'research_paper'],
    description: "Medical AI research topic"
  },
  {
    query: "Climate change policy effectiveness in developing countries",
    expectedTypes: ['policy_brief', 'literature_review'],
    description: "Policy research topic"
  },
  {
    query: "Comprehensive analysis of quantum computing applications",
    expectedTypes: ['academic_book', 'literature_review'],
    description: "Technical academic topic"
  },
  {
    query: "Quick overview of blockchain technology",
    expectedTypes: ['quick_research'],
    description: "Simple overview topic"
  }
];

const testSources = [
  {
    id: 'source_1',
    title: 'AI in Medical Diagnosis: A Systematic Review',
    url: 'https://pubmed.ncbi.nlm.nih.gov/example1',
    snippet: 'This systematic review examines the application of artificial intelligence in medical diagnosis...',
    domain: 'pubmed.ncbi.nlm.nih.gov',
    publishedDate: '2023-06-15',
    score: 0.95,
    type: 'academic' as const
  },
  {
    id: 'source_2',
    title: 'Government Report on AI Healthcare Implementation',
    url: 'https://health.gov/ai-report-2023',
    snippet: 'Federal analysis of AI implementation in healthcare systems across the United States...',
    domain: 'health.gov',
    publishedDate: '2023-08-20',
    score: 0.88,
    type: 'web' as const
  },
  {
    id: 'source_3',
    title: 'Machine Learning Applications in Radiology',
    url: 'https://journals.springer.com/example',
    snippet: 'Recent advances in machine learning have revolutionized radiological diagnosis...',
    domain: 'journals.springer.com',
    publishedDate: '2023-09-10',
    score: 0.92,
    type: 'academic' as const
  }
];

const testContent = `
# AI in Healthcare Diagnostics: A Comprehensive Analysis

## Introduction

Artificial intelligence (AI) has emerged as a transformative force in healthcare diagnostics, offering unprecedented opportunities to improve accuracy, efficiency, and accessibility of medical diagnosis (Smith et al., 2023). This comprehensive analysis examines the current state, challenges, and future prospects of AI implementation in diagnostic medicine.

## Current Applications

### Medical Imaging

AI algorithms have demonstrated remarkable success in medical imaging applications. Deep learning models can now detect diabetic retinopathy with 90% accuracy (Johnson & Williams, 2023), while convolutional neural networks show promise in early cancer detection through radiological analysis.

### Laboratory Diagnostics

Machine learning approaches are revolutionizing laboratory medicine by enabling:

- **Automated pattern recognition** in blood samples
- **Predictive analytics** for disease progression
- **Quality control** improvements in testing procedures

## Challenges and Limitations

Despite significant advances, several challenges remain:

1. **Data quality and standardization** issues across healthcare systems
2. **Regulatory compliance** requirements for medical AI systems
3. **Integration complexity** with existing healthcare infrastructure

## Future Directions

The future of AI in healthcare diagnostics appears promising, with emerging technologies such as:

- Quantum computing applications in drug discovery
- Federated learning for privacy-preserving medical research
- Edge computing for real-time diagnostic support

## Conclusion

AI represents a paradigm shift in healthcare diagnostics, offering both tremendous opportunities and significant challenges. Continued research and development, coupled with appropriate regulatory frameworks, will be essential for realizing the full potential of these technologies.

## References

Smith, J., Anderson, K., & Brown, L. (2023). Artificial intelligence in medical diagnosis: Current applications and future prospects. *Journal of Medical AI*, 15(3), 245-267.

Johnson, M., & Williams, R. (2023). Deep learning approaches to diabetic retinopathy detection. *Nature Medicine AI*, 8(2), 112-128.
`;

/**
 * Test Research Planning Service
 */
async function testResearchPlanning() {
  console.log('🧪 Testing Research Planning Service...');
  
  try {
    // Test 1: Get available research types
    const researchTypes = researchPlanningService.getResearchTypeTemplates();
    console.log(`✅ Found ${researchTypes.length} research type templates`);
    
    // Validate research types
    const expectedTypes = ['quick_research', 'literature_review', 'research_paper', 'academic_book', 'policy_brief'];
    const foundTypes = researchTypes.map(t => t.id);
    const missingTypes = expectedTypes.filter(t => !foundTypes.includes(t));
    
    if (missingTypes.length === 0) {
      console.log('✅ All expected research types found');
    } else {
      console.log(`❌ Missing research types: ${missingTypes.join(', ')}`);
    }

    // Test 2: Query analysis (would require API key)
    console.log('⏭️ Skipping query analysis test (requires API key)');
    
    return true;
  } catch (error) {
    console.error('❌ Research Planning test failed:', error);
    return false;
  }
}

/**
 * Test Reference Management Service
 */
async function testReferenceManagement() {
  console.log('🧪 Testing Reference Management Service...');
  
  try {
    // Clear any existing references
    referenceManagementService.clear();
    
    // Test 1: Add sources
    const sectionId = 'test_section_1';
    const references = testSources.map(source => 
      referenceManagementService.addSource(source, sectionId)
    );
    
    console.log(`✅ Added ${references.length} sources to reference manager`);
    
    // Test 2: Extract citations from content
    const { processedContent, citations } = referenceManagementService.extractAndLinkCitations(
      testContent,
      testSources,
      sectionId
    );
    
    console.log(`✅ Extracted ${citations.length} citations from content`);
    console.log(`✅ Processed content length: ${processedContent.length} characters`);
    
    // Test 3: Generate consolidated references
    const consolidatedRefs = referenceManagementService.generateConsolidatedReferences('apa');
    console.log(`✅ Generated consolidated references (${consolidatedRefs.length} characters)`);
    
    // Test 4: Get all references
    const allRefs = referenceManagementService.getAllReferences();
    console.log(`✅ Retrieved ${allRefs.length} total references`);
    
    // Validate reference quality
    const avgQuality = allRefs.reduce((sum, ref) => sum + ref.source.qualityScore, 0) / allRefs.length;
    console.log(`✅ Average source quality: ${avgQuality.toFixed(2)}`);
    
    return true;
  } catch (error) {
    console.error('❌ Reference Management test failed:', error);
    return false;
  }
}

/**
 * Test Academic Formatting Service
 */
async function testAcademicFormatting() {
  console.log('🧪 Testing Academic Formatting Service...');
  
  try {
    // Test 1: Format for academic output
    const academicHtml = academicFormattingService.formatForAcademicOutput(testContent);
    console.log(`✅ Generated academic HTML (${academicHtml.length} characters)`);
    
    // Validate HTML contains proper academic elements
    const hasHeaders = academicHtml.includes('<h1 class="academic-h1">') || academicHtml.includes('<h2 class="academic-h2">');
    const hasCitations = academicHtml.includes('<span class="citation">');
    const hasLists = academicHtml.includes('<ul class="academic-ul">') || academicHtml.includes('<ol class="academic-ol">');
    
    console.log(`✅ Academic formatting validation: Headers=${hasHeaders}, Citations=${hasCitations}, Lists=${hasLists}`);
    
    // Test 2: Format for Word export
    const wordContent = academicFormattingService.formatForWordExport(testContent);
    console.log(`✅ Generated Word export content (${wordContent.length} characters)`);
    
    // Validate markdown removal
    const hasMarkdown = wordContent.includes('#') || wordContent.includes('**') || wordContent.includes('*');
    console.log(`✅ Markdown removal: ${hasMarkdown ? '❌ Still contains markdown' : '✅ Clean text'}`);
    
    // Test 3: Word count
    const wordCount = academicFormattingService.countWords(testContent);
    console.log(`✅ Word count: ${wordCount} words`);
    
    // Test 4: Extract keywords
    const keywords = academicFormattingService.extractKeywords(testContent, 5);
    console.log(`✅ Extracted keywords: ${keywords.join(', ')}`);
    
    // Test 5: Generate abstract
    const abstract = academicFormattingService.generateAbstract(testContent, 100);
    console.log(`✅ Generated abstract (${abstract.length} characters)`);
    
    return true;
  } catch (error) {
    console.error('❌ Academic Formatting test failed:', error);
    return false;
  }
}

/**
 * Test Deep Research Service Integration
 */
async function testDeepResearchIntegration() {
  console.log('🧪 Testing Deep Research Service Integration...');
  
  try {
    // Test 1: Get research types
    const researchTypes = deepResearchService.getResearchTypes();
    console.log(`✅ Deep Research Service has ${researchTypes.length} research types`);
    
    // Test 2: Check service configuration
    const isConfigured = deepResearchService.isConfigured();
    console.log(`✅ Deep Research Service configured: ${isConfigured}`);
    
    if (!isConfigured) {
      console.log('⚠️ Deep Research Service not configured (missing API key)');
      console.log('⏭️ Skipping API-dependent tests');
      return true;
    }
    
    // Additional tests would require API calls
    console.log('⏭️ Skipping API-dependent integration tests');
    
    return true;
  } catch (error) {
    console.error('❌ Deep Research Integration test failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runEnhancedSystemTests() {
  console.log('🚀 Starting Enhanced Deep Research System Tests\n');
  
  const tests = [
    { name: 'Research Planning', test: testResearchPlanning },
    { name: 'Reference Management', test: testReferenceManagement },
    { name: 'Academic Formatting', test: testAcademicFormatting },
    { name: 'Deep Research Integration', test: testDeepResearchIntegration }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n📋 Running ${name} tests...`);
    const result = await test();
    results.push({ name, passed: result });
    console.log(`${result ? '✅' : '❌'} ${name} tests ${result ? 'passed' : 'failed'}\n`);
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Enhanced system is ready for use.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  return { passed, total, success: passed === total };
}

// Export test functions for individual testing
export {
  testResearchPlanning,
  testReferenceManagement,
  testAcademicFormatting,
  testDeepResearchIntegration
};
