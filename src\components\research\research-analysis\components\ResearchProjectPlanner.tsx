import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "lucide-react";

import { ResearchGap, ResearchHypothesis, ResearchDocument, ResearchProject, AIGenerationOptions } from '../types';

interface ResearchProjectPlannerProps {
  gaps: ResearchGap[];
  hypotheses: ResearchHypothesis[];
  documents: ResearchDocument[];
  onCreateProject: (project: ResearchProject) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function ResearchProjectPlanner({ gaps, hypotheses, documents }: ResearchProjectPlannerProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Calendar className="h-6 w-6 text-teal-500" />
          Research Project Planner
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Project planning feature coming soon!</p>
          <div className="flex justify-center gap-2">
            <Badge variant="secondary">{gaps.length} gaps</Badge>
            <Badge variant="secondary">{hypotheses.length} hypotheses</Badge>
            <Badge variant="secondary">{documents.length} documents</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
