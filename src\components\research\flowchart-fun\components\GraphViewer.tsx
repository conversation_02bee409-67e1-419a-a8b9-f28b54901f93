/**
 * Graph Viewer Component
 * Cytoscape-based graph visualization
 */

import React, { useRef, useEffect, useState } from 'react';
import cytoscape, { Core, ElementDefinition } from 'cytoscape';
import dagre from 'cytoscape-dagre';
import cose from 'cytoscape-cose-bilkent';
import { GraphViewerProps, FlowchartNode, FlowchartEdge } from '../types';
import { useFlowchartFunStore } from '../stores/flowchart-fun.store';
import { THEMES, LAYOUT_ALGORITHMS } from '../constants';
import { Button } from '@/components/ui/button';
import { 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  RotateCcw,
  Download,
  Settings,
  Move3D
} from 'lucide-react';
import { Card } from '@/components/ui/card';

// Register cytoscape extensions
cytoscape.use(dagre);
cytoscape.use(cose);

interface FlowchartGraphViewerProps extends Omit<GraphViewerProps, 'graph'> {
  showControls?: boolean;
  showMinimap?: boolean;
}

export function FlowchartGraphViewer({ 
  className = '',
  onNodeClick,
  onEdgeClick,
  onBackgroundClick,
  interactive = true,
  showControls = true,
  showMinimap = false
}: FlowchartGraphViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const cyRef = useRef<Core | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  const {
    parsedGraph,
    selectedNode,
    selectedEdge,
    theme,
    layoutAlgorithm,
    selectNode,
    selectEdge,
    clearSelection
  } = useFlowchartFunStore();

  // Initialize cytoscape
  useEffect(() => {
    if (!containerRef.current || isInitialized) return;

    const cy = cytoscape({
      container: containerRef.current,
      style: getCytoscapeStyle(theme),
      layout: LAYOUT_ALGORITHMS[layoutAlgorithm],
      minZoom: 0.1,
      maxZoom: 3,
      wheelSensitivity: 0.2,
      userPanningEnabled: interactive,
      userZoomingEnabled: interactive,
      boxSelectionEnabled: interactive,
      selectionType: 'single',
      autoungrabify: !interactive,
      autounselectify: !interactive
    });

    // Event handlers
    if (interactive) {
      cy.on('tap', 'node', (evt) => {
        const node = evt.target;
        const nodeId = node.id();
        selectNode(nodeId);
        onNodeClick?.(convertCyNodeToFlowchartNode(node));
      });

      cy.on('tap', 'edge', (evt) => {
        const edge = evt.target;
        const edgeId = edge.id();
        selectEdge(edgeId);
        onEdgeClick?.(convertCyEdgeToFlowchartEdge(edge));
      });

      cy.on('tap', (evt) => {
        if (evt.target === cy) {
          clearSelection();
          onBackgroundClick?.();
        }
      });

      // Node hover effects
      cy.on('mouseover', 'node', (evt) => {
        const node = evt.target;
        node.style('background-color', '#2563eb');
      });

      cy.on('mouseout', 'node', (evt) => {
        const node = evt.target;
        if (node.id() !== selectedNode) {
          node.style('background-color', THEMES[theme].node['background-color']);
        }
      });
    }

    cyRef.current = cy;
    setIsInitialized(true);

    return () => {
      if (cyRef.current) {
        cyRef.current.destroy();
        cyRef.current = null;
        setIsInitialized(false);
      }
    };
  }, [theme, interactive]);

  // Update graph data
  useEffect(() => {
    if (!cyRef.current || !parsedGraph) return;

    const elements = convertGraphToCytoscapeElements(parsedGraph);

    cyRef.current.elements().remove();
    cyRef.current.add(elements);

    // Apply layout
    const layout = cyRef.current.layout(LAYOUT_ALGORITHMS[layoutAlgorithm]);
    layout.run();

    // Fit to viewport
    setTimeout(() => {
      cyRef.current?.fit(undefined, 50);
    }, 100);
  }, [parsedGraph, layoutAlgorithm]);

  // Update selection
  useEffect(() => {
    if (!cyRef.current) return;

    // Clear previous selection
    cyRef.current.elements().unselect();
    cyRef.current.elements().style('background-color', THEMES[theme].node['background-color']);
    cyRef.current.elements().style('line-color', THEMES[theme].edge['line-color']);

    // Apply new selection
    if (selectedNode) {
      const node = cyRef.current.getElementById(selectedNode);
      node.select();
      node.style('background-color', '#dc2626');
    }

    if (selectedEdge) {
      const edge = cyRef.current.getElementById(selectedEdge);
      edge.select();
      edge.style('line-color', '#dc2626');
    }
  }, [selectedNode, selectedEdge, theme]);

  // Update theme
  useEffect(() => {
    if (!cyRef.current) return;
    
    cyRef.current.style(getCytoscapeStyle(theme));
  }, [theme]);

  // Control handlers
  const handleZoomIn = () => {
    cyRef.current?.zoom(cyRef.current.zoom() * 1.2);
  };

  const handleZoomOut = () => {
    cyRef.current?.zoom(cyRef.current.zoom() * 0.8);
  };

  const handleFit = () => {
    cyRef.current?.fit(undefined, 50);
  };

  const handleCenter = () => {
    cyRef.current?.center();
  };

  const handleReset = () => {
    cyRef.current?.reset();
  };

  const handleExportImage = async () => {
    if (!cyRef.current) return;

    try {
      const png = cyRef.current.png({
        output: 'blob',
        bg: THEMES[theme].background,
        full: true,
        scale: 2
      });

      const url = URL.createObjectURL(png);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'flowchart.png';
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className={`relative h-full ${className}`}>
      {/* Graph container */}
      <div 
        ref={containerRef} 
        className="w-full h-full bg-white dark:bg-gray-900"
        style={{ backgroundColor: THEMES[theme].background }}
      />
      
      {/* Controls */}
      {showControls && (
        <Card className="absolute top-4 right-4 p-2">
          <div className="flex flex-col space-y-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomIn}
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomOut}
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFit}
              title="Fit to View"
            >
              <Maximize className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCenter}
              title="Center"
            >
              <Move3D className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              title="Reset View"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <div className="w-full h-px bg-gray-200 dark:bg-gray-700 my-1" />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportImage}
              title="Export as PNG"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      )}

      {/* Empty state */}
      {!parsedGraph && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="text-lg font-medium mb-2">No flowchart to display</div>
            <div className="text-sm">Start typing in the editor to create your flowchart</div>
          </div>
        </div>
      )}

      {/* Loading state */}
      {parsedGraph && !isInitialized && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Rendering graph...</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper functions
function getCytoscapeStyle(theme: 'light' | 'dark', ffTheme?: any) {
  // If FFTheme is provided, use it; otherwise fall back to legacy theme
  if (ffTheme) {
    return [
      {
        selector: 'node',
        style: {
          'background-color': ffTheme.nodeBackground,
          'border-color': ffTheme.borderColor,
          'border-width': ffTheme.borderWidth,
          'label': 'data(label)',
          'text-valign': 'center',
          'text-halign': 'center',
          'font-family': ffTheme.fontFamily,
          'font-size': '12px',
          'color': ffTheme.nodeForeground,
          'text-wrap': 'wrap',
          'text-max-width': `${ffTheme.textMaxWidth}px`,
          'padding': `${ffTheme.padding}px`,
          'shape': ffTheme.shape,
          'line-height': ffTheme.lineHeight,
          'text-margin-y': ffTheme.textMarginY,
          ...(ffTheme.useFixedHeight && { 'height': `${ffTheme.fixedHeight}px` }),
        },
      },
      {
        selector: 'edge',
        style: {
          'width': ffTheme.edgeWidth,
          'line-color': ffTheme.edgeColor,
          'target-arrow-color': ffTheme.edgeColor,
          'source-arrow-color': ffTheme.edgeColor,
          'target-arrow-shape': ffTheme.targetArrowShape,
          'source-arrow-shape': ffTheme.sourceArrowShape,
          'curve-style': ffTheme.curveStyle,
          'label': 'data(label)',
          'font-size': `${ffTheme.edgeTextSize}px`,
          'font-family': ffTheme.fontFamily,
          'text-rotation': ffTheme.rotateEdgeLabel ? 'autorotate' : 'none',
          'text-margin-y': -10,
          'source-distance-from-node': ffTheme.sourceDistanceFromNode,
          'target-distance-from-node': ffTheme.targetDistanceFromNode,
          'arrow-scale': ffTheme.arrowScale,
        },
      },
      {
        selector: 'node:selected',
        style: {
          'border-color': '#3b82f6',
          'border-width': Math.max(ffTheme.borderWidth + 1, 3),
          'overlay-color': '#3b82f6',
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'edge:selected',
        style: {
          'line-color': '#3b82f6',
          'target-arrow-color': '#3b82f6',
          'source-arrow-color': '#3b82f6',
          'width': ffTheme.edgeWidth + 1,
          'overlay-color': '#3b82f6',
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'node:hover',
        style: {
          'overlay-color': ffTheme.borderColor,
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'edge:hover',
        style: {
          'overlay-color': ffTheme.edgeColor,
          'overlay-opacity': 0.1,
        },
      },
    ];
  }

  // Legacy theme fallback
  const themeConfig = THEMES[theme];

  return [
    {
      selector: 'node',
      style: themeConfig.node
    },
    {
      selector: 'edge',
      style: themeConfig.edge
    },
    {
      selector: 'node:selected',
      style: {
        'border-color': '#dc2626',
        'border-width': 3
      }
    },
    {
      selector: 'edge:selected',
      style: {
        'line-color': '#dc2626',
        'target-arrow-color': '#dc2626',
        'width': 3
      }
    }
  ];
}

function convertGraphToCytoscapeElements(graph: any): ElementDefinition[] {
  const elements: ElementDefinition[] = [];
  
  // Add nodes
  if (graph.nodes) {
    graph.nodes.forEach((node: FlowchartNode) => {
      elements.push({
        data: {
          id: node.id,
          label: node.label,
          ...node.data
        },
        position: node.position,
        classes: node.classes?.join(' ')
      });
    });
  }
  
  // Add edges
  if (graph.edges) {
    graph.edges.forEach((edge: FlowchartEdge) => {
      elements.push({
        data: {
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.label,
          ...edge.data
        },
        classes: edge.classes?.join(' ')
      });
    });
  }
  
  return elements;
}

function convertCyNodeToFlowchartNode(cyNode: any): FlowchartNode {
  return {
    id: cyNode.id(),
    label: cyNode.data('label'),
    data: cyNode.data(),
    position: cyNode.position()
  };
}

function convertCyEdgeToFlowchartEdge(cyEdge: any): FlowchartEdge {
  return {
    id: cyEdge.id(),
    source: cyEdge.source().id(),
    target: cyEdge.target().id(),
    label: cyEdge.data('label'),
    data: cyEdge.data()
  };
}
