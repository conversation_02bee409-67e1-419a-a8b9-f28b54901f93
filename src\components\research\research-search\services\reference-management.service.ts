/**
 * Reference Management Service
 * Handles citation tracking, reference collection, and academic formatting
 */

import { 
  ConsolidatedReference, 
  InTextCitation, 
  EnhancedSearchSource,
  SearchSource,
  Citation,
  DeepResearchSession
} from '../types';

export class ReferenceManagementService {
  private references: Map<string, ConsolidatedReference> = new Map();
  private citationCounter = 0;

  /**
   * Add a source and track its usage
   */
  addSource(source: SearchSource, sectionId: string): ConsolidatedReference {
    const enhancedSource = this.enhanceSource(source);
    const citationKey = this.generateCitationKey(enhancedSource);
    
    let reference = this.references.get(citationKey);
    
    if (!reference) {
      reference = {
        id: `ref_${Date.now()}_${this.citationCounter++}`,
        citationKey,
        formattedCitation: this.formatReference(enhancedSource),
        inTextCitations: [],
        source: enhancedSource,
        usageCount: 0,
        sections: []
      };
      this.references.set(citationKey, reference);
    }
    
    if (!reference.sections.includes(sectionId)) {
      reference.sections.push(sectionId);
    }
    
    return reference;
  }

  /**
   * Add an in-text citation
   */
  addInTextCitation(
    referenceKey: string, 
    text: string, 
    position: number, 
    sectionId: string,
    context: string,
    citationType: 'direct_quote' | 'paraphrase' | 'reference' | 'data' = 'reference'
  ): InTextCitation {
    const reference = this.references.get(referenceKey);
    if (!reference) {
      throw new Error(`Reference not found: ${referenceKey}`);
    }

    const citation: InTextCitation = {
      id: `citation_${Date.now()}_${position}`,
      text,
      position,
      sectionId,
      context,
      citationType
    };

    reference.inTextCitations.push(citation);
    reference.usageCount++;

    return citation;
  }

  /**
   * Extract citations from content and link to references
   */
  extractAndLinkCitations(content: string, sources: SearchSource[], sectionId: string): {
    processedContent: string;
    citations: InTextCitation[];
  } {
    const citations: InTextCitation[] = [];
    let processedContent = content;

    // Add sources to reference manager
    const references = sources.map(source => this.addSource(source, sectionId));

    // Citation patterns to look for
    const patterns = [
      // APA style: (Author, Year)
      /\(([^,)]+),\s*(\d{4})\)/g,
      // Numbered citations: [1], [2]
      /\[(\d+)\]/g,
      // Author-year: Smith (2023)
      /([A-Z][a-z]+)\s*\((\d{4})\)/g
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const citationText = match[0];
        const position = match.index;

        // Try to match with available references
        const matchingRef = references.find(ref => 
          this.matchesCitation(ref, citationText, match)
        );

        if (matchingRef) {
          const context = this.extractContext(content, position, 100);
          const citation = this.addInTextCitation(
            matchingRef.citationKey,
            citationText,
            position,
            sectionId,
            context
          );
          citations.push(citation);
        }
      }
    });

    return { processedContent, citations };
  }

  /**
   * Generate consolidated reference list with enhanced academic formatting
   */
  generateConsolidatedReferences(citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard' = 'apa'): string {
    const sortedReferences = Array.from(this.references.values())
      .filter(ref => ref.usageCount > 0)
      .sort((a, b) => {
        // Enhanced sorting: Academic sources first, then by author
        const aIsAcademic = a.source.isAcademic || a.source.isPeerReviewed;
        const bIsAcademic = b.source.isAcademic || b.source.isPeerReviewed;

        if (aIsAcademic && !bIsAcademic) return -1;
        if (!aIsAcademic && bIsAcademic) return 1;

        // Then sort alphabetically by author
        const aAuthor = this.extractFirstAuthor(a.source.title);
        const bAuthor = this.extractFirstAuthor(b.source.title);
        return aAuthor.localeCompare(bAuthor);
      });

    let referenceList = '## References\n\n';

    // Group references by type for better organization
    const academicRefs = sortedReferences.filter(r => r.source.isAcademic || r.source.isPeerReviewed);
    const governmentRefs = sortedReferences.filter(r => r.source.isGovernment && !r.source.isAcademic);
    const otherRefs = sortedReferences.filter(r => !r.source.isAcademic && !r.source.isGovernment && !r.source.isPeerReviewed);

    // Academic sources first
    if (academicRefs.length > 0) {
      academicRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref.source, citationStyle);
        referenceList += `${formattedRef}\n\n`;
      });
    }

    // Government sources
    if (governmentRefs.length > 0) {
      governmentRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref.source, citationStyle);
        referenceList += `${formattedRef}\n\n`;
      });
    }

    // Other sources
    if (otherRefs.length > 0) {
      otherRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref.source, citationStyle);
        referenceList += `${formattedRef}\n\n`;
      });
    }

    // Enhanced statistics
    referenceList += `---\n\n**Reference Quality Assessment:**\n\n`;
    referenceList += `- **Total References:** ${sortedReferences.length}\n`;
    referenceList += `- **Academic/Peer-Reviewed Sources:** ${academicRefs.length} (${((academicRefs.length / sortedReferences.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Government/Official Sources:** ${governmentRefs.length} (${((governmentRefs.length / sortedReferences.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Other Sources:** ${otherRefs.length} (${((otherRefs.length / sortedReferences.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Average Quality Score:** ${this.calculateAverageQuality(sortedReferences).toFixed(2)}/1.0\n`;
    referenceList += `- **Citation Style:** ${citationStyle.toUpperCase()}\n\n`;

    // Quality recommendations
    if (academicRefs.length / sortedReferences.length < 0.6) {
      referenceList += `**Quality Recommendation:** Consider increasing the proportion of academic and peer-reviewed sources for stronger scholarly support.\n\n`;
    }

    return referenceList;
  }

  /**
   * Enhance basic source with quality metrics
   */
  private enhanceSource(source: SearchSource): EnhancedSearchSource {
    return {
      ...source,
      qualityScore: this.calculateQualityScore(source),
      isGovernment: this.isGovernmentSource(source),
      isPeerReviewed: this.isPeerReviewedSource(source),
      dataQuality: this.assessDataQuality(source),
      recency: this.assessRecency(source)
    };
  }

  /**
   * Calculate source quality score
   */
  private calculateQualityScore(source: SearchSource): number {
    let score = 0.5; // Base score

    // Domain reputation
    if (this.isAcademicDomain(source.domain)) score += 0.3;
    if (this.isGovernmentDomain(source.domain)) score += 0.25;
    if (this.isNewsOrganization(source.domain)) score += 0.1;

    // Content quality indicators
    if (source.snippet.length > 200) score += 0.1;
    if (source.publishedDate) score += 0.1;
    if (source.type === 'academic') score += 0.2;

    // Search relevance
    score += Math.min(source.score * 0.3, 0.3);

    return Math.min(score, 1.0);
  }

  /**
   * Check if source is from government
   */
  private isGovernmentSource(source: SearchSource): boolean {
    const govDomains = ['.gov', '.edu', '.org'];
    const govKeywords = ['government', 'federal', 'state', 'department', 'agency'];
    
    return govDomains.some(domain => source.domain.includes(domain)) ||
           govKeywords.some(keyword => source.title.toLowerCase().includes(keyword));
  }

  /**
   * Check if source is peer-reviewed
   */
  private isPeerReviewedSource(source: SearchSource): boolean {
    const academicDomains = ['pubmed', 'scholar.google', 'jstor', 'springer', 'elsevier', 'wiley'];
    const academicKeywords = ['journal', 'peer-reviewed', 'research', 'study', 'analysis'];
    
    return academicDomains.some(domain => source.domain.includes(domain)) ||
           academicKeywords.some(keyword => source.title.toLowerCase().includes(keyword));
  }

  /**
   * Generate citation key (e.g., "Smith2023")
   */
  private generateCitationKey(source: EnhancedSearchSource): string {
    const author = this.extractFirstAuthor(source.title);
    const year = this.extractYear(source.publishedDate || source.title);
    return `${author}${year}`;
  }

  /**
   * Format reference according to academic style standards
   */
  private formatReferenceByStyle(source: EnhancedSearchSource, style: string): string {
    const author = this.extractAuthorName(source);
    const year = this.extractYear(source.publishedDate || source.title);
    const title = this.cleanTitle(source.title);
    const url = source.url;
    const domain = this.extractDomainName(source.domain);
    const accessDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    switch (style) {
      case 'apa':
        const doi = this.extractDOI(source);
        const journal = this.extractJournalName(source);
        const volume = this.extractVolume(source);
        const issue = this.extractIssue(source);
        const pages = this.extractPages(source);

        if (source.isAcademic || source.isPeerReviewed) {
          // Academic source format with complete bibliographic information
          let reference = `${author} (${year}). ${title}.`;

          if (journal) {
            reference += ` *${journal}*`;
            if (volume) {
              reference += `, *${volume}*`;
              if (issue) reference += `(${issue})`;
              if (pages) reference += `, ${pages}`;
            }
            reference += '.';
          }

          if (doi) {
            reference += ` https://doi.org/${doi}`;
          } else if (journal) {
            reference += ` Retrieved from ${url}`;
          } else {
            reference += ` ${url}`;
          }

          return reference;
        } else if (source.isGovernment) {
          // Government source format
          const reference = doi ? `https://doi.org/${doi}` : url;
          return `${author} (${year}). *${title}*. ${this.extractOrganizationName(domain)}. ${reference}`;
        } else {
          // Web source format
          const reference = doi ? `https://doi.org/${doi}` : url;
          return `${author} (${year}). ${title}. *${this.extractDomainName(domain)}*. Retrieved ${accessDate} from ${reference}`;
        }

      case 'mla':
        if (source.isAcademic || source.isPeerReviewed) {
          const journal = this.extractJournalName(source);
          return journal
            ? `${author}. "${title}" *${journal}*, ${year}, ${url}.`
            : `${author}. "${title}" *${domain}*, ${year}, ${url}.`;
        } else {
          return `${author}. "${title}" *${domain}*, ${year}, ${url}. Accessed ${accessDate}.`;
        }

      case 'chicago':
        if (source.isAcademic || source.isPeerReviewed) {
          const journal = this.extractJournalName(source);
          return journal
            ? `${author}. "${title}" *${journal}* (${year}). ${url}.`
            : `${author}. "${title}" *${domain}*, ${year}. ${url}.`;
        } else {
          return `${author}. "${title}" *${domain}*. Accessed ${accessDate}. ${url}.`;
        }

      case 'harvard':
        return `${author} ${year}, '${title}', *${domain}*, viewed ${accessDate}, <${url}>.`;

      default:
        return `${author} (${year}). ${title}. Retrieved from ${url}`;
    }
  }

  /**
   * Extract and format author name properly (enhanced to avoid website names)
   */
  private extractAuthorName(source: EnhancedSearchSource): string {
    const content = source.snippet || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    // Enhanced author patterns to find real authors
    const authorPatterns = [
      // Standard academic patterns
      /(?:by|author[s]?:|written by)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+)+)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.(?:\s*[A-Z]\.)*(?:\s*,?\s*&?\s*[A-Z][a-z]+,?\s+[A-Z]\.)*(?:\s+et\s+al\.?)?/,
      /([A-Z][a-z]+\s+et\s+al\.?)/i,
      // First author in citation format
      /([A-Z][a-z]+),?\s+[A-Z]\.?\s*(?:\([0-9]{4}\))?/,
      // Author in academic format
      /^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\(/
    ];

    for (const pattern of authorPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        const author = match[1].trim();
        // Avoid website names
        if (!this.isWebsiteName(author)) {
          return this.formatAuthorName(author);
        }
      }
    }

    // For government sources, use organization name
    if (source.isGovernment) {
      return this.getOrganizationName(source.domain);
    }

    // Last resort: use "Anonymous" instead of website name
    return 'Anonymous';
  }

  /**
   * Check if a name is likely a website/platform name
   */
  private isWebsiteName(name: string): boolean {
    const websiteNames = [
      'researchgate', 'sciencedirect', 'springer', 'nature', 'science',
      'pubmed', 'arxiv', 'jstor', 'wiley', 'elsevier', 'google',
      'scholar', 'academia', 'frontiers', 'mdpi', 'plos', 'ieee',
      'acm', 'bmj', 'nejm', 'lancet', 'cell'
    ];

    return websiteNames.some(site =>
      name.toLowerCase().includes(site) ||
      name.toLowerCase() === site
    );
  }

  /**
   * Format author name for citations
   */
  private formatAuthorName(name: string): string {
    // Handle "et al." cases
    if (name.toLowerCase().includes('et al')) {
      return name;
    }

    // Handle single names or organization names
    if (!name.includes(' ') || name.length < 4) {
      return name;
    }

    // For full names, format as "Last, F."
    const parts = name.split(' ');
    if (parts.length >= 2) {
      const lastName = parts[parts.length - 1];
      const firstInitial = parts[0].charAt(0).toUpperCase();
      return `${lastName}, ${firstInitial}.`;
    }

    return name;
  }

  /**
   * Get organization name from domain
   */
  private getOrganizationName(domain: string): string {
    const orgMap: { [key: string]: string } = {
      'nih.gov': 'National Institutes of Health',
      'cdc.gov': 'Centers for Disease Control and Prevention',
      'who.int': 'World Health Organization',
      'nature.com': 'Nature Publishing Group',
      'science.org': 'American Association for the Advancement of Science',
      'pubmed.ncbi.nlm.nih.gov': 'National Center for Biotechnology Information',
      'scholar.google.com': 'Google Scholar',
      'researchgate.net': 'ResearchGate',
      'arxiv.org': 'arXiv',
      'jstor.org': 'JSTOR'
    };

    for (const [domainKey, orgName] of Object.entries(orgMap)) {
      if (domain.includes(domainKey)) {
        return orgName;
      }
    }

    // Extract organization from domain
    const domainParts = domain.split('.');
    if (domainParts.length >= 2) {
      const orgName = domainParts[domainParts.length - 2];
      return orgName.charAt(0).toUpperCase() + orgName.slice(1);
    }

    return 'Unknown Author';
  }

  /**
   * Clean and format title
   */
  private cleanTitle(title: string): string {
    // Remove common prefixes and clean up
    return title
      .replace(/^(Abstract|Summary|Introduction|Overview):\s*/i, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract journal name from source
   */
  private extractJournalName(source: EnhancedSearchSource): string | null {
    const content = source.snippet || '';
    const title = source.title || '';

    // Common journal patterns
    const journalPatterns = [
      /published in\s+([A-Z][a-zA-Z\s&]+Journal[a-zA-Z\s]*)/i,
      /([A-Z][a-zA-Z\s&]+Journal[a-zA-Z\s]*)/i,
      /(Nature|Science|Cell|PNAS|BMJ|NEJM|Lancet)/i
    ];

    for (const pattern of journalPatterns) {
      const match = content.match(pattern) || title.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Extract clean domain name
   */
  private extractDomainName(domain: string): string {
    // Clean up domain for display
    return domain
      .replace(/^www\./, '')
      .replace(/\.com$|\.org$|\.gov$|\.edu$/, '')
      .split('.')[0]
      .charAt(0).toUpperCase() + domain.split('.')[0].slice(1);
  }

  /**
   * Extract DOI from source
   */
  private extractDOI(source: EnhancedSearchSource): string | null {
    const content = source.snippet || '';
    const url = source.url || '';
    const title = source.title || '';
    const fullText = `${title} ${content} ${url}`;

    // DOI patterns
    const doiPatterns = [
      /doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /doi:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /https?:\/\/doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /DOI:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /digital object identifier:\s*([0-9]+\.[0-9]+\/[^\s]+)/i
    ];

    for (const pattern of doiPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1].trim().replace(/[.,;]$/, ''); // Remove trailing punctuation
      }
    }

    return null;
  }

  /**
   * Extract first author from title or content
   */
  private extractFirstAuthor(title: string): string {
    // Simple extraction - in real implementation, this would be more sophisticated
    const words = title.split(' ');
    return words[0] || 'Unknown';
  }

  /**
   * Extract year from date or content
   */
  private extractYear(dateOrContent: string): string {
    const yearMatch = dateOrContent.match(/\b(19|20)\d{2}\b/);
    return yearMatch ? yearMatch[0] : new Date().getFullYear().toString();
  }

  /**
   * Check if citation matches reference
   */
  private matchesCitation(reference: ConsolidatedReference, citationText: string, match: RegExpExecArray): boolean {
    const citationKey = reference.citationKey.toLowerCase();
    const text = citationText.toLowerCase();
    
    // Simple matching - could be enhanced
    return text.includes(citationKey.slice(0, 4)) || 
           text.includes(reference.source.domain.split('.')[0]);
  }

  /**
   * Extract context around citation
   */
  private extractContext(content: string, position: number, contextLength: number): string {
    const start = Math.max(0, position - contextLength);
    const end = Math.min(content.length, position + contextLength);
    return content.substring(start, end);
  }

  /**
   * Assess data quality
   */
  private assessDataQuality(source: SearchSource): 'high' | 'medium' | 'low' {
    if (source.type === 'academic' || this.isGovernmentSource(source)) return 'high';
    if (source.score > 0.7) return 'medium';
    return 'low';
  }

  /**
   * Assess recency
   */
  private assessRecency(source: SearchSource): 'current' | 'recent' | 'dated' {
    if (!source.publishedDate) return 'dated';
    
    const publishedYear = parseInt(this.extractYear(source.publishedDate));
    const currentYear = new Date().getFullYear();
    const yearsDiff = currentYear - publishedYear;
    
    if (yearsDiff <= 1) return 'current';
    if (yearsDiff <= 3) return 'recent';
    return 'dated';
  }

  /**
   * Check if domain is academic
   */
  private isAcademicDomain(domain: string): boolean {
    const academicDomains = ['.edu', 'scholar.google', 'pubmed', 'jstor', 'springer'];
    return academicDomains.some(d => domain.includes(d));
  }

  /**
   * Check if domain is government
   */
  private isGovernmentDomain(domain: string): boolean {
    return domain.includes('.gov');
  }

  /**
   * Check if domain is news organization
   */
  private isNewsOrganization(domain: string): boolean {
    const newsDomains = ['reuters', 'bbc', 'cnn', 'nytimes', 'washingtonpost'];
    return newsDomains.some(d => domain.includes(d));
  }

  /**
   * Format basic reference
   */
  private formatReference(source: EnhancedSearchSource): string {
    return this.formatReferenceByStyle(source, 'apa');
  }

  /**
   * Calculate average quality score
   */
  private calculateAverageQuality(references: ConsolidatedReference[]): number {
    if (references.length === 0) return 0;
    const total = references.reduce((sum, ref) => sum + ref.source.qualityScore, 0);
    return total / references.length;
  }

  /**
   * Clear all references (for new research session)
   */
  clear(): void {
    this.references.clear();
    this.citationCounter = 0;
  }

  /**
   * Get all references
   */
  getAllReferences(): ConsolidatedReference[] {
    return Array.from(this.references.values());
  }

  /**
   * Get references by section
   */
  getReferencesBySection(sectionId: string): ConsolidatedReference[] {
    return Array.from(this.references.values())
      .filter(ref => ref.sections.includes(sectionId));
  }

  /**
   * Extract volume from source
   */
  private extractVolume(source: EnhancedSearchSource): string | null {
    const content = source.snippet || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const volumePatterns = [
      /volume\s*(\d+)/i,
      /vol\.?\s*(\d+)/i,
      /v\.?\s*(\d+)/i,
      /\s(\d+)\s*\(\d+\)/,  // Pattern like "45(2)"
      /,\s*(\d+)\s*\(/      // Pattern like ", 45("
    ];

    for (const pattern of volumePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Extract issue from source
   */
  private extractIssue(source: EnhancedSearchSource): string | null {
    const content = source.snippet || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const issuePatterns = [
      /issue\s*(\d+)/i,
      /no\.?\s*(\d+)/i,
      /number\s*(\d+)/i,
      /\((\d+)\)/,          // Pattern like "(2)"
      /vol\.?\s*\d+\s*\((\d+)\)/i  // Pattern like "vol. 45(2)"
    ];

    for (const pattern of issuePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Extract pages from source
   */
  private extractPages(source: EnhancedSearchSource): string | null {
    const content = source.snippet || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const pagePatterns = [
      /pages?\s*(\d+[-–]\d+)/i,
      /pp\.?\s*(\d+[-–]\d+)/i,
      /p\.?\s*(\d+[-–]\d+)/i,
      /(\d+)[-–](\d+)/,     // Pattern like "123-145"
      /,\s*(\d+[-–]\d+)\./  // Pattern like ", 123-145."
    ];

    for (const pattern of pagePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1] || `${match[1]}-${match[2]}`;
      }
    }

    return null;
  }

  /**
   * Extract organization name for government sources
   */
  private extractOrganizationName(domain: string): string {
    const orgMap: { [key: string]: string } = {
      'nih.gov': 'National Institutes of Health',
      'cdc.gov': 'Centers for Disease Control and Prevention',
      'fda.gov': 'Food and Drug Administration',
      'who.int': 'World Health Organization',
      'nasa.gov': 'National Aeronautics and Space Administration',
      'epa.gov': 'Environmental Protection Agency'
    };

    for (const [domainKey, orgName] of Object.entries(orgMap)) {
      if (domain.includes(domainKey)) {
        return orgName;
      }
    }

    return this.extractDomainName(domain);
  }
}

// Export singleton instance
export const referenceManagementService = new ReferenceManagementService();
