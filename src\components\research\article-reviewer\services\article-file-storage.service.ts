import { supabase } from '@/lib/supabase';

/**
 * Service for managing article file storage in Supabase Storage
 */
export class ArticleFileStorageService {
  private readonly BUCKET_NAME = 'article-files';

  /**
   * Initialize the storage bucket if it doesn't exist
   */
  async initializeBucket(): Promise<{ success: boolean; error?: any }> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) {
        console.error('Error listing buckets:', listError);
        return { success: false, error: listError };
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);

      if (!bucketExists) {
        // Create the bucket with proper policies
        const { error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: false, // Private bucket - users can only access their own files
          allowedMimeTypes: [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ],
          fileSizeLimit: 50 * 1024 * 1024 // 50MB limit
        });

        if (createError) {
          console.error('Error creating bucket:', createError);
          return { success: false, error: createError };
        }

        console.log('Article files bucket created successfully');
      }

      return { success: true };
    } catch (error) {
      console.error('Error initializing bucket:', error);
      return { success: false, error };
    }
  }

  /**
   * Upload an article file to Supabase Storage
   */
  async uploadArticleFile(
    file: File,
    userId: string,
    reviewId?: string
  ): Promise<{ filePath: string | null; error: any }> {
    try {
      // Ensure bucket exists
      const { success: bucketReady } = await this.initializeBucket();
      if (!bucketReady) {
        return { filePath: null, error: new Error('Failed to initialize storage bucket') };
      }

      // Generate unique file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop();
      const fileName = `${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}_${timestamp}.${fileExtension}`;
      const filePath = `${userId}/${reviewId || 'temp'}/${fileName}`;

      // Upload file
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Error uploading file:', error);
        return { filePath: null, error };
      }

      return { filePath: data.path, error: null };
    } catch (error) {
      console.error('Error in uploadArticleFile:', error);
      return { filePath: null, error };
    }
  }

  /**
   * Get a signed URL for downloading a file
   */
  async getFileDownloadUrl(filePath: string, expiresIn = 3600): Promise<{ url: string | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn);

      if (error) {
        console.error('Error creating signed URL:', error);
        return { url: null, error };
      }

      return { url: data.signedUrl, error: null };
    } catch (error) {
      console.error('Error in getFileDownloadUrl:', error);
      return { url: null, error };
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(filePath: string): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error('Error deleting file:', error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error in deleteFile:', error);
      return { success: false, error };
    }
  }

  /**
   * Move a file from temp location to permanent location
   */
  async moveFileToReview(
    tempFilePath: string,
    userId: string,
    reviewId: string
  ): Promise<{ newFilePath: string | null; error: any }> {
    try {
      // Extract filename from temp path
      const fileName = tempFilePath.split('/').pop();
      if (!fileName) {
        return { newFilePath: null, error: new Error('Invalid file path') };
      }

      const newFilePath = `${userId}/${reviewId}/${fileName}`;

      // Move file by copying and then deleting original
      const { data: copyData, error: copyError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .copy(tempFilePath, newFilePath);

      if (copyError) {
        console.error('Error copying file:', copyError);
        return { newFilePath: null, error: copyError };
      }

      // Delete the temporary file
      await this.deleteFile(tempFilePath);

      return { newFilePath, error: null };
    } catch (error) {
      console.error('Error in moveFileToReview:', error);
      return { newFilePath: null, error };
    }
  }

  /**
   * Get file metadata
   */
  async getFileInfo(filePath: string): Promise<{ 
    size: number | null; 
    lastModified: string | null; 
    error: any 
  }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop()
        });

      if (error) {
        console.error('Error getting file info:', error);
        return { size: null, lastModified: null, error };
      }

      const fileInfo = data?.[0];
      if (!fileInfo) {
        return { size: null, lastModified: null, error: new Error('File not found') };
      }

      return { 
        size: fileInfo.metadata?.size || null, 
        lastModified: fileInfo.updated_at || fileInfo.created_at || null, 
        error: null 
      };
    } catch (error) {
      console.error('Error in getFileInfo:', error);
      return { size: null, lastModified: null, error };
    }
  }

  /**
   * List all files for a user
   */
  async getUserFiles(userId: string): Promise<{ files: any[] | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(userId, {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.error('Error listing user files:', error);
        return { files: null, error };
      }

      return { files: data, error: null };
    } catch (error) {
      console.error('Error in getUserFiles:', error);
      return { files: null, error };
    }
  }

  /**
   * Clean up temporary files older than 24 hours
   */
  async cleanupTempFiles(userId: string): Promise<{ cleaned: number; error: any }> {
    try {
      const { data: tempFiles, error: listError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(`${userId}/temp`);

      if (listError) {
        return { cleaned: 0, error: listError };
      }

      if (!tempFiles || tempFiles.length === 0) {
        return { cleaned: 0, error: null };
      }

      // Filter files older than 24 hours
      const oneDayAgo = new Date();
      oneDayAgo.setHours(oneDayAgo.getHours() - 24);

      const oldFiles = tempFiles.filter(file => {
        const fileDate = new Date(file.created_at || file.updated_at || '');
        return fileDate < oneDayAgo;
      });

      if (oldFiles.length === 0) {
        return { cleaned: 0, error: null };
      }

      // Delete old files
      const filePaths = oldFiles.map(file => `${userId}/temp/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove(filePaths);

      if (deleteError) {
        console.error('Error cleaning up temp files:', deleteError);
        return { cleaned: 0, error: deleteError };
      }

      return { cleaned: oldFiles.length, error: null };
    } catch (error) {
      console.error('Error in cleanupTempFiles:', error);
      return { cleaned: 0, error };
    }
  }

  /**
   * Get storage usage for a user
   */
  async getUserStorageUsage(userId: string): Promise<{ 
    totalSize: number; 
    fileCount: number; 
    error: any 
  }> {
    try {
      const { files, error } = await this.getUserFiles(userId);

      if (error || !files) {
        return { totalSize: 0, fileCount: 0, error };
      }

      const totalSize = files.reduce((sum, file) => {
        return sum + (file.metadata?.size || 0);
      }, 0);

      return { totalSize, fileCount: files.length, error: null };
    } catch (error) {
      console.error('Error in getUserStorageUsage:', error);
      return { totalSize: 0, fileCount: 0, error };
    }
  }
}

export const articleFileStorageService = new ArticleFileStorageService();
