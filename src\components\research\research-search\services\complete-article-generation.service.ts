/**
 * Complete Article Generation Service
 * Handles generation of complete detailed review articles with proper academic structure
 */

import { ResearchType, DeepResearchOutline, ResearchPoint, SearchSource, Citation } from '../types';
import { tavilySearchService } from './tavily-search.service';
import { researchAIService } from './research-ai.service';
import { academicFormattingService } from './academic-formatting.service';
import { enhancedReferenceManagementService } from './enhanced-reference-management.service';

export interface CompleteArticle {
  id: string;
  title: string;
  abstract: string;
  sections: ArticleSection[];
  references: ArticleReference[];
  metadata: ArticleMetadata;
  wordCount: number;
  citationCount: number;
  generatedAt: Date;
}

export interface ArticleSection {
  id: string;
  title: string;
  content: string;
  subsections?: ArticleSubsection[];
  citations: InlineCitation[];
  wordCount: number;
  order: number;
}

export interface ArticleSubsection {
  id: string;
  title: string;
  content: string;
  citations: InlineCitation[];
  wordCount: number;
}

export interface InlineCitation {
  id: string;
  text: string;
  position: number;
  referenceId: string;
  citationType: 'direct' | 'paraphrase' | 'support';
}

export interface ArticleReference {
  id: string;
  authors: string[];
  title: string;
  journal?: string;
  year: number;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  url?: string;
  type: 'journal' | 'book' | 'conference' | 'web' | 'report';
  formattedAPA: string;
  formattedMLA: string;
}

export interface ArticleMetadata {
  researchType: string;
  keywords: string[];
  academicLevel: string;
  targetAudience: string;
  estimatedReadingTime: number;
  qualityScore: number;
  completeness: number;
  citationDensity: number;
}

export interface ArticleGenerationOptions {
  model: string;
  researchType: ResearchType;
  outline: DeepResearchOutline;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  minWordCount: number;
  maxWordCount: number;
  minCitationsPerSection: number;
  includeAbstract: boolean;
  includeConclusion: boolean;
  academicLevel: 'undergraduate' | 'graduate' | 'doctoral' | 'professional';
  generateTables: boolean;
  generateFigures: boolean;
}

export interface GenerationProgress {
  stage: string;
  currentSection: string;
  progress: number;
  message: string;
  sectionsCompleted: number;
  totalSections: number;
  estimatedTimeRemaining: number;
}

class CompleteArticleGenerationService {
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found. Article generation will be limited.');
    }
  }

  /**
   * Generate a complete detailed review article
   */
  async generateCompleteArticle(
    options: ArticleGenerationOptions,
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<CompleteArticle> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key is required for article generation');
    }

    const startTime = Date.now();
    const totalSections = options.outline.points.length + (options.includeAbstract ? 1 : 0) + (options.includeConclusion ? 1 : 0);
    let sectionsCompleted = 0;

    // Initialize article structure
    const article: CompleteArticle = {
      id: `article_${Date.now()}`,
      title: options.outline.title,
      abstract: '',
      sections: [],
      references: [],
      metadata: {
        researchType: options.researchType.id,
        keywords: options.outline.keywords || [],
        academicLevel: options.academicLevel,
        targetAudience: options.outline.targetAudience || '',
        estimatedReadingTime: 0,
        qualityScore: 0,
        completeness: 0,
        citationDensity: 0
      },
      wordCount: 0,
      citationCount: 0,
      generatedAt: new Date()
    };

    try {
      // Stage 1: Generate Abstract (if requested)
      if (options.includeAbstract) {
        if (onProgress) {
          onProgress({
            stage: 'Generating Abstract',
            currentSection: 'Abstract',
            progress: (sectionsCompleted / totalSections) * 100,
            message: 'Creating comprehensive abstract...',
            sectionsCompleted,
            totalSections,
            estimatedTimeRemaining: this.estimateTimeRemaining(startTime, sectionsCompleted, totalSections)
          });
        }

        article.abstract = await this.generateAbstract(options);
        sectionsCompleted++;
      }

      // Stage 2: Generate main sections
      for (const point of options.outline.points) {
        if (onProgress) {
          onProgress({
            stage: 'Generating Sections',
            currentSection: point.title,
            progress: (sectionsCompleted / totalSections) * 100,
            message: `Writing section: ${point.title}...`,
            sectionsCompleted,
            totalSections,
            estimatedTimeRemaining: this.estimateTimeRemaining(startTime, sectionsCompleted, totalSections)
          });
        }

        const section = await this.generateSection(point, options, article.references);
        article.sections.push(section);
        sectionsCompleted++;
      }

      // Stage 3: Generate Conclusion (if requested)
      if (options.includeConclusion) {
        if (onProgress) {
          onProgress({
            stage: 'Generating Conclusion',
            currentSection: 'Conclusion',
            progress: (sectionsCompleted / totalSections) * 100,
            message: 'Writing comprehensive conclusion...',
            sectionsCompleted,
            totalSections,
            estimatedTimeRemaining: this.estimateTimeRemaining(startTime, sectionsCompleted, totalSections)
          });
        }

        const conclusion = await this.generateConclusion(article, options);
        article.sections.push(conclusion);
        sectionsCompleted++;
      }

      // Stage 4: Finalize article
      if (onProgress) {
        onProgress({
          stage: 'Finalizing',
          currentSection: 'References & Formatting',
          progress: 95,
          message: 'Finalizing references and formatting...',
          sectionsCompleted,
          totalSections,
          estimatedTimeRemaining: 0
        });
      }

      // Calculate final metrics
      article.wordCount = this.calculateWordCount(article);
      article.citationCount = this.calculateCitationCount(article);
      article.metadata.estimatedReadingTime = Math.ceil(article.wordCount / 200); // 200 words per minute
      article.metadata.qualityScore = this.calculateQualityScore(article, options);
      article.metadata.completeness = this.calculateCompleteness(article, options);
      article.metadata.citationDensity = article.citationCount / article.wordCount * 1000; // Citations per 1000 words

      // Sort references alphabetically
      article.references.sort((a, b) => a.authors[0]?.localeCompare(b.authors[0]) || 0);

      if (onProgress) {
        onProgress({
          stage: 'Complete',
          currentSection: 'Finished',
          progress: 100,
          message: 'Article generation completed successfully!',
          sectionsCompleted: totalSections,
          totalSections,
          estimatedTimeRemaining: 0
        });
      }

      return article;
    } catch (error) {
      console.error('Error generating complete article:', error);
      throw new Error(`Failed to generate article: ${error.message}`);
    }
  }

  /**
   * Generate abstract for the article
   */
  private async generateAbstract(options: ArticleGenerationOptions): Promise<string> {
    const systemPrompt = `You are an expert academic writer specializing in ${options.researchType.name}. Generate a comprehensive abstract that summarizes the key points, methodology, findings, and implications of the research.

The abstract should be:
- 200-300 words
- Structured with background, objectives, methods, results, and conclusions
- Written in academic style appropriate for ${options.academicLevel} level
- Clear and concise while being comprehensive`;

    const userPrompt = `Generate an abstract for this ${options.researchType.name}:

Title: ${options.outline.title}
Description: ${options.outline.description}

Key Points to Cover:
${options.outline.points.map(p => `- ${p.title}: ${p.description}`).join('\n')}

Keywords: ${options.outline.keywords?.join(', ') || 'Not specified'}
Target Audience: ${options.outline.targetAudience || options.academicLevel}

Write a comprehensive abstract that effectively summarizes this research.`;

    return await this.callOpenRouter(options.model, systemPrompt, userPrompt);
  }

  /**
   * Generate a complete section with citations
   */
  private async generateSection(
    point: ResearchPoint,
    options: ArticleGenerationOptions,
    existingReferences: ArticleReference[]
  ): Promise<ArticleSection> {
    // Search for relevant sources
    const sources = await this.searchForSources(point, options.minCitationsPerSection);
    
    // Convert sources to references
    const references = this.convertSourcesToReferences(sources);
    existingReferences.push(...references);

    // Generate section content with citations
    const content = await this.generateSectionContent(point, references, options);
    
    // Extract inline citations
    const citations = this.extractInlineCitations(content, references);

    const section: ArticleSection = {
      id: `section_${Date.now()}_${point.pointNumber}`,
      title: point.title,
      content: content,
      citations: citations,
      wordCount: content.split(/\s+/).length,
      order: point.pointNumber || 0
    };

    // Generate subsections if the point has subpoints
    if (point.subpoints && point.subpoints.length > 0) {
      section.subsections = await this.generateSubsections(point.subpoints, references, options);
    }

    return section;
  }

  /**
   * Generate conclusion section
   */
  private async generateConclusion(article: CompleteArticle, options: ArticleGenerationOptions): Promise<ArticleSection> {
    const systemPrompt = `You are an expert academic writer. Generate a comprehensive conclusion that synthesizes the key findings, discusses implications, identifies limitations, and suggests future research directions.

The conclusion should:
- Synthesize main findings from all sections
- Discuss theoretical and practical implications
- Acknowledge limitations
- Suggest future research directions
- Be 800-1200 words
- Maintain academic tone appropriate for ${options.academicLevel} level`;

    const sectionsContent = article.sections.map(s => `${s.title}: ${s.content.substring(0, 500)}...`).join('\n\n');

    const userPrompt = `Generate a comprehensive conclusion for this ${options.researchType.name}:

Title: ${article.title}
Abstract: ${article.abstract}

Main Sections Summary:
${sectionsContent}

Write a conclusion that effectively synthesizes the research, discusses implications, and provides direction for future work.`;

    const content = await this.callOpenRouter(options.model, systemPrompt, userPrompt);

    return {
      id: `conclusion_${Date.now()}`,
      title: 'Conclusion',
      content: content,
      citations: [],
      wordCount: content.split(/\s+/).length,
      order: article.sections.length + 1
    };
  }

  /**
   * Search for relevant sources for a research point
   */
  private async searchForSources(point: ResearchPoint, minSources: number): Promise<SearchSource[]> {
    const sources: SearchSource[] = [];
    const queries = point.searchQueries || [point.title, point.description];

    for (const query of queries.slice(0, 3)) { // Limit to 3 queries per point
      try {
        const results = await tavilySearchService.searchAcademic(query, {
          maxResults: Math.ceil(minSources / queries.length),
          searchDepth: 'comprehensive',
          includeImages: false
        });

        sources.push(...results.results);
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Error searching for "${query}":`, error);
      }
    }

    return sources.slice(0, minSources);
  }

  /**
   * Convert search sources to article references
   */
  private convertSourcesToReferences(sources: SearchSource[]): ArticleReference[] {
    return sources.map((source, index) => ({
      id: `ref_${Date.now()}_${index}`,
      authors: this.extractAuthors(source.title),
      title: source.title,
      year: source.publishedDate ? new Date(source.publishedDate).getFullYear() : new Date().getFullYear(),
      url: source.url,
      type: this.determineSourceType(source),
      formattedAPA: this.formatReferenceAPA(source),
      formattedMLA: this.formatReferenceMLA(source)
    }));
  }

  /**
   * Generate section content with proper citations
   */
  private async generateSectionContent(
    point: ResearchPoint,
    references: ArticleReference[],
    options: ArticleGenerationOptions
  ): Promise<string> {
    const systemPrompt = `You are an expert academic writer specializing in ${options.researchType.name}. Write a comprehensive section that incorporates the provided sources with proper in-text citations.

Requirements:
- 1000-1500 words
- Include ${options.minCitationsPerSection} citations minimum
- Use ${options.citationStyle.toUpperCase()} citation style
- Academic tone appropriate for ${options.academicLevel} level
- Integrate sources naturally into the narrative
- Provide critical analysis, not just summary`;

    const sourcesText = references.map((ref, index) => 
      `[${index + 1}] ${ref.authors.join(', ')} (${ref.year}). ${ref.title}`
    ).join('\n');

    const userPrompt = `Write a comprehensive section on: ${point.title}

Description: ${point.description}

Available Sources:
${sourcesText}

Write a detailed academic section that incorporates these sources with proper citations. Focus on critical analysis and synthesis rather than just summarizing sources.`;

    return await this.callOpenRouter(options.model, systemPrompt, userPrompt);
  }

  /**
   * Generate subsections for complex points
   */
  private async generateSubsections(
    subpoints: any[],
    references: ArticleReference[],
    options: ArticleGenerationOptions
  ): Promise<ArticleSubsection[]> {
    const subsections: ArticleSubsection[] = [];

    for (const subpoint of subpoints) {
      const content = await this.generateSubsectionContent(subpoint, references, options);
      const citations = this.extractInlineCitations(content, references);

      subsections.push({
        id: `subsection_${Date.now()}_${subpoint.subpointNumber}`,
        title: subpoint.title,
        content: content,
        citations: citations,
        wordCount: content.split(/\s+/).length
      });
    }

    return subsections;
  }

  /**
   * Generate content for a subsection
   */
  private async generateSubsectionContent(
    subpoint: any,
    references: ArticleReference[],
    options: ArticleGenerationOptions
  ): Promise<string> {
    const systemPrompt = `Write a focused subsection (400-600 words) that addresses the specific topic with academic rigor and proper citations.`;

    const userPrompt = `Write a subsection on: ${subpoint.title}
Description: ${subpoint.description}

Use the available references appropriately and maintain academic standards.`;

    return await this.callOpenRouter(options.model, systemPrompt, userPrompt);
  }

  // Helper methods
  private extractInlineCitations(content: string, references: ArticleReference[]): InlineCitation[] {
    const citations: InlineCitation[] = [];
    const citationRegex = /\(([^)]+),?\s*(\d{4})\)/g;
    let match;

    while ((match = citationRegex.exec(content)) !== null) {
      const author = match[1];
      const year = parseInt(match[2]);
      const position = match.index;

      const reference = references.find(ref => 
        ref.authors.some(a => a.includes(author)) && ref.year === year
      );

      if (reference) {
        citations.push({
          id: `citation_${Date.now()}_${citations.length}`,
          text: match[0],
          position: position,
          referenceId: reference.id,
          citationType: 'paraphrase'
        });
      }
    }

    return citations;
  }

  private extractAuthors(title: string): string[] {
    // Simple author extraction - in real implementation, this would be more sophisticated
    return ['Author, A.']; // Placeholder
  }

  private determineSourceType(source: SearchSource): 'journal' | 'book' | 'conference' | 'web' | 'report' {
    if (source.domain?.includes('journal') || source.title.includes('Journal')) return 'journal';
    if (source.domain?.includes('conference') || source.title.includes('Conference')) return 'conference';
    if (source.domain?.includes('book') || source.title.includes('Book')) return 'book';
    if (source.domain?.includes('gov') || source.title.includes('Report')) return 'report';
    return 'web';
  }

  private formatReferenceAPA(source: SearchSource): string {
    const year = source.publishedDate ? new Date(source.publishedDate).getFullYear() : new Date().getFullYear();
    return `Author, A. (${year}). ${source.title}. Retrieved from ${source.url}`;
  }

  private formatReferenceMLA(source: SearchSource): string {
    const year = source.publishedDate ? new Date(source.publishedDate).getFullYear() : new Date().getFullYear();
    return `Author, A. "${source.title}." Web. ${year}.`;
  }

  private calculateWordCount(article: CompleteArticle): number {
    let total = 0;
    if (article.abstract) total += article.abstract.split(/\s+/).length;
    total += article.sections.reduce((sum, section) => sum + section.wordCount, 0);
    return total;
  }

  private calculateCitationCount(article: CompleteArticle): number {
    return article.sections.reduce((sum, section) => sum + section.citations.length, 0);
  }

  private calculateQualityScore(article: CompleteArticle, options: ArticleGenerationOptions): number {
    let score = 0.5; // Base score

    // Word count factor
    if (article.wordCount >= options.minWordCount) score += 0.2;
    if (article.wordCount <= options.maxWordCount) score += 0.1;

    // Citation density factor
    const citationDensity = article.citationCount / article.wordCount * 1000;
    if (citationDensity >= 10) score += 0.2; // Good citation density

    return Math.min(score, 1.0);
  }

  private calculateCompleteness(article: CompleteArticle, options: ArticleGenerationOptions): number {
    let completeness = 0;

    // Check if all required sections are present
    if (options.includeAbstract && article.abstract) completeness += 0.2;
    if (article.sections.length >= options.outline.points.length) completeness += 0.6;
    if (options.includeConclusion && article.sections.some(s => s.title.toLowerCase().includes('conclusion'))) completeness += 0.2;

    return completeness;
  }

  private estimateTimeRemaining(startTime: number, completed: number, total: number): number {
    if (completed === 0) return 0;
    const elapsed = Date.now() - startTime;
    const avgTimePerSection = elapsed / completed;
    return Math.round((total - completed) * avgTimePerSection / 1000); // seconds
  }

  private async callOpenRouter(model: string, systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Complete Article Generation'
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 4000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }
}

export const completeArticleGenerationService = new CompleteArticleGenerationService();
