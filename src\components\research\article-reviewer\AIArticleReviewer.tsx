import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from '@/components/ui/badge';
import { FileCheck, FileX, Upload, FileText, CheckCircle, Eye, AlertCircle, FileSearch, Highlighter, Lightbulb, Book } from 'lucide-react';
import { 
  ArticleUploader,
  ArticleReviewResult,
  ArticleReviewProgress,
  AIModelSelector,
  ReviewScorecard
} from './components';
import { ArticleSection } from './types';
import { useArticleReview } from './hooks';

/**
 * AI Article Reviewer Component
 * 
 * This component allows users to upload academic papers for AI-powered review,
 * providing section-by-section analysis and improvement suggestions.
 */
export function AIArticleReviewer() {
  // Get review functionality from the hook
  const {
    articleFile,
    setArticleFile,
    parsedContent,
    reviewResults,
    currentSection,
    isAnalyzing,
    progress,
    selectedModel,
    availableModels,
    setSelectedModel,
    startReview,
    cancelReview,
  } = useArticleReview();
  
  // Active tab for the UI
  const [activeTab, setActiveTab] = useState<'upload' | 'review'>('upload');

  // Handle review completion to switch to results tab
  useEffect(() => {
    if (reviewResults && Object.keys(reviewResults).length > 0) {
      setActiveTab('review');
    }
  }, [reviewResults]);

  return (
    <div className="flex flex-col h-screen p-6 bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 overflow-y-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="bg-gradient-to-br from-blue-600 to-indigo-700 p-3 rounded-xl shadow-md mr-4 transform -rotate-3">
            <FileCheck className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
              AI Article Reviewer
            </h1>
            <p className="text-sm text-gray-600 flex items-center">
              Powered by <span className="font-medium ml-1 bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">{selectedModel.split('/').pop()?.toUpperCase() || 'AI'}</span>
            </p>
          </div>
        </div>
        
        {/* Display badge with current status */}
        {isAnalyzing && (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 shadow-sm">
            <Eye className="h-3 w-3 mr-1 animate-pulse" />
            Analyzing Article
          </Badge>
        )}
        
        {reviewResults && (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 shadow-sm">
            <CheckCircle className="h-3 w-3 mr-1" />
            Review Complete
          </Badge>
        )}
        
        {!isAnalyzing && !reviewResults && articleFile && (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 shadow-sm">
            <AlertCircle className="h-3 w-3 mr-1" />
            Ready to Analyze
          </Badge>
        )}
      </div>
      
      <div className="text-gray-700 mb-6 bg-gradient-to-r from-blue-50/80 to-indigo-50/50 p-6 rounded-xl shadow-sm border border-blue-200/30">
        <div className="mb-4 border-l-4 border-blue-500 pl-4 pb-1">
          <p className="text-xl font-medium bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
            In-depth Academic Paper Analysis
          </p>
          <p className="text-sm text-gray-600">
            Upload your research paper for a comprehensive AI-powered review with detailed sentence-level insights
          </p>
        </div>
        <p className="mb-4 text-gray-700 leading-relaxed">
          Our sophisticated AI reviewer analyzes your academic paper sequentially from introduction to conclusion, providing 
          detailed feedback on structure, clarity, academic rigor, and logical flow. Benefit from <span className="text-indigo-700 font-medium">sentence-by-sentence analysis</span> that 
          identifies specific problems and offers actionable suggestions for improvement.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-5">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center mb-2">
              <div className="p-1.5 rounded-md bg-blue-100/80 shadow-inner">
                <FileSearch className="h-4 w-4 text-blue-700" />
              </div>
              <span className="ml-2 font-medium text-blue-800">Sequential Analysis</span>
            </div>
            <p className="text-xs text-blue-700 leading-relaxed">Step-by-step review of each section, from introduction to conclusion, with context from previous sections</p>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-lg border border-green-200 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center mb-2">
              <div className="p-1.5 rounded-md bg-green-100/80 shadow-inner">
                <Highlighter className="h-4 w-4 text-green-700" />
              </div>
              <span className="ml-2 font-medium text-green-800">Sentence-level Feedback</span>
            </div>
            <p className="text-xs text-green-700">Identify specific problematic sentences with highlighted issues and suggestions</p>
          </div>
          <div className="bg-amber-50 p-3 rounded-lg border border-amber-100">
            <div className="flex items-center mb-2">
              <div className="p-1 rounded-md bg-amber-100">
                <CheckCircle className="h-4 w-4 text-amber-700" />
              </div>
              <span className="ml-2 font-medium text-amber-800">Academic Rigor</span>
            </div>
            <p className="text-xs text-amber-700">Assessment of methodology, citation practices, and logical consistency</p>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-100">
            <div className="flex items-center mb-2">
              <div className="p-1 rounded-md bg-purple-100">
                <CheckCircle className="h-4 w-4 text-purple-700" />
              </div>
              <span className="ml-2 font-medium text-purple-800">Actionable Insights</span>
            </div>
            <p className="text-xs text-purple-700">Concrete suggestions to improve each section and overall paper quality</p>
          </div>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'upload' | 'review')} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="upload" disabled={isAnalyzing}>
            <Upload className="h-4 w-4 mr-2" /> Upload Article
          </TabsTrigger>
          <TabsTrigger value="review" disabled={!reviewResults && !isAnalyzing}>
            <FileText className="h-4 w-4 mr-2" /> Review Results
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left column: Upload panel */}
            <div className="md:col-span-2">
              <ArticleUploader 
                articleFile={articleFile}
                setArticleFile={setArticleFile}
                parsedContent={parsedContent}
                onStartReview={startReview}
                isAnalyzing={isAnalyzing}
                onCancelReview={cancelReview}
              />
            </div>
            
            {/* Right column: Model selection and settings */}
            <div>
              <AIModelSelector 
                selectedModel={selectedModel}
                availableModels={availableModels}
                setSelectedModel={setSelectedModel}
                isDisabled={isAnalyzing}
              />
              
              {isAnalyzing && (
                <Card className="mt-6">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium flex items-center">
                      <Eye className="h-4 w-4 mr-2 text-blue-500" />
                      Review Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ArticleReviewProgress 
                      currentSection={currentSection}
                      progress={progress}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="review" className="mt-0">
          {reviewResults ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Left sidebar: Review scorecard */}
              <div className="md:col-span-1">
                <ReviewScorecard 
                  results={reviewResults} 
                />
              </div>
              
              {/* Main content: Detailed review results */}
              <div className="md:col-span-3">
                <ArticleReviewResult 
                  articleFile={articleFile} 
                  reviewResults={reviewResults}
                />
              </div>
            </div>
          ) : isAnalyzing ? (
            <div className="p-6 text-center">
              <ArticleReviewProgress 
                currentSection={currentSection}
                progress={progress}
              />
            </div>
          ) : (
            <div className="p-6 text-center">
              <FileX className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium">No Review Results Yet</h3>
              <p className="text-gray-500 mt-2">
                Upload an article and start the review process to see results here.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
