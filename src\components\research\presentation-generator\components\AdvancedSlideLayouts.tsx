import React from 'react';
import { PresentationTheme } from '../types';

interface LayoutProps {
  theme: PresentationTheme;
  isPreview?: boolean;
  children?: React.ReactNode;
  items?: any[];
}

// COLUMNS Layout - For comparisons
export function ColumnsLayout({ theme, isPreview, items = [] }: LayoutProps) {
  const columnCount = Math.min(items.length, 3);
  
  return (
    <div 
      className={`grid gap-6 h-full`}
      style={{ 
        gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
        padding: isPreview ? '1rem' : '2rem'
      }}
    >
      {items.map((item, index) => (
        <div
          key={index}
          style={{
            background: `linear-gradient(135deg, ${theme.colors.muted}20, ${theme.colors.accent}10)`,
            borderRadius: '16px',
            padding: isPreview ? '1rem' : '2rem',
            border: `2px solid ${theme.colors.accent}30`,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              height: '4px',
              background: `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.accent})`
            }}
          />
          <h3
            style={{
              fontFamily: theme.fonts.heading,
              fontSize: isPreview ? '1rem' : '1.5rem',
              color: theme.colors.primary,
              marginBottom: '1rem',
              fontWeight: '600'
            }}
          >
            {item.title || `Column ${index + 1}`}
          </h3>
          <p
            style={{
              fontFamily: theme.fonts.body,
              fontSize: isPreview ? '0.8rem' : '1rem',
              color: theme.colors.text,
              lineHeight: '1.6'
            }}
          >
            {item.description || 'Column content goes here'}
          </p>
        </div>
      ))}
    </div>
  );
}

// BULLETS Layout - For key points
export function BulletsLayout({ theme, isPreview, items = [] }: LayoutProps) {
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <h2
        style={{
          fontFamily: theme.fonts.heading,
          fontSize: isPreview ? '1.2rem' : '2rem',
          color: theme.colors.primary,
          marginBottom: '2rem',
          textAlign: 'center',
          background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.secondary})`,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}
      >
        Key Points
      </h2>
      <div className="space-y-4">
        {items.map((item, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              gap: '1rem',
              padding: '1.5rem',
              background: `linear-gradient(135deg, ${theme.colors.background}, ${theme.colors.muted}20)`,
              borderRadius: '12px',
              border: `1px solid ${theme.colors.accent}30`,
              boxShadow: `0 4px 12px ${theme.colors.primary}10`,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: isPreview ? '0.8rem' : '1rem',
                flexShrink: 0
              }}
            >
              {index + 1}
            </div>
            <div>
              <h4
                style={{
                  fontFamily: theme.fonts.heading,
                  fontSize: isPreview ? '0.9rem' : '1.2rem',
                  color: theme.colors.heading,
                  marginBottom: '0.5rem',
                  fontWeight: '600'
                }}
              >
                {item.title || `Point ${index + 1}`}
              </h4>
              <p
                style={{
                  fontFamily: theme.fonts.body,
                  fontSize: isPreview ? '0.7rem' : '1rem',
                  color: theme.colors.text,
                  lineHeight: '1.6'
                }}
              >
                {item.description || 'Bullet point description'}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// ICONS Layout - For concepts with symbols
export function IconsLayout({ theme, isPreview, items = [] }: LayoutProps) {
  const gridCols = Math.min(items.length, 3);
  
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <div 
        className="grid gap-6"
        style={{ gridTemplateColumns: `repeat(${gridCols}, 1fr)` }}
      >
        {items.map((item, index) => (
          <div
            key={index}
            style={{
              textAlign: 'center',
              padding: '2rem',
              background: `radial-gradient(circle at center, ${theme.colors.accent}15, transparent 70%)`,
              borderRadius: '20px',
              border: `2px solid ${theme.colors.accent}40`,
              position: 'relative'
            }}
          >
            <div
              style={{
                width: isPreview ? '40px' : '80px',
                height: isPreview ? '40px' : '80px',
                margin: '0 auto 1rem',
                background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: isPreview ? '1.2rem' : '2rem',
                boxShadow: `0 8px 24px ${theme.colors.primary}30`
              }}
            >
              {item.icon || '🚀'}
            </div>
            <h3
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: isPreview ? '0.9rem' : '1.3rem',
                color: theme.colors.primary,
                marginBottom: '0.5rem',
                fontWeight: '600'
              }}
            >
              {item.title || `Concept ${index + 1}`}
            </h3>
            <p
              style={{
                fontFamily: theme.fonts.body,
                fontSize: isPreview ? '0.7rem' : '0.9rem',
                color: theme.colors.text,
                lineHeight: '1.5'
              }}
            >
              {item.description || 'Icon description'}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

// CYCLE Layout - For processes and workflows
export function CycleLayout({ theme, isPreview, items = [] }: LayoutProps) {
  const radius = isPreview ? 80 : 150;
  const centerX = isPreview ? 120 : 200;
  const centerY = isPreview ? 80 : 150;
  
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem', position: 'relative' }}>
      <svg
        width={isPreview ? 240 : 400}
        height={isPreview ? 160 : 300}
        style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
      >
        {/* Draw connecting lines */}
        {items.map((_, index) => {
          const angle1 = (index * 2 * Math.PI) / items.length - Math.PI / 2;
          const angle2 = ((index + 1) * 2 * Math.PI) / items.length - Math.PI / 2;
          const x1 = centerX + radius * Math.cos(angle1);
          const y1 = centerY + radius * Math.sin(angle1);
          const x2 = centerX + radius * Math.cos(angle2);
          const y2 = centerY + radius * Math.sin(angle2);
          
          return (
            <line
              key={index}
              x1={x1}
              y1={y1}
              x2={x2}
              y2={y2}
              stroke={theme.colors.accent}
              strokeWidth="2"
              opacity="0.6"
            />
          );
        })}
        
        {/* Draw cycle items */}
        {items.map((item, index) => {
          const angle = (index * 2 * Math.PI) / items.length - Math.PI / 2;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          
          return (
            <g key={index}>
              <circle
                cx={x}
                cy={y}
                r={isPreview ? 20 : 30}
                fill={theme.colors.primary}
                stroke={theme.colors.accent}
                strokeWidth="3"
              />
              <text
                x={x}
                y={y + 5}
                textAnchor="middle"
                fill="white"
                fontSize={isPreview ? "10" : "14"}
                fontWeight="bold"
              >
                {index + 1}
              </text>
            </g>
          );
        })}
      </svg>
      
      {/* Item labels */}
      <div className="grid grid-cols-2 gap-4 mt-8">
        {items.map((item, index) => (
          <div
            key={index}
            style={{
              padding: '1rem',
              background: `${theme.colors.muted}20`,
              borderRadius: '8px',
              border: `1px solid ${theme.colors.accent}30`
            }}
          >
            <h4
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: isPreview ? '0.8rem' : '1rem',
                color: theme.colors.primary,
                marginBottom: '0.5rem'
              }}
            >
              {item.title || `Step ${index + 1}`}
            </h4>
            <p
              style={{
                fontFamily: theme.fonts.body,
                fontSize: isPreview ? '0.6rem' : '0.8rem',
                color: theme.colors.text,
                lineHeight: '1.4'
              }}
            >
              {item.description || 'Process step description'}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

// ARROWS Layout - For cause-effect or flows
export function ArrowsLayout({ theme, isPreview, items = [] }: LayoutProps) {
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <div className="flex items-center justify-between">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <div
              style={{
                flex: 1,
                textAlign: 'center',
                padding: '1.5rem',
                background: `linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.accent}15)`,
                borderRadius: '16px',
                border: `2px solid ${theme.colors.accent}40`,
                position: 'relative'
              }}
            >
              <h3
                style={{
                  fontFamily: theme.fonts.heading,
                  fontSize: isPreview ? '0.9rem' : '1.3rem',
                  color: theme.colors.primary,
                  marginBottom: '1rem',
                  fontWeight: '600'
                }}
              >
                {item.title || `Step ${index + 1}`}
              </h3>
              <p
                style={{
                  fontFamily: theme.fonts.body,
                  fontSize: isPreview ? '0.7rem' : '1rem',
                  color: theme.colors.text,
                  lineHeight: '1.5'
                }}
              >
                {item.description || 'Flow step description'}
              </p>
            </div>

            {index < items.length - 1 && (
              <div
                style={{
                  width: '60px',
                  height: '4px',
                  background: `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                  position: 'relative',
                  margin: '0 1rem'
                }}
              >
                <div
                  style={{
                    position: 'absolute',
                    right: '-8px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '0',
                    height: '0',
                    borderLeft: `12px solid ${theme.colors.accent}`,
                    borderTop: '8px solid transparent',
                    borderBottom: '8px solid transparent'
                  }}
                />
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

// TIMELINE Layout - For chronological progression
export function TimelineLayout({ theme, isPreview, items = [] }: LayoutProps) {
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <div className="relative">
        {/* Timeline line */}
        <div
          style={{
            position: 'absolute',
            left: '30px',
            top: '0',
            bottom: '0',
            width: '4px',
            background: `linear-gradient(180deg, ${theme.colors.primary}, ${theme.colors.accent})`,
            borderRadius: '2px'
          }}
        />

        {/* Timeline items */}
        <div className="space-y-8">
          {items.map((item, index) => (
            <div
              key={index}
              style={{
                position: 'relative',
                paddingLeft: '80px'
              }}
            >
              {/* Timeline dot */}
              <div
                style={{
                  position: 'absolute',
                  left: '18px',
                  top: '10px',
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                  border: `4px solid ${theme.colors.background}`,
                  boxShadow: `0 0 0 2px ${theme.colors.accent}40`
                }}
              />

              {/* Content */}
              <div
                style={{
                  background: `linear-gradient(135deg, ${theme.colors.background}, ${theme.colors.muted}20)`,
                  borderRadius: '12px',
                  padding: '1.5rem',
                  border: `1px solid ${theme.colors.accent}30`,
                  boxShadow: `0 4px 12px ${theme.colors.primary}10`
                }}
              >
                <h3
                  style={{
                    fontFamily: theme.fonts.heading,
                    fontSize: isPreview ? '0.9rem' : '1.3rem',
                    color: theme.colors.primary,
                    marginBottom: '0.5rem',
                    fontWeight: '600'
                  }}
                >
                  {item.title || `Event ${index + 1}`}
                </h3>
                <p
                  style={{
                    fontFamily: theme.fonts.body,
                    fontSize: isPreview ? '0.7rem' : '1rem',
                    color: theme.colors.text,
                    lineHeight: '1.6'
                  }}
                >
                  {item.description || 'Timeline event description'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// PYRAMID Layout - For hierarchical importance
export function PyramidLayout({ theme, isPreview, items = [] }: LayoutProps) {
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <div className="flex flex-col items-center space-y-4">
        {items.map((item, index) => {
          const width = 100 - (index * 20); // Decreasing width for pyramid effect

          return (
            <div
              key={index}
              style={{
                width: `${width}%`,
                padding: '1.5rem',
                background: `linear-gradient(135deg, ${theme.colors.primary}${20 + index * 10}, ${theme.colors.accent}${15 + index * 5})`,
                borderRadius: '12px',
                border: `2px solid ${theme.colors.accent}${40 + index * 10}`,
                textAlign: 'center',
                position: 'relative',
                boxShadow: `0 ${4 + index * 2}px ${12 + index * 4}px ${theme.colors.primary}${10 + index * 5}`
              }}
            >
              <h3
                style={{
                  fontFamily: theme.fonts.heading,
                  fontSize: isPreview ? `${1 - index * 0.1}rem` : `${1.5 - index * 0.1}rem`,
                  color: theme.colors.primary,
                  marginBottom: '0.5rem',
                  fontWeight: '600'
                }}
              >
                {item.title || `Level ${index + 1}`}
              </h3>
              <p
                style={{
                  fontFamily: theme.fonts.body,
                  fontSize: isPreview ? `${0.8 - index * 0.05}rem` : `${1 - index * 0.05}rem`,
                  color: theme.colors.text,
                  lineHeight: '1.5'
                }}
              >
                {item.description || 'Pyramid level description'}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// STAIRCASE Layout - For progressive advancement
export function StaircaseLayout({ theme, isPreview, items = [] }: LayoutProps) {
  return (
    <div style={{ padding: isPreview ? '1rem' : '2rem' }}>
      <div className="flex items-end justify-center space-x-4">
        {items.map((item, index) => {
          const height = 60 + (index * 40); // Increasing height for staircase effect

          return (
            <div
              key={index}
              style={{
                width: isPreview ? '80px' : '120px',
                height: isPreview ? `${height * 0.6}px` : `${height}px`,
                background: `linear-gradient(180deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                borderRadius: '8px 8px 0 0',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                alignItems: 'center',
                padding: '1rem 0.5rem',
                position: 'relative',
                boxShadow: `0 4px 12px ${theme.colors.primary}30`
              }}
            >
              <div
                style={{
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  background: theme.colors.background,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: theme.colors.primary,
                  fontWeight: 'bold',
                  fontSize: isPreview ? '0.7rem' : '1rem',
                  marginBottom: '0.5rem'
                }}
              >
                {index + 1}
              </div>
              <h4
                style={{
                  fontFamily: theme.fonts.heading,
                  fontSize: isPreview ? '0.6rem' : '0.8rem',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}
              >
                {item.title || `Step ${index + 1}`}
              </h4>
              <p
                style={{
                  fontFamily: theme.fonts.body,
                  fontSize: isPreview ? '0.5rem' : '0.7rem',
                  color: 'rgba(255,255,255,0.9)',
                  textAlign: 'center',
                  lineHeight: '1.3'
                }}
              >
                {item.description || 'Step description'}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
}
