import { Extension } from '@tiptap/core'
import Suggestion from '@tiptap/suggestion'

export interface AcademicSuggestionOptions {
  suggestion: {
    char: string
    startOfLine: boolean
    command: (props: { editor: any, range: any, props: any }) => void
  }
}

export const AcademicSuggestion = Extension.create<AcademicSuggestionOptions>({
  name: 'academicSuggestion',

  addOptions() {
    return {
      suggestion: {
        char: '/',
        startOfLine: true,
        command: () => null,
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
      }),
    ]
  },
})

// Academic terms and phrases for auto-suggestions
export const academicPhrases = [
  { title: 'Therefore', description: 'For conclusions' },
  { title: 'Furthermore', description: 'Adding information' },
  { title: 'However', description: 'Contrasting idea' },
  { title: 'Consequently', description: 'Show result' },
  { title: 'Moreover', description: 'Adding emphasis' },
  { title: 'In addition', description: 'Adding information' },
  { title: 'Nevertheless', description: 'Contrasting idea' },
  { title: 'The findings suggest that', description: 'Discussing results' },
  { title: 'Previous research has shown that', description: 'Citing literature' },
  { title: 'It is important to note that', description: 'Adding emphasis' },
  { title: 'In conclusion', description: 'Concluding section' },
  { title: 'The purpose of this study was to', description: 'Introduction' },
  { title: 'According to the literature', description: 'Citing sources' },
  { title: 'The results indicate that', description: 'Discussing findings' },
  { title: 'This phenomenon can be explained by', description: 'Analysis' },
]

// Define common academic section templates
export const sectionTemplates = [
  {
    title: 'Abstract Section',
    description: 'Insert a formatted abstract section',
    content: `<h2>Abstract</h2><p>This study investigates [research topic] through [methodology]. The findings suggest [brief summary of results]. These results indicate [brief conclusion and implications for the field].</p>`,
  },
  {
    title: 'Introduction Section',
    description: 'Insert a formatted introduction',
    content: `<h2>Introduction</h2><p>Recent developments in the field of [field] have led to a renewed interest in [specific topic]. This research aims to [research objective] by examining [specific aspects].</p><p>The following research questions will be addressed:</p><ul><li>What is the relationship between [variable A] and [variable B]?</li><li>How does [variable C] influence [outcome]?</li><li>To what extent does [factor] contribute to [phenomenon]?</li></ul>`,
  },
  {
    title: 'Literature Review',
    description: 'Insert a literature review section',
    content: `<h2>Literature Review</h2><p>The existing literature on [topic] reveals several important themes. [Author] (YEAR) argued that [key point]. Similarly, [Author] (YEAR) found that [related finding].</p><p>However, [Author] (YEAR) presented contradictory evidence suggesting that [opposing view]. This inconsistency in the literature highlights the need for further research in this area.</p>`,
  },
  {
    title: 'Methodology Section',
    description: 'Insert a methodology section',
    content: `<h2>Methodology</h2><p>This study employed a [quantitative/qualitative/mixed methods] approach to investigate [research question]. Data was collected through [data collection methods].</p><h3>Participants</h3><p>The study included [number] participants who [participant characteristics]. Participants were selected based on [selection criteria].</p><h3>Data Analysis</h3><p>The data was analyzed using [analysis techniques] to identify [patterns/relationships/themes].</p>`,
  },
  {
    title: 'Results Section',
    description: 'Insert a results section',
    content: `<h2>Results</h2><p>The analysis revealed several significant findings. First, [finding 1]. This suggests that [interpretation of finding 1].</p><p>Second, [finding 2], which indicates [interpretation of finding 2]. Figure 1 illustrates the relationship between [variables].</p><p>Finally, [finding 3] was observed, consistent with [related literature or hypothesis].</p>`,
  },
  {
    title: 'Discussion Section',
    description: 'Insert a discussion section',
    content: `<h2>Discussion</h2><p>The findings of this study suggest that [key insight]. This is consistent with [previous research] which found [similar finding].</p><p>However, the results also revealed [unexpected finding], which contradicts [existing theory or research]. This discrepancy might be explained by [possible explanation].</p><p>Several limitations should be acknowledged. First, [limitation 1]. Second, [limitation 2].</p>`,
  },
  {
    title: 'Conclusion Section',
    description: 'Insert a conclusion section',
    content: `<h2>Conclusion</h2><p>This study has examined [research focus] and has identified [key findings]. The results contribute to our understanding of [topic] by [contribution].</p><p>Future research should focus on [future direction 1] and [future direction 2] to address the limitations of the current study and further expand knowledge in this field.</p>`,
  },
  {
    title: 'References Section',
    description: 'Insert a references section',
    content: `<h2>References</h2><p style="text-indent: -2em; padding-left: 2em;">Author, A. A. (YEAR). Title of article. <em>Journal Title</em>, Volume(Issue), page range. DOI</p><p style="text-indent: -2em; padding-left: 2em;">Author, B. B. (YEAR). <em>Title of book</em>. Publisher.</p><p style="text-indent: -2em; padding-left: 2em;">Author, C. C., & Author, D. D. (YEAR). Title of chapter. In Editor, E. (Ed.), <em>Title of book</em> (pp. page range). Publisher.</p>`,
  },
]

// Command suggestions
export const slashCommands = [
  {
    title: 'Heading 1',
    description: 'Large section heading',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 1 }).run()
    },
  },
  {
    title: 'Heading 2',
    description: 'Medium section heading',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 2 }).run()
    },
  },
  {
    title: 'Bullet List',
    description: 'Create a bulleted list',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBulletList().run()
    },
  },
  {
    title: 'Numbered List',
    description: 'Create a numbered list',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleOrderedList().run()
    },
  },
  {
    title: 'Block Quote',
    description: 'Add a block quote',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBlockquote().run()
    },
  },
  {
    title: 'Code Block',
    description: 'Add a code block',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleCodeBlock().run()
    },
  },
  {
    title: 'Table',
    description: 'Insert a table',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
    },
  },
  ...sectionTemplates.map(template => ({
    title: template.title,
    description: template.description,
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).insertContent(template.content).run()
    },
  })),
]
