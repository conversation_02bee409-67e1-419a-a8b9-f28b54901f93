import { GoogleGenAI } from '@google/genai';
import { 
  CareerGenerationRequest, 
  CareerGenerationResponse, 
  CareerPath, 
  ResumeData 
} from '../types';
import { CAREER_PROMPTS } from '../constants';

/**
 * Google Gemini Career Service
 * Provides career path generation using Google's Gemini API
 */
export class GeminiCareerService {
  private ai: GoogleGenAI;
  private lastRequestTime: Date | null = null;
  private readonly delayBetweenRequests = 2000; // 2 seconds between requests

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('Google Gemini API key not found. Please add VITE_GEMINI_API_KEY to your .env file.');
    }

    if (apiKey.length < 20) {
      throw new Error('Google Gemini API key appears to be invalid. Please check your VITE_GEMINI_API_KEY.');
    }

    this.ai = new GoogleGenAI({ apiKey });
    console.log('Gemini Career service initialized');
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    return Boolean(apiKey && apiKey.length > 20);
  }

  /**
   * Test the API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeGeminiRequest(
        'Say "Hello, Career API test successful!" and nothing else.',
        'gemini-2.5-flash',
        { maxOutputTokens: 100, temperature: 0.1 }
      );

      if (response && response.trim().length > 0) {
        return { success: true };
      } else {
        return { success: false, error: 'Empty response from API' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate career paths using Gemini
   */
  async generateCareerPaths(request: CareerGenerationRequest): Promise<CareerGenerationResponse> {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildInitialAnalysisPrompt(request.resumeText, request.additionalContext);
      
      const response = await this.makeGeminiRequest(
        prompt,
        request.model,
        {
          maxOutputTokens: 4096,
          temperature: 0.7
        }
      );

      // Parse the JSON response
      const careerData = this.parseCareerResponse(response);
      
      // Add unique IDs and clean the data
      const careers: CareerPath[] = careerData.map((career: any, index: number) => ({
        id: `career-${Date.now()}-${index}`,
        ...this.cleanCareerData(career),
        connectPosition: this.getConnectPosition(index)
      }));

      const processingTime = Date.now() - startTime;

      return {
        careers,
        analysis: 'Career analysis completed using Google Gemini',
        confidence: 0.9,
        processingTime
      };
    } catch (error) {
      console.error('Error generating career paths with Gemini:', error);
      throw new Error('Failed to generate career paths. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for a specific career path
   */
  async generateDetailedAnalysis(
    career: CareerPath, 
    resumeData: ResumeData, 
    additionalContext: string,
    model: string
  ): Promise<CareerPath> {
    try {
      const prompt = this.buildDetailedAnalysisPrompt(career, resumeData.rawText, additionalContext);
      
      const response = await this.makeGeminiRequest(
        prompt,
        model,
        {
          maxOutputTokens: 4096,
          temperature: 0.7
        }
      );

      const detailedData = this.parseDetailedResponse(response);
      
      // Convert roadmap format to match expected structure
      const roadmap = detailedData.roadmap?.map((step: any) => {
        const timeframe = Object.keys(step)[0];
        const description = Object.values(step)[0] as string;
        return {
          timeframe,
          description,
          tasks: [description],
          resources: []
        };
      }) || [];

      return {
        ...career,
        workRequired: this.cleanText(detailedData.workRequired),
        aboutTheRole: this.cleanText(detailedData.aboutTheRole),
        whyItsGoodFit: this.cleanArray(detailedData.whyItsGoodFit || detailedData.whyItsagoodfit),
        roadmap,
        skills: this.cleanArray(detailedData.skills || []).slice(0, 5), // Limit to top 5 skills
        certifications: this.cleanArray(detailedData.certifications || []).slice(0, 3) // Limit to top 3 certs
      };
    } catch (error) {
      console.error('Error generating detailed analysis with Gemini:', error);
      throw new Error('Failed to generate detailed career analysis. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for all career paths
   */
  async generateAllDetailedAnalyses(
    careers: CareerPath[], 
    resumeData: ResumeData, 
    additionalContext: string,
    model: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<CareerPath[]> {
    const detailedCareers: CareerPath[] = [];
    
    for (let i = 0; i < careers.length; i++) {
      try {
        const detailedCareer = await this.generateDetailedAnalysis(
          careers[i], 
          resumeData, 
          additionalContext, 
          model
        );
        detailedCareers.push(detailedCareer);
        
        if (onProgress) {
          onProgress(i + 1, careers.length);
        }
        
        // Add delay to avoid rate limiting
        if (i < careers.length - 1) {
          await this.enforceRateLimit();
        }
      } catch (error) {
        console.error(`Error generating detailed analysis for ${careers[i].jobTitle}:`, error);
        // Include the original career without detailed analysis
        detailedCareers.push(careers[i]);
      }
    }
    
    return detailedCareers;
  }

  /**
   * Make a request to Gemini with error handling
   */
  private async makeGeminiRequest(
    prompt: string,
    model: string,
    config: {
      maxOutputTokens: number;
      temperature: number;
    }
  ): Promise<string> {
    await this.enforceRateLimit();

    try {
      const requestConfig = {
        generationConfig: {
          maxOutputTokens: config.maxOutputTokens,
          temperature: config.temperature
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: prompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model,
        config: requestConfig,
        contents,
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
        }
      }

      if (!fullText || fullText.trim().length === 0) {
        throw new Error('Empty response from Gemini API');
      }

      return fullText;
    } catch (error) {
      console.error('Gemini API request failed:', error);
      throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the initial analysis prompt
   */
  private buildInitialAnalysisPrompt(resumeText: string, additionalContext: string): string {
    return `You are a professional career counselor. Analyze the resume and suggest 6 realistic career paths.

Requirements:
- Focus on careers that match their existing skills and experience
- Provide concise, actionable information
- Use realistic timelines and salary ranges
- Consider career growth potential

<resume>
${resumeText}
</resume>

<additional_context>
${additionalContext || 'No additional context provided.'}
</additional_context>

Respond ONLY with valid JSON array:
[
  {
    "jobTitle": "Specific Job Title",
    "jobDescription": "One clear sentence describing the role",
    "timeline": "X-Y months",
    "salary": "$XXk - $YYk",
    "difficulty": "Low|Medium|High"
  }
]`;
  }

  /**
   * Build the detailed analysis prompt
   */
  private buildDetailedAnalysisPrompt(career: CareerPath, resumeData: string, additionalContext: string): string {
    return `You are a career transition expert. Provide focused guidance for transitioning into ${career.jobTitle}.

Requirements:
- Be concise and actionable
- Focus on practical steps
- Highlight relevant skills from their background
- Provide realistic timeline and effort estimates

<resume>
${resumeData}
</resume>

<additional_context>
${additionalContext || 'No additional context provided.'}
</additional_context>

<target_role>
Job Title: ${career.jobTitle}
Description: ${career.jobDescription}
Timeline: ${career.timeline}
</target_role>

Respond ONLY with valid JSON:
{
  "workRequired": "X-Y hours per week",
  "aboutTheRole": "2-3 sentences describing key responsibilities and impact",
  "whyItsGoodFit": ["Specific reason based on their background", "Another relevant reason", "Third compelling reason"],
  "roadmap": [
    {"Month 1-2": "Specific action steps"},
    {"Month 3-4": "Next phase actions"},
    {"Month 5-6": "Advanced preparation"}
  ],
  "skills": ["Essential skill 1", "Essential skill 2", "Essential skill 3", "Essential skill 4", "Essential skill 5"],
  "certifications": ["Most valuable cert 1", "Important cert 2"]
}`;
  }

  /**
   * Parse career response from Gemini
   */
  private parseCareerResponse(response: string): any[] {
    try {
      // Clean the response to extract JSON
      let cleanedResponse = response.trim();
      
      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/, '');
      
      // Try to parse the JSON
      const parsed = JSON.parse(cleanedResponse);
      
      if (Array.isArray(parsed)) {
        return parsed;
      } else {
        throw new Error('Response is not an array');
      }
    } catch (error) {
      console.error('Failed to parse career response:', error);
      console.error('Raw response:', response);
      throw new Error('Failed to parse career data from AI response');
    }
  }

  /**
   * Parse detailed response from Gemini
   */
  private parseDetailedResponse(response: string): any {
    try {
      // Clean the response to extract JSON
      let cleanedResponse = response.trim();
      
      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/, '');
      
      // Try to parse the JSON
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Failed to parse detailed response:', error);
      console.error('Raw response:', response);
      throw new Error('Failed to parse detailed career data from AI response');
    }
  }

  /**
   * Determine connection position for visualization
   */
  private getConnectPosition(index: number): 'top' | 'bottom' {
    return index % 2 === 0 ? 'bottom' : 'top';
  }

  /**
   * Enforce rate limiting between requests
   */
  private async enforceRateLimit(): Promise<void> {
    if (this.lastRequestTime) {
      const timeSinceLastRequest = Date.now() - this.lastRequestTime.getTime();
      const remainingDelay = this.delayBetweenRequests - timeSinceLastRequest;

      if (remainingDelay > 0) {
        console.log(`Rate limiting: waiting ${remainingDelay}ms before next request`);
        await this.sleep(remainingDelay);
      }
    }

    this.lastRequestTime = new Date();
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean and format career data to remove unnecessary information
   */
  private cleanCareerData(career: any): any {
    return {
      jobTitle: this.cleanText(career.jobTitle),
      jobDescription: this.cleanText(career.jobDescription),
      timeline: this.cleanText(career.timeline),
      salary: this.cleanText(career.salary),
      difficulty: career.difficulty
    };
  }

  /**
   * Clean text by removing unnecessary words and formatting
   */
  private cleanText(text: string): string {
    if (!text) return '';

    return text
      .replace(/\b(jcareer|j-career|job career|career job)\b/gi, '') // Remove "jcareer" references
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\.$/, '') // Remove trailing period if present
      .trim();
  }

  /**
   * Clean array by removing empty items and unnecessary text
   */
  private cleanArray(arr: string[]): string[] {
    if (!Array.isArray(arr)) return [];

    return arr
      .filter(item => item && typeof item === 'string')
      .map(item => this.cleanText(item))
      .filter(item => item.length > 0 && !item.toLowerCase().includes('jcareer'))
      .slice(0, 10); // Limit array size
  }
}

// Export singleton instance
export const geminiCareerService = new GeminiCareerService();
export default geminiCareerService;
