import React, { useState, use<PERSON><PERSON>back } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  BookOpen,
  FileText,
  Download,
  RefreshCw,
  Eye,
  Edit,
  Save,
  Plus,
  Trash2,
  Copy,
  Share,
  Wand2,
  CheckCircle,
  AlertCircle,
  Users,
  Calendar,
  Target,
  Lightbulb,
  Brain
} from "lucide-react";

import { ResearchDocument, LiteratureReview, CitationStyle, AIGenerationOptions, GenerationProgress } from '../types';
import { literatureReviewService } from '../services/literature-review.service';
import { analysisResultsStorageService } from '../services/analysis-results-storage.service';
import { ProgressTracker } from './ProgressTracker';

interface LiteratureReviewGeneratorProps {
  documents: ResearchDocument[];
  onGenerate: (review: LiteratureReview) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function LiteratureReviewGenerator({
  documents,
  onGenerate,
  aiSettings
}: LiteratureReviewGeneratorProps) {
  const [reviewTopic, setReviewTopic] = useState('');
  const [citationStyle, setCitationStyle] = useState<CitationStyle>('APA');
  const [customSections, setCustomSections] = useState<string[]>([
    'Introduction',
    'Chronological Development',
    'Key Themes and Findings',
    'Methodological Approaches',
    'Research Gaps',
    'Conclusions and Future Directions'
  ]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress | null>(null);
  const [currentReview, setCurrentReview] = useState<LiteratureReview | null>(null);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [sectionEdits, setSectionEdits] = useState<{ [sectionId: string]: string }>({});

  // Generate literature review
  const handleGenerateReview = useCallback(async () => {
    if (!reviewTopic.trim()) {
      toast.error('Please enter a review topic');
      return;
    }

    if (documents.length === 0) {
      toast.error('Please select documents for the literature review');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(null);

    try {
      const review = await literatureReviewService.generateLiteratureReview(
        documents,
        reviewTopic,
        citationStyle,
        (progress) => {
          setGenerationProgress(progress);
        },
        aiSettings.selectedModel
      );

      setCurrentReview(review);
      onGenerate(review);

      // Save to database
      try {
        const { data: savedReview, error: saveError } = await analysisResultsStorageService.saveLiteratureReview(review);
        if (saveError) {
          console.error('Error saving literature review to database:', saveError);
          toast.warning('Literature review generated but not saved to database');
        } else {
          console.log('Literature review saved to database:', savedReview);
          toast.success('Literature review generated and saved successfully!');
        }
      } catch (saveError) {
        console.error('Error saving literature review:', saveError);
        toast.success('Literature review generated successfully!');
      }
    } catch (error) {
      console.error('Error generating literature review:', error);
      toast.error('Failed to generate literature review. Please try again.');
    } finally {
      setIsGenerating(false);
      setGenerationProgress(null);
    }
  }, [reviewTopic, documents, citationStyle, onGenerate]);

  // Add custom section
  const handleAddSection = useCallback(() => {
    const sectionName = prompt('Enter section name:');
    if (sectionName && !customSections.includes(sectionName)) {
      setCustomSections(prev => [...prev, sectionName]);
    }
  }, [customSections]);

  // Remove custom section
  const handleRemoveSection = useCallback((sectionName: string) => {
    setCustomSections(prev => prev.filter(s => s !== sectionName));
  }, []);

  // Start editing section
  const handleEditSection = useCallback((sectionId: string, currentContent: string) => {
    setEditingSection(sectionId);
    setSectionEdits(prev => ({ ...prev, [sectionId]: currentContent }));
  }, []);

  // Save section edit
  const handleSaveSection = useCallback((sectionId: string) => {
    if (currentReview && sectionEdits[sectionId]) {
      // Update the review content
      const updatedReview = {
        ...currentReview,
        sections: currentReview.sections.map(section =>
          section.id === sectionId
            ? { ...section, content: sectionEdits[sectionId] }
            : section
        )
      };
      setCurrentReview(updatedReview);
      setEditingSection(null);
      toast.success('Section updated successfully!');
    }
  }, [currentReview, sectionEdits]);

  // Export review
  const handleExport = useCallback((format: 'PDF' | 'Word' | 'LaTeX') => {
    if (!currentReview) return;
    
    // This would integrate with actual export functionality
    toast.success(`Exporting literature review as ${format}...`);
  }, [currentReview]);

  // Get document statistics
  const documentStats = {
    total: documents.length,
    ready: documents.filter(d => d.status === 'ready').length,
    yearRange: documents.length > 0 ? {
      min: Math.min(...documents.map(d => d.publicationYear)),
      max: Math.max(...documents.map(d => d.publicationYear))
    } : null,
    uniqueAuthors: <AUTHORS>
    uniqueJournals: [...new Set(documents.map(d => d.journal).filter(Boolean))].length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-2xl">
                <BookOpen className="h-7 w-7 text-purple-500" />
                Literature Review Generator
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Generate comprehensive, AI-powered literature reviews from your selected research documents
              </p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white/80 rounded-full border border-purple-300">
              <Brain className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">
                {aiSettings.selectedModel.split('/')[1]?.replace('-', ' ') || 'AI Model'}
              </span>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Target className="h-5 w-5 text-blue-500" />
            Review Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Document Overview */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-3">Selected Documents Overview</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{documentStats.total}</div>
                <div className="text-blue-700">Total Documents</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{documentStats.ready}</div>
                <div className="text-green-700">Ready for Analysis</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{documentStats.uniqueAuthors}</div>
                <div className="text-purple-700">Unique Authors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {documentStats.yearRange ? `${documentStats.yearRange.min}-${documentStats.yearRange.max}` : 'N/A'}
                </div>
                <div className="text-orange-700">Year Range</div>
              </div>
            </div>
            
            {documents.length === 0 && (
              <div className="text-center py-4">
                <AlertCircle className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                <p className="text-orange-700">No documents selected. Please select documents from the library.</p>
              </div>
            )}
          </div>

          {/* Review Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Review Topic *</label>
                <Input
                  placeholder="Enter the main topic or research question..."
                  value={reviewTopic}
                  onChange={(e) => setReviewTopic(e.target.value)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Be specific about your research focus for better results
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Citation Style</label>
                <Select value={citationStyle} onValueChange={(value: CitationStyle) => setCitationStyle(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="APA">APA (American Psychological Association)</SelectItem>
                    <SelectItem value="MLA">MLA (Modern Language Association)</SelectItem>
                    <SelectItem value="Chicago">Chicago Manual of Style</SelectItem>
                    <SelectItem value="Harvard">Harvard Referencing</SelectItem>
                    <SelectItem value="IEEE">IEEE Citation Style</SelectItem>
                    <SelectItem value="Vancouver">Vancouver System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Review Sections</label>
              <div className="space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
                {customSections.map((section, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">{section}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSection(section)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddSection}
                  className="w-full"
                >
                  <Plus className="h-3 w-3 mr-2" />
                  Add Section
                </Button>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex justify-center">
            <Button
              size="lg"
              onClick={handleGenerateReview}
              disabled={isGenerating || !reviewTopic.trim() || documents.length === 0}
              className="px-8"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  Generating Review...
                </>
              ) : (
                <>
                  <Wand2 className="h-5 w-5 mr-2" />
                  Generate Literature Review
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Generation Progress */}
      {isGenerating && generationProgress && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <ProgressTracker progress={generationProgress} />
          </CardContent>
        </Card>
      )}

      {/* Generated Review */}
      {currentReview && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Generated Literature Review
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {currentReview.wordCount} words
                </Badge>
                <Badge variant="secondary">
                  {currentReview.sections.length} sections
                </Badge>
                <Button variant="outline" size="sm" onClick={() => handleExport('PDF')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleExport('Word')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Word
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="review" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="review">Review Content</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="references">References</TabsTrigger>
              </TabsList>
              
              <TabsContent value="review" className="space-y-6">
                {/* Review Title */}
                <div className="text-center border-b pb-4">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{currentReview.title}</h1>
                  <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      Generated {new Date(currentReview.generatedAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      {currentReview.documents.length} sources
                    </span>
                    <span className="flex items-center gap-1">
                      <BookOpen className="h-4 w-4" />
                      {citationStyle} style
                    </span>
                  </div>
                </div>

                {/* Review Sections */}
                <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px]">
                  <div className="space-y-8 pr-4">
                    {currentReview.sections.map((section) => (
                      <div key={section.id} className="border rounded-lg">
                        <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
                          <h2 className="text-xl font-semibold text-gray-900">{section.title}</h2>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {section.type}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditSection(section.id, section.content)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="p-4">
                          {editingSection === section.id ? (
                            <div className="space-y-3">
                              <Textarea
                                value={sectionEdits[section.id] || section.content}
                                onChange={(e) => setSectionEdits(prev => ({ ...prev, [section.id]: e.target.value }))}
                                rows={10}
                                className="w-full"
                              />
                              <div className="flex gap-2">
                                <Button size="sm" onClick={() => handleSaveSection(section.id)}>
                                  <Save className="h-4 w-4 mr-2" />
                                  Save
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => setEditingSection(null)}>
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="prose max-w-none">
                              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                                {section.content}
                              </p>
                              
                              {section.citations && section.citations.length > 0 && (
                                <div className="mt-4 pt-4 border-t">
                                  <h4 className="font-medium text-gray-900 mb-2">Citations in this section:</h4>
                                  <div className="space-y-1">
                                    {section.citations.map((citation, index) => (
                                      <p key={index} className="text-sm text-gray-600">
                                        {citation.fullReference}
                                      </p>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
              
              <TabsContent value="metadata" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Review Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <strong>ID:</strong> {currentReview.id}
                      </div>
                      <div>
                        <strong>Status:</strong> 
                        <Badge className="ml-2" variant={currentReview.status === 'completed' ? 'default' : 'secondary'}>
                          {currentReview.status}
                        </Badge>
                      </div>
                      <div>
                        <strong>Word Count:</strong> {currentReview.wordCount}
                      </div>
                      <div>
                        <strong>Sections:</strong> {currentReview.sections.length}
                      </div>
                      <div>
                        <strong>Citation Style:</strong> {currentReview.citationStyle}
                      </div>
                      <div>
                        <strong>Generated:</strong> {new Date(currentReview.generatedAt).toLocaleString()}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Source Documents</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {documents.map((doc) => (
                          <div key={doc.id} className="p-2 bg-gray-50 rounded text-sm">
                            <div className="font-medium">{doc.title}</div>
                            <div className="text-gray-600">
                              {doc.authors.join(', ')} ({doc.publicationYear})
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="references" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Bibliography</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {documents.map((doc, index) => (
                        <div key={doc.id} className="p-3 border-l-4 border-blue-500 bg-blue-50">
                          <p className="text-sm">
                            <strong>[{index + 1}]</strong> {doc.authors.join(', ')} ({doc.publicationYear}). 
                            <em>{doc.title}</em>
                            {doc.journal && (
                              <>. <em>{doc.journal}</em></>
                            )}
                            {doc.doi && (
                              <>. DOI: {doc.doi}</>
                            )}
                          </p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
