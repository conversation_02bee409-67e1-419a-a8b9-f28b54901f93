import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Brain,
  Play,
  CheckCircle,
  Clock,
  AlertTriangle,
  FlaskConical,
  Database,
  Sparkles,
  Target,
  BarChart3,
  Microscope,
  Activity,
  Users
} from "lucide-react";

import { UploadedFile, DataAnalysisResult } from '../types';
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';

interface ResearchAnalysisPipelineProps {
  file: UploadedFile;
  dataDescription: string;
  researchContext: {
    researchQuestion?: string;
    studyType?: string;
    researchField?: string;
    hypotheses?: string[];
  };
  onAnalysisComplete?: (result: DataAnalysisResult) => void;
  className?: string;
}

export const ResearchAnalysisPipeline: React.FC<ResearchAnalysisPipelineProps> = ({
  file,
  dataDescription,
  researchContext,
  onAnalysisComplete,
  className = ""
}) => {
  const { setAnalyzing, setCurrentAnalysis, setAnalysisResult } = useDataVisualizationStore();

  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [analysisResult, setAnalysisResultLocal] = useState<DataAnalysisResult | null>(null);

  const analysisSteps = [
    'Data Profiling & Quality Assessment',
    'Research Design Classification',
    'Sample Size & Power Analysis',
    'Statistical Test Recommendations',
    'Research Hypothesis Development',
    'Statistical Analysis Execution',
    'Research Figure Generation',
    'Results Interpretation & Insights'
  ];

  const startAnalysis = async () => {
    setIsRunning(true);
    setAnalyzing(true);
    setProgress(0);

    try {
      // Simulate analysis steps with progress updates
      for (let i = 0; i < analysisSteps.length; i++) {
        setCurrentStep(analysisSteps[i]);
        setProgress((i / analysisSteps.length) * 100);

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      // Perform actual analysis using existing service
      setCurrentStep('Generating analysis results...');
      const result = await GeminiAnalysisService.analyzeData(file);

      setProgress(100);
      setCurrentStep('Analysis completed!');

      setAnalysisResultLocal(result);
      setCurrentAnalysis(result);
      setAnalysisResult(result);

      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }

      toast.success('Research analysis completed successfully!');

    } catch (error) {
      console.error('Analysis failed:', error);
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsRunning(false);
      setAnalyzing(false);
    }
  };



  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <FlaskConical className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Research Analysis Pipeline
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Sequential AI-powered statistical analysis with research methodology validation
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{file.data.length}</div>
              <div className="text-sm text-gray-600">Sample Size</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{file.headers.length}</div>
              <div className="text-sm text-gray-600">Variables</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">8</div>
              <div className="text-sm text-gray-600">Analysis Steps</div>
            </div>
          </div>

          {!isRunning && !analysisResult && (
            <div className="mt-6 text-center">
              <Button 
                onClick={startAnalysis}
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Play className="h-5 w-5 mr-2" />
                Start Research Analysis
              </Button>
            </div>
          )}

          {isRunning && (
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Overall Progress</span>
                <span className="text-sm text-gray-500">{Math.round(overallProgress)}%</span>
              </div>
              <Progress value={overallProgress} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analysis Steps */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {steps.map((step, index) => (
          <Card 
            key={step.id} 
            className={`transition-all duration-300 ${getStepStatusColor(step)} ${
              index === currentStepIndex && isRunning ? 'ring-2 ring-blue-500 shadow-lg' : ''
            }`}
          >
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  {getStepStatusIcon(step)}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 mb-1">{step.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{step.description}</p>
                  
                  {step.status === 'running' && (
                    <div className="space-y-2">
                      <Progress value={step.progress} className="h-1" />
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Processing...</span>
                        <span>~{step.estimatedTime}</span>
                      </div>
                    </div>
                  )}
                  
                  {step.status === 'completed' && (
                    <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Completed
                    </Badge>
                  )}
                  
                  {step.status === 'error' && (
                    <Alert variant="destructive" className="mt-2">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {step.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Analysis Complete */}
      {analysisResult && (
        <Card className="border-0 shadow-xl bg-gradient-to-r from-green-50 to-blue-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-r from-green-600 to-blue-600 rounded-full">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">Analysis Complete!</h3>
                <p className="text-gray-600">
                  Your research analysis is ready. Review the findings, visualizations, and generate your research report.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
