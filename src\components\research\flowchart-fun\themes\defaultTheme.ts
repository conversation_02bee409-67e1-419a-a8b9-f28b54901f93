/**
 * Default Theme Configuration
 * Based on the reference Flowchart Fun implementation
 */

import { FFTheme, ThemePreset } from '../types';

export const defaultTheme: FFTheme = {
  // Global Style
  fontFamily: "Helvetica Neue, Arial, sans-serif",
  background: "#ffffff",
  lineHeight: 1.2,
  
  // Layout
  layoutName: "dagre",
  direction: "DOWN",
  spacingFactor: 1.25,
  
  // Node
  shape: "roundrectangle",
  nodeBackground: "#ffffff",
  nodeForeground: "#000000",
  padding: 8,
  borderWidth: 2,
  borderColor: "#000000",
  textMaxWidth: 144,
  textMarginY: 0,
  useFixedHeight: false,
  fixedHeight: 40,
  
  // Edge
  curveStyle: "bezier",
  edgeWidth: 2,
  edgeColor: "#000000",
  sourceArrowShape: "none",
  targetArrowShape: "triangle",
  sourceDistanceFromNode: 5,
  targetDistanceFromNode: 5,
  arrowScale: 1,
  edgeTextSize: 12,
  rotateEdgeLabel: false,
};

export const darkTheme: FFTheme = {
  ...defaultTheme,
  background: "#1a1a1a",
  nodeBackground: "#2d2d2d",
  nodeForeground: "#ffffff",
  borderColor: "#666666",
  edgeColor: "#666666",
};

export const professionalTheme: FFTheme = {
  ...defaultTheme,
  fontFamily: "Inter, system-ui, sans-serif",
  background: "#f8fafc",
  nodeBackground: "#ffffff",
  nodeForeground: "#1e293b",
  borderColor: "#3b82f6",
  edgeColor: "#64748b",
  padding: 12,
  borderWidth: 1,
  shape: "rectangle",
};

export const colorfulTheme: FFTheme = {
  ...defaultTheme,
  background: "#f0f9ff",
  nodeBackground: "#dbeafe",
  nodeForeground: "#1e40af",
  borderColor: "#3b82f6",
  edgeColor: "#6366f1",
  borderWidth: 3,
  padding: 10,
};

export const minimalistTheme: FFTheme = {
  ...defaultTheme,
  fontFamily: "SF Pro Display, system-ui, sans-serif",
  background: "#ffffff",
  nodeBackground: "transparent",
  nodeForeground: "#374151",
  borderColor: "#d1d5db",
  edgeColor: "#9ca3af",
  borderWidth: 1,
  padding: 6,
  shape: "rectangle",
};

export const themePresets: ThemePreset[] = [
  {
    id: "default",
    name: "Default",
    description: "Clean and simple design",
    theme: defaultTheme,
  },
  {
    id: "dark",
    name: "Dark",
    description: "Dark mode theme",
    theme: darkTheme,
  },
  {
    id: "professional",
    name: "Professional",
    description: "Business-ready appearance",
    theme: professionalTheme,
  },
  {
    id: "colorful",
    name: "Colorful",
    description: "Vibrant and engaging",
    theme: colorfulTheme,
  },
  {
    id: "minimalist",
    name: "Minimalist",
    description: "Clean and minimal",
    theme: minimalistTheme,
  },
];

export const getThemeById = (id: string): FFTheme => {
  const preset = themePresets.find(p => p.id === id);
  return preset ? preset.theme : defaultTheme;
};

export const createCustomTheme = (base: FFTheme, overrides: Partial<FFTheme>): FFTheme => {
  return { ...base, ...overrides };
};
