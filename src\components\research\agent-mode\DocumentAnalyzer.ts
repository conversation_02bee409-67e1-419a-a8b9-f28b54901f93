/**
 * DocumentAnalyzer - Utility class for analyzing document structure
 * Identifies headings, sections, and paragraphs for Agent Mode functionality
 */

import { nanoid } from 'nanoid';
import { 
  DocumentStructure, 
  Heading, 
  Paragraph, 
  Section, 
  SectionType, 
  DocumentType,
  SECTION_KEYWORDS 
} from './types';

export class DocumentAnalyzer {
  private static instance: DocumentAnalyzer;
  
  public static getInstance(): DocumentAnalyzer {
    if (!DocumentAnalyzer.instance) {
      DocumentAnalyzer.instance = new DocumentAnalyzer();
    }
    return DocumentAnalyzer.instance;
  }

  /**
   * Analyze document structure from HTML content
   */
  public analyzeDocument(htmlContent: string): DocumentStructure {
    const startTime = Date.now();
    
    // Parse headings first
    const headings = this.parseHeadings(htmlContent);
    
    // Parse paragraphs
    const paragraphs = this.parseParagraphs(htmlContent);
    
    // Identify sections based on headings and content
    const sections = this.identifySections(htmlContent, headings, paragraphs);
    
    // Determine document type
    const documentType = this.determineDocumentType(sections, headings);
    
    // Calculate metrics
    const wordCount = this.countWords(htmlContent);
    const characterCount = htmlContent.length;
    
    const processingTime = Date.now() - startTime;
    console.log(`Document analysis completed in ${processingTime}ms`);

    return {
      headings,
      paragraphs,
      sections,
      wordCount,
      characterCount,
      structure: documentType,
      lastAnalyzed: new Date()
    };
  }

  /**
   * Parse headings from HTML content
   */
  private parseHeadings(htmlContent: string): Heading[] {
    const headings: Heading[] = [];
    const headingRegex = /<(h[1-6])[^>]*>(.*?)<\/h[1-6]>/gi;
    let match;

    while ((match = headingRegex.exec(htmlContent)) !== null) {
      const [fullMatch, tag, text] = match;
      const level = parseInt(tag.charAt(1));
      const startPosition = match.index;
      const endPosition = match.index + fullMatch.length;
      
      headings.push({
        id: nanoid(),
        level,
        text: this.stripHtml(text).trim(),
        startPosition,
        endPosition,
        htmlTag: tag
      });
    }

    return headings.sort((a, b) => a.startPosition - b.startPosition);
  }

  /**
   * Parse paragraphs from HTML content
   */
  private parseParagraphs(htmlContent: string): Paragraph[] {
    const paragraphs: Paragraph[] = [];
    const paragraphRegex = /<p[^>]*>(.*?)<\/p>/gi;
    let match;

    while ((match = paragraphRegex.exec(htmlContent)) !== null) {
      const [fullMatch, text] = match;
      const cleanText = this.stripHtml(text).trim();
      
      if (cleanText.length > 10) { // Only include substantial paragraphs
        const startPosition = match.index;
        const endPosition = match.index + fullMatch.length;
        const wordCount = this.countWords(cleanText);
        
        paragraphs.push({
          id: nanoid(),
          text: cleanText,
          startPosition,
          endPosition,
          wordCount
        });
      }
    }

    return paragraphs.sort((a, b) => a.startPosition - b.startPosition);
  }

  /**
   * Identify sections based on headings and content structure
   */
  private identifySections(htmlContent: string, headings: Heading[], paragraphs: Paragraph[]): Section[] {
    const sections: Section[] = [];
    
    if (headings.length === 0) {
      // No headings - treat entire document as one section
      const entireContent = this.stripHtml(htmlContent);
      sections.push({
        id: nanoid(),
        title: 'Document Content',
        content: entireContent,
        startPosition: 0,
        endPosition: htmlContent.length,
        type: 'other',
        headingLevel: 0,
        paragraphIds: paragraphs.map(p => p.id),
        wordCount: this.countWords(entireContent)
      });
      return sections;
    }

    // Create sections based on headings
    for (let i = 0; i < headings.length; i++) {
      const heading = headings[i];
      const nextHeading = headings[i + 1];
      
      const startPosition = heading.startPosition;
      const endPosition = nextHeading ? nextHeading.startPosition : htmlContent.length;
      
      // Extract content between this heading and the next
      const sectionHtml = htmlContent.substring(startPosition, endPosition);
      const sectionContent = this.stripHtml(sectionHtml);
      
      // Find paragraphs within this section
      const sectionParagraphs = paragraphs.filter(p => 
        p.startPosition >= startPosition && p.endPosition <= endPosition
      );
      
      // Determine section type
      const sectionType = this.classifySection(heading.text, sectionContent);
      
      sections.push({
        id: nanoid(),
        title: heading.text,
        content: sectionContent,
        startPosition,
        endPosition,
        type: sectionType,
        headingLevel: heading.level,
        paragraphIds: sectionParagraphs.map(p => p.id),
        wordCount: this.countWords(sectionContent)
      });
    }

    return sections;
  }

  /**
   * Classify section type based on title and content
   */
  private classifySection(title: string, content: string): SectionType {
    const titleLower = title.toLowerCase();
    const contentLower = content.toLowerCase();
    
    // Check each section type for keyword matches
    for (const [sectionType, keywords] of Object.entries(SECTION_KEYWORDS)) {
      if (keywords.length === 0) continue;
      
      const titleMatches = keywords.some(keyword => titleLower.includes(keyword));
      const contentMatches = keywords.some(keyword => contentLower.includes(keyword));
      
      if (titleMatches || (contentMatches && content.length < 500)) {
        return sectionType as SectionType;
      }
    }
    
    // Special cases based on position and content
    if (titleLower.includes('abstract') || (content.length < 300 && titleLower.includes('summary'))) {
      return 'abstract';
    }
    
    if (titleLower.includes('intro') || titleLower.includes('background')) {
      return 'introduction';
    }
    
    if (titleLower.includes('method') || titleLower.includes('approach')) {
      return 'methodology';
    }
    
    if (titleLower.includes('result') || titleLower.includes('finding')) {
      return 'results';
    }
    
    if (titleLower.includes('discuss') || titleLower.includes('analysis')) {
      return 'discussion';
    }
    
    if (titleLower.includes('conclus') || titleLower.includes('final')) {
      return 'conclusion';
    }
    
    return 'other';
  }

  /**
   * Determine overall document type
   */
  private determineDocumentType(sections: Section[], headings: Heading[]): DocumentType {
    const sectionTypes = sections.map(s => s.type);
    
    // Academic paper indicators
    const hasAbstract = sectionTypes.includes('abstract');
    const hasMethodology = sectionTypes.includes('methodology') || sectionTypes.includes('methods');
    const hasResults = sectionTypes.includes('results');
    const hasReferences = sectionTypes.includes('references');
    
    if (hasAbstract && hasMethodology && hasResults) {
      return 'academic';
    }
    
    // Report indicators
    const hasMultipleChapters = headings.filter(h => h.level === 1).length > 3;
    if (hasMultipleChapters) {
      return 'report';
    }
    
    // Article indicators
    const hasIntroduction = sectionTypes.includes('introduction');
    const hasConclusion = sectionTypes.includes('conclusion');
    if (hasIntroduction && hasConclusion && !hasMethodology) {
      return 'article';
    }
    
    // Essay indicators
    if (sections.length <= 5 && hasIntroduction && hasConclusion) {
      return 'essay';
    }
    
    return 'general';
  }

  /**
   * Strip HTML tags from content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Find sections that match specific criteria
   */
  public findSectionsByType(structure: DocumentStructure, types: SectionType[]): Section[] {
    return structure.sections.filter(section => types.includes(section.type));
  }

  /**
   * Find sections by keyword matching
   */
  public findSectionsByKeywords(structure: DocumentStructure, keywords: string[]): Section[] {
    const keywordsLower = keywords.map(k => k.toLowerCase());
    
    return structure.sections.filter(section => {
      const titleLower = section.title.toLowerCase();
      const contentLower = section.content.toLowerCase();
      
      return keywordsLower.some(keyword => 
        titleLower.includes(keyword) || contentLower.includes(keyword)
      );
    });
  }

  /**
   * Get section content with position information
   */
  public getSectionContent(htmlContent: string, section: Section): string {
    return htmlContent.substring(section.startPosition, section.endPosition);
  }
}
