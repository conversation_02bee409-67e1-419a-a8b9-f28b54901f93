/**
 * Diagram Export Dialog Component
 * Advanced export options and settings
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  FileImage,
  FileText,
  File,
  Settings,
  Palette,
  Ruler,
  Info
} from 'lucide-react';
import { DiagramExportDialogProps, ExportFormat } from '../types';
import { EXPORT_FORMATS, MERMAID_THEMES } from '../constants';

export function DiagramExportDialog({
  diagram,
  isOpen,
  onClose,
  onExport
}: DiagramExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('png');
  const [filename, setFilename] = useState(
    diagram.metadata.title.replace(/[^a-zA-Z0-9]/g, '_')
  );
  const [width, setWidth] = useState(1200);
  const [height, setHeight] = useState(800);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [theme, setTheme] = useState<'default' | 'dark' | 'forest' | 'neutral'>('default');
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await onExport({
        format: selectedFormat,
        filename: filename || 'diagram',
        width,
        height,
        backgroundColor,
        theme
      });
      // Close dialog on successful export
      onClose();
    } catch (error: any) {
      console.error('Export failed:', error);
      // Error is handled by the parent component, just log here
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case 'png':
        return <FileImage className="h-4 w-4" />;
      case 'svg':
        return <File className="h-4 w-4" />;
      case 'pdf':
        return <FileText className="h-4 w-4" />;
      case 'mermaid':
        return <FileText className="h-4 w-4" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  const formatOptions = Object.entries(EXPORT_FORMATS).map(([key, config]) => ({
    value: key as ExportFormat,
    label: config.name,
    description: config.description,
    icon: getFormatIcon(key as ExportFormat)
  }));

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Diagram
          </DialogTitle>
          <DialogDescription>
            Export "{diagram.metadata.title}" in your preferred format with custom settings.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="format" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="format">Format</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="format" className="space-y-4">
            <div className="space-y-3">
              <Label>Export Format</Label>
              <div className="grid grid-cols-2 gap-3">
                {formatOptions.map((option) => (
                  <Card
                    key={option.value}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedFormat === option.value ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}
                    onClick={() => setSelectedFormat(option.value)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        {option.icon}
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-sm text-gray-500">{option.description}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <Label htmlFor="filename">Filename</Label>
              <Input
                id="filename"
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                placeholder="Enter filename"
              />
              <div className="text-sm text-gray-500">
                Extension will be added automatically: .{EXPORT_FORMATS[selectedFormat].extension}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {(selectedFormat === 'png' || selectedFormat === 'svg') && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">Width (px)</Label>
                    <Input
                      id="width"
                      type="number"
                      value={width}
                      onChange={(e) => setWidth(Number(e.target.value))}
                      min="100"
                      max="4000"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height (px)</Label>
                    <Input
                      id="height"
                      type="number"
                      value={height}
                      onChange={(e) => setHeight(Number(e.target.value))}
                      min="100"
                      max="4000"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="background">Background Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="background"
                      type="color"
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      placeholder="#ffffff"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <Select value={theme} onValueChange={(value: any) => setTheme(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(MERMAID_THEMES).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: config.primaryColor }}
                        />
                        <div>
                          <div className="font-medium">{config.name}</div>
                          <div className="text-xs text-gray-500">{config.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedFormat === 'mermaid' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    Mermaid Export Info
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-sm text-gray-600">
                  <p>
                    Exports the raw Mermaid code with metadata as a Markdown file. 
                    Perfect for sharing, version control, or importing into other tools.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Export Preview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Format:</span>
                    <div className="flex items-center gap-2 mt-1">
                      {getFormatIcon(selectedFormat)}
                      {EXPORT_FORMATS[selectedFormat].name}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Filename:</span>
                    <div className="mt-1 font-mono text-xs bg-gray-100 p-1 rounded">
                      {filename}.{EXPORT_FORMATS[selectedFormat].extension}
                    </div>
                  </div>
                  {(selectedFormat === 'png' || selectedFormat === 'svg') && (
                    <>
                      <div>
                        <span className="font-medium">Dimensions:</span>
                        <div className="mt-1">{width} × {height} px</div>
                      </div>
                      <div>
                        <span className="font-medium">Background:</span>
                        <div className="flex items-center gap-2 mt-1">
                          <div
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor }}
                          />
                          {backgroundColor}
                        </div>
                      </div>
                    </>
                  )}
                </div>

                <Separator />

                <div className="text-sm">
                  <span className="font-medium">Diagram Info:</span>
                  <div className="mt-1 space-y-1">
                    <div>Title: {diagram.metadata.title}</div>
                    <div>Type: {diagram.metadata.type}</div>
                    <div>Created: {diagram.generatedAt.toLocaleDateString()}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export {EXPORT_FORMATS[selectedFormat].name}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
