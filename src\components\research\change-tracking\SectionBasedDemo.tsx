import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useChangeTracking, ChangeTrackingControls, SectionBasedDiffViewer } from './index';
import { toast } from 'sonner';

export function SectionBasedDemo() {
  const [content, setContent] = useState(`This is the original content. It contains multiple sentences that can be modified independently. Each sentence can be accepted or rejected separately. This provides granular control over AI changes.`);
  const changeTracking = useChangeTracking();

  const handleToggleTracking = (enabled: boolean) => {
    if (enabled) {
      changeTracking.startTracking(content);
      toast.success('Change tracking started');
    } else {
      changeTracking.stopTracking();
      toast.info('Change tracking stopped');
    }
  };

  const handleToggleDiffView = () => {
    changeTracking.toggleViewMode();
  };

  const handleAcceptSelectedSections = () => {
    const selectedCount = changeTracking.selectedSections.length;
    changeTracking.acceptSelectedSections();
    toast.success(`Accepted ${selectedCount} selected sections`);
  };

  const handleRejectSelectedSections = () => {
    const selectedCount = changeTracking.selectedSections.length;
    changeTracking.rejectSelectedSections();
    toast.success(`Rejected ${selectedCount} selected sections`);
  };

  const handleAcceptAllChanges = () => {
    changeTracking.acceptAllChanges();
    toast.success('All changes accepted');
  };

  const handleRejectAllChanges = () => {
    const originalContent = changeTracking.rejectAllChanges();
    if (originalContent) {
      setContent(originalContent);
      toast.success('All changes rejected, content reverted');
    }
  };

  const handleNavigateChange = (direction: 'next' | 'previous') => {
    if (direction === 'next') {
      changeTracking.navigateToNextChange();
    } else {
      changeTracking.navigateToPreviousChange();
    }
  };

  const simulateComplexAIChange = () => {
    if (!changeTracking.isTrackingEnabled) {
      toast.error('Please start change tracking first');
      return;
    }

    // Simulate a complex AI change with multiple sentences
    const originalText = 'This is the original content. It contains multiple sentences that can be modified independently. Each sentence can be accepted or rejected separately.';
    const newText = 'This is the enhanced original content with AI improvements. It contains multiple well-structured sentences that can be modified independently with precision. Each individual sentence can be carefully accepted or rejected separately for maximum control.';
    
    const startPos = content.indexOf(originalText);
    
    if (startPos !== -1) {
      const endPos = startPos + originalText.length;
      const newContent = content.replace(originalText, newText);
      
      // Record the change (this will automatically create sections)
      changeTracking.recordChange(
        originalText,
        newText,
        startPos,
        endPos,
        'replace',
        'Enhance this text with better structure and clarity'
      );
      
      // Update content
      setContent(newContent);
      changeTracking.updateCurrentContent(newContent);
      
      toast.success('Complex AI change simulated with multiple sections');
    } else {
      toast.error('Could not find text to replace');
    }
  };

  const simulateAnotherChange = () => {
    if (!changeTracking.isTrackingEnabled) {
      toast.error('Please start change tracking first');
      return;
    }

    const originalText = 'This provides granular control over AI changes.';
    const newText = 'This provides exceptional granular control over sophisticated AI changes with advanced review capabilities.';
    
    const startPos = content.indexOf(originalText);
    
    if (startPos !== -1) {
      const endPos = startPos + originalText.length;
      const newContent = content.replace(originalText, newText);
      
      changeTracking.recordChange(
        originalText,
        newText,
        startPos,
        endPos,
        'replace',
        'Make this sentence more descriptive and professional'
      );
      
      setContent(newContent);
      changeTracking.updateCurrentContent(newContent);
      
      toast.success('Another AI change added');
    }
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    if (changeTracking.isTrackingEnabled) {
      changeTracking.updateCurrentContent(newContent);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Section-Based Change Tracking Demo</CardTitle>
          <p className="text-sm text-gray-600">
            This demo shows how you can select individual sections of AI changes and accept/reject them independently.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Change Tracking Controls */}
          <ChangeTrackingControls
            onToggleTracking={handleToggleTracking}
            onToggleDiffView={handleToggleDiffView}
            onAcceptAllChanges={handleAcceptAllChanges}
            onRejectAllChanges={handleRejectAllChanges}
          />

          {/* Content Editor */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Content:</label>
            <Textarea
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={6}
              className="w-full"
            />
          </div>

          {/* Demo Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={simulateComplexAIChange} variant="outline">
              Simulate Complex AI Change
            </Button>
            <Button onClick={simulateAnotherChange} variant="outline">
              Add Another Change
            </Button>
          </div>

          {/* Section-Based Diff Viewer */}
          {changeTracking.viewMode === 'diff' && changeTracking.hasChanges && (
            <SectionBasedDiffViewer
              onAcceptSelectedSections={handleAcceptSelectedSections}
              onRejectSelectedSections={handleRejectSelectedSections}
              onAcceptAllChanges={handleAcceptAllChanges}
              onRejectAllChanges={handleRejectAllChanges}
              onNavigateChange={handleNavigateChange}
            />
          )}

          {/* Status Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div><strong>Tracking:</strong> {changeTracking.isTrackingEnabled ? 'Enabled' : 'Disabled'}</div>
              <div><strong>View Mode:</strong> {changeTracking.viewMode}</div>
              <div><strong>Pending Changes:</strong> {changeTracking.pendingChangesCount}</div>
            </div>
            <div className="space-y-1">
              <div><strong>Selected Sections:</strong> {changeTracking.selectedSections.length}</div>
              <div><strong>Selection Mode:</strong> {changeTracking.selectionMode}</div>
              <div><strong>Has Changes:</strong> {changeTracking.hasChanges ? 'Yes' : 'No'}</div>
            </div>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">How to use:</h4>
            <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
              <li>Start change tracking</li>
              <li>Click "Simulate Complex AI Change" to create multiple sections</li>
              <li>Switch to "Diff View" to see the changes</li>
              <li>Click on individual sections to select them</li>
              <li>Use "Accept Selected" or "Reject Selected" for granular control</li>
              <li>Or use "Accept All" / "Reject All" for bulk operations</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
