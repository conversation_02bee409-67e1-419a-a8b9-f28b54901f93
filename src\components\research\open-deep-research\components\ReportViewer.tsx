/**
 * Report Viewer Component
 * Displays generated research reports with export functionality
 */

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  Save, 
  Share2, 
  FileText, 
  Clock, 
  BarChart3,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { toast } from 'sonner';

import { ReportViewerProps } from '../types';
import { reportService } from '../services/report.service';
import { knowledgeBaseService } from '../services/knowledge-base.service';
import { supabaseService } from '../services/supabase.service';
import { SUCCESS_MESSAGES } from '../constants';

export const ReportViewer: React.FC<ReportViewerProps> = ({
  report,
  reportPrompt,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [copied, setCopied] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  if (!report) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-2">
          <FileText className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-600 mb-2">No Report Generated</h3>
        <p className="text-gray-500">
          Select sources and generate a report to view it here.
        </p>
      </div>
    );
  }

  const stats = reportService.getReportStats(report);

  const handleExport = async (format: 'pdf' | 'docx' | 'txt') => {
    setIsExporting(true);
    try {
      const blob = await reportService.exportReport(report, {
        format,
        includeMetadata: true,
        includeSources: true,
      });

      const filename = reportService.generateFilename(report, format);

      // Save to Supabase if configured and we have a session
      if (supabaseService.isConfigured() && currentSessionId) {
        try {
          await supabaseService.saveExport(
            currentSessionId,
            format,
            filename,
            blob,
            {
              includeMetadata: true,
              includeSources: true,
            }
          );
        } catch (error) {
          console.warn('Failed to save export to database:', error);
        }
      }

      // Download the file
      reportService.downloadReport(blob, filename);

      toast.success('Export Successful', {
        description: `Report exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast.error('Export Failed', {
        description: error instanceof Error ? error.message : 'Failed to export report',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleSaveToKnowledgeBase = async () => {
    setIsSaving(true);
    try {
      // Try to save to Supabase first
      if (supabaseService.isConfigured()) {
        try {
          // Create a new session if we don't have one
          if (!currentSessionId) {
            const session = await supabaseService.createSession({
              title: report.title,
              query: reportPrompt || 'Research Report',
              report_prompt: reportPrompt || 'Research Report',
              selected_model: 'google/gemini-2.0-flash-001', // Default model
              is_agent_mode: false,
              status: 'completed',
            });

            if (session) {
              setCurrentSessionId(session.id);

              // Save the report
              await supabaseService.saveReport(session.id, report);

              toast.success(SUCCESS_MESSAGES.reportSaved);
              setIsSaving(false);
              return;
            }
          }
        } catch (error) {
          console.warn('Failed to save to Supabase, falling back to local storage:', error);
        }
      }

      // Fall back to local storage
      const success = await knowledgeBaseService.addReport(reportPrompt || 'Research Report', report);
      if (success) {
        toast.success(SUCCESS_MESSAGES.reportSaved);
      } else {
        throw new Error('Failed to save report');
      }
    } catch (error) {
      toast.error('Save Failed', {
        description: error instanceof Error ? error.message : 'Failed to save report',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCopyToClipboard = async () => {
    try {
      const formattedReport = reportService.formatReportForDisplay(report);
      await navigator.clipboard.writeText(formattedReport);
      setCopied(true);
      toast.success('Copied to Clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Copy Failed', {
        description: 'Failed to copy report to clipboard',
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {report.title}
              </h1>
              
              {/* Report Stats */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  <span>{stats.wordCount} words</span>
                </div>
                <div className="flex items-center gap-1">
                  <BarChart3 className="h-4 w-4" />
                  <span>{stats.sectionCount} sections</span>
                </div>
                <div className="flex items-center gap-1">
                  <ExternalLink className="h-4 w-4" />
                  <span>{stats.sourceCount} sources</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{stats.readingTime} min read</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyToClipboard}
                className="flex items-center gap-2"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4" />
                    Copy
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveToKnowledgeBase}
                disabled={isSaving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isSaving ? 'Saving...' : 'Save'}
              </Button>

              {/* Export Dropdown */}
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('pdf')}
                  disabled={isExporting}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  PDF
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('docx')}
                  disabled={isExporting}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Word
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport('txt')}
                  disabled={isExporting}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Text
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Content */}
      <Card>
        <CardContent className="p-6">
          {/* Summary */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Summary</h2>
            <div className="prose max-w-none text-gray-700">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {report.summary}
              </ReactMarkdown>
            </div>
          </div>

          <Separator className="my-8" />

          {/* Sections */}
          <div className="space-y-8">
            {report.sections.map((section, index) => (
              <div key={index}>
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  {section.title}
                </h2>
                <div className="prose max-w-none text-gray-700">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {section.content}
                  </ReactMarkdown>
                </div>
                {index < report.sections.length - 1 && (
                  <Separator className="mt-8" />
                )}
              </div>
            ))}
          </div>

          {/* Sources */}
          {report.sources.length > 0 && (
            <>
              <Separator className="my-8" />
              <div>
                <h2 className="text-xl font-semibold text-gray-800 mb-4">
                  Sources ({report.sources.length})
                </h2>
                <div className="space-y-3">
                  {report.sources.map((source, index) => (
                    <div
                      key={source.id}
                      className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg"
                    >
                      <Badge variant="outline" className="mt-0.5">
                        {index + 1}
                      </Badge>
                      <div className="flex-1 min-w-0">
                        <a
                          href={source.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
                        >
                          {source.name}
                        </a>
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {source.url}
                        </p>
                      </div>
                      <ExternalLink className="h-4 w-4 text-gray-400 mt-1 flex-shrink-0" />
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
