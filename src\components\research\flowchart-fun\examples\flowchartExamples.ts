/**
 * Flowchart Examples
 * Pre-built flowchart templates and examples
 */

import { FlowchartTemplate } from '../types';

export const flowchartExamples: FlowchartTemplate[] = [
  {
    id: 'simple-process',
    name: 'Simple Process Flow',
    description: 'A basic process flow with decision points',
    category: 'Business Process',
    tags: ['process', 'decision', 'basic'],
    text: `Start
  Check Requirements
    Requirements Met?
      Yes: Proceed to Implementation
        Implementation
          Testing
            Test Passed?
              Yes: Deploy
                End
              No: Fix Issues
                Testing
      No: Gather More Info
        Check Requirements`,
    preview: 'A simple linear process with decision branches'
  },
  {
    id: 'software-development',
    name: 'Software Development Lifecycle',
    description: 'Complete SDLC process flow',
    category: 'Software Development',
    tags: ['sdlc', 'development', 'agile'],
    text: `Planning
  Requirements Analysis
    System Design
      Implementation
        Unit Testing
          Integration Testing
            System Testing
              User Acceptance Testing
                Deployment
                  Maintenance
                    Bug Reports?
                      Yes: Bug Fixes
                        Unit Testing
                      No: Feature Requests?
                        Yes: Planning
                        No: Monitor
                          Maintenance`,
    preview: 'Complete software development lifecycle with feedback loops'
  },
  {
    id: 'decision-tree',
    name: 'Decision Tree Example',
    description: 'Complex decision-making flowchart',
    category: 'Decision Making',
    tags: ['decision', 'tree', 'logic'],
    text: `Problem Identified
  Urgent?
    Yes: Emergency Response
      Immediate Action
        Problem Solved?
          Yes: Document Solution
            End
          No: Escalate
            Senior Review
              Alternative Solution
                Implement
                  Problem Solved?
                    Yes: Document Solution
                      End
                    No: External Help
                      Consultant Review
                        Final Solution
                          End
    No: Standard Process
      Analyze Problem
        Research Solutions
          Cost Analysis
            Budget Available?
              Yes: Implement Solution
                Monitor Results
                  Success?
                    Yes: Document Solution
                      End
                    No: Revise Approach
                      Research Solutions
              No: Request Budget
                Approved?
                  Yes: Implement Solution
                    Monitor Results
                  No: Alternative Solution
                    Low Cost Options
                      Implement
                        End`,
    preview: 'Complex decision tree with multiple branches and conditions'
  },
  {
    id: 'user-registration',
    name: 'User Registration Flow',
    description: 'User registration and onboarding process',
    category: 'User Experience',
    tags: ['user', 'registration', 'onboarding'],
    text: `User Visits Site
  Registration Page
    Fill Form
      Valid Data?
        No: Show Errors
          Fill Form
        Yes: Email Verification
          Email Sent
            User Clicks Link?
              No: Resend Email?
                Yes: Email Sent
                No: Registration Incomplete
              Yes: Account Activated
                Welcome Email
                  Onboarding Tutorial
                    Profile Setup
                      Preferences
                        Dashboard
                          Registration Complete`,
    preview: 'User registration flow with email verification and onboarding'
  },
  {
    id: 'customer-support',
    name: 'Customer Support Process',
    description: 'Customer service ticket handling workflow',
    category: 'Customer Service',
    tags: ['support', 'tickets', 'customer'],
    text: `Customer Issue
  Create Ticket
    Categorize Issue
      Technical?
        Yes: Technical Team
          Diagnose Problem
            Simple Fix?
              Yes: Resolve Issue
                Update Ticket
                  Close Ticket
                    Customer Satisfaction Survey
              No: Complex Issue
                Senior Technical Review
                  Development Team
                    Bug Fix/Feature
                      Testing
                        Deploy Fix
                          Resolve Issue
        No: General Support
          FAQ Available?
            Yes: Provide FAQ Link
              Issue Resolved?
                Yes: Close Ticket
                No: Personal Assistance
                  Resolve Issue
            No: Personal Assistance
              Resolve Issue
                Update Knowledge Base
                  Close Ticket`,
    preview: 'Customer support workflow with technical and general support paths'
  },
  {
    id: 'e-commerce-order',
    name: 'E-commerce Order Processing',
    description: 'Online order fulfillment process',
    category: 'E-commerce',
    tags: ['ecommerce', 'order', 'fulfillment'],
    text: `Customer Places Order
  Payment Processing
    Payment Successful?
      No: Payment Failed
        Retry Payment?
          Yes: Payment Processing
          No: Order Cancelled
      Yes: Order Confirmed
        Inventory Check
          Items Available?
            No: Backorder
              Notify Customer
                Wait for Stock
                  Items Available?
                    Yes: Prepare Shipment
                    No: Cancel Order
            Yes: Prepare Shipment
              Pick Items
                Pack Order
                  Generate Shipping Label
                    Ship Order
                      Tracking Notification
                        Delivered?
                          Yes: Order Complete
                            Customer Review Request
                          No: Track Package
                            Delivered?`,
    preview: 'Complete e-commerce order processing from payment to delivery'
  },
  {
    id: 'project-management',
    name: 'Project Management Workflow',
    description: 'Project lifecycle management process',
    category: 'Project Management',
    tags: ['project', 'management', 'lifecycle'],
    text: `Project Initiation
  Define Scope
    Stakeholder Analysis
      Resource Planning
        Budget Approval
          Project Charter
            Team Assembly
              Project Planning
                Task Breakdown
                  Timeline Creation
                    Risk Assessment
                      Project Execution
                        Monitor Progress
                          On Track?
                            No: Issue Resolution
                              Adjust Plan
                                Monitor Progress
                            Yes: Continue Execution
                              Milestone Reached?
                                Yes: Stakeholder Review
                                  Approved?
                                    Yes: Next Phase
                                      Continue Execution
                                    No: Revisions Required
                                      Adjust Plan
                                No: Continue Execution
                        Project Completion
                          Deliverable Review
                            Quality Check
                              Project Closure
                                Lessons Learned
                                  Documentation
                                    Project Archive`,
    preview: 'Comprehensive project management workflow with monitoring and reviews'
  },
  {
    id: 'hiring-process',
    name: 'Employee Hiring Process',
    description: 'Complete recruitment and hiring workflow',
    category: 'Human Resources',
    tags: ['hiring', 'recruitment', 'hr'],
    text: `Job Requisition
  Job Posting
    Application Collection
      Initial Screening
        Qualified?
          No: Rejection Email
          Yes: Phone Interview
            Phone Screen Passed?
              No: Rejection Email
              Yes: Technical Assessment
                Assessment Passed?
                  No: Rejection Email
                  Yes: In-Person Interview
                    Interview Panel
                      Candidate Evaluation
                        Hire Recommendation?
                          No: Rejection Email
                          Yes: Reference Check
                            References Good?
                              No: Further Review
                                Hire Decision?
                                  No: Rejection Email
                                  Yes: Job Offer
                              Yes: Job Offer
                                Offer Accepted?
                                  No: End Process
                                  Yes: Background Check
                                    Background Clear?
                                      No: Withdraw Offer
                                      Yes: Onboarding
                                        First Day
                                          Training Program
                                            Probation Period
                                              Performance Review
                                                Permanent Employee`,
    preview: 'Complete hiring process from job posting to permanent employment'
  }
];

export const getExamplesByCategory = (category: string): FlowchartTemplate[] => {
  return flowchartExamples.filter(example => example.category === category);
};

export const getExamplesByTag = (tag: string): FlowchartTemplate[] => {
  return flowchartExamples.filter(example => example.tags.includes(tag));
};

export const searchExamples = (query: string): FlowchartTemplate[] => {
  const lowercaseQuery = query.toLowerCase();
  return flowchartExamples.filter(example => 
    example.name.toLowerCase().includes(lowercaseQuery) ||
    example.description.toLowerCase().includes(lowercaseQuery) ||
    example.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    example.category.toLowerCase().includes(lowercaseQuery)
  );
};

export const getAllCategories = (): string[] => {
  return [...new Set(flowchartExamples.map(example => example.category))];
};

export const getAllTags = (): string[] => {
  return [...new Set(flowchartExamples.flatMap(example => example.tags))];
};
