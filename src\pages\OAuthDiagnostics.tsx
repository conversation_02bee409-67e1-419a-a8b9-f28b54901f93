import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ArrowRight, CheckCircle, ExternalLink, RefreshCw, Trash } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Link } from "react-router-dom";

// This is a diagnostic page to help troubleshoot OAuth issues
const OAuthDiagnostics = () => {
  const [diagnosticsResults, setDiagnosticsResults] = useState<{
    origin: string;
    supabaseUrl: string;
    providersEnabled: { google: boolean };
    testAuthUrl: string | null;
    browserInfo: string;
    localStorageItems: Record<string, boolean>;
    errors: string[];
    redirectFix: boolean;
  }>({
    origin: "",
    supabaseUrl: "",
    providersEnabled: { google: false },
    testAuthUrl: null,
    browserInfo: "",
    localStorageItems: {},
    errors: [],
    redirectFix: false
  });

  const [isRunning, setIsRunning] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    // Get basic environment info on load
    const localStorageItems: Record<string, boolean> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('supabase') || key.includes('oauth'))) {
        localStorageItems[key] = true;
      }
    }
    
    setDiagnosticsResults(prev => ({
      ...prev,
      origin: window.location.origin,
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL || "Not configured",
      browserInfo: `${navigator.userAgent}`,
      localStorageItems,
      redirectFix: false
    }));
  }, []);

  const clearAuthData = () => {
    setIsClearing(true);
    
    try {
      // Clear all Supabase related localStorage items
      const cleared: string[] = [];
      for (const key in localStorage) {
        if (key.startsWith('supabase') || key.includes('oauth')) {
          localStorage.removeItem(key);
          cleared.push(key);
        }
      }
      
      // Remove session cookie if it exists
      document.cookie = "sb-auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      
      // Update state to reflect cleared items
      setDiagnosticsResults(prev => ({
        ...prev,
        localStorageItems: {}
      }));
      
      if (cleared.length > 0) {
        toast.success(`Cleared ${cleared.length} authentication data items`);
      } else {
        toast.info("No authentication data to clear");
      }
    } catch (error) {
      toast.error("Failed to clear authentication data");
    } finally {
      setIsClearing(false);
    }
  };

  const fixRedirectIssue = () => {
    setDiagnosticsResults(prev => ({ ...prev, redirectFix: true }));
    toast.info("Try using this solution if you're experiencing bad_oauth_state errors");
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    const errors: string[] = [];
    
    try {
      // Check if /auth/callback route exists by making a HEAD request
      try {
        const callbackUrl = `${window.location.origin}/auth/callback`;
        const response = await fetch(callbackUrl, { method: 'HEAD' });
        
        if (response.status === 404) {
          errors.push('The /auth/callback route returned a 404 status - route may not exist in your router configuration');
        } else {
          console.log("Auth callback route check:", response.status);
        }
      } catch (error) {
        console.error("Error checking callback route:", error);
      }
      
      // Log App origins
      console.log("Current origin:", window.location.origin);
      console.log("Testing callback URL:", `${window.location.origin}/auth/callback`);
      
      // Update localStorage items list
      const localStorageItems: Record<string, boolean> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith('supabase') || key.includes('oauth'))) {
          localStorageItems[key] = true;
        }
      }
      
      // Check if Google provider is configured
      try {
        // Test by trying to get a sign-in URL without actually redirecting
        const { data, error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/auth/callback`,
            skipBrowserRedirect: true
          }
        });

        if (error) {
          if (error.message.includes('provider is not enabled')) {
            errors.push("Google provider is not enabled in Supabase");
            setDiagnosticsResults(prev => ({
              ...prev,
              providersEnabled: { google: false },
              localStorageItems,
              errors
            }));
          } else {
            errors.push(`OAuth error: ${error.message}`);
            setDiagnosticsResults(prev => ({
              ...prev,
              localStorageItems,
              errors
            }));
          }
        } else {
          // Check if URL has required parameters
          try {
            const url = new URL(data.url);
            const hasCode = url.searchParams.has('code');
            const hasState = url.searchParams.has('state');
            
            if (!hasCode && !hasState) {
              errors.push("OAuth URL is missing expected parameters (code or state)");
            }
            
            setDiagnosticsResults(prev => ({
              ...prev,
              providersEnabled: { google: true },
              testAuthUrl: data.url,
              localStorageItems,
              errors
            }));
          } catch (urlError) {
            errors.push("Failed to parse generated OAuth URL");
            setDiagnosticsResults(prev => ({
              ...prev,
              providersEnabled: { google: true },
              testAuthUrl: data.url,
              localStorageItems,
              errors
            }));
          }
        }
      } catch (error: any) {
        errors.push(`OAuth test error: ${error.message}`);
        setDiagnosticsResults(prev => ({
          ...prev,
          localStorageItems,
          errors
        }));
      }
    } catch (error: any) {
      errors.push(`Unexpected error: ${error.message}`);
      setDiagnosticsResults(prev => ({
        ...prev,
        errors
      }));
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 flex items-center justify-center p-6">
      <Card className="w-full max-w-3xl border-0 shadow-xl bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-2xl font-bold text-gray-900">
            OAuth Diagnostics
          </CardTitle>
          <CardDescription className="text-lg">
            Troubleshoot Google OAuth configuration issues
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Quick Fixes for Common Issues */}
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-md space-y-3">
            <h3 className="text-lg font-medium text-yellow-800">Current Error Fixes</h3>
            
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-800">Error: "bad_oauth_state" or "Unable to exchange external code"</p>
                  <p className="text-sm text-yellow-700 mb-2">
                    These errors often occur when there's a mismatch in the OAuth flow. Try these solutions:
                  </p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <Button variant="outline" size="sm" onClick={clearAuthData} disabled={isClearing}>
                      {isClearing ? <><div className="animate-spin rounded-full h-3 w-3 border-b-2 border-yellow-600 mr-1"></div> Clearing...</> : <><Trash className="h-3 w-3 mr-1" /> Clear Auth Data</>}
                    </Button>
                    <Button variant="outline" size="sm" onClick={fixRedirectIssue} disabled={diagnosticsResults.redirectFix}>
                      <RefreshCw className="h-3 w-3 mr-1" /> Show Direct Login Fix
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {diagnosticsResults.redirectFix && (
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-md space-y-3">
              <h3 className="text-lg font-medium text-blue-800">Direct Login Method</h3>
              
              <div>
                <p className="text-sm text-blue-700 mb-2">
                  If you're experiencing persistent OAuth issues, try this direct login method that bypasses some OAuth state issues:
                </p>
                <div className="p-3 bg-blue-100 rounded-md">
                  <pre className="text-xs overflow-auto whitespace-pre-wrap"><code>{`// Add this function to LoginPage.tsx and use it instead of handleGoogleLogin

const handleDirectGoogleLogin = () => {
  // Clear auth data first
  for (const key in localStorage) {
    if (key.startsWith('supabase.auth')) {
      localStorage.removeItem(key);
    }
  }
  
  // Redirect directly to the Supabase OAuth endpoint
  const redirectUrl = \`${diagnosticsResults.supabaseUrl}/auth/v1/authorize?provider=google&redirect_to=\${window.location.origin}/auth/callback\`;
  window.location.href = redirectUrl;
}`}</code></pre>
                </div>
              </div>
            </div>
          )}

          {/* Environment Information */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Environment Information</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <p className="text-sm font-medium">App Origin:</p>
                  <p className="text-sm font-mono">{diagnosticsResults.origin}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Supabase URL:</p>
                  <p className="text-sm font-mono">{diagnosticsResults.supabaseUrl}</p>
                </div>
                <div className="col-span-1 md:col-span-2">
                  <p className="text-sm font-medium">Browser:</p>
                  <p className="text-sm font-mono truncate">{diagnosticsResults.browserInfo}</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Local Storage State */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Authentication Storage State</h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={clearAuthData}
                disabled={isClearing || Object.keys(diagnosticsResults.localStorageItems).length === 0}
              >
                {isClearing ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
                    Clearing...
                  </>
                ) : (
                  <>
                    <Trash className="h-4 w-4 mr-1" /> Clear Auth Data
                  </>
                )}
              </Button>
            </div>
            <div className="bg-gray-50 p-4 rounded-md">
              {Object.keys(diagnosticsResults.localStorageItems).length > 0 ? (
                <div className="space-y-1">
                  <p className="text-sm font-medium mb-2">Found {Object.keys(diagnosticsResults.localStorageItems).length} authentication-related items:</p>
                  <ul className="space-y-1 text-sm font-mono text-gray-600">
                    {Object.keys(diagnosticsResults.localStorageItems).map(key => (
                      <li key={key} className="truncate">{key}</li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p className="text-sm text-gray-600">No authentication data found in local storage</p>
              )}
            </div>
          </div>

          {/* Run Diagnostics Button */}
          <div className="flex justify-center">
            <Button 
              onClick={runDiagnostics} 
              disabled={isRunning}
              className="bg-gradient-to-r from-blue-600 to-purple-600"
            >
              {isRunning ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Running Diagnostics...
                </>
              ) : (
                <>
                  Run OAuth Diagnostics
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>

          {/* Results */}
          {(diagnosticsResults.providersEnabled.google !== undefined || diagnosticsResults.errors.length > 0) && (
            <div className="space-y-4 mt-6">
              <h3 className="text-lg font-semibold">Diagnostic Results</h3>
              
              {/* Provider Status */}
              <div className="flex items-center gap-2 p-3 rounded-md bg-gray-50">
                <div className="flex-shrink-0">
                  {diagnosticsResults.providersEnabled.google ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                  )}
                </div>
                <div>
                  <p className="font-medium">
                    Google OAuth Provider: {diagnosticsResults.providersEnabled.google ? "Enabled" : "Not Enabled"}
                  </p>
                  {!diagnosticsResults.providersEnabled.google && (
                    <p className="text-sm text-gray-600">
                      The Google provider needs to be enabled in your Supabase project settings.
                    </p>
                  )}
                </div>
              </div>

              {/* OAuth URL Test */}
              {diagnosticsResults.testAuthUrl && (
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="font-medium mb-1">OAuth URL Generated Successfully</p>
                  <p className="text-sm text-gray-600 mb-2">
                    The application was able to generate a valid OAuth URL.
                  </p>
                </div>
              )}

              {/* Errors */}
              {diagnosticsResults.errors.length > 0 && (
                <div className="bg-red-50 border border-red-100 p-3 rounded-md">
                  <p className="font-medium text-red-800 mb-2">Errors Detected:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    {diagnosticsResults.errors.map((error, index) => (
                      <li key={index} className="text-sm text-red-700">{error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Configuration Instructions */}
              <div className="bg-blue-50 border border-blue-100 p-4 rounded-md space-y-3">
                <p className="font-medium text-blue-800">Recommended Configuration:</p>
                
                <div>
                  <p className="text-sm font-medium text-blue-800">In Google Cloud Console:</p>
                  <ul className="list-disc pl-5 text-sm text-blue-700">
                    <li>Make sure your Google Client ID and Secret are correctly entered</li>
                    <li>Authorized JavaScript origins should include:
                      <pre className="mt-1 bg-blue-100 p-1 rounded text-xs">{diagnosticsResults.supabaseUrl}<br/>{diagnosticsResults.origin}</pre>
                    </li>
                    <li>Authorized redirect URIs should include:
                      <pre className="mt-1 bg-blue-100 p-1 rounded text-xs">{`${diagnosticsResults.supabaseUrl}/auth/v1/callback`}<br/>{`${diagnosticsResults.origin}/auth/callback`}</pre>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-blue-800">In Supabase Dashboard:</p>
                  <ul className="list-disc pl-5 text-sm text-blue-700">
                    <li>Go to Authentication → Providers → Google</li>
                    <li>Enable the Google provider</li>
                    <li>Enter your Google Client ID and Secret</li>
                    <li>Make sure "Authorized redirect URL" is set to:
                      <pre className="mt-1 bg-blue-100 p-1 rounded text-xs">{`${diagnosticsResults.supabaseUrl}/auth/v1/callback`}</pre>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-blue-800">Specific Error Solutions:</p>
                  <ul className="list-disc pl-5 text-sm text-blue-700">
                    <li><strong>"bad_oauth_state"</strong>: 
                      <ul className="list-disc pl-5 mt-1">
                        <li>Clear all browser storage using the button above</li>
                        <li>Try using incognito/private mode</li>
                        <li>Try the direct login method above</li>
                        <li>Use only one authentication method at a time (don't mix methods)</li>
                      </ul>
                    </li>
                    <li><strong>"Unable to exchange external code"</strong>: 
                      <ul className="list-disc pl-5 mt-1">
                        <li>Make sure redirect URIs in Google Cloud Console and Supabase match exactly</li>
                        <li>Try signing out completely, clear browser storage, then sign in again</li>
                        <li>Check that your Google Cloud OAuth screen is configured correctly</li>
                      </ul>
                    </li>
                  </ul>
                </div>
                
                <p className="text-sm text-blue-700">
                  <strong>Note:</strong> After making configuration changes, it may take up to 30 minutes for them to fully propagate.
                </p>
                
                <div className="border-t border-blue-200 mt-2 pt-2">
                  <p className="text-sm font-medium text-blue-800 flex items-center">
                    <ExternalLink className="h-4 w-4 mr-1" /> Additional Resources:
                  </p>
                  <ul className="list-disc pl-5 text-sm text-blue-700">
                    <li><a href="https://supabase.com/docs/guides/auth/social-login/auth-google" target="_blank" rel="noopener noreferrer" className="underline">Supabase Google OAuth Documentation</a></li>
                    <li><a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener noreferrer" className="underline">Google Cloud Console Credentials</a></li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Route testing section */}
          <div className="space-y-3 mt-6">
            <h3 className="text-lg font-semibold">Route Testing</h3>
            <div className="flex flex-wrap gap-3 justify-center">
              <Button 
                variant="outline" 
                onClick={() => window.open(`${window.location.origin}/auth/callback?test=true`, '_blank')}
              >
                Test Auth Callback Route
              </Button>
              <Link to="/login">
                <Button variant="outline">
                  Return to Login
                </Button>
              </Link>
            </div>
            <p className="text-sm text-center text-gray-500">
              "Test Auth Callback Route" should show the callback loading page, not a 404 error
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OAuthDiagnostics;
