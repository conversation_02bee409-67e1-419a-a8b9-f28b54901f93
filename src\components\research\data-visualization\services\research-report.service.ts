import { GoogleGenAI } from '@google/genai';
import {
  UploadedFile,
  DataAnalysisResult,
  ResearchReport,
  ResearchFigure,
  ResearchTable,
  VisualizationConfig
} from '../types';

export class ResearchReportService {
  private static ai: GoogleGenAI | null = null;

  /**
   * Initialize Gemini AI client
   */
  private static getAI(): GoogleGenAI {
    if (!this.ai) {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('Gemini API key is not configured.');
      }
      this.ai = new GoogleGenAI({ apiKey });
    }
    return this.ai;
  }

  /**
   * Generate comprehensive research report from analysis results
   */
  static async generateResearchReport(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    reportConfig: {
      title?: string;
      researchField?: string;
      citationStyle?: 'APA' | 'IEEE' | 'Harvard' | 'MLA';
      includeAbstract?: boolean;
      includeLimitations?: boolean;
    } = {}
  ): Promise<ResearchReport> {
    const config = {
      title: reportConfig.title || `Analysis of ${file.name}`,
      researchField: reportConfig.researchField || 'Data Science',
      citationStyle: reportConfig.citationStyle || 'APA',
      includeAbstract: reportConfig.includeAbstract ?? true,
      includeLimitations: reportConfig.includeLimitations ?? true
    };

    console.log('Generating research report with sequential approach...');

    try {
      // Step 1: Generate Abstract
      const abstract = config.includeAbstract 
        ? await this.generateAbstract(file, analysis, config)
        : '';

      // Step 2: Generate Introduction
      const introduction = await this.generateIntroduction(file, analysis, config);

      // Step 3: Generate Methods Section
      const methods = await this.generateMethods(file, analysis, config);

      // Step 4: Generate Results Section
      const results = await this.generateResults(file, analysis, config);

      // Step 5: Generate Discussion
      const discussion = await this.generateDiscussion(file, analysis, config);

      // Step 6: Generate Conclusion
      const conclusion = await this.generateConclusion(file, analysis, config);

      // Step 7: Generate Limitations (if requested)
      const limitations = config.includeLimitations
        ? await this.generateLimitations(file, analysis, config)
        : '';

      // Step 8: Generate Future Research Suggestions
      const futureResearch = await this.generateFutureResearch(file, analysis, config);

      // Step 9: Process Figures and Tables
      const figures = await this.processFigures(analysis.visualizations);
      const tables = await this.generateTables(file, analysis);

      // Step 10: Generate References
      const references = await this.generateReferences(config);

      const report: ResearchReport = {
        id: `report-${Date.now()}`,
        fileId: file.id,
        analysisId: analysis.id,
        title: config.title,
        abstract,
        sections: {
          introduction,
          methods,
          results,
          discussion,
          conclusion,
          limitations,
          futureResearch
        },
        figures,
        tables,
        references,
        metadata: {
          generatedAt: new Date(),
          wordCount: this.calculateWordCount([
            abstract, introduction, methods, results, 
            discussion, conclusion, limitations, futureResearch
          ]),
          citationStyle: config.citationStyle,
          researchField: config.researchField
        }
      };

      return report;

    } catch (error) {
      console.error('Research report generation failed:', error);
      throw new Error(`Report generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate Abstract section
   */
  private static async generateAbstract(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const prompt = `
Write a structured abstract for a research paper analyzing the dataset "${file.name}".

Dataset characteristics:
- Sample size: ${file.data.length}
- Variables: ${file.headers.join(', ')}
- Key findings: ${analysis.insights.keyFindings.join('; ')}

Research context:
- Field: ${config.researchField}
- Analysis type: Statistical data analysis

Structure the abstract with:
1. Background/Objective (1-2 sentences)
2. Methods (1-2 sentences)
3. Results (2-3 sentences highlighting key findings)
4. Conclusions (1-2 sentences)

Write in academic style, 150-250 words, past tense for completed work.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Introduction section
   */
  private static async generateIntroduction(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const prompt = `
Write an introduction section for a research paper analyzing "${file.name}".

Context:
- Research field: ${config.researchField}
- Dataset variables: ${file.headers.join(', ')}
- Sample size: ${file.data.length}

Structure:
1. Background and context (2-3 paragraphs)
2. Research gap or problem statement
3. Study objectives and research questions
4. Brief overview of approach

Write 300-500 words in academic style with clear research rationale.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Methods section
   */
  private static async generateMethods(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const researchAnalysis = analysis.researchAnalysis;
    
    const prompt = `
Write a methods section for data analysis research.

Study details:
- Dataset: ${file.name}
- Sample size: ${file.data.length}
- Variables: ${file.headers.join(', ')}
- Study type: ${researchAnalysis?.studyType || 'observational'}
- Statistical tests: ${researchAnalysis?.statisticalTests.map(t => t.test).join(', ') || 'descriptive statistics'}

Include:
1. Data source and collection
2. Sample characteristics
3. Variables and measurements
4. Statistical analysis plan
5. Software and significance levels

Write 200-400 words in past tense, academic style.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Results section
   */
  private static async generateResults(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const prompt = `
Write a results section presenting statistical findings.

Data summary:
- Sample: ${file.data.length} observations
- Variables: ${file.headers.join(', ')}
- Key findings: ${analysis.insights.keyFindings.join('; ')}
- Patterns: ${analysis.insights.patterns.join('; ')}
- Correlations: ${analysis.insights.correlations.join('; ')}

Structure:
1. Sample characteristics
2. Descriptive statistics
3. Main statistical findings
4. Additional analyses
5. Figure/table references

Write 300-600 words, objective tone, past tense. Include statistical details where appropriate.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Discussion section
   */
  private static async generateDiscussion(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const prompt = `
Write a discussion section interpreting the research findings.

Key findings: ${analysis.insights.keyFindings.join('; ')}
Patterns: ${analysis.insights.patterns.join('; ')}
Recommendations: ${analysis.insights.recommendations.join('; ')}

Structure:
1. Interpretation of main findings
2. Comparison with existing literature (general statements)
3. Practical implications
4. Theoretical contributions
5. Study strengths

Write 400-700 words, analytical and interpretive tone.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Conclusion section
   */
  private static async generateConclusion(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const prompt = `
Write a conclusion section summarizing the research.

Key findings: ${analysis.insights.keyFindings.join('; ')}
Recommendations: ${analysis.insights.recommendations.join('; ')}

Include:
1. Summary of main findings
2. Research contributions
3. Practical applications
4. Final thoughts

Write 150-300 words, concise and impactful.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Limitations section
   */
  private static async generateLimitations(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const limitations = analysis.researchAnalysis?.limitations || [];
    
    const prompt = `
Write a limitations section for the research study.

Study context:
- Sample size: ${file.data.length}
- Study type: ${analysis.researchAnalysis?.studyType || 'observational'}
- Identified limitations: ${limitations.join('; ')}

Address:
1. Sample limitations
2. Design limitations
3. Measurement limitations
4. Generalizability concerns

Write 200-400 words, honest and balanced tone.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Generate Future Research section
   */
  private static async generateFutureResearch(
    file: UploadedFile,
    analysis: DataAnalysisResult,
    config: any
  ): Promise<string> {
    const futureResearch = analysis.researchAnalysis?.futureResearch || [];
    
    const prompt = `
Suggest future research directions based on current findings.

Current findings: ${analysis.insights.keyFindings.join('; ')}
Suggested directions: ${futureResearch.join('; ')}

Include:
1. Methodological improvements
2. Extended research questions
3. Different populations or contexts
4. Longitudinal studies
5. Experimental designs

Write 200-350 words, forward-looking and constructive.
`;

    return await this.executeGeminiGeneration(prompt);
  }

  /**
   * Process visualizations into research figures
   */
  private static async processFigures(visualizations: VisualizationConfig[]): Promise<ResearchFigure[]> {
    return visualizations.map((viz, index) => ({
      id: `figure-${index + 1}`,
      number: `Figure ${index + 1}`,
      title: viz.title,
      caption: viz.description,
      visualization: viz,
      placement: 'inline',
      quality: 'publication-ready'
    }));
  }

  /**
   * Generate research tables
   */
  private static async generateTables(file: UploadedFile, analysis: DataAnalysisResult): Promise<ResearchTable[]> {
    const tables: ResearchTable[] = [];

    // Descriptive statistics table
    if (analysis.summary.basicStats) {
      tables.push({
        id: 'table-1',
        number: 'Table 1',
        title: 'Descriptive Statistics',
        caption: 'Summary statistics for all variables in the dataset',
        data: this.formatDescriptiveStatsTable(analysis.summary.basicStats),
        headers: ['Variable', 'N', 'Mean', 'SD', 'Min', 'Max'],
        statisticalSummary: true
      });
    }

    return tables;
  }

  /**
   * Helper methods
   */
  private static async executeGeminiGeneration(prompt: string): Promise<string> {
    try {
      const ai = this.getAI();
      
      const response = await ai.models.generateContent({
        model: 'gemini-2.0-flash-exp',
        config: {
          generationConfig: {
            maxOutputTokens: 2048,
            temperature: 0.7
          }
        },
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }]
      });

      return response.text || '';
    } catch (error) {
      console.error('Gemini generation error:', error);
      return 'Content generation failed. Please try again.';
    }
  }

  private static calculateWordCount(sections: string[]): number {
    return sections.join(' ').split(/\s+/).length;
  }

  private static formatDescriptiveStatsTable(basicStats: any): any[][] {
    return Object.entries(basicStats).map(([variable, stats]: [string, any]) => [
      variable,
      stats.count || 'N/A',
      stats.mean?.toFixed(2) || 'N/A',
      stats.std?.toFixed(2) || 'N/A',
      stats.min || 'N/A',
      stats.max || 'N/A'
    ]);
  }

  private static async generateReferences(config: any): Promise<string[]> {
    // Placeholder for reference generation
    return [
      'Statistical analysis conducted using advanced computational methods.',
      'Data visualization and interpretation following best practices in research methodology.'
    ];
  }
}
