/**
 * Article Finder AI Service
 * Provides article analysis and journal recommendations using Google Gemini API
 */

import { GoogleGenAI } from '@google/genai';
import { 
  ArticleFinderRequest, 
  ArticleFinderResponse, 
  ArticleAnalysis, 
  JournalRecommendation,
  ArticleContent,
  InputType,
  AnalysisDepth
} from '../types';
import { ANALYSIS_PROMPTS } from '../constants';

export class ArticleFinderAIService {
  private ai: GoogleGenAI;
  private lastRequestTime: Date | null = null;
  private readonly delayBetweenRequests = 2000; // 2 seconds between requests

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('Google Gemini API key not found. Please add VITE_GEMINI_API_KEY to your .env file.');
    }

    if (apiKey.length < 20) {
      throw new Error('Google Gemini API key appears to be invalid. Please check your VITE_GEMINI_API_KEY.');
    }

    this.ai = new GoogleGenAI({ apiKey });
    console.log('Article Finder AI service initialized');
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const testPrompt = "Respond with 'API connection successful' if you can read this.";
      await this.makeGeminiRequest(testPrompt, 'gemini-2.5-flash');
      return { success: true };
    } catch (error) {
      console.error('Gemini API connection test failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Analyze article content and generate journal recommendations
   */
  async analyzeArticle(request: ArticleFinderRequest): Promise<ArticleFinderResponse> {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildAnalysisPrompt(
        request.content, 
        request.inputType, 
        request.analysisDepth,
        request.additionalContext
      );
      

      // Fallback: If user selects gemini-2.5-pro, use gemini-2.5-flash and show warning
      let modelToUse = request.model;
      if (modelToUse === 'gemini-2.5-pro') {
        modelToUse = 'gemini-2.5-flash';
        if (typeof window !== 'undefined' && window?.toast) {
          window.toast.warning?.('Gemini 2.5 Pro is not available for public API keys. Using Gemini 2.5 Flash instead.');
        } else if (typeof toast !== 'undefined') {
          toast.warning?.('Gemini 2.5 Pro is not available for public API keys. Using Gemini 2.5 Flash instead.');
        }
      }

      const response = await this.makeGeminiRequest(
        prompt,
        modelToUse,
        {
          maxOutputTokens: 8192,
          temperature: 0.3
        }
      );

      // Parse the JSON response
      const analysisData = this.parseAnalysisResponse(response);
      
      // Create analysis object
      const analysis: ArticleAnalysis = {
        researchDomain: analysisData.researchDomain,
        methodology: analysisData.methodology,
        keyTopics: analysisData.keyTopics || [],
        researchThemes: analysisData.researchThemes || [],
        academicField: analysisData.academicField,
        noveltyScore: analysisData.noveltyScore || 0.7,
        contributionLevel: analysisData.contributionLevel || 'Medium',
        recommendedJournals: this.processJournalRecommendations(analysisData.journals || []),
        analysisConfidence: analysisData.confidence || 0.8,
        generatedAt: new Date()
      };

      const processingTime = Date.now() - startTime;

      return {
        analysis,
        recommendations: analysis.recommendedJournals,
        confidence: analysis.analysisConfidence,
        processingTime
      };

    } catch (error) {
      console.error('Article analysis failed:', error);
      throw new Error(`Failed to analyze article: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate detailed journal analysis for a specific journal
   */
  async analyzeSpecificJournal(
    articleContent: string, 
    journalName: string, 
    model: string = 'gemini-2.5-flash'
  ): Promise<{ matchScore: number; analysis: string; recommendations: string[] }> {
    try {
      const prompt = this.buildJournalSpecificPrompt(articleContent, journalName);
      
      const response = await this.makeGeminiRequest(prompt, model, {
        maxOutputTokens: 2048,
        temperature: 0.2
      });

      const analysisData = this.parseJournalAnalysisResponse(response);
      
      return {
        matchScore: analysisData.matchScore || 0.5,
        analysis: analysisData.analysis || 'Analysis not available',
        recommendations: analysisData.recommendations || []
      };

    } catch (error) {
      console.error('Journal-specific analysis failed:', error);
      throw new Error(`Failed to analyze journal match: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Make request to Gemini API with rate limiting
   */
  private async makeGeminiRequest(
    prompt: string,
    modelName: string,
    config: any = {}
  ): Promise<string> {
    // Rate limiting
    if (this.lastRequestTime) {
      const timeSinceLastRequest = Date.now() - this.lastRequestTime.getTime();
      if (timeSinceLastRequest < this.delayBetweenRequests) {
        const delay = this.delayBetweenRequests - timeSinceLastRequest;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    this.lastRequestTime = new Date();

    try {
      const requestConfig = {
        generationConfig: {
          maxOutputTokens: config.maxOutputTokens || 4096,
          temperature: config.temperature || 0.7,
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: prompt }],
      }];

      let modelTried = modelName;
      let lastError = null;
      const tryModels = [modelName, 'gemini-2.5-flash', 'gemini-1.0-pro'];
      for (const model of tryModels) {
        try {
          const response = await this.ai.models.generateContentStream({
            model, // Use full model name as required by Gemini API
            config: requestConfig,
            contents,
          });

          let fullText = '';
          for await (const chunk of response) {
            if (chunk.text) {
              fullText += chunk.text;
            }
          }

          if (!fullText) {
            throw new Error('Empty response from Gemini API');
          }

          if (model !== modelName && typeof window !== 'undefined' && window?.toast) {
            window.toast.warning?.(`Gemini model '${modelName}' is not available. Using '${model}' instead.`);
          }
          return fullText;
        } catch (err) {
          lastError = err;
          // Try next model
        }
      }
      // If all models fail, throw last error
      throw new Error(`Gemini API error: ${lastError?.message || 'Unknown error. No Gemini models are available for your API key.'}`);
    } catch (error) {
      console.error('Gemini API request failed:', error);

      if (error.message?.includes('API_KEY_INVALID')) {
        throw new Error('Invalid Gemini API key. Please check your VITE_GEMINI_API_KEY.');
      }

      if (error.message?.includes('QUOTA_EXCEEDED')) {
        throw new Error('Gemini API quota exceeded. Please try again later.');
      }

      throw new Error(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Build analysis prompt based on input type and depth
   */
  private buildAnalysisPrompt(
    content: string, 
    inputType: InputType, 
    depth: AnalysisDepth,
    additionalContext?: string
  ): string {
    const basePrompt = ANALYSIS_PROMPTS[depth];
    const contextInfo = additionalContext ? `\n\nAdditional Context: ${additionalContext}` : '';
    
    return `${basePrompt}

Input Type: ${inputType}
Content to analyze:
${content}${contextInfo}

Please provide your response in the following JSON format:
{
  "researchDomain": "string",
  "methodology": "string", 
  "keyTopics": ["topic1", "topic2", ...],
  "researchThemes": ["theme1", "theme2", ...],
  "academicField": "string",
  "noveltyScore": 0.0-1.0,
  "contributionLevel": "High|Medium|Low",
  "confidence": 0.0-1.0,
  "journals": [
    {
      "name": "Journal Name",
      "publisher": "Publisher",
      "description": "Brief description",
      "scope": ["scope1", "scope2"],
      "impactFactor": 0.0,
      "acceptanceRate": 0.0,
      "averageReviewTime": 0,
      "publicationFees": 0,
      "isOpenAccess": boolean,
      "matchScore": 0.0-1.0,
      "matchReasons": ["reason1", "reason2"]
    }
  ]
}`;
  }

  /**
   * Build journal-specific analysis prompt
   */
  private buildJournalSpecificPrompt(content: string, journalName: string): string {
    return `Analyze how well this research content matches the journal "${journalName}".

Research Content:
${content}

Provide analysis in JSON format:
{
  "matchScore": 0.0-1.0,
  "analysis": "detailed analysis text",
  "recommendations": ["recommendation1", "recommendation2"]
}`;
  }

  /**
   * Parse analysis response from Gemini
   */
  private parseAnalysisResponse(response: string): any {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Failed to parse analysis response:', error);
      throw new Error('Invalid response format from AI service');
    }
  }

  /**
   * Parse journal analysis response
   */
  private parseJournalAnalysisResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Failed to parse journal analysis response:', error);
      return {
        matchScore: 0.5,
        analysis: 'Analysis parsing failed',
        recommendations: []
      };
    }
  }

  /**
   * Process and enhance journal recommendations
   */
  private processJournalRecommendations(journals: any[]): JournalRecommendation[] {
    return journals.map((journal, index) => ({
      id: `journal-${Date.now()}-${index}`,
      name: journal.name || 'Unknown Journal',
      publisher: journal.publisher || 'Unknown Publisher',
      issn: journal.issn || '',
      website: journal.website || '',
      description: journal.description || '',
      scope: journal.scope || [],
      targetAudience: journal.targetAudience || 'Academic researchers',
      editorialBoard: journal.editorialBoard || [],
      recentTopics: journal.recentTopics || [],
      metrics: {
        impactFactor: journal.impactFactor || 0,
        citeScore: journal.citeScore || 0,
        hIndex: journal.hIndex || 0,
        acceptanceRate: journal.acceptanceRate || 0,
        averageReviewTime: journal.averageReviewTime || 90,
        averagePublicationTime: journal.averagePublicationTime || 120,
        publicationFees: journal.publicationFees || 0,
        isOpenAccess: journal.isOpenAccess || false,
        quartile: journal.quartile || 'Q3'
      },
      matchScore: journal.matchScore || 0.5,
      matchReasons: journal.matchReasons || [],
      submissionGuidelines: journal.submissionGuidelines || 'Check journal website for guidelines',
      specialIssues: journal.specialIssues || [],
      geographicFocus: journal.geographicFocus || 'International',
      languageRequirements: journal.languageRequirements || ['English']
    }));
  }
}

export const articleFinderAIService = new ArticleFinderAIService();

// Add to window for testing
if (typeof window !== 'undefined') {
  (window as any).testArticleFinderAI = async () => {
    console.log('Testing Article Finder AI...');
    try {
      const testRequest = {
        content: 'Machine learning applications in healthcare diagnostics',
        inputType: 'title' as InputType,
        analysisDepth: 'basic' as AnalysisDepth,
        model: 'gemini-2.5-flash'
      };

      const result = await articleFinderAIService.analyzeArticle(testRequest);
      console.log('✅ Article Finder AI test successful:', result);
      return result;
    } catch (error) {
      console.error('❌ Article Finder AI test failed:', error);
      throw error;
    }
  };
}
