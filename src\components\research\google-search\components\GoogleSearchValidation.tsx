/**
 * Google Search Validation Component
 * Tests and validates Google Search grounding and academic source quality
 */

import React, { useState } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Search, 
  ExternalLink,
  BookOpen,
  Shield,
  Zap,
  Play,
  RefreshCw
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';

import { googleSearchService } from '../services/google-search.service';
import {
  runAllValidationTests,
  testAcademicSearchIntegration,
  ValidationResult
} from '../utils/validation-utils';

// ValidationResult is now imported from validation-utils

interface SourceValidation {
  url: string;
  isValid: boolean;
  isAcademic: boolean;
  score: number;
  domain: string;
  title: string;
}

export function GoogleSearchValidation() {
  const [isRunning, setIsRunning] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [sourceValidations, setSourceValidations] = useState<SourceValidation[]>([]);
  const [testProgress, setTestProgress] = useState(0);
  const [searchResult, setSearchResult] = useState<any>(null);

  const testQueries = [
    'machine learning applications in healthcare',
    'climate change impact on agriculture',
    'renewable energy technologies research',
    'artificial intelligence ethics frameworks',
    'quantum computing algorithms'
  ];

  const runValidationTests = async () => {
    setIsRunning(true);
    setValidationResults([]);
    setSourceValidations([]);
    setTestProgress(0);

    try {
      // Run all validation tests using the utility functions
      setTestProgress(20);
      const results = runAllValidationTests();

      setTestProgress(50);
      setValidationResults(results);

      // Check if API is configured before running real search test
      const apiConfigured = results.find(r => r.test === 'API Configuration')?.status === 'pass';

      if (!apiConfigured) {
        setTestProgress(100);
        setIsRunning(false);
        return;
      }

      // Run real search test
      setTestProgress(70);
      const testQuery = testQueries[Math.floor(Math.random() * testQueries.length)];
      
      try {
        const searchResult = await googleSearchService.searchAcademic(testQuery, {
          maxResults: 5,
          searchDepth: 'advanced',
          includeAcademicSources: true,
          academicFocus: true,
          citationStyle: 'apa'
        });

        setSearchResult(searchResult);
        setTestProgress(80);

        // Validate sources
        const sourceValidations: SourceValidation[] = searchResult.sources.map(source => ({
          url: source.url,
          isValid: googleSearchService['isValidUrl'](source.url),
          isAcademic: googleSearchService['isAcademicSource'](source.url, source.domain),
          score: googleSearchService['calculateAcademicScore'](source),
          domain: source.domain,
          title: source.title
        }));

        setSourceValidations(sourceValidations);

        const validSources = sourceValidations.filter(s => s.isValid).length;
        const academicSources = sourceValidations.filter(s => s.isAcademic).length;
        const highQualitySources = sourceValidations.filter(s => s.score > 0.6).length;

        const searchTestResult: ValidationResult = {
          test: 'Real Search Test',
          status: validSources > 0 && academicSources > 0 ? 'pass' : 'fail',
          message: `Found ${validSources} valid sources, ${academicSources} academic sources`,
          details: {
            totalSources: sourceValidations.length,
            validSources,
            academicSources,
            highQualitySources,
            query: testQuery
          }
        };

        // Add search test result to existing results
        const updatedResults = [...results, searchTestResult];
        setValidationResults(updatedResults);

      } catch (error) {
        const errorResult: ValidationResult = {
          test: 'Real Search Test',
          status: 'fail',
          message: `Search failed: ${error.message}`,
          details: { error: error.message }
        };

        const updatedResults = [...results, errorResult];
        setValidationResults(updatedResults);
      }

      setTestProgress(100);

    } catch (error) {
      const errorResult: ValidationResult = {
        test: 'Validation Error',
        status: 'fail',
        message: `Validation failed: ${error.message}`,
        details: { error: error.message }
      };

      setValidationResults([errorResult]);
    }

    setIsRunning(false);
  };

  const runIntegrationTest = async () => {
    setIsRunning(true);
    try {
      const success = await testAcademicSearchIntegration();
      const result: ValidationResult = {
        test: 'Integration Test',
        status: success ? 'pass' : 'fail',
        message: success ? 
          'Academic search integration working correctly' : 
          'Academic search integration needs improvement'
      };
      setValidationResults([result]);
    } catch (error) {
      setValidationResults([{
        test: 'Integration Test',
        status: 'fail',
        message: `Integration test failed: ${error.message}`
      }]);
    }
    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'fail': return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      default: return <AlertTriangle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const passedTests = validationResults.filter(r => r.status === 'pass').length;
  const totalTests = validationResults.length;

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Google Search Validation</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Validate Google Search grounding, academic source detection, and real URL verification
        </p>
      </div>

      {/* Controls */}
      <div className="flex justify-center space-x-4">
        <Button 
          onClick={runValidationTests}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isRunning ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Play className="h-4 w-4 mr-2" />}
          Run Full Validation
        </Button>
        <Button 
          variant="outline"
          onClick={runIntegrationTest}
          disabled={isRunning}
        >
          <Zap className="h-4 w-4 mr-2" />
          Quick Integration Test
        </Button>
      </div>

      {/* Progress */}
      {isRunning && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Running validation tests...</span>
                <span>{testProgress}%</span>
              </div>
              <Progress value={testProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {validationResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-green-600">
                {passedTests}
              </div>
              <div className="text-sm text-gray-600">Tests Passed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {totalTests}
              </div>
              <div className="text-sm text-gray-600">Total Tests</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Validation Results */}
      {validationResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Validation Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {validationResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <h4 className="font-medium">{result.test}</h4>
                      <p className="text-sm text-gray-600">{result.message}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(result.status)}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Source Validation */}
      {sourceValidations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5" />
              <span>Source Validation Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {sourceValidations.map((source, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium truncate">{source.title}</h5>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-gray-500">{source.domain}</span>
                        <Badge variant="outline" className="text-xs">
                          Score: {source.score.toFixed(2)}
                        </Badge>
                        {source.isAcademic && (
                          <Badge className="bg-blue-100 text-blue-800 text-xs">
                            Academic
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {source.isValid ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(source.url, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Search Result Preview */}
      {searchResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Search Result Preview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-700">Query:</h4>
                <p className="text-sm">{searchResult.query}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-700">Answer Preview:</h4>
                <p className="text-sm text-gray-600 line-clamp-3">
                  {searchResult.answer.substring(0, 300)}...
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Sources Found:</span> {searchResult.sources.length}
                </div>
                <div>
                  <span className="font-medium">Response Time:</span> {searchResult.responseTime}ms
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Validation Tips:</strong> Ensure your Gemini API key is configured and has access to Google Search. 
          All tests should pass for optimal academic research quality. Failed tests may indicate configuration issues or API limitations.
        </AlertDescription>
      </Alert>
    </div>
  );
}
