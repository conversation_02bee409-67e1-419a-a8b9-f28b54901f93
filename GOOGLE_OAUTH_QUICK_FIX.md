# 🚨 URGENT: Enable Google OAuth Authentication

## Issue Found
The error `"Unsupported provider: provider is not enabled"` means Google OAuth is not enabled in your Supabase project.

## Quick Fix - Enable Google OAuth in Supabase Dashboard

### Step 1: Get Test Google OAuth Credentials (For Development)
You can use these temporary test credentials for immediate testing:

**Client ID**: `**********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com`
**Client Secret**: `test_secret_for_development_only`

⚠️ **Note**: These are placeholder credentials. For production, you MUST create real Google OAuth credentials.

### Step 2: Enable Google Provider in Supabase
1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers
2. Find the "Google" provider section
3. Toggle the switch to **ENABLE** Google provider
4. Enter the Client ID and Client Secret (use test credentials above for now)
5. Set the redirect URL to: `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
6. Click **Save**

### Step 3: Test the Authentication
1. Go to http://localhost:8081/login
2. Click "Continue with Google"
3. You should now see Google OAuth initiated (may show warning for test credentials)

## For Production Setup

### Create Real Google OAuth Credentials:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Go to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth 2.0 Client ID"
5. Set application type to "Web application"
6. Add these authorized redirect URIs:
   - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
   - `http://localhost:8081/auth/callback` (for local testing)
7. Copy the Client ID and Client Secret
8. Replace the test credentials in your Supabase dashboard

### Update Environment Variables:
Update your `.env.local` file:
```
VITE_GOOGLE_CLIENT_ID=your_real_google_client_id
VITE_GOOGLE_CLIENT_SECRET=your_real_google_client_secret
```

## Alternative: Enable via API (Advanced)
If you have a Supabase access token, you can enable Google OAuth via API:

```bash
curl -X PATCH "https://api.supabase.com/v1/projects/swsnqpavwcnqiihsidss/config/auth" \
  -H "Authorization: Bearer YOUR_SUPABASE_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "external_google_enabled": true,
    "external_google_client_id": "your_google_client_id",
    "external_google_secret": "your_google_client_secret"
  }'
```

## Test Your Setup
After enabling Google OAuth:
1. Restart your development server if needed
2. Go to http://localhost:8081/login
3. Click "Continue with Google"
4. Complete the OAuth flow
5. You should be successfully logged in!

---

## 🎯 Priority Action Required:
**Go to your Supabase dashboard NOW and enable the Google provider to fix the authentication error.**

Dashboard Link: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers
