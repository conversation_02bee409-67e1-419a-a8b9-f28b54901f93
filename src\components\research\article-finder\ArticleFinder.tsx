/**
 * Article Finder - AI-Powered Journal Discovery Platform
 * Main component for the article finder module
 */

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  ArticleFinderProps,
  ArticleContent,
  ArticleAnalysis,
  JournalRecommendation
} from './types';
import { useArticleFinderStore } from './stores/article-finder.store';
import { articleFinderAIService } from './services/article-finder-ai.service';
import { journalSearchService } from './services/journal-search.service';
import { openRouterArticleService } from './services/openrouter-article.service';
import { useAuth } from '@/contexts/AuthContext';

// Component imports (to be created)
import { ArticleInputForm } from './components/ArticleInputForm';
import { ArticleAnalysisDisplay } from './components/ArticleAnalysisDisplay';
import { JournalRankingSystem } from './components/JournalRankingSystem';
import { ExportDialog } from './components/ExportDialog';
import { HistoryPanel } from './components/HistoryPanel';

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowLeft, 
  ArrowRight, 
  Search, 
  Download, 
  History, 
  RefreshCw,
  Sparkles,
  Target,
  TrendingUp,
  BookOpen,
  Settings
} from "lucide-react";

export const ArticleFinder: React.FC<ArticleFinderProps> = ({ className = "" }) => {
  const { user } = useAuth();
  
  // Store state
  const {
    currentStep,
    articleContent,
    selectedInputType,
    selectedModel,
    analysisDepth,
    articleAnalysis,
    isAnalyzing,
    error,
    searchProgress,
    rankingCriteria,
    
    // Actions
    setArticleContent,
    setSelectedInputType,
    setSelectedModel,
    setAnalysisDepth,
    setCurrentStep,
    nextStep,
    previousStep,
    setArticleAnalysis,
    setIsAnalyzing,
    setSearchProgress,
    setError,
    clearError,
    resetAnalysis
  } = useArticleFinderStore();

  // Local state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showHistoryPanel, setShowHistoryPanel] = useState(false);
  const [additionalContext, setAdditionalContext] = useState('');

  // Check service configuration on mount
  useEffect(() => {
    const checkServices = async () => {
      try {
        // Test Gemini connection
        const geminiTest = await articleFinderAIService.testConnection();
        if (!geminiTest.success) {
          console.warn('Gemini service not available:', geminiTest.error);
        }

        // Test Tavily connection
        const tavilyConfigured = journalSearchService.isConfigured();
        if (!tavilyConfigured) {
          console.warn('Tavily search service not configured');
        }

        // Test OpenRouter connection
        const openRouterConfigured = openRouterArticleService.isServiceConfigured();
        if (!openRouterConfigured) {
          console.warn('OpenRouter service not configured');
        }

      } catch (error) {
        console.error('Service configuration check failed:', error);
      }
    };

    checkServices();
  }, []);

  /**
   * Handle article content submission
   */
  const handleArticleSubmit = async (content: ArticleContent) => {
    try {
      setArticleContent(content);
      setIsAnalyzing(true);
      setSearchProgress(0);
      clearError();

      // Start analysis
      await performArticleAnalysis(content);

    } catch (error) {
      console.error('Article submission failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to submit article');
      setIsAnalyzing(false);
    }
  };

  /**
   * Perform comprehensive article analysis
   */
  const performArticleAnalysis = async (content: ArticleContent) => {
    try {
      setSearchProgress(10);

      // Step 1: AI Analysis
      const analysisRequest = {
        content: content.content,
        inputType: selectedInputType,
        analysisDepth,
        model: selectedModel,
        additionalContext
      };

      setSearchProgress(25);

      let aiResponse;
      if (selectedModel.includes('gemini')) {
        aiResponse = await articleFinderAIService.analyzeArticle(analysisRequest);
      } else {
        aiResponse = await openRouterArticleService.analyzeArticle(analysisRequest);
      }

      setSearchProgress(50);

      // Step 2: Enhanced Journal Search
      const enhancedJournals = await enhanceJournalRecommendations(
        aiResponse.analysis,
        content
      );

      setSearchProgress(75);

      // Step 3: Combine and finalize results
      const finalAnalysis: ArticleAnalysis = {
        ...aiResponse.analysis,
        recommendedJournals: enhancedJournals
      };

      setSearchProgress(100);
      setArticleAnalysis(finalAnalysis);
      setCurrentStep('results');

      toast.success(`Found ${enhancedJournals.length} journal recommendations!`);

    } catch (error) {
      console.error('Article analysis failed:', error);
      setError(error instanceof Error ? error.message : 'Analysis failed');
      setIsAnalyzing(false);
    }
  };

  /**
   * Enhance journal recommendations with search data
   */
  const enhanceJournalRecommendations = async (
    analysis: ArticleAnalysis,
    content: ArticleContent
  ): Promise<JournalRecommendation[]> => {
    try {
      // Use Tavily to find additional journals
      const searchJournals = await journalSearchService.searchJournals(
        content,
        analysis.researchDomain,
        analysis.keyTopics
      );

      // Combine AI recommendations with search results
      const allJournals = [...analysis.recommendedJournals, ...searchJournals];

      // Deduplicate by name
      const uniqueJournals = allJournals.filter((journal, index, array) =>
        array.findIndex(j => j.name.toLowerCase() === journal.name.toLowerCase()) === index
      );

      // Sort by match score
      return uniqueJournals
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, 12); // Top 12 recommendations

    } catch (error) {
      console.warn('Journal search enhancement failed:', error);
      return analysis.recommendedJournals;
    }
  };

  /**
   * Handle export functionality
   */
  const handleExport = () => {
    if (!articleAnalysis) {
      toast.error('No analysis data to export');
      return;
    }
    setShowExportDialog(true);
  };

  /**
   * Handle starting new analysis
   */
  const handleNewAnalysis = () => {
    resetAnalysis();
    setAdditionalContext('');
  };

  /**
   * Render step indicator
   */
  const renderStepIndicator = () => {
    const steps = [
      { key: 'input', label: 'Input', icon: BookOpen },
      { key: 'analyzing', label: 'Analyzing', icon: Search },
      { key: 'results', label: 'Results', icon: Target },
      { key: 'export', label: 'Export', icon: Download }
    ];

    return (
      <div className="flex items-center justify-center space-x-4 mb-8">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = currentStep === step.key;
          const isCompleted = steps.findIndex(s => s.key === currentStep) > index;
          
          return (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                ${isActive 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : isCompleted 
                    ? 'bg-green-600 border-green-600 text-white'
                    : 'bg-gray-100 border-gray-300 text-gray-500'
                }
              `}>
                <Icon className="w-5 h-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-8 h-0.5 mx-4 ${
                  isCompleted ? 'bg-green-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  /**
   * Render current step content
   */
  const renderStepContent = () => {
    switch (currentStep) {
      case 'input':
        return (
          <ArticleInputForm
            onSubmit={handleArticleSubmit}
            isLoading={isAnalyzing}
            selectedInputType={selectedInputType}
            onInputTypeChange={setSelectedInputType}
          />
        );

      case 'analyzing':
        return (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <Sparkles className="w-16 h-16 text-blue-600 mx-auto mb-6 animate-pulse" />
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Analyzing Your Research
              </h3>
              <p className="text-gray-600 mb-6">
                Our AI is analyzing your content and searching for the best journal matches...
              </p>
              <Progress value={searchProgress} className="mb-4" />
              <p className="text-sm text-gray-500">
                {searchProgress < 25 && "Analyzing content..."}
                {searchProgress >= 25 && searchProgress < 50 && "Generating recommendations..."}
                {searchProgress >= 50 && searchProgress < 75 && "Searching journal databases..."}
                {searchProgress >= 75 && "Finalizing results..."}
              </p>
            </div>
          </div>
        );

      case 'results':
        return (
          <div className="space-y-6">
            {articleAnalysis && (
              <>
                <ArticleAnalysisDisplay
                  analysis={articleAnalysis}
                  isLoading={false}
                />
                <JournalRankingSystem
                  journals={articleAnalysis.recommendedJournals}
                  criteria={rankingCriteria}
                  onCriteriaChange={() => {}} // Will be implemented in component
                  onRankingUpdate={() => {}} // Will be implemented in component
                />
              </>
            )}
          </div>
        );

      case 'export':
        return (
          <div className="text-center py-12">
            <Download className="w-16 h-16 text-green-600 mx-auto mb-6" />
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Export Your Results
            </h3>
            <p className="text-gray-600 mb-6">
              Download your journal recommendations and analysis in your preferred format.
            </p>
            <Button onClick={handleExport} size="lg">
              <Download className="w-5 h-5 mr-2" />
              Export Results
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Target className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Article Finder</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistoryPanel(true)}
              >
                <History className="w-4 h-4 mr-2" />
                History
              </Button>
              
              {articleAnalysis && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNewAnalysis}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  New Analysis
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Error Display */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        <Card className="shadow-lg">
          <CardContent className="p-8">
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        {currentStep !== 'input' && currentStep !== 'analyzing' && (
          <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={previousStep}
              disabled={currentStep === 'input'}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            
            <Button
              onClick={nextStep}
              disabled={currentStep === 'export'}
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Dialogs */}
      {showExportDialog && articleAnalysis && (
        <ExportDialog
          isOpen={showExportDialog}
          onClose={() => setShowExportDialog(false)}
          data={{
            articleMetadata: {
              title: articleContent?.content.substring(0, 100) || 'Untitled',
              abstract: articleContent?.type === 'abstract' ? articleContent.content : undefined,
              fullContent: articleContent?.type === 'full' ? articleContent.content : undefined,
              keywords: articleAnalysis.keyTopics,
              researchField: articleAnalysis.researchDomain,
              methodology: articleAnalysis.methodology,
              novelty: articleAnalysis.noveltyScore.toString(),
              contributionLevel: articleAnalysis.contributionLevel as any
            },
            analysis: articleAnalysis,
            recommendations: articleAnalysis.recommendedJournals,
            rankings: [], // Will be calculated
            exportedAt: new Date()
          }}
          onExport={() => {}} // Will be implemented
        />
      )}

      {showHistoryPanel && (
        <HistoryPanel
          isOpen={showHistoryPanel}
          onClose={() => setShowHistoryPanel(false)}
          onLoadSubmission={() => {}} // Will be implemented
        />
      )}
    </div>
  );
};
