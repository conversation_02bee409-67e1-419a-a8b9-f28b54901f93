-- AI Tutor Database Schema
-- This schema supports the AI Tutor component with sessions, progress tracking, visualizations, and gamification

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table for storing tutor sessions
CREATE TABLE IF NOT EXISTS public.tutor_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    topic TEXT NOT NULL,
    education_level TEXT NOT NULL CHECK (education_level IN ('elementary', 'middle-school', 'high-school', 'college')),
    messages JSONB DEFAULT '[]', -- Array of TutorMessage objects
    sources JSONB DEFAULT '[]', -- Array of TutorSource objects
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}', -- Store session metadata like duration, concepts, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing individual tutor messages (for better querying and analytics)
CREATE TABLE IF NOT EXISTS public.tutor_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    sources TEXT[] DEFAULT '{}', -- Array of source IDs referenced in this message
    metadata JSONB DEFAULT '{}', -- Store tokens, model, confidence, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking student progress and learning analytics
CREATE TABLE IF NOT EXISTS public.tutor_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE NOT NULL,
    concepts_learned TEXT[] DEFAULT '{}',
    questions_asked INTEGER DEFAULT 0,
    time_spent INTEGER DEFAULT 0, -- in minutes
    comprehension_level INTEGER DEFAULT 0 CHECK (comprehension_level >= 0 AND comprehension_level <= 100),
    engagement_score INTEGER DEFAULT 0 CHECK (engagement_score >= 0 AND engagement_score <= 100),
    learning_progress INTEGER DEFAULT 0 CHECK (learning_progress >= 0 AND learning_progress <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing educational visualizations
CREATE TABLE IF NOT EXISTS public.tutor_visualizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    subject TEXT NOT NULL,
    topic TEXT NOT NULL,
    education_level TEXT NOT NULL,
    visualization_type TEXT NOT NULL, -- 'react', 'html', 'd3', 'plotly', etc.
    code TEXT NOT NULL, -- The generated visualization code
    dependencies TEXT[] DEFAULT '{}', -- Required dependencies
    parameters JSONB DEFAULT '{}', -- Visualization parameters
    learning_objectives TEXT[] DEFAULT '{}',
    key_points TEXT[] DEFAULT '{}',
    interactive_elements TEXT[] DEFAULT '{}',
    estimated_duration INTEGER DEFAULT 5, -- in minutes
    prerequisites TEXT[] DEFAULT '{}',
    related_concepts TEXT[] DEFAULT '{}',
    complexity TEXT DEFAULT 'beginner' CHECK (complexity IN ('beginner', 'intermediate', 'advanced')),
    status TEXT DEFAULT 'completed' CHECK (status IN ('generating', 'completed', 'error')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking visualization usage and analytics
CREATE TABLE IF NOT EXISTS public.tutor_visualization_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    visualization_id UUID REFERENCES public.tutor_visualizations(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    total_time_spent INTEGER DEFAULT 0, -- in seconds
    interaction_count INTEGER DEFAULT 0,
    pause_count INTEGER DEFAULT 0,
    replay_count INTEGER DEFAULT 0,
    speed_changes INTEGER DEFAULT 0,
    parameters_changed TEXT[] DEFAULT '{}',
    completion_rate DECIMAL(3,2) DEFAULT 0.0 CHECK (completion_rate >= 0.0 AND completion_rate <= 1.0),
    engagement_score INTEGER DEFAULT 0 CHECK (engagement_score >= 1 AND engagement_score <= 10),
    learning_effectiveness INTEGER DEFAULT 0 CHECK (learning_effectiveness >= 1 AND learning_effectiveness <= 10),
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for gamification - student achievements
CREATE TABLE IF NOT EXISTS public.tutor_achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    achievement_type TEXT NOT NULL, -- 'learning', 'engagement', 'streak', 'mastery', 'social', 'special'
    achievement_id TEXT NOT NULL, -- Unique identifier for the achievement
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    xp_reward INTEGER DEFAULT 0,
    progress INTEGER DEFAULT 0,
    max_progress INTEGER DEFAULT 1,
    unlocked_at TIMESTAMP WITH TIME ZONE,
    requirements TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);

-- Table for tracking student XP and levels
CREATE TABLE IF NOT EXISTS public.tutor_student_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    current_level INTEGER DEFAULT 1,
    total_xp INTEGER DEFAULT 0,
    streak_days INTEGER DEFAULT 0,
    completed_topics INTEGER DEFAULT 0,
    total_topics INTEGER DEFAULT 0,
    last_activity_date DATE DEFAULT CURRENT_DATE,
    learning_preferences JSONB DEFAULT '{}',
    statistics JSONB DEFAULT '{}', -- Store various learning statistics
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking learning activities
CREATE TABLE IF NOT EXISTS public.tutor_activities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL CHECK (activity_type IN ('topic_completed', 'streak_milestone', 'level_up', 'achievement_unlocked', 'visualization_viewed', 'question_asked')),
    title TEXT NOT NULL,
    description TEXT,
    xp_gained INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing educational context and mode switching data
CREATE TABLE IF NOT EXISTS public.tutor_context_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.tutor_sessions(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    detected_context JSONB NOT NULL, -- EducationalContext object
    intent_classification JSONB NOT NULL, -- IntentClassification object
    boundary_check JSONB NOT NULL, -- BoundaryCheckResult object
    suggested_mode TEXT,
    actual_mode TEXT NOT NULL,
    mode_switched BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tutor_sessions_user_id ON public.tutor_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_tutor_sessions_active ON public.tutor_sessions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_tutor_messages_session_id ON public.tutor_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_tutor_progress_user_id ON public.tutor_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_tutor_visualizations_user_id ON public.tutor_visualizations(user_id);
CREATE INDEX IF NOT EXISTS idx_tutor_visualizations_subject ON public.tutor_visualizations(subject, education_level);
CREATE INDEX IF NOT EXISTS idx_tutor_achievements_user_id ON public.tutor_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_tutor_activities_user_id ON public.tutor_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_tutor_context_history_user_id ON public.tutor_context_history(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.tutor_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_visualizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_visualization_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_student_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_context_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own data
CREATE POLICY "Users can view their own tutor sessions" ON public.tutor_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tutor sessions" ON public.tutor_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tutor sessions" ON public.tutor_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tutor sessions" ON public.tutor_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Similar policies for other tables
CREATE POLICY "Users can manage their own tutor messages" ON public.tutor_messages
    FOR ALL USING (EXISTS (
        SELECT 1 FROM public.tutor_sessions 
        WHERE tutor_sessions.id = tutor_messages.session_id 
        AND tutor_sessions.user_id = auth.uid()
    ));

CREATE POLICY "Users can manage their own progress" ON public.tutor_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own visualizations" ON public.tutor_visualizations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own visualization analytics" ON public.tutor_visualization_analytics
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own achievements" ON public.tutor_achievements
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own student progress" ON public.tutor_student_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own activities" ON public.tutor_activities
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own context history" ON public.tutor_context_history
    FOR ALL USING (auth.uid() = user_id);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tutor_sessions_updated_at BEFORE UPDATE ON public.tutor_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_progress_updated_at BEFORE UPDATE ON public.tutor_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_visualizations_updated_at BEFORE UPDATE ON public.tutor_visualizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_student_progress_updated_at BEFORE UPDATE ON public.tutor_student_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
