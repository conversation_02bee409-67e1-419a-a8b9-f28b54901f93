/**
 * Text Editor Component
 * Monaco-based editor for flowchart text input
 */

import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { TextEditorProps } from '../types';
import { useFlowchartFunStore } from '../stores/flowchart-fun.store';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Type, 
  Download, 
  Upload, 
  RotateCcw, 
  RotateCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FlowchartTextEditorProps extends Omit<TextEditorProps, 'value' | 'onChange'> {
  showToolbar?: boolean;
  showStatus?: boolean;
}

export function FlowchartTextEditor({ 
  className = '',
  readOnly = false,
  showToolbar = true,
  showStatus = true,
  onCursorPositionChange
}: FlowchartTextEditorProps) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  
  const {
    text,
    parseErrors,
    isValidSyntax,
    isLoading,
    theme,
    canUndo,
    canRedo,
    cursorPosition,
    setText,
    undo,
    redo,
    setCursorPosition
  } = useFlowchartFunStore();

  // Handle editor mount
  const handleEditorDidMount = (editorInstance: editor.IStandaloneCodeEditor) => {
    editorRef.current = editorInstance;
    
    // Set up cursor position tracking
    editorInstance.onDidChangeCursorPosition((e) => {
      const { lineNumber, column } = e.position;
      setCursorPosition(lineNumber, column);
      onCursorPositionChange?.(lineNumber, column);
    });

    // Set up error markers
    updateErrorMarkers();
  };

  // Update error markers when parse errors change
  useEffect(() => {
    updateErrorMarkers();
  }, [parseErrors]);

  const updateErrorMarkers = () => {
    if (!editorRef.current) return;

    const model = editorRef.current.getModel();
    if (!model) return;

    const markers = parseErrors.map(error => ({
      startLineNumber: error.line,
      startColumn: error.column || 1,
      endLineNumber: error.line,
      endColumn: error.column ? error.column + 10 : 100,
      message: error.message,
      severity: error.severity === 'error' ? 8 : error.severity === 'warning' ? 4 : 1
    }));

    // Set markers
    const monaco = (window as any).monaco;
    if (monaco) {
      monaco.editor.setModelMarkers(model, 'flowchart-fun', markers);
    }
  };

  // Handle text change
  const handleTextChange = (value: string | undefined) => {
    if (value !== undefined && value !== text) {
      setText(value);
    }
  };

  // Format text
  const handleFormatText = () => {
    if (!editorRef.current) return;
    
    const model = editorRef.current.getModel();
    if (!model) return;

    // Simple formatting: ensure proper indentation
    const lines = text.split('\n');
    const formatted = lines.map(line => {
      const trimmed = line.trim();
      if (!trimmed) return '';
      
      // Count leading spaces in original
      const leadingSpaces = line.length - line.trimStart().length;
      const indentLevel = Math.floor(leadingSpaces / 2);
      
      return '  '.repeat(indentLevel) + trimmed;
    });

    setText(formatted.join('\n'));
  };

  // Import text from file
  const handleImportText = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.md';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setText(content);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // Export text to file
  const handleExportText = () => {
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'flowchart.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Toolbar */}
      {showToolbar && (
        <div className="flex items-center justify-between p-2 border-b bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canUndo || readOnly}
              title="Undo"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canRedo || readOnly}
              title="Redo"
            >
              <RotateCw className="h-4 w-4" />
            </Button>
            <div className="w-px h-4 bg-gray-300 dark:bg-gray-600" />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFormatText}
              disabled={readOnly}
              title="Format Text"
            >
              <Type className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleImportText}
              disabled={readOnly}
              title="Import Text File"
            >
              <Upload className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportText}
              title="Export Text File"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="flex-1 relative">
        <Editor
          value={text}
          onChange={handleTextChange}
          onMount={handleEditorDidMount}
          language="plaintext"
          theme={theme === 'dark' ? 'vs-dark' : 'vs'}
          options={{
            readOnly,
            minimap: { enabled: false },
            lineNumbers: 'on',
            wordWrap: 'on',
            automaticLayout: true,
            scrollBeyondLastLine: false,
            fontSize: 14,
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
            tabSize: 2,
            insertSpaces: true,
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: false,
            renderLineHighlight: 'line',
            selectionHighlight: false,
            occurrencesHighlight: false,
            overviewRulerBorder: false,
            hideCursorInOverviewRuler: true,
            scrollbar: {
              vertical: 'auto',
              horizontal: 'auto',
              verticalScrollbarSize: 12,
              horizontalScrollbarSize: 12
            }
          }}
          loading={
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }
        />
        
        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">Parsing...</span>
            </div>
          </div>
        )}
      </div>

      {/* Status bar */}
      {showStatus && (
        <div className="flex items-center justify-between p-2 border-t bg-gray-50 dark:bg-gray-800 text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-600 dark:text-gray-400">
              Line {cursorPosition.line}, Column {cursorPosition.column}
            </span>
            <div className="flex items-center space-x-1">
              {isValidSyntax ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-600 dark:text-green-400">Valid syntax</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-red-600 dark:text-red-400">
                    {parseErrors.length} error{parseErrors.length !== 1 ? 's' : ''}
                  </span>
                </>
              )}
            </div>
          </div>
          
          <div className="text-gray-600 dark:text-gray-400">
            {text.split('\n').length} lines, {text.length} characters
          </div>
        </div>
      )}

      {/* Error messages */}
      {parseErrors.length > 0 && (
        <div className="p-2 border-t max-h-32 overflow-y-auto">
          {parseErrors.slice(0, 3).map((error, index) => (
            <Alert key={index} variant="destructive" className="mb-2 last:mb-0">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Line {error.line}: {error.message}
              </AlertDescription>
            </Alert>
          ))}
          {parseErrors.length > 3 && (
            <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
              ... and {parseErrors.length - 3} more error{parseErrors.length - 3 !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
