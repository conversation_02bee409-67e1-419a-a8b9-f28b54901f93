/**
 * Types for Google Search functionality
 */

export interface GoogleSearchMessage {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'google_search_result';
  content: string;
  timestamp: Date;
  searchQuery?: string;
  sources?: GoogleSearchSource[];
  citations?: GoogleCitation[];
  references?: GoogleReference[];
  isGoogleSearch?: boolean;
}

export interface GoogleSearchSource {
  id: string;
  title: string;
  url: string;
  snippet: string;
  domain: string;
  publishedDate?: string;
  score: number;
  type: 'academic' | 'web' | 'news' | 'book' | 'journal';
  authors?: string[];
  journal?: string;
  year?: number;
}

export interface GoogleCitation {
  id: string;
  text: string;
  sourceId: string;
  url: string;
  title: string;
  position: number;
  inTextFormat: string; // APA format: (Author, Year)
}

export interface GoogleReference {
  id: string;
  title: string;
  authors: string[];
  year: number;
  url: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  apaFormat: string; // Full APA reference
}

export interface GoogleSearchSession {
  id: string;
  title: string;
  messages: GoogleSearchMessage[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  totalQueries: number;
  isActive: boolean;
}

export interface GoogleSearchResult {
  query: string;
  answer: string;
  sources: GoogleSearchSource[];
  followUpQuestions?: string[];
  responseTime: number;
  totalResults: number;
}

export interface GoogleSearchOptions {
  maxResults: number;
  searchDepth: 'basic' | 'advanced' | 'comprehensive';
  includeAcademicSources: boolean;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicFocus: boolean;
  includeRecentOnly?: boolean;
  yearRange?: {
    start: number;
    end: number;
  };
}

export interface GoogleSearchProgress {
  stage: 'searching' | 'analyzing' | 'formatting' | 'generating_citations' | 'complete';
  message: string;
  progress: number;
}

export interface GoogleSearchError {
  type: 'api_error' | 'network_error' | 'validation_error' | 'rate_limit';
  message: string;
  details?: any;
}

export interface GoogleUserPreferences {
  defaultSearchDepth: 'basic' | 'advanced' | 'comprehensive';
  maxResults: number;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicFocus: boolean;
  includeRecentOnly: boolean;
}

// Deep Research Types
export interface DeepResearchSession {
  id: string;
  userId: string;
  query: string;
  outline: ResearchOutline | null;
  researchData: ResearchData[];
  sections: ResearchSection[];
  status: 'outline' | 'researching' | 'writing' | 'completed' | 'error';
  progress: DeepResearchProgress;
  createdAt: Date;
  updatedAt: Date;
  estimatedCost?: number;
  actualCost?: number;
}

export interface ResearchOutline {
  id: string;
  mainTopic: string;
  description: string;
  subtopics: SubTopic[];
  researchQuestions: string[];
  estimatedLength: number;
  targetAudience?: string;
  researchDepth: 'basic' | 'advanced' | 'comprehensive';
}

export interface SubTopic {
  id: string;
  title: string;
  description: string;
  keyQuestions: string[];
  estimatedWords: number;
  priority: 'high' | 'medium' | 'low';
  searchTerms: string[];
  order: number;
}

export interface ResearchData {
  id: string;
  subtopicId: string;
  query: string;
  sources: GoogleSearchSource[];
  summary: string;
  keyFindings: string[];
  searchTime: number;
  totalResults: number;
}

export interface ResearchSection {
  id: string;
  subtopicId: string;
  title: string;
  content: string;
  wordCount: number;
  citations: GoogleCitation[];
  references: GoogleReference[];
  status: 'pending' | 'generating' | 'completed' | 'error';
  generatedAt?: Date;
  order: number;
}

export interface DeepResearchProgress {
  phase: 'outline' | 'research' | 'writing' | 'assembly' | 'completed';
  currentStep: string;
  completedSteps: number;
  totalSteps: number;
  percentage: number;
  estimatedTimeRemaining?: number;
  currentSubtopic?: string;
  errors?: string[];
}

export interface DeepResearchOptions {
  maxSubtopics: number;
  wordsPerSection: number;
  researchDepth: 'basic' | 'advanced' | 'comprehensive';
  includeExecutiveSummary: boolean;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  targetAudience: 'academic' | 'professional' | 'general';
  allowOutlineEditing: boolean;
}
