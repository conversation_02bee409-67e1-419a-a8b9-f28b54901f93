/**
 * Service Status Component
 * Shows the health status of Tavily and OpenRouter APIs
 */

import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { tavilySearchService } from '../services/tavily-search.service';
import { researchAIService } from '../services/research-ai.service';
import { cn } from '@/lib/utils';

interface ServiceStatusProps {
  className?: string;
  compact?: boolean;
}

interface ServiceHealth {
  tavily: 'checking' | 'healthy' | 'error' | 'unknown';
  openRouter: 'checking' | 'healthy' | 'error' | 'unknown';
  database: 'checking' | 'healthy' | 'error' | 'unknown';
}

export function ServiceStatus({ className, compact = false }: ServiceStatusProps) {
  const [status, setStatus] = useState<ServiceHealth>({
    tavily: 'unknown',
    openRouter: 'unknown',
    database: 'unknown'
  });
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkServices = async () => {
    setStatus({
      tavily: 'checking',
      openRouter: 'checking',
      database: 'checking'
    });

    // Check Tavily API
    try {
      const tavilyHealthy = await tavilySearchService.testConnection();
      setStatus(prev => ({ ...prev, tavily: tavilyHealthy ? 'healthy' : 'error' }));
    } catch (error) {
      setStatus(prev => ({ ...prev, tavily: 'error' }));
    }

    // Check OpenRouter API
    try {
      const openRouterHealthy = researchAIService.isConfigured();
      setStatus(prev => ({ ...prev, openRouter: openRouterHealthy ? 'healthy' : 'error' }));
    } catch (error) {
      setStatus(prev => ({ ...prev, openRouter: 'error' }));
    }

    // Check Database (simplified check)
    try {
      // This is a basic check - in a real app you'd ping the database
      const dbHealthy = typeof window !== 'undefined';
      setStatus(prev => ({ ...prev, database: dbHealthy ? 'healthy' : 'error' }));
    } catch (error) {
      setStatus(prev => ({ ...prev, database: 'error' }));
    }

    setLastChecked(new Date());
  };

  useEffect(() => {
    checkServices();
  }, []);

  const getStatusIcon = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'checking':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'checking':
        return 'bg-blue-100 text-blue-800';
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'checking':
        return 'Checking...';
      case 'healthy':
        return 'Healthy';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  const allHealthy = Object.values(status).every(s => s === 'healthy');
  const anyError = Object.values(status).some(s => s === 'error');

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className={cn(
          "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
          allHealthy ? "bg-green-100 text-green-800" :
          anyError ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"
        )}>
          {allHealthy ? (
            <CheckCircle className="w-3 h-3" />
          ) : anyError ? (
            <XCircle className="w-3 h-3" />
          ) : (
            <AlertCircle className="w-3 h-3" />
          )}
          <span>
            {allHealthy ? 'All Systems Operational' :
             anyError ? 'Service Issues' : 'Checking Services'}
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={checkServices}
          className="h-6 w-6 p-0"
        >
          <Loader2 className={cn("w-3 h-3", status.tavily === 'checking' && "animate-spin")} />
        </Button>
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Service Status</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={checkServices}
            className="h-7 px-2 text-xs"
          >
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.tavily)}
              <span className="text-sm font-medium">Tavily Search</span>
            </div>
            <Badge variant="secondary" className={cn("text-xs", getStatusColor(status.tavily))}>
              {getStatusText(status.tavily)}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.openRouter)}
              <span className="text-sm font-medium">AI Models</span>
            </div>
            <Badge variant="secondary" className={cn("text-xs", getStatusColor(status.openRouter))}>
              {getStatusText(status.openRouter)}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(status.database)}
              <span className="text-sm font-medium">Database</span>
            </div>
            <Badge variant="secondary" className={cn("text-xs", getStatusColor(status.database))}>
              {getStatusText(status.database)}
            </Badge>
          </div>
        </div>
        
        {lastChecked && (
          <div className="text-xs text-gray-500 pt-2 border-t">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
