/**
 * Revision Results Component
 * Displays the results of the article revision process
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  FileText, 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Download,
  Edit,
  Copy,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';

import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';
import { ArticleSection } from '../types';

interface RevisionResultsProps {
  onExport: () => void;
}

export function RevisionResults({ onExport }: RevisionResultsProps) {
  const [selectedSection, setSelectedSection] = useState<ArticleSection>('introduction');
  const [showChangesOnly, setShowChangesOnly] = useState(true);

  const {
    originalArticle,
    revisedArticle,
    responseLetter,
    manualInterventions
  } = useRevisionWorkflowStore();

  if (!revisedArticle || !originalArticle) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>No Results Available</AlertTitle>
        <AlertDescription>
          Please complete the revision process first to view results.
        </AlertDescription>
      </Alert>
    );
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const getChangeTypeColor = (changeType: string): string => {
    switch (changeType) {
      case 'addition': return 'text-green-700 bg-green-100 border-green-200';
      case 'deletion': return 'text-red-700 bg-red-100 border-red-200';
      case 'modification': return 'text-blue-700 bg-blue-100 border-blue-200';
      default: return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'addition': return '+';
      case 'deletion': return '-';
      case 'modification': return '~';
      default: return '•';
    }
  };

  const highlightChanges = (text: string, changes: any[]): JSX.Element => {
    if (!changes || changes.length === 0) {
      return <span>{text}</span>;
    }

    let highlightedText = text;
    const highlights: Array<{start: number, end: number, type: string, reason: string}> = [];

    // Create highlights for each change
    changes.forEach(change => {
      if (change.originalText && text.includes(change.originalText)) {
        const start = text.indexOf(change.originalText);
        const end = start + change.originalText.length;
        highlights.push({
          start,
          end,
          type: change.changeType,
          reason: change.reason
        });
      }
    });

    // Sort highlights by start position
    highlights.sort((a, b) => a.start - b.start);

    // Build highlighted JSX
    const elements: JSX.Element[] = [];
    let lastEnd = 0;

    highlights.forEach((highlight, index) => {
      // Add text before highlight
      if (highlight.start > lastEnd) {
        elements.push(
          <span key={`text-${index}`}>
            {text.substring(lastEnd, highlight.start)}
          </span>
        );
      }

      // Add highlighted text
      elements.push(
        <span
          key={`highlight-${index}`}
          className={`px-1 py-0.5 rounded border ${getChangeTypeColor(highlight.type)}`}
          title={highlight.reason}
        >
          {text.substring(highlight.start, highlight.end)}
        </span>
      );

      lastEnd = highlight.end;
    });

    // Add remaining text
    if (lastEnd < text.length) {
      elements.push(
        <span key="text-end">
          {text.substring(lastEnd)}
        </span>
      );
    }

    return <>{elements}</>;
  };

  const sectionsWithChanges = Object.entries(revisedArticle.sectionRevisions)
    .filter(([_, revision]) => revision.changes.length > 0);

  return (
    <div className="space-y-6">
      {/* Results Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Revision Results
          </CardTitle>
          <CardDescription>
            Review the changes made to your article and the response letter
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {revisedArticle.metadata.totalChanges}
              </div>
              <div className="text-sm text-blue-700">Total Changes</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {revisedArticle.metadata.addressedComments}
              </div>
              <div className="text-sm text-green-700">Comments Addressed</div>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {manualInterventions.length}
              </div>
              <div className="text-sm text-orange-700">Manual Actions</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {sectionsWithChanges.length}
              </div>
              <div className="text-sm text-purple-700">Sections Modified</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Results Tabs */}
      <Tabs defaultValue="changes" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="changes">Article Changes</TabsTrigger>
          <TabsTrigger value="response">Response Letter</TabsTrigger>
          <TabsTrigger value="manual">Manual Actions</TabsTrigger>
          <TabsTrigger value="comparison">Side-by-Side</TabsTrigger>
        </TabsList>

        {/* Article Changes Tab */}
        <TabsContent value="changes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Article Changes
                </span>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowChangesOnly(!showChangesOnly)}
                  >
                    {showChangesOnly ? 'Show All' : 'Changes Only'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(
                      Object.values(revisedArticle.sectionRevisions)
                        .map(r => r.revisedContent)
                        .join('\n\n'),
                      'Revised article'
                    )}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy All
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Section Navigation */}
              <div className="flex flex-wrap gap-2 mb-4">
                {sectionsWithChanges.map(([section, revision]) => (
                  <Button
                    key={section}
                    variant={selectedSection === section ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedSection(section as ArticleSection)}
                  >
                    {section.charAt(0).toUpperCase() + section.slice(1)}
                    <Badge variant="secondary" className="ml-2">
                      {revision.changes.length}
                    </Badge>
                  </Button>
                ))}
              </div>

              {/* Selected Section Content */}
              {revisedArticle.sectionRevisions[selectedSection] && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold capitalize">
                      {selectedSection} Section
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(
                        revisedArticle.sectionRevisions[selectedSection].revisedContent,
                        `${selectedSection} section`
                      )}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Section
                    </Button>
                  </div>

                  {/* Changes Summary */}
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600 mb-2">
                      <strong>Summary:</strong> {revisedArticle.sectionRevisions[selectedSection].revisionSummary}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {revisedArticle.sectionRevisions[selectedSection].changes.map((change, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className={getChangeTypeColor(change.changeType)}
                        >
                          {getChangeTypeIcon(change.changeType)} {change.changeType}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Section Content with Highlighting */}
                  <div className="border rounded-lg p-4 bg-white">
                    <h4 className="text-md font-medium mb-3">Revised Content</h4>
                    <div className="prose prose-sm max-w-none">
                      <div className="whitespace-pre-wrap text-sm leading-relaxed">
                        {highlightChanges(
                          revisedArticle.sectionRevisions[selectedSection].revisedContent,
                          revisedArticle.sectionRevisions[selectedSection].changes
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Detailed Changes */}
                  <div className="space-y-3">
                    {revisedArticle.sectionRevisions[selectedSection].changes.map((change, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <Badge className={getChangeTypeColor(change.changeType)}>
                            {change.changeType}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            Confidence: {change.confidence}%
                          </span>
                        </div>
                        
                        {change.originalText && (
                          <div className="mb-2">
                            <div className="text-sm font-medium text-red-700 mb-1">Original:</div>
                            <div className="text-sm bg-red-50 p-2 rounded border-l-4 border-red-200">
                              {change.originalText}
                            </div>
                          </div>
                        )}
                        
                        <div className="mb-2">
                          <div className="text-sm font-medium text-green-700 mb-1">Revised:</div>
                          <div className="text-sm bg-green-50 p-2 rounded border-l-4 border-green-200">
                            {change.revisedText}
                          </div>
                        </div>
                        
                        <div className="text-xs text-gray-600">
                          <strong>Reason:</strong> {change.reason}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Full Revised Content */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">Complete Revised Section:</h4>
                    <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                      <pre className="whitespace-pre-wrap text-sm">
                        {revisedArticle.sectionRevisions[selectedSection].revisedContent}
                      </pre>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Response Letter Tab */}
        <TabsContent value="response" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Response Letter
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => responseLetter && copyToClipboard(
                    `${responseLetter.introduction}\n\n${responseLetter.summaryOfChanges.join('\n')}\n\n${responseLetter.conclusion}`,
                    'Response letter'
                  )}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Letter
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {responseLetter ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Title:</strong> {responseLetter.title}
                    </div>
                    <div>
                      <strong>Revision Date:</strong> {responseLetter.revisionDate.toLocaleDateString()}
                    </div>
                    <div>
                      <strong>Total Comments:</strong> {responseLetter.totalComments}
                    </div>
                    <div>
                      <strong>Addressed:</strong> {responseLetter.addressedComments}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Introduction</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        {responseLetter.introduction}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Summary of Changes</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <ul className="list-disc list-inside space-y-1">
                          {responseLetter.summaryOfChanges.map((change, index) => (
                            <li key={index}>{change}</li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Reviewer Responses</h4>
                      <div className="space-y-3">
                        {Object.entries(responseLetter.reviewerResponses).map(([reviewerNum, responses]) => (
                          <div key={reviewerNum} className="border rounded-lg p-3">
                            <h5 className="font-medium mb-2">Reviewer {reviewerNum}</h5>
                            <div className="space-y-2">
                              {responses.map((response, index) => (
                                <div key={index} className="text-sm">
                                  <div className="font-medium">Comment:</div>
                                  <div className="text-gray-600 mb-1">{response.originalComment}</div>
                                  <div className="font-medium">Response:</div>
                                  <div className="text-gray-800">{response.response}</div>
                                  <Badge variant={response.status === 'addressed' ? 'default' : 'secondary'} className="mt-1">
                                    {response.status}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Conclusion</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        {responseLetter.conclusion}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>No Response Letter</AlertTitle>
                  <AlertDescription>
                    Response letter generation failed or is not available.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manual Actions Tab */}
        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Manual Interventions Required
              </CardTitle>
              <CardDescription>
                These items require your manual attention and cannot be automatically fixed
              </CardDescription>
            </CardHeader>
            <CardContent>
              {manualInterventions.length > 0 ? (
                <div className="space-y-4">
                  {manualInterventions.map((intervention) => (
                    <div key={intervention.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={intervention.priority === 'critical' ? 'destructive' : 'secondary'}>
                          {intervention.priority} priority
                        </Badge>
                        <Badge variant="outline">
                          {intervention.type}
                        </Badge>
                      </div>
                      
                      <h4 className="font-medium mb-2">{intervention.description}</h4>
                      
                      <div className="space-y-2">
                        <div>
                          <div className="text-sm font-medium">Suggested Actions:</div>
                          <ul className="list-disc list-inside text-sm text-gray-600">
                            {intervention.suggestedActions.map((action, index) => (
                              <li key={index}>{action}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="text-xs text-gray-500">
                          Estimated effort: {intervention.estimatedEffort}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>No Manual Actions Required</AlertTitle>
                  <AlertDescription>
                    All reviewer comments have been automatically addressed!
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Side-by-Side Comparison Tab */}
        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Side-by-Side Comparison
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 h-96">
                <div>
                  <h4 className="font-medium mb-2">Original Article</h4>
                  <div className="bg-gray-50 p-3 rounded-lg h-full overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                      {originalArticle.fullText}
                    </pre>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Revised Article</h4>
                  <div className="bg-green-50 p-3 rounded-lg h-full overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                      {Object.values(revisedArticle.sectionRevisions)
                        .map(r => r.revisedContent)
                        .join('\n\n')}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Actions */}
      <div className="flex justify-center">
        <Button onClick={onExport} size="lg" className="px-8">
          <Download className="h-4 w-4 mr-2" />
          Export All Files
        </Button>
      </div>
    </div>
  );
}
