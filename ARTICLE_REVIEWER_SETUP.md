# Article Reviewer with History - Setup Guide

## 🚨 IMPORTANT: Cache Issue Fix

If you're seeing import errors like "does not provide an export named 'default'", this is a cache issue. **Follow these steps in order**:

### Step 1: Clear Build Cache
```bash
# Stop the development server (Ctrl+C)
# Remove build artifacts
rm -rf dist
rm -rf node_modules/.vite
npm cache clean --force
```

### Step 2: Clear Browser Cache
1. **Hard Refresh**: `Ctrl+Shift+R` (Windows/Linux) or `Cmd+Shift+R` (Mac)
2. **Clear Browser Cache**:
   - Chrome: `Ctrl+Shift+Delete` → Clear "Cached images and files"
   - Firefox: `Ctrl+Shift+Delete` → Clear "Cache"
3. **Or use Incognito/Private window** for testing

### Step 3: Restart Development Server
```bash
npm run dev
# or
yarn dev
```

### Step 4: Test Implementation
- Navigate to Article Reviewer
- Look for "🔧 Test DB" tab (development mode only)
- Run database tests to verify connection

**See `fix-cache-issues.md` for detailed troubleshooting steps.**

## ✅ Database Setup Complete

The Supabase database has been successfully configured with all required tables:
- ✅ `article_reviews` - Main review metadata and scores
- ✅ `article_section_reviews` - Individual section analyses
- ✅ `article_detailed_feedback` - Sentence-level feedback items
- ✅ `article_review_tags` - Review categorization
- ✅ `article_review_exports` - Export history
- ✅ All RLS policies and triggers are active

## Overview

The Article Reviewer has been enhanced with comprehensive history and file storage functionality. Users can now:

- ✅ Upload and review academic papers with AI analysis
- ✅ Automatically save all reviews to Supabase database
- ✅ View review history with search and filtering
- ✅ Access detailed review results anytime
- ✅ Store uploaded files securely in Supabase Storage
- ✅ Modern, responsive UI with beautiful design

## Database Setup

### 1. Apply Database Schema

Run the following SQL script in your Supabase SQL Editor to create the required tables:

```sql
-- Execute the contents of: database/article_reviews_schema.sql
```

This will create the following tables:
- `article_reviews` - Main review metadata and scores
- `article_section_reviews` - Individual section analyses
- `article_detailed_feedback` - Sentence-level feedback items
- `article_review_tags` - Review categorization
- `article_review_exports` - Export history

### 2. Set up Storage Bucket

The system will automatically create a storage bucket called `article-files` when first used. However, you can manually create it:

1. Go to Supabase Dashboard → Storage
2. Create a new bucket named `article-files`
3. Set it as **Private** (not public)
4. Configure allowed MIME types:
   - `application/pdf`
   - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
5. Set file size limit to 50MB

### 3. Row Level Security (RLS)

The schema automatically sets up RLS policies to ensure users can only access their own reviews and files.

## Features Implemented

### 🎯 Core Functionality

1. **Enhanced Article Reviewer**
   - Original review functionality preserved
   - Automatic saving after review completion
   - File upload to Supabase Storage
   - Processing time tracking

2. **Review History Dashboard**
   - Beautiful grid layout with review cards
   - Search by title or content
   - Sort by date, score, or last updated
   - Filter by review status
   - Statistics overview (total reviews, average score, recent activity)

3. **Detailed Review View**
   - Complete review display with all sections
   - Interactive tabs for each analyzed section
   - Detailed feedback with severity indicators
   - Score visualizations and metrics
   - Export and sharing options (UI ready)

4. **Modern UI/UX**
   - Gradient backgrounds and modern design
   - Responsive layout for all screen sizes
   - Loading states and error handling
   - Toast notifications for user feedback
   - Consistent design language

### 🔧 Technical Implementation

1. **Database Services**
   - `ArticleReviewStorageService` - Complete CRUD operations
   - `ArticleFileStorageService` - File upload/download management
   - Proper error handling and type safety

2. **Component Architecture**
   - `AIArticleReviewerWithHistory` - Main enhanced component
   - `ArticleReviewHistory` - History dashboard
   - `SavedReviewDetail` - Detailed review viewer
   - Modular, reusable components

3. **State Management**
   - View mode switching (new review / history / detail view)
   - Automatic saving workflow
   - File upload progress tracking

## Usage

### For Users

1. **Creating Reviews**
   - Upload PDF files (DOCX support planned)
   - Select AI model for analysis
   - Review is automatically saved upon completion
   - Access via "History" button anytime

2. **Managing History**
   - View all past reviews in chronological order
   - Search by title or content keywords
   - Sort by creation date, update date, or score
   - Click "View" to see detailed analysis

3. **Viewing Details**
   - Complete review breakdown by section
   - Interactive tabs for easy navigation
   - Detailed feedback with improvement suggestions
   - Score metrics and visualizations

### For Developers

1. **Database Operations**
```typescript
import { articleReviewStorageService } from '@/components/research/article-reviewer/services';

// Get user's reviews
const { data: reviews } = await articleReviewStorageService.getUserArticleReviews();

// Get specific review with details
const { review, sections, feedback } = await articleReviewStorageService.getArticleReviewById(reviewId);

// Search reviews
const { data: results } = await articleReviewStorageService.searchArticleReviews(query);
```

2. **File Storage**
```typescript
import { articleFileStorageService } from '@/components/research/article-reviewer/services';

// Upload file
const { filePath } = await articleFileStorageService.uploadArticleFile(file, userId, reviewId);

// Get download URL
const { url } = await articleFileStorageService.getFileDownloadUrl(filePath);
```

## Export Functionality ✅

The system now includes comprehensive export capabilities:

1. **Text Export** - Clean, formatted text files for easy sharing
2. **HTML Export** - Beautiful, styled HTML reports with proper formatting
3. **Copy to Clipboard** - Quick copying for pasting into other applications
4. **Export from History** - Direct export from the review history list
5. **Export from Detail View** - Multiple export options in the detailed review view

### Export Features:
- Professional formatting with proper styling
- Complete review data including all sections and feedback
- Severity-coded detailed feedback items
- Score visualizations and metrics
- Responsive HTML layout for viewing on any device

## Next Steps (Future Enhancements)

1. **Advanced Export Options** 🚧
   - PDF export with custom styling
   - DOCX export with Word formatting
   - Email sharing capabilities

2. **Advanced Features**
   - Review comparison tools
   - Collaborative review sharing
   - Review templates and customization
   - Bulk operations

3. **Analytics**
   - Progress tracking over time
   - Writing improvement metrics
   - Detailed analytics dashboard

## File Structure

```
src/components/research/article-reviewer/
├── AIArticleReviewer.tsx                    # Original component
├── AIArticleReviewerWithHistory.tsx         # Enhanced component with history
├── components/
│   ├── ArticleReviewHistory.tsx             # History dashboard
│   ├── SavedReviewDetail.tsx                # Detailed review viewer
│   └── ... (existing components)
├── services/
│   ├── article-review-storage.service.ts    # Database operations
│   ├── article-file-storage.service.ts      # File storage operations
│   └── ... (existing services)
└── types.ts                                 # Type definitions

database/
└── article_reviews_schema.sql               # Database schema
```

## Environment Variables

Ensure these are set in your `.env` file:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_OPENROUTER_API_KEY=your_openrouter_api_key
```

## Support

The implementation includes comprehensive error handling and user feedback. All operations are logged for debugging purposes.

For issues or questions, check the browser console for detailed error messages and ensure all environment variables are properly configured.
