/**
 * Research Comprehension Settings Component
 * Manages user preferences for the research comprehension platform
 */

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { 
  Settings, 
  Brain, 
  FileText, 
  Target,
  Search,
  BarChart3,
  Palette,
  ArrowLeft,
  Save,
  RotateCcw
} from "lucide-react";
import { ResearchComprehensionSettings } from '../types';
import { TUTOR_AI_MODELS, EDUCATION_LEVELS, DEFAULT_RESEARCH_COMPREHENSION_SETTINGS } from '../constants';

interface ResearchSettingsProps {
  settings: ResearchComprehensionSettings;
  onUpdateSettings: (settings: Partial<ResearchComprehensionSettings>) => void;
  onBack: () => void;
}

export function ResearchSettings({ settings, onUpdateSettings, onBack }: ResearchSettingsProps) {
  const [localSettings, setLocalSettings] = useState<ResearchComprehensionSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  const updateLocalSetting = (key: keyof ResearchComprehensionSettings, value: any) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    onUpdateSettings(localSettings);
    setHasChanges(false);
  };

  const handleReset = () => {
    setLocalSettings(DEFAULT_RESEARCH_COMPREHENSION_SETTINGS);
    setHasChanges(true);
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Button variant="ghost" onClick={onBack} className="p-2">
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Research Settings</h1>
            <p className="text-gray-600">Customize your research comprehension experience</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges}
            className="bg-blue-500 hover:bg-blue-600"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Model Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-500" />
              <span>AI Model</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="model">Preferred AI Model</Label>
              <Select
                value={localSettings.preferredModel}
                onValueChange={(value) => updateLocalSetting('preferredModel', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gemini-2.5-pro">Gemini 2.5 Pro</SelectItem>
                  <SelectItem value="gemini-2.5-flash">Gemini 2.5 Flash</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="education-level">Education Level</Label>
              <Select 
                value={localSettings.educationLevel} 
                onValueChange={(value) => updateLocalSetting('educationLevel', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EDUCATION_LEVELS.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name} ({level.ageRange})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Document Processing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-green-500" />
              <span>Document Processing</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-quizzes">Auto-generate Quizzes</Label>
                <p className="text-sm text-gray-500">Automatically create quizzes from documents</p>
              </div>
              <Switch
                id="auto-quizzes"
                checked={localSettings.autoGenerateQuizzes}
                onCheckedChange={(checked) => updateLocalSetting('autoGenerateQuizzes', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-games">Auto-generate Games</Label>
                <p className="text-sm text-gray-500">Create educational games from content</p>
              </div>
              <Switch
                id="auto-games"
                checked={localSettings.autoGenerateGames}
                onCheckedChange={(checked) => updateLocalSetting('autoGenerateGames', checked)}
              />
            </div>

            <div>
              <Label htmlFor="chunk-size">Chunk Size: {localSettings.chunkSize}</Label>
              <Slider
                id="chunk-size"
                min={500}
                max={2000}
                step={100}
                value={[localSettings.chunkSize]}
                onValueChange={([value]) => updateLocalSetting('chunkSize', value)}
                className="mt-2"
              />
            </div>
          </CardContent>
        </Card>

        {/* Learning Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-purple-500" />
              <span>Learning Preferences</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="quiz-difficulty">Quiz Difficulty</Label>
              <Select 
                value={localSettings.quizDifficulty} 
                onValueChange={(value) => updateLocalSetting('quizDifficulty', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="game-difficulty">Game Difficulty: {localSettings.gameDifficulty}</Label>
              <Slider
                id="game-difficulty"
                min={1}
                max={5}
                step={1}
                value={[localSettings.gameDifficulty]}
                onValueChange={([value]) => updateLocalSetting('gameDifficulty', value)}
                className="mt-2"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="show-hints">Show Hints</Label>
                <p className="text-sm text-gray-500">Display helpful hints during learning</p>
              </div>
              <Switch
                id="show-hints"
                checked={localSettings.showHints}
                onCheckedChange={(checked) => updateLocalSetting('showHints', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="allow-retries">Allow Retries</Label>
                <p className="text-sm text-gray-500">Allow multiple attempts on quizzes</p>
              </div>
              <Switch
                id="allow-retries"
                checked={localSettings.allowRetries}
                onCheckedChange={(checked) => updateLocalSetting('allowRetries', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Search & Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="w-5 h-5 text-orange-500" />
              <span>Search & Analytics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="max-results">Max Search Results: {localSettings.maxSearchResults}</Label>
              <Slider
                id="max-results"
                min={5}
                max={50}
                step={5}
                value={[localSettings.maxSearchResults]}
                onValueChange={([value]) => updateLocalSetting('maxSearchResults', value)}
                className="mt-2"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="detailed-analytics">Detailed Analytics</Label>
                <p className="text-sm text-gray-500">Track detailed learning analytics</p>
              </div>
              <Switch
                id="detailed-analytics"
                checked={localSettings.trackDetailedAnalytics}
                onCheckedChange={(checked) => updateLocalSetting('trackDetailedAnalytics', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notifications">Enable Notifications</Label>
                <p className="text-sm text-gray-500">Receive learning progress notifications</p>
              </div>
              <Switch
                id="notifications"
                checked={localSettings.enableNotifications}
                onCheckedChange={(checked) => updateLocalSetting('enableNotifications', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
