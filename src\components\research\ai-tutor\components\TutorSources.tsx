/**
 * Tutor Sources Component
 * Displays educational sources and references for the current topic
 */

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ExternalLink, 
  BookOpen, 
  Globe, 
  GraduationCap,
  Video,
  FileText,
  Star,
  Clock,
  Loader2
} from "lucide-react";
import { TutorSourcesProps, TutorSource } from '../types';

export function TutorSources({ sources, isLoading, onSourceClick }: TutorSourcesProps) {
  
  const getSourceIcon = (type: TutorSource['type']) => {
    switch (type) {
      case 'academic':
        return GraduationCap;
      case 'educational':
        return BookOpen;
      case 'video':
        return Video;
      case 'book':
        return FileText;
      default:
        return Globe;
    }
  };

  const getSourceColor = (type: TutorSource['type']) => {
    switch (type) {
      case 'academic':
        return 'text-blue-600 bg-blue-100';
      case 'educational':
        return 'text-green-600 bg-green-100';
      case 'video':
        return 'text-red-600 bg-red-100';
      case 'book':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDomain = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return 'Unknown';
    }
  };

  const getRelevanceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-gray-600';
  };

  return (
    <Card className="h-full shadow-lg border-0">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <BookOpen className="w-5 h-5 text-blue-500" />
          <span>Learning Sources</span>
          {sources.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {sources.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
              <span className="text-gray-600">Loading sources...</span>
            </div>
          </div>
        ) : sources.length === 0 ? (
          <div className="text-center py-8 px-4">
            <Globe className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">
              No sources available for this topic yet.
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-300px)]">
            <div className="space-y-3 p-4">
              {sources.map((source, index) => {
                const SourceIcon = getSourceIcon(source.type);
                const sourceColor = getSourceColor(source.type);
                
                return (
                  <div
                    key={source.id}
                    className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-200 cursor-pointer group"
                    onClick={() => onSourceClick(source)}
                  >
                    {/* Source Header */}
                    <div className="flex items-start space-x-3 mb-2">
                      <div className={`p-1.5 rounded-md ${sourceColor}`}>
                        <SourceIcon className="w-4 h-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 text-sm line-clamp-2 group-hover:text-blue-700 transition-colors">
                          {source.title}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-gray-500">
                            {formatDomain(source.url)}
                          </span>
                          <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-blue-500 transition-colors" />
                        </div>
                      </div>
                    </div>

                    {/* Source Snippet */}
                    <p className="text-xs text-gray-600 line-clamp-3 mb-3">
                      {source.snippet}
                    </p>

                    {/* Source Metadata */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs capitalize">
                          {source.type}
                        </Badge>
                        {source.educationLevel && (
                          <Badge variant="secondary" className="text-xs">
                            {source.educationLevel.replace('-', ' ')}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Star className={`w-3 h-3 ${getRelevanceColor(source.relevanceScore)}`} />
                        <span className={`text-xs ${getRelevanceColor(source.relevanceScore)}`}>
                          {Math.round(source.relevanceScore * 100)}%
                        </span>
                      </div>
                    </div>

                    {/* Published Date */}
                    {source.publishedDate && (
                      <div className="flex items-center space-x-1 mt-2 pt-2 border-t border-gray-100">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {new Date(source.publishedDate).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>

      {/* Sources Footer */}
      {sources.length > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500 text-center">
            Click any source to open in a new tab
          </div>
          
          {/* Source Type Legend */}
          <div className="flex flex-wrap gap-2 mt-2 justify-center">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Academic</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Educational</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Video</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-xs text-gray-600">Book</span>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}
