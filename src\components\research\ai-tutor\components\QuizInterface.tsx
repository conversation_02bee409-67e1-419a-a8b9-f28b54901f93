/**
 * Beautiful Quiz Interface Component
 * Interactive quiz taking experience with multiple question types
 */

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Clock,
  CheckCircle,
  XCircle,
  Lightbulb,
  Trophy,
  Target,
  BookOpen,
  Brain,
  Star,
  Timer,
  ArrowRight,
  ArrowLeft,
  RotateCcw,
  Download,
  FileText
} from "lucide-react";
import { Quiz, QuizQuestion } from '../types';
import { toast } from 'sonner';

interface QuizInterfaceProps {
  quiz: Quiz;
  onComplete: (results: QuizResults) => void;
  onExit: () => void;
}

interface QuizResults {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: Array<{
    questionId: string;
    answer: string;
    isCorrect: boolean;
    timeSpent: number;
  }>;
}

export function QuizInterface({ quiz, onComplete, onExit }: QuizInterfaceProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showExplanation, setShowExplanation] = useState(false);
  const [timeSpent, setTimeSpent] = useState(0);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [isCompleted, setIsCompleted] = useState(false);
  const [results, setResults] = useState<QuizResults | null>(null);

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quiz.questions.length) * 100;

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Reset question timer when question changes
  useEffect(() => {
    setQuestionStartTime(Date.now());
    setShowExplanation(false);
  }, [currentQuestionIndex]);

  const handleAnswer = (answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answer
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      completeQuiz();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const completeQuiz = () => {
    const questionResults = quiz.questions.map(question => {
      const userAnswer = answers[question.id] || '';
      const isCorrect = checkAnswer(question, userAnswer);
      
      return {
        questionId: question.id,
        answer: userAnswer,
        isCorrect,
        timeSpent: 60 // Placeholder - would track actual time per question
      };
    });

    const correctAnswers = questionResults.filter(r => r.isCorrect).length;
    const score = Math.round((correctAnswers / quiz.questions.length) * 100);

    const finalResults: QuizResults = {
      score,
      totalQuestions: quiz.questions.length,
      correctAnswers,
      timeSpent,
      answers: questionResults
    };

    setResults(finalResults);
    setIsCompleted(true);
    toast.success(`Quiz completed! Score: ${score}%`);
  };

  const checkAnswer = (question: QuizQuestion, userAnswer: string): boolean => {
    if (question.type === 'multiple-choice') {
      return userAnswer === question.correctAnswer;
    } else if (question.type === 'true-false') {
      return userAnswer.toLowerCase() === question.correctAnswer.toString().toLowerCase();
    } else {
      // For short-answer, do a simple comparison (in real app, would use AI to grade)
      return userAnswer.toLowerCase().includes(question.correctAnswer.toString().toLowerCase());
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const downloadQuizResults = () => {
    if (!results) return;

    const quizData = {
      quiz: {
        title: quiz.title,
        topic: quiz.title.replace('Quiz: ', ''),
        difficulty: quiz.difficulty,
        totalQuestions: quiz.questions.length,
        estimatedTime: quiz.estimatedTime
      },
      questions: quiz.questions.map((q, index) => ({
        number: index + 1,
        question: q.question,
        type: q.type,
        options: q.options || [],
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        userAnswer: answers[q.id] || 'Not answered',
        isCorrect: checkAnswer(q, answers[q.id] || '')
      })),
      results: {
        score: results.score,
        correctAnswers: results.correctAnswers,
        totalQuestions: results.totalQuestions,
        timeSpent: formatTime(results.timeSpent),
        completedAt: new Date().toLocaleString()
      }
    };

    // Create downloadable content
    let content = `# ${quiz.title}\n\n`;
    content += `**Difficulty:** ${quiz.difficulty}\n`;
    content += `**Total Questions:** ${quiz.questions.length}\n`;
    content += `**Score:** ${results.score}% (${results.correctAnswers}/${results.totalQuestions})\n`;
    content += `**Time Spent:** ${formatTime(results.timeSpent)}\n`;
    content += `**Completed:** ${new Date().toLocaleString()}\n\n`;
    content += `---\n\n`;

    // Add questions, answers, and explanations
    quiz.questions.forEach((q, index) => {
      const userAnswer = answers[q.id] || 'Not answered';
      const isCorrect = checkAnswer(q, userAnswer);

      content += `## Question ${index + 1}\n\n`;
      content += `**${q.question}**\n\n`;

      if (q.options && q.options.length > 0) {
        content += `**Options:**\n`;
        q.options.forEach(option => {
          content += `- ${option}\n`;
        });
        content += `\n`;
      }

      content += `**Your Answer:** ${userAnswer}\n`;
      content += `**Correct Answer:** ${q.correctAnswer}\n`;
      content += `**Result:** ${isCorrect ? '✅ Correct' : '❌ Incorrect'}\n\n`;
      content += `**Explanation:** ${q.explanation}\n\n`;
      content += `---\n\n`;
    });

    // Download the file
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${quiz.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_results.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Quiz results downloaded successfully!');
  };

  if (isCompleted && results) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="text-center">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <Trophy className="w-16 h-16 text-yellow-500" />
            </div>
            <CardTitle className="text-3xl font-bold">Quiz Completed!</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{results.score}%</div>
                <div className="text-sm text-gray-600">Final Score</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{results.correctAnswers}/{results.totalQuestions}</div>
                <div className="text-sm text-gray-600">Correct Answers</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{formatTime(results.timeSpent)}</div>
                <div className="text-sm text-gray-600">Time Spent</div>
              </div>
            </div>

            <div className="flex justify-center space-x-4">
              <Button onClick={downloadQuizResults} className="flex items-center space-x-2 bg-green-600 hover:bg-green-700">
                <Download className="w-4 h-4" />
                <span>Download Results</span>
              </Button>
              <Button onClick={() => onComplete(results)} className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4" />
                <span>View Results</span>
              </Button>
              <Button variant="outline" onClick={onExit} className="flex items-center space-x-2">
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Tutor</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{quiz.title}</h1>
            <p className="text-gray-600">{quiz.description}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Badge className={getDifficultyColor(quiz.difficulty)}>
              {quiz.difficulty}
            </Badge>
            <div className="flex items-center space-x-2 text-gray-600">
              <Timer className="w-4 h-4" />
              <span>{formatTime(timeSpent)}</span>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* Question Card */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              {currentQuestion.question}
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{currentQuestion.type}</Badge>
              <Badge variant="outline">{currentQuestion.points} pts</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Multiple Choice */}
          {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
            <RadioGroup
              value={answers[currentQuestion.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {currentQuestion.options.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`option-${index}`} />
                  <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}

          {/* True/False */}
          {currentQuestion.type === 'true-false' && (
            <RadioGroup
              value={answers[currentQuestion.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="true" />
                <Label htmlFor="true" className="cursor-pointer">True</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="false" />
                <Label htmlFor="false" className="cursor-pointer">False</Label>
              </div>
            </RadioGroup>
          )}

          {/* Short Answer */}
          {currentQuestion.type === 'short-answer' && (
            <Textarea
              value={answers[currentQuestion.id] || ''}
              onChange={(e) => handleAnswer(e.target.value)}
              placeholder="Type your answer here..."
              className="min-h-[100px]"
            />
          )}

          {/* Hints */}
          {currentQuestion.hint && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Lightbulb className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Hint</span>
              </div>
              <p className="text-sm text-blue-700">{currentQuestion.hint}</p>
            </div>
          )}

          {/* Explanation (shown after answering) */}
          {showExplanation && answers[currentQuestion.id] && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Brain className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-800">Explanation</span>
              </div>
              <p className="text-sm text-gray-700">{currentQuestion.explanation}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentQuestionIndex === 0}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Previous</span>
        </Button>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowExplanation(!showExplanation)}
            disabled={!answers[currentQuestion.id]}
            className="flex items-center space-x-2"
          >
            <Brain className="w-4 h-4" />
            <span>Explanation</span>
          </Button>

          <Button
            onClick={handleNext}
            disabled={!answers[currentQuestion.id]}
            className="flex items-center space-x-2"
          >
            <span>{currentQuestionIndex === quiz.questions.length - 1 ? 'Finish' : 'Next'}</span>
            <ArrowRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
