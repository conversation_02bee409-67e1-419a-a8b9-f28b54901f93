import {
  Presentation,
  FileText,
  Image,
  Quote,
  Target,
  Grid2X2,
  <PERSON>,
  Sparkles,
  CheckCircle
} from "lucide-react";
import { SlideTemplate, PresentationTheme, AIModelOption } from "./types";

// Available slide layouts with templates
export const SLIDE_TEMPLATES: SlideTemplate[] = [
  {
    id: 'title-slide',
    name: 'Title Slide',
    description: 'Opening slide with title and subtitle',
    layout: 'title-slide',
    icon: Type,
    defaultContent: [
      {
        type: 'h1',
        children: [{ text: 'Presentation Title' }]
      },
      {
        type: 'h2', 
        children: [{ text: 'Subtitle or Description' }]
      }
    ]
  },
  {
    id: 'content-slide',
    name: 'Content Slide',
    description: 'Standard content slide with heading and body',
    layout: 'content-slide',
    icon: FileText,
    defaultContent: [
      {
        type: 'h2',
        children: [{ text: 'Slide Title' }]
      },
      {
        type: 'p',
        children: [{ text: 'Your content goes here...' }]
      }
    ]
  },
  {
    id: 'bullet-points',
    name: 'Bullet Points',
    description: 'List of key points or items',
    layout: 'bullet-points',
    icon: CheckCircle,
    defaultContent: [
      {
        type: 'h2',
        children: [{ text: 'Key Points' }]
      },
      {
        type: 'ul',
        children: [
          { type: 'li', children: [{ text: 'First point' }] },
          { type: 'li', children: [{ text: 'Second point' }] },
          { type: 'li', children: [{ text: 'Third point' }] }
        ]
      }
    ]
  },
  {
    id: 'two-column',
    name: 'Two Column',
    description: 'Split content into two columns',
    layout: 'two-column',
    icon: Grid2X2,
    defaultContent: [
      {
        type: 'h2',
        children: [{ text: 'Two Column Layout' }]
      },
      {
        type: 'div',
        className: 'grid grid-cols-2 gap-6',
        children: [
          {
            type: 'div',
            children: [
              { type: 'h3', children: [{ text: 'Left Column' }] },
              { type: 'p', children: [{ text: 'Content for left side...' }] }
            ]
          },
          {
            type: 'div', 
            children: [
              { type: 'h3', children: [{ text: 'Right Column' }] },
              { type: 'p', children: [{ text: 'Content for right side...' }] }
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'image-text',
    name: 'Image & Text',
    description: 'Combination of image and text content',
    layout: 'image-text',
    icon: Image,
    defaultContent: [
      {
        type: 'h2',
        children: [{ text: 'Image & Text' }]
      },
      {
        type: 'div',
        className: 'flex items-center gap-6',
        children: [
          {
            type: 'div',
            className: 'flex-1',
            children: [{ type: 'p', children: [{ text: 'Your text content here...' }] }]
          },
          {
            type: 'div',
            className: 'w-1/3',
            children: [{ type: 'p', children: [{ text: '[Image placeholder]' }] }]
          }
        ]
      }
    ]
  },
  {
    id: 'quote',
    name: 'Quote',
    description: 'Highlighted quote or testimonial',
    layout: 'quote',
    icon: Quote,
    defaultContent: [
      {
        type: 'blockquote',
        className: 'text-center text-2xl italic',
        children: [{ text: '"Your inspiring quote goes here"' }]
      },
      {
        type: 'p',
        className: 'text-center text-lg mt-4',
        children: [{ text: '— Author Name' }]
      }
    ]
  },
  {
    id: 'conclusion',
    name: 'Conclusion',
    description: 'Closing slide with summary or call to action',
    layout: 'conclusion',
    icon: Target,
    defaultContent: [
      {
        type: 'h2',
        children: [{ text: 'Conclusion' }]
      },
      {
        type: 'p',
        children: [{ text: 'Summary of key takeaways...' }]
      },
      {
        type: 'h3',
        children: [{ text: 'Next Steps' }]
      }
    ]
  }
];

// Presentation styles
export const PRESENTATION_STYLES = [
  { id: 'professional', name: 'Professional', description: 'Clean and business-focused' },
  { id: 'casual', name: 'Casual', description: 'Relaxed and conversational' },
  { id: 'academic', name: 'Academic', description: 'Scholarly and research-oriented' },
  { id: 'creative', name: 'Creative', description: 'Artistic and visually engaging' }
];

// Language options
export const LANGUAGE_OPTIONS = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'zh', name: 'Chinese' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' }
];

// Slide count options
export const SLIDE_COUNT_OPTIONS = [
  { value: 5, label: '5 slides', description: 'Quick overview' },
  { value: 10, label: '10 slides', description: 'Standard presentation' },
  { value: 15, label: '15 slides', description: 'Detailed presentation' },
  { value: 20, label: '20 slides', description: 'Comprehensive presentation' },
  { value: 25, label: '25 slides', description: 'Extended presentation' }
];

// AI models for presentation generation
export const AI_MODELS: AIModelOption[] = [
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'Google',
    capabilities: ['text', 'structure', 'multimodal', 'fast'],
    maxTokens: 8192,
    supportsImages: true,
    recommended: true
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    provider: 'Google',
    capabilities: ['text', 'structure', 'creativity', 'multimodal', 'advanced'],
    maxTokens: 8192,
    supportsImages: true,
    recommended: true
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    capabilities: ['text', 'structure', 'creativity', 'multimodal'],
    maxTokens: 8192,
    supportsImages: true,
    recommended: true
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    capabilities: ['text', 'structure', 'fast'],
    maxTokens: 4096,
    supportsImages: false,
    recommended: true
  },
  {
    id: 'gpt-4.1',
    name: 'GPT-4.1 (mapped to GPT-4o)',
    provider: 'OpenAI',
    capabilities: ['text', 'structure', 'creativity'],
    maxTokens: 8192,
    supportsImages: false
  },
  {
    id: 'o4-mini',
    name: 'o4-mini (mapped to GPT-4o Mini)',
    provider: 'OpenAI',
    capabilities: ['text', 'structure', 'fast'],
    maxTokens: 4096,
    supportsImages: false
  }
];

// Default generation options
export const DEFAULT_GENERATION_OPTIONS = {
  model: 'gemini-2.5-flash',
  temperature: 0.7,
  maxTokens: 8192,
  includeImages: false,
  includeSpeakerNotes: true
};

// Built-in presentation themes
export const PRESENTATION_THEMES: PresentationTheme[] = [
  {
    id: 'modern',
    name: 'modern',
    displayName: 'Modern',
    colors: {
      primary: '#3B82F6',
      secondary: '#1E40AF',
      background: '#FFFFFF',
      text: '#1F2937',
      accent: '#F59E0B',
      muted: '#F3F4F6'
    },
    fonts: {
      heading: 'Inter, sans-serif',
      body: 'Inter, sans-serif',
      size: {
        title: '3rem',
        heading: '2rem',
        body: '1.125rem'
      }
    },
    layout: {
      padding: '2rem',
      spacing: '1.5rem',
      borderRadius: '0.5rem'
    }
  },
  {
    id: 'minimal',
    name: 'minimal',
    displayName: 'Minimal',
    colors: {
      primary: '#000000',
      secondary: '#374151',
      background: '#FFFFFF',
      text: '#111827',
      accent: '#EF4444',
      muted: '#F9FAFB'
    },
    fonts: {
      heading: 'system-ui, sans-serif',
      body: 'system-ui, sans-serif',
      size: {
        title: '2.5rem',
        heading: '1.875rem',
        body: '1rem'
      }
    },
    layout: {
      padding: '3rem',
      spacing: '2rem',
      borderRadius: '0'
    }
  },
  {
    id: 'corporate',
    name: 'corporate',
    displayName: 'Corporate',
    colors: {
      primary: '#1E3A8A',
      secondary: '#3730A3',
      background: '#FFFFFF',
      text: '#1F2937',
      accent: '#DC2626',
      muted: '#E5E7EB'
    },
    fonts: {
      heading: 'Georgia',
      body: 'system-ui',
      size: {
        title: '2.75rem',
        heading: '2rem',
        body: '1.125rem'
      }
    },
    layout: {
      padding: '2.5rem',
      spacing: '1.75rem',
      borderRadius: '0.25rem'
    }
  },
  {
    id: 'creative',
    name: 'creative',
    displayName: 'Creative',
    colors: {
      primary: '#7C3AED',
      secondary: '#C026D3',
      background: '#FEFEFE',
      text: '#1F2937',
      accent: '#F59E0B',
      muted: '#F3F4F6'
    },
    fonts: {
      heading: 'Poppins, sans-serif',
      body: 'Inter, sans-serif',
      size: {
        title: '3.5rem',
        heading: '2.25rem',
        body: '1.125rem'
      }
    },
    layout: {
      padding: '2rem',
      spacing: '2rem',
      borderRadius: '1rem'
    }
  },
  {
    id: 'dark',
    name: 'dark',
    displayName: 'Dark',
    colors: {
      primary: '#60A5FA',
      secondary: '#3B82F6',
      background: '#111827',
      text: '#F9FAFB',
      accent: '#10B981',
      muted: '#374151'
    },
    fonts: {
      heading: 'Inter, sans-serif',
      body: 'Inter, sans-serif',
      size: {
        title: '3rem',
        heading: '2rem',
        body: '1.125rem'
      }
    },
    layout: {
      padding: '2rem',
      spacing: '1.5rem',
      borderRadius: '0.5rem'
    }
  },
  {
    id: 'elegant',
    name: 'elegant',
    displayName: 'Elegant',
    colors: {
      primary: '#8B5A3C',
      secondary: '#D4A574',
      background: '#FDF6E3',
      text: '#5D4037',
      accent: '#FF6B35',
      muted: '#F5E6D3'
    },
    fonts: {
      heading: 'Playfair Display, serif',
      body: 'Source Sans Pro, sans-serif',
      size: {
        title: '3.2rem',
        heading: '2.1rem',
        body: '1.1rem'
      }
    },
    layout: {
      padding: '2.5rem',
      spacing: '1.8rem',
      borderRadius: '0.75rem'
    }
  }
];

// Image generation models
export const IMAGE_MODELS = [
  { value: "black-forest-labs/FLUX1.1-pro", label: "FLUX 1.1 [pro]" },
  { value: "black-forest-labs/FLUX.1-schnell", label: "FLUX.1 [schnell]" },
  { value: "black-forest-labs/FLUX.1-dev", label: "FLUX.1 [dev]" },
  { value: "stabilityai/stable-diffusion-3-5-large", label: "Stable Diffusion 3.5 Large" },
  { value: "stabilityai/stable-diffusion-3-5-large-turbo", label: "Stable Diffusion 3.5 Large Turbo" },
  { value: "stabilityai/stable-diffusion-3-medium", label: "Stable Diffusion 3 Medium" },
  { value: "runwayml/stable-diffusion-v1-5", label: "Stable Diffusion v1.5" },
  { value: "SG161222/Realistic_Vision_V6.0_B1_noVAE", label: "Realistic Vision V6.0" }
];

// Export format options
export const EXPORT_FORMATS = [
  { id: 'pdf', name: 'PDF', description: 'Portable Document Format', icon: FileText },
  { id: 'pptx', name: 'PowerPoint', description: 'Microsoft PowerPoint format', icon: Presentation },
  { id: 'html', name: 'HTML', description: 'Web-based presentation', icon: Sparkles },
  { id: 'markdown', name: 'Markdown', description: 'Plain text format', icon: Type }
];
