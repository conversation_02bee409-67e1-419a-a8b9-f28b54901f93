import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  FileText,
  Calendar,
  Users,
  Tag,
  Star,
  Eye,
  Download,
  MessageSquare,
  MoreHorizontal,
  SortAsc,
  SortDesc,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Heart,
  Bookmark,
  Share,
  Trash2,
  Brain,
  Info,
  BookOpen,
  Award,
  Target,
  Lightbulb,
  TrendingUp,
  Database,
  ChevronRight,
  ExternalLink,
  Copy,
  Zap,
  Sparkles,
  GraduationCap,
  Clock,
  Globe,
  Building,
  Quote,
  ChevronDown,
  ChevronUp,
  Link,
  FileCheck,
  Microscope,
  FlaskConical,
  Atom,
  PieChart,
  BarChart3,
  LineChart,
  Activity,
  Layers,
  Workflow,
  Network,
  GitBranch,
  Lightbulb as LightbulbIcon,
  ArrowRight,
  Plus,
  Minus,
  X,
  Settings,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  Maximize2,
  Minimize2,
  ExternalLinkIcon
} from "lucide-react";

import { ResearchDocument } from '../types';
import { researchAnalysisService } from '../services/research-analysis.service';
import { cn } from "@/lib/utils";

interface BeautifulDocumentLibraryProps {
  documents: ResearchDocument[];
  selectedDocuments: string[];
  onSelectionChange: (documentIds: string[]) => void;
  onSearch: (query: string) => void;
  onFilter: (filters: any) => void;
}

interface DocumentPreview {
  document: ResearchDocument;
  summary?: string;
  isLoading: boolean;
}

export function BeautifulDocumentLibrary({
  documents,
  selectedDocuments,
  onSelectionChange,
  onSearch,
  onFilter
}: BeautifulDocumentLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'cards' | 'compact' | 'detailed'>('detailed');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'year' | 'authors' | 'relevance'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<DocumentPreview | null>(null);
  const [expandedDocuments, setExpandedDocuments] = useState<Set<string>>(new Set());

  // Enhanced filters state
  const [filters, setFilters] = useState({
    years: [2015, 2025] as [number, number],
    authors: [] as string[],
    journals: [] as string[],
    tags: [] as string[],
    categories: [] as string[],
    status: 'all' as 'all' | 'ready' | 'processing' | 'error',
    hasKeyFindings: false,
    hasMethodology: false,
    hasDOI: false,
    confidenceThreshold: 0.5
  });

  // Handle search with advanced features
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    onSearch(query);
  }, [onSearch]);

  // Handle document selection with bulk actions
  const handleDocumentSelection = useCallback((documentId: string, checked: boolean) => {
    let newSelection: string[];
    if (checked) {
      newSelection = [...selectedDocuments, documentId];
    } else {
      newSelection = selectedDocuments.filter(id => id !== documentId);
    }
    onSelectionChange(newSelection);
  }, [selectedDocuments, onSelectionChange]);

  // Advanced sorting and filtering
  const filteredDocuments = useMemo(() => {
    let filtered = [...documents];

    // Apply filters
    if (filters.status !== 'all') {
      filtered = filtered.filter(doc => doc.status === filters.status);
    }
    
    if (filters.authors.length > 0) {
      filtered = filtered.filter(doc => 
        doc.authors.some(author => filters.authors.includes(author))
      );
    }
    
    if (filters.tags.length > 0) {
      filtered = filtered.filter(doc => 
        doc.tags.some(tag => filters.tags.includes(tag))
      );
    }
    
    if (filters.hasKeyFindings) {
      filtered = filtered.filter(doc => doc.keyFindings.length > 0);
    }
    
    if (filters.hasMethodology) {
      filtered = filtered.filter(doc => doc.methodology.length > 0);
    }
    
    if (filters.hasDOI) {
      filtered = filtered.filter(doc => doc.doi);
    }
    
    if (filters.confidenceThreshold > 0) {
      filtered = filtered.filter(doc => 
        (doc.metadata?.confidence || 0) >= filters.confidenceThreshold
      );
    }

    // Apply search
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(doc => 
        doc.title.toLowerCase().includes(searchLower) ||
        doc.authors.some(author => author.toLowerCase().includes(searchLower)) ||
        doc.abstract.toLowerCase().includes(searchLower) ||
        doc.keywords.some(keyword => keyword.toLowerCase().includes(searchLower))
      );
    }

    return filtered;
  }, [documents, filters, searchQuery]);

  const sortedDocuments = useMemo(() => {
    const sorted = [...filteredDocuments];

    // Apply sorting
    sorted.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'year':
          comparison = a.publicationYear - b.publicationYear;
          break;
        case 'authors':
          comparison = a.authors[0]?.localeCompare(b.authors[0] || '') || 0;
          break;
        case 'relevance':
          comparison = (b.metadata?.confidence || 0) - (a.metadata?.confidence || 0);
          break;
        case 'date':
        default:
          comparison = new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime();
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [filteredDocuments, sortBy, sortOrder]);

  // Handle select all/none with smart filtering
  const handleSelectAll = useCallback(() => {
    const filteredDocumentIds = sortedDocuments.map(d => d.id);
    if (selectedDocuments.length === filteredDocumentIds.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredDocumentIds);
    }
  }, [sortedDocuments, selectedDocuments, onSelectionChange]);

  // Toggle document expansion
  const toggleDocumentExpansion = useCallback((documentId: string) => {
    setExpandedDocuments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  }, []);

  // Enhanced preview with full document analysis
  const handlePreviewDocument = useCallback(async (document: ResearchDocument) => {
    setPreviewDocument({ document, isLoading: true });
    
    try {
      const summary = await researchAnalysisService.generateDocumentSummary(document);
      setPreviewDocument({ document, summary, isLoading: false });
    } catch (error) {
      console.error('Error generating summary:', error);
      setPreviewDocument({ document, isLoading: false });
      toast.error('Failed to generate document summary');
    }
  }, []);

  // Copy document information
  const copyDocumentInfo = useCallback((document: ResearchDocument) => {
    const info = `${document.title}\n${document.authors.join(', ')}\n${document.publicationYear}\n${document.abstract}`;
    navigator.clipboard.writeText(info);
    toast.success('Document information copied to clipboard');
  }, []);

  // Get enhanced status display
  const getStatusDisplay = (document: ResearchDocument) => {
    const confidence = document.metadata?.confidence || 0;
    
    return (
      <div className="flex items-center gap-3">
        {document.status === 'ready' && (
          <div className="flex items-center gap-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
            <CheckCircle className="h-4 w-4" />
            <span>Analyzed</span>
          </div>
        )}
        {document.status === 'processing' && (
          <div className="flex items-center gap-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Processing</span>
          </div>
        )}
        {document.status === 'error' && (
          <div className="flex items-center gap-2 bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
            <AlertTriangle className="h-4 w-4" />
            <span>Error</span>
          </div>
        )}
        {confidence > 0 && (
          <div className={cn(
            "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
            confidence > 0.8 ? 'bg-green-100 text-green-700' : 
            confidence > 0.5 ? 'bg-yellow-100 text-yellow-700' : 
            'bg-red-100 text-red-700'
          )}>
            <Brain className="h-3 w-3" />
            <span>{Math.round(confidence * 100)}% confidence</span>
          </div>
        )}
      </div>
    );
  };

  // Get research quality indicators
  const getQualityIndicators = (document: ResearchDocument) => {
    const indicators = [];
    
    if (document.doi) indicators.push({ icon: Link, label: 'DOI Available', color: 'bg-blue-100 text-blue-700' });
    if (document.keyFindings.length > 5) indicators.push({ icon: Target, label: 'Rich Findings', color: 'bg-green-100 text-green-700' });
    if (document.methodology.length > 3) indicators.push({ icon: Brain, label: 'Detailed Methods', color: 'bg-purple-100 text-purple-700' });
    if (document.journal) indicators.push({ icon: Award, label: 'Journal Publication', color: 'bg-orange-100 text-orange-700' });
    if (document.sections.length > 8) indicators.push({ icon: BookOpen, label: 'Comprehensive', color: 'bg-indigo-100 text-indigo-700' });
    
    return indicators;
  };

  // Beautiful modern document card with prominent AI analysis
  const renderBeautifulDocumentCard = (document: ResearchDocument) => {
    const isExpanded = expandedDocuments.has(document.id);
    const isSelected = selectedDocuments.includes(document.id);
    const qualityIndicators = getQualityIndicators(document);
    
    return (
      <Card 
        key={document.id} 
        className={cn(
          "group hover:shadow-2xl transition-all duration-500 border-0 overflow-hidden backdrop-blur-sm",
          isSelected 
            ? "ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50" 
            : "bg-white/80 hover:bg-white/90",
          document.status === 'error' && "ring-2 ring-red-300 bg-gradient-to-br from-red-50 to-orange-50"
        )}
        style={{
          boxShadow: isSelected 
            ? "0 25px 50px -12px rgba(59, 130, 246, 0.25)" 
            : "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
        }}
      >
        {/* Status gradient bar */}
        <div className={cn(
          "h-1.5 w-full",
          document.status === 'ready' ? 'bg-gradient-to-r from-green-400 via-green-500 to-emerald-600' : 
          document.status === 'processing' ? 'bg-gradient-to-r from-blue-400 via-blue-500 to-cyan-600 animate-pulse' : 
          'bg-gradient-to-r from-red-400 via-red-500 to-orange-600'
        )} />
        
        <CardHeader className="pb-4 space-y-4">
          <div className="flex items-start gap-4">
            <div className="relative">
              <Checkbox
                checked={isSelected}
                onCheckedChange={(checked) => handleDocumentSelection(document.id, checked as boolean)}
                className="h-5 w-5 rounded-lg border-2 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
              />
              {isSelected && (
                <div className="absolute -inset-1 bg-blue-500/20 rounded-lg animate-pulse" />
              )}
            </div>
            
            <div className="flex-1 min-w-0 space-y-4">
              {/* Title and status */}
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-xl font-bold text-gray-900 leading-tight line-clamp-2 group-hover:text-blue-900 transition-colors">
                    {document.title}
                  </CardTitle>
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleDocumentExpansion(document.id)}
                      className="h-8 w-8 p-0 rounded-full hover:bg-blue-100"
                    >
                      {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewDocument(document)}
                      className="px-3 py-1.5 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
                    >
                      <Brain className="h-4 w-4 mr-1" />
                      AI Summary
                    </Button>
                  </div>
                </div>
                {getStatusDisplay(document)}
              </div>
              
              {/* Authors and publication info in beautiful layout */}
              <div className="flex flex-wrap items-center gap-6 text-sm">
                <div className="flex items-center gap-3 bg-gray-50 px-4 py-2 rounded-xl">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 font-medium">Authors</div>
                    <div className="font-semibold text-gray-800">
                      {document.authors.slice(0, 2).join(', ')}
                      {document.authors.length > 2 && ` +${document.authors.length - 2} more`}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 bg-gray-50 px-4 py-2 rounded-xl">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 font-medium">Published</div>
                    <div className="font-semibold text-gray-800">{document.publicationYear}</div>
                  </div>
                </div>
                
                {document.journal && (
                  <div className="flex items-center gap-3 bg-gray-50 px-4 py-2 rounded-xl">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <Building className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 font-medium">Journal</div>
                      <div className="font-semibold text-gray-800 truncate max-w-32">{document.journal}</div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Quality indicators as beautiful badges */}
              {qualityIndicators.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {qualityIndicators.map((indicator, index) => (
                    <TooltipProvider key={index}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className={cn(
                            "flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium",
                            indicator.color
                          )}>
                            <indicator.icon className="h-3 w-3" />
                            {indicator.label}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{indicator.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0 space-y-6">
          {/* Abstract with beautiful styling */}
          {document.abstract && (
            <div className="relative">
              <div className="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full" />
              <div className="ml-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Quote className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-sm font-bold text-blue-900">Abstract</span>
                </div>
                <p className={cn(
                  "text-sm text-blue-800 leading-relaxed",
                  !isExpanded && "line-clamp-3"
                )}>
                  {document.abstract}
                </p>
              </div>
            </div>
          )}
          
          {/* Keywords as beautiful tags */}
          {document.keywords.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                  <Tag className="h-3 w-3 text-white" />
                </div>
                <span className="text-sm font-bold text-gray-900">Keywords</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {document.keywords.map((keyword, index) => (
                  <div
                    key={index}
                    className="px-3 py-1.5 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 text-xs font-medium rounded-full border border-indigo-200 hover:from-indigo-200 hover:to-purple-200 transition-all cursor-pointer"
                  >
                    #{keyword}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Expanded AI Analysis Content */}
          {isExpanded && (
            <div className="space-y-6 border-t pt-6">
              {/* AI Analysis Header */}
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">AI-Enhanced Analysis</h3>
                <div className="flex-1 h-px bg-gradient-to-r from-purple-200 to-transparent" />
              </div>
              
              {/* Key Findings */}
              {document.keyFindings.length > 0 && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-2xl border border-green-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-green-500 rounded-xl flex items-center justify-center">
                      <Target className="h-4 w-4 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-green-900">Key Findings</h4>
                    <Badge variant="secondary" className="bg-green-200 text-green-800">
                      {document.keyFindings.length} findings
                    </Badge>
                  </div>
                  <div className="grid gap-3">
                    {document.keyFindings.map((finding, index) => (
                      <div key={index} className="flex items-start gap-3 bg-white/60 p-4 rounded-xl">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">
                          {index + 1}
                        </div>
                        <p className="text-sm text-green-800 leading-relaxed font-medium">{finding}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Methodology */}
              {document.methodology.length > 0 && (
                <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-2xl border border-purple-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-purple-500 rounded-xl flex items-center justify-center">
                      <FlaskConical className="h-4 w-4 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-purple-900">Research Methodology</h4>
                    <Badge variant="secondary" className="bg-purple-200 text-purple-800">
                      {document.methodology.length} methods
                    </Badge>
                  </div>
                  <div className="grid gap-3">
                    {document.methodology.map((method, index) => (
                      <div key={index} className="flex items-start gap-3 bg-white/60 p-4 rounded-xl">
                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                          <Microscope className="h-3 w-3 text-white" />
                        </div>
                        <p className="text-sm text-purple-800 leading-relaxed font-medium">{method}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Limitations */}
              {document.limitations.length > 0 && (
                <div className="bg-gradient-to-br from-orange-50 to-red-50 p-6 rounded-2xl border border-orange-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-orange-500 rounded-xl flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-orange-900">Study Limitations</h4>
                    <Badge variant="secondary" className="bg-orange-200 text-orange-800">
                      {document.limitations.length} identified
                    </Badge>
                  </div>
                  <div className="grid gap-3">
                    {document.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-start gap-3 bg-white/60 p-4 rounded-xl">
                        <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs">
                          !
                        </div>
                        <p className="text-sm text-orange-800 leading-relaxed font-medium">{limitation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Future Work */}
              {document.futureWork.length > 0 && (
                <div className="bg-gradient-to-br from-yellow-50 to-amber-50 p-6 rounded-2xl border border-yellow-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-yellow-500 rounded-xl flex items-center justify-center">
                      <Lightbulb className="h-4 w-4 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-yellow-900">Future Research Directions</h4>
                    <Badge variant="secondary" className="bg-yellow-200 text-yellow-800">
                      {document.futureWork.length} directions
                    </Badge>
                  </div>
                  <div className="grid gap-3">
                    {document.futureWork.map((future, index) => (
                      <div key={index} className="flex items-start gap-3 bg-white/60 p-4 rounded-xl">
                        <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                          <ArrowRight className="h-3 w-3 text-white" />
                        </div>
                        <p className="text-sm text-yellow-800 leading-relaxed font-medium">{future}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Document Structure Visualization */}
              {document.sections.length > 0 && (
                <div className="bg-gradient-to-br from-gray-50 to-slate-50 p-6 rounded-2xl border border-gray-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-gray-600 rounded-xl flex items-center justify-center">
                      <Layers className="h-4 w-4 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-900">Document Structure</h4>
                    <Badge variant="secondary" className="bg-gray-200 text-gray-800">
                      {document.sections.length} sections
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                    {document.sections.map((section, index) => (
                      <div key={index} className="flex items-center gap-2 bg-white/60 p-3 rounded-lg border border-gray-200">
                        <div className="w-3 h-3 bg-blue-500 rounded-full" />
                        <span className="text-xs font-medium text-gray-700 capitalize">
                          {section.type.replace('_', ' ')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Footer with enhanced metadata and actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="flex items-center gap-6 text-xs text-gray-500">
              <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                <FileCheck className="h-3 w-3" />
                <span className="font-medium">{document.sections.length} sections</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                <Clock className="h-3 w-3" />
                <span className="font-medium">{new Date(document.uploadedAt).toLocaleDateString()}</span>
              </div>
              {document.fileSize && (
                <div className="flex items-center gap-2 bg-gray-50 px-3 py-1.5 rounded-full">
                  <Database className="h-3 w-3" />
                  <span className="font-medium">{(document.fileSize / 1024 / 1024).toFixed(1)}MB</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => copyDocumentInfo(document)}
                      className="h-8 w-8 p-0 rounded-full hover:bg-blue-100"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Copy document info</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => toast.info('Share functionality coming soon')}
                      className="h-8 w-8 p-0 rounded-full hover:bg-green-100"
                    >
                      <Share className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share document</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full hover:bg-red-100"
                    >
                      <Heart className={cn(
                        "h-4 w-4",
                        document.favorite ? "text-red-500 fill-current" : "text-gray-400"
                      )} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Add to favorites</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Statistics summary
  const getLibraryStats = () => {
    const total = documents.length;
    const ready = documents.filter(d => d.status === 'ready').length;
    const processing = documents.filter(d => d.status === 'processing').length;
    const errors = documents.filter(d => d.status === 'error').length;
    const withFindings = documents.filter(d => d.keyFindings.length > 0).length;
    const withMethods = documents.filter(d => d.methodology.length > 0).length;
    
    return { total, ready, processing, errors, withFindings, withMethods };
  };

  const stats = getLibraryStats();

  return (
    <div className="space-y-8">
      {/* Beautiful Header with Enhanced Statistics */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-3xl transform rotate-1"></div>
        <Card className="relative bg-white border-0 rounded-3xl shadow-2xl">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <BookOpen className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Research Library
                  </h1>
                  <p className="text-lg text-gray-600 font-medium">AI-Enhanced Document Analysis & Insights</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'detailed' ? 'cards' : 'detailed')}
                  className="bg-white hover:bg-gray-50 border-2"
                >
                  {viewMode === 'detailed' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
                  <span className="ml-2 hidden sm:inline">
                    {viewMode === 'detailed' ? 'Card View' : 'Detailed View'}
                  </span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="bg-white hover:bg-gray-50 border-2"
                >
                  <Filter className="h-4 w-4" />
                  <span className="ml-2 hidden sm:inline">Filters</span>
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Beautiful Statistics Dashboard */}
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl text-center border border-blue-200">
                <div className="text-3xl font-bold text-blue-700 mb-1">{stats.total}</div>
                <div className="text-sm text-blue-600 font-medium">Total Papers</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl text-center border border-green-200">
                <div className="text-3xl font-bold text-green-700 mb-1">{stats.ready}</div>
                <div className="text-sm text-green-600 font-medium">Analyzed</div>
              </div>
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl text-center border border-orange-200">
                <div className="text-3xl font-bold text-orange-700 mb-1">{stats.processing}</div>
                <div className="text-sm text-orange-600 font-medium">Processing</div>
              </div>
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-xl text-center border border-red-200">
                <div className="text-3xl font-bold text-red-700 mb-1">{stats.errors}</div>
                <div className="text-sm text-red-600 font-medium">Errors</div>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl text-center border border-purple-200">
                <div className="text-3xl font-bold text-purple-700 mb-1">{stats.withFindings}</div>
                <div className="text-sm text-purple-600 font-medium">With Findings</div>
              </div>
              <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-xl text-center border border-indigo-200">
                <div className="text-3xl font-bold text-indigo-700 mb-1">{stats.withMethods}</div>
                <div className="text-sm text-indigo-600 font-medium">With Methods</div>
              </div>
            </div>
            
            {/* Enhanced Search and Controls */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search documents, authors, keywords, or content..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-12 h-12 text-lg bg-white border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                  />
                </div>
                <Select value={sortBy} onValueChange={(value: string) => setSortBy(value as typeof sortBy)}>
                  <SelectTrigger className="w-48 h-12 bg-white border-2 border-gray-200 rounded-xl">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Upload Date</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="year">Publication Year</SelectItem>
                    <SelectItem value="authors">Authors</SelectItem>
                    <SelectItem value="relevance">AI Confidence</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="h-12 px-4 bg-white border-2 border-gray-200 rounded-xl hover:bg-gray-50"
                >
                  {sortOrder === 'asc' ? <SortAsc className="h-5 w-5" /> : <SortDesc className="h-5 w-5" />}
                </Button>
              </div>
              
              {/* Selection Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    className="bg-white border-2 border-gray-200 rounded-xl hover:bg-gray-50"
                  >
                    {selectedDocuments.length === sortedDocuments.length ? 'Deselect All' : 'Select All'}
                  </Button>
                  {selectedDocuments.length > 0 && (
                    <div className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-4 py-2 rounded-xl border border-blue-200">
                      <span className="font-semibold">{selectedDocuments.length} documents selected</span>
                    </div>
                  )}
                </div>
                <div className="text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl">
                  Showing <span className="font-semibold">{sortedDocuments.length}</span> of <span className="font-semibold">{documents.length}</span> documents
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Beautiful Documents Display */}
      <div className="space-y-6">
        {sortedDocuments.length === 0 ? (
          <Card className="p-16 text-center border-2 border-dashed border-gray-300 rounded-3xl bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="space-y-6">
              <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto">
                <BookOpen className="h-12 w-12 text-gray-400" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">No documents found</h3>
                <p className="text-gray-600 text-lg">
                  {searchQuery || Object.values(filters).some(f => f !== 'all' && f !== false && f !== 0.5) 
                    ? 'Try adjusting your search or filters to find more documents'
                    : 'Upload some research papers to get started with AI-powered analysis'
                  }
                </p>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-6">
            {sortedDocuments.map(document => renderBeautifulDocumentCard(document))}
          </div>
        )}
      </div>

      {/* Enhanced Document Preview Dialog */}
      <Dialog open={!!previewDocument} onOpenChange={() => setPreviewDocument(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          {previewDocument && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold pr-8 text-gray-900">
                  {previewDocument.document.title}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-8">
                {previewDocument.isLoading ? (
                  <div className="flex items-center justify-center py-16">
                    <div className="text-center space-y-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                        <RefreshCw className="h-8 w-8 animate-spin text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Generating AI Summary</h3>
                        <p className="text-gray-600">Our AI is analyzing the document to provide comprehensive insights...</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    {previewDocument.summary && (
                      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border-l-4 border-blue-500">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <Sparkles className="h-4 w-4 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-blue-900">AI-Generated Summary</h3>
                        </div>
                        <div className="text-blue-800 whitespace-pre-wrap leading-relaxed">
                          {previewDocument.summary}
                        </div>
                      </div>
                    )}
                    
                    {/* Full document analysis display */}
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-6">
                        <div className="bg-gray-50 p-6 rounded-2xl">
                          <h4 className="text-lg font-bold text-gray-900 mb-4">Document Details</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-600">Authors: <AUTHORS>
                              <span className="text-gray-900">{previewDocument.document.authors.join(', ')}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-600">Year:</span>
                              <span className="text-gray-900">{previewDocument.document.publicationYear}</span>
                            </div>
                            {previewDocument.document.journal && (
                              <div className="flex justify-between">
                                <span className="font-medium text-gray-600">Journal:</span>
                                <span className="text-gray-900">{previewDocument.document.journal}</span>
                              </div>
                            )}
                            {previewDocument.document.doi && (
                              <div className="flex justify-between">
                                <span className="font-medium text-gray-600">DOI:</span>
                                <span className="text-gray-900">{previewDocument.document.doi}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {previewDocument.document.keyFindings.length > 0 && (
                          <div className="bg-green-50 p-6 rounded-2xl">
                            <h4 className="text-lg font-bold text-green-900 mb-4">Key Findings</h4>
                            <div className="space-y-3">
                              {previewDocument.document.keyFindings.map((finding, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">
                                    {index + 1}
                                  </div>
                                  <p className="text-sm text-green-800 leading-relaxed">{finding}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-6">
                        {previewDocument.document.methodology.length > 0 && (
                          <div className="bg-purple-50 p-6 rounded-2xl">
                            <h4 className="text-lg font-bold text-purple-900 mb-4">Methodology</h4>
                            <div className="space-y-3">
                              {previewDocument.document.methodology.map((method, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center mt-0.5">
                                    <Microscope className="h-3 w-3 text-white" />
                                  </div>
                                  <p className="text-sm text-purple-800 leading-relaxed">{method}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {previewDocument.document.limitations.length > 0 && (
                          <div className="bg-orange-50 p-6 rounded-2xl">
                            <h4 className="text-lg font-bold text-orange-900 mb-4">Limitations</h4>
                            <div className="space-y-3">
                              {previewDocument.document.limitations.map((limitation, index) => (
                                <div key={index} className="flex items-start gap-3">
                                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs mt-0.5">
                                    !
                                  </div>
                                  <p className="text-sm text-orange-800 leading-relaxed">{limitation}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {previewDocument.document.abstract && (
                      <div className="bg-blue-50 p-6 rounded-2xl">
                        <h4 className="text-lg font-bold text-blue-900 mb-4">Abstract</h4>
                        <p className="text-blue-800 leading-relaxed">
                          {previewDocument.document.abstract}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
