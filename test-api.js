// Simple test to verify API connectivity
import paperAIService from './src/components/research/paper-generator/paper-ai.service.ts';

async function testAPIConnection() {
  console.log('Testing AI service configuration...');
  
  // Check if API key is configured
  const hasValidKey = paperAIService.hasValidApiKey();
  console.log('Has valid API key:', hasValidKey);
  
  if (hasValidKey) {
    const provider = paperAIService.getApiProvider();
    console.log('API Provider:', provider);
    
    try {
      // Test a simple AI request
      const response = await paperAIService.analyzeText('What is AI?', {
        model: 'google/gemini-2.5-flash-lite-preview-06-17',
        maxTokens: 100
      });
      
      console.log('AI Response:', response.substring(0, 200) + '...');
      console.log('✅ API connection successful!');
    } catch (error) {
      console.error('❌ API request failed:', error.message);
    }
  } else {
    console.log('❌ No valid API key configured');
  }
}

testAPIConnection();
