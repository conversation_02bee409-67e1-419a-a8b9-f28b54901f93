/**
 * Diagram Editor Component
 * Code editor for Mermaid diagrams with syntax highlighting and validation
 */

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Edit3,
  Save,
  X,
  Eye,
  Code,
  AlertTriangle,
  CheckCircle,
  Copy,
  RotateCcw,
  Lightbulb,
  Sparkles,
  Send,
  Loader2,
  Wand2
} from 'lucide-react';
import { DiagramEditorProps } from '../types';
import { DiagramRenderer } from './DiagramRenderer';
import { useFlowBuilderStore } from '../stores/flow-builder.store';

export function DiagramEditor({
  diagram,
  isEditing,
  editedCode,
  onCodeChange,
  onSave,
  onCancel,
  validationErrors
}: DiagramEditorProps) {
  const [activeTab, setActiveTab] = useState('preview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGeneratingChanges, setIsGeneratingChanges] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { mermaidConfig, selectedModel, regenerateDiagram } = useFlowBuilderStore();



  useEffect(() => {
    setHasUnsavedChanges(editedCode !== diagram.mermaidCode);
  }, [editedCode, diagram.mermaidCode]);

  const handleCodeChange = (value: string) => {
    onCodeChange(value);
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(editedCode);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const handleResetCode = () => {
    onCodeChange(diagram.mermaidCode);
  };

  const handleAIPrompt = async () => {
    if (!aiPrompt.trim()) return;

    console.log('🤖 DiagramEditor: Starting AI prompt processing', {
      prompt: aiPrompt,
      currentCodeLength: editedCode.length,
      selectedModel
    });

    setIsGeneratingChanges(true);
    try {
      const { flowAIService } = await import('../services/flow-ai.service');

      // Create a prompt that includes the current code and the user's request
      const enhancedPrompt = `Current Mermaid diagram code:
\`\`\`mermaid
${editedCode}
\`\`\`

User request: ${aiPrompt}

Please modify the above Mermaid diagram according to the user's request.

CRITICAL: Follow these Mermaid syntax rules:
- Use only alphanumeric characters, spaces, hyphens, and underscores in node labels
- NO special characters: / \\ < > { } | ( ) + * % $ # @ ! ^ &
- Use proper spacing around arrows: A --> B
- Keep labels simple and clear

Return only the updated Mermaid code without any explanations or markdown formatting.`;

      console.log('📝 DiagramEditor: Sending enhanced prompt', {
        enhancedPromptLength: enhancedPrompt.length
      });

      const response = await flowAIService.generateDiagram({
        input: {
          description: enhancedPrompt,
          type: diagram.metadata.type,
          context: 'Modifying existing diagram based on user feedback'
        },
        model: selectedModel
      });

      console.log('📨 DiagramEditor: Received AI response', {
        success: response.success,
        hasNewCode: !!response.diagram?.mermaidCode,
        newCodeLength: response.diagram?.mermaidCode?.length
      });

      if (response.success && response.diagram) {
        console.log('✅ DiagramEditor: Applying new code');
        onCodeChange(response.diagram.mermaidCode);
        toast.success('Diagram updated with AI suggestions!');
        setAiPrompt('');
      } else {
        console.error('❌ DiagramEditor: AI generation failed', response.error);
        toast.error(`Failed to generate AI suggestions: ${response.error || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('❌ DiagramEditor: AI prompt error:', error);
      toast.error(`Failed to process AI prompt: ${error.message}`);
    } finally {
      setIsGeneratingChanges(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Ctrl+S to save
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      if (validationErrors.length === 0) {
        onSave();
      }
    }

    // Handle Escape to cancel
    if (e.key === 'Escape') {
      onCancel();
    }
  };

  const handlePromptKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleAIPrompt();
    }
  };

  // Create a temporary diagram for preview with updated timestamp to force re-render
  const previewDiagram = React.useMemo(() => ({
    ...diagram,
    id: `preview-${diagram.id}`,
    mermaidCode: editedCode,
    metadata: {
      ...diagram.metadata,
      updatedAt: new Date()
    }
  }), [diagram, editedCode]);

  const lineCount = editedCode.split('\n').length;

  return (
    <div className="space-y-4">
      {/* Editor Header */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Edit3 className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-lg">Diagram Editor</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">{diagram.metadata.type}</Badge>
                  <span className="text-sm text-gray-500">
                    {lineCount} lines
                  </span>
                  {hasUnsavedChanges && (
                    <Badge variant="secondary" className="bg-orange-50 text-orange-700">
                      Unsaved changes
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleCopyCode}>
                <Copy className="h-4 w-4 mr-1" />
                Copy
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetCode}>
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <Button variant="outline" size="sm" onClick={onCancel}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button 
                size="sm" 
                onClick={onSave}
                disabled={validationErrors.length > 0}
              >
                <Save className="h-4 w-4 mr-1" />
                Save Changes
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Validation Errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Editor Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Live Preview
          </TabsTrigger>
          <TabsTrigger value="ai-prompt" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            AI Assistant
          </TabsTrigger>
          <TabsTrigger value="editor" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Code Editor
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="mt-4">
          {validationErrors.length === 0 ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Live Preview
                    <Badge variant="outline" className="ml-auto">
                      {editedCode.split('\n').length} lines
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <DiagramRenderer
                    key={`preview-${previewDiagram.id}-${editedCode.length}`}
                    diagram={previewDiagram}
                    config={mermaidConfig}
                    className="w-full"
                    onError={(error) => {
                      console.error('Preview render error:', error);
                    }}
                  />
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertTriangle className="h-12 w-12 text-orange-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">Cannot Preview</h3>
                <p className="text-gray-500 text-center mb-4">
                  Fix the validation errors in the Code Editor tab to see the live preview.
                </p>
                <Button variant="outline" onClick={() => setActiveTab('editor')}>
                  <Code className="h-4 w-4 mr-2" />
                  Go to Editor
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="ai-prompt" className="mt-4">
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  AI Assistant
                  <Badge variant="outline" className="ml-auto">
                    {selectedModel.split('/').pop()}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Describe the changes you want to make:</label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="e.g., Add a decision point after data collection, change colors to blue theme, add more detail to the analysis step..."
                      value={aiPrompt}
                      onChange={(e) => setAiPrompt(e.target.value)}
                      onKeyDown={handlePromptKeyDown}
                      className="flex-1"
                      disabled={isGeneratingChanges}
                    />
                    <Button
                      onClick={handleAIPrompt}
                      disabled={!aiPrompt.trim() || isGeneratingChanges}
                      size="sm"
                    >
                      {isGeneratingChanges ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Press Ctrl+Enter to apply changes • Be specific about what you want to modify
                  </p>
                </div>

                {/* Quick Actions */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quick Actions:</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAiPrompt('Add more detail and sub-steps to each process')}
                      disabled={isGeneratingChanges}
                    >
                      <Wand2 className="h-4 w-4 mr-1" />
                      Add Detail
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAiPrompt('Simplify the diagram and remove unnecessary steps')}
                      disabled={isGeneratingChanges}
                    >
                      <Wand2 className="h-4 w-4 mr-1" />
                      Simplify
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAiPrompt('Add decision points and conditional flows')}
                      disabled={isGeneratingChanges}
                    >
                      <Wand2 className="h-4 w-4 mr-1" />
                      Add Decisions
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setAiPrompt('Change the layout to left-to-right flow')}
                      disabled={isGeneratingChanges}
                    >
                      <Wand2 className="h-4 w-4 mr-1" />
                      Change Layout
                    </Button>
                  </div>
                </div>

                {/* Current Preview */}
                <div className="border rounded-lg p-3 bg-gray-50">
                  <div className="text-sm font-medium mb-2">Current Diagram Preview:</div>
                  <div className="max-h-64 overflow-hidden">
                    <DiagramRenderer
                      key={`ai-preview-${previewDiagram.id}-${editedCode.length}`}
                      diagram={previewDiagram}
                      config={mermaidConfig}
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="editor" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <div className="relative">
                {/* Line numbers and editor */}
                <div className="flex">
                  {/* Line numbers */}
                  <div className="bg-gray-50 border-r border-gray-200 p-4 text-sm text-gray-500 font-mono select-none">
                    {Array.from({ length: lineCount }, (_, i) => (
                      <div key={i + 1} className="leading-6">
                        {i + 1}
                      </div>
                    ))}
                  </div>
                  
                  {/* Code editor */}
                  <div className="flex-1">
                    <Textarea
                      ref={textareaRef}
                      value={editedCode}
                      onChange={(e) => handleCodeChange(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="min-h-[400px] border-0 rounded-none font-mono text-sm leading-6 resize-none focus:ring-0"
                      placeholder="Enter your Mermaid diagram code here..."
                      spellCheck={false}
                    />
                  </div>
                </div>
                
                {/* Status bar */}
                <div className="border-t border-gray-200 bg-gray-50 px-4 py-2 flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center gap-4">
                    <span>Lines: {lineCount}</span>
                    <span>Characters: {editedCode.length}</span>
                    <span>Type: {diagram.metadata.type}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {validationErrors.length === 0 ? (
                      <div className="flex items-center gap-1 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        Valid syntax
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-red-600">
                        <AlertTriangle className="h-4 w-4" />
                        {validationErrors.length} error(s)
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Editor Tips */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Editor Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-600 space-y-2">
              <div>• Use <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Ctrl+S</kbd> to save changes</div>
              <div>• Press <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Escape</kbd> to cancel editing</div>
              <div>• Check the Live Preview tab to see your changes in real-time</div>
              <div>• Validation errors will prevent saving - fix them before proceeding</div>
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>
    </div>
  );
}
