// Data Visualization and Analysis Constants

export const DATA_VIZ_CONFIG = {
  // File upload settings
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks for processing
  SUPPORTED_EXTENSIONS: ['.csv', '.xlsx', '.xls'],
  
  // Analysis settings
  SAMPLE_SIZE_FOR_PREVIEW: 100,
  MAX_COLUMNS_FOR_CORRELATION: 20,
  MIN_ROWS_FOR_ANALYSIS: 10,
  
  // Visualization settings
  DEFAULT_CHART_HEIGHT: 400,
  DEFAULT_CHART_WIDTH: 600,
  MAX_CATEGORIES_IN_PIE: 10,
  MAX_POINTS_IN_SCATTER: 10000,
  
  // Query system
  MAX_QUERY_LENGTH: 1000,
  QUERY_TIMEOUT: 30000, // 30 seconds
  MAX_CONCURRENT_QUERIES: 3,
  
  // Gemini API settings
  GEMINI_MODEL: 'gemini-2.5-pro',
  THINKING_BUDGET: 1000, // Limited thinking budget to prevent overload
  RESPONSE_MIME_TYPE: 'text/plain',

  // Rate limiting
  MIN_REQUEST_INTERVAL: 2000, // 2 seconds between requests
  MAX_RETRIES: 2
} as const;

export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
  UNSUPPORTED_FORMAT: 'Unsupported file format. Please upload CSV or Excel files.',
  INVALID_DATA: 'The uploaded file contains invalid or corrupted data',
  INSUFFICIENT_DATA: 'The file must contain at least 10 rows of data for analysis',
  NETWORK_ERROR: 'Network error occurred. Please check your connection and try again.',
  ANALYSIS_FAILED: 'Data analysis failed. Please try again or contact support.',
  QUERY_TOO_LONG: 'Query is too long. Please keep it under 1000 characters.',
  GEMINI_API_ERROR: 'Error communicating with AI service. Please try again.',
  NO_NUMERIC_COLUMNS: 'No numeric columns found for visualization',
  EMPTY_FILE: 'The uploaded file is empty or contains no valid data'
} as const;

export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  ANALYSIS_COMPLETE: 'Data analysis completed',
  VISUALIZATION_GENERATED: 'Visualization generated successfully',
  QUERY_PROCESSED: 'Query processed successfully'
} as const;

export const ANALYSIS_PROMPTS = {
  DATA_SUMMARY: `
    Analyze the provided dataset and generate a comprehensive summary including:
    1. Data structure overview (rows, columns, data types)
    2. Data quality assessment (missing values, duplicates, outliers)
    3. Basic statistical summary for numerical columns
    4. Key patterns and insights
    5. Potential data issues or recommendations
    
    Format the response as a structured analysis with clear sections.
  `,
  
  INSIGHTS_GENERATION: `
    Based on the dataset analysis, provide detailed insights including:
    1. Key findings and patterns in the data
    2. Correlation analysis between variables
    3. Distribution characteristics
    4. Anomalies or outliers
    5. Business or research implications
    6. Recommendations for further analysis
    
    Make the insights actionable and easy to understand.
  `,
  
  VISUALIZATION_SUGGESTIONS: `
    Suggest appropriate visualizations for this dataset including:
    1. Best chart types for each variable or combination
    2. Specific columns to visualize
    3. Reasoning for each visualization choice
    4. Interactive features that would be valuable
    
    Focus on visualizations that reveal insights and patterns.
  `
} as const;

export const PLOTLY_THEMES = {
  default: {
    layout: {
      font: { family: 'Inter, system-ui, sans-serif', size: 12 },
      colorway: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'],
      paper_bgcolor: 'white',
      plot_bgcolor: 'white',
      margin: { t: 60, r: 40, b: 60, l: 60 }
    }
  },
  dark: {
    layout: {
      font: { family: 'Inter, system-ui, sans-serif', size: 12, color: '#E5E7EB' },
      colorway: ['#60A5FA', '#F87171', '#34D399', '#FBBF24', '#A78BFA', '#F472B6', '#22D3EE', '#A3E635'],
      paper_bgcolor: '#1F2937',
      plot_bgcolor: '#1F2937',
      margin: { t: 60, r: 40, b: 60, l: 60 }
    }
  }
} as const;

export const CHART_CONFIGS = {
  histogram: {
    type: 'histogram',
    marker: { opacity: 0.7 },
    nbinsx: 30
  },
  scatter: {
    type: 'scatter',
    mode: 'markers',
    marker: { size: 6, opacity: 0.7 }
  },
  bar: {
    type: 'bar',
    marker: { opacity: 0.8 }
  },
  line: {
    type: 'scatter',
    mode: 'lines+markers',
    line: { width: 2 },
    marker: { size: 4 }
  },
  box: {
    type: 'box',
    boxpoints: 'outliers'
  },
  heatmap: {
    type: 'heatmap',
    colorscale: 'RdBu',
    reversescale: true
  }
} as const;

export const DATA_TYPE_DETECTION = {
  patterns: {
    integer: /^-?\d+$/,
    float: /^-?\d*\.?\d+([eE][+-]?\d+)?$/,
    date: /^\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4}/,
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    url: /^https?:\/\/.+/,
    boolean: /^(true|false|yes|no|1|0)$/i
  },
  
  thresholds: {
    numeric_ratio: 0.8, // 80% of values must be numeric to classify as numeric
    unique_ratio: 0.5,  // If >50% values are unique, likely continuous
    category_max: 20    // Max unique values for categorical classification
  }
} as const;

export const LOADING_STATES = {
  UPLOADING: 'Uploading file...',
  PARSING: 'Parsing data...',
  VALIDATING: 'Validating data...',
  ANALYZING: 'Analyzing data structure...',
  GENERATING_INSIGHTS: 'Generating insights...',
  CREATING_VISUALIZATIONS: 'Creating visualizations...',
  PROCESSING_QUERY: 'Processing your question...',
  EXECUTING_CODE: 'Executing analysis code...'
} as const;
