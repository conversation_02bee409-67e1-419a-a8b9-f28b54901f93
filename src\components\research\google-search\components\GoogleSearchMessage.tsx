/**
 * Google Search Message Component
 * Displays individual messages in the Google search conversation
 */

import React, { useState } from 'react';
import { <PERSON>r, Bot, ExternalLink, Quote, FileText, Copy, Check, ChevronDown, ChevronUp } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import ReactMarkdown from 'react-markdown';
import { toast } from 'sonner';

import {
  GoogleSearchMessage as GoogleSearchMessageType,
  GoogleSearchSource,
  GoogleCitation,
  GoogleReference
} from '../types';

interface GoogleSearchMessageProps {
  message: GoogleSearchMessageType;
  onSourceClick?: (url: string) => void;
  className?: string;
}

export function GoogleSearchMessage({
  message,
  onSourceClick,
  className = ''
}: GoogleSearchMessageProps) {
  const [copied, setCopied] = useState(false);
  const [showSources, setShowSources] = useState(false);
  const [showReferences, setShowReferences] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      toast.success('Message copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy message');
    }
  };

  const handleSourceClick = (source: GoogleSearchSource) => {
    if (onSourceClick) {
      onSourceClick(source.url);
    } else {
      window.open(source.url, '_blank');
    }
  };

  const handleCopyUrl = async (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(url);
      toast.success('URL copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  const handleCopyAllUrls = async () => {
    if (!message.sources || message.sources.length === 0) return;

    const urls = message.sources.map(source => `${source.title}: ${source.url}`).join('\n\n');
    try {
      await navigator.clipboard.writeText(urls);
      toast.success(`${message.sources.length} URLs copied to clipboard`);
    } catch (error) {
      toast.error('Failed to copy URLs');
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getSourceTypeColor = (type: string) => {
    switch (type) {
      case 'academic': return 'bg-blue-100 text-blue-800';
      case 'journal': return 'bg-green-100 text-green-800';
      case 'news': return 'bg-orange-100 text-orange-800';
      case 'book': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (message.type === 'user') {
    return (
      <div className={`flex justify-end ${className}`}>
        <div className="max-w-3xl">
          <Card className="bg-blue-600 text-white">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-1">
                  <p className="text-sm leading-relaxed">{message.content}</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4" />
                  </div>
                </div>
              </div>
              <div className="mt-2 text-xs text-blue-100 text-right">
                {formatTimestamp(message.timestamp)}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (message.type === 'system') {
    return (
      <div className={`flex justify-center ${className}`}>
        <Card className="max-w-2xl bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 text-yellow-800">
              <Bot className="h-4 w-4" />
              <p className="text-sm">{message.content}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Assistant message
  return (
    <div className={`flex justify-start ${className}`}>
      <div className="max-w-4xl w-full">
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">Google Search Assistant</div>
                  <div className="text-xs text-gray-500">
                    {formatTimestamp(message.timestamp)}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  Google Search
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-8 w-8 p-0"
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown
                components={{
                  p: ({ children }) => <p className="mb-4 leading-relaxed text-gray-800">{children}</p>,
                  h1: ({ children }) => <h1 className="text-xl font-bold mb-3 text-gray-900">{children}</h1>,
                  h2: ({ children }) => <h2 className="text-lg font-semibold mb-2 text-gray-900">{children}</h2>,
                  h3: ({ children }) => <h3 className="text-base font-medium mb-2 text-gray-900">{children}</h3>,
                  ul: ({ children }) => <ul className="list-disc list-inside mb-4 space-y-1">{children}</ul>,
                  ol: ({ children }) => <ol className="list-decimal list-inside mb-4 space-y-1">{children}</ol>,
                  li: ({ children }) => <li className="text-gray-700">{children}</li>,
                  strong: ({ children }) => <strong className="font-semibold text-gray-900">{children}</strong>,
                  em: ({ children }) => <em className="italic text-gray-700">{children}</em>,
                  code: ({ children }) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>

            {/* Sources Section - Always visible and prominent */}
            {message.sources && message.sources.length > 0 && (
              <div className="mt-6 border-t pt-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <ExternalLink className="h-5 w-5 text-blue-600" />
                    <span className="font-semibold text-gray-900 text-lg">
                      Sources ({message.sources.length})
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyAllUrls}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy URLs
                    </Button>
                    {message.sources.length > 3 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowSources(!showSources)}
                      >
                        {showSources ? (
                          <>
                            <ChevronUp className="h-4 w-4 mr-1" />
                            Hide
                          </>
                        ) : (
                          <>
                            <ChevronDown className="h-4 w-4 mr-1" />
                            Show All
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>

                {/* Always show first 3 sources */}
                <div className="grid gap-3 mb-3">
                  {message.sources.slice(0, 3).map((source, index) => (
                    <div
                      key={source.id}
                      className="p-4 border-2 border-blue-100 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-all duration-200 group"
                      onClick={() => handleSourceClick(source)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-base font-semibold text-gray-900 group-hover:text-blue-700 line-clamp-2">
                              {source.title}
                            </span>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${getSourceTypeColor(source.type)}`}
                            >
                              {source.type}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-blue-600 group-hover:text-blue-800">
                              {source.domain}
                            </span>
                            <span className="text-xs text-gray-500">
                              Score: {(source.score * 100).toFixed(0)}%
                            </span>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-3 mb-2">{source.snippet}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-xs text-blue-600 group-hover:text-blue-800 flex-1 min-w-0">
                              <ExternalLink className="h-3 w-3 mr-1 flex-shrink-0" />
                              <span className="truncate">{source.url}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleCopyUrl(source.url, e)}
                              className="h-6 w-6 p-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="ml-3 flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 group-hover:bg-blue-200 rounded-full flex items-center justify-center">
                            <ExternalLink className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Collapsible additional sources */}
                {message.sources.length > 3 && (
                  <Collapsible open={showSources} onOpenChange={setShowSources}>
                    <CollapsibleContent>
                      <div className="grid gap-3">
                        {message.sources.slice(3).map((source, index) => (
                          <div
                            key={source.id}
                            className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => handleSourceClick(source)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="text-sm font-medium text-gray-900 truncate">
                                    {source.title}
                                  </span>
                                  <Badge
                                    variant="secondary"
                                    className={`text-xs ${getSourceTypeColor(source.type)}`}
                                  >
                                    {source.type}
                                  </Badge>
                                </div>
                                <p className="text-xs text-blue-600 mb-1 truncate">{source.url}</p>
                                <p className="text-sm text-gray-700 line-clamp-2">{source.snippet}</p>
                              </div>
                              <ExternalLink className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                )}
              </div>
            )}

            {/* References Section */}
            {message.references && message.references.length > 0 && (
              <div className="mt-4 border-t pt-4">
                <Collapsible open={showReferences} onOpenChange={setShowReferences}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                      <div className="flex items-center space-x-2">
                        <Quote className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-gray-900">
                          References ({message.references.length})
                        </span>
                      </div>
                      {showReferences ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-3">
                    <div className="space-y-3">
                      {message.references.map((reference, index) => (
                        <div key={reference.id} className="text-sm">
                          <div className="flex items-start space-x-2">
                            <span className="text-gray-500 font-mono text-xs mt-0.5">
                              [{index + 1}]
                            </span>
                            <div className="flex-1">
                              <p className="text-gray-800 leading-relaxed">
                                {reference.apaFormat}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default GoogleSearchMessage;
