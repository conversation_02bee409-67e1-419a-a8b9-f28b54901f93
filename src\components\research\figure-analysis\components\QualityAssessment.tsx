/**
 * Quality Assessment Component
 * Comprehensive display of figure quality analysis results
 */

import React, { useState } from 'react';
import { 
  <PERSON><PERSON>ircle, AlertTriangle, XCircle, Info, 
  Eye, Monitor, Printer, Accessibility, Award,
  ChevronDown, ChevronRight, TrendingUp, TrendingDown
} from 'lucide-react';
import { QualityAssessment as QualityAssessmentType, QualityIssue } from '../types';
import { QUALITY_THRESHOLDS } from '../constants';

interface QualityAssessmentProps {
  assessment: QualityAssessmentType;
  className?: string;
}

export const QualityAssessment: React.FC<QualityAssessmentProps> = ({
  assessment,
  className = '',
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overall']));

  /**
   * Toggle section expansion
   */
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  /**
   * Get grade color classes
   */
  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100 border-green-200';
      case 'B': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'C': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'D': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'F': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  /**
   * Get severity icon and color
   */
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'low':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  /**
   * Get score color based on value
   */
  const getScoreColor = (score: number, thresholds?: { poor: number; acceptable: number; good: number }) => {
    if (!thresholds) {
      if (score >= 80) return 'text-green-600';
      if (score >= 60) return 'text-yellow-600';
      return 'text-red-600';
    }

    if (score >= thresholds.good) return 'text-green-600';
    if (score >= thresholds.acceptable) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * Render progress bar
   */
  const renderProgressBar = (score: number, max: number = 100) => {
    const percentage = (score / max) * 100;
    const colorClass = getScoreColor(score);
    
    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            colorClass.includes('green') ? 'bg-green-500' :
            colorClass.includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    );
  };

  /**
   * Render collapsible section
   */
  const renderSection = (
    title: string,
    icon: React.ReactNode,
    content: React.ReactNode,
    sectionKey: string,
    badge?: React.ReactNode
  ) => {
    const isExpanded = expandedSections.has(sectionKey);
    
    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
        >
          <div className="flex items-center space-x-3">
            {icon}
            <span className="font-medium text-gray-900">{title}</span>
            {badge}
          </div>
          {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </button>
        
        {isExpanded && (
          <div className="p-4 bg-white">
            {content}
          </div>
        )}
      </div>
    );
  };

  /**
   * Render issues list
   */
  const renderIssues = (issues: QualityIssue[]) => {
    if (issues.length === 0) {
      return (
        <div className="flex items-center space-x-2 text-green-600">
          <CheckCircle className="w-4 h-4" />
          <span className="text-sm">No issues detected</span>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {issues.map((issue, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            {getSeverityIcon(issue.severity)}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">{issue.description}</p>
              <p className="text-xs text-gray-600 mt-1">{issue.suggestion}</p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall Quality */}
      {renderSection(
        'Overall Quality',
        <Award className="w-5 h-5 text-purple-500" />,
        (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`px-4 py-2 rounded-lg border font-bold text-lg ${
                  getGradeColor(assessment.overall.grade)
                }`}>
                  Grade {assessment.overall.grade}
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {assessment.overall.score}/100
                </div>
              </div>
            </div>
            
            {renderProgressBar(assessment.overall.score)}
            
            <div className="mt-4">
              <h4 className="font-medium text-gray-900 mb-2">Issues & Recommendations</h4>
              {renderIssues(assessment.overall.issues)}
            </div>

            {assessment.overall.improvements.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Improvement Suggestions</h4>
                <ul className="space-y-1">
                  {assessment.overall.improvements.map((improvement, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                      <TrendingUp className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{improvement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ),
        'overall',
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(assessment.overall.grade)}`}>
          {assessment.overall.score}/100
        </span>
      )}

      {/* Resolution Analysis */}
      {renderSection(
        'Resolution & Print Quality',
        <Printer className="w-5 h-5 text-blue-500" />,
        (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">DPI</span>
                  <span className="text-sm font-bold">{assessment.resolution.dpi}</span>
                </div>
                {renderProgressBar(assessment.resolution.dpi, 600)}
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">High Resolution:</span>
                {assessment.resolution.isHighRes ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Print Quality:</span>
                <span className={`text-sm font-medium ${
                  assessment.resolution.printQuality === 'excellent' ? 'text-green-600' :
                  assessment.resolution.printQuality === 'good' ? 'text-blue-600' :
                  assessment.resolution.printQuality === 'acceptable' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {assessment.resolution.printQuality}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Web Quality:</span>
                <span className={`text-sm font-medium ${
                  assessment.resolution.webQuality === 'excellent' ? 'text-green-600' :
                  assessment.resolution.webQuality === 'good' ? 'text-blue-600' :
                  assessment.resolution.webQuality === 'acceptable' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {assessment.resolution.webQuality}
                </span>
              </div>
            </div>

            {assessment.resolution.recommendations.length > 0 && (
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-2">Recommendations</h4>
                <ul className="space-y-1">
                  {assessment.resolution.recommendations.map((rec, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                      <Info className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ),
        'resolution'
      )}

      {/* Clarity Analysis */}
      {renderSection(
        'Clarity & Visual Quality',
        <Eye className="w-5 h-5 text-green-500" />,
        (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Sharpness</span>
                  <span className="text-sm font-bold">{assessment.clarity.sharpness}/100</span>
                </div>
                {renderProgressBar(assessment.clarity.sharpness)}
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Contrast</span>
                  <span className="text-sm font-bold">{assessment.clarity.contrast}/100</span>
                </div>
                {renderProgressBar(assessment.clarity.contrast)}
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Brightness</span>
                  <span className="text-sm font-bold">{assessment.clarity.brightness}/100</span>
                </div>
                {renderProgressBar(assessment.clarity.brightness)}
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Color Balance</span>
                  <span className="text-sm font-bold">{assessment.clarity.colorBalance}/100</span>
                </div>
                {renderProgressBar(assessment.clarity.colorBalance)}
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Noise Level</span>
                  <span className="text-sm font-bold">{100 - assessment.clarity.noiseLevel}/100</span>
                </div>
                {renderProgressBar(100 - assessment.clarity.noiseLevel)}
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overall Clarity:</span>
                <span className={`text-sm font-medium ${
                  assessment.clarity.overallClarity === 'excellent' ? 'text-green-600' :
                  assessment.clarity.overallClarity === 'good' ? 'text-blue-600' :
                  assessment.clarity.overallClarity === 'fair' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {assessment.clarity.overallClarity}
                </span>
              </div>
            </div>
          </div>
        ),
        'clarity'
      )}

      {/* Readability Analysis */}
      {renderSection(
        'Text Readability',
        <Monitor className="w-5 h-5 text-indigo-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Text Detected:</span>
                  {assessment.readability.textDetected ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-gray-500" />
                  )}
                </div>
                
                {assessment.readability.textDetected && (
                  <>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">Text Clarity</span>
                        <span className="text-sm font-bold">{assessment.readability.textClarity}/100</span>
                      </div>
                      {renderProgressBar(assessment.readability.textClarity)}
                    </div>
                    
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">Font Contrast</span>
                        <span className="text-sm font-bold">{assessment.readability.fontContrast}/100</span>
                      </div>
                      {renderProgressBar(assessment.readability.fontContrast)}
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Font Size:</span>
                  <span className={`text-sm font-medium ${
                    assessment.readability.fontSize === 'optimal' ? 'text-green-600' :
                    assessment.readability.fontSize === 'acceptable' ? 'text-blue-600' : 'text-red-600'
                  }`}>
                    {assessment.readability.fontSize}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Label Visibility:</span>
                  <span className={`text-sm font-medium ${
                    assessment.readability.labelVisibility === 'excellent' ? 'text-green-600' :
                    assessment.readability.labelVisibility === 'good' ? 'text-blue-600' :
                    assessment.readability.labelVisibility === 'fair' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {assessment.readability.labelVisibility}
                  </span>
                </div>
                
                {assessment.readability.axisReadability !== undefined && (
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">Axis Readability</span>
                      <span className="text-sm font-bold">{assessment.readability.axisReadability}/100</span>
                    </div>
                    {renderProgressBar(assessment.readability.axisReadability)}
                  </div>
                )}
              </div>
            </div>
          </div>
        ),
        'readability'
      )}

      {/* Accessibility Analysis */}
      {renderSection(
        'Accessibility Compliance',
        <Accessibility className="w-5 h-5 text-purple-500" />,
        (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Color Blind Friendly:</span>
                  {assessment.accessibility.colorBlindFriendly ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">Contrast Ratio</span>
                    <span className="text-sm font-bold">{assessment.accessibility.contrastRatio.toFixed(1)}:1</span>
                  </div>
                  {renderProgressBar(Math.min(assessment.accessibility.contrastRatio, 10), 10)}
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Screen Reader Compatible:</span>
                  {assessment.accessibility.screenReaderCompatible ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">WCAG Compliance:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    assessment.accessibility.wcagCompliance === 'AAA' ? 'bg-green-100 text-green-800' :
                    assessment.accessibility.wcagCompliance === 'AA' ? 'bg-blue-100 text-blue-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {assessment.accessibility.wcagCompliance}
                  </span>
                </div>
              </div>
            </div>

            {assessment.accessibility.altTextSuggestion && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Suggested Alt Text</h4>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">{assessment.accessibility.altTextSuggestion}</p>
                </div>
              </div>
            )}

            {assessment.accessibility.improvements.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Accessibility Improvements</h4>
                <ul className="space-y-1">
                  {assessment.accessibility.improvements.map((improvement, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                      <TrendingUp className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{improvement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ),
        'accessibility'
      )}
    </div>
  );
};
