/**
 * History Panel Component
 * Displays research history with search and filtering capabilities
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Calendar, 
  FileText, 
  Download, 
  Eye, 
  Filter,
  Clock,
  Brain,
  User,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

import { ResearchHistoryItem, HistorySearchFilters, ModelVariant } from '../types';
import { supabaseService } from '../services/supabase.service';
import { AI_MODELS } from '../constants';

interface HistoryPanelProps {
  onSelectSession?: (historyItem: ResearchHistoryItem) => void;
  className?: string;
}

export const HistoryPanel: React.FC<HistoryPanelProps> = ({
  onSelectSession,
  className = '',
}) => {
  const [historyItems, setHistoryItems] = useState<ResearchHistoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<ResearchHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedModel, setSelectedModel] = useState<ModelVariant | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'completed' | 'processing' | 'error'>('all');
  const [selectedMode, setSelectedMode] = useState<'all' | 'agent' | 'manual'>('all');

  // Load history on component mount
  useEffect(() => {
    loadHistory();
  }, []);

  // Filter items when search criteria change
  useEffect(() => {
    filterItems();
  }, [historyItems, searchQuery, selectedModel, selectedStatus, selectedMode]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      
      if (!supabaseService.isConfigured()) {
        toast.error('Database not configured');
        return;
      }

      const items = await supabaseService.getResearchHistory(50, 0);
      setHistoryItems(items);
    } catch (error) {
      console.error('Failed to load history:', error);
      toast.error('Failed to load research history');
    } finally {
      setLoading(false);
    }
  };

  const filterItems = () => {
    let filtered = [...historyItems];

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.session.title.toLowerCase().includes(query) ||
        item.session.query.toLowerCase().includes(query) ||
        item.report?.title.toLowerCase().includes(query)
      );
    }

    // Model filter
    if (selectedModel !== 'all') {
      filtered = filtered.filter(item => item.session.selected_model === selectedModel);
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(item => item.session.status === selectedStatus);
    }

    // Mode filter
    if (selectedMode !== 'all') {
      const isAgent = selectedMode === 'agent';
      filtered = filtered.filter(item => item.session.is_agent_mode === isAgent);
    }

    setFilteredItems(filtered);
  };

  const handleViewSession = (item: ResearchHistoryItem) => {
    if (onSelectSession) {
      onSelectSession(item);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getModelDisplayName = (model: string) => {
    // Find the model in AI_MODELS
    for (const platform of Object.values(AI_MODELS)) {
      for (const modelConfig of Object.values(platform.models)) {
        if (modelConfig.value === model) {
          return modelConfig.label;
        }
      }
    }
    return model;
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading research history...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Research History</h2>
          <p className="text-gray-600">
            {filteredItems.length} of {historyItems.length} sessions
          </p>
        </div>
        <Button onClick={loadHistory} variant="outline" size="sm">
          <Search className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Input
              type="text"
              placeholder="Search sessions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
          </div>

          {/* Filter Controls */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select value={selectedModel} onValueChange={(value) => setSelectedModel(value as ModelVariant | 'all')}>
              <SelectTrigger>
                <SelectValue placeholder="All Models" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Models</SelectItem>
                {Object.values(AI_MODELS.openrouter.models).map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedMode} onValueChange={(value) => setSelectedMode(value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="All Modes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Modes</SelectItem>
                <SelectItem value="agent">Agent Mode</SelectItem>
                <SelectItem value="manual">Manual Mode</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* History Items */}
      <ScrollArea className="h-[600px]">
        <div className="space-y-4">
          {filteredItems.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">No Research Sessions Found</h3>
                <p className="text-gray-500">
                  {searchQuery || selectedModel !== 'all' || selectedStatus !== 'all' || selectedMode !== 'all'
                    ? 'Try adjusting your filters to see more results.'
                    : 'Start your first research session to see it here.'}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredItems.map((item) => (
              <Card key={item.session.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-800 truncate">
                          {item.session.title}
                        </h3>
                        <Badge className={getStatusColor(item.session.status)}>
                          {item.session.status}
                        </Badge>
                        {item.session.is_agent_mode && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            Agent
                          </Badge>
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {item.session.query}
                      </p>

                      <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(item.session.created_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {getModelDisplayName(item.session.selected_model)}
                        </span>
                        {item.report && (
                          <span className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {item.report.word_count} words
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {item.sources.length} sources
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {item.report && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewSession(item)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
