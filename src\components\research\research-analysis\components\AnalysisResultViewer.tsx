import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  FileText,
  BookOpen,
  Target,
  Lightbulb,
  Download,
  Share2,
  Copy,
  Eye,
  Calendar,
  User,
  BarChart3,
  TrendingUp,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Info,
  ExternalLink,
  Quote,
  Layers,
  Network,
  Zap
} from "lucide-react";

import { LiteratureReview, GapAnalysis, ResearchHypothesis, ResearchDocument } from '../types';
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface AnalysisResultViewerProps {
  result: LiteratureReview | GapAnalysis | ResearchHypothesis | null;
  type: 'literature_review' | 'gap_analysis' | 'hypothesis' | null;
  onClose: () => void;
  className?: string;
}

export function AnalysisResultViewer({ 
  result, 
  type, 
  onClose, 
  className 
}: AnalysisResultViewerProps) {
  const [activeTab, setActiveTab] = useState('overview');

  if (!result || !type) {
    return null;
  }

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Content copied to clipboard');
  };

  const handleExport = (format: 'pdf' | 'word' | 'text') => {
    // Implementation would depend on your export service
    toast.info(`Export to ${format.toUpperCase()} functionality would be implemented here`);
  };

  const renderLiteratureReview = (review: LiteratureReview) => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{review.title}</h1>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {review.generatedAt.toLocaleDateString()}
            </span>
            <span className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              {review.wordCount} words
            </span>
            <span className="flex items-center gap-1">
              <BookOpen className="h-4 w-4" />
              {review.documents.length} documents
            </span>
            <Badge variant="outline">{review.citationStyle}</Badge>
            <Badge variant={review.status === 'completed' ? 'default' : 'secondary'}>
              {review.status}
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="h-4 w-4 mr-1" />
            PDF
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport('word')}>
            <Download className="h-4 w-4 mr-1" />
            Word
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleCopyContent(review.sections.map(s => s.content).join('\n\n'))}>
            <Copy className="h-4 w-4 mr-1" />
            Copy
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sections">Sections</TabsTrigger>
          <TabsTrigger value="citations">Citations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Review Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Structure</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      {review.sections.map((section, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full" />
                          {section.title} ({section.type})
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Statistics</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Sections:</span>
                        <span className="font-medium">{review.sections.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Word Count:</span>
                        <span className="font-medium">{review.wordCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Source Documents:</span>
                        <span className="font-medium">{review.documents.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Citation Style:</span>
                        <span className="font-medium">{review.citationStyle}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sections" className="mt-6">
          <div className="space-y-4">
            {review.sections.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                    <Badge variant="outline">{section.type}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none">
                    <p className="text-gray-700 whitespace-pre-wrap">{section.content}</p>
                  </div>
                  {section.citations && section.citations.length > 0 && (
                    <div className="mt-4 pt-4 border-t">
                      <h5 className="font-medium text-gray-900 mb-2">Citations in this section:</h5>
                      <div className="space-y-1">
                        {section.citations.map((citation, citIndex) => (
                          <div key={citIndex} className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                            {citation.fullReference}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="citations" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>All Citations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {review.sections.flatMap(section => section.citations || []).map((citation, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-900 mb-1">
                      {citation.inText}
                    </div>
                    <div className="text-sm text-gray-600">
                      {citation.fullReference}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  const renderGapAnalysis = (analysis: GapAnalysis) => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Research Gap Analysis
          </h1>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {analysis.generatedAt.toLocaleDateString()}
            </span>
            <span className="flex items-center gap-1">
              <Target className="h-4 w-4" />
              {analysis.gaps.length} gaps
            </span>
            <span className="flex items-center gap-1">
              <Layers className="h-4 w-4" />
              {analysis.themes.length} themes
            </span>
            <span className="flex items-center gap-1">
              <Zap className="h-4 w-4" />
              {analysis.opportunities.length} opportunities
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleCopyContent(analysis.summary)}>
            <Copy className="h-4 w-4 mr-1" />
            Copy
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="gaps">Research Gaps</TabsTrigger>
          <TabsTrigger value="themes">Themes</TabsTrigger>
          <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Analysis Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{analysis.summary}</p>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{analysis.gaps.length}</div>
                  <div className="text-sm text-blue-800">Research Gaps</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{analysis.themes.length}</div>
                  <div className="text-sm text-purple-800">Key Themes</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{analysis.opportunities.length}</div>
                  <div className="text-sm text-green-800">Opportunities</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gaps" className="mt-6">
          <div className="space-y-4">
            {analysis.gaps.map((gap, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{gap.title}</CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={gap.priority === 'critical' ? 'destructive' : 
                                      gap.priority === 'high' ? 'default' : 'secondary'}>
                          {gap.priority} priority
                        </Badge>
                        <Badge variant="outline">{gap.category}</Badge>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-600">
                      <div>Feasibility: {gap.feasibility}/10</div>
                      <div>Impact: {gap.impact}/10</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{gap.description}</p>
                  
                  {gap.suggestedMethods.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-gray-900 mb-2">Suggested Methods:</h5>
                      <div className="flex flex-wrap gap-1">
                        {gap.suggestedMethods.map((method, methodIndex) => (
                          <Badge key={methodIndex} variant="outline" className="text-xs">
                            {method}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {gap.requiredResources.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-gray-900 mb-2">Required Resources:</h5>
                      <ul className="text-sm text-gray-600 list-disc list-inside">
                        {gap.requiredResources.map((resource, resourceIndex) => (
                          <li key={resourceIndex}>{resource}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {gap.timelineEstimate && (
                    <div className="text-sm text-gray-600">
                      <span className="font-medium">Timeline:</span> {gap.timelineEstimate}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="themes" className="mt-6">
          <div className="space-y-4">
            {analysis.themes.map((theme, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{theme.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-3">{theme.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    <span>Frequency: {theme.frequency}</span>
                    <span>Documents: {theme.relatedDocuments.length}</span>
                  </div>
                  {theme.keyTerms.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Key Terms:</h5>
                      <div className="flex flex-wrap gap-1">
                        {theme.keyTerms.map((term, termIndex) => (
                          <Badge key={termIndex} variant="outline" className="text-xs">
                            {term}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="opportunities" className="mt-6">
          <div className="space-y-4">
            {analysis.opportunities.map((opportunity, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg">{opportunity.title}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={opportunity.potentialImpact === 'transformative' ? 'default' : 'secondary'}>
                        {opportunity.potentialImpact} impact
                      </Badge>
                      <Badge variant="outline">{opportunity.difficulty}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{opportunity.description}</p>
                  
                  {opportunity.requiredExpertise.length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-medium text-gray-900 mb-2">Required Expertise:</h5>
                      <div className="flex flex-wrap gap-1">
                        {opportunity.requiredExpertise.map((expertise, expIndex) => (
                          <Badge key={expIndex} variant="outline" className="text-xs">
                            {expertise}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {opportunity.suggestedApproach.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Suggested Approach:</h5>
                      <ul className="text-sm text-gray-600 list-disc list-inside">
                        {opportunity.suggestedApproach.map((approach, approachIndex) => (
                          <li key={approachIndex}>{approach}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  const renderHypothesis = (hypothesis: ResearchHypothesis) => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Research Hypothesis</h1>
          <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
            <Badge variant={hypothesis.type === 'directional' ? 'default' : 'secondary'}>
              {hypothesis.type}
            </Badge>
            <span>Testability: {hypothesis.testability}/10</span>
            <span>Novelty: {hypothesis.novelty}/10</span>
            <span>Significance: {hypothesis.significance}/10</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleCopyContent(hypothesis.statement)}>
            <Copy className="h-4 w-4 mr-1" />
            Copy
          </Button>
        </div>
      </div>

      {/* Hypothesis Statement */}
      <Card>
        <CardHeader>
          <CardTitle>Hypothesis Statement</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
            <Quote className="h-5 w-5 text-blue-500 mb-2" />
            <p className="text-gray-900 font-medium italic">{hypothesis.statement}</p>
          </div>
        </CardContent>
      </Card>

      {/* Variables */}
      <Card>
        <CardHeader>
          <CardTitle>Variables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(hypothesis.variables).map(([type, variables]) => (
              variables && Array.isArray(variables) && variables.length > 0 && (
                <div key={type}>
                  <h4 className="font-medium text-gray-900 mb-2 capitalize">
                    {type.replace(/([A-Z])/g, ' $1').trim()} Variables
                  </h4>
                  <div className="space-y-2">
                    {variables.map((variable: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="font-medium text-gray-900">{variable.name}</div>
                        <div className="text-sm text-gray-600 mt-1">{variable.description}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          Type: {variable.type} • Measurement: {variable.measurement}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Additional Details */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Methodology</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {hypothesis.suggestedMethodology.map((method, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{method}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Expected Outcomes</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {hypothesis.expectedOutcomes.map((outcome, index) => (
                <li key={index} className="flex items-start gap-2">
                  <TrendingUp className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{outcome}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Limitations</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {hypothesis.limitations.map((limitation, index) => (
                <li key={index} className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{limitation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Required Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {hypothesis.requiredResources.map((resource, index) => (
                <li key={index} className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{resource}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {hypothesis.timeline && (
        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-gray-700">{hypothesis.timeline}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <Dialog open={!!result} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              {type === 'literature_review' && <BookOpen className="h-5 w-5" />}
              {type === 'gap_analysis' && <Target className="h-5 w-5" />}
              {type === 'hypothesis' && <Lightbulb className="h-5 w-5" />}
              Analysis Result
            </DialogTitle>
          </div>
        </DialogHeader>
        
        <ScrollArea className="h-[calc(90vh-120px)]">
          <div className={cn("p-6", className)}>
            {type === 'literature_review' && renderLiteratureReview(result as LiteratureReview)}
            {type === 'gap_analysis' && renderGapAnalysis(result as GapAnalysis)}
            {type === 'hypothesis' && renderHypothesis(result as ResearchHypothesis)}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
