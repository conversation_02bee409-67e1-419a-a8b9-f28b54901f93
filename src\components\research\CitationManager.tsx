
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  Search, 
  Download, 
  Upload, 
  BookOpen, 
  Edit, 
  Trash2,
  Co<PERSON>,
  Filter
} from "lucide-react";

interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  doi?: string;
  type: "journal" | "book" | "conference" | "website";
  tags: string[];
}

export function CitationManager() {
  const [searchQuery, setSearchQuery] = useState("");
  const [citations] = useState<Citation[]>([
    {
      id: "1",
      title: "Deep Learning Approaches for Climate Change Prediction",
      authors: ["<PERSON>, J<PERSON>", "Johnson, M.", "Williams, K."],
      year: 2023,
      source: "Nature Climate Change",
      doi: "10.1000/xyz123",
      type: "journal",
      tags: ["machine learning", "climate", "prediction"]
    },
    {
      id: "2",
      title: "Machine Learning for Environmental Data Analysis",
      authors: ["<PERSON>, <PERSON>", "<PERSON>, <PERSON>"],
      year: 2023,
      source: "Environmental Science & Technology",
      doi: "10.1000/abc456",
      type: "journal",
      tags: ["data analysis", "environment", "ML"]
    },
    {
      id: "3",
      title: "Artificial Intelligence in Research Methods",
      authors: ["Wilson, P.", "Clark, S.", "Taylor, M."],
      year: 2022,
      source: "Academic Press",
      type: "book",
      tags: ["AI", "research methods", "methodology"]
    }
  ]);

  const [selectedStyle, setSelectedStyle] = useState("APA 7th Edition");

  const formatCitation = (citation: Citation, style: string) => {
    // Simple APA formatting for demo
    const authors = citation.authors.join(", ");
    return `${authors} (${citation.year}). ${citation.title}. *${citation.source}*${citation.doi ? `. https://doi.org/${citation.doi}` : ''}`;
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Citation Manager</h1>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import BibTeX
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Citation
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search citations..."
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <select
            value={selectedStyle}
            onChange={(e) => setSelectedStyle(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option>APA 7th Edition</option>
            <option>MLA 9th Edition</option>
            <option>Chicago 17th Edition</option>
            <option>IEEE</option>
            <option>Harvard</option>
          </select>
        </div>
      </div>

      {/* Citations List */}
      <div className="flex-1 overflow-auto p-6">
        <div className="mb-4">
          <p className="text-sm text-gray-600">
            {citations.length} citations in your library
          </p>
        </div>

        <div className="space-y-4">
          {citations.map((citation) => (
            <Card key={citation.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">
                      {citation.title}
                    </CardTitle>
                    <div className="text-sm text-gray-600 mb-2">
                      {citation.authors.join(", ")} • {citation.year} • {citation.source}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {citation.type}
                      </Badge>
                      {citation.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-3 rounded text-sm font-mono">
                  {formatCitation(citation, selectedStyle)}
                </div>
                <div className="mt-3 flex space-x-2">
                  <Button variant="outline" size="sm">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Insert Citation
                  </Button>
                  <Button variant="outline" size="sm">
                    Copy Formatted
                  </Button>
                  {citation.doi && (
                    <Button variant="outline" size="sm">
                      View Paper
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
