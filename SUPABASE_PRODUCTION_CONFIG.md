# Supabase Production Configuration for verbira.com

## 🚨 URGENT: Update Supabase Settings for Production

You need to update your Supabase project configuration to support both development and production domains.

### 1. Update Supabase Authentication Settings

Go to: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/settings

#### Site URL
Update the **Site URL** to your production domain:
```
https://verbira.com
```

#### Redirect URLs
Add both development and production URLs to **Redirect URLs**:
```
http://localhost:8081/auth/callback
http://localhost:8081/**
https://verbira.com/auth/callback
https://verbira.com/**
```

### 2. Update Google Cloud Console

Go to: https://console.cloud.google.com/apis/credentials

#### Update OAuth 2.0 Client ID
Edit your existing OAuth 2.0 Client ID and update:

**Authorized JavaScript origins:**
```
http://localhost:8081
https://verbira.com
https://swsnqpavwcnqiihsidss.supabase.co
```

**Authorized redirect URIs:**
```
https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback
http://localhost:8081/auth/callback
https://verbira.com/auth/callback
```

### 3. Code Changes Made

✅ **Updated `src/lib/supabase.ts`:**
- Added `getBaseUrl()` function that returns `https://verbira.com` in production
- Updated Google OAuth redirect to use correct domain
- Updated email signup redirect to use correct domain
- Added logging for debugging

### 4. Testing After Configuration

#### Development (localhost:8081)
- Should redirect to: `http://localhost:8081/auth/callback`
- Then redirect to: `http://localhost:8081/app`

#### Production (verbira.com)
- Should redirect to: `https://verbira.com/auth/callback`
- Then redirect to: `https://verbira.com/app`

### 5. Deployment Checklist

- [ ] Update Supabase Site URL to `https://verbira.com`
- [ ] Add production redirect URLs to Supabase
- [ ] Update Google Cloud Console OAuth settings
- [ ] Deploy updated code to verbira.com
- [ ] Test authentication flow on production

### 6. Quick Fix Commands

After updating Supabase settings, your authentication should work correctly on verbira.com.

The code changes ensure that:
1. **Development**: Uses `http://localhost:8081` for redirects
2. **Production**: Uses `https://verbira.com` for redirects

This will fix the issue where users were being redirected to `localhost:8081` after authentication on verbira.com.
