import { LucideIcon } from "lucide-react";

// Paper structure and content types
export interface PaperMetadata {
  title: string;
  researchField: string;
  keywords: string[];
  authors: string[];
}

export interface ContentItem {
  id: string;
  type: 'text' | 'figure';
  content: string;
  order: number;
  title?: string;
  caption?: string;
  aiAnalysis?: string;
  analysis?: string; // Alternative property name for analysis
  base64Data?: string; // For AI image analysis
  originalFile?: File; // Store original file for base64 conversion
}

export interface UserSection {
  id: string;
  name: string;
  items: ContentItem[];
}

export interface UserInputs {
  metadata: PaperMetadata;
  userSections: UserSection[];
}

export interface GeneratedSection {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  icon: LucideIcon;
  content?: string;
  citations?: string[];     // Array of citation IDs found in this section
}

export interface SectionType {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
  description: string;
  order: number;
  required?: boolean;
}

// AI generation types
export interface AIModelOption {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  maxTokens: number;
}

export interface AIGenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  stopSequences?: string[];
}

export interface AIAnalysisResult {
  content: string;
  confidence: number;
  tokens: number;
}

/**
 * Structure for a citation extracted from paper content
 */
export interface Citation {
  id: string;           // Unique ID for the citation
  inTextFormat: string; // How it appears in text, e.g., "(Smith, 2023)"
  authors: string[];    // List of author names
  year: number;         // Publication year
  title: string;        // Title of the work (when available)
  source: string;       // Journal/conference/book name (when available)
  doi?: string;         // DOI if available
  url?: string;         // URL if available
  sectionIds: string[]; // IDs of sections where this citation appears
  referenceText?: string; // Full formatted reference text
}

// Database types for paper generation
export interface PaperGeneration {
  id: string;
  user_id: string;
  title: string;
  research_field?: string;
  keywords: string[];
  authors: string[];
  ai_model: string;
  status: 'draft' | 'generating' | 'completed' | 'error';
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PaperSection {
  id: string;
  paper_generation_id: string;
  section_id: string;
  section_name: string;
  prompt_text: string;
  generated_content?: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  generation_order: number;
  ai_model: string;
  generation_metadata: Record<string, any>;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface PaperCitation {
  id: string;
  paper_generation_id: string;
  section_id?: string;
  citation_id: string;
  in_text_format: string;
  authors: string[];
  year: number;
  reference_text?: string;
  section_ids: string[];
  created_at: string;
}

export interface PaperGenerationInsert {
  user_id: string;
  title: string;
  research_field?: string;
  keywords?: string[];
  authors?: string[];
  ai_model: string;
  status?: 'draft' | 'generating' | 'completed' | 'error';
  metadata?: Record<string, any>;
}

export interface PaperSectionInsert {
  paper_generation_id: string;
  section_id: string;
  section_name: string;
  prompt_text: string;
  generated_content?: string;
  status?: 'pending' | 'generating' | 'completed' | 'error';
  generation_order?: number;
  ai_model: string;
  generation_metadata?: Record<string, any>;
  error_message?: string;
}

export interface PaperCitationInsert {
  paper_generation_id: string;
  section_id?: string;
  citation_id: string;
  in_text_format: string;
  authors: string[];
  year: number;
  reference_text?: string;
  section_ids?: string[];
}
