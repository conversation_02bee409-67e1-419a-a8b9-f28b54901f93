/**
 * ChangeTrackingAdapter - Integrates the agentic system with the existing change tracking
 */

import { ChangeTrackingIntegration, EditResult } from './types';
import { changeTrackingService } from '../../change-tracking';

export class ChangeTrackingAdapter implements ChangeTrackingIntegration {
  /**
   * Record changes from agentic edits in the change tracking system
   */
  recordChanges(edits: EditResult[], prompt: string): string[] {
    const changeIds: string[] = [];

    console.log(`📝 Recording ${edits.length} agentic edits in change tracking system`);

    edits.forEach((edit, index) => {
      try {
        const changeId = changeTrackingService.recordChange(
          edit.originalText,
          edit.newText,
          edit.startPosition,
          edit.endPosition,
          this.mapEditTypeToActionType(edit.editType),
          `${prompt} (Edit ${index + 1}/${edits.length})`
        );

        changeIds.push(changeId);
        
        console.log(`✅ Recorded change ${index + 1}: ${changeId}`);
      } catch (error: any) {
        console.error(`❌ Failed to record change ${index + 1}:`, error);
      }
    });

    return changeIds;
  }

  /**
   * Preview changes by temporarily recording them and switching to diff view
   */
  previewChanges(edits: EditResult[]): void {
    console.log(`👁️ Previewing ${edits.length} agentic edits`);

    try {
      const state = changeTrackingService.getState();

      // Ensure tracking is enabled
      if (!state.isTrackingEnabled) {
        console.log('🔄 Starting change tracking for preview');
        changeTrackingService.startTracking(state.currentContent || '');
      }

      // Record changes as preview (they can be rejected later)
      edits.forEach((edit, index) => {
        changeTrackingService.recordChange(
          edit.originalText,
          edit.newText,
          edit.startPosition,
          edit.endPosition,
          this.mapEditTypeToActionType(edit.editType),
          `Preview: Agentic Edit ${index + 1}/${edits.length}`
        );
      });

      // Switch to diff view to show the preview
      if (state.viewMode !== 'diff') {
        changeTrackingService.toggleViewMode();
      }

      console.log(`✅ Preview mode activated with ${edits.length} changes`);
    } catch (error: any) {
      console.error('❌ Failed to preview changes:', error);
    }
  }

  /**
   * Apply previously recorded changes
   */
  applyChanges(changeIds: string[]): void {
    console.log(`✅ Applying ${changeIds.length} changes:`, changeIds);

    let appliedCount = 0;
    changeIds.forEach(changeId => {
      try {
        changeTrackingService.acceptChange(changeId);
        appliedCount++;
        console.log(`✅ Applied change: ${changeId}`);
      } catch (error: any) {
        console.error(`❌ Failed to apply change ${changeId}:`, error);
      }
    });

    // Switch back to normal view after applying
    const state = changeTrackingService.getState();
    if (state.viewMode === 'diff') {
      changeTrackingService.toggleViewMode();
    }

    console.log(`✅ Applied ${appliedCount}/${changeIds.length} changes successfully`);
  }

  /**
   * Reject previously recorded changes
   */
  rejectChanges(changeIds: string[]): void {
    console.log(`❌ Rejecting ${changeIds.length} changes:`, changeIds);

    let rejectedCount = 0;
    changeIds.forEach(changeId => {
      try {
        changeTrackingService.rejectChange(changeId);
        rejectedCount++;
        console.log(`❌ Rejected change: ${changeId}`);
      } catch (error: any) {
        console.error(`❌ Failed to reject change ${changeId}:`, error);
      }
    });

    // Switch back to normal view after rejecting
    const state = changeTrackingService.getState();
    if (state.viewMode === 'diff') {
      changeTrackingService.toggleViewMode();
    }

    console.log(`❌ Rejected ${rejectedCount}/${changeIds.length} changes successfully`);
  }

  /**
   * Record batch changes for better performance
   */
  recordBatchChanges(edits: EditResult[], prompt: string): string[] {
    if (!changeTrackingService.getState().isTrackingEnabled) {
      console.warn('⚠️ Change tracking is not enabled');
      return [];
    }

    console.log(`📦 Recording batch of ${edits.length} agentic edits`);

    // Sort edits by position to avoid conflicts
    const sortedEdits = [...edits].sort((a, b) => a.startPosition - b.startPosition);
    
    return this.recordChanges(sortedEdits, prompt);
  }

  /**
   * Get the current state of change tracking
   */
  getChangeTrackingState() {
    return changeTrackingService.getState();
  }

  /**
   * Check if change tracking is enabled
   */
  isChangeTrackingEnabled(): boolean {
    return changeTrackingService.getState().isTrackingEnabled;
  }

  /**
   * Start change tracking if not already enabled
   */
  ensureChangeTrackingEnabled(documentContent: string): boolean {
    const state = changeTrackingService.getState();
    
    if (!state.isTrackingEnabled) {
      console.log('🔄 Starting change tracking for agentic edits');
      changeTrackingService.startTracking(documentContent);
      return true;
    }
    
    return false;
  }

  /**
   * Map agentic edit types to change tracking action types
   */
  private mapEditTypeToActionType(editType: string): 'replace' | 'insert' | 'display' {
    switch (editType) {
      case 'insert':
        return 'insert';
      case 'replace':
      case 'enhance':
      case 'rewrite':
        return 'replace';
      default:
        return 'replace';
    }
  }

  /**
   * Create a preview of changes without recording them
   */
  createPreview(edits: EditResult[]): {
    previewContent: string;
    changesSummary: string;
  } {
    let changesSummary = `${edits.length} change(s) ready to apply:\n`;
    
    edits.forEach((edit, index) => {
      const changeType = edit.editType.charAt(0).toUpperCase() + edit.editType.slice(1);
      const preview = edit.newText.length > 50 
        ? edit.newText.substring(0, 50) + '...'
        : edit.newText;
      
      changesSummary += `${index + 1}. ${changeType}: "${preview}"\n`;
    });

    // For preview content, we would typically apply changes to a copy of the document
    // This is a simplified version
    const previewContent = this.applyEditsToContent('', edits);

    return {
      previewContent,
      changesSummary
    };
  }

  /**
   * Apply edits to content for preview purposes
   */
  private applyEditsToContent(originalContent: string, edits: EditResult[]): string {
    // Sort edits by position in reverse order to avoid position shifts
    const sortedEdits = [...edits].sort((a, b) => b.startPosition - a.startPosition);
    
    let content = originalContent;
    
    sortedEdits.forEach(edit => {
      const before = content.substring(0, edit.startPosition);
      const after = content.substring(edit.endPosition);
      content = before + edit.newText + after;
    });

    return content;
  }

  /**
   * Validate that edits don't conflict with existing changes
   */
  validateEditsCompatibility(edits: EditResult[]): {
    isCompatible: boolean;
    conflicts: string[];
    warnings: string[];
  } {
    const state = changeTrackingService.getState();
    const existingChanges = state.changes.filter(change => change.status === 'pending');
    
    const conflicts: string[] = [];
    const warnings: string[] = [];

    // Check for overlapping positions
    edits.forEach((edit, editIndex) => {
      existingChanges.forEach(existingChange => {
        const editStart = edit.startPosition;
        const editEnd = edit.endPosition;
        const existingStart = existingChange.startPosition;
        const existingEnd = existingChange.endPosition;

        // Check for overlap
        if (!(editEnd <= existingStart || editStart >= existingEnd)) {
          conflicts.push(
            `Edit ${editIndex + 1} overlaps with existing change at position ${existingStart}-${existingEnd}`
          );
        }
      });

      // Check for very large changes that might be problematic
      const changeRatio = edit.newText.length / Math.max(edit.originalText.length, 1);
      if (changeRatio > 5) {
        warnings.push(
          `Edit ${editIndex + 1} significantly increases content size (${(changeRatio * 100).toFixed(0)}%)`
        );
      }
    });

    return {
      isCompatible: conflicts.length === 0,
      conflicts,
      warnings
    };
  }

  /**
   * Get statistics about recorded changes
   */
  getChangeStatistics(): {
    totalChanges: number;
    pendingChanges: number;
    acceptedChanges: number;
    rejectedChanges: number;
    agenticChanges: number;
  } {
    const state = changeTrackingService.getState();
    const changes = state.changes;

    const agenticChanges = changes.filter(change => 
      change.prompt?.includes('Agentic Edit') || 
      change.prompt?.includes('Agent Mode')
    );

    return {
      totalChanges: changes.length,
      pendingChanges: changes.filter(c => c.status === 'pending').length,
      acceptedChanges: changes.filter(c => c.status === 'accepted').length,
      rejectedChanges: changes.filter(c => c.status === 'rejected').length,
      agenticChanges: agenticChanges.length
    };
  }

  /**
   * Clear all agentic changes
   */
  clearAgenticChanges(): number {
    const state = changeTrackingService.getState();
    const agenticChanges = state.changes.filter(change => 
      change.prompt?.includes('Agentic Edit') || 
      change.prompt?.includes('Agent Mode')
    );

    let clearedCount = 0;
    agenticChanges.forEach(change => {
      try {
        changeTrackingService.rejectChange(change.id);
        clearedCount++;
      } catch (error: any) {
        console.error(`Failed to clear agentic change ${change.id}:`, error);
      }
    });

    console.log(`🧹 Cleared ${clearedCount} agentic changes`);
    return clearedCount;
  }
}

// Create singleton instance
export const changeTrackingAdapter = new ChangeTrackingAdapter();
