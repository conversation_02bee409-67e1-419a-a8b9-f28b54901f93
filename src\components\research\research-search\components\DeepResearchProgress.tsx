/**
 * Deep Research Progress Component
 * Shows step-by-step progress of deep research execution
 */

import React, { useState } from 'react';
import { 
  CheckCircle, 
  Clock, 
  Search, 
  FileText, 
  AlertCircle, 
  Loader2,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  BookOpen,
  Target,
  TrendingUp
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

import {
  DeepResearchSession,
  DeepResearchProgress as ProgressType,
  CompletedPoint,
  ResearchReference
} from '../types';

interface DeepResearchProgressProps {
  session: DeepResearchSession;
  currentProgress?: ProgressType;
  onViewPoint?: (pointId: string) => void;
  onViewReference?: (reference: ResearchReference) => void;
  className?: string;
}

export function DeepResearchProgress({
  session,
  currentProgress,
  onViewPoint,
  onViewReference,
  className
}: DeepResearchProgressProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [showReferences, setShowReferences] = useState(false);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const getStatusIcon = (status: ProgressType['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'searching':
      case 'analyzing':
      case 'writing':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: ProgressType['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 border-green-200 text-green-800';
      case 'searching':
      case 'analyzing':
      case 'writing':
        return 'bg-blue-100 border-blue-200 text-blue-800';
      case 'error':
        return 'bg-red-100 border-red-200 text-red-800';
      default:
        return 'bg-gray-100 border-gray-200 text-gray-600';
    }
  };

  const overallProgress = session.totalPoints > 0
    ? Math.round((session.completedPoints.length / session.totalPoints) * 100)
    : 0;

  const completedCount = session.completedPoints.length;
  const academicReferences = session.allReferences.filter(ref => ref.isAcademic).length;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Progress Header */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-blue-900">Deep Research Progress</CardTitle>
                <p className="text-sm text-blue-700 mt-1">
                  {session.status === 'completed' 
                    ? 'Research completed successfully' 
                    : `Researching: ${session.outline.title}`
                  }
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-900">{overallProgress}%</div>
              <div className="text-xs text-blue-700">
                {completedCount} of {session.totalPoints} points
              </div>
            </div>
          </div>
          
          <div className="mt-4">
            <Progress value={overallProgress} className="h-2" />
          </div>

          <div className="flex items-center gap-4 mt-3 text-sm">
            <div className="flex items-center gap-1 text-blue-700">
              <FileText className="w-4 h-4" />
              {session.completedPoints.length} points completed
            </div>
            <div className="flex items-center gap-1 text-blue-700">
              <BookOpen className="w-4 h-4" />
              {session.allReferences.length} references ({academicReferences} academic)
            </div>
            <div className="flex items-center gap-1 text-blue-700">
              <Target className="w-4 h-4" />
              {session.aiAssistants.filter(a => a.status === 'active').length} AI assistants working
            </div>
            {session.status === 'completed' && (
              <div className="flex items-center gap-1 text-green-700">
                <Target className="w-4 h-4" />
                Final report ready
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Current Progress */}
      {currentProgress && session.status === 'researching' && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {getStatusIcon(currentProgress.status)}
              <div className="flex-1">
                <div className="font-medium text-orange-900">{currentProgress.message}</div>
                <div className="text-sm text-orange-700 mt-1">
                  Point {currentProgress.pointNumber} of {session.totalPoints}: {currentProgress.pointTitle}
                </div>
                <div className="text-xs text-orange-600 mt-1">
                  AI Assistant: {currentProgress.assistantName}
                  {currentProgress.currentSubpoint && ` • ${currentProgress.currentSubpoint}`}
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold text-orange-900">{currentProgress.progress}%</div>
              </div>
            </div>
            <Progress value={currentProgress.progress} className="mt-3 h-1" />
          </CardContent>
        </Card>
      )}

      {/* Progress Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Research Points Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {session.outline.points.map((point, index) => {
                const assistant = session.aiAssistants.find(a => a.assignedPointId === point.id);
                const completedPoint = session.completedPoints.find(cp => cp.pointId === point.id);
                const isExpanded = expandedSections.has(point.id);

                return (
                  <Card key={point.id} className={cn(
                    "border transition-all duration-200",
                    assistant?.status === 'completed' ? 'border-green-200 bg-green-50' :
                    assistant?.status === 'error' ? 'border-red-200 bg-red-50' :
                    assistant?.status === 'active' ? 'border-blue-200 bg-blue-50' :
                    'border-gray-200'
                  )}>
                    <Collapsible open={isExpanded} onOpenChange={() => toggleSection(point.id)}>
                      <CollapsibleTrigger asChild>
                        <div className="p-4 cursor-pointer hover:bg-gray-50/50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getStatusIcon(assistant?.status || 'pending')}
                              <div>
                                <div className="font-medium">
                                  Point {point.pointNumber}. {point.title}
                                </div>
                                <div className="text-sm text-gray-600 mt-1">
                                  {point.description}
                                </div>
                                {assistant && (
                                  <div className="text-xs text-blue-600 mt-1">
                                    AI Assistant: {assistant.name} • {assistant.specialization}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {assistant?.status && (
                                <Badge className={getStatusColor(assistant.status)}>
                                  {assistant.status}
                                </Badge>
                              )}
                              {assistant?.progress > 0 && assistant?.status === 'active' && (
                                <Badge variant="outline" className="text-xs">
                                  {assistant.progress}%
                                </Badge>
                              )}
                              {isExpanded ? (
                                <ChevronDown className="w-4 h-4 text-gray-400" />
                              ) : (
                                <ChevronRight className="w-4 h-4 text-gray-400" />
                              )}
                            </div>
                          </div>
                        </div>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <div className="px-4 pb-4 border-t border-gray-100">
                          {/* Point Details */}
                          <div className="mt-3 space-y-3">
                            {/* Subpoints */}
                            <div>
                              <span className="font-medium text-gray-700">Subpoints to Research:</span>
                              <div className="mt-2 space-y-2">
                                {point.subpoints.map((subpoint, spIndex) => (
                                  <div key={subpoint.id} className="bg-blue-50 p-2 rounded">
                                    <div className="text-sm font-medium text-blue-800">
                                      {subpoint.subpointNumber}. {subpoint.title}
                                    </div>
                                    <div className="text-xs text-blue-600 mt-1">
                                      {subpoint.description}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* AI Assistant Info */}
                            {assistant && (
                              <div className="bg-purple-50 p-3 rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium text-purple-800">AI Assistant</span>
                                  <Badge variant="outline" className="bg-white">
                                    {assistant.status}
                                  </Badge>
                                </div>
                                <div className="text-sm text-purple-700">
                                  <div><strong>{assistant.name}</strong></div>
                                  <div className="text-xs mt-1">{assistant.specialization}</div>
                                  {assistant.currentTask && (
                                    <div className="text-xs mt-1 italic">Current: {assistant.currentTask}</div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Completed Point Info */}
                            {completedPoint && (
                              <div className="bg-white rounded-lg p-3 border border-green-200">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium text-green-800">Point Completed</span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => onViewPoint?.(point.id)}
                                  >
                                    View Content
                                  </Button>
                                </div>
                                <div className="grid grid-cols-3 gap-4 text-sm">
                                  <div>
                                    <span className="text-gray-600">Word Count:</span>
                                    <div className="font-medium">{completedPoint.wordCount}</div>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Sources:</span>
                                    <div className="font-medium">{completedPoint.sources.length}</div>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Citations:</span>
                                    <div className="font-medium">{completedPoint.citations.length}</div>
                                  </div>
                                </div>

                                {/* Key Findings */}
                                {completedPoint.keyFindings.length > 0 && (
                                  <div className="mt-2">
                                    <span className="text-sm font-medium text-gray-700">Key Findings:</span>
                                    <div className="mt-1 space-y-1">
                                      {completedPoint.keyFindings.slice(0, 2).map((finding, fIndex) => (
                                        <div key={fIndex} className="text-xs text-gray-600">
                                          • {finding}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {/* Subpoint Results */}
                                {completedPoint.subpointResults.length > 0 && (
                                  <div className="mt-2">
                                    <span className="text-sm font-medium text-gray-700">Subpoint Research:</span>
                                    <div className="mt-1 space-y-1">
                                      {completedPoint.subpointResults.map((subResult, srIndex) => (
                                        <div key={srIndex} className="text-xs">
                                          <span className="font-medium">{subResult.title}:</span>
                                          <span className="text-gray-600 ml-1">
                                            {subResult.sources.length} sources, {subResult.keyInsights.length} insights
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Assistant Progress Info */}
                            {assistant && assistant.status !== 'waiting' && (
                              <div className="text-xs text-gray-500">
                                {assistant.startTime && (
                                  <div>Started: {assistant.startTime.toLocaleTimeString()}</div>
                                )}
                                {assistant.endTime && (
                                  <div>Completed: {assistant.endTime.toLocaleTimeString()}</div>
                                )}
                                {assistant.status === 'active' && (
                                  <div>Progress: {assistant.progress}%</div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* References Panel */}
      {session.allReferences.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Research References</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowReferences(!showReferences)}
              >
                {showReferences ? 'Hide' : 'Show'} References ({session.allReferences.length})
              </Button>
            </div>
          </CardHeader>

          {showReferences && (
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {session.allReferences.map((reference) => (
                    <div
                      key={reference.id}
                      className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                      onClick={() => onViewReference?.(reference)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{reference.title}</div>
                          <div className="text-xs text-gray-600 mt-1">{reference.domain}</div>
                          {reference.publishedDate && (
                            <div className="text-xs text-gray-500">
                              Published: {reference.publishedDate}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2 ml-2">
                          <Badge variant={reference.isAcademic ? 'default' : 'secondary'} className="text-xs">
                            {reference.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {reference.citationCount}x
                          </Badge>
                          <ExternalLink className="w-3 h-3 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
}
