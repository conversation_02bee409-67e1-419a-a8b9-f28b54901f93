/**
 * Document Processing Service
 * Handles PDF/DOC upload, parsing, content extraction, and metadata analysis
 * for the Research Comprehension Platform
 */

import { ResearchDocument, DocumentSection, DocumentMetadata } from '../types';

interface ProcessingProgress {
  stage: 'uploading' | 'extracting' | 'analyzing' | 'chunking' | 'complete';
  progress: number; // 0-100
  message: string;
}

interface ProcessingOptions {
  extractMetadata: boolean;
  generateSections: boolean;
  analyzeContent: boolean;
  chunkSize: number;
  onProgress?: (progress: ProcessingProgress) => void;
}

class DocumentProcessorService {
  private readonly SUPPORTED_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];

  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

  /**
   * Check if file type is supported
   */
  isSupportedFileType(file: File): boolean {
    return this.SUPPORTED_TYPES.includes(file.type);
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    if (!this.isSupportedFileType(file)) {
      return {
        isValid: false,
        error: `Unsupported file type: ${file.type}. Supported types: PDF, DOC, DOCX, TXT`
      };
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    return { isValid: true };
  }

  /**
   * Process uploaded document
   */
  async processDocument(
    file: File,
    userId: string,
    options: Partial<ProcessingOptions> = {}
  ): Promise<ResearchDocument> {
    const defaultOptions: ProcessingOptions = {
      extractMetadata: true,
      generateSections: true,
      analyzeContent: true,
      chunkSize: 1000,
      ...options
    };

    // Validate file
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    try {
      // Stage 1: Upload and extract text
      defaultOptions.onProgress?.({
        stage: 'extracting',
        progress: 10,
        message: 'Extracting text from document...'
      });

      const extractedText = await this.extractTextFromFile(file);

      // Stage 2: Analyze content and extract metadata
      defaultOptions.onProgress?.({
        stage: 'analyzing',
        progress: 40,
        message: 'Analyzing document content...'
      });

      const metadata = defaultOptions.extractMetadata
        ? await this.extractMetadata(extractedText, file.name)
        : this.createBasicMetadata();

      // Stage 3: Generate sections
      defaultOptions.onProgress?.({
        stage: 'analyzing',
        progress: 70,
        message: 'Identifying document sections...'
      });

      const sections = defaultOptions.generateSections
        ? await this.generateSections(extractedText)
        : this.createBasicSections(extractedText);

      // Stage 4: Create document object
      defaultOptions.onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Document processing complete!'
      });

      const document: ResearchDocument = {
        id: crypto.randomUUID(),
        title: metadata.title || this.extractTitleFromFilename(file.name),
        authors: metadata.authors || [],
        abstract: metadata.abstract || this.extractAbstract(extractedText),
        content: extractedText,
        sections,
        metadata: {
          ...metadata,
          extractedAt: new Date(),
          confidence: 0.8, // Default confidence
          sources: ['pdf-extract', 'text-analysis'],
          language: 'english'
        },
        uploadedAt: new Date(),
        processedAt: new Date(),
        userId,
        fileSize: file.size,
        fileType: file.type
      };

      return document;

    } catch (error) {
      console.error('Document processing failed:', error);
      throw new Error(`Failed to process document: ${error.message}`);
    }
  }

  /**
   * Extract text content from file based on type
   */
  private async extractTextFromFile(file: File): Promise<string> {
    switch (file.type) {
      case 'application/pdf':
        return this.extractTextFromPDF(file);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractTextFromWord(file);
      case 'text/plain':
        return this.extractTextFromPlainText(file);
      default:
        throw new Error(`Unsupported file type: ${file.type}`);
    }
  }

  /**
   * Extract text from PDF file with enhanced error handling
   */
  private async extractTextFromPDF(file: File): Promise<string> {
    try {
      // Dynamic import to avoid loading PDF.js unless needed
      const pdfjsLib = await import('pdfjs-dist');

      // Configure worker - try multiple worker sources
      try {
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';
      } catch (workerError) {
        console.warn('Primary worker failed, trying CDN fallback');
        pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
      }

      const arrayBuffer = await file.arrayBuffer();
      const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
      const pdf = await loadingTask.promise;

      let fullText = '';
      const numPages = pdf.numPages;

      console.log(`Processing PDF: ${file.name} with ${numPages} pages`);

      // Extract text from each page with improved error handling
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          // Validate textContent before processing
          if (textContent && Array.isArray(textContent.items)) {
            const pageText = textContent.items
              .map((item: any) => (item && 'str' in item ? item.str : ''))
              .filter(Boolean)
              .join(' ');

            if (pageText.trim()) {
              fullText += `\n--- Page ${pageNum} ---\n${pageText}\n`;
            }
          }
        } catch (pageError) {
          console.warn(`Error extracting text from page ${pageNum}:`, pageError);
          fullText += `\n--- Page ${pageNum} ---\n[Error extracting text from this page]\n`;
        }
      }

      // Clean up the extracted text
      fullText = fullText
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n\n')
        .trim();

      if (!fullText || fullText.length < 50) {
        return `[PDF Document: ${file.name}]

This PDF document appears to be image-based or contains no extractable text.
For scanned documents, OCR (Optical Character Recognition) would be required.

File Information:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Pages: ${numPages}
- Type: PDF Document

Please try uploading a text-based PDF or convert this document to a text format.`;
      }

      // Return extracted text with metadata
      return `[PDF Document: ${file.name}]
Pages: ${numPages}
File Size: ${(file.size / 1024 / 1024).toFixed(2)} MB

--- EXTRACTED CONTENT ---

${fullText}`;

    } catch (error: any) {
      console.error('PDF extraction error:', error);

      // Fallback to basic extraction
      try {
        return await this.extractTextFromPDFBasic(file);
      } catch (fallbackError) {
        console.error('Fallback PDF extraction also failed:', fallbackError);

        return `[PDF Document: ${file.name}]

PDF text extraction encountered an error: ${error.message}

This could be due to:
- Password-protected PDF
- Corrupted file
- Unsupported PDF format
- Resource limitations

File Information:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB

Please try a different PDF document or convert it to a supported format.`;
      }
    }
  }

  /**
   * Basic PDF text extraction fallback
   */
  private async extractTextFromPDFBasic(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);
          const decoder = new TextDecoder('utf-8', { fatal: false });
          const pdfString = decoder.decode(uint8Array);

          // Extract text between common PDF text markers
          const textMatches = pdfString.match(/\((.*?)\)/g) || [];
          let text = '';

          textMatches.forEach(match => {
            const cleanText = match.replace(/[()]/g, '').trim();
            if (cleanText.length > 3 && /[a-zA-Z]/.test(cleanText)) {
              text += cleanText + ' ';
            }
          });

          // Clean up the extracted text
          text = text.replace(/\s+/g, ' ').trim();

          if (text.length < 50) {
            reject(new Error('Unable to extract meaningful text from PDF using basic method'));
          } else {
            resolve(`[PDF Document: ${file.name}] - Basic Extraction\n\n${text}`);
          }
        } catch (error) {
          reject(new Error('Basic PDF extraction failed'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read PDF file'));
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * Extract text from Word document
   */
  private async extractTextFromWord(file: File): Promise<string> {
    try {
      // For now, we'll use a simple approach
      // In a production environment, you'd want to use a library like mammoth.js
      const text = await file.text();
      
      // Basic cleanup for Word documents
      return text
        .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    } catch (error) {
      console.error('Word document extraction failed:', error);
      throw new Error('Failed to extract text from Word document');
    }
  }

  /**
   * Extract text from plain text file
   */
  private async extractTextFromPlainText(file: File): Promise<string> {
    try {
      return await file.text();
    } catch (error) {
      console.error('Text file extraction failed:', error);
      throw new Error('Failed to read text file');
    }
  }

  /**
   * Extract metadata from document content
   */
  private async extractMetadata(content: string, filename: string): Promise<Partial<DocumentMetadata>> {
    // This would typically use AI/ML for better extraction
    // For now, we'll use simple heuristics
    
    const lines = content.split('\n').filter(line => line.trim());
    const firstLines = lines.slice(0, 20);
    
    // Try to extract title (usually the first significant line)
    const title = this.extractTitle(firstLines) || this.extractTitleFromFilename(filename);
    
    // Try to extract authors
    const authors = this.extractAuthors(firstLines);
    
    // Try to extract abstract
    const abstract = this.extractAbstract(content);
    
    // Extract keywords
    const keywords = this.extractKeywords(content);
    
    return {
      title,
      authors,
      abstract,
      keywords,
      confidence: 0.7 // Heuristic-based extraction has lower confidence
    };
  }

  /**
   * Create basic metadata when extraction is disabled
   */
  private createBasicMetadata(): Partial<DocumentMetadata> {
    return {
      confidence: 0.5,
      sources: ['basic-extraction'],
      language: 'english'
    };
  }

  /**
   * Generate document sections using content analysis
   */
  private async generateSections(content: string): Promise<DocumentSection[]> {
    const sections: DocumentSection[] = [];
    
    // Split content into paragraphs
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    
    // Simple section detection based on common academic paper structure
    let currentSection: DocumentSection | null = null;
    let sectionIndex = 0;
    
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim();
      
      // Check if this paragraph is a section header
      const sectionType = this.detectSectionType(paragraph);
      
      if (sectionType) {
        // Save previous section if exists
        if (currentSection) {
          sections.push(currentSection);
        }
        
        // Start new section
        currentSection = {
          id: crypto.randomUUID(),
          title: paragraph,
          content: '',
          type: sectionType,
          orderIndex: sectionIndex++
        };
      } else if (currentSection) {
        // Add content to current section
        currentSection.content += paragraph + '\n\n';
      } else {
        // No section detected yet, create a default section
        currentSection = {
          id: crypto.randomUUID(),
          title: 'Introduction',
          content: paragraph + '\n\n',
          type: 'introduction',
          orderIndex: sectionIndex++
        };
      }
    }
    
    // Add the last section
    if (currentSection) {
      sections.push(currentSection);
    }
    
    return sections;
  }

  /**
   * Create basic sections when generation is disabled
   */
  private createBasicSections(content: string): DocumentSection[] {
    return [{
      id: crypto.randomUUID(),
      title: 'Full Document',
      content: content,
      type: 'other',
      orderIndex: 0
    }];
  }

  // Helper methods for content extraction
  private extractTitle(lines: string[]): string | null {
    // Look for title patterns in first few lines
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.length > 10 && trimmed.length < 200 && !trimmed.includes('@')) {
        return trimmed;
      }
    }
    return null;
  }

  private extractTitleFromFilename(filename: string): string {
    return filename.replace(/\.[^/.]+$/, '').replace(/[_-]/g, ' ');
  }

  private extractAuthors(lines: string[]): string[] {
    const authors: string[] = [];
    
    for (const line of lines) {
      // Look for author patterns (names, emails, affiliations)
      if (line.includes('@') || /\b[A-Z][a-z]+ [A-Z][a-z]+\b/.test(line)) {
        const matches = line.match(/\b[A-Z][a-z]+ [A-Z][a-z]+\b/g);
        if (matches) {
          authors.push(...matches);
        }
      }
    }
    
    return [...new Set(authors)]; // Remove duplicates
  }

  private extractAbstract(content: string): string {
    // Look for abstract section
    const abstractMatch = content.match(/abstract[:\s]+(.*?)(?=\n\s*\n|\n\s*[A-Z])/is);
    if (abstractMatch) {
      return abstractMatch[1].trim().substring(0, 500); // Limit length
    }
    
    // Fallback: use first paragraph if it's substantial
    const firstParagraph = content.split('\n\n')[0];
    if (firstParagraph && firstParagraph.length > 100) {
      return firstParagraph.substring(0, 500);
    }
    
    return '';
  }

  private extractKeywords(content: string): string[] {
    // Simple keyword extraction based on frequency and importance
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const frequency: { [key: string]: number } = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .filter(([word, count]) => count > 2)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private detectSectionType(text: string): DocumentSection['type'] | null {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('abstract')) return 'abstract';
    if (lowerText.includes('introduction')) return 'introduction';
    if (lowerText.includes('method') || lowerText.includes('approach')) return 'methodology';
    if (lowerText.includes('result') || lowerText.includes('finding')) return 'results';
    if (lowerText.includes('discussion') || lowerText.includes('analysis')) return 'discussion';
    if (lowerText.includes('conclusion') || lowerText.includes('summary')) return 'conclusion';
    if (lowerText.includes('reference') || lowerText.includes('bibliography')) return 'references';
    
    return null;
  }
}

export const documentProcessorService = new DocumentProcessorService();
