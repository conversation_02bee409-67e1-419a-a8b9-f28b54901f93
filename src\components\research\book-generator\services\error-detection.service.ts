import { BookMetadata, UserChapter, GeneratedOutline, GeneratedChapter } from '../types';

export interface ErrorDetectionResult {
  hasErrors: boolean;
  errorType: 'fake_outline' | 'generic_content' | 'context_mismatch' | 'quality_low' | 'none';
  severity: 'low' | 'medium' | 'high' | 'critical';
  issues: string[];
  suggestions: string[];
  confidence: number; // 0-100
  recoveryActions: RecoveryAction[];
}

export interface RecoveryAction {
  type: 'regenerate' | 'enhance_prompt' | 'change_model' | 'add_context' | 'manual_review';
  description: string;
  priority: number;
  automated: boolean;
}

/**
 * Service for detecting and recovering from AI generation errors
 */
export class ErrorDetectionService {
  
  /**
   * Detect fake or generic outlines
   */
  detectFakeOutline(
    outline: GeneratedOutline, 
    userChapter: UserChapter, 
    metadata: BookMetadata
  ): ErrorDetectionResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const recoveryActions: RecoveryAction[] = [];
    let confidence = 0;

    // Check for generic section titles
    const genericTitles = [
      'introduction', 'background', 'overview', 'analysis', 'conclusion', 
      'summary', 'discussion', 'methodology', 'literature review', 'results',
      'findings', 'recommendations', 'future work', 'limitations'
    ];

    const hasGenericTitles = outline.sections.some(section => 
      genericTitles.some(generic => 
        section.title.toLowerCase().includes(generic) && 
        !this.isSpecificToTopic(section.title, userChapter, metadata)
      )
    );

    if (hasGenericTitles) {
      issues.push('Contains generic academic section titles');
      suggestions.push('Regenerate with more topic-specific section titles');
      confidence += 30;
    }

    // Check for topic-specific terminology
    const topicTerms = [
      ...metadata.keywords.map(k => k.toLowerCase()),
      ...userChapter.outline.description.toLowerCase().split(' ').filter(w => w.length > 4)
    ];

    const outlineText = outline.sections
      .map(s => `${s.title} ${s.description}`)
      .join(' ')
      .toLowerCase();

    const hasTopicTerms = topicTerms.some(term => outlineText.includes(term));

    if (!hasTopicTerms && topicTerms.length > 0) {
      issues.push('Lacks topic-specific terminology from user input');
      suggestions.push('Include specific terms and concepts from the chapter description');
      confidence += 25;
    }

    // Check section depth and specificity
    const hasShallowSections = outline.sections.some(section => 
      section.description.length < 50 || section.keyPoints.length < 3
    );

    if (hasShallowSections) {
      issues.push('Some sections lack sufficient detail and specificity');
      suggestions.push('Add more detailed descriptions and specific key points');
      confidence += 20;
    }

    // Check for repetitive patterns
    const sectionTitles = outline.sections.map(s => s.title.toLowerCase());
    const hasRepetitivePatterns = this.detectRepetitivePatterns(sectionTitles);

    if (hasRepetitivePatterns) {
      issues.push('Sections follow repetitive or template-like patterns');
      suggestions.push('Create more varied and topic-specific section structures');
      confidence += 25;
    }

    // Determine error type and severity
    let errorType: ErrorDetectionResult['errorType'] = 'none';
    let severity: ErrorDetectionResult['severity'] = 'low';

    if (confidence >= 70) {
      errorType = 'fake_outline';
      severity = 'critical';
      recoveryActions.push({
        type: 'regenerate',
        description: 'Regenerate outline with enhanced topic-specific prompting',
        priority: 1,
        automated: true
      });
    } else if (confidence >= 50) {
      errorType = 'generic_content';
      severity = 'high';
      recoveryActions.push({
        type: 'enhance_prompt',
        description: 'Enhance prompts with more specific topic details',
        priority: 1,
        automated: true
      });
    } else if (confidence >= 30) {
      errorType = 'quality_low';
      severity = 'medium';
      recoveryActions.push({
        type: 'add_context',
        description: 'Add more context and specific requirements',
        priority: 2,
        automated: false
      });
    }

    return {
      hasErrors: confidence >= 30,
      errorType,
      severity,
      issues,
      suggestions,
      confidence,
      recoveryActions
    };
  }

  /**
   * Detect generic or low-quality chapter content
   */
  detectGenericContent(
    chapter: GeneratedChapter,
    outline: GeneratedOutline,
    metadata: BookMetadata
  ): ErrorDetectionResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const recoveryActions: RecoveryAction[] = [];
    let confidence = 0;

    if (!chapter.content) {
      return {
        hasErrors: true,
        errorType: 'quality_low',
        severity: 'critical',
        issues: ['No content generated'],
        suggestions: ['Regenerate chapter content'],
        confidence: 100,
        recoveryActions: [{
          type: 'regenerate',
          description: 'Regenerate chapter content',
          priority: 1,
          automated: true
        }]
      };
    }

    const content = chapter.content.toLowerCase();

    // Check for generic phrases
    const genericPhrases = [
      'in this chapter, we will',
      'this chapter covers',
      'we will explore',
      'let us examine',
      'it is important to note',
      'in conclusion',
      'to summarize',
      'as we have seen',
      'moving forward'
    ];

    const genericPhraseCount = genericPhrases.filter(phrase => 
      content.includes(phrase)
    ).length;

    if (genericPhraseCount >= 3) {
      issues.push('Contains too many generic academic phrases');
      suggestions.push('Use more specific, topic-focused language');
      confidence += 20;
    }

    // Check for topic-specific content
    const topicTerms = [
      ...metadata.keywords.map(k => k.toLowerCase()),
      ...outline.sections.flatMap(s => s.keyPoints.map(kp => kp.toLowerCase()))
    ];

    const topicTermsFound = topicTerms.filter(term => content.includes(term)).length;
    const topicCoverage = topicTerms.length > 0 ? topicTermsFound / topicTerms.length : 0;

    if (topicCoverage < 0.3) {
      issues.push('Insufficient coverage of specified topics and key points');
      suggestions.push('Include more specific content related to the outlined topics');
      confidence += 30;
    }

    // Check content depth
    const wordCount = chapter.content.split(' ').length;
    const expectedWordCount = outline.estimatedWordCount || 3000;
    const wordCountRatio = wordCount / expectedWordCount;

    if (wordCountRatio < 0.7) {
      issues.push('Content is significantly shorter than expected');
      suggestions.push('Generate more comprehensive content to meet word count targets');
      confidence += 15;
    }

    // Check for section structure adherence
    const outlineSectionTitles = outline.sections.map(s => s.title.toLowerCase());
    const contentSectionMatches = outlineSectionTitles.filter(title => 
      content.includes(title.substring(0, Math.min(title.length, 20)))
    ).length;

    const structureAdherence = outlineSectionTitles.length > 0 ? 
      contentSectionMatches / outlineSectionTitles.length : 0;

    if (structureAdherence < 0.5) {
      issues.push('Content does not follow the outlined structure');
      suggestions.push('Ensure content follows the detailed outline structure');
      confidence += 25;
    }

    // Determine error type and recovery actions
    let errorType: ErrorDetectionResult['errorType'] = 'none';
    let severity: ErrorDetectionResult['severity'] = 'low';

    if (confidence >= 60) {
      errorType = 'generic_content';
      severity = 'high';
      recoveryActions.push({
        type: 'regenerate',
        description: 'Regenerate with enhanced topic-specific prompting',
        priority: 1,
        automated: true
      });
    } else if (confidence >= 40) {
      errorType = 'quality_low';
      severity = 'medium';
      recoveryActions.push({
        type: 'enhance_prompt',
        description: 'Enhance generation prompts with more specific requirements',
        priority: 1,
        automated: true
      });
    }

    return {
      hasErrors: confidence >= 30,
      errorType,
      severity,
      issues,
      suggestions,
      confidence,
      recoveryActions
    };
  }

  /**
   * Detect context mismatches between chapters
   */
  detectContextMismatch(
    currentChapter: GeneratedChapter,
    previousChapters: GeneratedChapter[],
    metadata: BookMetadata
  ): ErrorDetectionResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const recoveryActions: RecoveryAction[] = [];
    let confidence = 0;

    if (previousChapters.length === 0) {
      return {
        hasErrors: false,
        errorType: 'none',
        severity: 'low',
        issues: [],
        suggestions: [],
        confidence: 0,
        recoveryActions: []
      };
    }

    // Check for narrative consistency
    const currentContent = currentChapter.content?.toLowerCase() || '';
    const previousContent = previousChapters
      .map(ch => ch.content?.toLowerCase() || '')
      .join(' ');

    // Check for contradictory statements (basic implementation)
    const contradictionIndicators = [
      'however', 'but', 'on the contrary', 'conversely', 'in contrast',
      'unlike', 'different from', 'opposite'
    ];

    const contradictionCount = contradictionIndicators.filter(indicator =>
      currentContent.includes(indicator)
    ).length;

    if (contradictionCount > 3) {
      issues.push('Potential contradictions with previous chapters');
      suggestions.push('Review content for consistency with previous chapters');
      confidence += 15;
    }

    // Check for proper references to previous content
    const referenceIndicators = [
      'as mentioned', 'previously discussed', 'earlier chapter', 'building on',
      'following from', 'as we saw', 'continuing from'
    ];

    const hasReferences = referenceIndicators.some(indicator =>
      currentContent.includes(indicator)
    );

    if (previousChapters.length > 2 && !hasReferences) {
      issues.push('Lacks proper references to previous chapters');
      suggestions.push('Add references to maintain narrative continuity');
      confidence += 20;
    }

    return {
      hasErrors: confidence >= 25,
      errorType: confidence >= 25 ? 'context_mismatch' : 'none',
      severity: confidence >= 40 ? 'medium' : 'low',
      issues,
      suggestions,
      confidence,
      recoveryActions
    };
  }

  /**
   * Check if a section title is specific to the topic
   */
  private isSpecificToTopic(
    sectionTitle: string, 
    userChapter: UserChapter, 
    metadata: BookMetadata
  ): boolean {
    const topicTerms = [
      ...(metadata.keywords || []).filter(k => typeof k === 'string'),
      ...(userChapter.outline.description || '').split(' ').filter(w => w.length > 4),
      ...(metadata.title || '').split(' ').filter(w => w.length > 4)
    ].filter(term => typeof term === 'string' && term.length > 0).map(term => term.toLowerCase());

    const titleLower = sectionTitle.toLowerCase();
    return topicTerms.some(term => titleLower.includes(term));
  }

  /**
   * Detect repetitive patterns in section titles
   */
  private detectRepetitivePatterns(titles: string[]): boolean {
    if (titles.length < 3) return false;

    // Check for similar starting words
    const startingWords = titles.map(title => title.split(' ')[0]);
    const uniqueStartingWords = new Set(startingWords);
    
    if (uniqueStartingWords.size < titles.length * 0.7) {
      return true;
    }

    // Check for similar patterns
    const patterns = titles.map(title => 
      title.replace(/\d+/g, 'N').replace(/[A-Z][a-z]+/g, 'Word')
    );
    const uniquePatterns = new Set(patterns);

    return uniquePatterns.size < titles.length * 0.8;
  }

  /**
   * Execute automated recovery actions
   */
  async executeRecoveryAction(
    action: RecoveryAction,
    context: {
      outline?: GeneratedOutline;
      chapter?: GeneratedChapter;
      userChapter?: UserChapter;
      metadata?: BookMetadata;
    }
  ): Promise<boolean> {
    try {
      switch (action.type) {
        case 'regenerate':
          // This would trigger regeneration in the calling component
          return true;
          
        case 'enhance_prompt':
          // This would enhance the prompt and trigger regeneration
          return true;
          
        case 'change_model':
          // This would switch to a different AI model
          return true;
          
        case 'add_context':
          // This would add more context and regenerate
          return true;
          
        case 'manual_review':
          // This requires manual intervention
          return false;
          
        default:
          return false;
      }
    } catch (error) {
      console.error('Recovery action failed:', error);
      return false;
    }
  }
}

export const errorDetectionService = new ErrorDetectionService();
