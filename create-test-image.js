// Simple script to create a test image file for testing figure upload
const fs = require('fs');
const { createCanvas } = require('canvas');

// Create a canvas
const canvas = createCanvas(400, 300);
const ctx = canvas.getContext('2d');

// Create a simple test chart/graph
ctx.fillStyle = '#f0f0f0';
ctx.fillRect(0, 0, 400, 300);

// Draw title
ctx.fillStyle = '#000';
ctx.font = '16px Arial';
ctx.fillText('Test Research Figure', 150, 30);

// Draw axes
ctx.strokeStyle = '#000';
ctx.lineWidth = 2;
ctx.beginPath();
ctx.moveTo(50, 250);
ctx.lineTo(350, 250);
ctx.moveTo(50, 50);
ctx.lineTo(50, 250);
ctx.stroke();

// Draw some data points
ctx.fillStyle = '#0066cc';
const points = [[80, 200], [120, 180], [160, 150], [200, 120], [240, 100], [280, 90], [320, 85]];
points.forEach(point => {
    ctx.beginPath();
    ctx.arc(point[0], point[1], 5, 0, 2 * Math.PI);
    ctx.fill();
});

// Connect points with line
ctx.strokeStyle = '#0066cc';
ctx.lineWidth = 2;
ctx.beginPath();
ctx.moveTo(points[0][0], points[0][1]);
points.forEach(point => {
    ctx.lineTo(point[0], point[1]);
});
ctx.stroke();

// Add labels
ctx.fillStyle = '#000';
ctx.font = '12px Arial';
ctx.fillText('Time (hours)', 180, 280);
ctx.save();
ctx.translate(20, 150);
ctx.rotate(-Math.PI/2);
ctx.fillText('Temperature (°C)', 0, 0);
ctx.restore();

// Save as PNG
const buffer = canvas.toBuffer('image/png');
fs.writeFileSync('test-research-figure.png', buffer);
console.log('Test image created: test-research-figure.png');
