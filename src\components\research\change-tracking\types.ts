export interface ChangeRecord {
  id: string;
  timestamp: Date;
  originalText: string;
  newText: string;
  startPosition: number;
  endPosition: number;
  aiActionType: 'replace' | 'insert' | 'display';
  status: 'pending' | 'accepted' | 'rejected';
  prompt?: string;
  isSelected?: boolean;
  sections?: ChangeSection[];
}

export interface ChangeSection {
  id: string;
  changeId: string;
  originalText: string;
  newText: string;
  startPosition: number;
  endPosition: number;
  status: 'pending' | 'accepted' | 'rejected';
  isSelected: boolean;
}

export interface ChangeTrackingState {
  originalContent: string;
  currentContent: string;
  changes: ChangeRecord[];
  isTrackingEnabled: boolean;
  currentChangeIndex: number;
  viewMode: 'normal' | 'diff';
  selectedSections: Set<string>;
  selectionMode: 'individual' | 'all' | 'none';
}

export interface DiffViewOptions {
  showLineNumbers: boolean;
  splitView: boolean;
  hideUnchangedLines: boolean;
  contextLines: number;
}

export interface ChangeNavigationState {
  currentIndex: number;
  totalChanges: number;
  hasNext: boolean;
  hasPrevious: boolean;
}
