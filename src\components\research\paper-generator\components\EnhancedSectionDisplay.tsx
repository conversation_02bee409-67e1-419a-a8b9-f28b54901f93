/**
 * Enhanced Section Display with Citation Links
 * Shows generated content with clickable citations and enhanced formatting
 */

import React, { useState } from 'react';
import { ExternalLink, Eye, EyeOff, Copy, Check } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { CitationSource } from '../services/enhanced-citation-search.service';
import { citationValidationService } from '../services/citation-validation.service';

interface EnhancedSectionDisplayProps {
  sectionId: string;
  sectionName: string;
  content: string;
  citations: CitationSource[];
  isGenerating?: boolean;
  onRegenerate?: () => void;
  onEdit?: () => void;
}

export function EnhancedSectionDisplay({
  sectionId,
  sectionName,
  content,
  citations,
  isGenerating = false,
  onRegenerate,
  onEdit
}: EnhancedSectionDisplayProps) {
  const [showCitations, setShowCitations] = useState(true);
  const [copiedCitation, setCopiedCitation] = useState<string | null>(null);

  // Process content to add clickable citation links
  const processContentWithLinks = (text: string): string => {
    let processedText = text;

    citations.forEach(citation => {
      const citationPattern = new RegExp(
        citation.inTextCitation.replace(/[()]/g, '\\$&'),
        'g'
      );

      const linkHtml = `<a href="${citation.url}" target="_blank" rel="noopener noreferrer" 
        class="citation-link inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 
        underline decoration-dotted hover:decoration-solid transition-colors duration-200"
        title="${citation.title} - ${citation.authors.join(', ')} (${citation.year})">
        ${citation.inTextCitation}
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
        </svg>
      </a>`;

      processedText = processedText.replace(citationPattern, linkHtml);
    });

    return processedText;
  };

  const copyToClipboard = async (text: string, citationId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCitation(citationId);
      setTimeout(() => setCopiedCitation(null), 2000);
    } catch (error) {
      console.error('Failed to copy citation:', error);
    }
  };

  const formatCitationForCopy = (citation: CitationSource): string => {
    return citationValidationService.formatCitation(citation, {
      style: 'apa',
      includeLinks: false,
      includeDOI: true,
      maxAuthors: 3
    });
  };

  if (isGenerating) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            Generating {sectionName}...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="animate-pulse bg-gray-200 h-4 rounded w-3/4"></div>
            <div className="animate-pulse bg-gray-200 h-4 rounded w-full"></div>
            <div className="animate-pulse bg-gray-200 h-4 rounded w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {sectionName}
            {citations.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {citations.length} citations
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {citations.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCitations(!showCitations)}
                className="text-sm"
              >
                {showCitations ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showCitations ? 'Hide' : 'Show'} Citations
              </Button>
            )}
            {onRegenerate && (
              <Button variant="outline" size="sm" onClick={onRegenerate}>
                Regenerate
              </Button>
            )}
            {onEdit && (
              <Button variant="outline" size="sm" onClick={onEdit}>
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Content */}
        <div 
          className="prose prose-sm max-w-none leading-relaxed text-gray-800"
          dangerouslySetInnerHTML={{ 
            __html: processContentWithLinks(content).replace(/\n/g, '<br />') 
          }}
        />

        {/* Citations Panel */}
        {citations.length > 0 && (
          <Collapsible open={showCitations} onOpenChange={setShowCitations}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-2 h-auto">
                <span className="font-medium text-sm">
                  References Used ({citations.length})
                </span>
                {showCitations ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-3 mt-3">
              {citations.map((citation, index) => {
                const validation = citationValidationService.validateCitation(citation);
                const formattedCitation = formatCitationForCopy(citation);
                
                return (
                  <div 
                    key={citation.id}
                    className="border rounded-lg p-3 bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm text-gray-600">
                            {index + 1}.
                          </span>
                          <a
                            href={citation.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium text-blue-600 hover:text-blue-800 
                              hover:underline flex items-center gap-1 text-sm"
                            title="Open source"
                          >
                            {citation.title}
                            <ExternalLink className="w-3 h-3" />
                          </a>
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Authors: <AUTHORS>
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span><strong>Year:</strong> {citation.year}</span>
                          {citation.journal && (
                            <span><strong>Journal:</strong> {citation.journal}</span>
                          )}
                          <span><strong>Relevance:</strong> {Math.round(citation.relevanceScore * 100)}%</span>
                        </div>

                        {citation.abstract && (
                          <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                            <strong>Abstract:</strong> {citation.abstract.substring(0, 200)}...
                          </div>
                        )}

                        {/* Validation Status */}
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={validation.isValid ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {validation.isValid ? 'Valid' : 'Issues Found'}
                          </Badge>
                          {validation.score < 0.7 && (
                            <Badge variant="outline" className="text-xs">
                              Quality: {Math.round(validation.score * 100)}%
                            </Badge>
                          )}
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(formattedCitation, citation.id)}
                        className="shrink-0"
                        title="Copy formatted citation"
                      >
                        {copiedCitation === citation.id ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>

                    {/* Show validation warnings if any */}
                    {validation.warnings.length > 0 && (
                      <div className="mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded">
                        <strong>Warnings:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {validation.warnings.map((warning, idx) => (
                            <li key={idx}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                );
              })}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Section Statistics */}
        <div className="flex items-center gap-4 text-xs text-gray-500 pt-2 border-t">
          <span>Words: {content.split(/\s+/).length}</span>
          <span>Citations: {citations.length}</span>
          {citations.length > 0 && (
            <span>
              Avg. Relevance: {Math.round(
                citations.reduce((sum, c) => sum + c.relevanceScore, 0) / citations.length * 100
              )}%
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// CSS styles for citation links (to be added to global styles)
export const citationLinkStyles = `
  .citation-link {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 2px;
    color: #2563eb;
    text-decoration: underline;
    text-decoration-style: dotted;
    transition: all 0.2s ease;
  }

  .citation-link:hover {
    color: #1d4ed8;
    text-decoration-style: solid;
  }

  .citation-link svg {
    width: 12px;
    height: 12px;
    opacity: 0.7;
  }

  .citation-link:hover svg {
    opacity: 1;
  }
`;
