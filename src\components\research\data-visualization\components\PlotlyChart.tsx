import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Download,
  Maximize2,
  Copy,
  Refresh<PERSON><PERSON>,
  Settings,
  AlertTriangle,
  Loader2,
  BarChart3
} from "lucide-react";
import { VisualizationConfig } from '../types';
import { PLOTLY_THEMES, DATA_VIZ_CONFIG } from '../constants';
import { FallbackChart } from './FallbackChart';

// Dynamic import for Plotly to avoid SSR issues
let Plotly: any = null;

interface PlotlyChartProps {
  visualization: VisualizationConfig;
  theme?: 'default' | 'dark';
  className?: string;
  onError?: (error: string) => void;
}

export const PlotlyChart: React.FC<PlotlyChartProps> = ({
  visualization,
  theme = 'default',
  className = "",
  onError
}) => {
  const plotRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [useFallback, setUseFallback] = useState(false);

  useEffect(() => {
    loadPlotly();
  }, []);

  useEffect(() => {
    // Add a small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      if (Plotly && plotRef.current) {
        renderChart();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [visualization, theme, Plotly]);

  const loadPlotly = async () => {
    try {
      if (!Plotly) {
        // Dynamic import of Plotly
        const plotlyModule = await import('plotly.js-dist-min');
        Plotly = plotlyModule.default;
      }
      setIsLoading(false);
    } catch (error) {
      const errorMessage = 'Failed to load Plotly library';
      setError(errorMessage);
      setIsLoading(false);
      if (onError) {
        onError(errorMessage);
      }
    }
  };

  const renderChart = async () => {
    // Store reference to avoid it becoming null during async operations
    const plotElement = plotRef.current;

    if (!Plotly || !plotElement) {
      console.warn('Plotly or DOM element not available for rendering');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Validate visualization data
      if (!visualization.data || !Array.isArray(visualization.data) || visualization.data.length === 0) {
        throw new Error('Invalid visualization data: data is empty or not an array');
      }

      // Apply theme
      const themeConfig = PLOTLY_THEMES[theme];
      const layout = {
        ...visualization.layout,
        ...themeConfig.layout,
        autosize: true,
        responsive: true,
        // Ensure minimum dimensions
        width: undefined, // Let autosize handle this
        height: undefined // Let autosize handle this
      };

      // Configure plot
      const config = {
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
        displaylogo: false,
        toImageButtonOptions: {
          format: 'png',
          filename: `${visualization.title.replace(/\s+/g, '_')}_chart`,
          height: 600,
          width: 800,
          scale: 2
        },
        ...visualization.config
      };

      // Clear previous plot safely using stored reference
      try {
        if (plotElement.hasChildNodes()) {
          await Plotly.purge(plotElement);
        }
      } catch (purgeError) {
        console.warn('Error purging previous plot:', purgeError);
        // Continue anyway, sometimes purge fails but newPlot still works
      }

      // Double-check the element is still in the DOM
      if (!plotElement.isConnected) {
        throw new Error('DOM element was removed from document');
      }

      // Create new plot using stored reference
      await Plotly.newPlot(
        plotElement,
        visualization.data,
        layout,
        config
      );

      // Add resize handler using stored reference
      const resizeHandler = () => {
        if (plotElement && plotElement.isConnected) {
          try {
            Plotly.Plots.resize(plotElement);
          } catch (resizeError) {
            console.warn('Error resizing plot:', resizeError);
          }
        }
      };

      window.addEventListener('resize', resizeHandler);

      // Cleanup function
      return () => {
        window.removeEventListener('resize', resizeHandler);
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to render chart';
      console.warn('Plotly chart failed, switching to fallback:', errorMessage);

      // Switch to fallback chart instead of showing error
      setUseFallback(true);
      setError(null);

      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const downloadChart = async (format: 'png' | 'svg' | 'pdf' = 'png') => {
    if (!Plotly || !plotRef.current) return;

    try {
      const filename = `${visualization.title.replace(/\s+/g, '_')}_chart.${format}`;
      
      if (format === 'png') {
        await Plotly.downloadImage(plotRef.current, {
          format: 'png',
          filename,
          height: 600,
          width: 800,
          scale: 2
        });
      } else {
        await Plotly.downloadImage(plotRef.current, {
          format,
          filename,
          height: 600,
          width: 800
        });
      }

      toast.success(`Chart downloaded as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to download chart');
    }
  };

  const copyChartData = async () => {
    try {
      const chartData = {
        title: visualization.title,
        type: visualization.type,
        data: visualization.data,
        layout: visualization.layout
      };

      await navigator.clipboard.writeText(JSON.stringify(chartData, null, 2));
      toast.success('Chart data copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy chart data');
    }
  };

  const refreshChart = () => {
    setUseFallback(false);
    setError(null);
    if (Plotly && plotRef.current) {
      renderChart();
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // Trigger resize after fullscreen toggle
    setTimeout(() => {
      if (Plotly && plotRef.current) {
        Plotly.Plots.resize(plotRef.current);
      }
    }, 100);
  };

  const getChartIcon = () => {
    switch (visualization.type) {
      case 'histogram':
        return '📊';
      case 'scatter':
        return '🔵';
      case 'correlation_heatmap':
        return '🔥';
      case 'box_plot':
        return '📦';
      case 'bar_chart':
        return '📊';
      case 'line_chart':
        return '📈';
      case 'pie_chart':
        return '🥧';
      default:
        return '📊';
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
            <p className="text-sm text-gray-500">Loading visualization...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Use fallback chart if Plotly failed
  if (useFallback) {
    return <FallbackChart visualization={visualization} className={className} />;
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Visualization Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="flex gap-2 mt-3">
            <Button onClick={refreshChart} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            <Button onClick={() => setUseFallback(true)} variant="outline" size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              Show Simple View
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">{getChartIcon()}</span>
            <div>
              <CardTitle className="text-lg">{visualization.title}</CardTitle>
              {visualization.description && (
                <p className="text-sm text-gray-500 mt-1">{visualization.description}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {visualization.type.replace('_', ' ').toUpperCase()}
            </Badge>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={copyChartData}
                title="Copy chart data"
              >
                <Copy className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => downloadChart('png')}
                title="Download as PNG"
              >
                <Download className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                title={isFullscreen ? "Exit fullscreen" : "Fullscreen"}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshChart}
                title="Refresh chart"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div 
          ref={plotRef}
          className={`w-full ${isFullscreen ? 'h-[calc(100vh-200px)]' : 'h-96'}`}
          style={{ minHeight: DATA_VIZ_CONFIG.DEFAULT_CHART_HEIGHT }}
        />
      </CardContent>
    </Card>
  );
};
