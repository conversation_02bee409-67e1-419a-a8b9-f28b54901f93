/**
 * Flow Builder - Modern AI-Powered Research Diagram Generator
 * Beautiful, full-page interface for creating research flowcharts
 */

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  FlowBuilderProps,
  DiagramInput,
  DiagramExportOptions
} from './types';
import { useFlowBuilderStore } from './stores/flow-builder.store';
import {
  DiagramInputForm,
  DiagramRenderer,
  DiagramExportDialog,
  AIModelSelector,
  DiagramGenerationLoading
} from './components/index';
import { Button } from "@/components/ui/button";
import {
  GitBranch,
  Download,
  Edit3,
  RefreshCw,
  Loader2,
  Share,
  Settings,
  History,
  Zap,
  Lightbulb,
  Sparkles,
  PanelRightOpen,
  PanelRightClose
} from 'lucide-react';

export function FlowBuilder({ className = "" }: FlowBuilderProps) {
  // Store state
  const {
    currentDiagram,
    workflow,
    templates,
    selectedModel,
    mermaidConfig,
    generateDiagram,
    regenerateDiagram,
    editDiagramWithAI,
    fixDiagramErrors,
    exportDiagram,
    setSelectedModel,
    resetState
  } = useFlowBuilderStore();

  // Local state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  // Handle diagram generation
  const handleGenerate = async (input: DiagramInput) => {
    try {
      await generateDiagram({
        input,
        model: selectedModel,
        temperature: 0.7,
        maxTokens: 4096
      });
      toast.success('Diagram generated successfully!');
    } catch (error: any) {
      console.error('Generation error:', error);
      toast.error(error.message || 'Failed to generate diagram');
    }
  };

  // Handle diagram regeneration
  const handleRegenerate = async () => {
    try {
      await regenerateDiagram();
      toast.success('Diagram regenerated successfully!');
    } catch (error: any) {
      console.error('Regeneration error:', error);
      toast.error(error.message || 'Failed to regenerate diagram');
    }
  };

  // Handle error fixing
  const handleFixErrors = async (errorMessage: string) => {
    try {
      await fixDiagramErrors(errorMessage);
      toast.success('Diagram errors fixed successfully!');
    } catch (error: any) {
      console.error('Error fixing error:', error);
      toast.error(error.message || 'Failed to fix diagram errors');
    }
  };

  // Handle diagram editing
  const handleEditDiagram = async (editPrompt: string) => {
    try {
      await editDiagramWithAI(editPrompt);
      toast.success('Diagram updated successfully!');
    } catch (error: any) {
      console.error('Edit error:', error);
      toast.error(error.message || 'Failed to edit diagram');
    }
  };

  // Enhanced export handler with better error handling
  const handleExport = async (options: DiagramExportOptions) => {
    try {
      console.log('Starting export with options:', options);
      await exportDiagram(options);
      toast.success(`Diagram exported successfully as ${options.format.toUpperCase()}!`);
      setShowExportDialog(false);
    } catch (error: any) {
      console.error('Export error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to export diagram';
      if (error.message?.includes('No rendered diagram found')) {
        errorMessage = 'Please wait for the diagram to fully load before exporting';
      } else if (error.message?.includes('Canvas context not available')) {
        errorMessage = 'Your browser does not support this export format';
      } else if (error.message?.includes('popup blockers')) {
        errorMessage = 'Please disable popup blockers for PDF export';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    }
  };



  // Handle template application
  const handleApplyTemplate = (templateId: string) => {
    const { applyTemplate } = useFlowBuilderStore.getState();
    applyTemplate(templateId);
    toast.success('Template applied successfully!');
  };

  const isGenerating = workflow.isGenerating;

  // Keyboard shortcut for toggling sidebar (Ctrl/Cmd + \)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === '\\') {
        e.preventDefault();
        setShowSidebar(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 ${className}`}>
      {/* Enhanced Header with Gradient */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <GitBranch className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                  AI Flow Builder
                </h1>
                <p className="text-sm text-gray-600 font-medium">Research Diagram Generator</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <AIModelSelector
                selectedModel={selectedModel}
                onModelChange={setSelectedModel}
              />

              <div className="flex items-center gap-2 bg-gray-50/50 rounded-xl p-1 border border-gray-200/50">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSidebar(!showSidebar)}
                  className="hover:bg-white/80 transition-all duration-200 text-blue-600 hover:text-blue-700"
                  title={showSidebar ? "Hide AI Assistant (Ctrl + \\)" : "Show AI Assistant (Ctrl + \\)"}
                >
                  {showSidebar ? <PanelRightClose className="h-4 w-4" /> : <PanelRightOpen className="h-4 w-4" />}
                  <span className="ml-1 text-xs font-medium">AI Assistant</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                  className="hover:bg-white/80 transition-all duration-200"
                  title="Settings"
                >
                  <Settings className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetState}
                  className="hover:bg-white/80 transition-all duration-200"
                  title="Reset"
                >
                  <History className="h-4 w-4" />
                </Button>

                {currentDiagram && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowExportDialog(true)}
                    className="hover:bg-white/80 transition-all duration-200 text-blue-600 hover:text-blue-700"
                    title="Export Diagram"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Layout with Sidebar */}
      <div className="flex flex-1 pt-20">
        {/* Main Content Area */}
        <main className={`flex-1 px-6 py-8 transition-all duration-300 ${showSidebar ? 'pr-0' : 'pr-6'}`}>
        {/* Enhanced Generation Status */}
        {isGenerating && (
          <div className="mb-8">
            <DiagramGenerationLoading
              stage="generating"
              progress={workflow.progress || 50}
              model={selectedModel}
              description="Creating your research diagram with AI assistance"
            />
          </div>
        )}

        {/* Enhanced Welcome Section */}
        {!currentDiagram && !isGenerating && (
          <div className="flex items-center justify-center min-h-[70vh]">
            <div className="text-center space-y-8 max-w-4xl">
              {/* Hero Icon with Animation */}
              <div className="relative mx-auto w-32 h-32">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 rounded-3xl rotate-6 opacity-20 animate-pulse"></div>
                <div className="relative w-full h-full bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl">
                  <GitBranch className="h-16 w-16 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-400 rounded-full border-4 border-white flex items-center justify-center">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
              </div>

              {/* Enhanced Text Content */}
              <div className="space-y-4">
                <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                  AI Research Diagram Generator
                </h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                  Transform your research ideas into professional diagrams with the power of AI
                </p>
                <p className="text-sm text-gray-500 flex items-center justify-center gap-2">
                  <Sparkles className="h-4 w-4 text-blue-500" />
                  Click the AI assistant bubble to get started
                </p>
              </div>

              {/* Enhanced Feature Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                <div className="group p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <GitBranch className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Flowcharts</h3>
                  <p className="text-sm text-gray-600">Process flows and decision trees for research methodology</p>
                </div>

                <div className="group p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 hover:border-purple-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Timelines</h3>
                  <p className="text-sm text-gray-600">Project schedules and research phases visualization</p>
                </div>

                <div className="group p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 hover:border-indigo-300/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Lightbulb className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Mind Maps</h3>
                  <p className="text-sm text-gray-600">Concept mapping and idea organization tools</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Diagram Display */}
        {currentDiagram && (
          <div className="space-y-6">
            {/* Enhanced Action Bar */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-6 shadow-sm">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                    <GitBranch className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-900 mb-1">
                      {currentDiagram.metadata.title}
                    </h2>
                    <div className="flex items-center gap-3">
                      <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 text-xs font-semibold rounded-full border border-blue-300/50">
                        {currentDiagram.metadata.type}
                      </span>
                      <span className="text-xs text-gray-500 flex items-center gap-1">
                        <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                        Generated with {currentDiagram.model}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRegenerate()}
                    disabled={isGenerating}
                    className="bg-white/80 hover:bg-white border-gray-300/50 hover:border-blue-400 transition-all duration-200"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
                    Regenerate
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowExportDialog(true)}
                    className="bg-white/80 hover:bg-white border-gray-300/50 hover:border-green-400 transition-all duration-200"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetState}
                    className="bg-white/80 hover:bg-white border-gray-300/50 hover:border-purple-400 transition-all duration-200"
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    New Diagram
                  </Button>
                </div>
              </div>
            </div>

            {/* Enhanced Responsive Diagram Renderer Container */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 overflow-hidden shadow-lg">
              <DiagramRenderer
                diagram={currentDiagram}
                config={mermaidConfig}
                onFixErrors={handleFixErrors}
                className="w-full"
              />
            </div>
          </div>
        )}

        {/* Export Dialog */}
        {currentDiagram && (
          <DiagramExportDialog
            diagram={currentDiagram}
            isOpen={showExportDialog}
            onClose={() => setShowExportDialog(false)}
            onExport={handleExport}
          />
        )}
        </main>

        {/* Right Sidebar AI Assistant - Conditional with Animation */}
        <aside className={`bg-white/95 backdrop-blur-xl border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out ${
          showSidebar
            ? 'w-96 opacity-100 translate-x-0'
            : 'w-0 opacity-0 translate-x-full overflow-hidden'
        }`}>
          {showSidebar && (
            <DiagramInputForm
              onGenerate={handleGenerate}
              isGenerating={isGenerating}
              templates={templates}
              onApplyTemplate={handleApplyTemplate}
              currentDiagram={currentDiagram}
              onEditDiagram={handleEditDiagram}
            />
          )}
        </aside>

        {/* Floating Toggle Button - Shows when sidebar is hidden */}
        {!showSidebar && (
          <div className="fixed bottom-6 right-6 z-50 animate-in slide-in-from-right-4 duration-300">
            <div className="relative group">
              <Button
                onClick={() => setShowSidebar(true)}
                className="w-14 h-14 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 text-white rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-white/50 relative overflow-hidden"
                title="Show AI Assistant (Ctrl + \)"
              >
                {/* Pulse Animation */}
                <div className="absolute inset-0 rounded-full bg-blue-400/30 animate-ping"></div>

                {/* Main Icon */}
                <Sparkles className="h-6 w-6 relative z-10 group-hover:rotate-12 transition-transform duration-300" />

                {/* Status Indicator */}
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                </div>
              </Button>

              {/* Enhanced Tooltip */}
              <div className="absolute bottom-full right-0 mb-3 px-3 py-2 bg-gray-900/95 backdrop-blur-sm text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap shadow-xl border border-gray-700/50 transform translate-y-1 group-hover:translate-y-0">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-3 w-3 text-blue-300" />
                  <span className="font-medium">AI Assistant (Ctrl + \)</span>
                </div>
                <div className="absolute top-full right-3 w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent border-t-gray-900/95"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
