import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const MODELS = [
	{ label: "GPT-4.1 (OpenAI)", value: "openai/gpt-4.1" },
	{ label: "GPT-4.1 Mini (OpenAI)", value: "openai/gpt-4.1-mini" },
	{ label: "GPT-4.1 Nano (OpenAI)", value: "openai/gpt-4.1-nano" },
	{ label: "Gemini Pro (Google)", value: "google/gemini-pro" },
	{
		label: "Gemini 2.5 Flash Lite (Google)",
		value: "google/gemini-2.5-flash-lite-preview-06-17",
	},
	{
		label: "Gemini 2.5 Flash (Google)",
		value: "google/gemini-2.5-flash-preview-05-20:thinking",
	},
	{ label: "Claude 3 Opus (Anthropic)", value: "anthropic/claude-3-opus" },
	// Add more models as needed
];

export function AIModelSelector({ model, setModel }) {
	return (
		<div className="mb-4">
			<label className="block text-sm font-medium mb-1">Model</label>
			<select
				value={model}
				onChange={e => setModel(e.target.value)}
				className="w-full border rounded px-2 py-1"
			>
				{MODELS.map(m => (
					<option key={m.value} value={m.value}>
						{m.label}
					</option>
				))}
			</select>
		</div>
	);
}

export function AISettingsModal({
	open,
	onClose,
	onSave,
	initialApiKey,
	initialModel,
}) {
	const [apiKey, setApiKey] = useState(initialApiKey || "");
	const [model, setModel] = useState(initialModel || MODELS[0].value);

	const handleSave = () => {
		onSave({ apiKey, model });
		onClose();
	};

	if (!open) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
				<h2 className="text-lg font-bold mb-4">AI Settings</h2>
				<div className="mb-4">
					<label className="block text-sm font-medium mb-1">
						OpenRouter API Key
					</label>
					<Input
						type="password"
						value={apiKey}
						onChange={e => setApiKey(e.target.value)}
						placeholder="sk-..."
						className="w-full"
					/>
				</div>
				<AIModelSelector model={model} setModel={setModel} />
				<div className="flex justify-end gap-2">
					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button onClick={handleSave}>Save</Button>
				</div>
			</div>
		</div>
	);
}
