import { BookMetadata } from '../types';
import { TASK_SPECIFIC_MODELS } from '../constants';

/**
 * Service for AI-powered enhancement of book metadata
 * Helps users improve titles, descriptions, and other metadata using AI
 */
export class AIEnhancementService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    const isValidKey = this.apiKey && 
                       !this.apiKey.includes('your_') && 
                       this.apiKey.length > 20;
                       
    if (!isValidKey) {
      console.warn('No valid OpenRouter API key found - AI enhancement features will not work');
    }
  }

  /**
   * Enhance a book title to make it more compelling and marketable
   */
  async enhanceTitle(currentTitle: string, genre?: string, targetAudience?: string): Promise<string[]> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const prompt = `You are a professional book title consultant. Given the current title "${currentTitle}", create 5 improved, more compelling and marketable book titles.

Consider:
- Genre: ${genre || 'General'}
- Target Audience: ${targetAudience || 'General readers'}
- The title should be clear, engaging, and memorable
- It should accurately reflect the book's content
- Make it appealing to the target audience
- Consider SEO and discoverability

Current title: "${currentTitle}"

Provide exactly 5 alternative titles, each on a new line, without numbering or bullet points. Focus on making them more specific, engaging, and professional.`;

    try {
      const response = await this.callAI(prompt, TASK_SPECIFIC_MODELS.enhancement);
      const titles = response.split('\n')
        .map(title => title.trim())
        .filter(title => title.length > 0 && !title.match(/^\d+\.?\s*/))
        .slice(0, 5);
      
      return titles.length > 0 ? titles : [currentTitle];
    } catch (error) {
      console.error('Error enhancing title:', error);
      return [currentTitle];
    }
  }

  /**
   * Enhance a book description to make it more detailed and compelling
   */
  async enhanceDescription(
    currentDescription: string, 
    title: string, 
    genre?: string, 
    targetAudience?: string,
    keywords?: string[]
  ): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const keywordText = keywords && keywords.length > 0 ? keywords.join(', ') : '';

    const prompt = `You are a professional book marketing consultant. Enhance the following book description to make it more compelling, detailed, and marketable.

Book Title: "${title}"
Genre: ${genre || 'General'}
Target Audience: ${targetAudience || 'General readers'}
Keywords: ${keywordText}

Current Description:
"${currentDescription}"

Create an enhanced description that:
1. Is 150-300 words long
2. Clearly explains what readers will learn or gain
3. Highlights the unique value proposition
4. Uses engaging, professional language
5. Appeals to the target audience
6. Incorporates relevant keywords naturally
7. Creates excitement and urgency to read
8. Follows best practices for book marketing

Provide only the enhanced description, no additional commentary.`;

    try {
      const response = await this.callAI(prompt, TASK_SPECIFIC_MODELS.enhancement);
      return response.trim();
    } catch (error) {
      console.error('Error enhancing description:', error);
      return currentDescription;
    }
  }

  /**
   * Generate enhanced keywords based on title, description, and genre
   */
  async enhanceKeywords(
    title: string, 
    description: string, 
    genre?: string, 
    currentKeywords?: string[]
  ): Promise<string[]> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const currentKeywordText = currentKeywords && currentKeywords.length > 0 
      ? currentKeywords.join(', ') 
      : 'None provided';

    const prompt = `You are an SEO and book marketing expert. Generate 10-15 relevant, high-impact keywords for this book that will help with discoverability and marketing.

Book Title: "${title}"
Genre: ${genre || 'General'}
Description: "${description}"
Current Keywords: ${currentKeywordText}

Generate keywords that are:
1. Relevant to the book's content and theme
2. Commonly searched by the target audience
3. Specific enough to be meaningful
4. Include both broad and niche terms
5. Mix of single words and short phrases (2-3 words max)
6. Avoid overly generic terms
7. Include industry-specific terminology where appropriate

Provide exactly 10-15 keywords, one per line, without numbering or bullet points.`;

    try {
      const response = await this.callAI(prompt, TASK_SPECIFIC_MODELS.enhancement);
      const keywords = response.split('\n')
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0 && !keyword.match(/^\d+\.?\s*/))
        .slice(0, 15);
      
      return keywords;
    } catch (error) {
      console.error('Error enhancing keywords:', error);
      return currentKeywords || [];
    }
  }

  /**
   * Enhance complete book metadata in one go
   */
  async enhanceCompleteMetadata(metadata: BookMetadata): Promise<Partial<BookMetadata>> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    try {
      const [enhancedTitles, enhancedDescription, enhancedKeywords] = await Promise.all([
        this.enhanceTitle(metadata.title, metadata.genre, metadata.targetAudience),
        this.enhanceDescription(
          metadata.description, 
          metadata.title, 
          metadata.genre, 
          metadata.targetAudience, 
          metadata.keywords
        ),
        this.enhanceKeywords(metadata.title, metadata.description, metadata.genre, metadata.keywords)
      ]);

      return {
        title: enhancedTitles[0] || metadata.title, // Use the first enhanced title
        description: enhancedDescription,
        keywords: [...new Set([...metadata.keywords, ...enhancedKeywords])], // Merge and deduplicate
        // Include alternative titles for user to choose from
        alternativeTitles: enhancedTitles.slice(1)
      };
    } catch (error) {
      console.error('Error enhancing complete metadata:', error);
      return {};
    }
  }

  /**
   * Generate a compelling subtitle based on title and description
   */
  async generateSubtitle(title: string, description: string, genre?: string): Promise<string[]> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const prompt = `You are a professional book marketing consultant. Generate 3-5 compelling subtitles for this book.

Title: "${title}"
Genre: ${genre || 'General'}
Description: "${description}"

Create subtitles that:
1. Complement and enhance the main title
2. Clarify what the book is about
3. Highlight the key benefit or outcome
4. Are concise but descriptive (5-10 words)
5. Appeal to the target audience
6. Follow industry best practices

Provide 3-5 subtitle options, one per line, without numbering or bullet points.`;

    try {
      const response = await this.callAI(prompt, TASK_SPECIFIC_MODELS.enhancement);
      const subtitles = response.split('\n')
        .map(subtitle => subtitle.trim())
        .filter(subtitle => subtitle.length > 0 && !subtitle.match(/^\d+\.?\s*/))
        .slice(0, 5);
      
      return subtitles;
    } catch (error) {
      console.error('Error generating subtitles:', error);
      return [];
    }
  }

  /**
   * Make an API call to the AI service
   */
  private async callAI(prompt: string, model: string): Promise<string> {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Verbira AI Book Generator'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2048,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from AI service');
    }

    return data.choices[0].message.content.trim();
  }
}

// Export singleton instance
export const aiEnhancementService = new AIEnhancementService();
