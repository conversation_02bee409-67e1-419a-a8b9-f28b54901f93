import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  BookContext,
  ChapterContext,
  GeneratedChapter,
  BookMetadata,
  GeneratedOutline
} from '../types';
import { CONTEXT_SETTINGS } from '../constants';

interface BookContextState {
  // Core context data
  bookContext: BookContext | null;
  chapterContexts: ChapterContext[];

  // Outline management
  chapterOutlines: Record<string, GeneratedOutline>; // Chapter ID -> Outline

  // Generation queue and status
  generationQueue: string[]; // Chapter IDs in generation order
  currentlyGenerating: string | null;
  generationProgress: Record<string, number>; // Chapter ID -> progress percentage

  // Context management
  contextTokenCount: number;
  maxContextTokens: number;
  
  // Actions
  initializeBookContext: (metadata: BookMetadata, chapterOutlines: string[]) => void;
  addChapterContext: (context: ChapterContext) => void;
  updateChapterContext: (chapterId: string, updates: Partial<ChapterContext>) => void;
  removeOldestContext: () => void;

  // Outline management
  setChapterOutline: (chapterId: string, outline: GeneratedOutline) => void;
  getChapterOutline: (chapterId: string) => GeneratedOutline | null;
  
  // Generation queue management
  setGenerationQueue: (chapterIds: string[]) => void;
  startChapterGeneration: (chapterId: string) => void;
  completeChapterGeneration: (chapterId: string, chapter: GeneratedChapter) => void;
  updateGenerationProgress: (chapterId: string, progress: number) => void;
  
  // Context optimization
  optimizeContext: () => void;
  getContextForChapter: (chapterId: string) => BookContext;
  createRollingContext: (allPreviousChapters: ChapterContext[], baseContext: string) => ChapterContext[];
  calculateTokenCount: (text: string) => number;
  
  // Reset and cleanup
  resetContext: () => void;
  clearCompletedChapters: () => void;
}

export const useBookContextStore = create<BookContextState>()(
  devtools(
    (set, get) => ({
      // Initial state
      bookContext: null,
      chapterContexts: [],
      chapterOutlines: {},
      generationQueue: [],
      currentlyGenerating: null,
      generationProgress: {},
      contextTokenCount: 0,
      maxContextTokens: CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS,

      // Initialize book context with metadata and outline
      initializeBookContext: (metadata: BookMetadata, chapterOutlines: string[]) => {
        const bookOutline = `
Book: ${metadata.title}
${metadata.subtitle ? `Subtitle: ${metadata.subtitle}` : ''}
Genre: ${metadata.genre}
Target Audience: ${metadata.targetAudience}
Tone: ${metadata.tone}
Description: ${metadata.description}

Chapter Structure:
${chapterOutlines.map((outline, index) => `${index + 1}. ${outline}`).join('\n')}
        `.trim();

        const newContext: BookContext = {
          bookOutline,
          previousChapters: [],
          currentChapter: '',
          totalWordCount: 0,
          maxContextTokens: CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS
        };

        set({
          bookContext: newContext,
          contextTokenCount: get().calculateTokenCount(bookOutline),
          chapterContexts: [],
          generationQueue: [],
          currentlyGenerating: null,
          generationProgress: {}
        });
      },

      // Add a new chapter context
      addChapterContext: (context: ChapterContext) => {
        const state = get();
        
        // Check if this chapter is already in the contexts
        const existingContextIndex = state.chapterContexts.findIndex(ctx => ctx.chapterId === context.chapterId);
        
        let newContexts;
        if (existingContextIndex !== -1) {
          // Replace existing context
          console.log(`Replacing existing context for chapter ${context.chapterId}`);
          newContexts = [...state.chapterContexts];
          newContexts[existingContextIndex] = context;
        } else {
          // Add new context
          console.log(`Adding new context for chapter ${context.chapterId}`);
          newContexts = [...state.chapterContexts, context];
        }
        
        // Sort contexts to maintain chapter order - handle multiple ID formats
        newContexts.sort((a, b) => {
          // Extract chapter numbers from IDs using regex to be more robust
          // First try to get a chapter number from the ID itself
          const aMatch = a.chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
          const bMatch = b.chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
          
          // Get the first capturing group that matched
          const aNum = aMatch ? parseInt(aMatch.slice(1).find(group => group !== undefined) || '0') : 0;
          const bNum = bMatch ? parseInt(bMatch.slice(1).find(group => group !== undefined) || '0') : 0;
          
          console.log(`Sorting chapters: ${a.chapterId} (${aNum}) vs ${b.chapterId} (${bNum})`);
          return aNum - bNum;
        });
        
        // Keep only the most recent chapters to manage context size
        const trimmedContexts = newContexts.slice(-CONTEXT_SETTINGS.MAX_PREVIOUS_CHAPTERS);
        
        // Update total word count
        const totalWordCount = trimmedContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
        
        // Update book context
        const updatedBookContext = state.bookContext ? {
          ...state.bookContext,
          previousChapters: trimmedContexts,
          totalWordCount
        } : null;

        set({
          chapterContexts: trimmedContexts,
          bookContext: updatedBookContext,
          contextTokenCount: state.calculateTokenCount(
            (updatedBookContext?.bookOutline || '') + 
            trimmedContexts.map(ctx => ctx.summary).join('\n')
          )
        });
      },

      // Update existing chapter context
      updateChapterContext: (chapterId: string, updates: Partial<ChapterContext>) => {
        const state = get();
        const updatedContexts = state.chapterContexts.map(ctx =>
          ctx.chapterId === chapterId ? { ...ctx, ...updates } : ctx
        );

        const totalWordCount = updatedContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
        
        const updatedBookContext = state.bookContext ? {
          ...state.bookContext,
          previousChapters: updatedContexts,
          totalWordCount
        } : null;

        set({
          chapterContexts: updatedContexts,
          bookContext: updatedBookContext
        });
      },

      // Remove oldest context to free up space
      removeOldestContext: () => {
        const state = get();
        if (state.chapterContexts.length > 0) {
          const newContexts = state.chapterContexts.slice(1);
          const totalWordCount = newContexts.reduce((sum, ctx) => sum + ctx.wordCount, 0);
          
          const updatedBookContext = state.bookContext ? {
            ...state.bookContext,
            previousChapters: newContexts,
            totalWordCount
          } : null;

          set({
            chapterContexts: newContexts,
            bookContext: updatedBookContext,
            contextTokenCount: state.calculateTokenCount(
              (updatedBookContext?.bookOutline || '') + 
              newContexts.map(ctx => ctx.summary).join('\n')
            )
          });
        }
      },

      // Set the generation queue
      setGenerationQueue: (chapterIds: string[]) => {
        set({
          generationQueue: chapterIds,
          generationProgress: chapterIds.reduce((acc, id) => ({ ...acc, [id]: 0 }), {})
        });
      },

      // Start generating a chapter
      startChapterGeneration: (chapterId: string) => {
        set({
          currentlyGenerating: chapterId,
          generationProgress: {
            ...get().generationProgress,
            [chapterId]: 10 // Initial progress
          }
        });
      },

      // Complete chapter generation and add to context
      completeChapterGeneration: (chapterId: string, chapter: GeneratedChapter) => {
        const state = get();
        
        // Create chapter context from generated chapter
        if (chapter.summary) {
          const chapterContext: ChapterContext = {
            chapterId: chapter.id,
            summary: chapter.summary,
            keyPoints: [], // Will be extracted from summary
            wordCount: chapter.wordCount || 0,
            generatedAt: new Date()
          };

          state.addChapterContext(chapterContext);
        }

        // Update generation status
        const updatedQueue = state.generationQueue.filter(id => id !== chapterId);
        const updatedProgress = { ...state.generationProgress };
        updatedProgress[chapterId] = 100;

        set({
          currentlyGenerating: updatedQueue.length > 0 ? updatedQueue[0] : null,
          generationQueue: updatedQueue,
          generationProgress: updatedProgress
        });
      },

      // Update generation progress
      updateGenerationProgress: (chapterId: string, progress: number) => {
        set({
          generationProgress: {
            ...get().generationProgress,
            [chapterId]: Math.min(100, Math.max(0, progress))
          }
        });
      },

      // Optimize context by removing old chapters if needed
      optimizeContext: () => {
        const state = get();
        
        while (state.contextTokenCount > state.maxContextTokens && state.chapterContexts.length > 1) {
          state.removeOldestContext();
        }
      },

      // Get context for a specific chapter generation
      getContextForChapter: (chapterId: string): BookContext => {
        const state = get();

        if (!state.bookContext) {
          console.error('Book context not initialized');
          // Create an empty context rather than throwing an error
          return {
            bookOutline: '',
            previousChapters: [],
            currentChapter: chapterId,
            totalWordCount: 0,
            maxContextTokens: CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS
          };
        }

        // Optimize context before returning
        state.optimizeContext();

        // Include outline information in context
        const chapterOutline = state.chapterOutlines[chapterId];
        const outlineContext = chapterOutline ? `
Chapter Outline:
Title: ${chapterOutline.title}
Description: ${chapterOutline.description}
Sections: ${chapterOutline.sections.map(s => `${s.order}. ${s.title} (${s.estimatedWordCount} words)`).join(', ')}
Key Points: ${chapterOutline.keyPoints.join(', ')}
` : '';

        // Extract chapter number from ID - handle different ID formats with improved regex
        const currentChapterMatch = chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
        // Get the first capturing group that matched
        const currentChapterNum = currentChapterMatch 
          ? parseInt(currentChapterMatch.slice(1).find(group => group !== undefined) || '0') 
          : 0;
        
        console.log(`Getting context for chapter ${chapterId} (extracted number ${currentChapterNum})`);

        // Filter previous chapters to only include those that come before this one
        let previousContexts = state.chapterContexts.filter(ctx => {
          // Extract chapter number from context ID with improved regex
          const ctxChapterMatch = ctx.chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
          // Get the first capturing group that matched
          const ctxChapterNum = ctxChapterMatch
            ? parseInt(ctxChapterMatch.slice(1).find(group => group !== undefined) || '0')
            : 0;

          return ctxChapterNum < currentChapterNum;
        });

        // Sort by chapter number to ensure proper order
        previousContexts.sort((a, b) => {
          const aMatch = a.chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
          const bMatch = b.chapterId.match(/chapter[^0-9]*(\d+)|chap[^0-9]*(\d+)|ch[^0-9]*(\d+)|[^0-9]*(\d+)/i);
          const aNum = aMatch ? parseInt(aMatch.slice(1).find(group => group !== undefined) || '0') : 0;
          const bNum = bMatch ? parseInt(bMatch.slice(1).find(group => group !== undefined) || '0') : 0;
          return aNum - bNum;
        });

        // Apply rolling context to respect token limits
        const rollingContext = state.createRollingContext(previousContexts, state.bookContext.bookOutline + outlineContext);

        console.log(`Providing context for chapter ${chapterId} with ${rollingContext.length} previous chapters (rolling context applied)`);
        console.log(`Previous chapters in context:`, rollingContext.map(ch => ch.chapterId));

        return {
          ...state.bookContext,
          currentChapter: chapterId,
          previousChapters: rollingContext,
          bookOutline: state.bookContext.bookOutline + outlineContext
        };
      },

      // Create rolling context that respects token limits
      createRollingContext: (allPreviousChapters: ChapterContext[], baseContext: string): ChapterContext[] => {
        const state = get();

        if (allPreviousChapters.length === 0) {
          return [];
        }

        // Calculate base context tokens
        let currentTokens = state.calculateTokenCount(baseContext);
        const maxTokens = CONTEXT_SETTINGS.MAX_CONTEXT_TOKENS;

        // Reserve tokens for the response
        const availableTokens = maxTokens - 1000; // Reserve 1000 tokens for response

        console.log(`Rolling context: Base tokens: ${currentTokens}, Available: ${availableTokens}`);

        // Start from the most recent chapters and work backwards
        const rollingChapters: ChapterContext[] = [];

        for (let i = allPreviousChapters.length - 1; i >= 0; i--) {
          const chapter = allPreviousChapters[i];
          const chapterTokens = state.calculateTokenCount(chapter.summary + chapter.keyPoints.join(' '));

          if (currentTokens + chapterTokens <= availableTokens) {
            rollingChapters.unshift(chapter); // Add to beginning to maintain order
            currentTokens += chapterTokens;
            console.log(`Added chapter ${chapter.chapterId} to rolling context (${chapterTokens} tokens, total: ${currentTokens})`);
          } else {
            console.log(`Skipping chapter ${chapter.chapterId} - would exceed token limit (${chapterTokens} tokens)`);
            break;
          }

          // Also respect the max previous chapters limit
          if (rollingChapters.length >= CONTEXT_SETTINGS.MAX_PREVIOUS_CHAPTERS) {
            console.log(`Reached max previous chapters limit (${CONTEXT_SETTINGS.MAX_PREVIOUS_CHAPTERS})`);
            break;
          }
        }

        console.log(`Rolling context created with ${rollingChapters.length} chapters, ${currentTokens} total tokens`);
        return rollingChapters;
      },

      // Set chapter outline
      setChapterOutline: (chapterId: string, outline: GeneratedOutline) => {
        set(state => ({
          chapterOutlines: {
            ...state.chapterOutlines,
            [chapterId]: outline
          }
        }));
      },

      // Get chapter outline
      getChapterOutline: (chapterId: string): GeneratedOutline | null => {
        const state = get();
        return state.chapterOutlines[chapterId] || null;
      },

      // Calculate approximate token count (rough estimation: 1 token ≈ 4 characters)
      calculateTokenCount: (text: string): number => {
        return Math.ceil(text.length / 4);
      },

      // Reset all context
      resetContext: () => {
        set({
          bookContext: null,
          chapterContexts: [],
          chapterOutlines: {},
          generationQueue: [],
          currentlyGenerating: null,
          generationProgress: {},
          contextTokenCount: 0
        });
      },

      // Clear completed chapters from queue
      clearCompletedChapters: () => {
        const state = get();
        const incompleteChapters = state.generationQueue.filter(
          id => state.generationProgress[id] < 100
        );
        
        set({
          generationQueue: incompleteChapters,
          currentlyGenerating: incompleteChapters.length > 0 ? incompleteChapters[0] : null
        });
      }
    }),
    {
      name: 'book-context-store',
      partialize: (state) => ({
        // Only persist essential data, not the entire state
        bookContext: state.bookContext,
        chapterContexts: state.chapterContexts,
        chapterOutlines: state.chapterOutlines,
        generationProgress: state.generationProgress
      })
    }
  )
);
