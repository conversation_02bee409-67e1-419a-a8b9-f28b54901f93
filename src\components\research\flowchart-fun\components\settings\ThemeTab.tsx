/**
 * Themes Settings Tab
 * Manage and apply theme presets
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Palette, Download, Upload, Save, Eye } from 'lucide-react';
import { SettingsTabProps, FFTheme } from '../../types';
import { themePresets, createCustomTheme } from '../../themes/defaultTheme';

const ThemeTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const [customThemeName, setCustomThemeName] = useState('');
  const [customThemeDescription, setCustomThemeDescription] = useState('');
  const [importThemeJson, setImportThemeJson] = useState('');
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);

  const handleApplyPreset = (presetTheme: FFTheme) => {
    onThemeChange(presetTheme);
  };

  const handleExportTheme = () => {
    const dataStr = JSON.stringify(theme, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `flowchart-theme-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportTheme = () => {
    try {
      const importedTheme = JSON.parse(importThemeJson);
      onThemeChange(importedTheme);
      setImportThemeJson('');
      setIsImportDialogOpen(false);
    } catch (error) {
      alert('Invalid JSON format. Please check your theme data.');
    }
  };

  const handleSaveCustomTheme = () => {
    // In a real app, this would save to localStorage or a backend
    const customTheme = {
      id: `custom-${Date.now()}`,
      name: customThemeName,
      description: customThemeDescription,
      theme: theme,
    };
    
    // For now, just export it
    const dataStr = JSON.stringify(customTheme, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${customThemeName.toLowerCase().replace(/\s+/g, '-')}-theme.json`;
    link.click();
    URL.revokeObjectURL(url);
    
    setCustomThemeName('');
    setCustomThemeDescription('');
    setIsSaveDialogOpen(false);
  };

  const ThemePreview: React.FC<{ presetTheme: FFTheme; name: string }> = ({ presetTheme, name }) => (
    <div 
      className="w-full h-24 rounded-lg border-2 p-3 relative overflow-hidden"
      style={{ backgroundColor: presetTheme.background }}
    >
      {/* Sample Node */}
      <div
        className="absolute top-2 left-2 px-2 py-1 text-xs rounded"
        style={{
          backgroundColor: presetTheme.nodeBackground,
          color: presetTheme.nodeForeground,
          borderWidth: `${presetTheme.borderWidth}px`,
          borderColor: presetTheme.borderColor,
          borderStyle: 'solid',
          fontFamily: presetTheme.fontFamily,
        }}
      >
        Node
      </div>
      
      {/* Sample Edge */}
      <div
        className="absolute top-6 left-12 w-8 h-0"
        style={{
          borderTopWidth: `${presetTheme.edgeWidth}px`,
          borderTopColor: presetTheme.edgeColor,
          borderTopStyle: 'solid',
        }}
      />
      
      {/* Arrow */}
      <div
        className="absolute top-5 right-8 w-0 h-0"
        style={{
          borderLeft: `4px solid ${presetTheme.edgeColor}`,
          borderTop: '2px solid transparent',
          borderBottom: '2px solid transparent',
        }}
      />
      
      {/* Second Node */}
      <div
        className="absolute top-2 right-2 px-2 py-1 text-xs rounded"
        style={{
          backgroundColor: presetTheme.nodeBackground,
          color: presetTheme.nodeForeground,
          borderWidth: `${presetTheme.borderWidth}px`,
          borderColor: presetTheme.borderColor,
          borderStyle: 'solid',
          fontFamily: presetTheme.fontFamily,
        }}
      >
        Node
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme Presets
          </CardTitle>
          <CardDescription>
            Choose from pre-designed themes or create your own
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {themePresets.map((preset) => (
              <Card key={preset.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">{preset.name}</CardTitle>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleApplyPreset(preset.theme)}
                      className="h-6 px-2 text-xs"
                    >
                      Apply
                    </Button>
                  </div>
                  <CardDescription className="text-xs">
                    {preset.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <ThemePreview presetTheme={preset.theme} name={preset.name} />
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Theme Preview</CardTitle>
          <CardDescription>
            Preview of your current theme settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <ThemePreview presetTheme={theme} name="Current Theme" />
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <Label className="text-xs text-muted-foreground">Layout</Label>
                <Badge variant="outline" className="w-full justify-center">
                  {theme.layoutName}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Direction</Label>
                <Badge variant="outline" className="w-full justify-center">
                  {theme.direction}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Shape</Label>
                <Badge variant="outline" className="w-full justify-center">
                  {theme.shape}
                </Badge>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Curve</Label>
                <Badge variant="outline" className="w-full justify-center">
                  {theme.curveStyle}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Theme Management</CardTitle>
          <CardDescription>
            Save, export, and import custom themes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save as Custom Theme
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Save Custom Theme</DialogTitle>
                  <DialogDescription>
                    Save your current settings as a custom theme
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="theme-name">Theme Name</Label>
                    <Input
                      id="theme-name"
                      value={customThemeName}
                      onChange={(e) => setCustomThemeName(e.target.value)}
                      placeholder="My Custom Theme"
                    />
                  </div>
                  <div>
                    <Label htmlFor="theme-description">Description</Label>
                    <Textarea
                      id="theme-description"
                      value={customThemeDescription}
                      onChange={(e) => setCustomThemeDescription(e.target.value)}
                      placeholder="Describe your theme..."
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleSaveCustomTheme}
                      disabled={!customThemeName.trim()}
                    >
                      Save Theme
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Button 
              variant="outline" 
              onClick={handleExportTheme}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export Current Theme
            </Button>

            <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Import Theme
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Import Theme</DialogTitle>
                  <DialogDescription>
                    Paste your theme JSON data below
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="theme-json">Theme JSON</Label>
                    <Textarea
                      id="theme-json"
                      value={importThemeJson}
                      onChange={(e) => setImportThemeJson(e.target.value)}
                      placeholder="Paste theme JSON here..."
                      rows={10}
                      className="font-mono text-sm"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleImportTheme}
                      disabled={!importThemeJson.trim()}
                    >
                      Import Theme
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThemeTab;
