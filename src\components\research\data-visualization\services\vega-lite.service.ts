// Vega-Lite AI Service for generating visualizations using Google Gemini
import { GoogleGenAI } from '@google/genai';
import { ChatMessage, VegaLiteSpec, UploadedFile, DataField } from '../types';
import { inferDataFields } from '../utils/vega-utils';

interface VegaLiteGenerationRequest {
  messages: ChatMessage[];
  file: UploadedFile;
  userPrompt: string;
}

interface VegaLiteGenerationResponse {
  success: boolean;
  content: string;
  vegaSpec?: VegaLiteSpec;
  error?: string;
}

export class VegaLiteService {
  private ai: GoogleGenAI;
  private model: string = 'gemini-2.5-pro';

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('VITE_GEMINI_API_KEY is not configured');
    }
    this.ai = new GoogleGenAI({ apiKey });
  }

  /**
   * Generate Vega-Lite visualization specification using Gemini
   */
  async generateVisualization(request: VegaLiteGenerationRequest): Promise<VegaLiteGenerationResponse> {
    try {
      const fields = inferDataFields(request.file);
      const systemPrompt = this.buildSystemPrompt(fields);
      const conversationContext = this.buildConversationContext(request.messages);
      const fullPrompt = `${systemPrompt}\n\n${conversationContext}\n\nUser: ${request.userPrompt}`;

      const requestConfig = {
        generationConfig: {
          maxOutputTokens: 2048,
          temperature: 0.1
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: fullPrompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: this.model,
        config: requestConfig,
        contents,
      });

      let content = '';
      for await (const chunk of response) {
        if (chunk.text) {
          content += chunk.text;
        }
      }

      return {
        success: true,
        content,
      };
    } catch (error) {
      console.error('Vega-Lite generation error:', error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Build system prompt for Vega-Lite generation
   */
  private buildSystemPrompt(fields: DataField[]): string {
    const fieldDescriptions = fields
      .map(field => `${field.name} (${field.semanticType})`)
      .join(', ');

    return `You are a great assistant at vega-lite visualization creation. No matter what the user asks, you should always respond with a valid vega-lite specification in JSON format.

You should create the vega-lite specification based on user's query and the conversation context.

Requirements:
1. Do not include the key called 'data' in vega-lite specification.
2. If the user asks many times, you should generate the specification based on the previous context.
3. You should consider aggregating the field if it is quantitative and the chart has a mark type of rect, bar, line, area or arc.
4. Consider using bin for field if it is a chart like heatmap or histogram.
5. The available fields in the dataset and their types are: ${fieldDescriptions}

If the field is aggregated or transformed, the field title in spec should contain both the aggregate/transform info and the column title. For example, the title of field sales aggregated by mean should be "Mean(Sales)".

Always respond with only the JSON specification, wrapped in triple backticks with json language identifier.`;
  }

  /**
   * Build conversation context from previous messages
   */
  private buildConversationContext(messages: ChatMessage[]): string {
    if (messages.length === 0) return '';

    const context = messages
      .slice(-6) // Keep last 6 messages for context
      .map(msg => {
        if (msg.role === 'user') {
          return `User: ${msg.content}`;
        } else {
          return `Assistant: ${msg.content}`;
        }
      })
      .join('\n');

    return `Previous conversation:\n${context}`;
  }

  /**
   * Test the API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const requestConfig = {
        generationConfig: {
          maxOutputTokens: 100,
          temperature: 0.1
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: 'Say "Hello, API test successful!" and nothing else.' }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: this.model,
        config: requestConfig,
        contents,
      });

      let text = '';
      for await (const chunk of response) {
        if (chunk.text) {
          text += chunk.text;
        }
      }

      if (text && text.trim().length > 0) {
        return { success: true };
      } else {
        return { success: false, error: 'Empty response from API' };
      }
    } catch (error) {
      console.error('API test error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Generate a simple chart recommendation based on data
   */
  async generateChartRecommendation(file: UploadedFile): Promise<VegaLiteGenerationResponse> {
    const fields = inferDataFields(file);
    const quantitativeFields = fields.filter(f => f.semanticType === 'quantitative');
    const nominalFields = fields.filter(f => f.semanticType === 'nominal');
    const temporalFields = fields.filter(f => f.semanticType === 'temporal');

    let recommendationPrompt = `Based on the dataset with fields: ${fields.map(f => `${f.name} (${f.semanticType})`).join(', ')}, `;

    if (temporalFields.length > 0 && quantitativeFields.length > 0) {
      recommendationPrompt += `create a line chart showing ${quantitativeFields[0].name} over ${temporalFields[0].name}.`;
    } else if (nominalFields.length > 0 && quantitativeFields.length > 0) {
      recommendationPrompt += `create a bar chart showing ${quantitativeFields[0].name} by ${nominalFields[0].name}.`;
    } else if (quantitativeFields.length >= 2) {
      recommendationPrompt += `create a scatter plot showing ${quantitativeFields[0].name} vs ${quantitativeFields[1].name}.`;
    } else {
      recommendationPrompt += `create an appropriate visualization for this data.`;
    }

    return this.generateVisualization({
      messages: [],
      file,
      userPrompt: recommendationPrompt
    });
  }
}

// Export singleton instance
export const vegaLiteService = new VegaLiteService();
