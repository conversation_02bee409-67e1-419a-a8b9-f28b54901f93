# Data Visualization and Analysis Platform

A comprehensive AI-powered data visualization and analysis platform integrated with Google Gemini's code execution capabilities for secure server-side data processing.

## Features

### 🔄 File Upload System
- **Supported Formats**: CSV (.csv), Excel (.xlsx, .xls)
- **File Validation**: Size limits (50MB max), format validation, data integrity checks
- **Drag & Drop**: Intuitive file upload with drag-and-drop support
- **Progress Tracking**: Real-time upload and processing progress

### 🧠 Automated Data Analysis Pipeline
- **Step 1**: Automatic data structure analysis (data types, missing values, basic statistics)
- **Step 2**: AI-powered insights generation using Google Gemini
- **Step 3**: Interactive visualization creation with Plotly.js

### 📊 Interactive Visualizations
- **Chart Types**: Histograms, scatter plots, correlation heatmaps, box plots, bar charts, line charts, pie charts
- **Interactive Features**: Zoom, pan, hover tooltips, fullscreen mode
- **Export Options**: PNG, SVG, PDF download formats
- **Responsive Design**: Adapts to different screen sizes

### 💬 Natural Language Query System
- **AI-Powered**: Ask questions about your data in plain English
- **Code Generation**: Gemini generates Python analysis code
- **Secure Execution**: Code runs on Google's servers, not locally
- **Rich Responses**: Text answers with optional visualizations

## Architecture

### Component Structure
```
data-visualization/
├── DataVisualizationPlatform.tsx    # Main platform component
├── components/
│   ├── FileUploader.tsx             # File upload interface
│   ├── AnalysisPipeline.tsx         # Analysis workflow
│   ├── VisualizationGallery.tsx     # Chart display gallery
│   ├── QueryInterface.tsx           # Natural language queries
│   ├── PlotlyChart.tsx              # Individual chart component
│   ├── ErrorBoundary.tsx            # Error handling
│   └── LoadingStates.tsx            # Loading indicators
├── services/
│   ├── file-processing.service.ts   # File parsing and validation
│   └── gemini-analysis.service.ts   # AI analysis integration
├── stores/
│   └── data-visualization.store.ts  # Zustand state management
├── types.ts                         # TypeScript interfaces
├── constants.ts                     # Configuration constants
└── index.ts                         # Module exports
```

### State Management
Uses Zustand for efficient state management with the following key states:
- **File Management**: Upload status, file metadata, processing state
- **Analysis Results**: Generated insights, visualizations, statistics
- **Query System**: Chat history, query responses, processing status
- **UI State**: Active tabs, selected visualizations, loading states

### Integration Points
- **Sidebar Navigation**: Added to research tools menu
- **ResearchDashboard**: Integrated as new route
- **Error Handling**: Comprehensive error boundary and user feedback
- **Authentication**: Respects existing auth context

## Usage

### Basic Workflow
1. **Upload Data**: Drag and drop or select CSV/Excel files
2. **Run Analysis**: Automated pipeline analyzes data structure and generates insights
3. **Explore Visualizations**: Interactive charts created automatically
4. **Ask Questions**: Natural language queries for deeper analysis

### Example Queries
- "What are the main patterns in this data?"
- "Show me the correlation between sales and marketing spend"
- "Are there any outliers in the revenue column?"
- "Create a visualization of customer segments by region"
- "What's the trend over time for this metric?"

## Configuration

### Environment Variables
```bash
# Required for AI analysis
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

### File Limits
- **Maximum file size**: 50MB
- **Minimum rows**: 10 rows for analysis
- **Supported formats**: CSV, XLSX, XLS
- **Column limit for correlation**: 20 columns

### Visualization Limits
- **Max categories in pie chart**: 10
- **Max points in scatter plot**: 10,000
- **Default chart dimensions**: 600x400px

## Security

### Data Privacy
- **Local Processing**: File parsing happens in browser
- **Secure Transmission**: Data sent to Gemini API over HTTPS
- **No Storage**: Files not permanently stored on servers
- **Code Execution**: Runs on Google's secure infrastructure

### Error Handling
- **Input Validation**: File format and size validation
- **Error Boundaries**: React error boundaries prevent crashes
- **User Feedback**: Clear error messages and recovery options
- **Logging**: Development and production error logging

## Dependencies

### Core Dependencies
```json
{
  "@google/genai": "^latest",
  "plotly.js-dist-min": "^latest",
  "react-plotly.js": "^latest",
  "xlsx": "^latest",
  "papaparse": "^latest",
  "react-dropzone": "^latest",
  "zustand": "^latest"
}
```

### Peer Dependencies
- React 18+
- TypeScript 5+
- Tailwind CSS
- Radix UI components

## Development

### Local Setup
1. Install dependencies: `npm install`
2. Set environment variables in `.env.local`
3. Start development server: `npm run dev`

### Testing
```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e
```

### Building
```bash
# Development build
npm run build:dev

# Production build
npm run build
```

## API Integration

### Google Gemini Configuration
```typescript
const config = {
  thinkingConfig: {
    thinkingBudget: -1,
  },
  tools: [{ codeExecution: {} }],
  responseMimeType: 'text/plain',
};
```

### File Processing Pipeline
1. **Validation**: Check file type, size, and format
2. **Parsing**: Extract data using appropriate parser (CSV/Excel)
3. **Type Detection**: Automatically detect column data types
4. **Statistics**: Calculate basic statistical measures
5. **Analysis**: Generate AI insights and visualizations

## Troubleshooting

### Common Issues

**File Upload Fails**
- Check file size (must be < 50MB)
- Verify file format (CSV, XLSX, XLS only)
- Ensure file is not corrupted

**Analysis Errors**
- Verify Gemini API key is set
- Check network connectivity
- Ensure data has minimum 10 rows

**Visualization Issues**
- Clear browser cache
- Check for JavaScript errors in console
- Verify Plotly.js loaded correctly

**Query System Problems**
- Keep queries under 1000 characters
- Use clear, specific language
- Check API rate limits

### Performance Optimization
- **Large Files**: Consider splitting into smaller chunks
- **Many Columns**: Limit correlation analysis to key variables
- **Complex Queries**: Break into simpler questions
- **Memory Usage**: Refresh page for very large datasets

## Contributing

### Code Style
- Follow existing TypeScript patterns
- Use functional components with hooks
- Implement proper error handling
- Add comprehensive type definitions

### Testing Requirements
- Unit tests for all services
- Integration tests for components
- E2E tests for critical workflows
- Error boundary testing

### Documentation
- Update README for new features
- Add JSDoc comments for complex functions
- Include usage examples
- Document breaking changes

## License

This module is part of the Paper Genius Platform and follows the same licensing terms.
