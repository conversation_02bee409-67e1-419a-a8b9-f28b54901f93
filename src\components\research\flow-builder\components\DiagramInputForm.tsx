/**
 * Modern Diagram Input Interface
 * Clean, prompt-based interface for AI-powered diagram generation
 */

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DiagramInputFormProps,
  DiagramInput,
  DiagramType
} from '../types';
import { DIAGRAM_TYPE_CONFIG, VALIDATION_RULES } from '../constants';
import { diagramHistoryService, UserDiagram } from '../services/diagram-history.service';
import {
  Sparkles,
  Lightbulb,
  ArrowRight,
  GitBranch,
  Zap,
  Send,
  X,
  History,
  Star,
  Clock,
  ChevronDown,
  ChevronUp,
  Trash2,
  Eye,
  Download,
  Heart,
  Search,
  Filter,
  Plus,
  Minimize2,
  Maximize2,
  Edit3
} from 'lucide-react';

// AI Research Assistant Prompts - Enhanced for better research support
const RESEARCH_PROMPTS = [
  {
    title: "Research Methodology",
    prompt: "Create a comprehensive research methodology flowchart for my study. Include: literature review → hypothesis formation → research design → data collection → statistical analysis → results interpretation → conclusion and publication. Show decision points for methodology selection and validation steps.",
    icon: <GitBranch className="h-4 w-4" />,
    category: "methodology"
  },
  {
    title: "Data Analysis Pipeline",
    prompt: "Design a complete data analysis workflow: raw data → data cleaning & preprocessing → exploratory data analysis → statistical testing → visualization → interpretation → reporting. Include quality checks and validation at each step.",
    icon: <Zap className="h-4 w-4" />,
    category: "analysis"
  },
  {
    title: "Literature Review Process",
    prompt: "Create a systematic literature review flowchart: research question → search strategy → database searching → screening (title/abstract) → full-text assessment → quality appraisal → data extraction → synthesis → report writing. Include PRISMA guidelines.",
    icon: <Lightbulb className="h-4 w-4" />,
    category: "review"
  },
  {
    title: "Experimental Design",
    prompt: "Design an experimental research flowchart: problem identification → hypothesis → variable definition → control/experimental groups → randomization → data collection → statistical analysis → results → conclusion. Include ethical considerations.",
    icon: <GitBranch className="h-4 w-4" />,
    category: "experiment"
  },
  {
    title: "Survey Research",
    prompt: "Create a survey research methodology flowchart: objectives → questionnaire design → pilot testing → sample selection → data collection → validation → analysis → interpretation → reporting. Include response rate optimization.",
    icon: <Zap className="h-4 w-4" />,
    category: "survey"
  },
  {
    title: "Qualitative Research",
    prompt: "Design a qualitative research process: research question → theoretical framework → participant selection → data collection (interviews/focus groups) → transcription → coding → theme development → validation → findings interpretation.",
    icon: <Lightbulb className="h-4 w-4" />,
    category: "qualitative"
  }
];

export function DiagramInputForm({
  onGenerate,
  isGenerating,
  templates,
  onApplyTemplate,
  currentDiagram,
  onEditDiagram
}: DiagramInputFormProps) {
  const [description, setDescription] = useState('');
  const [selectedType, setSelectedType] = useState<DiagramType | undefined>(undefined);
  const [isOpen, setIsOpen] = useState(!currentDiagram); // Open by default when no diagram
  const [showTemplates, setShowTemplates] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // History state
  const [showHistory, setShowHistory] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [historyDiagrams, setHistoryDiagrams] = useState<UserDiagram[]>([]);

  // Edit state
  const [showEdit, setShowEdit] = useState(false);
  const [editPrompt, setEditPrompt] = useState('');



  const handleSubmit = async () => {
    if (description.trim().length < VALIDATION_RULES.MIN_DESCRIPTION_LENGTH) {
      return;
    }

    const input: DiagramInput = {
      description: description.trim(),
      type: selectedType,
      direction: 'TD'
    };

    onGenerate(input);
    setDescription(''); // Clear after generation


  };

  const handlePromptSelect = (prompt: string) => {
    // Prevent page scroll by maintaining current position
    const currentScrollPosition = window.scrollY;
    setDescription(prompt);
    setShowTemplates(false); // Hide templates after selection

    // Restore scroll position after state update
    requestAnimationFrame(() => {
      window.scrollTo(0, currentScrollPosition);
    });
  };

  // Load diagram history
  const loadHistory = async () => {
    setHistoryLoading(true);
    try {
      const result = await diagramHistoryService.getUserDiagrams({
        search: searchQuery,
        limit: 20
      });
      setHistoryDiagrams(result.diagrams);
    } catch (error) {
      console.error('Failed to load history:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Handle history diagram selection
  const handleHistorySelect = (diagram: UserDiagram) => {
    setDescription(diagram.description);
    setSelectedType(diagram.diagram_type as DiagramType);
    setShowHistory(false);
  };

  // Toggle favorite status
  const toggleFavorite = async (diagramId: string, currentStatus: boolean) => {
    try {
      await diagramHistoryService.toggleFavorite(diagramId, !currentStatus);
      loadHistory(); // Refresh the list
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  // Handle edit submission
  const handleEditSubmit = () => {
    if (editPrompt.trim() && onEditDiagram) {
      onEditDiagram(editPrompt.trim());
      setEditPrompt('');
      setShowEdit(false);
    }
  };

  // Load history when component mounts or search query changes
  useEffect(() => {
    if (showHistory) {
      loadHistory();
    }
  }, [showHistory, searchQuery]);



  const isValid = description.trim().length >= VALIDATION_RULES.MIN_DESCRIPTION_LENGTH;

  // Get diagram type options
  const diagramTypeOptions = Object.entries(DIAGRAM_TYPE_CONFIG).map(([key, config]) => ({
    value: key as DiagramType,
    label: config.name,
    description: config.description,
    icon: config.icon
  }));

  return (
    <div className="h-full flex flex-col">
      {/* Sidebar Header */}
      <div className="bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 p-4 text-white relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-20 h-20 bg-white rounded-full -translate-x-10 -translate-y-10 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-16 h-16 bg-white rounded-full translate-x-8 translate-y-8 animate-pulse delay-1000"></div>
        </div>

        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30">
              <Sparkles className="h-5 w-5 animate-pulse" />
            </div>
            <div>
              <h3 className="font-bold text-lg">AI Research Assistant</h3>
              <p className="text-xs text-blue-100 flex items-center gap-2">
                <span className="w-1.5 h-1.5 bg-green-300 rounded-full animate-pulse"></span>
                Ready to create diagrams
              </p>
            </div>
          </div>

          {/* Enhanced Tab Navigation */}
          <div className="flex items-center gap-1 bg-white/10 rounded-lg p-1">
            <button
              onClick={() => {
                setShowTemplates(false);
                setShowHistory(false);
                setShowEdit(false);
              }}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                !showTemplates && !showHistory && !showEdit
                  ? 'bg-white/90 text-blue-600 shadow-sm'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              <Plus className="h-4 w-4" />
              Create
            </button>
            <button
              onClick={() => {
                setShowTemplates(true);
                setShowHistory(false);
                setShowEdit(false);
              }}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                showTemplates
                  ? 'bg-white/90 text-blue-600 shadow-sm'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              <Lightbulb className="h-4 w-4" />
              Templates
            </button>
            <button
              onClick={() => {
                setShowTemplates(false);
                setShowHistory(true);
                setShowEdit(false);
              }}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                showHistory
                  ? 'bg-white/90 text-blue-600 shadow-sm'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              <History className="h-4 w-4" />
              History
            </button>
            {currentDiagram && (
              <button
                onClick={() => {
                  setShowTemplates(false);
                  setShowHistory(false);
                  setShowEdit(true);
                }}
                className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                  showEdit
                    ? 'bg-white/90 text-blue-600 shadow-sm'
                    : 'text-white/80 hover:text-white hover:bg-white/10'
                }`}
              >
                <Edit3 className="h-4 w-4" />
                Edit
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-hidden bg-gradient-to-b from-gray-50/30 to-white">
        <ScrollArea className="h-full p-4">



          {/* Enhanced Templates Gallery */}
          {showTemplates && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Lightbulb className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">Research Templates</h4>
                  <p className="text-sm text-gray-600">Professional diagram templates</p>
                </div>
              </div>

              {/* Template Categories */}
              <div className="flex flex-wrap gap-2 mb-4">
                {['methodology', 'analysis', 'review', 'experiment', 'survey', 'qualitative'].map((category) => (
                  <Badge
                    key={category}
                    variant="outline"
                    className="text-xs bg-white/80 hover:bg-blue-50 border-gray-300/50 hover:border-blue-300 transition-all duration-200 cursor-pointer capitalize"
                  >
                    {category}
                  </Badge>
                ))}
              </div>

              {/* Templates Grid */}
              <div className="grid gap-3">
                {RESEARCH_PROMPTS.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => handlePromptSelect(prompt.prompt)}
                    className="w-full p-4 text-left bg-gradient-to-r from-white via-blue-50/30 to-indigo-50/30 rounded-xl hover:from-blue-50 hover:via-blue-100/50 hover:to-indigo-100/50 transition-all duration-300 border border-gray-200/50 hover:border-blue-300/50 group hover:shadow-lg hover:-translate-y-0.5 relative overflow-hidden"
                  >
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                      <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500 rounded-full -translate-y-10 translate-x-10"></div>
                    </div>

                    <div className="relative flex items-start gap-3">
                      <div className="p-3 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl text-blue-600 group-hover:from-blue-200 group-hover:to-blue-300 transition-all duration-300 group-hover:scale-105 shadow-sm">
                        {prompt.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h5 className="font-bold text-gray-900 text-sm group-hover:text-blue-900 transition-colors">{prompt.title}</h5>
                          <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-200 flex-shrink-0 ml-2" />
                        </div>
                        <p className="text-xs text-gray-600 line-clamp-3 mb-3 leading-relaxed">{prompt.prompt}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary" className="text-xs bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 border-blue-200/50 shadow-sm">
                            {prompt.category}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Sparkles className="h-3 w-3" />
                            <span>AI Ready</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

                    {/* Template Stats */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-xl border border-blue-200/30">
                      <div className="flex items-center justify-center gap-6 text-sm">
                        <div className="text-center">
                          <div className="font-bold text-blue-800">{RESEARCH_PROMPTS.length}</div>
                          <div className="text-blue-600 text-xs">Templates</div>
                        </div>
                        <div className="w-px h-8 bg-blue-300/50"></div>
                        <div className="text-center">
                          <div className="font-bold text-blue-800">6</div>
                          <div className="text-blue-600 text-xs">Categories</div>
                        </div>
                        <div className="w-px h-8 bg-blue-300/50"></div>
                        <div className="text-center">
                          <div className="font-bold text-blue-800">100%</div>
                          <div className="text-blue-600 text-xs">AI Powered</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* History Section */}
                {showHistory && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <History className="h-4 w-4 text-blue-600" />
                        <h4 className="text-sm font-semibold text-gray-800">Recent Diagrams</h4>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={loadHistory}
                        disabled={historyLoading}
                        className="h-6 px-2 text-xs"
                      >
                        {historyLoading ? <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" /> : 'Refresh'}
                      </Button>
                    </div>

                    {/* Search */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search diagrams..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && loadHistory()}
                        className="w-full pl-8 pr-3 py-2 text-xs border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                      />
                    </div>

                    {/* History List */}
                    <div className="space-y-2">
                      {historyDiagrams.length === 0 && !historyLoading && (
                        <div className="text-center py-8 text-gray-500">
                          <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No diagrams yet</p>
                          <p className="text-xs">Create your first diagram to see it here</p>
                        </div>
                      )}

                      {historyDiagrams.map((diagram) => (
                        <div
                          key={diagram.id}
                          className="p-3 bg-gray-50 rounded-xl hover:bg-blue-50 transition-colors border border-gray-200 hover:border-blue-300 group cursor-pointer"
                          onClick={() => handleHistorySelect(diagram)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h5 className="font-medium text-gray-900 text-sm truncate">{diagram.title}</h5>
                                {diagram.is_favorite && <Heart className="h-3 w-3 text-red-500 fill-current" />}
                              </div>
                              <p className="text-xs text-gray-600 line-clamp-2 mb-2">{diagram.description}</p>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {diagram.diagram_type}
                                </Badge>
                                <span className="text-xs text-gray-500 flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {new Date(diagram.created_at).toLocaleDateString()}
                                </span>
                                <span className="text-xs text-gray-500 flex items-center gap-1">
                                  <Eye className="h-3 w-3" />
                                  {diagram.view_count}
                                </span>
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleFavorite(diagram.id, diagram.is_favorite);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-white rounded"
                            >
                              <Star className={`h-3 w-3 ${diagram.is_favorite ? 'text-yellow-500 fill-current' : 'text-gray-400'}`} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Edit Section */}
                {showEdit && currentDiagram && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-3">
                      <GitBranch className="h-4 w-4 text-purple-600" />
                      <h4 className="text-sm font-semibold text-gray-800">Edit Current Diagram</h4>
                    </div>

                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-blue-600 font-medium text-xs">Current:</span>
                        <span className="text-blue-800 text-xs">{currentDiagram.metadata.title}</span>
                      </div>
                      <p className="text-blue-700 text-xs">{currentDiagram.metadata.description}</p>
                    </div>

                    <div className="space-y-3">
                      <Textarea
                        value={editPrompt}
                        onChange={(e) => setEditPrompt(e.target.value)}
                        placeholder="Describe what you want to change or add to the current diagram:

• 'Add error handling steps after data validation'
• 'Change the flow to include peer review process'
• 'Add decision points for quality control'
• 'Modify the timeline to include buffer time'
• 'Update labels to be more descriptive'"
                        className="min-h-[120px] resize-none border-gray-300 focus:border-purple-500 focus:ring-purple-500 text-sm rounded-xl"
                      />

                      <Button
                        onClick={handleEditSubmit}
                        disabled={!editPrompt.trim() || isGenerating}
                        className="w-full bg-gradient-to-r from-purple-500 via-purple-600 to-blue-600 hover:from-purple-600 hover:via-purple-700 hover:to-blue-700 text-white rounded-xl py-3 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        {isGenerating ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            Editing...
                          </>
                        ) : (
                          <>
                            <GitBranch className="h-4 w-4" />
                            Apply Changes
                          </>
                        )}
                      </Button>

                      {editPrompt.trim() && (
                        <div className="flex items-center gap-2 p-2 bg-purple-50 border border-purple-200 rounded-lg">
                          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                          <p className="text-xs text-purple-700">Ready to apply changes!</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

          {/* Enhanced Create/Input Section */}
          {!showTemplates && !showHistory && !showEdit && (
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Plus className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">Create New Diagram</h4>
                  <p className="text-sm text-gray-600">Describe your research diagram and let AI create it</p>
                </div>
              </div>

              <div className="space-y-5">
                <div className="relative">
                  <label className="text-sm font-semibold text-gray-700 mb-2 block">
                    📝 Description
                  </label>
                  <Textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder={currentDiagram
                      ? "What would you like to create or modify?\n\n• Add error handling steps\n• Create survey methodology\n• Design clinical trial flow"
                      : "Describe your research diagram...\n\n• Literature review process\n• Data analysis workflow\n• Mixed-methods design\n• Experimental methodology"
                    }
                    className="min-h-[120px] resize-none border-gray-300/50 focus:border-blue-500 focus:ring-blue-500/20 text-sm rounded-xl bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200 hover:shadow-md"
                  />
                  <div className="absolute bottom-3 right-3 text-xs text-gray-400 bg-white/90 px-2 py-1 rounded-md">
                    {description.length} chars
                  </div>
                </div>

                {/* Enhanced Type Selection */}
                <div className="space-y-3">
                  <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <span>🎯</span>
                    Diagram Type
                  </label>
                  <Select
                    value={selectedType || 'auto'}
                    onValueChange={(value) => {
                      setSelectedType(value === 'auto' ? undefined : value as DiagramType);
                    }}
                  >
                    <SelectTrigger className="w-full h-12 text-sm border-gray-300/50 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200">
                      <SelectValue placeholder="🤖 Auto-detect from description" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60 rounded-xl border-gray-200/50 shadow-xl z-[99999]">
                      <SelectItem value="auto" className="rounded-lg">
                        <div className="flex items-center gap-3 py-1">
                          <span className="text-lg">🤖</span>
                          <div>
                            <span className="font-medium">Auto-detect</span>
                            <p className="text-xs text-gray-500">Let AI choose the best type</p>
                          </div>
                        </div>
                      </SelectItem>
                      {diagramTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value} className="rounded-lg">
                          <div className="flex items-center gap-3 py-1">
                            <span className="text-lg">{option.icon}</span>
                            <div className="flex flex-col">
                              <span className="font-medium">{option.label}</span>
                              <span className="text-xs text-gray-500">{option.description}</span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedType && selectedType !== 'auto' && (
                    <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 rounded-xl">
                      <span className="text-2xl">{DIAGRAM_TYPE_CONFIG[selectedType]?.icon}</span>
                      <div className="flex-1">
                        <p className="text-sm font-semibold text-blue-900">{DIAGRAM_TYPE_CONFIG[selectedType]?.name}</p>
                        <p className="text-xs text-blue-700">{DIAGRAM_TYPE_CONFIG[selectedType]?.description}</p>
                      </div>
                    </div>
                  )}
                </div>

                <Button
                  onClick={handleSubmit}
                  disabled={!isValid || isGenerating}
                  className="w-full bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 text-white rounded-xl py-4 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transition-all duration-300 font-semibold text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Creating your diagram...
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      Generate Diagram
                    </>
                  )}
                </Button>

                {/* Enhanced Status Messages */}
                {description.length > 0 && description.length < VALIDATION_RULES.MIN_DESCRIPTION_LENGTH && (
                  <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200/50 rounded-xl">
                    <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse"></div>
                    <p className="text-sm text-amber-700 font-medium">
                      Need {VALIDATION_RULES.MIN_DESCRIPTION_LENGTH - description.length} more characters to generate
                    </p>
                  </div>
                )}

                {description.length >= VALIDATION_RULES.MIN_DESCRIPTION_LENGTH && (
                  <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <p className="text-sm text-green-700 font-medium">Ready to generate your diagram!</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* History Section */}
          {showHistory && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <History className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">Diagram History</h4>
                  <p className="text-sm text-gray-600">Your previously created diagrams</p>
                </div>
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search diagrams..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 text-sm border border-gray-300/50 rounded-xl focus:border-blue-500 focus:ring-blue-500/20 bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200 hover:shadow-md"
                />
              </div>

              {/* History List */}
              <div className="space-y-3">
                {historyDiagrams.length === 0 && !historyLoading && (
                  <div className="text-center py-8 text-gray-500">
                    <History className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm font-medium">No diagrams yet</p>
                    <p className="text-xs">Create your first diagram to see it here</p>
                  </div>
                )}

                {historyDiagrams.map((diagram) => (
                  <div
                    key={diagram.id}
                    className="p-4 bg-gradient-to-r from-white via-gray-50/30 to-blue-50/30 rounded-xl hover:from-gray-50 hover:via-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 border border-gray-200/50 hover:border-blue-300/50 group cursor-pointer hover:shadow-lg"
                    onClick={() => handleHistorySelect(diagram)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-semibold text-gray-900 text-sm truncate">{diagram.title}</h5>
                          {diagram.is_favorite && <Heart className="h-4 w-4 text-red-500 fill-current" />}
                        </div>
                        <p className="text-xs text-gray-600 line-clamp-2 mb-3">{diagram.description}</p>
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="text-xs">
                            {diagram.diagram_type}
                          </Badge>
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {new Date(diagram.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFavorite(diagram.id, diagram.is_favorite);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity p-2 hover:bg-white/80 rounded-lg"
                      >
                        <Star className={`h-4 w-4 ${diagram.is_favorite ? 'text-yellow-500 fill-current' : 'text-gray-400'}`} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Edit Section */}
          {showEdit && currentDiagram && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Edit3 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">Edit Diagram</h4>
                  <p className="text-sm text-gray-600">Modify your current diagram with AI</p>
                </div>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-blue-600 font-medium text-sm">Current:</span>
                  <span className="text-blue-800 text-sm font-semibold">{currentDiagram.metadata.title}</span>
                </div>
                <p className="text-blue-700 text-xs">{currentDiagram.metadata.description}</p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 block">
                    ✏️ Edit Instructions
                  </label>
                  <Textarea
                    value={editPrompt}
                    onChange={(e) => setEditPrompt(e.target.value)}
                    placeholder="Describe what you want to change or add to the current diagram:

• 'Add error handling steps after data validation'
• 'Change the flow to include peer review process'
• 'Add decision points for quality control'
• 'Modify the timeline to include buffer time'
• 'Update labels to be more descriptive'"
                    className="min-h-[120px] resize-none border-gray-300/50 focus:border-orange-500 focus:ring-orange-500/20 text-sm rounded-xl bg-white/80 backdrop-blur-sm shadow-sm transition-all duration-200 hover:shadow-md"
                  />
                </div>

                <Button
                  onClick={handleEditSubmit}
                  disabled={!editPrompt.trim() || isGenerating}
                  className="w-full bg-gradient-to-r from-orange-500 via-orange-600 to-red-600 hover:from-orange-600 hover:via-orange-700 hover:to-red-700 text-white rounded-xl py-4 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transition-all duration-300 font-semibold text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Applying changes...
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-5 w-5" />
                      Apply Changes
                    </>
                  )}
                </Button>

                {editPrompt.trim() && (
                  <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200/50 rounded-xl">
                    <div className="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                    <p className="text-sm text-orange-700 font-medium">Ready to apply changes!</p>
                  </div>
                )}
              </div>
            </div>
          )}

        </ScrollArea>
      </div>
    </div>
  );
}
