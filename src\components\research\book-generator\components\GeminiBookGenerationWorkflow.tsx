import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  CheckCircle,
  Clock,
  Loader2,
  ArrowLeft,
  FileText,
  Target,
  Zap,
  AlertCircle,
  Download
} from "lucide-react";
import {
  BookMetadata,
  UserChapter,
  GeminiGeneratedChapter,
  GeneratedChapter,
  Citation
} from '../types';
import { useGeminiBookStore } from '../stores/gemini-book.store';
import { GeminiOutlineApproval } from './GeminiOutlineApproval';
import { GeminiChapterReader } from './GeminiChapterReader';
import { GeminiBookExportDialog } from './GeminiBookExportDialog';
import geminiBookService from '../services/gemini-book.service';
import { extractCitationsFromText } from '../../paper-generator/citation-extraction.enhanced';
import { toast } from 'sonner';

interface GeminiBookGenerationWorkflowProps {
  bookMetadata: BookMetadata;
  userChapters: UserChapter[];
  onComplete: (chapters: GeneratedChapter[], citations: Citation[]) => void;
  onBack: () => void;
}

export const GeminiBookGenerationWorkflow: React.FC<GeminiBookGenerationWorkflowProps> = ({
  bookMetadata,
  userChapters,
  onComplete,
  onBack
}) => {
  const {
    phase,
    outline,
    outlineApproved,
    currentChapterIndex,
    generatedChapters,
    isGenerating,
    totalProgress,
    error,
    estimatedTimeRemaining,
    initializeGeneration,
    setOutline,
    approveOutline,
    rejectOutline,
    startChapterGeneration,
    addGeneratedChapter,
    updateProgress,
    completeGeneration,
    setError,
    getBookContext,
    getEstimatedTimeRemaining
  } = useGeminiBookStore();

  const [currentChapterContent, setCurrentChapterContent] = useState('');

  // Initialize generation on mount
  useEffect(() => {
    if (!geminiBookService.isConfigured()) {
      setError('Google Gemini API key not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
      return;
    }

    initializeGeneration(bookMetadata, userChapters);
    generateOutline();
  }, []);

  // Generate book outline
  const generateOutline = async () => {
    try {
      toast.info('Generating comprehensive book outline...', { duration: 3000 });

      console.log('Generating outline with metadata:', bookMetadata);
      console.log('User chapters:', userChapters);

      const generatedOutline = await geminiBookService.generateBookOutline(bookMetadata, userChapters);
      console.log('=== WORKFLOW: Generated outline ===');
      console.log('Outline chapters:', generatedOutline.chapters.map(ch => ({
        id: ch.id,
        title: ch.title,
        order: ch.order
      })));
      setOutline(generatedOutline);
      
      toast.success('Book outline generated successfully! Please review and approve.');
    } catch (error) {
      console.error('Error generating outline:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate outline';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  // Handle outline approval
  const handleOutlineApproval = () => {
    approveOutline();
    startChapterGeneration();
    generateNextChapter();
    toast.success('Outline approved! Starting chapter generation...', { duration: 3000 });
  };

  // Handle outline rejection
  const handleOutlineRejection = (feedback?: string) => {
    rejectOutline(feedback);
    toast.info('Regenerating outline with your feedback...', { duration: 3000 });
    generateOutline();
  };

  // Generate next chapter
  const generateNextChapter = async () => {
    console.log(`=== generateNextChapter called ===`);

    // Get fresh state to avoid stale closure issues
    const currentState = useGeminiBookStore.getState();
    const freshCurrentIndex = currentState.currentChapterIndex;
    const freshGeneratedChapters = currentState.generatedChapters;

    console.log(`Fresh current index: ${freshCurrentIndex}`);
    console.log(`Stale current index: ${currentChapterIndex}`);
    console.log(`Total chapters: ${outline?.chapters.length}`);
    console.log(`Generated chapters so far: ${freshGeneratedChapters.length}`);
    console.log(`Generated chapter IDs:`, freshGeneratedChapters.map(ch => `${ch.id} (order: ${ch.order})`));

    if (!outline || freshCurrentIndex >= outline.chapters.length) {
      console.log('All chapters completed, finishing generation...');
      completeGeneration();
      handleGenerationComplete();
      return;
    }

    const chapterOutline = outline.chapters[freshCurrentIndex];
    console.log(`Target chapter at index ${freshCurrentIndex}:`, {
      id: chapterOutline.id,
      title: chapterOutline.title,
      order: chapterOutline.order
    });

    // Check if this chapter was already generated
    const alreadyGenerated = freshGeneratedChapters.find(gc => gc.id === chapterOutline.id);
    if (alreadyGenerated) {
      console.warn(`Chapter ${chapterOutline.id} already generated! Skipping to next...`);
      // Move to next chapter
      const nextIndex = freshCurrentIndex + 1;
      if (nextIndex < outline.chapters.length) {
        updateProgress(nextIndex);
        setTimeout(() => generateNextChapter(), 1000);
      } else {
        completeGeneration();
        handleGenerationComplete();
      }
      return;
    }

    const context = getBookContext();
    console.log(`Book context:`, {
      currentChapterIndex: context?.currentChapterIndex,
      totalChapters: context?.totalChapters,
      previousSummariesCount: context?.previousChapterSummaries.length
    });

    if (!context) {
      setError('Failed to get book context for chapter generation');
      return;
    }

    try {
      // Use fresh index instead of stale closure
      const freshIndex = useGeminiBookStore.getState().currentChapterIndex;

      setCurrentChapterContent('');

      toast.info(`Generating Chapter ${chapterOutline.order}: ${chapterOutline.title}...`, {
        duration: 5000
      });

      console.log(`Starting generation for chapter ${chapterOutline.order}/${outline.chapters.length}: ${chapterOutline.title}`);

      // Find user content for this chapter if available
      const userChapter = userChapters.find(uc => 
        uc.outline.title.toLowerCase().includes(chapterOutline.title.toLowerCase()) ||
        chapterOutline.title.toLowerCase().includes(uc.outline.title.toLowerCase())
      );
      const userContent = userChapter?.items.map(item => item.content).join('\n\n');

      const generatedChapter = await geminiBookService.generateChapter(
        chapterOutline,
        context,
        userContent
      );

      console.log(`=== Chapter Generation Completed ===`);
      console.log(`Generated chapter:`, {
        id: generatedChapter.id,
        title: generatedChapter.title,
        order: generatedChapter.order,
        wordCount: generatedChapter.wordCount
      });

      addGeneratedChapter(generatedChapter);
      setCurrentChapterContent(generatedChapter.content);

      const completedCount = generatedChapter.order;
      const totalChapters = outline?.chapters.length || 0;

      console.log(`Chapter ${completedCount} completed. Total generated: ${generatedChapters.length + 1}/${totalChapters}`);
      console.log(`Next chapter index should be: ${currentChapterIndex + 1}`);

      toast.success(`Chapter ${completedCount} of ${totalChapters} completed! (${generatedChapter.wordCount.toLocaleString()} words)`);

      // Continue to next chapter after a brief delay
      setTimeout(() => {
        console.log('=== Continuing to next chapter ===');
        generateNextChapter();
      }, 2000); // Slightly longer delay for rate limiting

    } catch (error) {
      console.error('Error generating chapter:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate chapter';

      // For citation errors, create a chapter without citations and continue
      if (errorMessage.includes('citations') || errorMessage.includes('map')) {
        console.log('Citation error detected, creating chapter without citations...');

        try {
          // Create a basic chapter structure without citations
          const basicChapter: GeminiGeneratedChapter = {
            id: chapterOutline.id,
            title: chapterOutline.title,
            content: currentChapterContent || `# ${chapterOutline.title}\n\n[Chapter content generation failed due to citation processing error. Please regenerate this chapter.]`,
            summary: `Summary of ${chapterOutline.title}`,
            wordCount: currentChapterContent ? currentChapterContent.split(' ').length : 100,
            order: chapterOutline.order,
            generatedAt: new Date(),
            citations: [] // Empty citations array
          };

          addGeneratedChapter(basicChapter);
          toast.warning(`Chapter ${currentChapterIndex + 1} generated with citation processing issue. Continuing...`);

          // Continue to next chapter after a brief delay
          setTimeout(() => {
            generateNextChapter();
          }, 2000);

          return; // Don't set error, continue generation
        } catch (fallbackError) {
          console.error('Fallback chapter creation failed:', fallbackError);
        }
      }

      // For other errors, stop generation
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  // Handle generation completion
  const handleGenerationComplete = () => {
    const allCitations: Citation[] = [];

    // Extract citations from all chapters
    generatedChapters.forEach((chapter, index) => {
      const chapterCitations = extractCitationsFromText(chapter.content);
      chapterCitations.forEach(citation => {
        citation.chapterIds = [chapter.id];
        allCitations.push(citation);
      });
    });

    // Convert to the expected GeneratedChapter format
    const convertedChapters: GeneratedChapter[] = generatedChapters.map(chapter => ({
      id: chapter.id,
      title: chapter.title,
      description: outline?.chapters.find(c => c.id === chapter.id)?.description || '',
      status: 'completed' as const,
      icon: BookOpen,
      content: chapter.content,
      summary: chapter.summary,
      wordCount: chapter.wordCount,
      citations: chapter.citations,
      order: chapter.order,
      userApproved: true
    }));

    onComplete(convertedChapters, allCitations);
    toast.success('Book generation completed successfully!', { duration: 5000 });
  };

  // Render based on current phase
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 py-12">
        <div className="max-w-4xl mx-auto px-6">
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-5 w-5" />
                Generation Error
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-700 mb-4">{error}</p>
              <div className="flex gap-3">
                <Button onClick={onBack} variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Setup
                </Button>
                <Button onClick={() => window.location.reload()}>
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (phase === 'outline-generation' && !outline) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12">
        <div className="max-w-4xl mx-auto px-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                Generating Book Outline
              </CardTitle>
              <p className="text-gray-600">
                Creating a comprehensive outline for "{bookMetadata.title}" using Google Gemini...
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  Analyzing book metadata and user content
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Target className="h-4 w-4 text-green-500" />
                  Creating detailed chapter structures
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <FileText className="h-4 w-4 text-blue-500" />
                  Estimating word counts and reading time
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (phase === 'outline-approval' && outline) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="mb-6">
            <Button onClick={onBack} variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Setup
            </Button>
          </div>
          
          <GeminiOutlineApproval
            outline={outline}
            onApprove={handleOutlineApproval}
            onReject={handleOutlineRejection}
            isLoading={isGenerating}
          />
        </div>
      </div>
    );
  }

  if (phase === 'chapter-generation' || phase === 'completed') {
    const estimatedTime = getEstimatedTimeRemaining();

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 py-12">
        <div className="max-w-7xl mx-auto px-6">
          {/* Enhanced Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full">
                <BookOpen className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  {phase === 'completed' ? '🎉 Book Generation Complete!' : '✨ Generating Your Book'}
                </h1>
                <p className="text-gray-600 mt-1">
                  {phase === 'completed'
                    ? `Successfully created "${bookMetadata.title}" with ${generatedChapters.length} chapters`
                    : `Creating "${bookMetadata.title}" chapter by chapter using Google Gemini AI`
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Enhanced Progress Overview */}
          <Card className="mb-8 bg-gradient-to-r from-white to-blue-50 border-blue-200 shadow-lg">
            <CardContent className="pt-6">
              <div className="space-y-6">
                {/* Progress Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Target className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">Generation Progress</h3>
                      <p className="text-sm text-gray-600">Real-time book creation status</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{totalProgress}%</div>
                    <div className="text-xs text-gray-500">Complete</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <Progress value={totalProgress} className="w-full h-3 bg-gray-200" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Started</span>
                    <span>{generatedChapters.length} / {outline?.chapters.length || 0} chapters</span>
                    <span>{phase === 'completed' ? 'Completed' : 'In Progress'}</span>
                  </div>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white p-4 rounded-lg border border-green-200 shadow-sm">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-6 w-6 text-green-500" />
                      <div>
                        <div className="text-lg font-bold text-green-700">{generatedChapters.length}</div>
                        <div className="text-sm text-gray-600">Chapters Complete</div>
                      </div>
                    </div>
                  </div>

                  {estimatedTime && phase !== 'completed' && (
                    <div className="bg-white p-4 rounded-lg border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-3">
                        <Clock className="h-6 w-6 text-blue-500" />
                        <div>
                          <div className="text-lg font-bold text-blue-700">{estimatedTime}</div>
                          <div className="text-sm text-gray-600">Time Remaining</div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-white p-4 rounded-lg border border-purple-200 shadow-sm">
                    <div className="flex items-center gap-3">
                      <FileText className="h-6 w-6 text-purple-500" />
                      <div>
                        <div className="text-lg font-bold text-purple-700">
                          {generatedChapters.reduce((sum, ch) => sum + ch.wordCount, 0).toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Words Generated</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Current Generation Status */}
                {phase === 'chapter-generation' && isGenerating && (
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
                    <div className="flex items-center gap-3">
                      <Loader2 className="h-5 w-5 animate-spin text-yellow-600" />
                      <div>
                        <div className="font-medium text-yellow-800">
                          Currently generating Chapter {currentChapterIndex + 1}
                        </div>
                        <div className="text-sm text-yellow-700">
                          {outline?.chapters[currentChapterIndex]?.title}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Chapter Reader and Progress */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Chapter Reader - Takes up more space */}
            <div className="lg:col-span-2">
              <GeminiChapterReader
                chapters={generatedChapters}
                currentGeneratingIndex={currentChapterIndex}
                totalChapters={outline?.chapters.length || 0}
                isGenerating={phase === 'chapter-generation' && isGenerating}
              />
            </div>

            {/* Enhanced Chapter Progress Summary */}
            <div className="space-y-4">
              <Card className="bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                      <BookOpen className="h-4 w-4 text-white" />
                    </div>
                    Chapter Timeline
                  </CardTitle>
                  <p className="text-sm text-gray-600">Track the progress of each chapter</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {outline?.chapters.slice(0, 10).map((chapter, index) => {
                      const generatedChapter = generatedChapters.find(gc => gc.id === chapter.id);
                      const isCurrentChapter = index === currentChapterIndex && phase === 'chapter-generation';
                      const isCompleted = !!generatedChapter;

                      return (
                        <div
                          key={chapter.id}
                          className={`relative flex items-center gap-4 p-3 rounded-lg transition-all duration-300 ${
                            isCurrentChapter
                              ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 shadow-md transform scale-105'
                              : isCompleted
                                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200'
                                : 'bg-gray-50 border border-gray-200'
                          }`}
                        >
                          {/* Status Icon */}
                          <div className={`flex-shrink-0 p-2 rounded-full ${
                            isCompleted
                              ? 'bg-green-100'
                              : isCurrentChapter
                                ? 'bg-blue-100'
                                : 'bg-gray-100'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : isCurrentChapter ? (
                              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                            ) : (
                              <Clock className="h-5 w-5 text-gray-400" />
                            )}
                          </div>

                          {/* Chapter Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className={`text-xs font-bold px-2 py-1 rounded-full ${
                                isCompleted
                                  ? 'bg-green-200 text-green-800'
                                  : isCurrentChapter
                                    ? 'bg-blue-200 text-blue-800'
                                    : 'bg-gray-200 text-gray-600'
                              }`}>
                                Ch {chapter.order}
                              </span>
                              <h4 className="font-medium text-sm truncate">
                                {chapter.title}
                              </h4>
                            </div>

                            {generatedChapter && (
                              <div className="flex items-center gap-3 mt-1 text-xs text-gray-600">
                                <span className="flex items-center gap-1">
                                  <FileText className="h-3 w-3" />
                                  {generatedChapter.wordCount.toLocaleString()} words
                                </span>
                                <span className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {Math.ceil(generatedChapter.wordCount / 200)} min read
                                </span>
                              </div>
                            )}

                            {isCurrentChapter && (
                              <div className="mt-1">
                                <div className="text-xs text-blue-600 font-medium">Generating...</div>
                                <div className="w-full bg-blue-200 rounded-full h-1 mt-1">
                                  <div className="bg-blue-600 h-1 rounded-full animate-pulse" style={{width: '60%'}}></div>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Status Badge */}
                          <Badge
                            variant={isCompleted ? 'default' : isCurrentChapter ? 'secondary' : 'outline'}
                            className={`text-xs font-medium ${
                              isCompleted
                                ? 'bg-green-100 text-green-800 border-green-300'
                                : isCurrentChapter
                                  ? 'bg-blue-100 text-blue-800 border-blue-300'
                                  : 'bg-gray-100 text-gray-600 border-gray-300'
                            }`}
                          >
                            {isCompleted ? 'Complete' : isCurrentChapter ? 'Generating' : 'Pending'}
                          </Badge>

                          {/* Connecting Line */}
                          {index < (outline?.chapters.length || 0) - 1 && (
                            <div className={`absolute left-6 top-full w-0.5 h-3 ${
                              isCompleted ? 'bg-green-300' : 'bg-gray-300'
                            }`}></div>
                          )}
                        </div>
                      );
                    })}

                    {outline && outline.chapters.length > 10 && (
                      <div className="text-center py-3">
                        <Badge variant="outline" className="text-xs">
                          ... and {outline.chapters.length - 10} more chapters
                        </Badge>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          {phase === 'completed' && (
            <div className="mt-8 space-y-4">
              {/* Success Message */}
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                    <h3 className="text-2xl font-bold text-green-800">Book Generation Complete!</h3>
                  </div>
                  <p className="text-gray-700 mb-4">
                    Your book "{bookMetadata.title}" has been successfully generated with {generatedChapters.length} chapters
                    and {generatedChapters.reduce((sum, ch) => sum + ch.wordCount, 0).toLocaleString()} words.
                  </p>
                  <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-4 w-4" />
                      {generatedChapters.length} chapters
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      {generatedChapters.reduce((sum, ch) => sum + ch.wordCount, 0).toLocaleString()} words
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      ~{Math.ceil(generatedChapters.reduce((sum, ch) => sum + ch.wordCount, 0) / 200)} min read
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Export Options */}
              <div className="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto">
                <GeminiBookExportDialog
                  bookMetadata={bookMetadata}
                  generatedChapters={generatedChapters}
                  allCitations={[]} // Will be populated from chapters
                  onEditInEditor={onComplete}
                />
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 border-2 hover:shadow-xl hover:scale-105 transition-all duration-300 bg-white/80 backdrop-blur-sm border-purple-200 hover:border-purple-300 hover:bg-purple-50 group"
                  onClick={() => {
                    // Extract all citations from chapters
                    const allCitations: Citation[] = [];
                    generatedChapters.forEach((chapter) => {
                      chapter.citations.forEach((citation) => {
                        allCitations.push({
                          id: `${chapter.id}-${citation}`,
                          text: citation,
                          inTextFormat: citation,
                          chapterIds: [chapter.id]
                        });
                      });
                    });
                    handleGenerationComplete();
                  }}
                >
                  <ArrowLeft className="h-5 w-5 mr-2 group-hover:text-purple-600 transition-colors" />
                  Continue to Main Flow
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return null;
};
