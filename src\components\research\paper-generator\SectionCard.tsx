import React from 'react';
import { X, FileText, ImageIcon, GripVertical } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { UserSection, ContentItem } from "./types";
import { ContentItemRenderer } from "./ContentItemRenderer";
import { EMPTY_SECTION_PROMPTS, TEXT_PLACEHOLDERS, FIGURE_PLACEHOLDERS, DEFAULT_TEXT_PROMPT, DEFAULT_FIGURE_PROMPT, SECTION_TYPES, SECTION_GUIDANCE } from "./constants";

interface SectionCardProps {
  section: UserSection;
  sectionColor: string;
  sectionIcon: React.ElementType;
  sectionDescription: string;
  removeUserSection: (sectionId: string) => void;
  addContentItem: (sectionId: string, type: 'text' | 'figure') => void;
  updateContentItem: (sectionId: string, itemId: string, updates: Partial<ContentItem>) => void;
  removeContentItem: (sectionId: string, itemId: string) => void;
  moveContentItem: (sectionId: string, itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedModel: string;
  allSections: UserSection[]; // Add all sections to calculate figure numbers
}

export const SectionCard: React.FC<SectionCardProps> = ({
  section,
  sectionColor,
  sectionIcon: Icon,
  sectionDescription,
  removeUserSection,
  addContentItem,
  updateContentItem,
  removeContentItem,
  moveContentItem,
  analyzingItems,
  setAnalyzingItems,
  selectedModel,
  allSections
}) => {
  // Calculate figure numbers across all sections
  const calculateFigureNumber = (currentSectionId: string, currentItemIndex: number): number | undefined => {
    let figureCount = 0;

    // Sort sections by their order in SECTION_TYPES
    const sortedSections = [...allSections].sort((a, b) => {
      const aType = SECTION_TYPES.find(t => t.name === a.name);
      const bType = SECTION_TYPES.find(t => t.name === b.name);
      return (aType?.order || 0) - (bType?.order || 0);
    });

    for (const sec of sortedSections) {
      for (let i = 0; i < sec.items.length; i++) {
        const item = sec.items[i];
        if (item.type === 'figure') {
          figureCount++;
          if (sec.id === currentSectionId && i === currentItemIndex) {
            return figureCount;
          }
        }
      }
    }
    return undefined;
  };
  return (
    <div className="bg-gradient-to-br from-white to-gray-50/50 border-2 border-gray-200 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
      {/* Enhanced Section Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className={`w-4 h-4 rounded-full ${sectionColor} shadow-sm`}></div>
          <div>
            <h3 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <Icon className="h-6 w-6" />
              {section.name}
            </h3>
            <p className="text-gray-600 mt-1">{sectionDescription}</p>
          </div>
          <Badge variant="outline" className="text-sm px-3 py-1 bg-white/70 backdrop-blur-sm">
            {section.items.length} item{section.items.length !== 1 ? 's' : ''}
          </Badge>
        </div>
        
        {/* Enhanced Section Actions */}
        <div className="flex items-center gap-3">
          <Button 
            onClick={() => addContentItem(section.id, 'text')} 
            variant="outline" 
            className="flex items-center gap-2 hover:bg-blue-50 border-2 hover:border-blue-300 px-4 py-2"
          >
            <FileText className="h-4 w-4" />
            Add Analysis
          </Button>
          <Button 
            onClick={() => addContentItem(section.id, 'figure')} 
            variant="outline" 
            className="flex items-center gap-2 hover:bg-green-50 border-2 hover:border-green-300 px-4 py-2"
          >
            <ImageIcon className="h-4 w-4" />
            Add Figure
          </Button>
          <Button 
            onClick={() => removeUserSection(section.id)} 
            variant="ghost" 
            className="text-red-500 hover:bg-red-50 hover:text-red-600 p-2"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {section.items.length === 0 ? (
        <div className="border-2 border-dashed border-gray-300 rounded-2xl bg-gradient-to-br from-gray-50/50 to-white overflow-hidden">
          {/* Enhanced Header with Section-Specific Guidance */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 border-b border-gray-200">
            <div className="flex items-center gap-4 mb-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${sectionColor}`}>
                <Icon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold text-gray-800">
                  {SECTION_GUIDANCE[section.name]?.title || `Add ${section.name} Content`}
                </h4>
                <p className="text-gray-600">
                  {SECTION_GUIDANCE[section.name]?.description || 'Build comprehensive research content'}
                </p>
              </div>
            </div>
          </div>

          {/* Content Area with Tips */}
          <div className="p-6">
            <div className="mb-6">
              <p className="text-gray-700 mb-4 leading-relaxed">
                {EMPTY_SECTION_PROMPTS[section.name] || 'Add text analysis or figures with detailed interpretations to build comprehensive research content.'}
              </p>

              {SECTION_GUIDANCE[section.name] && (
                <div className="bg-blue-50 rounded-lg p-4 mb-6">
                  <h5 className="font-semibold text-blue-800 mb-2">💡 What to include:</h5>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {SECTION_GUIDANCE[section.name].tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="flex justify-center gap-4">
              <Button
                onClick={() => addContentItem(section.id, 'text')}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 px-6 py-3"
              >
                <FileText className="h-4 w-4" />
                Add Text Analysis
              </Button>
              <Button
                onClick={() => addContentItem(section.id, 'figure')}
                variant="outline"
                className="flex items-center gap-2 border-2 hover:border-green-400 px-6 py-3"
              >
                <ImageIcon className="h-4 w-4" />
                Add Figure & Analysis
              </Button>
            </div>

            {SECTION_GUIDANCE[section.name]?.examples && (
              <div className="mt-6 text-center">
                <p className="text-xs text-gray-500 mb-2">Examples for this section:</p>
                <div className="flex flex-wrap justify-center gap-2">
                  {SECTION_GUIDANCE[section.name].examples.map((example, index) => (
                    <Badge key={index} variant="outline" className="text-xs bg-gray-50">
                      {example}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-8">
          {section.items.map((item, index) => (
            <ContentItemRenderer
              key={item.id}
              sectionId={section.id}
              sectionName={section.name}
              item={item}
              index={index}
              totalItems={section.items.length}
              updateContentItem={updateContentItem}
              removeContentItem={removeContentItem}
              moveContentItem={moveContentItem}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
              textPlaceholders={TEXT_PLACEHOLDERS}
              figurePlaceholders={FIGURE_PLACEHOLDERS}
              defaultTextPrompt={DEFAULT_TEXT_PROMPT}
              defaultFigurePrompt={DEFAULT_FIGURE_PROMPT}
              figureNumber={item.type === 'figure' ? calculateFigureNumber(section.id, index) : undefined}
            />
          ))}
        </div>
      )}
    </div>
  );
};
