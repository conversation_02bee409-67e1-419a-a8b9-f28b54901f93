// Quick test script for Gemini Agent Service
// Run this in browser console to test the service

import { geminiAgentService } from './GeminiAgentService.ts';

// Test document with references
const testDocument = `
<h1>Research Paper Title</h1>
<p>This is the introduction section with some citations (<PERSON>, 2020) and references to other work.</p>

<h2>Literature Review</h2>
<p>Previous studies have shown various results (<PERSON> et al., 2019; Brown, 2021). The methodology used by these researchers provides a foundation for our work.</p>

<h2>Methodology</h2>
<p>Our approach builds on established methods from the literature. We used a quantitative approach similar to <PERSON> (2018).</p>

<h2>Results</h2>
<p>The findings indicate significant improvements over baseline methods. Statistical analysis was performed using standard techniques.</p>

<h2>References</h2>
<p><PERSON>, <PERSON>. (2021). Advanced Research Methods. Journal of Science, 15(3), 45-67.</p>
<p><PERSON>, M. (2018). Quantitative Analysis Techniques. Research Quarterly, 22(1), 12-28.</p>
<p><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2019). Comprehensive Study Results. Academic Review, 8(2), 89-102.</p>
<p><PERSON>, <PERSON> (2020). Introduction to Modern Research. Science Today, 12(4), 234-245.</p>
`;

// Test the service
async function testGeminiService() {
  console.log('🧪 Testing Gemini Agent Service...');
  
  try {
    const result = await geminiAgentService.executeTargetedEdit(
      'please remove references from the file',
      testDocument,
      {
        editMode: 'moderate',
        maxSections: 8,
        preserveFormatting: true
      }
    );
    
    console.log('✅ Test completed!');
    console.log('Result:', result);
    
    if (result.success && result.edits.length > 0) {
      console.log('🎉 Success! Generated edits:');
      result.edits.forEach((edit, index) => {
        console.log(`Edit ${index + 1}:`);
        console.log('Original:', edit.originalContent.substring(0, 100) + '...');
        console.log('Edited:', edit.editedContent.substring(0, 100) + '...');
        console.log('---');
      });
    } else {
      console.log('❌ No edits generated');
      console.log('Reason:', result.reasoning);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Export for manual testing
window.testGeminiService = testGeminiService;
console.log('🔧 Test function available as window.testGeminiService()');
