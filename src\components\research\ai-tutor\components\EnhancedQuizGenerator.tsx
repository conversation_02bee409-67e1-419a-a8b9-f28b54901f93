/**
 * Enhanced Quiz Generator Component
 * Modern interface for generating quizzes with source selection and topic prompts
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Wand2,
  BookOpen,
  Search,
  Brain,
  FileText,
  Settings,
  Loader2,
  Sparkles,
  Target,
  Clock,
  HelpCircle,
  Lightbulb,
  Globe,
  Database,
  CheckCircle,
  AlertCircle,
  Play
} from "lucide-react";
import { Quiz, EducationLevel, DifficultyLevel, ResearchDocument } from '../types';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';
import { SourceSelector, SourceType } from './SourceSelector';

interface EnhancedQuizGeneratorProps {
  onQuizGenerated: (quiz: Quiz) => void;
  uploadedDocuments?: ResearchDocument[];
}

interface QuizOptions {
  source: SourceType;
  topic: string;
  educationLevel: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionCount: number;
  questionTypes: string[];
  includeExplanations: boolean;
  includeHints: boolean;
  timeLimit: number;
}

const educationLevels = [
  { value: 'elementary', label: 'Elementary School' },
  { value: 'middle-school', label: 'Middle School' },
  { value: 'high-school', label: 'High School' },
  { value: 'undergraduate', label: 'Undergraduate' },
  { value: 'graduate', label: 'Graduate' },
  { value: 'professional', label: 'Professional' }
];

const difficultyLevels = [
  { value: 'easy', label: 'Easy', color: 'bg-green-100 text-green-800', description: 'Basic concepts and recall' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800', description: 'Application and analysis' },
  { value: 'hard', label: 'Hard', color: 'bg-red-100 text-red-800', description: 'Synthesis and evaluation' }
];

const questionTypes = [
  { value: 'multiple-choice', label: 'Multiple Choice', icon: Target, description: 'Choose from 4 options' },
  { value: 'true-false', label: 'True/False', icon: HelpCircle, description: 'Binary choice questions' },
  { value: 'short-answer', label: 'Short Answer', icon: FileText, description: 'Brief written responses' },
  { value: 'essay', label: 'Essay', icon: BookOpen, description: 'Extended written responses' }
];



const topicSuggestions = [
  'Photosynthesis and Plant Biology',
  'World War II History',
  'Calculus and Derivatives',
  'Shakespeare\'s Hamlet',
  'Chemical Bonding',
  'Machine Learning Basics',
  'Human Anatomy',
  'Climate Change',
  'Economic Principles',
  'Programming Fundamentals'
];

export function EnhancedQuizGenerator({ onQuizGenerated, uploadedDocuments = [] }: EnhancedQuizGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  
  const [quizOptions, setQuizOptions] = useState<QuizOptions>({
    source: 'ai',
    topic: '',
    educationLevel: 'high-school',
    difficulty: 'medium',
    questionCount: 10,
    questionTypes: ['multiple-choice'],
    includeExplanations: true,
    includeHints: false,
    timeLimit: 15
  });

  const handleSourceChange = (source: string) => {
    setQuizOptions(prev => ({ ...prev, source: source as any }));
  };

  const handleQuestionTypeToggle = (type: string, checked: boolean) => {
    setQuizOptions(prev => ({
      ...prev,
      questionTypes: checked 
        ? [...prev.questionTypes, type]
        : prev.questionTypes.filter(t => t !== type)
    }));
  };

  const handleTopicSuggestionClick = (topic: string) => {
    setQuizOptions(prev => ({ ...prev, topic }));
  };

  const generateQuiz = async () => {
    if (!quizOptions.topic.trim()) {
      toast.error('Please enter a topic for the quiz');
      return;
    }

    if (quizOptions.questionTypes.length === 0) {
      toast.error('Please select at least one question type');
      return;
    }

    if (quizOptions.source === 'documents' && uploadedDocuments.length === 0) {
      toast.error('Please upload documents to use them as a source');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setCurrentStep('Preparing quiz generation...');

    try {
      // Simulate progress steps
      const steps = [
        'Analyzing topic and requirements...',
        'Gathering knowledge from selected sources...',
        'Generating questions...',
        'Creating answer options...',
        'Adding explanations and hints...',
        'Finalizing quiz structure...'
      ];

      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(steps[i]);
        setGenerationProgress((i + 1) * (100 / steps.length));
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Generate the actual quiz using the integrated tutor service
      const questions = await integratedTutorService.generatePracticeQuestions(
        quizOptions.topic,
        quizOptions.educationLevel,
        quizOptions.questionCount,
        quizOptions.difficulty
      );

      if (questions.length === 0) {
        throw new Error('No questions were generated. Please try again.');
      }

      const quiz: Quiz = {
        id: crypto.randomUUID(),
        title: `${quizOptions.topic} Quiz`,
        description: `A ${quizOptions.difficulty} level quiz on ${quizOptions.topic} for ${quizOptions.educationLevel} students`,
        questions: questions.map((q, index) => ({
          id: `q${index + 1}`,
          type: quizOptions.questionTypes[index % quizOptions.questionTypes.length] as any,
          question: q.question,
          options: q.options || [],
          correctAnswer: q.correctAnswer || '',
          explanation: q.explanation,
          hint: quizOptions.includeHints ? `Think about the key concepts related to ${quizOptions.topic}` : undefined,
          points: 1,
          timeLimit: Math.floor(quizOptions.timeLimit / quizOptions.questionCount) * 60 // Convert to seconds per question
        })),
        settings: {
          timeLimit: quizOptions.timeLimit * 60, // Convert to seconds
          showHints: quizOptions.includeHints,
          showExplanations: quizOptions.includeExplanations,
          shuffleQuestions: true,
          shuffleOptions: true
        },
        metadata: {
          source: quizOptions.source,
          educationLevel: quizOptions.educationLevel as EducationLevel,
          difficulty: quizOptions.difficulty as DifficultyLevel,
          topic: quizOptions.topic,
          createdAt: new Date(),
          estimatedTime: quizOptions.timeLimit
        }
      };

      setCurrentStep('Quiz generated successfully!');
      setGenerationProgress(100);
      
      setTimeout(() => {
        onQuizGenerated(quiz);
        toast.success(`Quiz "${quiz.title}" generated successfully!`);
        setIsGenerating(false);
      }, 500);

    } catch (error) {
      console.error('Quiz generation failed:', error);
      toast.error(`Failed to generate quiz: ${error.message}`);
      setIsGenerating(false);
      setGenerationProgress(0);
      setCurrentStep('');
    }
  };

  if (isGenerating) {
    return (
      <div className="max-w-2xl mx-auto p-8">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
          <CardContent className="p-8 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mx-auto mb-6">
              <Wand2 className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Generating Your Quiz</h3>
            <p className="text-gray-600 mb-6">{currentStep}</p>
            <Progress value={generationProgress} className="w-full mb-4" />
            <p className="text-sm text-gray-500">{Math.round(generationProgress)}% complete</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Quiz Generator</h1>
        <p className="text-gray-600">Create personalized quizzes from various knowledge sources</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Topic Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                <span>Topic & Subject</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="topic">Quiz Topic</Label>
                <Textarea
                  id="topic"
                  placeholder="Enter the topic you want to create a quiz about..."
                  value={quizOptions.topic}
                  onChange={(e) => setQuizOptions(prev => ({ ...prev, topic: e.target.value }))}
                  className="mt-1"
                  rows={3}
                />
              </div>
              
              <div>
                <Label className="text-sm text-gray-600">Suggested Topics</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {topicSuggestions.map((topic) => (
                    <Badge
                      key={topic}
                      variant="outline"
                      className="cursor-pointer hover:bg-blue-50 hover:border-blue-300"
                      onClick={() => handleTopicSuggestionClick(topic)}
                    >
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Source Selection */}
          <SourceSelector
            selectedSource={quizOptions.source}
            onSourceChange={(source) => setQuizOptions(prev => ({ ...prev, source }))}
            uploadedDocuments={uploadedDocuments}
            showMixed={true}
            title="Knowledge Source"
            description="Choose where to generate quiz questions from"
          />
        </div>

        {/* Settings Panel */}
        <div className="space-y-6">
          {/* Basic Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5 text-gray-500" />
                <span>Quiz Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="educationLevel">Education Level</Label>
                <Select value={quizOptions.educationLevel} onValueChange={(value) => setQuizOptions(prev => ({ ...prev, educationLevel: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {educationLevels.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="difficulty">Difficulty Level</Label>
                <Select value={quizOptions.difficulty} onValueChange={(value) => setQuizOptions(prev => ({ ...prev, difficulty: value as any }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {difficultyLevels.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        <div className="flex items-center space-x-2">
                          <Badge className={level.color}>{level.label}</Badge>
                          <span className="text-sm text-gray-600">{level.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="questionCount">Number of Questions</Label>
                <Select value={quizOptions.questionCount.toString()} onValueChange={(value) => setQuizOptions(prev => ({ ...prev, questionCount: parseInt(value) }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[5, 10, 15, 20, 25].map(count => (
                      <SelectItem key={count} value={count.toString()}>
                        {count} questions
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                <Select value={quizOptions.timeLimit.toString()} onValueChange={(value) => setQuizOptions(prev => ({ ...prev, timeLimit: parseInt(value) }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[5, 10, 15, 20, 30, 45, 60].map(time => (
                      <SelectItem key={time} value={time.toString()}>
                        {time} minutes
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Question Types */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-500" />
                <span>Question Types</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {questionTypes.map((type) => (
                <div key={type.value} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    id={type.value}
                    checked={quizOptions.questionTypes.includes(type.value)}
                    onCheckedChange={(checked) => handleQuestionTypeToggle(type.value, checked as boolean)}
                  />
                  <type.icon className="w-5 h-5 text-gray-500" />
                  <div className="flex-1">
                    <Label htmlFor={type.value} className="font-medium cursor-pointer">
                      {type.label}
                    </Label>
                    <p className="text-sm text-gray-600">{type.description}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Additional Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-purple-500" />
                <span>Additional Options</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="includeExplanations"
                  checked={quizOptions.includeExplanations}
                  onCheckedChange={(checked) => setQuizOptions(prev => ({ ...prev, includeExplanations: checked as boolean }))}
                />
                <Label htmlFor="includeExplanations" className="cursor-pointer">
                  Include explanations for answers
                </Label>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="includeHints"
                  checked={quizOptions.includeHints}
                  onCheckedChange={(checked) => setQuizOptions(prev => ({ ...prev, includeHints: checked as boolean }))}
                />
                <Label htmlFor="includeHints" className="cursor-pointer">
                  Include hints for difficult questions
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <Button
            onClick={generateQuiz}
            disabled={!quizOptions.topic.trim() || quizOptions.questionTypes.length === 0}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white py-3 text-lg font-medium shadow-lg"
          >
            <Play className="w-5 h-5 mr-2" />
            Generate Quiz
          </Button>
        </div>
      </div>
    </div>
  );
}
