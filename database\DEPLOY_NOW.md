# 🚀 Deploy to verbira.com - Step by Step Guide

## ✅ Current Status
- [x] Build configuration optimized for production
- [x] API server configured for Vercel serverless functions  
- [x] Environment variables template ready
- [x] Production build tested successfully
- [x] Vercel CLI installed and ready

## 🎯 Next Steps (Do These Now)

### Step 1: Deploy to Vercel
Run this command in your terminal:
```bash
vercel
```

When prompted:
1. **Log in to Vercel**: Choose "Continue with GitHub" (recommended)
2. **Set up and deploy?**: Yes
3. **Which scope?**: Select your account
4. **Link to existing project?**: No (create new)
5. **Project name?**: `verbira-genius` or similar
6. **Directory?**: `./` (current directory)
7. **Override settings?**: No

### Step 2: Configure Environment Variables in Vercel
After deployment, go to Vercel Dashboard → Your Project → Settings → Environment Variables

Add these variables (copy-paste exactly):

```
VITE_OPENROUTER_API_KEY=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
OPENROUTER_API_KEY=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
VITE_SUPABASE_URL=https://swsnqpavwcnqiihsidss.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.cOSvLGmaJx1y9PutRST81mTiCNXnVTRO8kHWii9CFfg
VITE_TAVILY_API_KEY=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
NODE_ENV=production
VITE_NODE_ENV=production
```

**Important**: Set these for Production, Preview, AND Development environments.

### Step 3: Add Custom Domain
1. In Vercel Dashboard → Your Project → Settings → Domains
2. Click "Add Domain"
3. Enter: `verbira.com`
4. Follow DNS configuration instructions
5. Also add: `www.verbira.com` (optional)

### Step 4: Redeploy with Environment Variables
After adding environment variables:
```bash
vercel --prod
```

## 🔧 Post-Deployment Configuration

### Update Supabase Settings
I'll help you update these using Supabase tools once the deployment is complete.

**Site URL**: Change to `https://verbira.com`
**Redirect URLs**: Add `https://verbira.com/auth/callback`
**CORS**: Add `https://verbira.com`

### Update Google OAuth
Add to Google Cloud Console → OAuth redirect URIs:
- `https://verbira.com/auth/callback`

## 🧪 Testing After Deployment

Visit `https://verbira.com` and test:
- [ ] Homepage loads
- [ ] User registration works
- [ ] Google OAuth login works  
- [ ] AI paper generation works
- [ ] Research search works
- [ ] File uploads work

## 🚨 If Something Goes Wrong

1. Check Vercel deployment logs
2. Check browser console for errors
3. Verify all environment variables are set
4. Make sure DNS has propagated (can take up to 24 hours)

## 📞 Ready for Next Steps?

Once you complete the deployment:
1. Share the Vercel deployment URL with me
2. I'll help update the Supabase configuration
3. We'll test everything together

**Start with Step 1 now!** 🚀
