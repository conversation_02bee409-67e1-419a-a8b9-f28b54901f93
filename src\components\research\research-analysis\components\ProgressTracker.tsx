import React from 'react';
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Zap
} from "lucide-react";

import { GenerationProgress } from '../types';

interface ProgressTrackerProps {
  progress: GenerationProgress;
  showDetails?: boolean;
}

export function ProgressTracker({ progress, showDetails = true }: ProgressTrackerProps) {
  const getStageIcon = (stage: string) => {
    if (stage.toLowerCase().includes('complete')) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    if (stage.toLowerCase().includes('error')) {
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
    return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-orange-500';
  };

  const estimatedTimeText = progress.estimatedTimeRemaining 
    ? `~${Math.ceil(progress.estimatedTimeRemaining / 60)} min remaining`
    : '';

  return (
    <div className="space-y-4">
      {/* Main Progress */}
      <div className="flex items-center gap-4">
        {getStageIcon(progress.stage)}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <div>
              <p className="font-medium text-gray-900">{progress.stage}</p>
              <p className="text-sm text-gray-600">{progress.message}</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {Math.round(progress.progress)}%
              </p>
              {estimatedTimeText && (
                <p className="text-xs text-gray-500 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {estimatedTimeText}
                </p>
              )}
            </div>
          </div>
          
          <div className="relative">
            <Progress 
              value={progress.progress} 
              className="h-3"
            />
            <div 
              className={`absolute top-0 left-0 h-3 rounded-full transition-all duration-300 ${getProgressColor(progress.progress)}`}
              style={{ width: `${progress.progress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Additional Details */}
      {showDetails && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <Zap className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-700">Processing Details</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
              <span className="text-gray-600">Current Stage</span>
              <Badge variant="secondary" className="ml-auto">
                {progress.stage}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span className="text-gray-600">Progress</span>
              <Badge variant="secondary" className="ml-auto">
                {Math.round(progress.progress)}%
              </Badge>
            </div>
            
            {estimatedTimeText && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                <span className="text-gray-600">Time Remaining</span>
                <Badge variant="secondary" className="ml-auto">
                  {estimatedTimeText}
                </Badge>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
