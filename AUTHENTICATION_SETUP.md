# Google OAuth Setup Guide for Verbira

## Current Status ✅
- ✅ Supabase project created and configured (`verbira-platform`)
- ✅ User authentication tables and RLS policies set up
- ✅ Supabase client library installed and configured
- ✅ Authentication context implemented
- ✅ Login and signup pages updated to use Supabase Auth
- ✅ Development server running on http://localhost:8081/

## Next Steps: Google OAuth Configuration

### 1. Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (if not already enabled)

### 2. Configure OAuth Consent Screen
1. Go to [APIs & Services > OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent)
2. Choose "External" user type (for public app)
3. Fill in the required information:
   - App name: `Verbira`
   - User support email: Your email
   - App logo: (optional)
   - Application home page: `http://localhost:8081` (for development)
   - Privacy policy: `http://localhost:8081/privacy`
   - Terms of service: `http://localhost:8081/terms`
4. Under "Authorized domains", add:
   - `localhost` (for development)
   - `swsnqpavwcnqiihsidss.supabase.co` (your Supabase domain)
5. Configure scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`

### 3. Create OAuth 2.0 Credentials
1. Go to [APIs & Services > Credentials](https://console.cloud.google.com/apis/credentials)
2. Click "Create Credentials" > "OAuth 2.0 Client ID"
3. Choose "Web application"
4. Set the name: `Verbira Web Client`
5. Under "Authorized JavaScript origins", add:
   - `http://localhost:8081` (for development)
   - `https://swsnqpavwcnqiihsidss.supabase.co` (your Supabase domain)
6. Under "Authorized redirect URIs", add:
   - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`

### 4. Configure Supabase with Google Credentials
1. Copy your Google Client ID and Client Secret from the credentials page
2. Go to [Supabase Dashboard > Authentication > Providers](https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers)
3. Find the Google provider and toggle it ON
4. Paste your Client ID and Client Secret
5. Save the configuration

### 5. Update Environment Variables
Update your `.env.local` file with the Google credentials:
```
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

## Testing the Authentication Flow

### Email/Password Authentication:
1. Go to http://localhost:8081/signup
2. Create a new account with email and password
3. Check your email for verification link
4. Try logging in at http://localhost:8081/login

### Google OAuth Authentication:
1. Go to http://localhost:8081/login
2. Click "Continue with Google"
3. Complete the Google OAuth flow
4. You should be redirected back to the app and signed in

## Important URLs for Your Reference
- **Supabase Project**: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss
- **Google OAuth Callback URL**: `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
- **Local Development URL**: http://localhost:8081/
- **Supabase Auth Providers**: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers

## Troubleshooting

### Common Issues:
1. **"OAuth app not verified"**: This is normal for development. Click "Advanced" and "Go to Verbira (unsafe)" to proceed.
2. **Redirect URI mismatch**: Ensure the callback URL in Google Cloud Console exactly matches the Supabase callback URL.
3. **CORS errors**: Make sure your Supabase project allows the correct origins.

### User Profile Data
When users sign up, their profile information is automatically stored in the `user_profiles` table with:
- `id` (linked to auth.users)
- `full_name`
- `email`
- `organization` (optional)
- `avatar_url` (from OAuth providers)
- `newsletter_subscribed`
- `created_at` / `updated_at`

## Database Tables Created
- `user_profiles`: User profile information
- `user_documents`: User's saved documents/papers
- `user_usage`: Track user activity and usage

All tables have Row Level Security (RLS) enabled and proper policies configured.

---

## 🎉 Your Verbira platform is ready for public launch!

The authentication system is fully configured with:
- ✅ Email/password authentication
- ✅ Google OAuth (pending your Google credentials setup)
- ✅ User profile management
- ✅ Secure database with RLS policies
- ✅ Modern, branded UI

Just complete the Google OAuth setup above and you'll have a fully functional authentication system!
