/**
 * Journal Recommendation Card Component
 * Displays individual journal recommendations with metrics and details
 */

import React, { useState } from 'react';
import { JournalRecommendationCardProps } from '../types';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  ExternalLink, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Users, 
  Award,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  Star,
  Globe,
  BookOpen
} from "lucide-react";

export const JournalRecommendationCard: React.FC<JournalRecommendationCardProps> = ({
  journal,
  ranking,
  onSelect,
  isSelected
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  /**
   * Get impact factor color
   */
  const getImpactFactorColor = (impactFactor: number): string => {
    if (impactFactor >= 5) return 'text-green-600 bg-green-100';
    if (impactFactor >= 2) return 'text-blue-600 bg-blue-100';
    if (impactFactor >= 1) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  /**
   * Get quartile color
   */
  const getQuartileColor = (quartile: string): string => {
    switch (quartile) {
      case 'Q1': return 'bg-green-100 text-green-800 border-green-200';
      case 'Q2': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Q3': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Q4': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  /**
   * Get acceptance rate color
   */
  const getAcceptanceRateColor = (rate: number): string => {
    if (rate < 20) return 'text-red-600';
    if (rate < 40) return 'text-yellow-600';
    if (rate < 60) return 'text-blue-600';
    return 'text-green-600';
  };

  /**
   * Format review time
   */
  const formatReviewTime = (days: number): string => {
    if (days < 30) return `${days} days`;
    const months = Math.round(days / 30);
    return `${months} month${months > 1 ? 's' : ''}`;
  };

  /**
   * Format publication fees
   */
  const formatFees = (fees: number): string => {
    if (fees === 0) return 'Free';
    return `$${fees.toLocaleString()}`;
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-lg ${
      isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
              {journal.name}
            </CardTitle>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {journal.publisher}
              </Badge>
              {journal.metrics.isOpenAccess && (
                <Badge className="bg-green-100 text-green-800 text-xs">
                  Open Access
                </Badge>
              )}
              <Badge className={`text-xs ${getQuartileColor(journal.metrics.quartile)}`}>
                {journal.metrics.quartile}
              </Badge>
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center mb-1">
              <Star className="w-4 h-4 text-yellow-500 mr-1" />
              <span className="text-sm font-semibold text-gray-900">
                {Math.round(journal.matchScore * 100)}%
              </span>
            </div>
            <div className="text-xs text-gray-500">Match Score</div>
          </div>
        </div>

        <p className="text-sm text-gray-600 line-clamp-2">
          {journal.description}
        </p>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          {/* Impact Factor */}
          <div className="text-center">
            <div className={`text-lg font-bold px-2 py-1 rounded ${getImpactFactorColor(journal.metrics.impactFactor)}`}>
              {journal.metrics.impactFactor.toFixed(2)}
            </div>
            <div className="text-xs text-gray-500 mt-1">Impact Factor</div>
          </div>

          {/* Acceptance Rate */}
          <div className="text-center">
            <div className={`text-lg font-bold ${getAcceptanceRateColor(journal.metrics.acceptanceRate)}`}>
              {journal.metrics.acceptanceRate}%
            </div>
            <div className="text-xs text-gray-500 mt-1">Acceptance Rate</div>
          </div>

          {/* Review Time */}
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {formatReviewTime(journal.metrics.averageReviewTime)}
            </div>
            <div className="text-xs text-gray-500 mt-1">Review Time</div>
          </div>

          {/* Publication Fees */}
          <div className="text-center">
            <div className={`text-lg font-bold ${journal.metrics.publicationFees === 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatFees(journal.metrics.publicationFees)}
            </div>
            <div className="text-xs text-gray-500 mt-1">Pub. Fees</div>
          </div>
        </div>

        {/* Match Reasons */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-900 mb-2">Why this journal matches:</h4>
          <div className="flex flex-wrap gap-1">
            {journal.matchReasons.slice(0, 3).map((reason, index) => (
              <Badge key={index} variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                {reason}
              </Badge>
            ))}
            {journal.matchReasons.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{journal.matchReasons.length - 3} more
              </Badge>
            )}
          </div>
        </div>

        {/* Ranking Breakdown */}
        {ranking && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-gray-900">Overall Score</span>
              <span className="text-sm font-bold text-blue-600">
                {Math.round(ranking.weightedScore * 100)}%
              </span>
            </div>
            <Progress value={ranking.weightedScore * 100} className="h-2 mb-2" />
            
            <div className="grid grid-cols-5 gap-1 text-xs">
              <div className="text-center">
                <div className="text-blue-600 font-medium">
                  {Math.round(ranking.breakdown.relevance * 100)}
                </div>
                <div className="text-gray-500">Relevance</div>
              </div>
              <div className="text-center">
                <div className="text-green-600 font-medium">
                  {Math.round(ranking.breakdown.impact * 100)}
                </div>
                <div className="text-gray-500">Impact</div>
              </div>
              <div className="text-center">
                <div className="text-yellow-600 font-medium">
                  {Math.round(ranking.breakdown.speed * 100)}
                </div>
                <div className="text-gray-500">Speed</div>
              </div>
              <div className="text-center">
                <div className="text-red-600 font-medium">
                  {Math.round(ranking.breakdown.cost * 100)}
                </div>
                <div className="text-gray-500">Cost</div>
              </div>
              <div className="text-center">
                <div className="text-purple-600 font-medium">
                  {Math.round(ranking.breakdown.accessibility * 100)}
                </div>
                <div className="text-gray-500">Access</div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            {journal.website && (
              <Button variant="outline" size="sm" asChild>
                <a href={journal.website} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="w-4 h-4 mr-1" />
                  Visit
                </a>
              </Button>
            )}
            
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm">
                  {isExpanded ? (
                    <>
                      <ChevronUp className="w-4 h-4 mr-1" />
                      Less
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4 mr-1" />
                      More
                    </>
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>

          <Button
            onClick={() => onSelect(journal)}
            variant={isSelected ? "default" : "outline"}
            size="sm"
          >
            {isSelected ? (
              <>
                <CheckCircle className="w-4 h-4 mr-1" />
                Selected
              </>
            ) : (
              'Select'
            )}
          </Button>
        </div>

        {/* Expanded Details */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="mt-4 space-y-4">
            {/* Scope */}
            {journal.scope.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                  <BookOpen className="w-4 h-4 mr-1" />
                  Journal Scope
                </h4>
                <div className="flex flex-wrap gap-1">
                  {journal.scope.map((scope, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {scope}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Target Audience */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                <Users className="w-4 h-4 mr-1" />
                Target Audience
              </h4>
              <p className="text-sm text-gray-600">{journal.targetAudience}</p>
            </div>

            {/* Geographic Focus */}
            {journal.geographicFocus && (
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                  <Globe className="w-4 h-4 mr-1" />
                  Geographic Focus
                </h4>
                <p className="text-sm text-gray-600">{journal.geographicFocus}</p>
              </div>
            )}

            {/* Language Requirements */}
            {journal.languageRequirements.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-2">
                  Language Requirements
                </h4>
                <div className="flex flex-wrap gap-1">
                  {journal.languageRequirements.map((lang, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Submission Guidelines */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-2">
                Submission Guidelines
              </h4>
              <p className="text-sm text-gray-600">{journal.submissionGuidelines}</p>
            </div>

            {/* All Match Reasons */}
            {journal.matchReasons.length > 3 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-2">
                  All Match Reasons
                </h4>
                <div className="flex flex-wrap gap-1">
                  {journal.matchReasons.map((reason, index) => (
                    <Badge key={index} variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                      {reason}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};
