/**
 * Utility functions for Agent Mode
 * Error handling, validation, and helper functions
 */

import { AgentModeError, PromptAnalysis, DocumentStructure } from './types';

/**
 * Validate user prompt for Agent Mode
 */
export function validatePrompt(prompt: string): { isValid: boolean; error?: string; suggestions?: string[] } {
  const trimmedPrompt = prompt.trim();
  
  if (!trimmedPrompt) {
    return {
      isValid: false,
      error: 'Please enter a prompt describing what you want to edit.',
      suggestions: [
        'Try: "Improve the introduction for better clarity"',
        'Try: "Rewrite the methodology section to be more detailed"',
        'Try: "Make the conclusion more compelling"'
      ]
    };
  }

  if (trimmedPrompt.length < 10) {
    return {
      isValid: false,
      error: 'Please provide a more detailed description of what you want to edit.',
      suggestions: [
        'Be specific about which section you want to edit',
        'Describe the type of improvement you want',
        'Use keywords like "introduction", "methodology", "conclusion"'
      ]
    };
  }

  if (trimmedPrompt.length > 500) {
    return {
      isValid: false,
      error: 'Prompt is too long. Please keep it under 500 characters.',
      suggestions: [
        'Focus on one specific improvement',
        'Break down complex requests into multiple prompts',
        'Use concise language to describe your needs'
      ]
    };
  }

  // Check for potentially problematic prompts
  const problematicPatterns = [
    /delete|remove|erase/i,
    /replace everything|rewrite all|change all/i,
    /ignore|disregard/i
  ];

  for (const pattern of problematicPatterns) {
    if (pattern.test(trimmedPrompt)) {
      return {
        isValid: false,
        error: 'This type of request is not supported by Agent Mode.',
        suggestions: [
          'Focus on improving specific sections rather than wholesale changes',
          'Use the regular AI tools for major document restructuring',
          'Be specific about which parts you want to enhance'
        ]
      };
    }
  }

  return { isValid: true };
}

/**
 * Validate document content for Agent Mode processing
 */
export function validateDocumentContent(content: string): { isValid: boolean; error?: string; suggestions?: string[] } {
  const trimmedContent = content.trim();
  
  if (!trimmedContent) {
    return {
      isValid: false,
      error: 'Document is empty. Please add some content before using Agent Mode.',
      suggestions: [
        'Write or paste your document content',
        'Import a document using the import feature',
        'Start with a basic outline or draft'
      ]
    };
  }

  if (trimmedContent.length < 100) {
    return {
      isValid: false,
      error: 'Document is too short for Agent Mode analysis.',
      suggestions: [
        'Add more content to your document (at least 100 characters)',
        'Use regular AI tools for very short texts',
        'Expand your content with more details'
      ]
    };
  }

  if (trimmedContent.length > 50000) {
    return {
      isValid: false,
      error: 'Document is too long for Agent Mode processing.',
      suggestions: [
        'Break your document into smaller sections',
        'Use Agent Mode on specific sections rather than the entire document',
        'Consider using document-level AI tools instead'
      ]
    };
  }

  return { isValid: true };
}

/**
 * Create user-friendly error messages for Agent Mode failures
 */
export function createAgentModeError(
  code: string, 
  message: string, 
  details?: any
): AgentModeError {
  const errorMap: Record<string, { message: string; suggestions: string[] }> = {
    'NO_SECTIONS_FOUND': {
      message: 'Could not identify any relevant sections in your document for this request.',
      suggestions: [
        'Try being more specific about which part of the document you want to edit',
        'Use section names like "introduction", "methodology", or "conclusion"',
        'Check if your document has clear headings and structure',
        'Try a different approach or use manual text selection instead'
      ]
    },
    'AMBIGUOUS_PROMPT': {
      message: 'Your request is too ambiguous. Please be more specific.',
      suggestions: [
        'Specify which section you want to edit',
        'Describe the type of improvement you want',
        'Use clear, actionable language',
        'Try breaking complex requests into simpler ones'
      ]
    },
    'AI_SERVICE_ERROR': {
      message: 'The AI service encountered an error while processing your request.',
      suggestions: [
        'Try again in a few moments',
        'Check your internet connection',
        'Try a simpler prompt',
        'Contact support if the problem persists'
      ]
    },
    'DOCUMENT_ANALYSIS_FAILED': {
      message: 'Failed to analyze your document structure.',
      suggestions: [
        'Ensure your document has proper formatting',
        'Check for any unusual characters or formatting',
        'Try with a simpler document structure',
        'Use manual text selection as an alternative'
      ]
    },
    'EDIT_APPLICATION_FAILED': {
      message: 'Generated edits could not be applied to your document.',
      suggestions: [
        'Try refreshing the page and attempting again',
        'Use manual copy-paste for the generated content',
        'Check if your document structure has changed',
        'Contact support if this continues to happen'
      ]
    }
  };

  const errorInfo = errorMap[code] || {
    message: message || 'An unexpected error occurred.',
    suggestions: [
      'Try again with a different approach',
      'Check your document and prompt for any issues',
      'Contact support if the problem persists'
    ]
  };

  return {
    code,
    message: errorInfo.message,
    details,
    suggestions: errorInfo.suggestions
  };
}

/**
 * Analyze prompt quality and provide suggestions
 */
export function analyzePromptQuality(prompt: string): {
  score: number; // 0-100
  feedback: string[];
  suggestions: string[];
} {
  const feedback: string[] = [];
  const suggestions: string[] = [];
  let score = 50; // Base score

  // Check for section specificity
  const sectionKeywords = ['introduction', 'methodology', 'methods', 'results', 'discussion', 'conclusion', 'abstract'];
  const hasSectionKeyword = sectionKeywords.some(keyword => 
    prompt.toLowerCase().includes(keyword)
  );

  if (hasSectionKeyword) {
    score += 20;
    feedback.push('✓ Targets specific document section');
  } else {
    score -= 10;
    suggestions.push('Specify which section you want to edit (e.g., "introduction", "methodology")');
  }

  // Check for action clarity
  const actionKeywords = ['improve', 'enhance', 'rewrite', 'clarify', 'expand', 'revise', 'edit'];
  const hasActionKeyword = actionKeywords.some(keyword => 
    prompt.toLowerCase().includes(keyword)
  );

  if (hasActionKeyword) {
    score += 15;
    feedback.push('✓ Clear action specified');
  } else {
    score -= 15;
    suggestions.push('Specify what type of improvement you want (e.g., "improve clarity", "expand details")');
  }

  // Check for quality descriptors
  const qualityKeywords = ['clarity', 'detailed', 'compelling', 'engaging', 'precise', 'concise'];
  const hasQualityKeyword = qualityKeywords.some(keyword => 
    prompt.toLowerCase().includes(keyword)
  );

  if (hasQualityKeyword) {
    score += 10;
    feedback.push('✓ Specific quality improvement mentioned');
  } else {
    suggestions.push('Add quality descriptors (e.g., "for better clarity", "more engaging")');
  }

  // Check length
  if (prompt.length >= 20 && prompt.length <= 100) {
    score += 10;
    feedback.push('✓ Good prompt length');
  } else if (prompt.length < 20) {
    score -= 10;
    suggestions.push('Provide more detail in your prompt');
  } else {
    score -= 5;
    suggestions.push('Try to be more concise');
  }

  // Ensure score is within bounds
  score = Math.max(0, Math.min(100, score));

  return { score, feedback, suggestions };
}

/**
 * Generate helpful suggestions based on document structure
 */
export function generatePromptSuggestions(documentStructure: DocumentStructure): string[] {
  const suggestions: string[] = [];
  
  // Base suggestions
  const baseSuggestions = [
    'Improve the overall clarity and readability',
    'Enhance the academic tone and language',
    'Make the writing more engaging and compelling'
  ];

  // Section-specific suggestions based on document structure
  documentStructure.sections.forEach(section => {
    switch (section.type) {
      case 'introduction':
        suggestions.push(`Improve the introduction for better engagement`);
        suggestions.push(`Make the introduction more compelling and clear`);
        break;
      case 'methodology':
      case 'methods':
        suggestions.push(`Enhance the methodology section with more detail`);
        suggestions.push(`Clarify the research methods and procedures`);
        break;
      case 'results':
        suggestions.push(`Improve the results section presentation`);
        suggestions.push(`Make the findings more clear and impactful`);
        break;
      case 'discussion':
        suggestions.push(`Enhance the discussion with deeper analysis`);
        suggestions.push(`Improve the flow and argumentation in discussion`);
        break;
      case 'conclusion':
        suggestions.push(`Make the conclusion more compelling and decisive`);
        suggestions.push(`Strengthen the concluding statements and implications`);
        break;
      case 'abstract':
        suggestions.push(`Improve the abstract to better highlight key findings`);
        suggestions.push(`Make the abstract more concise and impactful`);
        break;
    }
  });

  // Combine and deduplicate
  const allSuggestions = [...baseSuggestions, ...suggestions];
  return Array.from(new Set(allSuggestions)).slice(0, 8); // Limit to 8 suggestions
}

/**
 * Format processing time for user display
 */
export function formatProcessingTime(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Estimate token usage for cost tracking
 */
export function estimateTokenUsage(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}
