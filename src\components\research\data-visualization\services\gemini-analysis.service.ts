import { GoogleGenAI } from '@google/genai';
import {
  GeminiCodeExecutionRequest,
  GeminiCodeExecutionResponse,
  DataAnalysisResult,
  VisualizationConfig,
  UploadedFile
} from '../types';
import { DATA_VIZ_CONFIG, ANALYSIS_PROMPTS } from '../constants';
import { FileProcessingService } from './file-processing.service';
import { FallbackAnalysisService } from './fallback-analysis.service';

export class GeminiAnalysisService {
  private static ai: GoogleGenAI | null = null;
  private static lastRequestTime: number = 0;

  /**
   * Initialize Gemini AI client
   */
  private static getAI(): GoogleGenAI {
    if (!this.ai) {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('Gemini API key is not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
      }
      if (apiKey.length < 20) {
        throw new Error('Gemini API key appears to be invalid. Please check your VITE_GEMINI_API_KEY in the .env file.');
      }
      this.ai = new GoogleGenAI({ apiKey });
    }
    return this.ai;
  }

  /**
   * Perform comprehensive data analysis
   */
  static async analyzeData(file: UploadedFile): Promise<DataAnalysisResult> {
    // Check if Gemini API is available
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (!apiKey || apiKey.length < 20) {
      console.warn('Gemini API key not available, using fallback analysis');
      return FallbackAnalysisService.analyzeData(file);
    }

    try {
      // Try Gemini analysis first
      return await this.performGeminiAnalysis(file);
    } catch (error) {
      console.error('Gemini analysis failed, falling back to basic analysis:', error);

      // If Gemini fails, use fallback
      try {
        const fallbackResult = await FallbackAnalysisService.analyzeData(file);
        // Add a note that this is fallback analysis
        fallbackResult.insights.recommendations.unshift(
          'Note: Advanced AI analysis unavailable. Using basic statistical analysis.'
        );
        return fallbackResult;
      } catch (fallbackError) {
        console.error('Fallback analysis also failed:', fallbackError);
        throw new Error(`Both AI and fallback analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Perform Gemini-powered analysis
   */
  private static async performGeminiAnalysis(file: UploadedFile): Promise<DataAnalysisResult> {
    try {
      // Detect data types
      const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);

      // Calculate basic statistics
      const basicStats = FileProcessingService.calculateBasicStats(file.data, file.headers, dataTypes);

      // Count missing values
      const missingValues: Record<string, number> = {};
      file.headers.forEach((header, index) => {
        const columnData = file.data.map(row => row[index]);
        missingValues[header] = columnData.filter(val => val == null || val === '').length;
      });

      // Generate AI insights
      const insights = await this.generateInsights(file, dataTypes, basicStats);

      // Generate visualizations
      const visualizations = await this.generateVisualizations(file, dataTypes);

      const result: DataAnalysisResult = {
        id: `gemini-analysis-${file.id}`,
        fileId: file.id,
        summary: {
          rowCount: file.data.length,
          columnCount: file.headers.length,
          dataTypes,
          missingValues,
          basicStats
        },
        insights,
        visualizations,
        generatedAt: new Date()
      };

      return result;
    } catch (error) {
      console.error('Gemini analysis failed:', error);
      throw error; // Re-throw to trigger fallback
    }
  }

  /**
   * Generate AI-powered insights
   */
  private static async generateInsights(
    file: UploadedFile, 
    dataTypes: Record<string, string>, 
    basicStats: Record<string, any>
  ) {
    const prompt = this.buildInsightsPrompt(file, dataTypes, basicStats);
    
    const response = await this.executeWithGemini({
      prompt,
      dataContext: {
        fileName: file.name,
        headers: file.headers,
        sampleData: file.data.slice(0, 10), // First 10 rows as sample
        dataTypes
      }
    });

    return this.parseInsightsResponse(response.text || '');
  }

  /**
   * Generate visualization configurations
   */
  private static async generateVisualizations(
    file: UploadedFile, 
    dataTypes: Record<string, string>
  ): Promise<VisualizationConfig[]> {
    const visualizations: VisualizationConfig[] = [];
    
    // Find numeric columns for histograms and correlations
    const numericColumns = file.headers.filter(header => 
      dataTypes[header] === 'number' || dataTypes[header] === 'integer'
    );

    // Find categorical columns
    const categoricalColumns = file.headers.filter(header => 
      dataTypes[header] === 'categorical' || dataTypes[header] === 'string'
    );

    // Generate histograms for numeric columns
    numericColumns.slice(0, 3).forEach((column, index) => {
      const columnIndex = file.headers.indexOf(column);
      const columnData = file.data.map(row => row[columnIndex]).filter(val => val != null);
      
      visualizations.push({
        id: `histogram-${index}`,
        type: 'histogram',
        title: `Distribution of ${column}`,
        description: `Histogram showing the distribution of values in ${column}`,
        data: [{
          x: columnData,
          type: 'histogram',
          name: column,
          nbinsx: 30,
          marker: { opacity: 0.7 }
        }],
        layout: {
          title: `Distribution of ${column}`,
          xaxis: { title: column },
          yaxis: { title: 'Frequency' },
          showlegend: false
        }
      });
    });

    // Generate correlation heatmap if multiple numeric columns
    if (numericColumns.length > 1) {
      const correlationData = this.calculateCorrelationMatrix(file, numericColumns);
      
      visualizations.push({
        id: 'correlation-heatmap',
        type: 'correlation_heatmap',
        title: 'Correlation Matrix',
        description: 'Heatmap showing correlations between numeric variables',
        data: [{
          z: correlationData.matrix,
          x: correlationData.labels,
          y: correlationData.labels,
          type: 'heatmap',
          colorscale: 'RdBu',
          reversescale: true,
          showscale: true
        }],
        layout: {
          title: 'Correlation Matrix',
          xaxis: { title: 'Variables' },
          yaxis: { title: 'Variables' }
        }
      });
    }

    // Generate bar chart for categorical data
    if (categoricalColumns.length > 0 && numericColumns.length > 0) {
      const catColumn = categoricalColumns[0];
      const numColumn = numericColumns[0];
      
      const aggregatedData = this.aggregateData(file, catColumn, numColumn);
      
      visualizations.push({
        id: 'bar-chart',
        type: 'bar_chart',
        title: `${numColumn} by ${catColumn}`,
        description: `Bar chart showing ${numColumn} grouped by ${catColumn}`,
        data: [{
          x: aggregatedData.categories,
          y: aggregatedData.values,
          type: 'bar',
          marker: { opacity: 0.8 }
        }],
        layout: {
          title: `${numColumn} by ${catColumn}`,
          xaxis: { title: catColumn },
          yaxis: { title: numColumn }
        }
      });
    }

    return visualizations;
  }

  /**
   * Execute code with Gemini with timeout and retry logic
   */
  static async executeWithGemini(request: GeminiCodeExecutionRequest, retryCount = 0): Promise<GeminiCodeExecutionResponse> {
    try {
      // Rate limiting: ensure minimum interval between requests
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < DATA_VIZ_CONFIG.MIN_REQUEST_INTERVAL) {
        const waitTime = DATA_VIZ_CONFIG.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
        console.log(`Rate limiting: waiting ${waitTime}ms before next request`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      this.lastRequestTime = Date.now();

      const ai = this.getAI();

      const tools = [{ codeExecution: {} }];
      const config = {
        thinkingConfig: {
          thinkingBudget: DATA_VIZ_CONFIG.THINKING_BUDGET,
        },
        tools,
        responseMimeType: DATA_VIZ_CONFIG.RESPONSE_MIME_TYPE,
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: request.prompt }],
      }];

      // Add timeout to the request
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Gemini API request timed out after 30 seconds'));
        }, 30000);
      });

      const responsePromise = ai.models.generateContentStream({
        model: DATA_VIZ_CONFIG.GEMINI_MODEL,
        config,
        contents,
      });

      const response = await Promise.race([responsePromise, timeoutPromise]);

      let fullText = '';
      let code = '';
      let result = '';
      
      for await (const chunk of response) {
        if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
          continue;
        }
        
        const part = chunk.candidates[0].content.parts[0];
        
        if (part.text) {
          fullText += part.text;
        }
        
        if (part.executableCode) {
          code += part.executableCode.code || '';
        }
        
        if (part.codeExecutionResult) {
          const output = part.codeExecutionResult.output || '';
          result += output;

          // Check for common library import errors
          if (output.includes('No module named') || output.includes('ModuleNotFoundError')) {
            console.warn('Library import error detected:', output);
          }
        }
      }

      // Check for library import errors in the result
      let processedText = fullText;
      if (result && (result.includes('No module named') || result.includes('ModuleNotFoundError'))) {
        processedText += '\n\n⚠️ **Note**: Some visualization libraries are not available in the execution environment. The analysis has been completed with available libraries (pandas, matplotlib, numpy, scipy).';
      }

      // Extract base64 images from the result
      const images: string[] = [];
      if (result) {
        const imageMatches = result.match(/PLOT_IMAGE_BASE64:([A-Za-z0-9+/=]+)/g);
        if (imageMatches) {
          imageMatches.forEach(match => {
            const base64Data = match.replace('PLOT_IMAGE_BASE64:', '');
            images.push(base64Data);
          });
        }
      }

      return {
        text: processedText,
        code: code || undefined,
        result: result || undefined,
        images: images.length > 0 ? images : undefined
      };
    } catch (error) {
      console.error('Gemini execution failed:', error);

      if (error instanceof Error) {
        // Check for specific API overload errors that warrant retry
        const isRetryableError = error.message.includes('overloaded') ||
                                error.message.includes('quota') ||
                                error.message.includes('rate limit') ||
                                error.message.includes('429') ||
                                error.message.includes('timeout');

        if (isRetryableError && retryCount < DATA_VIZ_CONFIG.MAX_RETRIES) {
          const backoffDelay = Math.pow(2, retryCount) * 1000; // Exponential backoff
          console.log(`Retrying request in ${backoffDelay}ms (attempt ${retryCount + 1}/${DATA_VIZ_CONFIG.MAX_RETRIES})`);
          await new Promise(resolve => setTimeout(resolve, backoffDelay));
          return this.executeWithGemini(request, retryCount + 1);
        }

        if (isRetryableError) {
          throw new Error('Gemini API is currently overloaded. Please wait a few minutes and try again.');
        }
      }

      throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform advanced analysis with custom configuration
   */
  static async performAdvancedAnalysis(file: UploadedFile, config: any): Promise<DataAnalysisResult> {
    try {
      // Detect data types
      const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);

      // Calculate basic statistics
      const basicStats = FileProcessingService.calculateBasicStats(file.data, file.headers, dataTypes);

      // Count missing values
      const missingValues: Record<string, number> = {};
      file.headers.forEach((header, index) => {
        const columnData = file.data.map(row => row[index]);
        missingValues[header] = columnData.filter(val => val == null || val === '').length;
      });

      // Generate advanced insights with configuration
      const insights = await this.generateAdvancedInsights(file, dataTypes, basicStats, config);

      // Generate custom visualizations based on preferences
      const visualizations = await this.generateCustomVisualizations(file, dataTypes, config);

      const result: DataAnalysisResult = {
        id: `advanced-analysis-${file.id}`,
        fileId: file.id,
        summary: {
          rowCount: file.data.length,
          columnCount: file.headers.length,
          dataTypes,
          missingValues,
          basicStats
        },
        insights,
        visualizations,
        generatedAt: new Date()
      };

      return result;
    } catch (error) {
      console.error('Advanced analysis failed:', error);
      throw error;
    }
  }

  /**
   * Process enhanced query with settings and code execution
   */
  static async processEnhancedQuery(
    file: UploadedFile,
    question: string,
    settings: any,
    dataDescription?: string
  ): Promise<GeminiCodeExecutionResponse> {
    const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);

    // Prepare data for the AI
    const csvData = this.convertToCSVString(file);

    const prompt = `
Data scientist: Answer "${question}" about dataset (${file.data.length} rows, ${file.headers.length} cols).

IMPORTANT: The data is provided below as CSV format. DO NOT try to load from file.

CSV Data:
\`\`\`csv
${csvData}
\`\`\`

Columns: ${file.headers.join(', ')}
${dataDescription ? `Context: ${dataDescription}` : ''}

Style: ${settings.responseStyle}, Type: ${settings.analysisType}

CRITICAL: Use ONLY these standard libraries:
- pandas (data analysis)
- matplotlib (visualizations)
- numpy (calculations)
- scipy (statistics)

DO NOT use plotly, seaborn, or try to load files.

${settings.includeCode ? 'Generate executable Python code with error handling.' : 'Text analysis only.'}
${settings.includeVisualizations ? 'Create visualizations using matplotlib only.' : ''}

Code template:
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from io import StringIO
import base64
from io import BytesIO

try:
    # Create DataFrame from provided CSV data
    csv_data = """${csvData.replace(/"/g, '""')}"""
    df = pd.read_csv(StringIO(csv_data))

    print(f"Data loaded: {len(df)} rows, {len(df.columns)} columns")
    print(f"Columns: {list(df.columns)}")

    # Your analysis code here
    plt.figure(figsize=(10, 6))

    # Example for different chart types:
    # Histogram: plt.hist(df['numeric_column'], bins=30, alpha=0.7)
    # Scatter: plt.scatter(df['x_col'], df['y_col'], alpha=0.6)
    # Bar chart: df['category_col'].value_counts().plot(kind='bar')
    # Line chart: plt.plot(df['x_col'], df['y_col'])

    # Always include proper labels and title
    plt.title("Analysis Result")
    plt.xlabel("X Label")
    plt.ylabel("Y Label")
    plt.grid(True, alpha=0.3)

    # Save plot as base64 image for display
    buffer = BytesIO()
    plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode('utf-8')
    buffer.close()
    plt.close()

    # Output the image for display
    print(f"PLOT_IMAGE_BASE64:{image_base64}")
    print("Analysis completed successfully")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
\`\`\`

Use code_execution tool. Be concise but thorough.
    `;

    return this.executeWithGemini({
      prompt,
      dataContext: {
        fileName: file.name,
        headers: file.headers,
        sampleData: file.data.slice(0, 5),
        dataTypes,
        description: dataDescription
      }
    });
  }

  /**
   * Process natural language query with enhanced visualization support
   */
  static async processQuery(file: UploadedFile, question: string, dataDescription?: string): Promise<GeminiCodeExecutionResponse> {
    const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);
    const csvData = this.convertToCSVString(file);

    const prompt = `
Answer: "${question}" about dataset (${file.data.length} rows, ${file.headers.length} cols).

IMPORTANT: Data provided as CSV below. DO NOT try to load from file.

CSV Data:
\`\`\`csv
${csvData}
\`\`\`

Columns: ${file.headers.join(', ')}
${dataDescription ? `Context: ${dataDescription}` : ''}

CRITICAL: Use ONLY standard Python libraries:
- pandas (data analysis)
- matplotlib (visualizations)
- numpy (calculations)
- scipy (statistics)

DO NOT use plotly, seaborn, or try to load files.

Code template:
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from io import StringIO
import base64
from io import BytesIO

try:
    # Create DataFrame from provided CSV data
    csv_data = """${csvData.replace(/"/g, '""')}"""
    df = pd.read_csv(StringIO(csv_data))

    print(f"Data loaded: {len(df)} rows, {len(df.columns)} columns")
    print(f"Columns: {list(df.columns)}")

    # Your analysis code here
    plt.figure(figsize=(10, 6))

    # Example visualizations:
    # plt.hist(df['numeric_column'], bins=30, alpha=0.7)
    # plt.scatter(df['x_col'], df['y_col'], alpha=0.6)
    # df['category_col'].value_counts().plot(kind='bar')

    plt.title("Analysis Result")
    plt.xlabel("X Label")
    plt.ylabel("Y Label")
    plt.grid(True, alpha=0.3)

    # Save plot as base64 image for display
    buffer = BytesIO()
    plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.read()).decode('utf-8')
    buffer.close()
    plt.close()

    # Output the image for display
    print(f"PLOT_IMAGE_BASE64:{image_base64}")
    print("Analysis completed successfully")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
\`\`\`

Provide executable code with error handling.
    `;

    return this.executeWithGemini({
      prompt,
      dataContext: {
        fileName: file.name,
        headers: file.headers,
        sampleData: file.data.slice(0, 10),
        dataTypes,
        description: dataDescription
      }
    });
  }

  /**
   * Generate advanced insights with configuration
   */
  private static async generateAdvancedInsights(
    file: UploadedFile,
    dataTypes: Record<string, string>,
    basicStats: Record<string, any>,
    config: any
  ) {
    const prompt = this.buildAdvancedInsightsPrompt(file, dataTypes, basicStats, config);

    const response = await this.executeWithGemini({
      prompt,
      dataContext: {
        fileName: file.name,
        headers: file.headers,
        sampleData: file.data.slice(0, 5), // Limited sample data to prevent overload
        dataTypes
      }
    });

    return this.parseInsightsResponse(response.text || '');
  }

  /**
   * Generate custom visualizations based on user preferences
   */
  private static async generateCustomVisualizations(
    file: UploadedFile,
    dataTypes: Record<string, string>,
    config: any
  ): Promise<VisualizationConfig[]> {
    const visualizations: VisualizationConfig[] = [];

    // Find numeric and categorical columns
    const numericColumns = file.headers.filter(header =>
      dataTypes[header] === 'number' || dataTypes[header] === 'integer'
    );
    const categoricalColumns = file.headers.filter(header =>
      dataTypes[header] === 'categorical' || dataTypes[header] === 'string'
    );

    // Generate visualizations based on user preferences
    const selectedTypes = config.visualizationTypes.length > 0
      ? config.visualizationTypes
      : ['histogram', 'scatter', 'correlation', 'bar'];

    // Generate histograms if requested
    if (selectedTypes.includes('histogram') && numericColumns.length > 0) {
      numericColumns.slice(0, 4).forEach((column, index) => {
        const columnIndex = file.headers.indexOf(column);
        const columnData = file.data.map(row => row[columnIndex]).filter(val => val != null);

        visualizations.push({
          id: `advanced-histogram-${index}`,
          type: 'histogram',
          title: `Distribution Analysis: ${column}`,
          description: `Advanced histogram showing distribution patterns in ${column}`,
          data: [{
            x: columnData,
            type: 'histogram',
            name: column,
            nbinsx: 40,
            marker: {
              opacity: 0.7,
              color: this.getColorForScheme(config.colorScheme, index)
            }
          }],
          layout: {
            title: `Distribution Analysis: ${column}`,
            xaxis: { title: column },
            yaxis: { title: 'Frequency' },
            showlegend: false,
            ...this.getLayoutForScheme(config.colorScheme)
          }
        });
      });
    }

    // Generate scatter plots if requested
    if (selectedTypes.includes('scatter') && numericColumns.length >= 2) {
      for (let i = 0; i < Math.min(numericColumns.length - 1, 3); i++) {
        const xColumn = numericColumns[i];
        const yColumn = numericColumns[i + 1];

        const xIndex = file.headers.indexOf(xColumn);
        const yIndex = file.headers.indexOf(yColumn);

        const xData = file.data.map(row => Number(row[xIndex])).filter(val => !isNaN(val));
        const yData = file.data.map(row => Number(row[yIndex])).filter(val => !isNaN(val));

        visualizations.push({
          id: `advanced-scatter-${i}`,
          type: 'scatter',
          title: `Relationship: ${xColumn} vs ${yColumn}`,
          description: `Scatter plot analysis of relationship between ${xColumn} and ${yColumn}`,
          data: [{
            x: xData,
            y: yData,
            type: 'scatter',
            mode: 'markers',
            name: `${xColumn} vs ${yColumn}`,
            marker: {
              size: 8,
              opacity: 0.7,
              color: this.getColorForScheme(config.colorScheme, i)
            }
          }],
          layout: {
            title: `Relationship: ${xColumn} vs ${yColumn}`,
            xaxis: { title: xColumn },
            yaxis: { title: yColumn },
            showlegend: false,
            ...this.getLayoutForScheme(config.colorScheme)
          }
        });
      }
    }

    // Generate correlation heatmap if requested
    if (selectedTypes.includes('correlation') && numericColumns.length > 1) {
      const correlationData = this.calculateCorrelationMatrix(file, numericColumns);

      visualizations.push({
        id: 'advanced-correlation-heatmap',
        type: 'correlation_heatmap',
        title: 'Advanced Correlation Analysis',
        description: 'Comprehensive correlation matrix with statistical significance',
        data: [{
          z: correlationData.matrix,
          x: correlationData.labels,
          y: correlationData.labels,
          type: 'heatmap',
          colorscale: this.getHeatmapColorScale(config.colorScheme),
          reversescale: true,
          showscale: true,
          hoverongaps: false
        }],
        layout: {
          title: 'Advanced Correlation Analysis',
          xaxis: { title: 'Variables', side: 'bottom' },
          yaxis: { title: 'Variables' },
          ...this.getLayoutForScheme(config.colorScheme)
        }
      });
    }

    // Generate bar charts if requested
    if (selectedTypes.includes('bar') && categoricalColumns.length > 0 && numericColumns.length > 0) {
      const catColumn = categoricalColumns[0];
      const numColumn = numericColumns[0];

      const aggregatedData = this.aggregateData(file, catColumn, numColumn);

      visualizations.push({
        id: 'advanced-bar-chart',
        type: 'bar_chart',
        title: `Advanced Analysis: ${numColumn} by ${catColumn}`,
        description: `Comprehensive bar chart showing ${numColumn} patterns across ${catColumn} categories`,
        data: [{
          x: aggregatedData.categories,
          y: aggregatedData.values,
          type: 'bar',
          marker: {
            opacity: 0.8,
            color: aggregatedData.categories.map((_, i) =>
              this.getColorForScheme(config.colorScheme, i)
            )
          }
        }],
        layout: {
          title: `Advanced Analysis: ${numColumn} by ${catColumn}`,
          xaxis: { title: catColumn },
          yaxis: { title: numColumn },
          ...this.getLayoutForScheme(config.colorScheme)
        }
      });
    }

    return visualizations;
  }

  /**
   * Helper methods
   */
  private static buildInsightsPrompt(
    file: UploadedFile,
    dataTypes: Record<string, string>,
    basicStats: Record<string, any>
  ): string {
    return `
${ANALYSIS_PROMPTS.INSIGHTS_GENERATION}

Dataset: ${file.name}
Rows: ${file.data.length}
Columns: ${file.headers.length}

Column Information:
${file.headers.map(header => `- ${header}: ${dataTypes[header]}`).join('\n')}

Basic Statistics:
${JSON.stringify(basicStats, null, 2)}

Sample Data (first 5 rows):
${this.formatSampleData(file)}

Please provide insights in the following JSON format:
{
  "dataQuality": "Overall assessment of data quality",
  "keyFindings": ["Finding 1", "Finding 2", ...],
  "patterns": ["Pattern 1", "Pattern 2", ...],
  "correlations": ["Correlation 1", "Correlation 2", ...],
  "recommendations": ["Recommendation 1", "Recommendation 2", ...]
}
    `;
  }

  private static parseInsightsResponse(response: string) {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('Failed to parse insights JSON, using fallback');
    }

    // Fallback to default structure
    return {
      dataQuality: "Analysis completed successfully",
      keyFindings: ["Data structure analyzed", "Basic statistics calculated"],
      patterns: ["Data patterns identified"],
      correlations: ["Correlation analysis performed"],
      recommendations: ["Consider further analysis", "Explore data visualizations"]
    };
  }

  private static formatSampleData(file: UploadedFile): string {
    const sampleRows = file.data.slice(0, 5);
    const table = [file.headers, ...sampleRows];
    
    return table.map(row => row.join('\t')).join('\n');
  }

  private static calculateCorrelationMatrix(file: UploadedFile, numericColumns: string[]) {
    const matrix: number[][] = [];
    const labels = numericColumns;

    for (let i = 0; i < numericColumns.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < numericColumns.length; j++) {
        if (i === j) {
          matrix[i][j] = 1;
        } else {
          const col1Index = file.headers.indexOf(numericColumns[i]);
          const col2Index = file.headers.indexOf(numericColumns[j]);
          
          const data1 = file.data.map(row => Number(row[col1Index])).filter(val => !isNaN(val));
          const data2 = file.data.map(row => Number(row[col2Index])).filter(val => !isNaN(val));
          
          matrix[i][j] = this.calculateCorrelation(data1, data2);
        }
      }
    }

    return { matrix, labels };
  }

  private static calculateCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const meanX = x.reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let sumXSquared = 0;
    let sumYSquared = 0;

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = y[i] - meanY;
      
      numerator += deltaX * deltaY;
      sumXSquared += deltaX * deltaX;
      sumYSquared += deltaY * deltaY;
    }

    const denominator = Math.sqrt(sumXSquared * sumYSquared);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  private static aggregateData(file: UploadedFile, catColumn: string, numColumn: string) {
    const catIndex = file.headers.indexOf(catColumn);
    const numIndex = file.headers.indexOf(numColumn);
    
    const groups: Record<string, number[]> = {};
    
    file.data.forEach(row => {
      const category = String(row[catIndex] || 'Unknown');
      const value = Number(row[numIndex]);
      
      if (!isNaN(value)) {
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(value);
      }
    });

    const categories = Object.keys(groups);
    const values = categories.map(cat => {
      const vals = groups[cat];
      return vals.reduce((sum, val) => sum + val, 0) / vals.length; // Average
    });

    return { categories, values };
  }

  /**
   * Build advanced insights prompt with configuration
   */
  private static buildAdvancedInsightsPrompt(
    file: UploadedFile,
    dataTypes: Record<string, string>,
    basicStats: Record<string, any>,
    config: any
  ): string {
    const focusAreasText = config.focusAreas.length > 0
      ? `Focus specifically on: ${config.focusAreas.join(', ')}`
      : '';

    const customPromptText = config.customPrompt
      ? `Additional instructions: ${config.customPrompt}`
      : '';

    return `
Analyze dataset "${file.name}" (${file.data.length} rows, ${file.headers.length} cols) for ${config.analysisType} insights.

Columns: ${file.headers.slice(0, 8).join(', ')}${file.headers.length > 8 ? '...' : ''}
Sample: ${this.formatSampleData(file).slice(0, 300)}...

${focusAreasText}
${customPromptText}

Requirements: ${config.includeStatisticalTests ? 'Stats, ' : ''}${config.includeCorrelations ? 'Correlations, ' : ''}${config.includeOutlierAnalysis ? 'Outliers, ' : ''}${config.includePredictiveInsights ? 'Predictions' : ''}

Return JSON:
{
  "dataQuality": "Brief quality assessment",
  "keyFindings": ["Finding 1", "Finding 2", "Finding 3"],
  "patterns": ["Pattern 1", "Pattern 2"],
  "correlations": ["Correlation 1", "Correlation 2"],
  "recommendations": ["Recommendation 1", "Recommendation 2"]
}
    `;
  }

  /**
   * Get color for visualization based on color scheme
   */
  private static getColorForScheme(scheme: string, index: number): string {
    const colorSchemes = {
      default: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'],
      professional: ['#1E40AF', '#DC2626', '#059669', '#D97706', '#7C3AED', '#BE185D', '#0891B2', '#65A30D'],
      vibrant: ['#2563EB', '#F87171', '#34D399', '#FBBF24', '#A78BFA', '#F472B6', '#22D3EE', '#A3E635'],
      minimal: ['#6B7280', '#374151', '#4B5563', '#9CA3AF', '#D1D5DB', '#E5E7EB', '#F3F4F6', '#F9FAFB']
    };

    const colors = colorSchemes[scheme as keyof typeof colorSchemes] || colorSchemes.default;
    return colors[index % colors.length];
  }

  /**
   * Get layout configuration for color scheme
   */
  private static getLayoutForScheme(scheme: string): any {
    const layouts = {
      default: {
        paper_bgcolor: 'white',
        plot_bgcolor: 'white',
        font: { family: 'Inter, system-ui, sans-serif', size: 12 }
      },
      professional: {
        paper_bgcolor: '#F8FAFC',
        plot_bgcolor: '#F8FAFC',
        font: { family: 'Inter, system-ui, sans-serif', size: 12, color: '#1E293B' }
      },
      vibrant: {
        paper_bgcolor: 'white',
        plot_bgcolor: '#FEFEFE',
        font: { family: 'Inter, system-ui, sans-serif', size: 12, color: '#111827' }
      },
      minimal: {
        paper_bgcolor: '#FAFAFA',
        plot_bgcolor: '#FAFAFA',
        font: { family: 'Inter, system-ui, sans-serif', size: 11, color: '#374151' }
      }
    };

    return layouts[scheme as keyof typeof layouts] || layouts.default;
  }

  /**
   * Get heatmap color scale for scheme
   */
  private static getHeatmapColorScale(scheme: string): string {
    const scales = {
      default: 'RdBu',
      professional: 'Blues',
      vibrant: 'Viridis',
      minimal: 'Greys'
    };

    return scales[scheme as keyof typeof scales] || scales.default;
  }

  /**
   * Convert uploaded file data to CSV string format
   */
  private static convertToCSVString(file: UploadedFile): string {
    // Create CSV header
    const csvLines = [file.headers.join(',')];

    // Add data rows (limit to first 100 rows to avoid token limits)
    const maxRows = Math.min(file.data.length, 100);
    for (let i = 0; i < maxRows; i++) {
      const row = file.data[i];
      // Escape commas and quotes in data
      const escapedRow = row.map(cell => {
        if (cell === null || cell === undefined) return '';
        const cellStr = String(cell);
        if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
          return `"${cellStr.replace(/"/g, '""')}"`;
        }
        return cellStr;
      });
      csvLines.push(escapedRow.join(','));
    }

    return csvLines.join('\n');
  }

  /**
   * Create fallback analysis when visualization libraries fail
   */
  private static createFallbackAnalysis(file: UploadedFile, question: string): string {
    const numericColumns = file.headers.filter((_, index) => {
      const columnData = file.data.map(row => row[index]);
      return columnData.some(val => !isNaN(Number(val)) && val !== null && val !== '');
    });

    const categoricalColumns = file.headers.filter((_, index) => {
      const columnData = file.data.map(row => row[index]);
      return !columnData.some(val => !isNaN(Number(val)) && val !== null && val !== '');
    });

    return `
## Data Analysis Summary

**Dataset**: ${file.name}
- **Rows**: ${file.data.length}
- **Columns**: ${file.headers.length}

**Column Types**:
- **Numeric columns** (${numericColumns.length}): ${numericColumns.slice(0, 5).join(', ')}${numericColumns.length > 5 ? '...' : ''}
- **Categorical columns** (${categoricalColumns.length}): ${categoricalColumns.slice(0, 5).join(', ')}${categoricalColumns.length > 5 ? '...' : ''}

**Analysis for**: "${question}"

**Key Insights**:
1. The dataset contains ${file.data.length} records across ${file.headers.length} variables
2. ${numericColumns.length} numeric variables are available for statistical analysis
3. ${categoricalColumns.length} categorical variables can be used for grouping and segmentation

**Recommended Analysis Steps**:
1. **Descriptive Statistics**: Calculate mean, median, standard deviation for numeric columns
2. **Distribution Analysis**: Examine the distribution of key variables
3. **Correlation Analysis**: Identify relationships between numeric variables
4. **Categorical Analysis**: Analyze frequency distributions of categorical variables

**Note**: Visualization libraries (plotly, seaborn) are not available in the current execution environment. Analysis has been provided using available standard libraries.
    `;
  }
}
