/**
 * ReviewTool - Verifies that changes were applied correctly and match user intent
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { ToolContext, ToolResult, ReviewResult, ReviewToolResult, EditResult, AnalysisResult } from '../types';

export class ReviewTool extends BaseAgentTool {
  constructor() {
    super(
      'review-tool',
      'Change Reviewer',
      'Verifies that changes were applied correctly and match user intent'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { userRequest, previousResults } = context;
    
    console.log(`🔍 [ReviewTool] Reviewing changes for: "${userRequest}"`);

    try {
      // Extract edit and analysis results
      const editResults = this.extractEditResults(previousResults);
      const analysisResult = this.extractAnalysisResult(previousResults);

      if (!editResults || editResults.length === 0) {
        return this.createSuccessResult(
          startTime,
          {
            isValid: true,
            meetsRequirements: false,
            qualityScore: 0,
            issues: ['No edits were generated to review'],
            suggestions: ['Try a more specific request', 'Check if the content needs the requested changes'],
            approvedChanges: []
          } as ReviewResult,
          'No changes to review',
          0.5
        );
      }

      console.log(`📋 [ReviewTool] Reviewing ${editResults.length} edit(s)`);

      // Perform comprehensive review
      const reviewResult = await this.performComprehensiveReview(
        editResults,
        analysisResult,
        userRequest
      );

      const confidence = this.calculateReviewConfidence(reviewResult, editResults);
      const reasoning = this.generateReviewReasoning(reviewResult, editResults);

      return this.createSuccessResult(
        startTime,
        reviewResult,
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Review failed: ${error.message}`,
        ['Review process encountered an error', 'Changes may still be valid but require manual verification']
      );
    }
  }

  /**
   * Extract edit results from previous tool executions
   */
  private extractEditResults(previousResults?: ToolResult[]): EditResult[] {
    if (!previousResults) return [];
    
    const editResult = previousResults.find(r => r.toolId === 'edit-tool' && r.success);
    return editResult?.data?.edits || [];
  }

  /**
   * Extract analysis result from previous tool executions
   */
  private extractAnalysisResult(previousResults?: ToolResult[]): AnalysisResult | null {
    if (!previousResults) return null;
    
    const analysisResult = previousResults.find(r => r.toolId === 'analysis-tool' && r.success);
    return analysisResult?.data || null;
  }

  /**
   * Perform comprehensive review of all edits
   */
  private async performComprehensiveReview(
    edits: EditResult[],
    analysis: AnalysisResult | null,
    userRequest: string
  ): Promise<ReviewResult> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const approvedChanges: EditResult[] = [];
    
    let totalQualityScore = 0;
    let validEditsCount = 0;

    // Review each edit individually
    for (const edit of edits) {
      const editReview = this.reviewIndividualEdit(edit, analysis, userRequest);
      
      if (editReview.isValid) {
        approvedChanges.push(edit);
        validEditsCount++;
        totalQualityScore += editReview.qualityScore;
      } else {
        issues.push(...editReview.issues);
        suggestions.push(...editReview.suggestions);
      }
    }

    // Overall quality assessment
    const overallQualityScore = validEditsCount > 0 ? totalQualityScore / validEditsCount : 0;
    
    // Check if requirements are met
    const meetsRequirements = this.checkRequirementsFulfillment(
      approvedChanges,
      analysis,
      userRequest
    );

    // Add overall suggestions if needed
    if (approvedChanges.length < edits.length) {
      suggestions.push('Some edits were rejected due to quality concerns');
    }
    
    if (overallQualityScore < 0.7) {
      suggestions.push('Consider refining the request for better results');
    }

    return {
      isValid: approvedChanges.length > 0,
      meetsRequirements,
      qualityScore: overallQualityScore,
      issues: [...new Set(issues)], // Remove duplicates
      suggestions: [...new Set(suggestions)], // Remove duplicates
      approvedChanges
    };
  }

  /**
   * Review an individual edit
   */
  private reviewIndividualEdit(
    edit: EditResult,
    analysis: AnalysisResult | null,
    userRequest: string
  ): {
    isValid: boolean;
    qualityScore: number;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let qualityScore = 0.5; // Base score

    // Check for basic validity
    if (!edit.newText || edit.newText.trim().length === 0) {
      issues.push('Edit resulted in empty content');
      return { isValid: false, qualityScore: 0, issues, suggestions };
    }

    // Check for minimal changes
    if (edit.originalText.trim() === edit.newText.trim()) {
      issues.push('No actual changes were made');
      return { isValid: false, qualityScore: 0, issues, suggestions };
    }

    // Quality checks
    qualityScore += this.assessContentQuality(edit);
    qualityScore += this.assessLengthAppropriate(edit, analysis);
    qualityScore += this.assessStyleConsistency(edit);
    qualityScore += this.assessRequestAlignment(edit, userRequest);

    // Confidence threshold check
    if (edit.confidence < 0.5) {
      issues.push(`Low confidence edit (${(edit.confidence * 100).toFixed(0)}%)`);
      suggestions.push('Consider manual review of this change');
    }

    // Length sanity check
    const lengthRatio = edit.newText.length / edit.originalText.length;
    if (lengthRatio > 3 || lengthRatio < 0.3) {
      issues.push('Significant length change may indicate over-editing');
      suggestions.push('Verify that the meaning is preserved');
    }

    const isValid = issues.length === 0 && qualityScore > 0.4;
    
    return {
      isValid,
      qualityScore: Math.min(1, qualityScore),
      issues,
      suggestions
    };
  }

  /**
   * Assess content quality
   */
  private assessContentQuality(edit: EditResult): number {
    let score = 0;

    // Check for improved readability (simplified heuristic)
    const originalSentences = edit.originalText.split(/[.!?]+/).length;
    const newSentences = edit.newText.split(/[.!?]+/).length;
    
    // Reasonable sentence structure
    if (newSentences > 0 && newSentences <= originalSentences * 1.5) {
      score += 0.1;
    }

    // Check for grammar improvements (basic patterns)
    const grammarImprovements = this.detectGrammarImprovements(edit.originalText, edit.newText);
    score += grammarImprovements * 0.1;

    // Check for vocabulary improvements
    const vocabularyImprovements = this.detectVocabularyImprovements(edit.originalText, edit.newText);
    score += vocabularyImprovements * 0.1;

    return Math.min(0.3, score);
  }

  /**
   * Assess if length change is appropriate
   */
  private assessLengthAppropriate(edit: EditResult, analysis: AnalysisResult | null): number {
    const lengthRatio = edit.newText.length / edit.originalText.length;
    
    if (!analysis) return 0.1; // Default score if no analysis

    // Expected length changes based on edit type
    const expectedRatios = {
      'insert': { min: 1.1, max: 2.0 },
      'replace': { min: 0.8, max: 1.3 },
      'enhance': { min: 0.9, max: 1.4 },
      'rewrite': { min: 0.7, max: 1.8 }
    };

    const expected = expectedRatios[edit.editType];
    if (expected && lengthRatio >= expected.min && lengthRatio <= expected.max) {
      return 0.2;
    }

    return 0.1;
  }

  /**
   * Assess style consistency
   */
  private assessStyleConsistency(edit: EditResult): number {
    // Simple style consistency checks
    let score = 0.1; // Base score

    // Check for consistent capitalization
    const originalCaps = (edit.originalText.match(/[A-Z]/g) || []).length;
    const newCaps = (edit.newText.match(/[A-Z]/g) || []).length;
    const capsRatio = newCaps / Math.max(originalCaps, 1);
    
    if (capsRatio >= 0.5 && capsRatio <= 2.0) {
      score += 0.1;
    }

    // Check for consistent punctuation style
    const originalPunct = (edit.originalText.match(/[.!?;:,]/g) || []).length;
    const newPunct = (edit.newText.match(/[.!?;:,]/g) || []).length;
    const punctRatio = newPunct / Math.max(originalPunct, 1);
    
    if (punctRatio >= 0.7 && punctRatio <= 1.5) {
      score += 0.1;
    }

    return Math.min(0.2, score);
  }

  /**
   * Assess alignment with user request
   */
  private assessRequestAlignment(edit: EditResult, userRequest: string): number {
    const request = userRequest.toLowerCase();
    const originalText = edit.originalText.toLowerCase();
    const newText = edit.newText.toLowerCase();

    let score = 0.1; // Base score

    // Check for specific request fulfillment
    if (request.includes('add') && newText.length > originalText.length) score += 0.1;
    if (request.includes('remove') && newText.length < originalText.length) score += 0.1;
    if (request.includes('improve') || request.includes('enhance')) score += 0.1;
    if (request.includes('fix') || request.includes('correct')) score += 0.1;

    return Math.min(0.2, score);
  }

  /**
   * Detect grammar improvements (simplified)
   */
  private detectGrammarImprovements(original: string, improved: string): number {
    let improvements = 0;

    // Check for common grammar fixes
    const grammarPatterns = [
      { wrong: /\bi\s/g, right: /\bI\s/g }, // Capitalization of "I"
      { wrong: /\s+/g, right: /\s/g }, // Multiple spaces
      { wrong: /\s+\./g, right: /\./g }, // Space before period
    ];

    grammarPatterns.forEach(pattern => {
      const originalMatches = (original.match(pattern.wrong) || []).length;
      const improvedMatches = (improved.match(pattern.wrong) || []).length;
      
      if (improvedMatches < originalMatches) {
        improvements++;
      }
    });

    return Math.min(improvements, 3);
  }

  /**
   * Detect vocabulary improvements (simplified)
   */
  private detectVocabularyImprovements(original: string, improved: string): number {
    // Simple check for more sophisticated vocabulary
    const sophisticatedWords = [
      'therefore', 'furthermore', 'however', 'moreover', 'consequently',
      'nevertheless', 'subsequently', 'accordingly', 'specifically', 'particularly'
    ];

    const originalSophisticated = sophisticatedWords.filter(word => 
      original.toLowerCase().includes(word)
    ).length;
    
    const improvedSophisticated = sophisticatedWords.filter(word => 
      improved.toLowerCase().includes(word)
    ).length;

    return improvedSophisticated > originalSophisticated ? 1 : 0;
  }

  /**
   * Check if requirements are fulfilled
   */
  private checkRequirementsFulfillment(
    approvedChanges: EditResult[],
    analysis: AnalysisResult | null,
    userRequest: string
  ): boolean {
    if (!analysis || approvedChanges.length === 0) return false;

    // Basic fulfillment check based on approved changes count
    const targetSectionsCount = analysis.targetSections.length;
    const approvedChangesCount = approvedChanges.length;
    
    // At least 50% of target sections should have approved changes
    return approvedChangesCount >= Math.max(1, Math.floor(targetSectionsCount * 0.5));
  }

  /**
   * Calculate review confidence
   */
  private calculateReviewConfidence(review: ReviewResult, originalEdits: EditResult[]): number {
    const approvalRate = review.approvedChanges.length / Math.max(originalEdits.length, 1);
    const qualityScore = review.qualityScore;
    const issuesPenalty = Math.max(0, 1 - (review.issues.length * 0.1));

    return this.calculateConfidence({
      resultQuality: qualityScore,
      contextRelevance: approvalRate,
      textMatch: issuesPenalty
    });
  }

  /**
   * Generate review reasoning
   */
  private generateReviewReasoning(review: ReviewResult, originalEdits: EditResult[]): string {
    const approvedCount = review.approvedChanges.length;
    const totalCount = originalEdits.length;
    const qualityPercent = (review.qualityScore * 100).toFixed(0);

    let reasoning = `Reviewed ${totalCount} edit(s), approved ${approvedCount} with ${qualityPercent}% average quality.`;

    if (review.issues.length > 0) {
      reasoning += ` Found ${review.issues.length} issue(s) requiring attention.`;
    }

    if (review.meetsRequirements) {
      reasoning += ' Changes meet the specified requirements.';
    } else {
      reasoning += ' Changes may not fully meet all requirements.';
    }

    return reasoning;
  }
}
