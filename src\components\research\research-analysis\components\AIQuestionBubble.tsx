import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, <PERSON>etHeader, SheetT<PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { MessageSquare, X, Send, Bot, User, Loader2, Maximize2, Minimize2, AtSign, FileText, ChevronUp, ChevronDown, ArrowLeftRight, Info, Clock } from "lucide-react";
import { ResearchDocument } from '../types';
import { researchAnalysisService } from '../services/research-analysis.service';
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Resizable } from "re-resizable";
import { cn } from "@/lib/utils";

interface AIQuestionBubbleProps {
  documents: ResearchDocument[];
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  referencedDocuments?: string[]; // IDs of documents referenced in the message
}

interface MentionSuggestion {
  id: string;
  title: string;
  authors: string[];
  year: number;
}

export function AIQuestionBubble({ documents }: AIQuestionBubbleProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isExpandedView, setIsExpandedView] = useState(false);
  
  // Log documents when they change
  useEffect(() => {
    console.log('AIQuestionBubble received documents:', documents.length);
    console.log('Document IDs:', documents.map(d => d.id).join(', '));
  }, [documents]);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'initial',
      type: 'ai',
      content: 'Ask me anything about your selected research papers! You can mention specific papers using @ symbol.',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showMentionSuggestions, setShowMentionSuggestions] = useState(false);
  const [mentionSearch, setMentionSearch] = useState('');
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [showDocumentSelector, setShowDocumentSelector] = useState(false);
  const [chatWidth, setChatWidth] = useState(380);
  const [chatHeight, setChatHeight] = useState(500);
  
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const mentionTriggerPos = useRef<number | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Helper functions for @ mention functionality
  const getFilteredDocumentSuggestions = (): MentionSuggestion[] => {
    console.log('Getting document suggestions. Total documents available:', documents.length);
    
    if (!mentionSearch.trim()) {
      // Return all available documents (no filtering)
      const suggestions = documents.map(doc => ({
        id: doc.id,
        title: doc.title,
        authors: doc.authors,
        year: doc.publicationYear
      }));
      console.log('All suggestions (no search):', suggestions.length);
      return suggestions;
    }

    const searchLower = mentionSearch.toLowerCase();
    const filteredSuggestions = documents
      .filter(doc => 
        doc.title.toLowerCase().includes(searchLower) || 
        doc.authors.some(author => author.toLowerCase().includes(searchLower))
      )
      .map(doc => ({
        id: doc.id,
        title: doc.title,
        authors: doc.authors,
        year: doc.publicationYear
      }));
    
    console.log('Filtered suggestions:', filteredSuggestions.length);
    return filteredSuggestions;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInput(value);
    
    // Debounce mention suggestion updates to prevent input lag
    // Check for @ symbol typing to trigger mention suggestions
    const lastAtSymbolIndex = value.lastIndexOf('@');
    if (lastAtSymbolIndex !== -1 && (lastAtSymbolIndex === 0 || value[lastAtSymbolIndex-1] === ' ')) {
      mentionTriggerPos.current = lastAtSymbolIndex;
      const searchAfterAt = value.slice(lastAtSymbolIndex + 1);
      setMentionSearch(searchAfterAt);
      // Only show suggestions if we're at the end of the input (cursor is after the @)
      if (e.target.selectionStart && e.target.selectionStart > lastAtSymbolIndex) {
        setShowMentionSuggestions(true);
      }
    } else {
      mentionTriggerPos.current = null;
      setShowMentionSuggestions(false);
    }
  };

  const handleMentionSelect = (suggestion: MentionSuggestion) => {
    if (mentionTriggerPos.current !== null) {
      const beforeMention = input.slice(0, mentionTriggerPos.current);
      
      let mentionText;
      if (suggestion.id === 'all') {
        mentionText = `@all (${documents.length} documents)`;
        setSelectedDocumentId(null); // All documents will be referenced
      } else {
        mentionText = `@${suggestion.title.substring(0, 20)}${suggestion.title.length > 20 ? '...' : ''} (${suggestion.year})`;
        setSelectedDocumentId(suggestion.id);
      }
      
      const afterMentionPos = mentionTriggerPos.current + mentionSearch.length + 1; // +1 for @ symbol
      const afterMention = input.slice(afterMentionPos);
      
      setInput(`${beforeMention}${mentionText} ${afterMention}`);
      setShowMentionSuggestions(false);
      
      // Log document selection
      console.log('Selected document for mention:', suggestion.id === 'all' ? 'All documents' : suggestion.id);
      
      // Focus back on input after selection
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 10);
    }
  };
  
  // Automatically scroll to the bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const extractReferencedDocuments = (text: string): string[] => {
    // If text contains '@all', reference all documents
    if (text.toLowerCase().includes('@all')) {
      console.log('Referencing ALL documents:', documents.length);
      return documents.map(doc => doc.id);
    }
    
    const referencedIds: string[] = [];
    
    // Check for direct mentions in the text using regex
    const mentionRegex = /@([^@]+?)(?=\s\(\d{4}\))/g;
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      const mentionedTitle = match[1].trim();
      // Find document that matches the mentioned title (partial match)
      const matchedDoc = documents.find(doc => 
        doc.title.toLowerCase().includes(mentionedTitle.toLowerCase())
      );
      
      if (matchedDoc) {
        referencedIds.push(matchedDoc.id);
        console.log('Found referenced document:', matchedDoc.title);
      }
    }
    
    // If a specific document was selected during mention
    if (selectedDocumentId && !referencedIds.includes(selectedDocumentId)) {
      referencedIds.push(selectedDocumentId);
      console.log('Added selectedDocumentId:', selectedDocumentId);
    }
    
    // If no specific documents were referenced, use all documents
    if (referencedIds.length === 0) {
      console.log('No specific documents mentioned, using all documents:', documents.length);
      return documents.map(doc => doc.id);
    }
    
    console.log('Total referenced documents:', referencedIds.length);
    return referencedIds;
  };

  const toggleExpandedView = () => {
    setIsExpandedView(!isExpandedView);
    if (!isExpandedView) {
      setChatWidth(500);
      setChatHeight(600);
    } else {
      setChatWidth(380);
      setChatHeight(500);
    }
  };

  const handleSendQuestion = async () => {
    if (!input.trim() || isProcessing) return;
    
    setIsProcessing(true);
    
    // Extract document references
    const referencedDocIds = extractReferencedDocuments(input);
    const referencedDocs = documents.filter(doc => referencedDocIds.includes(doc.id));
    
    // Add user message
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: input,
      timestamp: new Date(),
      referencedDocuments: referencedDocIds
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput(''); // Clear input field
    setSelectedDocumentId(null); // Reset selected document
    
    try {
      // Add typing indicator message
      const typingMessage: Message = {
        id: `typing_${Date.now()}`,
        type: 'ai',
        content: '...',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, typingMessage]);
      
      // Get the documents to analyze based on references
      const docsToAnalyze = referencedDocs.length > 0 ? referencedDocs : documents;
      
      // Call the AI service with the selected documents
      const response = await researchAnalysisService.answerQuestion(
        input, 
        docsToAnalyze,
        (progress) => {
          // Update the typing message with progress if needed
        }
      );
      
      // Remove typing indicator and add the actual response
      setMessages(prev => {
        const filtered = prev.filter(m => m.id !== typingMessage.id);
        return [
          ...filtered,
          {
            id: `ai_${Date.now()}`,
            type: 'ai',
            content: response,
            timestamp: new Date(),
            referencedDocuments: referencedDocIds
          }
        ];
      });
    } catch (error) {
      // Handle error
      setMessages(prev => {
        // Remove typing indicator if exists
        const filtered = prev.filter(m => m.id !== `typing_${Date.now()}`);
        return [
          ...filtered,
          {
            id: `error_${Date.now()}`,
            type: 'ai',
            content: `Sorry, I couldn't process your question: ${error instanceof Error ? error.message : 'Unknown error'}`,
            timestamp: new Date()
          }
        ];
      });
    } finally {
      setIsProcessing(false);
      setShowMentionSuggestions(false);
    }
  };

  // Render the paper mention suggestions
  const renderMentionSuggestions = () => {
    if (!showMentionSuggestions) return null;
    
    const suggestions = getFilteredDocumentSuggestions();
    const allDocumentsCount = documents.length;
    
    return (
      <div className="absolute bottom-16 left-0 right-0 mx-3 bg-white/95 backdrop-blur-sm rounded-xl border border-gray-200 shadow-xl z-50">
        <div className="p-2 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1.5 text-sm text-blue-700 font-medium">
              <AtSign className="h-4 w-4" />
              <span>Reference papers ({allDocumentsCount} available)</span>
            </div>
            <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
              {suggestions.length} shown
            </Badge>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Type '@all' to reference all papers or select specific ones below
          </p>
        </div>
        <div className="max-h-[220px] overflow-y-auto">
          {suggestions.length > 0 ? (
            <div className="p-1.5">
              <div 
                onClick={() => handleMentionSelect({
                  id: 'all',
                  title: 'All Documents',
                  authors: [''],
                  year: new Date().getFullYear()
                })}
                className="px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded-lg cursor-pointer text-sm transition-all my-2 flex items-center gap-2"
              >
                <FileText className="h-4 w-4 text-blue-700" />
                <div className="font-medium text-blue-700">Reference all {allDocumentsCount} documents</div>
              </div>
              
              {suggestions.map(suggestion => (
                <div
                  key={suggestion.id}
                  onClick={() => handleMentionSelect(suggestion)}
                  className="px-3 py-2.5 hover:bg-blue-50 hover:shadow-sm rounded-lg cursor-pointer text-sm transition-all border border-transparent hover:border-blue-100 my-1"
                >
                  <div className="font-medium text-blue-700 truncate flex items-center gap-2">
                    <FileText className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{suggestion.title}</span>
                  </div>
                  <div className="text-xs text-gray-500 flex justify-between items-center mt-1">
                    <span className="truncate max-w-[80%]">
                      {suggestion.authors.slice(0, 2).join(", ")}{suggestion.authors.length > 2 ? ", et al." : ""}
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {suggestion.year}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-sm text-gray-500 text-center">
              <div className="flex justify-center mb-2">
                <AtSign className="h-8 w-8 text-gray-300" />
              </div>
              No matching papers found
              <p className="text-xs mt-1">Try a different search term or click '@' to see all options</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderDocumentTags = (documentIds?: string[]) => {
    if (!documentIds || documentIds.length === 0) return null;
    
    return (
      <div className="flex flex-wrap gap-1.5 mt-3 mb-1">
        {documentIds.map(id => {
          const doc = documents.find(d => d.id === id);
          if (!doc) return null;
          
          return (
            <TooltipProvider key={id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="outline" 
                    className="text-xs bg-opacity-10 hover:bg-opacity-20 transition-all 
                             px-2 py-1 border border-blue-200 flex items-center gap-1.5"
                  >
                    <FileText className="h-3 w-3 text-blue-600" /> 
                    <span className="truncate max-w-[150px]">
                      {doc.title.substring(0, 30)}{doc.title.length > 30 ? '...' : ''}
                    </span>
                    <span className="text-gray-500">{doc.publicationYear}</span>
                  </Badge>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="font-medium">{doc.title}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {doc.authors.slice(0, 3).join(", ")}
                    {doc.authors.length > 3 ? ", et al." : ""}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
        {documentIds.length > 2 && (
          <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 px-2 py-1">
            {documentIds.length} papers referenced
          </Badge>
        )}
      </div>
    );
  };

  // Component definitions removed since they're now integrated directly in the return statement
  
  // Component for full-screen chat
  const FullScreenChat = () => (
    <Dialog open={isFullScreen} onOpenChange={setIsFullScreen}>
      <DialogContent className="max-w-5xl w-full h-[90vh] p-0 gap-0 bg-gradient-to-br from-blue-50 to-indigo-50">
        <DialogHeader className="absolute top-2 right-2 z-50">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setIsFullScreen(false)}
            className="rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>
        <div className="h-full flex flex-col">
          <div className="flex-grow p-4 flex flex-col">
            <ChatContent 
              isCompact={false}
              onExpand={() => setIsFullScreen(false)}
              onClose={() => setIsFullScreen(false)}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
  
  // Shared chat content component
  const ChatContent = ({ isCompact, onExpand, onClose }: { 
    isCompact: boolean;
    onExpand: () => void;
    onClose: () => void;
  }) => (
    <>
      {/* Chat Header */}
      <div className="p-3 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-xl flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          <div>
            <span className="font-medium">Research AI Assistant</span>
            {documents.length > 0 && (
              <div className="text-xs text-white/80">
                Analyzing {documents.length} selected document{documents.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Button 
            variant="ghost" 
            size="sm" 
            className="hover:bg-white/20 text-white" 
            onClick={toggleExpandedView}
          >
            <ArrowLeftRight className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="hover:bg-white/20 text-white" 
            onClick={onExpand}
          >
            {isCompact ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="hover:bg-white/20 text-white" 
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Document Selection Notice */}
      {documents.length === 0 && (
        <div className="p-4 bg-amber-50 border-b border-amber-200">
          <p className="text-sm text-amber-800">
            Please select documents from the library to enable AI questions.
          </p>
        </div>
      )}

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" style={{ maxHeight: isCompact ? 'calc(100% - 140px)' : 'calc(90vh - 140px)' }}>
        <div className="space-y-4" ref={scrollAreaRef}>
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
            >
              <div
                className={cn(
                  "max-w-[85%] rounded-2xl p-4 shadow-sm", 
                  message.type === "user"
                    ? "bg-gradient-to-br from-blue-600 to-blue-700 text-white"
                    : "bg-white border border-gray-100 text-gray-900"
                )}
              >
                <div className="flex items-start gap-2">
                  {message.type === "ai" ? (
                    <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                      <Bot className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                  ) : (
                    <div className="h-6 w-6 rounded-full bg-blue-200 flex items-center justify-center flex-shrink-0">
                      <User className="h-3.5 w-3.5 text-blue-600" />
                    </div>
                  )}
                  <div className="flex-1">
                    <p className={cn(
                      "text-sm whitespace-pre-wrap",
                      message.type === "user" ? "" : "leading-relaxed"
                    )}>
                      {message.content}
                    </p>
                    {renderDocumentTags(message.referencedDocuments)}
                    <p className={cn(
                      "text-xs mt-1.5 flex items-center gap-1",
                      message.type === "user" ? "text-blue-100" : "text-gray-400"
                    )}>
                      <Clock className="h-3 w-3" />
                      {message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
          {isProcessing && (
            <div className="flex justify-center my-4 bg-blue-50 rounded-full p-2 w-14 mx-auto">
              <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50 rounded-b-xl relative">
        <div className="flex flex-col gap-2">
          <div className="flex gap-2 items-start">
            <div className="relative flex-1">
              <Textarea
                ref={inputRef}
                value={input}
                onChange={handleInputChange}
                placeholder={documents.length > 0 
                  ? "Ask about your papers... Use @ to mention specific papers" 
                  : "Select documents first to enable AI questions..."}
                disabled={documents.length === 0 || isProcessing}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSendQuestion();
                  }
                }}
                spellCheck="false"
                autoComplete="off"
                style={{ caretColor: 'auto' }}
                className={cn(
                  "flex-1 bg-white min-h-[65px] pr-10 rounded-xl shadow-sm focus-visible:ring-blue-400 resize-none",
                  documents.length === 0 ? "bg-gray-50 text-gray-400" : "bg-white"
                )}
              />
              <div className="absolute right-3 top-3 flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        size="icon" 
                        variant="outline"
                        className={cn(
                          "h-7 w-7 rounded-full bg-white border-gray-200", 
                          showMentionSuggestions ? "bg-blue-100 text-blue-700 border-blue-300" : ""
                        )}
                        onClick={() => {
                          setShowMentionSuggestions(!showMentionSuggestions);
                          setMentionSearch("");
                          setTimeout(() => inputRef.current?.focus(), 50);
                        }}
                        disabled={documents.length === 0}
                      >
                        <AtSign className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p className="text-xs">Reference specific papers using @</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <Button 
              size="icon" 
              disabled={documents.length === 0 || !input.trim() || isProcessing}
              onClick={handleSendQuestion}
              className={cn(
                "bg-blue-600 hover:bg-blue-700 h-[65px] w-12 rounded-xl shadow-md",
                documents.length === 0 ? "bg-gray-300 cursor-not-allowed" : "",
                isProcessing ? "bg-blue-400 cursor-not-allowed" : ""
              )}
            >
              {isProcessing ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </div>
          
          <div className="text-xs text-gray-500 flex items-center justify-between px-1">
            <div className="flex items-center gap-1.5">
              <Info className="h-3.5 w-3.5" /> 
              <span>Press Enter to send, Shift+Enter for new line</span>
            </div>
            {documents.length > 0 && (
              <Badge variant="outline" className="bg-white text-blue-700 gap-1.5 flex items-center">
                <FileText className="h-3 w-3" />
                {documents.length} papers available
              </Badge>
            )}
          </div>
        </div>
        
        {renderMentionSuggestions()}
      </div>
    </>
  );

  // Try a more React-compliant approach to fix the popover issue
  return (
    <div className="fixed bottom-8 right-8 z-50">
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            size="lg"
            className="rounded-full w-14 h-14 shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            <MessageSquare className="h-6 w-6 text-white" />
            {documents.length > 0 && (
              <span className="absolute top-0 right-0 h-5 w-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center">
                {documents.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          side="top" 
          align="end" 
          className="p-0 bg-white border border-gray-200 shadow-xl rounded-xl w-auto"
          sideOffset={20}
        >
          <Resizable
            size={{ width: chatWidth, height: chatHeight }}
            minWidth={320}
            minHeight={400}
            maxWidth={800}
            maxHeight={800}
            onResizeStop={(e, direction, ref, d) => {
              setChatWidth(chatWidth + d.width);
              setChatHeight(chatHeight + d.height);
            }}
            className="flex flex-col rounded-xl overflow-hidden"
          >
            <ChatContent 
              isCompact={true}
              onExpand={() => {
                setIsPopoverOpen(false);
                setIsFullScreen(true);
              }}
              onClose={() => setIsPopoverOpen(false)}
            />
          </Resizable>
        </PopoverContent>
      </Popover>
      <FullScreenChat />
    </div>
  );
}
