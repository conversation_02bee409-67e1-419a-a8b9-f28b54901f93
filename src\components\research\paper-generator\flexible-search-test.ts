/**
 * Flexible Search Test
 * Tests the improved Tavily search strategy for topic-specific results
 */

import { tavilySearchService } from '../research-search/services/tavily-search.service';
import { enhancedCitationSearchService } from './services/enhanced-citation-search.service';

export class FlexibleSearchTester {
  private testQueries = [
    {
      title: "PSInSAR Analysis for Urban Subsidence Monitoring in Metropolitan Areas",
      field: "Remote Sensing",
      keywords: ["PSInSAR", "subsidence", "urban monitoring", "satellite interferometry"],
      userContent: "This study employs Persistent Scatterer Interferometric Synthetic Aperture Radar (PSInSAR) technique using Sentinel-1 data to monitor urban subsidence in metropolitan areas.",
      expectedTerms: ["PSInSAR", "Sentinel-1", "interferometry", "subsidence", "urban", "monitoring"]
    },
    {
      title: "Machine Learning Applications in Climate Change Prediction",
      field: "Computer Science",
      keywords: ["machine learning", "climate change", "prediction", "neural networks"],
      userContent: "We implemented deep learning algorithms including LSTM and CNN models to predict climate patterns using satellite data and meteorological observations.",
      expectedTerms: ["machine learning", "climate change", "LSTM", "CNN", "satellite data"]
    },
    {
      title: "Blockchain Technology in Healthcare Data Management",
      field: "Healthcare Technology",
      keywords: ["blockchain", "healthcare", "data management", "security"],
      userContent: "The study implements a permissioned blockchain network using Hyperledger Fabric for secure healthcare data sharing between hospitals and research institutions.",
      expectedTerms: ["blockchain", "healthcare", "Hyperledger Fabric", "data sharing", "security"]
    }
  ];

  /**
   * Test the flexible search improvements
   */
  async testFlexibleSearch(): Promise<void> {
    console.log('🧪 Testing Flexible Search Strategy...\n');
    console.log('═'.repeat(80));

    for (let i = 0; i < this.testQueries.length; i++) {
      const testCase = this.testQueries[i];
      console.log(`\n📋 Test Case ${i + 1}: ${testCase.title.substring(0, 50)}...`);
      console.log('─'.repeat(60));

      await this.testSingleQuery(testCase);
      console.log('═'.repeat(80));
    }

    console.log('✅ Flexible Search Testing Completed!');
  }

  /**
   * Test a single query case
   */
  private async testSingleQuery(testCase: any): Promise<void> {
    const { title, field, keywords, userContent, expectedTerms } = testCase;

    console.log(`Title: ${title}`);
    console.log(`Field: ${field}`);
    console.log(`Keywords: [${keywords.join(', ')}]`);
    console.log(`Expected Terms: [${expectedTerms.join(', ')}]`);

    // Test 1: Query Generation
    console.log('\n🔍 Testing Query Generation...');
    const context = {
      sectionId: 'introduction',
      sectionName: 'Introduction',
      title,
      researchField: field,
      keywords,
      userContent,
      relatedSections: []
    };

    // Simulate the enhanced query generation
    const simulatedQueries = this.generateTestQueries(context);
    console.log('Generated Queries:');
    simulatedQueries.forEach((query, index) => {
      console.log(`  ${index + 1}. "${query}"`);
    });

    // Test 2: Query Quality Assessment
    console.log('\n📊 Query Quality Assessment:');
    const qualityScores = simulatedQueries.map(query => ({
      query,
      score: this.assessQueryQuality(query, expectedTerms, title)
    }));

    qualityScores.forEach(({ query, score }) => {
      const quality = score >= 0.7 ? '✅ High' : score >= 0.4 ? '⚠️ Medium' : '❌ Low';
      console.log(`  ${quality} (${score.toFixed(2)}): ${query.substring(0, 60)}...`);
    });

    // Test 3: Tavily Search Simulation
    console.log('\n🌐 Tavily Search Strategy:');
    try {
      // Test the flexible search approach
      console.log('Strategy 1: Exact query with minimal enhancement');
      const exactQuery = simulatedQueries[0];
      console.log(`  Query: "${exactQuery}"`);
      console.log(`  Expected: Specific results about ${expectedTerms.slice(0, 2).join(' and ')}`);

      console.log('\nStrategy 2: Alternative query for broader coverage');
      const alternativeQuery = this.createAlternativeQuery(exactQuery, expectedTerms);
      console.log(`  Query: "${alternativeQuery}"`);
      console.log(`  Expected: Related research with synonyms and variations`);

      console.log('\nStrategy 3: Domain-specific enhancement');
      const domainQuery = this.createDomainSpecificQuery(title, field, expectedTerms);
      console.log(`  Query: "${domainQuery}"`);
      console.log(`  Expected: Field-specific academic sources`);

    } catch (error) {
      console.error('❌ Search strategy simulation failed:', error);
    }

    // Test 4: Expected Improvements
    console.log('\n📈 Expected Improvements:');
    console.log('✅ Queries focus on specific technical terms instead of generic "survey" or "state-of-the-art"');
    console.log('✅ Multiple search strategies increase chance of finding relevant papers');
    console.log('✅ Domain-specific enhancements target appropriate academic sources');
    console.log('✅ Flexible approach adapts to different research fields and topics');

    const avgQuality = qualityScores.reduce((sum, item) => sum + item.score, 0) / qualityScores.length;
    console.log(`\n🏆 Overall Query Quality Score: ${avgQuality.toFixed(2)}/1.0`);
  }

  /**
   * Generate test queries (simulating the enhanced system)
   */
  private generateTestQueries(context: any): string[] {
    const { title, researchField, keywords, userContent } = context;
    const queries: string[] = [];

    // Extract technical terms from user content
    const technicalTerms = userContent.match(/\b[A-Z]{2,10}\b/g) || [];
    const specificTerms = this.extractSpecificTerms(userContent, researchField);

    // Primary query with exact title
    const cleanTitle = title.substring(0, 50).replace(/[^\w\s]/g, ' ').trim();
    queries.push(`"${cleanTitle}" ${researchField}`);

    // Technical term queries
    if (technicalTerms.length > 0) {
      queries.push(`"${technicalTerms[0]}" ${researchField}`);
      if (technicalTerms.length > 1) {
        queries.push(`${technicalTerms.slice(0, 2).join(' ')} ${researchField}`);
      }
    }

    // Specific concept queries
    if (specificTerms.length > 0) {
      queries.push(`"${specificTerms[0]}" ${researchField}`);
      if (specificTerms.length > 1) {
        queries.push(`${specificTerms.slice(0, 2).join(' ')} ${researchField}`);
      }
    }

    // Keyword-based query (fallback)
    if (keywords.length >= 2) {
      queries.push(`${keywords.slice(0, 2).join(' ')} ${researchField}`);
    }

    return queries.slice(0, 4); // Return top 4 queries
  }

  /**
   * Extract specific terms from content
   */
  private extractSpecificTerms(content: string, field: string): string[] {
    const terms: string[] = [];
    const lowerContent = content.toLowerCase();
    const lowerField = field.toLowerCase();

    // Field-specific term extraction
    if (lowerField.includes('remote sensing') || lowerContent.includes('psinsar')) {
      const remoteSensingTerms = ['interferometry', 'deformation', 'subsidence', 'monitoring', 'satellite'];
      terms.push(...remoteSensingTerms.filter(term => lowerContent.includes(term)));
    }

    if (lowerField.includes('computer science') || lowerContent.includes('machine learning')) {
      const csTerms = ['algorithm', 'neural network', 'deep learning', 'prediction', 'model'];
      terms.push(...csTerms.filter(term => lowerContent.includes(term)));
    }

    if (lowerField.includes('healthcare') || lowerContent.includes('blockchain')) {
      const healthcareTerms = ['blockchain', 'security', 'data management', 'privacy', 'network'];
      terms.push(...healthcareTerms.filter(term => lowerContent.includes(term)));
    }

    return [...new Set(terms)].slice(0, 3);
  }

  /**
   * Assess query quality based on specificity and relevance
   */
  private assessQueryQuality(query: string, expectedTerms: string[], title: string): number {
    let score = 0;
    const lowerQuery = query.toLowerCase();
    const lowerTitle = title.toLowerCase();

    // Check for expected terms
    const termMatches = expectedTerms.filter(term => 
      lowerQuery.includes(term.toLowerCase())
    ).length;
    score += (termMatches / expectedTerms.length) * 0.5;

    // Check for title relevance
    const titleWords = lowerTitle.split(/\s+/).filter(word => word.length > 3);
    const titleMatches = titleWords.filter(word => lowerQuery.includes(word)).length;
    score += (titleMatches / titleWords.length) * 0.3;

    // Bonus for quoted terms (more specific)
    const quotedTerms = (query.match(/"/g) || []).length / 2;
    score += Math.min(quotedTerms * 0.1, 0.2);

    // Penalty for generic terms
    const genericTerms = ['survey', 'state-of-the-art', 'literature review', 'academic', 'research'];
    const genericCount = genericTerms.filter(term => lowerQuery.includes(term)).length;
    score -= genericCount * 0.1;

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Create alternative query for broader search
   */
  private createAlternativeQuery(originalQuery: string, expectedTerms: string[]): string {
    const mainTerms = expectedTerms.slice(0, 2);
    return `${mainTerms.join(' ')} methodology technique`;
  }

  /**
   * Create domain-specific query
   */
  private createDomainSpecificQuery(title: string, field: string, expectedTerms: string[]): string {
    const keyTerm = expectedTerms[0];
    return `"${keyTerm}" ${field} journal article`;
  }
}

// Export test runner
export async function runFlexibleSearchTest(): Promise<void> {
  const tester = new FlexibleSearchTester();
  await tester.testFlexibleSearch();
}

// Add to window for manual testing
if (typeof window !== 'undefined') {
  (window as any).testFlexibleSearch = runFlexibleSearchTest;
}
