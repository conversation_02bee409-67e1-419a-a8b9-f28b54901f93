/**
 * Database Setup Service
 * Handles database initialization and schema setup
 * for the Research Comprehension Platform
 */

import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface SetupResult {
  success: boolean;
  message: string;
  details?: string[];
}

class DatabaseSetupService {
  /**
   * Check if database is properly configured
   */
  async checkDatabaseSetup(): Promise<{
    isConfigured: boolean;
    missingTables: string[];
    missingPolicies: string[];
  }> {
    try {
      const requiredTables = [
        'chat_sessions',
        'chat_messages',
        'research_documents',
        'document_sections',
        'document_chunks',
        'quizzes',
        'quiz_results',
        'educational_games',
        'game_results',
        'learning_progress',
        'study_sessions'
      ];

      const missingTables: string[] = [];
      const missingPolicies: string[] = [];

      // Check if tables exist
      for (const table of requiredTables) {
        try {
          const { error } = await supabase
            .from(table)
            .select('*')
            .limit(1);

          if (error && error.code === '42P01') {
            missingTables.push(table);
          }
        } catch (error) {
          missingTables.push(table);
        }
      }

      // Check storage bucket
      try {
        const { data: buckets, error } = await supabase.storage.listBuckets();
        if (error || !buckets?.find(b => b.name === 'research-documents')) {
          missingTables.push('storage bucket: research-documents');
        }
      } catch (error) {
        missingTables.push('storage bucket: research-documents');
      }

      return {
        isConfigured: missingTables.length === 0,
        missingTables,
        missingPolicies
      };

    } catch (error) {
      console.error('Error checking database setup:', error);
      return {
        isConfigured: false,
        missingTables: ['Unable to check'],
        missingPolicies: []
      };
    }
  }

  /**
   * Get setup instructions for manual database configuration
   */
  getSetupInstructions(): {
    title: string;
    steps: string[];
    sqlFile: string;
  } {
    return {
      title: 'Research Comprehension Platform Database Setup',
      steps: [
        '1. Open your Supabase project dashboard',
        '2. Navigate to the SQL Editor',
        '3. Copy and paste the SQL schema from the file below',
        '4. Execute the SQL to create all required tables and policies',
        '5. Verify that the pgvector extension is enabled for vector embeddings',
        '6. Check that Row Level Security (RLS) policies are properly applied',
        '7. Ensure the storage bucket "research-documents" is created',
        '8. Test the setup by uploading a document through the platform'
      ],
      sqlFile: 'src/components/research/ai-tutor/database/schema.sql'
    };
  }

  /**
   * Attempt to create missing tables (limited by Supabase permissions)
   */
  async attemptAutoSetup(): Promise<SetupResult> {
    try {
      const setupCheck = await this.checkDatabaseSetup();
      
      if (setupCheck.isConfigured) {
        return {
          success: true,
          message: 'Database is already properly configured!'
        };
      }

      // Try to create storage bucket if missing
      const bucketResult = await this.createStorageBucket();
      
      return {
        success: false,
        message: 'Automatic setup is limited. Please run the SQL schema manually.',
        details: [
          `Missing tables: ${setupCheck.missingTables.join(', ')}`,
          'Storage bucket creation: ' + (bucketResult.success ? 'Success' : bucketResult.message),
          'Please execute the SQL schema in your Supabase dashboard.'
        ]
      };

    } catch (error) {
      console.error('Auto setup failed:', error);
      return {
        success: false,
        message: 'Auto setup failed. Please run the SQL schema manually.',
        details: [error.message]
      };
    }
  }

  /**
   * Create storage bucket for documents
   */
  private async createStorageBucket(): Promise<SetupResult> {
    try {
      // Check if bucket already exists
      const { data: buckets } = await supabase.storage.listBuckets();
      const bucketExists = buckets?.find(b => b.name === 'research-documents');

      if (bucketExists) {
        return {
          success: true,
          message: 'Storage bucket already exists'
        };
      }

      // Try to create bucket
      const { error } = await supabase.storage.createBucket('research-documents', {
        public: false,
        allowedMimeTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain'
        ],
        fileSizeLimit: 52428800 // 50MB
      });

      if (error) {
        return {
          success: false,
          message: `Failed to create storage bucket: ${error.message}`
        };
      }

      return {
        success: true,
        message: 'Storage bucket created successfully'
      };

    } catch (error) {
      return {
        success: false,
        message: `Storage bucket creation failed: ${error.message}`
      };
    }
  }

  /**
   * Check if AI tutor can work without database (fallback mode)
   */
  canWorkWithoutDatabase(): boolean {
    // The AI tutor can work in a limited mode without database
    // by just using the AI services directly
    return true;
  }

  /**
   * Get minimal setup status for AI tutor
   */
  async getMinimalSetupStatus(): Promise<{
    canWork: boolean;
    hasDatabase: boolean;
    hasAuth: boolean;
    message: string;
  }> {
    try {
      // Check authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      const hasAuth = !!user && !authError;

      // Check if essential tables exist
      let hasDatabase = false;
      try {
        const { error } = await supabase
          .from('chat_sessions')
          .select('id')
          .limit(1);
        hasDatabase = !error || error.code !== '42P01';
      } catch (error) {
        hasDatabase = false;
      }

      const canWork = true; // AI tutor can always work with just API keys

      let message = '';
      if (hasAuth && hasDatabase) {
        message = 'Full functionality available';
      } else if (hasAuth) {
        message = 'Limited functionality - database not configured';
      } else {
        message = 'Basic functionality - no authentication';
      }

      return {
        canWork,
        hasDatabase,
        hasAuth,
        message
      };
    } catch (error) {
      return {
        canWork: true,
        hasDatabase: false,
        hasAuth: false,
        message: 'Basic functionality available'
      };
    }
  }

  /**
   * Test database connectivity and basic operations
   */
  async testDatabaseConnection(): Promise<SetupResult> {
    try {
      // Test basic connection
      const { data: user, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        return {
          success: false,
          message: 'Authentication failed',
          details: [authError.message]
        };
      }

      if (!user.user) {
        return {
          success: false,
          message: 'No authenticated user found',
          details: ['Please sign in to test database operations']
        };
      }

      // Test table access
      const testResults: string[] = [];
      const tables = ['research_documents', 'quizzes', 'educational_games', 'learning_progress'];

      for (const table of tables) {
        try {
          const { error } = await supabase
            .from(table)
            .select('id')
            .limit(1);

          if (error) {
            testResults.push(`❌ ${table}: ${error.message}`);
          } else {
            testResults.push(`✅ ${table}: OK`);
          }
        } catch (error) {
          testResults.push(`❌ ${table}: ${error.message}`);
        }
      }

      // Test storage access
      try {
        const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
        
        if (storageError) {
          testResults.push(`❌ Storage: ${storageError.message}`);
        } else {
          const hasDocumentsBucket = buckets?.find(b => b.name === 'research-documents');
          testResults.push(`${hasDocumentsBucket ? '✅' : '❌'} Storage bucket: ${hasDocumentsBucket ? 'OK' : 'Missing'}`);
        }
      } catch (error) {
        testResults.push(`❌ Storage: ${error.message}`);
      }

      const allPassed = testResults.every(result => result.startsWith('✅'));

      return {
        success: allPassed,
        message: allPassed ? 'All database tests passed!' : 'Some database tests failed',
        details: testResults
      };

    } catch (error) {
      return {
        success: false,
        message: 'Database connection test failed',
        details: [error.message]
      };
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<{
    documents: number;
    quizzes: number;
    games: number;
    studySessions: number;
    totalUsers: number;
  }> {
    try {
      const stats = {
        documents: 0,
        quizzes: 0,
        games: 0,
        studySessions: 0,
        totalUsers: 0
      };

      // Count documents
      try {
        const { count } = await supabase
          .from('research_documents')
          .select('*', { count: 'exact', head: true });
        stats.documents = count || 0;
      } catch (error) {
        console.warn('Failed to count documents:', error);
      }

      // Count quizzes
      try {
        const { count } = await supabase
          .from('quizzes')
          .select('*', { count: 'exact', head: true });
        stats.quizzes = count || 0;
      } catch (error) {
        console.warn('Failed to count quizzes:', error);
      }

      // Count games
      try {
        const { count } = await supabase
          .from('educational_games')
          .select('*', { count: 'exact', head: true });
        stats.games = count || 0;
      } catch (error) {
        console.warn('Failed to count games:', error);
      }

      // Count study sessions
      try {
        const { count } = await supabase
          .from('study_sessions')
          .select('*', { count: 'exact', head: true });
        stats.studySessions = count || 0;
      } catch (error) {
        console.warn('Failed to count study sessions:', error);
      }

      // Count unique users (from documents)
      try {
        const { data } = await supabase
          .from('research_documents')
          .select('user_id');
        
        const uniqueUsers = new Set(data?.map(d => d.user_id) || []);
        stats.totalUsers = uniqueUsers.size;
      } catch (error) {
        console.warn('Failed to count users:', error);
      }

      return stats;

    } catch (error) {
      console.error('Failed to get database stats:', error);
      return {
        documents: 0,
        quizzes: 0,
        games: 0,
        studySessions: 0,
        totalUsers: 0
      };
    }
  }

  /**
   * Clean up test data (for development)
   */
  async cleanupTestData(userId?: string): Promise<SetupResult> {
    try {
      const currentUserId = userId || (await supabase.auth.getUser()).data.user?.id;
      
      if (!currentUserId) {
        return {
          success: false,
          message: 'No user ID provided for cleanup'
        };
      }

      const deletedItems: string[] = [];

      // Delete in correct order to respect foreign key constraints
      const tables = [
        'game_results',
        'quiz_results', 
        'study_sessions',
        'learning_progress',
        'educational_games',
        'quizzes',
        'document_chunks',
        'document_sections',
        'research_documents'
      ];

      for (const table of tables) {
        try {
          const { count, error } = await supabase
            .from(table)
            .delete()
            .eq('user_id', currentUserId);

          if (error) {
            console.warn(`Failed to clean ${table}:`, error);
          } else {
            deletedItems.push(`${table}: ${count || 0} items`);
          }
        } catch (error) {
          console.warn(`Error cleaning ${table}:`, error);
        }
      }

      return {
        success: true,
        message: 'Test data cleanup completed',
        details: deletedItems
      };

    } catch (error) {
      return {
        success: false,
        message: 'Cleanup failed',
        details: [error.message]
      };
    }
  }

  /**
   * Display setup status in console
   */
  async logSetupStatus(): Promise<void> {
    console.log('🔍 Checking Research Comprehension Platform database setup...');
    
    const setupCheck = await this.checkDatabaseSetup();
    const stats = await this.getDatabaseStats();
    
    if (setupCheck.isConfigured) {
      console.log('✅ Database is properly configured!');
      console.log('📊 Current stats:', stats);
    } else {
      console.log('❌ Database setup incomplete');
      console.log('Missing tables:', setupCheck.missingTables);
      console.log('📋 Setup instructions:');
      
      const instructions = this.getSetupInstructions();
      instructions.steps.forEach(step => console.log(step));
      console.log(`📄 SQL file: ${instructions.sqlFile}`);
    }
  }
}

export const databaseSetupService = new DatabaseSetupService();
