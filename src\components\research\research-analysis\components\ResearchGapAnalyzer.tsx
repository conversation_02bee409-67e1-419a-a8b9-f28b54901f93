import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Target,
  Brain,
  Lightbulb,
  TrendingUp,
  Users,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from "lucide-react";

import { ResearchDocument, GapAnalysis, AIGenerationOptions, GenerationProgress } from '../types';
import { researchGapAnalysisService } from '../services/research-gap-analysis.service';
import { analysisResultsStorageService } from '../services/analysis-results-storage.service';
import { ProgressTracker } from './ProgressTracker';

interface ResearchGapAnalyzerProps {
  documents: ResearchDocument[];
  onAnalyze: (analysis: GapAnalysis) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function ResearchGapAnalyzer({
  documents,
  onAnalyze,
  aiSettings
}: ResearchGapAnalyzerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress | null>(null);
  const [currentAnalysis, setCurrentAnalysis] = useState<GapAnalysis | null>(null);

  const testAPIConnection = useCallback(async () => {
    try {
      console.log('🧪 Testing OpenRouter API connection...');
      const result = await researchGapAnalysisService.testConnection();

      if (result.success) {
        console.log('✅ API connection successful');
        toast.success('OpenRouter API connection successful!');
      } else {
        console.error('❌ API connection failed:', result.error);
        toast.error(`API connection failed: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Error testing API:', error);
      toast.error('Error testing API connection');
    }
  }, []);

  const handleAnalyzeGaps = useCallback(async () => {
    if (documents.length === 0) {
      toast.error('Please select documents for gap analysis');
      return;
    }

    setIsAnalyzing(true);
    setGenerationProgress(null);

    try {
      const analysis = await researchGapAnalysisService.identifyResearchGaps(
        documents,
        (progress) => {
          setGenerationProgress(progress);
        },
        aiSettings.selectedModel
      );

      setCurrentAnalysis(analysis);
      onAnalyze(analysis);

      // Save to database
      try {
        const { data: savedAnalysis, error: saveError } = await analysisResultsStorageService.saveGapAnalysis(analysis);
        if (saveError) {
          console.error('Error saving gap analysis to database:', saveError);
          toast.warning('Gap analysis completed but not saved to database');
        } else {
          console.log('Gap analysis saved to database:', savedAnalysis);
          toast.success('Research gap analysis completed and saved successfully!');
        }
      } catch (saveError) {
        console.error('Error saving gap analysis:', saveError);
        toast.success('Research gap analysis completed!');
      }
    } catch (error) {
      console.error('Error analyzing research gaps:', error);
      toast.error('Failed to analyze research gaps. Please try again.');
    } finally {
      setIsAnalyzing(false);
      setGenerationProgress(null);
    }
  }, [documents, onAnalyze]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-2xl">
                <Target className="h-7 w-7 text-orange-500" />
                Research Gap Analyzer
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Identify unexplored research opportunities and gaps in your selected literature
              </p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white/80 rounded-full border border-orange-300">
              <Brain className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                {aiSettings.selectedModel.split('/')[1]?.replace('-', ' ') || 'AI Model'}
              </span>
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Document Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Brain className="h-5 w-5 text-blue-500" />
            Analysis Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{documents.length}</div>
                <div className="text-sm text-blue-700">Documents</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {documents.filter(d => d.status === 'ready').length}
                </div>
                <div className="text-sm text-green-700">Ready</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {[...new Set(documents.flatMap(d => d.authors))].length}
                </div>
                <div className="text-sm text-purple-700">Authors</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {documents.length > 0 ? 
                    `${Math.min(...documents.map(d => d.publicationYear))}-${Math.max(...documents.map(d => d.publicationYear))}` 
                    : 'N/A'}
                </div>
                <div className="text-sm text-orange-700">Year Range</div>
              </div>
            </div>
          </div>

          {documents.length === 0 && (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <p className="text-orange-700">No documents selected for analysis</p>
              <p className="text-sm text-gray-600">Please select documents from the library first</p>
            </div>
          )}

          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={testAPIConnection}
              disabled={isAnalyzing}
              className="px-6"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Test API
            </Button>
            <Button
              size="lg"
              onClick={handleAnalyzeGaps}
              disabled={isAnalyzing || documents.length === 0}
              className="px-8"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  Analyzing Gaps...
                </>
              ) : (
                <>
                  <Target className="h-5 w-5 mr-2" />
                  Analyze Research Gaps
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Progress */}
      {isAnalyzing && generationProgress && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <ProgressTracker progress={generationProgress} />
          </CardContent>
        </Card>
      )}

      {/* Analysis Results */}
      {currentAnalysis && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Analysis Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{currentAnalysis.gaps.length}</div>
                  <div className="text-sm text-red-700">Research Gaps</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{currentAnalysis.themes.length}</div>
                  <div className="text-sm text-blue-700">Key Themes</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{currentAnalysis.opportunities.length}</div>
                  <div className="text-sm text-green-700">Opportunities</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Analysis Summary</h3>
                <p className="text-gray-700">{currentAnalysis.summary}</p>
              </div>
            </CardContent>
          </Card>

          {/* Research Gaps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Target className="h-5 w-5 text-red-500" />
                Identified Research Gaps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentAnalysis.gaps.map((gap) => (
                  <div key={gap.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-semibold text-lg">{gap.title}</h3>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(gap.priority)}>
                          {gap.priority}
                        </Badge>
                        <Badge variant="outline">
                          {gap.category}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-4">{gap.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <strong className="text-sm text-gray-600">Feasibility Score:</strong>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full" 
                              style={{ width: `${gap.feasibility * 10}%` }}
                            />
                          </div>
                          <span className="text-sm">{gap.feasibility}/10</span>
                        </div>
                      </div>
                      
                      <div>
                        <strong className="text-sm text-gray-600">Impact Score:</strong>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full" 
                              style={{ width: `${gap.impact * 10}%` }}
                            />
                          </div>
                          <span className="text-sm">{gap.impact}/10</span>
                        </div>
                      </div>
                    </div>

                    {gap.suggestedMethods.length > 0 && (
                      <div className="mb-3">
                        <strong className="text-sm text-gray-600">Suggested Methods:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {gap.suggestedMethods.map((method, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {method}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="text-sm text-gray-600">
                      <strong>Timeline:</strong> {gap.timelineEstimate}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Research Themes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                Key Research Themes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentAnalysis.themes.map((theme) => (
                  <div key={theme.id} className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-2">{theme.name}</h3>
                    <p className="text-gray-700 text-sm mb-3">{theme.description}</p>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline">
                        Frequency: {theme.frequency}
                      </Badge>
                      <Badge variant="outline">
                        {theme.relatedDocuments.length} docs
                      </Badge>
                    </div>

                    {theme.keyTerms.length > 0 && (
                      <div>
                        <strong className="text-sm text-gray-600">Key Terms:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {theme.keyTerms.map((term, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {term}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Research Opportunities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                Research Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentAnalysis.opportunities.map((opportunity) => (
                  <div key={opportunity.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-semibold">{opportunity.title}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {opportunity.type}
                        </Badge>
                        <Badge className={
                          opportunity.potentialImpact === 'transformative' ? 'bg-purple-100 text-purple-800' :
                          opportunity.potentialImpact === 'high' ? 'bg-red-100 text-red-800' :
                          opportunity.potentialImpact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }>
                          {opportunity.potentialImpact} impact
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-4">{opportunity.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {opportunity.requiredExpertise.length > 0 && (
                        <div>
                          <strong className="text-sm text-gray-600">Required Expertise:</strong>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {opportunity.requiredExpertise.map((expertise, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {expertise}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {opportunity.suggestedApproach.length > 0 && (
                        <div>
                          <strong className="text-sm text-gray-600">Suggested Approach:</strong>
                          <ul className="list-disc list-inside text-sm text-gray-700 mt-1">
                            {opportunity.suggestedApproach.map((approach, index) => (
                              <li key={index}>{approach}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
