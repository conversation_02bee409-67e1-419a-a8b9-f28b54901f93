import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  FileText,
  Calendar,
  Star,
  Download,
  Share2,
  Clock,
  User,
  Zap,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Lightbulb,
  Target,
  BookOpen,
  Copy,
  FileText as FileTextIcon,
  Eye,
  Highlighter
} from 'lucide-react';
import { toast } from 'sonner';
import {
  articleReviewStorageService,
  SavedArticleReview,
  SavedSectionReview,
  SavedDetailedFeedback
} from '../services/article-review-storage.service';
import { articleExportService } from '../services/article-export.service';

interface SavedReviewDetailProps {
  reviewId: string;
  onBack: () => void;
}

export function SavedReviewDetail({ reviewId, onBack }: SavedReviewDetailProps) {
  const [review, setReview] = useState<SavedArticleReview | null>(null);
  const [sections, setSections] = useState<SavedSectionReview[]>([]);
  const [feedback, setFeedback] = useState<Record<string, SavedDetailedFeedback[]>>({});
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<string>('overview');
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    loadReviewData();
  }, [reviewId]);

  const loadReviewData = async () => {
    try {
      setLoading(true);
      const result = await articleReviewStorageService.getArticleReviewById(reviewId);
      
      if (result.error) {
        console.error('Error loading review:', result.error);
        toast.error('Failed to load review details');
        return;
      }

      setReview(result.review);
      setSections(result.sections);
      setFeedback(result.feedback);
      
      // Set first section as active if available
      if (result.sections.length > 0) {
        setActiveSection(result.sections[0].section_name);
      }
    } catch (error) {
      console.error('Error loading review:', error);
      toast.error('Failed to load review details');
    } finally {
      setLoading(false);
    }
  };

  const handleExportText = async () => {
    if (!review || !sections) return;

    try {
      setIsExporting(true);
      articleExportService.downloadAsText(review, sections, feedback);
      toast.success('Review exported as text file');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export review');
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportHTML = async () => {
    if (!review || !sections) return;

    try {
      setIsExporting(true);
      articleExportService.downloadAsHTML(review, sections, feedback);
      toast.success('Review exported as HTML file');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export review');
    } finally {
      setIsExporting(false);
    }
  };

  const handleCopyToClipboard = async () => {
    if (!review || !sections) return;

    try {
      const success = await articleExportService.copyToClipboard(review, sections, feedback);
      if (success) {
        toast.success('Review copied to clipboard');
      } else {
        toast.error('Failed to copy to clipboard');
      }
    } catch (error) {
      console.error('Copy error:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatSectionName = (sectionName: string) => {
    // Convert section names to proper title case
    return sectionName
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score?: number) => {
    if (!score) return null;
    if (score >= 80) return <TrendingUp className="h-4 w-4" />;
    if (score >= 60) return <TrendingUp className="h-4 w-4" />;
    return <TrendingDown className="h-4 w-4" />;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'major': return 'text-red-600 bg-red-50 border-red-200';
      case 'moderate': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'minor': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Review Not Found</h3>
        <p className="text-gray-500 mb-4">The requested review could not be loaded.</p>
        <Button onClick={onBack} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to History
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{review.title}</h1>
            <p className="text-gray-600">{review.original_filename}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleCopyToClipboard}>
            <Copy className="h-4 w-4 mr-2" />
            Copy
          </Button>
          <Button variant="outline" onClick={handleExportText} disabled={isExporting}>
            <FileTextIcon className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export Text'}
          </Button>
          <Button variant="outline" onClick={handleExportHTML} disabled={isExporting}>
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export HTML'}
          </Button>
        </div>
      </div>

      {/* Review Info Card */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Created</p>
                <p className="font-medium">{formatDate(review.created_at)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-green-100">
                <Star className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Overall Score</p>
                <p className={`font-bold text-lg ${getScoreColor(review.overall_score)}`}>
                  {review.overall_score ? `${Math.round(review.overall_score)}/100` : 'N/A'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-purple-100">
                <Zap className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">AI Model</p>
                <p className="font-medium">{review.ai_model_used.split('/').pop()?.toUpperCase()}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-orange-100">
                <BookOpen className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Word Count</p>
                <p className="font-medium">{review.word_count?.toLocaleString() || 'N/A'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeSection} onValueChange={setActiveSection}>
        <div className="w-full overflow-x-auto">
          <TabsList className="inline-flex h-12 items-center justify-start rounded-lg bg-gray-100 p-1 text-gray-500 min-w-full">
            <TabsTrigger
              value="overview"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-950 data-[state=active]:shadow-sm"
            >
              <FileText className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            {sections.map((section) => (
              <TabsTrigger
                key={section.section_name}
                value={section.section_name}
                className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-gray-950 data-[state=active]:shadow-sm"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                {formatSectionName(section.section_name)}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Summary */}
          {review.review_summary && (
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-800">
                  <div className="p-2 rounded-full bg-blue-100">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  Review Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white rounded-lg p-4 border border-blue-100">
                  <p className="text-gray-700 leading-relaxed text-lg">{review.review_summary}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Scores Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {[
              { label: 'Clarity', score: review.clarity_score, icon: Eye },
              { label: 'Structure', score: review.structure_score, icon: BookOpen },
              { label: 'Methodology', score: review.methodology_score, icon: Target },
              { label: 'Significance', score: review.significance_score, icon: TrendingUp },
              { label: 'Originality', score: review.originality_score, icon: Lightbulb }
            ].map((metric) => (
              <Card key={metric.label} className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div className={`p-3 rounded-full ${getScoreColor(metric.score).includes('green') ? 'bg-green-100' : getScoreColor(metric.score).includes('yellow') ? 'bg-yellow-100' : 'bg-red-100'}`}>
                      <metric.icon className={`h-6 w-6 ${getScoreColor(metric.score).includes('green') ? 'text-green-600' : getScoreColor(metric.score).includes('yellow') ? 'text-yellow-600' : 'text-red-600'}`} />
                    </div>
                  </div>
                  <div className={`text-3xl font-bold ${getScoreColor(metric.score)} mb-2`}>
                    {metric.score ? Math.round(metric.score) : 'N/A'}
                  </div>
                  <p className="text-sm font-medium text-gray-600">{metric.label}</p>
                  {metric.score && (
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${getScoreColor(metric.score).includes('green') ? 'bg-green-500' : getScoreColor(metric.score).includes('yellow') ? 'bg-yellow-500' : 'bg-red-500'}`}
                        style={{ width: `${metric.score}%` }}
                      ></div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Strengths and Weaknesses */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Strengths */}
            {review.major_strengths && review.major_strengths.length > 0 && (
              <Card className="border-green-200 bg-green-50/50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <div className="p-2 rounded-full bg-green-100">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    Major Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {review.major_strengths.map((strength, index) => (
                      <li key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-green-100 shadow-sm">
                        <div className="p-1 rounded-full bg-green-100 mt-1">
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Weaknesses */}
            {review.major_weaknesses && review.major_weaknesses.length > 0 && (
              <Card className="border-red-200 bg-red-50/50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-red-800">
                    <div className="p-2 rounded-full bg-red-100">
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    </div>
                    Major Weaknesses
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {review.major_weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-red-100 shadow-sm">
                        <div className="p-1 rounded-full bg-red-100 mt-1">
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{weakness}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Recommendations */}
          {review.recommendations && review.recommendations.length > 0 && (
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-blue-800">
                  <div className="p-2 rounded-full bg-blue-100">
                    <Lightbulb className="h-5 w-5 text-blue-600" />
                  </div>
                  Key Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-4">
                  {review.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-4 p-4 bg-white rounded-lg border border-blue-100 shadow-sm">
                      <div className="flex-shrink-0 mt-1">
                        <div className="bg-blue-100 rounded-full p-2">
                          <Target className="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <span className="text-gray-700 leading-relaxed">{recommendation}</span>
                      </div>
                      <div className="flex-shrink-0">
                        <Badge variant="outline" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Section Tabs */}
        {sections.map((section) => (
          <TabsContent key={section.section_name} value={section.section_name} className="space-y-6">
            {/* Section Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                  {formatSectionName(section.section_name)} Analysis
                </h2>
                {section.score && (
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={section.score >= 80 ? 'default' : section.score >= 60 ? 'secondary' : 'destructive'}
                      className="text-sm px-3 py-1"
                    >
                      <Star className="h-4 w-4 mr-1" />
                      {Math.round(section.score)}/100
                    </Badge>
                  </div>
                )}
              </div>

              {/* Section Content Preview */}
              {section.content && (
                <div className="bg-white rounded-lg p-4 border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-600 mb-2">Section Content</h3>
                  <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                    {section.content.substring(0, 200)}...
                  </p>
                </div>
              )}
            </div>

            {/* Section Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-800">
                  <Target className="h-5 w-5 text-blue-600" />
                  Detailed Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-gray max-w-none">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{section.analysis}</p>
                </div>
              </CardContent>
            </Card>

            {/* Section Feedback */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Strengths */}
              {section.strengths && section.strengths.length > 0 && (
                <Card className="border-green-200 bg-green-50/50">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-green-800">
                      <div className="p-1.5 rounded-full bg-green-100">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      Strengths
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {section.strengths.map((strength, index) => (
                        <li key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-green-100">
                          <div className="p-1 rounded-full bg-green-100 mt-0.5">
                            <TrendingUp className="h-3 w-3 text-green-600" />
                          </div>
                          <span className="text-gray-700 leading-relaxed">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Weaknesses */}
              {section.weaknesses && section.weaknesses.length > 0 && (
                <Card className="border-red-200 bg-red-50/50">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-red-800">
                      <div className="p-1.5 rounded-full bg-red-100">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      </div>
                      Areas for Improvement
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {section.weaknesses.map((weakness, index) => (
                        <li key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-red-100">
                          <div className="p-1 rounded-full bg-red-100 mt-0.5">
                            <TrendingDown className="h-3 w-3 text-red-600" />
                          </div>
                          <span className="text-gray-700 leading-relaxed">{weakness}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Suggestions */}
            {section.suggestions && section.suggestions.length > 0 && (
              <Card className="border-blue-200 bg-blue-50/50">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-blue-800">
                    <div className="p-1.5 rounded-full bg-blue-100">
                      <Lightbulb className="h-4 w-4 text-blue-600" />
                    </div>
                    Suggestions for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {section.suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-3 p-3 bg-white rounded-lg border border-blue-100">
                        <div className="p-1 rounded-full bg-blue-100 mt-0.5">
                          <Target className="h-3 w-3 text-blue-600" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Detailed Feedback */}
            {feedback[section.section_name] && feedback[section.section_name].length > 0 && (
              <Card className="border-purple-200 bg-purple-50/30">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-purple-800">
                    <div className="p-1.5 rounded-full bg-purple-100">
                      <Highlighter className="h-4 w-4 text-purple-600" />
                    </div>
                    Detailed Feedback
                  </CardTitle>
                  <CardDescription className="text-purple-700">
                    Specific issues and suggestions for improvement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {feedback[section.section_name].map((item, index) => (
                      <div key={index} className={`p-5 rounded-xl border-2 bg-white shadow-sm ${getSeverityColor(item.severity)}`}>
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs font-medium px-2 py-1">
                              {item.feedback_type}
                            </Badge>
                            {item.line_number && (
                              <Badge variant="secondary" className="text-xs">
                                Line {item.line_number}
                              </Badge>
                            )}
                          </div>
                          <Badge
                            variant={item.severity === 'major' ? 'destructive' : item.severity === 'moderate' ? 'secondary' : 'default'}
                            className="text-xs font-medium"
                          >
                            {item.severity.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="space-y-4">
                          <div className="bg-gray-50 rounded-lg p-3">
                            <p className="text-sm font-semibold text-gray-800 mb-1">Original Text:</p>
                            <p className="text-sm text-gray-700 italic leading-relaxed">"{item.original_text}"</p>
                          </div>
                          <div className="bg-red-50 rounded-lg p-3 border border-red-100">
                            <p className="text-sm font-semibold text-red-800 mb-1">Issue:</p>
                            <p className="text-sm text-red-700 leading-relaxed">{item.issue_description}</p>
                          </div>
                          <div className="bg-green-50 rounded-lg p-3 border border-green-100">
                            <p className="text-sm font-semibold text-green-800 mb-1">Suggestion:</p>
                            <p className="text-sm text-green-700 leading-relaxed">{item.suggestion}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
