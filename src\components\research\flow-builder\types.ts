/**
 * Flow Builder Types
 * TypeScript interfaces for the Flow Chart Builder module
 */

// Core diagram types
export type DiagramType = 
  | 'flowchart' 
  | 'sequence' 
  | 'gantt' 
  | 'pie' 
  | 'gitgraph' 
  | 'mindmap' 
  | 'timeline' 
  | 'sankey' 
  | 'user-journey' 
  | 'c4' 
  | 'er' 
  | 'state' 
  | 'class';

export type DiagramDirection = 'TD' | 'TB' | 'BT' | 'RL' | 'LR';

export type ExportFormat = 'png' | 'svg' | 'pdf' | 'mermaid';

// Diagram metadata
export interface DiagramMetadata {
  title: string;
  description: string;
  type: DiagramType;
  direction?: DiagramDirection;
  keywords: string[];
  author?: string;
  createdAt: Date;
  updatedAt: Date;
}

// User input for diagram generation
export interface DiagramInput {
  description: string;
  type?: DiagramType;
  direction?: DiagramDirection;
  additionalRequirements?: string;
  context?: string; // Research context or methodology details
}

// Generated diagram
export interface GeneratedDiagram {
  id: string;
  metadata: DiagramMetadata;
  mermaidCode: string;
  svgContent?: string;
  status: 'generating' | 'ready' | 'error';
  error?: string;
  generatedAt: Date;
  model: string; // AI model used for generation
}

// Diagram generation request
export interface DiagramGenerationRequest {
  input: DiagramInput;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

// Diagram generation response
export interface DiagramGenerationResponse {
  success: boolean;
  diagram?: GeneratedDiagram;
  error?: string;
  suggestions?: string[];
}

// Diagram history and management
export interface DiagramHistory {
  diagrams: GeneratedDiagram[];
  currentDiagram?: string; // ID of currently selected diagram
  totalGenerated: number;
  lastGenerated?: Date;
}

// Export options
export interface DiagramExportOptions {
  format: ExportFormat;
  filename?: string;
  width?: number;
  height?: number;
  backgroundColor?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
}

// AI model configuration
export interface FlowAIModel {
  id: string;
  name: string;
  provider: 'google' | 'openrouter' | 'openai';
  description: string;
  maxTokens: number;
  supportsStreaming: boolean;
  costPerToken?: number;
  recommended?: boolean;
}

// Generation workflow state
export interface FlowGenerationWorkflow {
  currentStep: 'input' | 'generating' | 'review' | 'editing' | 'export';
  isGenerating: boolean;
  progress: number;
  estimatedTimeRemaining?: number;
  error?: string;
}

// Diagram editing state
export interface DiagramEditingState {
  isEditing: boolean;
  originalCode: string;
  editedCode: string;
  hasUnsavedChanges: boolean;
  validationErrors: string[];
}

// Mermaid configuration
export interface MermaidConfig {
  theme: 'default' | 'dark' | 'forest' | 'neutral';
  themeVariables?: Record<string, string>;
  flowchart?: {
    useMaxWidth: boolean;
    htmlLabels: boolean;
    curve: 'basis' | 'linear' | 'stepAfter';
  };
  sequence?: {
    diagramMarginX: number;
    diagramMarginY: number;
    actorMargin: number;
    width: number;
    height: number;
  };
  gantt?: {
    numberSectionStyles: number;
    axisFormat: string;
    tickInterval: string;
  };
}

// Template for common diagram patterns
export interface DiagramTemplate {
  id: string;
  name: string;
  description: string;
  type: DiagramType;
  category: 'research' | 'methodology' | 'process' | 'analysis' | 'general';
  mermaidCode: string;
  variables?: string[]; // Placeholder variables that can be replaced
  preview?: string; // Base64 encoded preview image
}

// Diagram validation result
export interface DiagramValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Flow builder store state
export interface FlowBuilderState {
  // Core state
  metadata: DiagramMetadata | null;
  currentDiagram: GeneratedDiagram | null;
  history: DiagramHistory;
  
  // Generation state
  workflow: FlowGenerationWorkflow;
  selectedModel: string;
  
  // Editing state
  editing: DiagramEditingState;
  
  // Configuration
  mermaidConfig: MermaidConfig;
  exportOptions: DiagramExportOptions;
  
  // Templates
  templates: DiagramTemplate[];
  selectedTemplate?: string;
}

// Action types for the store
export interface FlowBuilderActions {
  // Diagram generation
  generateDiagram: (request: DiagramGenerationRequest) => Promise<void>;
  regenerateDiagram: (feedback?: string) => Promise<void>;
  editDiagramWithAI: (editPrompt: string) => Promise<void>;
  fixDiagramErrors: (errorMessage: string) => Promise<void>;
  
  // Diagram management
  setCurrentDiagram: (diagram: GeneratedDiagram) => void;
  saveDiagram: (diagram: GeneratedDiagram) => void;
  deleteDiagram: (id: string) => void;
  loadDiagramHistory: () => void;
  
  // Editing
  startEditing: () => void;
  updateDiagramCode: (code: string) => void;
  saveEdits: () => void;
  cancelEditing: () => void;
  validateDiagram: (code: string) => DiagramValidationResult;
  
  // Export
  exportDiagram: (options: DiagramExportOptions) => Promise<void>;
  
  // Configuration
  updateMermaidConfig: (config: Partial<MermaidConfig>) => void;
  setSelectedModel: (model: string) => void;
  
  // Templates
  loadTemplates: () => void;
  applyTemplate: (templateId: string, variables?: Record<string, string>) => void;
  
  // Utility
  resetState: () => void;
  setError: (error: string) => void;
  clearError: () => void;
}

// Combined store interface
export interface FlowBuilderStore extends FlowBuilderState, FlowBuilderActions {}

// Component props interfaces
export interface FlowBuilderProps {
  className?: string;
}

export interface DiagramInputFormProps {
  onGenerate: (input: DiagramInput) => void;
  isGenerating: boolean;
  templates: DiagramTemplate[];
  onApplyTemplate: (templateId: string) => void;
  currentDiagram?: GeneratedDiagram | null;
  onEditDiagram?: (editPrompt: string) => void;
}

export interface DiagramRendererProps {
  diagram: GeneratedDiagram;
  config: MermaidConfig;
  onError?: (error: string) => void;
  onFixErrors?: (errorMessage: string) => void;
  className?: string;
}

export interface DiagramEditorProps {
  diagram: GeneratedDiagram;
  isEditing: boolean;
  editedCode: string;
  onCodeChange: (code: string) => void;
  onSave: () => void;
  onCancel: () => void;
  validationErrors: string[];
}

export interface DiagramExportDialogProps {
  diagram: GeneratedDiagram;
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: DiagramExportOptions) => void;
}

export interface DiagramHistoryPanelProps {
  history: DiagramHistory;
  onSelectDiagram: (diagram: GeneratedDiagram) => void;
  onDeleteDiagram: (id: string) => void;
  currentDiagramId?: string;
}

// Error types
export class FlowBuilderError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'FlowBuilderError';
  }
}

export class MermaidRenderError extends FlowBuilderError {
  constructor(message: string, details?: any) {
    super(message, 'MERMAID_RENDER_ERROR', details);
  }
}

export class DiagramGenerationError extends FlowBuilderError {
  constructor(message: string, details?: any) {
    super(message, 'DIAGRAM_GENERATION_ERROR', details);
  }
}

export class DiagramExportError extends FlowBuilderError {
  constructor(message: string, details?: any) {
    super(message, 'DIAGRAM_EXPORT_ERROR', details);
  }
}
