import React from 'react';
import { CheckCircle, Circle, AlertCircle, Loader2, Search, PenTool, BookOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ResearchStep, ResearchFlowContext } from '../services/research-flow.service';

interface ResearchFlowProgressProps {
  context: ResearchFlowContext;
  className?: string;
}

const stepIcons = {
  search: Search,
  write: PenTool,
  organize: BookOpen
};

const stepColors = {
  pending: 'text-gray-400',
  'in-progress': 'text-blue-600',
  completed: 'text-green-600',
  error: 'text-red-600'
};

const stepBgColors = {
  pending: 'bg-gray-100',
  'in-progress': 'bg-blue-100',
  completed: 'bg-green-100',
  error: 'bg-red-100'
};

export function ResearchFlowProgress({ context, className }: ResearchFlowProgressProps) {
  const getStepIcon = (step: ResearchStep) => {
    const IconComponent = stepIcons[step.id as keyof typeof stepIcons] || Circle;
    
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'in-progress':
        return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      default:
        return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const getProgressPercentage = () => {
    const completedSteps = context.steps.filter(step => step.status === 'completed').length;
    return (completedSteps / context.steps.length) * 100;
  };

  return (
    <div className={cn("bg-white rounded-lg border border-gray-200 p-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Research Progress</h3>
        <div className="text-sm text-gray-600">
          {context.steps.filter(s => s.status === 'completed').length} of {context.steps.length} steps completed
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          <span>{Math.round(getProgressPercentage())}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {context.steps.map((step, index) => (
          <div key={step.id} className="flex items-start space-x-4">
            {/* Step Icon */}
            <div className={cn(
              "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center",
              stepBgColors[step.status]
            )}>
              {getStepIcon(step)}
            </div>

            {/* Step Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className={cn(
                  "text-sm font-medium",
                  stepColors[step.status]
                )}>
                  {step.name}
                </h4>
                <span className={cn(
                  "text-xs px-2 py-1 rounded-full",
                  step.status === 'completed' && "bg-green-100 text-green-800",
                  step.status === 'in-progress' && "bg-blue-100 text-blue-800",
                  step.status === 'error' && "bg-red-100 text-red-800",
                  step.status === 'pending' && "bg-gray-100 text-gray-600"
                )}>
                  {step.status === 'in-progress' ? 'In Progress' : 
                   step.status === 'completed' ? 'Completed' :
                   step.status === 'error' ? 'Error' : 'Pending'}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mt-1">
                {step.description}
              </p>

              {/* Error Message */}
              {step.status === 'error' && step.error && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  <strong>Error:</strong> {step.error}
                </div>
              )}

              {/* Step Results Preview */}
              {step.status === 'completed' && step.result && (
                <div className="mt-2">
                  {step.id === 'search' && (
                    <div className="text-xs text-gray-500">
                      Found {step.result.results?.length || 0} sources
                    </div>
                  )}
                  {step.id === 'write' && (
                    <div className="text-xs text-gray-500">
                      Generated {Math.round((step.result.text?.length || 0) / 100) * 100}+ characters with citations
                    </div>
                  )}
                  {step.id === 'organize' && (
                    <div className="text-xs text-gray-500">
                      Organized {step.result.references?.length || 0} references
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Connector Line */}
            {index < context.steps.length - 1 && (
              <div className="absolute left-[2.5rem] mt-10 w-px h-6 bg-gray-200" />
            )}
          </div>
        ))}
      </div>

      {/* Current Step Details */}
      {context.currentStep < context.steps.length && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            <strong>Current Step:</strong> {context.steps[context.currentStep]?.name}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {context.steps[context.currentStep]?.description}
          </div>
        </div>
      )}

      {/* Completion Message */}
      {context.steps.every(step => step.status === 'completed') && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Research completed successfully!</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            All steps completed with properly formatted references
          </div>
        </div>
      )}
    </div>
  );
}

export default ResearchFlowProgress;
