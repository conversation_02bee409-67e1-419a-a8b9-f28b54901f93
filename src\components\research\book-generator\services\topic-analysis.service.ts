import { BookMetadata, UserChapter } from '../types';
import { getOptimalModel } from '../constants';

export interface TopicAnalysisResult {
  quality: 'poor' | 'good' | 'excellent';
  suggestions: string[];
  keywords: string[];
  improvements: string[];
  score: number;
}

export interface ChapterTopicAnalysis {
  chapterId: string;
  quality: 'poor' | 'good' | 'excellent';
  suggestions: string[];
  missingElements: string[];
  score: number;
}

/**
 * Service for analyzing book topics and providing intelligent suggestions
 */
export class TopicAnalysisService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
  }

  /**
   * Analyze book metadata and provide topic suggestions
   */
  async analyzeBookTopic(metadata: BookMetadata): Promise<TopicAnalysisResult> {
    try {
      if (!this.hasValidApiKey()) {
        return this.getFallbackBookAnalysis(metadata);
      }

      const prompt = this.buildBookAnalysisPrompt(metadata);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: getOptimalModel("topic-analysis", metadata),
          messages: [
            {
              role: "system",
              content: `You are an expert book topic analyzer. Analyze the provided book information and return a JSON response with topic quality assessment and suggestions.

RESPONSE FORMAT (JSON only):
{
  "quality": "poor|good|excellent",
  "suggestions": ["suggestion1", "suggestion2", ...],
  "keywords": ["keyword1", "keyword2", ...],
  "improvements": ["improvement1", "improvement2", ...],
  "score": 0-100
}`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      try {
        const parsed = JSON.parse(content);
        return this.validateAnalysisResult(parsed);
      } catch (parseError) {
        console.warn('Failed to parse AI response, using fallback');
        return this.getFallbackBookAnalysis(metadata);
      }

    } catch (error) {
      console.error('Topic analysis failed:', error);
      return this.getFallbackBookAnalysis(metadata);
    }
  }

  /**
   * Analyze individual chapter topics
   */
  async analyzeChapterTopics(
    metadata: BookMetadata, 
    chapters: UserChapter[]
  ): Promise<ChapterTopicAnalysis[]> {
    try {
      if (!this.hasValidApiKey()) {
        return this.getFallbackChapterAnalysis(chapters);
      }

      const analyses: ChapterTopicAnalysis[] = [];
      
      // Analyze chapters in batches to avoid rate limits
      for (let i = 0; i < chapters.length; i += 3) {
        const batch = chapters.slice(i, i + 3);
        const batchAnalyses = await this.analyzeChapterBatch(metadata, batch);
        analyses.push(...batchAnalyses);
        
        // Small delay between batches
        if (i + 3 < chapters.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      return analyses;
    } catch (error) {
      console.error('Chapter analysis failed:', error);
      return this.getFallbackChapterAnalysis(chapters);
    }
  }

  /**
   * Generate topic suggestions based on genre and audience
   */
  async generateTopicSuggestions(
    genre: string, 
    targetAudience: string, 
    keywords: string[]
  ): Promise<string[]> {
    try {
      if (!this.hasValidApiKey()) {
        return this.getFallbackTopicSuggestions(genre, targetAudience);
      }

      const prompt = `Generate 8-10 specific topic suggestions for a ${genre} book targeting ${targetAudience}.
      
Keywords: ${keywords.join(', ')}

Focus on:
- Specific methodologies, frameworks, or techniques
- Practical applications and use cases
- Advanced concepts and implementations
- Real-world examples and case studies

Return as JSON array: ["suggestion1", "suggestion2", ...]`;

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: "anthropic/claude-3-haiku",
          messages: [
            {
              role: "system",
              content: "You are a topic suggestion expert. Return only valid JSON arrays of specific, actionable topic suggestions."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 800,
          temperature: 0.8
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      try {
        const suggestions = JSON.parse(content);
        return Array.isArray(suggestions) ? suggestions.slice(0, 10) : [];
      } catch (parseError) {
        return this.getFallbackTopicSuggestions(genre, targetAudience);
      }

    } catch (error) {
      console.error('Topic suggestion generation failed:', error);
      return this.getFallbackTopicSuggestions(genre, targetAudience);
    }
  }

  private hasValidApiKey(): boolean {
    return this.apiKey && !this.apiKey.includes('your_') && this.apiKey.length > 20;
  }

  private buildBookAnalysisPrompt(metadata: BookMetadata): string {
    return `Analyze this book topic for outline generation quality:

Title: ${metadata.title}
Genre: ${metadata.genre}
Target Audience: ${metadata.targetAudience}
Description: ${metadata.description}
Keywords: ${metadata.keywords.join(', ')}

Assess:
1. Topic specificity and clarity
2. Potential for detailed chapter outlines
3. Technical depth and scope
4. Missing elements that would improve AI outline generation

Provide quality score (0-100), suggestions for chapter topics, relevant keywords, and specific improvements needed.`;
  }

  private async analyzeChapterBatch(
    metadata: BookMetadata, 
    chapters: UserChapter[]
  ): Promise<ChapterTopicAnalysis[]> {
    const prompt = `Analyze these chapter topics for a ${metadata.genre} book:

${chapters.map((ch, i) => `
Chapter ${i + 1}: ${ch.outline.title}
Description: ${ch.outline.description}
`).join('\n')}

For each chapter, assess:
- Topic specificity and detail level
- Potential for generating quality outlines
- Missing technical elements
- Suggestions for improvement

Return JSON: [{"chapterId": "id", "quality": "poor|good|excellent", "suggestions": [], "missingElements": [], "score": 0-100}]`;

    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: "anthropic/claude-3-haiku",
        messages: [
          {
            role: "system",
            content: "You are a chapter topic analyzer. Return only valid JSON arrays with chapter analysis results."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.6
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content || '';
    
    try {
      const analyses = JSON.parse(content);
      return Array.isArray(analyses) ? analyses.map((analysis, index) => ({
        chapterId: chapters[index]?.id || `chapter-${index}`,
        quality: analysis.quality || 'poor',
        suggestions: analysis.suggestions || [],
        missingElements: analysis.missingElements || [],
        score: analysis.score || 0
      })) : [];
    } catch (parseError) {
      return this.getFallbackChapterAnalysis(chapters);
    }
  }

  private validateAnalysisResult(result: any): TopicAnalysisResult {
    return {
      quality: ['poor', 'good', 'excellent'].includes(result.quality) ? result.quality : 'poor',
      suggestions: Array.isArray(result.suggestions) ? result.suggestions.slice(0, 8) : [],
      keywords: Array.isArray(result.keywords) ? result.keywords.slice(0, 10) : [],
      improvements: Array.isArray(result.improvements) ? result.improvements.slice(0, 5) : [],
      score: typeof result.score === 'number' ? Math.max(0, Math.min(100, result.score)) : 0
    };
  }

  private getFallbackBookAnalysis(metadata: BookMetadata): TopicAnalysisResult {
    const hasGoodDescription = metadata.description.length > 200;
    const hasKeywords = metadata.keywords.length > 3;
    const hasSpecificTerms = /\b(advanced|specific|implementation|framework|methodology|technique|analysis|system|process|model)\b/i.test(metadata.description);
    
    let quality: 'poor' | 'good' | 'excellent' = 'poor';
    let score = 20;
    
    if (hasGoodDescription && hasKeywords && hasSpecificTerms) {
      quality = 'excellent';
      score = 85;
    } else if ((hasGoodDescription && hasKeywords) || (hasGoodDescription && hasSpecificTerms)) {
      quality = 'good';
      score = 65;
    }

    return {
      quality,
      score,
      suggestions: [
        `Advanced ${metadata.genre.toLowerCase()} techniques`,
        `Practical implementation strategies`,
        `Case studies and real-world applications`,
        `Best practices and methodologies`,
        `Common challenges and solutions`
      ],
      keywords: [
        'implementation',
        'methodology',
        'framework',
        'analysis',
        'optimization'
      ],
      improvements: quality === 'poor' ? [
        'Add more specific technical details to the description',
        'Include relevant keywords and terminology',
        'Specify methodologies or frameworks to be covered',
        'Mention target outcomes and learning objectives'
      ] : []
    };
  }

  private getFallbackChapterAnalysis(chapters: UserChapter[]): ChapterTopicAnalysis[] {
    return chapters.map(chapter => {
      const description = chapter.outline.description.toLowerCase();
      const title = chapter.outline.title.toLowerCase();
      const combinedText = `${title} ${description}`;
      
      const hasSpecificTerms = /\b(advanced|specific|implementation|framework|methodology|technique|analysis|system|process|model)\b/.test(combinedText);
      const hasGoodLength = description.length > 100;
      const hasActionWords = /\b(how|implement|create|build|develop|design|optimize|analyze|evaluate)\b/.test(combinedText);
      
      let quality: 'poor' | 'good' | 'excellent' = 'poor';
      let score = 20;
      
      if (hasSpecificTerms && hasGoodLength && hasActionWords) {
        quality = 'excellent';
        score = 90;
      } else if ((hasSpecificTerms && hasGoodLength) || (hasActionWords && hasGoodLength)) {
        quality = 'good';
        score = 70;
      }

      return {
        chapterId: chapter.id,
        quality,
        score,
        suggestions: quality === 'poor' ? [
          'Add specific methodologies or frameworks',
          'Include technical implementation details',
          'Mention practical applications or use cases',
          'Specify learning outcomes and objectives'
        ] : [],
        missingElements: quality === 'poor' ? [
          'Technical terminology',
          'Specific methodologies',
          'Implementation details',
          'Practical examples'
        ] : []
      };
    });
  }

  private getFallbackTopicSuggestions(genre: string, targetAudience: string): string[] {
    const baseTopics = [
      'Fundamental concepts and principles',
      'Advanced techniques and methodologies',
      'Practical implementation strategies',
      'Real-world case studies and examples',
      'Best practices and optimization',
      'Common challenges and solutions',
      'Tools and technologies overview',
      'Future trends and developments'
    ];

    return baseTopics.map(topic => 
      `${topic} for ${targetAudience.toLowerCase()} in ${genre.toLowerCase()}`
    );
  }
}

export const topicAnalysisService = new TopicAnalysisService();
