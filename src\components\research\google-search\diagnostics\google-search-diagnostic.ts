/**
 * Google Search Diagnostic Tool
 * Comprehensive diagnostics to verify if Google Search is actually being used
 */

import { GoogleGenAI } from '@google/genai';

export interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
  evidence?: string[];
}

export class GoogleSearchDiagnostic {
  private ai: GoogleGenAI;
  private apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.apiKey) {
      throw new Error('VITE_GEMINI_API_KEY not found in environment variables');
    }
    this.ai = new GoogleGenAI({ apiKey: this.apiKey });
  }

  /**
   * Test 1: Verify Google Search tool is available and working
   */
  async testGoogleSearchAvailability(): Promise<DiagnosticResult> {
    try {
      const tools = [{ googleSearch: {} }];
      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        tools,
        responseMimeType: 'text/plain',
      };

      const model = 'gemini-2.5-pro';
      const testQuery = 'What is the current date and latest news about artificial intelligence?';

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: `CRITICAL: You MUST use Google Search to find current information. 
              
              Search for: "${testQuery}"
              
              IMPORTANT INSTRUCTIONS:
              1. Use Google Search tool to find REAL, current information
              2. Include the EXACT URLs you find in your search
              3. Mention specific recent dates or events you discover
              4. If you cannot access Google Search, explicitly say "I cannot access Google Search"
              5. Do NOT use your training data - only use Google Search results
              
              Start your response with "GOOGLE SEARCH RESULTS:" if you successfully used Google Search.
              Start with "NO GOOGLE SEARCH:" if you could not use Google Search.`,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      // Analyze response for Google Search indicators
      const indicators = this.analyzeGoogleSearchIndicators(fullResponse);
      
      return {
        test: 'Google Search Availability',
        status: indicators.hasGoogleSearch ? 'pass' : 'fail',
        message: indicators.hasGoogleSearch 
          ? 'Google Search appears to be working'
          : 'Google Search is NOT being used - using training data instead',
        details: {
          response: fullResponse.substring(0, 500) + '...',
          indicators,
          responseLength: fullResponse.length
        },
        evidence: indicators.evidence
      };

    } catch (error) {
      return {
        test: 'Google Search Availability',
        status: 'fail',
        message: `Google Search test failed: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test 2: Check for real URLs vs fake URLs
   */
  async testUrlAuthenticity(): Promise<DiagnosticResult> {
    try {
      const tools = [{ googleSearch: {} }];
      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        tools,
        responseMimeType: 'text/plain',
      };

      const model = 'gemini-2.5-pro';

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: `Use Google Search to find 5 recent research papers about "machine learning in healthcare" from academic sources.

              CRITICAL REQUIREMENTS:
              1. MUST use Google Search tool - do not use training data
              2. Find REAL, working URLs from actual academic sources
              3. Include the complete, exact URLs you find
              4. Verify these are from .edu, .gov, or academic journal domains
              5. Include publication dates and author names if available

              Format your response as:
              **SEARCH RESULTS:**
              1. Title: [Exact title from search]
                 URL: [Complete URL from search]
                 Source: [Domain/Institution]
                 Date: [Publication date if found]

              [Repeat for all 5 results]

              If you cannot find real URLs, explicitly state "COULD NOT FIND REAL URLS"`,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      // Extract and validate URLs
      const urlAnalysis = this.analyzeUrls(fullResponse);
      
      return {
        test: 'URL Authenticity',
        status: urlAnalysis.realUrls > 0 ? 'pass' : 'fail',
        message: `Found ${urlAnalysis.realUrls} real URLs, ${urlAnalysis.fakeUrls} fake URLs`,
        details: {
          response: fullResponse,
          urlAnalysis,
          extractedUrls: urlAnalysis.allUrls
        },
        evidence: urlAnalysis.evidence
      };

    } catch (error) {
      return {
        test: 'URL Authenticity',
        status: 'fail',
        message: `URL authenticity test failed: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test 3: Check for current/recent information vs training data
   */
  async testCurrentInformation(): Promise<DiagnosticResult> {
    try {
      const tools = [{ googleSearch: {} }];
      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        tools,
        responseMimeType: 'text/plain',
      };

      const model = 'gemini-2.5-pro';
      const currentYear = new Date().getFullYear();

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: `Use Google Search to find information about events that happened in ${currentYear}.

              SEARCH FOR: "major technology breakthroughs ${currentYear}" OR "latest AI developments ${currentYear}"

              REQUIREMENTS:
              1. MUST use Google Search - not your training data
              2. Find information specifically from ${currentYear}
              3. Include specific dates, company names, and recent events
              4. Provide URLs to news articles or press releases
              5. If you find information from ${currentYear}, start with "CURRENT INFO FOUND:"
              6. If you only have training data, start with "ONLY TRAINING DATA:"

              Look for very recent information that would not be in training data.`,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      // Analyze for current information indicators
      const currentInfo = this.analyzeCurrentInformation(fullResponse, currentYear);
      
      return {
        test: 'Current Information',
        status: currentInfo.hasCurrentInfo ? 'pass' : 'fail',
        message: currentInfo.hasCurrentInfo 
          ? `Found current ${currentYear} information`
          : `No current information found - likely using training data`,
        details: {
          response: fullResponse.substring(0, 500) + '...',
          currentInfo,
          currentYear
        },
        evidence: currentInfo.evidence
      };

    } catch (error) {
      return {
        test: 'Current Information',
        status: 'fail',
        message: `Current information test failed: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  /**
   * Analyze response for Google Search indicators
   */
  private analyzeGoogleSearchIndicators(response: string) {
    const evidence: string[] = [];
    let hasGoogleSearch = false;

    // Positive indicators
    if (response.includes('GOOGLE SEARCH RESULTS:')) {
      evidence.push('✅ Response starts with "GOOGLE SEARCH RESULTS:"');
      hasGoogleSearch = true;
    }

    if (response.includes('NO GOOGLE SEARCH:')) {
      evidence.push('❌ Response starts with "NO GOOGLE SEARCH:"');
      hasGoogleSearch = false;
    }

    // Look for real URLs
    const urlRegex = /https?:\/\/[^\s\)\]\,\;\"\'\`]+/g;
    const urls = response.match(urlRegex) || [];
    if (urls.length > 0) {
      evidence.push(`✅ Found ${urls.length} URLs in response`);
      hasGoogleSearch = true;
    }

    // Look for current date references
    const currentYear = new Date().getFullYear();
    if (response.includes(currentYear.toString())) {
      evidence.push(`✅ Contains current year (${currentYear})`);
      hasGoogleSearch = true;
    }

    // Look for specific recent events or dates
    const recentPatterns = [
      /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+2024\b/,
      /\b2024\b/,
      /\blast week\b/i,
      /\bthis month\b/i,
      /\brecently\b/i
    ];

    recentPatterns.forEach(pattern => {
      if (pattern.test(response)) {
        evidence.push(`✅ Contains recent time references`);
        hasGoogleSearch = true;
      }
    });

    // Negative indicators
    if (response.includes('I cannot access Google Search')) {
      evidence.push('❌ Explicitly states cannot access Google Search');
      hasGoogleSearch = false;
    }

    if (response.includes('based on my training data')) {
      evidence.push('❌ Mentions using training data');
      hasGoogleSearch = false;
    }

    return {
      hasGoogleSearch,
      evidence,
      urlCount: urls.length,
      responseLength: response.length
    };
  }

  /**
   * Analyze URLs for authenticity
   */
  private analyzeUrls(response: string) {
    const urlRegex = /https?:\/\/[^\s\)\]\,\;\"\'\`]+/g;
    const allUrls = response.match(urlRegex) || [];
    
    const evidence: string[] = [];
    let realUrls = 0;
    let fakeUrls = 0;

    const fakePatterns = [
      'example.com',
      'placeholder.com',
      'fake-url',
      'dummy-link',
      'test.com',
      'your-domain.com',
      'sample-site.com'
    ];

    const realPatterns = [
      '.edu',
      '.gov',
      'scholar.google',
      'pubmed.ncbi.nlm.nih.gov',
      'arxiv.org',
      'nature.com',
      'science.org',
      'ieee.org',
      'acm.org'
    ];

    allUrls.forEach(url => {
      const isFake = fakePatterns.some(pattern => url.includes(pattern));
      const isReal = realPatterns.some(pattern => url.includes(pattern));

      if (isFake) {
        fakeUrls++;
        evidence.push(`❌ Fake URL detected: ${url}`);
      } else if (isReal) {
        realUrls++;
        evidence.push(`✅ Real academic URL: ${url}`);
      } else {
        // Try to validate URL format
        try {
          new URL(url);
          realUrls++;
          evidence.push(`✅ Valid URL format: ${url}`);
        } catch {
          fakeUrls++;
          evidence.push(`❌ Invalid URL format: ${url}`);
        }
      }
    });

    return {
      allUrls,
      realUrls,
      fakeUrls,
      evidence,
      totalUrls: allUrls.length
    };
  }

  /**
   * Analyze for current information
   */
  private analyzeCurrentInformation(response: string, currentYear: number) {
    const evidence: string[] = [];
    let hasCurrentInfo = false;

    if (response.includes('CURRENT INFO FOUND:')) {
      evidence.push('✅ Response indicates current info found');
      hasCurrentInfo = true;
    }

    if (response.includes('ONLY TRAINING DATA:')) {
      evidence.push('❌ Response indicates only training data available');
      hasCurrentInfo = false;
    }

    // Look for current year mentions
    if (response.includes(currentYear.toString())) {
      evidence.push(`✅ Contains current year (${currentYear})`);
      hasCurrentInfo = true;
    }

    // Look for recent months
    const recentMonths = ['January', 'February', 'March', 'April', 'May', 'June', 
                         'July', 'August', 'September', 'October', 'November', 'December'];
    
    recentMonths.forEach(month => {
      if (response.includes(`${month} ${currentYear}`)) {
        evidence.push(`✅ Contains recent date: ${month} ${currentYear}`);
        hasCurrentInfo = true;
      }
    });

    return {
      hasCurrentInfo,
      evidence,
      currentYear
    };
  }

  /**
   * Run all diagnostic tests
   */
  async runFullDiagnostic(): Promise<DiagnosticResult[]> {
    console.log('🔍 Running Google Search Diagnostic Tests...\n');

    const results: DiagnosticResult[] = [];

    try {
      // Test 1: Google Search Availability
      console.log('1️⃣ Testing Google Search availability...');
      const availabilityResult = await this.testGoogleSearchAvailability();
      results.push(availabilityResult);

      // Test 2: URL Authenticity
      console.log('2️⃣ Testing URL authenticity...');
      const urlResult = await this.testUrlAuthenticity();
      results.push(urlResult);

      // Test 3: Current Information
      console.log('3️⃣ Testing current information access...');
      const currentInfoResult = await this.testCurrentInformation();
      results.push(currentInfoResult);

      // Summary
      const passedTests = results.filter(r => r.status === 'pass').length;
      const totalTests = results.length;

      console.log('\n📊 Diagnostic Summary:');
      console.log('=====================');
      results.forEach((result, index) => {
        const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
        console.log(`${icon} ${result.test}: ${result.message}`);
        
        if (result.evidence && result.evidence.length > 0) {
          console.log('   Evidence:');
          result.evidence.forEach(evidence => {
            console.log(`   ${evidence}`);
          });
        }
        console.log('');
      });

      console.log(`🎯 Overall: ${passedTests}/${totalTests} tests passed`);

      if (passedTests === totalTests) {
        console.log('🎉 Google Search is working correctly!');
      } else {
        console.log('⚠️ Google Search has issues. See details above.');
      }

    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      results.push({
        test: 'Diagnostic Error',
        status: 'fail',
        message: `Diagnostic failed: ${error.message}`,
        details: { error: error.message }
      });
    }

    return results;
  }
}

// Export diagnostic function for easy use
export const runGoogleSearchDiagnostic = async () => {
  try {
    const diagnostic = new GoogleSearchDiagnostic();
    return await diagnostic.runFullDiagnostic();
  } catch (error) {
    console.error('❌ Failed to create diagnostic:', error);
    return [{
      test: 'Diagnostic Setup',
      status: 'fail' as const,
      message: `Failed to setup diagnostic: ${error.message}`,
      details: { error: error.message }
    }];
  }
};
