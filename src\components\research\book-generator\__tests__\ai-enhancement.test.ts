import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AIEnhancementService } from '../services/ai-enhancement.service';

// Mock the environment variable
vi.mock('import.meta.env', () => ({
  VITE_OPENROUTER_API_KEY: 'test-api-key-12345678901234567890'
}));

// Mock fetch
global.fetch = vi.fn();

describe('AIEnhancementService', () => {
  let service: AIEnhancementService;

  beforeEach(() => {
    service = new AIEnhancementService();
    vi.clearAllMocks();
  });

  describe('enhanceTitle', () => {
    it('should enhance a basic title', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'Advanced Machine Learning Techniques for Data Scientists\nMastering AI: A Comprehensive Guide to Machine Learning\nData Science Revolution: Modern ML Approaches\nThe Complete Machine Learning Handbook\nAI-Powered Analytics: Advanced ML Methods'
            }
          }]
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await service.enhanceTitle(
        'Machine Learning Guide',
        'Technology',
        'professionals'
      );

      expect(result).toHaveLength(5);
      expect(result[0]).toBe('Advanced Machine Learning Techniques for Data Scientists');
      expect(fetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key-12345678901234567890',
            'Content-Type': 'application/json'
          })
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      (global.fetch as any).mockRejectedValue(new Error('API Error'));

      const result = await service.enhanceTitle('Test Title');
      expect(result).toEqual(['Test Title']);
    });
  });

  describe('enhanceDescription', () => {
    it('should enhance a basic description', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'This comprehensive guide to machine learning provides data scientists and AI practitioners with cutting-edge techniques and practical implementations. Readers will master advanced algorithms, deep learning frameworks, and real-world applications that drive modern AI solutions. The book covers everything from foundational concepts to state-of-the-art methodologies, making it an essential resource for professionals looking to excel in the rapidly evolving field of artificial intelligence.'
            }
          }]
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await service.enhanceDescription(
        'A guide to machine learning',
        'Machine Learning Guide',
        'Technology',
        'professionals',
        ['AI', 'data science']
      );

      expect(result).toContain('comprehensive guide');
      expect(result).toContain('machine learning');
      expect(result.length).toBeGreaterThan(100);
    });
  });

  describe('enhanceKeywords', () => {
    it('should generate relevant keywords', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'machine learning\nartificial intelligence\ndata science\ndeep learning\nneural networks\nalgorithms\npython programming\ndata analysis\npredictive modeling\nAI applications\nstatistical learning\ncomputer vision\nnatural language processing\nbig data\ndata mining'
            }
          }]
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await service.enhanceKeywords(
        'Machine Learning Guide',
        'A comprehensive guide to machine learning',
        'Technology',
        ['AI']
      );

      expect(result).toHaveLength(15);
      expect(result).toContain('machine learning');
      expect(result).toContain('artificial intelligence');
    });
  });

  describe('enhanceCompleteMetadata', () => {
    it('should enhance all metadata fields', async () => {
      const mockTitleResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'Advanced Machine Learning Techniques\nMastering AI and Data Science\nThe Complete ML Handbook'
            }
          }]
        })
      };

      const mockDescriptionResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'Enhanced description with better marketing appeal and detailed explanations of what readers will learn.'
            }
          }]
        })
      };

      const mockKeywordsResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'machine learning\nartificial intelligence\ndata science\ndeep learning\nneural networks'
            }
          }]
        })
      };

      (global.fetch as any)
        .mockResolvedValueOnce(mockTitleResponse)
        .mockResolvedValueOnce(mockDescriptionResponse)
        .mockResolvedValueOnce(mockKeywordsResponse);

      const metadata = {
        title: 'ML Guide',
        description: 'Basic description',
        genre: 'Technology',
        targetAudience: 'professionals',
        keywords: ['AI'],
        authors: ['Test Author'],
        estimatedLength: 'medium' as const,
        tone: 'professional' as const
      };

      const result = await service.enhanceCompleteMetadata(metadata);

      expect(result.title).toBe('Advanced Machine Learning Techniques');
      expect(result.description).toContain('Enhanced description');
      expect(result.keywords).toContain('machine learning');
      expect(result.alternativeTitles).toHaveLength(2);
    });
  });

  describe('generateSubtitle', () => {
    it('should generate relevant subtitles', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          choices: [{
            message: {
              content: 'A Comprehensive Guide for Data Scientists\nMastering AI Techniques and Applications\nFrom Theory to Practice\nAdvanced Methods and Real-World Solutions'
            }
          }]
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await service.generateSubtitle(
        'Machine Learning Guide',
        'A comprehensive guide to machine learning',
        'Technology'
      );

      expect(result).toHaveLength(4);
      expect(result[0]).toBe('A Comprehensive Guide for Data Scientists');
    });
  });
});
