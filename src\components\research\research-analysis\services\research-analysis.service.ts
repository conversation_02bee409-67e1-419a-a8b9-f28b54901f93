import {
  ResearchDocument,
  DocumentSection,
  LiteratureReview,
  ResearchGap,
  GapAnalysis,
  ResearchHypothesis,
  MethodologyAnalysis,
  CitationNetwork,
  ResearchProject,
  KnowledgeBase,
  ExtractedData,
  AIGenerationOptions,
  GenerationProgress
} from '../types';

// PDF.js is imported dynamically to avoid build issues
// import * as pdfjsLib from 'pdfjs-dist';
// import 'pdfjs-dist/build/pdf.worker.mjs';

class ResearchAnalysisService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';
  private notifyDocumentProcessing?: (document: ResearchDocument) => void;
  private notifyDocumentProcessingUpdate?: (update: { status: string, message: string, progress: number }) => void;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
    
    // Pre-load PDF.js worker to avoid runtime issues
    this.preloadPdfWorker();
  }
  
  /**
   * Preload PDF.js worker to ensure it's available when needed
   */
  private async preloadPdfWorker(): Promise<void> {
    try {
      const pdfjsLib = await import('pdfjs-dist');
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';
      console.log('PDF.js worker configured successfully');
    } catch (error) {
      console.error('Failed to configure PDF.js worker:', error);
    }
  }

  /**
   * Process uploaded documents and extract metadata
   * Now with enhanced academic paper analysis
   */
  async processDocuments(
    files: File[],
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<ResearchDocument[]> {
    const documents: ResearchDocument[] = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const currentProgress = (i / files.length) * 100;
      
      onProgress?.({
        stage: `Processing document ${i + 1} of ${files.length}`,
        progress: currentProgress,
        message: `Analyzing ${file.name}...`,
        estimatedTimeRemaining: ((files.length - i) * 45) // Updated estimate: 45 seconds per document for deeper analysis
      });

      try {
        // First, try to extract text content
        onProgress?.({
          stage: `Processing document ${i + 1} of ${files.length}`,
          progress: currentProgress + 5,
          message: `Extracting text from ${file.name}...`
        });

        const content = await this.extractTextFromFile(file);
        
        if (!content || content.trim().length < 50) {
          throw new Error('Unable to extract meaningful content from file');
        }

        // Enhanced analysis for research papers
        onProgress?.({
          stage: `Processing document ${i + 1} of ${files.length}`,
          progress: currentProgress + 15,
          message: `Performing advanced analysis of ${file.name}...`
        });

        // Use the enhanced document analysis method
        const document = await this.analyzeDocument(file, content);
        documents.push(document);
        successCount++;
        
        onProgress?.({
          stage: `Processing document ${i + 1} of ${files.length}`,
          progress: currentProgress + 25,
          message: `Successfully processed ${file.name} with detailed analysis`
        });

      } catch (error) {
        console.error(`Error processing ${file.name}:`, error);
        errorCount++;
        
        // Create a minimal document entry for failed processing
        const errorDocument: ResearchDocument = {
          id: crypto.randomUUID(),
          title: file.name,
          authors: [],
          abstract: `Processing failed: ${error.message}`,
          publicationYear: new Date().getFullYear(),
          keywords: [],
          filename: file.name,
          fileSize: file.size,
          uploadedAt: new Date(),
          lastModified: new Date(),
          status: 'error',
          sections: [],
          rawContent: '',
          metadata: {
            extractedAt: new Date(),
            confidence: 0,
            sources: [],
            language: 'unknown'
          },
          keyFindings: [],
          methodology: [],
          limitations: [`Error: ${error.message}`],
          futureWork: [],
          tags: ['error', 'processing-failed'],
          categories: ['failed-processing'],
          favorite: false,
          notes: `Processing failed: ${error.message}. Please check file format and try again.`
        };
        
        documents.push(errorDocument);
      }
    }

    const finalMessage = `Processing complete: ${successCount} successful, ${errorCount} failed`;
    onProgress?.({
      stage: 'Processing complete',
      progress: 100,
      message: finalMessage
    });

    return documents;
  }

  /**
   * Analyze a single document using AI
   */
  private async analyzeDocument(file: File, content: string): Promise<ResearchDocument> {
    // Enhanced prompt for deeper academic analysis
    const prompt = `You are an expert academic researcher. Thoroughly analyze this research paper and extract detailed information in JSON format.

Paper content (excerpt): ${content.substring(0, 10000)}

Extract the following information:
1. Title (be precise, remove any non-title text)
2. Authors (full list of authors)
3. Abstract (complete abstract only)
4. Publication year
5. Journal or conference name
6. DOI if available
7. Keywords (extract or generate relevant keywords)
8. Detailed key findings (5+ key findings with explanations)
9. Methodology details (comprehensive analysis of methods used)
10. Results summary (quantitative and qualitative results)
11. Limitations identified in the study (be thorough)
12. Future work suggestions from the paper
13. Achievements and contributions of the paper
14. Document sections with titles, content excerpts, and types
15. Citations and references format

Return in this exact JSON format:
{
  "title": "complete paper title",
  "authors": ["author1", "author2", ...],
  "abstract": "complete abstract text",
  "publicationYear": number,
  "journal": "journal name or null",
  "doi": "doi string or null",
  "keywords": ["keyword1", "keyword2", ...],
  "keyFindings": ["detailed finding 1", "detailed finding 2", ...],
  "methodology": ["method description 1", "method description 2", ...],
  "results": ["result 1", "result 2", ...],
  "limitations": ["limitation 1", "limitation 2", ...],
  "futureWork": ["future direction 1", "future direction 2", ...],
  "achievements": ["achievement 1", "achievement 2", ...],
  "sections": [
    {
      "title": "section title",
      "content": "excerpt of the section content (most relevant parts)",
      "type": "one of [abstract, introduction, literature_review, methodology, results, discussion, conclusion, references]",
      "order": number
    }
  ],
  "citationFormat": "identified citation style (APA, MLA, etc.)",
  "confidence": float (0-1)
}

Be thorough and accurate in your analysis, particularly for methodology, results, and limitations.`;

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 4000,
        temperature: 0.1
      });

      // Use the improved robust JSON parser
      const analysisResult = this.parseJSONResponse(response);
      
      // Create the document with enhanced analysis
      const document: ResearchDocument = {
        id: crypto.randomUUID(),
        title: analysisResult.title || file.name,
        authors: analysisResult.authors || [],
        abstract: analysisResult.abstract || '',
        publicationYear: analysisResult.publicationYear || new Date().getFullYear(),
        journal: analysisResult.journal,
        doi: analysisResult.doi,
        keywords: analysisResult.keywords || [],
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date(),
        lastModified: new Date(),
        status: 'ready',
        sections: analysisResult.sections?.map((section: any, index: number) => ({
          id: crypto.randomUUID(),
          title: section.title || `Section ${index + 1}`,
          content: section.content || '',
          type: section.type || this.inferSectionType(section.title || `Section ${index + 1}`),
          order: section.order || index,
          wordCount: section.content ? section.content.split(' ').length : 0
        })) || [],
        rawContent: content,
        metadata: {
          extractedAt: new Date(),
          confidence: analysisResult.confidence || 0.8,
          sources: ['ai-analysis', 'pdf-extract'],
          language: analysisResult.language || 'english',
          citationCount: analysisResult.citations?.length
        },
        summary: analysisResult.abstract,
        keyFindings: analysisResult.keyFindings || [],
        methodology: analysisResult.methodology || [],
        limitations: analysisResult.limitations || [],
        futureWork: analysisResult.futureWork || [],
        tags: analysisResult.keywords || [],
        categories: [],
        favorite: false,
        notes: ''
      };
      
      // Store additional data if available
      if (analysisResult.results || analysisResult.achievements) {
        document.notes = '--- ADDITIONAL ANALYSIS ---\n\n';
        
        if (analysisResult.results?.length > 0) {
          document.notes += '\nRESULTS:\n' + analysisResult.results.map((r: string) => `- ${r}`).join('\n');
        }
        
        if (analysisResult.achievements?.length > 0) {
          document.notes += '\n\nACHIEVEMENTS:\n' + analysisResult.achievements.map((a: string) => `- ${a}`).join('\n');
        }
      }
      
      return document;
    } catch (error) {
      console.error('Error analyzing document:', error);
      
      // Create a useful error document instead of just throwing an error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Check for API key errors specifically
      const isApiKeyError = errorMessage.includes('API key') || 
                          errorMessage.includes('auth') || 
                          errorMessage.includes('401');
                          
      // Create an error document that contains helpful information
      return {
        id: crypto.randomUUID(),
        title: `${file.name} (Analysis Error)`,
        authors: [],
        abstract: isApiKeyError 
          ? 'API key error detected. Please check your OpenRouter API key in the .env file (VITE_OPENROUTER_API_KEY).'
          : `Failed to analyze document: ${errorMessage}. Please try again.`,
        publicationYear: new Date().getFullYear(),
        keywords: [],
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date(),
        lastModified: new Date(),
        status: 'error',
        sections: [{
          id: crypto.randomUUID(),
          title: 'Error Information',
          content: `An error occurred during document analysis: ${errorMessage}\n\n${isApiKeyError 
            ? 'This appears to be an API key issue. Please verify your OpenRouter API key is correctly set in the .env file as VITE_OPENROUTER_API_KEY.'
            : 'This may be due to unexpected document format or AI processing limits. Try uploading a different document or extracting the text manually.'}`,
          type: 'other',
          order: 0,
          wordCount: 50
        }],
        rawContent: content.substring(0, 1000) + '... (content truncated)',
        metadata: {
          extractedAt: new Date(),
          confidence: 0,
          sources: ['error'],
          language: 'english'
        },
        keyFindings: [],
        methodology: [],
        limitations: [],
        futureWork: [],
        tags: [],
        categories: [],
        favorite: false,
        notes: `Error analysis timestamp: ${new Date().toLocaleString()}\nError details: ${errorMessage}`
      };
    }
  }

  /**
   * Generate literature review from selected documents
   */
  async generateLiteratureReview(
    documents: ResearchDocument[],
    topic: string,
    citationStyle: string = 'APA',
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<LiteratureReview> {
    const documentSummaries = documents.map(doc => ({
      title: doc.title,
      authors: doc.authors,
      year: doc.publicationYear,
      abstract: doc.abstract,
      keyFindings: doc.keyFindings,
      methodology: doc.methodology
    }));

    const prompt = `Generate a comprehensive literature review on the topic: "${topic}"

Based on the following research documents:
${JSON.stringify(documentSummaries, null, 2)}

Structure the literature review with the following sections:
1. Introduction to the research area
2. Chronological development of the field
3. Key themes and findings
4. Methodological approaches
5. Identified gaps and limitations
6. Conflicting results and debates
7. Conclusions and future directions

For each section, provide:
- Clear heading
- Well-structured content (300-500 words per section)
- Proper in-text citations in ${citationStyle} format
- Logical flow and academic writing style

Return the result in JSON format:
{
  "title": "Literature Review: [Topic]",
  "sections": [
    {
      "title": "string",
      "content": "string",
      "type": "string",
      "order": number,
      "sourceDocuments": ["document IDs that support this section"],
      "citations": [
        {
          "documentId": "string",
          "inText": "string",
          "fullReference": "string"
        }
      ]
    }
  ],
  "summary": "Overall summary of the literature review",
  "wordCount": number
}`;

    onProgress?.({
      stage: 'Generating literature review',
      progress: 20,
      message: 'Analyzing document themes...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 6000,
        temperature: 0.3
      });

      onProgress?.({
        stage: 'Processing results',
        progress: 80,
        message: 'Formatting literature review...'
      });

      const result = this.parseJSONResponse(response);

      const literatureReview: LiteratureReview = {
        id: crypto.randomUUID(),
        title: result.title || `Literature Review: ${topic}`,
        documents: documents.map(d => d.id),
        sections: result.sections?.map((section: any, index: number) => ({
          id: crypto.randomUUID(),
          title: section.title,
          content: section.content,
          type: section.type || 'thematic',
          order: section.order || index,
          sourceDocuments: section.sourceDocuments || [],
          citations: section.citations || []
        })) || [],
        citationStyle: 'APA',
        status: 'completed',
        generatedAt: new Date(),
        wordCount: result.wordCount || 0,
        exportOptions: ['PDF', 'Word']
      };

      return literatureReview;
    } catch (error) {
      console.error('Error generating literature review:', error);
      throw new Error('Failed to generate literature review');
    }
  }

  /**
   * Identify research gaps from documents
   */
  async identifyResearchGaps(
    documents: ResearchDocument[],
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<GapAnalysis> {
    const documentContext = documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      authors: doc.authors,
      year: doc.publicationYear,
      keyFindings: doc.keyFindings,
      methodology: doc.methodology,
      limitations: doc.limitations,
      futureWork: doc.futureWork
    }));

    const prompt = `Analyze the provided research papers to identify research gaps and opportunities.

Research Papers:
${JSON.stringify(documentContext, null, 2)}

Please identify:
1. Unexplored research questions
2. Methodological gaps
3. Contradictory findings requiring investigation
4. Opportunities for interdisciplinary approaches
5. Emerging trends not yet fully explored

For each identified gap, provide:
- Clear title and description
- Category (methodological, theoretical, empirical, interdisciplinary)
- Priority level (low, medium, high, critical)
- Feasibility score (1-10)
- Impact score (1-10)
- Suggested methods
- Required resources
- Timeline estimate
- Related documents

Also identify key themes and research opportunities.

Return in JSON format:
{
  "gaps": [
    {
      "title": "string",
      "description": "string",
      "category": "string",
      "priority": "string",
      "feasibility": number,
      "impact": number,
      "relatedDocuments": ["string"],
      "suggestedMethods": ["string"],
      "requiredResources": ["string"],
      "timelineEstimate": "string",
      "collaborationPotential": number
    }
  ],
  "themes": [
    {
      "name": "string",
      "description": "string",
      "frequency": number,
      "relatedDocuments": ["string"],
      "keyTerms": ["string"]
    }
  ],
  "opportunities": [
    {
      "title": "string",
      "description": "string",
      "type": "string",
      "difficulty": "string",
      "potentialImpact": "string",
      "requiredExpertise": ["string"],
      "suggestedApproach": ["string"]
    }
  ],
  "summary": "string"
}`;

    onProgress?.({
      stage: 'Analyzing documents',
      progress: 30,
      message: 'Identifying patterns and gaps...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 5000,
        temperature: 0.4
      });

      onProgress?.({
        stage: 'Processing analysis',
        progress: 80,
        message: 'Organizing research gaps...'
      });

      const result = this.parseJSONResponse(response);
      
      const gapAnalysis: GapAnalysis = {
        id: crypto.randomUUID(),
        documentIds: documents.map(d => d.id),
        gaps: result.gaps?.map((gap: any) => ({
          id: crypto.randomUUID(),
          title: gap.title,
          description: gap.description,
          category: gap.category,
          priority: gap.priority,
          feasibility: gap.feasibility,
          impact: gap.impact,
          relatedDocuments: gap.relatedDocuments || [],
          suggestedMethods: gap.suggestedMethods || [],
          requiredResources: gap.requiredResources || [],
          timelineEstimate: gap.timelineEstimate,
          collaborationPotential: gap.collaborationPotential || 5
        })) || [],
        themes: result.themes?.map((theme: any) => ({
          id: crypto.randomUUID(),
          name: theme.name,
          description: theme.description,
          frequency: theme.frequency,
          relatedDocuments: theme.relatedDocuments || [],
          keyTerms: theme.keyTerms || [],
          evolution: []
        })) || [],
        opportunities: result.opportunities?.map((opp: any) => ({
          id: crypto.randomUUID(),
          title: opp.title,
          description: opp.description,
          type: opp.type,
          difficulty: opp.difficulty,
          potentialImpact: opp.potentialImpact,
          requiredExpertise: opp.requiredExpertise || [],
          suggestedApproach: opp.suggestedApproach || []
        })) || [],
        generatedAt: new Date(),
        summary: result.summary || 'Gap analysis completed successfully'
      };

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Research gap analysis completed'
      });

      return gapAnalysis;
    } catch (error) {
      console.error('Error identifying research gaps:', error);
      throw error;
    }
  }

  /**
   * Generate testable hypotheses
   */
  async generateHypotheses(
    gaps: ResearchGap[],
    documents: ResearchDocument[],
    count: number = 10,
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<ResearchHypothesis[]> {
    const gapContext = gaps.map(gap => ({
      title: gap.title,
      description: gap.description,
      category: gap.category,
      suggestedMethods: gap.suggestedMethods
    }));

    const documentContext = documents.map(doc => ({
      title: doc.title,
      keyFindings: doc.keyFindings,
      methodology: doc.methodology
    }));

    const prompt = `Based on the identified research gaps and analyzed papers, generate ${count} testable hypotheses.

Research Gaps:
${JSON.stringify(gapContext, null, 2)}

Related Research Papers:
${JSON.stringify(documentContext, null, 2)}

For each hypothesis, provide:
1. Clear hypothesis statement
2. Type (directional, non-directional, null)
3. Variables (independent, dependent, control, moderating, mediating)
4. Testability score (1-10)
5. Novelty score (1-10)
6. Significance score (1-10)
7. Suggested methodology
8. Expected outcomes
9. Potential limitations
10. Required resources
11. Timeline estimate

Return in JSON format:
{
  "hypotheses": [
    {
      "statement": "string",
      "type": "string",
      "variables": {
        "independent": [
          {
            "name": "string",
            "type": "string",
            "description": "string",
            "measurement": "string",
            "operationalization": "string"
          }
        ],
        "dependent": [...],
        "control": [...],
        "moderating": [...],
        "mediating": [...]
      },
      "testability": number,
      "novelty": number,
      "significance": number,
      "suggestedMethodology": ["string"],
      "expectedOutcomes": ["string"],
      "limitations": ["string"],
      "requiredResources": ["string"],
      "timeline": "string"
    }
  ]
}`;

    onProgress?.({
      stage: 'Generating hypotheses',
      progress: 40,
      message: 'Creating testable hypotheses...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 6000,
        temperature: 0.5
      });

      onProgress?.({
        stage: 'Validating hypotheses',
        progress: 80,
        message: 'Checking hypothesis quality...'
      });

      const result = this.parseJSONResponse(response);
      
      const hypotheses: ResearchHypothesis[] = result.hypotheses?.map((hyp: any) => ({
        id: crypto.randomUUID(),
        statement: hyp.statement,
        type: hyp.type,
        variables: hyp.variables,
        testability: hyp.testability,
        novelty: hyp.novelty,
        significance: hyp.significance,
        suggestedMethodology: hyp.suggestedMethodology || [],
        expectedOutcomes: hyp.expectedOutcomes || [],
        limitations: hyp.limitations || [],
        requiredResources: hyp.requiredResources || [],
        timeline: hyp.timeline,
        relatedGaps: gaps.map(g => g.id)
      })) || [];

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: `Generated ${hypotheses.length} hypotheses`
      });

      return hypotheses;
    } catch (error) {
      console.error('Error generating hypotheses:', error);
      throw error;
    }
  }

  /**
   * Analyze methodologies across documents
   */
  async analyzeMethodologies(
    documents: ResearchDocument[],
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<MethodologyAnalysis> {
    const methodologyContext = documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      methodology: doc.methodology,
      keyFindings: doc.keyFindings,
      limitations: doc.limitations,
      year: doc.publicationYear
    }));

    const prompt = `Analyze the methodologies used across the provided research papers.

Research Papers:
${JSON.stringify(methodologyContext, null, 2)}

Please extract:
1. All unique methodologies used
2. Categorize each method (quantitative, qualitative, mixed-methods)
3. Analyze advantages and disadvantages
4. Usage frequency and success rates
5. Tools and datasets commonly used
6. Validation approaches
7. Reproducibility assessment
8. Trends over time
9. Method comparisons
10. Recommendations for future research

Return in JSON format:
{
  "methods": [
    {
      "name": "string",
      "category": "string",
      "description": "string",
      "advantages": ["string"],
      "disadvantages": ["string"],
      "usageCount": number,
      "successRate": number,
      "tools": ["string"],
      "datasets": ["string"],
      "validationApproaches": ["string"],
      "reproducibility": "string",
      "documents": ["string"]
    }
  ],
  "comparison": [
    {
      "methods": ["string"],
      "criteria": [
        {
          "name": "string",
          "weight": number,
          "description": "string"
        }
      ],
      "scores": {},
      "recommendation": "string"
    }
  ],
  "recommendations": [
    {
      "researchQuestion": "string",
      "primaryMethod": "string",
      "supportingMethods": ["string"],
      "justification": "string",
      "expectedOutcomes": ["string"],
      "considerations": ["string"]
    }
  ],
  "trends": [
    {
      "method": "string",
      "yearlyUsage": [{"year": number, "count": number}],
      "evolutionDescription": "string",
      "futureOutlook": "string"
    }
  ]
}`;

    onProgress?.({
      stage: 'Extracting methodologies',
      progress: 30,
      message: 'Analyzing research methods...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 5000,
        temperature: 0.3
      });

      onProgress?.({
        stage: 'Processing analysis',
        progress: 80,
        message: 'Comparing methodologies...'
      });

      const result = this.parseJSONResponse(response);
      
      const methodologyAnalysis: MethodologyAnalysis = {
        id: crypto.randomUUID(),
        documentIds: documents.map(d => d.id),
        methods: result.methods?.map((method: any) => ({
          id: crypto.randomUUID(),
          name: method.name,
          category: method.category,
          description: method.description,
          advantages: method.advantages || [],
          disadvantages: method.disadvantages || [],
          usageCount: method.usageCount || 0,
          successRate: method.successRate,
          sampleSizes: [],
          tools: method.tools || [],
          datasets: method.datasets || [],
          validationApproaches: method.validationApproaches || [],
          reproducibility: method.reproducibility || 'medium',
          documents: method.documents || []
        })) || [],
        comparison: result.comparison || [],
        recommendations: result.recommendations || [],
        trends: result.trends || [],
        generatedAt: new Date()
      };

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Methodology analysis completed'
      });

      return methodologyAnalysis;
    } catch (error) {
      console.error('Error analyzing methodologies:', error);
      throw error;
    }
  }

  /**
   * Generate a comprehensive document summary with Q&A capability
   */
  async generateDocumentSummary(
    document: ResearchDocument,
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<string> {
    const prompt = `Create a comprehensive summary of this research document:

Title: ${document.title}
Authors: <AUTHORS>
Year: ${document.publicationYear}
Abstract: ${document.abstract}

Key Findings: ${document.keyFindings.join('; ')}
Methodology: ${document.methodology.join('; ')}
Limitations: ${document.limitations.join('; ')}

Content Sections:
${document.sections.map(section => `${section.title}: ${section.content.substring(0, 500)}...`).join('\n\n')}

Please provide:
1. Executive summary (150-200 words)
2. Main contributions and novelty
3. Research methodology overview
4. Key results and findings
5. Implications and significance
6. Limitations and future work
7. Relevance to current research trends

Make the summary comprehensive yet accessible, suitable for quick understanding by researchers.`;

    onProgress?.({
      stage: 'Generating summary',
      progress: 50,
      message: 'Analyzing document content...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 2000,
        temperature: 0.3
      });

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Summary generated successfully'
      });

      return response;
    } catch (error) {
      console.error('Error generating document summary:', error);
      return 'Error generating summary. Please try again.';
    }
  }

  /**
   * Answer questions about documents
   */
  async answerQuestion(
    question: string,
    documents: ResearchDocument[],
    onProgress?: (progress: GenerationProgress) => void,
    model: string = 'google/gemini-2.5-flash'
  ): Promise<string> {
    const documentContext = documents.map(doc => ({
      title: doc.title,
      authors: doc.authors,
      year: doc.publicationYear,
      abstract: doc.abstract,
      keyFindings: doc.keyFindings,
      methodology: doc.methodology,
      sections: doc.sections.map(s => ({ title: s.title, content: s.content.substring(0, 1000) }))
    }));

    const prompt = `Answer the following question based on the provided research documents:

Question: ${question}

Research Documents:
${JSON.stringify(documentContext, null, 2)}

Please provide:
1. A comprehensive answer to the question
2. Supporting evidence from the documents
3. Specific citations where relevant
4. Any limitations or uncertainties in the answer
5. Related insights or implications

Be accurate and cite specific documents when making claims.`;

    onProgress?.({
      stage: 'Processing question',
      progress: 30,
      message: 'Analyzing documents for relevant information...'
    });

    try {
      const response = await this.callAI(prompt, {
        model: model,
        maxTokens: 2000,
        temperature: 0.2
      });

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Question answered successfully'
      });

      return response;
    } catch (error) {
      console.error('Error answering question:', error);
      return 'Error processing question. Please try again.';
    }
  }

  /**
   * Auto-analyze an academic paper from a PDF file
   * This method is triggered automatically when a PDF is uploaded
   * @param file The PDF file to analyze
   */
  async autoAnalyzePaperFromPdf(file: File): Promise<ResearchDocument> {
    try {
      console.log(`Auto-analyzing academic paper: ${file.name}`);
      
      // Extract text from the PDF
      const content = await this.extractTextFromFile(file);
      
      // Create a processing document as a placeholder
      const processingDoc: ResearchDocument = {
        id: crypto.randomUUID(),
        title: file.name,
        authors: [],
        abstract: 'Analyzing paper content...',
        publicationYear: new Date().getFullYear(),
        keywords: [],
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date(),
        lastModified: new Date(),
        status: 'processing',
        processingProgress: 10,
        sections: [],
        rawContent: content.substring(0, 500) + '...',
        metadata: {
          extractedAt: new Date(),
          confidence: 0,
          sources: ['pdf-extract'],
          language: 'english'
        },
        keyFindings: [],
        methodology: [],
        limitations: [],
        futureWork: [],
        tags: [],
        categories: [],
        favorite: false,
        notes: ''
      };
      
      // Publish processing document for immediate display
      this.notifyDocumentProcessing?.(processingDoc);
      
      // Now perform detailed analysis
      return this.analyzeAcademicPaper(file, content);
    } catch (error) {
      console.error('Auto-analysis failed:', error);
      throw new Error(`PDF analysis failed: ${error.message}`);
    }
  }
  
  /**
   * Perform in-depth analysis of an academic paper
   * Extracts structured information like methodology, results, limitations, etc.
   */
  async analyzeAcademicPaper(file: File, content: string): Promise<ResearchDocument> {
    const prompt = `You are an expert academic researcher. Thoroughly analyze this research paper and extract detailed information in JSON format.

Paper content (excerpt): ${content.substring(0, 10000)}

Extract the following information:
1. Title (be precise, remove any non-title text)
2. Authors (full list of authors)
3. Abstract (complete abstract only)
4. Publication year
5. Journal or conference name
6. DOI if available
7. Keywords (extract or generate relevant keywords)
8. Detailed key findings (5+ key findings with explanations)
9. Methodology details (comprehensive analysis of methods used)
10. Results summary (quantitative and qualitative results)
11. Limitations identified in the study (be thorough)
12. Future work suggestions from the paper
13. Achievements and contributions of the paper
14. Document sections with titles, content excerpts, and types
15. Citations and references format

Return in this exact JSON format:
{
  "title": "complete paper title",
  "authors": ["author1", "author2", ...],
  "abstract": "complete abstract text",
  "publicationYear": number,
  "journal": "journal name or null",
  "doi": "doi string or null",
  "keywords": ["keyword1", "keyword2", ...],
  "keyFindings": ["detailed finding 1", "detailed finding 2", ...],
  "methodology": ["method description 1", "method description 2", ...],
  "results": ["result 1", "result 2", ...],
  "limitations": ["limitation 1", "limitation 2", ...],
  "futureWork": ["future direction 1", "future direction 2", ...],
  "achievements": ["achievement 1", "achievement 2", ...],
  "sections": [
    {
      "title": "section title",
      "content": "excerpt of the section content (most relevant parts)",
      "type": "one of [abstract, introduction, literature_review, methodology, results, discussion, conclusion, references]",
      "order": number
    }
  ],
  "citationFormat": "identified citation style (APA, MLA, etc.)",
  "confidence": float (0-1)
}

Be thorough and accurate in your analysis, particularly for methodology, results, and limitations.`;

    try {
      // Update processing status
      this.notifyDocumentProcessingUpdate?.({
        status: 'processing',
        message: 'Analyzing academic paper structure and content...',
        progress: 25
      });
      
      const response = await this.callAI(prompt, {
        model: "google/gemini-2.5-flash",
        maxTokens: 4000,
        temperature: 0.1
      });

      // Update processing status
      this.notifyDocumentProcessingUpdate?.({
        status: 'processing',
        message: 'Finalizing paper analysis and extracting insights...',
        progress: 75
      });

      const analysisResult = this.parseJSONResponse(response);
      
      // Create the final analyzed document
      const analyzedDocument: ResearchDocument = {
        id: crypto.randomUUID(),
        title: analysisResult.title || file.name,
        authors: analysisResult.authors || [],
        abstract: analysisResult.abstract || '',
        publicationYear: analysisResult.publicationYear || new Date().getFullYear(),
        journal: analysisResult.journal,
        doi: analysisResult.doi,
        keywords: analysisResult.keywords || [],
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date(),
        lastModified: new Date(),
        status: 'ready',
        sections: analysisResult.sections?.map((section: any, index: number) => ({
          id: crypto.randomUUID(),
          title: section.title || `Section ${index + 1}`,
          content: section.content || '',
          type: section.type || this.inferSectionType(section.title || `Section ${index + 1}`),
          order: section.order || index,
          wordCount: section.content ? section.content.split(' ').length : 0
        })) || [],
        rawContent: content,
        metadata: {
          extractedAt: new Date(),
          confidence: analysisResult.confidence || 0.8,
          sources: ['ai-analysis', 'pdf-extract'],
          language: analysisResult.language || 'english',
          citationCount: analysisResult.citations?.length
        },
        summary: analysisResult.abstract,
        keyFindings: analysisResult.keyFindings || [],
        methodology: analysisResult.methodology || [],
        limitations: analysisResult.limitations || [],
        futureWork: analysisResult.futureWork || [],
        tags: analysisResult.keywords || [],
        categories: [],
        favorite: false,
        notes: `Auto-analyzed on ${new Date().toLocaleString()}`
      };
      
      // Store additional data if available
      if (analysisResult.results || analysisResult.achievements) {
        analyzedDocument.notes += '\n\n--- ADDITIONAL ANALYSIS ---\n\n';
        
        if (analysisResult.results?.length > 0) {
          analyzedDocument.notes += '\nRESULTS:\n' + analysisResult.results.map((r: string) => `- ${r}`).join('\n');
        }
        
        if (analysisResult.achievements?.length > 0) {
          analyzedDocument.notes += '\n\nACHIEVEMENTS:\n' + analysisResult.achievements.map((a: string) => `- ${a}`).join('\n');
        }
      }

      // Final update - analysis complete
      this.notifyDocumentProcessingUpdate?.({
        status: 'ready',
        message: 'Analysis complete!',
        progress: 100
      });

      return analyzedDocument;
    } catch (error) {
      console.error('Error analyzing academic paper:', error);
      
      // Create an error document
      const errorDocument: ResearchDocument = {
        id: crypto.randomUUID(),
        title: `${file.name} (Analysis Failed)`,
        authors: [],
        abstract: `Analysis error: ${error.message}. Please try again or analyze manually.`,
        publicationYear: new Date().getFullYear(),
        keywords: [],
        filename: file.name,
        fileSize: file.size,
        uploadedAt: new Date(),
        lastModified: new Date(),
        status: 'error',
        sections: [{
          id: 'error_section',
          title: 'Error',
          content: `Failed to analyze document: ${error.message}`,
          type: 'other',
          order: 0,
          wordCount: 0
        }],
        rawContent: content.substring(0, 1000) + '...',
        metadata: {
          extractedAt: new Date(),
          confidence: 0,
          sources: ['error'],
          language: 'english'
        },
        keyFindings: [],
        methodology: [],
        limitations: [],
        futureWork: [],
        tags: [],
        categories: [],
        favorite: false,
        notes: `Error occurred during analysis: ${error.message}`
      };
      
      // Notify of error status
      this.notifyDocumentProcessingUpdate?.({
        status: 'error',
        message: `Analysis failed: ${error.message}`,
        progress: 0
      });
      
      throw error;
    }
  }
  
  // Notification callbacks for document processing
  
  /**
   * Set callbacks for document processing notifications
   * @param onProcessingStart Called when processing starts with initial document
   * @param onProcessingUpdate Called with status updates during processing
   */
  setProcessingCallbacks(
    onProcessingStart: (document: ResearchDocument) => void,
    onProcessingUpdate: (update: { status: string, message: string, progress: number }) => void
  ) {
    this.notifyDocumentProcessing = onProcessingStart;
    this.notifyDocumentProcessingUpdate = onProcessingUpdate;
  }

  /**
   * Helper Methods
   */
  private async callAI(prompt: string, options: AIGenerationOptions = {}): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Paper Genius - Research Analysis'
      },
      body: JSON.stringify({
        model: options.model || "google/gemini-2.5-flash",
        messages: [
          {
            role: "system",
            content: "You are an expert research analyst AI specializing in academic paper analysis, literature reviews, and research methodology. Provide accurate, detailed, and scholarly responses."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.3,
        top_p: options.topP || 0.9,
        frequency_penalty: options.frequencyPenalty || 0,
        presence_penalty: options.presencePenalty || 0
      })
    });

    if (!response.ok) {
      throw new Error(`AI API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No response generated';
  }

  private parseJSONResponse(response: string): any {
    try {
      // First attempt: Try direct parse if it's clean JSON
      if (response.trim().startsWith('{') && response.trim().endsWith('}')) {
        try {
          return JSON.parse(response);
        } catch (directError) {
          console.warn('Direct JSON parse failed, trying alternative methods');
        }
      }
      
      // Second attempt: Extract JSON from code block
      const codeBlockMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch && codeBlockMatch[1]) {
        try {
          return JSON.parse(codeBlockMatch[1]);
        } catch (codeBlockError) {
          console.warn('Found JSON in code block but parsing failed:', codeBlockError);
        }
      }
      
      // Third attempt: Match the broadest JSON-like pattern
      const jsonMatch = response.match(/(\{[\s\S]*\})/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (innerError) {
          console.warn('Found JSON-like pattern but parsing failed:', innerError);
        }
      }

      // Fourth attempt: Try to fix common JSON issues and parse
      let fixedJson = response
        .replace(/^[^{]*/, '') // Remove everything before first {
        .replace(/[^}]*$/, '') // Remove everything after last }
        .replace(/\\"/g, '"') // Fix escaped quotes
        .replace(/(\w+):/g, '"$1":') // Add quotes to keys without quotes
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays
      
      try {
        return JSON.parse(fixedJson);
      } catch (fixedError) {
        console.warn('Fixed JSON parsing failed:', fixedError);
      }
      
      // Last resort: Create a default structure as fallback
      console.error('All JSON parsing attempts failed, using fallback structure');
      console.log('Original response:', response.substring(0, 500) + '...');
      
      // Return a minimal valid structure to prevent further errors
      return {
        title: "Parsing Error: Could not extract document structure",
        authors: ["Unknown Author"],
        abstract: "The document could not be properly analyzed. Please try another document or format.",
        publicationYear: new Date().getFullYear(),
        keywords: ["error", "parsing-failed"],
        sections: [
          {
            title: "Error Information",
            content: "Failed to parse AI response. Raw content is available in notes.",
            type: "error",
            order: 0
          }
        ]
      };
    } catch (error) {
      console.error('Critical error in parseJSONResponse:', error);
      console.log('Response that caused error:', response?.substring(0, 300) + '...');
      
      // Return minimal structure instead of throwing to prevent cascade failures
      return {
        title: "Critical Error in Document Analysis",
        authors: ["Unknown Author"],
        abstract: "A critical error occurred during document analysis: " + (error.message || "unknown error"),
        publicationYear: new Date().getFullYear(),
        sections: []
      };
    }
  }

private async extractTextFromFile(file: File): Promise<string> {
  const fileType = file.type.toLowerCase();
  const fileName = file.name.toLowerCase();
  
  try {
    // Handle different file types
    if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      return await this.extractPDFText(file);
    } else if (
      fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      fileName.endsWith('.docx')
    ) {
      return await this.extractDocxText(file);
    } else if (
      fileType === 'application/msword' ||
      fileName.endsWith('.doc')
    ) {
      return await this.extractDocText(file);
    } else if (
      fileType === 'text/plain' ||
      fileName.endsWith('.txt')
    ) {
      return await this.extractPlainText(file);
    } else {
      // Fallback: try to read as plain text
      console.warn(`Unsupported file type: ${fileType}, attempting plain text extraction`);
      return await this.extractPlainText(file);
    }
  } catch (error) {
    console.error('Error extracting text from file:', error);
    throw new Error(`Failed to extract text from ${file.name}: ${error.message}`);
  }
}

private async extractPlainText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      resolve(text || '');
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}

private async extractPDFText(file: File): Promise<string> {
  try {
    // Import pdfjs-dist
    const pdfjsLib = await import('pdfjs-dist');
    
    // Configure the worker source - using the existing worker file in public directory
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';
    
    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument(arrayBuffer);
    const pdfDoc = await loadingTask.promise;
    
    let fullText = '';
    const numPages = pdfDoc.numPages;
    
    // Extract text from each page with improved error handling
    for (let i = 1; i <= numPages; i++) {
      try {
        const page = await pdfDoc.getPage(i);
        const textContent = await page.getTextContent();
        
        // Validate textContent before processing
        if (textContent && Array.isArray(textContent.items)) {
          const pageText = textContent.items
            .map((item: any) => (item && 'str' in item ? item.str : ''))
            .filter(Boolean)
            .join(' ');
          fullText += `\n--- Page ${i} ---\n${pageText}\n`;
        } else {
          fullText += `\n--- Page ${i} ---\n[No extractable text content found]\n`;
        }
      } catch (pageError) {
        console.warn(`Error extracting text from page ${i}:`, pageError);
        fullText += `\n--- Page ${i} ---\n[Error extracting text: ${pageError.message}]\n`;
      }
    }
    
    if (!fullText || fullText.trim().length < 50) {
      return `[PDF Document: ${file.name}]

This PDF document appears to be image-based or contains no extractable text.
For scanned documents, OCR (Optical Character Recognition) would be required.

File Information:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Pages: ${numPages || 'Unknown'}
- Type: PDF Document

Please try uploading a text-based PDF or convert this document to a text format.`;
    }
    
    // Return extracted text with metadata
    return `[PDF Document: ${file.name}]
Pages: ${numPages}
File Size: ${(file.size / 1024 / 1024).toFixed(2)} MB

--- EXTRACTED CONTENT ---

${fullText}`;
    
  } catch (error: any) {
    console.error('PDF extraction error:', error);
    
    // Fallback to placeholder content if extraction fails
    return `[PDF Document: ${file.name}]

PDF text extraction encountered an error: ${error.message}

This could be due to:
- Password-protected PDF
- Corrupted file
- Unsupported PDF format
- Resource limitations

File Information:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB

Please try a different PDF document or convert it to a supported format.`;
  }
}

private async extractDocxText(file: File): Promise<string> {
  try {
    // For now, we'll simulate DOCX extraction  
    // In a real implementation, you'd use mammoth.js or similar library
    console.warn('DOCX text extraction not fully implemented. Using placeholder content.');
    
    return `[Word Document: ${file.name}]

This is a Microsoft Word document that requires specialized text extraction.
In a production environment, this would be extracted using document parsing libraries.

File details:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Type: Word Document (.docx)

To properly analyze this document, please:
1. Convert it to text format, or
2. Use a document conversion service
3. Or implement mammoth.js for client-side extraction

For now, you can continue with the analysis using this placeholder, 
but the AI analysis will be limited without the actual content.`;
  } catch (error) {
    throw new Error(`DOCX extraction failed: ${error.message}`);
  }
}

private async extractDocText(file: File): Promise<string> {
  try {
    // Legacy .doc files are more complex to parse
    console.warn('DOC text extraction not implemented. Using placeholder content.');
    
    return `[Legacy Word Document: ${file.name}]

This is a legacy Microsoft Word document (.doc) that requires specialized text extraction.
Legacy .doc files use a binary format that's complex to parse in browsers.

File details:
- Name: ${file.name}
- Size: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Type: Legacy Word Document (.doc)

Recommendations:
1. Convert to .docx or .txt format for better compatibility
2. Use a server-side conversion service
3. Or upload the document in text format

For now, you can continue with the analysis using this placeholder, 
but the AI analysis will be limited without the actual content.`;
  } catch (error) {
    throw new Error(`DOC extraction failed: ${error.message}`);
  }
}

private inferSectionType(title: string | null | undefined): string {
  // Handle null or undefined title
  if (!title) {
    return 'other';
  }
  
  const titleLower = title.toLowerCase();
  
  if (titleLower.includes('abstract')) return 'abstract';
  if (titleLower.includes('introduction')) return 'introduction';
  if (titleLower.includes('literature') || titleLower.includes('review')) return 'literature_review';
  if (titleLower.includes('method')) return 'methodology';
  if (titleLower.includes('result')) return 'results';
  if (titleLower.includes('discussion')) return 'discussion';
  if (titleLower.includes('conclusion')) return 'conclusion';
  if (titleLower.includes('reference')) return 'references';
  if (titleLower.includes('appendix')) return 'appendix';
  
  return 'other';
}
}

export const researchAnalysisService = new ResearchAnalysisService();
export default researchAnalysisService;
