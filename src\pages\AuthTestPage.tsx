import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, User, Mail, Key } from 'lucide-react';
import { toast } from 'sonner';

const AuthTestPage = () => {
  const [user, setUser] = useState<any>(null);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>({});
  
  // Test credentials
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Password123!');

  useEffect(() => {
    checkAuthStatus();
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      setSession(session);
      setUser(session?.user || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      
      setSession(session);
      setUser(session?.user || null);
      
      setTestResults(prev => ({
        ...prev,
        sessionCheck: { success: true, message: session ? 'Active session found' : 'No active session' }
      }));
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        sessionCheck: { success: false, message: error.message }
      }));
    }
  };

  const testEmailLogin = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      setTestResults(prev => ({
        ...prev,
        emailLogin: { success: true, message: `Logged in as ${data.user.email}` }
      }));
      
      toast.success('Email login successful!');
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        emailLogin: { success: false, message: error.message }
      }));
      toast.error(`Email login failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testGoogleLogin = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;

      setTestResults(prev => ({
        ...prev,
        googleLogin: { success: true, message: 'Google OAuth initiated' }
      }));
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        googleLogin: { success: false, message: error.message }
      }));
      toast.error(`Google login failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogout = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      setTestResults(prev => ({
        ...prev,
        logout: { success: true, message: 'Logged out successfully' }
      }));
      
      toast.success('Logout successful!');
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        logout: { success: false, message: error.message }
      }));
      toast.error(`Logout failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const TestResult = ({ test, result }: { test: string; result: any }) => {
    if (!result) return null;
    
    return (
      <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50">
        {result.success ? (
          <CheckCircle className="w-4 h-4 text-green-600" />
        ) : (
          <XCircle className="w-4 h-4 text-red-600" />
        )}
        <span className="text-sm font-medium">{test}:</span>
        <span className="text-sm text-gray-600">{result.message}</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Authentication Test Page</h1>
          <p className="text-gray-600">Test email/password and Google OAuth authentication</p>
        </div>

        {/* Current Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Current Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-2">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Authenticated
                </Badge>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Provider:</strong> {user.app_metadata?.provider || 'email'}</p>
                <p><strong>Last Sign In:</strong> {new Date(user.last_sign_in_at).toLocaleString()}</p>
              </div>
            ) : (
              <div className="space-y-2">
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  Not Authenticated
                </Badge>
                <p>No active session found</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Email/Password Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Email/Password Authentication Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password123!"
                />
              </div>
            </div>
            
            <Button 
              onClick={testEmailLogin} 
              disabled={loading}
              className="w-full"
            >
              Test Email Login
            </Button>
            
            <TestResult test="Email Login" result={testResults.emailLogin} />
          </CardContent>
        </Card>

        {/* Google OAuth Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="w-5 h-5" />
              Google OAuth Authentication Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">Configuration Required</span>
              </div>
              <p className="text-sm text-yellow-700">
                Google OAuth must be configured in Supabase dashboard before this test will work.
              </p>
            </div>
            
            <Button 
              onClick={testGoogleLogin} 
              disabled={loading}
              className="w-full"
              variant="outline"
            >
              Test Google OAuth Login
            </Button>
            
            <TestResult test="Google OAuth" result={testResults.googleLogin} />
          </CardContent>
        </Card>

        {/* Logout Test */}
        {user && (
          <Card>
            <CardHeader>
              <CardTitle>Logout Test</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testLogout} 
                disabled={loading}
                variant="destructive"
                className="w-full"
              >
                Test Logout
              </Button>
              
              <TestResult test="Logout" result={testResults.logout} />
            </CardContent>
          </Card>
        )}

        {/* Test Results Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <TestResult test="Session Check" result={testResults.sessionCheck} />
            <TestResult test="Email Login" result={testResults.emailLogin} />
            <TestResult test="Google OAuth" result={testResults.googleLogin} />
            <TestResult test="Logout" result={testResults.logout} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthTestPage;
