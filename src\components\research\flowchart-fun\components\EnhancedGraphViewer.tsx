/**
 * Enhanced Graph Viewer Component
 * Advanced Cytoscape-based graph visualization with FFTheme support
 */

import React, { useRef, useEffect, useState } from 'react';
import cytoscape, { Core, ElementDefinition } from 'cytoscape';
import dagre from 'cytoscape-dagre';
import cose from 'cytoscape-cose-bilkent';
import { FlowchartGraph, FFTheme, LayoutName } from '../types';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ZoomIn,
  ZoomOut,
  Maximize,
  RotateCcw,
  Download,
  Move3D,
  Loader2
} from 'lucide-react';

// Register cytoscape extensions
cytoscape.use(dagre);
cytoscape.use(cose);

interface EnhancedGraphViewerProps {
  graph: FlowchartGraph;
  theme: FFTheme;
  className?: string;
  onNodeClick?: (node: any) => void;
  onEdgeClick?: (edge: any) => void;
  onBackgroundClick?: () => void;
  onZoomChange?: (zoom: number) => void;
  onLayoutComplete?: () => void;
  interactive?: boolean;
  showControls?: boolean;
  showStats?: boolean;
}

const EnhancedGraphViewer: React.FC<EnhancedGraphViewerProps> = ({
  graph,
  theme,
  className = '',
  onNodeClick,
  onEdgeClick,
  onBackgroundClick,
  onZoomChange,
  onLayoutComplete,
  interactive = true,
  showControls = true,
  showStats = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cyRef = useRef<Core | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(1);
  const [stats, setStats] = useState({ nodes: 0, edges: 0 });

  // Convert FFTheme to Cytoscape styles
  const getStyleFromTheme = (theme: FFTheme) => {
    return [
      {
        selector: 'node',
        style: {
          'background-color': theme.nodeBackground,
          'border-color': theme.borderColor,
          'border-width': theme.borderWidth,
          'label': 'data(label)',
          'text-valign': 'center',
          'text-halign': 'center',
          'font-family': theme.fontFamily,
          'font-size': '12px',
          'color': theme.nodeForeground,
          'text-wrap': 'wrap',
          'text-max-width': `${theme.textMaxWidth}px`,
          'padding': `${theme.padding}px`,
          'shape': theme.shape,
          'line-height': theme.lineHeight,
          'text-margin-y': theme.textMarginY,
          ...(theme.useFixedHeight && { 'height': `${theme.fixedHeight}px` }),
        },
      },
      {
        selector: 'edge',
        style: {
          'width': theme.edgeWidth,
          'line-color': theme.edgeColor,
          'target-arrow-color': theme.edgeColor,
          'source-arrow-color': theme.edgeColor,
          'target-arrow-shape': theme.targetArrowShape,
          'source-arrow-shape': theme.sourceArrowShape,
          'curve-style': theme.curveStyle,
          'label': 'data(label)',
          'font-size': `${theme.edgeTextSize}px`,
          'font-family': theme.fontFamily,
          'text-rotation': theme.rotateEdgeLabel ? 'autorotate' : 'none',
          'text-margin-y': -10,
          'source-distance-from-node': theme.sourceDistanceFromNode,
          'target-distance-from-node': theme.targetDistanceFromNode,
          'arrow-scale': theme.arrowScale,
        },
      },
      {
        selector: 'node:selected',
        style: {
          'border-color': '#3b82f6',
          'border-width': Math.max(theme.borderWidth + 1, 3),
          'overlay-color': '#3b82f6',
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'edge:selected',
        style: {
          'line-color': '#3b82f6',
          'target-arrow-color': '#3b82f6',
          'source-arrow-color': '#3b82f6',
          'width': theme.edgeWidth + 1,
          'overlay-color': '#3b82f6',
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'node:hover',
        style: {
          'overlay-color': theme.borderColor,
          'overlay-opacity': 0.1,
        },
      },
      {
        selector: 'edge:hover',
        style: {
          'overlay-color': theme.edgeColor,
          'overlay-opacity': 0.1,
        },
      },
    ];
  };

  // Get layout configuration from theme
  const getLayoutFromTheme = (theme: FFTheme) => {
    const baseConfig = {
      spacingFactor: theme.spacingFactor,
      nodeDimensionsIncludeLabels: true,
      animate: true,
      animationDuration: 500,
    };

    switch (theme.layoutName) {
      case 'dagre':
        return {
          name: 'dagre',
          rankDir: theme.direction === 'DOWN' ? 'TB' :
                   theme.direction === 'UP' ? 'BT' :
                   theme.direction === 'RIGHT' ? 'LR' : 'RL',
          ...baseConfig,
        };
      case 'cose':
        return {
          name: 'cose-bilkent',
          idealEdgeLength: 100 * theme.spacingFactor,
          nodeRepulsion: 4500 * theme.spacingFactor,
          ...baseConfig,
        };
      case 'breadthfirst':
        return {
          name: 'breadthfirst',
          directed: true,
          spacingFactor: theme.spacingFactor,
          ...baseConfig,
        };
      case 'concentric':
        return {
          name: 'concentric',
          spacingFactor: theme.spacingFactor,
          ...baseConfig,
        };
      case 'circle':
        return {
          name: 'circle',
          spacingFactor: theme.spacingFactor,
          ...baseConfig,
        };
      case 'grid':
        return {
          name: 'grid',
          spacingFactor: theme.spacingFactor,
          ...baseConfig,
        };
      default:
        return {
          name: 'dagre',
          rankDir: 'TB',
          ...baseConfig,
        };
    }
  };

  // Initialize and update cytoscape
  useEffect(() => {
    if (!containerRef.current || !graph) return;

    setIsLoading(true);

    try {
      // Clean up existing instance
      if (cyRef.current) {
        cyRef.current.destroy();
      }

      // Convert graph to Cytoscape elements
      const elements: ElementDefinition[] = [
        ...graph.nodes.map(node => ({
          data: {
            id: node.id,
            label: node.label,
            ...node.data,
          },
          classes: node.classes?.join(' ') || '',
          style: node.style || {},
        })),
        ...graph.edges.map(edge => ({
          data: {
            id: edge.id,
            source: edge.source,
            target: edge.target,
            label: edge.label || '',
            ...edge.data,
          },
          classes: edge.classes?.join(' ') || '',
          style: edge.style || {},
        })),
      ];

      // Get styles and layout from theme
      const styles = getStyleFromTheme(theme);
      const layout = getLayoutFromTheme(theme);

      // Initialize Cytoscape
      const cy = cytoscape({
        container: containerRef.current,
        elements,
        style: styles,
        layout,
        userZoomingEnabled: interactive,
        userPanningEnabled: interactive,
        boxSelectionEnabled: interactive,
        selectionType: interactive ? 'single' : 'none',
        autoungrabify: !interactive,
        wheelSensitivity: 0.2,
        minZoom: 0.1,
        maxZoom: 3,
      });

      // Set background color from theme
      if (theme?.background) {
        cy.style().selector('core').style('background-color', theme.background).update();
      }

      // Event handlers
      if (interactive) {
        cy.on('tap', 'node', (evt) => {
          const node = evt.target;
          const nodeData = graph.nodes.find(n => n.id === node.id());
          if (nodeData && onNodeClick) {
            onNodeClick(nodeData);
          }
        });

        cy.on('tap', 'edge', (evt) => {
          const edge = evt.target;
          const edgeData = graph.edges.find(e => e.id === edge.id());
          if (edgeData && onEdgeClick) {
            onEdgeClick(edgeData);
          }
        });

        cy.on('tap', (evt) => {
          if (evt.target === cy && onBackgroundClick) {
            onBackgroundClick();
          }
        });

        // Zoom change handler
        if (onZoomChange) {
          cy.on('zoom', () => {
            const zoom = cy.zoom();
            setCurrentZoom(zoom);
            onZoomChange(zoom);
          });
        }
      }

      // Layout complete handler
      if (onLayoutComplete) {
        cy.one('layoutstop', () => {
          onLayoutComplete();
        });
      }

      // Update stats
      setStats({
        nodes: graph.nodes.length,
        edges: graph.edges.length,
      });

      cyRef.current = cy;
      setIsLoading(false);
    } catch (err) {
      console.error('Error rendering graph:', err);
      setIsLoading(false);
    }

    return () => {
      if (cyRef.current) {
        cyRef.current.destroy();
        cyRef.current = null;
      }
    };
  }, [graph, theme, interactive, onNodeClick, onEdgeClick, onBackgroundClick, onZoomChange, onLayoutComplete]);

  // Control handlers
  const handleZoomIn = () => {
    if (cyRef.current) {
      cyRef.current.zoom(cyRef.current.zoom() * 1.2);
    }
  };

  const handleZoomOut = () => {
    if (cyRef.current) {
      cyRef.current.zoom(cyRef.current.zoom() * 0.8);
    }
  };

  const handleFit = () => {
    if (cyRef.current) {
      cyRef.current.fit(undefined, 50);
    }
  };

  const handleCenter = () => {
    if (cyRef.current) {
      cyRef.current.center();
    }
  };

  const handleReset = () => {
    if (cyRef.current) {
      cyRef.current.reset();
    }
  };

  const handleExportImage = async () => {
    if (!cyRef.current) return;

    try {
      const png = cyRef.current.png({
        output: 'blob',
        bg: theme.background,
        full: true,
        scale: 2
      });

      const url = URL.createObjectURL(png);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'flowchart.png';
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className={`relative h-full ${className}`}>
      {/* Graph container */}
      <div 
        ref={containerRef} 
        className="w-full h-full border border-gray-200 rounded-lg"
        style={{ 
          backgroundColor: theme.background,
          minHeight: '400px'
        }}
      />
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Rendering flowchart...</span>
          </div>
        </div>
      )}
      
      {/* Controls */}
      {showControls && (
        <Card className="absolute top-4 right-4 p-2">
          <div className="flex flex-col space-y-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomIn}
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomOut}
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFit}
              title="Fit to View"
            >
              <Maximize className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCenter}
              title="Center"
            >
              <Move3D className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              title="Reset View"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <div className="w-full h-px bg-gray-200 my-1" />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExportImage}
              title="Export as PNG"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      )}

      {/* Stats */}
      {showStats && (
        <Card className="absolute bottom-4 left-4 p-2">
          <div className="flex items-center gap-2 text-xs">
            <Badge variant="outline">{stats.nodes} nodes</Badge>
            <Badge variant="outline">{stats.edges} edges</Badge>
            <Badge variant="outline">{Math.round(currentZoom * 100)}% zoom</Badge>
          </div>
        </Card>
      )}

      {/* Empty state */}
      {!graph.nodes.length && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="text-lg font-medium mb-2">No flowchart to display</div>
            <div className="text-sm">Start typing in the editor to create your flowchart</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedGraphViewer;
