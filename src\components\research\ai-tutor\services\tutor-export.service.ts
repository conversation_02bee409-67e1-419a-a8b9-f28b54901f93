/**
 * Tutor Export Service
 * Handles exporting tutoring sessions to various formats
 */

import { TutorSession, TutorExportOptions } from '../types';

class TutorExportService {
  
  /**
   * Export a tutoring session to the specified format
   */
  async exportSession(session: TutorSession, options: TutorExportOptions): Promise<void> {
    const content = this.generateExportContent(session, options);
    
    switch (options.format) {
      case 'txt':
        this.downloadAsText(content, this.generateFilename(session, 'txt'));
        break;
      case 'html':
        this.downloadAsHTML(content, this.generateFilename(session, 'html'));
        break;
      case 'pdf':
        // For now, we'll export as HTML and let the user print to PDF
        // In the future, this could be enhanced with a PDF library
        this.downloadAsHTML(content, this.generateFilename(session, 'html'));
        break;
      case 'docx':
        // For now, we'll export as HTML which can be opened in Word
        // In the future, this could be enhanced with a DOCX library
        this.downloadAsHTML(content, this.generateFilename(session, 'html'));
        break;
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Generate export content based on the session and options
   */
  private generateExportContent(session: TutorSession, options: TutorExportOptions): string {
    const sections: string[] = [];

    // Header
    sections.push(`# ${session.title}`);
    sections.push(`**Topic:** ${session.topic}`);
    sections.push(`**Education Level:** ${session.educationLevel.replace('-', ' ')}`);
    sections.push(`**Date:** ${new Date(session.createdAt).toLocaleDateString()}`);
    sections.push('');

    // Metadata
    if (options.includeMetadata && session.metadata) {
      sections.push('## Session Information');
      sections.push(`- Total Messages: ${session.metadata.totalMessages}`);
      sections.push(`- Duration: ${session.metadata.duration} minutes`);
      sections.push(`- Difficulty: ${session.metadata.difficulty}`);
      if (session.metadata.concepts.length > 0) {
        sections.push(`- Concepts Covered: ${session.metadata.concepts.join(', ')}`);
      }
      sections.push('');
    }

    // Messages
    if (options.includeMessages) {
      sections.push('## Conversation');
      sections.push('');
      
      session.messages.forEach((message, index) => {
        const timestamp = new Date(message.timestamp).toLocaleTimeString();
        const role = message.role === 'user' ? 'Student' : 'AI Tutor';
        
        sections.push(`### ${role} (${timestamp})`);
        sections.push(message.content);
        sections.push('');
      });
    }

    // Sources
    if (options.includeSources && session.sources.length > 0) {
      sections.push('## Sources and References');
      sections.push('');
      
      session.sources.forEach((source, index) => {
        sections.push(`${index + 1}. **${source.title}**`);
        sections.push(`   - URL: ${source.url}`);
        sections.push(`   - Type: ${source.type}`);
        sections.push(`   - Relevance: ${Math.round(source.relevanceScore * 100)}%`);
        if (source.snippet) {
          sections.push(`   - Summary: ${source.snippet}`);
        }
        sections.push('');
      });
    }

    // Progress
    if (options.includeProgress && session.metadata) {
      sections.push('## Learning Progress');
      sections.push(`This session covered ${session.metadata.totalMessages} exchanges over ${session.metadata.duration} minutes.`);
      if (session.metadata.concepts.length > 0) {
        sections.push(`Key concepts learned: ${session.metadata.concepts.join(', ')}`);
      }
      sections.push('');
    }

    // Footer
    sections.push('---');
    sections.push('*Generated by AI Tutor - Personalized Learning Assistant*');

    return sections.join('\n');
  }

  /**
   * Generate filename for export
   */
  private generateFilename(session: TutorSession, extension: string): string {
    const date = new Date(session.createdAt).toISOString().split('T')[0];
    const topic = session.topic.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
    return `ai_tutor_${topic}_${date}.${extension}`;
  }

  /**
   * Download content as text file
   */
  private downloadAsText(content: string, filename: string): void {
    const blob = new Blob([content], { type: 'text/plain' });
    this.downloadBlob(blob, filename);
  }

  /**
   * Download content as HTML file
   */
  private downloadAsHTML(content: string, filename: string): void {
    const htmlContent = this.convertMarkdownToHTML(content);
    const fullHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tutor Session Export</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 { color: #2563eb; }
        h1 { border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
        .metadata { background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .message { margin: 20px 0; padding: 15px; border-left: 4px solid #2563eb; background: #f8fafc; }
        .source { margin: 10px 0; padding: 10px; background: #f0f9ff; border-radius: 6px; }
        a { color: #2563eb; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
    
    const blob = new Blob([fullHTML], { type: 'text/html' });
    this.downloadBlob(blob, filename);
  }

  /**
   * Convert basic markdown to HTML
   */
  private convertMarkdownToHTML(markdown: string): string {
    return markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(.*)$/gm, '<p>$1</p>')
      .replace(/<p><\/p>/g, '')
      .replace(/<p>(<h[1-6]>.*<\/h[1-6]>)<\/p>/g, '$1')
      .replace(/<p>(<ul>.*<\/ul>)<\/p>/g, '$1');
  }

  /**
   * Download blob as file
   */
  private downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'txt', label: 'Text (.txt)', description: 'Plain text format' },
      { value: 'html', label: 'HTML (.html)', description: 'Web page format' },
      { value: 'pdf', label: 'PDF (.pdf)', description: 'Portable document format (via HTML)' },
      { value: 'docx', label: 'Word (.docx)', description: 'Microsoft Word format (via HTML)' }
    ];
  }
}

export const tutorExportService = new TutorExportService();
export default tutorExportService;
