/**
 * Deep Research Service
 * Handles comprehensive academic research with step-by-step outline generation and execution
 */

import {
  DeepResearchSession,
  DeepResearchOutline,
  ResearchSection,
  DeepResearchProgress,
  CompletedSection,
  DeepResearchOptions,
  ResearchReference,
  TavilySearchResult,
  SearchSource,
  Citation,
  ResearchType,
  ResearchTypeTemplate,
  ResearchPlan,
  ResearchMetadata
} from '../types';
import { tavilySearchService } from './tavily-search.service';
import { researchAIService } from './research-ai.service';
import { researchPlanningService } from './research-planning.service';
import { referenceManagementService } from './reference-management.service';
import { academicFormattingService } from './academic-formatting.service';

export class DeepResearchService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found for deep research');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Get available research types
   */
  getResearchTypes(): ResearchTypeTemplate[] {
    return researchPlanningService.getResearchTypeTemplates();
  }

  /**
   * Analyze query and suggest research types
   */
  async analyzeQueryForResearchType(query: string, model: string): Promise<ResearchTypeTemplate[]> {
    return await researchPlanningService.analyzeQueryAndSuggestType(query, model);
  }

  /**
   * Generate intelligent outline based on research type
   */
  async generateIntelligentOutline(
    query: string,
    researchType: ResearchType,
    model: string = 'google/gemini-2.0-flash-001'
  ): Promise<DeepResearchOutline> {
    if (!this.isConfigured()) {
      throw new Error('Deep research service not properly configured');
    }

    try {
      return await researchPlanningService.generateIntelligentOutline(query, researchType, model);
    } catch (error: any) {
      console.error('Intelligent outline generation error:', error);
      throw error;
    }
  }

  /**
   * Generate research outline from user query (legacy method)
   */
  async generateOutline(
    query: string,
    model: string = 'google/gemini-2.0-flash-001',
    options: Partial<DeepResearchOptions> = {}
  ): Promise<DeepResearchOutline> {
    // Use default research type if not specified
    const researchTypes = this.getResearchTypes();
    const defaultType = researchTypes.find(t => t.id === 'literature_review')?.config || researchTypes[0].config;

    return this.generateIntelligentOutline(query, defaultType, model);
  }

  /**
   * Execute enhanced deep research with intelligent assistants
   */
  async executeDeepResearch(
    session: DeepResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<DeepResearchSession> {
    if (!this.isConfigured()) {
      throw new Error('Deep research service not properly configured');
    }

    // Clear reference manager for fresh start
    referenceManagementService.clear();

    const updatedSession = { ...session };
    updatedSession.status = 'researching';
    updatedSession.completedPoints = [];
    updatedSession.allReferences = [];

    // Clear reference manager for new session
    referenceManagementService.clear();

    // Initialize AI assistants based on actual number of points
    updatedSession.aiAssistants = this.createAIAssistants(session.outline.points);

    // Initialize research metadata
    updatedSession.researchMetadata = {
      totalWordCount: 0,
      totalCitations: 0,
      academicSources: 0,
      governmentSources: 0,
      averageSourceQuality: 0,
      researchDuration: 0,
      sectionsCompleted: 0,
      keyTopics: [],
      confidenceScore: 0
    };

    const startTime = Date.now();

    try {
      // Process each point sequentially with dedicated AI assistant
      for (let i = 0; i < session.outline.points.length; i++) {
        const point = session.outline.points[i];
        const assistant = updatedSession.aiAssistants[i % updatedSession.aiAssistants.length]; // Cycle through assistants if more points than assistants

        // Update current point
        updatedSession.currentPoint = i + 1;

        // Activate assistant
        assistant.status = 'active';
        assistant.startTime = new Date();
        assistant.currentTask = `Researching: ${point.title}`;

        // Create progress entry
        const progressEntry: DeepResearchProgress = {
          pointNumber: i + 1,
          pointId: point.id,
          pointTitle: point.title,
          assistantId: assistant.id,
          assistantName: assistant.name,
          status: 'searching',
          message: `${assistant.name} is researching: ${point.title}`,
          progress: 0,
          startTime: new Date()
        };

        if (onProgress) {
          onProgress(progressEntry);
        }

        // Execute research for this point
        const completedPoint = await this.researchPoint(
          point,
          assistant,
          options,
          (stepProgress, currentSubpoint) => {
            progressEntry.progress = stepProgress;
            progressEntry.currentSubpoint = currentSubpoint;
            progressEntry.message = `${assistant.name}: ${currentSubpoint || point.title} (${stepProgress}%)`;
            assistant.progress = stepProgress;
            assistant.currentTask = currentSubpoint || `Researching: ${point.title}`;

            if (onProgress) {
              onProgress(progressEntry);
            }
          }
        );

        // Mark point as completed
        progressEntry.status = 'completed';
        progressEntry.progress = 100;
        progressEntry.endTime = new Date();
        progressEntry.message = `${assistant.name} completed: ${point.title}`;

        assistant.status = 'completed';
        assistant.endTime = new Date();
        assistant.progress = 100;
        assistant.currentTask = 'Completed';

        updatedSession.completedPoints.push(completedPoint);

        // Update metadata
        updatedSession.researchMetadata.totalWordCount += completedPoint.wordCount;
        updatedSession.researchMetadata.sectionsCompleted++;

        // Process sources through reference manager
        completedPoint.sources.forEach(source => {
          referenceManagementService.addSource(source, point.id);
        });

        if (onProgress) {
          onProgress(progressEntry);
        }

        // Increased delay between points to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 5000));
      }

      // Calculate research duration
      updatedSession.researchMetadata.researchDuration = Math.round((Date.now() - startTime) / 60000);

      // Generate consolidated references
      if (options.generateConsolidatedReferences) {
        updatedSession.consolidatedReferences = referenceManagementService.generateConsolidatedReferences(options.citationStyle);
      }

      // Generate final comprehensive report
      updatedSession.finalReport = await this.generateFinalReport(updatedSession, options);

      // Generate formatted version
      if (updatedSession.finalReport) {
        updatedSession.formattedReport = academicFormattingService.formatForAcademicOutput(updatedSession.finalReport);
      }

      // Update final metadata
      const allReferences = referenceManagementService.getAllReferences();
      updatedSession.allReferences = allReferences.map(ref => ({
        id: ref.id,
        title: ref.source.title,
        authors: [], // Could be enhanced to extract authors
        url: ref.source.url,
        domain: ref.source.domain,
        publishedDate: ref.source.publishedDate,
        type: ref.source.type,
        citationCount: ref.usageCount,
        isAcademic: ref.source.isPeerReviewed || ref.source.type === 'academic'
      }));

      updatedSession.researchMetadata.totalCitations = allReferences.length;
      updatedSession.researchMetadata.academicSources = allReferences.filter(r => r.source.isPeerReviewed).length;
      updatedSession.researchMetadata.governmentSources = allReferences.filter(r => r.source.isGovernment).length;
      updatedSession.researchMetadata.averageSourceQuality = allReferences.reduce((sum, r) => sum + r.source.qualityScore, 0) / allReferences.length || 0;
      updatedSession.researchMetadata.confidenceScore = this.calculateConfidenceScore(updatedSession);

      updatedSession.status = 'completed';
      updatedSession.updatedAt = new Date();

      return updatedSession;

    } catch (error: any) {
      console.error('Deep research execution error:', error);
      updatedSession.status = 'error';
      throw error;
    }
  }

  /**
   * Create specialized AI assistants for research points
   */
  private createAIAssistants(points: any[]): AIAssistant[] {
    const assistantNames = [
      { name: "Dr. Research", specialization: "Research Methodology & Foundation" },
      { name: "Prof. Analysis", specialization: "Data Analysis & Interpretation" },
      { name: "Dr. Scholar", specialization: "Academic Literature Review" },
      { name: "Prof. Synthesis", specialization: "Information Synthesis" },
      { name: "Dr. Expert", specialization: "Subject Matter Expertise" },
      { name: "Prof. Critic", specialization: "Critical Analysis & Evaluation" },
      { name: "Dr. Innovation", specialization: "Innovation & Future Trends" },
      { name: "Prof. Context", specialization: "Historical & Contextual Analysis" },
      { name: "Dr. Evidence", specialization: "Evidence-Based Research" },
      { name: "Prof. Conclusion", specialization: "Synthesis & Conclusions" },
      { name: "Dr. Policy", specialization: "Policy Analysis & Government Sources" },
      { name: "Prof. Theory", specialization: "Theoretical Framework Development" },
      { name: "Dr. Methods", specialization: "Research Methods & Validation" },
      { name: "Prof. Impact", specialization: "Impact Assessment & Implications" },
      { name: "Dr. Future", specialization: "Future Directions & Recommendations" }
    ];

    // Create assistants up to the number of points, but at least 5
    const numAssistants = Math.max(5, Math.min(points.length, assistantNames.length));

    return Array.from({ length: numAssistants }, (_, index) => ({
      id: `assistant_${Date.now()}_${index}`,
      assistantNumber: index + 1,
      name: assistantNames[index].name,
      specialization: assistantNames[index].specialization,
      assignedPointId: points[index % points.length]?.id || `point_${index}`,
      status: 'waiting' as const,
      progress: 0,
      currentTask: 'Waiting to start research'
    }));
  }

  /**
   * Calculate confidence score for research session
   */
  private calculateConfidenceScore(session: DeepResearchSession): number {
    let score = 0;
    let factors = 0;

    // Source quality factor (30%)
    if (session.researchMetadata.averageSourceQuality > 0) {
      score += session.researchMetadata.averageSourceQuality * 30;
      factors++;
    }

    // Academic sources factor (25%)
    const academicRatio = session.researchMetadata.totalCitations > 0
      ? session.researchMetadata.academicSources / session.researchMetadata.totalCitations
      : 0;
    score += academicRatio * 25;
    factors++;

    // Completion factor (20%)
    const completionRatio = session.outline.totalPoints > 0
      ? session.researchMetadata.sectionsCompleted / session.outline.totalPoints
      : 0;
    score += completionRatio * 20;
    factors++;

    // Word count factor (15%)
    const targetWords = session.researchType?.totalWordTarget || 5000;
    const wordRatio = Math.min(session.researchMetadata.totalWordCount / targetWords, 1);
    score += wordRatio * 15;
    factors++;

    // Citation density factor (10%)
    const citationDensity = session.researchMetadata.totalWordCount > 0
      ? session.researchMetadata.totalCitations / (session.researchMetadata.totalWordCount / 1000)
      : 0;
    const normalizedDensity = Math.min(citationDensity / 5, 1); // Normalize to 5 citations per 1000 words
    score += normalizedDensity * 10;
    factors++;

    return factors > 0 ? Math.round(score / factors * 100) / 100 : 0;
  }

  /**
   * Research a single point with dedicated AI assistant
   */
  private async researchPoint(
    point: any,
    assistant: AIAssistant,
    options: DeepResearchOptions,
    onProgress?: (progress: number, currentSubpoint?: string) => void
  ): Promise<CompletedPoint> {
    const allSearchResults: TavilySearchResult[] = [];
    const allSources: SearchSource[] = [];
    const subpointResults: SubpointResult[] = [];

    // Generate search queries for the main point and subpoints
    const searchQueries = await this.generateSearchQueries(point);

    if (onProgress) {
      onProgress(10, "Generating search queries");
    }

    // Research each subpoint
    for (let i = 0; i < point.subpoints.length; i++) {
      const subpoint = point.subpoints[i];
      const subpointQueries = searchQueries.filter((_, index) =>
        index >= i * 2 && index < (i + 1) * 2
      );

      if (onProgress) {
        onProgress(20 + (i * 15), `Researching: ${subpoint.title}`);
      }

      const subpointSources: SearchSource[] = [];
      const subpointInsights: string[] = [];

      // Search for each query related to this subpoint
      for (const query of subpointQueries) {
        try {
          // Add delay between searches to avoid rate limiting
          if (subpointQueries.indexOf(query) > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          // Check if Tavily is available
          if (tavilySearchService.isConfigured()) {
            const result = await tavilySearchService.searchAcademic(query, {
              maxResults: Math.min(options.maxResultsPerSection || 6, 5), // Limit results
              searchDepth: 'basic', // Use basic instead of comprehensive
              includeImages: false
            });

            allSearchResults.push(result);
            const sources = tavilySearchService.transformToSearchSources(result.results);
            allSources.push(...sources);
            subpointSources.push(...sources);

            // Extract key insights from search results
            if (result.answer) {
              subpointInsights.push(result.answer);
            }
          } else {
            // Fallback: create mock result without search
            const fallbackResult = {
              query,
              answer: `Research analysis for ${query} (using AI knowledge)`,
              results: []
            };
            allSearchResults.push(fallbackResult);
            subpointInsights.push(`Analysis of ${query} based on expert knowledge.`);
          }

        } catch (error) {
          console.warn(`Search failed for query: ${query}`, error);

          // Create a fallback result for failed searches
          const fallbackResult = {
            query,
            answer: `Research information for ${query} (search temporarily unavailable)`,
            results: []
          };
          allSearchResults.push(fallbackResult);
          subpointInsights.push(`Information about ${query} will be included based on AI knowledge.`);
        }
      }

      subpointResults.push({
        subpointId: subpoint.id,
        title: subpoint.title,
        searchQueries: subpointQueries,
        sources: subpointSources,
        keyInsights: subpointInsights
      });
    }

    if (onProgress) {
      onProgress(80, "Analyzing and writing content");
    }

    // Generate comprehensive content for this point
    const content = await this.generatePointContent(
      point,
      subpointResults,
      allSearchResults,
      assistant,
      options
    );

    if (onProgress) {
      onProgress(95, "Finalizing content");
    }

    // Process content through reference manager for enhanced citation tracking
    const { processedContent, citations } = referenceManagementService.extractAndLinkCitations(
      content,
      allSources,
      point.id
    );

    const keyFindings = this.extractKeyFindings(processedContent);

    if (onProgress) {
      onProgress(100, "Completed");
    }

    return {
      pointId: point.id,
      pointNumber: point.pointNumber,
      title: point.title,
      content: processedContent,
      wordCount: processedContent.split(' ').length,
      sources: allSources,
      citations,
      completedAt: new Date(),
      searchQueries,
      keyFindings,
      assistantId: assistant.id,
      subpointResults
    };
  }

  /**
   * Generate comprehensive search queries for a research point
   */
  private async generateSearchQueries(point: any): Promise<string[]> {
    // Enhanced base queries for comprehensive coverage
    const baseQueries = [
      `${point.title} research`,
      `${point.title} academic study`,
      `${point.title} literature review`,
      `${point.title} systematic review`,
      `${point.title} meta analysis`,
      `${point.title} recent developments`,
      `${point.title} current trends`,
      `${point.title} methodology`,
      `${point.title} theoretical framework`,
      `${point.title} empirical evidence`
    ];

    // Add subpoint-specific queries for detailed coverage
    const subpointQueries: string[] = [];
    point.subpoints.forEach((sub: any) => {
      subpointQueries.push(
        `${sub.title} ${point.title}`,
        `${sub.title} research`,
        `${sub.title} academic literature`,
        `${sub.title} case study`,
        `${sub.title} evidence`
      );
    });

    // Add academic-specific search terms
    const academicQueries = [
      `"${point.title}" peer reviewed`,
      `"${point.title}" journal article`,
      `"${point.title}" academic paper`,
      `"${point.title}" scholarly research`,
      `"${point.title}" university study`
    ];

    // Add temporal queries for comprehensive coverage
    const temporalQueries = [
      `${point.title} 2020-2024`,
      `${point.title} latest research`,
      `${point.title} future directions`,
      `${point.title} historical perspective`
    ];

    // Combine all queries
    const allQueries = [...baseQueries, ...subpointQueries, ...academicQueries, ...temporalQueries]
      .map(query => query.replace(/['"]/g, '').trim())
      .filter(query => query.length > 0 && query.length < 150)
      .slice(0, 20); // Increased to 20 queries for comprehensive coverage

    return allQueries.length > 0 ? allQueries : [
      `${point.title}`,
      `${point.title} research`,
      `${point.title} academic study`,
      `${point.title} literature review`
    ];
  }

  /**
   * Research a single section
   */
  private async researchSection(
    section: ResearchSection,
    options: DeepResearchOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompletedSection> {
    const searchResults: TavilySearchResult[] = [];
    const allSources: SearchSource[] = [];
    const allCitations: Citation[] = [];

    // Search for each query in the section
    for (let i = 0; i < section.searchQueries.length; i++) {
      const query = section.searchQueries[i];
      
      if (onProgress) {
        onProgress(Math.round((i / section.searchQueries.length) * 50));
      }

      try {
        const result = await tavilySearchService.searchAcademic(query, {
          maxResults: Math.max(options.maxResultsPerSection || 15, 15), // Increased minimum to 15
          searchDepth: options.searchDepth || 'comprehensive',
          includeImages: options.includeImages || false
        });

        searchResults.push(result);
        
        // Transform and add sources
        const sources = tavilySearchService.transformToSearchSources(result.results);
        allSources.push(...sources);

      } catch (error) {
        console.warn(`Search failed for query: ${query}`, error);
      }
    }

    if (onProgress) {
      onProgress(60);
    }

    // Generate content for this section
    const content = await this.generateSectionContent(
      section,
      searchResults,
      options
    );

    if (onProgress) {
      onProgress(80);
    }

    // Extract citations from content
    const citations = this.extractSectionCitations(content, allSources);

    if (onProgress) {
      onProgress(100);
    }

    return {
      sectionId: section.id,
      title: section.title,
      content,
      wordCount: content.split(' ').length,
      sources: allSources,
      citations,
      completedAt: new Date(),
      searchQueries: section.searchQueries,
      keyFindings: this.extractKeyFindings(content)
    };
  }

  /**
   * Generate content for a specific point using AI assistant
   */
  private async generatePointContent(
    point: any,
    subpointResults: SubpointResult[],
    searchResults: TavilySearchResult[],
    assistant: AIAssistant,
    options: DeepResearchOptions
  ): Promise<string> {
    const prompt = this.buildPointContentPrompt(point, subpointResults, searchResults, assistant, options);

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Deep Research Platform'
      },
      body: JSON.stringify({
        model: options.model,
        messages: [
          {
            role: 'system',
            content: this.getPointSystemPrompt(assistant, options)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 8000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Build point content generation prompt
   */
  private buildPointContentPrompt(
    point: any,
    subpointResults: SubpointResult[],
    searchResults: TavilySearchResult[],
    assistant: AIAssistant,
    options: DeepResearchOptions
  ): string {
    let prompt = `As ${assistant.name}, an expert in ${assistant.specialization}, write a comprehensive analysis for:

RESEARCH POINT ${point.pointNumber}: ${point.title}
Description: ${point.description}

SUBPOINTS TO COVER:
${point.subpoints.map((sub: any, index: number) =>
  `${index + 1}. ${sub.title}: ${sub.description}`
).join('\n')}`;

    // Add research findings if available
    if (subpointResults.length > 0 && subpointResults.some(sr => sr.sources.length > 0)) {
      prompt += `\n\nRESEARCH FINDINGS:
`;
      subpointResults.forEach((subResult, index) => {
        prompt += `\n--- Subpoint ${index + 1}: ${subResult.title} ---\n`;
        if (subResult.keyInsights.length > 0) {
          prompt += `Key Insights: ${subResult.keyInsights.join(' | ')}\n`;
        }
        prompt += `Sources Found: ${subResult.sources.length}\n`;

        subResult.sources.slice(0, 2).forEach((source, sourceIndex) => {
          prompt += `Source ${sourceIndex + 1}: ${source.title} (${source.domain})\n`;
          if (source.snippet) {
            prompt += `Content: ${source.snippet.substring(0, 200)}...\n`;
          }
        });
      });
    } else {
      prompt += `\n\nNOTE: Limited search results available. Use your expert knowledge to provide comprehensive analysis.`;
    }

    prompt += `\n\nWRITING REQUIREMENTS:
- Write ${options.minWordCountPerSection || 800}-${options.maxWordCountPerSection || 1500} words
- Use your specialization in ${assistant.specialization}
- Include proper academic citations in ${options.citationStyle || 'APA'} format
- Cover all subpoints thoroughly
- Provide evidence-based analysis using your expert knowledge
- Include specific examples and data where available
- Maintain scholarly tone and rigor
- Structure with clear headings for each subpoint
- If search results are limited, rely on your comprehensive knowledge base

Write the complete analysis now:`;

    return prompt;
  }

  /**
   * Get system prompt for point content generation
   */
  private getPointSystemPrompt(assistant: AIAssistant, options: DeepResearchOptions): string {
    return `You are ${assistant.name}, a distinguished academic expert specializing in ${assistant.specialization}.

Your expertise includes:
- Advanced research methodology
- Critical analysis and evaluation
- Evidence-based writing
- Academic citation standards
- Comprehensive literature synthesis

Writing Style:
- Scholarly and authoritative tone
- Clear, well-structured arguments
- Proper use of ${options.citationStyle || 'APA'} citations
- Evidence-based conclusions
- Professional academic language

Your task is to write comprehensive, well-researched content that demonstrates deep understanding and expert analysis of the assigned research point.`;
  }

  /**
   * Generate content for a specific section
   */
  private async generateSectionContent(
    section: any,
    searchResults: TavilySearchResult[],
    options: DeepResearchOptions
  ): Promise<string> {
    const prompt = this.buildSectionPrompt(section, searchResults, options);
    
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Deep Research Platform'
      },
      body: JSON.stringify({
        model: options.model,
        messages: [
          {
            role: 'system',
            content: this.getSectionSystemPrompt(options)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 8000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle rate limiting with retry
      if (response.status === 429) {
        console.warn('Rate limit hit, waiting before retry...');
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        throw new Error(`Rate limit exceeded. Please wait a moment and try again.`);
      }

      throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Generate final comprehensive report with consolidated references
   */
  private async generateFinalReport(
    session: DeepResearchSession,
    options: DeepResearchOptions
  ): Promise<string> {
    const prompt = this.buildFinalReportPrompt(session, options);

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Deep Research Platform'
      },
      body: JSON.stringify({
        model: options.model,
        messages: [
          {
            role: 'system',
            content: this.getFinalReportSystemPrompt(options)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 12000,
        temperature: 0.6,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    let finalReport = data.choices?.[0]?.message?.content || '';

    // Append consolidated references if enabled
    if (options.generateConsolidatedReferences) {
      // Ensure all sources from completed points are in reference manager
      session.completedPoints.forEach(point => {
        point.sources.forEach(source => {
          referenceManagementService.addSource(source, point.pointId);
        });
      });

      const consolidatedRefs = referenceManagementService.generateConsolidatedReferences(options.citationStyle);
      if (consolidatedRefs && consolidatedRefs.trim()) {
        finalReport += '\n\n' + consolidatedRefs;
      } else {
        // Fallback: Generate references from session sources
        finalReport += '\n\n' + this.generateFallbackReferences(session, options.citationStyle);
      }
    }

    return finalReport;
  }

  /**
   * Build outline generation prompt (NO SEARCH - Pure AI outline)
   */
  private buildOutlinePrompt(query: string, options: Partial<DeepResearchOptions>): string {
    return `Create a comprehensive research outline for the following research query. DO NOT search online or reference specific sources. Use your knowledge to create a logical, structured outline.

Research Query: "${query}"

Create EXACTLY 10 main points that thoroughly cover this topic. Each point should have 2-4 subpoints for detailed coverage.

Requirements:
- 10 main points (no more, no less)
- 2-4 subpoints under each main point
- Clear, specific point titles
- Logical flow from basic concepts to advanced analysis
- Academic structure suitable for detailed research
- Each point should be researchable and substantial

Format as JSON:
{
  "title": "Research Title",
  "description": "Brief description of research scope",
  "points": [
    {
      "pointNumber": 1,
      "title": "Main Point Title",
      "description": "What this point will cover",
      "subpoints": [
        {
          "subpointNumber": 1,
          "title": "Subpoint Title",
          "description": "Specific focus area"
        },
        {
          "subpointNumber": 2,
          "title": "Subpoint Title",
          "description": "Specific focus area"
        }
      ]
    }
  ]
}

Focus on creating a logical progression that builds understanding from fundamentals to complex analysis. Make each point substantial enough for detailed research and writing.`;
  }

  /**
   * Build enhanced section content generation prompt
   */
  private buildSectionPrompt(
    section: ResearchSection,
    searchResults: TavilySearchResult[],
    options: DeepResearchOptions
  ): string {
    const citationTarget = options.researchType?.citationDensity === 'heavy' ? 15 :
                          options.researchType?.citationDensity === 'moderate' ? 10 : 8;

    let prompt = `Write a comprehensive, high-quality academic section for: "${section.title}"

Section Description: ${section.description}

CRITICAL ACADEMIC REQUIREMENTS:
- Write ${options.minWordCountPerSection || 800}-${options.maxWordCountPerSection || 1500} words of substantive academic content
- Include AT LEAST ${citationTarget} in-text citations from the provided sources
- Use ${options.citationStyle || 'apa'} citation format consistently
- Write at the ${options.researchType?.academicLevel || 'graduate'} academic level
- Provide deep analysis, not just summary
- Include specific data, statistics, and research findings
- Synthesize information from multiple sources
- Identify research gaps and contradictions where relevant
- Use formal academic language and structure

CONTENT STRUCTURE REQUIREMENTS:
- Start with a clear topic sentence introducing the section
- Develop 3-4 main arguments or themes with supporting evidence
- Include subsection headings if the content is substantial
- Conclude with synthesis and implications
- Ensure logical flow between paragraphs
- Use transitional phrases to connect ideas

CITATION REQUIREMENTS:
- Cite specific findings, not general statements
- Include page numbers or specific data points when available
- Use a variety of sources (don't over-rely on one source)
- Integrate citations naturally into the text
- Support all major claims with evidence
- Include both recent (2020+) and foundational sources

ACADEMIC QUALITY STANDARDS:
- Demonstrate critical thinking and analysis
- Compare and contrast different perspectives
- Identify methodological strengths and limitations
- Discuss implications for theory and practice
- Maintain objectivity while acknowledging debates
- Use precise, discipline-specific terminology

Search Results and Sources to Reference:
`;

    let sourceCount = 0;
    searchResults.forEach((result, index) => {
      prompt += `\n=== SEARCH QUERY ${index + 1}: "${result.query}" ===\n`;
      if (result.answer) {
        prompt += `Research Summary: ${result.answer}\n`;
      }

      result.results.forEach((source, sourceIndex) => {
        sourceCount++;
        prompt += `\n[SOURCE ${sourceCount}] - CITE AS: (${this.extractAuthorYear(source.title, source.published_date)})\n`;
        prompt += `Title: ${source.title}\n`;
        prompt += `URL: ${source.url}\n`;
        prompt += `Published: ${source.published_date || 'Date not specified'}\n`;
        prompt += `Relevance: ${(source.score * 100).toFixed(0)}%\n`;
        prompt += `Content: ${source.content}\n`;
        prompt += `--- Use this source for specific claims and data ---\n`;
      });
    });

    prompt += `\n\nTOTAL SOURCES AVAILABLE: ${sourceCount}

CRITICAL CITATION INSTRUCTIONS:
1. Write comprehensive academic content that thoroughly covers "${section.title}"
2. Use AT LEAST ${citationTarget} citations from the sources above - NO PLACEHOLDER CITATIONS
3. Reference sources ONLY using the exact citation format provided: (Author, Year)
4. NEVER use placeholder citations like [Placeholder Citation 1] or [Citation Needed]
5. ONLY cite sources that are actually provided in the source list above
6. Provide specific examples, data, and research findings from the actual sources
7. Synthesize information across multiple sources to create original insights
8. Maintain high academic standards throughout
9. Ensure every major claim is supported by evidence from the provided sources
10. Write substantive content that demonstrates deep understanding of the topic

EXAMPLE OF CORRECT CITATION FORMAT:
- "Recent studies have shown significant improvements in renewable energy efficiency (Smith, 2023)."
- "According to government data, solar panel adoption has increased by 40% (Department of Energy, 2024)."

FORBIDDEN - DO NOT USE:
- [Placeholder Citation 1]
- [Citation Needed]
- [Source 1]
- Any citation not based on the actual sources provided above

Begin writing the academic section now using ONLY the real sources provided:`;

    return prompt;
  }

  /**
   * Build final report generation prompt
   */
  private buildFinalReportPrompt(
    session: DeepResearchSession,
    options: DeepResearchOptions
  ): string {
    let prompt = `Generate a comprehensive final research report based on the completed research points.

Original Research Query: "${session.originalQuery}"
Research Title: "${session.outline.title}"

COMPLETED RESEARCH POINTS:
`;

    session.completedPoints.forEach((point, index) => {
      prompt += `\n--- Point ${point.pointNumber}: ${point.title} ---\n`;
      prompt += `Word Count: ${point.wordCount}\n`;
      prompt += `Key Findings: ${point.keyFindings.join(', ')}\n`;
      prompt += `AI Assistant: ${session.aiAssistants.find(a => a.id === point.assistantId)?.name}\n`;
      prompt += `Subpoints Covered: ${point.subpointResults.length}\n`;
      prompt += `Content Preview: ${point.content.substring(0, 200)}...\n`;
    });

    prompt += `\n\nFINAL REPORT REQUIREMENTS:

STRUCTURE:
1. Executive Summary (300-400 words)
2. Introduction and Background
3. Comprehensive Analysis (synthesizing all 10 points)
4. Key Findings and Insights
5. Implications and Impact
6. Limitations and Challenges
7. Recommendations
8. Future Directions
9. Conclusion
10. References

CONTENT REQUIREMENTS:
- Synthesize all 10 research points into a cohesive narrative
- Maintain logical flow and transitions between sections
- Include specific data, examples, and evidence from the research
- Provide critical analysis and expert insights
- Use proper academic citations in ${options.citationStyle || 'APA'} format
- Ensure comprehensive coverage of the original research query
- Include cross-references between related points
- Maintain scholarly tone throughout

STATISTICS:
- Total Research Points: ${session.completedPoints.length}
- Total References: ${session.allReferences.length}
- Academic Sources: ${session.allReferences.filter(ref => ref.isAcademic).length}
- Total Word Count: ${session.completedPoints.reduce((total, point) => total + point.wordCount, 0)}

Generate the complete final research report now:`;

    return prompt;
  }

  /**
   * Parse AI outline response
   */
  private parseOutlineResponse(response: string, originalQuery: string): DeepResearchOutline {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in outline response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Generate IDs for points and subpoints
      const points: ResearchPoint[] = parsed.points.map((point: any, index: number) => ({
        id: `point_${Date.now()}_${index}`,
        pointNumber: point.pointNumber || index + 1,
        title: point.title,
        description: point.description,
        status: 'pending' as const,
        subpoints: point.subpoints?.map((sub: any, subIndex: number) => ({
          id: `subpoint_${Date.now()}_${index}_${subIndex}`,
          subpointNumber: sub.subpointNumber || subIndex + 1,
          title: sub.title,
          description: sub.description
        })) || []
      }));

      return {
        id: `outline_${Date.now()}`,
        title: parsed.title || `Research Analysis: ${originalQuery}`,
        description: parsed.description || `Comprehensive 10-point analysis of ${originalQuery}`,
        points,
        totalPoints: points.length,
        createdAt: new Date()
      };

    } catch (error) {
      console.error('Failed to parse outline response:', error);

      // Fallback: create a basic outline
      return this.createFallbackOutline(originalQuery);
    }
  }

  /**
   * Create fallback outline if parsing fails
   */
  private createFallbackOutline(query: string): DeepResearchOutline {
    const points: ResearchPoint[] = [
      {
        id: `point_${Date.now()}_1`,
        pointNumber: 1,
        title: 'Introduction and Background',
        description: 'Overview and foundational context of the research topic',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_1_1`, subpointNumber: 1, title: 'Historical Context', description: 'Historical development and background' },
          { id: `sub_${Date.now()}_1_2`, subpointNumber: 2, title: 'Current Relevance', description: 'Why this topic matters today' }
        ]
      },
      {
        id: `point_${Date.now()}_2`,
        pointNumber: 2,
        title: 'Literature Review and Current State',
        description: 'Review of existing research and current understanding',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_2_1`, subpointNumber: 1, title: 'Academic Research', description: 'Key academic studies and findings' },
          { id: `sub_${Date.now()}_2_2`, subpointNumber: 2, title: 'Current Trends', description: 'Recent developments and trends' }
        ]
      },
      {
        id: `point_${Date.now()}_3`,
        pointNumber: 3,
        title: 'Key Findings and Analysis',
        description: 'Main findings and detailed analysis',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_3_1`, subpointNumber: 1, title: 'Primary Findings', description: 'Most significant discoveries and results' },
          { id: `sub_${Date.now()}_3_2`, subpointNumber: 2, title: 'Data Analysis', description: 'Statistical and analytical insights' }
        ]
      },
      {
        id: `point_${Date.now()}_4`,
        pointNumber: 4,
        title: 'Challenges and Limitations',
        description: 'Identified challenges and limitations in the field',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_4_1`, subpointNumber: 1, title: 'Current Challenges', description: 'Major obstacles and difficulties' },
          { id: `sub_${Date.now()}_4_2`, subpointNumber: 2, title: 'Research Limitations', description: 'Methodological and scope limitations' }
        ]
      },
      {
        id: `point_${Date.now()}_5`,
        pointNumber: 5,
        title: 'Solutions and Innovations',
        description: 'Proposed solutions and innovative approaches',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_5_1`, subpointNumber: 1, title: 'Existing Solutions', description: 'Current approaches and methods' },
          { id: `sub_${Date.now()}_5_2`, subpointNumber: 2, title: 'Innovative Approaches', description: 'New and emerging solutions' }
        ]
      },
      {
        id: `point_${Date.now()}_6`,
        pointNumber: 6,
        title: 'Case Studies and Examples',
        description: 'Real-world applications and case studies',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_6_1`, subpointNumber: 1, title: 'Success Stories', description: 'Successful implementations and outcomes' },
          { id: `sub_${Date.now()}_6_2`, subpointNumber: 2, title: 'Lessons Learned', description: 'Key insights from practical applications' }
        ]
      },
      {
        id: `point_${Date.now()}_7`,
        pointNumber: 7,
        title: 'Impact and Implications',
        description: 'Broader impact and implications of the research',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_7_1`, subpointNumber: 1, title: 'Societal Impact', description: 'Effects on society and communities' },
          { id: `sub_${Date.now()}_7_2`, subpointNumber: 2, title: 'Economic Implications', description: 'Economic effects and considerations' }
        ]
      },
      {
        id: `point_${Date.now()}_8`,
        pointNumber: 8,
        title: 'Future Directions',
        description: 'Future research directions and opportunities',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_8_1`, subpointNumber: 1, title: 'Research Opportunities', description: 'Areas for future investigation' },
          { id: `sub_${Date.now()}_8_2`, subpointNumber: 2, title: 'Emerging Trends', description: 'Future trends and developments' }
        ]
      },
      {
        id: `point_${Date.now()}_9`,
        pointNumber: 9,
        title: 'Recommendations',
        description: 'Practical recommendations and best practices',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_9_1`, subpointNumber: 1, title: 'Best Practices', description: 'Recommended approaches and methods' },
          { id: `sub_${Date.now()}_9_2`, subpointNumber: 2, title: 'Implementation Guidelines', description: 'Practical implementation advice' }
        ]
      },
      {
        id: `point_${Date.now()}_10`,
        pointNumber: 10,
        title: 'Conclusions and Summary',
        description: 'Final conclusions and comprehensive summary',
        status: 'pending',
        subpoints: [
          { id: `sub_${Date.now()}_10_1`, subpointNumber: 1, title: 'Key Takeaways', description: 'Most important insights and conclusions' },
          { id: `sub_${Date.now()}_10_2`, subpointNumber: 2, title: 'Final Thoughts', description: 'Concluding remarks and reflections' }
        ]
      }
    ];

    return {
      id: `outline_${Date.now()}`,
      title: `Comprehensive 10-Point Analysis: ${query}`,
      description: `Detailed academic research covering 10 key aspects of ${query}`,
      points,
      totalPoints: 10,
      createdAt: new Date()
    };
  }

  /**
   * Extract citations from section content
   */
  private extractSectionCitations(content: string, sources: SearchSource[]): Citation[] {
    const citations: Citation[] = [];

    // Enhanced citation extraction - look for various citation patterns
    const citationPatterns = [
      /\(([A-Z][a-z]+(?:\s+et\s+al\.?)?),?\s*(\d{4})\)/g, // (Author, Year) or (Author et al., Year)
      /\(([A-Z][a-z]+(?:\s+&\s+[A-Z][a-z]+)?),?\s*(\d{4})\)/g, // (Author & Author, Year)
      /\[(\d+)\]/g, // [1]
      /\(([^)]+(?:\.org|\.edu|\.gov|\.com)[^)]*)\)/g // (URL citations)
    ];

    // Create a map of sources for better matching
    const sourceMap = new Map();
    sources.forEach((source, index) => {
      // Extract potential author names from title or content
      const titleWords = source.title.split(' ');
      const firstWord = titleWords[0];
      const domain = source.domain || new URL(source.url).hostname;

      sourceMap.set(index + 1, source); // For numbered citations [1], [2], etc.
      sourceMap.set(firstWord.toLowerCase(), source);
      sourceMap.set(domain.toLowerCase(), source);

      // Try to extract author from title patterns
      const authorMatch = source.title.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/);
      if (authorMatch) {
        sourceMap.set(authorMatch[1].toLowerCase(), source);
      }
    });

    citationPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const citationText = match[0];
        const position = match.index;
        let matchingSource = null;

        // Try different matching strategies
        if (match[1] && match[2]) {
          // Author, Year format
          const author = match[1].toLowerCase().replace(/\s+et\s+al\.?/, '').replace(/\s+&.*/, '').trim();
          matchingSource = sourceMap.get(author);
        } else if (match[1] && /^\d+$/.test(match[1])) {
          // Numbered citation [1]
          const num = parseInt(match[1]);
          matchingSource = sourceMap.get(num);
        } else if (match[1] && (match[1].includes('.') || match[1].includes('http'))) {
          // URL or domain citation
          const domain = match[1].toLowerCase();
          for (const [key, source] of sourceMap.entries()) {
            if (typeof key === 'string' && domain.includes(key)) {
              matchingSource = source;
              break;
            }
          }
        }

        // If we found a matching source, create citation
        if (matchingSource) {
          citations.push({
            id: `citation_${Date.now()}_${position}`,
            text: citationText,
            sourceId: matchingSource.id,
            url: matchingSource.url,
            title: matchingSource.title,
            position,
            author: this.extractAuthorFromSource(matchingSource),
            year: this.extractYearFromSource(matchingSource)
          });
        } else {
          // Create citation even without perfect match, using available sources
          const fallbackSource = sources[citations.length % sources.length];
          if (fallbackSource) {
            citations.push({
              id: `citation_${Date.now()}_${position}`,
              text: citationText,
              sourceId: fallbackSource.id,
              url: fallbackSource.url,
              title: fallbackSource.title,
              position,
              author: this.extractAuthorFromSource(fallbackSource),
              year: this.extractYearFromSource(fallbackSource)
            });
          }
        }
      }
    });

    return citations;
  }

  /**
   * Extract author from source
   */
  private extractAuthorFromSource(source: SearchSource): string {
    // Try to extract author from title
    const titleMatch = source.title.match(/^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/);
    if (titleMatch) {
      return titleMatch[1];
    }

    // Fallback to domain
    const domain = source.domain || new URL(source.url).hostname;
    return domain.replace(/^www\./, '').replace(/\.(com|org|edu|gov)$/, '');
  }

  /**
   * Extract year from source
   */
  private extractYearFromSource(source: SearchSource): string {
    // Try to extract year from published date
    if (source.published_date) {
      const year = new Date(source.published_date).getFullYear();
      if (year > 1900 && year <= new Date().getFullYear()) {
        return year.toString();
      }
    }

    // Try to extract year from title or content
    const text = `${source.title} ${source.content || ''}`;
    const yearMatch = text.match(/\b(20[0-2][0-9]|19[8-9][0-9])\b/);
    if (yearMatch) {
      return yearMatch[1];
    }

    // Fallback to current year
    return new Date().getFullYear().toString();
  }

  /**
   * Extract key findings from content
   */
  private extractKeyFindings(content: string): string[] {
    const findings: string[] = [];

    // Look for sentences that indicate findings
    const findingPatterns = [
      /(?:found that|discovered that|revealed that|showed that|demonstrated that|indicated that)\s+([^.]+)/gi,
      /(?:results show|findings indicate|research suggests|studies reveal)\s+([^.]+)/gi,
      /(?:key finding|main result|important discovery)\s*:?\s*([^.]+)/gi
    ];

    findingPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const finding = match[1].trim();
        if (finding.length > 20 && finding.length < 200) {
          findings.push(finding);
        }
      }
    });

    return findings.slice(0, 5); // Return top 5 findings
  }

  /**
   * Get system prompt for outline generation
   */
  private getOutlineSystemPrompt(): string {
    return `You are an expert academic research planner. Your role is to create comprehensive, well-structured research outlines for academic investigations. You excel at:

1. Breaking down complex topics into logical, manageable sections
2. Identifying key research questions and search strategies
3. Structuring research to ensure comprehensive coverage
4. Estimating realistic timeframes for research tasks
5. Creating outlines that lead to high-quality academic reports

Always respond with properly formatted JSON that can be parsed programmatically.`;
  }

  /**
   * Get enhanced system prompt for section content generation
   */
  private getSectionSystemPrompt(options: DeepResearchOptions): string {
    // Enhanced citation requirements - minimum 20 citations per section
    const citationTarget = Math.max(20, options.researchType?.citationDensity === 'heavy' ? 25 :
                          options.researchType?.citationDensity === 'moderate' ? 20 : 20);

    return `You are a distinguished academic writer and researcher with expertise in ${options.researchType?.name || 'academic research'}. You specialize in creating high-quality academic content that meets the standards of top-tier journals and institutions.

CORE COMPETENCIES:
1. Advanced research synthesis and critical analysis
2. Comprehensive literature review methodology
3. Evidence-based academic writing at the ${options.researchType?.academicLevel || 'graduate'} level
4. Expert use of ${options.citationStyle || 'APA'} citation standards
5. Deep subject matter expertise across multiple disciplines

ENHANCED WRITING STANDARDS FOR ${options.researchType?.name?.toUpperCase() || 'ACADEMIC RESEARCH'}:
- Produce MINIMUM 1000-1500 words of substantive academic content per section
- Include MINIMUM ${citationTarget} in-text citations per section from provided sources
- CRITICAL: Each section must be comprehensive and detailed with extensive evidence
- Demonstrate critical thinking through analysis, synthesis, and evaluation
- Present multiple perspectives and acknowledge scholarly debates
- Use discipline-specific terminology and concepts appropriately
- Maintain rigorous academic objectivity while providing insightful analysis

ENHANCED CONTENT QUALITY REQUIREMENTS:
- Every major claim must be supported by credible evidence with specific citations
- Integrate quantitative data, statistics, and research findings with precise details
- Compare and contrast different theoretical frameworks with extensive analysis
- Identify research gaps, limitations, and methodological considerations in detail
- Provide original insights through synthesis of multiple sources
- Use sophisticated academic language and complex sentence structures
- Ensure logical progression of ideas with clear transitions
- Include detailed explanations, examples, and case studies
- Provide comprehensive coverage of all aspects of the topic
- Write in-depth analysis that demonstrates mastery of the subject

ENHANCED CITATION EXCELLENCE (MINIMUM 20 CITATIONS PER SECTION):
- Cite specific findings, data points, and methodological details frequently
- Vary citation placement (beginning, middle, end of sentences) throughout
- Use signal phrases to introduce sources effectively and naturally
- Include page numbers and specific details when available
- Balance recent research (2020+) with foundational studies
- Demonstrate comprehensive knowledge of the literature
- Use parenthetical citations (Author, Year) format consistently
- Ensure every paragraph contains multiple citations
- Support all statistical claims with specific source citations
- Reference methodology and theoretical frameworks extensively

FORMATTING REQUIREMENTS:
- Use proper academic headings and subheadings (bold, no HTML tags)
- Structure content with clear paragraphs and logical flow
- Avoid HTML formatting in output - use plain text with proper formatting
- Ensure headings are bold and properly formatted without HTML tags
- Use bullet points and lists appropriately for academic writing

Your goal is to produce academic content that would be suitable for publication in peer-reviewed journals and meets the highest standards of scholarly writing.`;
  }

  /**
   * Get enhanced system prompt for final report generation
   */
  private getFinalReportSystemPrompt(options: DeepResearchOptions): string {
    return `You are a distinguished academic report writer and research synthesis expert specializing in ${options.researchType?.name || 'comprehensive academic research'}. You have extensive experience in creating publication-quality research documents for ${options.researchType?.academicLevel || 'graduate'} level audiences.

EXPERTISE AREAS:
1. Advanced research synthesis and meta-analysis
2. Comprehensive literature integration and narrative construction
3. Executive summary writing for academic and professional audiences
4. Critical evaluation and gap analysis in research literature
5. Future research direction identification and recommendation development

FINAL REPORT STANDARDS FOR ${options.researchType?.name?.toUpperCase() || 'ACADEMIC RESEARCH'}:
- Target word count: ${options.researchType?.totalWordTarget || 8000} words total
- Academic level: ${options.researchType?.academicLevel || 'graduate'}
- Citation density: ${options.researchType?.citationDensity || 'moderate'} (50+ total references minimum)
- Professional formatting suitable for academic publication or institutional submission

SYNTHESIS REQUIREMENTS:
- Integrate findings from all research sections into a coherent narrative
- Identify patterns, themes, and connections across different sections
- Highlight contradictions, debates, and unresolved questions in the literature
- Provide original insights through cross-sectional analysis
- Demonstrate comprehensive understanding of the research domain
- Present balanced perspective acknowledging multiple viewpoints

STRUCTURAL EXCELLENCE:
- Create compelling executive summary that captures all key insights
- Write engaging introduction that establishes context and significance
- Develop logical flow between sections with sophisticated transitions
- Provide substantive analysis rather than mere summarization
- Include critical evaluation of methodologies and evidence quality
- Conclude with actionable recommendations and future research directions

ACADEMIC RIGOR:
- Maintain consistent ${options.citationStyle || 'APA'} citation format throughout
- Support all major claims with specific evidence from research sections
- Use precise academic language and discipline-specific terminology
- Demonstrate critical thinking through analysis and evaluation
- Ensure objectivity while providing expert interpretation
- Meet publication standards for academic journals or institutional reports

Your goal is to create a comprehensive, publication-ready document that demonstrates mastery of the research topic and provides valuable insights to the academic and professional community.`;
  }

  /**
   * Extract author and year for citation formatting
   */
  private extractAuthorYear(title: string, publishedDate?: string): string {
    // Extract year from published date or title
    const year = publishedDate ?
      publishedDate.match(/\b(19|20)\d{2}\b/)?.[0] || new Date().getFullYear().toString() :
      title.match(/\b(19|20)\d{2}\b/)?.[0] || new Date().getFullYear().toString();

    // Extract potential author from title (first word that looks like a name)
    const words = title.split(' ');
    const potentialAuthor = words.find(word =>
      word.length > 2 &&
      word[0] === word[0].toUpperCase() &&
      !['The', 'A', 'An', 'In', 'On', 'At', 'To', 'For', 'Of', 'With', 'By'].includes(word)
    ) || 'Author';

    return `${potentialAuthor}, ${year}`;
  }

  /**
   * Generate fallback references when reference manager fails
   */
  private generateFallbackReferences(session: DeepResearchSession, citationStyle: string = 'apa'): string {
    const allSources: SearchSource[] = [];

    // Collect all sources from completed points
    session.completedPoints.forEach(point => {
      point.sources.forEach(source => {
        // Avoid duplicates
        if (!allSources.find(s => s.url === source.url)) {
          allSources.push(source);
        }
      });
    });

    if (allSources.length === 0) {
      return `## References\n\n*No sources were collected during the research process.*\n\n`;
    }

    let referenceList = `## References\n\n`;

    // Sort sources by quality and type
    const sortedSources = allSources.sort((a, b) => {
      // Academic sources first
      const aIsAcademic = a.isAcademic || a.isPeerReviewed || false;
      const bIsAcademic = b.isAcademic || b.isPeerReviewed || false;

      if (aIsAcademic && !bIsAcademic) return -1;
      if (!aIsAcademic && bIsAcademic) return 1;

      // Then by title alphabetically
      return a.title.localeCompare(b.title);
    });

    // Generate formatted references
    sortedSources.forEach((source, index) => {
      const formattedRef = this.formatSourceAsReference(source, citationStyle);
      referenceList += `${formattedRef}\n\n`;
    });

    // Add quality assessment
    const academicCount = sortedSources.filter(s => s.isAcademic || s.isPeerReviewed).length;
    const governmentCount = sortedSources.filter(s => s.isGovernment).length;

    referenceList += `---\n\n**Reference Quality Assessment:**\n\n`;
    referenceList += `- **Total References:** ${sortedSources.length}\n`;
    referenceList += `- **Academic/Peer-Reviewed Sources:** ${academicCount} (${((academicCount / sortedSources.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Government/Official Sources:** ${governmentCount} (${((governmentCount / sortedSources.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Other Sources:** ${sortedSources.length - academicCount - governmentCount} (${(((sortedSources.length - academicCount - governmentCount) / sortedSources.length) * 100).toFixed(1)}%)\n`;
    referenceList += `- **Citation Style:** ${citationStyle.toUpperCase()}\n\n`;

    return referenceList;
  }

  /**
   * Format a source as a proper academic reference
   */
  private formatSourceAsReference(source: SearchSource, citationStyle: string = 'apa'): string {
    const author = this.extractAuthorFromSource(source);
    const year = this.extractYearFromSource(source);
    const title = source.title;
    const url = source.url;
    const domain = source.domain || new URL(url).hostname;

    switch (citationStyle.toLowerCase()) {
      case 'apa':
        return `${author} (${year}). *${title}*. Retrieved from ${url}`;

      case 'mla':
        return `${author}. "${title}." *${domain}*, ${year}, ${url}.`;

      case 'chicago':
        return `${author}. "${title}." ${domain}. Accessed ${new Date().toLocaleDateString()}. ${url}.`;

      case 'harvard':
        return `${author} ${year}, '${title}', *${domain}*, viewed ${new Date().toLocaleDateString()}, <${url}>.`;

      default:
        return `${author} (${year}). *${title}*. Retrieved from ${url}`;
    }
  }
}

// Export singleton instance
export const deepResearchService = new DeepResearchService();
