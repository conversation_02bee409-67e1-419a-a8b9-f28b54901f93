/**
 * Prompt Enhancement Dialog Component
 * Allows users to enhance simple prompts into detailed research specifications
 */

import React, { useState, useEffect } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wand2, 
  Clock, 
  BookOpen, 
  Target, 
  Users, 
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  Copy,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { promptEnhancementService, EnhancedPrompt } from '../services/prompt-enhancement.service';
import { ResearchTypeTemplate } from '../types';

interface PromptEnhancementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  originalPrompt: string;
  availableResearchTypes: ResearchTypeTemplate[];
  selectedModel: string;
  onEnhancedPromptAccept: (enhancedPrompt: EnhancedPrompt, selectedResearchType: ResearchTypeTemplate) => void;
}

export function PromptEnhancementDialog({
  isOpen,
  onClose,
  originalPrompt,
  availableResearchTypes,
  selectedModel,
  onEnhancedPromptAccept
}: PromptEnhancementDialogProps) {
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancedPrompt, setEnhancedPrompt] = useState<EnhancedPrompt | null>(null);
  const [selectedResearchType, setSelectedResearchType] = useState<ResearchTypeTemplate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('enhanced');

  useEffect(() => {
    if (isOpen && originalPrompt) {
      enhancePrompt();
    }
  }, [isOpen, originalPrompt]);

  const enhancePrompt = async () => {
    if (!originalPrompt.trim()) return;

    setIsEnhancing(true);
    setError(null);

    try {
      const enhanced = await promptEnhancementService.enhancePrompt(
        originalPrompt,
        selectedResearchType?.id,
        selectedModel
      );

      setEnhancedPrompt(enhanced);

      // Find and select the suggested research type
      const suggestedType = availableResearchTypes.find(
        type => type.id === enhanced.suggestedResearchType
      );
      if (suggestedType) {
        setSelectedResearchType(suggestedType);
      }
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      setError('Failed to enhance prompt. Please try again.');
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleResearchTypeSelect = (type: ResearchTypeTemplate) => {
    setSelectedResearchType(type);
    // Re-enhance with the new research type
    if (enhancedPrompt) {
      enhancePrompt();
    }
  };

  const handleAccept = () => {
    if (enhancedPrompt && selectedResearchType) {
      onEnhancedPromptAccept(enhancedPrompt, selectedResearchType);
      onClose();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatList = (items: string[]) => {
    return items.map((item, index) => (
      <li key={index} className="text-sm text-gray-600 mb-1">
        • {item}
      </li>
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-purple-600" />
            Enhance Your Research Prompt
          </DialogTitle>
          <DialogDescription>
            Transform your simple prompt into a comprehensive research specification for better AI-generated content.
          </DialogDescription>
        </DialogHeader>

        {isEnhancing ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
              <p className="text-gray-600">Analyzing and enhancing your prompt...</p>
              <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={enhancePrompt} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        ) : enhancedPrompt ? (
          <div className="space-y-6">
            {/* Original vs Enhanced Comparison */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Original Prompt</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-800 bg-gray-50 p-3 rounded-md">
                    {originalPrompt}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-green-600 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Enhanced Prompt
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <p className="text-sm text-gray-800 bg-green-50 p-3 rounded-md pr-10">
                      {enhancedPrompt.enhancedPrompt}
                    </p>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute top-2 right-2 h-6 w-6 p-0"
                      onClick={() => copyToClipboard(enhancedPrompt.enhancedPrompt)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Research Type Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Recommended Research Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {availableResearchTypes
                    .filter(type => 
                      type.id.includes('review') || 
                      type.id === enhancedPrompt.suggestedResearchType ||
                      ['literature_review', 'research_paper', 'academic_article'].includes(type.id)
                    )
                    .map((type) => (
                      <div
                        key={type.id}
                        className={cn(
                          "p-3 border rounded-lg cursor-pointer transition-all",
                          selectedResearchType?.id === type.id
                            ? "border-purple-500 bg-purple-50"
                            : "border-gray-200 hover:border-gray-300"
                        )}
                        onClick={() => handleResearchTypeSelect(type)}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <div className={cn("w-2 h-2 rounded-full", `bg-${type.color}-500`)} />
                          <h4 className="font-medium text-sm">{type.name}</h4>
                          {type.id === enhancedPrompt.suggestedResearchType && (
                            <Badge variant="secondary" className="text-xs">Recommended</Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-600">{type.description}</p>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Details Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="enhanced">Enhanced</TabsTrigger>
                <TabsTrigger value="research">Research</TabsTrigger>
                <TabsTrigger value="structure">Structure</TabsTrigger>
                <TabsTrigger value="requirements">Requirements</TabsTrigger>
              </TabsList>

              <TabsContent value="enhanced" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Research Context
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700">{enhancedPrompt.researchContext}</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Target Audience
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Badge variant="outline">{enhancedPrompt.academicLevel}</Badge>
                      <p className="text-sm text-gray-600 mt-2">{enhancedPrompt.estimatedScope}</p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Keywords & Terms</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {enhancedPrompt.keywords.map((keyword, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="research" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium">Research Questions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {formatList(enhancedPrompt.researchQuestions)}
                      </ul>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium">Methodology</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-1">
                        {formatList(enhancedPrompt.methodology)}
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Expected Outcomes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {formatList(enhancedPrompt.expectedOutcomes)}
                    </ul>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="structure" className="space-y-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Recommended Structure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {enhancedPrompt.structureRecommendations.map((section, index) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                          <div className="w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-xs font-medium">
                            {index + 1}
                          </div>
                          <span className="text-sm font-medium">{section}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="requirements" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Time & Scope
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Estimated Time:</span>
                        <span className="text-sm font-medium">{enhancedPrompt.timeEstimate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Word Count:</span>
                        <span className="text-sm font-medium">{enhancedPrompt.wordCountEstimate.toLocaleString()} words</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Citations
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700">{enhancedPrompt.citationRequirements}</p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        ) : null}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleAccept} 
            disabled={!enhancedPrompt || !selectedResearchType}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Use Enhanced Prompt
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
