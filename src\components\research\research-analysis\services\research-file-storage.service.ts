import { supabase } from '@/lib/supabase';

/**
 * Service for managing research document file storage in Supabase Storage
 * Handles file uploads, downloads, and metadata management for research analysis platform
 */
export class ResearchFileStorageService {
  private readonly BUCKET_NAME = 'research-files';
  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly ALLOWED_MIME_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain'
  ];

  /**
   * Check if the storage bucket exists (bucket should be pre-created)
   */
  async checkBucket(): Promise<{ success: boolean; error?: any }> {
    try {
      // Just check if bucket exists, don't try to create it
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.error('Error listing buckets:', listError);
        return { success: false, error: listError };
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);

      if (!bucketExists) {
        console.error(`Storage bucket '${this.BUCKET_NAME}' does not exist. Please create it in Supabase dashboard.`);
        return { success: false, error: new Error(`Storage bucket '${this.BUCKET_NAME}' not found`) };
      }

      return { success: true };
    } catch (error) {
      console.error('Error checking bucket:', error);
      return { success: false, error };
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${this.MAX_FILE_SIZE / 1024 / 1024}MB)`
      };
    }

    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not supported. Allowed types: PDF, Word documents, and text files.`
      };
    }

    // Check file extension as backup
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.pdf', '.docx', '.doc', '.txt'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
      return {
        valid: false,
        error: `File extension is not supported. Allowed extensions: ${allowedExtensions.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Upload a research document file to Supabase Storage
   */
  async uploadResearchFile(
    file: File,
    userId: string,
    documentId?: string
  ): Promise<{ filePath: string | null; error: any }> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { filePath: null, error: new Error(validation.error) };
      }

      // Check bucket exists
      const { success: bucketReady } = await this.checkBucket();
      if (!bucketReady) {
        return { filePath: null, error: new Error('Storage bucket not available') };
      }

      // Generate unique file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop();
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const fileName = `${sanitizedFileName}_${timestamp}.${fileExtension}`;
      const filePath = `${userId}/${documentId || 'temp'}/${fileName}`;

      // Upload file with metadata
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            originalName: file.name,
            uploadedAt: new Date().toISOString(),
            userId: userId,
            documentId: documentId || '',
            fileSize: file.size.toString(),
            mimeType: file.type
          }
        });

      if (error) {
        console.error('Error uploading file:', error);
        return { filePath: null, error };
      }

      console.log(`Successfully uploaded file: ${filePath}`);
      return { filePath: data.path, error: null };
    } catch (error) {
      console.error('Error in uploadResearchFile:', error);
      return { filePath: null, error };
    }
  }

  /**
   * Get a signed URL for downloading a file
   */
  async getFileDownloadUrl(filePath: string, expiresIn: number = 3600): Promise<{ url: string | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn);

      if (error) {
        console.error('Error creating signed URL:', error);
        return { url: null, error };
      }

      return { url: data.signedUrl, error: null };
    } catch (error) {
      console.error('Error in getFileDownloadUrl:', error);
      return { url: null, error };
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(filePath: string): Promise<{ metadata: any | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(filePath.substring(0, filePath.lastIndexOf('/')), {
          search: filePath.substring(filePath.lastIndexOf('/') + 1)
        });

      if (error) {
        console.error('Error getting file metadata:', error);
        return { metadata: null, error };
      }

      const fileInfo = data?.[0];
      if (!fileInfo) {
        return { metadata: null, error: new Error('File not found') };
      }

      return { metadata: fileInfo, error: null };
    } catch (error) {
      console.error('Error in getFileMetadata:', error);
      return { metadata: null, error };
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(filePath: string): Promise<{ success: boolean; error?: any }> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error('Error deleting file:', error);
        return { success: false, error };
      }

      console.log(`Successfully deleted file: ${filePath}`);
      return { success: true };
    } catch (error) {
      console.error('Error in deleteFile:', error);
      return { success: false, error };
    }
  }

  /**
   * Move a file from temp location to permanent location
   */
  async moveFileToDocument(
    tempFilePath: string,
    userId: string,
    documentId: string
  ): Promise<{ newFilePath: string | null; error: any }> {
    try {
      // Generate new file path
      const fileName = tempFilePath.substring(tempFilePath.lastIndexOf('/') + 1);
      const newFilePath = `${userId}/${documentId}/${fileName}`;

      // Move the file
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .move(tempFilePath, newFilePath);

      if (error) {
        console.error('Error moving file:', error);
        return { newFilePath: null, error };
      }

      console.log(`Successfully moved file from ${tempFilePath} to ${newFilePath}`);
      return { newFilePath, error: null };
    } catch (error) {
      console.error('Error in moveFileToDocument:', error);
      return { newFilePath: null, error };
    }
  }

  /**
   * List all files for a user
   */
  async listUserFiles(userId: string): Promise<{ files: any[] | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(userId, {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.error('Error listing user files:', error);
        return { files: null, error };
      }

      return { files: data || [], error: null };
    } catch (error) {
      console.error('Error in listUserFiles:', error);
      return { files: null, error };
    }
  }

  /**
   * Get storage usage statistics for a user
   */
  async getUserStorageStats(userId: string): Promise<{ stats: any | null; error: any }> {
    try {
      const { files, error } = await this.listUserFiles(userId);
      
      if (error || !files) {
        return { stats: null, error };
      }

      const totalFiles = files.length;
      const totalSize = files.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      const fileTypes = files.reduce((types, file) => {
        const ext = file.name.split('.').pop()?.toLowerCase() || 'unknown';
        types[ext] = (types[ext] || 0) + 1;
        return types;
      }, {} as Record<string, number>);

      return {
        stats: {
          totalFiles,
          totalSize,
          totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
          fileTypes,
          lastUpload: files[0]?.created_at || null
        },
        error: null
      };
    } catch (error) {
      console.error('Error in getUserStorageStats:', error);
      return { stats: null, error };
    }
  }
}

// Export singleton instance
export const researchFileStorageService = new ResearchFileStorageService();
