import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  CareerExplorationState, 
  ResumeData, 
  CareerAnalysis, 
  CareerPath,
  CareerVisualizationNode,
  CareerVisualizationEdge
} from '../types';
import { DEFAULT_GENERATION_OPTIONS, VISUALIZATION_CONFIG } from '../constants';

interface CareerExplorerActions {
  // Resume and input management
  setResumeData: (resumeData: ResumeData) => void;
  setAdditionalContext: (context: string) => void;
  setSelectedModel: (model: string) => void;
  
  // Step navigation
  setCurrentStep: (step: CareerExplorationState['currentStep']) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // Career analysis
  setCareerAnalysis: (analysis: CareerAnalysis) => void;
  addCareerPath: (career: CareerPath) => void;
  updateCareerPath: (id: string, updates: Partial<CareerPath>) => void;
  removeCareerPath: (id: string) => void;
  
  // Generation state
  setIsGenerating: (isGenerating: boolean) => void;
  setError: (error: string | null) => void;
  
  // Visualization
  updateVisualizationNodes: (nodes: CareerVisualizationNode[]) => void;
  updateVisualizationEdges: (edges: CareerVisualizationEdge[]) => void;
  generateVisualization: () => void;
  
  // Utility actions
  resetState: () => void;
  clearError: () => void;
}

const initialState: CareerExplorationState = {
  currentStep: 'input',
  resumeData: null,
  additionalContext: '',
  selectedModel: DEFAULT_GENERATION_OPTIONS.model,
  careerAnalysis: null,
  isGenerating: false,
  error: null,
  visualizationNodes: [],
  visualizationEdges: []
};

export const useCareerExplorerStore = create<CareerExplorationState & CareerExplorerActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Resume and input management
      setResumeData: (resumeData: ResumeData) => {
        set({ resumeData, error: null });
      },

      setAdditionalContext: (context: string) => {
        set({ additionalContext: context });
      },

      setSelectedModel: (model: string) => {
        set({ selectedModel: model });
      },

      // Step navigation
      setCurrentStep: (step: CareerExplorationState['currentStep']) => {
        set({ currentStep: step });
      },

      nextStep: () => {
        const { currentStep } = get();
        const steps: CareerExplorationState['currentStep'][] = ['input', 'processing', 'visualization', 'export'];
        const currentIndex = steps.indexOf(currentStep);
        if (currentIndex < steps.length - 1) {
          set({ currentStep: steps[currentIndex + 1] });
        }
      },

      previousStep: () => {
        const { currentStep } = get();
        const steps: CareerExplorationState['currentStep'][] = ['input', 'processing', 'visualization', 'export'];
        const currentIndex = steps.indexOf(currentStep);
        if (currentIndex > 0) {
          set({ currentStep: steps[currentIndex - 1] });
        }
      },

      // Career analysis
      setCareerAnalysis: (analysis: CareerAnalysis) => {
        set({ careerAnalysis: analysis });
        get().generateVisualization();
      },

      addCareerPath: (career: CareerPath) => {
        const { careerAnalysis } = get();
        if (careerAnalysis) {
          const updatedAnalysis = {
            ...careerAnalysis,
            careerPaths: [...careerAnalysis.careerPaths, career]
          };
          set({ careerAnalysis: updatedAnalysis });
          get().generateVisualization();
        }
      },

      updateCareerPath: (id: string, updates: Partial<CareerPath>) => {
        const { careerAnalysis } = get();
        if (careerAnalysis) {
          const updatedPaths = careerAnalysis.careerPaths.map(path =>
            path.id === id ? { ...path, ...updates } : path
          );
          const updatedAnalysis = {
            ...careerAnalysis,
            careerPaths: updatedPaths
          };
          set({ careerAnalysis: updatedAnalysis });
          get().generateVisualization();
        }
      },

      removeCareerPath: (id: string) => {
        const { careerAnalysis } = get();
        if (careerAnalysis) {
          const updatedPaths = careerAnalysis.careerPaths.filter(path => path.id !== id);
          const updatedAnalysis = {
            ...careerAnalysis,
            careerPaths: updatedPaths
          };
          set({ careerAnalysis: updatedAnalysis });
          get().generateVisualization();
        }
      },

      // Generation state
      setIsGenerating: (isGenerating: boolean) => {
        set({ isGenerating });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Visualization
      updateVisualizationNodes: (nodes: CareerVisualizationNode[]) => {
        set({ visualizationNodes: nodes });
      },

      updateVisualizationEdges: (edges: CareerVisualizationEdge[]) => {
        set({ visualizationEdges: edges });
      },

      generateVisualization: () => {
        const { careerAnalysis } = get();
        if (!careerAnalysis || careerAnalysis.careerPaths.length === 0) {
          set({ visualizationNodes: [], visualizationEdges: [] });
          return;
        }

        // Create center node
        const centerNode: CareerVisualizationNode = {
          id: 'center',
          type: 'center',
          position: VISUALIZATION_CONFIG.centerNode.position,
          data: { label: 'Career Paths' },
          style: VISUALIZATION_CONFIG.centerNode.style
        };

        // Create career nodes
        const careerNodes: CareerVisualizationNode[] = careerAnalysis.careerPaths.map((career, index) => {
          const position = VISUALIZATION_CONFIG.careerNodePositions[index] || 
                          VISUALIZATION_CONFIG.careerNodePositions[index % VISUALIZATION_CONFIG.careerNodePositions.length];
          
          return {
            id: career.id,
            type: 'career',
            position,
            data: { career },
            style: {}
          };
        });

        // Create edges connecting center to each career
        const edges: CareerVisualizationEdge[] = careerAnalysis.careerPaths.map(career => ({
          id: `edge-center-${career.id}`,
          source: 'center',
          target: career.id,
          animated: true,
          style: VISUALIZATION_CONFIG.edgeStyle
        }));

        set({
          visualizationNodes: [centerNode, ...careerNodes],
          visualizationEdges: edges
        });
      },

      // Utility actions
      resetState: () => {
        set(initialState);
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'career-explorer-store'
    }
  )
);
