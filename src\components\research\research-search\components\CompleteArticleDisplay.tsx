/**
 * Complete Article Display Component
 * Shows generated complete articles with proper formatting and export options
 */

import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Download, 
  Copy, 
  FileText, 
  BookOpen, 
  Clock, 
  Target, 
  BarChart3,
  CheckCircle,
  Quote,
  ExternalLink,
  Printer,
  Share2,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { CompleteArticle, ArticleSection, ArticleReference } from '../services/complete-article-generation.service';

interface CompleteArticleDisplayProps {
  article: CompleteArticle;
  onExport?: (format: 'pdf' | 'docx' | 'txt') => void;
  onSave?: () => void;
  className?: string;
}

export function CompleteArticleDisplay({
  article,
  onExport,
  onSave,
  className
}: CompleteArticleDisplayProps) {
  const [showReferences, setShowReferences] = useState(true);
  const [showMetadata, setShowMetadata] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatCitationCount = (count: number) => {
    return count === 1 ? '1 citation' : `${count} citations`;
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Article Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                {article.title}
              </CardTitle>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <FileText className="w-4 h-4" />
                  <span>{article.wordCount.toLocaleString()} words</span>
                </div>
                <div className="flex items-center gap-1">
                  <Quote className="w-4 h-4" />
                  <span>{formatCitationCount(article.citationCount)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{article.metadata.estimatedReadingTime} min read</span>
                </div>
                <div className="flex items-center gap-1">
                  <Target className="w-4 h-4" />
                  <span className={getQualityColor(article.metadata.qualityScore)}>
                    {Math.round(article.metadata.qualityScore * 100)}% quality
                  </span>
                </div>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowMetadata(!showMetadata)}
              >
                <BarChart3 className="w-4 h-4 mr-1" />
                Metadata
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(article.title)}
              >
                <Copy className="w-4 h-4 mr-1" />
                Copy
              </Button>
              {onSave && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onSave}
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Save
                </Button>
              )}
              {onExport && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onExport('pdf')}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    PDF
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onExport('docx')}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    DOCX
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Metadata Panel */}
          {showMetadata && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Research Type:</span>
                  <p className="text-gray-600 capitalize">{article.metadata.researchType.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Academic Level:</span>
                  <p className="text-gray-600 capitalize">{article.metadata.academicLevel}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Completeness:</span>
                  <p className="text-gray-600">{Math.round(article.metadata.completeness * 100)}%</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Citation Density:</span>
                  <p className="text-gray-600">{article.metadata.citationDensity.toFixed(1)}/1000 words</p>
                </div>
              </div>
              
              {article.metadata.keywords.length > 0 && (
                <div className="mt-3">
                  <span className="font-medium text-gray-700 text-sm">Keywords:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {article.metadata.keywords.map((keyword, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Abstract */}
      {article.abstract && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Abstract</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <p className="text-gray-700 leading-relaxed italic">
                {article.abstract}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Article Sections */}
      <div className="space-y-4">
        {article.sections.map((section, index) => (
          <Card key={section.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  {index + 1}. {section.title}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {section.wordCount} words
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {formatCitationCount(section.citations.length)}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSection(section.id)}
                  >
                    {expandedSections.has(section.id) ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            {(!expandedSections.has(section.id) || expandedSections.has(section.id)) && (
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <div 
                    className={cn(
                      "text-gray-700 leading-relaxed whitespace-pre-wrap",
                      expandedSections.has(section.id) ? "" : "line-clamp-3"
                    )}
                  >
                    {section.content}
                  </div>
                </div>

                {/* Subsections */}
                {section.subsections && section.subsections.length > 0 && (
                  <div className="mt-6 space-y-4">
                    {section.subsections.map((subsection, subIndex) => (
                      <div key={subsection.id} className="pl-4 border-l-2 border-gray-200">
                        <h4 className="font-medium text-gray-900 mb-2">
                          {index + 1}.{subIndex + 1} {subsection.title}
                        </h4>
                        <div className="prose prose-sm max-w-none">
                          <p className="text-gray-700 leading-relaxed">
                            {subsection.content}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Section Actions */}
                <div className="mt-4 flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(section.content)}
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Copy Section
                  </Button>
                  {section.citations.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleSection(section.id)}
                    >
                      <Quote className="w-3 h-3 mr-1" />
                      {expandedSections.has(section.id) ? 'Hide' : 'Show'} Citations
                    </Button>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* References Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">References</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {article.references.length} references
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReferences(!showReferences)}
              >
                {showReferences ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {showReferences && (
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {article.references.map((reference, index) => (
                  <div key={reference.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 mb-1">
                          [{index + 1}] {reference.formattedAPA}
                        </p>
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <Badge variant="secondary" className="text-xs">
                            {reference.type}
                          </Badge>
                          <span>{reference.year}</span>
                          {reference.doi && (
                            <span>DOI: {reference.doi}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(reference.formattedAPA)}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        {reference.url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(reference.url, '_blank')}
                          >
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        )}
      </Card>

      {/* Article Footer */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-sm text-gray-600">
            <p>Generated on {article.generatedAt.toLocaleDateString()} at {article.generatedAt.toLocaleTimeString()}</p>
            <p className="mt-1">
              This article was generated using AI-powered research tools. Please review and verify all content and citations.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
