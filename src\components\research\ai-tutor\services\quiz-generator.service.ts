/**
 * Quiz Generation Service
 * AI-powered quiz generator that creates questions from document content
 * with difficulty adaptation for the Research Comprehension Platform
 */

import { ResearchDocument, Quiz, QuizQuestion, QuizGenerationOptions, DifficultyLevel, EducationLevel } from '../types';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { tavilySearchService } from './tavily-search.service';

interface QuestionGenerationPrompt {
  type: QuizQuestion['type'];
  prompt: string;
  examples: string[];
}

class QuizGeneratorService {
  private readonly GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
  private readonly MAX_QUESTIONS_PER_REQUEST = 5;

  /**
   * Check if service is configured
   */
  isConfigured(): boolean {
    return Boolean(this.GEMINI_API_KEY);
  }

  /**
   * Generate quiz from document content
   */
  async generateQuiz(
    document: ResearchDocument,
    userId: string,
    options: QuizGenerationOptions
  ): Promise<Quiz> {
    return this.generateQuizFromDocument(document, userId, options);
  }

  /**
   * Generate quiz from document content
   */
  async generateQuizFromDocument(
    document: ResearchDocument,
    userId: string,
    options: QuizGenerationOptions
  ): Promise<Quiz> {
    if (!this.isConfigured()) {
      return this.generateSampleQuiz(document.title, options);
    }

    try {
      console.log(`Generating quiz for document "${document.title}"`);

      // Prepare content for quiz generation
      const content = this.prepareContentForQuiz(document, options);

      // Generate questions in batches
      const allQuestions: QuizQuestion[] = [];
      const questionsPerType = Math.ceil(options.questionCount / options.questionTypes.length);

      for (const questionType of options.questionTypes) {
        const questions = await this.generateQuestionsOfType(
          content,
          questionType,
          Math.min(questionsPerType, this.MAX_QUESTIONS_PER_REQUEST),
          options.difficulty,
          document.title
        );
        allQuestions.push(...questions);

        // Add delay to avoid rate limiting
        if (options.questionTypes.indexOf(questionType) < options.questionTypes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Trim to requested count and shuffle
      const finalQuestions = this.shuffleArray(allQuestions).slice(0, options.questionCount);

      // Create quiz object
      const quiz: Quiz = {
        id: crypto.randomUUID(),
        documentId: document.id,
        userId,
        title: `Quiz: ${document.title}`,
        questions: finalQuestions,
        difficulty: options.difficulty,
        estimatedTime: this.calculateEstimatedTime(finalQuestions),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save quiz to database
      await this.saveQuiz(quiz);

      console.log(`Generated quiz with ${finalQuestions.length} questions`);
      toast.success(`Quiz generated with ${finalQuestions.length} questions!`);
      
      return quiz;

    } catch (error) {
      console.error('Quiz generation failed:', error);
      toast.error(`Failed to generate quiz: ${error.message}`);
      return this.generateSampleQuiz(document.title, options);
    }
  }

  /**
   * Generate quiz from topic (without document)
   */
  async generateQuizFromTopic(
    topic: string,
    educationLevel: EducationLevel,
    options: Partial<QuizGenerationOptions> = {}
  ): Promise<Quiz> {
    const defaultOptions: QuizGenerationOptions = {
      questionCount: 10,
      questionTypes: ['multiple-choice', 'true-false'],
      difficulty: 'medium',
      educationLevel,
      includeExplanations: true,
      timeLimit: 30,
      ...options
    };

    if (!this.isConfigured()) {
      return this.generateSampleQuiz(topic, defaultOptions);
    }

    try {
      console.log(`Generating quiz for topic "${topic}"`);

      // Try to get additional context from web search
      let contextContent = '';
      if (tavilySearchService.isAvailable()) {
        try {
          const searchResults = await tavilySearchService.searchEducational(
            topic,
            educationLevel,
            { maxResults: 3 }
          );
          contextContent = this.formatSearchResults(searchResults);
        } catch (searchError) {
          console.warn('Web search failed, using AI knowledge only:', searchError);
        }
      }

      // Generate questions using AI knowledge + optional web context
      const allQuestions: QuizQuestion[] = [];
      const questionsPerType = Math.ceil(defaultOptions.questionCount / defaultOptions.questionTypes.length);

      for (const questionType of defaultOptions.questionTypes) {
        const questions = await this.generateQuestionsForTopic(
          topic,
          questionType,
          Math.min(questionsPerType, this.MAX_QUESTIONS_PER_REQUEST),
          defaultOptions,
          contextContent
        );
        allQuestions.push(...questions);
      }

      // Trim to exact count and shuffle
      const finalQuestions = this.shuffleArray(allQuestions).slice(0, defaultOptions.questionCount);

      // Create quiz object
      const quiz: Quiz = {
        id: `quiz-${Date.now()}`,
        title: `Quiz: ${topic}`,
        description: `Educational quiz about ${topic} for ${educationLevel} level`,
        documentId: null, // No document for topic-based quiz
        userId: 'anonymous', // Will be set by caller if needed
        questions: finalQuestions,
        totalQuestions: finalQuestions.length,
        difficulty: defaultOptions.difficulty,
        educationLevel: defaultOptions.educationLevel,
        estimatedTime: Math.ceil(finalQuestions.length * 1.5), // 1.5 minutes per question
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        metadata: {
          source: contextContent ? 'ai-knowledge-with-search' : 'ai-knowledge',
          topic,
          generatedWithoutDocument: true
        }
      };

      console.log(`Generated quiz with ${finalQuestions.length} questions for topic "${topic}"`);
      return quiz;

    } catch (error) {
      console.error('Topic-based quiz generation failed:', error);
      return this.generateSampleQuiz(topic, defaultOptions);
    }
  }

  /**
   * Generate quiz with web search enhancement
   */
  async generateQuizWithSearch(
    topic: string,
    educationLevel: EducationLevel,
    options: Partial<QuizGenerationOptions> = {}
  ): Promise<Quiz> {
    const defaultOptions: QuizGenerationOptions = {
      questionCount: 10,
      questionTypes: ['multiple-choice', 'true-false', 'short-answer'],
      difficulty: 'medium',
      educationLevel,
      includeExplanations: true,
      timeLimit: 30,
      ...options
    };

    if (!tavilySearchService.isAvailable()) {
      console.warn('Web search not available, falling back to topic-based generation');
      return this.generateQuizFromTopic(topic, educationLevel, options);
    }

    try {
      console.log(`Generating quiz with web search for topic "${topic}"`);

      // Get comprehensive search results
      const searchResults = await tavilySearchService.searchEducational(
        topic,
        educationLevel,
        { maxResults: 5 }
      );

      const contextContent = this.formatSearchResults(searchResults);

      // Generate quiz using search context
      return this.generateQuizFromTopic(topic, educationLevel, {
        ...defaultOptions,
        metadata: {
          source: 'web-search',
          searchResults: searchResults.results?.slice(0, 3)
        }
      });

    } catch (error) {
      console.error('Search-enhanced quiz generation failed:', error);
      return this.generateQuizFromTopic(topic, educationLevel, options);
    }
  }

  /**
   * Generate questions of a specific type
   */
  private async generateQuestionsOfType(
    content: string,
    questionType: QuizQuestion['type'],
    count: number,
    difficulty: DifficultyLevel,
    documentTitle: string
  ): Promise<QuizQuestion[]> {
    const prompt = this.createQuestionPrompt(content, questionType, count, difficulty, documentTitle);
    
    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key=${this.GEMINI_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 2000
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!generatedText) {
        throw new Error('No content generated');
      }

      return this.parseGeneratedQuestions(generatedText, questionType, difficulty);

    } catch (error) {
      console.error(`Error generating ${questionType} questions:`, error);
      return [];
    }
  }

  /**
   * Create prompt for question generation
   */
  private createQuestionPrompt(
    content: string,
    questionType: QuizQuestion['type'],
    count: number,
    difficulty: DifficultyLevel,
    documentTitle: string
  ): string {
    const difficultyInstructions = {
      beginner: 'Focus on basic comprehension and recall of key facts.',
      intermediate: 'Include analysis and application of concepts.',
      advanced: 'Emphasize critical thinking, synthesis, and evaluation.'
    };

    const typeInstructions = {
      'multiple-choice': `Generate ${count} multiple-choice questions with 4 options each (A, B, C, D). Mark the correct answer clearly.`,
      'true-false': `Generate ${count} true/false questions. Provide clear explanations for the correct answers.`,
      'short-answer': `Generate ${count} short-answer questions that require 1-2 sentence responses.`,
      'essay': `Generate ${count} essay questions that require detailed analysis and discussion.`
    };

    return `You are an expert educator creating quiz questions from academic content. 

Document Title: "${documentTitle}"
Question Type: ${questionType}
Difficulty Level: ${difficulty}
Number of Questions: ${count}

Instructions:
- ${typeInstructions[questionType]}
- ${difficultyInstructions[difficulty]}
- Base questions strictly on the provided content
- Ensure questions are clear, unambiguous, and educational
- Include explanations for correct answers
- Format your response as JSON with this structure:

{
  "questions": [
    {
      "question": "Question text here",
      "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"], // Only for multiple-choice
      "correctAnswer": "Correct answer here",
      "explanation": "Explanation of why this is correct",
      "relatedSection": "Section title or topic this relates to",
      "difficulty": 1-5 (1=easy, 5=hard),
      "points": 1-10 (based on difficulty)
    }
  ]
}

Content to base questions on:
${content.substring(0, 4000)} // Limit content length

Generate the questions now:`;
  }

  /**
   * Parse generated questions from AI response
   */
  private parseGeneratedQuestions(
    generatedText: string,
    questionType: QuizQuestion['type'],
    difficulty: DifficultyLevel
  ): QuizQuestion[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      const questions = parsed.questions || [];

      return questions.map((q: any, index: number) => ({
        id: crypto.randomUUID(),
        type: questionType,
        question: q.question || `Question ${index + 1}`,
        options: questionType === 'multiple-choice' ? (q.options || []) : undefined,
        correctAnswer: q.correctAnswer || '',
        explanation: q.explanation || '',
        relatedSection: q.relatedSection || 'General',
        difficulty: q.difficulty || this.getDifficultyNumber(difficulty),
        points: q.points || this.getDefaultPoints(difficulty)
      }));

    } catch (error) {
      console.error('Error parsing generated questions:', error);
      
      // Fallback: create basic questions from the text
      return this.createFallbackQuestions(generatedText, questionType, difficulty);
    }
  }

  /**
   * Create fallback questions when parsing fails
   */
  private createFallbackQuestions(
    text: string,
    questionType: QuizQuestion['type'],
    difficulty: DifficultyLevel
  ): QuizQuestion[] {
    const fallbackQuestion: QuizQuestion = {
      id: crypto.randomUUID(),
      type: questionType,
      question: this.extractQuestionFromText(text, questionType),
      options: questionType === 'multiple-choice' ? ['A) Option 1', 'B) Option 2', 'C) Option 3', 'D) Option 4'] : undefined,
      correctAnswer: 'Please review the document content',
      explanation: 'This question was generated as a fallback. Please review the document for accurate information.',
      relatedSection: 'General',
      difficulty: this.getDifficultyNumber(difficulty),
      points: this.getDefaultPoints(difficulty)
    };

    return [fallbackQuestion];
  }

  /**
   * Extract a basic question from text
   */
  private extractQuestionFromText(text: string, questionType: QuizQuestion['type']): string {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    
    if (sentences.length === 0) {
      return `What is the main concept discussed in this ${questionType.replace('-', ' ')} question?`;
    }

    const firstSentence = sentences[0].trim();
    
    switch (questionType) {
      case 'multiple-choice':
        return `Which of the following best describes: ${firstSentence}?`;
      case 'true-false':
        return `True or False: ${firstSentence}`;
      case 'short-answer':
        return `Explain the concept mentioned in: "${firstSentence}"`;
      case 'essay':
        return `Discuss and analyze the following statement: "${firstSentence}"`;
      default:
        return firstSentence + '?';
    }
  }

  /**
   * Prepare document content for quiz generation
   */
  private prepareContentForQuiz(document: ResearchDocument, options: QuizGenerationOptions): string {
    let content = '';

    // Include abstract if available
    if (document.abstract) {
      content += `Abstract: ${document.abstract}\n\n`;
    }

    // Include specified sections or all sections
    const sectionsToInclude = options.focusSections?.length 
      ? document.sections.filter(s => options.focusSections!.includes(s.id))
      : document.sections;

    for (const section of sectionsToInclude) {
      if (section.type !== 'references') { // Skip references section
        content += `${section.title}:\n${section.content}\n\n`;
      }
    }

    // Limit content length to avoid token limits
    return content.substring(0, 8000);
  }

  /**
   * Calculate estimated time for quiz completion
   */
  private calculateEstimatedTime(questions: QuizQuestion[]): number {
    const timePerQuestion = {
      'multiple-choice': 1.5,
      'true-false': 1,
      'short-answer': 3,
      'essay': 10
    };

    const totalMinutes = questions.reduce((total, q) => {
      return total + (timePerQuestion[q.type] || 2);
    }, 0);

    return Math.ceil(totalMinutes);
  }

  /**
   * Save quiz to database
   */
  private async saveQuiz(quiz: Quiz): Promise<void> {
    try {
      const { error } = await supabase
        .from('quizzes')
        .insert({
          id: quiz.id,
          document_id: quiz.documentId,
          user_id: quiz.userId,
          title: quiz.title,
          questions: quiz.questions,
          difficulty: quiz.difficulty,
          estimated_time: quiz.estimatedTime,
          created_at: quiz.createdAt.toISOString(),
          updated_at: quiz.updatedAt.toISOString()
        });

      if (error) {
        console.error('Error saving quiz:', error);
        throw new Error(`Failed to save quiz: ${error.message}`);
      }
    } catch (error) {
      console.error('Error in saveQuiz:', error);
      throw error;
    }
  }

  /**
   * Get quizzes for a document
   */
  async getQuizzesForDocument(documentId: string, userId?: string): Promise<Quiz[]> {
    try {
      let query = supabase
        .from('quizzes')
        .select('*')
        .eq('document_id', documentId);

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching quizzes:', error);
        return [];
      }

      return (data || []).map(quiz => ({
        id: quiz.id,
        documentId: quiz.document_id,
        userId: quiz.user_id,
        title: quiz.title,
        questions: quiz.questions || [],
        difficulty: quiz.difficulty,
        estimatedTime: quiz.estimated_time,
        createdAt: new Date(quiz.created_at),
        updatedAt: new Date(quiz.updated_at)
      }));

    } catch (error) {
      console.error('Error in getQuizzesForDocument:', error);
      return [];
    }
  }

  /**
   * Delete quiz
   */
  async deleteQuiz(quizId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quizzes')
        .delete()
        .eq('id', quizId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting quiz:', error);
        return false;
      }

      toast.success('Quiz deleted successfully');
      return true;

    } catch (error) {
      console.error('Error in deleteQuiz:', error);
      toast.error('Failed to delete quiz');
      return false;
    }
  }

  // Helper methods
  private getDifficultyNumber(difficulty: DifficultyLevel): number {
    const mapping = { beginner: 2, intermediate: 3, advanced: 4 };
    return mapping[difficulty] || 3;
  }

  private getDefaultPoints(difficulty: DifficultyLevel): number {
    const mapping = { beginner: 5, intermediate: 7, advanced: 10 };
    return mapping[difficulty] || 5;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Generate questions for a specific topic
   */
  private async generateQuestionsForTopic(
    topic: string,
    questionType: QuizQuestion['type'],
    count: number,
    options: QuizGenerationOptions,
    contextContent: string = ''
  ): Promise<QuizQuestion[]> {
    const prompt = this.buildTopicQuizPrompt(topic, questionType, count, options, contextContent);

    try {
      // Use the existing generateQuestionsOfType method pattern
      const questions = await this.generateQuestionsOfType(
        contextContent || `Topic: ${topic}`,
        questionType,
        count,
        options.difficulty,
        topic
      );
      return questions;
    } catch (error) {
      console.error(`Failed to generate ${questionType} questions for topic ${topic}:`, error);
      return this.generateSampleQuestionsForTopic(topic, questionType, count, options.difficulty);
    }
  }

  /**
   * Build prompt for topic-based quiz generation
   */
  private buildTopicQuizPrompt(
    topic: string,
    questionType: QuizQuestion['type'],
    count: number,
    options: QuizGenerationOptions,
    contextContent: string
  ): string {
    const basePrompt = `Generate ${count} ${questionType} questions about "${topic}" for ${options.educationLevel} level students.

Difficulty: ${options.difficulty}
Include explanations: ${options.includeExplanations}

${contextContent ? `Additional Context:\n${contextContent}\n\n` : ''}

Requirements:
- Questions should be educational and test understanding
- Appropriate for ${options.educationLevel} level
- Difficulty level: ${options.difficulty}
- Include clear, accurate answers
${options.includeExplanations ? '- Provide detailed explanations for each answer' : ''}

Format the response as a JSON array of question objects.`;

    return basePrompt + `\n\nQuestion Type: ${questionType}\nGenerate questions in the specified format.`;
  }

  /**
   * Format search results for context
   */
  private formatSearchResults(searchResults: any): string {
    if (!searchResults || !searchResults.results) {
      return '';
    }

    let formatted = 'Educational Sources:\n\n';

    searchResults.results.slice(0, 3).forEach((result: any, index: number) => {
      formatted += `${index + 1}. ${result.title}\n`;
      formatted += `   ${result.content.substring(0, 300)}...\n\n`;
    });

    return formatted;
  }

  /**
   * Generate sample quiz when AI is not available
   */
  private generateSampleQuiz(topic: string, options: QuizGenerationOptions): Quiz {
    const sampleQuestions: QuizQuestion[] = [];

    for (let i = 1; i <= Math.min(options.questionCount, 5); i++) {
      sampleQuestions.push({
        id: `sample-q${i}`,
        type: 'multiple-choice',
        question: `Sample question ${i} about ${topic}`,
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correctAnswer: 'Option A',
        explanation: 'This is a sample explanation. Configure your Gemini API key to generate real educational content.',
        difficulty: options.difficulty,
        points: 10,
        timeLimit: 60,
        topic: topic,
        hints: ['This is a sample quiz', 'Configure your AI service for real content']
      });
    }

    return {
      id: `sample-quiz-${Date.now()}`,
      title: `Sample Quiz: ${topic}`,
      description: 'This is a sample quiz. Configure your Gemini API key to generate real educational content.',
      documentId: null,
      userId: 'anonymous',
      questions: sampleQuestions,
      totalQuestions: sampleQuestions.length,
      difficulty: options.difficulty,
      educationLevel: options.educationLevel,
      estimatedTime: Math.ceil(sampleQuestions.length * 1.5),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      metadata: {
        source: 'sample',
        topic,
        note: 'Configure Gemini API key for real content'
      }
    };
  }

  /**
   * Generate sample questions for a topic
   */
  private generateSampleQuestionsForTopic(
    topic: string,
    questionType: QuizQuestion['type'],
    count: number,
    difficulty: DifficultyLevel
  ): QuizQuestion[] {
    const questions: QuizQuestion[] = [];

    for (let i = 1; i <= count; i++) {
      questions.push({
        id: `sample-${questionType}-${i}`,
        type: questionType,
        question: `Sample ${questionType} question ${i} about ${topic}`,
        options: questionType === 'multiple-choice' ? ['Option A', 'Option B', 'Option C', 'Option D'] : undefined,
        correctAnswer: questionType === 'multiple-choice' ? 'Option A' : 'Sample answer',
        explanation: 'This is a sample explanation. Configure your Gemini API key to generate real educational content.',
        difficulty,
        points: 10,
        timeLimit: 60,
        topic,
        hints: ['Configure your AI service', 'This is sample content']
      });
    }

    return questions;
  }

}

export const quizGeneratorService = new QuizGeneratorService();
