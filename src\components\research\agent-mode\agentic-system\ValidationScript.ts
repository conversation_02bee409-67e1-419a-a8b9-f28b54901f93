/**
 * Final validation script to ensure all components work together
 */

import { agenticEditingService } from './AgenticEditingService';
import { changeTrackingAdapter } from './ChangeTrackingAdapter';
import { errorHandler } from './ErrorHandler';
import { agenticSystemTest } from './AgenticSystemTest';

export interface ValidationReport {
  systemStatus: 'healthy' | 'warning' | 'error';
  components: ComponentStatus[];
  integrationTests: IntegrationTestResult[];
  recommendations: string[];
  summary: string;
}

export interface ComponentStatus {
  name: string;
  status: 'ok' | 'warning' | 'error';
  details: string;
  version?: string;
}

export interface IntegrationTestResult {
  name: string;
  success: boolean;
  message: string;
  duration: number;
}

export class SystemValidator {
  /**
   * Run complete system validation
   */
  async validateSystem(): Promise<ValidationReport> {
    console.log('🔍 Starting comprehensive system validation...');
    
    const components = await this.checkComponents();
    const integrationTests = await this.runIntegrationTests();
    const recommendations = this.generateRecommendations(components, integrationTests);
    
    const systemStatus = this.determineSystemStatus(components, integrationTests);
    const summary = this.generateSummary(systemStatus, components, integrationTests);
    
    const report: ValidationReport = {
      systemStatus,
      components,
      integrationTests,
      recommendations,
      summary
    };
    
    this.printValidationReport(report);
    return report;
  }

  /**
   * Check all system components
   */
  private async checkComponents(): Promise<ComponentStatus[]> {
    const components: ComponentStatus[] = [];
    
    // Check Agentic Editing Service
    try {
      agenticEditingService.initialize();
      const status = agenticEditingService.getStatus();
      
      components.push({
        name: 'Agentic Editing Service',
        status: status.initialized ? 'ok' : 'error',
        details: `Initialized: ${status.initialized}, Tools: ${status.toolsCount}`
      });
    } catch (error: any) {
      components.push({
        name: 'Agentic Editing Service',
        status: 'error',
        details: `Initialization failed: ${error.message}`
      });
    }
    
    // Check Change Tracking Adapter
    try {
      const stats = changeTrackingAdapter.getChangeStatistics();
      components.push({
        name: 'Change Tracking Adapter',
        status: 'ok',
        details: `Total changes: ${stats.totalChanges}, Agentic changes: ${stats.agenticChanges}`
      });
    } catch (error: any) {
      components.push({
        name: 'Change Tracking Adapter',
        status: 'error',
        details: `Error: ${error.message}`
      });
    }
    
    // Check Error Handler
    try {
      const errorStats = errorHandler.getErrorStatistics();
      const status = errorStats.totalErrors > 10 ? 'warning' : 'ok';
      
      components.push({
        name: 'Error Handler',
        status,
        details: `Errors: ${errorStats.totalErrors}, Recovery rate: ${(errorStats.recoveryRate * 100).toFixed(0)}%`
      });
    } catch (error: any) {
      components.push({
        name: 'Error Handler',
        status: 'error',
        details: `Error: ${error.message}`
      });
    }
    
    // Check Individual Tools
    const tools = agenticEditingService.getAvailableTools();
    components.push({
      name: 'Agent Tools',
      status: tools.length === 6 ? 'ok' : 'warning',
      details: `Available tools: ${tools.length}/6 (${tools.map(t => t.id).join(', ')})`
    });
    
    return components;
  }

  /**
   * Run integration tests
   */
  private async runIntegrationTests(): Promise<IntegrationTestResult[]> {
    const tests: IntegrationTestResult[] = [];
    
    // Test 1: Basic workflow
    tests.push(await this.runIntegrationTest(
      'Basic Workflow',
      async () => {
        const testDoc = 'This is a test document with some content that needs improvement.';
        const result = await agenticEditingService.executeTargetedEdit(
          'Improve the academic tone',
          testDoc,
          { editMode: 'conservative', enableChangeTracking: false }
        );
        
        if (!result.success && result.changes.length === 0) {
          throw new Error('No changes generated');
        }
        
        return 'Basic workflow completed successfully';
      }
    ));
    
    // Test 2: Change tracking integration
    tests.push(await this.runIntegrationTest(
      'Change Tracking Integration',
      async () => {
        const testDoc = 'This document will test change tracking integration.';
        
        // Ensure change tracking is enabled
        changeTrackingAdapter.ensureChangeTrackingEnabled(testDoc);
        
        const result = await agenticEditingService.executeTargetedEdit(
          'Fix any grammar issues',
          testDoc,
          { editMode: 'conservative', enableChangeTracking: true }
        );
        
        return 'Change tracking integration working';
      }
    ));
    
    // Test 3: Error handling
    tests.push(await this.runIntegrationTest(
      'Error Handling',
      async () => {
        try {
          await agenticEditingService.executeTargetedEdit(
            '', // Empty request should trigger error handling
            'Test document',
            { editMode: 'conservative', enableChangeTracking: false }
          );
        } catch (error) {
          // Expected to handle gracefully
        }
        
        return 'Error handling working correctly';
      }
    ));
    
    // Test 4: UI Integration (simulated)
    tests.push(await this.runIntegrationTest(
      'UI Integration',
      async () => {
        // Simulate UI integration by checking if services are properly exposed
        const hasAgenticService = typeof agenticEditingService.executeTargetedEdit === 'function';
        const hasChangeTracking = typeof changeTrackingAdapter.recordChanges === 'function';
        
        if (!hasAgenticService || !hasChangeTracking) {
          throw new Error('UI integration interfaces not properly exposed');
        }
        
        return 'UI integration interfaces available';
      }
    ));
    
    return tests;
  }

  /**
   * Run a single integration test
   */
  private async runIntegrationTest(
    name: string,
    testFunction: () => Promise<string>
  ): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      const message = await testFunction();
      const duration = Date.now() - startTime;
      
      return {
        name,
        success: true,
        message,
        duration
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      return {
        name,
        success: false,
        message: error.message,
        duration
      };
    }
  }

  /**
   * Generate recommendations based on validation results
   */
  private generateRecommendations(
    components: ComponentStatus[],
    tests: IntegrationTestResult[]
  ): string[] {
    const recommendations: string[] = [];
    
    // Check for component issues
    const errorComponents = components.filter(c => c.status === 'error');
    const warningComponents = components.filter(c => c.status === 'warning');
    
    if (errorComponents.length > 0) {
      recommendations.push(`Fix ${errorComponents.length} component error(s): ${errorComponents.map(c => c.name).join(', ')}`);
    }
    
    if (warningComponents.length > 0) {
      recommendations.push(`Address ${warningComponents.length} component warning(s): ${warningComponents.map(c => c.name).join(', ')}`);
    }
    
    // Check for test failures
    const failedTests = tests.filter(t => !t.success);
    if (failedTests.length > 0) {
      recommendations.push(`Fix ${failedTests.length} integration test failure(s): ${failedTests.map(t => t.name).join(', ')}`);
    }
    
    // Performance recommendations
    const slowTests = tests.filter(t => t.duration > 5000);
    if (slowTests.length > 0) {
      recommendations.push(`Optimize performance for slow tests: ${slowTests.map(t => t.name).join(', ')}`);
    }
    
    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('System is healthy - consider running comprehensive tests periodically');
      recommendations.push('Monitor error rates and performance metrics');
      recommendations.push('Keep documentation updated as system evolves');
    }
    
    return recommendations;
  }

  /**
   * Determine overall system status
   */
  private determineSystemStatus(
    components: ComponentStatus[],
    tests: IntegrationTestResult[]
  ): 'healthy' | 'warning' | 'error' {
    const hasErrors = components.some(c => c.status === 'error') || tests.some(t => !t.success);
    const hasWarnings = components.some(c => c.status === 'warning');
    
    if (hasErrors) return 'error';
    if (hasWarnings) return 'warning';
    return 'healthy';
  }

  /**
   * Generate summary
   */
  private generateSummary(
    systemStatus: string,
    components: ComponentStatus[],
    tests: IntegrationTestResult[]
  ): string {
    const okComponents = components.filter(c => c.status === 'ok').length;
    const passedTests = tests.filter(t => t.success).length;
    
    return `System Status: ${systemStatus.toUpperCase()} | ` +
           `Components: ${okComponents}/${components.length} OK | ` +
           `Tests: ${passedTests}/${tests.length} Passed`;
  }

  /**
   * Print validation report
   */
  private printValidationReport(report: ValidationReport): void {
    console.log('\n🔍 System Validation Report');
    console.log('==========================');
    console.log(`Status: ${this.getStatusIcon(report.systemStatus)} ${report.systemStatus.toUpperCase()}`);
    console.log(`Summary: ${report.summary}`);
    
    console.log('\n📦 Components:');
    report.components.forEach(component => {
      const icon = this.getStatusIcon(component.status);
      console.log(`  ${icon} ${component.name}: ${component.details}`);
    });
    
    console.log('\n🔗 Integration Tests:');
    report.integrationTests.forEach(test => {
      const icon = test.success ? '✅' : '❌';
      console.log(`  ${icon} ${test.name}: ${test.message} (${test.duration}ms)`);
    });
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }
    
    console.log('\n==========================');
  }

  /**
   * Get status icon
   */
  private getStatusIcon(status: string): string {
    switch (status) {
      case 'ok':
      case 'healthy':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  }
}

// Export validator instance
export const systemValidator = new SystemValidator();
