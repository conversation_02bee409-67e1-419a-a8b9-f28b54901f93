import { LucideIcon } from "lucide-react";

// Core article and journal types
export interface ArticleMetadata {
  title: string;
  abstract?: string;
  fullContent?: string;
  keywords: string[];
  researchField: string;
  methodology: string;
  novelty: string;
  contributionLevel: 'High' | 'Medium' | 'Low';
}

export interface JournalMetrics {
  impactFactor: number;
  citeScore: number;
  hIndex: number;
  acceptanceRate: number;
  averageReviewTime: number; // in days
  averagePublicationTime: number; // in days
  publicationFees: number; // in USD
  isOpenAccess: boolean;
  quartile: 'Q1' | 'Q2' | 'Q3' | 'Q4';
}

export interface JournalRecommendation {
  id: string;
  name: string;
  publisher: string;
  issn: string;
  website: string;
  description: string;
  scope: string[];
  targetAudience: string;
  editorialBoard: string[];
  recentTopics: string[];
  metrics: JournalMetrics;
  matchScore: number;
  matchReasons: string[];
  submissionGuidelines: string;
  specialIssues?: string[];
  geographicFocus?: string;
  languageRequirements: string[];
}

export interface ArticleAnalysis {
  researchDomain: string;
  methodology: string;
  keyTopics: string[];
  researchThemes: string[];
  academicField: string;
  noveltyScore: number;
  contributionLevel: string;
  recommendedJournals: JournalRecommendation[];
  analysisConfidence: number;
  generatedAt: Date;
}

export interface ArticleContent {
  type: 'title' | 'abstract' | 'full';
  content: string;
  wordCount: number;
  extractedKeywords: string[];
  detectedLanguage: string;
}

// UI and state types
export interface ArticleFinderState {
  currentStep: 'input' | 'analyzing' | 'results' | 'export';
  articleContent: ArticleContent | null;
  selectedInputType: InputType;
  selectedModel: string;
  analysisDepth: AnalysisDepth;
  articleAnalysis: ArticleAnalysis | null;
  isAnalyzing: boolean;
  error: string | null;
  searchProgress: number;
  rankingCriteria: RankingCriteria;
}

export interface JournalRanking {
  criteria: RankingCriteria;
  weightedScore: number;
  breakdown: {
    relevance: number;
    impact: number;
    speed: number;
    cost: number;
    accessibility: number;
  };
}

export interface RankingCriteria {
  relevanceWeight: number;
  impactWeight: number;
  speedWeight: number;
  costWeight: number;
  accessibilityWeight: number;
}

// API and service types
export interface ArticleFinderRequest {
  content: string;
  inputType: InputType;
  analysisDepth: AnalysisDepth;
  model: string;
  additionalContext?: string;
}

export interface ArticleFinderResponse {
  analysis: ArticleAnalysis;
  recommendations: JournalRecommendation[];
  confidence: number;
  processingTime: number;
}

// Component props
export interface ArticleFinderProps {
  className?: string;
}

export interface ArticleInputFormProps {
  onSubmit: (content: ArticleContent) => void;
  isLoading: boolean;
  selectedInputType: InputType;
  onInputTypeChange: (type: InputType) => void;
}

export interface JournalRecommendationCardProps {
  journal: JournalRecommendation;
  ranking: JournalRanking;
  onSelect: (journal: JournalRecommendation) => void;
  isSelected: boolean;
}

export interface JournalRankingSystemProps {
  journals: JournalRecommendation[];
  criteria: RankingCriteria;
  onCriteriaChange: (criteria: RankingCriteria) => void;
  onRankingUpdate: (rankings: JournalRanking[]) => void;
}

export interface ArticleAnalysisDisplayProps {
  analysis: ArticleAnalysis;
  isLoading: boolean;
}

export interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  data: ExportData;
  onExport: (options: ExportOptions) => void;
}

export interface HistoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadSubmission: (submission: ArticleSubmission) => void;
}

// Export and history types
export interface ExportOptions {
  format: ExportFormat;
  includeAnalysis: boolean;
  includeRecommendations: boolean;
  includeMetrics: boolean;
  includeRankings: boolean;
  maxRecommendations: number;
}

export interface ExportData {
  articleMetadata: ArticleMetadata;
  analysis: ArticleAnalysis;
  recommendations: JournalRecommendation[];
  rankings: JournalRanking[];
  exportedAt: Date;
}

export interface ArticleSubmission {
  id: string;
  user_id: string;
  title: string;
  metadata: ArticleMetadata;
  content: ArticleContent;
  analysis: ArticleAnalysis;
  recommendations: JournalRecommendation[];
  ai_model: string;
  status: 'draft' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

// Utility types
export type InputType = 'title' | 'abstract' | 'full';
export type AnalysisDepth = 'basic' | 'comprehensive' | 'detailed';
export type JournalField = 'STEM' | 'Medicine' | 'Social Sciences' | 'Humanities' | 'Business' | 'Arts' | 'Other';
export type ExportFormat = 'pdf' | 'docx' | 'json' | 'csv';
export type JournalCategory = 'High Impact' | 'Specialized' | 'Open Access' | 'Fast Track' | 'Emerging';

// AI model types
export interface AIModelOption {
  id: string;
  name: string;
  provider: 'gemini' | 'openrouter';
  model: string;
  description: string;
  maxTokens: number;
  costPerToken: number;
  enabled: boolean;
}
