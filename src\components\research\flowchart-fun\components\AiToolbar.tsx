/**
 * AI Toolbar Component for Flowchart Fun
 * Provides AI-powered flowchart generation interface
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Sparkles, 
  ChevronDown, 
  ChevronUp, 
  Wand2, 
  FileText, 
  Edit3,
  Check,
  X,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { useAIPromptStore, useAIGeneration, useAIDiff, useAIUI, AIMode } from '../stores/ai-prompt.store';
import { AI_MODE_DESCRIPTIONS, AI_PLACEHOLDERS, AI_EXAMPLE_PROMPTS, SAMPLE_FLOWCHARTS } from '../constants/ai-examples';

interface AiToolbarProps {
  currentFlowchartText: string;
  onFlowchartUpdate: (text: string) => void;
}

export const AiToolbar: React.FC<AiToolbarProps> = ({
  currentFlowchartText,
  onFlowchartUpdate
}) => {
  const { isOpen, mode, currentText, setIsOpen, setMode, setCurrentText } = useAIUI();
  const { generateStreamingFlowchart, isGenerating, error } = useAIGeneration();
  const { diff, acceptDiff, rejectDiff } = useAIDiff();
  const geminiService = useAIPromptStore(state => state.geminiService);

  const [streamingText, setStreamingText] = useState('');

  // Check if Gemini service is configured
  const isServiceConfigured = geminiService.isServiceConfigured();

  const handleModeChange = (newMode: AIMode) => {
    if (newMode === mode && isOpen) {
      setIsOpen(false);
    } else {
      setMode(newMode);
      setIsOpen(true);
    }
  };

  const handleGenerate = async () => {
    if (!currentText.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    if (!isServiceConfigured) {
      toast.error('Gemini API key not configured. Please check your environment variables.');
      return;
    }

    setStreamingText('');
    
    try {
      await generateStreamingFlowchart(
        currentFlowchartText,
        (chunk: string) => {
          setStreamingText(prev => prev + chunk);
        }
      );
    } catch (error) {
      console.error('Generation error:', error);
      toast.error('Failed to generate flowchart');
    }
  };

  const handleAcceptDiff = () => {
    if (diff) {
      onFlowchartUpdate(diff);
      acceptDiff();
      setStreamingText('');
      toast.success('Flowchart updated successfully');
    }
  };

  const handleRejectDiff = () => {
    rejectDiff();
    setStreamingText('');
    toast.info('Changes discarded');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerate();
    }
  };

  const getModeDescription = (mode: AIMode): string => {
    return AI_MODE_DESCRIPTIONS[mode] || '';
  };

  const getModeIcon = (mode: AIMode) => {
    switch (mode) {
      case 'prompt':
        return <Wand2 className="h-4 w-4" />;
      case 'convert':
        return <FileText className="h-4 w-4" />;
      case 'edit':
        return <Edit3 className="h-4 w-4" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  };

  if (!isServiceConfigured) {
    return (
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-amber-700">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">
              AI features require a Gemini API key. Please configure VITE_GEMINI_API_KEY in your environment.
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="border-b bg-white">
      <div className="p-4">
        {/* Mode Selection */}
        <div className="flex items-center gap-2 mb-4">
          <div className="flex items-center gap-1">
            {(['prompt', 'convert', 'edit'] as AIMode[]).map((modeOption) => (
              <Button
                key={modeOption}
                variant={mode === modeOption && isOpen ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleModeChange(modeOption)}
                className="capitalize"
              >
                {getModeIcon(modeOption)}
                <span className="ml-1">{modeOption}</span>
              </Button>
            ))}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="ml-auto"
          >
            {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>

        {/* Input Area */}
        {isOpen && (
          <div className="space-y-3">
            <div className="text-sm text-gray-600">
              {getModeDescription(mode)}
            </div>
            
            <div className="space-y-2">
              <Textarea
                value={currentText}
                onChange={(e) => setCurrentText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={AI_PLACEHOLDERS[mode]}
                className="min-h-[80px] resize-none"
                disabled={isGenerating}
              />
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  {error && (
                    <Badge variant="destructive" className="text-xs">
                      {error}
                    </Badge>
                  )}
                  {isGenerating && (
                    <Badge variant="secondary" className="text-xs">
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Generating...
                    </Badge>
                  )}
                  {!currentText && !isGenerating && (
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const examples = AI_EXAMPLE_PROMPTS[mode];
                          const randomExample = examples[Math.floor(Math.random() * examples.length)];
                          setCurrentText(randomExample);
                        }}
                        className="text-xs text-gray-500 hover:text-gray-700"
                      >
                        Try example
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs text-blue-600 hover:text-blue-700"
                          >
                            Load sample
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => onFlowchartUpdate(SAMPLE_FLOWCHARTS.simple)}
                          >
                            Simple Process
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onFlowchartUpdate(SAMPLE_FLOWCHARTS.login)}
                          >
                            User Login Flow
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onFlowchartUpdate(SAMPLE_FLOWCHARTS.research)}
                          >
                            Research Process
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onFlowchartUpdate(SAMPLE_FLOWCHARTS.development)}
                          >
                            Software Development
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>
                
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating || !currentText.trim()}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isGenerating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4" />
                  )}
                  <span className="ml-1">
                    {isGenerating ? 'Generating...' : 'Generate'}
                  </span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Diff Preview */}
        {diff && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-700">
                Preview Changes
              </span>
              <div className="flex gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleAcceptDiff}
                        className="h-7 px-2 text-green-600 border-green-300 hover:bg-green-50"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Accept changes</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleRejectDiff}
                        className="h-7 px-2 text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Reject changes</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            
            <div className="text-xs text-gray-600 bg-white p-2 rounded border max-h-32 overflow-y-auto font-mono">
              {streamingText || diff}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
