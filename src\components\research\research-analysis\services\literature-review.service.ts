import { ResearchDocument, LiteratureReview, CitationStyle, GenerationProgress, AIGenerationOptions, ReviewSection } from '../types';

class LiteratureReviewService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }

  /**
   * Check if the OpenRouter API key is configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Generates a literature review from the provided documents using the OpenRouter API
   * 
   * @param documents The research documents to analyze
   * @param topic The topic for the literature review
   * @param citationStyle The preferred citation style (APA, MLA, etc.)
   * @param onProgress Callback for progress updates
   * @param model The AI model to use
   * @returns A structured literature review
   */
  async generateLiteratureReview(
    documents: ResearchDocument[],
    topic: string,
    citationStyle: CitationStyle = 'APA',
    onProgress?: (progress: GenerationProgress) => void,
    model: string = 'google/gemini-2.5-flash'
  ): Promise<LiteratureReview> {
    // Check for API key
    if (!this.isConfigured()) {
      throw new Error('OpenRouter API key not configured in .env file (VITE_OPENROUTER_API_KEY)');
    }

    onProgress?.({
      stage: 'Preparing document data',
      progress: 5,
      message: 'Processing documents for literature review...',
      estimatedTimeRemaining: documents.length * 10 // Rough estimate
    });

    // Extract relevant content from documents for the AI prompt
    const documentSummaries = documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      authors: doc.authors,
      year: doc.publicationYear,
      abstract: doc.abstract.substring(0, 500), // Limit length
      keyFindings: doc.keyFindings,
      methodology: doc.methodology.slice(0, 3), // Top methods
      relevantSections: doc.sections
        .filter(s => 
          s.title.toLowerCase().includes('introduction') || 
          s.title.toLowerCase().includes('discussion') ||
          s.title.toLowerCase().includes('conclusion'))
        .map(s => ({ title: s.title, summary: s.content.substring(0, 200) }))
    }));

    onProgress?.({
      stage: 'Preparing AI analysis',
      progress: 20,
      message: 'Generating literature review framework...',
      estimatedTimeRemaining: 60 // About a minute
    });

    const prompt = `Generate a comprehensive academic literature review on the topic: "${topic}".

Documents to analyze (${documents.length}):
${JSON.stringify(documentSummaries, null, 2)}

Please organize the literature review with the following sections:
1. Introduction and Context
2. Theoretical Framework
3. Chronological Development
4. Thematic Analysis
5. Methodological Approaches
6. Critical Evaluation of Existing Research
7. Research Gaps
8. Conclusions and Future Directions

For each section, provide:
- A comprehensive analysis incorporating insights from the provided documents
- Appropriate citations in ${citationStyle} format
- Connections between different papers and findings
- Critical assessment of the research

Follow proper academic writing standards appropriate for a scholarly literature review.
Include a bibliography in ${citationStyle} format at the end.

Respond with a structured JSON including:
{
  "title": "The title for the literature review",
  "abstract": "A summary of the literature review",
  "sections": [
    {
      "title": "Section title",
      "content": "Section content with proper citations",
      "type": "section type (introduction, methodological, etc.)",
      "order": "numeric order in the document"
    }
  ],
  "citedDocuments": ["document IDs that are cited"],
  "bibliography": ["Formatted bibliography entries"],
  "wordCount": "Approximate word count"
}`;

    onProgress?.({
      stage: 'Generating literature review',
      progress: 40,
      message: 'AI is analyzing documents and synthesizing information...',
      estimatedTimeRemaining: 120 // About 2 minutes
    });

    try {
      // Call the OpenRouter API to generate the literature review
      const response = await this.callAI(prompt, {
        model: model,
        maxTokens: 6000,
        temperature: 0.3
      });

      onProgress?.({
        stage: 'Processing results',
        progress: 80,
        message: 'Formatting literature review...'
      });

      // Parse the JSON response
      const result = this.parseJSONResponse(response);

      console.log('🤖 AI Response parsed:', {
        title: result.title,
        sectionsCount: result.sections?.length || 0,
        hasContent: !!result.content,
        wordCount: result.wordCount
      });

      // Create citation objects
      const citations = (result.bibliography || []).map((bibEntry: string, index: number) => ({
        id: crypto.randomUUID(),
        documentId: result.citedDocuments?.[index] || documents[0].id,
        inText: `(Author, ${new Date().getFullYear()})`,
        fullReference: bibEntry
      }));

      // Create sections
      const sections: ReviewSection[] = result.sections?.map((section: any, index: number) => ({
        id: crypto.randomUUID(),
        title: section.title,
        content: section.content,
        type: this.inferSectionType(section.title),
        order: section.order || index,
        sourceDocuments: result.citedDocuments || documents.map(d => d.id),
        citations: citations.filter((_, i) => i % result.sections.length === index)
      })) || [];

      console.log('📝 Created sections from AI response:', sections.length, sections.map(s => ({ id: s.id, title: s.title, contentLength: s.content?.length || 0 })));

      // If no sections were returned, create a default one
      if (sections.length === 0) {
        console.log('⚠️ No sections in AI response, creating default section');
        sections.push({
          id: crypto.randomUUID(),
          title: 'Introduction and Context',
          content: result.content || 'No content was generated. Please try again with more specific documents.',
          type: 'introduction',
          order: 0,
          sourceDocuments: documents.map(d => d.id),
          citations: []
        });
      }

      const literatureReview: LiteratureReview = {
        id: crypto.randomUUID(),
        title: result.title || `Literature Review: ${topic}`,
        documents: documents.map(d => d.id),
        sections: sections,
        citationStyle,
        status: 'completed',
        generatedAt: new Date(),
        wordCount: result.wordCount || this.calculateWordCount(sections),
        exportOptions: ['PDF', 'Word', 'LaTeX']
      };

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Literature review generated successfully'
      });

      return literatureReview;
    } catch (error) {
      console.error('Error generating literature review:', error);
      
      // Create an error-state literature review
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const isApiKeyError = errorMessage.includes('API key') || 
                            errorMessage.includes('authorization') || 
                            errorMessage.includes('401');
      
      const errorSection: ReviewSection = {
        id: crypto.randomUUID(),
        title: 'Error',
        content: isApiKeyError
          ? 'OpenRouter API key error. Please check your API key in the .env file (VITE_OPENROUTER_API_KEY).'
          : `An error occurred during literature review generation: ${errorMessage}`,
        type: 'introduction', // Using a valid type from the ReviewSection type
        order: 0,
        sourceDocuments: [],
        citations: []
      };

      return {
        id: crypto.randomUUID(),
        title: `Literature Review: ${topic} (Error)`,
        documents: documents.map(d => d.id),
        sections: [errorSection],
        citationStyle,
        status: 'completed', // We still mark as completed to allow viewing the error
        generatedAt: new Date(),
        wordCount: errorSection.content.split(' ').length,
        exportOptions: ['PDF', 'Word', 'LaTeX']
      };
    }
  }

  /**
   * Call the OpenRouter API
   */
  private async callAI(prompt: string, options: AIGenerationOptions = {}): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Paper Genius - Literature Review'
      },
      body: JSON.stringify({
        model: options.model || "google/gemini-2.5-flash",
        messages: [
          {
            role: "system",
            content: "You are an expert academic researcher specializing in literature reviews. Create comprehensive, scholarly literature reviews that synthesize research findings with proper academic citation."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.3,
        top_p: options.topP || 0.9,
        frequency_penalty: options.frequencyPenalty || 0,
        presence_penalty: options.presencePenalty || 0
      })
    });

    if (!response.ok) {
      throw new Error(`AI API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No response generated';
  }

  /**
   * Parse JSON response from the AI with enhanced error handling
   */
  private parseJSONResponse(response: string): any {
    try {
      // First attempt: Look for JSON pattern in the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (innerError) {
          console.warn('Found JSON-like pattern but parsing failed:', innerError);
          // Continue to other methods
        }
      }
      
      // Second attempt: Try with regular JSON.parse if the response looks like JSON
      if (response.trim().startsWith('{') && response.trim().endsWith('}')) {
        return JSON.parse(response);
      }
      
      // Third attempt: Look for markdown code blocks with JSON content
      const codeBlockMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch && codeBlockMatch[1]) {
        try {
          return JSON.parse(codeBlockMatch[1]);
        } catch (codeBlockError) {
          console.warn('Found JSON in code block but parsing failed:', codeBlockError);
        }
      }
      
      // Fourth attempt: Strip any non-JSON text at the beginning/end and try to parse
      const potentialJsonContent = response.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
      if (potentialJsonContent && potentialJsonContent.trim().startsWith('{') && potentialJsonContent.trim().endsWith('}')) {
        try {
          return JSON.parse(potentialJsonContent);
        } catch (strippedError) {
          console.warn('Attempted to parse stripped content but failed:', strippedError);
        }
      }
      
      // If all parsing attempts fail, return a minimal structure with the original content
      console.error('All JSON parsing attempts failed. Using fallback structure.');
      return {
        content: response,
        sections: []
      };
    } catch (error) {
      console.error('Error in parseJSONResponse:', error, 'Response:', response);
      // Return a minimal structure if JSON parsing fails
      return {
        content: response,
        sections: []
      };
    }
  }

  /**
   * Infer the type of section based on its title
   */
  private inferSectionType(title: string): string {
    const titleLower = title.toLowerCase();
    
    if (titleLower.includes('introduction') || titleLower.includes('context')) return 'introduction';
    if (titleLower.includes('theoretical') || titleLower.includes('framework')) return 'thematic';
    if (titleLower.includes('chronological') || titleLower.includes('development')) return 'chronological';
    if (titleLower.includes('theme') || titleLower.includes('thematic')) return 'thematic';
    if (titleLower.includes('method')) return 'methodological';
    if (titleLower.includes('evaluation') || titleLower.includes('critical')) return 'thematic';
    if (titleLower.includes('gap')) return 'gaps';
    if (titleLower.includes('conclusion')) return 'conclusion';
    
    return 'thematic';
  }

  /**
   * Calculate total word count across all sections
   */
  private calculateWordCount(sections: ReviewSection[]): number {
    return sections.reduce((count, section) => {
      return count + section.content.split(/\s+/).length;
    }, 0);
  }
}

export const literatureReviewService = new LiteratureReviewService();
export default literatureReviewService;
