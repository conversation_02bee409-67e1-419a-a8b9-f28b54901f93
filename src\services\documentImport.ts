import { documentService } from './documentService';
import { Database } from '@/lib/database.types';

type DocumentType = Database['public']['Tables']['user_documents']['Row']['document_type'];

export interface ImportResult {
  success: boolean;
  document?: Database['public']['Tables']['user_documents']['Row'];
  error?: string;
}

export class DocumentImportService {
  /**
   * Import a file and create a new document
   */
  async importFile(file: File, documentType?: DocumentType): Promise<ImportResult> {
    try {
      const content = await this.parseFile(file);
      if (!content) {
        return { success: false, error: 'Failed to parse file content' };
      }

      const title = this.extractTitleFromFilename(file.name);
      const wordCount = documentService.countWords(content);

      const document = await documentService.createDocument({
        title,
        content,
        document_type: documentType || this.inferDocumentTypeFromFile(file),
        status: 'draft',
        metadata: {
          originalFilename: file.name,
          fileSize: file.size,
          importedAt: new Date().toISOString(),
        },
        word_count: wordCount,
      });

      if (!document) {
        return { success: false, error: 'Failed to create document' };
      }

      return { success: true, document };
    } catch (error) {
      console.error('Error importing file:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Parse different file types
   */
  private async parseFile(file: File): Promise<string | null> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'txt':
        return await this.parseTextFile(file);
      case 'md':
        return await this.parseMarkdownFile(file);
      case 'html':
      case 'htm':
        return await this.parseHtmlFile(file);
      case 'rtf':
        return await this.parseRtfFile(file);
      case 'docx':
        return await this.parseDocxFile(file);
      case 'pdf':
        return await this.parsePdfFile(file);
      default:
        // Try to parse as text
        return await this.parseTextFile(file);
    }
  }

  /**
   * Parse plain text files
   */
  private async parseTextFile(file: File): Promise<string> {
    const text = await file.text();
    // Convert plain text to HTML paragraphs
    return text
      .split(/\n\s*\n/)
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('');
  }

  /**
   * Parse markdown files
   */
  private async parseMarkdownFile(file: File): Promise<string> {
    const markdown = await file.text();
    // Basic markdown to HTML conversion
    return this.markdownToHtml(markdown);
  }

  /**
   * Parse HTML files
   */
  private async parseHtmlFile(file: File): Promise<string> {
    const html = await file.text();
    // Extract content from body if it exists, otherwise use the whole content
    const bodyMatch = html.match(/<body[^>]*>([\s\S]*)<\/body>/i);
    if (bodyMatch) {
      return bodyMatch[1].trim();
    }
    // Remove script and style tags
    return html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .trim();
  }

  /**
   * Parse RTF files (basic implementation)
   */
  private async parseRtfFile(file: File): Promise<string> {
    const rtf = await file.text();
    // Very basic RTF to text conversion - remove RTF control words
    const text = rtf
      .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF control words
      .replace(/[{}]/g, '') // Remove braces
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    return `<p>${text}</p>`;
  }

  /**
   * Basic markdown to HTML conversion
   */
  private markdownToHtml(markdown: string): string {
    return markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/__(.*?)__/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/_(.*?)_/gim, '<em>$1</em>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      // Line breaks and paragraphs
      .split(/\n\s*\n/)
      .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
      .join('');
  }

  /**
   * Parse DOCX files using mammoth.js
   */
  private async parseDocxFile(file: File): Promise<string> {
    try {
      // Dynamic import to avoid bundling issues
      const mammoth = await import('mammoth');
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });

      if (result.messages.length > 0) {
        console.warn('DOCX parsing warnings:', result.messages);
      }

      return result.value || '';
    } catch (error) {
      console.error('Error parsing DOCX file:', error);
      throw new Error('Failed to parse DOCX file. Please ensure the file is not corrupted.');
    }
  }

  /**
   * Parse PDF files using pdfjs-dist
   */
  private async parsePdfFile(file: File): Promise<string> {
    try {
      // Dynamic import to avoid bundling issues
      const pdfjsLib = await import('pdfjs-dist');

      // Configure worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let fullText = '';

      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        fullText += pageText + '\n\n';
      }

      // Convert to HTML paragraphs
      return fullText
        .split(/\n\s*\n/)
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p>${paragraph.trim()}</p>`)
        .join('');

    } catch (error) {
      console.error('Error parsing PDF file:', error);
      throw new Error('Failed to parse PDF file. Please ensure the file is not corrupted.');
    }
  }

  /**
   * Extract title from filename
   */
  private extractTitleFromFilename(filename: string): string {
    const nameWithoutExtension = filename.replace(/\.[^/.]+$/, '');
    // Convert underscores and hyphens to spaces, capitalize first letter
    return nameWithoutExtension
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Infer document type from file
   */
  private inferDocumentTypeFromFile(file: File): DocumentType {
    const filename = file.name.toLowerCase();
    
    if (filename.includes('paper') || filename.includes('research')) {
      return 'paper';
    } else if (filename.includes('book') || filename.includes('chapter')) {
      return 'book';
    } else if (filename.includes('article') || filename.includes('review')) {
      return 'article_review';
    } else if (filename.includes('analysis')) {
      return 'research_analysis';
    }
    
    return 'paper'; // Default to paper
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): string[] {
    return ['.txt', '.md', '.html', '.htm', '.rtf', '.docx', '.pdf'];
  }

  /**
   * Get file type accept string for input element
   */
  getFileAcceptString(): string {
    return this.getSupportedFileTypes().join(',');
  }
}

export const documentImportService = new DocumentImportService();