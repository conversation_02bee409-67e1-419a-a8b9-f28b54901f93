import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  BookOpen,
  FileText,
  Download,
  Settings,
  Sparkles,
  CheckCircle,
  Clock,
  AlertTriangle,
  Brain,
  Target,
  Users,
  Calendar,
  Globe,
  Award,
  TrendingUp,
  BarChart3,
  Eye,
  Share2
} from "lucide-react";

import { UploadedFile, DataAnalysisResult, ResearchReport } from '../types';
import { ResearchReportService } from '../services/research-report.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';

interface ResearchReportGeneratorProps {
  file: UploadedFile;
  analysis: DataAnalysisResult;
  researchContext: {
    researchQuestion?: string;
    studyType?: string;
    researchField?: string;
    hypotheses?: string[];
  };
  onReportGenerated?: (report: ResearchReport) => void;
  className?: string;
}

export const ResearchReportGenerator: React.FC<ResearchReportGeneratorProps> = ({
  file,
  analysis,
  researchContext,
  onReportGenerated,
  className = ""
}) => {
  const { setGeneratingReport, currentReport } = useDataVisualizationStore();
  
  const [reportConfig, setReportConfig] = useState({
    title: `Research Analysis of ${file.name}`,
    researchField: researchContext.researchField || 'Data Science',
    citationStyle: 'APA' as 'APA' | 'IEEE' | 'Harvard' | 'MLA',
    includeAbstract: true,
    includeLimitations: true,
    includeMethodology: true,
    includeFigures: true
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');

  const generateReport = async () => {
    setIsGenerating(true);
    setGeneratingReport(true);
    setGenerationProgress(0);

    try {
      // Simulate progress updates
      const steps = [
        'Generating abstract...',
        'Writing introduction...',
        'Documenting methodology...',
        'Presenting results...',
        'Developing discussion...',
        'Formulating conclusions...',
        'Processing figures...',
        'Finalizing report...'
      ];

      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(steps[i]);
        setGenerationProgress((i / steps.length) * 100);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const report = await ResearchReportService.generateResearchReport(
        file,
        analysis,
        reportConfig
      );

      setGenerationProgress(100);
      setCurrentStep('Report generated successfully!');

      if (onReportGenerated) {
        onReportGenerated(report);
      }

      toast.success('Research report generated successfully!');

    } catch (error) {
      console.error('Report generation failed:', error);
      toast.error('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
      setGeneratingReport(false);
    }
  };

  if (currentReport) {
    return (
      <div className={`space-y-8 ${className}`}>
        {/* Report Preview */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl">
              <div className="p-3 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl">
                <BookOpen className="h-8 w-8 text-white" />
              </div>
              <div>
                <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  Research Report Generated
                </span>
                <p className="text-sm font-normal text-gray-600 mt-1">
                  Your comprehensive research report is ready for review and export
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{currentReport.metadata.wordCount}</div>
                <div className="text-sm text-gray-600">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{currentReport.figures.length}</div>
                <div className="text-sm text-gray-600">Figures</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{currentReport.tables.length}</div>
                <div className="text-sm text-gray-600">Tables</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{currentReport.references.length}</div>
                <div className="text-sm text-gray-600">References</div>
              </div>
            </div>

            <Separator className="my-6" />

            <div className="flex gap-4">
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Download PDF
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Download Word
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Share2 className="h-4 w-4" />
                Share
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Report Sections Preview */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle>Report Sections</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(currentReport.sections).map(([section, content]) => (
                content && (
                  <div key={section} className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-semibold text-gray-900 mb-2 capitalize">
                      {section.replace(/([A-Z])/g, ' $1').trim()}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {content.substring(0, 200)}...
                    </p>
                  </div>
                )
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Research Report Generator
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Generate comprehensive research reports with automated writing and formatting
              </p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Report Configuration */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            Report Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title">Report Title</Label>
              <Input
                id="title"
                value={reportConfig.title}
                onChange={(e) => setReportConfig(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter report title"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="field">Research Field</Label>
              <Input
                id="field"
                value={reportConfig.researchField}
                onChange={(e) => setReportConfig(prev => ({ ...prev, researchField: e.target.value }))}
                placeholder="e.g., Psychology, Medicine, Economics"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="citation">Citation Style</Label>
            <Select 
              value={reportConfig.citationStyle} 
              onValueChange={(value: any) => setReportConfig(prev => ({ ...prev, citationStyle: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="APA">APA (American Psychological Association)</SelectItem>
                <SelectItem value="IEEE">IEEE (Institute of Electrical and Electronics Engineers)</SelectItem>
                <SelectItem value="Harvard">Harvard</SelectItem>
                <SelectItem value="MLA">MLA (Modern Language Association)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            <Label>Report Sections</Label>
            <div className="grid grid-cols-2 gap-4">
              {[
                { key: 'includeAbstract', label: 'Abstract' },
                { key: 'includeLimitations', label: 'Limitations' },
                { key: 'includeMethodology', label: 'Methodology' },
                { key: 'includeFigures', label: 'Figures & Tables' }
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={key}
                    checked={reportConfig[key as keyof typeof reportConfig] as boolean}
                    onChange={(e) => setReportConfig(prev => ({ ...prev, [key]: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor={key} className="text-sm">{label}</Label>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Progress */}
      {isGenerating && (
        <Card className="border-0 shadow-lg bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <Brain className="h-6 w-6 text-blue-600 animate-pulse" />
              <div>
                <h3 className="font-semibold text-gray-900">Generating Research Report</h3>
                <p className="text-sm text-gray-600">{currentStep}</p>
              </div>
            </div>
            <Progress value={generationProgress} className="h-2" />
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>Progress</span>
              <span>{Math.round(generationProgress)}%</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generate Button */}
      {!isGenerating && (
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6 text-center">
            <Button 
              onClick={generateReport}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              Generate Research Report
            </Button>
            <p className="text-sm text-gray-600 mt-2">
              This will create a comprehensive research report with all sections, figures, and references.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
