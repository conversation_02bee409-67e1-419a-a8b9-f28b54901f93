/**
 * Preview and Theme Test Component
 * Comprehensive test for preview functionality and theme changes
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Play, Palette, Eye, Code } from 'lucide-react';
import { FlowchartFun } from '../FlowchartFun';
import { useFlowchartFunStore } from '../stores/flowchart-fun.store';
import { defaultTheme, themePresets } from '../themes/defaultTheme';
import { FFTheme } from '../types';

const PreviewAndThemeTest: React.FC = () => {
  const [testText, setTestText] = useState(`Start
  Input Data
    Valid Data?
      Yes: Process Information
        Save to Database
          Success?
            Yes: Send Confirmation
              Display Success Message
                End
            No: Log Error
              Retry Save?
                Yes: Save to Database
                No: Display Error
                  End
      No: Show Validation Error
        Request Correct Input
          Input Data`);

  const [selectedTheme, setSelectedTheme] = useState<FFTheme>(defaultTheme);
  const [testResults, setTestResults] = useState<Array<{name: string, status: 'pass' | 'fail', message: string}>>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Test the core functionality
  const runPreviewTests = async () => {
    setIsRunning(true);
    const results: Array<{name: string, status: 'pass' | 'fail', message: string}> = [];

    // Test 1: Store initialization
    try {
      const store = useFlowchartFunStore.getState();
      if (store.text && store.initialize) {
        results.push({
          name: 'Store Initialization',
          status: 'pass',
          message: 'Store initialized with text and methods'
        });
      } else {
        results.push({
          name: 'Store Initialization',
          status: 'fail',
          message: 'Store missing required properties'
        });
      }
    } catch (error) {
      results.push({
        name: 'Store Initialization',
        status: 'fail',
        message: `Store error: ${error}`
      });
    }

    // Test 2: Text parsing
    try {
      const store = useFlowchartFunStore.getState();
      store.setText(testText);
      
      // Wait for parsing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedStore = useFlowchartFunStore.getState();
      if (updatedStore.parsedGraph && updatedStore.parsedGraph.nodes.length > 0) {
        results.push({
          name: 'Text Parsing',
          status: 'pass',
          message: `Successfully parsed ${updatedStore.parsedGraph.nodes.length} nodes`
        });
      } else {
        results.push({
          name: 'Text Parsing',
          status: 'fail',
          message: 'Failed to parse text into graph'
        });
      }
    } catch (error) {
      results.push({
        name: 'Text Parsing',
        status: 'fail',
        message: `Parsing error: ${error}`
      });
    }

    // Test 3: Theme system
    try {
      if (themePresets.length > 0 && selectedTheme.nodeBackground) {
        results.push({
          name: 'Theme System',
          status: 'pass',
          message: `${themePresets.length} themes available, current theme valid`
        });
      } else {
        results.push({
          name: 'Theme System',
          status: 'fail',
          message: 'Theme system not working properly'
        });
      }
    } catch (error) {
      results.push({
        name: 'Theme System',
        status: 'fail',
        message: `Theme error: ${error}`
      });
    }

    // Test 4: Layout algorithms
    try {
      const layouts = ['dagre', 'breadthfirst', 'cose', 'concentric', 'circle', 'grid'];
      const validLayouts = layouts.filter(layout => 
        selectedTheme.layoutName === layout || layout === 'dagre'
      );
      
      results.push({
        name: 'Layout Algorithms',
        status: 'pass',
        message: `${layouts.length} layout algorithms available`
      });
    } catch (error) {
      results.push({
        name: 'Layout Algorithms',
        status: 'fail',
        message: `Layout error: ${error}`
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const handleThemeChange = (themeId: string) => {
    const theme = themePresets.find(t => t.id === themeId);
    if (theme) {
      setSelectedTheme(theme.theme);
    }
  };

  const getStatusIcon = (status: 'pass' | 'fail') => {
    return status === 'pass' 
      ? <CheckCircle className="h-4 w-4 text-green-500" />
      : <XCircle className="h-4 w-4 text-red-500" />;
  };

  const passedTests = testResults.filter(r => r.status === 'pass').length;
  const failedTests = testResults.filter(r => r.status === 'fail').length;

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Preview & Theme Functionality Test
          </CardTitle>
          <CardDescription>
            Test the core preview functionality and dynamic theme changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button 
                onClick={runPreviewTests} 
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                {isRunning ? 'Running Tests...' : 'Test Core Functionality'}
              </Button>
              
              {testResults.length > 0 && (
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    {passedTests} Passed
                  </Badge>
                  {failedTests > 0 && (
                    <Badge variant="destructive">
                      {failedTests} Failed
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {testResults.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-medium">Test Results:</h3>
                {testResults.map((result, index) => (
                  <div 
                    key={index} 
                    className={`p-3 rounded-lg border ${
                      result.status === 'pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <span className="font-medium">{result.name}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Theme Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select Theme:</label>
              <Select onValueChange={handleThemeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a theme" />
                </SelectTrigger>
                <SelectContent>
                  {themePresets.map((preset) => (
                    <SelectItem key={preset.id} value={preset.id}>
                      {preset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Test Text:</label>
              <Textarea
                value={testText}
                onChange={(e) => setTestText(e.target.value)}
                className="h-32 font-mono text-sm"
                placeholder="Enter flowchart text..."
              />
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Current Theme:</h4>
              <div className="text-sm space-y-1">
                <div>Layout: <Badge variant="outline">{selectedTheme.layoutName}</Badge></div>
                <div>Direction: <Badge variant="outline">{selectedTheme.direction}</Badge></div>
                <div className="flex items-center gap-2">
                  Background: 
                  <div 
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: selectedTheme.background }}
                  />
                  <span className="text-xs">{selectedTheme.background}</span>
                </div>
                <div className="flex items-center gap-2">
                  Node Color: 
                  <div 
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: selectedTheme.nodeBackground }}
                  />
                  <span className="text-xs">{selectedTheme.nodeBackground}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Live Preview */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Live Preview Test
            </CardTitle>
            <CardDescription>
              This should show real-time preview with theme changes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg" style={{ height: '500px' }}>
              <FlowchartFun
                initialText={testText}
                onTextChange={(newText) => {
                  setTestText(newText);
                  console.log('Text changed:', newText.length, 'characters');
                }}
                onGraphChange={(graph) => {
                  console.log('Graph changed:', graph?.nodes?.length || 0, 'nodes');
                }}
                className="h-full"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Alert className={failedTests === 0 && testResults.length > 0 ? 'border-green-200 bg-green-50' : 'border-blue-200 bg-blue-50'}>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Expected Behavior:</strong>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li><strong>Left Side:</strong> Text editor with AI toolbar and syntax highlighting</li>
            <li><strong>Right Side:</strong> Real-time preview that updates as you type</li>
            <li><strong>Theme Changes:</strong> Selecting different themes should immediately update the preview</li>
            <li><strong>Layout Changes:</strong> Different layout algorithms should reorganize the flowchart</li>
            <li><strong>Interactive Elements:</strong> Zoom, pan, and layout controls should work</li>
            <li><strong>Examples Tab:</strong> Should show gallery of professional templates</li>
            <li><strong>Settings Tab:</strong> Should show comprehensive theme customization</li>
          </ul>
          {failedTests === 0 && testResults.length > 0 && (
            <div className="mt-3 p-2 bg-green-100 rounded">
              🎉 All tests passed! The Flow Builder is working correctly.
            </div>
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default PreviewAndThemeTest;
