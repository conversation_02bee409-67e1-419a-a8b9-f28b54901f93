import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import {
  FileText,
  ImageIcon,
  Bot,
  Zap,
  Upload,
  Eye,
  CheckCircle,
  Loader2,
  AlertTriangle,
  Lightbulb,
  Wrench,
  TrendingUp,
  MessageSquare,
  Brain,
  ChevronDown,
  ChevronUp,
  X
} from 'lucide-react';

import { enhancedAIService } from './enhanced-ai.service';
import { paperAIService } from './paper-ai.service';
import { AI_MODELS } from './constants';

interface EnhancedSectionInputProps {
  sectionName: string;
  sectionType: {
    id: string;
    name: string;
    description: string;
    icon: any;
    color: string;
  };
  userInputs: {
    userSections: Array<{
      id: string;
      name: string;
      items: Array<{
        id: string;
        type: 'text' | 'figure';
        content: string;
        title?: string;
        caption?: string;
        analysis?: string;
        aiAnalysis?: string; // For compatibility with types.ts
        base64Data?: string; // For AI image analysis
        originalFile?: File; // Store original file for base64 conversion
      }>;
    }>;
  };
  addUserSection: (sectionTypeId: string) => void;
  removeUserSection: (sectionId: string) => void;
  addContentItem: (sectionId: string, type: 'text' | 'figure') => void;
  updateContentItem: (sectionId: string, itemId: string, updates: any) => void;
  removeContentItem: (sectionId: string, itemId: string) => void;
  moveContentItem: (sectionId: string, itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedModel: string;
  sectionAnalysis: Record<string, { feedback: string; suggestions: string[]; isAnalyzing: boolean }>;
  analyzeSectionContent: (sectionId: string, sectionName: string) => void;
  fixSectionContent: (sectionId: string, sectionName: string) => void;
  isFixing: Set<string>;
  // Optional custom function for adding figures with data
  addFigureWithData?: (sectionId: string, figureData: {
    content: string;
    title: string;
    caption: string;
    analysis: string;
    originalFile: File;
  }) => void;
}

export const EnhancedSectionInput: React.FC<EnhancedSectionInputProps> = (props) => {
  const {
    sectionName,
    sectionType,
    userInputs,
    addUserSection,
    removeUserSection,
    addContentItem,
    updateContentItem,
    removeContentItem,
    moveContentItem,
    analyzingItems,
    setAnalyzingItems,
    selectedModel,
    sectionAnalysis,
    analyzeSectionContent,
    fixSectionContent,
    isFixing,
    addFigureWithData
  } = props;

  const [activeTab, setActiveTab] = useState<'write' | 'analyze'>('write');
  const [localModel, setLocalModel] = useState(selectedModel);
  const [hiddenAnalysis, setHiddenAnalysis] = useState<Set<string>>(new Set());
  const [textAreaSize, setTextAreaSize] = useState<'small' | 'medium' | 'large'>('medium');
  
  // Find the section in user inputs
  const section = userInputs.userSections.find(s => s.name === sectionName);
  const sectionId = section?.id;
  const analysis = sectionId ? sectionAnalysis[sectionId] : undefined;

  // If section doesn't exist, create it
  React.useEffect(() => {
    if (!section) {
      addUserSection(sectionType.id);
    }
  }, [section, addUserSection, sectionType.id]);

  if (!section || !sectionId) {
    return (
      <Card className="shadow-lg border-2 border-gray-200">
        <CardContent className="p-6">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Setting up {sectionName} section...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Function to add a figure with file data - using improved approach
  const addFigureWithFile = (file: File) => {
    const fileUrl = URL.createObjectURL(file);
    const fileName = file.name.split('.').slice(0, -1).join('.') || file.name;

    console.log('=== FIGURE UPLOAD DEBUG START ===');
    console.log('Adding figure with file:', {
      fileName,
      fileSize: file.size,
      fileType: file.type,
      sectionId,
      hasCustomAddFunction: !!addFigureWithData
    });

    // If we have a custom function for adding figures with data, use it
    if (addFigureWithData && sectionId) {
      console.log('Using custom addFigureWithData function');
      try {
        addFigureWithData(sectionId, {
          content: fileUrl,
          title: fileName,
          caption: '',
          analysis: '',
          originalFile: file
        });
        console.log('=== FIGURE UPLOAD SUCCESS (custom function) ===');
        toast.success('Figure uploaded successfully!');
        return;
      } catch (error) {
        console.error('Custom addFigureWithData failed:', error);
        // Fall back to the original method
      }
    }

    // Fallback to original method with improved retry logic
    console.log('Using fallback method with addContentItem + updateContentItem');

    // Log current state before adding
    const currentSection = userInputs.userSections.find(s => s.id === sectionId);
    console.log('Current section before addContentItem:', {
      sectionId,
      sectionExists: !!currentSection,
      itemCount: currentSection?.items.length || 0,
      items: currentSection?.items.map(item => ({
        id: item.id,
        type: item.type,
        hasContent: !!item.content,
        contentLength: item.content?.length || 0
      })) || []
    });

    // Add empty figure item
    console.log('Calling addContentItem...');
    addContentItem(sectionId, 'figure');

    // Use a more aggressive retry strategy with detailed logging
    const attemptUpdate = (attempt = 1, maxAttempts = 10) => {
      console.log(`\n--- Update attempt ${attempt}/${maxAttempts} ---`);

      const currentSection = userInputs.userSections.find(s => s.id === sectionId);

      if (!currentSection) {
        console.error('Section not found:', sectionId);
        console.log('Available sections:', userInputs.userSections.map(s => ({ id: s.id, name: s.name })));

        if (attempt < maxAttempts) {
          console.log('Retrying in', 100 * attempt, 'ms...');
          setTimeout(() => attemptUpdate(attempt + 1, maxAttempts), 100 * attempt);
          return false;
        } else {
          console.error('Section never found after', maxAttempts, 'attempts');
          toast.error('Section not found. Please refresh and try again.');
          return false;
        }
      }

      console.log('Section found:', {
        sectionId: currentSection.id,
        sectionName: currentSection.name,
        totalItems: currentSection.items.length
      });

      // Log all items in detail
      console.log('All items in section:');
      currentSection.items.forEach((item, index) => {
        console.log(`  Item ${index}:`, {
          id: item.id,
          type: item.type,
          hasContent: !!item.content,
          contentValue: item.content,
          contentType: typeof item.content,
          contentLength: item.content?.length || 0,
          isEmpty: item.content === '',
          isFalsy: !item.content
        });
      });

      const figureItems = currentSection.items.filter(item => item.type === 'figure');
      console.log('Figure items found:', figureItems.length);

      // Try different strategies to find the empty item
      let emptyFigureItem = null;

      // Strategy 1: Find item with empty string content
      emptyFigureItem = figureItems.find(item => item.content === '');
      if (emptyFigureItem) {
        console.log('Found empty figure item (strategy 1 - empty string):', emptyFigureItem.id);
      }

      // Strategy 2: Find item with falsy content
      if (!emptyFigureItem) {
        emptyFigureItem = figureItems.find(item => !item.content);
        if (emptyFigureItem) {
          console.log('Found empty figure item (strategy 2 - falsy content):', emptyFigureItem.id);
        }
      }

      // Strategy 3: Find the last added item (most recent timestamp)
      if (!emptyFigureItem && figureItems.length > 0) {
        const sortedItems = [...figureItems].sort((a, b) => parseInt(b.id) - parseInt(a.id));
        const lastItem = sortedItems[0];
        if (!lastItem.content || lastItem.content === '') {
          emptyFigureItem = lastItem;
          console.log('Found empty figure item (strategy 3 - last added):', emptyFigureItem.id);
        }
      }

      if (emptyFigureItem) {
        console.log('SUCCESS: Found empty figure item to update:', {
          id: emptyFigureItem.id,
          content: emptyFigureItem.content,
          contentType: typeof emptyFigureItem.content
        });

        console.log('Updating item with file data...');
        updateContentItem(sectionId, emptyFigureItem.id, {
          content: fileUrl,
          title: fileName,
          caption: '',
          analysis: '',
          originalFile: file
        });

        console.log('=== FIGURE UPLOAD SUCCESS ===');
        toast.success('Figure uploaded successfully!');
        return true;
      } else {
        console.log('No empty figure item found');

        if (attempt < maxAttempts) {
          console.log('Retrying in', 100 * attempt, 'ms...');
          setTimeout(() => attemptUpdate(attempt + 1, maxAttempts), 100 * attempt);
          return false;
        } else {
          console.error('=== FIGURE UPLOAD FAILED ===');
          console.error('Failed to find empty figure item after', maxAttempts, 'attempts');
          console.error('Final state:', {
            sectionExists: !!currentSection,
            totalItems: currentSection?.items.length || 0,
            figureItems: figureItems.length,
            allItems: currentSection?.items.map(item => ({
              id: item.id,
              type: item.type,
              content: item.content,
              hasContent: !!item.content
            })) || []
          });
          toast.error('Failed to upload figure. Please try again.');
          return false;
        }
      }
    };

    // Start the update attempts with a small initial delay
    console.log('Starting update attempts in 50ms...');
    setTimeout(() => attemptUpdate(), 50);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      console.log('File upload started:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        sectionId,
        currentSectionExists: !!userInputs.userSections.find(s => s.id === sectionId)
      });

      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
      if (!validTypes.includes(file.type)) {
        toast.error('Please upload a valid image file (JPEG, PNG, GIF, WebP) or PDF.');
        event.target.value = '';
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error('File size must be less than 10MB.');
        event.target.value = '';
        return;
      }

      try {
        addFigureWithFile(file);
      } catch (error) {
        console.error('Error adding figure with file:', error);
        toast.error('Failed to upload figure. Please try again.');
      }

      // Reset the file input
      event.target.value = '';
    } else {
      console.log('No file selected');
    }
  };

  const handleAIEnhancement = async (itemId: string, content: string) => {
    if (!content.trim()) {
      toast.error('Please add some content first');
      return;
    }

    updateContentItem(sectionId, itemId, { content: 'Enhancing content...' });

    try {
      const enhancementPrompt = `Enhance and improve the following ${sectionName} content for a research paper. Make it more professional, detailed, and academically rigorous while maintaining the original meaning:

Original content: ${content}

Please provide an enhanced version that:
- Uses formal academic language
- Adds relevant technical details where appropriate
- Improves clarity and structure
- Maintains scientific accuracy
- Is suitable for publication in a research journal`;

      const enhancedContent = await enhancedAIService.generateText(enhancementPrompt, localModel, {
        maxTokens: 1024,
        temperature: 0.7
      });

      updateContentItem(sectionId, itemId, { content: enhancedContent });
      toast.success('Content enhanced successfully!');
    } catch (error) {
      updateContentItem(sectionId, itemId, { content });
      toast.error('Failed to enhance content');
    }
  };

  // Function to toggle analysis visibility
  const toggleAnalysisVisibility = (itemId: string) => {
    setHiddenAnalysis(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // Function to get text area height based on size
  const getTextAreaHeight = () => {
    switch (textAreaSize) {
      case 'small': return 'min-h-[150px]';
      case 'medium': return 'min-h-[250px]';
      case 'large': return 'min-h-[400px]';
      default: return 'min-h-[250px]';
    }
  };

  // Function to get text area font size
  const getTextAreaFontSize = () => {
    switch (textAreaSize) {
      case 'small': return 'text-sm';
      case 'medium': return 'text-base';
      case 'large': return 'text-lg';
      default: return 'text-base';
    }
  };

  // Function to send analysis to the main text box
  const handleSendAnalysisToTextBox = (analysis: string) => {
    if (!analysis) {
      toast.error('No analysis available to send.');
      return;
    }

    // Find or create the text item for this section
    let textItem = section.items.find(item => item.type === 'text');

    if (!textItem) {
      // Create a new text item if it doesn't exist
      addContentItem(sectionId, 'text');

      // Wait a moment for the state to update, then add the analysis
      setTimeout(() => {
        const updatedSection = userInputs.userSections.find(s => s.id === sectionId);
        const newTextItem = updatedSection?.items.find(item => item.type === 'text');

        if (newTextItem) {
          const formattedAnalysis = `\n\n--- Figure Analysis ---\n${analysis}\n--- End Analysis ---\n\n`;
          updateContentItem(sectionId, newTextItem.id, {
            content: newTextItem.content + formattedAnalysis
          });
          toast.success('Analysis added to text area!');
        } else {
          toast.error('Failed to create text area. Please try again.');
        }
      }, 100);
    } else {
      // Add analysis to existing text content
      const currentContent = textItem.content || '';
      const formattedAnalysis = `\n\n--- Figure Analysis ---\n${analysis}\n--- End Analysis ---\n\n`;
      const newContent = currentContent + formattedAnalysis;

      updateContentItem(sectionId, textItem.id, { content: newContent });
      toast.success('Analysis added to text area!');
    }
  };

  const handleImageAnalysis = async (itemId: string, item: any) => {
    const figureTitle = item.title?.trim() || 'Untitled Figure';
    const originalFile = item.originalFile;

    console.log('Starting image analysis:', {
      itemId,
      hasOriginalFile: !!originalFile,
      figureTitle,
      model: localModel
    });

    if (!originalFile) {
      toast.error('Original image file not available for analysis. Please re-upload the image.');
      return;
    }

    setAnalyzingItems(prev => new Set(prev).add(itemId));

    try {
      // Convert file to base64 for analysis
      const convertToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = error => reject(error);
        });
      };

      console.log('Converting image to base64 for analysis...');
      const base64Data = await convertToBase64(originalFile);

      const analysisPrompt = `Analyze this research figure and provide a professional academic analysis:

Figure title: ${figureTitle}
Section: ${sectionName}

Please provide:
1. A detailed description of what the figure shows
2. Key findings or patterns visible in the data
3. Research implications and significance
4. How this relates to the ${sectionName} of the study

Write in formal academic language suitable for a research paper.`;

      console.log('Calling paperAIService.analyzeImageWithBase64 with:', {
        base64DataPrefix: base64Data.substring(0, 50) + '...',
        promptLength: analysisPrompt.length,
        model: localModel
      });

      // Use the new method that accepts base64 data directly
      const analysis = await paperAIService.analyzeImageWithBase64(base64Data, analysisPrompt, { model: localModel });

      console.log('Analysis completed:', {
        analysisLength: analysis?.length,
        analysisPreview: analysis?.substring(0, 100) + '...'
      });

      updateContentItem(sectionId, itemId, { analysis: analysis });
      toast.success('Figure analysis generated!');
    } catch (error: any) {
      console.error('Image analysis error:', error);
      const errorMessage = error?.message || 'Unknown error occurred';
      toast.error(`Failed to analyze figure: ${errorMessage}`);
    } finally {
      setAnalyzingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemId);
        return newSet;
      });
    }
  };

  const textItem = section.items.find(item => item.type === 'text');
  const figureItems = section.items.filter(item => item.type === 'figure');

  // Debug logging (can be removed in production)
  if (figureItems.length > 0) {
    console.log(`${sectionName} section: ${figureItems.length} figure(s) loaded`);
  }

  return (
    <Card className="shadow-lg border-2 border-gray-200">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${sectionType.color}`}>
                <sectionType.icon className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-2xl">{sectionName}</CardTitle>
                <p className="text-gray-600">{sectionType.description}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={activeTab === 'write' ? 'default' : 'outline'}
              onClick={() => setActiveTab('write')}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              Write
            </Button>
            <Button
              variant={activeTab === 'analyze' ? 'default' : 'outline'}
              onClick={() => setActiveTab('analyze')}
              className="flex items-center gap-2"
            >
              <Bot className="h-4 w-4" />
              Analyze
            </Button>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border-2 border-blue-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Brain className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-semibold text-gray-800">AI Model</p>
                  <p className="text-sm text-gray-600">
                    Using <strong>{AI_MODELS.find(m => m.id === localModel)?.name}</strong> for analysis and enhancement
                  </p>
                </div>
              </div>
              <select
                value={localModel}
                onChange={(e) => setLocalModel(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {AI_MODELS.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {activeTab === 'write' && (
          <div className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Write Your {sectionName} Content
                </h3>
              </div>

              {textItem ? (
                <div className="space-y-4">
                  {/* Text Area Size Controls */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-600">Text Size:</span>
                      <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                        <Button
                          variant={textAreaSize === 'small' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setTextAreaSize('small')}
                          className="h-7 px-3 text-xs"
                        >
                          Small
                        </Button>
                        <Button
                          variant={textAreaSize === 'medium' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setTextAreaSize('medium')}
                          className="h-7 px-3 text-xs"
                        >
                          Medium
                        </Button>
                        <Button
                          variant={textAreaSize === 'large' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setTextAreaSize('large')}
                          className="h-7 px-3 text-xs"
                        >
                          Large
                        </Button>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {textItem.content.length} characters
                    </Badge>
                  </div>

                  {/* Enhanced Text Area */}
                  <div className="relative">
                    <Textarea
                      placeholder={`Describe your ${sectionName.toLowerCase()} in detail. Include specific methods, procedures, findings, or analysis relevant to your research...`}
                      value={textItem.content}
                      onChange={(e) => updateContentItem(sectionId, textItem.id, { content: e.target.value })}
                      className={`${getTextAreaHeight()} ${getTextAreaFontSize()} border-2 border-gray-200 focus:border-blue-400 rounded-xl p-4 leading-relaxed resize-none focus:ring-2 focus:ring-blue-200 transition-all duration-200`}
                    />
                    <div className="absolute bottom-3 right-3 flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs bg-white/80 backdrop-blur-sm">
                        {textItem.content.split(' ').filter(word => word.length > 0).length} words
                      </Badge>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => handleAIEnhancement(textItem.id, textItem.content)}
                      disabled={!textItem.content.trim()}
                      className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <Zap className="h-4 w-4" />
                      Enhance with AI
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => updateContentItem(sectionId, textItem.id, { content: '' })}
                      className="flex items-center gap-2 border-2 border-gray-200 hover:border-red-300 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                      Clear Text
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-xl">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">No text content yet</p>
                  <Button
                    onClick={() => addContentItem(sectionId, 'text')}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Add Text Content
                  </Button>
                </div>
              )}
            </div>

            {/* Figure Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-green-600" />
                Add Figures & Analysis
              </h3>

              <div className="space-y-4">
                {figureItems.length > 0 ? (
                  figureItems.map((item, index) => (
                    <div key={item.id} className="relative border-2 border-gray-200 rounded-2xl p-6 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-blue-300">
                      {/* Figure Number Badge */}
                      <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                        {index + 1}
                      </div>

                      <div className="space-y-6">
                        {/* Step 1: Figure Title */}
                        <div className="space-y-3">
                          <label className="text-sm font-semibold text-gray-700 flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                              1
                            </div>
                            <span className="text-base">Figure Title</span>
                          </label>
                          <div className="relative">
                            <Input
                              placeholder="Enter a descriptive title for your figure..."
                              value={item.title || ''}
                              onChange={(e) => updateContentItem(sectionId, item.id, { title: e.target.value })}
                              className="border-2 border-gray-200 focus:border-blue-400 rounded-xl pl-4 pr-4 py-3 text-sm placeholder:text-gray-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            />
                            {item.title && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Step 2: Figure Preview */}
                        {item.content && (
                          <div className="space-y-3">
                            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                              <span className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                              Figure Preview
                            </label>
                            <div className="relative group">
                              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                              <div className="relative bg-white rounded-xl p-2 border-2 border-gray-200 group-hover:border-blue-300 transition-colors">
                                <img
                                  src={item.content}
                                  alt={item.title || 'Figure'}
                                  className="w-full h-48 object-cover rounded-lg"
                                />
                                <div className="absolute top-4 right-4">
                                  <div className="bg-green-500 text-white rounded-full p-1 shadow-lg">
                                    <CheckCircle className="h-5 w-5" />
                                  </div>
                                </div>
                                <div className="absolute bottom-4 left-4 right-4">
                                  <div className="bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg border border-gray-200">
                                    <div className="text-sm font-semibold text-gray-800">
                                      Figure {index + 1}: {item.title || 'Untitled'}
                                    </div>
                                    <div className="text-xs text-gray-600 mt-1">
                                      Ready for analysis
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Step 3: Figure Caption */}
                        <div className="space-y-3">
                          <label className="text-sm font-semibold text-gray-700 flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                              3
                            </div>
                            <span className="text-base">Figure Caption</span>
                            <Badge variant="outline" className="text-xs text-gray-500">Optional</Badge>
                          </label>
                          <div className="relative">
                            <Textarea
                              placeholder="Write a brief caption describing what this figure shows..."
                              value={item.caption || ''}
                              onChange={(e) => updateContentItem(sectionId, item.id, { caption: e.target.value })}
                              className="min-h-[80px] border-2 border-gray-200 focus:border-yellow-400 rounded-xl p-4 text-sm placeholder:text-gray-400 focus:ring-2 focus:ring-yellow-200 transition-all duration-200 resize-none"
                            />
                            <div className="absolute bottom-3 right-3 text-xs text-gray-400">
                              {(item.caption || '').length}/500
                            </div>
                          </div>
                        </div>

                        {/* Step 4: AI Analysis */}
                        {item.content && (
                          <div className="space-y-3">
                            <label className="text-sm font-semibold text-gray-700 flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                                4
                              </div>
                              <span className="text-base">AI Figure Analysis</span>
                              <Badge variant="secondary" className="bg-purple-100 text-purple-800 border-purple-200">
                                Powered by AI
                              </Badge>
                            </label>
                            <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 border border-purple-200">
                              <p className="text-sm text-gray-600 mb-3">
                                Generate detailed analysis using <strong>{AI_MODELS.find(m => m.id === localModel)?.name}</strong>
                              </p>
                              <div className="flex gap-2">
                                <Button
                                  onClick={() => handleImageAnalysis(item.id, item)}
                                  disabled={analyzingItems.has(item.id)}
                                  className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                                >
                                  {analyzingItems.has(item.id) ? (
                                    <>
                                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                      Analyzing...
                                    </>
                                  ) : (
                                    <>
                                      <Bot className="h-4 w-4 mr-2" />
                                      {item.analysis ? 'Reanalyze Figure' : 'Generate Analysis'}
                                    </>
                                  )}
                                </Button>
                                {item.analysis && (
                                  <Button
                                    onClick={() => handleSendAnalysisToTextBox(item.analysis)}
                                    variant="outline"
                                    className="flex items-center gap-2 border-2 border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 px-4"
                                    title="Insert analysis into the main text area"
                                  >
                                    <FileText className="h-4 w-4" />
                                    Send to Text
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Step 5: AI Analysis Results */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <label className="text-sm font-semibold text-gray-700 flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                                5
                              </div>
                              <span className="text-base">AI Analysis Results</span>
                              {analyzingItems.has(item.id) && (
                                <div className="flex items-center gap-2">
                                  <Loader2 className="h-4 w-4 animate-spin text-orange-500" />
                                  <Badge variant="secondary" className="bg-orange-100 text-orange-800 animate-pulse">
                                    Analyzing...
                                  </Badge>
                                </div>
                              )}
                            </label>
                            {item.analysis && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleAnalysisVisibility(item.id)}
                                className="flex items-center gap-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                              >
                                {hiddenAnalysis.has(item.id) ? (
                                  <>
                                    <ChevronDown className="h-4 w-4" />
                                    Show Analysis
                                  </>
                                ) : (
                                  <>
                                    <ChevronUp className="h-4 w-4" />
                                    Hide Analysis
                                  </>
                                )}
                              </Button>
                            )}
                          </div>
                          {item.analysis ? (
                            !hiddenAnalysis.has(item.id) ? (
                              <div className="relative">
                                <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-red-400 rounded-xl blur opacity-10"></div>
                                <div className="relative bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-5 border-2 border-orange-200 shadow-sm">
                                  <div className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                      <Bot className="h-4 w-4 text-white" />
                                    </div>
                                    <div className="flex-1">
                                      <div className="prose prose-sm max-w-none">
                                        <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">{item.analysis}</p>
                                      </div>
                                      <div className="flex items-center gap-2 mt-4">
                                        <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
                                          AI Generated Analysis
                                        </Badge>
                                        <Badge variant="outline" className="text-xs">
                                          {item.analysis.length} characters
                                        </Badge>
                                        <Badge variant="outline" className="text-xs">
                                          {item.analysis.split(' ').filter(word => word.length > 0).length} words
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-xl bg-gray-50">
                                <div className="flex items-center justify-center gap-2 text-gray-500">
                                  <Bot className="h-5 w-5" />
                                  <span className="text-sm font-medium">Analysis Hidden</span>
                                  <Badge variant="secondary" className="text-xs">
                                    {item.analysis.length} chars
                                  </Badge>
                                </div>
                                <p className="text-xs text-gray-400 mt-1">Click "Show Analysis" to view the generated content</p>
                              </div>
                            )
                          ) : (
                            <div className="text-center py-6 border-2 border-dashed border-orange-300 rounded-xl bg-orange-50/50">
                              <Bot className="h-8 w-8 text-orange-400 mx-auto mb-2" />
                              <p className="text-sm text-orange-600 font-medium">No analysis generated yet</p>
                              <p className="text-xs text-orange-500 mt-1">Click "Generate Analysis" to analyze this figure</p>
                            </div>
                          )}
                        </div>

                        {/* Remove Figure Button */}
                        <div className="pt-2 border-t border-gray-200">
                          <Button
                            onClick={() => removeContentItem(sectionId, item.id)}
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            Remove Figure
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="relative text-center py-12 border-2 border-dashed border-blue-300 rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 transition-all duration-300">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-2xl blur opacity-5"></div>
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <ImageIcon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">No figures uploaded yet</h3>
                      <p className="text-sm text-gray-600 mb-4">Upload your first figure to get started with AI-powered analysis</p>
                      <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span>Supports images and PDFs</span>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <span>AI analysis included</span>
                      </div>
                    </div>
                  </div>
                )}



                {/* Upload More Figures */}
                <div className="text-center pt-6">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-400 rounded-2xl blur opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
                    <div className="relative border-2 border-dashed border-green-300 rounded-2xl p-6 hover:border-green-400 transition-all duration-300 bg-gradient-to-br from-green-50/50 to-blue-50/50 hover:from-green-50 hover:to-blue-50">
                      <div className="space-y-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                          <ImageIcon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-800 mb-2">
                            {figureItems.length === 0 ? 'Upload Your First Figure' : 'Add Another Figure'}
                          </h4>
                          <p className="text-sm text-gray-600 mb-4">
                            {figureItems.length === 0
                              ? 'Start by uploading an image or PDF for AI analysis'
                              : 'Each upload creates a new figure with independent analysis'
                            }
                          </p>
                          <div className="relative">
                            <Input
                              type="file"
                              accept="image/*,.pdf"
                              onChange={handleFileUpload}
                              className="file:bg-gradient-to-r file:from-green-500 file:to-blue-500 file:text-white file:border-0 file:rounded-xl file:px-6 file:py-3 file:mr-4 file:font-semibold file:shadow-lg hover:file:from-green-600 hover:file:to-blue-600 file:transition-all file:duration-300 border-2 border-gray-200 rounded-xl hover:border-green-300 transition-colors"
                            />
                          </div>
                          <div className="flex items-center justify-center gap-4 mt-4 text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3 text-green-500" />
                              <span>JPEG, PNG, GIF, WebP</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3 text-blue-500" />
                              <span>PDF documents</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3 text-purple-500" />
                              <span>Max 10MB</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analyze' && (
          <div className="space-y-6">
            <div className="text-center">
              <Bot className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">AI Section Analysis</h3>
              <p className="text-gray-600">Get feedback and suggestions to improve your {sectionName.toLowerCase()} content</p>
            </div>

            {section.items.length === 0 ? (
              <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 text-center">
                <AlertTriangle className="h-12 w-12 text-amber-600 mx-auto mb-4" />
                <p className="text-amber-800 font-medium mb-2">No content to analyze</p>
                <p className="text-amber-700 text-sm">Add some text or figures to get AI feedback and suggestions.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-4">
                    Analyze your {sectionName.toLowerCase()} content using <strong>{AI_MODELS.find(m => m.id === localModel)?.name}</strong>
                  </p>
                  <Button
                    onClick={() => analyzeSectionContent(sectionId, sectionName)}
                    disabled={analysis?.isAnalyzing}
                    className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    {analysis?.isAnalyzing ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <TrendingUp className="h-4 w-4" />
                        Analyze Content
                      </>
                    )}
                  </Button>
                </div>

                {analysis && !analysis.isAnalyzing && (
                  <div className="space-y-4">
                    {/* Feedback */}
                    <Card className="bg-blue-50 border-blue-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <MessageSquare className="h-5 w-5 text-blue-600" />
                          AI Feedback
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700">{analysis.feedback}</p>
                      </CardContent>
                    </Card>

                    {/* Suggestions */}
                    {analysis.suggestions.length > 0 && (
                      <Card className="bg-yellow-50 border-yellow-200">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Lightbulb className="h-5 w-5 text-yellow-600" />
                            Suggestions for Improvement
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {analysis.suggestions.map((suggestion, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <span className="w-5 h-5 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                                  {index + 1}
                                </span>
                                <span className="text-gray-700">{suggestion}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    )}

                    {/* Fix Button */}
                    <div className="text-center space-y-3">
                      <p className="text-sm text-gray-600">
                        Apply improvements using <strong>{AI_MODELS.find(m => m.id === localModel)?.name}</strong>
                      </p>
                      <Button
                        onClick={() => fixSectionContent(sectionId, sectionName)}
                        disabled={isFixing.has(sectionId)}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                      >
                        {isFixing.has(sectionId) ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Applying Fixes...
                          </>
                        ) : (
                          <>
                            <Wrench className="h-4 w-4" />
                            Apply AI Improvements
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
