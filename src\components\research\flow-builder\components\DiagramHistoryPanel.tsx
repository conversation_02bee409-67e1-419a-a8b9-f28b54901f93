/**
 * Enhanced Diagram History Panel Component
 * Dedicated history panel for the header area with better accessibility and features
 */

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  History,
  Search,
  RefreshCw,
  Clock,
  Eye,
  Star,
  Heart,
  Trash2,
  GitBranch,
  Filter,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { diagramHistoryService } from '../services/diagram-history.service';
import { UserDiagram } from '../types';

interface DiagramHistoryPanelProps {
  onDiagramSelect?: (diagram: UserDiagram) => void;
  className?: string;
}

export const DiagramHistoryPanel: React.FC<DiagramHistoryPanelProps> = ({
  onDiagramSelect,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [historyDiagrams, setHistoryDiagrams] = useState<UserDiagram[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'favorites' | 'recent'>('all');

  // Load history when panel is opened
  useEffect(() => {
    if (isOpen && !historyLoading) {
      loadHistory();
    }
  }, [isOpen, searchQuery, filterType]);

  const loadHistory = async () => {
    setHistoryLoading(true);
    try {
      const { diagrams, error } = await diagramHistoryService.getUserDiagrams({
        limit: 50,
        search: searchQuery || undefined,
        favorites_only: filterType === 'favorites'
      });

      if (!error) {
        let filteredDiagrams = diagrams || [];

        // Apply additional filtering
        if (filterType === 'recent') {
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          filteredDiagrams = filteredDiagrams.filter(
            diagram => new Date(diagram.created_at) > oneWeekAgo
          );
        }

        setHistoryDiagrams(filteredDiagrams);
      }
    } catch (error) {
      console.error('Failed to load history:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleSelectDiagram = (diagramId: string) => {
    const diagram = history.diagrams.find(d => d.id === diagramId);
    if (diagram) {
      onSelectDiagram(diagram);
    }
  };

  const formatTimeAgo = (date: Date | string) => {
    const now = new Date();
    const targetDate = date instanceof Date ? date : new Date(date);
    const diffMs = now.getTime() - targetDate.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const getTypeIcon = (type: string) => {
    return DIAGRAM_TYPE_CONFIG[type as keyof typeof DIAGRAM_TYPE_CONFIG]?.icon || '📊';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5 text-blue-600" />
          Diagram History
          <Badge variant="outline" className="ml-auto">
            {history.totalGenerated} total
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filter */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search diagrams..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        {/* Type Filter */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={filterType === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilterType('all')}
          >
            All Types
          </Button>
          {Object.entries(DIAGRAM_TYPE_CONFIG).slice(0, 4).map(([type, config]) => (
            <Button
              key={type}
              variant={filterType === type ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilterType(type)}
              className="text-xs"
            >
              {config.icon} {config.name}
            </Button>
          ))}
        </div>

        <Separator />

        {/* Diagram List */}
        <ScrollArea className="h-96">
          {Object.keys(groupedDiagrams).length === 0 ? (
            <div className="text-center py-8">
              <GitBranch className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">No diagrams found</h3>
              <p className="text-gray-500">
                {searchQuery ? 'Try adjusting your search terms' : 'Create your first diagram to get started'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(groupedDiagrams).map(([date, diagrams]) => (
                <div key={date} className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <Calendar className="h-4 w-4" />
                    {date}
                  </div>
                  
                  <div className="space-y-2">
                    {diagrams.map((diagram) => (
                      <Card
                        key={diagram.id}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          currentDiagramId === diagram.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                        }`}
                        onClick={() => handleSelectDiagram(diagram.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-lg">{getTypeIcon(diagram.metadata.type)}</span>
                                <h4 className="font-medium text-sm truncate">
                                  {diagram.metadata.title}
                                </h4>
                                <Badge variant="outline" className="text-xs">
                                  {diagram.metadata.type}
                                </Badge>
                              </div>
                              
                              <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                                {diagram.metadata.description}
                              </p>
                              
                              <div className="flex items-center gap-3 text-xs text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatTimeAgo(diagram.generatedAt)}
                                </div>
                                <div className="flex items-center gap-1">
                                  <span>Model: {diagram.model.split('/').pop()}</span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1 ml-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onSelectDiagram(diagram);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(diagram.mermaidCode);
                                }}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDeleteDiagram(diagram.id);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {/* Summary Stats */}
        {history.diagrams.length > 0 && (
          <>
            <Separator />
            <div className="grid grid-cols-3 gap-4 text-center text-sm">
              <div>
                <div className="font-semibold text-blue-600">{history.totalGenerated}</div>
                <div className="text-gray-500">Total Created</div>
              </div>
              <div>
                <div className="font-semibold text-green-600">
                  {new Set(history.diagrams.map(d => d.metadata.type)).size}
                </div>
                <div className="text-gray-500">Types Used</div>
              </div>
              <div>
                <div className="font-semibold text-purple-600">
                  {history.lastGenerated ? formatTimeAgo(history.lastGenerated) : 'Never'}
                </div>
                <div className="text-gray-500">Last Created</div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
