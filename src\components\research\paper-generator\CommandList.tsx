import React, { forwardRef, useImperativeHandle, useState, useEffect } from 'react'

interface CommandListProps {
  items: Array<{
    title: string
    description?: string
  }>
  command: (item: any) => void
}

export const CommandList = forwardRef<
  { onKeyDown: ({ event }: { event: KeyboardEvent }) => boolean },
  CommandListProps
>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const selectItem = (index: number) => {
    const item = props.items[index]
    if (item) {
      props.command(item)
    }
  }

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length)
  }

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length)
  }

  const enterHandler = () => {
    selectItem(selectedIndex)
  }

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler()
        return true
      }

      if (event.key === 'ArrowDown') {
        downHandler()
        return true
      }

      if (event.key === 'Enter') {
        enterHandler()
        return true
      }

      return false
    },
  }))

  useEffect(() => {
    setSelectedIndex(0)
  }, [props.items])

  return (
    <div className="command-list bg-white rounded-md shadow-lg border border-gray-200 overflow-hidden max-h-72 overflow-y-auto max-w-md" style={{ minWidth: '280px' }}>
      {props.items.length ? (
        props.items.map((item, index) => (
          <button
            key={index}
            className={`flex items-start p-2 w-full text-left hover:bg-blue-50 transition-colors ${index === selectedIndex ? 'bg-blue-100' : ''}`}
            onClick={() => selectItem(index)}
          >
            <div>
              <div className="font-medium text-sm text-gray-800">{item.title}</div>
              {item.description && <div className="text-xs text-gray-500">{item.description}</div>}
            </div>
          </button>
        ))
      ) : (
        <div className="p-2 text-sm text-gray-500">No results</div>
      )}
    </div>
  )
})

CommandList.displayName = 'CommandList'
