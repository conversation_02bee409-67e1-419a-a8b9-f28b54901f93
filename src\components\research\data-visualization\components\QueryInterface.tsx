import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Send,
  MessageSquare,
  Bot,
  User,
  Code,
  BarChart3,
  Loader2,
  Copy,
  CheckCircle,
  AlertTriangle,
  Clock,
  Sparkles
} from "lucide-react";
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { QueryRequest, QueryResponse, UploadedFile } from '../types';
import { DATA_VIZ_CONFIG, ERROR_MESSAGES } from '../constants';
import { nanoid } from 'nanoid';

interface QueryInterfaceProps {
  file: UploadedFile;
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  code?: string;
  visualizations?: any[];
  timestamp: Date;
  status?: 'sending' | 'completed' | 'error';
  error?: string;
}

export const QueryInterface: React.FC<QueryInterfaceProps> = ({
  file,
  className = ""
}) => {
  const {
    addQuery,
    updateQuery,
    addQueryResponse,
    setQuerying,
    addError,
    isQuerying
  } = useDataVisualizationStore();

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'assistant',
      content: `Hello! I'm your AI data analyst. I can help you explore and analyze your dataset "${file.name}". Ask me questions like:

• "What are the main patterns in this data?"
• "Show me the correlation between [column1] and [column2]"
• "What are the outliers in [column]?"
• "Create a visualization of [column] by [category]"
• "Summarize the key statistics"

What would you like to know about your data?`,
      timestamp: new Date()
    }
  ]);

  const [currentQuery, setCurrentQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmitQuery = async () => {
    if (!currentQuery.trim() || isProcessing) return;

    if (currentQuery.length > DATA_VIZ_CONFIG.MAX_QUERY_LENGTH) {
      addError(ERROR_MESSAGES.QUERY_TOO_LONG);
      return;
    }

    const queryId = nanoid();
    const userMessage: ChatMessage = {
      id: `user-${queryId}`,
      type: 'user',
      content: currentQuery.trim(),
      timestamp: new Date()
    };

    const assistantMessage: ChatMessage = {
      id: `assistant-${queryId}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      status: 'sending'
    };

    // Add messages to chat
    setMessages(prev => [...prev, userMessage, assistantMessage]);
    
    // Create query request
    const queryRequest: QueryRequest = {
      id: queryId,
      fileId: file.id,
      question: currentQuery.trim(),
      timestamp: new Date(),
      status: 'processing'
    };

    addQuery(queryRequest);
    setCurrentQuery('');
    setIsProcessing(true);
    setQuerying(true);

    try {
      // Process query with Gemini
      const response = await GeminiAnalysisService.processQuery(file, queryRequest.question);
      
      // Create query response
      const queryResponse: QueryResponse = {
        id: nanoid(),
        requestId: queryId,
        answer: response.text || 'Analysis completed',
        code: response.code,
        visualizations: response.visualizations,
        executionTime: 0 // TODO: Track actual execution time
      };

      addQueryResponse(queryResponse);
      updateQuery(queryId, { status: 'completed' });

      // Update assistant message
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? {
              ...msg,
              content: queryResponse.answer,
              code: queryResponse.code,
              visualizations: queryResponse.visualizations,
              status: 'completed'
            }
          : msg
      ));

      toast.success('Query processed successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Query processing failed';
      
      updateQuery(queryId, { status: 'error' });
      addError(errorMessage);

      // Update assistant message with error
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? {
              ...msg,
              content: 'I apologize, but I encountered an error processing your query. Please try rephrasing your question or check if your data is properly formatted.',
              status: 'error',
              error: errorMessage
            }
          : msg
      ));

      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
      setQuerying(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitQuery();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const suggestedQuestions = [
    "What are the main patterns in this data?",
    "Show me summary statistics for all numeric columns",
    "Are there any outliers in the data?",
    "What's the correlation between variables?",
    "Create a visualization of the data distribution"
  ];

  return (
    <div className={`flex flex-col h-[600px] ${className}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Ask Questions About Your Data
          </CardTitle>
          <p className="text-sm text-gray-500">
            Use natural language to explore and analyze your dataset
          </p>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          {/* Messages Area */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className="space-y-3">
                  <div className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex gap-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      {/* Avatar */}
                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {message.type === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                      </div>

                      {/* Message Content */}
                      <div className={`space-y-2 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                        <div className={`inline-block p-3 rounded-lg ${
                          message.type === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          {message.status === 'sending' ? (
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Analyzing your question...</span>
                            </div>
                          ) : (
                            <div className="whitespace-pre-wrap">{message.content}</div>
                          )}
                        </div>

                        {/* Code Block */}
                        {message.code && (
                          <div className="bg-gray-900 text-gray-100 p-3 rounded-lg text-sm font-mono">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Code className="h-4 w-4" />
                                <span className="text-xs">Generated Code</span>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(message.code!)}
                                className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                            <pre className="whitespace-pre-wrap text-xs">{message.code}</pre>
                          </div>
                        )}

                        {/* Visualizations */}
                        {message.visualizations && message.visualizations.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <BarChart3 className="h-4 w-4" />
                              <span>Generated Visualizations</span>
                            </div>
                            {/* TODO: Render actual visualizations */}
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <p className="text-sm text-blue-700">
                                {message.visualizations.length} visualization(s) created
                              </p>
                            </div>
                          </div>
                        )}

                        {/* Error */}
                        {message.status === 'error' && (
                          <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-xs">
                              {message.error || 'An error occurred processing this query'}
                            </AlertDescription>
                          </Alert>
                        )}

                        {/* Timestamp */}
                        <div className="text-xs text-gray-500">
                          {formatTimestamp(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <Separator />

          {/* Suggested Questions */}
          {messages.length === 1 && (
            <div className="p-4 border-b">
              <p className="text-sm font-medium mb-2">Suggested questions:</p>
              <div className="flex flex-wrap gap-2">
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentQuery(question)}
                    className="text-xs"
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    {question}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Input Area */}
          <div className="p-4">
            <div className="flex gap-2">
              <Textarea
                ref={textareaRef}
                value={currentQuery}
                onChange={(e) => setCurrentQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask a question about your data..."
                className="flex-1 min-h-[60px] max-h-[120px] resize-none"
                disabled={isProcessing}
              />
              <Button
                onClick={handleSubmitQuery}
                disabled={!currentQuery.trim() || isProcessing}
                className="self-end"
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            {currentQuery.length > DATA_VIZ_CONFIG.MAX_QUERY_LENGTH * 0.8 && (
              <p className="text-xs text-gray-500 mt-1">
                {DATA_VIZ_CONFIG.MAX_QUERY_LENGTH - currentQuery.length} characters remaining
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
