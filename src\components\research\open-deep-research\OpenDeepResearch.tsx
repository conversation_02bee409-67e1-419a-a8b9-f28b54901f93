/**
 * Open Deep Research - Main Component
 * A powerful research assistant that generates comprehensive AI-powered reports
 */

import React, { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';
import { Brain, Code, Loader2, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';

import { useOpenDeepResearchStore } from './stores/open-deep-research.store';
import { SearchInterface } from './components/SearchInterface';
import { AgentProgress } from './components/AgentProgress';
import { ResultsList } from './components/ResultsList';
import { ReportViewer } from './components/ReportViewer';
import { KnowledgeBaseSidebar } from './components/KnowledgeBaseSidebar';
import { HistoryPanel } from './components/HistoryPanel';
import { searchService } from './services/search.service';
import { reportService } from './services/report.service';
import { contentService } from './services/content.service';
import { knowledgeBaseService } from './services/knowledge-base.service';
import { supabaseService } from './services/supabase.service';
import { OpenDeepResearchProps, ResearchHistoryItem, SearchResult } from './types';
import { SEARCH_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from './constants';

export const OpenDeepResearch: React.FC<OpenDeepResearchProps> = ({ className }) => {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [selectedHistoryItem, setSelectedHistoryItem] = useState<ResearchHistoryItem | null>(null);

  const {
    // State
    query,
    timeFilter,
    results,
    selectedResults,
    reportPrompt,
    report,
    error,
    newUrl,
    selectedModel,
    isAgentMode,
    sidebarOpen,
    activeTab,
    status,

    // Actions
    setQuery,
    setTimeFilter,
    setResults,
    addResult,
    removeResult,
    setSelectedResults,
    toggleResultSelection,
    setReportPrompt,
    setReport,
    setError,
    setNewUrl,
    setSelectedModel,
    setIsAgentMode,
    setSidebarOpen,
    setActiveTab,
    setLoading,
    setGeneratingReport,
    setAgentStep,
    addAgentInsight,
    clearAgentInsights,
    setSearchQueries,
    updateStatus,
    clearResults,
    clearReport,
  } = useOpenDeepResearchStore();

  // Initialize Supabase on component mount
  useEffect(() => {
    if (!supabaseService.isConfigured()) {
      console.warn('Supabase not configured. Some features may not work properly.');
    }
  }, []);

  // Error handler
  const handleError = useCallback((error: unknown, context: string) => {
    let message = ERROR_MESSAGES.unknownError;
    
    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }
    
    setError(message);
    toast.error(context, {
      description: message,
      duration: 5000,
    });
  }, [setError]);

  // Create new session when starting research
  const createNewSession = useCallback(async () => {
    try {
      if (!supabaseService.isConfigured()) {
        console.warn('Supabase not configured, continuing without session tracking');
        return null;
      }

      const session = await supabaseService.createSession({
        title: query || reportPrompt || 'Research Session',
        query: query || reportPrompt,
        report_prompt: reportPrompt,
        selected_model: selectedModel,
        is_agent_mode: isAgentMode,
        status: 'processing',
      });

      if (session) {
        setCurrentSessionId(session.id);
        return session.id;
      }
    } catch (error) {
      console.error('Failed to create session:', error);
    }
    return null;
  }, [query, reportPrompt, selectedModel, isAgentMode]);

  // Search handler
  const handleSearch = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    // Check if this is report generation
    if (selectedResults.length > 0 && !isAgentMode) {
      await handleGenerateReport();
      return;
    }

    setLoading(true);
    setError(null);
    clearResults();

    // Create new session
    const sessionId = await createNewSession();

    try {
      const searchResults = await searchService.search({
        query,
        timeFilter,
        provider: SEARCH_CONFIG.provider,
      });

      setResults(searchResults);

      // Save search results to database
      if (sessionId && searchResults.length > 0) {
        await supabaseService.saveSources(sessionId, searchResults);
      }

      if (searchResults.length === 0) {
        toast.info('No Results', {
          description: 'No search results found. Try a different query.',
        });
      }
    } catch (error) {
      handleError(error, 'Search Error');
      // Update session status to error
      if (sessionId) {
        await supabaseService.updateSessionStatus(sessionId, 'error');
      }
    } finally {
      setLoading(false);
    }
  }, [
    query,
    timeFilter,
    selectedResults.length,
    isAgentMode,
    setLoading,
    setError,
    clearResults,
    setResults,
    handleError,
    createNewSession,
  ]);

  // Agent search handler
  const handleAgentSearch = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reportPrompt.trim()) {
      toast.error('Missing Information', {
        description: 'Please provide a research topic',
      });
      return;
    }

    setAgentStep('processing');
    clearAgentInsights();
    setSearchQueries([]);
    setError(null);
    clearResults();
    clearReport();

    try {
      // Step 1: Optimize research query
      addAgentInsight('Analyzing research topic and optimizing search strategy...');
      const optimization = await searchService.optimizeResearch({
        prompt: reportPrompt,
        model: selectedModel,
      });

      setQuery(optimization.query);
      setSearchQueries([optimization.query]);
      addAgentInsight(`Research strategy: ${optimization.explanation}`);

      // Step 2: Perform search
      setAgentStep('searching');
      addAgentInsight('Searching for relevant sources...');
      const searchResults = await searchService.search({
        query: optimization.query,
        timeFilter,
        provider: SEARCH_CONFIG.provider,
      });

      if (searchResults.length === 0) {
        throw new Error('No search results found. Please try a different query.');
      }

      // Step 3: Analyze and rank results
      setAgentStep('analyzing');
      addAgentInsight('Analyzing and ranking search results...');
      const analysis = await searchService.analyzeResults({
        prompt: optimization.optimizedPrompt,
        results: searchResults,
        model: selectedModel,
      });

      // Select top results
      const rankedResults = searchResults
        .map(result => ({
          ...result,
          score: analysis.rankings.find(r => r.url === result.url)?.score || 0,
        }))
        .sort((a, b) => (b.score || 0) - (a.score || 0));

      const selectedUrls = new Set<string>();
      const topResults = rankedResults.filter(result => {
        if (selectedUrls.size >= SEARCH_CONFIG.maxSelectableResults) return false;
        const domain = new URL(result.url).hostname;
        const hasSimilar = Array.from(selectedUrls).some(url => 
          new URL(url).hostname === domain
        );
        if (!hasSimilar && result.score && result.score > 0.5) {
          selectedUrls.add(result.url);
          return true;
        }
        return false;
      });

      setResults(rankedResults);
      setSelectedResults(topResults.map(r => r.id));
      addAgentInsight(`Selected ${topResults.length} high-quality sources from ${new Set(topResults.map(r => new URL(r.url).hostname)).size} unique domains`);

      // Step 4: Generate report
      setAgentStep('generating');
      addAgentInsight('Generating comprehensive report...');
      await handleGenerateReport(topResults, optimization.optimizedPrompt);

      addAgentInsight('Report generated successfully!');
      setAgentStep('idle');
      setActiveTab('report');

    } catch (error) {
      handleError(error, 'Agent Research Error');
      setAgentStep('idle');
    }
  }, [
    reportPrompt,
    timeFilter,
    selectedModel,
    setAgentStep,
    clearAgentInsights,
    setSearchQueries,
    setError,
    clearResults,
    clearReport,
    addAgentInsight,
    setQuery,
    setResults,
    setSelectedResults,
    setActiveTab,
    handleError,
  ]);

  // Generate report handler
  const handleGenerateReport = useCallback(async (
    customResults?: SearchResult[],
    customPrompt?: string
  ) => {
    // Ensure we have a valid array
    let resultsToUse: SearchResult[] = [];

    if (customResults && Array.isArray(customResults)) {
      resultsToUse = customResults;
    } else if (Array.isArray(results)) {
      resultsToUse = results.filter(r => selectedResults.includes(r.id));
    } else {
      console.error('Results is not an array:', results);
      toast.error('Invalid Data', {
        description: 'Search results data is invalid. Please try searching again.',
      });
      return;
    }

    const promptToUse = customPrompt || `${query}. Provide comprehensive analysis.`;

    if (resultsToUse.length === 0) {
      toast.error('No Sources Selected', {
        description: 'Please select at least one source to generate a report.',
      });
      return;
    }

    setGeneratingReport(true);
    updateStatus({
      fetchStatus: {
        total: resultsToUse.length,
        successful: 0,
        fallback: 0,
        sourceStatuses: {},
      },
    });

    // Create session if not already created
    let sessionId = currentSessionId;
    if (!sessionId) {
      const newSession = await createNewSession();
      sessionId = newSession;
    }

    try {
      // Update session status
      if (sessionId) {
        await supabaseService.updateSessionStatus(sessionId, 'processing');
      }

      // Fetch content for selected results
      const contentResults = await Promise.all(
        resultsToUse.map(async (result) => {
          if (result.content) {
            updateStatus(prev => ({
              fetchStatus: {
                ...prev.fetchStatus,
                successful: prev.fetchStatus.successful + 1,
                sourceStatuses: {
                  ...prev.fetchStatus.sourceStatuses,
                  [result.url]: 'fetched',
                },
              },
            }));
            return {
              url: result.url,
              title: result.name,
              content: result.content,
            };
          }

          try {
            const content = await contentService.fetchContent(result.url);
            updateStatus(prev => ({
              fetchStatus: {
                ...prev.fetchStatus,
                successful: prev.fetchStatus.successful + 1,
                sourceStatuses: {
                  ...prev.fetchStatus.sourceStatuses,
                  [result.url]: 'fetched',
                },
              },
            }));
            return {
              url: result.url,
              title: result.name,
              content,
            };
          } catch (error) {
            updateStatus(prev => ({
              fetchStatus: {
                ...prev.fetchStatus,
                fallback: prev.fetchStatus.fallback + 1,
                sourceStatuses: {
                  ...prev.fetchStatus.sourceStatuses,
                  [result.url]: 'preview',
                },
              },
            }));
            return {
              url: result.url,
              title: result.name,
              content: result.snippet,
            };
          }
        })
      );

      // Generate report
      const generatedReport = await reportService.generateReport({
        selectedResults: contentResults.filter(r => r.content?.trim()),
        sources: resultsToUse,
        prompt: promptToUse,
        model: selectedModel,
      });

      setReport(generatedReport);
      setActiveTab('report');

      // Save report to database
      if (sessionId) {
        await supabaseService.saveReport(sessionId, generatedReport);
        await supabaseService.updateSessionStatus(sessionId, 'completed');

        // Save selected sources
        await supabaseService.saveSources(
          sessionId,
          resultsToUse,
          selectedResults
        );
      }

      toast.success(SUCCESS_MESSAGES.reportGenerated);
    } catch (error) {
      handleError(error, 'Report Generation Failed');

      // Update session status to error
      if (sessionId) {
        await supabaseService.updateSessionStatus(sessionId, 'error');
      }
    } finally {
      setGeneratingReport(false);
    }
  }, [
    results,
    selectedResults,
    query,
    selectedModel,
    currentSessionId,
    setGeneratingReport,
    updateStatus,
    setReport,
    setActiveTab,
    handleError,
    createNewSession,
  ]);

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 p-4 sm:p-8 ${className}`}>
      <KnowledgeBaseSidebar
        open={sidebarOpen}
        onOpenChange={setSidebarOpen}
      />
      
      <main className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Open Deep Research
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Generate comprehensive AI-powered research reports from web search results. 
            Choose between manual source selection or let our AI agent automatically find and analyze the best sources.
          </p>
          
          <div className="flex flex-wrap justify-center items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="inline-flex items-center gap-2"
            >
              <Brain className="h-4 w-4" />
              Knowledge Base
            </Button>
            <Button
              asChild
              variant="outline"
              size="sm"
              className="inline-flex items-center gap-2"
            >
              <a
                href="https://github.com/btahir/open-deep-research"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Code className="h-4 w-4" />
                View Source
              </a>
            </Button>
          </div>
        </div>

        <Separator />

        {/* Agent Progress */}
        {status.agentStep !== 'idle' && (
          <AgentProgress status={status} />
        )}

        {/* Search Interface */}
        <SearchInterface
          query={query}
          timeFilter={timeFilter}
          selectedModel={selectedModel}
          isAgentMode={isAgentMode}
          reportPrompt={reportPrompt}
          newUrl={newUrl}
          status={status}
          onQueryChange={setQuery}
          onTimeFilterChange={setTimeFilter}
          onModelChange={setSelectedModel}
          onAgentModeChange={setIsAgentMode}
          onReportPromptChange={setReportPrompt}
          onNewUrlChange={setNewUrl}
          onSearch={handleSearch}
          onAgentSearch={handleAgentSearch}
          onAddCustomUrl={() => {}} // TODO: Implement
          onFileUpload={() => {}} // TODO: Implement
          onGenerateReport={handleGenerateReport}
        />

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <div>
                  <h3 className="font-semibold">Error</h3>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'search' | 'report' | 'history')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="search">
              Search Results {results.length > 0 && `(${results.length})`}
            </TabsTrigger>
            <TabsTrigger value="report" disabled={!report && !selectedHistoryItem?.report}>
              Report
            </TabsTrigger>
            <TabsTrigger value="history">
              History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-4">
            {results.length > 0 ? (
              <ResultsList
                results={results}
                selectedResults={selectedResults}
                maxSelections={SEARCH_CONFIG.maxSelectableResults}
                onResultSelect={toggleResultSelection}
                onRemoveResult={removeResult}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="text-gray-400 mb-2">
                    <Search className="h-12 w-12 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-600 mb-2">No Search Results</h3>
                  <p className="text-gray-500">
                    Start by entering a search query or research topic above.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="report">
            <ReportViewer
              report={selectedHistoryItem?.report ? {
                title: selectedHistoryItem.report.title,
                summary: selectedHistoryItem.report.summary,
                sections: selectedHistoryItem.report.sections,
                sources: selectedHistoryItem.report.sources,
                usedSources: selectedHistoryItem.report.used_sources,
              } : report}
              reportPrompt={selectedHistoryItem?.session.query || reportPrompt}
            />
          </TabsContent>

          <TabsContent value="history">
            <HistoryPanel
              onSelectSession={(historyItem) => {
                setSelectedHistoryItem(historyItem);
                if (historyItem.report) {
                  setActiveTab('report');
                }
              }}
            />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};
