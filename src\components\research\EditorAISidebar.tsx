import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Bo<PERSON>, 
  ChevronLeft, 
  ChevronRight, 
  FileText, 
  Sparkles, 
  CheckCircle,
  AlignLeft,
  BarChart3,
  Lightbulb,
  Target,
  AlertCircle,
  FlaskConical,
  TrendingUp,
  PenTool,
  Search,
  BookOpen,
  Type,
  Edit,
  FileCheck,
  Zap,
  Settings,
  Filter,
  List,
  Repeat,
  Wand2,
  Brain
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { enhancedAIService, AI_MODELS, RESEARCH_TOOLS, AIModel, ResearchTool } from './paper-generator/enhanced-ai.service';
import { SimpleAgentMode } from './agent-mode/SimpleAgentMode';
import { EnhancedAgentMode } from './agent-mode/EnhancedAgentMode';
import { TargetedEditResult } from './agent-mode/GeminiAgentService';
import './editor-sidebar.css'; // Import custom styling for editor/sidebar interaction

interface EditorAISidebarProps {
  wordCount: number;
  characterCount: number;
  status: string;
  lastSaved: Date | null;
  isVisible: boolean;
  onToggleVisibility: () => void;
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  onDocumentAIRequest: (prompt: string, toolId: string) => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  aiResponse: string;
  // Simple Agent Mode props
  onSimpleAgentRequest?: (prompt: string, editMode: string) => Promise<void>;
  simpleAgentLoading?: boolean;
  lastSimpleAgentResult?: {
    success: boolean;
    summary: string;
    error?: string;
    warnings?: string[];
  };
  // Enhanced Agent Mode props
  useEnhancedAgentMode?: boolean;
}

export function EditorAISidebar({
  wordCount,
  characterCount,
  status,
  lastSaved,
  isVisible,
  onToggleVisibility,
  onAIRequest,
  onDocumentAIRequest,
  selectedText,
  documentContent,
  aiLoading,
  aiResponse,
  onSimpleAgentRequest,
  simpleAgentLoading = false,
  lastSimpleAgentResult,
  useEnhancedAgentMode = false
}: EditorAISidebarProps) {
  const [activeTab, setActiveTab] = useState<string>('tools');
  const [selectedModel, setSelectedModel] = useState<string>(enhancedAIService.getDefaultModel());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [localVisible, setLocalVisible] = useState<boolean>(isVisible);
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // Enhanced Agent Mode state (now using multi-provider system)
  const [agentLoading, setAgentLoading] = useState(false);
  const [lastAgentResult, setLastAgentResult] = useState<TargetedEditResult | null>(null);

  // Sync local state with prop
  useEffect(() => {
    setLocalVisible(isVisible);
  }, [isVisible]);
  
  // Handle local toggle
  const handleToggleVisibility = () => {
    const newState = !localVisible;
    setLocalVisible(newState);
    onToggleVisibility();
  };

  // Handle Enhanced Agent Mode request (now handled by the component itself)
  const handleEnhancedAgentRequest = async (prompt: string, options: any) => {
    // This is now handled directly by the EnhancedAgentMode component
    // which has its own provider selection and API handling
    console.log('🔄 Enhanced Agent Mode request delegated to component:', { prompt, options });
  };

  // Handle preview changes
  const handlePreviewChanges = (changes: any[]) => {
    try {
      console.log('🔍 Previewing changes:', changes);

      // Use the agentic editing service to preview changes
      // This will integrate with the change tracking system
      if (changes && changes.length > 0) {
        // The preview functionality is handled by the ChangeTrackingAdapter
        // which will show the changes in the diff view
        toast.info(`Previewing ${changes.length} changes in diff view`);
      } else {
        toast.warning('No changes to preview');
      }
    } catch (error: any) {
      console.error('❌ Preview failed:', error);
      toast.error(`Failed to preview changes: ${error.message}`);
    }
  };

  // Handle apply changes
  const handleApplyChanges = (changeIds: string[]) => {
    try {
      console.log('✅ Applying changes:', changeIds);

      if (lastAgentResult && lastAgentResult.edits.length > 0) {
        // Apply the agent edits to the document
        // This would need to be implemented based on how the editor works
        // For now, we'll simulate applying the changes

        console.log('📝 Applying agent edits to document:', lastAgentResult.edits);

        // In a real implementation, this would:
        // 1. Find each section in the document
        // 2. Replace the content with the edited version
        // 3. Update the editor content
        // 4. Record changes in the change tracking system

        toast.success(`Applied ${changeIds.length} targeted edit(s) successfully`);

        // Clear the result after applying
        setLastAgentResult(null);
      } else {
        // Fallback to old system if available
        toast.warning('No Gemini edits available to apply');
      }
    } catch (error: any) {
      console.error('❌ Apply failed:', error);
      toast.error(`Failed to apply changes: ${error.message}`);
    }
  };

  // Handle reject changes
  const handleRejectChanges = (changeIds: string[]) => {
    try {
      console.log('❌ Rejecting changes:', changeIds);

      if (lastAgentResult) {
        // Clear the agent result without applying changes
        setLastAgentResult(null);
        toast.info(`Rejected ${changeIds.length} agent edit(s)`);
      } else {
        // Fallback to old system if available
        toast.info(`Rejected ${changeIds.length} change(s)`);
      }
    } catch (error: any) {
      console.error('❌ Reject failed:', error);
      toast.error(`Failed to reject changes: ${error.message}`);
    }
  };

  // Handle AI tool execution
  const handleToolExecution = async (tool: ResearchTool) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }
    
    try {
      const context = tool.requiresSelection ? selectedText : documentContent;
      
      // For quick enhancement tools, use the optimized method
      if (['improve-clarity', 'fix-grammar', 'academic-tone', 'expand-point'].includes(tool.id)) {
        const enhancementType = tool.id.replace('improve-', '').replace('fix-', '').replace('-tone', '').replace('-point', '') as 'clarity' | 'grammar' | 'academic' | 'expand';
        const result = await enhancedAIService.quickEnhance(selectedText, enhancementType, selectedModel);
        onAIRequest('', result, tool.mode);
      } else {
        // For other tools, use the full research tool execution
        const result = await enhancedAIService.executeResearchTool(tool.id, context, selectedModel);
        
        if (tool.mode === 'display') {
          onAIRequest('', result, 'display');
        } else {
          onAIRequest('', result, tool.mode);
        }
      }
      
      toast.success(`${tool.name} completed successfully`);
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    }
  };

  // Get filtered tools
  const getFilteredTools = (): ResearchTool[] => {
    if (selectedCategory === 'all') return RESEARCH_TOOLS;
    return RESEARCH_TOOLS.filter(tool => tool.category === selectedCategory);
  };

  // Get model by category
  const getModelsByCategory = (category: string): AIModel[] => {
    if (category === 'all') return AI_MODELS;
    return AI_MODELS.filter(model => model.category === category);
  };

  // Icon mapping for tools
  const iconMap: Record<string, React.ReactNode> = {
    Type: <Type className="h-4 w-4" />,
    Target: <Target className="h-4 w-4" />,
    BookOpen: <BookOpen className="h-4 w-4" />,
    Sparkles: <Sparkles className="h-4 w-4" />,
    FlaskConical: <FlaskConical className="h-4 w-4" />,
    Lightbulb: <Lightbulb className="h-4 w-4" />,
    Search: <Search className="h-4 w-4" />,
    TrendingUp: <TrendingUp className="h-4 w-4" />,
    CheckCircle: <CheckCircle className="h-4 w-4" />,
    FileText: <FileText className="h-4 w-4" />,
    BarChart3: <BarChart3 className="h-4 w-4" />,
    FileCheck: <FileCheck className="h-4 w-4" />,
    Edit: <Edit className="h-4 w-4" />,
    PenTool: <PenTool className="h-4 w-4" />,
    AlignLeft: <AlignLeft className="h-4 w-4" />,
    List: <List className="h-4 w-4" />,
    Repeat: <Repeat className="h-4 w-4" />
  };

  return (
    <>
      {/* Enhanced Toggle Button - Always visible on the right edge with improved design */}
      <div className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={handleToggleVisibility}
          className={`rounded-l-lg rounded-r-none border-r-0 bg-white hover:bg-blue-50 transition-all duration-300 shadow-lg border-blue-200 ${
            localVisible ? 'translate-x-0 bg-blue-50' : 'translate-x-0'
          }`}
          title={localVisible ? "Hide AI Assistant" : "Show AI Assistant"}
        >
          {localVisible ? (
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 text-blue-600" />
            </div>
          ) : (
            <div className="flex items-center space-x-1">
              <Bot className="h-4 w-4 text-blue-600" />
              <ChevronLeft className="h-4 w-4 text-blue-600" />
            </div>
          )}
        </Button>
      </div>

      {/* Main Sidebar */}
      <div className={`ai-sidebar-overlay ${localVisible ? 'visible' : ''}`}>
        {/* Main sidebar content */}
        <div className="ai-sidebar-content">
        {/* Compact Enhanced Header with Model Selection */}
        <div className="flex-shrink-0 border-b border-gray-200 p-3 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="p-1.5 bg-blue-100 rounded-full">
                <Bot className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 text-sm">AI Assistant</h3>
                <p className="text-xs text-gray-500">Writing & research tools</p>
              </div>
            </div>
          </div>

          {/* Compact Model Selection */}
          <div className="space-y-1.5">
            <label className="text-xs font-medium text-gray-700">AI Model</label>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent className="z-[10002]" sideOffset={5}>
                <div className="p-2">
                  <div className="text-xs font-semibold text-gray-500 mb-1">FAST MODELS</div>
                  {getModelsByCategory('fast').map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{model.name}</span>
                        <Badge variant="outline" className="ml-2 text-xs">
                          {model.cost}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </div>
                <div className="p-2">
                  <div className="text-xs font-semibold text-gray-500 mb-1">BALANCED</div>
                  {getModelsByCategory('balanced').map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{model.name}</span>
                        <Badge variant="outline" className="ml-2 text-xs">
                          {model.cost}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </div>
                <div className="p-2">
                  <div className="text-xs font-semibold text-gray-500 mb-1">PREMIUM</div>
                  {getModelsByCategory('premium').map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{model.name}</span>
                        <Badge variant="outline" className="ml-2 text-xs">
                          {model.cost}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </div>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Compact Tabs for different sections */}
        <Tabs defaultValue="tools" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col h-full">
          <TabsList className="flex-shrink-0 grid grid-cols-3 p-0.5 mx-2 mt-1">
            <TabsTrigger value="tools" className="flex items-center gap-1 text-xs py-1.5">
              <Zap className="h-3 w-3" />
              Tools
            </TabsTrigger>
            <TabsTrigger value="agent" className="flex items-center gap-1 text-xs py-1.5">
              <Brain className="h-3 w-3" />
              Agent
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center gap-1 text-xs py-1.5">
              <BarChart3 className="h-3 w-3" />
              Stats
            </TabsTrigger>
          </TabsList>
            
          {/* AI Tools Tab */}
          <TabsContent value="tools" className="flex-1 flex flex-col h-full">
            <div className="tools-filter-container px-3 py-1.5 border-b bg-gray-50 flex-shrink-0">
              <div className="flex items-center justify-between mb-1.5">
                <label className="text-xs font-medium text-gray-700">Category</label>
                <Filter className="h-3 w-3 text-gray-400" />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="z-[10002]" sideOffset={5}>
                  <SelectItem value="all">All Tools</SelectItem>
                  <SelectItem value="generation">Content Generation</SelectItem>
                  <SelectItem value="enhancement">Text Enhancement</SelectItem>
                  <SelectItem value="analysis">Analysis & Research</SelectItem>
                  <SelectItem value="review">Review & Feedback</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="px-4 py-4 space-y-4">
                {/* Quick Actions for Selected Text */}
                {selectedText && (
                  <Card className="p-3 bg-blue-50 border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Edit className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-800">Quick Actions</span>
                      <Badge variant="secondary" className="text-xs">
                        {selectedText.length} chars
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {RESEARCH_TOOLS.filter(tool => 
                        ['improve-clarity', 'fix-grammar', 'academic-tone', 'expand-point'].includes(tool.id)
                      ).map((tool) => (
                        <Button
                          key={tool.id}
                          variant="outline"
                          size="sm"
                          className="h-auto p-2 text-xs justify-start"
                          onClick={() => handleToolExecution(tool)}
                          disabled={aiLoading}
                        >
                          {iconMap[tool.icon] || <Sparkles className="h-4 w-4" />}
                          <span className="ml-1">{tool.name}</span>
                        </Button>
                      ))}
                    </div>
                  </Card>
                )}

                {/* Research Tools */}
                <div className="space-y-3">
                  {getFilteredTools().length === 0 ? (
                    <div className="text-center text-gray-500 text-sm py-8">
                      <Sparkles className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <h3 className="font-medium mb-2">No tools found</h3>
                      <p className="text-xs">Try selecting a different category</p>
                    </div>
                  ) : (
                    getFilteredTools().map((tool) => (
                      <Card key={tool.id} className="p-3 hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-blue-200">
                        <Button
                          variant="ghost"
                          className="w-full h-auto p-0 justify-start text-left"
                          onClick={() => handleToolExecution(tool)}
                          disabled={aiLoading || (tool.requiresSelection && !selectedText)}
                        >
                          <div className="flex items-start gap-3 w-full">
                            <div className="p-2 bg-gray-100 rounded-lg flex-shrink-0">
                              {iconMap[tool.icon] || <Sparkles className="h-4 w-4" />}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-sm">{tool.name}</span>
                                <Badge 
                                  variant={tool.category === 'generation' ? 'default' : 
                                         tool.category === 'enhancement' ? 'secondary' :
                                         tool.category === 'review' ? 'destructive' : 'outline'}
                                  className="text-xs"
                                >
                                  {tool.category}
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-600">
                                {tool.description}
                              </p>
                              {tool.requiresSelection && !selectedText && (
                                <p className="text-xs text-amber-600 mt-1">
                                  ⚠️ Select text to use this tool
                                </p>
                              )}
                            </div>
                          </div>
                        </Button>
                      </Card>
                    ))
                  )}
                </div>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* Agent Mode Tab */}
          <TabsContent value="agent" className="flex-1 flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="p-2">
                  {useEnhancedAgentMode ? (
                    <EnhancedAgentMode
                      documentContent={documentContent}
                      onAgentRequest={handleEnhancedAgentRequest}
                      isLoading={agentLoading}
                      lastResult={lastAgentResult}
                      onPreviewChanges={handlePreviewChanges}
                      onApplyChanges={handleApplyChanges}
                      onRejectChanges={handleRejectChanges}
                    />
                  ) : onSimpleAgentRequest ? (
                    <SimpleAgentMode
                      documentContent={documentContent}
                      onAgentRequest={onSimpleAgentRequest}
                      isLoading={simpleAgentLoading}
                      lastResult={lastSimpleAgentResult}
                    />
                  ) : (
                    <div className="text-center text-gray-500 p-4">
                      <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Agent Mode not available</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* Document Stats Tab */}
          <TabsContent value="stats" className="flex-1 flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-6">
                  <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Document Statistics
                  </h3>
                  <div className="stats-grid">
                    <Card className="p-4">
                      <div className="text-sm text-gray-500">Words</div>
                      <div className="text-2xl font-semibold text-blue-600">{wordCount.toLocaleString()}</div>
                    </Card>
                    <Card className="p-4">
                      <div className="text-sm text-gray-500">Characters</div>
                      <div className="text-2xl font-semibold text-green-600">{characterCount.toLocaleString()}</div>
                    </Card>
                  </div>
                 
                 {/* Custom Prompt Section */}
                 <Card className="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200 mt-4">
                   <div className="flex items-center gap-2 mb-3">
                     <Wand2 className="h-4 w-4 text-purple-600" />
                     <span className="font-medium text-purple-800">Custom AI Prompt</span>
                     <Badge variant="outline" className="text-xs bg-purple-100 text-purple-700 border-purple-300">
                       Advanced
                     </Badge>
                   </div>
                   <div className="space-y-3">
                     <Textarea
                       value={customPrompt}
                       onChange={(e) => setCustomPrompt(e.target.value)}
                       placeholder="Enter your custom prompt for advanced AI assistance..."
                       className="w-full text-sm min-h-[80px] resize-none"
                       disabled={aiLoading}
                     />
                     <div className="flex gap-2">
                       <Button
                         size="sm"
                         className="flex-1 bg-purple-600 hover:bg-purple-700"
                         disabled={!customPrompt.trim() || aiLoading}
                         onClick={async () => {
                           try {
                             const result = await enhancedAIService.generateText(
                               customPrompt.trim(),
                               selectedModel,
                               { maxTokens: 2048, temperature: 0.7 }
                             );
                             onAIRequest('', result, 'display');
                             setCustomPrompt('');
                             toast.success('Custom prompt executed successfully');
                           } catch (error: any) {
                             console.error('Custom prompt error:', error);
                             toast.error(error.message || 'Failed to execute custom prompt');
                           }
                         }}
                       >
                         {aiLoading ? (
                           <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                         ) : (
                           <Wand2 className="h-4 w-4 mr-2" />
                         )}
                         Run Prompt
                       </Button>
                       <Button
                         variant="outline"
                         size="sm"
                         onClick={() => setCustomPrompt('')}
                         disabled={!customPrompt.trim() || aiLoading}
                       >
                         Clear
                       </Button>
                     </div>
                     <p className="text-xs text-gray-600">
                       💡 Tip: Be specific about format, length, and style requirements for best results.
                     </p>
                   </div>
                 </Card>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Status
                  </h3>
                  <Card className="p-4">
                    <div className="flex items-center gap-3">
                      {status === 'saved' && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {status === 'saving' && <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />}
                      {status === 'error' && <AlertCircle className="h-5 w-5 text-red-500" />}
                      {status === 'unsaved' && <AlertCircle className="h-5 w-5 text-amber-500" />}
                      <div>
                        <div className="font-medium capitalize">{status}</div>
                        {lastSaved && (
                          <div className="text-sm text-gray-500">
                            Last saved: {lastSaved.toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    AI Configuration
                  </h3>
                  <Card className="p-4">
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm text-gray-500">Current Model</div>
                        <div className="font-medium">
                          {AI_MODELS.find(m => m.id === selectedModel)?.name || 'Unknown'}
                        </div>
                        <div className="text-xs text-gray-400">
                          {AI_MODELS.find(m => m.id === selectedModel)?.provider}
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-gray-500">API Status</div>
                        <div className="flex items-center gap-2">
                          {enhancedAIService.hasValidApiKey() ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm text-green-600">Connected</span>
                            </>
                          ) : (
                            <>
                              <AlertCircle className="h-4 w-4 text-red-500" />
                              <span className="text-sm text-red-600">No API Key</span>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-gray-500">Tools Available</div>
                        <div className="font-medium">{RESEARCH_TOOLS.length} tools</div>
                      </div>
                    </div>
                  </Card>
                </div>
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </>
  );
}
