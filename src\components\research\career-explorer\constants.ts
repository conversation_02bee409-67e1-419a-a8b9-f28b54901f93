import { AIModelOption, DifficultyLevel, CareerField, GeminiModelConfig } from './types';

// AI Models for career analysis
export const AI_MODELS: AIModelOption[] = [
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'openrouter',
    capabilities: ['career-analysis', 'roadmap-generation', 'skill-assessment'],
    maxTokens: 4096,
    description: 'Advanced reasoning and detailed analysis'
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    provider: 'openrouter',
    capabilities: ['career-analysis', 'detailed-planning', 'industry-insights'],
    maxTokens: 4096,
    description: 'Excellent for structured career planning'
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    provider: 'gemini',
    capabilities: ['career-analysis', 'market-research', 'skill-matching', 'comprehensive-roadmaps'],
    maxTokens: 8192,
    description: 'Google\'s most advanced model for career guidance',
    isRecommended: true
  },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'gemini',
    capabilities: ['career-analysis', 'quick-insights', 'skill-matching'],
    maxTokens: 8192,
    description: 'Fast and efficient career analysis'
  }
];

// Gemini model configurations
export const GEMINI_MODELS: Record<string, GeminiModelConfig> = {
  'gemini-2.5-pro': {
    model: 'gemini-2.5-pro',
    maxOutputTokens: 8192,
    temperature: 0.7,
    topP: 0.95,
    topK: 40
  },
  'gemini-2.5-flash': {
    model: 'gemini-2.5-flash',
    maxOutputTokens: 8192,
    temperature: 0.7,
    topP: 0.95,
    topK: 40
  }
};

// Career difficulty levels with styling
export const DIFFICULTY_LEVELS: Record<DifficultyLevel, { color: string; description: string }> = {
  'Low': {
    color: 'text-green-600',
    description: 'Easy transition with existing skills'
  },
  'Medium': {
    color: 'text-orange-600',
    description: 'Moderate learning curve required'
  },
  'High': {
    color: 'text-red-600',
    description: 'Significant skill development needed'
  }
};

// Career fields and categories
export const CAREER_FIELDS: CareerField[] = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Marketing',
  'Design',
  'Other'
];

// Default career visualization layout
export const VISUALIZATION_CONFIG = {
  centerNode: {
    position: { x: 650, y: 450 },
    style: {
      background: 'linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%)',
      color: '#fff',
      fontSize: '18px',
      fontWeight: 'bold',
      borderRadius: '12px',
      padding: '16px 24px',
      border: 'none',
      boxShadow: '0 8px 32px rgba(59, 130, 246, 0.3)'
    }
  },
  careerNodePositions: [
    { x: 50, y: 550 },   // Bottom left
    { x: 1050, y: 550 }, // Bottom right
    { x: 50, y: 150 },   // Top left
    { x: 1050, y: 150 }, // Top right
    { x: 550, y: 700 },  // Bottom center
    { x: 550, y: 0 }     // Top center
  ],
  edgeStyle: {
    stroke: '#3B82F6',
    strokeWidth: 2,
    strokeDasharray: '5,5'
  }
};

// Export format options
export const EXPORT_FORMATS = [
  {
    id: 'pdf',
    name: 'PDF Report',
    description: 'Comprehensive career analysis report',
    icon: 'FileText'
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Editable career roadmap document',
    icon: 'FileEdit'
  },
  {
    id: 'json',
    name: 'JSON Data',
    description: 'Raw data for further analysis',
    icon: 'Database'
  }
];

// Default generation options
export const DEFAULT_GENERATION_OPTIONS = {
  model: 'gemini-2.5-flash', // Default to Gemini Flash as requested
  temperature: 0.7,
  maxTokens: 8192,
  numberOfCareers: 6,
  includeRoadmaps: true,
  includeSkillAnalysis: true
};

// Career analysis prompts - Enhanced for concise, relevant output
export const CAREER_PROMPTS = {
  initialAnalysis: `You are a professional career counselor. Analyze the resume and suggest 6 realistic career paths.

For each career, provide:
- jobTitle: Specific, clear job title
- jobDescription: One concise sentence describing the role
- timeline: Realistic transition time (e.g., "3-6 months")
- salary: Market salary range (e.g., "$70k - $95k")
- difficulty: Low/Medium/High based on skill requirements

Respond ONLY with valid JSON array format:
[
  {
    "jobTitle": "string",
    "jobDescription": "string",
    "timeline": "string",
    "salary": "string",
    "difficulty": "Low|Medium|High"
  }
]`,

  detailedAnalysis: `You are a career transition expert. Provide focused guidance for transitioning into the {jobTitle} role.

Respond ONLY with valid JSON:
{
  "workRequired": "string (study hours per week)",
  "aboutTheRole": "string (2-3 sentences about the role)",
  "whyItsGoodFit": ["string", "string", "string"] (3 specific reasons based on their background),
  "roadmap": [
    {"timeframe": "action description"},
    {"timeframe": "action description"}
  ],
  "skills": ["skill1", "skill2", "skill3"] (top 5 essential skills),
  "certifications": ["cert1", "cert2"] (most valuable certifications)
}`
};

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: ['.pdf', '.doc', '.docx', '.txt'],
  acceptedMimeTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
};

// UI Constants
export const UI_CONSTANTS = {
  animationDuration: 300,
  debounceDelay: 500,
  maxResumeTextLength: 50000,
  maxContextLength: 2000,
  defaultTimeout: 30000
};

// Color scheme for career visualization
export const CAREER_COLORS = {
  primary: '#3B82F6',
  secondary: '#1E40AF', 
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  muted: '#6B7280',
  background: '#F8FAFC',
  cardBackground: '#FFFFFF'
};

// Default career categories for suggestions
export const CAREER_CATEGORIES = {
  'Technology': ['Software Engineer', 'Data Scientist', 'UX Designer', 'DevOps Engineer'],
  'Healthcare': ['Nurse Practitioner', 'Medical Technician', 'Healthcare Administrator'],
  'Finance': ['Financial Analyst', 'Investment Advisor', 'Risk Manager'],
  'Education': ['Instructional Designer', 'Corporate Trainer', 'Educational Consultant'],
  'Marketing': ['Digital Marketing Specialist', 'Content Strategist', 'Brand Manager'],
  'Design': ['Graphic Designer', 'Product Designer', 'Creative Director']
};
