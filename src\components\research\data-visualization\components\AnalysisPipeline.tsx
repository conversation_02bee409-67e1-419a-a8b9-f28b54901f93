import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Play,
  CheckCircle,
  AlertTriangle,
  Loader2,
  BarChart3,
  Brain,
  Eye,
  RefreshCw,
  Clock,
  Database,
  TrendingUp,
  Lightbulb
} from "lucide-react";
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { AnalysisPipelineStep, UploadedFile, DataAnalysisResult } from '../types';
import { LOADING_STATES, SUCCESS_MESSAGES } from '../constants';

interface AnalysisPipelineProps {
  file: UploadedFile;
  onAnalysisComplete?: (result: DataAnalysisResult) => void;
  className?: string;
}

export const AnalysisPipeline: React.FC<AnalysisPipelineProps> = ({
  file,
  onAnalysisComplete,
  className = ""
}) => {
  const {
    setAnalysisResult,
    setCurrentAnalysis,
    setAnalyzing,
    addError,
    isAnalyzing
  } = useDataVisualizationStore();

  const [steps, setSteps] = useState<AnalysisPipelineStep[]>([
    {
      id: 'data-structure',
      name: 'Data Structure Analysis',
      description: 'Analyzing data types, missing values, and basic statistics',
      status: 'pending',
      progress: 0
    },
    {
      id: 'insights-generation',
      name: 'AI Insights Generation',
      description: 'Generating comprehensive insights and patterns',
      status: 'pending',
      progress: 0
    },
    {
      id: 'visualization-creation',
      name: 'Visualization Creation',
      description: 'Creating interactive charts and visualizations',
      status: 'pending',
      progress: 0
    }
  ]);

  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [analysisResult, setLocalAnalysisResult] = useState<DataAnalysisResult | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);

  const updateStep = (stepId: string, updates: Partial<AnalysisPipelineStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const runAnalysis = async () => {
    if (isAnalyzing) return;

    setAnalyzing(true);
    setStartTime(new Date());
    setCurrentStepIndex(0);

    try {
      // Step 1: Data Structure Analysis
      updateStep('data-structure', { status: 'running', progress: 0 });
      
      // Simulate progress for data structure analysis
      for (let i = 0; i <= 100; i += 20) {
        updateStep('data-structure', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      updateStep('data-structure', { 
        status: 'completed', 
        progress: 100,
        result: 'Data structure analyzed successfully'
      });

      // Step 2: AI Insights Generation
      setCurrentStepIndex(1);
      updateStep('insights-generation', { status: 'running', progress: 0 });

      // Start the actual analysis
      const result = await GeminiAnalysisService.analyzeData(file);

      // Check if this was fallback analysis
      const isFallback = result.id.includes('fallback');
      const resultMessage = isFallback
        ? 'Basic statistical analysis completed (AI unavailable)'
        : 'AI insights generated successfully';

      updateStep('insights-generation', {
        status: 'completed',
        progress: 100,
        result: resultMessage
      });

      // Step 3: Visualization Creation
      setCurrentStepIndex(2);
      updateStep('visualization-creation', { status: 'running', progress: 0 });

      // Simulate visualization creation progress
      for (let i = 0; i <= 100; i += 25) {
        updateStep('visualization-creation', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      updateStep('visualization-creation', { 
        status: 'completed', 
        progress: 100,
        result: `${result.visualizations.length} visualizations created`
      });

      // Store results
      setLocalAnalysisResult(result);
      setAnalysisResult(result);
      setCurrentAnalysis(result);

      toast.success(SUCCESS_MESSAGES.ANALYSIS_COMPLETE);
      
      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Analysis failed';
      console.error('Analysis pipeline error:', error);

      // Mark current step as failed
      if (currentStepIndex >= 0 && currentStepIndex < steps.length) {
        updateStep(steps[currentStepIndex].id, {
          status: 'error',
          error: errorMessage
        });
      }

      // Check if this is a timeout or API error, suggest fallback
      let userMessage = errorMessage;
      if (errorMessage.includes('timeout') || errorMessage.includes('API')) {
        userMessage = 'AI analysis timed out. The system will use basic statistical analysis instead.';

        // Try fallback analysis
        try {
          console.log('Attempting fallback analysis...');
          const { FallbackAnalysisService } = await import('../services/fallback-analysis.service');
          const fallbackResult = await FallbackAnalysisService.analyzeData(file);

          // Store fallback results
          setLocalAnalysisResult(fallbackResult);
          setAnalysisResult(fallbackResult);
          setCurrentAnalysis(fallbackResult);

          updateStep('insights-generation', {
            status: 'completed',
            progress: 100,
            result: 'Basic statistical analysis completed (AI unavailable)'
          });

          // Continue with visualization step
          setCurrentStepIndex(2);
          updateStep('visualization-creation', { status: 'running', progress: 0 });

          for (let i = 0; i <= 100; i += 25) {
            updateStep('visualization-creation', { progress: i });
            await new Promise(resolve => setTimeout(resolve, 200));
          }

          updateStep('visualization-creation', {
            status: 'completed',
            progress: 100,
            result: `${fallbackResult.visualizations.length} visualizations created`
          });

          toast.success('Analysis completed using basic statistical methods');

          if (onAnalysisComplete) {
            onAnalysisComplete(fallbackResult);
          }

          return; // Exit successfully
        } catch (fallbackError) {
          console.error('Fallback analysis also failed:', fallbackError);
          userMessage = 'Both AI and basic analysis failed. Please try again with a different file.';
        }
      }

      addError(userMessage);
      toast.error(userMessage);
    } finally {
      setAnalyzing(false);
      setCurrentStepIndex(-1);
    }
  };

  const resetAnalysis = () => {
    setSteps(prev => prev.map(step => ({
      ...step,
      status: 'pending',
      progress: 0,
      result: undefined,
      error: undefined
    })));
    setCurrentStepIndex(-1);
    setLocalAnalysisResult(null);
    setStartTime(null);
  };

  const getStepIcon = (step: AnalysisPipelineStep) => {
    switch (step.status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStepBadge = (step: AnalysisPipelineStep) => {
    switch (step.status) {
      case 'running':
        return <Badge variant="default">Running</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Completed</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const formatDuration = (start: Date, end: Date = new Date()) => {
    const diff = end.getTime() - start.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  const allStepsCompleted = steps.every(step => step.status === 'completed');
  const hasErrors = steps.some(step => step.status === 'error');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Analysis Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Automated Data Analysis
            </CardTitle>
            <div className="flex items-center gap-2">
              {startTime && (
                <Badge variant="outline" className="text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDuration(startTime)}
                </Badge>
              )}
              {!isAnalyzing && !allStepsCompleted && (
                <Button onClick={runAnalysis} size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  Start Analysis
                </Button>
              )}
              {(allStepsCompleted || hasErrors) && (
                <Button onClick={resetAnalysis} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* File Info */}
            <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
              <Database className="h-5 w-5 text-blue-500" />
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-gray-500">
                  {file.data.length} rows × {file.headers.length} columns
                </p>
              </div>
            </div>

            {/* Overall Progress */}
            {isAnalyzing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Overall Progress</span>
                  <span>{Math.round(((currentStepIndex + 1) / steps.length) * 100)}%</span>
                </div>
                <Progress 
                  value={((currentStepIndex + 1) / steps.length) * 100} 
                  className="w-full" 
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Analysis Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {steps.map((step, index) => (
              <div key={step.id} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStepIcon(step)}
                    <div>
                      <h4 className="font-medium">{step.name}</h4>
                      <p className="text-sm text-gray-500">{step.description}</p>
                    </div>
                  </div>
                  {getStepBadge(step)}
                </div>

                {/* Step Progress */}
                {step.status === 'running' && (
                  <div className="ml-7 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{step.progress}%</span>
                    </div>
                    <Progress value={step.progress} className="w-full" />
                  </div>
                )}

                {/* Step Result */}
                {step.result && (
                  <div className="ml-7">
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>{step.result}</AlertDescription>
                    </Alert>
                  </div>
                )}

                {/* Step Error */}
                {step.error && (
                  <div className="ml-7">
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{step.error}</AlertDescription>
                    </Alert>
                  </div>
                )}

                {index < steps.length - 1 && <Separator />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Summary */}
      {analysisResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Analysis Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <BarChart3 className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-600">
                  {analysisResult.visualizations.length}
                </p>
                <p className="text-sm text-gray-600">Visualizations Created</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <Lightbulb className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-600">
                  {analysisResult.insights.keyFindings.length}
                </p>
                <p className="text-sm text-gray-600">Key Insights</p>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <Eye className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-purple-600">
                  {Object.keys(analysisResult.summary.dataTypes).length}
                </p>
                <p className="text-sm text-gray-600">Columns Analyzed</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
