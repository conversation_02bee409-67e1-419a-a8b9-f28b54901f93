/**
 * Article Review API Service
 * 
 * This service handles communication with the OpenRouter API for article analysis.
 */

import { ArticleSection, SectionReview, ReviewMetrics } from '../types';

export class ArticleReviewAPIService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }

  /**
   * Check if the API is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Analyze a section of an academic paper
   * 
   * @param section The section to analyze
   * @param content The text content of the section
   * @param modelId The AI model to use
   * @returns Analysis results for the section
   */
  async analyzeSection(
    section: ArticleSection,
    content: string,
    modelId: string
  ): Promise<SectionReview | ReviewMetrics> {
    if (!this.isConfigured()) {
      throw new Error('OpenRouter API key not configured in .env file (VITE_OPENROUTER_API_KEY)');
    }

    try {
      // Build the appropriate prompt based on section type
      const prompt = this.buildPromptForSection(section, content);
      
      // Get messages for the API
      const messages = [
        {
          role: "system",
          content: `You are an academic paper reviewer with expertise in analyzing academic writing. 
          Your task is to review a ${section} section of an academic paper and provide detailed, 
          constructive feedback. Be specific and actionable in your critique.`
        },
        {
          role: "user",
          content: prompt
        }
      ];

      // Make the API call to OpenRouter
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Paper Genius - Article Reviewer'
        },
        body: JSON.stringify({
          model: modelId,
          messages: messages,
          max_tokens: 2000,
          temperature: 0.3,
          response_format: { type: "json_object" }
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API request failed with status ${response.status}: ${JSON.stringify(errorData)}`);
      }

      const data = await response.json();
      
      // Extract and parse the response content
      const responseText = data.choices[0]?.message?.content;
      
      if (!responseText) {
        throw new Error('Invalid response from OpenRouter API');
      }

      try {
        // Parse the JSON response
        const parsedResponse = JSON.parse(responseText);
        
        // For overall section
        if (section === 'overall') {
          return this.formatOverallResponse(parsedResponse);
        } 
        // For regular sections
        else {
          return this.formatSectionResponse(parsedResponse, content);
        }
      } catch (parseError) {
        console.error('Error parsing API response:', parseError);
        throw new Error('Invalid JSON response from API');
      }
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  /**
   * Build a prompt appropriate for the section type
   */
  private buildPromptForSection(section: ArticleSection, content: string): string {
    // Base prompt that applies to all sections
    let basePrompt = `
Please analyze the following ${section} section of an academic paper and respond in JSON format:

${content.substring(0, 10000)} ${content.length > 10000 ? '... [content truncated for length]' : ''}

Respond with a JSON object containing the following fields:
`;

    // Add section-specific prompt components
    if (section === 'overall') {
      return basePrompt + `
{
  "clarity": number, // Score from 0-100 on writing clarity
  "structure": number, // Score from 0-100 on paper structure
  "methodology": number, // Score from 0-100 on research methodology
  "significance": number, // Score from 0-100 on research significance
  "originality": number, // Score from 0-100 on originality of research
  "overallScore": number, // Overall score from 0-100
  "summary": string, // 2-3 paragraph summary assessment of the paper
  "majorStrengths": string[], // 3-5 major strengths of the paper
  "majorWeaknesses": string[], // 3-5 key weaknesses of the paper
  "recommendations": string[] // 3-5 actionable recommendations
}`;
    } else {
      return basePrompt + `
{
  "analysis": string, // A detailed analysis of the section
  "strengths": string[], // 3-5 key strengths of this section
  "weaknesses": string[], // 3-5 specific weaknesses or issues
  "suggestions": string[], // 3-5 actionable suggestions for improvement
  "score": number, // Overall score for this section from 0-100
  "detailedFeedback": [
    {
      "originalText": string, // The specific text with an issue
      "issue": string, // Description of the issue
      "suggestion": string, // Suggestion for improvement
      "type": string, // One of: "grammar", "clarity", "structure", "content", "style", "citation", "logic"
      "severity": string // One of: "minor", "moderate", "major"
    }
    // Include 3-7 detailed feedback items
  ]
}`;
    }
  }

  /**
   * Format response for a regular section
   */
  private formatSectionResponse(response: any, originalContent: string): SectionReview {
    // Ensure all required fields have at least default values
    return {
      content: originalContent,
      analysis: response.analysis || 'No analysis provided',
      strengths: Array.isArray(response.strengths) ? response.strengths : [],
      weaknesses: Array.isArray(response.weaknesses) ? response.weaknesses : [],
      suggestions: Array.isArray(response.suggestions) ? response.suggestions : [],
      score: typeof response.score === 'number' ? response.score : 0,
      detailedFeedback: Array.isArray(response.detailedFeedback) 
        ? response.detailedFeedback.map(item => ({
            originalText: item.originalText || '',
            issue: item.issue || '',
            suggestion: item.suggestion || '',
            type: this.validateFeedbackType(item.type),
            severity: this.validateSeverity(item.severity)
          }))
        : []
    };
  }

  /**
   * Format response for overall metrics
   */
  private formatOverallResponse(response: any): ReviewMetrics {
    return {
      clarity: typeof response.clarity === 'number' ? response.clarity : 0,
      structure: typeof response.structure === 'number' ? response.structure : 0,
      methodology: typeof response.methodology === 'number' ? response.methodology : 0,
      significance: typeof response.significance === 'number' ? response.significance : 0,
      originality: typeof response.originality === 'number' ? response.originality : 0,
      overallScore: typeof response.overallScore === 'number' ? response.overallScore : 0,
      summary: response.summary || 'No summary provided',
      majorStrengths: Array.isArray(response.majorStrengths) ? response.majorStrengths : [],
      majorWeaknesses: Array.isArray(response.majorWeaknesses) ? response.majorWeaknesses : [],
      recommendations: Array.isArray(response.recommendations) ? response.recommendations : []
    };
  }

  /**
   * Validate feedback type is one of the allowed values
   */
  private validateFeedbackType(type: string): 'grammar' | 'clarity' | 'structure' | 'content' | 'style' | 'citation' | 'logic' {
    const validTypes = ['grammar', 'clarity', 'structure', 'content', 'style', 'citation', 'logic'];
    return validTypes.includes(type) ? type as any : 'content';
  }

  /**
   * Validate severity is one of the allowed values
   */
  private validateSeverity(severity: string): 'minor' | 'moderate' | 'major' {
    const validSeverity = ['minor', 'moderate', 'major'];
    return validSeverity.includes(severity) ? severity as any : 'moderate';
  }
}

export default new ArticleReviewAPIService();
