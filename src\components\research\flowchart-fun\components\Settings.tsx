/**
 * Settings Panel Component
 * Comprehensive settings interface for Flowchart Fun
 */

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings as SettingsIcon, Layout, Palette, Network, Zap, Download } from 'lucide-react';
import { FFTheme, ThemePreset } from '../types';
import { themePresets, defaultTheme } from '../themes/defaultTheme';
import LayoutTab from './settings/LayoutTab';
import GeneralTab from './settings/GeneralTab';
import NodesTab from './settings/NodesTab';
import EdgesTab from './settings/EdgesTab';
import AdvancedTab from './settings/AdvancedTab';
import ThemeTab from './settings/ThemeTab';

interface SettingsProps {
  theme: FFTheme;
  onThemeChange: (theme: Partial<FFTheme>) => void;
  onExport?: () => void;
  onReset?: () => void;
  className?: string;
}

const Settings: React.FC<SettingsProps> = ({
  theme,
  onThemeChange,
  onExport,
  onReset,
  className = '',
}) => {
  const [activeTab, setActiveTab] = useState('layout');

  const handleResetTheme = () => {
    if (onReset) {
      onReset();
    } else {
      onThemeChange(defaultTheme);
    }
  };

  const handleExportTheme = () => {
    if (onExport) {
      onExport();
    } else {
      const dataStr = JSON.stringify(theme, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'flowchart-theme.json';
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <SettingsIcon className="h-5 w-5" />
              <CardTitle>Flowchart Settings</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportTheme}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Export Theme
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetTheme}
              >
                Reset to Default
              </Button>
            </div>
          </div>
          <CardDescription>
            Customize the appearance and behavior of your flowcharts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="layout" className="flex items-center gap-1">
                <Layout className="h-4 w-4" />
                Layout
              </TabsTrigger>
              <TabsTrigger value="general" className="flex items-center gap-1">
                <SettingsIcon className="h-4 w-4" />
                General
              </TabsTrigger>
              <TabsTrigger value="nodes" className="flex items-center gap-1">
                <div className="w-4 h-4 border border-current rounded" />
                Nodes
              </TabsTrigger>
              <TabsTrigger value="edges" className="flex items-center gap-1">
                <Network className="h-4 w-4" />
                Edges
              </TabsTrigger>
              <TabsTrigger value="themes" className="flex items-center gap-1">
                <Palette className="h-4 w-4" />
                Themes
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-1">
                <Zap className="h-4 w-4" />
                Advanced
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="layout" className="space-y-4">
                <LayoutTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>

              <TabsContent value="general" className="space-y-4">
                <GeneralTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>

              <TabsContent value="nodes" className="space-y-4">
                <NodesTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>

              <TabsContent value="edges" className="space-y-4">
                <EdgesTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>

              <TabsContent value="themes" className="space-y-4">
                <ThemeTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <AdvancedTab theme={theme} onThemeChange={onThemeChange} />
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;
