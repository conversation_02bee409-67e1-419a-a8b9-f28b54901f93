/**
 * Zustand store for Article Revision Workflow State Management
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  RevisionWorkflowState, 
  ParsedArticle, 
  ParsedReviewerComments, 
  RevisedArticle, 
  ResponseLetter, 
  ManualIntervention,
  RevisionProgress,
  AIAssistantState,
  ExportConfiguration
} from '../types';
import { AI_ASSISTANTS } from '../constants';

interface RevisionWorkflowActions {
  // Initialization
  initializeWorkflow: () => void;
  resetWorkflow: () => void;
  
  // Document management
  setOriginalArticle: (article: ParsedArticle) => void;
  addReviewerComments: (comments: ParsedReviewerComments) => void;
  removeReviewerComments: (reviewerNumber: number) => void;
  
  // Progress management
  updateProgress: (progress: Partial<RevisionProgress>) => void;
  setCurrentPhase: (phase: RevisionProgress['phase']) => void;
  addError: (error: string) => void;
  addWarning: (warning: string) => void;
  clearErrors: () => void;
  
  // AI Assistant management
  initializeAIAssistants: () => void;
  updateAIAssistant: (id: string, update: Partial<AIAssistantState>) => void;
  setAIAssistantStatus: (id: string, status: AIAssistantState['status']) => void;
  setAIAssistantProgress: (id: string, progress: number) => void;
  setAIAssistantResult: (id: string, result: any) => void;
  setAIAssistantError: (id: string, error: string) => void;
  
  // Results management
  setRevisedArticle: (article: RevisedArticle) => void;
  setResponseLetter: (letter: ResponseLetter) => void;
  addManualIntervention: (intervention: ManualIntervention) => void;
  removeManualIntervention: (id: string) => void;
  
  // Export management
  updateExportConfiguration: (config: Partial<ExportConfiguration>) => void;
  
  // Utility functions
  canStartRevision: () => boolean;
  getOverallProgress: () => number;
  getActiveAssistants: () => AIAssistantState[];
  getCompletedAssistants: () => AIAssistantState[];
  getFailedAssistants: () => AIAssistantState[];
}

type RevisionWorkflowStore = RevisionWorkflowState & RevisionWorkflowActions;

const initialState: RevisionWorkflowState = {
  // Input data
  originalArticle: null,
  reviewerComments: [],
  
  // Processing state
  progress: {
    phase: 'parsing',
    currentStep: 'Ready to start',
    totalSteps: 5,
    completedSteps: 0,
    percentage: 0,
    errors: [],
    warnings: []
  },
  aiAssistants: {},
  
  // Results
  revisedArticle: null,
  responseLetter: null,
  manualInterventions: [],
  
  // Export options
  exportFormats: ['word'],
  highlightChanges: true,
  includeComments: true
};

export const useRevisionWorkflowStore = create<RevisionWorkflowStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Initialization
        initializeWorkflow: () => {
          set({
            ...initialState,
            progress: {
              ...initialState.progress,
              currentStep: 'Workflow initialized'
            }
          });
          get().initializeAIAssistants();
        },

        resetWorkflow: () => {
          set(initialState);
        },

        // Document management
        setOriginalArticle: (article: ParsedArticle) => {
          set({ originalArticle: article });
          get().updateProgress({
            currentStep: `Article loaded: ${article.fileName}`,
            completedSteps: 1,
            percentage: Math.round((1 / get().progress.totalSteps) * 100)
          });
        },

        addReviewerComments: (comments: ParsedReviewerComments) => {
          const currentComments = get().reviewerComments;
          const existingIndex = currentComments.findIndex(
            c => c.reviewerNumber === comments.reviewerNumber
          );
          
          let updatedComments;
          if (existingIndex >= 0) {
            updatedComments = [...currentComments];
            updatedComments[existingIndex] = comments;
          } else {
            updatedComments = [...currentComments, comments];
          }
          
          set({ reviewerComments: updatedComments });
          get().updateProgress({
            currentStep: `Reviewer ${comments.reviewerNumber} comments loaded`
          });
        },

        removeReviewerComments: (reviewerNumber: number) => {
          const updatedComments = get().reviewerComments.filter(
            c => c.reviewerNumber !== reviewerNumber
          );
          set({ reviewerComments: updatedComments });
        },

        // Progress management
        updateProgress: (progress: Partial<RevisionProgress>) => {
          const currentProgress = get().progress;
          set({
            progress: {
              ...currentProgress,
              ...progress
            }
          });
        },

        setCurrentPhase: (phase: RevisionProgress['phase']) => {
          const phaseSteps = {
            'parsing': 'Parsing documents',
            'comment-analysis': 'Analyzing reviewer comments',
            'section-revision': 'Revising article sections',
            'integration': 'Integrating revisions',
            'response-generation': 'Generating response letter',
            'export': 'Preparing exports',
            'completed': 'Process completed'
          };
          
          get().updateProgress({
            phase,
            currentStep: phaseSteps[phase]
          });
        },

        addError: (error: string) => {
          const currentErrors = get().progress.errors;
          get().updateProgress({
            errors: [...currentErrors, error]
          });
        },

        addWarning: (warning: string) => {
          const currentWarnings = get().progress.warnings;
          get().updateProgress({
            warnings: [...currentWarnings, warning]
          });
        },

        clearErrors: () => {
          get().updateProgress({
            errors: [],
            warnings: []
          });
        },

        // AI Assistant management
        initializeAIAssistants: () => {
          const assistants: Record<string, AIAssistantState> = {};
          
          Object.values(AI_ASSISTANTS).forEach(assistant => {
            assistants[assistant.id] = {
              id: assistant.id,
              name: assistant.name,
              status: 'idle',
              progress: 0
            };
          });
          
          set({ aiAssistants: assistants });
        },

        updateAIAssistant: (id: string, update: Partial<AIAssistantState>) => {
          const currentAssistants = get().aiAssistants;
          set({
            aiAssistants: {
              ...currentAssistants,
              [id]: {
                ...currentAssistants[id],
                ...update
              }
            }
          });
        },

        setAIAssistantStatus: (id: string, status: AIAssistantState['status']) => {
          get().updateAIAssistant(id, { 
            status,
            startTime: status === 'processing' ? new Date() : get().aiAssistants[id]?.startTime,
            endTime: status === 'completed' || status === 'error' ? new Date() : undefined
          });
        },

        setAIAssistantProgress: (id: string, progress: number) => {
          get().updateAIAssistant(id, { progress });
        },

        setAIAssistantResult: (id: string, result: any) => {
          get().updateAIAssistant(id, { 
            result,
            status: 'completed',
            progress: 100,
            endTime: new Date()
          });
        },

        setAIAssistantError: (id: string, error: string) => {
          get().updateAIAssistant(id, { 
            error,
            status: 'error',
            endTime: new Date()
          });
        },

        // Results management
        setRevisedArticle: (article: RevisedArticle) => {
          set({ revisedArticle: article });
        },

        setResponseLetter: (letter: ResponseLetter) => {
          set({ responseLetter: letter });
        },

        addManualIntervention: (intervention: ManualIntervention) => {
          const currentInterventions = get().manualInterventions;
          set({
            manualInterventions: [...currentInterventions, intervention]
          });
        },

        removeManualIntervention: (id: string) => {
          const updatedInterventions = get().manualInterventions.filter(
            i => i.id !== id
          );
          set({ manualInterventions: updatedInterventions });
        },

        // Export management
        updateExportConfiguration: (config: Partial<ExportConfiguration>) => {
          // This would be implemented when we add export configuration state
          // For now, we'll just update the basic export options
          set({
            exportFormats: config.format ? [config.format] : get().exportFormats,
            highlightChanges: config.highlightChanges ?? get().highlightChanges,
            includeComments: config.includeComments ?? get().includeComments
          });
        },

        // Utility functions
        canStartRevision: () => {
          const state = get();
          return !!(state.originalArticle && state.reviewerComments.length > 0);
        },

        getOverallProgress: () => {
          const assistants = Object.values(get().aiAssistants);
          if (assistants.length === 0) return 0;
          
          const totalProgress = assistants.reduce((sum, assistant) => sum + assistant.progress, 0);
          return Math.round(totalProgress / assistants.length);
        },

        getActiveAssistants: () => {
          return Object.values(get().aiAssistants).filter(a => a.status === 'processing');
        },

        getCompletedAssistants: () => {
          return Object.values(get().aiAssistants).filter(a => a.status === 'completed');
        },

        getFailedAssistants: () => {
          return Object.values(get().aiAssistants).filter(a => a.status === 'error');
        }
      }),
      {
        name: 'article-revision-workflow',
        partialize: (state) => ({
          // Only persist essential state, not temporary processing state
          originalArticle: state.originalArticle,
          reviewerComments: state.reviewerComments,
          revisedArticle: state.revisedArticle,
          responseLetter: state.responseLetter,
          manualInterventions: state.manualInterventions,
          exportFormats: state.exportFormats,
          highlightChanges: state.highlightChanges,
          includeComments: state.includeComments
        })
      }
    ),
    { name: 'article-revision-workflow' }
  )
);
