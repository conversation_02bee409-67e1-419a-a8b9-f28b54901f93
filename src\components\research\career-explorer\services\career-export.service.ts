/**
 * Career Export Service
 * Handles exporting career analysis in various formats
 */

import { CareerAnalysis, CareerExportOptions, CareerExportData } from '../types';

class CareerExportService {
  /**
   * Export career analysis to PDF
   */
  async exportToPDF(analysis: CareerAnalysis, options: CareerExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(analysis, options);
      
      // Create PDF content
      const pdfContent = this.generatePDFContent(exportData);
      
      // Create and download PDF
      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      this.downloadFile(blob, 'career-analysis.pdf');
    } catch (error) {
      console.error('PDF export failed:', error);
      throw new Error('Failed to export PDF. Please try again.');
    }
  }

  /**
   * Export career analysis to Word document
   */
  async exportToWord(analysis: CareerAnalysis, options: CareerExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(analysis, options);
      
      // Create Word document content
      const wordContent = this.generateWordContent(exportData);
      
      // Create and download Word document
      const blob = new Blob([wordContent], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });
      this.downloadFile(blob, 'career-analysis.docx');
    } catch (error) {
      console.error('Word export failed:', error);
      throw new Error('Failed to export Word document. Please try again.');
    }
  }

  /**
   * Export career analysis to JSON
   */
  async exportToJSON(analysis: CareerAnalysis, options: CareerExportOptions): Promise<void> {
    try {
      const exportData = this.prepareExportData(analysis, options);
      
      // Create JSON content
      const jsonContent = JSON.stringify(exportData, null, 2);
      
      // Create and download JSON file
      const blob = new Blob([jsonContent], { type: 'application/json' });
      this.downloadFile(blob, 'career-analysis.json');
    } catch (error) {
      console.error('JSON export failed:', error);
      throw new Error('Failed to export JSON. Please try again.');
    }
  }

  /**
   * Prepare export data based on options
   */
  private prepareExportData(analysis: CareerAnalysis, options: CareerExportOptions): CareerExportData {
    const exportData: CareerExportData = {
      metadata: {
        name: 'Career Analysis',
        field: 'Multiple',
        interests: [],
        experience: '',
        goals: ''
      },
      analysis: {
        careerPaths: analysis.careerPaths,
        overallAnalysis: options.includeAnalysis ? analysis.overallAnalysis : '',
        recommendations: options.includeAnalysis ? analysis.recommendations : [],
        nextSteps: options.includeAnalysis ? analysis.nextSteps : [],
        generatedAt: analysis.generatedAt
      },
      exportedAt: new Date(),
      format: options.format
    };

    // Filter roadmaps if not included
    if (!options.includeRoadmaps) {
      exportData.analysis.careerPaths = exportData.analysis.careerPaths.map(career => ({
        ...career,
        roadmap: undefined
      }));
    }

    return exportData;
  }

  /**
   * Generate PDF content (simplified HTML for now)
   */
  private generatePDFContent(data: CareerExportData): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Career Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #2563eb; border-bottom: 2px solid #2563eb; }
        h2 { color: #1e40af; margin-top: 30px; }
        .career-path { margin: 20px 0; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .difficulty-low { border-left: 4px solid #10b981; }
        .difficulty-medium { border-left: 4px solid #f59e0b; }
        .difficulty-high { border-left: 4px solid #ef4444; }
        .roadmap { margin: 10px 0; }
        .roadmap-step { margin: 10px 0; padding: 10px; background: #f9fafb; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Career Analysis Report</h1>
    <p><strong>Generated:</strong> ${data.exportedAt.toLocaleDateString()}</p>
    
    ${data.analysis.overallAnalysis ? `
    <h2>Overall Analysis</h2>
    <p>${data.analysis.overallAnalysis}</p>
    ` : ''}
    
    <h2>Career Paths (${data.analysis.careerPaths.length})</h2>
    ${data.analysis.careerPaths.map(career => `
        <div class="career-path difficulty-${career.difficulty.toLowerCase()}">
            <h3>${career.jobTitle}</h3>
            <p><strong>Description:</strong> ${career.jobDescription}</p>
            <p><strong>Timeline:</strong> ${career.timeline}</p>
            <p><strong>Salary:</strong> ${career.salary}</p>
            <p><strong>Difficulty:</strong> ${career.difficulty}</p>
            
            ${career.aboutTheRole ? `<p><strong>About the Role:</strong> ${career.aboutTheRole}</p>` : ''}
            
            ${career.whyItsGoodFit ? `
            <p><strong>Why it's a good fit:</strong></p>
            <ul>${career.whyItsGoodFit.map(reason => `<li>${reason}</li>`).join('')}</ul>
            ` : ''}
            
            ${career.skills ? `
            <p><strong>Required Skills:</strong> ${career.skills.join(', ')}</p>
            ` : ''}
            
            ${career.roadmap ? `
            <div class="roadmap">
                <p><strong>Career Roadmap:</strong></p>
                ${career.roadmap.map(step => `
                    <div class="roadmap-step">
                        <strong>${step.timeframe}:</strong> ${step.description}
                    </div>
                `).join('')}
            </div>
            ` : ''}
        </div>
    `).join('')}
    
    ${data.analysis.recommendations.length > 0 ? `
    <h2>Recommendations</h2>
    <ul>${data.analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}</ul>
    ` : ''}
    
    ${data.analysis.nextSteps.length > 0 ? `
    <h2>Next Steps</h2>
    <ul>${data.analysis.nextSteps.map(step => `<li>${step}</li>`).join('')}</ul>
    ` : ''}
</body>
</html>`;
  }

  /**
   * Generate Word document content (simplified HTML)
   */
  private generateWordContent(data: CareerExportData): string {
    // For now, use the same HTML content as PDF
    // In a real implementation, you might use a library like docx
    return this.generatePDFContent(data);
  }

  /**
   * Download file to user's device
   */
  private downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate a shareable link for career analysis
   */
  async generateShareableLink(analysis: CareerAnalysis): Promise<string> {
    try {
      // In a real implementation, this would save to database and return a share URL
      const shareId = this.generateShareId();
      return `${window.location.origin}/career-analysis/shared/${shareId}`;
    } catch (error) {
      console.error('Failed to generate shareable link:', error);
      throw new Error('Failed to generate shareable link. Please try again.');
    }
  }

  /**
   * Generate unique share ID
   */
  private generateShareId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
}

export const careerExportService = new CareerExportService();
