// Cypress Integration Tests for AI Book Generator
// These tests validate the complete user workflow and context flow

describe('AI Book Generator - Integration Tests', () => {
  beforeEach(() => {
    // Visit the application and navigate to book generator
    cy.visit('/');
    
    // Mock the AI API responses to avoid actual API calls during testing
    cy.intercept('POST', 'https://openrouter.ai/api/v1/chat/completions', {
      statusCode: 200,
      body: {
        choices: [{
          message: {
            content: 'Generated test content for chapter'
          }
        }]
      }
    }).as('aiGeneration');
    
    // Navigate to book generator
    cy.get('[data-testid="book-generator-nav"]').click();
  });

  describe('Complete Book Generation Workflow', () => {
    it('should complete the full book creation process', () => {
      // Step 1: Fill Book Metadata
      cy.get('[data-testid="book-title-input"]').type('Test Book: AI in Education');
      cy.get('[data-testid="book-subtitle-input"]').type('A Comprehensive Guide');
      
      // Select genre
      cy.get('[data-testid="genre-select"]').click();
      cy.get('[data-value="Education"]').click();
      
      // Select target audience
      cy.get('[data-testid="audience-select"]').click();
      cy.get('[data-value="Students (Graduate)"]').click();
      
      // Select tone
      cy.get('[data-testid="tone-select"]').click();
      cy.get('[data-value="academic"]').click();
      
      // Select length
      cy.get('[data-testid="length-select"]').click();
      cy.get('[data-value="medium"]').click();
      
      // Fill description
      cy.get('[data-testid="description-textarea"]').type(
        'This book explores the intersection of artificial intelligence and education, ' +
        'providing practical insights for educators and students.'
      );
      
      // Add keywords
      cy.get('[data-testid="keyword-input"]').type('AI');
      cy.get('[data-testid="add-keyword-btn"]').click();
      cy.get('[data-testid="keyword-input"]').type('Education');
      cy.get('[data-testid="add-keyword-btn"]').click();
      
      // Add author
      cy.get('[data-testid="author-input"]').type('Dr. Jane Smith');
      cy.get('[data-testid="add-author-btn"]').click();
      
      // Proceed to chapter definition
      cy.get('[data-testid="next-to-chapters-btn"]').click();
      
      // Step 2: Define Chapters
      cy.url().should('include', 'chapters');
      
      // Add first chapter
      cy.get('[data-testid="add-chapter-btn"]').click();
      
      // Fill chapter 1 details
      cy.get('[data-testid="chapter-0-title"]').clear().type('Introduction to AI in Education');
      cy.get('[data-testid="chapter-0-description"]').type(
        'This chapter introduces the fundamental concepts of AI and its applications in educational settings.'
      );
      cy.get('[data-testid="chapter-0-word-count"]').clear().type('3000');
      
      // Add sub-sections to chapter 1
      cy.get('[data-testid="chapter-0-edit-outline"]').click();
      cy.get('[data-testid="chapter-0-new-subsection"]').type('What is Artificial Intelligence?');
      cy.get('[data-testid="chapter-0-add-subsection"]').click();
      cy.get('[data-testid="chapter-0-new-subsection"]').type('AI Applications in Education');
      cy.get('[data-testid="chapter-0-add-subsection"]').click();
      
      // Add content to chapter 1
      cy.get('[data-testid="chapter-0-add-text"]').click();
      cy.get('[data-testid="chapter-0-content-0-text"]').type(
        'Artificial Intelligence represents a paradigm shift in how we approach education. ' +
        'This chapter will explore the fundamental concepts and practical applications.'
      );
      
      // Add second chapter
      cy.get('[data-testid="add-chapter-btn"]').click();
      cy.get('[data-testid="chapter-1-title"]').clear().type('Machine Learning in Classrooms');
      cy.get('[data-testid="chapter-1-description"]').type(
        'Exploring practical implementations of machine learning technologies in educational environments.'
      );
      
      // Step 3: Start Generation
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Verify generation starts
      cy.get('[data-testid="generation-progress"]').should('be.visible');
      cy.get('[data-testid="generating-indicator"]').should('contain', 'Generating Your Book');
      
      // Wait for AI calls and verify they happen
      cy.wait('@aiGeneration').then((interception) => {
        expect(interception.request.body).to.have.property('messages');
        expect(interception.request.body.messages[0]).to.have.property('content');
      });
      
      // Step 4: Monitor Progress
      cy.get('[data-testid="progress-bar"]').should('be.visible');
      cy.get('[data-testid="chapter-progress-item"]').should('have.length.at.least', 2);
      
      // Verify context management
      cy.get('[data-testid="context-status"]').should('be.visible');
      cy.get('[data-testid="word-count-display"]').should('contain', 'Words Generated');
      
      // Step 5: Verify Completion (mocked)
      // In a real test, you'd wait for actual completion
      // Here we'll simulate completion by checking the UI updates
      cy.get('[data-testid="generation-complete"]', { timeout: 10000 }).should('be.visible');
      
      // Step 6: Test Export Functionality
      cy.get('[data-testid="export-book-btn"]').click();
      cy.get('[data-testid="export-dialog"]').should('be.visible');
      
      // Test export options
      cy.get('[data-testid="export-format-select"]').click();
      cy.get('[data-value="docx"]').click();
      
      // Enable/disable export options
      cy.get('[data-testid="include-toc-checkbox"]').should('be.checked');
      cy.get('[data-testid="include-bibliography-checkbox"]').should('be.checked');
      
      // Test export
      cy.get('[data-testid="export-confirm-btn"]').click();
      
      // Step 7: Test Editor Integration
      cy.get('[data-testid="edit-in-editor-btn"]').click();
      cy.get('[data-testid="editor-content"]').should('contain', 'Test Book: AI in Education');
    });
  });

  describe('Context Flow Validation', () => {
    it('should maintain context consistency across chapters', () => {
      // Set up a book with multiple chapters
      setupBookWithMultipleChapters();
      
      // Start generation
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Monitor context updates
      cy.get('[data-testid="context-summary"]').should('be.visible');
      
      // Verify context is passed between chapters
      cy.wait('@aiGeneration').then((interception) => {
        const requestBody = interception.request.body;
        expect(requestBody.messages[0].content).to.include('Previous chapters context');
      });
      
      // Check context optimization
      cy.get('[data-testid="context-token-count"]').should('be.visible');
      cy.get('[data-testid="context-optimization-status"]').should('contain', 'Optimized');
    });

    it('should handle context limits gracefully', () => {
      // Create a book with many chapters to test context limits
      setupLargeBook();
      
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Verify context optimization occurs
      cy.get('[data-testid="context-warning"]').should('be.visible');
      cy.get('[data-testid="context-optimization-log"]').should('contain', 'Context optimized');
    });
  });

  describe('Citation Management', () => {
    it('should extract and manage citations correctly', () => {
      setupBookWithCitations();
      
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Wait for generation to complete
      cy.get('[data-testid="generation-complete"]', { timeout: 15000 }).should('be.visible');
      
      // Check citations tab
      cy.get('[data-testid="citations-tab"]').click();
      cy.get('[data-testid="citation-count"]').should('contain', 'Citations Found');
      cy.get('[data-testid="bibliography-section"]').should('be.visible');
      
      // Verify citation deduplication
      cy.get('[data-testid="unique-references-count"]').should('be.visible');
      
      // Test citation export
      cy.get('[data-testid="copy-bibliography-btn"]').click();
      cy.get('[data-testid="citation-copied-toast"]').should('be.visible');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Mock API error
      cy.intercept('POST', 'https://openrouter.ai/api/v1/chat/completions', {
        statusCode: 500,
        body: { error: 'Internal Server Error' }
      }).as('aiError');
      
      setupBasicBook();
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Verify error handling
      cy.wait('@aiError');
      cy.get('[data-testid="error-message"]').should('be.visible');
      cy.get('[data-testid="retry-generation-btn"]').should('be.visible');
      
      // Test retry functionality
      cy.intercept('POST', 'https://openrouter.ai/api/v1/chat/completions', {
        statusCode: 200,
        body: {
          choices: [{ message: { content: 'Retry successful' } }]
        }
      }).as('aiRetry');
      
      cy.get('[data-testid="retry-generation-btn"]').click();
      cy.wait('@aiRetry');
      cy.get('[data-testid="generation-progress"]').should('be.visible');
    });
  });

  describe('Performance Tests', () => {
    it('should handle large books efficiently', () => {
      setupLargeBook();
      
      // Measure performance
      cy.window().then((win) => {
        win.performance.mark('generation-start');
      });
      
      cy.get('[data-testid="generate-book-btn"]').click();
      
      // Monitor memory usage and performance
      cy.get('[data-testid="performance-metrics"]').should('be.visible');
      cy.get('[data-testid="memory-usage"]').should('contain', 'MB');
      
      cy.window().then((win) => {
        win.performance.mark('generation-end');
        win.performance.measure('generation-time', 'generation-start', 'generation-end');
        
        const measure = win.performance.getEntriesByName('generation-time')[0];
        expect(measure.duration).to.be.lessThan(30000); // Should complete within 30 seconds
      });
    });
  });

  // Helper functions
  function setupBookWithMultipleChapters() {
    cy.get('[data-testid="book-title-input"]').type('Multi-Chapter Test Book');
    cy.get('[data-testid="genre-select"]').click();
    cy.get('[data-value="Non-Fiction"]').click();
    cy.get('[data-testid="audience-select"]').click();
    cy.get('[data-value="General Public"]').click();
    cy.get('[data-testid="tone-select"]').click();
    cy.get('[data-value="professional"]').click();
    cy.get('[data-testid="length-select"]').click();
    cy.get('[data-value="medium"]').click();
    cy.get('[data-testid="description-textarea"]').type('Test book for context validation');
    cy.get('[data-testid="next-to-chapters-btn"]').click();
    
    // Add multiple chapters
    for (let i = 0; i < 5; i++) {
      cy.get('[data-testid="add-chapter-btn"]').click();
      cy.get(`[data-testid="chapter-${i}-title"]`).clear().type(`Chapter ${i + 1}`);
      cy.get(`[data-testid="chapter-${i}-description"]`).type(`Description for chapter ${i + 1}`);
    }
  }

  function setupLargeBook() {
    setupBookWithMultipleChapters();
    
    // Add many more chapters to test limits
    for (let i = 5; i < 15; i++) {
      cy.get('[data-testid="add-chapter-btn"]').click();
      cy.get(`[data-testid="chapter-${i}-title"]`).clear().type(`Chapter ${i + 1}`);
      cy.get(`[data-testid="chapter-${i}-description"]`).type(`Description for chapter ${i + 1}`);
    }
  }

  function setupBookWithCitations() {
    setupBookWithMultipleChapters();
    
    // Add content with citations
    cy.get('[data-testid="chapter-0-add-text"]').click();
    cy.get('[data-testid="chapter-0-content-0-text"]').type(
      'According to Smith (2023), AI has revolutionized education. ' +
      'Johnson et al. (2022) found similar results in their study.'
    );
  }

  function setupBasicBook() {
    cy.get('[data-testid="book-title-input"]').type('Basic Test Book');
    cy.get('[data-testid="genre-select"]').click();
    cy.get('[data-value="Non-Fiction"]').click();
    cy.get('[data-testid="audience-select"]').click();
    cy.get('[data-value="General Public"]').click();
    cy.get('[data-testid="tone-select"]').click();
    cy.get('[data-value="professional"]').click();
    cy.get('[data-testid="length-select"]').click();
    cy.get('[data-value="short"]').click();
    cy.get('[data-testid="description-textarea"]').type('Basic test book');
    cy.get('[data-testid="next-to-chapters-btn"]').click();
    
    cy.get('[data-testid="add-chapter-btn"]').click();
    cy.get('[data-testid="chapter-0-title"]').clear().type('Test Chapter');
    cy.get('[data-testid="chapter-0-description"]').type('Test chapter description');
  }
});
