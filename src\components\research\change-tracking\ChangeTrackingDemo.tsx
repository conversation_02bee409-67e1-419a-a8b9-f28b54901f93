import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { useChangeTracking, ChangeTrackingControls, DiffViewer } from './index';
import { toast } from 'sonner';

export function ChangeTrackingDemo() {
  const [content, setContent] = useState('This is the original content. It will be modified by AI to demonstrate change tracking.');
  const changeTracking = useChangeTracking();

  const handleToggleTracking = (enabled: boolean) => {
    if (enabled) {
      changeTracking.startTracking(content);
      toast.success('Change tracking started');
    } else {
      changeTracking.stopTracking();
      toast.info('Change tracking stopped');
    }
  };

  const handleToggleDiffView = () => {
    changeTracking.toggleViewMode();
  };

  const handleAcceptChange = (changeId: string) => {
    changeTracking.acceptChange(changeId);
    toast.success('Change accepted');
  };

  const handleRejectChange = (changeId: string) => {
    changeTracking.rejectChange(changeId);
    toast.success('Change rejected');
  };

  const handleAcceptAllChanges = () => {
    changeTracking.acceptAllChanges();
    toast.success('All changes accepted');
  };

  const handleRejectAllChanges = () => {
    const originalContent = changeTracking.rejectAllChanges();
    if (originalContent) {
      setContent(originalContent);
      toast.success('All changes rejected, content reverted');
    }
  };

  const handleNavigateChange = (direction: 'next' | 'previous') => {
    if (direction === 'next') {
      changeTracking.navigateToNextChange();
    } else {
      changeTracking.navigateToPreviousChange();
    }
  };

  const simulateAIChange = () => {
    if (!changeTracking.isTrackingEnabled) {
      toast.error('Please start change tracking first');
      return;
    }

    const originalText = 'original content';
    const newText = 'enhanced content with AI improvements';
    const startPos = content.indexOf(originalText);
    
    if (startPos !== -1) {
      const endPos = startPos + originalText.length;
      const newContent = content.replace(originalText, newText);
      
      // Record the change
      changeTracking.recordChange(
        originalText,
        newText,
        startPos,
        endPos,
        'replace',
        'Enhance this text to be more engaging'
      );
      
      // Update content
      setContent(newContent);
      changeTracking.updateCurrentContent(newContent);
      
      toast.success('AI change simulated');
    } else {
      toast.error('Could not find text to replace');
    }
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    if (changeTracking.isTrackingEnabled) {
      changeTracking.updateCurrentContent(newContent);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Change Tracking Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Change Tracking Controls */}
          <ChangeTrackingControls
            onToggleTracking={handleToggleTracking}
            onToggleDiffView={handleToggleDiffView}
            onAcceptAllChanges={handleAcceptAllChanges}
            onRejectAllChanges={handleRejectAllChanges}
          />

          {/* Content Editor */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Content:</label>
            <Textarea
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={6}
              className="w-full"
            />
          </div>

          {/* Demo Actions */}
          <div className="flex gap-2">
            <Button onClick={simulateAIChange} variant="outline">
              Simulate AI Change
            </Button>
          </div>

          {/* Diff Viewer */}
          {changeTracking.viewMode === 'diff' && changeTracking.hasChanges && (
            <DiffViewer
              onAcceptChange={handleAcceptChange}
              onRejectChange={handleRejectChange}
              onAcceptAllChanges={handleAcceptAllChanges}
              onRejectAllChanges={handleRejectAllChanges}
              onNavigateChange={handleNavigateChange}
            />
          )}

          {/* Status Info */}
          <div className="text-sm text-gray-600 space-y-1">
            <div>Tracking Enabled: {changeTracking.isTrackingEnabled ? 'Yes' : 'No'}</div>
            <div>View Mode: {changeTracking.viewMode}</div>
            <div>Pending Changes: {changeTracking.pendingChangesCount}</div>
            <div>Has Changes: {changeTracking.hasChanges ? 'Yes' : 'No'}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
