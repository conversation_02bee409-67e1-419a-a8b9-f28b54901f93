# Authentication Testing Guide

## 🧪 **Test 1: Email/Password Authentication**

### Step 1: Test Demo Account Login
1. **Go to**: http://localhost:8081/login
2. **Enter credentials**:
   - Email: `<EMAIL>`
   - Password: `Password123!`
3. **Click**: "Sign In"
4. **Expected**: Should login successfully and redirect to dashboard

### Step 2: Test New User Registration
1. **Go to**: http://localhost:8081/signup
2. **Enter new credentials**:
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
   - Full Name: `Test User`
3. **Click**: "Create Account"
4. **Expected**: Should create account and login

## 🔧 **Test 2: Google OAuth (After Configuration)**

### Prerequisites
- Google OAuth must be configured in Supabase dashboard
- Client ID and Secret must be added
- Redirect URLs must be set

### Test Steps
1. **Go to**: http://localhost:8081/login
2. **Click**: "Continue with Google"
3. **Expected**: 
   - Redirects to Google login
   - After Google login, redirects back to app
   - User is logged in successfully

## 🚨 **Current Issue: Google OAuth Not Configured**

The error you're seeing indicates that Google OAuth is not properly set up in Supabase:

```
Unable to exchange external code: 4/0AVMBsJj0RLUattggUSxTiAfuxDBbx4Uupl7NAKxRW4raRFQnSDjjumi2o5P0Q4vJZ4MH3Q
```

This means:
- ✅ Google Cloud Console is configured correctly
- ✅ Your app receives the authorization code from Google
- ❌ Supabase can't exchange the code because Google provider is not configured

## 🛠️ **Fix Required**

You need to configure Google OAuth in Supabase:

1. **Go to Supabase Dashboard**:
   ```
   https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers
   ```

2. **Enable Google Provider**:
   - Find "Google" in the providers list
   - Toggle it ON
   - Add your Google Client ID and Secret

3. **Set Redirect URLs**:
   - Site URL: `http://localhost:8081`
   - Redirect URLs: `http://localhost:8081/auth/callback`

## 📋 **Testing Checklist**

### Email/Password Authentication
- [ ] Demo account login works
- [ ] New user registration works
- [ ] Password reset works (if implemented)
- [ ] User profile creation works
- [ ] Dashboard access after login

### Google OAuth Authentication (After Configuration)
- [ ] Google login button works
- [ ] Redirects to Google correctly
- [ ] Google login completes successfully
- [ ] Redirects back to app
- [ ] User is logged in
- [ ] User profile is created/updated
- [ ] Dashboard access after login

### General Authentication
- [ ] Protected routes work correctly
- [ ] Logout works
- [ ] Session persistence works
- [ ] Authentication state updates correctly
- [ ] No console errors during auth flow

## 🎯 **Expected Behavior After Fix**

1. **Email/Password**: Should work immediately (already configured)
2. **Google OAuth**: Should work after Supabase configuration
3. **Both methods**: Should create/update user profiles correctly
4. **All features**: Should work for authenticated users

## 🔍 **Debugging Tips**

### Check Browser Console
- Look for authentication errors
- Check network requests to Supabase
- Verify redirect URLs are correct

### Check Supabase Dashboard
- Verify user creation in Auth > Users
- Check authentication logs
- Verify provider configuration

### Test in Incognito Mode
- Eliminates cache/cookie issues
- Provides clean authentication test
- Helps identify persistent issues

## 📞 **Next Steps**

1. **Test email/password authentication first** (should work)
2. **Configure Google OAuth in Supabase dashboard**
3. **Test Google OAuth authentication**
4. **Verify all app features work for authenticated users**
5. **Prepare for production deployment**
