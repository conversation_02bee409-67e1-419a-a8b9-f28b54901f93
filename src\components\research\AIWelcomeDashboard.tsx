import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  FileText,
  BookOpen,
  FileCheck,
  BarChart3,
  Search,
  MessageSquare,
  Sparkles,
  Brain,
  GraduationCap,
  ArrowRight,
  Star,
  Clock,
  Users,
  Zap,
  Target,
  Award,
  TrendingUp,
  Play,
  Globe,
  Workflow
} from 'lucide-react';
import { ActiveView } from './ResearchDashboard';
import { useAuth } from '@/contexts/AuthContext';

interface AIWelcomeDashboardProps {
  onSelectTool: (view: ActiveView) => void;
}

export function AIWelcomeDashboard({ onSelectTool }: AIWelcomeDashboardProps) {
  const { user } = useAuth();
  const [hoveredTool, setHoveredTool] = useState<string | null>(null);

  const aiTools = [
    {
      id: 'ai-generator' as ActiveView,
      title: 'AI Paper Generator',
      description: 'Generate complete research papers with methodology, results, and citations',
      icon: Sparkles,
      color: 'from-blue-500 to-purple-600',
      badge: 'Most Popular',
      features: ['Auto-citation extraction', 'Section generation', 'Reference formatting'],
      estimatedTime: '5-10 minutes'
    },
    {
      id: 'book-generator' as ActiveView,
      title: 'AI Book Generator',
      description: 'Create comprehensive books and long-form content with chapters',
      icon: BookOpen,
      color: 'from-green-500 to-teal-600',
      badge: 'New',
      features: ['Chapter organization', 'Table of contents', 'Book formatting'],
      estimatedTime: '15-30 minutes'
    },
    {
      id: 'article-reviewer' as ActiveView,
      title: 'AI Article Reviewer',
      description: 'Analyze and review research articles with detailed feedback',
      icon: FileCheck,
      color: 'from-orange-500 to-red-600',
      badge: 'Expert',
      features: ['Quality assessment', 'Improvement suggestions', 'Citation analysis'],
      estimatedTime: '3-5 minutes'
    },
    {
      id: 'research-analysis' as ActiveView,
      title: 'Research Analysis',
      description: 'Perform advanced data analysis and visualization for research',
      icon: BarChart3,
      color: 'from-purple-500 to-pink-600',
      badge: 'Pro',
      features: ['Data visualization', 'Statistical analysis', 'Report generation'],
      estimatedTime: '10-20 minutes'
    },
    {
      id: 'research-search' as ActiveView,
      title: 'Research Search',
      description: 'AI-powered academic search with real-time citations and sources',
      icon: Globe,
      color: 'from-green-500 to-teal-600',
      badge: 'New',
      features: ['Academic search', 'Real-time citations', 'Source verification'],
      estimatedTime: '1-3 minutes'
    },
    {
      id: 'flowchart-fun' as ActiveView,
      title: 'Flowchart Fun',
      description: 'Create beautiful flowcharts from simple text with real-time visualization',
      icon: Workflow,
      color: 'from-indigo-500 to-purple-600',
      badge: 'New',
      features: ['Text-to-flowchart', 'Real-time preview', 'Multiple export formats'],
      estimatedTime: '2-5 minutes'
    }
  ];

  const quickActions = [
    {
      id: 'search',
      title: 'Literature Search',
      description: 'Find relevant research papers and sources',
      icon: Search,
      action: () => onSelectTool('search')
    },
    {
      id: 'citations',
      title: 'Citation Manager',
      description: 'Organize and format your references',
      icon: FileText,
      action: () => onSelectTool('citations')
    },
    {
      id: 'chat',
      title: 'AI Chat Assistant',
      description: 'Get help with research questions',
      icon: MessageSquare,
      action: () => onSelectTool('chat')
    },
    {
      id: 'editor',
      title: 'Document Editor',
      description: 'Write and edit your documents',
      icon: FileText,
      action: () => onSelectTool('editor')
    }
  ];

  const stats = [
    { label: 'Papers Generated', value: '12,000+', icon: FileText },
    { label: 'Active Researchers', value: '3,500+', icon: Users },
    { label: 'Citations Processed', value: '50,000+', icon: Award },
    { label: 'Success Rate', value: '98%', icon: TrendingUp }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-16">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-8">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-2xl opacity-20 animate-pulse"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-full">
                  <Brain className="h-16 w-16 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
              AI Research Assistant
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Transform your research process with powerful AI tools. Generate papers, analyze data, and accelerate your academic work.
            </p>
            
            {user ? (
              <div className="flex items-center justify-center gap-4 text-lg">
                <div className="flex items-center gap-2 text-green-600">
                  <GraduationCap className="h-6 w-6" />
                  <span>Welcome back, {user.email?.split('@')[0]}!</span>
                </div>
              </div>
            ) : (
              <div className="text-lg text-gray-600">
                Sign in to save your work and access premium features
              </div>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardContent className="p-6">
                  <stat.icon className="h-8 w-8 mx-auto mb-3 text-blue-600" />
                  <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* AI Tools Section */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Choose Your AI Research Tool
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Select the perfect AI assistant for your research needs. Each tool is designed to streamline different aspects of academic work.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {aiTools.map((tool) => (
            <Card
              key={tool.id}
              className={`relative overflow-hidden border-0 shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105 ${
                hoveredTool === tool.id ? 'shadow-2xl' : ''
              }`}
              onMouseEnter={() => setHoveredTool(tool.id)}
              onMouseLeave={() => setHoveredTool(null)}
              onClick={() => onSelectTool(tool.id)}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${tool.color} opacity-5`}></div>
              <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${tool.color}`}></div>
              
              <CardHeader className="relative pb-4">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${tool.color} shadow-lg`}>
                    <tool.icon className="h-8 w-8 text-white" />
                  </div>
                  {tool.badge && (
                    <Badge className={`bg-gradient-to-r ${tool.color} text-white border-0`}>
                      {tool.badge}
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                  {tool.title}
                </CardTitle>
                <p className="text-gray-600 text-lg leading-relaxed">
                  {tool.description}
                </p>
              </CardHeader>
              
              <CardContent className="relative">
                <div className="space-y-4 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>Estimated time: {tool.estimatedTime}</span>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {tool.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                          <Star className="h-3 w-3 text-yellow-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <Button
                  className={`w-full bg-gradient-to-r ${tool.color} hover:opacity-90 text-white border-0 shadow-lg`}
                  size="lg"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start {tool.title}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200 p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Button
                key={action.id}
                variant="outline"
                className="h-auto p-6 flex flex-col items-center gap-3 hover:shadow-lg transition-all duration-200 border-2 hover:border-blue-300"
                onClick={action.action}
              >
                <action.icon className="h-8 w-8 text-blue-600" />
                <div className="text-center">
                  <div className="font-semibold text-gray-900">{action.title}</div>
                  <div className="text-xs text-gray-600 mt-1">{action.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Features Highlight */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <div className="flex items-center justify-center mb-4">
              <Zap className="h-12 w-12" />
            </div>
            <h3 className="text-2xl font-bold mb-4">
              Powered by Advanced AI
            </h3>
            <p className="text-lg opacity-90 max-w-2xl mx-auto">
              Our research assistant uses state-of-the-art AI models to understand academic writing, 
              extract citations, format references, and maintain the highest standards of scholarly work.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
