import { 
  SavedArticleReview, 
  SavedSec<PERSON><PERSON><PERSON>iew, 
  SavedDetailedFeedback 
} from './article-review-storage.service';

/**
 * Service for exporting article reviews to various formats
 */
export class ArticleExportService {

  /**
   * Export review as formatted text
   */
  exportAsText(
    review: SavedArticleReview,
    sections: SavedSectionReview[],
    feedback: Record<string, SavedDetailedFeedback[]>
  ): string {
    let content = '';

    // Header
    content += `ARTICLE REVIEW REPORT\n`;
    content += `${'='.repeat(50)}\n\n`;

    // Basic Information
    content += `Title: ${review.title}\n`;
    content += `Original File: ${review.original_filename}\n`;
    content += `Review Date: ${new Date(review.created_at).toLocaleDateString()}\n`;
    content += `AI Model: ${review.ai_model_used}\n`;
    content += `Overall Score: ${review.overall_score ? Math.round(review.overall_score) : 'N/A'}/100\n\n`;

    // Summary
    if (review.review_summary) {
      content += `EXECUTIVE SUMMARY\n`;
      content += `${'-'.repeat(20)}\n`;
      content += `${review.review_summary}\n\n`;
    }

    // Scores
    content += `DETAILED SCORES\n`;
    content += `${'-'.repeat(20)}\n`;
    content += `Clarity: ${review.clarity_score ? Math.round(review.clarity_score) : 'N/A'}/100\n`;
    content += `Structure: ${review.structure_score ? Math.round(review.structure_score) : 'N/A'}/100\n`;
    content += `Methodology: ${review.methodology_score ? Math.round(review.methodology_score) : 'N/A'}/100\n`;
    content += `Significance: ${review.significance_score ? Math.round(review.significance_score) : 'N/A'}/100\n`;
    content += `Originality: ${review.originality_score ? Math.round(review.originality_score) : 'N/A'}/100\n\n`;

    // Major Strengths
    if (review.major_strengths && review.major_strengths.length > 0) {
      content += `MAJOR STRENGTHS\n`;
      content += `${'-'.repeat(20)}\n`;
      review.major_strengths.forEach((strength, index) => {
        content += `${index + 1}. ${strength}\n`;
      });
      content += '\n';
    }

    // Major Weaknesses
    if (review.major_weaknesses && review.major_weaknesses.length > 0) {
      content += `MAJOR WEAKNESSES\n`;
      content += `${'-'.repeat(20)}\n`;
      review.major_weaknesses.forEach((weakness, index) => {
        content += `${index + 1}. ${weakness}\n`;
      });
      content += '\n';
    }

    // Recommendations
    if (review.recommendations && review.recommendations.length > 0) {
      content += `RECOMMENDATIONS\n`;
      content += `${'-'.repeat(20)}\n`;
      review.recommendations.forEach((recommendation, index) => {
        content += `${index + 1}. ${recommendation}\n`;
      });
      content += '\n';
    }

    // Section-by-Section Analysis
    if (sections.length > 0) {
      content += `SECTION-BY-SECTION ANALYSIS\n`;
      content += `${'='.repeat(30)}\n\n`;

      sections.forEach((section) => {
        content += `${section.section_name.toUpperCase()}\n`;
        content += `${'-'.repeat(section.section_name.length)}\n`;
        
        if (section.score) {
          content += `Score: ${Math.round(section.score)}/100\n\n`;
        }

        content += `Analysis:\n${section.analysis}\n\n`;

        if (section.strengths && section.strengths.length > 0) {
          content += `Strengths:\n`;
          section.strengths.forEach((strength, index) => {
            content += `• ${strength}\n`;
          });
          content += '\n';
        }

        if (section.weaknesses && section.weaknesses.length > 0) {
          content += `Areas for Improvement:\n`;
          section.weaknesses.forEach((weakness, index) => {
            content += `• ${weakness}\n`;
          });
          content += '\n';
        }

        if (section.suggestions && section.suggestions.length > 0) {
          content += `Suggestions:\n`;
          section.suggestions.forEach((suggestion, index) => {
            content += `• ${suggestion}\n`;
          });
          content += '\n';
        }

        // Detailed Feedback
        const sectionFeedback = feedback[section.section_name];
        if (sectionFeedback && sectionFeedback.length > 0) {
          content += `Detailed Feedback:\n`;
          sectionFeedback.forEach((item, index) => {
            content += `${index + 1}. [${item.severity.toUpperCase()} - ${item.feedback_type}]\n`;
            content += `   Original: "${item.original_text}"\n`;
            content += `   Issue: ${item.issue_description}\n`;
            content += `   Suggestion: ${item.suggestion}\n\n`;
          });
        }

        content += `${'-'.repeat(50)}\n\n`;
      });
    }

    // Footer
    content += `Report generated on ${new Date().toLocaleString()}\n`;
    content += `Generated by Verbira AI Article Reviewer\n`;

    return content;
  }

  /**
   * Download review as text file
   */
  downloadAsText(
    review: SavedArticleReview,
    sections: SavedSectionReview[],
    feedback: Record<string, SavedDetailedFeedback[]>
  ): void {
    const content = this.exportAsText(review, sections, feedback);
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${review.title.replace(/[^a-zA-Z0-9]/g, '_')}_review.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Export review as HTML
   */
  exportAsHTML(
    review: SavedArticleReview,
    sections: SavedSectionReview[],
    feedback: Record<string, SavedDetailedFeedback[]>
  ): string {
    let html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Article Review: ${review.title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .header { border-bottom: 3px solid #3b82f6; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1e40af; font-size: 28px; margin-bottom: 10px; }
        .meta { color: #6b7280; font-size: 14px; }
        .section { margin-bottom: 30px; }
        .section-title { color: #1e40af; font-size: 20px; border-bottom: 2px solid #e5e7eb; padding-bottom: 5px; margin-bottom: 15px; }
        .score { background: #f3f4f6; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .score-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }
        .score-item { text-align: center; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e5e7eb; }
        .score-value { font-size: 24px; font-weight: bold; color: #059669; }
        .strengths { background: #f0fdf4; border-left: 4px solid #22c55e; padding: 15px; margin: 10px 0; }
        .weaknesses { background: #fef2f2; border-left: 4px solid #ef4444; padding: 15px; margin: 10px 0; }
        .recommendations { background: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 10px 0; }
        .feedback-item { background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .feedback-header { font-weight: bold; color: #374151; margin-bottom: 10px; }
        .severity-major { border-left: 4px solid #ef4444; }
        .severity-moderate { border-left: 4px solid #f59e0b; }
        .severity-minor { border-left: 4px solid #3b82f6; }
        ul { padding-left: 20px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">${review.title}</h1>
        <div class="meta">
            <strong>File:</strong> ${review.original_filename} | 
            <strong>Date:</strong> ${new Date(review.created_at).toLocaleDateString()} | 
            <strong>AI Model:</strong> ${review.ai_model_used}
        </div>
    </div>
`;

    // Overall Score
    if (review.overall_score) {
      html += `
    <div class="section">
        <h2 class="section-title">Overall Score</h2>
        <div class="score">
            <div style="text-align: center; font-size: 36px; font-weight: bold; color: ${review.overall_score >= 80 ? '#059669' : review.overall_score >= 60 ? '#d97706' : '#dc2626'};">
                ${Math.round(review.overall_score)}/100
            </div>
        </div>
    </div>
`;
    }

    // Detailed Scores
    html += `
    <div class="section">
        <h2 class="section-title">Detailed Scores</h2>
        <div class="score-grid">
            <div class="score-item">
                <div class="score-value">${review.clarity_score ? Math.round(review.clarity_score) : 'N/A'}</div>
                <div>Clarity</div>
            </div>
            <div class="score-item">
                <div class="score-value">${review.structure_score ? Math.round(review.structure_score) : 'N/A'}</div>
                <div>Structure</div>
            </div>
            <div class="score-item">
                <div class="score-value">${review.methodology_score ? Math.round(review.methodology_score) : 'N/A'}</div>
                <div>Methodology</div>
            </div>
            <div class="score-item">
                <div class="score-value">${review.significance_score ? Math.round(review.significance_score) : 'N/A'}</div>
                <div>Significance</div>
            </div>
            <div class="score-item">
                <div class="score-value">${review.originality_score ? Math.round(review.originality_score) : 'N/A'}</div>
                <div>Originality</div>
            </div>
        </div>
    </div>
`;

    // Summary
    if (review.review_summary) {
      html += `
    <div class="section">
        <h2 class="section-title">Executive Summary</h2>
        <p>${review.review_summary}</p>
    </div>
`;
    }

    // Strengths, Weaknesses, Recommendations
    if (review.major_strengths && review.major_strengths.length > 0) {
      html += `
    <div class="section">
        <h2 class="section-title">Major Strengths</h2>
        <div class="strengths">
            <ul>
                ${review.major_strengths.map(strength => `<li>${strength}</li>`).join('')}
            </ul>
        </div>
    </div>
`;
    }

    if (review.major_weaknesses && review.major_weaknesses.length > 0) {
      html += `
    <div class="section">
        <h2 class="section-title">Major Weaknesses</h2>
        <div class="weaknesses">
            <ul>
                ${review.major_weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
            </ul>
        </div>
    </div>
`;
    }

    if (review.recommendations && review.recommendations.length > 0) {
      html += `
    <div class="section">
        <h2 class="section-title">Recommendations</h2>
        <div class="recommendations">
            <ul>
                ${review.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
    </div>
`;
    }

    // Section Analysis
    if (sections.length > 0) {
      html += `<div class="section"><h2 class="section-title">Section-by-Section Analysis</h2>`;
      
      sections.forEach((section) => {
        html += `
        <div class="section">
            <h3>${section.section_name.charAt(0).toUpperCase() + section.section_name.slice(1)}</h3>
            ${section.score ? `<div class="score">Score: ${Math.round(section.score)}/100</div>` : ''}
            <p><strong>Analysis:</strong> ${section.analysis}</p>
`;

        if (section.strengths && section.strengths.length > 0) {
          html += `
            <div class="strengths">
                <strong>Strengths:</strong>
                <ul>${section.strengths.map(s => `<li>${s}</li>`).join('')}</ul>
            </div>
`;
        }

        if (section.weaknesses && section.weaknesses.length > 0) {
          html += `
            <div class="weaknesses">
                <strong>Areas for Improvement:</strong>
                <ul>${section.weaknesses.map(w => `<li>${w}</li>`).join('')}</ul>
            </div>
`;
        }

        if (section.suggestions && section.suggestions.length > 0) {
          html += `
            <div class="recommendations">
                <strong>Suggestions:</strong>
                <ul>${section.suggestions.map(s => `<li>${s}</li>`).join('')}</ul>
            </div>
`;
        }

        // Detailed Feedback
        const sectionFeedback = feedback[section.section_name];
        if (sectionFeedback && sectionFeedback.length > 0) {
          html += `<h4>Detailed Feedback</h4>`;
          sectionFeedback.forEach((item, index) => {
            html += `
            <div class="feedback-item severity-${item.severity}">
                <div class="feedback-header">${item.feedback_type.toUpperCase()} - ${item.severity.toUpperCase()}</div>
                <p><strong>Original:</strong> "${item.original_text}"</p>
                <p><strong>Issue:</strong> ${item.issue_description}</p>
                <p><strong>Suggestion:</strong> ${item.suggestion}</p>
            </div>
`;
          });
        }

        html += `</div>`;
      });
      
      html += `</div>`;
    }

    html += `
    <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
        Report generated on ${new Date().toLocaleString()}<br>
        Generated by Verbira AI Article Reviewer
    </div>
</body>
</html>
`;

    return html;
  }

  /**
   * Download review as HTML file
   */
  downloadAsHTML(
    review: SavedArticleReview,
    sections: SavedSectionReview[],
    feedback: Record<string, SavedDetailedFeedback[]>
  ): void {
    const content = this.exportAsHTML(review, sections, feedback);
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${review.title.replace(/[^a-zA-Z0-9]/g, '_')}_review.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Copy review summary to clipboard
   */
  async copyToClipboard(
    review: SavedArticleReview,
    sections: SavedSectionReview[],
    feedback: Record<string, SavedDetailedFeedback[]>
  ): Promise<boolean> {
    try {
      const content = this.exportAsText(review, sections, feedback);
      await navigator.clipboard.writeText(content);
      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }
}

export const articleExportService = new ArticleExportService();
