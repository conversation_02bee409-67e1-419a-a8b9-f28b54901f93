/**
 * Google Academic Service
 * Handles academic formatting, citations, and references for Google Search results
 */

import {
  GoogleSearchSource,
  GoogleCitation,
  GoogleReference,
  GoogleSearchOptions
} from '../types';

export class GoogleAcademicService {
  /**
   * Generate proper academic references from sources
   */
  generateReferences(
    sources: GoogleSearchSource[],
    citationStyle: string = 'apa'
  ): GoogleReference[] {
    return sources.map((source, index) => {
      const reference = this.createReference(source, citationStyle);
      return {
        id: `ref-${source.id}`,
        title: source.title,
        authors: source.authors || [this.extractAuthorFromDomain(source.domain)],
        year: source.year || new Date().getFullYear(),
        url: source.url,
        journal: source.journal,
        apaFormat: reference
      };
    });
  }

  /**
   * Create formatted reference based on citation style
   */
  private createReference(source: GoogleSearchSource, style: string): string {
    const authors = source.authors || [this.extractAuthorFromDomain(source.domain)];
    const year = source.year || new Date().getFullYear();
    const title = source.title;
    const url = source.url;
    const domain = source.domain.replace('www.', '');

    switch (style.toLowerCase()) {
      case 'apa':
        return this.formatAPA(authors, year, title, url, domain, source);
      case 'mla':
        return this.formatMLA(authors, year, title, url, domain);
      case 'chicago':
        return this.formatChicago(authors, year, title, url, domain);
      case 'harvard':
        return this.formatHarvard(authors, year, title, url, domain);
      default:
        return this.formatAPA(authors, year, title, url, domain, source);
    }
  }

  /**
   * Format reference in APA style
   */
  private formatAPA(
    authors: string[],
    year: number,
    title: string,
    url: string,
    domain: string,
    source: GoogleSearchSource
  ): string {
    const authorStr = this.formatAuthorsAPA(authors);
    const cleanTitle = this.cleanTitle(title);
    
    if (source.type === 'journal' && source.journal) {
      return `${authorStr} (${year}). ${cleanTitle}. *${source.journal}*${source.volume ? `, ${source.volume}` : ''}${source.issue ? `(${source.issue})` : ''}${source.pages ? `, ${source.pages}` : ''}. ${url}`;
    } else if (source.type === 'book') {
      return `${authorStr} (${year}). *${cleanTitle}*. Retrieved from ${url}`;
    } else {
      return `${authorStr} (${year}). ${cleanTitle}. *${domain}*. Retrieved from ${url}`;
    }
  }

  /**
   * Format reference in MLA style
   */
  private formatMLA(
    authors: string[],
    year: number,
    title: string,
    url: string,
    domain: string
  ): string {
    const authorStr = this.formatAuthorsMLA(authors);
    const cleanTitle = this.cleanTitle(title);
    const accessDate = new Date().toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });

    return `${authorStr} "${cleanTitle}" *${domain}*, ${year}, ${url}. Accessed ${accessDate}.`;
  }

  /**
   * Format reference in Chicago style
   */
  private formatChicago(
    authors: string[],
    year: number,
    title: string,
    url: string,
    domain: string
  ): string {
    const authorStr = this.formatAuthorsChicago(authors);
    const cleanTitle = this.cleanTitle(title);
    const accessDate = new Date().toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });

    return `${authorStr} "${cleanTitle}." ${domain}. ${year}. ${url} (accessed ${accessDate}).`;
  }

  /**
   * Format reference in Harvard style
   */
  private formatHarvard(
    authors: string[],
    year: number,
    title: string,
    url: string,
    domain: string
  ): string {
    const authorStr = this.formatAuthorsHarvard(authors);
    const cleanTitle = this.cleanTitle(title);

    return `${authorStr} ${year}, '${cleanTitle}', *${domain}*, viewed ${new Date().getDate()} ${new Date().toLocaleDateString('en-US', { month: 'long' })} ${new Date().getFullYear()}, <${url}>.`;
  }

  /**
   * Format authors for APA style
   */
  private formatAuthorsAPA(authors: string[]): string {
    if (authors.length === 0) return 'Unknown Author';
    if (authors.length === 1) return authors[0];
    if (authors.length === 2) return `${authors[0]} & ${authors[1]}`;
    
    const lastAuthor = authors[authors.length - 1];
    const otherAuthors = authors.slice(0, -1).join(', ');
    return `${otherAuthors}, & ${lastAuthor}`;
  }

  /**
   * Format authors for MLA style
   */
  private formatAuthorsMLA(authors: string[]): string {
    if (authors.length === 0) return 'Unknown Author';
    if (authors.length === 1) return authors[0];
    
    const firstAuthor = authors[0];
    const otherAuthors = authors.slice(1);
    return `${firstAuthor}, et al.`;
  }

  /**
   * Format authors for Chicago style
   */
  private formatAuthorsChicago(authors: string[]): string {
    if (authors.length === 0) return 'Unknown Author';
    if (authors.length === 1) return authors[0];
    if (authors.length === 2) return `${authors[0]} and ${authors[1]}`;
    
    const lastAuthor = authors[authors.length - 1];
    const otherAuthors = authors.slice(0, -1).join(', ');
    return `${otherAuthors}, and ${lastAuthor}`;
  }

  /**
   * Format authors for Harvard style
   */
  private formatAuthorsHarvard(authors: string[]): string {
    if (authors.length === 0) return 'Unknown Author';
    if (authors.length === 1) return authors[0];
    if (authors.length === 2) return `${authors[0]} & ${authors[1]}`;
    
    const lastAuthor = authors[authors.length - 1];
    const otherAuthors = authors.slice(0, -1).join(', ');
    return `${otherAuthors} & ${lastAuthor}`;
  }

  /**
   * Extract author name from domain
   */
  private extractAuthorFromDomain(domain: string): string {
    const cleanDomain = domain.replace('www.', '').replace('.com', '').replace('.org', '').replace('.edu', '').replace('.gov', '');

    // Handle special cases for known domains
    const domainMappings: { [key: string]: string } = {
      'nature': 'Nature Publishing Group',
      'science': 'Science Magazine',
      'pubmed': 'PubMed',
      'arxiv': 'arXiv',
      'ieee': 'IEEE',
      'acm': 'ACM Digital Library',
      'springer': 'Springer',
      'wiley': 'Wiley',
      'elsevier': 'Elsevier',
      'taylor': 'Taylor & Francis',
      'sage': 'SAGE Publications',
      'cambridge': 'Cambridge University Press',
      'oxford': 'Oxford University Press',
      'mit': 'MIT Press',
      'harvard': 'Harvard University',
      'stanford': 'Stanford University',
      'nih': 'National Institutes of Health',
      'who': 'World Health Organization',
      'cdc': 'Centers for Disease Control',
      'wikipedia': 'Wikipedia',
      'researchgate': 'ResearchGate',
      'academia': 'Academia.edu'
    };

    // Check for known mappings
    for (const [key, value] of Object.entries(domainMappings)) {
      if (cleanDomain.includes(key)) {
        return value;
      }
    }

    // Capitalize first letter and replace hyphens/underscores
    return cleanDomain
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Clean and format title
   */
  private cleanTitle(title: string): string {
    return title
      .replace(/^\d+\.\s*/, '') // Remove leading numbers
      .replace(/^-\s*/, '') // Remove leading dashes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Generate in-text citations
   */
  generateInTextCitations(
    sources: GoogleSearchSource[],
    citationStyle: string = 'apa'
  ): GoogleCitation[] {
    return sources.map((source, index) => ({
      id: `citation-${source.id}`,
      text: source.snippet,
      sourceId: source.id,
      url: source.url,
      title: source.title,
      position: index + 1,
      inTextFormat: this.generateInTextCitation(source, citationStyle)
    }));
  }

  /**
   * Generate in-text citation format
   */
  private generateInTextCitation(source: GoogleSearchSource, style: string): string {
    const authors = source.authors || [this.extractAuthorFromDomain(source.domain)];
    const year = source.year || new Date().getFullYear();
    const firstAuthor = authors[0];

    switch (style.toLowerCase()) {
      case 'apa':
        if (authors.length === 1) return `(${firstAuthor}, ${year})`;
        if (authors.length === 2) return `(${authors[0]} & ${authors[1]}, ${year})`;
        return `(${firstAuthor} et al., ${year})`;
        
      case 'mla':
        if (authors.length === 1) return `(${firstAuthor})`;
        return `(${firstAuthor} et al.)`;
        
      case 'chicago':
        return `(${firstAuthor} ${year})`;
        
      case 'harvard':
        return `(${firstAuthor} ${year})`;
        
      default:
        return `(${firstAuthor}, ${year})`;
    }
  }

  /**
   * Format complete academic response with proper citations
   */
  formatAcademicResponse(
    content: string,
    sources: GoogleSearchSource[],
    citationStyle: string = 'apa'
  ): { formattedContent: string; references: GoogleReference[] } {
    const references = this.generateReferences(sources, citationStyle);
    const citations = this.generateInTextCitations(sources, citationStyle);

    // Insert citations into content (basic implementation)
    let formattedContent = content;
    
    // Add citations at the end of paragraphs where relevant
    citations.forEach((citation, index) => {
      if (index < 3) { // Add first 3 citations to content
        const sentences = formattedContent.split('. ');
        if (sentences.length > index + 1) {
          sentences[index + 1] += ` ${citation.inTextFormat}`;
          formattedContent = sentences.join('. ');
        }
      }
    });

    return {
      formattedContent,
      references
    };
  }

  /**
   * Generate consolidated references section
   */
  generateReferencesSection(references: GoogleReference[]): string {
    const sortedRefs = references.sort((a, b) => 
      a.authors[0].localeCompare(b.authors[0])
    );

    let referencesSection = '\n\n## References\n\n';
    
    sortedRefs.forEach(ref => {
      referencesSection += `${ref.apaFormat}\n\n`;
    });

    return referencesSection;
  }
}

export const googleAcademicService = new GoogleAcademicService();
