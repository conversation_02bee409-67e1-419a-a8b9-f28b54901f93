import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  BookMetadata, 
  UserInputs, 
  UserChapter, 
  GeneratedChapter, 
  ChapterOutline,
  ContentItem,
  Citation
} from './types';
import { AI_MODELS, BOOK_SECTION_TYPES } from './constants';
import { BOOK_SECTION_PROMPTS } from './prompts';
import { BookMetadataForm } from './components/BookMetadataForm';
import { ChapterCard } from './components/ChapterCard';
import { BookGenerationPanel } from './components/BookGenerationPanel';
import { BookGenerationWorkflow } from './components/BookGenerationWorkflow';
import { GeminiBookGenerationWorkflow } from './components/GeminiBookGenerationWorkflow';
import { GeminiApiTest } from './components/GeminiApiTest';
import { BookGenerationErrorBoundary } from './components/BookGenerationErrorBoundary';
import { BookHistoryPanel } from './components/BookHistoryPanel';
import { AIModelSelector } from './components/AIModelSelector';
import { useBookContextStore } from './stores/book-context.store';
import bookAIService from './services/book-ai.service';
import { extractCitationsFromText } from '../paper-generator/citation-extraction.enhanced';
import { editorService } from '../paper-generator/editor.service';
import { bookHistoryService } from '@/services/bookHistoryService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Plus, BookOpen, ArrowLeft, ArrowRight, History } from "lucide-react";

export function AIBookGenerator() {
  const { user } = useAuth();

  // State for user inputs
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      subtitle: "",
      genre: "",
      targetAudience: "",
      keywords: [],
      authors: [],
      description: "",
      estimatedLength: "medium",
      tone: "professional",
      chapterCount: 15, // Default to 15 chapters for medium books
      outlineModel: "google/gemini-2.0-flash-001", // Recommended for outlines
      contentModel: "moonshotai/kimi-k2:free" // Recommended for content generation
    },
    userChapters: []
  });

  // Track generated chapters and sections
  const [generatedChapters, setGeneratedChapters] = useState<GeneratedChapter[]>([]);
  const [generatedSections, setGeneratedSections] = useState<GeneratedChapter[]>([]);

  // UI state
  const [currentStep, setCurrentStep] = useState<'metadata' | 'chapters' | 'generation' | 'auto-generation' | 'gemini-generation' | 'gemini-test' | 'completed' | 'history'>('metadata');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);
  const [isAutoGenerating, setIsAutoGenerating] = useState(false);

  // Book saving state
  const [currentBookId, setCurrentBookId] = useState<string | null>(null);
  const [isSavingBook, setIsSavingBook] = useState(false);

  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [chapterCitations, setChapterCitations] = useState<Record<string, string[]>>({});

  // Context store
  const {
    initializeBookContext,
    setGenerationQueue,
    startChapterGeneration,
    completeChapterGeneration,
    updateGenerationProgress,
    getContextForChapter,
    resetContext
  } = useBookContextStore();

  // Initialize generated sections based on book structure
  useEffect(() => {
    const sections = BOOK_SECTION_TYPES
      .filter(type => !type.isChapter)
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        title: type.name,
        description: type.description,
        status: 'pending' as const,
        icon: type.icon,
        order: type.order
      }));

    setGeneratedSections(sections);
  }, []);

  const handleMetadataChange = (metadata: BookMetadata) => {
    setUserInputs(prev => ({ ...prev, metadata }));
  };

  const handleNextFromMetadata = () => {
    setCurrentStep('chapters');
  };

  // Auto-generation handler
  const handleAutoGenerate = async (metadata: BookMetadata, chapters: UserChapter[]) => {
    try {
      setIsAutoGenerating(true);
      setCurrentStep('auto-generation');

      // Update the user inputs with the provided data
      setUserInputs({
        metadata,
        userChapters: chapters
      });

      toast.success("Starting auto-generation! Sit back and watch your book come to life.", { duration: 5000 });

    } catch (error) {
      console.error('Auto-generation setup failed:', error);
      toast.error("Failed to start auto-generation. Please try again.");
      setCurrentStep('metadata');
    } finally {
      setIsAutoGenerating(false);
    }
  };

  // Gemini generation handler
  const handleGeminiGenerate = async (metadata: BookMetadata, chapters: UserChapter[]) => {
    try {
      setCurrentStep('gemini-generation');

      // Update the user inputs with the provided data
      setUserInputs({
        metadata,
        userChapters: chapters
      });

      toast.success("Starting Google Gemini book generation!", { duration: 5000 });

    } catch (error) {
      console.error('Gemini generation setup failed:', error);
      toast.error("Failed to start Gemini generation. Please try again.");
      setCurrentStep('metadata');
    }
  };

  const addChapter = (count: number = 1) => {
    const currentCount = userInputs.userChapters.length;
    const newChapters: UserChapter[] = [];
    
    for (let i = 0; i < count; i++) {
      const chapterNumber = currentCount + i + 1;
      newChapters.push({
        id: `chapter-${Date.now()}-${i}`,
        title: `Chapter ${chapterNumber}`,
        outline: {
          id: `outline-${Date.now()}-${i}`,
          title: `Chapter ${chapterNumber}`,
          description: '',
          subSections: [],
          estimatedWordCount: 3000,
          order: chapterNumber
        },
        items: []
      });
    }

    setUserInputs(prev => ({
      ...prev,
      userChapters: [...prev.userChapters, ...newChapters]
    }));
  };

  const updateChapter = (chapterId: string, updates: Partial<UserChapter>) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  const removeChapter = (chapterId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userChapters: prev.userChapters.filter(chapter => chapter.id !== chapterId)
    }));
  };

  const moveChapter = (chapterId: string, direction: 'up' | 'down') => {
    const chapters = [...userInputs.userChapters];
    const index = chapters.findIndex(ch => ch.id === chapterId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= chapters.length) return;
    
    [chapters[index], chapters[newIndex]] = [chapters[newIndex], chapters[index]];
    
    // Update order values
    chapters.forEach((chapter, idx) => {
      chapter.outline.order = idx + 1;
    });
    
    setUserInputs(prev => ({ ...prev, userChapters: chapters }));
  };

  const handleStartGeneration = () => {
    if (userInputs.userChapters.length === 0) {
      toast.error("Please add at least one chapter before generating");
      return;
    }

    setCurrentStep('generation');
  };

  const handleGenerationComplete = async (chapters: GeneratedChapter[], citations: Citation[]) => {
    console.log('📚 Book generation completed with chapters:', chapters);
    console.log('📚 Total citations:', citations.length);
    console.log('📚 Received chapters detailed:', chapters.map(ch => ({
      id: ch.id,
      title: ch.title,
      status: ch.status,
      hasContent: !!ch.content,
      contentLength: ch.content?.length || 0,
      contentPreview: ch.content ? ch.content.substring(0, 100) + '...' : 'NO CONTENT RECEIVED'
    })));

    // Convert chapters to completed status
    const completedChapters = chapters.map(chapter => {
      // More accurate word count calculation
      let wordCount = 0;
      if (chapter.content) {
        // Remove markdown and count actual words
        const cleanContent = chapter.content
          .replace(/[#*`_~\[\]()]/g, '') // Remove markdown symbols
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();
        wordCount = cleanContent ? cleanContent.split(' ').filter(word => word.length > 0).length : 0;
      }

      console.log(`📚 Chapter ${chapter.title}: ${chapter.content?.length || 0} chars, ${wordCount} words`);

      return {
        ...chapter,
        status: 'completed' as const,
        wordCount: wordCount
      };
    });

    // Create chapter citations mapping
    const chapterCitationsMap: Record<string, string[]> = {};
    chapters.forEach(chapter => {
      if (chapter.citations && chapter.citations.length > 0) {
        chapterCitationsMap[chapter.id] = chapter.citations.map(c => c.id || `${c.authors[0]}-${c.year}`);
      }
    });

    console.log('📚 Completed chapters with word counts:', completedChapters.map(ch => ({
      id: ch.id,
      title: ch.title,
      wordCount: ch.wordCount,
      hasContent: !!ch.content
    })));

    setGeneratedChapters(completedChapters);
    setGeneratedSections(completedChapters); // Use the same data but it will be handled properly in the panel
    setAllCitations(citations);
    setChapterCitations(chapterCitationsMap);

    // Save book to database if user is authenticated
    if (user) {
      await saveBookToDatabase(completedChapters, citations);
    }

    setCurrentStep('completed');
    toast.success("Book generation completed successfully!");
  };

  // Save book to database
  const saveBookToDatabase = async (chapters: GeneratedChapter[], citations: Citation[]) => {
    if (!user) {
      console.log('User not authenticated, skipping book save');
      return;
    }

    setIsSavingBook(true);
    try {
      console.log('📚 Saving book to database...');

      // Create book record
      const { book, error: createError } = await bookHistoryService.createBook({
        metadata: userInputs.metadata,
        chapters,
        citations
      });

      if (createError || !book) {
        console.error('Error creating book:', createError);
        toast.error('Failed to save book to history');
        return;
      }

      console.log('📚 Book created with ID:', book.id);
      setCurrentBookId(book.id);

      // Save chapters
      const { success: chaptersSuccess, error: chaptersError } = await bookHistoryService.saveBookChapters(book.id, chapters);
      if (chaptersError) {
        console.error('Error saving chapters:', chaptersError);
        toast.error('Failed to save book chapters');
        return;
      }

      // Calculate total word count
      const totalWordCount = chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0);

      // Complete book generation
      const { success: completeSuccess, error: completeError } = await bookHistoryService.completeBookGeneration(book.id, totalWordCount);
      if (completeError) {
        console.error('Error completing book:', completeError);
        toast.error('Failed to complete book save');
        return;
      }

      console.log('📚 Book saved successfully to database');
      toast.success('Book saved to your library!');
    } catch (error) {
      console.error('Error saving book:', error);
      toast.error('Failed to save book to history');
    } finally {
      setIsSavingBook(false);
    }
  };





  const canProceedToGeneration = () => {
    return userInputs.metadata.title.trim() !== '' &&
           userInputs.metadata.genre !== '' &&
           userInputs.metadata.targetAudience !== '' &&
           userInputs.userChapters.length > 0;
  };

  const handleEditInEditor = () => {
    console.log('📝 Edit in Editor - Processing content...');
    console.log('📝 Generated sections:', generatedSections.length);
    console.log('📝 Generated chapters:', generatedChapters.length);

    // Use the same deduplication logic as the panel
    const contentMap = new Map();

    // Add sections first
    generatedSections.forEach(section => {
      if (section.content) {
        contentMap.set(section.id, section);
      }
    });

    // Add chapters (will overwrite sections with same ID, which is what we want)
    generatedChapters.forEach(chapter => {
      if (chapter.content) {
        contentMap.set(chapter.id, chapter);
      }
    });

    const allContent = Array.from(contentMap.values())
      .sort((a, b) => a.order - b.order);

    console.log('📝 Final content for editor:', allContent.map(item => ({
      id: item.id,
      title: item.title,
      hasContent: !!item.content,
      contentLength: item.content?.length || 0
    })));

    // Format content for HTML editor
    let htmlContent = '';

    // Title page
    htmlContent += `<h1 style="text-align: center; margin-bottom: 2rem;">${userInputs.metadata.title}</h1>`;
    if (userInputs.metadata.subtitle) {
      htmlContent += `<h2 style="text-align: center; margin-bottom: 1.5rem; color: #666;">${userInputs.metadata.subtitle}</h2>`;
    }
    if (userInputs.metadata.authors.length > 0) {
      htmlContent += `<p style="text-align: center; margin-bottom: 2rem;"><strong>Authors: <AUTHORS>
    }
    htmlContent += `<hr style="margin: 2rem 0;">`;

    // Main content
    allContent.forEach((item, index) => {
      // Chapter title
      htmlContent += `<h1 style="page-break-before: always; margin-top: 2rem; margin-bottom: 1.5rem;">Chapter ${index + 1}: ${item.title}</h1>`;

      // Format chapter content
      const formattedContent = formatContentForEditor(item.content || '');
      htmlContent += formattedContent;

      if (item.wordCount) {
        htmlContent += `<p style="font-style: italic; color: #666; margin-top: 1rem;"><em>Word count: ${item.wordCount.toLocaleString()}</em></p>`;
      }

      htmlContent += `<hr style="margin: 2rem 0;">`;
    });

    console.log('📝 Total book content length:', htmlContent.length);

    // Send to editor service and dispatch event for navigation
    editorService.sendToMainEditor({
      title: userInputs.metadata.title,
      content: htmlContent
    });

    // Dispatch custom event to trigger navigation to editor
    const event = new CustomEvent('navigateToEditor', {
      detail: { source: 'book-generator' }
    });
    window.dispatchEvent(event);

    toast.success("Book loaded in editor for further editing.");
  };

  // Handle navigation to book history
  const handleViewHistory = () => {
    setCurrentStep('history');
  };

  // Handle editing a book from history
  const handleEditBookFromHistory = async (bookId: string) => {
    try {
      const { book, error } = await bookHistoryService.getBookDetails(bookId);
      if (error || !book) {
        toast.error('Failed to load book details');
        return;
      }

      // Load book data into current state
      setUserInputs({
        metadata: {
          title: book.title,
          subtitle: book.subtitle || '',
          description: book.description || '',
          genre: book.genre,
          targetAudience: book.target_audience,
          authors: book.authors,
          keywords: book.keywords,
          estimatedLength: book.estimated_length as any,
          tone: book.tone as any,
          chapterCount: book.chapter_count,
          outlineModel: book.outline_model || '',
          contentModel: book.content_model || ''
        },
        userChapters: []
      });

      // Convert chapters to GeneratedChapter format
      if (book.chapters) {
        const chapters: GeneratedChapter[] = book.chapters.map(chapter => ({
          id: chapter.id,
          title: chapter.title,
          description: chapter.description || '',
          status: 'completed' as const,
          icon: BookOpen,
          content: chapter.content || '',
          wordCount: chapter.word_count,
          order: chapter.order_index,
          citations: Array.isArray(chapter.citations) ? chapter.citations : []
        }));

        setGeneratedChapters(chapters);
        setGeneratedSections(chapters);
      }

      setCurrentBookId(bookId);
      setCurrentStep('completed');
      toast.success('Book loaded successfully!');
    } catch (error) {
      console.error('Error loading book:', error);
      toast.error('Failed to load book');
    }
  };

  // Helper function to format content for the HTML editor
  const formatContentForEditor = (content: string): string => {
    if (!content) return '';

    // First, clean the content of markdown symbols
    let cleanedContent = content
      // Remove markdown headers but preserve the text
      .replace(/^#{1,6}\s+(.+)$/gm, '$1')
      // Remove bold markdown but preserve the text
      .replace(/\*\*(.*?)\*\*/g, '$1')
      // Remove italic markdown but preserve the text
      .replace(/\*(.*?)\*/g, '$1')
      // Remove code markdown but preserve the text
      .replace(/`(.*?)`/g, '$1')
      // Remove underline markdown
      .replace(/_{2,}(.*?)_{2,}/g, '$1')
      // Remove strikethrough markdown
      .replace(/~~(.*?)~~/g, '$1')
      // Remove links but keep text
      .replace(/\[(.*?)\]\(.*?\)/g, '$1')
      // Remove horizontal rules
      .replace(/^[-=]{3,}$/gm, '')
      // Remove bullet points and numbered lists formatting
      .replace(/^\s*[-*+]\s+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove blockquotes
      .replace(/^\s*>\s+/gm, '')
      // Clean up extra whitespace
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    // Split into paragraphs and process each one
    const paragraphs = cleanedContent.split('\n\n').filter(p => p.trim());
    let formattedContent = '';

    for (const paragraph of paragraphs) {
      const trimmed = paragraph.trim();
      if (!trimmed) continue;

      // Check if it looks like a heading
      if (isLikelyHeading(trimmed)) {
        const level = determineHeadingLevel(trimmed);
        formattedContent += `<h${level} style="margin-top: 1.5rem; margin-bottom: 1rem; font-weight: bold;">${trimmed}</h${level}>`;
      } else {
        // Regular paragraph - ensure proper formatting
        formattedContent += `<p style="margin-bottom: 1rem; line-height: 1.6; text-align: justify;">${trimmed}</p>`;
      }
    }

    return formattedContent;
  };

  // Helper function to check if text is likely a heading
  const isLikelyHeading = (text: string): boolean => {
    const trimmed = text.trim();

    // Too long to be a heading
    if (trimmed.length > 100) return false;

    // Must start with a capital letter
    if (!/^[A-Z]/.test(trimmed)) return false;

    // If it ends with a period and is long, probably not a heading
    if (trimmed.endsWith('.') && trimmed.length > 50) return false;

    // Common heading patterns
    const headingWords = [
      'introduction', 'overview', 'conclusion', 'summary', 'background',
      'methodology', 'results', 'discussion', 'analysis', 'implementation',
      'approach', 'framework', 'principles', 'concepts', 'applications',
      'benefits', 'challenges', 'future', 'recommendations', 'chapter',
      'section', 'part', 'definition', 'explanation', 'understanding',
      'exploring', 'examining', 'developing', 'creating', 'building'
    ];
    const lowerText = trimmed.toLowerCase();

    // Check for heading keywords
    if (headingWords.some(word => lowerText.includes(word))) return true;

    // Short text without period is likely a heading
    if (trimmed.length < 60 && !trimmed.endsWith('.')) return true;

    // Check for numbered sections (e.g., "1. Introduction", "Chapter 1")
    if (/^\d+\.?\s+[A-Z]/.test(trimmed)) return true;

    return false;
  };

  // Helper function to determine heading level
  const determineHeadingLevel = (text: string): number => {
    const trimmed = text.trim();

    // Main chapter headings (very short, likely chapter titles)
    if (trimmed.length < 30 && /^(chapter|part|section)\s+\d+/i.test(trimmed)) {
      return 2; // h2 for main chapters
    }

    // Major section headings (short, important topics)
    if (trimmed.length < 40) {
      return 3; // h3 for major sections
    }

    // Subsection headings (longer descriptive titles)
    if (trimmed.length < 70) {
      return 4; // h4 for subsections
    }

    // Default to h4 for other headings
    return 4;
  };

  // Render based on current step
  if (currentStep === 'gemini-test') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 py-12">
        <div className="max-w-4xl mx-auto px-6">
          <div className="mb-6">
            <Button onClick={() => setCurrentStep('metadata')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Book Generator
            </Button>
          </div>
          <GeminiApiTest />
        </div>
      </div>
    );
  }

  if (currentStep === 'history') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="mb-6">
            <Button
              onClick={() => setCurrentStep('metadata')}
              variant="outline"
              size="lg"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Generator
            </Button>
          </div>
          <BookHistoryPanel
            onEditBook={handleEditBookFromHistory}
            onCreateNew={() => setCurrentStep('metadata')}
          />
        </div>
      </div>
    );
  }

  if (currentStep === 'metadata') {
    return (
      <div className="relative">
        {/* History Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            onClick={handleViewHistory}
            variant="outline"
            size="sm"
          >
            <History className="h-4 w-4 mr-2" />
            Book History
          </Button>
        </div>

        <BookMetadataForm
          metadata={userInputs.metadata}
          onMetadataChange={handleMetadataChange}
          onNext={handleNextFromMetadata}
          onAutoGenerate={handleAutoGenerate}
          onGeminiGenerate={handleGeminiGenerate}
          onTestGemini={() => setCurrentStep('gemini-test')}
        />
      </div>
    );
  }

  if (currentStep === 'chapters') {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
            <BookOpen className="h-10 w-10 text-blue-600" />
            Define Your Chapters
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create detailed chapter outlines and provide any content you want the AI to incorporate.
          </p>
        </div>

        {/* AI Model Selector */}
        <AIModelSelector
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          models={AI_MODELS}
        />

        {/* Chapter Cards */}
        <div className="space-y-6">
          {userInputs.userChapters.map((chapter, index) => (
            <ChapterCard
              key={chapter.id}
              chapter={chapter}
              chapterIndex={index}
              onChapterUpdate={updateChapter}
              onChapterRemove={removeChapter}
              onMoveChapter={moveChapter}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
            />
          ))}

          {/* Add Chapter Buttons */}
          <div className="flex flex-col md:flex-row justify-center items-center gap-4">
            <div className="w-full md:w-auto flex flex-col gap-2 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-medium mb-2 text-center">Add Chapters</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button onClick={() => addChapter(1)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  1 Chapter
                </Button>
                
                <Button onClick={() => addChapter(3)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  3 Chapters
                </Button>
                
                <Button onClick={() => addChapter(5)} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" />
                  5 Chapters
                </Button>
              </div>
              
              <div className="mt-2">
                <Button 
                  onClick={() => {
                    const count = window.prompt("How many chapters do you want to add?", "1");
                    const numChapters = parseInt(count || "1", 10);
                    if (!isNaN(numChapters) && numChapters > 0 && numChapters <= 30) {
                      addChapter(numChapters);
                    } else if (numChapters > 30) {
                      toast.warning("Maximum 30 chapters allowed at once");
                    }
                  }} 
                  size="sm" 
                  variant="default"
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Custom Number of Chapters
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button 
            onClick={() => setCurrentStep('metadata')}
            variant="outline"
            size="lg"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Metadata
          </Button>
          
          <Button 
            onClick={handleStartGeneration}
            disabled={!canProceedToGeneration()}
            size="lg"
            className="px-8"
          >
            Generate Book
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    );
  }

  if (currentStep === 'auto-generation') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <BookGenerationErrorBoundary>
            <BookGenerationWorkflow
              bookMetadata={userInputs.metadata}
              userChapters={userInputs.userChapters}
              selectedModel={selectedModel}
              onComplete={handleGenerationComplete}
              onBack={() => setCurrentStep('metadata')}
              autoGenerate={true}
            />
          </BookGenerationErrorBoundary>
        </div>
      </div>
    );
  }

  if (currentStep === 'gemini-generation') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <BookGenerationErrorBoundary>
            <GeminiBookGenerationWorkflow
              bookMetadata={userInputs.metadata}
              userChapters={userInputs.userChapters}
              onComplete={handleGenerationComplete}
              onBack={() => setCurrentStep('metadata')}
            />
          </BookGenerationErrorBoundary>
        </div>
      </div>
    );
  }

  if (currentStep === 'generation') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <BookGenerationErrorBoundary>
            <BookGenerationWorkflow
              bookMetadata={userInputs.metadata}
              userChapters={userInputs.userChapters}
              selectedModel={selectedModel}
              onComplete={handleGenerationComplete}
              onBack={() => setCurrentStep('chapters')}
            />
          </BookGenerationErrorBoundary>
        </div>
      </div>
    );
  }

  if (currentStep === 'completed') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Book Generation Complete!
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Your book "{userInputs.metadata.title}" has been successfully generated with context-aware AI assistance.
            </p>
          </div>

          <BookGenerationPanel
            generatedChapters={generatedChapters}
            generatedSections={generatedSections}
            isGenerating={false}
            bookMetadata={userInputs.metadata}
            allCitations={allCitations}
            chapterCitations={chapterCitations}
            onEditInEditor={handleEditInEditor}
            bookId={currentBookId}
          />
        </div>
      </div>
    );
  }

  return null;
}
