import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Citation } from './types';
import { BookO<PERSON>, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ReferencesDisplayProps {
  referencesContent: string;
  citations: Citation[];
}

export function ReferencesDisplay({ referencesContent, citations }: ReferencesDisplayProps) {
  // Count references with actual content
  const citationsWithReferences = citations.filter(c => 
    c.referenceText && c.referenceText.trim() !== '' && 
    !c.referenceText.includes("This is a placeholder reference")
  ).length;
  
  // Check for error messages or default references
  const isErrorMessage = referencesContent.includes("No references could be") || 
                         referencesContent.includes("Cannot generate references");
  
  // Check for default fake references that we don't want to show
  const hasFakeReferences = 
    referencesContent.includes("<PERSON>, J<PERSON> (2023). Artificial intelligence in research paper generation") ||
    referencesContent.includes("<PERSON>, <PERSON> (2024). Neural networks for academic writing") ||
    referencesContent.includes("<PERSON>, <PERSON>, <PERSON>, L. <PERSON>., & <PERSON>, P. (2022). Automated reference");
  
  const hasPlaceholderReferences = citations.some(c => 
    c.referenceText && c.referenceText.includes("This is a placeholder reference")
  );
  
  const hasMissingReferences = citations.length > 0 && citationsWithReferences < citations.length;
  
  // Parse references into individual entries for better display
  const referenceLines = referencesContent
    .split('\n')
    .filter(line => line.trim() !== '')
    .map(line => line.trim());
  
  return (
    <Card className="w-full shadow-md border bg-white">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          References
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Show warnings for issues */}
        {isErrorMessage ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Finding References</AlertTitle>
            <AlertDescription>
              {referencesContent}
            </AlertDescription>
          </Alert>
        ) : hasFakeReferences ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>System is showing default/fake references</AlertTitle>
            <AlertDescription>
              <p className="mt-2">
                The system has generated placeholder references instead of actual ones. This typically happens when:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>The AI didn't generate proper references for citations</li>
                <li>The citation extraction failed to match citations with references</li>
                <li>The citations don't follow the expected format</li>
              </ul>
              <p className="mt-2">
                Try regenerating the paper with AI models that include proper references.
              </p>
            </AlertDescription>
          </Alert>
        ) : hasMissingReferences && citations.length > 0 ? (
          <Alert className="mb-4 bg-amber-50 border-amber-200">
            <AlertCircle className="h-4 w-4 text-amber-600" />
            <AlertTitle>Incomplete References</AlertTitle>
            <AlertDescription>
              {citationsWithReferences === 0 ? (
                "No full references were found for the citations in this paper. Try regenerating the paper sections."
              ) : (
                `Only ${citationsWithReferences} out of ${citations.length} citations have complete references. 
                Try regenerating the paper sections to improve reference matching.`
              )}
            </AlertDescription>
          </Alert>
        ) : hasPlaceholderReferences ? (
          <Alert className="mb-4 bg-blue-50 border-blue-200">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertTitle>Placeholder References</AlertTitle>
            <AlertDescription>
              Some references are placeholders. Please regenerate the paper for complete references.
            </AlertDescription>
          </Alert>
        ) : null}

        {/* Show references or a message if none */}
        {referenceLines.length === 0 || referenceLines[0].includes("No citations were found") ? (
          <div className="text-center py-8 text-gray-500">
            No references were found in this paper.
          </div>
        ) : (
          <div className="space-y-1 text-sm">
            {referenceLines.map((line, index) => {
              // Skip the "References" header and statistics lines
              if (line.toLowerCase() === 'references' || 
                  line.includes('Citation Statistics') || 
                  line.includes('Total Citations:')) {
                return null;
              }
              
              return (
                <div key={index} className={`${line.match(/^\d+\./) ? 'pl-4 -indent-4 mb-3' : ''}`}>
                  {line}
                </div>
              );
            })}
          </div>
        )}
            
        {citations.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
              <BookOpen className="h-4 w-4" />
              <span>Citation Statistics</span>
            </div>
            <div className="text-xs text-gray-600">
              Total Citations: {citations.length} | 
              Citations with References: {citations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} |
              Unique Authors: <AUTHORS>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
