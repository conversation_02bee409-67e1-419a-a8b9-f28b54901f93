/**
 * Research Search Interface
 * Main component for the research search chat functionality
 */

import React, { useState, useEffect, useRef } from 'react';
import { Settings, Loader2, AlertCircle, RefreshCw, History, BookOpen, PanelLeft, PanelRight, Menu, X, Quote, FileText, Calendar, Star, ExternalLink, Search, Brain, GraduationCap, Sparkles, Wand2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

import { SearchMessage } from './SearchMessage';
import { SearchInput } from './SearchInput';
import { SearchHistory } from './SearchHistory';
import { AIModelSelector } from './AIModelSelector';
import { ServiceStatus } from './ServiceStatus';
import { DeepResearchMessage } from './DeepResearchMessage';
import { ResearchTypeSelector } from './ResearchTypeSelector';
import { PromptEnhancementDialog } from './PromptEnhancementDialog';

import {
  SearchSession,
  SearchMessage as SearchMessageType,
  SearchProgress,
  UserPreferences,
  DeepResearchSession,
  DeepResearchOutline,
  DeepResearchProgress,
  DeepResearchOptions,
  OutlineEditRequest,
  ResearchType,
  ResearchTypeTemplate
} from '../types';

import { searchHistoryService } from '../services/search-history.service';
import { tavilySearchService } from '../services/tavily-search.service';
import { researchAIService } from '../services/research-ai.service';
import { deepResearchService } from '../services/deep-research.service';
import { enhancedDeepResearchService } from '../services/enhanced-deep-research.service';
import { enhancedDatabaseService } from '../services/enhanced-database.service';
import { enhancedReferenceManagementService } from '../services/enhanced-reference-management.service';
import { enhancedAcademicWritingService } from '../services/enhanced-academic-writing.service';
import { academicFormattingService } from '../services/academic-formatting.service';
import { researchFlowService, ResearchFlowContext } from '../services/research-flow.service';
import ResearchFlowProgress from './ResearchFlowProgress';
import { promptEnhancementService, EnhancedPrompt } from '../services/prompt-enhancement.service';
import { researchPlanningService } from '../services/research-planning.service';
import { completeArticleGenerationService, CompleteArticle, GenerationProgress } from '../services/complete-article-generation.service';
import { articleExportService } from '../services/article-export.service';
import { CompleteArticleDisplay } from './CompleteArticleDisplay';
import { SavedArticlesDialog } from './SavedArticlesDialog';
import { cn } from '@/lib/utils';

export function ResearchSearchInterface() {
  const [currentSession, setCurrentSession] = useState<SearchSession | null>(null);
  const [messages, setMessages] = useState<SearchMessageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchProgress, setSearchProgress] = useState<SearchProgress | null>(null);
  const [selectedModel, setSelectedModel] = useState('google/gemini-2.0-flash-001');
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showReferences, setShowReferences] = useState(true);

  // Deep Research State
  const [isDeepResearchLoading, setIsDeepResearchLoading] = useState(false);
  const [currentDeepResearchSession, setCurrentDeepResearchSession] = useState<DeepResearchSession | null>(null);

  // Enhanced Research State
  const [isEnhancedResearchLoading, setIsEnhancedResearchLoading] = useState(false);
  const [enhancedResearchMode, setEnhancedResearchMode] = useState(false);
  const [showWordExportOptions, setShowWordExportOptions] = useState(false);

  // Research Flow State (Always enabled for better references)
  const [researchFlowContext, setResearchFlowContext] = useState<ResearchFlowContext | null>(null);
  const [showFlowProgress, setShowFlowProgress] = useState(true);
  const [deepResearchProgress, setDeepResearchProgress] = useState<DeepResearchProgress | null>(null);

  // Research Type Selection State
  const [showResearchTypeSelector, setShowResearchTypeSelector] = useState(false);
  const [pendingResearchQuery, setPendingResearchQuery] = useState<string>('');

  // Prompt Enhancement State
  const [showPromptEnhancement, setShowPromptEnhancement] = useState(false);
  const [availableResearchTypes, setAvailableResearchTypes] = useState<ResearchTypeTemplate[]>([]);

  // Complete Article Generation State
  const [generatedArticle, setGeneratedArticle] = useState<CompleteArticle | null>(null);
  const [isGeneratingArticle, setIsGeneratingArticle] = useState(false);
  const [articleGenerationProgress, setArticleGenerationProgress] = useState<GenerationProgress | null>(null);

  // Saved Articles State
  const [showSavedArticles, setShowSavedArticles] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initializeInterface();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const initializeInterface = async () => {
    try {
      setError(null);

      // Load available research types
      const researchTypes = researchPlanningService.getResearchTypeTemplates();
      setAvailableResearchTypes(researchTypes);

      // Load user preferences with fallback
      try {
        const userPrefs = await searchHistoryService.getUserPreferences();
        setPreferences(userPrefs);
        setSelectedModel(userPrefs.defaultModel);
      } catch (prefError) {
        console.warn('Failed to load preferences, using defaults:', prefError);
        const defaultPrefs = {
          defaultModel: 'google/gemini-2.0-flash-001',
          defaultSearchDepth: 'advanced' as const,
          maxResults: 10,
          autoSave: true,
          showSources: true,
          showCitations: true,
          theme: 'auto' as const
        };
        setPreferences(defaultPrefs);
        setSelectedModel(defaultPrefs.defaultModel);
      }

      // Try to load active session or create new one
      try {
        const sessions = await searchHistoryService.getUserSessions();
        const activeSession = sessions.find(s => s.isActive);

        if (activeSession) {
          await loadSession(activeSession.id);
        } else {
          await createNewSession();
        }
      } catch (sessionError) {
        console.warn('Failed to load sessions, creating new session:', sessionError);
        await createNewSession();
      }
    } catch (error) {
      console.error('Error initializing interface:', error);
      setError('Failed to initialize research search interface. Please refresh the page.');
    }
  };

  const createNewSession = async () => {
    try {
      setError(null);
      const session = await searchHistoryService.createSession(
        'New Research Session',
        selectedModel
      );
      setCurrentSession(session);
      setMessages([]);
      console.log('New session created successfully:', session.id);
    } catch (error) {
      console.error('Error creating new session:', error);
      // Create a temporary session for offline use
      const tempSession = {
        id: 'temp-' + Date.now(),
        title: 'New Research Session',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'temp-user',
        model: selectedModel,
        totalQueries: 0,
        isActive: true
      };
      setCurrentSession(tempSession);
      setMessages([]);
      setError('Working in offline mode. Sessions will not be saved.');
    }
  };

  const loadSession = async (sessionId: string) => {
    try {
      const session = await searchHistoryService.getSession(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages);
        setSelectedModel(session.model);
        await searchHistoryService.setActiveSession(sessionId);
        setError(null);
      }
    } catch (error) {
      console.error('Error loading session:', error);
      setError('Failed to load search session');
    }
  };

  const handleStepByStepSearch = async (query: string) => {
    if (!currentSession || !query.trim() || isLoading) return;

    setIsLoading(true);
    setError(null);
    setShowFlowProgress(true);

    try {
      // Initialize research flow
      const context = researchFlowService.initializeFlow(query, {
        maxResults: preferences?.maxResults || 10,
        searchDepth: preferences?.searchDepth || 'advanced',
        citationStyle: 'apa'
      });

      setResearchFlowContext(context);

      // Add user message
      const userMessage: SearchMessageType = {
        id: Date.now().toString(),
        type: 'user',
        content: query,
        timestamp: new Date(),
        searchQuery: query
      };

      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);

      // Save user message to history
      try {
        await searchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.error('Database error, continuing with search:', dbError);
      }

      // Execute research flow with progress updates
      const finalContext = await researchFlowService.executeFlow(
        context,
        (updatedContext) => {
          setResearchFlowContext({ ...updatedContext });
        }
      );

      // Create assistant message with final result
      const assistantMessage: SearchMessageType = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: finalContext.organizedContent || finalContext.rawContent || 'Research completed',
        timestamp: new Date(),
        searchQuery: query,
        sources: finalContext.searchResults?.results || [],
        citations: finalContext.citations || []
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      setMessages(finalMessages);

      // Save assistant message to history
      try {
        await searchHistoryService.addMessage(currentSession.id, assistantMessage);
      } catch (dbError) {
        console.error('Database error saving assistant message:', dbError);
      }

    } catch (error) {
      console.error('Step-by-step search error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred during research');

      const errorMessage: SearchMessageType = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `I encountered an error while researching: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        searchQuery: query
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setShowFlowProgress(false);
    }
  };

  const handleSearch = async (query: string) => {
    // Use enhanced workflow for research, but keep original for simple queries
    if (query.toLowerCase().includes('outline') ||
        query.toLowerCase().includes('plan') ||
        query.toLowerCase().includes('structure') ||
        query.length < 50) {
      // Use original method for outline generation and simple queries
      return handleLegacySearch(query);
    }

    // Use enhanced step-by-step workflow for comprehensive research
    return handleStepByStepSearch(query);
  };

  // Legacy search method (kept for compatibility but not used)
  const handleLegacySearch = async (query: string) => {
    if (!currentSession || isLoading) return;

    setIsLoading(true);
    setError(null);
    
    // Add user message
    const userMessage: SearchMessageType = {
      id: Date.now().toString(),
      type: 'user',
      content: query,
      timestamp: new Date(),
      searchQuery: query
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      // Save user message to database (with fallback)
      try {
        await searchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.warn('Database error, continuing with search:', dbError);
        // Continue with search even if database save fails
      }

      // Update search progress
      setSearchProgress({ stage: 'searching', message: 'Searching academic sources...', progress: 25 });

      // Perform Tavily search
      const searchResults = await tavilySearchService.searchAcademic(query, {
        maxResults: preferences?.maxResults || 10,
        searchDepth: preferences?.defaultSearchDepth || 'advanced'
      });

      setSearchProgress({ stage: 'analyzing', message: 'Analyzing search results...', progress: 50 });

      // Generate AI response
      const { response, citations } = await researchAIService.generateResponse(
        query,
        searchResults,
        selectedModel,
        messages.slice(-5) // Last 5 messages for context
      );

      setSearchProgress({ stage: 'generating', message: 'Generating comprehensive response...', progress: 75 });

      // Create assistant message
      const assistantMessage: SearchMessageType = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        sources: tavilySearchService.transformToSearchSources(searchResults.results),
        citations
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Save assistant message to database (with fallback)
      try {
        await searchHistoryService.addMessage(currentSession.id, assistantMessage);
      } catch (dbError) {
        console.warn('Database error saving assistant message:', dbError);
        // Continue even if database save fails
      }

      setSearchProgress({ stage: 'complete', message: 'Response generated successfully', progress: 100 });

      // Update session title if it's the first query
      if (messages.length === 0) {
        const title = query.length > 50 ? query.substring(0, 50) + '...' : query;
        try {
          await searchHistoryService.updateSessionTitle(currentSession.id, title);
          setCurrentSession(prev => prev ? { ...prev, title } : null);
        } catch (dbError) {
          console.warn('Database error updating session title:', dbError);
          // Update local state even if database update fails
          setCurrentSession(prev => prev ? { ...prev, title } : null);
        }
      }

    } catch (error: any) {
      console.error('Search error:', error);

      let errorMessage = 'I apologize, but I encountered an error while searching. Please try again or rephrase your question.';

      // Provide more specific error messages
      if (error.message?.includes('Failed to add message')) {
        errorMessage = 'I encountered a database error while saving your message. Please try again.';
      } else if (error.message?.includes('Failed to search')) {
        errorMessage = 'I encountered an error while searching for academic sources. Please check your internet connection and try again.';
      } else if (error.message?.includes('Failed to generate response')) {
        errorMessage = 'I encountered an error while generating the AI response. Please try again with a different query.';
      }

      setError(error.message || 'Failed to perform search');

      // Add error message
      const errorMsg: SearchMessageType = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: errorMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
      setSearchProgress(null);
    }
  };

  const handleDeepResearch = async (query: string) => {
    if (!currentSession || isDeepResearchLoading || isLoading) return;

    // Check if enhanced research is available and should be used
    if (enhancedDeepResearchService.isConfigured() &&
        (query.toLowerCase().includes('academic') ||
         query.toLowerCase().includes('literature review') ||
         query.toLowerCase().includes('comprehensive') ||
         query.length > 100)) {
      setEnhancedResearchMode(true);
    }

    // Store the query and show prompt enhancement dialog
    setPendingResearchQuery(query);
    setShowPromptEnhancement(true);
  };

  const handlePromptEnhancementAccept = (enhancedPrompt: EnhancedPrompt, selectedResearchType: ResearchTypeTemplate) => {
    setShowPromptEnhancement(false);

    // Update the pending query with the enhanced prompt
    setPendingResearchQuery(enhancedPrompt.enhancedPrompt);

    // Proceed with research using the selected type
    handleResearchTypeSelected(selectedResearchType.config);
  };

  const handlePromptEnhancementSkip = () => {
    setShowPromptEnhancement(false);
    // Show research type selector with original prompt
    setShowResearchTypeSelector(true);
  };

  const handleGenerateCompleteArticle = async () => {
    if (!currentSession || !currentDeepResearchSession?.outline) {
      toast.error('No research session available for article generation');
      return;
    }

    setIsGeneratingArticle(true);
    setArticleGenerationProgress(null);

    try {
      const article = await enhancedDeepResearchService.generateCompleteArticle(
        currentDeepResearchSession as any,
        {
          citationStyle: 'apa',
          includeAbstract: true,
          includeConclusion: true,
          minWordCount: 5000,
          maxWordCount: 15000,
          academicLevel: 'graduate'
        },
        (progress) => {
          setArticleGenerationProgress(progress);
        }
      );

      setGeneratedArticle(article);
      toast.success('Complete article generated successfully!');
    } catch (error) {
      console.error('Error generating complete article:', error);
      toast.error('Failed to generate complete article. Please try again.');
    } finally {
      setIsGeneratingArticle(false);
      setArticleGenerationProgress(null);
    }
  };

  const handleExportArticle = async (format: 'pdf' | 'docx' | 'txt') => {
    if (!generatedArticle) return;

    try {
      const blob = await articleExportService.exportArticle(generatedArticle, {
        format: format,
        includeMetadata: true,
        citationStyle: 'apa',
        fontSize: 12,
        lineSpacing: 1.5,
        margins: { top: 1, bottom: 1, left: 1, right: 1 }
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${generatedArticle.title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success(`Article exported as ${format.toUpperCase()} with proper academic formatting`);
    } catch (error) {
      console.error(`Error exporting article as ${format}:`, error);
      toast.error(`Failed to export article as ${format.toUpperCase()}`);
    }
  };

  const handleSaveArticle = async () => {
    if (!generatedArticle || !currentSession) return;

    try {
      const articleId = await articleExportService.saveArticle(
        generatedArticle,
        currentSession.userId || 'anonymous'
      );

      toast.success('Article saved successfully! You can access it from your saved articles.');
      console.log('Article saved with ID:', articleId);
    } catch (error) {
      console.error('Error saving article:', error);
      toast.error('Failed to save article. Please try again.');
    }
  };

  const handleResearchTypeSelected = async (researchType: ResearchType) => {
    setShowResearchTypeSelector(false);

    if (!currentSession || !pendingResearchQuery) return;

    // Use enhanced research if enabled
    if (enhancedResearchMode) {
      return handleEnhancedResearchExecution(researchType);
    }

    setIsDeepResearchLoading(true);
    setError(null);

    // Add user message
    const userMessage: SearchMessageType = {
      id: Date.now().toString(),
      type: 'user',
      content: pendingResearchQuery,
      timestamp: new Date(),
      searchQuery: pendingResearchQuery,
      isDeepResearch: true
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      // Save user message to database
      try {
        await searchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.warn('Database error, continuing with deep research:', dbError);
      }

      // Generate intelligent outline based on research type
      const outline = await deepResearchService.generateIntelligentOutline(
        pendingResearchQuery,
        researchType,
        selectedModel
      );

      // Create outline message
      const outlineMessage: SearchMessageType = {
        id: (Date.now() + 1).toString(),
        type: 'deep_research_outline',
        content: `${researchType.name} outline generated for: "${pendingResearchQuery}"`,
        timestamp: new Date(),
        deepResearchOutline: outline,
        isDeepResearch: true
      };

      setMessages(prev => [...prev, outlineMessage]);

      // Save outline message
      try {
        await searchHistoryService.addMessage(currentSession.id, outlineMessage);
      } catch (dbError) {
        console.warn('Database error saving outline message:', dbError);
      }

      // Update session title if it's the first query
      if (messages.length === 0) {
        const title = `${researchType.name}: ${pendingResearchQuery.length > 30 ? pendingResearchQuery.substring(0, 30) + '...' : pendingResearchQuery}`;
        try {
          await searchHistoryService.updateSessionTitle(currentSession.id, title);
          setCurrentSession(prev => prev ? { ...prev, title } : null);
        } catch (dbError) {
          console.warn('Database error updating session title:', dbError);
          setCurrentSession(prev => prev ? { ...prev, title } : null);
        }
      }

    } catch (error: any) {
      console.error('Deep research error:', error);

      const errorMessage = 'I encountered an error while generating the research outline. Please try again or rephrase your research question.';
      setError(error.message || 'Failed to start deep research');

      const errorMsg: SearchMessageType = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: errorMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsDeepResearchLoading(false);
      setPendingResearchQuery('');
    }
  };

  const handleResearchTypeCancelled = () => {
    setShowResearchTypeSelector(false);
    setPendingResearchQuery('');
    setEnhancedResearchMode(false);
  };

  const handleEnhancedResearchExecution = async (researchType: ResearchType) => {
    if (!currentSession || !pendingResearchQuery) return;

    setIsEnhancedResearchLoading(true);
    setError(null);

    // Add user message
    const userMessage: SearchMessageType = {
      id: Date.now().toString(),
      type: 'user',
      content: pendingResearchQuery,
      timestamp: new Date(),
      searchQuery: pendingResearchQuery,
      isDeepResearch: true
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      // Save user message to database
      try {
        await searchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.warn('Database error, continuing with enhanced research:', dbError);
      }

      // Clear previous references
      enhancedReferenceManagementService.clear();

      // Execute enhanced deep research
      const options: DeepResearchOptions = {
        model: selectedModel,
        researchType: researchType,
        maxResultsPerSection: 10,
        searchDepth: 'comprehensive',
        includeImages: false,
        citationStyle: 'apa',
        academicFocus: true,
        minWordCountPerSection: 1200,
        maxWordCountPerSection: 2000,
        minCitationsPerSection: 20,
        maxCitationsPerSection: 30,
        requireInTextCitations: true,
        generateConsolidatedReferences: true
      };

      const enhancedSession = await enhancedDeepResearchService.executeEnhancedDeepResearch(
        pendingResearchQuery,
        researchType,
        options,
        (progress) => {
          setDeepResearchProgress(progress);

          // Create or update progress message
          const progressMessage: SearchMessageType = {
            id: `progress_${Date.now()}`,
            type: 'deep_research_progress',
            content: `Enhanced Research Progress: ${progress.message}`,
            timestamp: new Date(),
            deepResearchProgress: progress,
            isDeepResearch: true
          };

          setMessages(prev => {
            const existingProgressIndex = prev.findIndex(m => m.type === 'deep_research_progress');
            if (existingProgressIndex >= 0) {
              const updated = [...prev];
              updated[existingProgressIndex] = progressMessage;
              return updated;
            } else {
              return [...prev, progressMessage];
            }
          });
        }
      );

      // Format the final document
      const formattedDocument = academicFormattingService.formatResearchSessionForAcademicOutput(enhancedSession);

      // Create final report message
      const finalMessage: SearchMessageType = {
        id: (Date.now() + 2).toString(),
        type: 'deep_research_final',
        content: formattedDocument.content,
        timestamp: new Date(),
        deepResearchSession: enhancedSession,
        isDeepResearch: true
      };

      setMessages(prev => [...prev, finalMessage]);

      // Save final message
      try {
        await searchHistoryService.addMessage(currentSession.id, finalMessage);
      } catch (dbError) {
        console.warn('Database error saving final message:', dbError);
      }

      // Show Word export options
      setShowWordExportOptions(true);

      toast.success(`Enhanced research completed with ${enhancedSession.enhancedMetadata.citationsExtracted} citations and ${enhancedSession.enhancedMetadata.referencesFormatted} references!`);

    } catch (error: any) {
      console.error('Enhanced research execution error:', error);

      const errorMessage = 'I encountered an error during enhanced research execution. Please try again.';
      setError(error.message || 'Failed to execute enhanced research');

      const errorMsg: SearchMessageType = {
        id: (Date.now() + 3).toString(),
        type: 'assistant',
        content: errorMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsEnhancedResearchLoading(false);
      setEnhancedResearchMode(false);
      setDeepResearchProgress(null);
      setPendingResearchQuery('');
    }
  };

  const handleWordExport = (content: string, title: string) => {
    try {
      // Format content for Word export
      const wordContent = academicFormattingService.formatForWordExport(content);

      // Create blob and download
      const blob = new Blob([wordContent], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Document exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export document');
    }
  };

  const handleApproveOutline = async (outline: DeepResearchOutline) => {
    if (!currentSession || isDeepResearchLoading) return;

    setIsDeepResearchLoading(true);

    try {
      // Create deep research session
      const session: DeepResearchSession = {
        id: `deep_research_${Date.now()}`,
        title: outline.title,
        originalQuery: messages.find(m => m.isDeepResearch && m.type === 'user')?.content || '',
        outline,
        researchType: outline.researchType,
        currentPoint: 0,
        totalPoints: outline.totalPoints,
        status: 'outline_approved',
        aiAssistants: [],
        completedPoints: [],
        allReferences: [],
        researchMetadata: {
          totalWordCount: 0,
          totalCitations: 0,
          academicSources: 0,
          governmentSources: 0,
          averageSourceQuality: 0,
          researchDuration: 0,
          sectionsCompleted: 0,
          keyTopics: [],
          confidenceScore: 0
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'current_user', // TODO: Get from auth
        model: selectedModel
      };

      setCurrentDeepResearchSession(session);

      // Create progress message
      const progressMessage: SearchMessageType = {
        id: (Date.now() + 1).toString(),
        type: 'deep_research_progress',
        content: 'Starting deep research execution...',
        timestamp: new Date(),
        deepResearchSession: session,
        isDeepResearch: true
      };

      setMessages(prev => [...prev, progressMessage]);

      // Execute deep research with enhanced options
      const options: DeepResearchOptions = {
        model: selectedModel,
        researchType: outline.researchType,
        maxResultsPerSection: 8,
        searchDepth: 'comprehensive',
        includeImages: false,
        academicFocus: true,
        governmentSourcesOnly: false,
        peerReviewedOnly: false,
        minWordCountPerSection: outline.researchType.minWordsPerSection,
        maxWordCountPerSection: outline.researchType.maxWordsPerSection,
        citationStyle: 'apa',
        includeAbstract: true,
        includeConclusion: true,
        includeExecutiveSummary: outline.researchType.totalWordTarget > 10000,
        autoApproveOutline: false,
        sourceQualityThreshold: 0.6,
        maxCitationsPerSection: outline.researchType.citationDensity === 'heavy' ? 15 : outline.researchType.citationDensity === 'moderate' ? 10 : 5,
        requireInTextCitations: true,
        generateConsolidatedReferences: true
      };

      const completedSession = await deepResearchService.executeDeepResearch(
        session,
        options,
        (progress) => {
          setDeepResearchProgress(progress);
          // Update the progress message with current session state
          setMessages(prev => prev.map(msg =>
            msg.type === 'deep_research_progress' && msg.deepResearchSession?.id === session.id
              ? { ...msg, deepResearchProgress: progress, deepResearchSession: { ...session, currentPoint: progress.pointNumber } }
              : msg
          ));
        }
      );

      setCurrentDeepResearchSession(completedSession);

      // Create final report message
      const finalMessage: SearchMessageType = {
        id: (Date.now() + 2).toString(),
        type: 'deep_research_final',
        content: completedSession.finalReport || 'Deep research completed successfully.',
        timestamp: new Date(),
        deepResearchSession: completedSession,
        isDeepResearch: true
      };

      setMessages(prev => [...prev, finalMessage]);

      // Save final message
      try {
        await searchHistoryService.addMessage(currentSession.id, finalMessage);
      } catch (dbError) {
        console.warn('Database error saving final message:', dbError);
      }

    } catch (error: any) {
      console.error('Deep research execution error:', error);

      const errorMessage = 'I encountered an error during deep research execution. Please try again.';
      setError(error.message || 'Failed to execute deep research');

      const errorMsg: SearchMessageType = {
        id: (Date.now() + 3).toString(),
        type: 'assistant',
        content: errorMessage,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsDeepResearchLoading(false);
      setDeepResearchProgress(null);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleModelChange = async (modelId: string) => {
    setSelectedModel(modelId);
    
    // Update preferences
    if (preferences) {
      const updatedPrefs = { ...preferences, defaultModel: modelId };
      setPreferences(updatedPrefs);
      await searchHistoryService.updateUserPreferences(updatedPrefs);
    }
  };

  const retryLastSearch = () => {
    const lastUserMessage = messages.filter(m => m.type === 'user').pop();
    if (lastUserMessage?.searchQuery) {
      handleSearch(lastUserMessage.searchQuery);
    }
  };

  // Get current sources and citations for references sidebar
  const currentSources = messages
    .filter(m => m.type === 'assistant' && m.sources)
    .flatMap(m => m.sources || []);

  const currentCitations = messages
    .filter(m => m.type === 'assistant' && m.citations)
    .flatMap(m => m.citations || []);

  return (
    <div className="h-screen flex bg-gradient-to-br from-gray-50 via-white to-blue-50/30 relative">
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white/50" />
      {/* Modern History Sidebar */}
      <div className={cn(
        "fixed left-0 top-0 h-full z-30 transition-all duration-500 ease-out",
        showHistory ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="w-80 h-full bg-white/98 backdrop-blur-xl border-r border-gray-200/40 shadow-2xl">
          {/* Sidebar Header */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
                  <History className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-gray-900">Research History</h2>
                  <p className="text-xs text-gray-500">Your research sessions</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHistory(false)}
                className="h-8 w-8 p-0 rounded-full hover:bg-gray-100 transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </Button>
            </div>
          </div>

          {/* History Content */}
          <div className="flex-1 overflow-hidden">
            <SearchHistory
              currentSessionId={currentSession?.id}
              onSessionSelect={loadSession}
              onNewSession={createNewSession}
            />
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {showHistory && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-20 lg:hidden"
          onClick={() => setShowHistory(false)}
        />
      )}

      {/* Main Academic Interface */}
      <div className={cn(
        "flex-1 flex flex-col bg-white transition-all duration-300 overflow-hidden",
        showHistory ? "lg:ml-80" : "ml-0"
      )}>
        {/* Modern Header */}
        <div className="border-b border-gray-200/30 bg-white/80 backdrop-blur-2xl px-8 py-6 relative">
          {/* Subtle Header Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/20 to-indigo-50/20" />

          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Logo/Icon */}
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg">
                <BookOpen className="w-6 h-6 text-white" />
              </div>

              <div className="space-y-1">
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentSession?.title || 'Research Assistant'}
                </h1>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                  <span>AI-powered academic research with real-time citations</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Modern Toggle Buttons */}
              <div className="flex items-center bg-gray-50/80 rounded-2xl p-1 border border-gray-200/50">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowHistory(!showHistory)}
                  className={cn(
                    "h-8 px-3 rounded-xl transition-all duration-200 text-sm font-medium",
                    showHistory
                      ? "bg-white shadow-sm text-blue-700 border border-blue-200/50"
                      : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
                  )}
                >
                  <History className="w-4 h-4 mr-2" />
                  History
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReferences(!showReferences)}
                  className={cn(
                    "h-8 px-3 rounded-xl transition-all duration-200 text-sm font-medium",
                    showReferences
                      ? "bg-white shadow-sm text-emerald-700 border border-emerald-200/50"
                      : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
                  )}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  References
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSavedArticles(true)}
                  className="h-8 px-3 rounded-xl transition-all duration-200 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-white/50"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Saved Articles
                </Button>
              </div>

              <div className="h-6 w-px bg-gray-200" />

              <ServiceStatus compact />

              <AIModelSelector
                selectedModel={selectedModel}
                onModelChange={handleModelChange}
                compact
              />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className="h-9 px-4 rounded-xl text-gray-600 hover:text-gray-900 hover:bg-gray-100/80 transition-all duration-200"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Compact Search Progress */}
          {searchProgress && (
            <div className="mt-3 mx-6 p-3 bg-white/95 backdrop-blur-sm rounded-xl border border-gray-200/40 shadow-sm">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                  <Loader2 className="w-3 h-3 animate-spin text-white" />
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{searchProgress.message}</div>
                </div>
                <div className="text-sm font-medium text-blue-600">{searchProgress.progress}%</div>
              </div>
              <div className="mt-2 w-full bg-gray-200/50 rounded-full h-1 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 h-1 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${searchProgress.progress}%` }}
                />
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="mt-4 border-red-200/60 bg-gradient-to-r from-red-50 to-pink-50 shadow-sm">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <AlertDescription className="text-red-800 font-medium">
                <div className="flex items-center justify-between">
                  <span>{error}</span>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={retryLastSearch}
                      className="h-7 text-xs px-3 rounded-full border-red-300 text-red-700 hover:bg-red-100"
                    >
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Retry
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setError(null)}
                      className="h-7 text-xs px-3 rounded-full text-red-600 hover:bg-red-100"
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Prompt Enhancement Dialog */}
        <PromptEnhancementDialog
          isOpen={showPromptEnhancement}
          onClose={() => setShowPromptEnhancement(false)}
          originalPrompt={pendingResearchQuery}
          availableResearchTypes={availableResearchTypes}
          selectedModel={selectedModel}
          onEnhancedPromptAccept={handlePromptEnhancementAccept}
        />

        {/* Research Type Selector Modal */}
        {showResearchTypeSelector && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <ResearchTypeSelector
                  query={pendingResearchQuery}
                  selectedModel={selectedModel}
                  onTypeSelect={handleResearchTypeSelected}
                  onCancel={handleResearchTypeCancelled}
                />
              </div>
            </div>
          </div>
        )}

        {/* Clean Academic Messages Area */}
        <div className="flex-1 overflow-hidden bg-white">
          <ScrollArea ref={scrollAreaRef} className="h-full">
            <div className="w-full max-w-4xl mx-auto px-6 py-6">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center min-h-[70vh] text-center">
                  <div className="max-w-4xl space-y-16">
                    {/* Academic Hero Section */}
                    <div className="space-y-8">
                      <div className="relative mx-auto w-40 h-40">
                        <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 rounded-full blur-2xl animate-pulse" />
                        <div className="relative w-full h-full bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 rounded-full flex items-center justify-center shadow-2xl border-4 border-white">
                          <GraduationCap className="w-20 h-20 text-white" />
                        </div>
                        <div className="absolute -top-3 -right-3 w-12 h-12 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full flex items-center justify-center shadow-xl border-2 border-white">
                          <Sparkles className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      <div className="space-y-6">
                        <h1 className="text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                          Academic Research Assistant
                        </h1>
                        <p className="text-2xl text-gray-700 max-w-3xl mx-auto leading-relaxed font-medium">
                          Your AI-powered academic research companion. Ask any question and receive comprehensive,
                          peer-reviewed answers with <span className="font-bold text-indigo-600">real-time citations</span> and
                          <span className="font-bold text-purple-600">academic sources</span>.
                        </p>
                        <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="font-semibold">Live Academic Search</span>
                          </div>
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                            <span className="font-semibold">Peer-Reviewed Sources</span>
                          </div>
                          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                            <span className="font-semibold">Academic Citations</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Simplified Feature Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card className="p-6 bg-white border border-gray-200 hover:border-indigo-300 hover:shadow-lg transition-all duration-300">
                        <div className="space-y-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <Search className="w-6 h-6 text-white" />
                          </div>
                          <div className="space-y-2">
                            <h3 className="text-lg font-bold text-gray-900">Quick Search</h3>
                            <p className="text-sm text-gray-600">Instant answers with academic sources</p>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-6 bg-white border border-gray-200 hover:border-purple-300 hover:shadow-lg transition-all duration-300">
                        <div className="space-y-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                            <Brain className="w-6 h-6 text-white" />
                          </div>
                          <div className="space-y-2">
                            <h3 className="text-lg font-bold text-gray-900">Deep Research</h3>
                            <p className="text-sm text-gray-600">Comprehensive analysis with citations</p>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-6 bg-white border border-gray-200 hover:border-emerald-300 hover:shadow-lg transition-all duration-300">
                        <div className="space-y-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                            <Quote className="w-6 h-6 text-white" />
                          </div>
                          <div className="space-y-2">
                            <h3 className="text-lg font-bold text-gray-900">Citations</h3>
                            <p className="text-sm text-gray-600">Properly formatted references</p>
                          </div>
                        </div>
                      </Card>
                    </div>

                    {/* Simplified Example Queries */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-bold text-gray-800">Popular Research Topics</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {[
                          "AI and Machine Learning",
                          "Climate Change Research",
                          "Healthcare Technology",
                          "Quantum Computing"
                        ].map((query, index) => (
                          <Card
                            key={index}
                            className="p-3 bg-white hover:bg-indigo-50 border border-gray-200 hover:border-indigo-300 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md"
                            onClick={() => handleSearch(query)}
                          >
                            <p className="text-sm font-medium text-gray-700 hover:text-indigo-700 transition-colors">
                              {query}
                            </p>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {messages.map((message) => {
                    // Render deep research messages with special component
                    if (message.isDeepResearch && ['deep_research_outline', 'deep_research_progress', 'deep_research_section', 'deep_research_final'].includes(message.type)) {
                      return (
                        <div key={message.id} className="animate-in fade-in-50 slide-in-from-bottom-4 duration-500">
                          <DeepResearchMessage
                            message={message}
                            onApproveOutline={handleApproveOutline}
                            onEditOutline={(editRequest: OutlineEditRequest) => {
                              // TODO: Implement outline editing
                              console.log('Edit outline:', editRequest);
                            }}
                            onRegenerateOutline={() => {
                              // TODO: Implement outline regeneration
                              console.log('Regenerate outline');
                            }}
                            onViewPoint={(pointId: string) => {
                              // TODO: Implement point viewing
                              console.log('View point:', pointId);
                            }}
                            onViewReference={(reference: any) => {
                              // TODO: Implement reference viewing
                              console.log('View reference:', reference);
                            }}
                            onCopyContent={(content: string) => {
                              navigator.clipboard.writeText(content);
                              toast.success('Content copied to clipboard');
                            }}
                            onDownloadReport={(session: any) => {
                              // TODO: Implement report download
                              console.log('Download report:', session);
                            }}
                          />
                        </div>
                      );
                    }

                    // Render regular messages with animation
                    return (
                      <div key={message.id} className="animate-in fade-in-50 slide-in-from-bottom-4 duration-500">
                        <SearchMessage
                          message={message}
                          showSources={false}
                          showCitations={false}
                          isLongForm={message.content.length > 1000 || message.type === 'assistant'}
                        />
                      </div>
                    );
                  })}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Research Flow Progress */}
        {showFlowProgress && researchFlowContext && (
          <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
            <div className="max-w-4xl mx-auto">
              <ResearchFlowProgress
                context={researchFlowContext}
                className="border-blue-200"
              />
            </div>
          </div>
        )}

        {/* Clean Academic Input Area */}
        <div className="border-t border-gray-200 bg-white px-6 py-4">
          <div className="max-w-4xl mx-auto">


            <SearchInput
              onSubmit={handleSearch}
              onDeepResearch={handleDeepResearch}
              onPromptEnhance={(query) => {
                setPendingResearchQuery(query);
                setShowPromptEnhancement(true);
              }}
              isLoading={isLoading || isEnhancedResearchLoading}
              isDeepResearchLoading={isDeepResearchLoading || isEnhancedResearchLoading}
              placeholder={enhancedResearchMode ?
                "Enhanced academic research mode - generating comprehensive analysis..." :
                "Ask your research question or request academic writing assistance..."
              }
              disabled={!currentSession}
              showDeepResearch={true}
              showPromptEnhance={true}
            />

            {/* Enhanced Research Indicator */}
            {enhancedResearchMode && (
              <div className="mt-2 flex items-center gap-2 text-sm text-purple-600">
                <Sparkles className="w-4 h-4" />
                <span>Enhanced Academic Research Mode Active</span>
              </div>
            )}

            {/* Complete Article Generation Button */}
            {currentDeepResearchSession?.outline && !isGeneratingArticle && !generatedArticle && (
              <div className="mt-4 text-center">
                <Button
                  onClick={handleGenerateCompleteArticle}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                  size="lg"
                >
                  <FileText className="w-5 h-5 mr-2" />
                  Generate Complete Review Article
                </Button>
                <p className="text-sm text-gray-600 mt-2">
                  Create a comprehensive academic article with proper citations and formatting
                </p>
              </div>
            )}

            {/* Article Generation Progress */}
            {isGeneratingArticle && articleGenerationProgress && (
              <div className="mt-4 p-4 bg-purple-50 rounded-lg border border-purple-200">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-purple-900">Generating Complete Article</h4>
                    <p className="text-sm text-purple-700">{articleGenerationProgress.message}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-purple-700">
                    <span>{articleGenerationProgress.stage}</span>
                    <span>{Math.round(articleGenerationProgress.progress)}%</span>
                  </div>
                  <div className="w-full bg-purple-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${articleGenerationProgress.progress}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-purple-600">
                    <span>Section: {articleGenerationProgress.currentSection}</span>
                    <span>{articleGenerationProgress.sectionsCompleted}/{articleGenerationProgress.totalSections} completed</span>
                  </div>
                  {articleGenerationProgress.estimatedTimeRemaining > 0 && (
                    <p className="text-xs text-purple-600 text-center">
                      Estimated time remaining: {Math.round(articleGenerationProgress.estimatedTimeRemaining / 60)} minutes
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Word Export Options */}
            {showWordExportOptions && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Export Options</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowWordExportOptions(false)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                <div className="mt-2 flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => {
                      const lastMessage = messages[messages.length - 1];
                      if (lastMessage?.content) {
                        handleWordExport(lastMessage.content, lastMessage.deepResearchSession?.title || 'Research Report');
                      }
                    }}
                    className="text-xs"
                  >
                    Download Word Document
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const lastMessage = messages[messages.length - 1];
                      if (lastMessage?.content) {
                        navigator.clipboard.writeText(lastMessage.content);
                        toast.success('Content copied to clipboard');
                      }
                    }}
                    className="text-xs"
                  >
                    Copy to Clipboard
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Academic References Sidebar */}
      {showReferences && (currentSources.length > 0 || currentCitations.length > 0) && (
        <div className="w-80 border-l border-gray-200/30 bg-white/98 backdrop-blur-sm shadow-lg">
          <div className="h-full flex flex-col">
            {/* References Header */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-sm">
                    <BookOpen className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Sources</h3>
                    <p className="text-xs text-gray-600">
                      {currentCitations.length} citations • {currentSources.length} sources
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReferences(false)}
                  className="h-8 w-8 p-0 rounded-lg hover:bg-gray-100"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </Button>
              </div>
            </div>

            {/* References Content */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-6">
                {/* Citations Section */}
                {currentCitations.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Quote className="w-3 h-3 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-gray-800 text-sm">
                        Citations ({currentCitations.length})
                      </h4>
                    </div>
                    <div className="space-y-2">
                      {currentCitations.map((citation, index) => (
                        <div
                          key={citation.id}
                          className="p-3 bg-gray-50 rounded-lg border border-gray-200/60 hover:border-blue-200 cursor-pointer transition-colors text-sm"
                          onClick={() => {
                            if (citation.url) {
                              window.open(citation.url, '_blank');
                            }
                          }}
                        >
                          <div className="font-medium text-gray-900 mb-1">
                            {citation.title}
                          </div>
                          <div className="text-xs text-gray-600">
                            {citation.author && <span>{citation.author}</span>}
                            {citation.publishedDate && (
                              <span className="ml-2">({new Date(citation.publishedDate).getFullYear()})</span>
                            )}
                          </div>
                          {citation.url && (
                            <div className="mt-1 flex items-center gap-1 text-xs text-blue-600">
                              <ExternalLink className="w-3 h-3" />
                              <span>View Source</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Sources Section */}
                {currentSources.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 bg-green-100 rounded-lg flex items-center justify-center">
                        <FileText className="w-3 h-3 text-green-600" />
                      </div>
                      <h4 className="font-semibold text-gray-800 text-sm">
                        Sources ({currentSources.length})
                      </h4>
                    </div>
                    <div className="space-y-2">
                      {currentSources.map((source, index) => (
                        <div
                          key={source.id}
                          className="p-3 bg-gray-50 rounded-lg border border-gray-200/60 hover:border-green-200 cursor-pointer transition-colors text-sm"
                          onClick={() => {
                            window.open(source.url, '_blank');
                          }}
                        >
                          <div className="font-medium text-gray-900 mb-1">
                            {source.title}
                          </div>
                          <div className="text-xs text-gray-600 mb-2">
                            {source.domain}
                            {source.publishedDate && (
                              <span className="ml-2">• {new Date(source.publishedDate).getFullYear()}</span>
                            )}
                          </div>
                          {source.snippet && (
                            <div className="text-xs text-gray-600 italic line-clamp-2">
                              "{source.snippet}"
                            </div>
                          )}
                          <div className="mt-2 flex items-center justify-between">
                            <div className="flex items-center gap-1 text-xs text-green-600">
                              <Star className="w-3 h-3" />
                              <span>{(source.score * 100).toFixed(0)}% relevance</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs text-green-600">
                              <ExternalLink className="w-3 h-3" />
                              <span>View</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>
      )}

      {/* Complete Article Display */}
      {generatedArticle && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Generated Review Article</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setGeneratedArticle(null)}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              <CompleteArticleDisplay
                article={generatedArticle}
                onExport={handleExportArticle}
                onSave={handleSaveArticle}
              />
            </div>
          </div>
        </div>
      )}

      {/* Saved Articles Dialog */}
      <SavedArticlesDialog
        isOpen={showSavedArticles}
        onClose={() => setShowSavedArticles(false)}
        onViewArticle={(article) => {
          setGeneratedArticle(article);
          setShowSavedArticles(false);
        }}
        userId={currentSession?.userId}
      />
    </div>
  );
}
