/**
 * Enhanced Deep Research Service
 * Implements comprehensive academic research workflow with proper citation management
 */

import {
  DeepResearchSession,
  DeepResearchOutline,
  ResearchSection,
  DeepResearchProgress,
  CompletedSection,
  DeepResearchOptions,
  ResearchReference,
  TavilySearchResult,
  SearchSource,
  Citation,
  ResearchType,
  ResearchTypeTemplate,
  ResearchPlan,
  ResearchMetadata,
  EnhancedSearchSource
} from '../types';
import { tavilySearchService } from './tavily-search.service';
import { researchAIService } from './research-ai.service';
import { researchPlanningService } from './research-planning.service';
import { referenceManagementService } from './reference-management.service';
import { academicFormattingService } from './academic-formatting.service';
import { enhancedDatabaseService } from './enhanced-database.service';
import { completeArticleGenerationService, CompleteArticle, ArticleGenerationOptions, GenerationProgress } from './complete-article-generation.service';

export interface EnhancedResearchStep {
  stepNumber: number;
  stepName: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  data?: any;
  error?: string;
}

export interface EnhancedResearchSession extends DeepResearchSession {
  steps: EnhancedResearchStep[];
  currentStep: number;
  totalSteps: number;
  enhancedMetadata: {
    searchResultsCollected: number;
    citationsExtracted: number;
    referencesFormatted: number;
    qualityScore: number;
    academicSourcesPercentage: number;
    governmentSourcesPercentage: number;
    averageCitationsPerSection: number;
    totalSearchQueries: number;
    averageSourceQuality: number;
  };
}

export class EnhancedDeepResearchService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found for enhanced deep research');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Execute enhanced deep research with comprehensive workflow
   */
  async executeEnhancedDeepResearch(
    query: string,
    researchType: ResearchType,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<EnhancedResearchSession> {
    if (!this.isConfigured()) {
      throw new Error('Enhanced deep research service not properly configured');
    }

    // Initialize enhanced research session
    const session = await this.initializeEnhancedSession(query, researchType, options);
    
    try {
      // Step 1: Create detailed outline
      await this.executeStep1_CreateDetailedOutline(session, options, onProgress);
      
      // Step 2: Search for each section with Tavily
      await this.executeStep2_SearchForEachSection(session, options, onProgress);
      
      // Step 3: Store search results with citations
      await this.executeStep3_StoreSearchResults(session, options, onProgress);
      
      // Step 4: Generate academic content with proper citations
      await this.executeStep4_GenerateAcademicContent(session, options, onProgress);
      
      // Step 5: Compile final report with bibliography
      await this.executeStep5_CompileFinalReport(session, options, onProgress);
      
      // Finalize session
      session.status = 'completed';
      session.updatedAt = new Date();
      
      return session;
      
    } catch (error: any) {
      console.error('Enhanced deep research execution error:', error);
      session.status = 'error';
      session.steps[session.currentStep - 1].status = 'error';
      session.steps[session.currentStep - 1].error = error.message;
      throw error;
    }
  }

  /**
   * Initialize enhanced research session
   */
  private async initializeEnhancedSession(
    query: string,
    researchType: ResearchType,
    options: DeepResearchOptions
  ): Promise<EnhancedResearchSession> {
    const steps: EnhancedResearchStep[] = [
      {
        stepNumber: 1,
        stepName: 'Create Detailed Outline',
        description: 'Generate comprehensive research outline with 10 main points and subpoints',
        status: 'pending',
        progress: 0
      },
      {
        stepNumber: 2,
        stepName: 'Search for Each Section',
        description: 'Conduct academic searches for each section using Tavily API',
        status: 'pending',
        progress: 0
      },
      {
        stepNumber: 3,
        stepName: 'Store Search Results',
        description: 'Store and organize search results with citation extraction',
        status: 'pending',
        progress: 0
      },
      {
        stepNumber: 4,
        stepName: 'Generate Academic Content',
        description: 'Write academic content with 20+ citations per section',
        status: 'pending',
        progress: 0
      },
      {
        stepNumber: 5,
        stepName: 'Compile Final Report',
        description: 'Compile final report with comprehensive bibliography',
        status: 'pending',
        progress: 0
      }
    ];

    const session: EnhancedResearchSession = {
      id: `enhanced_research_${Date.now()}`,
      title: `Enhanced Research: ${query}`,
      originalQuery: query,
      outline: {} as DeepResearchOutline, // Will be populated in step 1
      researchType,
      currentPoint: 0,
      totalPoints: 10, // Default to 10 points
      status: 'outline_pending',
      aiAssistants: [],
      completedPoints: [],
      allReferences: [],
      researchMetadata: {
        totalWordCount: 0,
        totalCitations: 0,
        academicSources: 0,
        governmentSources: 0,
        averageSourceQuality: 0,
        researchDuration: 0,
        sectionsCompleted: 0,
        keyTopics: [],
        confidenceScore: 0
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'current_user', // TODO: Get from auth
      model: options.model,
      steps,
      currentStep: 1,
      totalSteps: 5,
      enhancedMetadata: {
        searchResultsCollected: 0,
        citationsExtracted: 0,
        referencesFormatted: 0,
        qualityScore: 0,
        academicSourcesPercentage: 0,
        governmentSourcesPercentage: 0,
        averageCitationsPerSection: 0,
        totalSearchQueries: 0,
        averageSourceQuality: 0
      }
    };

    return session;
  }

  /**
   * Step 1: Create detailed outline
   */
  private async executeStep1_CreateDetailedOutline(
    session: EnhancedResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<void> {
    const step = session.steps[0];
    step.status = 'in_progress';
    step.startTime = new Date();
    session.currentStep = 1;

    if (onProgress) {
      onProgress({
        pointNumber: 1,
        pointId: 'outline',
        pointTitle: 'Creating Detailed Outline',
        assistantId: 'outline_assistant',
        assistantName: 'Dr. Outline',
        status: 'analyzing',
        message: 'Generating comprehensive research outline...',
        progress: 25,
        startTime: new Date()
      });
    }

    try {
      // Generate intelligent outline using existing service
      const outline = await researchPlanningService.generateIntelligentOutline(
        session.originalQuery,
        session.researchType,
        options.model
      );

      session.outline = outline;
      session.totalPoints = outline.totalPoints;

      // Save outline to database
      const outlineId = await enhancedDatabaseService.saveResearchOutline(
        session.id,
        outline
      );

      step.status = 'completed';
      step.endTime = new Date();
      step.progress = 100;
      step.data = { outlineId, totalPoints: outline.totalPoints };

      if (onProgress) {
        onProgress({
          pointNumber: 1,
          pointId: 'outline',
          pointTitle: 'Outline Created',
          assistantId: 'outline_assistant',
          assistantName: 'Dr. Outline',
          status: 'completed',
          message: `Outline created with ${outline.totalPoints} main points`,
          progress: 100,
          startTime: step.startTime,
          endTime: step.endTime
        });
      }

    } catch (error: any) {
      step.status = 'error';
      step.error = error.message;
      throw error;
    }
  }

  /**
   * Step 2: Search for each section with Tavily
   */
  private async executeStep2_SearchForEachSection(
    session: EnhancedResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<void> {
    const step = session.steps[1];
    step.status = 'in_progress';
    step.startTime = new Date();
    session.currentStep = 2;

    const searchResults: Map<string, TavilySearchResult[]> = new Map();
    let totalSearches = 0;
    let completedSearches = 0;

    // Calculate total searches needed
    session.outline.points.forEach(point => {
      totalSearches += (point.subpoints?.length || 0) * 3; // 3 searches per subpoint
    });

    try {
      for (const point of session.outline.points) {
        const pointSearchResults: TavilySearchResult[] = [];

        if (onProgress) {
          onProgress({
            pointNumber: point.pointNumber,
            pointId: point.id,
            pointTitle: point.title,
            assistantId: `search_assistant_${point.pointNumber}`,
            assistantName: `Research Assistant`,
            status: 'searching',
            message: `Searching academic sources for: ${point.title}`,
            progress: Math.round((completedSearches / totalSearches) * 100),
            startTime: new Date(),
            currentSubpoint: 0,
            totalSubpoints: point.subpoints?.length || 0
          });
        }

        // Generate comprehensive search queries for this point
        const searchQueries = await this.generateEnhancedSearchQueries(point);
        session.enhancedMetadata.totalSearchQueries += searchQueries.length;

        // Execute searches for each query
        for (const query of searchQueries) {
          try {
            // Add delay to avoid rate limiting
            if (completedSearches > 0) {
              await new Promise(resolve => setTimeout(resolve, 2000));
            }

            const result = await tavilySearchService.searchAcademic(query, {
              maxResults: 8, // Increased for better coverage
              searchDepth: 'comprehensive',
              includeImages: false
            });

            pointSearchResults.push(result);
            completedSearches++;

            // Update progress with more detailed information
            if (onProgress) {
              onProgress({
                pointNumber: point.pointNumber,
                pointId: point.id,
                pointTitle: point.title,
                assistantId: `search_assistant_${point.pointNumber}`,
                assistantName: `Research Assistant`,
                status: 'searching',
                message: `Searching for "${point.title}" - Found ${pointSearchResults.length} sources (${completedSearches}/${totalSearches} searches completed)`,
                progress: Math.round((completedSearches / totalSearches) * 100),
                startTime: step.startTime,
                currentSubpoint: 0,
                totalSubpoints: point.subpoints?.length || 0,
                searchResults: pointSearchResults
              });
            }

          } catch (searchError) {
            console.warn(`Search failed for query: ${query}`, searchError);
            // Continue with other searches
          }
        }

        searchResults.set(point.id, pointSearchResults);
        session.enhancedMetadata.searchResultsCollected += pointSearchResults.length;
      }

      step.status = 'completed';
      step.endTime = new Date();
      step.progress = 100;
      step.data = { searchResults: Array.from(searchResults.entries()) };

      if (onProgress) {
        onProgress({
          pointNumber: session.outline.points.length,
          pointId: 'search_complete',
          pointTitle: 'Search Completed',
          assistantId: 'search_coordinator',
          assistantName: 'Dr. Search Coordinator',
          status: 'completed',
          message: `Collected ${session.enhancedMetadata.searchResultsCollected} search results`,
          progress: 100,
          startTime: step.startTime,
          endTime: step.endTime
        });
      }

    } catch (error: any) {
      step.status = 'error';
      step.error = error.message;
      throw error;
    }
  }

  /**
   * Generate enhanced search queries for a research point
   */
  private async generateEnhancedSearchQueries(point: any): Promise<string[]> {
    const queries: string[] = [];

    // Base academic queries
    queries.push(
      `"${point.title}" academic research`,
      `"${point.title}" peer reviewed study`,
      `"${point.title}" systematic review`,
      `"${point.title}" meta analysis`,
      `"${point.title}" literature review`
    );

    // Subpoint-specific queries
    if (point.subpoints && point.subpoints.length > 0) {
      point.subpoints.forEach((subpoint: any) => {
        queries.push(
          `"${subpoint.title}" research`,
          `"${subpoint.title}" academic study`,
          `"${subpoint.title}" evidence`
        );
      });
    }

    // Government and official source queries
    queries.push(
      `"${point.title}" government report`,
      `"${point.title}" official statistics`,
      `"${point.title}" policy analysis`
    );

    // Recent research queries
    const currentYear = new Date().getFullYear();
    queries.push(
      `"${point.title}" ${currentYear}`,
      `"${point.title}" ${currentYear - 1}`,
      `"${point.title}" recent findings`
    );

    // Clean and limit queries
    return queries
      .map(query => query.trim())
      .filter(query => query.length > 0 && query.length < 150)
      .slice(0, 15); // Limit to 15 queries per point
  }

  /**
   * Step 3: Store search results with citations
   */
  private async executeStep3_StoreSearchResults(
    session: EnhancedResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<void> {
    const step = session.steps[2];
    step.status = 'in_progress';
    step.startTime = new Date();
    session.currentStep = 3;

    try {
      const searchResultsData = step.data?.searchResults || [];
      let processedPoints = 0;

      for (const [pointId, searchResults] of searchResultsData) {
        const point = session.outline.points.find(p => p.id === pointId);
        if (!point) continue;

        if (onProgress) {
          onProgress({
            pointNumber: point.pointNumber,
            pointId: point.id,
            pointTitle: point.title,
            assistantId: `storage_assistant_${point.pointNumber}`,
            assistantName: `Dr. Storage ${point.pointNumber}`,
            status: 'analyzing',
            message: `Processing and storing search results for: ${point.title}`,
            progress: Math.round((processedPoints / session.outline.points.length) * 100),
            startTime: new Date()
          });
        }

        // Transform search results to enhanced sources
        const enhancedSources: EnhancedSearchSource[] = [];
        const extractedCitations: Citation[] = [];

        for (const searchResult of searchResults) {
          for (const result of searchResult.results) {
            const enhancedSource: EnhancedSearchSource = {
              id: `source_${Date.now()}_${Math.random()}`,
              title: result.title,
              url: result.url,
              snippet: result.content.substring(0, 500),
              domain: new URL(result.url).hostname,
              publishedDate: result.published_date,
              score: result.score,
              type: this.determineSourceType(result.url, result.title),
              qualityScore: this.calculateEnhancedQualityScore(result),
              isGovernment: this.isGovernmentSource(result.url),
              isPeerReviewed: this.isPeerReviewedSource(result.url, result.title),
              citationCount: 0,
              impactFactor: undefined,
              authorCredentials: [],
              institutionAffiliation: this.extractInstitution(result.url),
              methodology: this.extractMethodology(result.content),
              dataQuality: this.assessDataQuality(result),
              recency: this.assessRecency(result.published_date)
            };

            enhancedSources.push(enhancedSource);

            // Extract citations from content
            const citations = this.extractCitationsFromContent(
              result.content,
              enhancedSource,
              point.id
            );
            extractedCitations.push(...citations);
          }
        }

        // Store sources and citations in database
        await enhancedDatabaseService.saveResearchSources(point.id, enhancedSources);
        session.enhancedMetadata.citationsExtracted += extractedCitations.length;

        // Add sources to reference manager
        enhancedSources.forEach(source => {
          referenceManagementService.addSource(source, point.id);
        });

        processedPoints++;
      }

      step.status = 'completed';
      step.endTime = new Date();
      step.progress = 100;

      if (onProgress) {
        onProgress({
          pointNumber: session.outline.points.length,
          pointId: 'storage_complete',
          pointTitle: 'Storage Completed',
          assistantId: 'storage_coordinator',
          assistantName: 'Dr. Storage Coordinator',
          status: 'completed',
          message: `Stored ${session.enhancedMetadata.citationsExtracted} citations and references`,
          progress: 100,
          startTime: step.startTime,
          endTime: step.endTime
        });
      }

    } catch (error: any) {
      step.status = 'error';
      step.error = error.message;
      throw error;
    }
  }

  /**
   * Step 4: Generate academic content with proper citations
   */
  private async executeStep4_GenerateAcademicContent(
    session: EnhancedResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<void> {
    const step = session.steps[3];
    step.status = 'in_progress';
    step.startTime = new Date();
    session.currentStep = 4;

    try {
      let completedPoints = 0;

      for (const point of session.outline.points) {
        if (onProgress) {
          onProgress({
            pointNumber: point.pointNumber,
            pointId: point.id,
            pointTitle: point.title,
            assistantId: `writer_assistant_${point.pointNumber}`,
            assistantName: `Academic Writer`,
            status: 'writing',
            message: `Writing comprehensive analysis for: ${point.title}`,
            progress: Math.round((completedPoints / session.outline.points.length) * 100),
            startTime: new Date(),
            currentSubpoint: 0,
            totalSubpoints: point.subpoints?.length || 0
          });
        }

        // Generate high-quality academic content
        const content = await this.generateEnhancedAcademicContent(
          point,
          session,
          options
        );

        // Save point with content to database
        await enhancedDatabaseService.saveResearchPoint(
          session.outline.id,
          point,
          content
        );

        // Update session metadata
        session.researchMetadata.totalWordCount += content.split(' ').length;
        session.researchMetadata.sectionsCompleted++;

        completedPoints++;
      }

      step.status = 'completed';
      step.endTime = new Date();
      step.progress = 100;

      if (onProgress) {
        onProgress({
          pointNumber: session.outline.points.length,
          pointId: 'writing_complete',
          pointTitle: 'Writing Completed',
          assistantId: 'writing_coordinator',
          assistantName: 'Dr. Writing Coordinator',
          status: 'completed',
          message: `Generated ${session.researchMetadata.totalWordCount} words of academic content`,
          progress: 100,
          startTime: step.startTime,
          endTime: step.endTime
        });
      }

    } catch (error: any) {
      step.status = 'error';
      step.error = error.message;
      throw error;
    }
  }

  /**
   * Step 5: Compile final report with bibliography
   */
  private async executeStep5_CompileFinalReport(
    session: EnhancedResearchSession,
    options: DeepResearchOptions,
    onProgress?: (progress: DeepResearchProgress) => void
  ): Promise<void> {
    const step = session.steps[4];
    step.status = 'in_progress';
    step.startTime = new Date();
    session.currentStep = 5;

    try {
      if (onProgress) {
        onProgress({
          pointNumber: 0,
          pointId: 'final_report',
          pointTitle: 'Compiling Final Report',
          assistantId: 'report_compiler',
          assistantName: 'Dr. Report Compiler',
          status: 'writing',
          message: 'Compiling comprehensive final report with bibliography...',
          progress: 25,
          startTime: new Date()
        });
      }

      // Generate consolidated references
      const consolidatedReferences = referenceManagementService.generateConsolidatedReferences(
        options.citationStyle || 'apa'
      );

      session.consolidatedReferences = consolidatedReferences;
      session.enhancedMetadata.referencesFormatted = referenceManagementService.getAllReferences().length;

      // Generate final comprehensive report
      const finalReport = await this.generateFinalComprehensiveReport(session, options);
      session.finalReport = finalReport;

      // Generate formatted version for export
      session.formattedReport = academicFormattingService.formatForAcademicOutput(finalReport);

      // Calculate final quality metrics
      this.calculateFinalQualityMetrics(session);

      step.status = 'completed';
      step.endTime = new Date();
      step.progress = 100;

      if (onProgress) {
        onProgress({
          pointNumber: 0,
          pointId: 'final_report',
          pointTitle: 'Final Report Completed',
          assistantId: 'report_compiler',
          assistantName: 'Dr. Report Compiler',
          status: 'completed',
          message: `Final report completed with ${session.enhancedMetadata.referencesFormatted} references`,
          progress: 100,
          startTime: step.startTime,
          endTime: step.endTime
        });
      }

    } catch (error: any) {
      step.status = 'error';
      step.error = error.message;
      throw error;
    }
  }

  /**
   * Helper methods for enhanced research processing
   */

  private determineSourceType(url: string, title: string): 'academic' | 'web' | 'news' | 'book' {
    const academicDomains = ['pubmed', 'scholar.google', 'jstor', 'springer', 'elsevier', 'wiley', 'nature', 'science'];
    const newsDomains = ['reuters', 'bbc', 'cnn', 'nytimes', 'washingtonpost'];

    if (academicDomains.some(domain => url.includes(domain))) return 'academic';
    if (newsDomains.some(domain => url.includes(domain))) return 'news';
    if (title.toLowerCase().includes('book') || url.includes('books')) return 'book';
    return 'web';
  }

  private calculateEnhancedQualityScore(result: any): number {
    let score = result.score || 0.5;

    // Boost for academic sources
    if (this.isPeerReviewedSource(result.url, result.title)) score += 0.2;
    if (this.isGovernmentSource(result.url)) score += 0.15;

    // Boost for recent content
    if (result.published_date) {
      const publishedYear = new Date(result.published_date).getFullYear();
      const currentYear = new Date().getFullYear();
      if (currentYear - publishedYear <= 2) score += 0.1;
    }

    // Boost for content length
    if (result.content && result.content.length > 500) score += 0.05;

    return Math.min(score, 1.0);
  }

  private isGovernmentSource(url: string): boolean {
    return url.includes('.gov') || url.includes('.edu') ||
           url.includes('who.int') || url.includes('nih.gov') ||
           url.includes('cdc.gov');
  }

  private isPeerReviewedSource(url: string, title: string): boolean {
    const academicDomains = ['pubmed', 'scholar.google', 'jstor', 'springer', 'elsevier', 'wiley'];
    const academicKeywords = ['journal', 'peer-reviewed', 'research', 'study', 'analysis'];

    return academicDomains.some(domain => url.includes(domain)) ||
           academicKeywords.some(keyword => title.toLowerCase().includes(keyword));
  }

  private extractInstitution(url: string): string {
    try {
      const hostname = new URL(url).hostname;
      if (hostname.includes('.edu')) {
        return hostname.replace('www.', '').replace('.edu', '').toUpperCase();
      }
      return '';
    } catch {
      return '';
    }
  }

  private extractMethodology(content: string): string {
    const methodKeywords = ['methodology', 'method', 'approach', 'technique', 'procedure'];
    const sentences = content.split('.').slice(0, 10); // First 10 sentences

    for (const sentence of sentences) {
      if (methodKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
        return sentence.trim();
      }
    }
    return '';
  }

  private assessDataQuality(result: any): 'high' | 'medium' | 'low' {
    if (this.isPeerReviewedSource(result.url, result.title)) return 'high';
    if (this.isGovernmentSource(result.url)) return 'high';
    if (result.score > 0.7) return 'medium';
    return 'low';
  }

  private assessRecency(publishedDate?: string): 'current' | 'recent' | 'dated' {
    if (!publishedDate) return 'dated';

    const published = new Date(publishedDate);
    const now = new Date();
    const yearsDiff = now.getFullYear() - published.getFullYear();

    if (yearsDiff <= 1) return 'current';
    if (yearsDiff <= 3) return 'recent';
    return 'dated';
  }

  private extractCitationsFromContent(
    content: string,
    source: EnhancedSearchSource,
    pointId: string
  ): Citation[] {
    const citations: Citation[] = [];

    // Simple citation extraction - in a real implementation, this would be more sophisticated
    const sentences = content.split('.').slice(0, 5); // First 5 sentences

    sentences.forEach((sentence, index) => {
      if (sentence.trim().length > 50) {
        citations.push({
          id: `citation_${Date.now()}_${index}`,
          text: `(${this.extractAuthorFromTitle(source.title)}, ${this.extractYearFromDate(source.publishedDate)})`,
          sourceId: source.id,
          url: source.url,
          title: source.title,
          position: index * 100,
          author: this.extractAuthorFromTitle(source.title),
          year: this.extractYearFromDate(source.publishedDate)
        });
      }
    });

    return citations;
  }

  private extractAuthorFromTitle(title: string): string {
    const words = title.split(' ');
    return words[0] || 'Unknown';
  }

  private extractYearFromDate(publishedDate?: string): string {
    if (!publishedDate) return new Date().getFullYear().toString();
    return new Date(publishedDate).getFullYear().toString();
  }

  private calculateFinalQualityMetrics(session: EnhancedResearchSession): void {
    const allReferences = referenceManagementService.getAllReferences();

    session.enhancedMetadata.academicSourcesPercentage = allReferences.length > 0
      ? (allReferences.filter(ref => ref.source.isPeerReviewed).length / allReferences.length) * 100
      : 0;

    session.enhancedMetadata.governmentSourcesPercentage = allReferences.length > 0
      ? (allReferences.filter(ref => ref.source.isGovernment).length / allReferences.length) * 100
      : 0;

    session.enhancedMetadata.averageCitationsPerSection = session.outline.totalPoints > 0
      ? session.enhancedMetadata.citationsExtracted / session.outline.totalPoints
      : 0;

    session.enhancedMetadata.averageSourceQuality = allReferences.length > 0
      ? allReferences.reduce((sum, ref) => sum + ref.source.qualityScore, 0) / allReferences.length
      : 0;

    session.enhancedMetadata.qualityScore = this.calculateOverallQualityScore(session);
  }

  private calculateOverallQualityScore(session: EnhancedResearchSession): number {
    let score = 0;
    let factors = 0;

    // Academic sources factor (30%)
    if (session.enhancedMetadata.academicSourcesPercentage > 0) {
      score += (session.enhancedMetadata.academicSourcesPercentage / 100) * 30;
      factors++;
    }

    // Citation density factor (25%)
    if (session.enhancedMetadata.averageCitationsPerSection > 0) {
      const citationScore = Math.min(session.enhancedMetadata.averageCitationsPerSection / 20, 1);
      score += citationScore * 25;
      factors++;
    }

    // Word count factor (20%)
    const targetWords = session.researchType?.totalWordTarget || 10000;
    if (session.researchMetadata.totalWordCount > 0) {
      const wordScore = Math.min(session.researchMetadata.totalWordCount / targetWords, 1);
      score += wordScore * 20;
      factors++;
    }

    // Source quality factor (15%)
    if (session.enhancedMetadata.averageSourceQuality > 0) {
      score += session.enhancedMetadata.averageSourceQuality * 15;
      factors++;
    }

    // Completion factor (10%)
    const completionScore = session.researchMetadata.sectionsCompleted / session.outline.totalPoints;
    score += completionScore * 10;
    factors++;

    return factors > 0 ? score / factors : 0;
  }

  /**
   * Generate a complete detailed article from research session
   */
  async generateCompleteArticle(
    session: EnhancedResearchSession,
    options: {
      citationStyle?: 'apa' | 'mla' | 'chicago' | 'harvard';
      includeAbstract?: boolean;
      includeConclusion?: boolean;
      minWordCount?: number;
      maxWordCount?: number;
      academicLevel?: 'undergraduate' | 'graduate' | 'doctoral' | 'professional';
    } = {},
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<CompleteArticle> {
    if (!session.outline) {
      throw new Error('Research session must have an outline to generate complete article');
    }

    const articleOptions: ArticleGenerationOptions = {
      model: session.model || 'google/gemini-2.5-flash',
      researchType: session.researchType,
      outline: session.outline,
      citationStyle: options.citationStyle || 'apa',
      minWordCount: options.minWordCount || 5000,
      maxWordCount: options.maxWordCount || 15000,
      minCitationsPerSection: 15,
      includeAbstract: options.includeAbstract !== false,
      includeConclusion: options.includeConclusion !== false,
      academicLevel: options.academicLevel || 'graduate',
      generateTables: false,
      generateFigures: false
    };

    try {
      const article = await completeArticleGenerationService.generateCompleteArticle(
        articleOptions,
        onProgress
      );

      // Update session with generated article
      session.generatedArticle = article;
      session.status = 'completed';
      session.updatedAt = new Date();

      return article;
    } catch (error) {
      console.error('Error generating complete article:', error);
      throw new Error(`Failed to generate complete article: ${error.message}`);
    }
  }

  /**
   * Export complete article to various formats
   */
  async exportCompleteArticle(
    article: CompleteArticle,
    format: 'pdf' | 'docx' | 'txt'
  ): Promise<Blob> {
    try {
      switch (format) {
        case 'txt':
          return this.exportToText(article);
        case 'docx':
          return this.exportToDocx(article);
        case 'pdf':
          return this.exportToPdf(article);
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      console.error(`Error exporting article to ${format}:`, error);
      throw new Error(`Failed to export article: ${error.message}`);
    }
  }

  /**
   * Export article to plain text
   */
  private exportToText(article: CompleteArticle): Blob {
    let content = '';

    // Title
    content += `${article.title}\n`;
    content += '='.repeat(article.title.length) + '\n\n';

    // Abstract
    if (article.abstract) {
      content += 'ABSTRACT\n\n';
      content += `${article.abstract}\n\n`;
    }

    // Sections
    article.sections.forEach((section, index) => {
      content += `${index + 1}. ${section.title}\n`;
      content += '-'.repeat(section.title.length + 4) + '\n\n';
      content += `${section.content}\n\n`;

      // Subsections
      if (section.subsections) {
        section.subsections.forEach((subsection, subIndex) => {
          content += `${index + 1}.${subIndex + 1} ${subsection.title}\n\n`;
          content += `${subsection.content}\n\n`;
        });
      }
    });

    // References
    if (article.references.length > 0) {
      content += 'REFERENCES\n\n';
      article.references.forEach((ref, index) => {
        content += `[${index + 1}] ${ref.formattedAPA}\n\n`;
      });
    }

    return new Blob([content], { type: 'text/plain' });
  }

  /**
   * Export article to DOCX (placeholder - would need proper DOCX library)
   */
  private exportToDocx(article: CompleteArticle): Blob {
    // This is a simplified implementation
    // In a real application, you would use a library like docx or mammoth
    const htmlContent = this.convertToHtml(article);
    return new Blob([htmlContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
  }

  /**
   * Export article to PDF (placeholder - would need proper PDF library)
   */
  private exportToPdf(article: CompleteArticle): Blob {
    // This is a simplified implementation
    // In a real application, you would use a library like jsPDF or puppeteer
    const htmlContent = this.convertToHtml(article);
    return new Blob([htmlContent], { type: 'application/pdf' });
  }

  /**
   * Convert article to HTML format
   */
  private convertToHtml(article: CompleteArticle): string {
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${article.title}</title>
        <style>
          body { font-family: 'Times New Roman', serif; line-height: 1.6; margin: 40px; }
          h1 { text-align: center; margin-bottom: 30px; }
          h2 { margin-top: 30px; margin-bottom: 15px; }
          h3 { margin-top: 20px; margin-bottom: 10px; }
          .abstract { font-style: italic; margin: 20px 0; }
          .references { margin-top: 40px; }
          .reference { margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <h1>${article.title}</h1>
    `;

    if (article.abstract) {
      html += `
        <h2>Abstract</h2>
        <div class="abstract">${article.abstract}</div>
      `;
    }

    article.sections.forEach((section, index) => {
      html += `
        <h2>${index + 1}. ${section.title}</h2>
        <p>${section.content.replace(/\n/g, '</p><p>')}</p>
      `;

      if (section.subsections) {
        section.subsections.forEach((subsection, subIndex) => {
          html += `
            <h3>${index + 1}.${subIndex + 1} ${subsection.title}</h3>
            <p>${subsection.content.replace(/\n/g, '</p><p>')}</p>
          `;
        });
      }
    });

    if (article.references.length > 0) {
      html += '<div class="references"><h2>References</h2>';
      article.references.forEach((ref, index) => {
        html += `<div class="reference">[${index + 1}] ${ref.formattedAPA}</div>`;
      });
      html += '</div>';
    }

    html += '</body></html>';
    return html;
  }
}

// Export singleton instance
export const enhancedDeepResearchService = new EnhancedDeepResearchService();
