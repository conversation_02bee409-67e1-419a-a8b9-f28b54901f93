/**
 * Article Input Form Component
 * Handles different types of article content input
 */

import React, { useState } from 'react';
import { ArticleInputFormProps, InputType, ArticleContent } from '../types';
import { INPUT_TYPES, AI_MODELS, ANALYSIS_DEPTHS } from '../constants';
import { useArticleFinderStore } from '../stores/article-finder.store';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  Upload, 
  Sparkles, 
  Settings,
  Info,
  BookOpen,
  Search,
  Target
} from "lucide-react";

export const ArticleInputForm: React.FC<ArticleInputFormProps> = ({
  onSubmit,
  isLoading,
  selectedInputType,
  onInputTypeChange
}) => {
  const {
    selectedModel,
    analysisDepth,
    setSelectedModel,
    setAnalysisDepth
  } = useArticleFinderStore();

  const [content, setContent] = useState('');
  const [additionalContext, setAdditionalContext] = useState('');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  /**
   * Handle form submission
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      return;
    }

    // Extract keywords from content
    const extractedKeywords = extractKeywords(content);
    
    // Detect language (simple detection)
    const detectedLanguage = detectLanguage(content);

    const articleContent: ArticleContent = {
      type: selectedInputType,
      content: content.trim(),
      wordCount: content.trim().split(/\s+/).length,
      extractedKeywords,
      detectedLanguage
    };

    onSubmit(articleContent);
  };

  /**
   * Extract keywords from content
   */
  const extractKeywords = (text: string): string[] => {
    // Simple keyword extraction - remove common words and get unique terms
    const commonWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
    ]);

    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.has(word));

    // Get unique words and return top 10
    const uniqueWords = [...new Set(words)];
    return uniqueWords.slice(0, 10);
  };

  /**
   * Simple language detection
   */
  const detectLanguage = (text: string): string => {
    // Very basic language detection - could be enhanced
    const englishWords = ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with'];
    const wordCount = text.toLowerCase().split(/\s+/).length;
    const englishMatches = englishWords.filter(word => 
      text.toLowerCase().includes(word)
    ).length;
    
    return (englishMatches / englishWords.length) > 0.3 ? 'English' : 'Unknown';
  };

  /**
   * Get placeholder text based on input type
   */
  const getPlaceholder = (): string => {
    switch (selectedInputType) {
      case 'title':
        return 'Enter your article title here...';
      case 'abstract':
        return 'Paste your article abstract here...';
      case 'full':
        return 'Paste your complete article content here...';
      default:
        return 'Enter your content here...';
    }
  };

  /**
   * Get content length limits
   */
  const getContentLimits = () => {
    switch (selectedInputType) {
      case 'title':
        return { min: 10, max: 500, recommended: '10-100 words' };
      case 'abstract':
        return { min: 50, max: 5000, recommended: '150-300 words' };
      case 'full':
        return { min: 500, max: 100000, recommended: '3000+ words' };
      default:
        return { min: 10, max: 1000, recommended: '10+ words' };
    }
  };

  const limits = getContentLimits();
  const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
  const isValidLength = wordCount >= limits.min && wordCount <= limits.max;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <BookOpen className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Find the Perfect Journal for Your Research
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Submit your research content and let our AI analyze it to recommend the best academic journals for publication.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Input Type Selection */}
        <div>
          <Label className="text-base font-semibold text-gray-900 mb-3 block">
            What would you like to analyze?
          </Label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(INPUT_TYPES).map(([type, info]) => (
              <Card 
                key={type}
                className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedInputType === type 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => onInputTypeChange(type as InputType)}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    {type === 'title' && <FileText className="w-6 h-6 text-blue-600" />}
                    {type === 'abstract' && <Search className="w-6 h-6 text-blue-600" />}
                    {type === 'full' && <Target className="w-6 h-6 text-blue-600" />}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{info.label}</h3>
                  <p className="text-sm text-gray-600">{info.description}</p>
                  {selectedInputType === type && (
                    <Badge className="mt-2 bg-blue-600">Selected</Badge>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Content Input */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <Label htmlFor="content" className="text-base font-semibold text-gray-900">
              {INPUT_TYPES[selectedInputType].label}
            </Label>
            <div className="text-sm text-gray-500">
              {wordCount} words • Recommended: {limits.recommended}
            </div>
          </div>
          
          <Textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={getPlaceholder()}
            className={`min-h-[200px] resize-y ${
              !isValidLength && content.length > 0 
                ? 'border-red-300 focus:border-red-500' 
                : ''
            }`}
            disabled={isLoading}
          />
          
          {!isValidLength && content.length > 0 && (
            <p className="text-sm text-red-600 mt-1">
              Content should be between {limits.min} and {limits.max} words.
            </p>
          )}
        </div>

        {/* Additional Context */}
        <div>
          <Label htmlFor="context" className="text-base font-semibold text-gray-900 mb-3 block">
            Additional Context (Optional)
          </Label>
          <Textarea
            id="context"
            value={additionalContext}
            onChange={(e) => setAdditionalContext(e.target.value)}
            placeholder="Provide any additional context about your research, target audience, or specific requirements..."
            className="min-h-[100px]"
            disabled={isLoading}
          />
          <p className="text-sm text-gray-500 mt-1">
            Help us provide better recommendations by sharing more about your research goals.
          </p>
        </div>

        {/* Advanced Options */}
        <div>
          <Button
            type="button"
            variant="ghost"
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            className="mb-4"
          >
            <Settings className="w-4 h-4 mr-2" />
            Advanced Options
          </Button>

          {showAdvancedOptions && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              {/* AI Model Selection */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  AI Model
                </Label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select AI model" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AI_MODELS).map((model) => (
                      <SelectItem key={model.id} value={model.model} disabled={!model.enabled}>
                        <div className="flex items-center justify-between w-full">
                          <span>{model.name}</span>
                          <Badge variant="secondary" className="ml-2">
                            {model.provider}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Analysis Depth */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  Analysis Depth
                </Label>
                <Select value={analysisDepth} onValueChange={setAnalysisDepth}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select analysis depth" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(ANALYSIS_DEPTHS).map(([depth, info]) => (
                      <SelectItem key={depth} value={depth}>
                        <div>
                          <div className="font-medium">{info.label}</div>
                          <div className="text-xs text-gray-500">{info.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>

        {/* Info Alert */}
        <Alert className="border-blue-200 bg-blue-50">
          <Info className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            Our AI will analyze your content to identify the research domain, methodology, and key topics, 
            then recommend journals based on scope, impact factor, publication timeline, and costs.
          </AlertDescription>
        </Alert>

        {/* Submit Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            size="lg"
            disabled={!content.trim() || !isValidLength || isLoading}
            className="px-8 py-3"
          >
            {isLoading ? (
              <>
                <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Search className="w-5 h-5 mr-2" />
                Find Journals
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
