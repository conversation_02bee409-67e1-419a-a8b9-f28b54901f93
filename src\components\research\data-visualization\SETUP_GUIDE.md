# Data Visualization Platform Setup Guide

## Quick Start

### 1. Environment Setup

Create a `.env.local` file in your project root with your Google Gemini API key:

```bash
# Required for AI analysis
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

**Get your Gemini API key:**
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key to your environment file

### 2. Dependencies

All required dependencies have been installed. If you need to reinstall:

```bash
npm install @google/genai plotly.js-dist-min react-plotly.js @types/react-plotly.js xlsx papaparse @types/papaparse mime @types/mime react-dropzone @types/react-dropzone
```

### 3. Access the Feature

1. Start your development server: `npm run dev`
2. Navigate to the research dashboard
3. Click on "Data Visualization" in the sidebar
4. Upload a CSV or Excel file to begin

## Feature Overview

### 🔄 Complete Workflow
1. **Upload**: Drag & drop CSV/Excel files (up to 50MB)
2. **Analyze**: AI automatically analyzes data structure and generates insights
3. **Visualize**: Interactive charts created with <PERSON>lotly.js
4. **Query**: Ask natural language questions about your data

### 📊 Supported Visualizations
- **Histograms**: Data distribution analysis
- **Scatter Plots**: Variable relationships
- **Correlation Heatmaps**: Variable correlations
- **Box Plots**: Outlier detection
- **Bar Charts**: Categorical comparisons
- **Line Charts**: Trend analysis
- **Pie Charts**: Proportional data

### 🤖 AI Capabilities
- **Data Analysis**: Automatic type detection, statistics, quality assessment
- **Insight Generation**: Key findings, patterns, correlations
- **Code Execution**: Python analysis on Google's secure servers
- **Natural Language**: Ask questions in plain English

## Usage Examples

### Sample Data Files
Test the platform with these sample datasets:

**Sales Data (CSV)**
```csv
date,product,category,sales,region
2024-01-01,Laptop,Electronics,1200,North
2024-01-01,Phone,Electronics,800,South
2024-01-02,Desk,Furniture,300,North
2024-01-02,Chair,Furniture,150,South
```

**Employee Data (CSV)**
```csv
name,age,department,salary,experience
John Doe,28,Engineering,75000,3
Jane Smith,32,Marketing,65000,5
Bob Johnson,45,Sales,80000,15
Alice Brown,29,Engineering,72000,2
```

### Example Queries
Try these natural language questions:

- "What are the main patterns in this data?"
- "Show me the correlation between age and salary"
- "Are there any outliers in the sales column?"
- "Create a visualization of sales by region"
- "What's the average salary by department?"
- "Show me the trend over time"

## Architecture Details

### Component Structure
```
DataVisualizationPlatform
├── FileUploader (drag & drop interface)
├── AnalysisPipeline (3-step AI analysis)
├── VisualizationGallery (interactive charts)
└── QueryInterface (natural language queries)
```

### State Management
Uses Zustand store for:
- File upload status and metadata
- Analysis results and insights
- Visualization configurations
- Query history and responses
- UI state and error handling

### Security Features
- **Client-side validation**: File type and size checks
- **Secure API calls**: HTTPS communication with Gemini
- **No data persistence**: Files processed in memory only
- **Error boundaries**: Graceful error handling
- **Input sanitization**: Query validation and limits

## Troubleshooting

### Common Issues

**"Gemini API key not found"**
- Ensure `VITE_GEMINI_API_KEY` is set in `.env.local`
- Restart your development server after adding the key
- Verify the key is valid at Google AI Studio

**File upload fails**
- Check file size (must be under 50MB)
- Ensure file format is CSV, XLSX, or XLS
- Verify file is not corrupted or empty
- Try a different file format

**Analysis errors**
- Check your internet connection
- Verify Gemini API key has proper permissions
- Ensure data has at least 10 rows
- Try with a simpler dataset first

**Visualizations not loading**
- Clear browser cache and reload
- Check browser console for JavaScript errors
- Ensure Plotly.js loaded correctly
- Try refreshing the page

**Query system not responding**
- Keep queries under 1000 characters
- Use clear, specific language
- Check API rate limits
- Try simpler questions first

### Performance Tips

**For Large Files (>10MB):**
- Consider splitting into smaller chunks
- Focus on key columns for analysis
- Use sampling for initial exploration

**For Many Columns (>20):**
- Limit correlation analysis scope
- Focus on most important variables
- Consider data preprocessing

**For Complex Queries:**
- Break into simpler questions
- Be specific about what you want
- Reference column names directly

## Development

### Adding New Chart Types
1. Add type to `VisualizationConfig` in `types.ts`
2. Update `VISUALIZATION_TYPES` in `constants.ts`
3. Implement generation logic in `gemini-analysis.service.ts`
4. Add icon mapping in `VisualizationGallery.tsx`

### Extending Analysis Pipeline
1. Add new step to `AnalysisPipelineStep` type
2. Update pipeline in `AnalysisPipeline.tsx`
3. Implement analysis logic in services
4. Add progress tracking and error handling

### Custom Prompts
Modify prompts in `constants.ts`:
- `ANALYSIS_PROMPTS.DATA_SUMMARY`
- `ANALYSIS_PROMPTS.INSIGHTS_GENERATION`
- `ANALYSIS_PROMPTS.VISUALIZATION_SUGGESTIONS`

## Testing

### Manual Testing Checklist
- [ ] Upload CSV file successfully
- [ ] Upload Excel file successfully
- [ ] File validation works (size, type)
- [ ] Analysis pipeline completes
- [ ] Visualizations render correctly
- [ ] Query system responds
- [ ] Error handling works
- [ ] Loading states display
- [ ] Export functionality works

### Automated Tests
Run the test suite:
```bash
npm test src/components/research/data-visualization
```

### Integration Testing
Test with real data:
1. Upload various file formats
2. Test with different data types
3. Verify AI analysis quality
4. Check visualization accuracy
5. Test query responses

## Support

### Getting Help
- Check the README.md for detailed documentation
- Review error messages in browser console
- Test with provided sample data first
- Verify environment configuration

### Reporting Issues
Include in bug reports:
- Browser and version
- File type and size
- Error messages
- Steps to reproduce
- Sample data (if possible)

### Feature Requests
Consider:
- Use case description
- Expected behavior
- Alternative solutions tried
- Impact on existing functionality

## Next Steps

### Recommended Enhancements
1. **Data Export**: CSV/Excel export of analysis results
2. **Dashboard Sharing**: Share visualizations with others
3. **Data Preprocessing**: Built-in data cleaning tools
4. **Advanced Analytics**: Statistical tests, ML models
5. **Real-time Data**: Live data source connections
6. **Collaboration**: Multi-user analysis sessions

### Integration Opportunities
- Connect with existing paper generator
- Link to research analysis tools
- Export to citation manager
- Integration with external APIs

Enjoy exploring your data with AI-powered insights! 🚀
