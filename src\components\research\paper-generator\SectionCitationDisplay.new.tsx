import React, { useState } from 'react';
import { Citation } from './types';
import { ChevronDown, ChevronUp, Book } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SectionCitationDisplayProps {
  sectionId: string;
  sectionName: string;
  allCitations: Citation[];
  sectionCitations: Record<string, string[]>;
  className?: string;
}

/**
 * Component to display citations for a specific section directly under the section content
 */
export function SectionCitationDisplay({
  sectionId,
  sectionName,
  allCitations,
  sectionCitations,
  className = '',
}: SectionCitationDisplayProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Get citation IDs for this section
  const citationIds = sectionCitations[sectionId] || [];
  
  // Find the full citation objects
  const sectionCitationObjects = citationIds
    .map(id => allCitations.find(c => c.id === id))
    .filter(Boolean) as Citation[];
  
  if (sectionCitationObjects.length === 0) {
    return null; // Don't render if there are no citations
  }
  
  // Count citations that have reference text
  const citationsWithReferences = sectionCitationObjects.filter(c => c.referenceText && c.referenceText.trim() !== '');
  
  return (
    <div className={`mt-4 border rounded-lg overflow-hidden ${className}`}>
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 border-b">
        <Button 
          onClick={() => setIsOpen(!isOpen)}
          variant="ghost" 
          className="w-full flex items-center justify-between p-3 h-auto hover:bg-blue-50/50"
        >
          <div className="flex items-center gap-2">
            <Book size={16} className="text-blue-600" />
            <h3 className="text-sm font-medium">
              Section Citations ({sectionCitationObjects.length})
            </h3>
            <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full">
              {citationsWithReferences.length}/{sectionCitationObjects.length} referenced
            </span>
          </div>
          {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
        </Button>
      </div>
      
      {isOpen && (
        <div className="p-3 bg-white">
          <div className="space-y-3 text-sm">
            {sectionCitationObjects.map((citation) => (
              <div 
                key={citation.id} 
                className="bg-white border rounded-lg p-3 hover:shadow transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-blue-700 flex items-center gap-2">
                    <Book size={14} className="text-blue-600" />
                    {citation.inTextFormat}
                  </div>
                  
                  {citation.referenceText ? (
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">Reference found</span>
                  ) : (
                    <span className="text-xs px-2 py-1 bg-amber-100 text-amber-700 rounded-full">Reference missing</span>
                  )}
                </div>
                
                <div className="text-sm text-gray-600 mb-2">
                  Authors: <AUTHORS>
                  Year: <span className="font-medium">{citation.year}</span>
                </div>
                
                {citation.referenceText && (
                  <div className="mt-2 pt-2 border-t border-gray-100">
                    <div className="text-xs font-medium text-gray-500 mb-1 flex items-center gap-1">
                      <ChevronDown size={12} />
                      Full Reference:
                    </div>
                    <div className="text-gray-700 text-xs bg-gray-50 p-2 rounded border-l-2 border-blue-200">
                      {citation.referenceText}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
