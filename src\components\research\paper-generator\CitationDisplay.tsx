import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Users, Calendar, FileText, ExternalLink } from "lucide-react";
import { Citation } from './types';

interface CitationDisplayProps {
  citations: Citation[];
  sectionCitations: Record<string, string[]>;
}

export function CitationDisplay({ citations, sectionCitations }: CitationDisplayProps) {
  if (citations.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Extracted Citations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            No citations have been extracted yet. Citations will appear here as sections are generated.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Group citations by section
  const citationsBySection = Object.entries(sectionCitations).map(([sectionId, citationIds]) => {
    const sectionCitations = citationIds.map(id => 
      citations.find(c => c.id === id)
    ).filter(Boolean) as Citation[];
    
    return {
      sectionId,
      sectionName: sectionId.charAt(0).toUpperCase() + sectionId.slice(1).replace('-', ' '),
      citations: sectionCitations
    };
  }).filter(section => section.citations.length > 0);

  const totalCitations = citations.length;
  const uniqueAuthors = new Set(citations.flatMap(c => c.authors)).size;
  const yearRange = citations.length > 0 ? {
    min: Math.min(...citations.map(c => c.year)),
    max: Math.max(...citations.map(c => c.year))
  } : null;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          Extracted Citations
        </CardTitle>
        <div className="flex gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            {totalCitations} citations
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            {uniqueAuthors} unique authors
          </div>
          {yearRange && (
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {yearRange.min}-{yearRange.max}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {citationsBySection.map(section => (
          <div key={section.sectionId} className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              {section.sectionName}
              <Badge variant="secondary" className="text-xs">
                {section.citations.length}
              </Badge>
            </h4>
            <div className="space-y-1">
              {section.citations.map(citation => (
                <div 
                  key={citation.id} 
                  className="p-3 bg-white rounded-md shadow-sm border border-blue-100 hover:shadow-md transition-all"
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline" className="text-blue-600 bg-blue-50 hover:bg-blue-100">
                      Citation
                    </Badge>
                    <span className="font-medium text-sm">
                      {citation.inTextFormat}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                    <div className="space-y-1">
                      <div className="text-xs text-gray-500 font-medium">Citation Details:</div>
                      <div className="text-muted-foreground text-xs bg-gray-50 p-2 rounded">
                        <div><span className="font-medium">Authors: <AUTHORS>
                        <div><span className="font-medium">Year:</span> {citation.year}</div>
                        {citation.title && <div><span className="font-medium">Title:</span> {citation.title}</div>}
                        {citation.source && <div><span className="font-medium">Source:</span> {citation.source}</div>}
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-xs text-gray-500 font-medium flex items-center justify-between">
                        <span>Reference Format:</span>
                        {citation.referenceText ? (
                          <Badge variant="outline" className="text-green-600 bg-green-50">Found</Badge>
                        ) : (
                          <Badge variant="outline" className="text-amber-600 bg-amber-50">Not matched</Badge>
                        )}
                      </div>
                      
                      {citation.referenceText ? (
                        <div className="text-gray-700 text-xs bg-gray-50 p-2 rounded border-l-2 border-green-300">
                          {citation.referenceText}
                        </div>
                      ) : (
                        <div className="text-gray-500 italic text-xs bg-gray-50 p-2 rounded border-l-2 border-amber-300">
                          No reference found for this citation.
                        </div>
                      )}
                    </div>
                  </div>
                
                  {citation.doi && (
                    <div className="mt-1 flex justify-end">
                      <a 
                        href={`https://doi.org/${citation.doi}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs flex items-center gap-1 text-blue-600 hover:underline"
                      >
                        <ExternalLink size={12} />
                        View DOI
                      </a>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
        
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            These citations will be used to generate the References section automatically.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
