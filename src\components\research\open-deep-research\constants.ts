/**
 * Open Deep Research Constants
 * Configuration constants and default values
 */

import { ModelVariant, SearchConfig } from './types';

// Search Configuration
export const SEARCH_CONFIG: SearchConfig = {
  resultsPerPage: 10,
  maxSelectableResults: 3,
  provider: 'tavily', // Use Tavily for academic search
  safeSearch: {
    google: 'active',
    bing: 'moderate',
  },
  market: 'en-US',
};

// Time Filter Options
export const TIME_FILTERS = [
  { value: 'all', label: 'Any time' },
  { value: '24h', label: 'Past 24 hours' },
  { value: 'week', label: 'Past week' },
  { value: 'month', label: 'Past month' },
  { value: 'year', label: 'Past year' },
] as const;

// OpenRouter AI Model Configuration (Academic Research Focused)
export const AI_MODELS = {
  openrouter: {
    enabled: true,
    models: {
      'anthropic/claude-3.5-sonnet': {
        enabled: true,
        label: 'Claude 3.5 Sonnet',
        value: 'anthropic/claude-3.5-sonnet' as ModelVariant,
      },
      'anthropic/claude-3-haiku': {
        enabled: true,
        label: 'Claude 3 Haiku',
        value: 'anthropic/claude-3-haiku' as ModelVariant,
      },
      'openai/gpt-4o': {
        enabled: true,
        label: 'GPT-4o',
        value: 'openai/gpt-4o' as ModelVariant,
      },
      'openai/gpt-4o-mini': {
        enabled: true,
        label: 'GPT-4o Mini',
        value: 'openai/gpt-4o-mini' as ModelVariant,
      },
      'google/gemini-2.0-flash-001': {
        enabled: true,
        label: 'Gemini 2.0 Flash',
        value: 'google/gemini-2.0-flash-001' as ModelVariant,
      },
      'google/gemini-pro': {
        enabled: true,
        label: 'Gemini Pro',
        value: 'google/gemini-pro' as ModelVariant,
      },
      'meta-llama/llama-3.1-405b-instruct': {
        enabled: true,
        label: 'Llama 3.1 405B',
        value: 'meta-llama/llama-3.1-405b-instruct' as ModelVariant,
      },
      'meta-llama/llama-3.1-70b-instruct': {
        enabled: true,
        label: 'Llama 3.1 70B',
        value: 'meta-llama/llama-3.1-70b-instruct' as ModelVariant,
      },
      'deepseek/deepseek-chat': {
        enabled: true,
        label: 'DeepSeek Chat',
        value: 'deepseek/deepseek-chat' as ModelVariant,
      },
      'qwen/qwen-2.5-72b-instruct': {
        enabled: true,
        label: 'Qwen 2.5 72B',
        value: 'qwen/qwen-2.5-72b-instruct' as ModelVariant,
      },
    },
  },
};

// Default Model
export const DEFAULT_MODEL: ModelVariant = 'google/gemini-2.0-flash-001';

// File Upload Configuration
export const SUPPORTED_FILE_TYPES = '.txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document';

// Rate Limiting Configuration
export const RATE_LIMITS = {
  enabled: true,
  search: 10,
  contentFetch: 20,
  reportGeneration: 5,
  agentOptimizations: 10,
};

// Knowledge Base Configuration
export const KNOWLEDGE_BASE_KEY = 'open_deep_research_knowledge_base';

// Export Configuration
export const EXPORT_FORMATS = [
  { value: 'pdf', label: 'PDF Document' },
  { value: 'docx', label: 'Word Document' },
  { value: 'txt', label: 'Text File' },
] as const;

// API Endpoints
export const API_ENDPOINTS = {
  search: '/api/deep-research/search',
  analyzeResults: '/api/deep-research/analyze-results',
  generateReport: '/api/deep-research/generate-report',
  fetchContent: '/api/deep-research/fetch-content',
  optimizeResearch: '/api/deep-research/optimize-research',
  parseDocument: '/api/deep-research/parse-document',
  export: '/api/deep-research/export',
};

// UI Constants
export const UI_CONSTANTS = {
  maxResultsDisplay: 50,
  reportMaxHeight: '800px',
  sidebarWidth: '400px',
  debounceDelay: 300,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Agent Mode Steps
export const AGENT_STEPS = {
  idle: 'idle',
  processing: 'processing',
  searching: 'searching',
  analyzing: 'analyzing',
  generating: 'generating',
} as const;

// Agent Step Labels
export const AGENT_STEP_LABELS = {
  idle: 'Ready',
  processing: 'Planning Research...',
  searching: 'Searching Web...',
  analyzing: 'Analyzing Results...',
  generating: 'Writing Report...',
};

// Error Messages
export const ERROR_MESSAGES = {
  searchFailed: 'Search failed. Please try again.',
  reportGenerationFailed: 'Report generation failed. Please try again.',
  fileUploadFailed: 'File upload failed. Please check the file format.',
  invalidUrl: 'Please enter a valid URL.',
  noResults: 'No search results found. Please try a different query.',
  rateLimitExceeded: 'Rate limit exceeded. Please try again later.',
  networkError: 'Network error. Please check your connection.',
  unknownError: 'An unexpected error occurred.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  reportGenerated: 'Report generated successfully!',
  reportSaved: 'Report saved to knowledge base!',
  fileUploaded: 'File uploaded successfully!',
  urlAdded: 'URL added successfully!',
  reportExported: 'Report exported successfully!',
};

// Validation Rules
export const VALIDATION = {
  minQueryLength: 3,
  maxQueryLength: 500,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxUrlLength: 2000,
  maxReportPromptLength: 1000,
};
