/**
 * SearchTool - Locates specific content/sections for targeted editing
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { ToolContext, ToolResult, SearchResult, SearchToolResult } from '../types';

export class SearchTool extends BaseAgentTool {
  constructor() {
    super(
      'search-tool',
      'Content Search',
      'Locates specific content sections within the document that match the user request'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { documentContent, userRequest } = context;
    
    console.log(`🔍 [SearchTool] Searching for content related to: "${userRequest}"`);

    try {
      // Extract plain text for analysis
      const plainText = this.extractTextFromHtml(documentContent);
      
      // Analyze the user request to determine search strategy
      const searchStrategy = this.determineSearchStrategy(userRequest);
      console.log(`📋 [SearchTool] Using search strategy: ${searchStrategy.type}`);

      // Execute search based on strategy
      let matches: SearchResult[] = [];
      
      switch (searchStrategy.type) {
        case 'keyword':
          matches = this.keywordSearch(plainText, searchStrategy.keywords);
          break;
        case 'section':
          matches = this.sectionSearch(plainText, searchStrategy.sectionType);
          break;
        case 'semantic':
          matches = this.semanticSearch(plainText, userRequest);
          break;
        case 'position':
          matches = this.positionSearch(plainText, searchStrategy.position);
          break;
        default:
          matches = this.hybridSearch(plainText, userRequest);
      }

      // Filter and rank results
      const filteredMatches = this.filterAndRankMatches(matches, userRequest);
      
      // Calculate confidence based on match quality
      const confidence = this.calculateSearchConfidence(filteredMatches, userRequest);

      const reasoning = this.generateSearchReasoning(
        searchStrategy,
        filteredMatches.length,
        userRequest
      );

      return this.createSuccessResult(
        startTime,
        {
          matches: filteredMatches,
          totalMatches: filteredMatches.length,
          searchStrategy: searchStrategy.type
        } as SearchToolResult['data'],
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Search failed: ${error.message}`,
        ['Try rephrasing your request', 'Check if the document contains relevant content']
      );
    }
  }

  /**
   * Determine the best search strategy based on user request
   */
  private determineSearchStrategy(userRequest: string): {
    type: 'keyword' | 'section' | 'semantic' | 'position' | 'hybrid';
    keywords?: string[];
    sectionType?: string;
    position?: string;
  } {
    const request = userRequest.toLowerCase();

    // Position-based requests
    if (request.includes('line') || request.includes('paragraph') || request.includes('beginning') || request.includes('end')) {
      const positionMatch = request.match(/(line\s+\d+|paragraph\s+\d+|beginning|end|first|last)/);
      return {
        type: 'position',
        position: positionMatch?.[0] || 'unknown'
      };
    }

    // Section-based requests
    const sectionKeywords = {
      'introduction': ['introduction', 'intro', 'opening'],
      'methodology': ['methodology', 'methods', 'approach'],
      'results': ['results', 'findings', 'outcomes'],
      'discussion': ['discussion', 'analysis', 'interpretation'],
      'conclusion': ['conclusion', 'summary', 'closing'],
      'abstract': ['abstract', 'summary'],
      'references': ['references', 'citations', 'bibliography']
    };

    for (const [section, keywords] of Object.entries(sectionKeywords)) {
      if (keywords.some(keyword => request.includes(keyword))) {
        return {
          type: 'section',
          sectionType: section
        };
      }
    }

    // Keyword-based requests
    const keywordPatterns = [
      /fix\s+(\w+)/g,
      /improve\s+(\w+)/g,
      /add\s+(\w+)/g,
      /remove\s+(\w+)/g,
      /replace\s+(\w+)/g
    ];

    const extractedKeywords: string[] = [];
    keywordPatterns.forEach(pattern => {
      const matches = request.matchAll(pattern);
      for (const match of matches) {
        if (match[1]) extractedKeywords.push(match[1]);
      }
    });

    if (extractedKeywords.length > 0) {
      return {
        type: 'keyword',
        keywords: extractedKeywords
      };
    }

    // Default to semantic search
    return { type: 'semantic' };
  }

  /**
   * Perform keyword-based search
   */
  private keywordSearch(text: string, keywords: string[]): SearchResult[] {
    const results: SearchResult[] = [];
    const sentences = this.splitIntoSentences(text);

    keywords.forEach(keyword => {
      sentences.forEach((sentence, index) => {
        if (sentence.toLowerCase().includes(keyword.toLowerCase())) {
          const startPos = this.findSentencePosition(text, sentence);
          results.push({
            text: sentence,
            startPosition: startPos,
            endPosition: startPos + sentence.length,
            relevanceScore: this.calculateKeywordRelevance(sentence, keyword),
            context: this.getContext(sentences, index),
            sectionType: this.detectSectionType(sentence)
          });
        }
      });
    });

    return results;
  }

  /**
   * Perform section-based search
   */
  private sectionSearch(text: string, sectionType: string): SearchResult[] {
    const results: SearchResult[] = [];
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    paragraphs.forEach(paragraph => {
      const detectedType = this.detectSectionType(paragraph);
      if (detectedType === sectionType || this.isSectionMatch(paragraph, sectionType)) {
        const startPos = text.indexOf(paragraph);
        results.push({
          text: paragraph,
          startPosition: startPos,
          endPosition: startPos + paragraph.length,
          relevanceScore: 0.9,
          context: paragraph.substring(0, 200) + '...',
          sectionType: detectedType
        });
      }
    });

    return results;
  }

  /**
   * Perform semantic search (simplified version)
   */
  private semanticSearch(text: string, query: string): SearchResult[] {
    const results: SearchResult[] = [];
    const sentences = this.splitIntoSentences(text);
    const queryWords = query.toLowerCase().split(/\s+/);

    sentences.forEach((sentence, index) => {
      const relevanceScore = this.calculateSemanticRelevance(sentence, queryWords);
      if (relevanceScore > 0.3) {
        const startPos = this.findSentencePosition(text, sentence);
        results.push({
          text: sentence,
          startPosition: startPos,
          endPosition: startPos + sentence.length,
          relevanceScore,
          context: this.getContext(sentences, index),
          sectionType: this.detectSectionType(sentence)
        });
      }
    });

    return results;
  }

  /**
   * Perform position-based search
   */
  private positionSearch(text: string, position: string): SearchResult[] {
    const results: SearchResult[] = [];
    const sentences = this.splitIntoSentences(text);

    if (position.includes('beginning') || position.includes('first')) {
      const firstSentences = sentences.slice(0, 3);
      firstSentences.forEach((sentence, index) => {
        const startPos = this.findSentencePosition(text, sentence);
        results.push({
          text: sentence,
          startPosition: startPos,
          endPosition: startPos + sentence.length,
          relevanceScore: 0.8,
          context: this.getContext(sentences, index),
          sectionType: this.detectSectionType(sentence)
        });
      });
    } else if (position.includes('end') || position.includes('last')) {
      const lastSentences = sentences.slice(-3);
      lastSentences.forEach((sentence, index) => {
        const startPos = this.findSentencePosition(text, sentence);
        results.push({
          text: sentence,
          startPosition: startPos,
          endPosition: startPos + sentence.length,
          relevanceScore: 0.8,
          context: this.getContext(sentences, sentences.length - 3 + index),
          sectionType: this.detectSectionType(sentence)
        });
      });
    }

    return results;
  }

  /**
   * Perform hybrid search combining multiple strategies
   */
  private hybridSearch(text: string, query: string): SearchResult[] {
    const semanticResults = this.semanticSearch(text, query);
    const keywordResults = this.keywordSearch(text, query.split(/\s+/));

    // Also try a simple text search as fallback
    const simpleResults = this.simpleTextSearch(text, query);

    // Combine and deduplicate results
    const allResults = [...semanticResults, ...keywordResults, ...simpleResults];
    const uniqueResults = this.deduplicateResults(allResults);

    console.log(`🔍 [SearchTool] Hybrid search found ${uniqueResults.length} total matches`);
    return uniqueResults;
  }

  /**
   * Simple text search as fallback
   */
  private simpleTextSearch(text: string, query: string): SearchResult[] {
    const results: SearchResult[] = [];
    const sentences = this.splitIntoSentences(text);
    const queryLower = query.toLowerCase();

    sentences.forEach((sentence, index) => {
      const sentenceLower = sentence.toLowerCase();

      // Check if sentence contains any words from the query
      const queryWords = queryLower.split(/\s+/);
      const matchingWords = queryWords.filter(word => sentenceLower.includes(word));

      if (matchingWords.length > 0) {
        const relevanceScore = matchingWords.length / queryWords.length;
        const startPos = this.findSentencePosition(text, sentence);

        results.push({
          text: sentence,
          startPosition: startPos,
          endPosition: startPos + sentence.length,
          relevanceScore: relevanceScore * 0.8, // Slightly lower score for simple search
          context: this.getContext(sentences, index),
          sectionType: this.detectSectionType(sentence)
        });
      }
    });

    return results;
  }

  /**
   * Filter and rank search results for targeted editing
   */
  private filterAndRankMatches(matches: SearchResult[], userRequest: string): SearchResult[] {
    // Remove duplicates and low-relevance matches
    // Lowered threshold to ensure we find content
    const filtered = matches
      .filter(match => match.relevanceScore > 0.1) // Lowered threshold for better recall
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 8); // Increased limit for better coverage

    console.log(`🎯 [SearchTool] Filtered to ${filtered.length} matches (threshold: 0.1) for: "${userRequest}"`);

    // If no matches found, try a more lenient search
    if (filtered.length === 0) {
      console.log(`⚠️ [SearchTool] No matches found, trying fallback search...`);
      const fallbackMatches = matches
        .filter(match => match.relevanceScore > 0.05) // Very low threshold
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, 3);

      console.log(`🔄 [SearchTool] Fallback found ${fallbackMatches.length} matches`);
      return fallbackMatches;
    }

    return filtered;
  }

  /**
   * Calculate search confidence
   */
  private calculateSearchConfidence(matches: SearchResult[], userRequest: string): number {
    if (matches.length === 0) return 0;

    const avgRelevance = matches.reduce((sum, match) => sum + match.relevanceScore, 0) / matches.length;
    const coverageScore = Math.min(matches.length / 3, 1); // Ideal: 3+ matches
    const clarityScore = userRequest.length > 10 ? 0.8 : 0.5;

    return this.calculateConfidence({
      textMatch: avgRelevance,
      contextRelevance: coverageScore,
      requestClarity: clarityScore
    });
  }

  /**
   * Generate reasoning for search results
   */
  private generateSearchReasoning(
    strategy: any,
    matchCount: number,
    userRequest: string
  ): string {
    if (matchCount === 0) {
      return `No relevant content found for "${userRequest}" using ${strategy.type} search strategy`;
    }

    return `Found ${matchCount} relevant section(s) for "${userRequest}" using ${strategy.type} search strategy`;
  }

  // Helper methods
  private splitIntoSentences(text: string): string[] {
    return text.split(/[.!?]+/).filter(s => s.trim().length > 10);
  }

  private findSentencePosition(text: string, sentence: string): number {
    return text.indexOf(sentence.trim());
  }

  private getContext(sentences: string[], index: number): string {
    const start = Math.max(0, index - 1);
    const end = Math.min(sentences.length, index + 2);
    return sentences.slice(start, end).join(' ').substring(0, 300) + '...';
  }

  private detectSectionType(text: string): string {
    const lower = text.toLowerCase();
    if (lower.includes('introduction') || lower.includes('background')) return 'introduction';
    if (lower.includes('method') || lower.includes('approach')) return 'methodology';
    if (lower.includes('result') || lower.includes('finding')) return 'results';
    if (lower.includes('discussion') || lower.includes('analysis')) return 'discussion';
    if (lower.includes('conclusion') || lower.includes('summary')) return 'conclusion';
    return 'other';
  }

  private isSectionMatch(text: string, sectionType: string): boolean {
    return this.detectSectionType(text) === sectionType;
  }

  private calculateKeywordRelevance(sentence: string, keyword: string): number {
    const occurrences = (sentence.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
    return Math.min(occurrences * 0.3, 1);
  }

  private calculateSemanticRelevance(sentence: string, queryWords: string[]): number {
    const sentenceWords = sentence.toLowerCase().split(/\s+/);
    const matches = queryWords.filter(word => sentenceWords.includes(word));
    return matches.length / queryWords.length;
  }

  private deduplicateResults(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = `${result.startPosition}-${result.endPosition}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }
}
