import React from 'react';
import { ChatMessage as ChatMessageType, UploadedFile } from '../../types';
import { VegaLiteRenderer } from './VegaLiteRenderer';
import { getValidVegaSpec } from '../../utils/vega-utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  User, 
  Bot, 
  ThumbsUp, 
  ThumbsDown, 
  Trash2, 
  Copy,
  Download 
} from 'lucide-react';
import { toast } from 'sonner';

interface ChatMessageProps {
  message: ChatMessageType;
  file: UploadedFile;
  onFeedback?: (messageId: string, feedback: 'like' | 'dislike') => void;
  onDelete?: (messageId: string) => void;
  onCopySpec?: (spec: any) => void;
}

export const VizChatMessage: React.FC<ChatMessageProps> = ({
  message,
  file,
  onFeedback,
  onDelete,
  onCopySpec
}) => {
  const isUser = message.role === 'user';
  const vegaSpec = message.vegaSpec || getValidVegaSpec(message.content, file);

  const handleCopyContent = () => {
    navigator.clipboard.writeText(message.content);
    toast.success('Message copied to clipboard');
  };

  const handleCopySpec = () => {
    if (vegaSpec && onCopySpec) {
      onCopySpec(vegaSpec);
      toast.success('Vega-Lite specification copied');
    }
  };

  const handleDownloadSpec = () => {
    if (vegaSpec) {
      const blob = new Blob([JSON.stringify(vegaSpec, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chart-spec-${message.id}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('Specification downloaded');
    }
  };

  return (
    <div className={`flex gap-4 p-4 ${isUser ? 'bg-muted/30' : 'bg-background'}`}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          isUser 
            ? 'bg-primary text-primary-foreground' 
            : 'bg-secondary text-secondary-foreground'
        }`}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-medium text-sm">
            {isUser ? 'You' : 'AI Assistant'}
          </span>
          <span className="text-xs text-muted-foreground">
            {message.timestamp.toLocaleTimeString()}
          </span>
        </div>

        {/* Message Content */}
        {isUser ? (
          <div className="prose prose-sm max-w-none">
            <p className="text-foreground">{message.content}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Text Content */}
            {message.content && !vegaSpec && (
              <div className="prose prose-sm max-w-none">
                <p className="text-foreground">{message.content}</p>
              </div>
            )}

            {/* Visualization */}
            {vegaSpec && (
              <Card>
                <CardContent className="p-4">
                  <VegaLiteRenderer 
                    spec={vegaSpec} 
                    file={file}
                    className="w-full"
                  />
                </CardContent>
              </Card>
            )}

            {/* Error Display */}
            {message.error && (
              <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                <p className="text-destructive text-sm">{message.error}</p>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        {!isUser && (
          <div className="flex items-center gap-2 mt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFeedback?.(message.id, 'like')}
              className="h-8 px-2"
            >
              <ThumbsUp className="w-3 h-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFeedback?.(message.id, 'dislike')}
              className="h-8 px-2"
            >
              <ThumbsDown className="w-3 h-3" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyContent}
              className="h-8 px-2"
            >
              <Copy className="w-3 h-3" />
            </Button>

            {vegaSpec && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopySpec}
                  className="h-8 px-2"
                >
                  <Copy className="w-3 h-3" />
                  <span className="ml-1 text-xs">Spec</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownloadSpec}
                  className="h-8 px-2"
                >
                  <Download className="w-3 h-3" />
                </Button>
              </>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete?.(message.id)}
              className="h-8 px-2 text-destructive hover:text-destructive"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
