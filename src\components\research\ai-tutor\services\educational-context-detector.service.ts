/**
 * Educational Context Detector Service
 * Determines if a query is educational in nature and should be handled by the AI tutor
 */

export interface EducationalContext {
  isEducational: boolean;
  confidence: number;
  subject?: string;
  educationLevel?: string;
  intent: 'learning' | 'explanation' | 'practice' | 'assessment' | 'general' | 'non-educational';
  keywords: string[];
  reasoning: string;
}

export interface IntentClassification {
  intent: string;
  confidence: number;
  category: 'educational' | 'research' | 'general' | 'system';
  shouldUseTutor: boolean;
  suggestedMode: 'tutor' | 'research' | 'general';
}

class EducationalContextDetectorService {
  private educationalKeywords = [
    // Learning verbs
    'learn', 'study', 'understand', 'explain', 'teach', 'show', 'demonstrate',
    'clarify', 'illustrate', 'describe', 'define', 'analyze', 'compare',
    'summarize', 'review', 'practice', 'exercise', 'quiz', 'test', 'explore',
    'discover', 'investigate', 'examine', 'evaluate', 'interpret', 'apply',
    'synthesize', 'create', 'design', 'develop', 'construct', 'build',

    // All Academic Subjects
    'math', 'mathematics', 'algebra', 'geometry', 'calculus', 'statistics', 'trigonometry',
    'physics', 'chemistry', 'biology', 'science', 'astronomy', 'geology', 'ecology',
    'history', 'geography', 'social studies', 'civics', 'government', 'politics',
    'literature', 'english', 'language', 'writing', 'reading', 'grammar', 'poetry',
    'programming', 'computer science', 'technology', 'engineering', 'robotics',
    'economics', 'finance', 'business', 'marketing', 'management', 'accounting',
    'psychology', 'sociology', 'anthropology', 'philosophy', 'ethics', 'logic',
    'art', 'music', 'drama', 'theater', 'dance', 'visual arts', 'design',
    'law', 'legal', 'constitutional', 'criminal', 'civil', 'contract', 'tort',
    'medicine', 'health', 'anatomy', 'physiology', 'nutrition', 'fitness',
    'environmental', 'climate', 'sustainability', 'renewable', 'conservation',
    'cultural', 'diversity', 'multicultural', 'global', 'international',
    'communication', 'journalism', 'media', 'public speaking', 'rhetoric',
    'research', 'methodology', 'analysis', 'data', 'evidence', 'proof',

    // Educational concepts
    'concept', 'theory', 'principle', 'formula', 'equation', 'theorem',
    'hypothesis', 'experiment', 'method', 'process', 'procedure', 'model',
    'framework', 'structure', 'system', 'pattern', 'relationship', 'correlation',
    'example', 'solution', 'problem', 'question', 'answer', 'explanation',
    'definition', 'meaning', 'significance', 'importance', 'relevance',
    'cause', 'effect', 'consequence', 'result', 'outcome', 'impact',
    'factor', 'variable', 'element', 'component', 'aspect', 'dimension',

    // Learning contexts
    'homework', 'assignment', 'project', 'exam', 'test', 'quiz', 'assessment',
    'lesson', 'chapter', 'course', 'class', 'school', 'university', 'college',
    'grade', 'level', 'beginner', 'intermediate', 'advanced', 'basic', 'fundamental',
    'tutorial', 'guide', 'instruction', 'training', 'workshop', 'seminar',
    'curriculum', 'syllabus', 'textbook', 'reference', 'resource', 'material',
    'student', 'teacher', 'professor', 'instructor', 'educator', 'tutor',
    'academic', 'scholarly', 'educational', 'pedagogical', 'didactic',

    // Visualization and interaction terms
    'visualize', 'visualization', 'animate', 'animation', 'interactive', 'simulation',
    'model', 'diagram', 'chart', 'graph', 'plot', 'map', 'timeline', 'flowchart',
    'demonstrate', 'illustrate', 'show', 'display', 'present', 'exhibit'
  ];

  private nonEducationalKeywords = [
    // System/technical
    'api', 'database', 'server', 'deployment', 'configuration', 'settings',
    'login', 'password', 'authentication', 'authorization', 'security',
    
    // Business/commercial
    'price', 'cost', 'buy', 'sell', 'purchase', 'payment', 'invoice',
    'marketing', 'sales', 'revenue', 'profit', 'business',
    
    // Personal/casual
    'weather', 'news', 'sports', 'entertainment', 'movie', 'music',
    'restaurant', 'food', 'recipe', 'travel', 'vacation',
    
    // Research tools
    'search', 'find', 'lookup', 'browse', 'download', 'file',
    'document', 'pdf', 'export', 'import', 'save'
  ];

  private educationalPhrases = [
    'how does', 'what is', 'why does', 'can you explain', 'help me understand',
    'i need to learn', 'teach me', 'show me how', 'what are the steps',
    'give me an example', 'practice problems', 'quiz me on',
    'i don\'t understand', 'can you clarify', 'break it down',
    'step by step', 'in simple terms', 'for beginners'
  ];

  private subjects = {
    'mathematics': ['math', 'algebra', 'geometry', 'calculus', 'statistics', 'trigonometry', 'arithmetic', 'number theory', 'discrete math'],
    'science': ['physics', 'chemistry', 'biology', 'astronomy', 'geology', 'ecology', 'environmental science', 'earth science'],
    'language-arts': ['english', 'grammar', 'writing', 'literature', 'poetry', 'essay', 'rhetoric', 'composition', 'linguistics'],
    'history': ['history', 'historical', 'ancient', 'medieval', 'modern', 'war', 'civilization', 'timeline', 'chronology'],
    'computer-science': ['programming', 'coding', 'algorithm', 'data structure', 'software', 'computer', 'technology', 'ai', 'machine learning'],
    'social-studies': ['geography', 'psychology', 'sociology', 'politics', 'government', 'civics', 'anthropology', 'cultural studies'],
    'economics': ['economics', 'finance', 'market', 'supply', 'demand', 'inflation', 'gdp', 'trade', 'business', 'commerce'],
    'law': ['law', 'legal', 'constitution', 'court', 'justice', 'contract', 'tort', 'criminal', 'civil', 'legislation'],
    'philosophy': ['philosophy', 'ethics', 'moral', 'logic', 'metaphysics', 'epistemology', 'aesthetics', 'political philosophy'],
    'arts': ['art', 'music', 'drama', 'theater', 'dance', 'visual arts', 'design', 'creative', 'aesthetic', 'performance'],
    'health-medicine': ['medicine', 'health', 'anatomy', 'physiology', 'nutrition', 'fitness', 'medical', 'healthcare', 'wellness'],
    'engineering': ['engineering', 'mechanical', 'electrical', 'civil', 'chemical', 'aerospace', 'biomedical', 'industrial'],
    'business': ['business', 'management', 'marketing', 'strategy', 'leadership', 'organization', 'entrepreneurship', 'operations'],
    'communication': ['communication', 'journalism', 'media', 'public speaking', 'broadcasting', 'digital media', 'social media'],
    'environmental': ['environmental', 'climate', 'sustainability', 'renewable', 'conservation', 'green', 'ecosystem', 'biodiversity'],
    'data-science': ['data', 'statistics', 'analysis', 'research', 'methodology', 'probability', 'correlation', 'regression'],
    'foreign-languages': ['spanish', 'french', 'german', 'chinese', 'japanese', 'arabic', 'italian', 'portuguese', 'russian'],
    'religion-theology': ['religion', 'theology', 'spiritual', 'faith', 'belief', 'sacred', 'divine', 'worship', 'scripture'],
    'agriculture': ['agriculture', 'farming', 'crops', 'livestock', 'soil', 'irrigation', 'sustainable farming', 'food production'],
    'architecture': ['architecture', 'building', 'design', 'construction', 'urban planning', 'structural', 'blueprint', 'spatial']
  };

  /**
   * Detects if a query is educational and should be handled by the AI tutor
   */
  async detectEducationalContext(query: string, context?: string): Promise<EducationalContext> {
    const normalizedQuery = query.toLowerCase().trim();
    const normalizedContext = context?.toLowerCase() || '';
    
    // Calculate educational indicators
    const keywordScore = this.calculateKeywordScore(normalizedQuery);
    const phraseScore = this.calculatePhraseScore(normalizedQuery);
    const subjectScore = this.calculateSubjectScore(normalizedQuery);
    const intentScore = this.calculateIntentScore(normalizedQuery);
    const contextScore = this.calculateContextScore(normalizedContext);
    
    // Calculate overall confidence
    const confidence = Math.min(100, 
      (keywordScore * 0.3 + 
       phraseScore * 0.25 + 
       subjectScore * 0.2 + 
       intentScore * 0.15 + 
       contextScore * 0.1) * 100
    );

    // Determine if educational
    const isEducational = confidence >= 60;
    
    // Detect subject
    const subject = this.detectSubject(normalizedQuery);
    
    // Detect education level
    const educationLevel = this.detectEducationLevel(normalizedQuery, normalizedContext);
    
    // Determine intent
    const intent = this.determineIntent(normalizedQuery);
    
    // Extract keywords
    const keywords = this.extractEducationalKeywords(normalizedQuery);
    
    // Generate reasoning
    const reasoning = this.generateReasoning(confidence, keywordScore, phraseScore, subjectScore, intentScore);

    return {
      isEducational,
      confidence: Math.round(confidence),
      subject,
      educationLevel,
      intent,
      keywords,
      reasoning
    };
  }

  /**
   * Classifies the intent of a query to determine which AI mode to use
   */
  async classifyIntent(query: string, context?: string): Promise<IntentClassification> {
    const educationalContext = await this.detectEducationalContext(query, context);
    
    let category: 'educational' | 'research' | 'general' | 'system' = 'general';
    let suggestedMode: 'tutor' | 'research' | 'general' = 'general';
    
    if (educationalContext.isEducational) {
      category = 'educational';
      suggestedMode = 'tutor';
    } else if (this.isResearchQuery(query)) {
      category = 'research';
      suggestedMode = 'research';
    } else if (this.isSystemQuery(query)) {
      category = 'system';
      suggestedMode = 'general';
    }

    return {
      intent: educationalContext.intent,
      confidence: educationalContext.confidence,
      category,
      shouldUseTutor: category === 'educational' && educationalContext.confidence >= 70,
      suggestedMode
    };
  }

  private calculateKeywordScore(query: string): number {
    const words = query.split(/\s+/);
    const educationalMatches = words.filter(word => 
      this.educationalKeywords.some(keyword => word.includes(keyword))
    ).length;
    
    const nonEducationalMatches = words.filter(word => 
      this.nonEducationalKeywords.some(keyword => word.includes(keyword))
    ).length;
    
    const score = (educationalMatches - nonEducationalMatches * 0.5) / words.length;
    return Math.max(0, Math.min(1, score));
  }

  private calculatePhraseScore(query: string): number {
    const matches = this.educationalPhrases.filter(phrase => 
      query.includes(phrase)
    ).length;
    
    return Math.min(1, matches * 0.3);
  }

  private calculateSubjectScore(query: string): number {
    let maxScore = 0;
    
    Object.values(this.subjects).forEach(subjectKeywords => {
      const matches = subjectKeywords.filter(keyword => 
        query.includes(keyword)
      ).length;
      
      const score = matches / subjectKeywords.length;
      maxScore = Math.max(maxScore, score);
    });
    
    return maxScore;
  }

  private calculateIntentScore(query: string): number {
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'which', 'who'];
    const learningVerbs = ['learn', 'understand', 'explain', 'teach', 'show'];
    
    let score = 0;
    
    // Check for question words
    if (questionWords.some(word => query.startsWith(word))) {
      score += 0.4;
    }
    
    // Check for learning verbs
    if (learningVerbs.some(verb => query.includes(verb))) {
      score += 0.6;
    }
    
    return Math.min(1, score);
  }

  private calculateContextScore(context: string): number {
    if (!context) return 0;
    
    const educationalContexts = ['tutor', 'learning', 'education', 'study', 'lesson'];
    const matches = educationalContexts.filter(ctx => context.includes(ctx)).length;
    
    return Math.min(1, matches * 0.2);
  }

  private detectSubject(query: string): string | undefined {
    let bestMatch = '';
    let bestScore = 0;
    
    Object.entries(this.subjects).forEach(([subject, keywords]) => {
      const matches = keywords.filter(keyword => query.includes(keyword)).length;
      const score = matches / keywords.length;
      
      if (score > bestScore) {
        bestScore = score;
        bestMatch = subject;
      }
    });
    
    return bestScore > 0.1 ? bestMatch : undefined;
  }

  private detectEducationLevel(query: string, context: string): string | undefined {
    const levels = {
      'basic': ['basic', 'fundamental', 'introduction', 'beginner', 'simple', 'elementary', 'foundation'],
      'intermediate': ['intermediate', 'moderate', 'standard', 'general', 'comprehensive', 'detailed'],
      'advanced': ['advanced', 'complex', 'sophisticated', 'in-depth', 'specialized', 'university', 'college'],
      'expert': ['expert', 'cutting-edge', 'research', 'scholarly', 'academic', 'professional', 'graduate']
    };

    const combined = `${query} ${context}`;

    for (const [level, keywords] of Object.entries(levels)) {
      if (keywords.some(keyword => combined.includes(keyword))) {
        return level;
      }
    }

    return undefined;
  }

  private determineIntent(query: string): 'learning' | 'explanation' | 'practice' | 'assessment' | 'general' | 'non-educational' {
    if (query.includes('quiz') || query.includes('test') || query.includes('assess')) {
      return 'assessment';
    }
    
    if (query.includes('practice') || query.includes('exercise') || query.includes('problem')) {
      return 'practice';
    }
    
    if (query.includes('explain') || query.includes('what is') || query.includes('how does')) {
      return 'explanation';
    }
    
    if (query.includes('learn') || query.includes('teach') || query.includes('study')) {
      return 'learning';
    }
    
    // Check if it's educational at all
    const educationalScore = this.calculateKeywordScore(query) + this.calculatePhraseScore(query);
    
    return educationalScore > 0.3 ? 'general' : 'non-educational';
  }

  private extractEducationalKeywords(query: string): string[] {
    const words = query.split(/\s+/);
    return words.filter(word => 
      this.educationalKeywords.some(keyword => 
        word.toLowerCase().includes(keyword.toLowerCase())
      )
    );
  }

  private generateReasoning(confidence: number, keywordScore: number, phraseScore: number, subjectScore: number, intentScore: number): string {
    const reasons = [];
    
    if (keywordScore > 0.3) {
      reasons.push('Contains educational keywords');
    }
    
    if (phraseScore > 0.2) {
      reasons.push('Uses educational phrases');
    }
    
    if (subjectScore > 0.2) {
      reasons.push('References academic subjects');
    }
    
    if (intentScore > 0.3) {
      reasons.push('Shows learning intent');
    }
    
    if (reasons.length === 0) {
      return 'No clear educational indicators found';
    }
    
    return `Educational context detected: ${reasons.join(', ')}`;
  }

  private isResearchQuery(query: string): boolean {
    const researchKeywords = ['research', 'find', 'search', 'lookup', 'investigate', 'explore', 'discover'];
    return researchKeywords.some(keyword => query.toLowerCase().includes(keyword));
  }

  private isSystemQuery(query: string): boolean {
    const systemKeywords = ['settings', 'configuration', 'help', 'support', 'account', 'profile'];
    return systemKeywords.some(keyword => query.toLowerCase().includes(keyword));
  }
}

export const educationalContextDetector = new EducationalContextDetectorService();
