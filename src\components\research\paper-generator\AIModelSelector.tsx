import React from 'react';
import { <PERSON>, Chev<PERSON>UpDown, Brain, Zap, Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AIModelOption } from './types';
import { AI_MODELS as ENHANCED_AI_MODELS } from './enhanced-ai.service';

interface AIModelSelectorProps {
  model: string;
  models: AIModelOption[];
  setModel: (value: string) => void;
  className?: string;
}

// Enhanced dropdown with better model information
export function AIModelSelector({ model, models, setModel, className }: AIModelSelectorProps) {
  // Use enhanced AI models for better information
  const enhancedModels = ENHANCED_AI_MODELS;
  const selectedModel = enhancedModels.find(m => m.id === model) || enhancedModels[0];
  
  const handleModelChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setModel(event.target.value);
  };
  
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'fast': return <Zap className="h-4 w-4" />;
      case 'premium': return <Star className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };
  
  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <div className={cn("flex flex-col", className)}>
      <div className="relative">
        <select
          value={model}
          onChange={handleModelChange}
          className="w-full appearance-none bg-white/90 backdrop-blur-sm border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 rounded-xl py-3 px-4 pr-10 font-medium text-gray-700 transition-all duration-200"
        >
          {enhancedModels.map((m) => (
            <option key={m.id} value={m.id}>
              {m.name} ({m.provider}) - {m.cost} cost
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
          <ChevronsUpDown className="h-4 w-4 text-gray-500" />
        </div>
      </div>
      
      {/* Enhanced model details */}
      <div className="mt-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getCategoryIcon(selectedModel.category)}
            <span className="font-semibold text-gray-800">{selectedModel.name}</span>
          </div>
          <Badge className={getCostColor(selectedModel.cost)}>
            {selectedModel.cost} cost
          </Badge>
        </div>
        
        <p className="text-sm text-gray-600 mb-3">{selectedModel.description}</p>
        
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-3">
            <span className="bg-white/80 px-2 py-1 rounded-md font-medium">
              {selectedModel.provider}
            </span>
            <span className="text-gray-500">
              Max: {selectedModel.maxTokens.toLocaleString()} tokens
            </span>
          </div>
          
          {selectedModel.supportsImages && (
            <Badge variant="outline" className="text-xs">
              Image Support
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
}
