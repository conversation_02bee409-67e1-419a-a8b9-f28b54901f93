import { 
  UploadedFile, 
  DataAnalysisResult, 
  VisualizationConfig 
} from '../types';
import { FileProcessingService } from './file-processing.service';

/**
 * Fallback analysis service that works without external APIs
 * Provides basic analysis and visualizations when Gemini API is unavailable
 */
export class FallbackAnalysisService {
  
  /**
   * Perform basic data analysis without AI
   */
  static async analyzeData(file: UploadedFile): Promise<DataAnalysisResult> {
    try {
      console.log('Using fallback analysis (no AI)');
      
      // Detect data types
      const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);
      
      // Calculate basic statistics
      const basicStats = FileProcessingService.calculateBasicStats(file.data, file.headers, dataTypes);
      
      // Count missing values
      const missingValues: Record<string, number> = {};
      file.headers.forEach((header, index) => {
        const columnData = file.data.map(row => row[index]);
        missingValues[header] = columnData.filter(val => val == null || val === '').length;
      });

      // Generate basic insights without AI
      const insights = this.generateBasicInsights(file, dataTypes, basicStats, missingValues);
      
      // Generate visualizations
      const visualizations = this.generateBasicVisualizations(file, dataTypes);

      const result: DataAnalysisResult = {
        id: `fallback-analysis-${file.id}`,
        fileId: file.id,
        summary: {
          rowCount: file.data.length,
          columnCount: file.headers.length,
          dataTypes,
          missingValues,
          basicStats
        },
        insights,
        visualizations,
        generatedAt: new Date()
      };

      return result;
    } catch (error) {
      console.error('Fallback analysis failed:', error);
      throw new Error(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate basic insights without AI
   */
  private static generateBasicInsights(
    file: UploadedFile,
    dataTypes: Record<string, string>,
    basicStats: Record<string, any>,
    missingValues: Record<string, number>
  ) {
    const numericColumns = Object.keys(dataTypes).filter(col => 
      dataTypes[col] === 'number' || dataTypes[col] === 'integer'
    );
    
    const categoricalColumns = Object.keys(dataTypes).filter(col => 
      dataTypes[col] === 'categorical' || dataTypes[col] === 'string'
    );

    const totalMissing = Object.values(missingValues).reduce((sum, count) => sum + count, 0);
    const totalCells = file.data.length * file.headers.length;
    const missingPercentage = ((totalMissing / totalCells) * 100).toFixed(1);

    const keyFindings: string[] = [
      `Dataset contains ${file.data.length} rows and ${file.headers.length} columns`,
      `${numericColumns.length} numeric columns and ${categoricalColumns.length} categorical columns`,
      `${missingPercentage}% of data is missing`
    ];

    // Add findings about numeric data
    if (numericColumns.length > 0) {
      const avgMeans = numericColumns.map(col => basicStats[col]?.mean || 0);
      const highestMean = Math.max(...avgMeans);
      const highestMeanCol = numericColumns[avgMeans.indexOf(highestMean)];
      keyFindings.push(`Highest average value in ${highestMeanCol}: ${highestMean.toFixed(2)}`);
    }

    const patterns: string[] = [];
    
    // Identify patterns
    if (numericColumns.length > 1) {
      patterns.push('Multiple numeric variables available for correlation analysis');
    }
    
    if (categoricalColumns.length > 0 && numericColumns.length > 0) {
      patterns.push('Mixed data types suitable for grouped analysis');
    }

    const correlations: string[] = [];
    if (numericColumns.length > 1) {
      correlations.push('Correlation analysis available between numeric variables');
    }

    const recommendations: string[] = [
      'Review data quality and handle missing values if necessary',
      'Consider data visualization to identify patterns and outliers'
    ];

    if (numericColumns.length > 0) {
      recommendations.push('Explore distribution of numeric variables');
    }

    if (categoricalColumns.length > 0) {
      recommendations.push('Analyze categorical variable frequencies');
    }

    return {
      dataQuality: totalMissing === 0 ? 'Excellent - no missing values' : 
                   totalMissing < totalCells * 0.05 ? 'Good - minimal missing values' :
                   totalMissing < totalCells * 0.15 ? 'Fair - some missing values' :
                   'Poor - significant missing values',
      keyFindings,
      patterns,
      correlations,
      recommendations
    };
  }

  /**
   * Generate basic visualizations without AI
   */
  private static generateBasicVisualizations(
    file: UploadedFile, 
    dataTypes: Record<string, string>
  ): VisualizationConfig[] {
    const visualizations: VisualizationConfig[] = [];
    
    // Find numeric and categorical columns
    const numericColumns = file.headers.filter(header => 
      dataTypes[header] === 'number' || dataTypes[header] === 'integer'
    );
    
    const categoricalColumns = file.headers.filter(header => 
      dataTypes[header] === 'categorical' || dataTypes[header] === 'string'
    );

    // Generate histograms for numeric columns (max 3)
    numericColumns.slice(0, 3).forEach((column, index) => {
      const columnIndex = file.headers.indexOf(column);
      const columnData = file.data.map(row => row[columnIndex]).filter(val => val != null && !isNaN(Number(val)));
      
      if (columnData.length > 0) {
        visualizations.push({
          id: `histogram-${index}`,
          type: 'histogram',
          title: `Distribution of ${column}`,
          description: `Histogram showing the distribution of values in ${column}`,
          data: [{
            x: columnData.map(val => Number(val)),
            type: 'histogram',
            name: column,
            nbinsx: Math.min(30, Math.max(10, Math.floor(Math.sqrt(columnData.length)))),
            marker: { 
              opacity: 0.7,
              color: '#3B82F6'
            }
          }],
          layout: {
            title: `Distribution of ${column}`,
            xaxis: { title: column },
            yaxis: { title: 'Frequency' },
            showlegend: false,
            margin: { t: 60, r: 40, b: 60, l: 60 }
          }
        });
      }
    });

    // Generate scatter plot if we have at least 2 numeric columns
    if (numericColumns.length >= 2) {
      const xCol = numericColumns[0];
      const yCol = numericColumns[1];
      const xIndex = file.headers.indexOf(xCol);
      const yIndex = file.headers.indexOf(yCol);
      
      const xData = file.data.map(row => Number(row[xIndex])).filter(val => !isNaN(val));
      const yData = file.data.map(row => Number(row[yIndex])).filter(val => !isNaN(val));
      
      if (xData.length > 0 && yData.length > 0) {
        visualizations.push({
          id: 'scatter-plot',
          type: 'scatter',
          title: `${yCol} vs ${xCol}`,
          description: `Scatter plot showing the relationship between ${xCol} and ${yCol}`,
          data: [{
            x: xData,
            y: yData,
            type: 'scatter',
            mode: 'markers',
            name: `${yCol} vs ${xCol}`,
            marker: { 
              size: 6, 
              opacity: 0.7,
              color: '#10B981'
            }
          }],
          layout: {
            title: `${yCol} vs ${xCol}`,
            xaxis: { title: xCol },
            yaxis: { title: yCol },
            showlegend: false,
            margin: { t: 60, r: 40, b: 60, l: 60 }
          }
        });
      }
    }

    // Generate bar chart for categorical data
    if (categoricalColumns.length > 0 && numericColumns.length > 0) {
      const catColumn = categoricalColumns[0];
      const numColumn = numericColumns[0];
      
      const aggregatedData = this.aggregateData(file, catColumn, numColumn);
      
      if (aggregatedData.categories.length > 0) {
        visualizations.push({
          id: 'bar-chart',
          type: 'bar_chart',
          title: `Average ${numColumn} by ${catColumn}`,
          description: `Bar chart showing average ${numColumn} grouped by ${catColumn}`,
          data: [{
            x: aggregatedData.categories,
            y: aggregatedData.values,
            type: 'bar',
            name: `Average ${numColumn}`,
            marker: { 
              opacity: 0.8,
              color: '#F59E0B'
            }
          }],
          layout: {
            title: `Average ${numColumn} by ${catColumn}`,
            xaxis: { title: catColumn },
            yaxis: { title: `Average ${numColumn}` },
            showlegend: false,
            margin: { t: 60, r: 40, b: 60, l: 60 }
          }
        });
      }
    }

    return visualizations;
  }

  /**
   * Aggregate data for categorical analysis
   */
  private static aggregateData(file: UploadedFile, catColumn: string, numColumn: string) {
    const catIndex = file.headers.indexOf(catColumn);
    const numIndex = file.headers.indexOf(numColumn);
    
    const groups: Record<string, number[]> = {};
    
    file.data.forEach(row => {
      const category = String(row[catIndex] || 'Unknown');
      const value = Number(row[numIndex]);
      
      if (!isNaN(value)) {
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(value);
      }
    });

    const categories = Object.keys(groups).slice(0, 10); // Limit to 10 categories
    const values = categories.map(cat => {
      const vals = groups[cat];
      return vals.reduce((sum, val) => sum + val, 0) / vals.length; // Average
    });

    return { categories, values };
  }
}
