import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  FileText, 
  Calendar, 
  Star, 
  Eye, 
  Download, 
  Trash2, 
  Filter,
  SortDesc,
  Clock,
  TrendingUp,
  BookOpen,
  Plus
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from 'sonner';
import { articleReviewStorageService, SavedArticleReview } from '../services/article-review-storage.service';
import { articleExportService } from '../services/article-export.service';
import { useAuth } from '@/contexts/AuthContext';

interface ArticleReviewHistoryProps {
  onViewReview: (reviewId: string) => void;
  onCreateNew: () => void;
}

export function ArticleReviewHistory({ onViewReview, onCreateNew }: ArticleReviewHistoryProps) {
  const { user } = useAuth();
  const [reviews, setReviews] = useState<SavedArticleReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'overall_score'>('created_at');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageScore: 0,
    recentReviews: 0
  });

  // Load reviews and stats
  useEffect(() => {
    if (user) {
      loadReviews();
      loadStats();
    }
  }, [user, sortBy]);

  const loadReviews = async () => {
    try {
      setLoading(true);
      const { data, error } = await articleReviewStorageService.getUserArticleReviews(50, 0, sortBy);
      
      if (error) {
        console.error('Error loading reviews:', error);
        toast.error('Failed to load review history');
        return;
      }

      setReviews(data || []);
    } catch (error) {
      console.error('Error loading reviews:', error);
      toast.error('Failed to load review history');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const stats = await articleReviewStorageService.getUserReviewStats();
      if (!stats.error) {
        setStats({
          totalReviews: stats.totalReviews,
          averageScore: Math.round(stats.averageScore),
          recentReviews: stats.recentReviews
        });
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadReviews();
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await articleReviewStorageService.searchArticleReviews(searchQuery);
      
      if (error) {
        console.error('Error searching reviews:', error);
        toast.error('Search failed');
        return;
      }

      setReviews(data || []);
    } catch (error) {
      console.error('Error searching reviews:', error);
      toast.error('Search failed');
    } finally {
      setLoading(false);
    }
  };

  const handleExportReview = async (reviewId: string, title: string) => {
    try {
      const { review, sections, feedback } = await articleReviewStorageService.getArticleReviewById(reviewId);

      if (!review) {
        toast.error('Review not found');
        return;
      }

      articleExportService.downloadAsHTML(review, sections, feedback);
      toast.success('Review exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export review');
    }
  };

  const handleDeleteReview = async (reviewId: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const { error } = await articleReviewStorageService.deleteArticleReview(reviewId);
      
      if (error) {
        console.error('Error deleting review:', error);
        toast.error('Failed to delete review');
        return;
      }

      toast.success('Review deleted successfully');
      loadReviews();
      loadStats();
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };

  const filteredReviews = reviews.filter(review => {
    if (filterStatus !== 'all' && review.review_status !== filterStatus) {
      return false;
    }
    return true;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score?: number) => {
    if (!score) return 'secondary';
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Article Review History
          </h1>
          <p className="text-gray-600 mt-1">
            Manage and review your analyzed articles
          </p>
        </div>
        
        <Button onClick={onCreateNew} className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
          <Plus className="h-4 w-4 mr-2" />
          New Review
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-lg bg-blue-100">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalReviews}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-lg bg-green-100">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Average Score</p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageScore}/100</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="p-2 rounded-lg bg-purple-100">
                <Clock className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Recent (30 days)</p>
                <p className="text-2xl font-bold text-gray-900">{stats.recentReviews}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search reviews by title or content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            
            <Button onClick={handleSearch} variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-[180px]">
                <SortDesc className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Created</SelectItem>
                <SelectItem value="updated_at">Last Updated</SelectItem>
                <SelectItem value="overall_score">Score</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[140px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      {filteredReviews.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery ? 'No reviews match your search criteria.' : 'You haven\'t created any article reviews yet.'}
            </p>
            <Button onClick={onCreateNew} className="bg-gradient-to-r from-blue-600 to-indigo-600">
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Review
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredReviews.map((review) => (
            <Card key={review.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                        {review.title}
                      </h3>
                      <Badge variant={review.review_status === 'completed' ? 'default' : 'secondary'}>
                        {review.review_status}
                      </Badge>
                      {review.overall_score && (
                        <Badge variant={getScoreBadgeVariant(review.overall_score)}>
                          <Star className="h-3 w-3 mr-1" />
                          {Math.round(review.overall_score)}/100
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span className="flex items-center">
                        <FileText className="h-4 w-4 mr-1" />
                        {review.original_filename}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(review.created_at)}
                      </span>
                      {review.word_count && (
                        <span>{review.word_count.toLocaleString()} words</span>
                      )}
                    </div>
                    
                    {review.review_summary && (
                      <p className="text-gray-700 text-sm line-clamp-2 mb-3">
                        {review.review_summary}
                      </p>
                    )}
                    
                    {review.sections_analyzed && review.sections_analyzed.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {review.sections_analyzed.slice(0, 4).map((section) => (
                          <Badge key={section} variant="outline" className="text-xs">
                            {section}
                          </Badge>
                        ))}
                        {review.sections_analyzed.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{review.sections_analyzed.length - 4} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewReview(review.id)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          •••
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onViewReview(review.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleExportReview(review.id, review.title)}>
                          <Download className="h-4 w-4 mr-2" />
                          Export Review
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteReview(review.id, review.title)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
