Directory structure:
└── btahir-open-deep-research/
    ├── README.md
    ├── components.json
    ├── Dockerfile
    ├── env.example
    ├── eslint.config.mjs
    ├── LICENSE
    ├── next.config.ts
    ├── package.json
    ├── postcss.config.mjs
    ├── tailwind.config.ts
    ├── tsconfig.json
    ├── app/
    │   ├── globals.css
    │   ├── layout.tsx
    │   ├── page.tsx
    │   ├── api/
    │   │   ├── analyze-results/
    │   │   │   └── route.ts
    │   │   ├── consolidate-report/
    │   │   │   └── route.ts
    │   │   ├── download/
    │   │   │   └── route.ts
    │   │   ├── fetch-content/
    │   │   │   └── route.ts
    │   │   ├── generate-question/
    │   │   │   └── route.ts
    │   │   ├── optimize-research/
    │   │   │   └── route.ts
    │   │   ├── parse-document/
    │   │   │   └── route.ts
    │   │   ├── report/
    │   │   │   └── route.ts
    │   │   └── search/
    │   │       └── route.ts
    │   ├── flow/
    │   │   └── page.tsx
    │   └── report/
    │       └── [id]/
    │           ├── page.tsx
    │           └── report-content.tsx
    ├── components/
    │   ├── analytics.tsx
    │   ├── citations-footer.tsx
    │   ├── knowledge-base-sidebar.tsx
    │   ├── model-select.tsx
    │   ├── report-actions.tsx
    │   ├── flow/
    │   │   ├── consolidated-edge.tsx
    │   │   ├── project-actions.tsx
    │   │   ├── project-selector.tsx
    │   │   ├── question-node.tsx
    │   │   ├── report-node.tsx
    │   │   ├── search-node.tsx
    │   │   └── selection-node.tsx
    │   └── ui/
    │       ├── alert.tsx
    │       ├── button.tsx
    │       ├── card.tsx
    │       ├── checkbox.tsx
    │       ├── collapsible.tsx
    │       ├── dialog.tsx
    │       ├── dropdown-menu.tsx
    │       ├── input.tsx
    │       ├── scroll-area.tsx
    │       ├── select.tsx
    │       ├── separator.tsx
    │       ├── sheet.tsx
    │       ├── sidebar.tsx
    │       ├── skeleton.tsx
    │       ├── tabs.tsx
    │       ├── toast.tsx
    │       ├── toaster.tsx
    │       └── tooltip.tsx
    ├── docs/
    │   └── flow-projects.md
    ├── hooks/
    │   ├── use-flow-projects.ts
    │   ├── use-knowledge-base.ts
    │   ├── use-mobile.tsx
    │   └── use-toast.ts
    ├── lib/
    │   ├── config.ts
    │   ├── documents.ts
    │   ├── file-upload.ts
    │   ├── gemini.ts
    │   ├── knowledge-base.ts
    │   ├── localStorage-utils.ts
    │   ├── models.ts
    │   ├── redis.ts
    │   └── utils.ts
    ├── types/
    │   └── index.ts
    └── .github/
        └── ISSUE_TEMPLATE/
            ├── config.yml
            └── issue_template.yml

================================================
FILE: README.md
================================================
# Open Deep Research

<div align="center">
  <img src="demo.gif" alt="Open Deep Research Demo" width="800"/>
  <p><em>Note: Demo is sped up for brevity</em></p>
</div>

A powerful open-source research assistant that generates comprehensive AI-powered reports from web search results. Unlike other Deep Research solutions, it provides seamless integration with multiple AI platforms including Google, OpenAI, Anthropic, DeepSeek, and even local models - giving you the freedom to choose the perfect AI model for your specific research requirements.

This app functions in three key steps:

1. **Search Results Retrieval**: Using either Google Custom Search or Bing Search API (configurable), the app fetches comprehensive search results for the specified search term.
2. **Content Extraction**: Leveraging JinaAI, it retrieves and processes the contents of the selected search results, ensuring accurate and relevant information.
3. **Report Generation**: With the curated search results and extracted content, the app generates a detailed report using your chosen AI model (Gemini, GPT-4, Sonnet, etc.), providing insightful and synthesized output tailored to your custom prompts.
4. **Knowledge Base**: Save and access your generated reports in a personal knowledge base for future reference and easy retrieval.

Open Deep Research combines powerful tools to streamline research and report creation in a user-friendly, open-source platform. You can customize the app to your needs (select your preferred search provider, AI model, customize prompts, update rate limits, and configure the number of results both fetched and selected).

## Features

- 🔍 Flexible web search with Google or Bing APIs
- ⏱️ Time-based filtering of search results
- 📄 Content extraction from web pages
- 🤖 Multi-platform AI support (Google Gemini, OpenAI GPT, Anthropic Sonnet)
- 🎯 Flexible model selection with granular configuration
- 📊 Multiple export formats (PDF, Word, Text)
- 🧠 Knowledge Base for saving and accessing past reports
- ⚡ Rate limiting for stability
- 📱 Responsive design

### Local File Support

The app supports analyzing local files for research and report generation. You can:

- Upload TXT, PDF, and DOCX files directly through the interface
- Process local documents alongside web search results
- Generate reports from local files without requiring web search
- Combine insights from both local files and web sources

To use local files:

1. Click the upload button (⬆️) in the search interface
2. Select your file (supported formats: TXT, PDF, DOCX)
3. The file will appear as a custom source in your results
4. Select it and click "Generate Report" to analyze its contents

### Knowledge Base

The Knowledge Base feature allows you to:

- Save generated reports for future reference (reports are saved in the browser's local storage)
- Access your research history
- Quickly load and review past reports
- Build a personal research library over time

### Flow: Deep Research & Report Consolidation

<div align="center">
  <p><a href="https://www.loom.com/share/3c4d9811ac1d47eeaa7a0907c43aef7f">🎥 Watch the full demo video on Loom</a></p>
</div>

The Flow feature enables deep, recursive research by allowing you to:

- Create visual research flows with interconnected reports
- Generate follow-up queries based on initial research findings
- Dive deeper into specific topics through recursive exploration
- Consolidate multiple related reports into comprehensive final reports

Key capabilities:

- 🌳 **Deep Research Trees**: Start with a topic and automatically generate relevant follow-up questions to explore deeper aspects
- 🔄 **Recursive Exploration**: Follow research paths down various "rabbit holes" by generating new queries from report insights
- 🔍 **Visual Research Mapping**: See your entire research journey mapped out visually, showing connections between different research paths
- 🎯 **Smart Query Generation**: AI-powered generation of follow-up research questions based on report content
- 🔗 **Report Consolidation**: Select multiple related reports and combine them into a single, comprehensive final report
- 📊 **Interactive Interface**: Drag, arrange, and organize your research flows visually

The Flow interface makes it easy to:

1. Start with an initial research query
2. Review and select relevant search results
3. Generate detailed reports from selected sources
4. Get AI-suggested follow-up questions for deeper exploration
5. Create new research branches from those questions
6. Finally, consolidate related reports into comprehensive summaries

This feature is perfect for:

- Academic research requiring deep exploration of interconnected topics
- Market research needing multiple angles of investigation
- Complex topic analysis requiring recursive deep dives
- Any research task where you need to "follow the thread" of information

## Configuration

The app's settings can be customized through the configuration file at `lib/config.ts`. Here are the key parameters you can adjust:

### Rate Limits

Control rate limiting and the number of requests allowed per minute for different operations:

```typescript
rateLimits: {
  enabled: true,         // Enable/disable rate limiting (set to false to skip Redis setup)
  search: 5,            // Search requests per minute
  contentFetch: 20,     // Content fetch requests per minute
  reportGeneration: 5,  // Report generation requests per minute
}
```

Note: If you set `enabled: false`, you can run the application without setting up Redis. This is useful for local development or when you don't need rate limiting.

### Search Provider Configuration

The app supports both Google Custom Search and Bing Search APIs. You can configure your preferred search provider in `lib/config.ts`:

```typescript
search: {
  resultsPerPage: 10,
  maxSelectableResults: 3,
  provider: 'google', // 'google' or 'bing'
  safeSearch: {
    google: 'active',  // 'active' or 'off'
    bing: 'moderate'   // 'moderate', 'strict', or 'off'
  },
  market: 'en-US',
}
```

To use Google Custom Search:

1. Get your API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Create a Custom Search Engine and get your CX ID from [Google Programmable Search](https://programmablesearchengine.google.com/)
3. Add the credentials to your `.env.local` file:

```bash
GOOGLE_SEARCH_API_KEY="your-api-key"
GOOGLE_SEARCH_CX="your-cx-id"
```

To use Bing Search:

1. Get your API key from [Azure Portal](https://portal.azure.com/)
2. Add the credential to your `.env.local` file:

```bash
AZURE_SUB_KEY="your-azure-key"
```

### Knowledge Base

The Knowledge Base feature allows you to build a personal research library by:

- Saving generated reports with their original search queries
- Accessing and loading past reports instantly
- Building a searchable archive of your research
- Maintaining context across research sessions

Reports saved to the Knowledge Base include:

- The full report content with all sections
- Original search query and prompt
- Source URLs and references
- Generation timestamp

You can access your Knowledge Base through the dedicated button in the UI, which opens a sidebar containing all your saved reports.

### AI Platform Settings

Configure which AI platforms and models are available. The app supports multiple AI platforms (Google, OpenAI, Anthropic, DeepSeek) with various models for each platform. You can enable/disable platforms and individual models based on your needs:

```typescript
platforms: {
  google: {
    enabled: true,
    models: {
      'gemini-flash': {
        enabled: true,
        label: 'Gemini Flash',
      },
      'gemini-flash-thinking': {
        enabled: true,
        label: 'Gemini Flash Thinking',
      },
      'gemini-exp': {
        enabled: false,
        label: 'Gemini Exp',
      },
    },
  },
  openai: {
    enabled: true,
    models: {
      'gpt-4o': {
        enabled: false,
        label: 'GPT-4o',
      },
      'o1-mini': {
        enabled: false,
        label: 'o1-mini',
      },
      'o1': {
        enabled: false,
        label: 'o1',
      },
    },
  },
  anthropic: {
     enabled: true,
    models: {
      'claude-3-7-sonnet-latest': {
        enabled: false,
        label: 'Claude 3.7 Sonnet',
      },
      'claude-3-5-haiku-latest': {
        enabled: false,
        label: 'Claude 3.5 Haiku',
      },
    },
  },
  deepseek: {
    enabled: true,
    models: {
      'chat': {
        enabled: false,
        label: 'DeepSeek V3',
      },
      'reasoner': {
        enabled: false,
        label: 'DeepSeek R1',
      },
    },
  },
  openrouter: {
    enabled: true,
    models: {
      'openrouter/auto': {
        enabled: false,
        label: 'Auto',
      },
    },
  },
}
```

For each platform:

- `enabled`: Controls whether the platform is available
- For each model:
  - `enabled`: Controls whether the specific model is selectable
  - `label`: The display name shown in the UI

Disabled models will appear grayed out in the UI but remain visible to show all available options. This allows users to see the full range of available models while clearly indicating which ones are currently accessible.

To modify these settings, update the values in `lib/config.ts`. The changes will take effect after restarting the development server.

### OpenRouter Integration

OpenRouter provides access to various AI models through a unified API. By default, it's set to 'auto' mode which automatically selects the most suitable model, but you can configure it to use specific models of your choice by modifying the models section in the configuration.

### Important Note for Reasoning Models

When using advanced reasoning models like OpenAI's o1 or DeepSeek Reasoner, you may need to increase the serverless function duration limit as these models typically take longer to generate comprehensive reports. The default duration might not be sufficient.

For Vercel deployments, you can increase the duration limit in your `vercel.json`:

```json
{
  "functions": {
    "app/api/report/route.ts": {
      "maxDuration": 120
    }
  }
}
```

Or modify the duration in your route file:

```typescript
// In app/api/report/route.ts
export const maxDuration = 120 // Set to 120 seconds or higher
```

Note: The maximum duration limit may vary based on your hosting platform and subscription tier.

### Local Models with Ollama

The app supports local model inference through Ollama integration. You can:

1. Install [Ollama](https://ollama.ai/) on your machine
2. Pull your preferred models using `ollama pull model-name`
3. Configure the model in `lib/config.ts`:

```typescript
platforms: {
  ollama: {
    enabled: true,
    models: {
      'your-model-name': {
        enabled: true,
        label: 'Your Model Display Name'
      }
    }
  }
}
```

Local models through Ollama bypass rate limiting since they run on your machine. This makes them perfect for development, testing, or when you need unlimited generations.

## Getting Started

### Prerequisites

- Node.js 20+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:

```bash
git clone https://github.com/btahir/open-deep-research
cd open-deep-research
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

3. Create a `.env.local` file in the root directory:

```env
# Google Gemini Pro API key (required for AI report generation)
GEMINI_API_KEY=your_gemini_api_key

# OpenAI API key (optional - required only if OpenAI models are enabled)
OPENAI_API_KEY=your_openai_api_key

# Anthropic API key (optional - required only if Anthropic models are enabled)
ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek API key (optional - required only if DeepSeek models are enabled)
DEEPSEEK_API_KEY=your_deepseek_api_key

# OpenRouter API Key (Optional - if using OpenRouter as AI platform)
OPENROUTER_API_KEY="your-openrouter-api-key"

# Upstash Redis (required for rate limiting)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

# Bing Search API (Optional - if using Bing as search provider)
AZURE_SUB_KEY="your-azure-subscription-key"

# Google Custom Search API (Optional - if using Google as search provider)
GOOGLE_SEARCH_API_KEY="your-google-search-api-key"
GOOGLE_SEARCH_CX="your-google-search-cx"

# EXA API Key (Optional - if using EXA as search provider)
EXA_API_KEY="your-exa-api-key"
```

Note: You only need to provide API keys for the platforms you plan to use. If a platform is enabled in the config but its API key is missing, those models will appear disabled in the UI.

### Running the Application

You can run the application either directly on your machine or using Docker.

#### Option 1: Traditional Setup

1. Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

2. Open [http://localhost:3000](http://localhost:3000) in your browser.

#### Option 2: Docker Setup

If you prefer using Docker, you can build and run the application in a container after setting up your environment variables:

1. Build the Docker image:

```bash
docker build -t open-deep-research:v1 .
```

2. Run the container:

```bash
docker run -p 3000:3000 open-deep-research
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Getting API Keys

#### Azure Bing Search API

1. Go to [Azure Portal](https://portal.azure.com)
2. Create a Bing Search resource
3. Get the subscription key from "Keys and Endpoint"

#### Google Custom Search API

You'll need two components to use Google Custom Search:

1. **Get API Key**:

   - Visit [Get a Key](https://developers.google.com/custom-search/v1/introduction) page
   - Follow the prompts to get your API key
   - Copy it for the `GOOGLE_SEARCH_API_KEY` environment variable

2. **Get Search Engine ID (CX)**:
   - Visit [Programmable Search Engine Control Panel](https://programmablesearchengine.google.com/controlpanel/create)
   - Create a new search engine
   - After creation, find your Search Engine ID in the "Overview" page's "Basic" section
   - Copy the ID (this is the `cx` parameter) for the `GOOGLE_SEARCH_CX` environment variable

#### EXA API Key

1. Visit [EXA Platform](https://exa.ai/)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key

#### Google Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Copy the API key

#### OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key

#### Anthropic API Key

1. Visit [Anthropic Console](https://console.anthropic.com)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key

#### DeepSeek API Key

1. Visit [DeepSeek Platform](https://platform.deepseek.com)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key

#### OpenRouter API Key

1. Visit [OpenRouter Platform](https://openrouter.ai/)
2. Sign up or log in to your account
3. Go to API Keys section
4. Create a new API key

#### Upstash Redis

1. Sign up at [Upstash](https://upstash.com)
2. Create a new Redis database
3. Copy the REST URL and REST Token

## Tech Stack

- [Next.js 15](https://nextjs.org/) - React framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [JinaAI](https://jina.ai/) - Content extraction
- [Azure Bing Search](https://www.microsoft.com/en-us/bing/apis/bing-web-search-api) - Web search
- [Google Custom Search](https://developers.google.com/custom-search/v1/overview) - Web search
- [Upstash Redis](https://upstash.com/) - Rate limiting
- [jsPDF](https://github.com/parallax/jsPDF) & [docx](https://github.com/dolanmiu/docx) - Document generation

The app will use the configured provider (default: Google) for all searches. You can switch providers by updating the `provider` value in the config file.

## Demo

Try it out at: [Open Deep Research](https://opendeepresearch.vercel.app/)

## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

## License

[MIT](https://github.com/btahir/open-deep-research/blob/main/LICENSE)

## Acknowledgments

- Inspired by Google's Gemini Deep Research feature
- Built with amazing open source tools and APIs

## Follow Me

If you're interested in following all the random projects I'm working on, you can find me on Twitter:

[![Twitter Follow](https://img.shields.io/twitter/follow/deepwhitman?style=social)](https://x.com/deepwhitman)



================================================
FILE: components.json
================================================
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "app/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  },
  "iconLibrary": "lucide"
}


================================================
FILE: Dockerfile
================================================
# Use Node.js Alpine as the base image
FROM node:20-alpine

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies using npm
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Expose the port the app runs on
EXPOSE 3000

# Start the application
CMD ["npm", "start"]



================================================
FILE: env.example
================================================
# Google Gemini Pro API key (required for AI report generation)
GEMINI_API_KEY=your_gemini_api_key

# OpenAI API key (optional - required only if OpenAI models are enabled)
OPENAI_API_KEY=your_openai_api_key

# Anthropic API key (optional - required only if Anthropic models are enabled)
ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek API key (optional - required only if DeepSeek models are enabled)
DEEPSEEK_API_KEY=your_deepseek_api_key

# Exa API key (Optional - if using Exa as search provider)
EXA_API_KEY=your_exa_api_key

# Upstash Redis (required for rate limiting)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

# Bing Search API (Optional - if using Bing as search provider)
AZURE_SUB_KEY=your-azure-subscription-key

# Google Custom Search API (Optional - if using Google as search provider)
GOOGLE_SEARCH_API_KEY=your-google-search-api-key
GOOGLE_SEARCH_CX=your-google-search-cx 

# Google Analytics Id
NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID=your-google-measurement-id



================================================
FILE: eslint.config.mjs
================================================
import { dirname } from 'path'
import { fileURLToPath } from 'url'
import { FlatCompat } from '@eslint/eslintrc'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      'prefer-rest-params': 'off',
    },
  },
]

export default eslintConfig



================================================
FILE: LICENSE
================================================
MIT License

Copyright (c) 2024 Bilal

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.



================================================
FILE: next.config.ts
================================================
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
};

export default nextConfig;



================================================
FILE: package.json
================================================
{
  "name": "open-deep-research",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@anthropic-ai/sdk": "^0.18.0",
    "@google/generative-ai": "^0.21.0",
    "@radix-ui/react-checkbox": "^1.1.3",
    "@radix-ui/react-collapsible": "^1.1.2",
    "@radix-ui/react-dialog": "^1.1.4",
    "@radix-ui/react-dropdown-menu": "^2.1.4",
    "@radix-ui/react-scroll-area": "^1.2.2",
    "@radix-ui/react-select": "^2.1.4",
    "@radix-ui/react-separator": "^1.1.1",
    "@radix-ui/react-slot": "^1.1.1",
    "@radix-ui/react-tabs": "^1.1.2",
    "@radix-ui/react-toast": "^1.2.4",
    "@radix-ui/react-tooltip": "^1.1.6",
    "@types/markdown-it": "^14.1.2",
    "@upstash/ratelimit": "^2.0.5",
    "@upstash/redis": "^1.34.3",
    "@xyflow/react": "^12.4.3",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "date-fns": "^4.1.0",
    "docx": "^9.1.0",
    "jspdf": "^2.5.2",
    "lucide-react": "^0.469.0",
    "markdown-it": "^14.1.0",
    "next": "15.1.2",
    "officeparser": "^5.1.1",
    "ollama": "^0.5.12",
    "openai": "^4.80.1",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-markdown": "^9.0.1",
    "remark-gfm": "^4.0.0",
    "tailwind-merge": "^2.6.0",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@tailwindcss/typography": "^0.5.15",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^9",
    "eslint-config-next": "15.1.2",
    "postcss": "^8",
    "tailwindcss": "^3.4.1",
    "typescript": "^5"
  }
}



================================================
FILE: postcss.config.mjs
================================================
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
  },
};

export default config;



================================================
FILE: tailwind.config.ts
================================================
import type { Config } from 'tailwindcss'

export default {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
  	extend: {
  		fontFamily: {
  			sans: [
  				'var(--font-geist-sans)'
  			],
  			heading: [
  				'var(--font-zen-dots)'
  			]
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
} satisfies Config



================================================
FILE: tsconfig.json
================================================
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}



================================================
FILE: app/globals.css
================================================
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}



================================================
FILE: app/layout.tsx
================================================
import type { Metadata } from 'next'
import { Geist, Zen_Dots } from 'next/font/google'
import { Toaster } from '@/components/ui/toaster'
import { Analytics } from '@/components/analytics'

import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const zenDots = Zen_Dots({
  variable: '--font-zen-dots',
  weight: ['400'],
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Open Deep Research',
  description:
    'Open source alternative to Deep Research. Generate reports with AI based on search results.',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en'>
      <Analytics />
      <body className={`${geistSans.variable} ${zenDots.variable} antialiased`}>
        {children}
        <Toaster />
      </body>
    </html>
  )
}



================================================
FILE: app/page.tsx
================================================
'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import {
  Search,
  FileText,
  UploadIcon,
  Plus,
  X,
  ChevronDown,
  Brain,
  Code,
  Loader2,
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import type { SearchResult, RankingResult, Status, State } from '@/types'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { CONFIG } from '@/lib/config'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { useToast } from '@/hooks/use-toast'
import { KnowledgeBaseSidebar } from '@/components/knowledge-base-sidebar'
import { ReportActions } from '@/components/report-actions'
import { ModelSelect, DEFAULT_MODEL } from '@/components/model-select'
import { handleLocalFile, SUPPORTED_FILE_TYPES } from '@/lib/file-upload'
import { CitationsFooter } from '@/components/citations-footer'

const timeFilters = [
  { value: 'all', label: 'Any time' },
  { value: '24h', label: 'Past 24 hours' },
  { value: 'week', label: 'Past week' },
  { value: 'month', label: 'Past month' },
  { value: 'year', label: 'Past year' },
] as const

const MAX_SELECTIONS = CONFIG.search.maxSelectableResults

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

const retryWithBackoff = async <T,>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      if (error instanceof Error && error.message.includes('429')) {
        const delay = baseDelay * Math.pow(2, i)
        console.log(`Rate limited, retrying in ${delay}ms...`)
        await sleep(delay)
        continue
      }
      throw error
    }
  }
  throw lastError
}

export default function Home() {
  // Consolidated state management
  const [state, setState] = useState<State>({
    query: '',
    timeFilter: 'all',
    results: [],
    selectedResults: [],
    reportPrompt: '',
    report: null,
    error: null,
    newUrl: '',
    isSourcesOpen: false,
    selectedModel: DEFAULT_MODEL,
    isAgentMode: false,
    sidebarOpen: false,
    activeTab: 'search',
    status: {
      loading: false,
      generatingReport: false,
      agentStep: 'idle',
      fetchStatus: { total: 0, successful: 0, fallback: 0, sourceStatuses: {} },
      agentInsights: [],
      searchQueries: [],
    },
  })

  const { toast } = useToast()

  // Add form ref
  const formRef = useRef<HTMLFormElement>(null)

  // Memoized state update functions
  const updateState = useCallback((updates: Partial<State>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }, [])

  const updateStatus = useCallback(
    (updates: Partial<Status> | ((prev: Status) => Status)) => {
      setState((prev) => {
        const newStatus =
          typeof updates === 'function'
            ? updates(prev.status)
            : { ...prev.status, ...updates }
        return { ...prev, status: newStatus }
      })
    },
    []
  )

  // Memoized error handler
  const handleError = useCallback(
    (error: unknown, context: string) => {
      let message = 'An unexpected error occurred'

      if (error instanceof Error) {
        message = error.message
      } else if (error instanceof Response) {
        // Handle Response objects from fetch
        message = `Server error: ${error.status}`
      } else if (
        typeof error === 'object' &&
        error !== null &&
        'error' in error
      ) {
        // Handle error objects with error message
        message = (error as { error: string }).error
      } else if (typeof error === 'string') {
        message = error
      }

      updateState({ error: message })
      toast({
        title: context,
        description: message,
        variant: 'destructive',
        duration: 5000,
      })
    },
    [toast, updateState]
  )

  // Memoized content fetcher with proper type handling
  const fetchContent = useCallback(async (url: string) => {
    try {
      const response = await fetch('/api/fetch-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url }),
      })

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: 'Failed to fetch content' }))
        throw new Error(
          errorData.error || `Failed to fetch content: ${response.status}`
        )
      }

      const data = await response.json()
      return data
    } catch (error) {
      if (error instanceof Error && error.message.includes('429')) throw error
      console.error('Content fetch error:', error)
      throw error
    }
  }, [])

  // Memoized result selection handler
  const handleResultSelect = useCallback((resultId: string) => {
    setState((prev: State) => {
      if (prev.selectedResults.includes(resultId)) {
        return {
          ...prev,
          selectedResults: prev.selectedResults.filter((id) => id !== resultId),
          reportPrompt:
            prev.selectedResults.length <= 1 ? '' : prev.reportPrompt,
        }
      }
      if (prev.selectedResults.length >= MAX_SELECTIONS) return prev

      const newSelectedResults = [...prev.selectedResults, resultId]
      let newReportPrompt = prev.reportPrompt

      if (
        !prev.isAgentMode &&
        newSelectedResults.length === 1 &&
        !prev.reportPrompt
      ) {
        const result = prev.results.find((r) => r.id === resultId)
        if (result) {
          newReportPrompt = `Analyze and summarize the key points from ${result.name}`
        }
      }

      return {
        ...prev,
        selectedResults: newSelectedResults,
        reportPrompt: newReportPrompt,
      }
    })
  }, [])

  // Memoized search handler
  const handleSearch = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (!state.query.trim()) return

      const isGeneratingReport =
        state.selectedResults.length > 0 && !state.isAgentMode

      if (isGeneratingReport) {
        updateStatus({ generatingReport: true })
        updateState({ error: null })
        const initialFetchStatus: Status['fetchStatus'] = {
          total: state.selectedResults.length,
          successful: 0,
          fallback: 0,
          sourceStatuses: {},
        }
        updateStatus({ fetchStatus: initialFetchStatus })

        try {
          const contentResults = await Promise.all(
            state.results
              .filter((r) => state.selectedResults.includes(r.id))
              .map(async (article) => {
                // If the article already has content (e.g. from file upload), use it directly
                if (article.content) {
                  updateStatus((prev: Status) => ({
                    ...prev,
                    fetchStatus: {
                      ...prev.fetchStatus,
                      successful: prev.fetchStatus.successful + 1,
                      sourceStatuses: {
                        ...prev.fetchStatus.sourceStatuses,
                        [article.url]: 'fetched' as const,
                      },
                    },
                  }))
                  return {
                    url: article.url,
                    title: article.name,
                    content: article.content,
                  }
                }

                try {
                  const { content } = await fetchContent(article.url)
                  if (content) {
                    updateStatus((prev: Status) => ({
                      ...prev,
                      fetchStatus: {
                        ...prev.fetchStatus,
                        successful: prev.fetchStatus.successful + 1,
                        sourceStatuses: {
                          ...prev.fetchStatus.sourceStatuses,
                          [article.url]: 'fetched' as const,
                        },
                      },
                    }))
                    return { url: article.url, title: article.name, content }
                  }
                } catch (error) {
                  if (error instanceof Error && error.message.includes('429'))
                    throw error
                  console.error(
                    'Content fetch error for article:',
                    article.url,
                    error
                  )
                }
                updateStatus((prev: Status) => ({
                  ...prev,
                  fetchStatus: {
                    ...prev.fetchStatus,
                    fallback: prev.fetchStatus.fallback + 1,
                    sourceStatuses: {
                      ...prev.fetchStatus.sourceStatuses,
                      [article.url]: 'preview' as const,
                    },
                  },
                }))
                return {
                  url: article.url,
                  title: article.name,
                  content: article.snippet,
                }
              })
          )

          const response = await retryWithBackoff(async () => {
            const res = await fetch('/api/report', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                selectedResults: contentResults.filter((r) =>
                  r.content?.trim()
                ),
                sources: state.results.filter((r) =>
                  state.selectedResults.includes(r.id)
                ),
                prompt: `${state.query}. Provide comprehensive analysis.`,
                platformModel: state.selectedModel,
              }),
            })

            if (!res.ok) {
              const errorData = await res
                .json()
                .catch(() => ({ error: 'Failed to generate report' }))
              throw new Error(
                errorData.error || `Failed to generate report: ${res.status}`
              )
            }

            return res.json()
          })

          updateState({
            report: response,
            activeTab: 'report',
          })
        } catch (error) {
          handleError(error, 'Report Generation Failed')
        } finally {
          updateStatus({ generatingReport: false })
        }
        return
      }

      updateStatus({ loading: true })
      updateState({ error: null, reportPrompt: '' })

      try {
        const response = await retryWithBackoff(async () => {
          const res = await fetch('/api/search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: state.query,
              timeFilter: state.timeFilter,
            }),
          })

          if (!res.ok) {
            const errorData = await res
              .json()
              .catch(() => ({ error: 'Search failed' }))
            throw new Error(errorData.error || `Search failed: ${res.status}`)
          }

          return res.json()
        })

        const newResults = (response.webPages?.value || []).map(
          (result: SearchResult) => ({
            ...result,
            id: `search-${Date.now()}-${result.id || result.url}`,
          })
        )

        setState((prev) => ({
          ...prev,
          results: [
            ...prev.results.filter(
              (r) => r.isCustomUrl || prev.selectedResults.includes(r.id)
            ),
            ...newResults.filter(
              (newResult: SearchResult) =>
                !prev.results.some((existing) => existing.url === newResult.url)
            ),
          ],
          error: null,
        }))
      } catch (error) {
        handleError(error, 'Search Error')
      } finally {
        updateStatus({ loading: false })
      }
    },
    [
      state.query,
      state.timeFilter,
      state.selectedResults,
      state.selectedModel,
      state.results,
      state.isAgentMode,
      fetchContent,
      handleError,
      updateStatus,
      updateState,
    ]
  )

  // Add effect to handle form submission after query update
  useEffect(() => {
    if (
      state.query === state.reportPrompt &&
      state.reportPrompt &&
      state.selectedResults.length > 0
    ) {
      if (formRef.current) {
        formRef.current.dispatchEvent(
          new Event('submit', { cancelable: true, bubbles: true })
        )
      }
    }
  }, [state.query, state.reportPrompt, state.selectedResults.length])

  const generateReport = useCallback(() => {
    if (!state.reportPrompt || state.selectedResults.length === 0) return
    updateState({ query: state.reportPrompt })
  }, [state.reportPrompt, state.selectedResults.length, updateState])

  // Memoized agent search handler
  const handleAgentSearch = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (!state.reportPrompt.trim()) {
        toast({
          title: 'Missing Information',
          description: 'Please provide a research topic',
          variant: 'destructive',
        })
        return
      }

      updateStatus({
        agentStep: 'processing',
        agentInsights: [],
        searchQueries: [],
      })
      updateState({
        error: null,
        results: [],
        selectedResults: [],
        report: null,
      })

      try {
        // Step 1: Get optimized query and research prompt
        const { query, optimizedPrompt, explanation, suggestedStructure } =
          await retryWithBackoff(async () => {
            const response = await fetch('/api/optimize-research', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                prompt: state.reportPrompt,
                platformModel: state.selectedModel,
              }),
            })
            if (!response.ok) {
              throw new Error(
                `Failed to optimize research: ${response.status} ${response.statusText}`
              )
            }
            return response.json()
          })

        // Update the query state to show optimized query
        updateState({ query: query })

        updateStatus((prev: Status) => ({
          ...prev,
          searchQueries: [query],
          agentInsights: [
            ...prev.agentInsights,
            `Research strategy: ${explanation}`,
            ...(Array.isArray(suggestedStructure)
              ? [`Suggested structure: ${suggestedStructure.join(' → ')}`]
              : []),
          ],
        }))

        // Step 2: Perform search with optimized query
        updateStatus({ agentStep: 'searching' })
        console.log('Performing search with optimized query:', query)
        const searchResponse = await retryWithBackoff(async () => {
          const response = await fetch('/api/search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query,
              timeFilter: state.timeFilter,
              isTestQuery: query.toLowerCase() === 'test',
            }),
          })
          if (!response.ok) {
            const errorData = await response
              .json()
              .catch(() => ({ error: 'Could not parse error response' }))
            console.error('Search failed:', {
              status: response.status,
              query,
              error: errorData,
            })
            if (response.status === 429) {
              throw new Error('Rate limit exceeded')
            }
            if (response.status === 403) {
              throw new Error(
                'Search quota exceeded. Please try again later or contact support.'
              )
            }
            throw new Error('Search failed')
          }
          return response.json()
        })

        const searchResults = searchResponse.webPages?.value || []
        if (searchResults.length === 0) {
          throw new Error(
            'No search results found. Please try a different query.'
          )
        }

        // Process results
        const timestamp = Date.now()
        const allResults = searchResults.map(
          (result: SearchResult, idx: number) => ({
            ...result,
            id: `search-${timestamp}-${idx}-${result.url}`,
            score: 0,
          })
        )

        // Step 3: Analyze and rank results
        updateStatus({ agentStep: 'analyzing' })
        const { rankings, analysis } = await retryWithBackoff(async () => {
          const response = await fetch('/api/analyze-results', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              prompt: optimizedPrompt,
              results: allResults.map((r: SearchResult) => ({
                title: r.name,
                snippet: r.snippet,
                url: r.url,
                content: r.content,
              })),
              isTestQuery: query.toLowerCase() === 'test',
              platformModel: state.selectedModel,
            }),
          })
          if (!response.ok) {
            throw new Error(
              `Failed to analyze results: ${response.status} ${response.statusText}`
            )
          }
          return response.json()
        })

        const rankedResults = allResults
          .map((result: SearchResult) => ({
            ...result,
            score:
              rankings.find((r: RankingResult) => r.url === result.url)
                ?.score || 0,
          }))
          .sort(
            (a: SearchResult, b: SearchResult) =>
              (b.score || 0) - (a.score || 0)
          )

        if (rankedResults.every((r: SearchResult) => r.score === 0)) {
          throw new Error(
            'No relevant results found. Please try a different query.'
          )
        }

        updateStatus((prev: Status) => ({
          ...prev,
          agentInsights: [
            ...prev.agentInsights,
            `Analysis: ${analysis}`,
            `Found ${rankedResults.length} relevant results`,
          ],
        }))

        // Select top results with diversity heuristic
        const selectedUrls = new Set<string>()
        const selected = rankedResults.filter((result: SearchResult) => {
          if (selectedUrls.size >= CONFIG.search.maxSelectableResults)
            return false
          const domain = new URL(result.url).hostname
          const hasSimilar = Array.from(selectedUrls).some(
            (url) => new URL(url).hostname === domain
          )
          if (!hasSimilar && result.score && result.score > 0.5) {
            selectedUrls.add(result.url)
            return true
          }
          return false
        })

        if (selected.length === 0) {
          throw new Error(
            'Could not find enough diverse, high-quality sources. Please try a different query.'
          )
        }

        updateState({
          results: rankedResults,
          selectedResults: selected.map((r: SearchResult) => r.id),
        })

        updateStatus((prev: Status) => ({
          ...prev,
          agentInsights: [
            ...prev.agentInsights,
            `Selected ${selected.length} diverse sources from ${
              new Set(
                selected.map((s: SearchResult) => new URL(s.url).hostname)
              ).size
            } unique domains`,
          ],
        }))

        // Step 4: Generate report
        updateStatus({ agentStep: 'generating' })
        const initialFetchStatus: Status['fetchStatus'] = {
          total: selected.length,
          successful: 0,
          fallback: 0,
          sourceStatuses: {},
        }
        updateStatus({ fetchStatus: initialFetchStatus })

        const contentResults = await Promise.all(
          selected.map(async (article: SearchResult) => {
            // If the article already has content (e.g. from file upload), use it directly
            if (article.content) {
              updateStatus((prev: Status) => ({
                ...prev,
                fetchStatus: {
                  ...prev.fetchStatus,
                  successful: prev.fetchStatus.successful + 1,
                  sourceStatuses: {
                    ...prev.fetchStatus.sourceStatuses,
                    [article.url]: 'fetched' as const,
                  },
                },
              }))
              return {
                url: article.url,
                title: article.name,
                content: article.content,
              }
            }

            try {
              const { content } = await fetchContent(article.url)
              if (content) {
                updateStatus((prev: Status) => ({
                  ...prev,
                  fetchStatus: {
                    ...prev.fetchStatus,
                    successful: prev.fetchStatus.successful + 1,
                    sourceStatuses: {
                      ...prev.fetchStatus.sourceStatuses,
                      [article.url]: 'fetched' as const,
                    },
                  },
                }))
                return { url: article.url, title: article.name, content }
              }
            } catch (error) {
              if (error instanceof Error && error.message.includes('429'))
                throw error
            }
            updateStatus((prev: Status) => ({
              ...prev,
              fetchStatus: {
                ...prev.fetchStatus,
                fallback: prev.fetchStatus.fallback + 1,
                sourceStatuses: {
                  ...prev.fetchStatus.sourceStatuses,
                  [article.url]: 'preview' as const,
                },
              },
            }))
            return {
              url: article.url,
              title: article.name,
              content: article.snippet,
            }
          })
        )

        const reportResponse = await retryWithBackoff(() =>
          fetch('/api/report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              selectedResults: contentResults.filter((r) => r.content?.trim()),
              sources: selected,
              prompt: `${optimizedPrompt}. Provide comprehensive analysis.`,
              platformModel: state.selectedModel,
            }),
          }).then((res) => res.json())
        )

        updateState({
          report: reportResponse,
          activeTab: 'report',
        })

        updateStatus((prev: Status) => ({
          ...prev,
          agentInsights: [
            ...prev.agentInsights,
            `Report generated successfully`,
          ],
          agentStep: 'idle',
        }))
      } catch (error) {
        handleError(error, 'Agent Error')
      }
    },
    [
      state.reportPrompt,
      state.timeFilter,
      generateReport,
      handleError,
      updateState,
      updateStatus,
    ]
  )

  // Memoized utility functions
  const handleAddCustomUrl = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      if (!state.newUrl.trim()) return

      try {
        new URL(state.newUrl) // Validate URL format
        if (!state.results.some((r) => r.url === state.newUrl)) {
          const timestamp = Date.now()
          const newResult: SearchResult = {
            id: `custom-${timestamp}-${state.newUrl}`,
            url: state.newUrl,
            name: 'Custom URL',
            snippet: 'Custom URL added by user',
            isCustomUrl: true,
          }
          setState((prev: State) => ({
            ...prev,
            results: [newResult, ...prev.results],
            newUrl: '',
          }))
        }
      } catch {
        handleError('Please enter a valid URL', 'Invalid URL')
      }
    },
    [state.newUrl, state.results, handleError]
  )

  const handleRemoveResult = useCallback((resultId: string) => {
    setState((prev: State) => ({
      ...prev,
      results: prev.results.filter((r) => r.id !== resultId),
      selectedResults: prev.selectedResults.filter((id) => id !== resultId),
    }))
  }, [])

  // Add file upload handler
  const handleFileUpload = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (!file) return

      const result = await handleLocalFile(
        file,
        (loading) => {
          updateState({ error: null })
          updateStatus({ loading })
        },
        (error, context) => {
          toast({
            title: context,
            description: error instanceof Error ? error.message : String(error),
            variant: 'destructive',
          })
          updateState({
            error: error instanceof Error ? error.message : String(error),
          })
        }
      )

      if (result) {
        setState((prev: State) => ({
          ...prev,
          results: [result, ...prev.results],
        }))
      }

      // Reset the file input
      e.target.value = ''
    },
    [setState, updateState, updateStatus, toast]
  )

  return (
    <div className='min-h-screen bg-white p-4 sm:p-8'>
      <div className='fixed inset-x-0 top-0 bg-blue-50 border-b border-blue-100 p-4 flex flex-col sm:flex-row items-center justify-center gap-4 z-50'>
        <p className='text-blue-800 text-center'>
          <span className='font-semibold'>New:</span> Try our Visual Flow
          feature for deep, recursive research
        </p>
        <Button
          asChild
          variant='default'
          size='sm'
          className='whitespace-nowrap bg-blue-600 hover:bg-blue-700'
        >
          <a href='/flow'>Try Flow →</a>
        </Button>
      </div>
      <div className='pt-20'>
        <KnowledgeBaseSidebar
          open={state.sidebarOpen}
          onOpenChange={(open) => updateState({ sidebarOpen: open })}
        />
        <main className='max-w-4xl mx-auto space-y-8'>
          <div className='mb-3'>
            <h1 className='mb-2 text-center text-gray-800 flex items-center justify-center gap-2'>
              <img
                src='/apple-icon.png'
                alt='Open Deep Research'
                className='w-6 h-6 sm:w-8 sm:h-8 rounded-full'
              />
              <span className='text-xl sm:text-3xl font-bold font-heading'>
                Open Deep Research
              </span>
            </h1>
            <div className='text-center space-y-3 mb-8'>
              <p className='text-gray-600'>
                Open source alternative to Deep Research. Generate reports with
                AI based on search results.
              </p>
              <div className='flex flex-wrap justify-center items-center gap-2'>
                <Button
                  variant='default'
                  size='sm'
                  onClick={() => updateState({ sidebarOpen: true })}
                  className='inline-flex items-center gap-1 sm:gap-2 text-xs sm:text-sm rounded-full'
                >
                  <Brain className='h-4 w-4' />
                  View Knowledge Base
                </Button>
                <Button
                  asChild
                  variant='outline'
                  size='sm'
                  className='inline-flex items-center gap-1 sm:gap-2 text-xs sm:text-sm rounded-full'
                >
                  <a
                    href='https://github.com/btahir/open-deep-research'
                    target='_blank'
                    rel='noopener noreferrer'
                  >
                    <Code className='h-4 w-4' />
                    View Code
                  </a>
                </Button>
              </div>
              <div className='flex justify-center items-center'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='agent-mode'
                    checked={state.isAgentMode}
                    className='w-4 h-4'
                    onCheckedChange={(checked) =>
                      updateState({ isAgentMode: checked as boolean })
                    }
                  />
                  <label
                    htmlFor='agent-mode'
                    className='text-xs sm:text-sm font-medium leading-none text-muted-foreground peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    Agent Mode (Automatic search and report generation)
                  </label>
                </div>
              </div>
            </div>
            {state.status.agentStep !== 'idle' && (
              <div className='mb-4 p-4 bg-blue-50 rounded-lg'>
                <div className='flex items-center gap-3 mb-3'>
                  <Loader2 className='h-5 w-5 text-blue-600 animate-spin' />
                  <h3 className='font-semibold text-blue-800'>
                    Agent Progress
                  </h3>
                </div>

                <div className='space-y-2'>
                  <div className='flex items-center gap-2 text-sm'>
                    <span className='font-medium'>Current Step:</span>
                    <span className='capitalize'>{state.status.agentStep}</span>
                  </div>

                  {state.status.agentInsights.length > 0 && (
                    <Collapsible>
                      <CollapsibleTrigger className='text-sm text-blue-600 hover:underline flex items-center gap-1'>
                        Show Research Details{' '}
                        <ChevronDown className='h-4 w-4' />
                      </CollapsibleTrigger>
                      <CollapsibleContent className='mt-2 space-y-2 text-sm text-gray-600'>
                        {state.status.agentInsights.map((insight, idx) => (
                          <div key={idx} className='flex gap-2'>
                            <span className='text-gray-400'>•</span>
                            {insight}
                          </div>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  )}
                </div>
              </div>
            )}
            <form
              ref={formRef}
              onSubmit={state.isAgentMode ? handleAgentSearch : handleSearch}
              className='space-y-4'
            >
              {!state.isAgentMode ? (
                <>
                  <div className='flex flex-col sm:flex-row gap-2'>
                    <div className='relative flex-1'>
                      <Input
                        type='text'
                        value={state.query}
                        onChange={(e) => updateState({ query: e.target.value })}
                        placeholder='Enter your search query...'
                        className='pr-8'
                      />
                      <Search className='absolute right-2 top-2 h-5 w-5 text-gray-400' />
                    </div>

                    <div className='flex flex-col sm:flex-row gap-2 sm:items-center'>
                      <div className='flex gap-2 w-full sm:w-auto'>
                        <Select
                          value={state.timeFilter}
                          onValueChange={(value) =>
                            updateState({ timeFilter: value })
                          }
                        >
                          <SelectTrigger className='flex-1 sm:flex-initial sm:w-[140px]'>
                            <SelectValue placeholder='Select time range' />
                          </SelectTrigger>
                          <SelectContent>
                            {timeFilters.map((filter) => (
                              <SelectItem
                                key={filter.value}
                                value={filter.value}
                              >
                                {filter.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <ModelSelect
                          value={state.selectedModel}
                          onValueChange={(value) =>
                            updateState({ selectedModel: value })
                          }
                          triggerClassName='flex-1 sm:flex-initial sm:w-[200px]'
                        />
                      </div>

                      <Button
                        type='submit'
                        disabled={state.status.loading}
                        className='w-full sm:w-auto'
                      >
                        {state.status.loading ? 'Searching...' : 'Search'}
                      </Button>
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <Input
                      type='url'
                      value={state.newUrl}
                      onChange={(e) => updateState({ newUrl: e.target.value })}
                      placeholder='Add custom URL...'
                      className='flex-1'
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault()
                          handleAddCustomUrl(e)
                        }
                      }}
                    />
                    <Button
                      type='button'
                      variant='outline'
                      onClick={handleAddCustomUrl}
                      className='hidden sm:inline-flex items-center gap-2'
                    >
                      <Plus className='h-4 w-4' />
                      Add URL
                    </Button>
                    <Button
                      type='button'
                      variant='outline'
                      onClick={handleAddCustomUrl}
                      className='sm:hidden'
                      size='icon'
                    >
                      <Plus className='h-4 w-4' />
                    </Button>
                    <div className='relative'>
                      <Input
                        type='file'
                        onChange={handleFileUpload}
                        className='absolute inset-0 opacity-0 cursor-pointer'
                        accept={SUPPORTED_FILE_TYPES}
                      />
                      <Button
                        type='button'
                        variant='outline'
                        className='hidden sm:inline-flex items-center gap-2 pointer-events-none'
                      >
                        <UploadIcon className='h-4 w-4' />
                        Upload File
                      </Button>
                      <Button
                        type='button'
                        variant='outline'
                        size='icon'
                        className='sm:hidden pointer-events-none'
                      >
                        <UploadIcon className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className='space-y-4 sm:space-y-6 lg:space-y-0'>
                  <div className='flex flex-col sm:flex-row lg:items-center gap-2'>
                    <div className='relative flex-1'>
                      <Input
                        value={state.query || state.reportPrompt}
                        onChange={(e) => {
                          updateState({
                            reportPrompt: e.target.value,
                            query: '',
                          })
                        }}
                        placeholder="What would you like to research? (e.g., 'Tesla Q4 2024 financial performance and market impact')"
                        className='pr-8 text-lg'
                      />
                      <Brain className='absolute right-4 top-3 h-5 w-5 text-gray-400' />
                    </div>
                    <div className='flex flex-col sm:flex-row lg:flex-nowrap gap-2 sm:items-center'>
                      <div className='w-full sm:w-[200px]'>
                        <ModelSelect
                          value={state.selectedModel}
                          onValueChange={(value) =>
                            updateState({ selectedModel: value })
                          }
                          triggerClassName='w-full sm:w-[200px]'
                        />
                      </div>
                      <Button
                        type='submit'
                        disabled={state.status.agentStep !== 'idle'}
                        className='w-full sm:w-auto lg:w-[200px] bg-blue-600 hover:bg-blue-700 text-white whitespace-nowrap'
                      >
                        {state.status.agentStep !== 'idle' ? (
                          <span className='flex items-center gap-2'>
                            <Loader2 className='h-4 w-4 animate-spin' />
                            {
                              {
                                processing: 'Planning Research...',
                                searching: 'Searching Web...',
                                analyzing: 'Analyzing Results...',
                                generating: 'Writing Report...',
                              }[state.status.agentStep]
                            }
                          </span>
                        ) : (
                          'Start Deep Research'
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </form>
          </div>

          <Separator className='my-8' />

          {state.error && (
            <div className='p-4 mb-4 bg-red-50 border border-red-200 rounded-lg'>
              <div className='flex items-center gap-2 text-red-700'>
                <div>
                  <h3 className='font-semibold'>Error</h3>
                  <p className='text-sm'>{state.error}</p>
                </div>
              </div>
            </div>
          )}

          {state.results.length > 0 && (
            <Tabs
              value={state.activeTab}
              onValueChange={(value) => updateState({ activeTab: value })}
              className='w-full'
            >
              <div className='mb-6 space-y-4'>
                {state.selectedResults.length > 0 && !state.isAgentMode && (
                  <div className='flex flex-col sm:flex-row gap-2'>
                    <div className='relative flex-1'>
                      <Input
                        value={state.reportPrompt}
                        onChange={(e) =>
                          updateState({ reportPrompt: e.target.value })
                        }
                        placeholder="What would you like to know about these sources? (e.g., 'Compare and analyze the key points')"
                        className='pr-8'
                      />
                      <FileText className='absolute right-2 top-2.5 h-5 w-5 text-gray-400' />
                    </div>
                    <Button
                      onClick={generateReport}
                      disabled={
                        !state.reportPrompt.trim() ||
                        state.status.generatingReport ||
                        !state.selectedModel
                      }
                      type='button'
                      className='w-full sm:w-auto whitespace-nowrap bg-blue-600 hover:bg-blue-700 text-white'
                    >
                      {state.status.generatingReport ? (
                        <span className='flex items-center gap-2'>
                          <Loader2 className='h-4 w-4 animate-spin' />
                          Generating...
                        </span>
                      ) : (
                        'Generate Report'
                      )}
                    </Button>
                  </div>
                )}
                <div className='text-sm text-gray-600 text-center sm:text-left space-y-1'>
                  <p>
                    {state.selectedResults.length === 0
                      ? 'Select up to 3 results to generate a report'
                      : state.selectedModel
                      ? `${state.selectedResults.length} of ${MAX_SELECTIONS} results selected`
                      : 'Please select a model above to generate a report'}
                  </p>
                  {state.status.generatingReport && (
                    <p>
                      {state.status.fetchStatus.successful} fetched,{' '}
                      {state.status.fetchStatus.fallback} failed (of{' '}
                      {state.status.fetchStatus.total})
                    </p>
                  )}
                </div>
                <TabsList className='grid w-full grid-cols-2 mb-4'>
                  <TabsTrigger value='search'>Search Results</TabsTrigger>
                  <TabsTrigger value='report' disabled={!state.report}>
                    Report
                  </TabsTrigger>
                </TabsList>

                <TabsContent value='search' className='space-y-4'>
                  {!state.isAgentMode &&
                    state.results
                      .filter((r) => r.isCustomUrl)
                      .map((result) => (
                        <Card
                          key={result.id}
                          className='overflow-hidden border-2 border-blue-100'
                        >
                          <CardContent className='p-4 flex gap-4'>
                            <div className='pt-1'>
                              <Checkbox
                                checked={state.selectedResults.includes(
                                  result.id
                                )}
                                onCheckedChange={() =>
                                  handleResultSelect(result.id)
                                }
                                disabled={
                                  !state.selectedResults.includes(result.id) &&
                                  state.selectedResults.length >= MAX_SELECTIONS
                                }
                              />
                            </div>
                            <div className='flex-1 min-w-0'>
                              <div className='flex justify-between items-start'>
                                <a
                                  href={result.url}
                                  target='_blank'
                                  rel='noopener noreferrer'
                                  className='text-blue-600 hover:underline'
                                >
                                  <h2 className='text-xl font-semibold truncate'>
                                    {result.name}
                                  </h2>
                                </a>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() => handleRemoveResult(result.id)}
                                  className='ml-2'
                                >
                                  <X className='h-4 w-4' />
                                </Button>
                              </div>
                              <p className='text-green-700 text-sm truncate'>
                                {result.url}
                              </p>
                              <p className='mt-1 text-gray-600 line-clamp-2'>
                                {result.snippet}
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}

                  {state.results
                    .filter((r) => !r.isCustomUrl)
                    .map((result) => (
                      <Card key={result.id} className='overflow-hidden'>
                        <CardContent className='p-4 flex gap-4'>
                          <div className='pt-1'>
                            <Checkbox
                              checked={state.selectedResults.includes(
                                result.id
                              )}
                              onCheckedChange={() =>
                                handleResultSelect(result.id)
                              }
                              disabled={
                                !state.selectedResults.includes(result.id) &&
                                state.selectedResults.length >= MAX_SELECTIONS
                              }
                            />
                          </div>
                          <div className='flex-1 min-w-0'>
                            <h2 className='text-xl font-semibold truncate text-blue-600 hover:underline'>
                              <a
                                href={result.url}
                                target='_blank'
                                rel='noopener noreferrer'
                                dangerouslySetInnerHTML={{
                                  __html: result.name,
                                }}
                              />
                            </h2>
                            <p className='text-green-700 text-sm truncate'>
                              {result.url}
                            </p>
                            <p
                              className='mt-1 text-gray-600 line-clamp-2'
                              dangerouslySetInnerHTML={{
                                __html: result.snippet,
                              }}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </TabsContent>

                <TabsContent value='report'>
                  {state.report && (
                    <Card>
                      <CardContent className='p-6 space-y-4'>
                        <div className='flex flex-col-reverse sm:flex-row sm:justify-between sm:items-start gap-4'>
                          <h2 className='text-2xl font-bold text-gray-800 text-center sm:text-left'>
                            {state.report?.title}
                          </h2>
                          <ReportActions
                            report={state.report}
                            prompt={state.reportPrompt}
                          />
                        </div>
                        
                        {/* Scrollable content area with proper height constraint */}
                        <div className='max-h-[800px] overflow-y-auto pr-2' style={{ scrollbarWidth: 'thin' }}>
                          <p className='text-lg text-gray-700 mb-6'>
                            {state.report?.summary}
                          </p>
                          
                          {state.report?.sections?.map((section, index) => (
                            <div key={index} className='space-y-3 border-t pt-4 mb-6'>
                              <h3 className='text-xl font-semibold text-gray-700'>
                                {section.title}
                              </h3>
                              <div className='prose max-w-none text-gray-600'>
                                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                  {section.content}
                                </ReactMarkdown>
                              </div>
                            </div>
                          ))}
                          
                          {/* Citations Section */}
                          {state.report && <CitationsFooter report={state.report} />}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
              </div>
            </Tabs>
          )}
        </main>
      </div>
    </div>
  )
}



================================================
FILE: app/api/analyze-results/route.ts
================================================
import { NextResponse } from 'next/server'
import { reportContentRatelimit } from '@/lib/redis'
import { CONFIG } from '@/lib/config'
import { extractAndParseJSON } from '@/lib/utils'
import { generateWithModel } from '@/lib/models'
import { type ModelVariant } from '@/types'

type SearchResultInput = {
  title: string
  snippet: string
  url: string
  content?: string
}

export async function POST(request: Request) {
  try {
    const {
      prompt,
      results,
      isTestQuery = false,
      platformModel = 'google__gemini-flash',
    } = (await request.json()) as {
      prompt: string
      results: SearchResultInput[]
      isTestQuery?: boolean
      platformModel: ModelVariant
    }

    if (!prompt || !results?.length) {
      return NextResponse.json(
        { error: 'Prompt and results are required' },
        { status: 400 }
      )
    }

    // Return test results for test queries
    if (
      isTestQuery ||
      results.some((r) => r.url.includes('example.com/test'))
    ) {
      return NextResponse.json({
        rankings: results.map((result, index) => ({
          url: result.url,
          score: index === 0 ? 1 : 0.5, // Give first result highest score
          reasoning: 'Test ranking result',
        })),
        analysis: 'Test analysis of search results',
      })
    }

    // Only check rate limit if enabled and not using Ollama (local model)
    const platform = platformModel.split('__')[0]
    const model = platformModel.split('__')[1]
    if (CONFIG.rateLimits.enabled && platform !== 'ollama') {
      const { success } = await reportContentRatelimit.limit(
        'agentOptimizations'
      )
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    // Check if selected platform is enabled
    const platformConfig =
      CONFIG.platforms[platform as keyof typeof CONFIG.platforms]
    if (!platformConfig?.enabled) {
      return NextResponse.json(
        { error: `${platform} platform is not enabled` },
        { status: 400 }
      )
    }

    // Check if selected model exists and is enabled
    const modelConfig = (platformConfig as any).models[model]
    if (!modelConfig) {
      return NextResponse.json(
        { error: `${model} model does not exist` },
        { status: 400 }
      )
    }
    if (!modelConfig.enabled) {
      return NextResponse.json(
        { error: `${model} model is disabled` },
        { status: 400 }
      )
    }

    const systemPrompt = `You are a research assistant tasked with analyzing search results for relevance to a research topic.

Research Topic: "${prompt}"

Analyze these search results and score them based on:
1. Relevance to the research topic
2. Information quality and depth
3. Source credibility
4. Uniqueness of perspective

For each result, assign a score from 0 to 1, where:
- 1.0: Highly relevant, authoritative, and comprehensive
- 0.7-0.9: Very relevant with good information
- 0.4-0.6: Moderately relevant or basic information
- 0.1-0.3: Tangentially relevant
- 0.0: Not relevant or unreliable

Here are the results to analyze:

${results
  .map(
    (result, index) => `
Result ${index + 1}:
Title: ${result.title}
URL: ${result.url}
Snippet: ${result.snippet}
${result.content ? `Full Content: ${result.content}` : ''}
---`
  )
  .join('\n')}

Format your response as a JSON object with this structure:
{
  "rankings": [
    {
      "url": "result url",
      "score": 0.85,
      "reasoning": "Brief explanation of the score"
    }
  ],
  "analysis": "Brief overall analysis of the result set"
}

Focus on finding results that provide unique, high-quality information relevant to the research topic.`

    try {
      const response = await generateWithModel(systemPrompt, platformModel)

      if (!response) {
        throw new Error('No response from model')
      }

      try {
        const parsedResponse = extractAndParseJSON(response)
        return NextResponse.json(parsedResponse)
      } catch (parseError) {
        console.error('Failed to parse analysis:', parseError)
        return NextResponse.json(
          { error: 'Failed to analyze results' },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error('Model generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate analysis' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Result analysis failed:', error)
    return NextResponse.json(
      { error: 'Failed to analyze results' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/consolidate-report/route.ts
================================================
import { NextResponse } from 'next/server'
import { CONFIG } from '@/lib/config'
import { generateWithModel } from '@/lib/models'
import type { Report } from '@/types'
import { reportContentRatelimit } from '@/lib/redis'

export async function POST(request: Request) {
  try {
    const { reports, platformModel } = await request.json()
    const [platform, model] = platformModel.split('__')

    if (CONFIG.rateLimits.enabled && platform !== 'ollama') {
      const { success } = await reportContentRatelimit.limit('report')
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    console.log('Consolidating reports:', {
      numReports: reports.length,
      reportTitles: reports.map((r: Report) => r.title),
      platform,
      model,
    })

    if (!reports?.length) {
      return NextResponse.json(
        { error: 'Reports are required' },
        { status: 400 }
      )
    }

    // Collect all unique sources from all reports
    const allSources: { id: string; url: string; name: string }[] = []
    const sourceMap = new Map<string, number>() // Maps source id to index in allSources

    reports.forEach((report: Report) => {
      if (report.sources && report.sources.length > 0) {
        report.sources.forEach((source) => {
          if (!sourceMap.has(source.id)) {
            sourceMap.set(source.id, allSources.length)
            allSources.push(source)
          }
        })
      }
    })

    // Create source index for citations
    const sourceIndex = allSources
      .map(
        (source, index) =>
          `[${index + 1}] Source: ${source.name} - ${source.url}`
      )
      .join('\n')

    const prompt = `Create a comprehensive consolidated report that synthesizes the following research reports:

${reports
  .map(
    (report: Report, index: number) => `
Report ${index + 1} Title: ${report.title}
Report ${index + 1} Summary: ${report.summary}
Key Findings:
${report.sections
  ?.map((section) => `- ${section.title}: ${section.content}`)
  .join('\n')}
`
  )
  .join('\n\n')}

Sources for citation:
${sourceIndex}

Analyze and synthesize these reports to create a comprehensive consolidated report that:
1. Identifies common themes and patterns across the reports
2. Highlights key insights and findings
3. Shows how different reports complement or contrast each other
4. Draws overarching conclusions
5. Suggests potential areas for further research
6. Uses citations only when necessary to reference specific claims, statistics, or quotes from sources

Format the response as a structured report with:
- A clear title that encompasses the overall research topic
- An executive summary of the consolidated findings
- Detailed sections that analyze different aspects
- A conclusion that ties everything together
- Judicious use of citations in superscript format [¹], [²], etc. ONLY when necessary

Return the response in the following JSON format:
{
  "title": "Overall Research Topic Title",
  "summary": "Executive summary of findings",
  "sections": [
    {
      "title": "Section Title",
      "content": "Section content with selective citations"
    }
  ],
  "usedSources": [1, 2] // Array of source numbers that were actually cited in the report
}

CITATION GUIDELINES:
1. Only use citations when truly necessary - specifically for:
   - Direct quotes from sources
   - Specific statistics, figures, or data points
   - Non-obvious facts or claims that need verification
   - Controversial statements
   
2. DO NOT use citations for:
   - General knowledge
   - Your own analysis or synthesis of information
   - Widely accepted facts
   - Every sentence or paragraph

3. When needed, use superscript citation numbers in square brackets [¹], [²], etc. at the end of the relevant sentence
   
4. The citation numbers correspond directly to the source numbers provided in the list
   
5. Be judicious and selective with citations - a well-written report should flow naturally with citations only where they truly add credibility

6. You DO NOT need to cite every source provided. Only cite the sources that contain information directly relevant to the report. Track which sources you actually cite and include their numbers in the "usedSources" array in the output JSON.

7. It's completely fine if some sources aren't cited at all - this means they weren't needed for the specific analysis requested.`

    console.log('Generated prompt:', prompt)

    try {
      const response = await generateWithModel(prompt, platformModel)

      if (!response) {
        throw new Error('No response from model')
      }

      console.log('Model response:', response)

      // Try to parse the response as JSON, if it's not already
      let parsedResponse
      try {
        parsedResponse =
          typeof response === 'string' ? JSON.parse(response) : response
        console.log('Parsed response:', parsedResponse)
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError)
        // If it's not JSON, create a basic report structure
        parsedResponse = {
          title: 'Consolidated Research Report',
          summary: response.split('\n\n')[0] || 'Summary not available',
          sections: [
            {
              title: 'Findings',
              content: response,
            },
          ],
        }
      }

      // Add sources to the response
      parsedResponse.sources = allSources

      return NextResponse.json(parsedResponse)
    } catch (error) {
      console.error('Model generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate consolidated report' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Consolidation error:', error)
    return NextResponse.json(
      { error: 'Failed to consolidate reports' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/download/route.ts
================================================
import { NextResponse } from 'next/server'
import { generateDocx, generatePdf } from '@/lib/documents'
import { type Report } from '@/types'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    console.log('Download request received:', {
      format: body.format,
      reportTitle: body.report?.title
    })

    const { report, format } = body as { report: Report; format: 'pdf' | 'docx' | 'txt' }

    let content: string | Buffer
    const headers = new Headers()

    switch (format) {
      case 'pdf':
        console.log('Generating PDF')
        content = await generatePdf(report)
        headers.set('Content-Type', 'application/pdf')
        break

      case 'docx':
        console.log('Generating DOCX')
        content = await generateDocx(report)
        headers.set(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        break

      case 'txt':
      default:
        console.log('Generating TXT')
        content = `
${report.title}

${report.summary}

${report.sections
  .map(
    (section) => `
${section.title}
${section.content}
`
  )
  .join('\n')}
`.trim()
        headers.set('Content-Type', 'text/plain')
        break
    }

    console.log(`Generated ${format} content, size:`, content.length)
    headers.set('Content-Disposition', `attachment; filename=report.${format}`)

    return new Response(content, {
      headers,
      status: 200,
    })
  } catch (error) {
    console.error('Download generation error:', error)
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
    }
    return NextResponse.json(
      { error: 'Failed to generate download' },
      { status: 500 }
    )
  }
} 


================================================
FILE: app/api/fetch-content/route.ts
================================================
import { NextResponse } from 'next/server'
import { fetchContentRatelimit } from '@/lib/redis'
import { CONFIG } from '@/lib/config'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { url } = body

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 })
    }

    // Only check rate limit if enabled
    if (CONFIG.rateLimits.enabled) {
      const { success } = await fetchContentRatelimit.limit(url)
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    console.log('Fetching content for URL:', url)

    try {
      const response = await fetch(
        `https://r.jina.ai/${encodeURIComponent(url)}`
      )

      if (!response.ok) {
        console.warn(`Failed to fetch content for ${url}:`, response.status)
        return NextResponse.json(
          { error: 'Failed to fetch content' },
          { status: response.status }
        )
      }

      const content = await response.text()
      return NextResponse.json({ content })
    } catch (error) {
      console.warn(`Error fetching content for ${url}:`, error)
      return NextResponse.json(
        { error: 'Failed to fetch content' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Content fetching error:', error)
    return NextResponse.json({ error: 'Invalid request' }, { status: 400 })
  }
}



================================================
FILE: app/api/generate-question/route.ts
================================================
import { NextResponse } from 'next/server'
import { CONFIG } from '@/lib/config'
import { generateWithModel } from '@/lib/models'
import { reportContentRatelimit } from '@/lib/redis'
export async function POST(request: Request) {
  try {
    const { report, platformModel } = await request.json()
    const platform = platformModel.split('__')[0]

    if (CONFIG.rateLimits.enabled && platform !== 'ollama') {
      const { success } = await reportContentRatelimit.limit(
        'agentOptimizations'
      )
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    if (!report) {
      return NextResponse.json({ error: 'Report is required' }, { status: 400 })
    }

    const prompt = `Based on the following research report, generate 3 focused search terms or phrases for further research. These should be concise keywords or phrases that would help explore important aspects not fully covered in the current report.

Report Title: ${report.title}
Summary: ${report.summary}

Key Sections:
${report.sections
  ?.map(
    (section: { title: string; content: string }) =>
      `${section.title}: ${section.content}`
  )
  .join('\n')}

Generate exactly 3 search terms and return them in the following JSON format:
{
  "searchTerms": [
    "first search term",
    "second search term",
    "third search term"
  ]
}

The search terms should be specific and focused on unexplored aspects of the topic.`

    try {
      const response = await generateWithModel(prompt, platformModel)

      if (!response) {
        throw new Error('No response from model')
      }

      try {
        // Parse the JSON response
        const jsonResponse = JSON.parse(response)
        if (
          !Array.isArray(jsonResponse.searchTerms) ||
          jsonResponse.searchTerms.length !== 3
        ) {
          throw new Error('Invalid search terms format')
        }

        return NextResponse.json({ searchTerms: jsonResponse.searchTerms })
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError)
        // Fallback to line-based parsing if JSON parsing fails
        const searchTerms = response
          .split('\n')
          .map((term) => term.trim())
          .filter(
            (term) =>
              term.length > 0 && !term.includes('{') && !term.includes('}')
          )
          .slice(0, 3)

        return NextResponse.json({ searchTerms })
      }
    } catch (error) {
      console.error('Model generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate search terms' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Search terms generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate search terms' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/optimize-research/route.ts
================================================
import { NextResponse } from 'next/server'
import { reportContentRatelimit } from '@/lib/redis'
import { CONFIG } from '@/lib/config'
import { extractAndParseJSON } from '@/lib/utils'
import { generateWithModel } from '@/lib/models'
import { type ModelVariant } from '@/types'

export async function POST(request: Request) {
  try {
    const { prompt, platformModel = 'google__gemini-flash' } =
      (await request.json()) as {
        prompt: string
        platformModel: ModelVariant
      }

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 })
    }

    // Return test results for test queries
    if (prompt.toLowerCase() === 'test') {
      return NextResponse.json({
        query: 'test',
        optimizedPrompt:
          'Analyze and compare different research methodologies, focusing on scientific rigor, peer review processes, and validation techniques',
        explanation: 'Test optimization strategy',
        suggestedStructure: [
          'Test Structure 1',
          'Test Structure 2',
          'Test Structure 3',
        ],
      })
    }

    // Only check rate limit if enabled and not using Ollama (local model)
    const platform = platformModel.split('__')[0]
    const model = platformModel.split('__')[1]
    if (CONFIG.rateLimits.enabled && platform !== 'ollama') {
      const { success } = await reportContentRatelimit.limit(
        'agentOptimizations'
      )
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    // Check if selected platform is enabled
    const platformConfig =
      CONFIG.platforms[platform as keyof typeof CONFIG.platforms]
    if (!platformConfig?.enabled) {
      return NextResponse.json(
        { error: `${platform} platform is not enabled` },
        { status: 400 }
      )
    }

    // Check if selected model exists and is enabled
    const modelConfig = (platformConfig as any).models[model]
    if (!modelConfig) {
      return NextResponse.json(
        { error: `${model} model does not exist` },
        { status: 400 }
      )
    }
    if (!modelConfig.enabled) {
      return NextResponse.json(
        { error: `${model} model is disabled` },
        { status: 400 }
      )
    }

    const systemPrompt = `You are a research assistant tasked with optimizing a research topic into an effective search query.

Given this research topic: "${prompt}"

Your task is to:
1. Generate ONE optimized search query that will help gather comprehensive information
2. Create an optimized research prompt that will guide the final report generation
3. Suggest a logical structure for organizing the research

The query should:
- Cover the core aspects of the topic
- Use relevant technical terms and synonyms
- Be specific enough to return high-quality results
- Be comprehensive yet concise

Format your response as a JSON object with this structure:
{
  "query": "the optimized search query",
  "optimizedPrompt": "The refined research prompt that will guide report generation",
  "explanation": "Brief explanation of the optimization strategy",
  "suggestedStructure": [
    "Key aspect 1 to cover",
    "Key aspect 2 to cover",
    "Key aspect 3 to cover"
  ]
}

Make the query clear and focused, avoiding overly complex or lengthy constructions.`

    try {
      const response = await generateWithModel(systemPrompt, platformModel)

      if (!response) {
        throw new Error('No response from model')
      }

      try {
        const parsedResponse = extractAndParseJSON(response)
        return NextResponse.json(parsedResponse)
      } catch (parseError) {
        console.error('Failed to parse optimization:', parseError)
        return NextResponse.json(
          { error: 'Failed to optimize research' },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error('Model generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate optimization' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Research optimization failed:', error)
    return NextResponse.json(
      { error: 'Failed to optimize research' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/parse-document/route.ts
================================================
import { NextResponse } from 'next/server'
import { parseOfficeAsync } from 'officeparser'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    try {
      // Convert the file to a Buffer
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // Configure officeparser
      const config = {
        outputErrorToConsole: false,
        newlineDelimiter: '\n',
        ignoreNotes: false,
        putNotesAtLast: false,
      }

      // Parse the document
      const content = await parseOfficeAsync(buffer, config)
      return NextResponse.json({ content })
    } catch (error) {
      console.error('Content extraction error:', error)
      return NextResponse.json(
        { error: 'Failed to extract content from document' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Document parsing error:', error)
    return NextResponse.json(
      { error: 'Failed to parse document' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/report/route.ts
================================================
import { NextResponse } from 'next/server'
import { reportContentRatelimit } from '@/lib/redis'
import { type Article, type ModelVariant } from '@/types'
import { CONFIG } from '@/lib/config'
import { extractAndParseJSON } from '@/lib/utils'
import { generateWithModel } from '@/lib/models'

export const maxDuration = 60

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      selectedResults,
      sources,
      prompt,
      platformModel = 'google-gemini-flash',
    } = body as {
      selectedResults: Article[]
      sources: any[]
      prompt: string
      platformModel: ModelVariant
    }

    // Only check rate limit if enabled and not using Ollama (local model)
    const platform = platformModel.split('__')[0]
    const model = platformModel.split('__')[1]
    if (CONFIG.rateLimits.enabled && platform !== 'ollama') {
      const { success } = await reportContentRatelimit.limit('report')
      if (!success) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        )
      }
    }

    // Check if selected platform is enabled
    const platformConfig =
      CONFIG.platforms[platform as keyof typeof CONFIG.platforms]
    if (!platformConfig?.enabled) {
      return NextResponse.json(
        { error: `${platform} platform is not enabled` },
        { status: 400 }
      )
    }

    // Check if selected model exists and is enabled
    const modelConfig = (platformConfig as any).models[model]
    if (!modelConfig) {
      return NextResponse.json(
        { error: `${model} model does not exist` },
        { status: 400 }
      )
    }
    if (!modelConfig.enabled) {
      return NextResponse.json(
        { error: `${model} model is disabled` },
        { status: 400 }
      )
    }

    const generateSystemPrompt = (articles: Article[], userPrompt: string) => {
      return `You are a research assistant tasked with creating a comprehensive report based on multiple sources. 
The report should specifically address this request: "${userPrompt}"

Your report should:
1. Have a clear title that reflects the specific analysis requested
2. Begin with a concise executive summary
3. Be organized into relevant sections based on the analysis requested
4. Use markdown formatting for emphasis, lists, and structure
5. Use citations ONLY when necessary for specific claims, statistics, direct quotes, or important facts
6. Maintain objectivity while addressing the specific aspects requested in the prompt
7. Compare and contrast the information from sources, noting areas of consensus or points of contention
8. Showcase key insights, important data, or innovative ideas

Here are the source articles to analyze (numbered for citation purposes):

${articles
  .map(
    (article, index) => `
[${index + 1}] Title: ${article.title}
URL: ${article.url}
Content: ${article.content}
---
`
  )
  .join('\n')}

Format the report as a JSON object with the following structure:
{
  "title": "Report title",
  "summary": "Executive summary (can include markdown)",
  "sections": [
    {
      "title": "Section title",
      "content": "Section content with markdown formatting and selective citations"
    }
  ],
  "usedSources": [1, 2] // Array of source numbers that were actually cited in the report
}

Use markdown formatting in the content to improve readability:
- Use **bold** for emphasis
- Use bullet points and numbered lists where appropriate
- Use headings and subheadings with # syntax
- Include code blocks if relevant
- Use > for quotations
- Use --- for horizontal rules where appropriate

CITATION GUIDELINES:
1. Only use citations when truly necessary - specifically for:
   - Direct quotes from sources
   - Specific statistics, figures, or data points
   - Non-obvious facts or claims that need verification
   - Controversial statements
   
2. DO NOT use citations for:
   - General knowledge
   - Your own analysis or synthesis of information
   - Widely accepted facts
   - Every sentence or paragraph

3. When needed, use superscript citation numbers in square brackets [¹], [²], etc. at the end of the relevant sentence
   
4. The citation numbers correspond directly to the source numbers provided in the list
   
5. Be judicious and selective with citations - a well-written report should flow naturally with citations only where they truly add credibility

6. You DO NOT need to cite every source provided. Only cite the sources that contain information directly relevant to the report. Track which sources you actually cite and include their numbers in the "usedSources" array in the output JSON.

7. It's completely fine if some sources aren't cited at all - this means they weren't needed for the specific analysis requested.`
    }

    const systemPrompt = generateSystemPrompt(selectedResults, prompt)

    // console.log('Sending prompt to model:', systemPrompt)
    console.log('Model:', model)

    try {
      const response = await generateWithModel(systemPrompt, platformModel)

      if (!response) {
        throw new Error('No response from model')
      }

      try {
        const reportData = extractAndParseJSON(response)
        // Add sources to the report data
        reportData.sources = sources
        console.log('Parsed report data:', reportData)
        return NextResponse.json(reportData)
      } catch (parseError) {
        console.error('JSON parsing error:', parseError)
        return NextResponse.json(
          { error: 'Failed to parse report format' },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error('Model generation error:', error)
      return NextResponse.json(
        { error: 'Failed to generate report content' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Report generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    )
  }
}



================================================
FILE: app/api/search/route.ts
================================================
import { NextResponse } from 'next/server'
import { searchRatelimit } from '@/lib/redis'
import { CONFIG } from '@/lib/config'

const BING_ENDPOINT = 'https://api.bing.microsoft.com/v7.0/search'
const GOOGLE_ENDPOINT = 'https://customsearch.googleapis.com/customsearch/v1'
const EXA_ENDPOINT = 'https://api.exa.ai/search'

type TimeFilter = '24h' | 'week' | 'month' | 'year' | 'all'

function getBingFreshness(timeFilter: TimeFilter): string {
  switch (timeFilter) {
    case '24h':
      return 'Day'
    case 'week':
      return 'Week'
    case 'month':
      return 'Month'
    case 'year':
      return 'Year'
    default:
      return ''
  }
}

function getGoogleDateRestrict(timeFilter: TimeFilter): string | null {
  // Map our timeFilter to Google's dateRestrict values:
  // d1 for 24h, w1 for week, m1 for month, y1 for year
  switch (timeFilter) {
    case '24h':
      return 'd1'
    case 'week':
      return 'w1'
    case 'month':
      return 'm1'
    case 'year':
      return 'y1'
    default:
      return null
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      query,
      timeFilter = 'all',
      provider = CONFIG.search.provider,
      isTestQuery = false,
    } = body

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      )
    }

    // Return dummy results for test queries
    if (query.toLowerCase() === 'test' || isTestQuery) {
      return NextResponse.json({
        webPages: {
          value: [
            {
              id: 'test-1',
              url: 'https://example.com/test-1',
              name: 'Test Result 1',
              snippet:
                'This is a test search result for testing purposes. It contains some sample text about research and analysis.',
            },
            {
              id: 'test-2',
              url: 'https://example.com/test-2',
              name: 'Test Result 2',
              snippet:
                'Another test result with different content. This one discusses methodology and data collection.',
            },
            {
              id: 'test-3',
              url: 'https://example.com/test-3',
              name: 'Test Result 3',
              snippet:
                'A third test result focusing on academic research and scientific papers.',
            },
          ],
        },
      })
    }

    // Only check rate limit if enabled
    if (CONFIG.rateLimits.enabled) {
      const { success } = await searchRatelimit.limit(query)
      if (!success) {
        return NextResponse.json(
          {
            error:
              'Too many requests. Please wait a moment before trying again.',
          },
          { status: 429 }
        )
      }
    }

    if (provider === 'exa') {
      const exaApiKey = process.env.EXA_API_KEY
      if (!exaApiKey) {
        return NextResponse.json(
          {
            error:
              'Exa search API is not properly configured. Please check your environment variables.',
          },
          { status: 500 }
        )
      }

      try {
        const exaResponse = await fetch(EXA_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${exaApiKey}`,
          },
          body: JSON.stringify({
            query,
            type: 'auto',
            numResults: CONFIG.search.resultsPerPage,
            contents: {
              text: {
                maxCharacters: 500,
              },
            },
          }),
        })

        if (!exaResponse.ok) {
          if (exaResponse.status === 429) {
            return NextResponse.json(
              {
                error: 'Rate limit exceeded. Please try again later.',
              },
              { status: 429 }
            )
          }
          throw new Error(`Exa API error: ${exaResponse.status}`)
        }

        const response = await exaResponse.json()

        if (!response?.results) {
          throw new Error('Unexpected Exa API response format')
        }

        // Transform Exa results to match our format
        const transformedResults = {
          webPages: {
            value: response.results.map((item: any) => ({
              id: item.id || item.url,
              url: item.url,
              name: item.title || 'Untitled',
              snippet: item.text || '',
              publishedDate: item.publishedDate || undefined,
              author: item.author || undefined,
              image: item.image || undefined,
              favicon: item.favicon || undefined,
              score: item.score || undefined,
            })),
          },
        }

        return NextResponse.json(transformedResults)
      } catch (error: any) {
        console.error('Exa search error:', error)
        return NextResponse.json(
          {
            error: 'Failed to fetch search results from Exa.',
          },
          { status: 500 }
        )
      }
    } else if (provider === 'google') {
      // Ensure required Google API variables are available.
      const googleApiKey = process.env.GOOGLE_SEARCH_API_KEY
      const googleCx = process.env.GOOGLE_SEARCH_CX
      if (!googleApiKey || !googleCx) {
        return NextResponse.json(
          {
            error:
              'Google search API is not properly configured. Please check your environment variables.',
          },
          { status: 500 }
        )
      }

      const params = new URLSearchParams({
        q: query,
        key: googleApiKey,
        cx: googleCx,
        num: CONFIG.search.resultsPerPage.toString(),
      })

      // Add Google's dateRestrict parameter if a time filter is applied
      const dateRestrict = getGoogleDateRestrict(timeFilter as TimeFilter)
      if (dateRestrict) {
        params.append('dateRestrict', dateRestrict)
      }

      // Set safe search parameter based on config
      params.append('safe', CONFIG.search.safeSearch.google)

      const googleResponse = await fetch(
        `${GOOGLE_ENDPOINT}?${params.toString()}`
      )

      if (!googleResponse.ok) {
        const errorData = await googleResponse.json().catch(() => null)

        // Check for quota exceeded error
        if (errorData?.error?.message?.includes('Quota exceeded')) {
          return NextResponse.json(
            {
              error:
                'Daily search limit reached. Please try again tomorrow or contact support for increased limits.',
            },
            { status: 429 }
          )
        }

        return NextResponse.json(
          {
            error:
              'An error occurred while fetching search results. Please try again later.',
          },
          { status: googleResponse.status }
        )
      }

      const data = await googleResponse.json()

      // Transform Google search results to match our format
      const transformedResults = {
        webPages: {
          value:
            data.items?.map((item: any) => ({
              id: item.cacheId || item.link,
              url: item.link,
              name: item.title,
              snippet: item.snippet,
            })) || [],
        },
      }

      return NextResponse.json(transformedResults)
    } else {
      // Default to Bing search
      const subscriptionKey = process.env.AZURE_SUB_KEY
      if (!subscriptionKey) {
        return NextResponse.json(
          {
            error:
              'Search API is not properly configured. Please check your environment variables.',
          },
          { status: 500 }
        )
      }

      const params = new URLSearchParams({
        q: query,
        count: CONFIG.search.resultsPerPage.toString(),
        mkt: CONFIG.search.market,
        safeSearch: CONFIG.search.safeSearch.bing,
        textFormat: 'HTML',
        textDecorations: 'true',
      })

      // Add freshness parameter for Bing if a time filter is applied
      const freshness = getBingFreshness(timeFilter as TimeFilter)
      if (freshness) {
        params.append('freshness', freshness)
      }

      const bingResponse = await fetch(
        `${BING_ENDPOINT}?${params.toString()}`,
        {
          headers: {
            'Ocp-Apim-Subscription-Key': subscriptionKey,
            'Accept-Language': 'en-US',
          },
        }
      )

      if (!bingResponse.ok) {
        if (bingResponse.status === 403) {
          console.error('Bing Search API 403 Error:', {
            status: bingResponse.status,
            headers: Object.fromEntries(bingResponse.headers.entries()),
            query,
            timeFilter,
          })

          try {
            const errorBody = await bingResponse.json()
            console.error('Bing Error Response:', errorBody)
          } catch (e) {
            console.error('Could not parse Bing error response', e)
          }

          return NextResponse.json(
            {
              error:
                'Monthly search quota exceeded. Please try again next month or contact support for increased limits.',
            },
            { status: 403 }
          )
        }
        const errorData = await bingResponse.json().catch(() => null)
        return NextResponse.json(
          {
            error:
              errorData?.message ||
              `Search API returned error ${bingResponse.status}`,
          },
          { status: bingResponse.status }
        )
      }

      const data = await bingResponse.json()
      return NextResponse.json(data)
    }
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred while fetching search results',
      },
      { status: 500 }
    )
  }
}



================================================
FILE: app/flow/page.tsx
================================================
'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import type {
  Node,
  Edge,
  Connection,
  NodeTypes,
  NodeChange,
  EdgeChange,
  XYPosition,
  EdgeTypes,
  CoordinateExtent,
} from '@xyflow/react'
import {
  ReactFlow,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  MiniMap,
  useReactFlow,
  ReactFlowProvider,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Brain, Search, FileText, Loader2, AlertTriangle } from 'lucide-react'
import { SearchNode } from '@/components/flow/search-node'
import { ReportNode } from '@/components/flow/report-node'
import { SelectionNode } from '@/components/flow/selection-node'
import { QuestionNode } from '@/components/flow/question-node'
import { ConsolidatedEdge } from '@/components/flow/consolidated-edge'
import type { SearchResult, Report, FlowNodeData, NodeConfig } from '@/types'
import { ModelSelect, DEFAULT_MODEL } from '@/components/model-select'
import { handleLocalFile } from '@/lib/file-upload'
import { useToast } from '@/hooks/use-toast'
import { useFlowProjects } from '@/hooks/use-flow-projects'
import { ProjectSelector } from '@/components/flow/project-selector'
import Link from 'next/link'
import { ProjectActions } from '@/components/flow/project-actions'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Import the new utility functions
import {
  createDebouncedViewportSave,
  createDebouncedNodesSave,
  createDebouncedEdgesSave,
} from '@/lib/localStorage-utils'

// Constants for localStorage keys - Now using project-based viewport storage

// Node type definitions
const nodeTypes: NodeTypes = {
  searchNode: SearchNode,
  reportNode: ReportNode,
  selectionNode: SelectionNode,
  questionNode: QuestionNode,
}

const edgeTypes: EdgeTypes = {
  consolidated: ConsolidatedEdge,
}

// Extend Node with our FlowNodeData
interface ResearchNode extends Node {
  data: FlowNodeData
  style?: React.CSSProperties
  extent?: 'parent' | CoordinateExtent
}

// Configuration for different node types - centralized configuration
const NODE_CONFIG: Record<string, NodeConfig> = {
  group: {
    zIndex: 0,
    style: {
      width: 800,
      height: 1600,
      padding: 60,
      backgroundColor: 'rgba(240, 240, 240, 0.5)',
      borderRadius: 8,
    },
  },
  searchNode: { zIndex: 1 },
  selectionNode: { zIndex: 2 },
  reportNode: { zIndex: 3 },
  questionNode: { zIndex: 3 },
}

// Custom hook for handling the research workflow
function useResearchFlow(
  createNode: (
    type: string,
    position: XYPosition,
    data: Partial<FlowNodeData>,
    parentId?: string
  ) => ResearchNode,
  setNodes: React.Dispatch<React.SetStateAction<ResearchNode[]>>,
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
  selectedModel: string,
  edges: Edge[],
  query: string,
  selectedReports: string[],
  saveCurrentState: (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports: string[]
  ) => void,
  setQuery: React.Dispatch<React.SetStateAction<string>>,
  simpleSave?: (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports: string[]
  ) => void
) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)

  // Centralized function to handle API errors
  const handleApiError = useCallback((error: any, context: string) => {
    console.error(`${context} error:`, error)
    return error instanceof Error ? error.message : String(error)
  }, [])

  // Handle file upload for search nodes
  const handleFileUpload = useCallback(
    async (file: File, searchNodeId: string, groupId: string) => {
      const result = await handleLocalFile(
        file,
        (loading) => {
          setNodes((nds) =>
            nds.map((node) =>
              node.id === searchNodeId
                ? { ...node, data: { ...node.data, loading } }
                : node
            )
          )
        },
        (error, context) => {
          toast({
            title: context,
            description: error instanceof Error ? error.message : String(error),
            variant: 'destructive',
          })
        }
      )

      if (result) {
        setNodes((nds) => {
          const selectionNode = nds.find(
            (n) => n.type === 'selectionNode' && n.parentId === groupId
          )

          if (selectionNode) {
            return nds.map((node) =>
              node.id === selectionNode.id
                ? {
                    ...node,
                    data: {
                      ...node.data,
                      results: [result, ...(node.data.results || [])],
                    },
                  }
                : node.id === searchNodeId
                ? { ...node, data: { ...node.data, loading: false } }
                : node
            )
          }

          const newSelectionNode = createNode(
            'selectionNode',
            { x: 100, y: 200 },
            {
              results: [result],
              onGenerateReport: (selected, prompt) => {
                return handleGenerateReport(
                  selected,
                  searchNodeId,
                  groupId,
                  prompt
                )
              },
              childIds: [],
            },
            groupId
          )

          return [
            ...nds.map((n) =>
              n.id === searchNodeId
                ? { ...n, data: { ...n.data, loading: false } }
                : n
            ),
            newSelectionNode,
          ]
        })
      }
    },
    [createNode, setNodes, toast]
  )

  // Generate a report from selected search results
  const handleGenerateReport = useCallback(
    async (
      selectedResults: SearchResult[],
      searchNodeId: string,
      groupId: string,
      prompt: string
    ) => {
      if (selectedResults.length === 0) {
        return
      }

      const reportNode = createNode(
        'reportNode',
        { x: 100, y: 800 },
        {
          loading: true,
          hasChildren: false,
        },
        groupId
      )

      const searchTermsNode = createNode(
        'questionNode',
        { x: 100, y: 1000 },
        {
          loading: true,
        },
        groupId
      )

      setNodes((nds) => [...nds, reportNode, searchTermsNode])

      setEdges((eds) => [
        ...eds,
        {
          id: `edge-${searchNodeId}-${reportNode.id}`,
          source: searchNodeId,
          target: reportNode.id,
          animated: true,
        },
        {
          id: `edge-${reportNode.id}-${searchTermsNode.id}`,
          source: reportNode.id,
          target: searchTermsNode.id,
          animated: true,
        },
      ])

      try {
        // Process content for all selected results
        const contentResults = await Promise.all(
          selectedResults.map(async (result) => {
            if (result.content) {
              return {
                url: result.url,
                title: result.name,
                content: result.content,
              }
            }

            try {
              const response = await fetch('/api/fetch-content', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ url: result.url }),
              })

              if (!response.ok) throw new Error('Failed to fetch content')

              const { content } = await response.json()
              return {
                url: result.url,
                title: result.name,
                content: content || result.snippet,
              }
            } catch (error) {
              console.log('Error fetching content:', error)
              return {
                url: result.url,
                title: result.name,
                content: result.snippet,
              }
            }
          })
        )

        const validResults = contentResults.filter((r) => r.content?.trim())
        if (validResults.length === 0) {
          throw new Error('No valid content found in selected results')
        }

        // Generate report
        const reportResponse = await fetch('/api/report', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            selectedResults: validResults,
            sources: selectedResults,
            prompt:
              prompt ||
              'Provide comprehensive analysis of the selected sources.',
            platformModel: selectedModel,
          }),
        })

        if (!reportResponse.ok) {
          const errorData = await reportResponse
            .json()
            .catch(() => ({ error: 'Failed to generate report' }))
          throw new Error(
            errorData.error ||
              `Failed to generate report: ${reportResponse.status}`
          )
        }

        const report = await reportResponse.json()

        // Generate search terms
        const searchTermsResponse = await fetch('/api/generate-question', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            report,
            platformModel: selectedModel,
          }),
        })

        if (!searchTermsResponse.ok) {
          const errorData = await searchTermsResponse
            .json()
            .catch(() => ({ error: 'Failed to generate search terms' }))
          throw new Error(
            errorData.error ||
              `Failed to generate search terms: ${searchTermsResponse.status}`
          )
        }

        const { searchTerms } = await searchTermsResponse.json()

        // Update nodes with results
        let updatedNodes: Node[] = []

        setNodes((nds) => {
          const newNodes = nds.map((node) => {
            if (node.id === reportNode.id) {
              return {
                ...node,
                data: {
                  ...node.data,
                  report,
                  loading: false,
                },
              }
            }
            if (node.id === searchTermsNode.id) {
              return {
                ...node,
                data: {
                  ...node.data,
                  searchTerms,
                  loading: false,
                  onApprove: (term?: string) => {
                    if (term) {
                      setQuery(term)
                      toast({
                        title: 'Search query updated',
                        description: `Updated search query to: "${term}"`,
                      })
                    }
                    return term
                  },
                },
              }
            }
            return node
          })

          updatedNodes = newNodes
          return newNodes
        })

        // FORCE IMMEDIATE SAVE after nodes are updated
        // This is critical - we save directly to localStorage to ensure report data isn't lost
        setTimeout(() => {
          if (simpleSave) {
            simpleSave(updatedNodes, edges, query, selectedReports)
            console.log('🔥 FORCE SAVED REPORT DATA TO LOCALSTORAGE!')
          } else {
            console.warn('simpleSave function not available')
            // Fallback to regular save
            saveCurrentState(updatedNodes, edges, query, selectedReports)
          }
        }, 100)

        return { success: true, report, searchTerms }
      } catch (error) {
        const errorMsg = handleApiError(error, 'Report generation')

        setNodes((nds) =>
          nds.map((node) => {
            if (node.id === reportNode.id || node.id === searchTermsNode.id) {
              return {
                ...node,
                data: {
                  ...node.data,
                  loading: false,
                  error: errorMsg,
                },
              }
            }
            return node
          })
        )
        return { success: false, error: errorMsg }
      }
    },
    [
      createNode,
      setNodes,
      setEdges,
      edges,
      handleApiError,
      query,
      selectedReports,
      selectedModel,
      simpleSave,
      saveCurrentState,
      setQuery,
    ]
  )

  // Start a new research flow
  const startResearch = useCallback(
    async (query: string, parentReportId?: string, nodes?: ResearchNode[]) => {
      if (!query.trim()) return

      setLoading(true)
      try {
        // Calculate position for the new nodes
        const randomOffset = {
          x: Math.floor(Math.random() * 600) - 300,
          y: Math.floor(Math.random() * 300),
        }

        const basePosition = {
          x: parentReportId
            ? nodes?.find((n) => n.id === parentReportId)?.position.x || 0
            : (nodes?.length || 0) * 200,
          y: parentReportId
            ? (nodes?.find((n) => n.id === parentReportId)?.position.y || 0) +
              400
            : 0,
        }

        const groupPosition = {
          x: Math.max(0, basePosition.x + randomOffset.x),
          y: Math.max(0, basePosition.y + randomOffset.y),
        }

        // Create group and search nodes
        const groupNode = createNode('group', groupPosition, { query })

        const searchNode = createNode(
          'searchNode',
          { x: 100, y: 80 },
          {
            query,
            loading: true,
            childIds: [],
            onFileUpload: (file: File) =>
              handleFileUpload(file, searchNode.id, groupNode.id),
          },
          groupNode.id
        )

        setNodes((nds) => [...nds, groupNode, searchNode])

        // Perform search API call
        const response = await fetch('/api/search', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query,
            timeFilter: 'all',
            platformModel: selectedModel,
          }),
        })

        if (!response.ok) {
          const errorData = await response
            .json()
            .catch(() => ({ error: 'Search failed' }))
          throw new Error(
            errorData.error || `Search failed: ${response.status}`
          )
        }

        const data = await response.json()

        if (!data.webPages?.value?.length) {
          throw new Error('No search results found')
        }

        // Transform search results
        const searchResults = data.webPages.value.map((result: any) => ({
          id: result.id || `result-${Date.now()}-${Math.random()}`,
          url: result.url,
          name: result.name || result.title,
          snippet: result.snippet,
          isCustomUrl: false,
        }))

        // Create selection node with results
        const selectionNode = createNode(
          'selectionNode',
          { x: 100, y: 200 },
          {
            results: searchResults,
            onGenerateReport: (selected, prompt) => {
              return handleGenerateReport(
                selected,
                searchNode.id,
                groupNode.id,
                prompt
              )
            },
            childIds: [],
          },
          groupNode.id
        )

        // Update nodes and create edge
        setNodes((nds) => {
          const updatedNodes = nds.map((node) =>
            node.id === searchNode.id
              ? { ...node, data: { ...node.data, loading: false } }
              : node
          )
          return [...updatedNodes, selectionNode]
        })

        setEdges((eds) => [
          ...eds,
          {
            id: `edge-${searchNode.id}-${selectionNode.id}`,
            source: searchNode.id,
            target: selectionNode.id,
            animated: true,
          },
        ])
      } catch (error) {
        const errorMsg = handleApiError(error, 'Search')

        setNodes((nds) =>
          nds.map((node) =>
            node.data.loading
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    loading: false,
                    error: errorMsg,
                  },
                }
              : node
          )
        )
      } finally {
        setLoading(false)
      }
    },
    [
      createNode,
      selectedModel,
      setEdges,
      setNodes,
      handleApiError,
      handleFileUpload,
      handleGenerateReport,
    ]
  )

  return {
    loading,
    startResearch,
    handleGenerateReport,
    handleFileUpload,
  }
}

// Custom hook for handling consolidation of reports
function useConsolidation(
  createNode: (
    type: string,
    position: XYPosition,
    data: Partial<FlowNodeData>,
    parentId?: string
  ) => ResearchNode,
  nodes: ResearchNode[],
  setNodes: React.Dispatch<React.SetStateAction<ResearchNode[]>>,
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
  selectedModel: string
) {
  const [isConsolidating, setIsConsolidating] = useState(false)

  const consolidateReports = useCallback(
    async (selectedReports: string[]) => {
      if (selectedReports.length < 2) {
        return {
          success: false,
          error: 'Select at least 2 reports to consolidate',
        }
      }

      setIsConsolidating(true)
      try {
        const reportsToConsolidate = nodes
          .filter(
            (node) => selectedReports.includes(node.id) && node.data.report
          )
          .map((node) => node.data.report!)

        if (reportsToConsolidate.length < 2) {
          throw new Error('Need at least 2 valid reports to consolidate')
        }

        const response = await fetch('/api/consolidate-report', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            reports: reportsToConsolidate,
            platformModel: selectedModel,
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to generate consolidated report')
        }

        const consolidated: Report = await response.json()

        // Calculate position for consolidated node group
        const groupNode = createNode(
          'group',
          {
            x:
              Math.max(
                ...selectedReports.map(
                  (id) => nodes.find((n) => n.id === id)?.position.x || 0
                )
              ) + 1000,
            y: Math.min(
              ...selectedReports.map(
                (id) => nodes.find((n) => n.id === id)?.position.y || 0
              )
            ),
          },
          { query: 'Consolidated Research' }
        )

        // Create consolidated report node
        const consolidatedNode = createNode(
          'reportNode',
          { x: 100, y: 100 },
          {
            report: consolidated,
            loading: false,
            childIds: [],
            hasChildren: false,
            isConsolidated: true,
          },
          groupNode.id
        )

        // Update nodes and create edges
        setNodes((nds) => {
          const updatedNodes = nds.map((node) =>
            selectedReports.includes(node.id)
              ? { ...node, data: { ...node.data, isSelected: false } }
              : node
          )
          return [...updatedNodes, groupNode, consolidatedNode]
        })

        setEdges((eds) => [
          ...eds,
          ...selectedReports.map((reportId) => ({
            id: `edge-${reportId}-${consolidatedNode.id}`,
            source: reportId,
            target: consolidatedNode.id,
            animated: true,
            type: 'consolidated',
          })),
        ])

        return { success: true, consolidated }
      } catch (error) {
        console.error('Consolidation error:', error)
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to consolidate reports',
        }
      } finally {
        setIsConsolidating(false)
      }
    },
    [createNode, nodes, selectedModel, setEdges, setNodes]
  )

  return {
    isConsolidating,
    consolidateReports,
  }
}

// Function to wrap the Flow component with ReactFlowProvider to enable useReactFlow
function Flow() {
  const [nodes, setNodes] = useState<ResearchNode[]>([])
  const [edges, setEdges] = useState<Edge[]>([])
  const [query, setQuery] = useState('')
  const [selectedReports, setSelectedReports] = useState<string[]>([])
  const [selectedModel, setSelectedModel] = useState(DEFAULT_MODEL)
  // Only initialize ONCE - track if we've done first initialization
  const hasInitialized = useRef(false)

  const { toast } = useToast()

  const {
    projects,
    currentProject,
    setCurrentProject,
    createProject,
    updateCurrentProject,
    deleteProject,
    saveCurrentState,
    exportProjects,
    importProjects,
    storageInfo,
    refreshStorageInfo,
    simpleSave,
  } = useFlowProjects()

  // Get ReactFlow instance to access viewport
  const reactFlowInstance = useReactFlow()

  // Create stable references for the save functions
  const saveViewportRef = useRef<(() => void) | null>(null)
  const saveNodesRef = useRef<(() => void) | null>(null)
  const saveEdgesRef = useRef<(() => void) | null>(null)

  // Update the function references when dependencies change
  useEffect(() => {
    // Create the save functions only when the project or update function changes
    if (!currentProject) return

    // Use function that captures the current values but doesn't create
    // a dependency on them, to avoid recreating functions on every node/edge change
    saveViewportRef.current = createDebouncedViewportSave(
      () => reactFlowInstance.getViewport(),
      currentProject,
      updateCurrentProject
    )

    saveNodesRef.current = createDebouncedNodesSave(
      // Capture latest nodes via callback
      () => nodes,
      currentProject,
      updateCurrentProject
    )

    saveEdgesRef.current = createDebouncedEdgesSave(
      // Capture latest edges via callback
      () => edges,
      currentProject,
      updateCurrentProject
    )

    // Cleanup when project changes
    return () => {
      saveViewportRef.current = null
      saveNodesRef.current = null
      saveEdgesRef.current = null
    }
  }, [currentProject, reactFlowInstance, updateCurrentProject])

  // Stable callback wrappers that use the refs
  const saveViewport = useCallback(() => {
    if (saveViewportRef.current && currentProject && hasInitialized.current) {
      saveViewportRef.current()
    }
  }, [currentProject])

  const saveNodes = useCallback(() => {
    if (saveNodesRef.current && currentProject && hasInitialized.current) {
      saveNodesRef.current()
    }
  }, [currentProject])

  const saveEdges = useCallback(() => {
    if (saveEdgesRef.current && currentProject && hasInitialized.current) {
      saveEdgesRef.current()
    }
  }, [currentProject])

  // Node changes handler with stable callback
  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      const updatedNodes = applyNodeChanges(changes, nodes)
      setNodes(updatedNodes)

      // Skip if no current project or during initialization
      if (!currentProject || !hasInitialized.current) return

      // Use our debounced save function
      saveNodes()
    },
    [nodes, currentProject, saveNodes]
  )

  // Edge changes handler
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      const updatedEdges = applyEdgeChanges(changes, edges)
      setEdges(updatedEdges)

      // Skip if no current project or during initialization
      if (!currentProject || !hasInitialized.current) return

      // Use our debounced save function
      saveEdges()
    },
    [edges, currentProject, saveEdges]
  )

  // Connection handler
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdges = addEdge(params, edges)
      setEdges(newEdges)

      // Skip if no current project or during initialization
      if (!currentProject || !hasInitialized.current) return

      // Immediately save connections (less frequent operation)
      updateCurrentProject({ edges: newEdges })
    },
    [edges, currentProject, updateCurrentProject]
  )

  // Viewport change handler
  const onMoveEnd = useCallback(() => {
    if (currentProject && hasInitialized.current) {
      saveViewport()
    }
  }, [currentProject, saveViewport])

  // Load data from the current project - using a ref to prevent repeated execution
  const currentProjectIdRef = useRef<string | null>(null)

  useEffect(() => {
    // If the project is null or ID hasn't changed, skip the effect
    if (!currentProject || currentProjectIdRef.current === currentProject.id) {
      return
    }

    console.log(`Loading project: ${currentProject.name}`)
    hasInitialized.current = false
    currentProjectIdRef.current = currentProject.id

    // Set the nodes and edges from the project
    setNodes(currentProject.nodes || [])
    setEdges(currentProject.edges || [])
    setQuery(currentProject.query || '')
    setSelectedReports(currentProject.selectedReports || [])

    // Restore the viewport for this project using our utility
    let viewportTimer: NodeJS.Timeout | null = null
    if (currentProject.viewport && reactFlowInstance) {
      try {
        // Use a slight delay to ensure the nodes have been rendered
        viewportTimer = setTimeout(() => {
          if (reactFlowInstance && currentProject.viewport) {
            reactFlowInstance.setViewport(currentProject.viewport!)
          }
        }, 100)
      } catch (error) {
        console.error('Failed to restore project viewport:', error)
      }
    }

    // Mark as initialized after a short delay
    const initTimer = setTimeout(() => {
      hasInitialized.current = true
      console.log(`Project ${currentProject.name} fully initialized`)
    }, 200)

    // Cleanup the timers if component unmounts
    return () => {
      if (viewportTimer) clearTimeout(viewportTimer)
      clearTimeout(initTimer)
    }
  }, [currentProject, reactFlowInstance])

  // Detect when we're dealing with a fresh project with no nodes
  const hasDetectedNewFlowRef = useRef<boolean>(false)

  useEffect(() => {
    if (
      currentProject &&
      currentProject.nodes.length === 0 &&
      nodes.length > 0 &&
      hasInitialized.current &&
      !hasDetectedNewFlowRef.current
    ) {
      // This is a new flow with nodes but the current project doesn't have them yet
      console.log('Detected new flow - saving initial state')
      hasDetectedNewFlowRef.current = true

      // Save the current state to the project
      updateCurrentProject({
        nodes,
        edges,
        query,
        selectedReports,
        viewport: reactFlowInstance.getViewport(),
      })
    }

    // Reset the detection flag when the project changes
    if (!currentProject || currentProject.nodes.length > 0) {
      hasDetectedNewFlowRef.current = false
    }
  }, [
    currentProject,
    nodes,
    edges,
    query,
    selectedReports,
    reactFlowInstance,
    updateCurrentProject,
  ])

  // ULTRA-SIMPLE Report selection handler - directly updates both state and nodes in one go
  const handleReportSelect = useCallback(
    (reportId: string) => {
      setSelectedReports((currentSelectedReports) => {
        const isCurrentlySelected = currentSelectedReports.includes(reportId)
        const newSelectedReports = isCurrentlySelected
          ? currentSelectedReports.filter((id) => id !== reportId)
          : [...currentSelectedReports, reportId]

        // Update the nodes to reflect the selection state
        setNodes((nds) =>
          nds.map((node) =>
            node.id === reportId
              ? {
                  ...node,
                  data: {
                    ...node.data,
                    isSelected: !isCurrentlySelected,
                  },
                }
              : node
          )
        )

        return newSelectedReports
      })
    },
    [setNodes]
  )

  // Node creation utility - centralized logic for creating nodes
  const createNode = useCallback(
    (
      type: string,
      position: XYPosition,
      data: Partial<FlowNodeData>,
      parentId?: string
    ): ResearchNode => {
      const id =
        data.id ||
        `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      const config = NODE_CONFIG[type as keyof typeof NODE_CONFIG] || {
        zIndex: 1,
      }

      // Base node data with selection state for report nodes
      const nodeData = {
        ...data,
        id,
        childIds: data.childIds || [],
        ...(type === 'reportNode' && {
          isSelected: selectedReports.includes(id),
          onSelect: handleReportSelect,
        }),
      }

      // Create node with appropriate configuration
      const node: ResearchNode = {
        id,
        type,
        position: {
          x: Math.max(0, Math.round(position.x)),
          y: Math.max(0, Math.round(position.y)),
        },
        data: nodeData as FlowNodeData,
        parentId,
        extent: 'parent',
        zIndex: config.zIndex,
        ...(type === 'group' && { style: config.style }),
      }

      return node
    },
    [selectedReports, handleReportSelect]
  )

  // Use the research flow hook
  const {
    loading: researchLoading,
    startResearch,
    handleGenerateReport,
    handleFileUpload,
  } = useResearchFlow(
    createNode,
    setNodes,
    setEdges,
    selectedModel,
    edges,
    query,
    selectedReports,
    saveCurrentState,
    setQuery,
    simpleSave
  )

  // Use the consolidation hook
  const { isConsolidating, consolidateReports } = useConsolidation(
    createNode,
    nodes,
    setNodes,
    setEdges,
    selectedModel
  )

  // Update nodes when selectedReports changes
  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.type === 'reportNode') {
          return {
            ...node,
            data: {
              ...node.data,
              isSelected: selectedReports.includes(node.id),
            },
          }
        }
        return node
      })
    )
  }, [selectedReports, setNodes])

  // IMPROVED auto-save with debounce - prevent multiple saves of the same data
  const previousSaveRef = useRef<string>('')

  useEffect(() => {
    // Only save if we've completed initialization
    if (!hasInitialized.current) return

    const saveTimer = setTimeout(() => {
      // Create a signature of what we're about to save
      const saveSignature = JSON.stringify({
        nodeCount: nodes.length,
        edgeCount: edges.length,
        query,
        selectedReports,
      })

      // Only save if the signature has changed
      if (saveSignature !== previousSaveRef.current) {
        previousSaveRef.current = saveSignature
        saveCurrentState(nodes, edges, query, selectedReports)
      } else {
      }
    }, 1000)

    return () => clearTimeout(saveTimer)
  }, [nodes, edges, query, selectedReports, saveCurrentState])

  // REDESIGNED project initialization - only runs ONCE
  useEffect(() => {
    // Don't run if we've already initialized
    if (hasInitialized.current) return

    if (currentProject) {
      // Set basic state
      setQuery(currentProject.query)

      // Get the selectedReports from the project (only if we don't have any)
      const projectSelections = currentProject.selectedReports || []
      if (selectedReports.length === 0 && projectSelections.length > 0) {
        setSelectedReports(projectSelections)
      }

      // Map the nodes with the proper callbacks
      const nodesWithCallbacks = (currentProject.nodes as ResearchNode[]).map(
        (node) => {
          // Clear any error states when loading from localStorage
          const nodeData = {
            ...node.data,
            id: node.id,
            error: undefined, // Clear any error state
            loading: false, // Reset loading state
          }

          if (node.type === 'reportNode') {
            // Set isSelected based on the selectedReports we just set
            const isNodeSelected = projectSelections.includes(node.id)

            return {
              ...node,
              data: {
                ...nodeData,
                isSelected: isNodeSelected,
                onSelect: (id: string) => handleReportSelect(id),
              },
            }
          }
          if (node.type === 'selectionNode') {
            return {
              ...node,
              data: {
                ...nodeData,
                onGenerateReport: (
                  selectedResults: SearchResult[],
                  prompt: string
                ) => {
                  const parentGroupId = node.parentId
                  // Find the search node in the same group
                  const searchNode = currentProject.nodes.find(
                    (n) =>
                      n.type === 'searchNode' && n.parentId === parentGroupId
                  )
                  const searchNodeId = searchNode?.id || ''

                  if (!searchNodeId || !parentGroupId) {
                    console.error(
                      'Unable to find associated search node or group for selection node',
                      node.id
                    )
                    toast({
                      title: 'Error',
                      description:
                        "Couldn't find the search node associated with these results. Try refreshing the page.",
                      variant: 'destructive',
                    })
                    return
                  }

                  return handleGenerateReport(
                    selectedResults,
                    searchNodeId,
                    parentGroupId,
                    prompt
                  )
                },
              },
            }
          }
          if (node.type === 'searchNode') {
            return {
              ...node,
              data: {
                ...nodeData,
                onFileUpload: node.parentId
                  ? (file: File) =>
                      handleFileUpload(file, node.id, node.parentId || '')
                  : undefined,
              },
            }
          }
          if (node.type === 'questionNode') {
            return {
              ...node,
              data: {
                ...nodeData,
                onApprove: (term?: string) => {
                  if (term) {
                    setQuery(term)
                    toast({
                      title: 'Search query updated',
                      description: `Updated search query to: "${term}"`,
                    })
                  }
                  return term
                },
              },
            }
          }
          return {
            ...node,
            data: nodeData,
          }
        }
      )

      setNodes(nodesWithCallbacks as ResearchNode[])
      setEdges(currentProject.edges)

      // Mark that we've initialized - NEVER run this effect again
      hasInitialized.current = true
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentProject, handleReportSelect]) // Only these deps, but use hasInitialized to prevent re-runs

  // Start research handler
  const handleStartResearch = useCallback(
    async (parentReportId?: string) => {
      if (!query.trim()) return
      await startResearch(query, parentReportId, nodes)
    },
    [query, nodes, startResearch]
  )

  // Consolidation handler - add extra logging
  const handleConsolidateSelected = useCallback(async () => {
    if (selectedReports.length < 2) {
      console.error('Select at least 2 reports to consolidate')
      return
    }

    const result = await consolidateReports(selectedReports)
    if (result.success) {
      setSelectedReports([])
    } else if (result.error) {
      toast({
        title: 'Consolidation Failed',
        description: String(result.error),
        variant: 'destructive',
      })
    }
  }, [consolidateReports, selectedReports, toast])

  // Project management functions
  const handleCreateNewProject = useCallback(
    (name: string) => {
      createProject(name)
      setNodes([])
      setEdges([])
      setQuery('')
      setSelectedReports([])
    },
    [createProject]
  )

  const handleRenameProject = useCallback(
    (id: string, name: string) => {
      if (currentProject?.id === id) {
        updateCurrentProject({ name })
      }
    },
    [currentProject, updateCurrentProject]
  )

  // UI Components
  const renderNavigation = () => (
    <nav className='border-b bg-white shadow-sm'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between h-16'>
          {/* Logo and main navigation */}
          <div className='flex items-center'>
            <div className='flex-shrink-0 flex items-center'>
              <Link href='/' className='font-bold text-xl text-primary'>
                <img
                  src='/apple-icon.png'
                  alt='Open Deep Research'
                  className='h-8 w-8 rounded-full'
                />
              </Link>
            </div>
            <div className='hidden sm:ml-6 sm:flex sm:space-x-4'>
              <Button asChild variant='ghost' size='sm'>
                <Link href='/'>Home</Link>
              </Button>
              <Button asChild variant='ghost' size='sm'>
                <Link
                  href='https://www.loom.com/share/3c4d9811ac1d47eeaa7a0907c43aef7f'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  Watch Demo
                </Link>
              </Button>
            </div>
          </div>

          {/* Project controls */}
          <div className='flex items-center gap-2'>
            <ProjectSelector
              projects={projects}
              currentProject={currentProject}
              onSelectProject={setCurrentProject}
              onCreateProject={handleCreateNewProject}
              onDeleteProject={deleteProject}
              onRenameProject={handleRenameProject}
            />
            <ProjectActions
              exportProjects={exportProjects}
              importProjects={importProjects}
              storageInfo={storageInfo}
              refreshStorageInfo={refreshStorageInfo}
            />

            {/* Mobile menu */}
            <div className='sm:hidden flex items-center'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='ghost' size='icon' className='h-8 w-8'>
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      fill='none'
                      viewBox='0 0 24 24'
                      strokeWidth={1.5}
                      stroke='currentColor'
                      className='w-5 h-5'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        d='M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5'
                      />
                    </svg>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem asChild>
                    <Link href='/'>Home</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href='https://www.loom.com/share/3c4d9811ac1d47eeaa7a0907c43aef7f'
                      target='_blank'
                      rel='noopener noreferrer'
                    >
                      Watch Demo
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </nav>
  )

  const renderControls = () => (
    <div className='p-4 bg-gray-50'>
      <div className='max-w-4xl mx-auto flex flex-col gap-4'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder='Enter research topic'
            className='flex-1 min-w-0'
          />
          <Button
            onClick={() => handleStartResearch()}
            disabled={researchLoading}
            className='gap-2 whitespace-nowrap'
          >
            {researchLoading ? (
              <>
                <Brain className='h-4 w-4 animate-spin' />
                Researching...
              </>
            ) : (
              <>
                <Search className='h-4 w-4' />
                Start Research
              </>
            )}
          </Button>
        </div>
        <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
          <div className='flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto'>
            <p className='text-sm text-gray-500 whitespace-nowrap'>
              Model for report generation:
            </p>
            <ModelSelect
              value={selectedModel}
              onValueChange={setSelectedModel}
              triggerClassName='w-full sm:w-[200px]'
            />
          </div>
          <Button
            onClick={handleConsolidateSelected}
            disabled={selectedReports.length < 2 || isConsolidating}
            variant='outline'
            className='gap-2 w-full sm:w-auto sm:ml-auto'
          >
            {isConsolidating ? (
              <>
                <Loader2 className='h-4 w-4 animate-spin' />
                Consolidating...
              </>
            ) : (
              <>
                <FileText className='h-4 w-4' />
                Consolidate Selected ({selectedReports.length})
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )

  const renderErrorNotification = () => {
    const errorNode = nodes.find((node) => node.data.error)
    if (!errorNode) return null

    return (
      <div className='absolute top-0 left-0 right-0 z-10 p-3 bg-red-50 border-b border-red-200'>
        <div className='max-w-4xl mx-auto flex items-center gap-2 text-red-700'>
          <AlertTriangle className='h-4 w-4' />
          <p className='text-sm'>{errorNode.data.error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className='h-screen flex flex-col'>
      {renderNavigation()}
      {renderControls()}
      <div className='flex-1 w-full h-0 relative overflow-hidden'>
        {renderErrorNotification()}
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView={
            nodes.length > 0 && !currentProject?.nodes?.length ? true : false
          }
          minZoom={0.1}
          maxZoom={1.5}
          onMoveEnd={onMoveEnd}
          className='transition-all duration-200'
        >
          <MiniMap
            nodeStrokeWidth={3}
            className='!bottom-4 !right-4 !left-auto'
            pannable
            zoomable
          />
          <Background />
          <Controls
            className='!top-4 !right-4 !left-auto !bottom-auto'
            showInteractive={false}
          />
        </ReactFlow>
      </div>
    </div>
  )
}

// Export the flow component wrapped with ReactFlowProvider
export default function FlowPage() {
  return (
    <ReactFlowProvider>
      <Flow />
    </ReactFlowProvider>
  )
}



================================================
FILE: app/report/[id]/page.tsx
================================================
import React from 'react'
import { ReportContent } from './report-content'

export default function ReportPage({ params }: any) {
  return <ReportContent id={params.id} />
}



================================================
FILE: app/report/[id]/report-content.tsx
================================================
'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { ArrowLeft, Trash2, AlertTriangle, Brain } from 'lucide-react'
import { type KnowledgeBaseReport } from '@/types'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { formatDistanceToNow } from 'date-fns'
import { useKnowledgeBase } from '@/hooks/use-knowledge-base'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ReportActions } from '@/components/report-actions'
import { CitationsFooter } from '@/components/citations-footer'

export function ReportContent({ id }: any) {
  const router = useRouter()
  const { reports, deleteReport } = useKnowledgeBase()
  const [report, setReport] = useState<KnowledgeBaseReport | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  useEffect(() => {
    const foundReport = reports.find((r) => r.id === id)
    if (foundReport) {
      setReport(foundReport)
    }
  }, [id, reports])

  const handleDelete = () => {
    if (!report) return
    deleteReport(report.id)
    router.push('/')
  }

  if (!report) {
    return (
      <div className='min-h-screen bg-white p-4 sm:p-8'>
        <div className='max-w-4xl mx-auto'>
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertTitle>Report Not Found</AlertTitle>
            <AlertDescription>
              The report you&apos;re looking for doesn&apos;t exist or has been
              deleted.
            </AlertDescription>
          </Alert>
          <div className='mt-4 text-center'>
            <Button
              variant='ghost'
              onClick={() => router.push('/')}
              className='gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='min-h-screen bg-white p-4 sm:p-8'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8 flex items-center justify-between'>
          <Button
            variant='ghost'
            onClick={() => router.back()}
            className='gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
            Back
          </Button>
          <div className='flex gap-2'>
            <ReportActions
              report={report?.report}
              prompt={report?.query}
              size='sm'
              hideKnowledgeBase={true}
            />
            <Button
              variant='destructive'
              size='sm'
              onClick={() => setShowDeleteConfirm(true)}
              className='gap-2'
            >
              <Trash2 className='h-4 w-4' />
              Delete Report
            </Button>
          </div>
        </div>

        {showDeleteConfirm && (
          <Alert variant='destructive' className='mb-6'>
            <AlertTriangle className='h-4 w-4' />
            <AlertTitle>Delete Report?</AlertTitle>
            <AlertDescription className='space-y-2'>
              <p>
                This will permanently delete this report from your knowledge
                base.
              </p>
              <div className='flex gap-2 mt-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </Button>
                <Button variant='destructive' size='sm' onClick={handleDelete}>
                  Yes, Delete
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Alert className='mb-6'>
          <Brain className='h-4 w-4' />
          <AlertTitle>Knowledge Base Report</AlertTitle>
          <AlertDescription>
            This report was saved{' '}
            {formatDistanceToNow(report.timestamp, { addSuffix: true })}
            in response to the query: &apos;{report.query}&apos;
          </AlertDescription>
        </Alert>

        <Card className='p-6'>
          <div className='mb-6'>
            <h1 className='text-3xl font-bold text-gray-800 mb-2'>
              {report.report.title}
            </h1>
          </div>

          <div className='prose max-w-none'>
            <h2>Summary</h2>

            {/* Scrollable content area */}
            <div
              className='max-h-[700px] overflow-y-auto pr-2'
              style={{ scrollbarWidth: 'thin' }}
            >
              <p className='mb-6'>{report.report.summary}</p>

              {report.report.sections.map((section, index) => (
                <div key={index} className='mb-6'>
                  <h2>{section.title}</h2>
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {section.content}
                  </ReactMarkdown>
                </div>
              ))}

              {/* Citations Section */}
              <CitationsFooter report={report.report} />
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}



================================================
FILE: components/analytics.tsx
================================================
'use client'

import Script from 'next/script'

const GOOGLE_MEASUREMENT_ID =
  process.env.NEXT_PUBLIC_GOOGLE_MEASUREMENT_ID || ''

export function Analytics() {
  return (
    <Script
      src={`https://www.googletagmanager.com/gtag/js?id=${GOOGLE_MEASUREMENT_ID}`}
      strategy='afterInteractive'
      onLoad={() => {
        // @ts-ignore
        window.dataLayer = window.dataLayer || []
        function gtag() {
          // @ts-ignore
          dataLayer.push(arguments)
        }
        // @ts-ignore
        gtag('js', new Date())
        // @ts-ignore
        gtag('config', GOOGLE_MEASUREMENT_ID)
      }}
    />
  )
}



================================================
FILE: components/citations-footer.tsx
================================================
import React from 'react'
import { type Report } from '@/types'

interface CitationsFooterProps {
  report: Report
}

export function CitationsFooter({ report }: CitationsFooterProps) {
  // Filter sources to only include those that were actually used
  const filteredSources = React.useMemo(() => {
    // If usedSources exists, filter the sources to only include those that were cited
    if (report.usedSources && report.usedSources.length > 0 && report.sources) {
      // Convert 1-based indices to 0-based for our array
      const usedIndices = report.usedSources.map((num) => num - 1)
      return report.sources.filter((_, index) =>
        // Check if this source's index is in the usedSources array
        usedIndices.includes(index)
      )
    }
    // Default to all sources if no usedSources specified
    return report.sources
  }, [report.sources, report.usedSources])

  // If no filtered sources, don't render anything
  if (!filteredSources || filteredSources.length === 0) {
    return null
  }

  return (
    <div className='space-y-2 border-t pt-4 mt-6'>
      <h3 className='text-xl font-semibold text-gray-700'>References</h3>
      <ol className='list-decimal pl-5'>
        {filteredSources.map((source) => (
          <li key={source.id} className='mb-1'>
            <a
              href={source.url}
              target='_blank'
              rel='noopener noreferrer'
              className='text-blue-600 hover:underline'
            >
              {source.name}
            </a>
          </li>
        ))}
      </ol>
    </div>
  )
}



================================================
FILE: components/knowledge-base-sidebar.tsx
================================================
import { Brain, Search, Trash2, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { formatDistanceToNow } from 'date-fns'
import { useKnowledgeBase } from '@/hooks/use-knowledge-base'
import { useState } from 'react'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import Link from 'next/link'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface KnowledgeBaseSidebarProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function KnowledgeBaseSidebar({
  open,
  onOpenChange,
}: KnowledgeBaseSidebarProps) {
  const { reports, searchReports, clearAllReports } = useKnowledgeBase()
  const [searchQuery, setSearchQuery] = useState('')
  const [showClearConfirm, setShowClearConfirm] = useState(false)

  const filteredReports = searchQuery ? searchReports(searchQuery) : reports

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side='left' className='w-[400px] sm:w-[540px]'>
        <SheetHeader>
          <div className='flex items-center justify-between'>
            <SheetTitle className='flex items-center gap-2'>
              <Brain className='h-6 w-6' />
              Knowledge Base
            </SheetTitle>
          </div>
        </SheetHeader>
        <div className='mt-8 space-y-4'>
          {showClearConfirm && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertTitle>Clear All Reports?</AlertTitle>
              <AlertDescription className='space-y-2'>
                <p>
                  This will permanently delete all {reports.length} report
                  {reports.length === 1 ? '' : 's'} from your knowledge base.
                </p>
                <div className='flex gap-2 mt-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => setShowClearConfirm(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant='destructive'
                    size='sm'
                    onClick={() => {
                      clearAllReports()
                      setShowClearConfirm(false)
                    }}
                  >
                    Yes, Clear All
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {reports.length > 0 && !showClearConfirm && (
            <Alert>
              <AlertTitle className='font-semibold'>Current Status</AlertTitle>
              <AlertDescription>
                You have {reports.length} saved report
                {reports.length === 1 ? '' : 's'}. Click on a report to view
                details or use the search to find specific reports.
              </AlertDescription>
            </Alert>
          )}
          <div className='relative'>
            <Input
              type='text'
              placeholder='Search saved reports...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='pr-8'
            />
            <Search className='absolute right-2 top-2.5 h-4 w-4 text-gray-400' />
          </div>
          <ScrollArea className='h-[calc(100vh-280px)]'>
            <div className='space-y-2'>
              {filteredReports.map((savedReport) => (
                <Card
                  key={savedReport.id}
                  className='p-4 hover:bg-gray-50 transition-colors'
                >
                  <div className='flex justify-between items-start gap-2'>
                    <Link
                      href={`/report/${savedReport.id}`}
                      className='flex-1 min-w-0 cursor-pointer'
                    >
                      <h3 className='font-semibold truncate'>
                        {savedReport.report.title}
                      </h3>
                      <p className='text-sm text-gray-500 truncate'>
                        {savedReport.query}
                      </p>
                      <p className='text-xs text-gray-400'>
                        {formatDistanceToNow(savedReport.timestamp, {
                          addSuffix: true,
                        })}
                      </p>
                    </Link>
                  </div>
                </Card>
              ))}
              {filteredReports.length === 0 && (
                <Alert variant='destructive'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertTitle>No Reports Found</AlertTitle>
                  <AlertDescription>
                    {searchQuery
                      ? 'No reports match your search query. Try a different search term.'
                      : 'Your knowledge base is empty. Save some reports to get started.'}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </ScrollArea>
        </div>
        <div className='absolute bottom-0 left-0 right-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
          <Button
            variant='ghost'
            onClick={() => setShowClearConfirm(true)}
            className='relative w-full rounded-none h-16 hover:bg-destructive/10'
          >
            <div className='absolute left-4 top-1/2 -translate-y-1/2'>
              <div className='relative'>
                <Trash2 className='h-5 w-5 text-muted-foreground group-hover:text-destructive transition-colors' />
              </div>
            </div>
            <span className='text-sm font-medium text-muted-foreground group-hover:text-destructive transition-colors'>
              Clear All Reports
            </span>
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  )
}



================================================
FILE: components/model-select.tsx
================================================
'use client'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { PlatformModel } from '@/types'
import { CONFIG } from '@/lib/config'

const DEFAULT_MODEL = 'google__gemini-flash'

export const platformModels = Object.entries(CONFIG.platforms)
  .flatMap(([platform, config]) => {
    if (!config.enabled) return []

    return Object.entries(config.models).map(([modelId, modelConfig]) => {
      return {
        value: `${platform}__${modelId}`,
        label: `${platform.charAt(0).toUpperCase() + platform.slice(1)} - ${
          modelConfig.label
        }`,
        platform,
        disabled: !modelConfig.enabled,
      }
    })
  })
  .filter(Boolean) as (PlatformModel & { disabled: boolean })[]

interface ModelSelectProps {
  value: string
  onValueChange: (value: string) => void
  triggerClassName?: string
}

export function ModelSelect({
  value = DEFAULT_MODEL,
  onValueChange,
  triggerClassName,
}: ModelSelectProps) {
  return (
    <Select
      value={value}
      onValueChange={onValueChange}
      disabled={platformModels.length === 0}
    >
      <SelectTrigger className={triggerClassName}>
        <SelectValue
          placeholder={
            platformModels.length === 0 ? 'No models available' : 'Select model'
          }
        />
      </SelectTrigger>
      <SelectContent>
        {platformModels.map((model) => (
          <SelectItem
            key={model.value}
            value={model.value}
            disabled={model.disabled}
            className={model.disabled ? 'text-gray-400 cursor-not-allowed' : ''}
          >
            {model.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

export { DEFAULT_MODEL }



================================================
FILE: components/report-actions.tsx
================================================
import { Button } from '@/components/ui/button'
import { Brain, Download, Copy } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/hooks/use-toast'
import { useKnowledgeBase } from '@/hooks/use-knowledge-base'
import type { Report } from '@/types'

interface ReportActionsProps {
  report: Report
  prompt?: string
  size?: 'default' | 'sm'
  variant?: 'default' | 'outline'
  className?: string
  hideKnowledgeBase?: boolean
}

export function ReportActions({
  report,
  prompt,
  size = 'sm',
  variant = 'outline',
  className = '',
  hideKnowledgeBase = false,
}: ReportActionsProps) {
  const { addReport, reports } = useKnowledgeBase()
  const { toast } = useToast()

  // Check if report is already saved by comparing title and summary
  const isReportSaved = reports.some(
    (savedReport) =>
      savedReport.report.title === report.title &&
      savedReport.report.summary === report.summary
  )

  const handleDownload = async (format: 'pdf' | 'docx' | 'txt') => {
    try {
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          report,
          format,
        }),
      })

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `report.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      toast({
        title: 'Download failed',
        description: error instanceof Error ? error.message : 'Download failed',
        variant: 'destructive',
      })
    }
  }

  const handleSaveToKnowledgeBase = () => {
    if (isReportSaved) return
    const success = addReport(report, prompt || '')
    if (success) {
      toast({
        title: 'Saved to Knowledge Base',
        description: 'The report has been saved for future reference',
      })
    }
  }

  const handleCopy = async () => {
    try {
      let formattedContent = `${report.title}\n\n${
        report.summary
      }\n\n${report.sections
        .map((section) => `${section.title}\n${section.content}`)
        .join('\n\n')}`

      // Filter sources if usedSources is available
      const filteredSources =
        report.usedSources && report.usedSources.length > 0 && report.sources
          ? report.sources.filter((_, index) =>
              report.usedSources!.map((num) => num - 1).includes(index)
            )
          : report.sources

      // Add citations if filtered sources are available
      if (filteredSources && filteredSources.length > 0) {
        formattedContent +=
          '\n\nReferences:\n' +
          filteredSources
            .map(
              (source, index) => `${index + 1}. ${source.name} - ${source.url}`
            )
            .join('\n')
      }

      await navigator.clipboard.writeText(formattedContent)
      toast({
        title: 'Copied',
        description: 'Report content copied to clipboard',
      })
    } catch (error) {
      toast({
        title: 'Copy failed',
        description:
          error instanceof Error ? error.message : 'Failed to copy content',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className={`flex gap-2 ${className}`}>
      <Button
        variant={variant}
        size={size}
        className='gap-2'
        onClick={handleCopy}
        title='Copy report'
      >
        <Copy className='h-4 w-4' />
        <span className='hidden sm:inline'>Copy</span>
      </Button>
      {!hideKnowledgeBase && (
        <Button
          variant={variant}
          size={size}
          className='gap-2'
          onClick={handleSaveToKnowledgeBase}
          disabled={isReportSaved}
          title={isReportSaved ? 'Already saved' : 'Save to Knowledge Base'}
        >
          <Brain className='h-4 w-4' />
          <span className='hidden sm:inline'>
            {isReportSaved ? 'Saved' : 'Save to Knowledge Base'}
          </span>
        </Button>
      )}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className='gap-2'
            title='Download'
          >
            <Download className='h-4 w-4' />
            <span className='hidden sm:inline'>Download</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem onClick={() => handleDownload('pdf')}>
            Download as PDF
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleDownload('docx')}>
            Download as Word
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleDownload('txt')}>
            Download as Text
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}



================================================
FILE: components/flow/consolidated-edge.tsx
================================================
import { getBezierPath, EdgeProps } from '@xyflow/react'
import { memo } from 'react'

// Define constants for edge styling
const EDGE_STYLES = {
  strokeWidth: 2,
  stroke: '#6366f1', // Indigo color for consolidated edges
  strokeDasharray: '5,5', // Dashed line pattern
}

export const ConsolidatedEdge = memo(function ConsolidatedEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
}: EdgeProps) {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  })

  // Combine default edge styles with any custom styles
  const combinedStyles = {
    ...EDGE_STYLES,
    ...style,
  }

  return (
    <path
      id={id}
      style={combinedStyles}
      className='react-flow__edge-path'
      d={edgePath}
      markerEnd={markerEnd}
    />
  )
})



================================================
FILE: components/flow/project-actions.tsx
================================================
import { useState, useRef } from 'react'
import { Download, Upload, Info, Database, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useToast } from '@/hooks/use-toast'

interface ProjectActionsProps {
  exportProjects: () => string
  importProjects: (jsonData: string) => boolean
  storageInfo: {
    usage: number
    usagePercent: number
    available: number
    limit: number
    formattedUsage: string
    formattedAvailable: string
  }
  refreshStorageInfo: () => void
}

export function ProjectActions({
  exportProjects,
  importProjects,
  storageInfo,
  refreshStorageInfo,
}: ProjectActionsProps) {
  const [isStorageInfoOpen, setIsStorageInfoOpen] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const handleExport = () => {
    try {
      const jsonData = exportProjects()
      if (jsonData === '[]') {
        toast({
          title: 'No projects to export',
          description: "You don't have any projects saved to export.",
          variant: 'destructive',
        })
        return
      }

      const blob = new Blob([jsonData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `open-deep-research-projects-${
        new Date().toISOString().split('T')[0]
      }.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: 'Projects exported',
        description: 'Your projects have been exported successfully.',
      })
    } catch (error) {
      console.error('Export error:', error)
      toast({
        title: 'Export failed',
        description: 'There was an error exporting your projects.',
        variant: 'destructive',
      })
    }
  }

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      try {
        const jsonData = event.target?.result as string
        const success = importProjects(jsonData)

        if (success) {
          toast({
            title: 'Projects imported',
            description: 'Your projects have been imported successfully.',
          })
          refreshStorageInfo()
        } else {
          toast({
            title: 'Import failed',
            description: 'The file format is invalid or corrupted.',
            variant: 'destructive',
          })
        }
      } catch (error) {
        console.error('Import error:', error)
        toast({
          title: 'Import failed',
          description: 'There was an error importing your projects.',
          variant: 'destructive',
        })
      }
    }

    reader.readAsText(file)

    // Reset the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const getStorageStatusColor = (percent: number) => {
    if (percent < 50) return 'bg-green-500'
    if (percent < 80) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  return (
    <>
      <input
        type='file'
        ref={fileInputRef}
        onChange={handleFileChange}
        accept='.json'
        className='hidden'
      />

      <TooltipProvider>
        <DropdownMenu>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' className='h-9 w-9'>
                  <Database className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Project Data & Storage</p>
            </TooltipContent>
          </Tooltip>

          <DropdownMenuContent align='end' className='w-56'>
            <div className='px-2 py-1.5 text-sm font-medium text-gray-500'>
              Storage & Backup
            </div>
            <DropdownMenuSeparator />

            <div className='px-3 py-2'>
              <div className='text-xs text-gray-500 mb-1 flex justify-between'>
                <span>Storage Usage</span>
                <span>{storageInfo.formattedUsage} / 5 MB</span>
              </div>
              <div className='w-full h-1.5 bg-gray-200 rounded-full overflow-hidden'>
                <div
                  className={`h-full ${getStorageStatusColor(
                    storageInfo.usagePercent
                  )}`}
                  style={{
                    width: `${Math.min(100, storageInfo.usagePercent)}%`,
                  }}
                />
              </div>
            </div>

            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleExport} className='gap-2'>
              <Download className='h-4 w-4' />
              <div>
                <div className='text-sm'>Export Projects</div>
                <div className='text-xs text-gray-500'>
                  Backup your projects as JSON
                </div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleImportClick} className='gap-2'>
              <Upload className='h-4 w-4' />
              <div>
                <div className='text-sm'>Import Projects</div>
                <div className='text-xs text-gray-500'>Restore from backup</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => setIsStorageInfoOpen(true)}
              className='gap-2'
            >
              <Info className='h-4 w-4' />
              <div className='text-sm'>Storage Details</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TooltipProvider>

      <Dialog open={isStorageInfoOpen} onOpenChange={setIsStorageInfoOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>localStorage Usage</DialogTitle>
            <DialogDescription>
              Your research projects are stored in your browser&apos;s localStorage
            </DialogDescription>
          </DialogHeader>

          <div className='mt-4 space-y-4'>
            <div>
              <div className='flex justify-between text-sm mb-1'>
                <span>Storage Usage</span>
                <span>{storageInfo.formattedUsage} / 5 MB</span>
              </div>
              <div className='w-full h-2 bg-gray-200 rounded-full overflow-hidden'>
                <div
                  className={`h-full ${getStorageStatusColor(
                    storageInfo.usagePercent
                  )}`}
                  style={{
                    width: `${Math.min(100, storageInfo.usagePercent)}%`,
                  }}
                />
              </div>
            </div>

            {storageInfo.usagePercent > 80 && (
              <div className='flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md'>
                <AlertTriangle className='h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5' />
                <div className='text-sm text-amber-800'>
                  <p className='font-medium'>Storage is running low</p>
                  <p>
                    Consider exporting and deleting some projects to free up
                    space.
                  </p>
                </div>
              </div>
            )}

            <div className='text-sm space-y-2'>
              <p>
                <strong>Available Space:</strong>{' '}
                {storageInfo.formattedAvailable}
              </p>
              <p>
                <strong>Note:</strong> localStorage has a limit of approximately
                5MB per domain. Data is stored only in this browser and will be
                lost if you clear browser data.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsStorageInfoOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}



================================================
FILE: components/flow/project-selector.tsx
================================================
import { useState } from 'react'
import { PlusCircle, FolderOpen, Trash2, Edit, Check, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useToast } from '@/hooks/use-toast'
import type { FlowProject } from '@/hooks/use-flow-projects'

interface ProjectSelectorProps {
  projects: FlowProject[]
  currentProject: FlowProject | null
  onSelectProject: (project: FlowProject) => void
  onCreateProject: (name: string) => void
  onDeleteProject: (id: string) => void
  onRenameProject: (id: string, name: string) => void
}

export function ProjectSelector({
  projects,
  currentProject,
  onSelectProject,
  onCreateProject,
  onDeleteProject,
  onRenameProject,
}: ProjectSelectorProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [newProjectName, setNewProjectName] = useState('')
  const [projectToDelete, setProjectToDelete] = useState<FlowProject | null>(
    null
  )
  const [editingProject, setEditingProject] = useState<FlowProject | null>(null)
  const [editName, setEditName] = useState('')
  const { toast } = useToast()

  const handleCreateProject = () => {
    if (!newProjectName.trim()) {
      toast({
        title: 'Project name required',
        description: 'Please enter a name for your project',
        variant: 'destructive',
      })
      return
    }

    onCreateProject(newProjectName)
    setNewProjectName('')
    setIsCreateDialogOpen(false)

    toast({
      title: 'Project created',
      description: `"${newProjectName}" has been created successfully.`,
    })
  }

  const handleDeleteProject = () => {
    if (projectToDelete) {
      onDeleteProject(projectToDelete.id)
      setProjectToDelete(null)
      setIsDeleteDialogOpen(false)

      toast({
        title: 'Project deleted',
        description: `"${projectToDelete.name}" has been deleted.`,
      })
    }
  }

  const handleSaveRename = (project: FlowProject) => {
    if (editName.trim()) {
      onRenameProject(project.id, editName)

      toast({
        title: 'Project renamed',
        description: `Project has been renamed to "${editName}".`,
      })
    }
    setEditingProject(null)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date)
  }

  const handleSelectProject = (project: FlowProject) => {
    if (currentProject?.id !== project.id) {
      onSelectProject(project)

      toast({
        title: 'Switching projects',
        description: `Loading "${project.name}"...`,
      })

      setTimeout(() => {
        window.location.reload()
      }, 300)
    }
  }

  return (
    <>
      <TooltipProvider>
        <DropdownMenu>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='outline'
                  size='sm'
                  className='h-9 text-sm font-medium gap-1.5 px-2.5'
                >
                  <FolderOpen className='h-4 w-4' />
                  <span className='max-w-[120px] truncate'>
                    {currentProject ? currentProject.name : 'Select Project'}
                  </span>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Switch or manage projects</p>
            </TooltipContent>
          </Tooltip>

          <DropdownMenuContent align='end' className='w-[260px]'>
            <div className='px-2 py-1.5 text-sm font-medium text-gray-500 flex justify-between items-center'>
              <span>Your Projects</span>
              <span className='text-xs text-gray-400'>
                {projects.length} project{projects.length !== 1 ? 's' : ''}
              </span>
            </div>
            <DropdownMenuSeparator />

            <div className='max-h-[300px] overflow-y-auto py-1'>
              {projects.length === 0 ? (
                <div className='px-2 py-4 text-center text-sm text-gray-500'>
                  No projects yet. Create your first one!
                </div>
              ) : (
                projects.map((project) => (
                  <div key={project.id} className='px-1 py-0.5'>
                    {editingProject?.id === project.id ? (
                      <div className='flex items-center gap-1 p-1'>
                        <Input
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className='h-7 text-xs'
                          autoFocus
                        />
                        <Button
                          variant='ghost'
                          size='icon'
                          className='h-6 w-6'
                          onClick={() => handleSaveRename(project)}
                        >
                          <Check className='h-3 w-3' />
                        </Button>
                        <Button
                          variant='ghost'
                          size='icon'
                          className='h-6 w-6'
                          onClick={() => setEditingProject(null)}
                        >
                          <X className='h-3 w-3' />
                        </Button>
                      </div>
                    ) : (
                      <DropdownMenuItem
                        className={`flex justify-between items-center p-2 ${
                          currentProject?.id === project.id
                            ? 'bg-gray-100 dark:bg-gray-800'
                            : ''
                        }`}
                      >
                        <div
                          className='flex-1 overflow-hidden text-ellipsis'
                          onClick={() => handleSelectProject(project)}
                        >
                          <div className='font-medium'>{project.name}</div>
                          <div className='text-xs text-gray-500'>
                            Updated {formatDate(project.updatedAt)}
                          </div>
                        </div>
                        <div className='flex gap-1 ml-2'>
                          <Button
                            variant='ghost'
                            size='icon'
                            className='h-6 w-6'
                            onClick={(e) => {
                              e.stopPropagation()
                              setEditingProject(project)
                              setEditName(project.name)
                            }}
                          >
                            <Edit className='h-3 w-3' />
                          </Button>
                          <Button
                            variant='ghost'
                            size='icon'
                            className='h-6 w-6 text-red-500 hover:text-red-600'
                            onClick={(e) => {
                              e.stopPropagation()
                              setProjectToDelete(project)
                              setIsDeleteDialogOpen(true)
                            }}
                          >
                            <Trash2 className='h-3 w-3' />
                          </Button>
                        </div>
                      </DropdownMenuItem>
                    )}
                  </div>
                ))
              )}
            </div>

            <DropdownMenuSeparator />
            <DropdownMenuItem
              className='cursor-pointer text-primary focus:text-primary focus:bg-primary/10'
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <PlusCircle className='mr-2 h-4 w-4' />
              Create New Project
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TooltipProvider>

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Enter a name for your new research project
            </DialogDescription>
          </DialogHeader>
          <Input
            value={newProjectName}
            onChange={(e) => setNewProjectName(e.target.value)}
            placeholder='Project name'
            className='mt-4'
            autoFocus
          />
          <DialogFooter className='mt-4'>
            <Button
              variant='outline'
              onClick={() => setIsCreateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateProject}>Create Project</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Project</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;{projectToDelete?.name}
              &quot;? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className='mt-4'>
            <Button
              variant='outline'
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant='destructive' onClick={handleDeleteProject}>
              Delete Project
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}



================================================
FILE: components/flow/question-node.tsx
================================================
import { memo } from 'react'
import { Handle, Position } from '@xyflow/react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Search, Loader2, AlertTriangle } from 'lucide-react'
import { SearchTermsNodeData } from '@/types'

export const QuestionNode = memo(function SearchTermsNode({
  data,
}: {
  data: SearchTermsNodeData
}) {
  const handleApprove = (term: string) => {
    if (typeof data.onApprove !== 'function') {
      console.error('No onApprove handler defined')
      return
    }
    data.onApprove(term)
  }

  const hasTerms =
    Array.isArray(data.searchTerms) && data.searchTerms.length > 0

  return (
    <div className='w-[400px]'>
      <Card className='overflow-hidden'>
        <Handle type='target' position={Position.Top} />
        <CardContent className='p-4'>
          {data.loading ? (
            <div className='flex items-center gap-3'>
              <Loader2 className='h-5 w-5 animate-spin text-blue-500' />
              <p>Generating search terms...</p>
            </div>
          ) : data.error ? (
            <div className='flex items-center gap-3 text-red-500'>
              <AlertTriangle className='h-5 w-5' />
              <p>{data.error}</p>
            </div>
          ) : hasTerms ? (
            <div className='space-y-4'>
              <div className='flex items-center gap-2'>
                <Search className='h-5 w-5 text-blue-500' />
                <h3 className='font-medium'>Follow-up Queries</h3>
              </div>
              <div className='space-y-2'>
                {data.searchTerms &&
                  data.searchTerms.map((term, index) => (
                    <div
                      key={index}
                      className='flex items-center justify-between gap-2 p-2 rounded bg-gray-50 hover:bg-gray-100 transition-colors'
                    >
                      <p className='text-sm text-gray-600 flex-1'>{term}</p>
                      <Button
                        size='sm'
                        variant='ghost'
                        onClick={() => handleApprove(term)}
                        className='h-8 shrink-0'
                        title='Search this term'
                      >
                        <Search className='h-4 w-4' />
                      </Button>
                    </div>
                  ))}
              </div>
            </div>
          ) : (
            <div className='text-center text-gray-500 py-2'>
              <Search className='h-5 w-5 mx-auto mb-2 opacity-40' />
              <p>No suggestions available</p>
            </div>
          )}
        </CardContent>
        <Handle type='source' position={Position.Bottom} />
      </Card>
    </div>
  )
})



================================================
FILE: components/flow/report-node.tsx
================================================
import { memo } from 'react'
import { Handle, Position } from '@xyflow/react'
import { Card, CardContent } from '@/components/ui/card'
import { AlertTriangle, Loader2 } from 'lucide-react'
import type { ReportNodeData } from '@/types'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { ReportActions } from '@/components/report-actions'
import { Checkbox } from '@/components/ui/checkbox'
import { CitationsFooter } from '@/components/citations-footer'

export const ReportNode = memo(function ReportNode({
  id,
  data,
}: {
  id: string
  data: ReportNodeData
}) {
  const {
    report,
    loading,
    error,
    isSelected,
    isConsolidated,
    onSelect,
    isConsolidating,
  } = data

  return (
    <div className='w-[600px]'>
      <Handle type='target' position={Position.Top} />
      <Card
        className={`${isSelected ? 'ring-2 ring-blue-500' : ''} ${
          isConsolidated ? 'border border-yellow-500' : ''
        }`}
      >
        <CardContent className='p-6 space-y-4'>
          {loading ? (
            <div className='flex items-center justify-center p-4'>
              <Loader2 className='h-6 w-6 animate-spin' />
            </div>
          ) : error ? (
            <div className='flex items-center gap-2 text-red-500 text-center p-4'>
              <AlertTriangle className='h-5 w-5' />
              <span>{error}</span>
            </div>
          ) : report ? (
            <div className='space-y-4'>
              <div className='flex items-baseline gap-3'>
                {onSelect && (
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => onSelect(id)}
                    disabled={isConsolidating}
                  />
                )}
                <h2 className='text-xl font-bold text-gray-800'>
                  {report.title}
                </h2>
              </div>

              <div className='flex justify-start'>
                <ReportActions report={report} size='sm' />
              </div>

              <div className='max-h-[500px] overflow-y-auto pr-2 nowheel nodrag'>
                <p className='text-gray-700 mb-4'>{report.summary}</p>

                {report.sections?.map((section, index) => (
                  <div key={index} className='space-y-2 border-t pt-4 mb-4'>
                    <h3 className='text-lg font-semibold text-gray-700'>
                      {section.title}
                    </h3>
                    <div className='prose max-w-none text-gray-600'>
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {section.content}
                      </ReactMarkdown>
                    </div>
                  </div>
                ))}

                {/* Citations Section */}
                <CitationsFooter report={report} />
              </div>
            </div>
          ) : (
            <div className='text-gray-500 text-center p-4'>
              No report data available
            </div>
          )}
        </CardContent>
      </Card>
      <Handle type='source' position={Position.Bottom} />
    </div>
  )
})



================================================
FILE: components/flow/search-node.tsx
================================================
import { memo, useState } from 'react'
import { Handle, Position } from '@xyflow/react'
import { Card, CardContent } from '@/components/ui/card'
import { Search, Loader2, Upload, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SUPPORTED_FILE_TYPES } from '@/lib/file-upload'
import { SearchNodeData } from '@/types'

export const SearchNode = memo(function SearchNode({
  data,
}: {
  data: SearchNodeData
}) {
  const [uploadError, setUploadError] = useState<string | null>(null)

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUploadError(null)
    const file = e.target.files?.[0]

    if (!file) return

    if (!data.onFileUpload) {
      setUploadError('File upload is not available')
      return
    }

    try {
      data.onFileUpload(file)
      e.target.value = ''
    } catch (err) {
      setUploadError(
        `Upload failed: ${err instanceof Error ? err.message : 'Unknown error'}`
      )
    }
  }

  return (
    <div className='w-[400px]'>
      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between gap-3'>
            {/* Icon and Query Section */}
            <div className='flex items-start gap-3 flex-1 min-w-0'>
              <div className='p-2 rounded-full bg-blue-50'>
                {data.loading ? (
                  <Loader2 className='h-4 w-4 animate-spin text-blue-500' />
                ) : (
                  <Search className='h-4 w-4 text-blue-500' />
                )}
              </div>
              <div className='flex-1 min-w-0'>
                <h3 className='font-medium text-sm text-gray-900'>
                  Search Query
                </h3>
                <p className='text-sm text-gray-600 truncate'>{data.query}</p>
              </div>
            </div>

            {/* Upload Button */}
            {data.onFileUpload && (
              <div className='relative flex-shrink-0'>
                <Input
                  type='file'
                  onChange={handleFileUpload}
                  className='absolute inset-0 opacity-0 cursor-pointer'
                  accept={SUPPORTED_FILE_TYPES}
                />
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  className='pointer-events-none'
                  disabled={data.loading}
                >
                  <Upload className='h-4 w-4 mr-2' />
                  Upload
                </Button>
              </div>
            )}
          </div>

          {uploadError && (
            <div className='mt-2 text-sm text-red-600 flex items-center gap-1'>
              <AlertCircle className='h-3 w-3' />
              <span>{uploadError}</span>
            </div>
          )}
        </CardContent>

        <Handle
          type='source'
          position={Position.Bottom}
          className='!bg-blue-500'
        />
      </Card>
    </div>
  )
})



================================================
FILE: components/flow/selection-node.tsx
================================================
import { memo, useState, useEffect } from 'react'
import { Handle, Position } from '@xyflow/react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { FileText, AlertTriangle, Loader2 } from 'lucide-react'
import { Input } from '@/components/ui/input'
import type { SearchResult, SelectionNodeData } from '@/types'
import { CONFIG } from '@/lib/config'

const MAX_SELECTIONS = CONFIG.search.maxSelectableResults

export const SelectionNode = memo(function SelectionNode({
  data,
}: {
  data: SelectionNodeData
}) {
  const [selectedResults, setSelectedResults] = useState<SearchResult[]>([])
  const [prompt, setPrompt] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  // Reset selection when results change
  useEffect(() => {
    setSelectedResults([])
    setError(null)
  }, [data.results])

  const handleSelect = (result: SearchResult) => {
    setSelectedResults((prev) => {
      if (prev.find((r) => r.id === result.id)) {
        return prev.filter((r) => r.id !== result.id)
      }
      if (prev.length >= MAX_SELECTIONS) return prev
      return [...prev, result]
    })
  }

  const handleGenerateReport = () => {
    if (selectedResults.length === 0) {
      setError('Please select at least one result')
      return
    }

    // Clear any previous errors
    setError(null)

    // Check if onGenerateReport is defined
    if (typeof data.onGenerateReport !== 'function') {
      setError('Report generation is not available')
      return
    }

    setIsGenerating(true)

    try {
      // Call onGenerateReport and capture the result
      const result = data.onGenerateReport(selectedResults, prompt)

      // Check if the result is a Promise using Promise.resolve
      Promise.resolve(result)
        .then(() => {
          // Report generation completed successfully
        })
        .catch((err) => {
          setError(
            `Error generating report: ${
              err instanceof Error ? err.message : 'Unknown error'
            }`
          )
        })
        .finally(() => {
          setIsGenerating(false)
        })
    } catch (err) {
      setIsGenerating(false)
      setError(
        `Error generating report: ${
          err instanceof Error ? err.message : 'Unknown error'
        }`
      )
    }
  }

  const isSelectionValid = selectedResults.length > 0 && !error

  return (
    <div className='w-auto max-w-[600px] mx-auto'>
      <Card>
        <Handle type='target' position={Position.Top} />
        <CardContent className='p-6'>
          <div className='space-y-6'>
            <div className='flex items-center justify-between'>
              <h3 className='font-medium text-lg flex items-center gap-3'>
                <FileText className='h-6 w-6 text-blue-500' />
                Search Results
              </h3>
              <Button
                size='default'
                disabled={!isSelectionValid || isGenerating}
                onClick={handleGenerateReport}
                className='gap-2'
              >
                {isGenerating ? (
                  <Loader2 className='h-4 w-4 animate-spin' />
                ) : (
                  <FileText className='h-4 w-4' />
                )}
                {isGenerating
                  ? 'Generating...'
                  : `Generate Report (${selectedResults.length})`}
              </Button>
            </div>

            {error && (
              <div className='bg-red-50 border border-red-200 p-3 rounded-md flex items-center gap-2 text-red-700'>
                <AlertTriangle className='h-4 w-4' />
                <p className='text-sm'>{error}</p>
              </div>
            )}

            <p className='text-gray-600'>
              Select up to {MAX_SELECTIONS} results to analyze (
              {selectedResults.length} selected)
            </p>

            {selectedResults.length > 0 && (
              <Input
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder='What would you like to know about these sources?'
              />
            )}

            <div className='space-y-4 max-h-[400px] overflow-y-auto pr-4 nowheel nodrag'>
              {data.results.map((result) => (
                <div
                  key={result.id}
                  className='flex items-start gap-4 bg-gray-50 p-4 rounded-lg'
                >
                  <div className='pt-1'>
                    <Checkbox
                      checked={selectedResults.some((r) => r.id === result.id)}
                      onCheckedChange={() => handleSelect(result)}
                      disabled={
                        (!selectedResults.some((r) => r.id === result.id) &&
                          selectedResults.length >= MAX_SELECTIONS) ||
                        isGenerating
                      }
                    />
                  </div>
                  <div className='space-y-2 flex-1'>
                    <a
                      href={result.url}
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-blue-600 hover:underline font-medium block'
                    >
                      {result.name}
                    </a>
                    <p className='text-sm text-green-700 truncate'>
                      {result.url}
                    </p>
                    <p className='text-gray-600 line-clamp-3'>
                      {result.snippet}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
        <Handle type='source' position={Position.Bottom} />
      </Card>
    </div>
  )
})



================================================
FILE: components/ui/alert.tsx
================================================
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }



================================================
FILE: components/ui/button.tsx
================================================
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }



================================================
FILE: components/ui/card.tsx
================================================
import * as React from "react"

import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-xl border bg-card text-card-foreground shadow",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }



================================================
FILE: components/ui/checkbox.tsx
================================================
"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center text-current")}
    >
      <Check className="h-4 w-4" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }



================================================
FILE: components/ui/collapsible.tsx
================================================
"use client"

import * as CollapsiblePrimitive from "@radix-ui/react-collapsible"

const Collapsible = CollapsiblePrimitive.Root

const CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger

const CollapsibleContent = CollapsiblePrimitive.CollapsibleContent

export { Collapsible, CollapsibleTrigger, CollapsibleContent }



================================================
FILE: components/ui/dialog.tsx
================================================
"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}



================================================
FILE: components/ui/dropdown-menu.tsx
================================================
"use client"

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const DropdownMenu = DropdownMenuPrimitive.Root

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuGroup = DropdownMenuPrimitive.Group

const DropdownMenuPortal = DropdownMenuPrimitive.Portal

const DropdownMenuSub = DropdownMenuPrimitive.Sub

const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <DropdownMenuPrimitive.SubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
      inset && "pl-8",
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className="ml-auto" />
  </DropdownMenuPrimitive.SubTrigger>
))
DropdownMenuSubTrigger.displayName =
  DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
))
DropdownMenuSubContent.displayName =
  DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md",
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <DropdownMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.CheckboxItem>
))
DropdownMenuCheckboxItem.displayName =
  DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.RadioItem>
))
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest opacity-60", className)}
      {...props}
    />
  )
}
DropdownMenuShortcut.displayName = "DropdownMenuShortcut"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
}



================================================
FILE: components/ui/input.tsx
================================================
import * as React from "react"

import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }



================================================
FILE: components/ui/scroll-area.tsx
================================================
"use client"

import * as React from "react"
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area"

import { cn } from "@/lib/utils"

const ScrollArea = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <ScrollAreaPrimitive.Root
    ref={ref}
    className={cn("relative overflow-hidden", className)}
    {...props}
  >
    <ScrollAreaPrimitive.Viewport className="h-full w-full rounded-[inherit]">
      {children}
    </ScrollAreaPrimitive.Viewport>
    <ScrollBar />
    <ScrollAreaPrimitive.Corner />
  </ScrollAreaPrimitive.Root>
))
ScrollArea.displayName = ScrollAreaPrimitive.Root.displayName

const ScrollBar = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>
>(({ className, orientation = "vertical", ...props }, ref) => (
  <ScrollAreaPrimitive.ScrollAreaScrollbar
    ref={ref}
    orientation={orientation}
    className={cn(
      "flex touch-none select-none transition-colors",
      orientation === "vertical" &&
        "h-full w-2.5 border-l border-l-transparent p-[1px]",
      orientation === "horizontal" &&
        "h-2.5 flex-col border-t border-t-transparent p-[1px]",
      className
    )}
    {...props}
  >
    <ScrollAreaPrimitive.ScrollAreaThumb className="relative flex-1 rounded-full bg-border" />
  </ScrollAreaPrimitive.ScrollAreaScrollbar>
))
ScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName

export { ScrollArea, ScrollBar }



================================================
FILE: components/ui/select.tsx
================================================
"use client"

import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"

import { cn } from "@/lib/utils"

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
      className
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold", className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}



================================================
FILE: components/ui/separator.tsx
================================================
"use client"

import * as React from "react"
import * as SeparatorPrimitive from "@radix-ui/react-separator"

import { cn } from "@/lib/utils"

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(
  (
    { className, orientation = "horizontal", decorative = true, ...props },
    ref
  ) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        "shrink-0 bg-border",
        orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
        className
      )}
      {...props}
    />
  )
)
Separator.displayName = SeparatorPrimitive.Root.displayName

export { Separator }



================================================
FILE: components/ui/sheet.tsx
================================================
"use client"

import * as React from "react"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Sheet = SheetPrimitive.Root

const SheetTrigger = SheetPrimitive.Trigger

const SheetClose = SheetPrimitive.Close

const SheetPortal = SheetPrimitive.Portal

const SheetOverlay = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Overlay
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
    ref={ref}
  />
))
SheetOverlay.displayName = SheetPrimitive.Overlay.displayName

const sheetVariants = cva(
  "fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom:
          "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
        right:
          "inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",
      },
    },
    defaultVariants: {
      side: "right",
    },
  }
)

interface SheetContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
    VariantProps<typeof sheetVariants> {}

const SheetContent = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Content>,
  SheetContentProps
>(({ side = "right", className, children, ...props }, ref) => (
  <SheetPortal>
    <SheetOverlay />
    <SheetPrimitive.Content
      ref={ref}
      className={cn(sheetVariants({ side }), className)}
      {...props}
    >
      <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </SheetPrimitive.Close>
      {children}
    </SheetPrimitive.Content>
  </SheetPortal>
))
SheetContent.displayName = SheetPrimitive.Content.displayName

const SheetHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-2 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
SheetHeader.displayName = "SheetHeader"

const SheetFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
SheetFooter.displayName = "SheetFooter"

const SheetTitle = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Title
    ref={ref}
    className={cn("text-lg font-semibold text-foreground", className)}
    {...props}
  />
))
SheetTitle.displayName = SheetPrimitive.Title.displayName

const SheetDescription = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
SheetDescription.displayName = SheetPrimitive.Description.displayName

export {
  Sheet,
  SheetPortal,
  SheetOverlay,
  SheetTrigger,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetFooter,
  SheetTitle,
  SheetDescription,
}



================================================
FILE: components/ui/sidebar.tsx
================================================
"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { VariantProps, cva } from "class-variance-authority"
import { PanelLeft } from "lucide-react"

import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const SIDEBAR_COOKIE_NAME = "sidebar:state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7
const SIDEBAR_WIDTH = "16rem"
const SIDEBAR_WIDTH_MOBILE = "18rem"
const SIDEBAR_WIDTH_ICON = "3rem"
const SIDEBAR_KEYBOARD_SHORTCUT = "b"

type SidebarContext = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

const SidebarContext = React.createContext<SidebarContext | null>(null)

function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}

const SidebarProvider = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    defaultOpen?: boolean
    open?: boolean
    onOpenChange?: (open: boolean) => void
  }
>(
  (
    {
      defaultOpen = true,
      open: openProp,
      onOpenChange: setOpenProp,
      className,
      style,
      children,
      ...props
    },
    ref
  ) => {
    const isMobile = useIsMobile()
    const [openMobile, setOpenMobile] = React.useState(false)

    // This is the internal state of the sidebar.
    // We use openProp and setOpenProp for control from outside the component.
    const [_open, _setOpen] = React.useState(defaultOpen)
    const open = openProp ?? _open
    const setOpen = React.useCallback(
      (value: boolean | ((value: boolean) => boolean)) => {
        const openState = typeof value === "function" ? value(open) : value
        if (setOpenProp) {
          setOpenProp(openState)
        } else {
          _setOpen(openState)
        }

        // This sets the cookie to keep the sidebar state.
        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
      },
      [setOpenProp, open]
    )

    // Helper to toggle the sidebar.
    const toggleSidebar = React.useCallback(() => {
      return isMobile
        ? setOpenMobile((open) => !open)
        : setOpen((open) => !open)
    }, [isMobile, setOpen, setOpenMobile])

    // Adds a keyboard shortcut to toggle the sidebar.
    React.useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (
          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&
          (event.metaKey || event.ctrlKey)
        ) {
          event.preventDefault()
          toggleSidebar()
        }
      }

      window.addEventListener("keydown", handleKeyDown)
      return () => window.removeEventListener("keydown", handleKeyDown)
    }, [toggleSidebar])

    // We add a state so that we can do data-state="expanded" or "collapsed".
    // This makes it easier to style the sidebar with Tailwind classes.
    const state = open ? "expanded" : "collapsed"

    const contextValue = React.useMemo<SidebarContext>(
      () => ({
        state,
        open,
        setOpen,
        isMobile,
        openMobile,
        setOpenMobile,
        toggleSidebar,
      }),
      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]
    )

    return (
      <SidebarContext.Provider value={contextValue}>
        <TooltipProvider delayDuration={0}>
          <div
            style={
              {
                "--sidebar-width": SIDEBAR_WIDTH,
                "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
                ...style,
              } as React.CSSProperties
            }
            className={cn(
              "group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",
              className
            )}
            ref={ref}
            {...props}
          >
            {children}
          </div>
        </TooltipProvider>
      </SidebarContext.Provider>
    )
  }
)
SidebarProvider.displayName = "SidebarProvider"

const Sidebar = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    side?: "left" | "right"
    variant?: "sidebar" | "floating" | "inset"
    collapsible?: "offcanvas" | "icon" | "none"
  }
>(
  (
    {
      side = "left",
      variant = "sidebar",
      collapsible = "offcanvas",
      className,
      children,
      ...props
    },
    ref
  ) => {
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()

    if (collapsible === "none") {
      return (
        <div
          className={cn(
            "flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",
            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </div>
      )
    }

    if (isMobile) {
      return (
        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>
          <SheetContent
            data-sidebar="sidebar"
            data-mobile="true"
            className="w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden"
            style={
              {
                "--sidebar-width": SIDEBAR_WIDTH_MOBILE,
              } as React.CSSProperties
            }
            side={side}
          >
            <div className="flex h-full w-full flex-col">{children}</div>
          </SheetContent>
        </Sheet>
      )
    }

    return (
      <div
        ref={ref}
        className="group peer hidden md:block text-sidebar-foreground"
        data-state={state}
        data-collapsible={state === "collapsed" ? collapsible : ""}
        data-variant={variant}
        data-side={side}
      >
        {/* This is what handles the sidebar gap on desktop */}
        <div
          className={cn(
            "duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear",
            "group-data-[collapsible=offcanvas]:w-0",
            "group-data-[side=right]:rotate-180",
            variant === "floating" || variant === "inset"
              ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]"
              : "group-data-[collapsible=icon]:w-[--sidebar-width-icon]"
          )}
        />
        <div
          className={cn(
            "duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",
            side === "left"
              ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]"
              : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
            // Adjust the padding for floating and inset variants.
            variant === "floating" || variant === "inset"
              ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]"
              : "group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",
            className
          )}
          {...props}
        >
          <div
            data-sidebar="sidebar"
            className="flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow"
          >
            {children}
          </div>
        </div>
      </div>
    )
  }
)
Sidebar.displayName = "Sidebar"

const SidebarTrigger = React.forwardRef<
  React.ElementRef<typeof Button>,
  React.ComponentProps<typeof Button>
>(({ className, onClick, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()

  return (
    <Button
      ref={ref}
      data-sidebar="trigger"
      variant="ghost"
      size="icon"
      className={cn("h-7 w-7", className)}
      onClick={(event) => {
        onClick?.(event)
        toggleSidebar()
      }}
      {...props}
    >
      <PanelLeft />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  )
})
SidebarTrigger.displayName = "SidebarTrigger"

const SidebarRail = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button">
>(({ className, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()

  return (
    <button
      ref={ref}
      data-sidebar="rail"
      aria-label="Toggle Sidebar"
      tabIndex={-1}
      onClick={toggleSidebar}
      title="Toggle Sidebar"
      className={cn(
        "absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex",
        "[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize",
        "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize",
        "group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar",
        "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2",
        "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",
        className
      )}
      {...props}
    />
  )
})
SidebarRail.displayName = "SidebarRail"

const SidebarInset = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"main">
>(({ className, ...props }, ref) => {
  return (
    <main
      ref={ref}
      className={cn(
        "relative flex min-h-svh flex-1 flex-col bg-background",
        "peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",
        className
      )}
      {...props}
    />
  )
})
SidebarInset.displayName = "SidebarInset"

const SidebarInput = React.forwardRef<
  React.ElementRef<typeof Input>,
  React.ComponentProps<typeof Input>
>(({ className, ...props }, ref) => {
  return (
    <Input
      ref={ref}
      data-sidebar="input"
      className={cn(
        "h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",
        className
      )}
      {...props}
    />
  )
})
SidebarInput.displayName = "SidebarInput"

const SidebarHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="header"
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
})
SidebarHeader.displayName = "SidebarHeader"

const SidebarFooter = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="footer"
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
})
SidebarFooter.displayName = "SidebarFooter"

const SidebarSeparator = React.forwardRef<
  React.ElementRef<typeof Separator>,
  React.ComponentProps<typeof Separator>
>(({ className, ...props }, ref) => {
  return (
    <Separator
      ref={ref}
      data-sidebar="separator"
      className={cn("mx-2 w-auto bg-sidebar-border", className)}
      {...props}
    />
  )
})
SidebarSeparator.displayName = "SidebarSeparator"

const SidebarContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="content"
      className={cn(
        "flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarContent.displayName = "SidebarContent"

const SidebarGroup = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="group"
      className={cn("relative flex w-full min-w-0 flex-col p-2", className)}
      {...props}
    />
  )
})
SidebarGroup.displayName = "SidebarGroup"

const SidebarGroupLabel = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & { asChild?: boolean }
>(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "div"

  return (
    <Comp
      ref={ref}
      data-sidebar="group-label"
      className={cn(
        "duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupLabel.displayName = "SidebarGroupLabel"

const SidebarGroupAction = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button"> & { asChild?: boolean }
>(({ className, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      ref={ref}
      data-sidebar="group-action"
      className={cn(
        "absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 after:md:hidden",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarGroupAction.displayName = "SidebarGroupAction"

const SidebarGroupContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    data-sidebar="group-content"
    className={cn("w-full text-sm", className)}
    {...props}
  />
))
SidebarGroupContent.displayName = "SidebarGroupContent"

const SidebarMenu = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    data-sidebar="menu"
    className={cn("flex w-full min-w-0 flex-col gap-1", className)}
    {...props}
  />
))
SidebarMenu.displayName = "SidebarMenu"

const SidebarMenuItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li
    ref={ref}
    data-sidebar="menu-item"
    className={cn("group/menu-item relative", className)}
    {...props}
  />
))
SidebarMenuItem.displayName = "SidebarMenuItem"

const sidebarMenuButtonVariants = cva(
  "peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
        outline:
          "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]",
      },
      size: {
        default: "h-8 text-sm",
        sm: "h-7 text-xs",
        lg: "h-12 text-sm group-data-[collapsible=icon]:!p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const SidebarMenuButton = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button"> & {
    asChild?: boolean
    isActive?: boolean
    tooltip?: string | React.ComponentProps<typeof TooltipContent>
  } & VariantProps<typeof sidebarMenuButtonVariants>
>(
  (
    {
      asChild = false,
      isActive = false,
      variant = "default",
      size = "default",
      tooltip,
      className,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button"
    const { isMobile, state } = useSidebar()

    const button = (
      <Comp
        ref={ref}
        data-sidebar="menu-button"
        data-size={size}
        data-active={isActive}
        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}
        {...props}
      />
    )

    if (!tooltip) {
      return button
    }

    if (typeof tooltip === "string") {
      tooltip = {
        children: tooltip,
      }
    }

    return (
      <Tooltip>
        <TooltipTrigger asChild>{button}</TooltipTrigger>
        <TooltipContent
          side="right"
          align="center"
          hidden={state !== "collapsed" || isMobile}
          {...tooltip}
        />
      </Tooltip>
    )
  }
)
SidebarMenuButton.displayName = "SidebarMenuButton"

const SidebarMenuAction = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button"> & {
    asChild?: boolean
    showOnHover?: boolean
  }
>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      ref={ref}
      data-sidebar="menu-action"
      className={cn(
        "absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 after:md:hidden",
        "peer-data-[size=sm]/menu-button:top-1",
        "peer-data-[size=default]/menu-button:top-1.5",
        "peer-data-[size=lg]/menu-button:top-2.5",
        "group-data-[collapsible=icon]:hidden",
        showOnHover &&
          "group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuAction.displayName = "SidebarMenuAction"

const SidebarMenuBadge = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    data-sidebar="menu-badge"
    className={cn(
      "absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none",
      "peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground",
      "peer-data-[size=sm]/menu-button:top-1",
      "peer-data-[size=default]/menu-button:top-1.5",
      "peer-data-[size=lg]/menu-button:top-2.5",
      "group-data-[collapsible=icon]:hidden",
      className
    )}
    {...props}
  />
))
SidebarMenuBadge.displayName = "SidebarMenuBadge"

const SidebarMenuSkeleton = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    showIcon?: boolean
  }
>(({ className, showIcon = false, ...props }, ref) => {
  // Random width between 50 to 90%.
  const width = React.useMemo(() => {
    return `${Math.floor(Math.random() * 40) + 50}%`
  }, [])

  return (
    <div
      ref={ref}
      data-sidebar="menu-skeleton"
      className={cn("rounded-md h-8 flex gap-2 px-2 items-center", className)}
      {...props}
    >
      {showIcon && (
        <Skeleton
          className="size-4 rounded-md"
          data-sidebar="menu-skeleton-icon"
        />
      )}
      <Skeleton
        className="h-4 flex-1 max-w-[--skeleton-width]"
        data-sidebar="menu-skeleton-text"
        style={
          {
            "--skeleton-width": width,
          } as React.CSSProperties
        }
      />
    </div>
  )
})
SidebarMenuSkeleton.displayName = "SidebarMenuSkeleton"

const SidebarMenuSub = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    data-sidebar="menu-sub"
    className={cn(
      "mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5",
      "group-data-[collapsible=icon]:hidden",
      className
    )}
    {...props}
  />
))
SidebarMenuSub.displayName = "SidebarMenuSub"

const SidebarMenuSubItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ ...props }, ref) => <li ref={ref} {...props} />)
SidebarMenuSubItem.displayName = "SidebarMenuSubItem"

const SidebarMenuSubButton = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<"a"> & {
    asChild?: boolean
    size?: "sm" | "md"
    isActive?: boolean
  }
>(({ asChild = false, size = "md", isActive, className, ...props }, ref) => {
  const Comp = asChild ? Slot : "a"

  return (
    <Comp
      ref={ref}
      data-sidebar="menu-sub-button"
      data-size={size}
      data-active={isActive}
      className={cn(
        "flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground",
        "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",
        size === "sm" && "text-xs",
        size === "md" && "text-sm",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarMenuSubButton.displayName = "SidebarMenuSubButton"

export {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
}



================================================
FILE: components/ui/skeleton.tsx
================================================
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-primary/10", className)}
      {...props}
    />
  )
}

export { Skeleton }



================================================
FILE: components/ui/tabs.tsx
================================================
"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "@/lib/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",
      className
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

export { Tabs, TabsList, TabsTrigger, TabsContent }



================================================
FILE: components/ui/toast.tsx
================================================
"use client"

import * as React from "react"
import * as ToastPrimitives from "@radix-ui/react-toast"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const ToastProvider = ToastPrimitives.Provider

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        default: "border bg-background text-foreground",
        destructive:
          "destructive group border-destructive bg-destructive text-destructive-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    />
  )
})
Toast.displayName = ToastPrimitives.Root.displayName

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      className
    )}
    {...props}
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-semibold [&+div]:text-xs", className)}
    {...props}
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90", className)}
    {...props}
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
}



================================================
FILE: components/ui/toaster.tsx
================================================
"use client"

import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}



================================================
FILE: components/ui/tooltip.tsx
================================================
"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipProvider = TooltipPrimitive.Provider

const Tooltip = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Portal>
    <TooltipPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </TooltipPrimitive.Portal>
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }



================================================
FILE: docs/flow-projects.md
================================================
# Flow Project Management

The Open Deep Research platform now includes a project management system that allows users to persist their research flows and organize them into separate projects.

## Features

1. **Project Persistence** - All research flows are automatically saved to the browser's localStorage, ensuring that research progress is not lost if the browser is closed.

2. **Multiple Projects** - Users can create and switch between multiple research projects, each with its own set of nodes, edges, and research queries.

3. **Project Management** - Create, rename, and delete projects as needed.

4. **Import/Export** - Export projects to JSON files for backup and import them back when needed.

5. **Storage Monitoring** - View localStorage usage to manage your saved projects effectively.

## How It Works

- **Automatic Saving**: The flow state (nodes, edges, query) is automatically saved to localStorage as you work.
- **Project Selector**: Use the dropdown menu in the top navigation bar to:
  - Switch between existing projects
  - Create new projects
  - Rename or delete projects
- **Data Management**: Use the database icon button to:
  - Export projects as JSON files
  - Import previously exported projects
  - View storage usage information

## Technical Implementation

The project management system is built on:

1. `useFlowProjects` Hook:

   - Manages project state using localStorage
   - Provides functions for creating, updating, and deleting projects
   - Handles automatic saving of project state
   - Monitors localStorage usage

2. `ProjectSelector` Component:

   - Provides a user interface for project management
   - Displays a list of available projects with timestamps
   - Includes dialogs for creating and deleting projects

3. `ProjectActions` Component:

   - Handles import and export functionality
   - Shows storage usage information
   - Provides warnings when storage is running low

4. localStorage Keys:
   - `open-deep-research-flow-projects`: Stores the array of all projects
   - `open-deep-research-current-project`: Stores the ID of the currently active project

## Data Structure

Each project contains:

```typescript
interface FlowProject {
  id: string // Unique identifier
  name: string // User-defined project name
  createdAt: string // Creation timestamp
  updatedAt: string // Last update timestamp
  nodes: Node[] // ReactFlow nodes
  edges: Edge[] // ReactFlow edges
  query: string // Research query
}
```

## Limitations

- Project data is stored in the browser's localStorage, which has a size limit (typically 5MB)
- Projects are not synced across devices or browsers
- If localStorage is cleared, all projects will be lost (use export for backup)

## Best Practices

1. **Regular Exports**: Export your projects regularly to avoid data loss
2. **Monitor Storage**: Keep an eye on your storage usage to avoid hitting limits
3. **Project Organization**: Create separate projects for different research topics
4. **Clean Up**: Delete unnecessary projects to free up storage space

## Future Improvements

- Cloud synchronization for projects across devices
- More advanced project organization (folders, tags, etc.)
- Version history and rollback capabilities
- Selective import of specific projects



================================================
FILE: hooks/use-flow-projects.ts
================================================
import { useState, useEffect } from 'react'
import type { Node, Edge } from '@xyflow/react'
import {
  getLocalStorageUsage,
  formatBytes,
  exportFlowProjects,
  importFlowProjects,
} from '@/lib/localStorage-utils'

export interface FlowProject {
  id: string
  name: string
  createdAt: string
  updatedAt: string
  nodes: Node[]
  edges: Edge[]
  query: string
  selectedReports: string[]
  viewport?: {
    x: number
    y: number
    zoom: number
  }
}

interface StorageInfo {
  usage: number
  usagePercent: number
  available: number
  limit: number
  formattedUsage: string
  formattedAvailable: string
}

interface UseFlowProjectsReturn {
  projects: FlowProject[]
  currentProject: FlowProject | null
  setCurrentProject: (project: FlowProject) => void
  createProject: (name: string) => FlowProject
  updateCurrentProject: (
    data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>
  ) => void
  deleteProject: (id: string) => void
  saveCurrentState: (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports?: string[]
  ) => void
  exportProjects: () => string
  importProjects: (jsonData: string) => boolean
  storageInfo: StorageInfo
  refreshStorageInfo: () => void
  saveDirect: () => void
  simpleSave: (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports?: string[]
  ) => void
}

const LOCAL_STORAGE_KEY = 'open-deep-research-flow-projects'
const CURRENT_PROJECT_KEY = 'open-deep-research-current-project'

export function useFlowProjects(): UseFlowProjectsReturn {
  const [projects, setProjects] = useState<FlowProject[]>([])
  const [currentProject, setCurrentProject] = useState<FlowProject | null>(null)
  const [storageInfo, setStorageInfo] = useState<StorageInfo>(() => {
    const info = getLocalStorageUsage()
    return {
      ...info,
      formattedUsage: formatBytes(info.usage),
      formattedAvailable: formatBytes(info.available),
    }
  })

  // Save current state with preprocessing for loading states and debug logging
  const saveCurrentState = (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports: string[] = []
  ) => {
    // Just save everything directly without any complicated processing
    if (currentProject) {
      // Get all report nodes
      const reportNodes = nodes.filter((node) => node.type === 'reportNode')

      // Update the current project
      const updatedData = {
        nodes,
        edges,
        query,
        selectedReports,
      }

      // Update React state
      updateCurrentProject(updatedData)

      // Also directly save to localStorage
      const updatedProject = {
        ...currentProject,
        ...updatedData,
        updatedAt: new Date().toISOString(),
      }

      const updatedProjects = projects.map((p) =>
        p.id === currentProject.id ? updatedProject : p
      )

      // Save directly to localStorage
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updatedProjects))
    } else if (
      nodes.length > 0 ||
      edges.length > 0 ||
      selectedReports.length > 0
    ) {
      // Create a default project if we have any data but no current project
      const newProject = createProject('My Research Project')
      const updatedData = {
        nodes,
        edges,
        query,
        selectedReports,
      }

      updateCurrentProject(updatedData)

      // Also directly save to localStorage
      const updatedProject = {
        ...newProject,
        ...updatedData,
        updatedAt: new Date().toISOString(),
      }

      const updatedProjects = [...projects, updatedProject]

      // Save directly to localStorage
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updatedProjects))
      console.log('Created new project and saved directly to localStorage')
    }
  }

  // Helper function to postprocess nodes after loading to ensure proper state
  const postprocessLoadedNodes = (nodes: Node[]): Node[] => {
    return nodes.map((node) => {
      // Create a deep copy to avoid mutating the original
      const nodeCopy = JSON.parse(JSON.stringify(node))

      // Set appropriate loading states for report and question nodes
      if (nodeCopy.type === 'reportNode') {
        // In our application, the report data is stored in data.report object
        if (nodeCopy.data?.report) {
          // We have a report object
          nodeCopy.data.loading = false
        } else {
          // No report data exists
          nodeCopy.data.loading = false
          nodeCopy.data.report = {
            title: 'Report Unavailable',
            summary:
              'The report content is not available. Please regenerate the report.',
            sections: [],
          }
        }
      }

      if (nodeCopy.type === 'questionNode') {
        // For question nodes, we look for searchTerms, not questions
        if (
          nodeCopy.data?.searchTerms &&
          Array.isArray(nodeCopy.data.searchTerms)
        ) {
          nodeCopy.data.loading = false
        } else {
          nodeCopy.data.loading = false
          nodeCopy.data.searchTerms = []
        }
      }

      return nodeCopy
    })
  }

  // Load projects from localStorage with post-processing
  useEffect(() => {
    try {
      const savedProjects = localStorage.getItem(LOCAL_STORAGE_KEY)
      const savedCurrentProjectId = localStorage.getItem(CURRENT_PROJECT_KEY)

      if (savedProjects) {
        const parsedProjects = JSON.parse(savedProjects) as FlowProject[]

        // Process all projects
        const normalizedProjects = parsedProjects.map((project) => ({
          ...project,
          selectedReports: project.selectedReports || [],
          // Ensure nodes don't have invalid loading states
          nodes: postprocessLoadedNodes(project.nodes || []),
        }))

        setProjects(normalizedProjects)

        if (savedCurrentProjectId) {
          const current = normalizedProjects.find(
            (p) => p.id === savedCurrentProjectId
          )
          if (current) {
            setCurrentProject(current)
          }
        } else if (normalizedProjects.length > 0) {
          // Default to the most recently updated project
          const mostRecent = [...normalizedProjects].sort(
            (a, b) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          )[0]
          setCurrentProject(mostRecent)
          localStorage.setItem(CURRENT_PROJECT_KEY, mostRecent.id)
        }
      } else {
        // Create a default project if no projects exist
        const defaultProject = createProject('My Research Project')
        setProjects([defaultProject])
        setCurrentProject(defaultProject)
        localStorage.setItem(
          LOCAL_STORAGE_KEY,
          JSON.stringify([defaultProject])
        )
        localStorage.setItem(CURRENT_PROJECT_KEY, defaultProject.id)
      }

      refreshStorageInfo()
    } catch (error) {
      console.error('Failed to load projects from localStorage:', error)
    }
  }, [])

  // Save projects to localStorage whenever they change
  useEffect(() => {
    if (projects.length > 0) {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(projects))
      refreshStorageInfo()
    }
  }, [projects])

  // Save current project ID whenever it changes
  useEffect(() => {
    if (currentProject) {
      localStorage.setItem(CURRENT_PROJECT_KEY, currentProject.id)
    }
  }, [currentProject])

  const refreshStorageInfo = () => {
    const info = getLocalStorageUsage()
    setStorageInfo({
      ...info,
      formattedUsage: formatBytes(info.usage),
      formattedAvailable: formatBytes(info.available),
    })
  }

  const createProject = (name: string): FlowProject => {
    const now = new Date().toISOString()
    const newProject: FlowProject = {
      id: `project-${Date.now()}`,
      name,
      createdAt: now,
      updatedAt: now,
      nodes: [],
      edges: [],
      query: '',
      selectedReports: [],
    }

    setProjects((prev) => [...prev, newProject])
    setCurrentProject(newProject)
    refreshStorageInfo()
    return newProject
  }

  const updateCurrentProject = (
    data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>
  ) => {
    if (!currentProject) return

    const updatedProject = {
      ...currentProject,
      ...data,
      updatedAt: new Date().toISOString(),
    }

    setCurrentProject(updatedProject)
    setProjects((prev) =>
      prev.map((p) => (p.id === currentProject.id ? updatedProject : p))
    )
  }

  const deleteProject = (id: string) => {
    setProjects((prev) => prev.filter((p) => p.id !== id))

    if (currentProject?.id === id) {
      const remainingProjects = projects.filter((p) => p.id !== id)
      if (remainingProjects.length > 0) {
        setCurrentProject(remainingProjects[0])
      } else {
        // Reinitialize the project
        const defaultProject = createProject('My Research Project')
        setProjects([defaultProject])
        setCurrentProject(defaultProject)
        localStorage.setItem(
          LOCAL_STORAGE_KEY,
          JSON.stringify([defaultProject])
        )
        localStorage.setItem(CURRENT_PROJECT_KEY, defaultProject.id)
      }
    }

    refreshStorageInfo()
    window.location.reload() // Force reload the page after deletion
  }

  const exportProjects = (): string => {
    return exportFlowProjects()
  }

  const importProjectsData = (jsonData: string): boolean => {
    const success = importFlowProjects(jsonData)
    if (success) {
      // Reload projects from localStorage after import
      try {
        const savedProjects = localStorage.getItem(LOCAL_STORAGE_KEY)
        const savedCurrentProjectId = localStorage.getItem(CURRENT_PROJECT_KEY)

        if (savedProjects) {
          const parsedProjects = JSON.parse(savedProjects) as FlowProject[]

          // Process all imported projects
          const normalizedProjects = parsedProjects.map((project) => ({
            ...project,
            selectedReports: project.selectedReports || [],
            // Post-process nodes to handle loading states
            nodes: postprocessLoadedNodes(project.nodes || []),
          }))

          setProjects(normalizedProjects)

          if (savedCurrentProjectId) {
            const current = normalizedProjects.find(
              (p) => p.id === savedCurrentProjectId
            )
            if (current) {
              setCurrentProject(current)
            } else if (normalizedProjects.length > 0) {
              setCurrentProject(normalizedProjects[0])
            }
          } else if (normalizedProjects.length > 0) {
            setCurrentProject(normalizedProjects[0])
          }
        }

        refreshStorageInfo()
      } catch (error) {
        console.error('Failed to reload projects after import:', error)
      }
    }
    return success
  }

  // Get raw node and edge data and immediately save to localStorage
  const saveDirect = () => {
    if (!currentProject) return

    try {
      // Just directly save the current project we have in memory
      const allProjects = [...projects]
      const currentIndex = allProjects.findIndex(
        (p) => p.id === currentProject.id
      )

      if (currentIndex >= 0) {
        // Update the current project with what we have
        allProjects[currentIndex] = {
          ...allProjects[currentIndex],
          updatedAt: new Date().toISOString(),
        }

        // Save directly to localStorage
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(allProjects))
        console.log('Directly saved current state to localStorage!')
      }
    } catch (error) {
      console.error('Error directly saving to localStorage:', error)
    }
  }

  // A simplified method to save the current state without preprocessing
  const simpleSave = (
    nodes: Node[],
    edges: Edge[],
    query: string,
    selectedReports: string[] = []
  ) => {
    console.log('Simple save method called with nodes:', nodes)

    // First save the state to our React state
    if (currentProject) {
      const updatedData = {
        nodes,
        edges,
        query,
        selectedReports,
      }

      updateCurrentProject(updatedData)

      // Also force a direct localStorage save
      const updatedProject = {
        ...currentProject,
        ...updatedData,
        updatedAt: new Date().toISOString(),
      }

      const updatedProjects = projects.map((p) =>
        p.id === currentProject.id ? updatedProject : p
      )

      // Save directly to localStorage
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updatedProjects))
      console.log('Directly saved to localStorage!')
    }
  }

  return {
    projects,
    currentProject,
    setCurrentProject,
    createProject,
    updateCurrentProject,
    deleteProject,
    saveCurrentState,
    exportProjects,
    importProjects: importProjectsData,
    storageInfo,
    refreshStorageInfo,
    saveDirect,
    simpleSave,
  }
}



================================================
FILE: hooks/use-knowledge-base.ts
================================================
import { useState, useEffect } from 'react'
import { type KnowledgeBaseReport, type Report } from '@/types'
import {
  addToKnowledgeBase,
  getKnowledgeBaseReports,
  deleteFromKnowledgeBase,
  searchKnowledgeBase,
} from '@/lib/knowledge-base'

export function useKnowledgeBase() {
  const [reports, setReports] = useState<KnowledgeBaseReport[]>([])

  useEffect(() => {
    // Load reports on mount
    setReports(getKnowledgeBaseReports())

    // Listen for storage events from other tabs/windows
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'knowledge_base') {
        setReports(getKnowledgeBaseReports())
      }
    }

    // Listen for custom knowledge base change events (same window)
    const handleKnowledgeBaseChange = () => {
      setReports(getKnowledgeBaseReports())
    }

    // Listen for both storage and custom events
    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('knowledge_base_change', handleKnowledgeBaseChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('knowledge_base_change', handleKnowledgeBaseChange)
    }
  }, [])

  const addReport = (report: Report, query: string) => {
    const newReport: KnowledgeBaseReport = {
      id: `kb-${Date.now()}`,
      timestamp: Date.now(),
      query,
      report,
    }

    const success = addToKnowledgeBase(newReport)
    if (success) {
      setReports(getKnowledgeBaseReports())
    }
    return success
  }

  const deleteReport = (reportId: string) => {
    const success = deleteFromKnowledgeBase(reportId)
    if (success) {
      setReports(getKnowledgeBaseReports())
    }
    return success
  }

  const searchReports = (query: string) => {
    return searchKnowledgeBase(query)
  }

  const clearAllReports = () => {
    localStorage.removeItem('knowledge_base')
    setReports([])
    return true
  }

  return {
    reports,
    addReport,
    deleteReport,
    searchReports,
    clearAllReports,
  }
} 


================================================
FILE: hooks/use-mobile.tsx
================================================
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}



================================================
FILE: hooks/use-toast.ts
================================================
"use client"

// Inspired by react-hot-toast library
import * as React from "react"

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

const TOAST_LIMIT = 1
const TOAST_REMOVE_DELAY = 1000000

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER
  return count.toString()
}

type ActionType = typeof actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

function toast({ ...props }: Toast) {
  const id = genId()

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    })
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss()
      },
    },
  })

  return {
    id: id,
    dismiss,
    update,
  }
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }



================================================
FILE: lib/config.ts
================================================
export const CONFIG = {
  // Rate limits (requests per minute)
  rateLimits: {
    enabled: true, // Flag to enable/disable rate limiting
    search: 10,
    contentFetch: 20,
    reportGeneration: 5,
    agentOptimizations: 10,
  },

  // Search settings
  search: {
    resultsPerPage: 10,
    maxSelectableResults: 3,
    provider: 'google' as 'google' | 'bing' | 'exa', // Default search provider
    safeSearch: {
      google: 'active' as 'active' | 'off',
      bing: 'moderate' as 'moderate' | 'strict' | 'off',
    },
    market: 'en-US',
  },

  // AI Platform settings
  platforms: {
    google: {
      enabled: true,
      models: {
        'gemini-flash': {
          enabled: true,
          label: 'Gemini Flash',
        },
        'gemini-flash-thinking': {
          enabled: true,
          label: 'Gemini Flash Thinking',
        },
        'gemini-exp': {
          enabled: false,
          label: 'Gemini Exp',
        },
      },
    },
    ollama: {
      enabled: true,
      models: {
        'deepseek-r1:1.5b': {
          enabled: false,
          label: 'DeepSeek R1 1.5B',
        },
      },
    },
    openai: {
      enabled: true,
      models: {
        'gpt-4o': {
          enabled: false,
          label: 'GPT-4o',
        },
        'o1-mini': {
          enabled: false,
          label: 'o1-mini',
        },
        o1: {
          enabled: false,
          label: 'o1',
        },
      },
    },
    anthropic: {
      enabled: true,
      models: {
        'claude-3-7-sonnet-latest': {
          enabled: false,
          label: 'Claude 3.7 Sonnet',
        },
        'claude-3-5-haiku-latest': {
          enabled: false,
          label: 'Claude 3.5 Haiku',
        },
      },
    },
    deepseek: {
      enabled: true,
      models: {
        chat: {
          enabled: false,
          label: 'Chat',
        },
        reasoner: {
          enabled: false,
          label: 'Reasoner',
        },
      },
    },
    openrouter: {
      enabled: true,
      models: {
        'openrouter/auto': {
          enabled: false,
          label: 'Auto',
        },
      },
    },
  },
} as const



================================================
FILE: lib/documents.ts
================================================
import { type Report } from '@/types'
import {
  Document,
  Paragraph,
  TextRun,
  AlignmentType,
  Packer,
  Header,
  Footer,
  PageNumber,
} from 'docx'
import jsPDF from 'jspdf'
import MarkdownIt from 'markdown-it'

const md = new MarkdownIt()

export async function generateDocx(report: Report): Promise<Buffer> {
  try {
    console.log(
      'Starting DOCX generation with report:',
      JSON.stringify(report, null, 2)
    )

    const doc = new Document({
      sections: [
        {
          properties: {},
          headers: {
            default: new Header({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: report.title || 'Untitled Report',
                      size: 48,
                      bold: true,
                    }),
                  ],
                  alignment: AlignmentType.CENTER,
                  spacing: { after: 800 },
                }),
              ],
            }),
          },
          footers: {
            default: new Footer({
              children: [
                new Paragraph({
                  children: [
                    new TextRun('Page '),
                    new TextRun({
                      children: [PageNumber.CURRENT],
                    }),
                    new TextRun(' of '),
                    new TextRun({
                      children: [PageNumber.TOTAL_PAGES],
                    }),
                  ],
                  alignment: AlignmentType.CENTER,
                }),
              ],
            }),
          },
          children: [
            // Summary with increased spacing
            new Paragraph({
              children: [
                new TextRun({
                  text: report.summary || '',
                  size: 24,
                }),
              ],
              spacing: { before: 800, after: 800 },
              alignment: AlignmentType.JUSTIFIED,
            }),
            // Sections with increased spacing
            ...report.sections.flatMap((section) => [
              new Paragraph({
                children: [
                  new TextRun({
                    text: section.title || '',
                    size: 32,
                    bold: true,
                  }),
                ],
                spacing: { before: 800, after: 400 },
                alignment: AlignmentType.LEFT,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: section.content || '',
                    size: 24,
                  }),
                ],
                spacing: { before: 400, after: 800 },
                alignment: AlignmentType.JUSTIFIED,
              }),
            ]),
          ],
        },
      ],
    })

    console.log('Document instance created')

    try {
      console.log('Starting document packing')
      const buffer = await Packer.toBuffer(doc)
      console.log('Document packed successfully, buffer size:', buffer.length)
      return buffer
    } catch (packError) {
      console.error('Error packing document:', packError)
      throw packError
    }
  } catch (error) {
    console.error('Error in generateDocx:', error)
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })
    }
    throw new Error(
      `Failed to generate DOCX: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    )
  }
}

export function generatePdf(report: Report): Buffer {
  try {
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    })

    const pageWidth = doc.internal.pageSize.width
    const margin = 20
    const contentWidth = pageWidth - 2 * margin

    // Helper function to add text with proper line breaks and page management
    const addText = (
      text: string,
      y: number,
      fontSize: number,
      isBold: boolean = false,
      isJustified: boolean = false,
      isHTML: boolean = false
    ): number => {
      doc.setFontSize(fontSize)
      doc.setFont('helvetica', isBold ? 'bold' : 'normal')

      // If the text contains markdown, convert it to plain text
      let processedText = text
      if (isHTML) {
        // Remove HTML tags but preserve line breaks
        processedText = text
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<\/p>/gi, '\n')
          .replace(/<[^>]*>/g, '')
          // Handle markdown-style bold
          .replace(/\*\*(.*?)\*\*/g, (_, p1) => {
            doc.setFont('helvetica', 'bold')
            const result = p1
            doc.setFont('helvetica', isBold ? 'bold' : 'normal')
            return result
          })
          // Handle markdown-style italic
          .replace(/\*(.*?)\*/g, (_, p1) => {
            doc.setFont('helvetica', 'italic')
            const result = p1
            doc.setFont('helvetica', isBold ? 'bold' : 'normal')
            return result
          })
      }

      const lines = doc.splitTextToSize(processedText, contentWidth)
      const lineHeight = fontSize * 0.3527 // Convert pt to mm

      lines.forEach((line: string) => {
        if (y > 270) {
          doc.addPage()
          y = margin
        }

        // Handle bullet points
        if (line.trim().startsWith('•') || line.trim().startsWith('-')) {
          doc.text('•', margin, y)
          doc.text(line.trim().substring(1), margin + 5, y, {
            align: isJustified ? 'justify' : 'left',
            maxWidth: contentWidth - 5,
          })
        } else {
          doc.text(line, margin, y, {
            align: isJustified ? 'justify' : 'left',
            maxWidth: contentWidth,
          })
        }
        y += lineHeight + 1 // 1mm extra spacing between lines
      })

      return y + lineHeight // Return new Y position
    }

    // Start position
    let currentY = margin

    // Title
    currentY = addText(report.title, currentY, 24, true)
    currentY += 5 // Reduced from 10 to 5

    // Convert markdown to HTML for processing
    const summaryHtml = md.render(report.summary)
    currentY = addText(summaryHtml, currentY, 12, false, true, true)
    currentY += 3 // Reduced from 10 to 3

    // Sections
    report.sections.forEach((section) => {
      currentY += 2 // Reduced from 5 to 2

      // Section title
      currentY = addText(section.title, currentY, 16, true)
      currentY += 2 // Reduced from 5 to 2

      // Convert markdown to HTML for processing
      const contentHtml = md.render(section.content)
      currentY = addText(contentHtml, currentY, 12, false, true, true)
      currentY += 2 // Reduced from 5 to 2
    })

    // Add page numbers
    const pageCount = doc.internal.pages.length - 1
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)
      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      doc.text(`Page ${i} of ${pageCount}`, pageWidth / 2, 285, {
        align: 'center',
      })
    }

    return Buffer.from(doc.output('arraybuffer'))
  } catch (error) {
    console.error('Error generating PDF:', error)
    throw new Error('Failed to generate PDF')
  }
}



================================================
FILE: lib/file-upload.ts
================================================
import { type SearchResult } from '@/types'

export async function handleLocalFile(
  file: File,
  onStatusUpdate?: (loading: boolean) => void,
  onError?: (error: unknown, context: string) => void
): Promise<SearchResult | null> {
  try {
    onStatusUpdate?.(true)

    let content = ''
    if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
      content = await file.text()
    } else if (
      file.type === 'application/pdf' ||
      file.name.endsWith('.pdf') ||
      file.type ===
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.name.endsWith('.docx')
    ) {
      // Send the file to our parsing endpoint
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/parse-document', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: 'Failed to parse document' }))
        throw new Error(errorData.error || 'Failed to parse document')
      }

      const data = await response.json()
      content = data.content
    } else {
      throw new Error(
        'Unsupported file type. Only TXT, PDF, and DOCX files are supported.'
      )
    }

    // Truncate content to a reasonable snippet size
    const snippet = content.slice(0, 500) + (content.length > 500 ? '...' : '')

    // Create a search result from the file
    const timestamp = Date.now()
    const newResult: SearchResult = {
      id: `file-${timestamp}-${file.name}`,
      url: URL.createObjectURL(file),
      name: file.name,
      snippet: snippet,
      isCustomUrl: true,
      content: content, // Store full content for report generation
    }

    return newResult
  } catch (error) {
    onError?.(error, 'File Upload Error')
    return null
  } finally {
    onStatusUpdate?.(false)
  }
}

export const SUPPORTED_FILE_TYPES =
  '.txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document'



================================================
FILE: lib/gemini.ts
================================================
import {
  GoogleGenerativeAI,
  HarmBlockThreshold,
  HarmCategory,
} from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

const generationJsonConfig = {
  temperature: 1,
  maxOutputTokens: 8192,
  responseMimeType: 'application/json',
}

const generationPlainTextConfig = {
  temperature: 1,
  maxOutputTokens: 8192,
  responseMimeType: 'text/plain',
}

const safetySettings = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_NONE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_NONE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_NONE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_NONE,
  },
]

export const geminiFlashLiteModel = genAI.getGenerativeModel({
  model: 'gemini-2.0-flash-lite-preview-02-05',
  safetySettings,
  generationConfig: generationJsonConfig,
})

export const geminiFlashModel = genAI.getGenerativeModel({
  model: 'gemini-2.0-flash',
  safetySettings,
  generationConfig: generationJsonConfig,
})

export const geminiFlashThinkingModel = genAI.getGenerativeModel({
  model: 'gemini-2.0-flash-thinking-exp-01-21',
  safetySettings,
  generationConfig: generationPlainTextConfig,
})

export const geminiModel = genAI.getGenerativeModel({
  model: 'gemini-2.0-pro-exp-02-05',
  safetySettings,
  generationConfig: generationJsonConfig,
})



================================================
FILE: lib/knowledge-base.ts
================================================
import { type KnowledgeBaseReport } from '@/types'

const KNOWLEDGE_BASE_KEY = 'knowledge_base'

// Helper to notify about knowledge base changes
const notifyKnowledgeBaseChange = () => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new Event('knowledge_base_change'))
  }
}

export function addToKnowledgeBase(report: KnowledgeBaseReport): boolean {
  try {
    const reports = getKnowledgeBaseReports()
    reports.push(report)
    localStorage.setItem(KNOWLEDGE_BASE_KEY, JSON.stringify(reports))
    notifyKnowledgeBaseChange()
    return true
  } catch (error) {
    console.error('Failed to add report to knowledge base:', error)
    return false
  }
}

export function getKnowledgeBaseReports(): KnowledgeBaseReport[] {
  try {
    const reportsJson = localStorage.getItem(KNOWLEDGE_BASE_KEY)
    if (!reportsJson) return []
    
    const reports = JSON.parse(reportsJson) as KnowledgeBaseReport[]
    return reports.sort((a, b) => b.timestamp - a.timestamp)
  } catch (error) {
    console.error('Failed to get knowledge base reports:', error)
    return []
  }
}

export function deleteFromKnowledgeBase(reportId: string): boolean {
  try {
    const reports = getKnowledgeBaseReports()
    const filteredReports = reports.filter(report => report.id !== reportId)
    localStorage.setItem(KNOWLEDGE_BASE_KEY, JSON.stringify(filteredReports))
    notifyKnowledgeBaseChange()
    return true
  } catch (error) {
    console.error('Failed to delete report from knowledge base:', error)
    return false
  }
}

export function searchKnowledgeBase(query: string): KnowledgeBaseReport[] {
  try {
    const reports = getKnowledgeBaseReports()
    
    // Simple search implementation - can be enhanced with better search logic
    const searchTerms = query.toLowerCase().split(' ')
    return reports.filter(report => {
      const searchText = `${report.query} ${report.report.title} ${report.report.summary}`.toLowerCase()
      return searchTerms.every(term => searchText.includes(term))
    })
  } catch (error) {
    console.error('Failed to search knowledge base:', error)
    return []
  }
} 


================================================
FILE: lib/localStorage-utils.ts
================================================
import type { Viewport } from '@xyflow/react'
import type { FlowProject } from '@/hooks/use-flow-projects'

/**
 * Gets the current localStorage usage in bytes and as a percentage of the available space
 */
export function getLocalStorageUsage(): {
  usage: number // Size in bytes
  usagePercent: number // Percentage of available space
  available: number // Estimated available space in bytes
  limit: number // Estimated limit in bytes
} {
  try {
    // Current usage
    let totalSize = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i) || ''
      const value = localStorage.getItem(key) || ''
      totalSize += (key.length + value.length) * 2 // UTF-16 uses 2 bytes per character
    }

    // Estimate of localStorage limit (typically around 5MB)
    const estimatedLimit = 5 * 1024 * 1024 // 5MB in bytes

    return {
      usage: totalSize,
      usagePercent: (totalSize / estimatedLimit) * 100,
      available: Math.max(0, estimatedLimit - totalSize),
      limit: estimatedLimit,
    }
  } catch (error) {
    console.error('Error calculating localStorage usage:', error)
    return {
      usage: 0,
      usagePercent: 0,
      available: 0,
      limit: 0,
    }
  }
}

/**
 * Formats bytes into a human-readable string (KB, MB)
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i]
}

/**
 * Clears all flow projects from localStorage
 */
export function clearAllFlowProjects(): void {
  localStorage.removeItem('open-deep-research-flow-projects')
  localStorage.removeItem('open-deep-research-current-project')
}

/**
 * Exports all flow projects as a JSON string
 */
export function exportFlowProjects(): string {
  const projects =
    localStorage.getItem('open-deep-research-flow-projects') || '[]'
  return projects
}

/**
 * Imports flow projects from a JSON string
 * Returns success/failure status
 */
export function importFlowProjects(jsonProjects: string): boolean {
  try {
    // Validate JSON format
    const projects = JSON.parse(jsonProjects)

    if (!Array.isArray(projects)) {
      throw new Error('Invalid project data structure')
    }

    // Check if projects have the required structure
    for (const project of projects) {
      if (
        !project.id ||
        !project.name ||
        !project.createdAt ||
        !project.updatedAt
      ) {
        throw new Error('Invalid project data format')
      }
    }

    // Save to localStorage
    localStorage.setItem('open-deep-research-flow-projects', jsonProjects)

    // If there's a current project ID, verify it still exists in the imported data
    const currentProjectId = localStorage.getItem(
      'open-deep-research-current-project'
    )
    if (currentProjectId) {
      const exists = projects.some((p: any) => p.id === currentProjectId)
      if (!exists) {
        localStorage.removeItem('open-deep-research-current-project')
      }
    }

    return true
  } catch (error) {
    console.error('Failed to import projects:', error)
    return false
  }
}

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds have elapsed
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
      timeout = null
    }, wait)
  }
}

/**
 * Saves viewport state to the current project
 */
export function saveViewportToProject(
  project: FlowProject | null,
  viewport: Viewport,
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void
): void {
  if (!project) return

  try {
    updateProject({
      viewport,
    })
  } catch (error) {
    console.error('Failed to save viewport to project:', error)
  }
}

/**
 * Restores viewport state from the current project
 */
export function restoreViewportFromProject(
  project: FlowProject | null,
  setViewport: (viewport: Viewport) => void
): boolean {
  if (!project?.viewport) return false

  try {
    setViewport(project.viewport)
    return true
  } catch (error) {
    console.error('Failed to restore project viewport:', error)
    return false
  }
}

/**
 * Creates a debounced viewport save function
 */
export function createDebouncedViewportSave(
  getViewport: () => Viewport,
  project: FlowProject | null,
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void,
  debounceTime: number = 500
): () => void {
  return debounce(() => {
    saveViewportToProject(project, getViewport(), updateProject)
  }, debounceTime)
}

/**
 * Saves nodes to the current project
 */
export function saveNodesToProject<NodeType extends { id: string }>(
  project: FlowProject | null,
  nodes: NodeType[],
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void
): void {
  if (!project) return

  try {
    updateProject({
      nodes: nodes as any[],
    })
    console.log(`Nodes saved for project: ${project.name}`)
  } catch (error) {
    console.error('Failed to save nodes to project:', error)
  }
}

/**
 * Saves edges to the current project
 */
export function saveEdgesToProject<EdgeType extends { id: string }>(
  project: FlowProject | null,
  edges: EdgeType[],
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void
): void {
  if (!project) return

  try {
    updateProject({
      edges: edges as any[],
    })
    console.log(`Edges saved for project: ${project.name}`)
  } catch (error) {
    console.error('Failed to save edges to project:', error)
  }
}

/**
 * Creates a debounced nodes save function
 */
export function createDebouncedNodesSave<NodeType extends { id: string }>(
  getNodes: () => NodeType[],
  project: FlowProject | null,
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void,
  debounceTime: number = 500
): () => void {
  return debounce(() => {
    saveNodesToProject(project, getNodes(), updateProject)
  }, debounceTime)
}

/**
 * Creates a debounced edges save function
 */
export function createDebouncedEdgesSave<EdgeType extends { id: string }>(
  getEdges: () => EdgeType[],
  project: FlowProject | null,
  updateProject: (data: Partial<Omit<FlowProject, 'id' | 'createdAt'>>) => void,
  debounceTime: number = 500
): () => void {
  return debounce(() => {
    saveEdgesToProject(project, getEdges(), updateProject)
  }, debounceTime)
}



================================================
FILE: lib/models.ts
================================================
import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import ollama from 'ollama'
import {
  geminiFlashThinkingModel,
  geminiModel,
  geminiFlashModel,
} from './gemini'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
})

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY || '',
})

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '',
})

type DeepSeekMessage = {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export async function generateWithGemini(
  systemPrompt: string,
  model: string
): Promise<string> {
  let result
  if (model === 'gemini-flash-thinking') {
    result = await geminiFlashThinkingModel.generateContent(systemPrompt)
  } else if (model === 'gemini-exp') {
    result = await geminiModel.generateContent(systemPrompt)
  } else {
    result = await geminiFlashModel.generateContent(systemPrompt)
  }
  const text = result.response.text()
  if (!text) {
    throw new Error('No response content from Gemini')
  }
  return text
}

export async function generateWithOpenAI(
  systemPrompt: string,
  model: string
): Promise<string> {
  const response = await openai.chat.completions.create({
    model,
    messages: [
      {
        role: 'user',
        content: systemPrompt,
      },
    ],
  })
  const content = response.choices[0].message.content
  if (!content) {
    throw new Error('No response content from OpenAI')
  }
  return content
}

export async function generateWithDeepSeek(
  systemPrompt: string,
  model: string
): Promise<string> {
  try {
    const messages: DeepSeekMessage[] = [
      {
        role: 'user',
        content: systemPrompt,
      },
    ]

    const response = await deepseek.chat.completions.create({
      model: `deepseek-${model}`,
      messages: messages as any,
      max_tokens: 4000,
    })

    const content = response.choices[0].message.content
    if (!content) {
      throw new Error('No response content from DeepSeek')
    }

    // For the reasoner model, we can get additional reasoning content
    if (
      model === 'deepseek-reasoner' &&
      (response.choices[0].message as any).reasoning_content
    ) {
      console.log(
        'DeepSeek reasoning:',
        (response.choices[0].message as any).reasoning_content
      )
    }

    return content
  } catch (error) {
    console.error('DeepSeek API error:', error)
    throw error
  }
}

export async function generateWithAnthropic(
  systemPrompt: string,
  model: string
): Promise<string> {
  const response = await anthropic.messages.create({
    model,
    max_tokens: 3500,
    temperature: 0.9,
    messages: [
      {
        role: 'user',
        content: systemPrompt,
      },
    ],
  })
  const content = response.content[0].text
  if (!content) {
    throw new Error('No response content from Anthropic')
  }
  return content
}

export async function generateWithOllama(
  systemPrompt: string,
  model: string
): Promise<string> {
  const response = await ollama.chat({
    model: model.replace('ollama__', ''),
    messages: [{ role: 'user', content: systemPrompt }],
  })
  const content = response.message.content
  if (!content) {
    throw new Error('No response content from Ollama')
  }
  return content
}

export async function generateWithOpenRouter(
  systemPrompt: string,
  model: string
): Promise<string> {
  const response = await fetch(
    'https://openrouter.ai/api/v1/chat/completions',
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model.replace('openrouter__', ''),
        messages: [
          {
            role: 'user',
            content: systemPrompt,
          },
        ],
      }),
    }
  )

  const responseData = await response.text()

  if (!response.ok) {
    throw new Error(`OpenRouter API error: ${responseData}`)
  }

  let data
  try {
    data = JSON.parse(responseData)
  } catch (e) {
    console.log('error', e)
    throw new Error(`Failed to parse OpenRouter response: ${responseData}`)
  }

  if (!data.choices?.[0]?.message?.content) {
    throw new Error(
      `Invalid OpenRouter response format: ${JSON.stringify(data)}`
    )
  }

  return data.choices[0].message.content
}

export async function generateWithModel(
  systemPrompt: string,
  platformModel: string
): Promise<string> {
  const [platform, model] = platformModel.split('__')

  switch (platform) {
    case 'google':
      return generateWithGemini(systemPrompt, model)
    case 'openai':
      return generateWithOpenAI(systemPrompt, model)
    case 'deepseek':
      return generateWithDeepSeek(systemPrompt, model)
    case 'anthropic':
      return generateWithAnthropic(systemPrompt, model)
    case 'ollama':
      return generateWithOllama(systemPrompt, model)
    case 'openrouter':
      return generateWithOpenRouter(systemPrompt, model)
    default:
      throw new Error('Invalid platform specified')
  }
}



================================================
FILE: lib/redis.ts
================================================
import { Redis } from '@upstash/redis'
import { Ratelimit } from '@upstash/ratelimit'
import { CONFIG } from './config'

export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || '',
})

// Search: Configurable requests per minute
export const searchRatelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(CONFIG.rateLimits.search, '1 m'),
})

// Content fetching: Configurable requests per minute
export const fetchContentRatelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(CONFIG.rateLimits.contentFetch, '1 m'),
})

// Report generation: Configurable requests per minute
export const reportContentRatelimit = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(CONFIG.rateLimits.reportGeneration, '1 m'),
})



================================================
FILE: lib/utils.ts
================================================
import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function extractAndParseJSON(response: string) {
  function cleanJson(jsonStr: string): string {
    return (
      jsonStr
        // Remove YAML pipe characters
        .replace(/\|\n/g, '\n')
        // Remove YAML block scalar indicators (> and |) after colons
        .replace(/:\s*[>|](\s*\n|\s*$)/g, ': ')
        // Clean up any remaining YAML/Markdown artifacts
        .replace(/^\s*>/gm, '')
        // Remove trailing commas before closing braces/brackets
        .replace(/,(\s*[}\]])/g, '$1')
        // Normalize multiple newlines to single newlines
        .replace(/\n\s*\n/g, '\n')
        // Remove leading/trailing whitespace in multiline strings
        .replace(/:\s*"[\s\n]+/g, ': "')
        .replace(/[\s\n]+"/g, '"')
    )
  }

  // First attempt: Try to parse the entire response as JSON
  try {
    console.log(
      'Attempt 1 - Full parse, input:',
      response.slice(0, 100) + '...'
    )
    const result = JSON.parse(response)
    console.log('Attempt 1 succeeded')
    return result
  } catch (e) {
    console.log('Attempt 1 failed:', e)
  }

  // Second attempt: Look for JSON within code blocks
  const codeBlockRegex = /```(?:json)?\s*([\s\S]*?)```/
  const codeBlockMatch = response.match(codeBlockRegex)

  if (codeBlockMatch) {
    try {
      console.log(
        'Attempt 2 - Code block found, content:',
        codeBlockMatch[1].slice(0, 100) + '...'
      )
      const cleanedJson = cleanJson(codeBlockMatch[1])
      console.log(
        'Attempt 2 - Cleaned JSON:',
        cleanedJson.slice(0, 100) + '...'
      )
      const result = JSON.parse(cleanedJson)
      console.log('Attempt 2 succeeded')
      return result
    } catch (e) {
      console.log('Attempt 2 failed:', e)
    }
  } else {
    console.log('Attempt 2 - No code block found')
  }

  // Third attempt: Find the outermost matching braces
  console.log('Attempt 3 - Starting bracket matching')
  let bracketCount = 0
  let startIndex = -1
  let endIndex = -1
  let inString = false
  let escapeNext = false
  let foundStart = false

  for (let i = 0; i < response.length; i++) {
    // Handle string boundaries and escaped characters
    if (response[i] === '"' && !escapeNext) {
      inString = !inString
    } else if (response[i] === '\\' && !escapeNext) {
      escapeNext = true
      continue
    }

    escapeNext = false

    // Only count braces when not in a string
    if (!inString) {
      if (response[i] === '{') {
        if (bracketCount === 0) {
          startIndex = i
          foundStart = true
          console.log('Attempt 3 - Found opening brace at index:', i)
        }
        bracketCount++
      } else if (response[i] === '}') {
        bracketCount--
        if (bracketCount === 0 && foundStart) {
          endIndex = i + 1
          console.log('Attempt 3 - Found matching closing brace at index:', i)
          // Try parsing this JSON substring with cleanup
          try {
            const jsonCandidate = cleanJson(
              response.substring(startIndex, endIndex)
            )
            console.log(
              'Attempt 3 - Trying to parse substring:',
              jsonCandidate.slice(0, 100) + '...'
            )
            const result = JSON.parse(jsonCandidate)
            console.log('Attempt 3 succeeded')
            return result
          } catch (e) {
            console.log('Attempt 3 - Parse failed for this substring:', e)
            foundStart = false // Reset to keep looking
            continue
          }
        }
      }
    }
  }

  console.log('All attempts failed - Final bracket count:', bracketCount)
  throw new Error('No valid JSON found in response')
}



================================================
FILE: types/index.ts
================================================
export type Report = {
  title: string
  summary: string
  sections: {
    title: string
    content: string
  }[]
  sources: {
    id: string
    url: string
    name: string
  }[]
  usedSources?: number[] // Array of source indices that were actually used/cited
}
export interface Article {
  url: string
  title: string
  content: string
}

export type KnowledgeBaseReport = {
  id: string
  timestamp: number
  query: string
  report: Report
}

export type SearchResult = {
  id: string
  url: string
  name: string
  snippet: string
  isCustomUrl?: boolean
  score?: number
  content?: string
}

export type RankingResult = {
  url: string
  score: number
  reasoning: string
}

export type PlatformModel = {
  value: string
  label: string
  platform: string
  disabled: boolean
}

export type ModelVariant =
  | 'google__gemini-flash'
  | 'google__gemini-flash-thinking'
  | 'google__gemini-exp'
  | 'gpt-4o'
  | 'o1-mini'
  | 'o1'
  | 'claude-3-7-sonnet-latest'
  | 'claude-3-5-haiku-latest'
  | 'deepseek__chat'
  | 'deepseek__reasoner'
  | 'ollama__llama3.2'
  | 'ollama__deepseek-r1:14b'
  | 'openrouter__auto'

export type Status = {
  loading: boolean
  generatingReport: boolean
  agentStep: 'idle' | 'processing' | 'searching' | 'analyzing' | 'generating'
  fetchStatus: {
    total: number
    successful: number
    fallback: number
    sourceStatuses: Record<string, 'fetched' | 'preview'>
  }
  agentInsights: string[]
  searchQueries: string[]
}

export type State = {
  query: string
  timeFilter: string
  results: SearchResult[]
  selectedResults: string[]
  reportPrompt: string
  report: Report | null
  error: string | null
  newUrl: string
  isSourcesOpen: boolean
  selectedModel: string
  isAgentMode: boolean
  sidebarOpen: boolean
  activeTab: string
  status: Status
}

// Flow Component Types
export type BaseNodeData = {
  id?: string
  loading?: boolean
  error?: string
  parentId?: string
  childIds?: string[]
}

export type SearchNodeData = BaseNodeData & {
  query: string
  onFileUpload?: (file: File) => void
}

export type SelectionNodeData = BaseNodeData & {
  results: SearchResult[]
  onGenerateReport?: (
    selectedResults: SearchResult[],
    prompt: string
  ) => Promise<
    | { success: boolean; report: any; searchTerms: any; error?: undefined }
    | {
        success: boolean
        error: string
        report?: undefined
        searchTerms?: undefined
      }
    | undefined
  >
}

export type ReportNodeData = BaseNodeData & {
  report?: Report
  isSelected?: boolean
  onSelect?: (id: string) => void
  isConsolidated?: boolean
  isConsolidating?: boolean
}

export type SearchTermsNodeData = BaseNodeData & {
  searchTerms?: string[]
  onApprove?: (term: string) => void
}

// Combined interface for all node types with index signature for compatibility with xyflow
export interface FlowNodeData extends BaseNodeData {
  query?: string
  results?: SearchResult[]
  report?: Report
  searchTerms?: string[]
  question?: string
  onGenerateReport?: (
    selectedResults: SearchResult[],
    prompt: string
  ) => Promise<
    | { success: boolean; report: any; searchTerms: any; error?: undefined }
    | {
        success: boolean
        error: string
        report?: undefined
        searchTerms?: undefined
      }
    | undefined
  >
  onApprove?: (term?: string) => void
  onConsolidate?: () => void
  hasChildren?: boolean
  isSelected?: boolean
  onSelect?: (id: string) => void
  isConsolidated?: boolean
  isConsolidating?: boolean
  onFileUpload?: (file: File) => void
  [key: string]: any // This allows for dynamic properties required by xyflow
}

// Configuration for different node types
export interface NodeConfig {
  zIndex: number
  style?: React.CSSProperties
}



================================================
FILE: .github/ISSUE_TEMPLATE/config.yml
================================================
blank_issues_enabled: false



================================================
FILE: .github/ISSUE_TEMPLATE/issue_template.yml
================================================
name: 'Issue with Pre-Submission Checklist'
description: "Before submitting your issue, please confirm you've followed these steps."
title: '[Issue] - '
labels: []
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        **You are about to open an issue and interrupt the day of a stranger on the internet. And that's okay!**

        We all need help, and we should try to help each other as much as possible. But before you submit your issue, please make sure you've:
        1. Read through the README.
        2. Searched through existing GitHub issues to see if one of them answered your question.
        3. Copied and pasted the README into ChatGPT or your favorite LLM of choice and asked it the question.
        4. Spent 5 minutes thinking through this on your own.

        If all these failed, please feel free to open an issue. :)

  - type: checkboxes
    id: confirmation
    attributes:
      label: "Please confirm that you've done the steps above:"
      options:
        - label: "Yes, I've read the README, searched existing issues, consulted an LLM, and thought it through."
          required: true

  - type: textarea
    id: issue_description
    attributes:
      label: 'Describe the issue'
      description: 'Provide a clear and concise description of your issue.'
      placeholder: 'Describe your issue here...'


