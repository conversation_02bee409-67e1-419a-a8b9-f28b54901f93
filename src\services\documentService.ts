import { supabase } from '@/lib/supabase';
import { Database } from '@/lib/database.types';

type Document = Database['public']['Tables']['user_documents']['Row'];
type DocumentInsert = Database['public']['Tables']['user_documents']['Insert'];
type DocumentUpdate = Database['public']['Tables']['user_documents']['Update'];

export interface DocumentWithVersions extends Document {
  versions?: DocumentVersion[];
}

export interface DocumentVersion {
  id: string;
  version_number: number;
  title: string;
  content: string;
  changes_description?: string;
  word_count: number;
  created_at: string;
}

class DocumentService {
  /**
   * Get all documents for the current user
   */
  async getUserDocuments(limit = 50): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching documents:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get a specific document by ID
   */
  async getDocument(id: string): Promise<Document | null> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching document:', error);
      return null;
    }

    return data;
  }

  /**
   * Create a new document
   */
  async createDocument(document: Omit<DocumentInsert, 'user_id'>): Promise<Document | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('documents')
      .insert({
        ...document,
        user_id: user.id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating document:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update an existing document
   */
  async updateDocument(id: string, updates: Partial<DocumentUpdate>): Promise<Document | null> {
    const { data, error } = await supabase
      .from('documents')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating document:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete a document
   */
  async deleteDocument(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('documents')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting document:', error);
      return false;
    }

    return true;
  }

  /**
   * Duplicate a document
   */
  async duplicateDocument(id: string): Promise<Document | null> {
    const original = await this.getDocument(id);
    if (!original) return null;

    return this.createDocument({
      title: `${original.title} (Copy)`,
      content: original.content,
      document_type: original.document_type,
      metadata: original.metadata,
      status: 'draft',
    });
  }

  /**
   * Search documents by title or content
   */
  async searchDocuments(query: string): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .or(`title.ilike.%${query}%,content.ilike.%${query}%`)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error searching documents:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get documents by type
   */
  async getDocumentsByType(type: Document['document_type']): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('document_type', type)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents by type:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get recent documents
   */
  async getRecentDocuments(limit = 5): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent documents:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Count words in HTML content
   */
  countWords(htmlContent: string): number {
    // Remove HTML tags and count words
    const textContent = htmlContent.replace(/<[^>]*>/g, ' ').trim();
    if (!textContent) return 0;
    
    return textContent.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Auto-save document (throttled)
   */
  private autoSaveTimeouts = new Map<string, NodeJS.Timeout>();

  autoSave(id: string, updates: Partial<DocumentUpdate>, delay = 2000) {
    // Clear existing timeout for this document
    const existingTimeout = this.autoSaveTimeouts.get(id);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(async () => {
      try {
        await this.updateDocument(id, updates);
        console.log(`Auto-saved document ${id}`);
      } catch (error) {
        console.error('Auto-save failed:', error);
      } finally {
        this.autoSaveTimeouts.delete(id);
      }
    }, delay);

    this.autoSaveTimeouts.set(id, timeout);
  }

  /**
   * Import document from file
   */
  async importFromFile(file: File): Promise<{ title: string; content: string } | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          let title = file.name.replace(/\.[^/.]+$/, ""); // Remove file extension
          
          // For simple text files, wrap in basic HTML
          if (file.type === 'text/plain') {
            const htmlContent = content
              .split('\n')
              .map(line => line.trim() ? `<p>${line}</p>` : '<br>')
              .join('\n');
            
            resolve({ title, content: htmlContent });
          } else {
            // For other formats, return as is (could be enhanced with format-specific parsers)
            resolve({ title, content });
          }
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }
}

export const documentService = new DocumentService();
