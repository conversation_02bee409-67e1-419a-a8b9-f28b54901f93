/**
 * AI Visualization Generator Service
 * Generates educational visualizations using AI - NO FALLBACKS, REAL ERRORS ONLY
 */

import { openRouterTutorService } from './openrouter-tutor.service';
import { geminiTutorService } from './gemini-tutor.service';
import { TUTOR_AI_MODELS } from '../constants';

export interface VisualizationRequest {
  topic: string;
  subject: string;
  educationLevel: 'elementary' | 'middle' | 'high' | 'college' | 'graduate';
  visualizationType: 'interactive' | 'animated' | 'static' | 'simulation';
  userQuestion: string;
  context?: string;
  model?: string;
}

export interface GeneratedVisualization {
  code: string;
  type: 'react' | 'html' | 'd3' | 'plotly' | 'three';
  title: string;
  description: string;
  interactiveElements: string[];
  educationalNotes: string[];
  dependencies: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
}

export type EducationLevel = 'elementary' | 'middle' | 'high' | 'college' | 'graduate';

class AIVisualizationGeneratorService {
  private static instance: AIVisualizationGeneratorService;

  static getInstance(): AIVisualizationGeneratorService {
    if (!AIVisualizationGeneratorService.instance) {
      AIVisualizationGeneratorService.instance = new AIVisualizationGeneratorService();
    }
    return AIVisualizationGeneratorService.instance;
  }

  /**
   * Generate visualization code using AI - NO FALLBACKS
   */
  async generateVisualization(request: VisualizationRequest): Promise<GeneratedVisualization> {
    const model = request.model || 'google/gemini-2.5-pro';

    try {
      console.log('🎨 Generating visualization with AI:', {
        topic: request.topic,
        subject: request.subject,
        type: request.visualizationType,
        model
      });

      // Try AI generation with shorter timeout for code-only generation
      const prompt = this.buildVisualizationPrompt(request);
      const response = await Promise.race([
        this.callAIService(prompt, model, request.educationLevel),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI request timeout - try a simpler topic or different model')), 10000)
        )
      ]) as any;

      return this.parseVisualizationResponse(response.content, request);

    } catch (error) {
      console.error('❌ AI generation failed:', error.message);
      
      // Return error instead of fallback - no dummy visualizations
      throw new Error(`AI visualization generation failed: ${error.message}. Please try again or check your API configuration.`);
    }
  }

  /**
   * Build focused prompt for FAST visualization generation - CODE ONLY
   */
  private buildVisualizationPrompt(request: VisualizationRequest): string {
    const { topic, subject, educationLevel, visualizationType, userQuestion } = request;

    return `Generate ONLY React component code for "${topic}" visualization. NO explanations, NO text, JUST CODE.

TOPIC: ${topic}
SUBJECT: ${subject}
LEVEL: ${educationLevel}
TYPE: ${visualizationType}

REQUIREMENTS:
- Interactive React component with animations
- Use useState, useEffect for interactivity
- Include play/pause controls
- Topic-specific animations (not generic)
- Tailwind CSS styling
- Export as: export default function ${topic.replace(/\s+/g, '')}Visualization()

RESPOND WITH ONLY THE COMPLETE REACT CODE:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

export default function ${topic.replace(/\s+/g, '')}Visualization() {
  // Your code here
}
\`\`\`

Generate the code now:`;
  }

  /**
   * Call appropriate AI service based on model - OPTIMIZED FOR SPEED
   */
  private async callAIService(prompt: string, model: string, educationLevel: EducationLevel) {
    // Use fastest available model for code generation
    const fastModel = model.includes('flash') ? model : 'google/gemini-2.5-flash';
    const modelConfig = TUTOR_AI_MODELS.find(m => m.id === fastModel);
    const apiProvider = modelConfig?.apiProvider || 'gemini'; // Prefer Gemini for speed

    const options = {
      model: fastModel,
      temperature: 0.1, // Very low temperature for consistent code generation
      maxTokens: 2048, // Reduced tokens for faster generation
      educationLevel,
      includeExamples: false, // Skip examples for speed
      learningStyle: 'visual' as const,
      conversationHistory: [] // No history for speed
    };

    console.log('🚀 Using fast model for visualization:', fastModel);

    if (apiProvider === 'gemini') {
      return await geminiTutorService.generateTutoringResponse('visualization', prompt, options);
    } else {
      return await openRouterTutorService.generateTutoringResponse('visualization', prompt, options);
    }
  }

  /**
   * Parse AI response and extract visualization data - ENHANCED
   */
  private parseVisualizationResponse(content: string, request: VisualizationRequest): GeneratedVisualization {
    console.log('=== AI RESPONSE PARSING ===');
    console.log('Content length:', content.length);
    console.log('Topic:', request.topic);
    console.log('Full AI response:', content);

    // Try multiple parsing strategies with enhanced logging
    let parsedResult = this.tryParseMarkdownCode(content, request);
    if (parsedResult) {
      console.log('✅ Successfully parsed markdown code blocks');
      return parsedResult;
    }

    parsedResult = this.tryParseRawCode(content, request);
    if (parsedResult) {
      console.log('✅ Successfully parsed raw code');
      return parsedResult;
    }

    parsedResult = this.tryParseJSON(content, request);
    if (parsedResult) {
      console.log('✅ Successfully parsed JSON response');
      return parsedResult;
    }

    // Enhanced fallback - try to extract any React-like code
    parsedResult = this.tryParseAnyReactCode(content, request);
    if (parsedResult) {
      console.log('✅ Successfully parsed with enhanced extraction');
      return parsedResult;
    }

    console.error('❌ All parsing strategies failed');
    console.error('AI Response Content:', content);
    throw new Error(`Failed to parse AI response. The AI response was: "${content.substring(0, 200)}...". Please try a simpler request.`);
  }

  /**
   * Try to parse JSON response
   */
  private tryParseJSON(content: string, request: VisualizationRequest): GeneratedVisualization | null {
    try {
      // Try multiple JSON extraction patterns
      const jsonPatterns = [
        /\{[\s\S]*\}/,  // Basic JSON match
        /```json\s*(\{[\s\S]*?\})\s*```/,  // JSON in code blocks
        /```\s*(\{[\s\S]*?\})\s*```/,  // JSON without language specifier
      ];

      for (const pattern of jsonPatterns) {
        const match = content.match(pattern);
        if (match) {
          console.log('Found JSON pattern, attempting to parse...');
          const jsonStr = match[1] || match[0];
          const parsed = JSON.parse(jsonStr);

          if (parsed.code) {
            console.log('JSON contains code, validating...');
            if (this.validateGeneratedCode(parsed.code)) {
              console.log('✅ AI-generated code passed validation');
              return {
                code: parsed.code,
                type: parsed.type || 'react',
                title: parsed.title || `${request.topic} Visualization`,
                description: parsed.description || `Interactive visualization for ${request.topic}`,
                interactiveElements: parsed.interactiveElements || ['Interactive exploration'],
                educationalNotes: parsed.educationalNotes || ['Explore the visualization to learn more'],
                dependencies: parsed.dependencies || ['react'],
                complexity: parsed.complexity || 'intermediate'
              };
            } else {
              console.warn('❌ AI-generated code failed validation');
            }
          }
        }
      }
    } catch (error) {
      console.log('JSON parsing failed:', error.message);
    }
    return null;
  }

  /**
   * Try to parse markdown code blocks
   */
  private tryParseMarkdownCode(content: string, request: VisualizationRequest): GeneratedVisualization | null {
    console.log('Trying to extract code from markdown blocks...');

    const codeBlockPatterns = [
      /```(?:jsx?|typescript|tsx?)\s*([\s\S]*?)```/g,  // Language-specific blocks
      /```\s*([\s\S]*?)```/g,  // Generic code blocks
    ];

    for (const pattern of codeBlockPatterns) {
      const matches = [...content.matchAll(pattern)];
      if (matches.length > 0) {
        // Find the largest code block (likely the main component)
        const codes = matches.map(match => match[1].trim());
        const code = codes.reduce((longest, current) =>
          current.length > longest.length ? current : longest, ''
        );

        console.log('Extracted code block, length:', code.length);
        console.log('Code preview:', code.substring(0, 200));

        if (this.validateGeneratedCode(code)) {
          console.log('✅ Extracted code passed validation');
          return {
            code,
            type: 'react',
            title: `${request.topic} Visualization`,
            description: `Interactive visualization for ${request.topic}`,
            interactiveElements: ['Interactive exploration'],
            educationalNotes: ['Explore the visualization to learn more'],
            dependencies: ['react'],
            complexity: 'intermediate'
          };
        } else {
          console.warn('❌ Extracted code failed validation');
        }
      }
    }
    return null;
  }

  /**
   * Try to parse raw code (when AI doesn't use proper formatting)
   */
  private tryParseRawCode(content: string, request: VisualizationRequest): GeneratedVisualization | null {
    console.log('Trying to extract raw code...');

    // Look for React component patterns
    const reactPatterns = [
      /import React[\s\S]*?export default \w+;?/,
      /function \w+\(\)[\s\S]*?export default \w+;?/,
      /const \w+ = \(\) =>[\s\S]*?export default \w+;?/,
    ];

    for (const pattern of reactPatterns) {
      const match = content.match(pattern);
      if (match) {
        const code = match[0];
        console.log('Found raw React code, length:', code.length);

        if (this.validateGeneratedCode(code)) {
          console.log('✅ Raw code passed validation');
          return {
            code,
            type: 'react',
            title: `${request.topic} Visualization`,
            description: `Interactive visualization for ${request.topic}`,
            interactiveElements: ['Interactive exploration'],
            educationalNotes: ['Explore the visualization to learn more'],
            dependencies: ['react'],
            complexity: 'intermediate'
          };
        } else {
          console.warn('❌ Raw code failed validation');
        }
      }
    }
    return null;
  }

  /**
   * Enhanced parsing - try to extract any React-like code from response
   */
  private tryParseAnyReactCode(content: string, request: VisualizationRequest): GeneratedVisualization | null {
    console.log('Trying enhanced React code extraction...');

    // Look for any function that might be a React component
    const patterns = [
      // Function declarations
      /function\s+\w+\s*\([^)]*\)\s*{[\s\S]*?return[\s\S]*?<[\s\S]*?}/g,
      // Arrow functions
      /const\s+\w+\s*=\s*\([^)]*\)\s*=>\s*{[\s\S]*?return[\s\S]*?<[\s\S]*?}/g,
      // Any JSX return statement
      /return\s*\([\s\S]*?<[\s\S]*?>\s*[\s\S]*?\)/g,
      // Direct JSX
      /<div[\s\S]*?<\/div>/g
    ];

    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        // Take the longest match
        const code = matches.reduce((longest, current) =>
          current.length > longest.length ? current : longest, ''
        );

        console.log('Found potential React code:', code.substring(0, 200));

        // Try to create a complete component from the extracted code
        const completeCode = this.createCompleteComponent(code, request.topic);

        if (this.validateGeneratedCode(completeCode)) {
          console.log('✅ Enhanced extraction successful');
          return {
            code: completeCode,
            type: 'react',
            title: `${request.topic} Visualization`,
            description: `Interactive visualization for ${request.topic}`,
            interactiveElements: ['Interactive exploration'],
            educationalNotes: ['Explore the visualization to learn more'],
            dependencies: ['react'],
            complexity: 'intermediate'
          };
        }
      }
    }

    return null;
  }

  /**
   * Create a complete React component from partial code
   */
  private createCompleteComponent(partialCode: string, topic: string): string {
    const componentName = topic.replace(/\s+/g, '') + 'Visualization';

    // If it's already a complete function, return as is
    if (partialCode.includes('function') || partialCode.includes('const') && partialCode.includes('=>')) {
      return `import React, { useState, useEffect } from 'react';\n\n${partialCode}\n\nexport default ${componentName};`;
    }

    // If it's just JSX, wrap it in a component
    if (partialCode.includes('<') && partialCode.includes('>')) {
      return `import React, { useState, useEffect } from 'react';

export default function ${componentName}() {
  const [isPlaying, setIsPlaying] = useState(true);

  return (
    ${partialCode}
  );
}`;
    }

    return partialCode;
  }

  private validateGeneratedCode(code: string): boolean {
    if (!code || code.length < 50) {
      console.log('Code too short or empty');
      return false;
    }

    // Check for basic React component structure
    const hasExport = /export\s+default\s+function|export\s+default/.test(code);
    const hasFunction = /function\s+\w+|const\s+\w+\s*=|=>\s*{/.test(code);
    const hasReturn = /return\s*\(|return\s+</.test(code);
    const hasJSX = /<\w+/.test(code);

    if (!hasExport) {
      console.log('Code missing export statement');
      return false;
    }

    if (!hasFunction) {
      console.log('Code missing function definition');
      return false;
    }

    if (!hasReturn) {
      console.log('Code missing return statement');
      return false;
    }

    if (!hasJSX) {
      console.log('Code missing JSX elements');
      return false;
    }

    // Check for common syntax errors
    const syntaxErrors = [
      /\$\{[^}]*\$\{/, // Nested template literals
      /`[^`]*`[^`]*`/, // Unescaped template literals in strings
      /className=\{`[^}]*\$\{[^}]*`\}/, // Unescaped template literals in className
    ];

    for (const errorPattern of syntaxErrors) {
      if (errorPattern.test(code)) {
        console.log('Code contains syntax errors:', errorPattern);
        return false;
      }
    }

    console.log('Code validation passed');
    return true;
  }
}

export const aiVisualizationGenerator = AIVisualizationGeneratorService.getInstance();
