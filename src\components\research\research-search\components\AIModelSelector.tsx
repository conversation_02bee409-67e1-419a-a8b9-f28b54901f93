/**
 * AI Model Selector Component for Research Search
 */

import React, { useState } from 'react';
import { Check, ChevronDown, Brain, Zap, Star, Crown, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { AIModel } from '../types';
import { researchAIService } from '../services/research-ai.service';

interface AIModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  className?: string;
  showDescription?: boolean;
  compact?: boolean;
}

export function AIModelSelector({ 
  selectedModel, 
  onModelChange, 
  className,
  showDescription = true,
  compact = false
}: AIModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const models = researchAIService.getAvailableModels();
  const currentModel = models.find(m => m.id === selectedModel) || models[0];

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getCostIcon = (cost: string) => {
    switch (cost) {
      case 'low': return <Zap className="h-3 w-3" />;
      case 'medium': return <Star className="h-3 w-3" />;
      case 'high': return <Crown className="h-3 w-3" />;
      default: return <Brain className="h-3 w-3" />;
    }
  };

  const getProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google': return 'bg-blue-100 text-blue-800';
      case 'anthropic': return 'bg-purple-100 text-purple-800';
      case 'openai': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className="h-8 px-3 text-xs font-medium"
            >
              <Brain className="h-3 w-3 mr-1" />
              {currentModel.name.split(' ')[0]}
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64">
            <DropdownMenuLabel>Select AI Model</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {models.map((model) => (
              <DropdownMenuItem
                key={model.id}
                onClick={() => {
                  onModelChange(model.id);
                  setIsOpen(false);
                }}
                className="flex items-center justify-between p-3"
              >
                <div className="flex items-center space-x-2">
                  {getCostIcon(model.cost)}
                  <span className="font-medium text-sm">{model.name}</span>
                </div>
                {selectedModel === model.id && (
                  <Check className="h-4 w-4 text-blue-600" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700">
          AI Model
        </label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Info className="h-4 w-4 text-gray-400" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Choose the AI model that best fits your research needs</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-auto p-4 hover:bg-gray-50"
          >
            <div className="flex items-center space-x-3">
              {getCostIcon(currentModel.cost)}
              <div className="text-left">
                <div className="font-medium text-gray-900">{currentModel.name}</div>
                <div className="text-xs text-gray-500">{currentModel.provider}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className={cn("text-xs", getCostColor(currentModel.cost))}
              >
                {currentModel.cost} cost
              </Badge>
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </div>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-96 p-0">
          <DropdownMenuLabel className="px-4 py-3 border-b">
            Choose AI Model for Research
          </DropdownMenuLabel>
          
          <div className="max-h-96 overflow-y-auto">
            {models.map((model) => (
              <DropdownMenuItem
                key={model.id}
                onClick={() => {
                  onModelChange(model.id);
                  setIsOpen(false);
                }}
                className="p-4 cursor-pointer hover:bg-gray-50 focus:bg-gray-50"
              >
                <div className="flex items-start justify-between w-full">
                  <div className="flex items-start space-x-3 flex-1">
                    {getCostIcon(model.cost)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900">{model.name}</span>
                        <Badge 
                          variant="secondary" 
                          className={cn("text-xs", getProviderColor(model.provider))}
                        >
                          {model.provider}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                        {model.description}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Max: {model.maxTokens.toLocaleString()} tokens</span>
                        <Badge 
                          variant="outline" 
                          className={cn("text-xs", getCostColor(model.cost))}
                        >
                          {model.cost} cost
                        </Badge>
                      </div>
                      
                      <div className="mt-2 flex flex-wrap gap-1">
                        {model.strengths.slice(0, 3).map((strength, index) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="text-xs px-2 py-0.5 bg-blue-50 text-blue-700 border-blue-200"
                          >
                            {strength}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {selectedModel === model.id && (
                    <Check className="h-5 w-5 text-blue-600 flex-shrink-0 ml-2" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {showDescription && currentModel && (
        <div className="p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-medium text-gray-700">Best for:</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {currentModel.bestFor.map((use, index) => (
              <Badge 
                key={index} 
                variant="secondary" 
                className="text-xs bg-white text-gray-600"
              >
                {use}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
