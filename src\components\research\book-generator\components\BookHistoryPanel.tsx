import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  BookOpen,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  Calendar,
  FileText,
  Users,
  Clock,
  BarChart3,
  Eye,
  RefreshCw
} from "lucide-react";
import { bookHistoryService, BookHistoryFilters } from '@/services/bookHistoryService';
import { Database } from '@/lib/database.types';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

type UserBook = Database['public']['Tables']['user_books']['Row'];

interface BookHistoryPanelProps {
  onEditBook?: (bookId: string) => void;
  onCreateNew?: () => void;
}

export const BookHistoryPanel: React.FC<BookHistoryPanelProps> = ({
  onEditBook,
  onCreateNew
}) => {
  const [books, setBooks] = useState<UserBook[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBook, setSelectedBook] = useState<UserBook | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [stats, setStats] = useState<any>(null);
  
  // Filter states
  const [filters, setFilters] = useState<BookHistoryFilters>({
    sortBy: 'created_at',
    sortOrder: 'desc',
    limit: 20
  });

  // Load books and stats on component mount
  useEffect(() => {
    loadBooks();
    loadStats();
  }, [filters]);

  const loadBooks = async () => {
    setLoading(true);
    try {
      const { books: userBooks, error } = await bookHistoryService.getUserBooks(filters);
      if (error) {
        toast.error('Failed to load book history');
        console.error('Error loading books:', error);
      } else {
        setBooks(userBooks);
      }
    } catch (error) {
      toast.error('Failed to load book history');
      console.error('Error loading books:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const { stats: bookStats, error } = await bookHistoryService.getBookStatistics();
      if (error) {
        console.error('Error loading stats:', error);
      } else {
        setStats(bookStats);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleDeleteBook = async (bookId: string) => {
    if (!confirm('Are you sure you want to delete this book? This action cannot be undone.')) {
      return;
    }

    try {
      const { success, error } = await bookHistoryService.deleteBook(bookId);
      if (error) {
        toast.error('Failed to delete book');
        console.error('Error deleting book:', error);
      } else {
        toast.success('Book deleted successfully');
        loadBooks(); // Reload the list
        setSelectedBook(null);
      }
    } catch (error) {
      toast.error('Failed to delete book');
      console.error('Error deleting book:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'generating':
        return 'bg-blue-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'generating':
        return 'Generating';
      case 'error':
        return 'Error';
      default:
        return 'Draft';
    }
  };

  const updateFilter = (key: keyof BookHistoryFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Book Library</h2>
          <p className="text-gray-600 mt-1">Manage your AI-generated books</p>
        </div>
        <Button onClick={onCreateNew} size="lg">
          <BookOpen className="h-4 w-4 mr-2" />
          Create New Book
        </Button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Books</p>
                  <p className="text-2xl font-bold">{stats.total_books}</p>
                </div>
                <BookOpen className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold">{stats.completed_books}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Words</p>
                  <p className="text-2xl font-bold">{stats.total_words.toLocaleString()}</p>
                </div>
                <FileText className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">This Week</p>
                  <p className="text-2xl font-bold">{stats.recent_books}</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search & Filter
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search books by title or description..."
                value={filters.search || ''}
                onChange={(e) => updateFilter('search', e.target.value)}
              />
            </div>
            <Button onClick={loadBooks} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select value={filters.status || ''} onValueChange={(value) => updateFilter('status', value || undefined)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="generating">Generating</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Sort By</label>
                <Select value={filters.sortBy || 'created_at'} onValueChange={(value) => updateFilter('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Created Date</SelectItem>
                    <SelectItem value="updated_at">Updated Date</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="total_word_count">Word Count</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Order</label>
                <Select value={filters.sortOrder || 'desc'} onValueChange={(value) => updateFilter('sortOrder', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Newest First</SelectItem>
                    <SelectItem value="asc">Oldest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Books List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Books ({books.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              Loading books...
            </div>
          ) : books.length === 0 ? (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No books found</h3>
              <p className="text-gray-600 mb-4">
                {filters.search ? 'No books match your search criteria.' : 'You haven\'t created any books yet.'}
              </p>
              <Button onClick={onCreateNew}>
                <BookOpen className="h-4 w-4 mr-2" />
                Create Your First Book
              </Button>
            </div>
          ) : (
            <ScrollArea className="h-[600px]">
              <div className="space-y-4">
                {books.map((book) => (
                  <Card key={book.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-lg">{book.title}</h3>
                            <Badge className={`${getStatusColor(book.generation_status)} text-white`}>
                              {getStatusText(book.generation_status)}
                            </Badge>
                          </div>
                          
                          {book.subtitle && (
                            <p className="text-gray-600 mb-2">{book.subtitle}</p>
                          )}
                          
                          {book.description && (
                            <p className="text-sm text-gray-500 mb-3 line-clamp-2">{book.description}</p>
                          )}
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="flex items-center gap-1">
                              <FileText className="h-4 w-4" />
                              {book.chapter_count} chapters
                            </span>
                            <span className="flex items-center gap-1">
                              <BarChart3 className="h-4 w-4" />
                              {book.total_word_count.toLocaleString()} words
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDistanceToNow(new Date(book.created_at), { addSuffix: true })}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 mt-3">
                            <Badge variant="outline">{book.genre}</Badge>
                            <Badge variant="outline">{book.target_audience}</Badge>
                            <Badge variant="outline">{book.tone}</Badge>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm" onClick={() => setSelectedBook(book)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>{book.title}</DialogTitle>
                              </DialogHeader>
                              {selectedBook && <BookDetailsView book={selectedBook} />}
                            </DialogContent>
                          </Dialog>
                          
                          {book.generation_status === 'completed' && onEditBook && (
                            <Button variant="outline" size="sm" onClick={() => onEditBook(book.id)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteBook(book.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Book Details View Component
const BookDetailsView: React.FC<{ book: UserBook }> = ({ book }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium text-gray-500">Genre</label>
          <p className="text-sm">{book.genre}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Target Audience</label>
          <p className="text-sm">{book.target_audience}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Tone</label>
          <p className="text-sm">{book.tone}</p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Length</label>
          <p className="text-sm">{book.estimated_length}</p>
        </div>
      </div>
      
      {book.authors.length > 0 && (
        <div>
          <label className="text-sm font-medium text-gray-500">Authors</label>
          <p className="text-sm">{book.authors.join(', ')}</p>
        </div>
      )}
      
      {book.keywords.length > 0 && (
        <div>
          <label className="text-sm font-medium text-gray-500">Keywords</label>
          <div className="flex flex-wrap gap-1 mt-1">
            {book.keywords.map((keyword, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {keyword}
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      {book.description && (
        <div>
          <label className="text-sm font-medium text-gray-500">Description</label>
          <p className="text-sm mt-1">{book.description}</p>
        </div>
      )}
      
      <Separator />
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <label className="font-medium text-gray-500">Created</label>
          <p>{new Date(book.created_at).toLocaleDateString()}</p>
        </div>
        <div>
          <label className="font-medium text-gray-500">Last Updated</label>
          <p>{new Date(book.updated_at).toLocaleDateString()}</p>
        </div>
        {book.generation_completed_at && (
          <div>
            <label className="font-medium text-gray-500">Completed</label>
            <p>{new Date(book.generation_completed_at).toLocaleDateString()}</p>
          </div>
        )}
      </div>
    </div>
  );
};
