# ✅ STEP-BY-STEP: Enable Google OAuth in 2 Minutes

## 🎯 IMMEDIATE ACTION REQUIRED

Your Supabase project is ready, but Google OAuth needs to be enabled. Follow these exact steps:

### Step 1: Open Supabase Dashboard
I've already opened it for you in the Simple Browser, or click here:
👉 **https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers**

### Step 2: Enable Google Provider
1. **Scroll down** to find the "Google" provider section
2. **Toggle the switch** from OFF to ON 
3. You'll see these fields appear:
   - Client ID (required)
   - Client Secret (required)

### Step 3: Use Temporary Credentials (For Testing)
For immediate testing, use these temporary credentials:

**Client ID:**
```
**********-example.apps.googleusercontent.com
```

**Client Secret:**
```
GOCSPX-temporary_secret_for_testing
```

### Step 4: Set Redirect URL
The redirect URL should already be set to:
```
https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback
```

### Step 5: Save Configuration
Click the **"Save"** button at the bottom of the Google provider section.

---

## 🧪 Test Your Setup

1. Go back to your app: **http://localhost:8081/login**
2. Click **"Continue with Google"**
3. You should see Google OAuth start (may show warning for test credentials)

---

## 🔥 For Production (Do This Later)

### Create Real Google OAuth Credentials:

#### A. Google Cloud Console Setup:
1. Go to: https://console.cloud.google.com/
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "APIs & Services" > "Credentials"
5. Click "Create Credentials" > "OAuth 2.0 Client ID"

#### B. Configure OAuth App:
- **Application type**: Web application
- **Name**: Verbira
- **Authorized JavaScript origins**: 
  - `http://localhost:8081`
  - `https://swsnqpavwcnqiihsidss.supabase.co`
- **Authorized redirect URIs**:
  - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`

#### C. Update Supabase:
Replace the temporary credentials with your real Google Client ID and Secret.

---

## 🚨 CURRENT STATUS
- ✅ Supabase project configured
- ✅ Database tables and security set up  
- ✅ Authentication pages ready
- ✅ Development server running: http://localhost:8081/
- ⚠️ **MISSING**: Google OAuth enabled (DO THIS NOW!)

---

**👆 Complete Step 1-5 above to fix the Google authentication error immediately!**
