import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Send, Loader2, Lightbulb } from 'lucide-react';

interface VizChatInputProps {
  onSendMessage: (message: string) => void;
  isGenerating: boolean;
  disabled?: boolean;
  placeholder?: string;
}

const EXAMPLE_PROMPTS = [
  "Create a bar chart showing the distribution of values",
  "Show me a scatter plot to explore correlations",
  "Make a line chart to show trends over time",
  "Create a histogram to show the data distribution",
  "Generate a heatmap to visualize relationships"
];

export const VizChatInput: React.FC<VizChatInputProps> = ({
  onSendMessage,
  isGenerating,
  disabled = false,
  placeholder = "Describe the visualization you want to create..."
}) => {
  const [message, setMessage] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isGenerating && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setMessage(suggestion);
    setShowSuggestions(false);
    textareaRef.current?.focus();
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  return (
    <div className="space-y-3">
      {/* Suggestions */}
      {showSuggestions && (
        <Card className="p-3">
          <div className="flex items-center gap-2 mb-2">
            <Lightbulb className="w-4 h-4 text-yellow-500" />
            <span className="text-sm font-medium">Suggestions</span>
          </div>
          <div className="space-y-1">
            {EXAMPLE_PROMPTS.map((prompt, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(prompt)}
                className="block w-full text-left text-sm p-2 rounded hover:bg-muted transition-colors"
              >
                {prompt}
              </button>
            ))}
          </div>
        </Card>
      )}

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="space-y-3">
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setShowSuggestions(message.length === 0)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            placeholder={placeholder}
            disabled={disabled || isGenerating}
            className="w-full min-h-[60px] max-h-[120px] resize-none pr-12 p-3 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md"
            rows={2}
          />
          
          {/* Send Button */}
          <Button
            type="submit"
            size="sm"
            disabled={!message.trim() || isGenerating || disabled}
            className="absolute bottom-2 right-2 h-8 w-8 p-0"
          >
            {isGenerating ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Helper Text */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          {!showSuggestions && message.length === 0 && (
            <button
              type="button"
              onClick={() => setShowSuggestions(true)}
              className="text-primary hover:underline"
            >
              Show suggestions
            </button>
          )}
        </div>
      </form>
    </div>
  );
};
