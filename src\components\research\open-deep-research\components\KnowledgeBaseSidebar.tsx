/**
 * Knowledge Base Sidebar Component
 * Displays saved reports and allows management of the knowledge base
 */

import React, { useState, useEffect } from 'react';
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Trash2, 
  Download, 
  Upload, 
  Calendar,
  FileText,
  Brain,
  BarChart3,
  X
} from 'lucide-react';
import { toast } from 'sonner';

import { KnowledgeBaseSidebarProps, KnowledgeBaseReport } from '../types';
import { knowledgeBaseService } from '../services/knowledge-base.service';

export const KnowledgeBaseSidebar: React.FC<KnowledgeBaseSidebarProps> = ({
  open,
  onOpenChange,
}) => {
  const [reports, setReports] = useState<KnowledgeBaseReport[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredReports, setFilteredReports] = useState<KnowledgeBaseReport[]>([]);
  const [selectedReport, setSelectedReport] = useState<KnowledgeBaseReport | null>(null);

  // Load reports on mount and when knowledge base changes
  useEffect(() => {
    loadReports();

    const handleKnowledgeBaseChange = () => {
      loadReports();
    };

    window.addEventListener('knowledge_base_change', handleKnowledgeBaseChange);
    return () => {
      window.removeEventListener('knowledge_base_change', handleKnowledgeBaseChange);
    };
  }, []);

  // Filter reports based on search query
  useEffect(() => {
    filterReports();
  }, [searchQuery, reports]);

  const loadReports = async () => {
    try {
      const loadedReports = await knowledgeBaseService.getReports();
      setReports(loadedReports);
    } catch (error) {
      console.error('Failed to load reports:', error);
      toast.error('Failed to load knowledge base');
    }
  };

  const filterReports = async () => {
    try {
      if (searchQuery.trim()) {
        const searchResults = await knowledgeBaseService.searchReports(searchQuery);
        setFilteredReports(searchResults);
      } else {
        setFilteredReports(reports);
      }
    } catch (error) {
      console.error('Failed to filter reports:', error);
      setFilteredReports(reports);
    }
  };

  const handleDeleteReport = async (reportId: string) => {
    try {
      const success = await knowledgeBaseService.deleteReport(reportId);
      if (success) {
        toast.success('Report deleted successfully');
        if (selectedReport?.id === reportId) {
          setSelectedReport(null);
        }
        await loadReports(); // Reload reports
      } else {
        toast.error('Failed to delete report');
      }
    } catch (error) {
      toast.error('Failed to delete report');
    }
  };

  const handleExportKnowledgeBase = async () => {
    try {
      const jsonData = await knowledgeBaseService.exportAsJson();
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `knowledge-base-${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Knowledge base exported successfully');
    } catch (error) {
      toast.error('Failed to export knowledge base');
    }
  };

  const handleImportKnowledgeBase = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const jsonData = e.target?.result as string;
        const success = await knowledgeBaseService.importFromJson(jsonData);
        if (success) {
          toast.success('Knowledge base imported successfully');
          await loadReports(); // Reload reports
        } else {
          toast.error('Failed to import knowledge base');
        }
      } catch (error) {
        toast.error('Invalid file format');
      }
    };
    reader.readAsText(file);

    // Reset file input
    event.target.value = '';
  };

  const handleClearAll = async () => {
    if (window.confirm('Are you sure you want to delete all reports? This action cannot be undone.')) {
      try {
        const success = await knowledgeBaseService.clearAll();
        if (success) {
          toast.success('Knowledge base cleared');
          setSelectedReport(null);
          await loadReports(); // Reload reports
        } else {
          toast.error('Failed to clear knowledge base');
        }
      } catch (error) {
        toast.error('Failed to clear knowledge base');
      }
    }
  };

  // Get stats (async)
  const [stats, setStats] = useState({
    totalReports: 0,
    totalQueries: 0,
    oldestReport: null as Date | null,
    newestReport: null as Date | null,
    averageReportLength: 0,
  });

  useEffect(() => {
    const loadStats = async () => {
      try {
        const loadedStats = await knowledgeBaseService.getStats();
        setStats(loadedStats);
      } catch (error) {
        console.error('Failed to load stats:', error);
      }
    };

    loadStats();
  }, [reports]);

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full sm:w-[500px] p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-6 pb-4">
            <div className="flex items-center justify-between">
              <SheetTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                Knowledge Base
              </SheetTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>{stats.totalReports} reports</span>
              <span>{stats.totalQueries} unique queries</span>
            </div>
          </SheetHeader>

          <Separator />

          {/* Search and Actions */}
          <div className="p-4 space-y-3">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search reports..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
              <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportKnowledgeBase}
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              
              <div className="relative">
                <Input
                  type="file"
                  accept=".json"
                  onChange={handleImportKnowledgeBase}
                  className="absolute inset-0 opacity-0 cursor-pointer"
                />
                <Button variant="outline" size="sm" className="pointer-events-none">
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </Button>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Separator />

          {/* Reports List */}
          <div className="flex-1 overflow-hidden">
            {selectedReport ? (
              // Report Detail View
              <div className="h-full flex flex-col">
                <div className="p-4 border-b">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedReport(null)}
                    className="mb-2"
                  >
                    ← Back to list
                  </Button>
                  <h3 className="font-semibold text-lg line-clamp-2">
                    {selectedReport.report.title}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {new Date(selectedReport.timestamp).toLocaleDateString()}
                  </p>
                </div>
                
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Original Query</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        {selectedReport.query}
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Summary</h4>
                      <p className="text-sm text-gray-700">
                        {selectedReport.report.summary}
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Sections ({selectedReport.report.sections.length})</h4>
                      <div className="space-y-2">
                        {selectedReport.report.sections.map((section, index) => (
                          <div key={index} className="text-sm">
                            <div className="font-medium text-gray-700">{section.title}</div>
                            <div className="text-gray-600 line-clamp-3 mt-1">
                              {section.content.slice(0, 200)}...
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Sources ({selectedReport.report.sources.length})</h4>
                      <div className="space-y-1">
                        {selectedReport.report.sources.map((source, index) => (
                          <div key={source.id} className="text-sm">
                            <a
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {index + 1}. {source.name}
                            </a>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            ) : (
              // Reports List View
              <ScrollArea className="h-full">
                <div className="p-4 space-y-3">
                  {filteredReports.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">
                        {searchQuery ? 'No reports match your search' : 'No reports saved yet'}
                      </p>
                    </div>
                  ) : (
                    filteredReports.map((report) => (
                      <Card
                        key={report.id}
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedReport(report)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between gap-2 mb-2">
                            <h3 className="font-medium line-clamp-2 text-sm">
                              {report.report.title}
                            </h3>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteReport(report.id);
                              }}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                            {report.query}
                          </p>
                          
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {report.report.sections.length} sections
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {report.report.sources.length} sources
                              </Badge>
                            </div>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(report.timestamp).toLocaleDateString()}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
