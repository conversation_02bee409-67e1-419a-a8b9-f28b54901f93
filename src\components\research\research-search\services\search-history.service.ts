/**
 * Search History Service
 * Manages search sessions and history with Supabase integration
 */

import { supabase } from '@/lib/supabase';
import { SearchSession, SearchMessage, SearchStats, UserPreferences } from '../types';

export class SearchHistoryService {
  private currentUserId: string | null = null;
  private deepResearchDataCache: Map<string, any> = new Map(); // Cache for deep research data

  constructor() {
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  private async initializeAuth() {
    try {
      // First try to get the current session without throwing errors
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.warn('Auth session error during initialization:', error);
        this.currentUserId = null;
      } else {
        this.currentUserId = session?.user?.id || null;
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange((event, session) => {
        this.currentUserId = session?.user?.id || null;
        console.log('Auth state changed in search history service:', event, this.currentUserId ? 'User logged in' : 'User logged out');
      });
    } catch (error) {
      console.warn('Failed to initialize auth in search history service:', error);
      this.currentUserId = null;
    }
  }

  /**
   * Create a new search session
   */
  async createSession(title: string, model: string): Promise<SearchSession> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated to create search sessions');
    }

    const session = {
      user_id: this.currentUserId,
      title: title || 'New Research Session',
      model,
      total_queries: 0,
      is_active: true,
      session_metadata: {}
    };

    // Deactivate all other sessions first
    await supabase
      .from('research_search_sessions')
      .update({ is_active: false })
      .eq('user_id', this.currentUserId);

    const { data, error } = await supabase
      .from('research_search_sessions')
      .insert([session])
      .select()
      .single();

    if (error) {
      console.error('Error creating search session:', error);
      throw new Error('Failed to create search session');
    }

    return {
      id: data.id,
      title: data.title,
      messages: [],
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      userId: data.user_id,
      model: data.model,
      totalQueries: data.total_queries,
      isActive: data.is_active
    };
  }

  /**
   * Get all search sessions for current user
   */
  async getUserSessions(): Promise<SearchSession[]> {
    if (!this.currentUserId) {
      return [];
    }

    const { data, error } = await supabase
      .from('research_search_sessions')
      .select('*')
      .eq('user_id', this.currentUserId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching search sessions:', error);
      return [];
    }

    return data.map(session => ({
      id: session.id,
      title: session.title,
      messages: [], // Messages loaded separately
      createdAt: new Date(session.created_at),
      updatedAt: new Date(session.updated_at),
      userId: session.user_id,
      model: session.model,
      totalQueries: session.total_queries || 0,
      isActive: session.is_active || false
    }));
  }

  /**
   * Get a specific search session with messages
   */
  async getSession(sessionId: string): Promise<SearchSession | null> {
    if (!this.currentUserId) {
      return null;
    }

    // Get session details
    const { data: sessionData, error: sessionError } = await supabase
      .from('research_search_sessions')
      .select('*')
      .eq('id', sessionId)
      .eq('user_id', this.currentUserId)
      .single();

    if (sessionError || !sessionData) {
      console.error('Error fetching session:', sessionError);
      return null;
    }

    // Get session messages (excluding deep_research_data column that doesn't exist)
    const { data: messagesData, error: messagesError } = await supabase
      .from('research_search_messages')
      .select('id, session_id, type, content, search_query, sources, citations, is_deep_research, created_at')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching messages:', messagesError);
    }

    const messages: SearchMessage[] = (messagesData || []).map(msg => {
      const message: any = {
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: new Date(msg.created_at),
        searchQuery: msg.search_query,
        sources: msg.sources || undefined,
        citations: msg.citations || undefined,
        isDeepResearch: msg.is_deep_research || false
      };

      // Add deep research data from cache (since deep_research_data column doesn't exist)
      const cachedDeepData = this.getDeepResearchData(sessionId, msg.id);
      if (cachedDeepData && Object.keys(cachedDeepData).length > 0) {
        if (cachedDeepData.session) {
          message.deepResearchSession = cachedDeepData.session;
        }
        if (cachedDeepData.outline) {
          message.deepResearchOutline = cachedDeepData.outline;
        }
        if (cachedDeepData.progress) {
          message.deepResearchProgress = cachedDeepData.progress;
        }
        if (cachedDeepData.completedSection) {
          message.completedSection = cachedDeepData.completedSection;
        }
      }

      return message;
    });

    return {
      id: sessionData.id,
      title: sessionData.title,
      messages,
      createdAt: new Date(sessionData.created_at),
      updatedAt: new Date(sessionData.updated_at),
      userId: sessionData.user_id,
      model: sessionData.model,
      totalQueries: sessionData.total_queries || 0,
      isActive: sessionData.is_active || false
    };
  }

  /**
   * Add message to session
   */
  async addMessage(sessionId: string, message: Omit<SearchMessage, 'id'>): Promise<SearchMessage> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    // Prepare deep research data
    const deepResearchData: any = {};
    if (message.deepResearchSession) {
      deepResearchData.session = message.deepResearchSession;
    }
    if (message.deepResearchOutline) {
      deepResearchData.outline = message.deepResearchOutline;
    }
    if (message.deepResearchProgress) {
      deepResearchData.progress = message.deepResearchProgress;
    }
    if (message.completedSection) {
      deepResearchData.completedSection = message.completedSection;
    }

    // Sanitize and validate data before insertion
    const sanitizedContent = message.content ? message.content.substring(0, 50000) : ''; // Limit content size
    const sanitizedSources = message.sources ? message.sources.slice(0, 20) : null; // Limit sources
    const sanitizedCitations = message.citations ? message.citations.slice(0, 50) : null; // Limit citations

    // Prepare message data without deep_research_data column (not in database schema)
    const messageData = {
      session_id: sessionId,
      type: message.type,
      content: sanitizedContent,
      search_query: message.searchQuery ? message.searchQuery.substring(0, 1000) : null,
      sources: sanitizedSources,
      citations: sanitizedCitations,
      is_deep_research: message.isDeepResearch || false,
      created_at: message.timestamp.toISOString()
    };

    // Store deep research data separately in memory for this session
    if (Object.keys(deepResearchData).length > 0) {
      this.storeDeepResearchData(sessionId, message.id, deepResearchData);
    }

    try {
      const { data, error } = await supabase
        .from('research_search_messages')
        .insert([messageData])
        .select('id, session_id, type, content, search_query, sources, citations, is_deep_research, created_at')
        .single();

      if (error) {
        console.error('Error adding message:', error);
        console.error('Message data that failed:', {
          ...messageData,
          content: messageData.content.substring(0, 100) + '...',
          sources: messageData.sources ? `${messageData.sources.length} sources` : null,
          citations: messageData.citations ? `${messageData.citations.length} citations` : null
        });
        throw new Error(`Failed to add message: ${error.message}`);
      }

      // Update session's updated_at and total_queries if it's a user message
      if (message.type === 'user') {
        try {
          const { data: sessionData } = await supabase
            .from('research_search_sessions')
            .select('total_queries')
            .eq('id', sessionId)
            .single();

          const currentQueries = sessionData?.total_queries || 0;

          await supabase
            .from('research_search_sessions')
            .update({
              updated_at: new Date().toISOString(),
              total_queries: currentQueries + 1
            })
            .eq('id', sessionId);
        } catch (updateError) {
          console.error('Error updating session:', updateError);
        }
      }

      return {
        id: data.id,
        type: data.type,
        content: data.content,
        timestamp: new Date(data.created_at),
        searchQuery: data.search_query,
        sources: data.sources,
        citations: data.citations,
        isDeepResearch: data.is_deep_research,
        // Get deep research data from cache since it's not in database
        ...this.getDeepResearchDataForMessage(sessionId, data.id)
      };
    } catch (dbError: any) {
      console.error('Database error when adding message:', dbError);

      // Return a temporary message object for offline functionality
      return {
        id: `temp-${Date.now()}`,
        type: message.type,
        content: message.content,
        timestamp: message.timestamp,
        searchQuery: message.searchQuery,
        sources: message.sources,
        citations: message.citations,
        isDeepResearch: message.isDeepResearch,
        deepResearchSession: message.deepResearchSession,
        deepResearchOutline: message.deepResearchOutline,
        deepResearchProgress: message.deepResearchProgress,
        completedSection: message.completedSection
      };
    }

    // Note: Session update is handled in the try block above for successful database operations
  }

  /**
   * Update session title
   */
  async updateSessionTitle(sessionId: string, title: string): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    const { error } = await supabase
      .from('research_search_sessions')
      .update({ 
        title,
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionId)
      .eq('user_id', this.currentUserId);

    if (error) {
      console.error('Error updating session title:', error);
      throw new Error('Failed to update session title');
    }
  }

  /**
   * Delete a search session
   */
  async deleteSession(sessionId: string): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    // Delete messages first (due to foreign key constraint)
    await supabase
      .from('research_search_messages')
      .delete()
      .eq('session_id', sessionId);

    // Delete session
    const { error } = await supabase
      .from('research_search_sessions')
      .delete()
      .eq('id', sessionId)
      .eq('user_id', this.currentUserId);

    if (error) {
      console.error('Error deleting session:', error);
      throw new Error('Failed to delete session');
    }
  }

  /**
   * Get search statistics for current user
   */
  async getSearchStats(): Promise<SearchStats> {
    if (!this.currentUserId) {
      return {
        totalSearches: 0,
        totalSessions: 0,
        averageResponseTime: 0,
        mostUsedModel: '',
        topDomains: []
      };
    }

    try {
      // Get session count and total queries
      const { data: sessionStats } = await supabase
        .from('research_search_sessions')
        .select('total_queries, model')
        .eq('user_id', this.currentUserId);

      const totalSessions = sessionStats?.length || 0;
      const totalSearches = sessionStats?.reduce((sum, session) => sum + (session.total_queries || 0), 0) || 0;

      // Get most used model
      const modelCounts = sessionStats?.reduce((acc, session) => {
        acc[session.model] = (acc[session.model] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      const mostUsedModel = Object.entries(modelCounts)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

      return {
        totalSearches,
        totalSessions,
        averageResponseTime: 2.5, // Placeholder - would need to track actual response times
        mostUsedModel,
        topDomains: [] // Placeholder - would need to analyze sources
      };

    } catch (error) {
      console.error('Error getting search stats:', error);
      return {
        totalSearches: 0,
        totalSessions: 0,
        averageResponseTime: 0,
        mostUsedModel: '',
        topDomains: []
      };
    }
  }

  /**
   * Search through user's search history
   */
  async searchHistory(query: string): Promise<SearchSession[]> {
    if (!this.currentUserId) {
      return [];
    }

    const { data, error } = await supabase
      .from('research_search_sessions')
      .select('*')
      .eq('user_id', this.currentUserId)
      .or(`title.ilike.%${query}%`)
      .order('updated_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('Error searching history:', error);
      return [];
    }

    return data.map(session => ({
      id: session.id,
      title: session.title,
      messages: [],
      createdAt: new Date(session.created_at),
      updatedAt: new Date(session.updated_at),
      userId: session.user_id,
      model: session.model,
      totalQueries: session.total_queries || 0,
      isActive: session.is_active || false
    }));
  }

  /**
   * Set active session
   */
  async setActiveSession(sessionId: string): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    // Deactivate all sessions
    await supabase
      .from('research_search_sessions')
      .update({ is_active: false })
      .eq('user_id', this.currentUserId);

    // Activate selected session
    const { error } = await supabase
      .from('research_search_sessions')
      .update({ is_active: true })
      .eq('id', sessionId)
      .eq('user_id', this.currentUserId);

    if (error) {
      console.error('Error setting active session:', error);
      throw new Error('Failed to set active session');
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(): Promise<UserPreferences> {
    if (!this.currentUserId) {
      return this.getDefaultPreferences();
    }

    try {
      const { data, error } = await supabase
        .from('research_search_preferences')
        .select('*')
        .eq('user_id', this.currentUserId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching preferences:', error);
        return this.getDefaultPreferences();
      }

      if (!data) {
        return this.getDefaultPreferences();
      }

      return {
        defaultModel: data.default_model || 'google/gemini-2.0-flash-001',
        defaultSearchDepth: data.default_search_depth || 'advanced',
        maxResults: data.max_results || 10,
        autoSave: data.auto_save !== false,
        showSources: data.show_sources !== false,
        showCitations: data.show_citations !== false,
        theme: data.theme || 'auto'
      };
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<void> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    try {
      const updateData = {
        user_id: this.currentUserId,
        default_model: preferences.defaultModel,
        default_search_depth: preferences.defaultSearchDepth,
        max_results: preferences.maxResults,
        auto_save: preferences.autoSave,
        show_sources: preferences.showSources,
        show_citations: preferences.showCitations,
        theme: preferences.theme,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('research_search_preferences')
        .upsert([updateData]);

      if (error) {
        console.error('Error updating preferences:', error);
        throw new Error('Failed to update preferences');
      }
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      throw error;
    }
  }

  /**
   * Get default preferences
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      defaultModel: 'google/gemini-2.0-flash-001',
      defaultSearchDepth: 'advanced',
      maxResults: 10,
      autoSave: true,
      showSources: true,
      showCitations: true,
      theme: 'auto'
    };
  }

  /**
   * Store deep research data in memory cache
   */
  private storeDeepResearchData(sessionId: string, messageId: string, data: any): void {
    const key = `${sessionId}_${messageId}`;
    this.deepResearchDataCache.set(key, data);
  }

  /**
   * Retrieve deep research data from memory cache
   */
  private getDeepResearchData(sessionId: string, messageId: string): any {
    const key = `${sessionId}_${messageId}`;
    return this.deepResearchDataCache.get(key) || {};
  }

  /**
   * Clear deep research data cache for a session
   */
  clearDeepResearchCache(sessionId: string): void {
    const keysToDelete = Array.from(this.deepResearchDataCache.keys())
      .filter(key => key.startsWith(`${sessionId}_`));

    keysToDelete.forEach(key => {
      this.deepResearchDataCache.delete(key);
    });
  }

  /**
   * Get deep research data for a message from cache
   */
  private getDeepResearchDataForMessage(sessionId: string, messageId: string): any {
    const cachedData = this.getDeepResearchData(sessionId, messageId);
    return {
      deepResearchSession: cachedData.session,
      deepResearchOutline: cachedData.outline,
      deepResearchProgress: cachedData.progress,
      completedSection: cachedData.completedSection
    };
  }
}

export const searchHistoryService = new SearchHistoryService();
