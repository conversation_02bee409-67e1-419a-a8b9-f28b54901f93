/**
 * Flowchart Fun Zustand Store
 * State management for the Flowchart Fun module
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { nanoid } from 'nanoid';
import {
  FlowchartFunStore,
  FlowchartGraph,
  ExportOptions,
  ExportResult,
  FlowchartHistory,
  ParseError
} from '../types';
import { DEFAULT_FLOWCHART_TEXT, STORAGE_KEYS } from '../constants';
import { textParserService } from '../services/text-parser.service';

const initialState = {
  // Text and parsing
  text: DEFAULT_FLOWCHART_TEXT,
  parsedGraph: null as FlowchartGraph | null,
  parseErrors: [] as ParseError[],
  isValidSyntax: true,
  
  // UI state
  isLoading: false,
  selectedNode: null as string | null,
  selectedEdge: null as string | null,
  cursorPosition: { line: 1, column: 1 },
  
  // Export state
  isExporting: false,
  exportProgress: 0,
  lastExportResult: null as ExportResult | null,
  
  // Settings
  autoSave: true,
  theme: 'light' as 'light' | 'dark',
  layoutAlgorithm: 'dagre' as 'dagre' | 'cose' | 'grid' | 'circle',
  
  // History
  history: [] as FlowchartHistory[],
  historyIndex: -1,
  canUndo: false,
  canRedo: false
};

export const useFlowchartFunStore = create<FlowchartFunStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Initialize store with parsed default text
        initialize: () => {
          const currentText = get().text;
          if (currentText && !get().parsedGraph) {
            // Parse the initial text
            const parseResult = textParserService.parse(currentText);
            set((state) => {
              state.parsedGraph = parseResult.graph || null;
              state.parseErrors = parseResult.errors || [];
              state.isValidSyntax = parseResult.success;
            });
          }
        },

        // Text operations
        setText: (text: string) => {
          set((state) => {
            state.text = text;
            state.isLoading = true;
          });
          
          // Parse text asynchronously
          setTimeout(() => {
            const parseResult = textParserService.parse(text);

            set((state) => {
              state.isLoading = false;
              state.parsedGraph = parseResult.graph || null;
              state.parseErrors = parseResult.errors || [];
              state.isValidSyntax = parseResult.success;
            });

            // Add to history if valid
            if (parseResult.success && parseResult.graph) {
              get().addToHistory({
                id: nanoid(),
                text,
                graph: parseResult.graph,
                timestamp: new Date(),
                description: 'Text updated'
              });
            }
          }, 100);
        },

        updateText: (text: string) => {
          get().setText(text);
        },

        insertText: (text: string, position?: { line: number; column: number }) => {
          const currentText = get().text;
          const lines = currentText.split('\n');
          
          if (position) {
            const lineIndex = position.line - 1;
            if (lineIndex >= 0 && lineIndex < lines.length) {
              const line = lines[lineIndex];
              const before = line.substring(0, position.column - 1);
              const after = line.substring(position.column - 1);
              lines[lineIndex] = before + text + after;
            }
          } else {
            lines.push(text);
          }
          
          get().setText(lines.join('\n'));
        },

        // Graph operations
        parseText: () => {
          const text = get().text;
          set((state) => {
            state.isLoading = true;
          });
          
          const parseResult = textParserService.parse(text);
          set((state) => {
            state.isLoading = false;
            state.parsedGraph = parseResult.graph || null;
            state.parseErrors = parseResult.errors || [];
            state.isValidSyntax = parseResult.success;
          });
        },

        updateGraph: (graph: FlowchartGraph) => {
          set((state) => {
            state.parsedGraph = graph;
          });
        },

        selectNode: (nodeId: string | null) => {
          set((state) => {
            state.selectedNode = nodeId;
            state.selectedEdge = null;
          });
        },

        selectEdge: (edgeId: string | null) => {
          set((state) => {
            state.selectedEdge = edgeId;
            state.selectedNode = null;
          });
        },

        // Export operations
        exportGraph: async (options: ExportOptions): Promise<ExportResult> => {
          const { parsedGraph, text } = get();
          
          if (!parsedGraph) {
            const result: ExportResult = {
              success: false,
              error: 'No graph to export'
            };
            set((state) => {
              state.lastExportResult = result;
            });
            return result;
          }

          set((state) => {
            state.isExporting = true;
            state.exportProgress = 0;
          });

          try {
            // Simulate export progress
            const progressInterval = setInterval(() => {
              set((state) => {
                state.exportProgress = Math.min(state.exportProgress + 10, 90);
              });
            }, 100);

            // Simulate export delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            clearInterval(progressInterval);

            // Create export data based on format
            let data: Blob | string;
            let filename = options.filename || `flowchart.${options.format}`;

            switch (options.format) {
              case 'json':
                data = JSON.stringify(parsedGraph, null, 2);
                break;
              case 'txt':
                data = text;
                break;
              case 'png':
              case 'svg':
              case 'pdf':
                // For now, create a placeholder blob
                data = new Blob(['Exported flowchart'], { type: 'text/plain' });
                break;
              case 'word':
                // Create a simple text export for now
                data = new Blob([`Flowchart Export\n\n${text}`], { 
                  type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
                });
                break;
              default:
                throw new Error(`Unsupported export format: ${options.format}`);
            }

            const result: ExportResult = {
              success: true,
              data,
              filename
            };

            set((state) => {
              state.isExporting = false;
              state.exportProgress = 100;
              state.lastExportResult = result;
            });

            return result;
          } catch (error) {
            const result: ExportResult = {
              success: false,
              error: error instanceof Error ? error.message : 'Export failed'
            };

            set((state) => {
              state.isExporting = false;
              state.exportProgress = 0;
              state.lastExportResult = result;
            });

            return result;
          }
        },

        setExportProgress: (progress: number) => {
          set((state) => {
            state.exportProgress = Math.max(0, Math.min(100, progress));
          });
        },

        // History operations
        undo: () => {
          const { history, historyIndex } = get();
          if (historyIndex > 0) {
            const entry = history[historyIndex - 1];
            set((state) => {
              state.text = entry.text;
              state.parsedGraph = entry.graph;
              state.historyIndex = historyIndex - 1;
              state.canUndo = historyIndex - 1 > 0;
              state.canRedo = true;
            });
          }
        },

        redo: () => {
          const { history, historyIndex } = get();
          if (historyIndex < history.length - 1) {
            const entry = history[historyIndex + 1];
            set((state) => {
              state.text = entry.text;
              state.parsedGraph = entry.graph;
              state.historyIndex = historyIndex + 1;
              state.canUndo = true;
              state.canRedo = historyIndex + 1 < history.length - 1;
            });
          }
        },

        addToHistory: (entry: FlowchartHistory) => {
          set((state) => {
            // Remove any entries after current index
            state.history = state.history.slice(0, state.historyIndex + 1);
            
            // Add new entry
            state.history.push(entry);
            
            // Limit history size
            if (state.history.length > 50) {
              state.history = state.history.slice(-50);
            }
            
            state.historyIndex = state.history.length - 1;
            state.canUndo = state.history.length > 1;
            state.canRedo = false;
          });
        },

        clearHistory: () => {
          set((state) => {
            state.history = [];
            state.historyIndex = -1;
            state.canUndo = false;
            state.canRedo = false;
          });
        },

        // Settings
        setTheme: (theme: 'light' | 'dark') => {
          set((state) => {
            state.theme = theme;
          });
        },

        setLayoutAlgorithm: (algorithm: 'dagre' | 'cose' | 'grid' | 'circle') => {
          set((state) => {
            state.layoutAlgorithm = algorithm;
          });
        },

        setAutoSave: (enabled: boolean) => {
          set((state) => {
            state.autoSave = enabled;
          });
        },

        // UI operations
        setCursorPosition: (line: number, column: number) => {
          set((state) => {
            state.cursorPosition = { line, column };
          });
        },

        setLoading: (loading: boolean) => {
          set((state) => {
            state.isLoading = loading;
          });
        },

        clearSelection: () => {
          set((state) => {
            state.selectedNode = null;
            state.selectedEdge = null;
          });
        }
      })),
      {
        name: STORAGE_KEYS.FLOWCHART_TEXT,
        partialize: (state) => ({
          text: state.text,
          theme: state.theme,
          layoutAlgorithm: state.layoutAlgorithm,
          autoSave: state.autoSave
        })
      }
    ),
    {
      name: 'flowchart-fun-store'
    }
  )
);
