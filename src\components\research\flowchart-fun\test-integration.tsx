/**
 * Integration Test for Flowchart Fun Module
 * Simple test component to verify the integration works
 */

import React from 'react';
import { FlowchartFun } from './FlowchartFun';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useFlowchartFunStore } from './stores/flowchart-fun.store';

export function FlowchartFunIntegrationTest() {
  const { text, parsedGraph, parseErrors, isValidSyntax } = useFlowchartFunStore();

  const testText = `Research Methodology
  Literature Review: Systematic approach
    Database Search: PubMed, Scopus
    Inclusion Criteria: Peer-reviewed
    Exclusion Criteria: Non-English
  Data Collection: Primary data
    Survey Design: Questionnaire
    Sampling: Random sampling
    Ethics Approval: IRB review
  Data Analysis: Statistical methods
    Descriptive: Mean, SD
    Inferential: t-tests, ANOVA
    Software: SPSS, R
  Results: Key findings
    Significant Results: p < 0.05
    Effect Sizes: <PERSON>'s d
  Discussion: Interpretation
    Limitations: Sample size
    Implications: Clinical practice
  Conclusion: Summary of findings`;

  const handleLoadTest = () => {
    useFlowchartFunStore.getState().setText(testText);
  };

  const handleClear = () => {
    useFlowchartFunStore.getState().setText('');
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Flowchart Fun Integration Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Button onClick={handleLoadTest}>Load Test Data</Button>
            <Button variant="outline" onClick={handleClear}>Clear</Button>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Text Length:</strong> {text.length} characters
            </div>
            <div>
              <strong>Valid Syntax:</strong> {isValidSyntax ? '✅ Yes' : '❌ No'}
            </div>
            <div>
              <strong>Nodes:</strong> {parsedGraph?.nodes.length || 0}
            </div>
            <div>
              <strong>Edges:</strong> {parsedGraph?.edges.length || 0}
            </div>
            <div>
              <strong>Parse Errors:</strong> {parseErrors.length}
            </div>
            <div>
              <strong>Graph Status:</strong> {parsedGraph ? '✅ Generated' : '❌ None'}
            </div>
          </div>

          {parseErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <strong className="text-red-800">Parse Errors:</strong>
              <ul className="mt-2 text-red-700">
                {parseErrors.map((error, index) => (
                  <li key={index}>Line {error.line}: {error.message}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="h-[600px]">
        <FlowchartFun />
      </div>
    </div>
  );
}
