/**
 * Test file for Agent Mode functionality
 * This file contains test cases and sample documents for testing Agent Mode
 */

import { DocumentAnalyzer } from './DocumentAnalyzer';
import { AgentModeService } from './AgentModeService';
import { validatePrompt, validateDocumentContent, analyzePromptQuality } from './utils';

// Sample test documents
export const testDocuments = {
  academicPaper: `
    <h1>The Impact of AI on Modern Research</h1>
    
    <h2>Abstract</h2>
    <p>This paper examines the transformative effects of artificial intelligence on contemporary research methodologies. We analyze how AI tools are reshaping data collection, analysis, and interpretation across various academic disciplines.</p>
    
    <h2>Introduction</h2>
    <p>Artificial intelligence has emerged as a revolutionary force in modern research. The integration of AI technologies into research workflows has fundamentally altered how scholars approach complex problems and analyze vast datasets.</p>
    <p>This study aims to provide a comprehensive overview of AI's impact on research practices, examining both opportunities and challenges that arise from this technological integration.</p>
    
    <h2>Methodology</h2>
    <p>Our research employed a mixed-methods approach, combining quantitative analysis of research output data with qualitative interviews of researchers across multiple disciplines.</p>
    <p>We collected data from 500 research institutions worldwide, analyzing publication patterns, research efficiency metrics, and researcher satisfaction surveys conducted between 2020 and 2024.</p>
    
    <h2>Results</h2>
    <p>The analysis revealed significant improvements in research efficiency, with AI-assisted projects showing 40% faster completion times compared to traditional methods.</p>
    <p>However, concerns about data quality and interpretation accuracy were raised by 35% of surveyed researchers, highlighting the need for careful validation of AI-generated insights.</p>
    
    <h2>Discussion</h2>
    <p>The findings suggest that while AI offers substantial benefits for research acceleration, careful consideration must be given to maintaining research integrity and quality standards.</p>
    <p>The integration of AI tools requires new frameworks for validation and quality assurance to ensure reliable research outcomes.</p>
    
    <h2>Conclusion</h2>
    <p>AI represents both an opportunity and a challenge for modern research. Successful integration requires thoughtful implementation, ongoing validation, and continuous adaptation of research methodologies.</p>
    <p>Future research should focus on developing standardized protocols for AI-assisted research to maximize benefits while minimizing potential risks.</p>
  `,

  shortArticle: `
    <h1>Climate Change and Urban Planning</h1>
    
    <h2>Introduction</h2>
    <p>Climate change poses significant challenges for urban planners worldwide. Rising temperatures, changing precipitation patterns, and extreme weather events require adaptive planning strategies.</p>
    
    <h2>Key Challenges</h2>
    <p>Urban areas face unique vulnerabilities including heat island effects, flooding risks, and infrastructure strain. Planners must balance immediate needs with long-term sustainability goals.</p>
    
    <h2>Solutions</h2>
    <p>Innovative approaches include green infrastructure, resilient building codes, and community-based adaptation strategies. Technology integration and stakeholder engagement are crucial for success.</p>
  `,

  reportStructure: `
    <h1>Annual Research Report 2024</h1>
    
    <h2>Executive Summary</h2>
    <p>This report presents our organization's research achievements and findings for the fiscal year 2024, highlighting key breakthroughs and future directions.</p>
    
    <h2>Chapter 1: Research Overview</h2>
    <p>Our research program encompasses three main areas: computational biology, materials science, and environmental sustainability.</p>
    
    <h2>Chapter 2: Key Findings</h2>
    <p>Significant discoveries were made in protein folding prediction, novel semiconductor materials, and carbon capture technologies.</p>
    
    <h2>Chapter 3: Impact and Applications</h2>
    <p>Research outcomes have led to 15 patent applications, 45 peer-reviewed publications, and 3 commercial partnerships.</p>
    
    <h2>Recommendations</h2>
    <p>We recommend increased investment in interdisciplinary research and enhanced collaboration with industry partners.</p>
  `
};

// Test prompts for different scenarios
export const testPrompts = {
  good: [
    "Improve the introduction for better clarity and engagement",
    "Enhance the methodology section with more detailed explanations",
    "Make the conclusion more compelling and impactful",
    "Revise the discussion section for improved flow and analysis",
    "Clarify the results presentation for better understanding"
  ],
  
  problematic: [
    "Fix it",
    "Make it better",
    "Delete everything and start over",
    "Rewrite the entire document",
    "Change all the text"
  ],
  
  ambiguous: [
    "Improve this",
    "Make changes",
    "Edit the content",
    "Update the document",
    "Modify the text"
  ]
};

// Test functions
export async function testDocumentAnalysis() {
  console.log('Testing Document Analysis...');
  
  const analyzer = DocumentAnalyzer.getInstance();
  
  // Test academic paper analysis
  const academicStructure = analyzer.analyzeDocument(testDocuments.academicPaper);
  console.log('Academic Paper Structure:', {
    headings: academicStructure.headings.length,
    sections: academicStructure.sections.length,
    documentType: academicStructure.structure,
    wordCount: academicStructure.wordCount
  });
  
  // Test short article analysis
  const articleStructure = analyzer.analyzeDocument(testDocuments.shortArticle);
  console.log('Short Article Structure:', {
    headings: articleStructure.headings.length,
    sections: articleStructure.sections.length,
    documentType: articleStructure.structure,
    wordCount: articleStructure.wordCount
  });
  
  return { academicStructure, articleStructure };
}

export function testPromptValidation() {
  console.log('Testing Prompt Validation...');
  
  // Test good prompts
  testPrompts.good.forEach(prompt => {
    const validation = validatePrompt(prompt);
    const quality = analyzePromptQuality(prompt);
    console.log(`Good prompt "${prompt}":`, {
      valid: validation.isValid,
      quality: quality.score,
      feedback: quality.feedback
    });
  });
  
  // Test problematic prompts
  testPrompts.problematic.forEach(prompt => {
    const validation = validatePrompt(prompt);
    console.log(`Problematic prompt "${prompt}":`, {
      valid: validation.isValid,
      error: validation.error,
      suggestions: validation.suggestions
    });
  });
  
  // Test ambiguous prompts
  testPrompts.ambiguous.forEach(prompt => {
    const quality = analyzePromptQuality(prompt);
    console.log(`Ambiguous prompt "${prompt}":`, {
      quality: quality.score,
      suggestions: quality.suggestions
    });
  });
}

export function testDocumentValidation() {
  console.log('Testing Document Validation...');
  
  // Test valid documents
  Object.entries(testDocuments).forEach(([name, content]) => {
    const validation = validateDocumentContent(content);
    console.log(`Document "${name}":`, {
      valid: validation.isValid,
      length: content.length
    });
  });
  
  // Test invalid documents
  const invalidDocs = {
    empty: '',
    tooShort: 'Short text',
    tooLong: 'x'.repeat(60000)
  };
  
  Object.entries(invalidDocs).forEach(([name, content]) => {
    const validation = validateDocumentContent(content);
    console.log(`Invalid document "${name}":`, {
      valid: validation.isValid,
      error: validation.error
    });
  });
}

// Main test function
export async function runAgentModeTests() {
  console.log('🧪 Running Agent Mode Tests...\n');
  
  try {
    // Test document analysis
    await testDocumentAnalysis();
    console.log('✅ Document analysis tests passed\n');
    
    // Test prompt validation
    testPromptValidation();
    console.log('✅ Prompt validation tests passed\n');
    
    // Test document validation
    testDocumentValidation();
    console.log('✅ Document validation tests passed\n');
    
    console.log('🎉 All Agent Mode tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Agent Mode tests failed:', error);
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testAgentMode = {
    runTests: runAgentModeTests,
    testDocuments,
    testPrompts,
    testDocumentAnalysis,
    testPromptValidation,
    testDocumentValidation
  };
}
