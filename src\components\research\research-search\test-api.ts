/**
 * Test file to verify API integrations
 * This file can be used to test Tavily and OpenRouter APIs
 */

import { tavilySearchService } from './services/tavily-search.service';
import { researchAIService } from './services/research-ai.service';

export async function testTavilyAPI() {
  console.log('Testing Tavily API...');
  
  try {
    const result = await tavilySearchService.searchAcademic('artificial intelligence', {
      maxResults: 3,
      searchDepth: 'basic'
    });
    
    console.log('Tavily API test successful:', {
      query: result.query,
      resultsCount: result.results.length,
      hasAnswer: !!result.answer,
      responseTime: result.response_time
    });
    
    return { success: true, result };
  } catch (error) {
    console.error('Tavily API test failed:', error);
    return { success: false, error: error.message };
  }
}

export async function testOpenRouterAPI() {
  console.log('Testing OpenRouter API...');
  
  try {
    // Create a mock search result for testing
    const mockSearchResult = {
      query: 'test query',
      answer: 'test answer',
      results: [
        {
          title: 'Test Article',
          url: 'https://example.com',
          content: 'This is a test article about artificial intelligence.',
          score: 0.95,
          published_date: '2024-01-01'
        }
      ],
      images: [],
      response_time: 1.5,
      follow_up_questions: []
    };
    
    const result = await researchAIService.generateResponse(
      'What is artificial intelligence?',
      mockSearchResult,
      'google/gemini-2.0-flash-001'
    );
    
    console.log('OpenRouter API test successful:', {
      responseLength: result.response.length,
      citationsCount: result.citations.length
    });
    
    return { success: true, result };
  } catch (error) {
    console.error('OpenRouter API test failed:', error);
    return { success: false, error: error.message };
  }
}

export async function testFullWorkflow() {
  console.log('Testing full research workflow...');
  
  try {
    // Step 1: Search with Tavily
    const searchResult = await tavilySearchService.searchAcademic('machine learning', {
      maxResults: 5,
      searchDepth: 'advanced'
    });
    
    // Step 2: Generate AI response
    const aiResult = await researchAIService.generateResponse(
      'What is machine learning?',
      searchResult,
      'google/gemini-2.0-flash-001'
    );
    
    console.log('Full workflow test successful:', {
      searchResultsCount: searchResult.results.length,
      aiResponseLength: aiResult.response.length,
      citationsCount: aiResult.citations.length
    });
    
    return { success: true, searchResult, aiResult };
  } catch (error) {
    console.error('Full workflow test failed:', error);
    return { success: false, error: error.message };
  }
}

// Export test runner function
export async function runAllTests() {
  console.log('🧪 Running Research Search API Tests...\n');
  
  const results = {
    tavily: await testTavilyAPI(),
    openRouter: await testOpenRouterAPI(),
    fullWorkflow: await testFullWorkflow()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('Tavily API:', results.tavily.success ? '✅ PASS' : '❌ FAIL');
  console.log('OpenRouter API:', results.openRouter.success ? '✅ PASS' : '❌ FAIL');
  console.log('Full Workflow:', results.fullWorkflow.success ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(r => r.success);
  console.log('\nOverall:', allPassed ? '🎉 ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED');
  
  return results;
}

// For browser console testing
if (typeof window !== 'undefined') {
  (window as any).testResearchSearch = {
    testTavilyAPI,
    testOpenRouterAPI,
    testFullWorkflow,
    runAllTests
  };
}
