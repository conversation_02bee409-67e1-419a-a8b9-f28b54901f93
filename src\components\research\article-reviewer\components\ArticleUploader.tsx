import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Upload, FileText, AlertCircle, X, Check } from 'lucide-react';
import { ParsedArticle } from '../types';

interface ArticleUploaderProps {
  articleFile: File | null;
  setArticleFile: (file: File | null) => void;
  parsedContent: ParsedArticle | null;
  onStartReview: () => void;
  isAnalyzing: boolean;
  onCancelReview: () => void;
}

export function ArticleUploader({
  articleFile,
  setArticleFile,
  parsedContent,
  onStartReview,
  isAnalyzing,
  onCancelReview
}: ArticleUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    validateAndSetFile(file);
  };
  
  // Handle drag and drop events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    setError(null);
    
    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    validateAndSetFile(file);
  };
  
  // Validate file type and set it if valid
  const validateAndSetFile = (file: File) => {
    const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!validTypes.includes(file.type) && !['pdf', 'docx'].includes(fileExtension || '')) {
      setError('Please upload a PDF or DOCX file.');
      return;
    }
    
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      setError('File size exceeds the 50MB limit. Please upload a smaller file.');
      return;
    }
    
    setArticleFile(file);
  };
  
  // Clear the selected file
  const clearFile = () => {
    setArticleFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' bytes';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <Card className="h-full shadow-md">
      <CardHeader className="pb-2 bg-gradient-to-r from-gray-50 to-gray-100 border-b">
        <CardTitle>Upload Research Article</CardTitle>
        <CardDescription>
          Upload a PDF or DOCX file of your academic paper or article for AI review.
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {error && (
          <Alert variant="destructive" className="m-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {!articleFile ? (
          // Upload area
          <div className="p-6">
            <div 
              className={`border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-all hover:shadow-md ${
                dragActive ? 'border-blue-400 bg-blue-50 shadow-md' : 'border-gray-300 hover:border-blue-300'
              }`}
              onClick={() => fileInputRef.current?.click()}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
            >
              <div className="bg-gradient-to-r from-blue-500 to-indigo-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-md">
                <Upload className="h-8 w-8 text-white" />
              </div>
              <p className="text-xl font-medium mb-2">
                Drag and drop your article, or click to browse
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Supported formats: PDF, DOCX (Max 50MB)
              </p>
              <Button 
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-sm"
                size="lg"
              >
                Select File
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                onChange={handleFileChange}
              />
            </div>
          </div>
        ) : (
          // File preview and actions
          <div className="p-6 space-y-6">
            <div className="flex items-start p-5 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 shadow-sm">
              <div className="bg-blue-100 p-2 rounded-md mr-4">
                <FileText className="h-10 w-10 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-lg">{articleFile.name}</h4>
                <p className="text-sm text-gray-500 mt-1">
                  {formatFileSize(articleFile.size)} • {articleFile.type || `${articleFile.name.split('.').pop()} file`}
                </p>
                {parsedContent && (
                  <div className="mt-2 text-xs text-green-600 flex items-center bg-white/60 py-1 px-2 rounded-full w-fit">
                    <Check className="h-3 w-3 mr-1" />
                    Successfully parsed article content
                  </div>
                )}
              </div>
              <Button 
                variant="outline"
                size="sm"
                disabled={isAnalyzing}
                onClick={clearFile}
                className="border-blue-200 hover:border-red-300 hover:bg-red-50"
              >
                <X className="h-4 w-4 text-gray-500 hover:text-red-500" />
              </Button>
            </div>
            
            {parsedContent && (
              <div className="space-y-6">
                <div className="bg-white p-5 rounded-lg border shadow-sm">
                  <div className="font-medium mb-3 text-blue-800 flex items-center">
                    <span className="bg-blue-100 p-1 rounded-md mr-2">
                      <Check className="h-4 w-4 text-blue-600" />
                    </span>
                    Detected Sections:
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    {Object.entries(parsedContent.sectionMapping)
                      .filter(([key]) => key !== 'overall')
                      .map(([key, value]) => value ? (
                        <div key={key} className="flex items-center bg-gray-50 p-2 rounded-md border hover:border-blue-300 hover:bg-blue-50 transition-colors">
                          <Check className="h-3 w-3 mr-2 text-green-500" />
                          <span className="capitalize">{key}</span>
                        </div>
                      ) : null)}
                  </div>
                </div>
                
                <div className="flex justify-center gap-4 pt-2">
                  {isAnalyzing ? (
                    <Button 
                      variant="destructive"
                      className="shadow-sm"
                      onClick={onCancelReview}
                    >
                      Cancel Analysis
                    </Button>
                  ) : (
                    <Button 
                      onClick={onStartReview}
                      className="w-full md:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-md px-8 py-6 h-auto text-lg"
                    >
                      <FileText className="h-5 w-5 mr-2" />
                      Start AI Review
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
