/**
 * Journal Search Service
 * Handles journal discovery using Tavily API for academic research
 */

import { tavilySearchService } from '../../research-search/services/tavily-search.service';
import { 
  JournalRecommendation, 
  ArticleContent, 
  JournalMetrics,
  JournalField 
} from '../types';

export class JournalSearchService {
  private readonly maxSearchResults = 20;
  private readonly searchTimeout = 30000; // 30 seconds

  constructor() {
    console.log('Journal Search Service initialized');
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return tavilySearchService.isConfigured();
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      return await tavilySearchService.testConnection();
    } catch (error) {
      console.error('Journal search service connection test failed:', error);
      return false;
    }
  }

  /**
   * Search for journals based on research content
   */
  async searchJournals(
    articleContent: ArticleContent,
    researchField: string,
    keyTopics: string[]
  ): Promise<JournalRecommendation[]> {
    try {
      console.log('🔍 Searching for journals...', { researchField, keyTopics });

      // Build search queries for journal discovery
      const searchQueries = this.buildJournalSearchQueries(
        articleContent,
        researchField,
        keyTopics
      );

      const allJournals: JournalRecommendation[] = [];

      // Execute searches for each query
      for (const query of searchQueries) {
        try {
          const searchResults = await tavilySearchService.searchAcademic(query, {
            maxResults: Math.ceil(this.maxSearchResults / searchQueries.length),
            searchDepth: 'advanced',
            includeAnswer: false,
            includeImages: false
          });

          // Extract journal information from search results
          const journals = this.extractJournalsFromResults(
            searchResults.results,
            query,
            researchField
          );

          allJournals.push(...journals);
        } catch (searchError) {
          console.warn(`Search failed for query "${query}":`, searchError);
          // Continue with other queries
        }
      }

      // Deduplicate and rank journals
      const uniqueJournals = this.deduplicateJournals(allJournals);
      const rankedJournals = this.rankJournalsByRelevance(uniqueJournals, keyTopics);

      console.log(`✅ Found ${rankedJournals.length} unique journals`);
      return rankedJournals.slice(0, 12); // Return top 12 recommendations

    } catch (error) {
      console.error('Journal search failed:', error);
      throw new Error(`Failed to search for journals: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Search for recent publications in similar research areas
   */
  async findSimilarResearch(
    keyTopics: string[],
    researchField: string
  ): Promise<{ publications: any[]; journals: string[] }> {
    try {
      const searchQuery = this.buildSimilarResearchQuery(keyTopics, researchField);
      
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 15,
        searchDepth: 'comprehensive',
        includeAnswer: true,
        includeImages: false
      });

      const publications = searchResults.results.map(result => ({
        title: result.title,
        url: result.url,
        content: result.content,
        publishedDate: result.published_date,
        score: result.score
      }));

      // Extract journal names from publications
      const journals = this.extractJournalNamesFromPublications(publications);

      return { publications, journals };

    } catch (error) {
      console.error('Similar research search failed:', error);
      return { publications: [], journals: [] };
    }
  }

  /**
   * Get journal metrics and information
   */
  async getJournalMetrics(journalName: string): Promise<JournalMetrics | null> {
    try {
      const searchQuery = `"${journalName}" journal impact factor metrics acceptance rate`;
      
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true,
        includeImages: false
      });

      return this.extractMetricsFromResults(searchResults.results, journalName);

    } catch (error) {
      console.error(`Failed to get metrics for journal "${journalName}":`, error);
      return null;
    }
  }

  /**
   * Build search queries for journal discovery
   */
  private buildJournalSearchQueries(
    articleContent: ArticleContent,
    researchField: string,
    keyTopics: string[]
  ): string[] {
    const queries: string[] = [];

    // Query 1: Field-specific journal search
    queries.push(`"${researchField}" academic journals impact factor`);

    // Query 2: Topic-specific journal search
    if (keyTopics.length > 0) {
      const topicsQuery = keyTopics.slice(0, 3).join(' ');
      queries.push(`"${topicsQuery}" research journals academic publishing`);
    }

    // Query 3: Methodology-based search (if full content available)
    if (articleContent.type === 'full' && articleContent.content.length > 1000) {
      queries.push(`academic journals "${researchField}" peer review`);
    }

    // Query 4: Recent publications search
    queries.push(`recent publications "${researchField}" 2024 2023 journals`);

    return queries;
  }

  /**
   * Build query for finding similar research
   */
  private buildSimilarResearchQuery(keyTopics: string[], researchField: string): string {
    const topicsString = keyTopics.slice(0, 4).join(' ');
    return `"${topicsString}" "${researchField}" recent research publications 2024 2023`;
  }

  /**
   * Extract journal information from search results
   */
  private extractJournalsFromResults(
    results: any[],
    searchQuery: string,
    researchField: string
  ): JournalRecommendation[] {
    const journals: JournalRecommendation[] = [];

    for (const result of results) {
      const journalInfo = this.parseJournalFromResult(result, searchQuery, researchField);
      if (journalInfo) {
        journals.push(journalInfo);
      }
    }

    return journals;
  }

  /**
   * Parse journal information from a single search result
   */
  private parseJournalFromResult(
    result: any,
    searchQuery: string,
    researchField: string
  ): JournalRecommendation | null {
    try {
      // Extract journal name from title or content
      const journalName = this.extractJournalName(result.title, result.content);
      if (!journalName) return null;

      // Create journal recommendation
      return {
        id: `journal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: journalName,
        publisher: this.extractPublisher(result.content) || 'Unknown Publisher',
        issn: '',
        website: result.url,
        description: this.extractDescription(result.content) || result.content.substring(0, 200),
        scope: this.extractScope(result.content, researchField),
        targetAudience: 'Academic researchers',
        editorialBoard: [],
        recentTopics: [],
        metrics: this.extractBasicMetrics(result.content),
        matchScore: this.calculateMatchScore(result, searchQuery),
        matchReasons: this.generateMatchReasons(result, searchQuery),
        submissionGuidelines: 'Check journal website for detailed guidelines',
        specialIssues: [],
        geographicFocus: 'International',
        languageRequirements: ['English']
      };

    } catch (error) {
      console.warn('Failed to parse journal from result:', error);
      return null;
    }
  }

  /**
   * Extract journal name from text
   */
  private extractJournalName(title: string, content: string): string | null {
    // Common patterns for journal names
    const patterns = [
      /Journal of ([^,\n]+)/i,
      /([^,\n]+) Journal/i,
      /International Journal of ([^,\n]+)/i,
      /([^,\n]+) Review/i,
      /([^,\n]+) Letters/i,
      /([^,\n]+) Communications/i
    ];

    const text = `${title} ${content}`;
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[0].trim();
      }
    }

    // Fallback: use title if it looks like a journal name
    if (title.toLowerCase().includes('journal') || 
        title.toLowerCase().includes('review') ||
        title.toLowerCase().includes('letters')) {
      return title.split('-')[0].trim();
    }

    return null;
  }

  /**
   * Extract publisher information
   */
  private extractPublisher(content: string): string | null {
    const patterns = [
      /published by ([^,\n]+)/i,
      /publisher: ([^,\n]+)/i,
      /(Elsevier|Springer|Wiley|Taylor & Francis|IEEE|ACM|Nature|Science)/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Extract basic metrics from content
   */
  private extractBasicMetrics(content: string): JournalMetrics {
    const impactFactorMatch = content.match(/impact factor[:\s]*(\d+\.?\d*)/i);
    const acceptanceRateMatch = content.match(/acceptance rate[:\s]*(\d+)%?/i);
    
    return {
      impactFactor: impactFactorMatch ? parseFloat(impactFactorMatch[1]) : 0,
      citeScore: 0,
      hIndex: 0,
      acceptanceRate: acceptanceRateMatch ? parseInt(acceptanceRateMatch[1]) : 0,
      averageReviewTime: 90, // Default 3 months
      averagePublicationTime: 120, // Default 4 months
      publicationFees: 0,
      isOpenAccess: content.toLowerCase().includes('open access'),
      quartile: 'Q3' // Default
    };
  }

  /**
   * Calculate match score based on search result relevance
   */
  private calculateMatchScore(result: any, searchQuery: string): number {
    let score = result.score || 0.5;
    
    // Boost score for exact matches
    if (result.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      score += 0.2;
    }
    
    // Boost for academic sources
    if (result.url.includes('.edu') || result.url.includes('academic') || 
        result.url.includes('journal')) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Generate match reasons
   */
  private generateMatchReasons(result: any, searchQuery: string): string[] {
    const reasons: string[] = [];
    
    if (result.title.toLowerCase().includes('journal')) {
      reasons.push('Academic journal publication');
    }
    
    if (result.url.includes('.edu')) {
      reasons.push('University-affiliated source');
    }
    
    if (result.content.toLowerCase().includes('peer review')) {
      reasons.push('Peer-reviewed publication');
    }
    
    return reasons;
  }

  /**
   * Extract scope from content
   */
  private extractScope(content: string, researchField: string): string[] {
    const scope = [researchField];
    
    // Add common academic terms found in content
    const terms = ['research', 'analysis', 'methodology', 'theory', 'application'];
    for (const term of terms) {
      if (content.toLowerCase().includes(term)) {
        scope.push(term);
      }
    }
    
    return scope;
  }

  /**
   * Extract description from content
   */
  private extractDescription(content: string): string | null {
    // Look for description patterns
    const patterns = [
      /abstract[:\s]*([^.]+\.)/i,
      /description[:\s]*([^.]+\.)/i,
      /about[:\s]*([^.]+\.)/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Deduplicate journals by name
   */
  private deduplicateJournals(journals: JournalRecommendation[]): JournalRecommendation[] {
    const seen = new Set<string>();
    const unique: JournalRecommendation[] = [];

    for (const journal of journals) {
      const key = journal.name.toLowerCase().trim();
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(journal);
      }
    }

    return unique;
  }

  /**
   * Rank journals by relevance to key topics
   */
  private rankJournalsByRelevance(
    journals: JournalRecommendation[],
    keyTopics: string[]
  ): JournalRecommendation[] {
    return journals.sort((a, b) => {
      let scoreA = a.matchScore;
      let scoreB = b.matchScore;

      // Boost score for topic matches
      for (const topic of keyTopics) {
        if (a.name.toLowerCase().includes(topic.toLowerCase()) ||
            a.description.toLowerCase().includes(topic.toLowerCase())) {
          scoreA += 0.1;
        }
        if (b.name.toLowerCase().includes(topic.toLowerCase()) ||
            b.description.toLowerCase().includes(topic.toLowerCase())) {
          scoreB += 0.1;
        }
      }

      return scoreB - scoreA;
    });
  }

  /**
   * Extract journal names from publications
   */
  private extractJournalNamesFromPublications(publications: any[]): string[] {
    const journals: string[] = [];
    
    for (const pub of publications) {
      const journalName = this.extractJournalName(pub.title, pub.content);
      if (journalName && !journals.includes(journalName)) {
        journals.push(journalName);
      }
    }
    
    return journals;
  }

  /**
   * Extract metrics from search results
   */
  private extractMetricsFromResults(results: any[], journalName: string): JournalMetrics | null {
    for (const result of results) {
      if (result.title.toLowerCase().includes(journalName.toLowerCase()) ||
          result.content.toLowerCase().includes(journalName.toLowerCase())) {
        return this.extractBasicMetrics(result.content);
      }
    }
    
    return null;
  }
}

export const journalSearchService = new JournalSearchService();
