import { supabase } from '@/lib/supabase';
import { 
  ArticleReviewResults, 
  ParsedArticle, 
  SectionReview, 
  ReviewMetrics, 
  DetailedFeedbackItem 
} from '../types';

/**
 * Database types for article reviews
 */
export interface SavedArticleReview {
  id: string;
  user_id: string;
  title: string;
  original_filename: string;
  file_path?: string;
  file_type: string;
  file_size: number;
  ai_model_used: string;
  review_status: 'pending' | 'processing' | 'completed' | 'error';
  overall_score?: number;
  clarity_score?: number;
  structure_score?: number;
  methodology_score?: number;
  significance_score?: number;
  originality_score?: number;
  review_summary?: string;
  major_strengths?: string[];
  major_weaknesses?: string[];
  recommendations?: string[];
  word_count?: number;
  sections_analyzed?: string[];
  processing_time_seconds?: number;
  created_at: string;
  updated_at: string;
}

export interface SavedSectionReview {
  id: string;
  article_review_id: string;
  section_name: string;
  section_content?: string;
  analysis: string;
  strengths?: string[];
  weaknesses?: string[];
  suggestions?: string[];
  score?: number;
  word_count?: number;
  created_at: string;
}

export interface SavedDetailedFeedback {
  id: string;
  section_review_id: string;
  original_text: string;
  issue_description: string;
  suggestion: string;
  feedback_type: string;
  severity: string;
  line_number?: number;
  highlight_color?: string;
  created_at: string;
}

/**
 * Service for managing article review storage in Supabase
 */
export class ArticleReviewStorageService {
  
  /**
   * Save a complete article review to the database
   */
  async saveArticleReview(
    parsedArticle: ParsedArticle,
    reviewResults: ArticleReviewResults,
    aiModel: string,
    processingTimeSeconds?: number
  ): Promise<{ data: SavedArticleReview | null; error: any }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      // Extract overall metrics
      const overall = reviewResults.overall as ReviewMetrics;
      
      // Get sections that were analyzed
      const sectionsAnalyzed = Object.keys(reviewResults).filter(key => key !== 'overall');

      // Create the main article review record
      const articleReviewData = {
        user_id: user.id,
        title: parsedArticle.title || parsedArticle.fileName.replace(/\.[^/.]+$/, ""),
        original_filename: parsedArticle.fileName,
        file_type: parsedArticle.fileType,
        file_size: parsedArticle.fileSize,
        ai_model_used: aiModel,
        review_status: 'completed' as const,
        overall_score: overall?.overallScore,
        clarity_score: overall?.clarity,
        structure_score: overall?.structure,
        methodology_score: overall?.methodology,
        significance_score: overall?.significance,
        originality_score: overall?.originality,
        review_summary: overall?.summary,
        major_strengths: overall?.majorStrengths,
        major_weaknesses: overall?.majorWeaknesses,
        recommendations: overall?.recommendations,
        sections_analyzed: sectionsAnalyzed,
        processing_time_seconds: processingTimeSeconds
      };

      const { data: savedReview, error: reviewError } = await supabase
        .from('article_reviews')
        .insert(articleReviewData)
        .select()
        .single();

      if (reviewError) {
        return { data: null, error: reviewError };
      }

      // Save section reviews
      for (const [sectionName, sectionData] of Object.entries(reviewResults)) {
        if (sectionName === 'overall') continue;
        
        const sectionReview = sectionData as SectionReview;
        await this.saveSectionReview(savedReview.id, sectionName, sectionReview);
      }

      return { data: savedReview, error: null };
    } catch (error) {
      console.error('Error saving article review:', error);
      return { data: null, error };
    }
  }

  /**
   * Save a section review and its detailed feedback
   */
  private async saveSectionReview(
    articleReviewId: string,
    sectionName: string,
    sectionReview: SectionReview
  ): Promise<void> {
    try {
      // Save section review
      const sectionData = {
        article_review_id: articleReviewId,
        section_name: sectionName,
        section_content: sectionReview.content,
        analysis: sectionReview.analysis,
        strengths: sectionReview.strengths,
        weaknesses: sectionReview.weaknesses,
        suggestions: sectionReview.suggestions,
        score: sectionReview.score
      };

      const { data: savedSection, error: sectionError } = await supabase
        .from('article_section_reviews')
        .insert(sectionData)
        .select()
        .single();

      if (sectionError) {
        console.error('Error saving section review:', sectionError);
        return;
      }

      // Save detailed feedback items
      if (sectionReview.detailedFeedback && sectionReview.detailedFeedback.length > 0) {
        const feedbackData = sectionReview.detailedFeedback.map((feedback: DetailedFeedbackItem) => ({
          section_review_id: savedSection.id,
          original_text: feedback.originalText,
          issue_description: feedback.issue,
          suggestion: feedback.suggestion,
          feedback_type: feedback.type,
          severity: feedback.severity,
          line_number: feedback.lineNumber,
          highlight_color: feedback.highlightColor
        }));

        const { error: feedbackError } = await supabase
          .from('article_detailed_feedback')
          .insert(feedbackData);

        if (feedbackError) {
          console.error('Error saving detailed feedback:', feedbackError);
        }
      }
    } catch (error) {
      console.error('Error saving section review:', error);
    }
  }

  /**
   * Get all article reviews for the current user
   */
  async getUserArticleReviews(
    limit = 20,
    offset = 0,
    orderBy: 'created_at' | 'updated_at' | 'overall_score' = 'created_at'
  ): Promise<{ data: SavedArticleReview[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('article_reviews')
        .select('*')
        .order(orderBy, { ascending: false })
        .range(offset, offset + limit - 1);

      return { data, error };
    } catch (error) {
      console.error('Error fetching user article reviews:', error);
      return { data: null, error };
    }
  }

  /**
   * Get a specific article review with all its sections and feedback
   */
  async getArticleReviewById(reviewId: string): Promise<{
    review: SavedArticleReview | null;
    sections: SavedSectionReview[];
    feedback: Record<string, SavedDetailedFeedback[]>;
    error: any;
  }> {
    try {
      // Get the main review
      const { data: review, error: reviewError } = await supabase
        .from('article_reviews')
        .select('*')
        .eq('id', reviewId)
        .single();

      if (reviewError) {
        return { review: null, sections: [], feedback: {}, error: reviewError };
      }

      // Get section reviews
      const { data: sections, error: sectionsError } = await supabase
        .from('article_section_reviews')
        .select('*')
        .eq('article_review_id', reviewId)
        .order('section_name');

      if (sectionsError) {
        return { review, sections: [], feedback: {}, error: sectionsError };
      }

      // Get detailed feedback for all sections
      const feedback: Record<string, SavedDetailedFeedback[]> = {};
      
      for (const section of sections || []) {
        const { data: sectionFeedback, error: feedbackError } = await supabase
          .from('article_detailed_feedback')
          .select('*')
          .eq('section_review_id', section.id)
          .order('line_number');

        if (!feedbackError && sectionFeedback) {
          feedback[section.section_name] = sectionFeedback;
        }
      }

      return { review, sections: sections || [], feedback, error: null };
    } catch (error) {
      console.error('Error fetching article review:', error);
      return { review: null, sections: [], feedback: {}, error };
    }
  }

  /**
   * Delete an article review and all associated data
   */
  async deleteArticleReview(reviewId: string): Promise<{ error: any }> {
    try {
      const { error } = await supabase
        .from('article_reviews')
        .delete()
        .eq('id', reviewId);

      return { error };
    } catch (error) {
      console.error('Error deleting article review:', error);
      return { error };
    }
  }

  /**
   * Search article reviews by title or content
   */
  async searchArticleReviews(
    query: string,
    limit = 20
  ): Promise<{ data: SavedArticleReview[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('article_reviews')
        .select('*')
        .or(`title.ilike.%${query}%,review_summary.ilike.%${query}%`)
        .order('created_at', { ascending: false })
        .limit(limit);

      return { data, error };
    } catch (error) {
      console.error('Error searching article reviews:', error);
      return { data: null, error };
    }
  }

  /**
   * Update article review metadata (title, tags, etc.)
   */
  async updateArticleReview(
    reviewId: string,
    updates: Partial<Pick<SavedArticleReview, 'title'>>
  ): Promise<{ data: SavedArticleReview | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('article_reviews')
        .update(updates)
        .eq('id', reviewId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error updating article review:', error);
      return { data: null, error };
    }
  }

  /**
   * Get review statistics for the current user
   */
  async getUserReviewStats(): Promise<{
    totalReviews: number;
    averageScore: number;
    recentReviews: number;
    error: any;
  }> {
    try {
      const { data, error } = await supabase
        .from('article_reviews')
        .select('overall_score, created_at')
        .eq('review_status', 'completed');

      if (error) {
        return { totalReviews: 0, averageScore: 0, recentReviews: 0, error };
      }

      const totalReviews = data?.length || 0;
      const scores = data?.map(r => r.overall_score).filter(Boolean) || [];
      const averageScore = scores.length > 0 
        ? scores.reduce((sum, score) => sum + score, 0) / scores.length 
        : 0;

      // Count reviews from last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentReviews = data?.filter(r => 
        new Date(r.created_at) > thirtyDaysAgo
      ).length || 0;

      return { totalReviews, averageScore, recentReviews, error: null };
    } catch (error) {
      console.error('Error fetching user review stats:', error);
      return { totalReviews: 0, averageScore: 0, recentReviews: 0, error };
    }
  }
}

export const articleReviewStorageService = new ArticleReviewStorageService();
