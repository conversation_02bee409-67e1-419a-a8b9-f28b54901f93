import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import {
  FileText,
  BookOpen,
  Target,
  Lightbulb,
  Search,
  Filter,
  Calendar,
  Download,
  Eye,
  Trash2,
  Star,
  StarOff,
  Clock,
  User,
  BarChart3,
  TrendingUp,
  Activity,
  RefreshCw,
  Archive,
  Plus
} from "lucide-react";

import { ResearchDocument, LiteratureReview, GapAnalysis, ResearchHypothesis } from '../types';
import { researchDocumentStorageService } from '../services/research-document-storage.service';
import { analysisResultsStorageService } from '../services/analysis-results-storage.service';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from "@/lib/utils";

interface ResearchHistoryDashboardProps {
  className?: string;
  onDocumentSelect?: (document: ResearchDocument) => void;
  onAnalysisSelect?: (analysis: any) => void;
  onContinueSession?: (documents: ResearchDocument[], reviews: LiteratureReview[], analyses: GapAnalysis[]) => void;
}

export function ResearchHistoryDashboard({
  className,
  onDocumentSelect,
  onAnalysisSelect,
  onContinueSession
}: ResearchHistoryDashboardProps) {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<ResearchDocument[]>([]);
  const [literatureReviews, setLiteratureReviews] = useState<LiteratureReview[]>([]);
  const [gapAnalyses, setGapAnalyses] = useState<GapAnalysis[]>([]);
  const [hypotheses, setHypotheses] = useState<ResearchHypothesis[]>([]);
  const [activity, setActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'sessions'>('sessions');

  // Load data on component mount and when user changes
  useEffect(() => {
    if (user) {
      loadAllData();
    }
  }, [user]);

  // Group data by sessions (by date)
  const groupedSessions = useMemo(() => {
    const sessions: { [key: string]: { documents: ResearchDocument[], reviews: LiteratureReview[], analyses: GapAnalysis[], date: string } } = {};

    // Group documents by date
    documents.forEach(doc => {
      const date = new Date(doc.uploadedAt || doc.lastModified || new Date()).toDateString();
      if (!sessions[date]) {
        sessions[date] = { documents: [], reviews: [], analyses: [], date };
      }
      sessions[date].documents.push(doc);
    });

    // Group reviews by date
    literatureReviews.forEach(review => {
      const date = new Date(review.generatedAt).toDateString();
      if (!sessions[date]) {
        sessions[date] = { documents: [], reviews: [], analyses: [], date };
      }
      sessions[date].reviews.push(review);
    });

    // Group analyses by date
    gapAnalyses.forEach(analysis => {
      const date = new Date(analysis.generatedAt).toDateString();
      if (!sessions[date]) {
        sessions[date] = { documents: [], reviews: [], analyses: [], date };
      }
      sessions[date].analyses.push(analysis);
    });

    return Object.values(sessions).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [documents, literatureReviews, gapAnalyses]);

  // Continue session function
  const handleContinueSession = useCallback((sessionData: { documents: ResearchDocument[], reviews: LiteratureReview[], analyses: GapAnalysis[] }) => {
    if (onContinueSession) {
      onContinueSession(sessionData.documents, sessionData.reviews, sessionData.analyses);
      toast.success(`Continuing session with ${sessionData.documents.length} documents, ${sessionData.reviews.length} reviews, and ${sessionData.analyses.length} analyses`);
    }
  }, [onContinueSession]);



  const loadAllData = async () => {
    setLoading(true);
    try {
      if (!user) {
        setLoading(false);
        return;
      }

      // Load documents
      const { data: docsData, error: docsError } = await researchDocumentStorageService.getUserResearchDocuments(50);
      if (docsError) {
        setDocuments([]);
      } else {
        setDocuments(docsData || []);
      }

      // Load literature reviews
      const { data: reviewsData, error: reviewsError } = await analysisResultsStorageService.getUserLiteratureReviews(20);
      if (reviewsError) {
        setLiteratureReviews([]);
      } else {
        setLiteratureReviews(reviewsData || []);
      }

      // Load gap analyses
      const { data: gapsData, error: gapsError } = await analysisResultsStorageService.getUserGapAnalyses(20);
      if (gapsError) {
        setGapAnalyses([]);
      } else {
        setGapAnalyses(gapsData || []);
      }

      // Load hypotheses
      const { data: hypsData, error: hypsError } = await analysisResultsStorageService.getUserResearchHypotheses(50);
      if (hypsError) {
        setHypotheses([]);
      } else {
        setHypotheses(hypsData || []);
      }

      // Load activity
      const { data: activityData, error: activityError } = await analysisResultsStorageService.getUserActivity(50);
      if (activityError) {
        setActivity([]);
      } else {
        setActivity(activityData || []);
      }

    } catch (error) {
      toast.error('Failed to load research history');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      return;
    }

    const { success, error } = await researchDocumentStorageService.deleteResearchDocument(documentId);
    if (success) {
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      toast.success('Document deleted successfully');
    } else {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    }
  };

  const handleDeleteAnalysis = async (type: string, id: string) => {
    if (!confirm('Are you sure you want to delete this analysis? This action cannot be undone.')) {
      return;
    }

    const { success, error } = await analysisResultsStorageService.deleteAnalysisResult(type as any, id);
    if (success) {
      // Update state based on type
      if (type === 'literature_review') {
        setLiteratureReviews(prev => prev.filter(review => review.id !== id));
      } else if (type === 'gap_analysis') {
        setGapAnalyses(prev => prev.filter(analysis => analysis.id !== id));
      } else if (type === 'hypothesis') {
        setHypotheses(prev => prev.filter(hyp => hyp.id !== id));
      }
      toast.success('Analysis deleted successfully');
    } else {
      console.error('Error deleting analysis:', error);
      toast.error('Failed to delete analysis');
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchQuery === '' || 
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const stats = {
    totalDocuments: documents.length,
    readyDocuments: documents.filter(d => d.status === 'ready').length,
    totalReviews: literatureReviews.length,
    totalGapAnalyses: gapAnalyses.length,
    totalHypotheses: hypotheses.length,
    recentActivity: activity.slice(0, 10)
  };

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg">Loading research history...</span>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>


      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Research History
          </h1>
          <p className="text-gray-600 mt-1">View and manage your research documents and analysis results</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadAllData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">{stats.totalDocuments}</div>
            <div className="text-sm text-blue-800 font-medium">Documents</div>
            <div className="text-xs text-blue-600 mt-1">
              <FileText className="h-3 w-3 inline mr-1" />
              {stats.readyDocuments} ready
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600 mb-1">{stats.totalReviews}</div>
            <div className="text-sm text-purple-800 font-medium">Literature Reviews</div>
            <div className="text-xs text-purple-600 mt-1">
              <BookOpen className="h-3 w-3 inline mr-1" />
              Generated
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600 mb-1">{stats.totalGapAnalyses}</div>
            <div className="text-sm text-orange-800 font-medium">Gap Analyses</div>
            <div className="text-xs text-orange-600 mt-1">
              <Target className="h-3 w-3 inline mr-1" />
              Completed
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">{stats.totalHypotheses}</div>
            <div className="text-sm text-green-800 font-medium">Hypotheses</div>
            <div className="text-xs text-green-600 mt-1">
              <Lightbulb className="h-3 w-3 inline mr-1" />
              Generated
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-indigo-50 to-indigo-100 border-indigo-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-indigo-600 mb-1">{stats.recentActivity.length}</div>
            <div className="text-sm text-indigo-800 font-medium">Recent Actions</div>
            <div className="text-xs text-indigo-600 mt-1">
              <Activity className="h-3 w-3 inline mr-1" />
              Last 10
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search documents, authors, or keywords..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="sessions" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Sessions
            <Badge variant="secondary" className="ml-1">{groupedSessions.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Documents
            <Badge variant="secondary" className="ml-1">{filteredDocuments.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="reviews" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Reviews
            <Badge variant="secondary" className="ml-1">{literatureReviews.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="gaps" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Gap Analyses
            <Badge variant="secondary" className="ml-1">{gapAnalyses.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="hypotheses" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Hypotheses
            <Badge variant="secondary" className="ml-1">{hypotheses.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Activity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="mt-6">
          <div className="grid gap-4">
            {groupedSessions.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No research sessions found</h3>
                  <p className="text-gray-500">Start uploading documents and generating analyses to create research sessions.</p>
                </CardContent>
              </Card>
            ) : (
              groupedSessions.map((session, index) => (
                <Card key={session.date} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Calendar className="h-5 w-5 text-blue-500" />
                          <h3 className="text-lg font-semibold text-gray-900">
                            Research Session - {new Date(session.date).toLocaleDateString()}
                          </h3>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span className="flex items-center gap-1">
                            <FileText className="h-4 w-4" />
                            {session.documents.length} documents
                          </span>
                          <span className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4" />
                            {session.reviews.length} reviews
                          </span>
                          <span className="flex items-center gap-1">
                            <Target className="h-4 w-4" />
                            {session.analyses.length} analyses
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleContinueSession(session)}
                          className="border-blue-300 text-blue-700 hover:bg-blue-50"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Continue Session
                        </Button>
                      </div>
                    </div>

                    {/* Session Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                      {session.documents.length > 0 && (
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <h4 className="font-medium text-blue-900 mb-2">Recent Documents</h4>
                          <div className="space-y-1">
                            {session.documents.slice(0, 2).map(doc => (
                              <div key={doc.id} className="text-sm text-blue-700 truncate">
                                {doc.title}
                              </div>
                            ))}
                            {session.documents.length > 2 && (
                              <div className="text-xs text-blue-600">
                                +{session.documents.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {session.reviews.length > 0 && (
                        <div className="bg-purple-50 p-3 rounded-lg">
                          <h4 className="font-medium text-purple-900 mb-2">Literature Reviews</h4>
                          <div className="space-y-1">
                            {session.reviews.slice(0, 2).map(review => (
                              <div key={review.id} className="text-sm text-purple-700 truncate">
                                {review.title}
                              </div>
                            ))}
                            {session.reviews.length > 2 && (
                              <div className="text-xs text-purple-600">
                                +{session.reviews.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {session.analyses.length > 0 && (
                        <div className="bg-orange-50 p-3 rounded-lg">
                          <h4 className="font-medium text-orange-900 mb-2">Gap Analyses</h4>
                          <div className="space-y-1">
                            {session.analyses.slice(0, 2).map(analysis => (
                              <div key={analysis.id} className="text-sm text-orange-700 truncate">
                                {analysis.summary?.substring(0, 50)}...
                              </div>
                            ))}
                            {session.analyses.length > 2 && (
                              <div className="text-xs text-orange-600">
                                +{session.analyses.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          <div className="grid gap-4">
            {filteredDocuments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
                  <p className="text-gray-500">Upload some research documents to get started with analysis.</p>
                </CardContent>
              </Card>
            ) : (
              filteredDocuments.map((document) => (
                <Card key={document.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                            {document.title}
                          </h3>
                          <Badge 
                            variant={document.status === 'ready' ? 'default' : 
                                   document.status === 'processing' ? 'secondary' : 'destructive'}
                          >
                            {document.status}
                          </Badge>
                          {document.favorite && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                        </div>
                        
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Authors: <AUTHORS>
                        </div>
                        
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="font-medium">Year:</span> {document.publicationYear} • 
                          <span className="font-medium ml-2">Keywords:</span> {document.keywords.slice(0, 3).join(', ')}
                          {document.keywords.length > 3 && '...'}
                        </div>
                        
                        <p className="text-sm text-gray-700 line-clamp-2 mb-3">
                          {document.abstract || document.summary || 'No abstract available'}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {document.uploadedAt.toLocaleDateString()}
                          </span>
                          <span>{(document.fileSize / 1024 / 1024).toFixed(2)} MB</span>
                          <span>{document.keyFindings.length} findings</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDocumentSelect?.(document)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteDocument(document.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Other tabs content would be similar but for different data types */}
        <TabsContent value="reviews" className="mt-6">
          <div className="grid gap-4">
            {literatureReviews.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No literature reviews found</h3>
                  <p className="text-gray-500">Generate literature reviews from your uploaded documents.</p>
                </CardContent>
              </Card>
            ) : (
              literatureReviews.map((review) => (
                <Card key={review.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{review.title}</h3>
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Documents:</span> {review.documents.length} • 
                          <span className="font-medium ml-2">Word Count:</span> {review.wordCount} • 
                          <span className="font-medium ml-2">Citation Style:</span> {review.citationStyle}
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="font-medium">Sections:</span> {review.sections.length} • 
                          <span className="font-medium ml-2">Status:</span> {review.status}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {review.generatedAt.toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAnalysisSelect?.(review)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAnalysis('literature_review', review.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="gaps" className="mt-6">
          <div className="grid gap-4">
            {gapAnalyses.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No gap analyses found</h3>
                  <p className="text-gray-500">Analyze your documents to identify research gaps and opportunities.</p>
                </CardContent>
              </Card>
            ) : (
              gapAnalyses.map((analysis) => (
                <Card key={analysis.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          Gap Analysis - {analysis.generatedAt.toLocaleDateString()}
                        </h3>
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Documents Analyzed:</span> {analysis.documentIds.length} •
                          <span className="font-medium ml-2">Gaps Found:</span> {analysis.gaps.length} •
                          <span className="font-medium ml-2">Themes:</span> {analysis.themes.length}
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="font-medium">Opportunities:</span> {analysis.opportunities.length}
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-2 mb-3">
                          {analysis.summary}
                        </p>
                        <div className="flex flex-wrap gap-1 mb-3">
                          {analysis.gaps.slice(0, 3).map((gap, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {gap.title}
                            </Badge>
                          ))}
                          {analysis.gaps.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{analysis.gaps.length - 3} more
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {analysis.generatedAt.toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAnalysisSelect?.(analysis)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAnalysis('gap_analysis', analysis.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="hypotheses" className="mt-6">
          <div className="grid gap-4">
            {hypotheses.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Lightbulb className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No hypotheses found</h3>
                  <p className="text-gray-500">Generate testable hypotheses from your gap analyses.</p>
                </CardContent>
              </Card>
            ) : (
              hypotheses.map((hypothesis) => (
                <Card key={hypothesis.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant={hypothesis.type === 'directional' ? 'default' : 'secondary'}>
                            {hypothesis.type}
                          </Badge>
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <span>Testability: {hypothesis.testability}/10</span>
                            <span>•</span>
                            <span>Novelty: {hypothesis.novelty}/10</span>
                            <span>•</span>
                            <span>Significance: {hypothesis.significance}/10</span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-900 font-medium mb-3 line-clamp-2">
                          {hypothesis.statement}
                        </p>
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="font-medium">Methodology:</span> {hypothesis.suggestedMethodology.slice(0, 2).join(', ')}
                          {hypothesis.suggestedMethodology.length > 2 && '...'}
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="font-medium">Timeline:</span> {hypothesis.timeline}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Variables: {Object.keys(hypothesis.variables).length}</span>
                          <span>Resources: {hypothesis.requiredResources.length}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAnalysisSelect?.(hypothesis)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAnalysis('hypothesis', hypothesis.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="activity" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                {activity.length === 0 ? (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No activity found</h3>
                    <p className="text-gray-500">Your research activity will appear here.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activity.map((item, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                        <div className={cn(
                          "w-2 h-2 rounded-full mt-2 flex-shrink-0",
                          item.activity_type === 'upload' ? 'bg-green-500' :
                          item.activity_type === 'analysis' ? 'bg-blue-500' :
                          item.activity_type === 'generation' ? 'bg-purple-500' :
                          item.activity_type === 'delete' ? 'bg-red-500' : 'bg-gray-500'
                        )} />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{item.description}</p>
                          <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                            <span className="capitalize">{item.activity_type}</span>
                            <span>•</span>
                            <span className="capitalize">{item.entity_type}</span>
                            <span>•</span>
                            <span>{new Date(item.created_at).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
