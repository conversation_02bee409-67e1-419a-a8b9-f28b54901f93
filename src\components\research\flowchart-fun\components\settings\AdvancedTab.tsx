/**
 * Advanced Settings Tab
 * Configure advanced features and experimental options
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Zap, Code, Palette, Settings, AlertTriangle, Info } from 'lucide-react';
import { SettingsTabProps } from '../../types';

const AdvancedTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const [customCSS, setCustomCSS] = useState('');
  const [enableExperimentalFeatures, setEnableExperimentalFeatures] = useState(false);
  const [enableDebugMode, setEnableDebugMode] = useState(false);
  const [enablePerformanceMode, setEnablePerformanceMode] = useState(false);

  const handleResetToDefaults = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      // Reset to default theme
      onThemeChange({
        fontFamily: "Helvetica Neue, Arial, sans-serif",
        background: "#ffffff",
        lineHeight: 1.2,
        layoutName: "dagre",
        direction: "DOWN",
        spacingFactor: 1.25,
        shape: "roundrectangle",
        nodeBackground: "#ffffff",
        nodeForeground: "#000000",
        padding: 8,
        borderWidth: 2,
        borderColor: "#000000",
        textMaxWidth: 144,
        textMarginY: 0,
        useFixedHeight: false,
        fixedHeight: 40,
        curveStyle: "bezier",
        edgeWidth: 2,
        edgeColor: "#000000",
        sourceArrowShape: "none",
        targetArrowShape: "triangle",
        sourceDistanceFromNode: 5,
        targetDistanceFromNode: 5,
        arrowScale: 1,
        edgeTextSize: 12,
        rotateEdgeLabel: false,
      });
      
      // Reset advanced settings
      setCustomCSS('');
      setEnableExperimentalFeatures(false);
      setEnableDebugMode(false);
      setEnablePerformanceMode(false);
    }
  };

  const handleExportConfig = () => {
    const config = {
      theme,
      advanced: {
        customCSS,
        enableExperimentalFeatures,
        enableDebugMode,
        enablePerformanceMode,
      },
      exportedAt: new Date().toISOString(),
    };
    
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `flowchart-config-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const sampleCSS = `/* Custom CSS for flowchart elements */
.flowchart-node {
  transition: all 0.3s ease;
}

.flowchart-node:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.flowchart-edge {
  opacity: 0.8;
}

.flowchart-edge:hover {
  opacity: 1;
  stroke-width: 3px;
}`;

  return (
    <div className="space-y-6">
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Advanced settings can significantly impact performance and appearance. 
          Use with caution and test thoroughly.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Code className="h-5 w-5" />
            Custom CSS
          </CardTitle>
          <CardDescription>
            Add custom CSS to override default styles
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label htmlFor="custom-css">Custom CSS Rules</Label>
            <Textarea
              id="custom-css"
              value={customCSS}
              onChange={(e) => setCustomCSS(e.target.value)}
              placeholder={sampleCSS}
              rows={12}
              className="font-mono text-sm"
            />
            <p className="text-sm text-muted-foreground">
              CSS will be applied to flowchart elements. Use classes like 
              <code className="mx-1 px-1 bg-muted rounded">.flowchart-node</code> and 
              <code className="mx-1 px-1 bg-muted rounded">.flowchart-edge</code>
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setCustomCSS(sampleCSS)}
            >
              Load Sample CSS
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setCustomCSS('')}
            >
              Clear CSS
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Experimental Features
          </CardTitle>
          <CardDescription>
            Enable experimental features that may be unstable
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="experimental-features">Enable Experimental Features</Label>
                <p className="text-sm text-muted-foreground">
                  Unlock beta features and advanced functionality
                </p>
              </div>
              <Switch
                id="experimental-features"
                checked={enableExperimentalFeatures}
                onCheckedChange={setEnableExperimentalFeatures}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="debug-mode">Debug Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Show debug information and performance metrics
                </p>
              </div>
              <Switch
                id="debug-mode"
                checked={enableDebugMode}
                onCheckedChange={setEnableDebugMode}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="performance-mode">Performance Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Optimize for large flowcharts (may reduce visual quality)
                </p>
              </div>
              <Switch
                id="performance-mode"
                checked={enablePerformanceMode}
                onCheckedChange={setEnablePerformanceMode}
              />
            </div>
          </div>

          {enableExperimentalFeatures && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Experimental features enabled. You may experience instability or unexpected behavior.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration Management
          </CardTitle>
          <CardDescription>
            Manage your complete flowchart configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Current Configuration</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>Theme Settings:</span>
                  <Badge variant="outline">Configured</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Custom CSS:</span>
                  <Badge variant={customCSS ? "default" : "outline"}>
                    {customCSS ? 'Active' : 'None'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Experimental:</span>
                  <Badge variant={enableExperimentalFeatures ? "default" : "outline"}>
                    {enableExperimentalFeatures ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Actions</h4>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleExportConfig}
                  className="w-full justify-start"
                >
                  Export Complete Configuration
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleResetToDefaults}
                  className="w-full justify-start text-destructive hover:text-destructive"
                >
                  Reset All to Defaults
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Performance Information</CardTitle>
          <CardDescription>
            Tips for optimizing flowchart performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <Badge variant="outline" className="mt-0.5">Tip</Badge>
              <p>Use simpler layouts (like 'dagre') for large flowcharts with 100+ nodes</p>
            </div>
            <div className="flex items-start gap-2">
              <Badge variant="outline" className="mt-0.5">Tip</Badge>
              <p>Reduce border width and padding for better performance with many nodes</p>
            </div>
            <div className="flex items-start gap-2">
              <Badge variant="outline" className="mt-0.5">Tip</Badge>
              <p>Enable Performance Mode when working with flowcharts over 200 nodes</p>
            </div>
            <div className="flex items-start gap-2">
              <Badge variant="outline" className="mt-0.5">Tip</Badge>
              <p>Custom CSS animations may impact performance on slower devices</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedTab;
