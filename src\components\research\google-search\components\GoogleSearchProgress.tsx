/**
 * Google Search Progress Component
 * Shows progress during Google search operations
 */

import React from 'react';
import { Search, Brain, FileText, Quote, CheckCircle } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';

import { GoogleSearchProgress as GoogleSearchProgressType } from '../types';

interface GoogleSearchProgressProps {
  progress: GoogleSearchProgressType;
  className?: string;
}

export function GoogleSearchProgress({ progress, className = '' }: GoogleSearchProgressProps) {
  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'searching':
        return <Search className="h-4 w-4 text-blue-600 animate-pulse" />;
      case 'analyzing':
        return <Brain className="h-4 w-4 text-purple-600 animate-pulse" />;
      case 'formatting':
        return <FileText className="h-4 w-4 text-green-600 animate-pulse" />;
      case 'generating_citations':
        return <Quote className="h-4 w-4 text-orange-600 animate-pulse" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Search className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'searching':
        return 'text-blue-600';
      case 'analyzing':
        return 'text-purple-600';
      case 'formatting':
        return 'text-green-600';
      case 'generating_citations':
        return 'text-orange-600';
      case 'complete':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const getProgressColor = (stage: string) => {
    switch (stage) {
      case 'searching':
        return 'bg-blue-600';
      case 'analyzing':
        return 'bg-purple-600';
      case 'formatting':
        return 'bg-green-600';
      case 'generating_citations':
        return 'bg-orange-600';
      case 'complete':
        return 'bg-green-600';
      default:
        return 'bg-gray-600';
    }
  };

  return (
    <Card className={`border-l-4 border-l-blue-500 ${className}`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Stage and Message */}
          <div className="flex items-center space-x-3">
            {getStageIcon(progress.stage)}
            <div className="flex-1">
              <div className={`font-medium ${getStageColor(progress.stage)}`}>
                {progress.message}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {progress.progress}% complete
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress 
              value={progress.progress} 
              className="h-2"
              style={{
                '--progress-background': getProgressColor(progress.stage)
              } as React.CSSProperties}
            />
            
            {/* Stage Indicators */}
            <div className="flex justify-between text-xs text-gray-500">
              <div className={`flex items-center space-x-1 ${
                ['searching', 'analyzing', 'formatting', 'generating_citations', 'complete'].includes(progress.stage) 
                  ? 'text-blue-600 font-medium' 
                  : ''
              }`}>
                <Search className="h-3 w-3" />
                <span>Search</span>
              </div>
              <div className={`flex items-center space-x-1 ${
                ['analyzing', 'formatting', 'generating_citations', 'complete'].includes(progress.stage) 
                  ? 'text-purple-600 font-medium' 
                  : ''
              }`}>
                <Brain className="h-3 w-3" />
                <span>Analyze</span>
              </div>
              <div className={`flex items-center space-x-1 ${
                ['formatting', 'generating_citations', 'complete'].includes(progress.stage) 
                  ? 'text-green-600 font-medium' 
                  : ''
              }`}>
                <FileText className="h-3 w-3" />
                <span>Format</span>
              </div>
              <div className={`flex items-center space-x-1 ${
                ['generating_citations', 'complete'].includes(progress.stage) 
                  ? 'text-orange-600 font-medium' 
                  : ''
              }`}>
                <Quote className="h-3 w-3" />
                <span>Citations</span>
              </div>
              <div className={`flex items-center space-x-1 ${
                progress.stage === 'complete' 
                  ? 'text-green-600 font-medium' 
                  : ''
              }`}>
                <CheckCircle className="h-3 w-3" />
                <span>Complete</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default GoogleSearchProgress;
