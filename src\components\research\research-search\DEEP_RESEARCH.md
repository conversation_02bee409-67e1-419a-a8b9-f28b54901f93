# Deep Research Feature

## Overview

The Deep Research feature provides comprehensive, step-by-step academic research capabilities using **10 specialized AI assistants** working sequentially on **10 structured research points**. Unlike regular search which provides quick answers, Deep Research conducts thorough investigations following a structured methodology with dedicated AI experts.

## How It Works

### 1. **Pure AI Outline Generation (NO SEARCH)**
When a user submits a deep research query, the system:
- Uses AI knowledge (no online search) to create a logical outline
- Generates exactly **10 main research points** with 2-4 subpoints each
- Creates a structured, point-by-point research plan
- Presents the outline in simple point/subpoint format for user approval

### 2. **Sequential AI Assistant Execution**
After outline approval, the system:
- Assigns **10 specialized AI assistants** to the 10 research points
- Each assistant works on their assigned point sequentially (one at a time)
- **First searches online** using Tavi<PERSON> for their specific point and subpoints
- **Then analyzes and writes** comprehensive content based on search results
- Stores each completed point before moving to the next
- Tracks progress with real-time updates from each AI assistant

### 3. **Progressive Content Building**
The system builds the research progressively:
- **Point 1**: AI Assistant researches → writes → stores content
- **Point 2**: Next AI Assistant researches → writes → stores content
- **Point 3-10**: Continue sequentially until all points completed
- **Final Report**: Synthesizes all 10 points into comprehensive report

## Features

### 🤖 **10 Specialized AI Assistants**
- **Dr. Research**: Research Methodology & Foundation
- **Prof. Analysis**: Data Analysis & Interpretation
- **Dr. Scholar**: Academic Literature Review
- **Prof. Synthesis**: Information Synthesis
- **Dr. Expert**: Subject Matter Expertise
- **Prof. Critic**: Critical Analysis & Evaluation
- **Dr. Innovation**: Innovation & Future Trends
- **Prof. Context**: Historical & Contextual Analysis
- **Dr. Evidence**: Evidence-Based Research
- **Prof. Conclusion**: Synthesis & Conclusions

### 🔍 **Pure AI Outline Generation**
- No online search during outline creation
- AI uses internal knowledge to structure research
- Exactly 10 points with logical progression
- Simple point/subpoint format for easy review

### 📊 **Sequential Research Execution**
- One AI assistant works at a time (no parallel processing)
- Each assistant searches online for their specific point
- Progressive content building and storage
- Real-time progress tracking per assistant

### 📚 **Academic Quality**
- Proper citation formatting (APA, MLA, Chicago, Harvard)
- Academic source prioritization via Tavily search
- Peer-reviewed content focus
- Professional writing standards per AI specialization

## Usage

### Starting Deep Research

1. **Enter Research Query**: Type your research question in the search input
2. **Click "Deep Research"**: Select the purple "Deep Research" button instead of regular search
3. **Review Outline**: Examine the generated research structure
4. **Approve or Edit**: Modify sections if needed, then approve to start
5. **Monitor Progress**: Watch as each section is researched and written
6. **Access Final Report**: Download or copy the completed research

### Example Queries

**Good Deep Research Queries:**
- "Detailed analysis of global warming problems and achievements in climate action"
- "Comprehensive review of machine learning applications in healthcare with limitations"
- "In-depth study of renewable energy adoption challenges and breakthrough technologies"

**Query Characteristics:**
- Specific and focused topics
- Request for comprehensive analysis
- Academic or research-oriented
- Suitable for detailed investigation

## Components

### Core Services

- **`DeepResearchService`**: Main orchestration service
- **`TavilySearchService`**: Academic search integration
- **`ResearchAIService`**: AI content generation

### UI Components

- **`OutlineApproval`**: Review and edit research outline
- **`DeepResearchProgress`**: Track execution progress
- **`DeepResearchMessage`**: Display research results

### Data Types

- **`DeepResearchSession`**: Complete research session state
- **`DeepResearchOutline`**: Research structure and planning
- **`CompletedSection`**: Individual section results

## Configuration

### Research Options

```typescript
interface DeepResearchOptions {
  model: string;                    // AI model for content generation
  maxResultsPerSection: number;     // Search results per section
  searchDepth: 'basic' | 'advanced' | 'comprehensive';
  academicFocus: boolean;           // Prioritize academic sources
  minWordCountPerSection: number;   // Minimum section length
  maxWordCountPerSection: number;   // Maximum section length
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  includeAbstract: boolean;         // Add executive summary
  includeConclusion: boolean;       // Add conclusions section
}
```

### Default Settings

- **Model**: `google/gemini-2.0-flash-001`
- **Search Depth**: `comprehensive`
- **Word Count**: 800-1500 words per section
- **Citation Style**: `apa`
- **Academic Focus**: `true`

## API Integration

### Required Environment Variables

```env
VITE_OPENROUTER_API_KEY=your_openrouter_key
VITE_TAVILY_API_KEY=your_tavily_key
```

### Service Dependencies

- **OpenRouter API**: AI content generation
- **Tavily API**: Academic search and retrieval
- **Supabase**: Session and history storage

## Testing

### Manual Testing

1. Open browser console
2. Run: `testDeepResearch.testService()`
3. Check outline generation
4. Run: `testDeepResearch.testFull()`
5. Verify complete workflow

### Test Queries

- "Climate change impact on biodiversity"
- "Artificial intelligence ethics and governance"
- "Quantum computing applications and limitations"

## Performance Considerations

### Execution Time
- **Outline Generation**: 10-30 seconds
- **Section Research**: 2-5 minutes per section
- **Total Time**: 15-45 minutes depending on complexity

### Resource Usage
- **API Calls**: 20-50 per research session
- **Token Usage**: 15,000-40,000 tokens
- **Storage**: 50-200KB per completed research

## Limitations

1. **Time Intensive**: Deep research takes significantly longer than regular search
2. **API Costs**: Higher token usage due to comprehensive analysis
3. **Language Support**: Currently optimized for English content
4. **Source Availability**: Dependent on Tavily's academic database coverage

## Future Enhancements

- **Collaborative Editing**: Multi-user outline editing
- **Export Formats**: PDF, DOCX, LaTeX export options
- **Citation Management**: Integration with reference managers
- **Template Library**: Pre-built research templates
- **Quality Metrics**: Research quality scoring and validation

## Troubleshooting

### Common Issues

**Outline Generation Fails**
- Check API keys are configured
- Verify internet connection
- Try simpler research query

**Research Execution Stops**
- Monitor API rate limits
- Check for network interruptions
- Review error messages in console

**Poor Quality Results**
- Refine research query specificity
- Adjust academic focus settings
- Increase word count limits

### Support

For technical issues or feature requests, please check:
1. Console error messages
2. Network connectivity
3. API key validity
4. Service status indicators
