/**
 * Article Revision Uploader Component
 * Handles upload of original article and reviewer comments
 */

import React, { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  FileText,
  AlertCircle,
  X,
  Check,
  File,
  Users,
  Clock,
  FileCheck,
  Type,
  Plus
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';

import { ParsedArticle, ParsedReviewerComments } from '../types';
import { FILE_CONSTRAINTS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';
import { DocumentParserService } from '../services/document-parser.service';
import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';
import { EnhancedCommentsUploader } from './EnhancedCommentsUploader';

interface ArticleRevisionUploaderProps {
  onStartRevision: () => void;
  isProcessing: boolean;
}

export function ArticleRevisionUploader({
  onStartRevision,
  isProcessing
}: ArticleRevisionUploaderProps) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingFile, setProcessingFile] = useState<string | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [articleTab, setArticleTab] = useState<'file' | 'text'>('file');
  const [articleText, setArticleText] = useState('');
  const [processingText, setProcessingText] = useState(false);
  
  const {
    originalArticle,
    reviewerComments,
    setOriginalArticle,
    canStartRevision,
    addError,
    clearErrors
  } = useRevisionWorkflowStore();

  // Article upload
  const onDropArticle = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    clearErrors();
    setErrors([]);

    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        const errorMsg = error.code === 'file-too-large'
          ? ERROR_MESSAGES.FILE_TOO_LARGE
          : error.code === 'file-invalid-type'
          ? ERROR_MESSAGES.UNSUPPORTED_FORMAT
          : error.message;
        setErrors(prev => [...prev, `${file.name}: ${errorMsg}`]);
      });
    });

    // Process accepted files (should be only one for article)
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      await processArticleFile(file);
    }
  }, [clearErrors]);

  const processArticleFile = async (file: File) => {
    setProcessingFile(file.name);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Parse the article
      const parsedArticle = await DocumentParserService.parseArticle(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // Validate word count
      if (parsedArticle.wordCount > FILE_CONSTRAINTS.MAX_WORD_COUNT) {
        throw new Error(`Article too long (${parsedArticle.wordCount} words). Maximum ${FILE_CONSTRAINTS.MAX_WORD_COUNT} words allowed.`);
      }

      setOriginalArticle(parsedArticle);
      toast.success(SUCCESS_MESSAGES.DOCUMENT_PARSED);
      
    } catch (error) {
      console.error('Error processing article:', error);
      const errorMsg = error instanceof Error ? error.message : ERROR_MESSAGES.PARSING_FAILED;
      setErrors(prev => [...prev, errorMsg]);
      addError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setProcessingFile(null);
      setUploadProgress(0);
    }
  };

  // Handle text input for article
  const handleArticleTextSubmit = async () => {
    if (!articleText.trim()) {
      toast.error('Please enter article text');
      return;
    }

    if (articleText.trim().length < 100) {
      toast.error('Article text is too short. Please provide more content.');
      return;
    }

    setProcessingText(true);
    setErrors([]);

    try {
      // Create a mock parsed article from text input
      const wordCount = articleText.trim().split(/\s+/).length;

      if (wordCount > FILE_CONSTRAINTS.MAX_WORD_COUNT) {
        throw new Error(`Article too long (${wordCount} words). Maximum ${FILE_CONSTRAINTS.MAX_WORD_COUNT} words allowed.`);
      }

      // Create basic section mapping from text
      const sectionMapping = {
        title: 'Article from Text Input',
        abstract: '',
        keywords: '',
        introduction: '',
        methodology: '',
        results: '',
        discussion: '',
        conclusion: '',
        references: '',
        overall: articleText
      };

      const parsedArticle: ParsedArticle = {
        title: 'Article from Text Input',
        abstract: '',
        keywords: [],
        introduction: '',
        methodology: '',
        results: '',
        discussion: '',
        conclusion: '',
        references: '',
        fullText: articleText,
        sectionMapping,
        fileName: 'Article (Text Input)',
        fileType: 'text/plain',
        fileSize: articleText.length,
        wordCount
      };

      setOriginalArticle(parsedArticle);
      toast.success(`Article processed successfully (${wordCount.toLocaleString()} words)`);

    } catch (error) {
      console.error('Error processing article text:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to process article text';
      setErrors(prev => [...prev, errorMsg]);
      addError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setProcessingText(false);
    }
  };



  // Dropzone configuration for article upload
  const { getRootProps: getArticleRootProps, getInputProps: getArticleInputProps, isDragActive: isArticleDragActive } = useDropzone({
    onDrop: onDropArticle,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc']
    },
    maxSize: FILE_CONSTRAINTS.MAX_FILE_SIZE,
    multiple: false,
    disabled: isProcessing
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Upload Errors</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Article Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Original Article
          </CardTitle>
          <CardDescription>
            Upload your original research article or paste the text directly
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!originalArticle ? (
            <Tabs value={articleTab} onValueChange={(value) => setArticleTab(value as 'file' | 'text')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="file" className="flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  Upload File
                </TabsTrigger>
                <TabsTrigger value="text" className="flex items-center gap-2">
                  <Type className="h-4 w-4" />
                  Paste Text
                </TabsTrigger>
              </TabsList>

              <TabsContent value="file" className="mt-4">
                <div
                  {...getArticleRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isArticleDragActive
                      ? 'border-blue-400 bg-blue-50'
                      : 'border-gray-300 hover:border-gray-400'
                  } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <input {...getArticleInputProps()} />
                  <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium mb-2">
                    {isArticleDragActive ? 'Drop your article here' : 'Upload your article'}
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Drag and drop your file here, or click to browse
                  </p>
                  <p className="text-xs text-gray-400">
                    Supported formats: PDF, DOC, DOCX (max {formatFileSize(FILE_CONSTRAINTS.MAX_FILE_SIZE)})
                  </p>
                  <p className="text-xs text-blue-600 mt-2">
                    💡 Tip: For best results, ensure your article has clear section headers (Introduction, Methodology, Results, Discussion, Conclusion)
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="text" className="mt-4">
                <div className="space-y-4">
                  <Textarea
                    placeholder="Paste your article text here... Include the title, abstract, sections, and content."
                    value={articleText}
                    onChange={(e) => setArticleText(e.target.value)}
                    className="min-h-[300px] resize-y"
                    disabled={isProcessing || processingText}
                  />
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500">
                      {articleText.trim() ? `${articleText.trim().split(/\s+/).length.toLocaleString()} words` : 'No text entered'}
                    </p>
                    <Button
                      onClick={handleArticleTextSubmit}
                      disabled={!articleText.trim() || isProcessing || processingText}
                      className="flex items-center gap-2"
                    >
                      {processingText ? (
                        <>
                          <Clock className="h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Process Article
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-3">
                <FileCheck className="h-8 w-8 text-green-600" />
                <div>
                  <p className="font-medium text-green-800">{originalArticle.fileName}</p>
                  <p className="text-sm text-green-600">
                    {formatFileSize(originalArticle.fileSize)} • {originalArticle.wordCount.toLocaleString()} words
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setOriginalArticle(null)}
                disabled={isProcessing}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}

          {processingFile && (
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Processing {processingFile}...</span>
                <span className="text-sm text-gray-500">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Reviewer Comments Upload */}
      <EnhancedCommentsUploader isProcessing={isProcessing} />

      {/* Start Revision Button */}
      <div className="flex justify-center">
        <Button
          onClick={onStartRevision}
          disabled={!canStartRevision() || isProcessing}
          size="lg"
          className="px-8"
        >
          {isProcessing ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <FileCheck className="h-4 w-4 mr-2" />
              Start Article Revision
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
