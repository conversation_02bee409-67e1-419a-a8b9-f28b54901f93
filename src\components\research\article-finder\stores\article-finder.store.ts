import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  ArticleFinderState, 
  ArticleContent, 
  ArticleAnalysis, 
  JournalRecommendation,
  JournalRanking,
  RankingCriteria,
  InputType,
  AnalysisDepth
} from '../types';
import { DEFAULT_SEARCH_OPTIONS, DEFAULT_RANKING_CRITERIA } from '../constants';

interface ArticleFinderActions {
  // Content management
  setArticleContent: (content: ArticleContent) => void;
  setSelectedInputType: (type: InputType) => void;
  setSelectedModel: (model: string) => void;
  setAnalysisDepth: (depth: AnalysisDepth) => void;
  
  // Step navigation
  setCurrentStep: (step: ArticleFinderState['currentStep']) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // Analysis management
  setArticleAnalysis: (analysis: ArticleAnalysis) => void;
  setIsAnalyzing: (analyzing: boolean) => void;
  setSearchProgress: (progress: number) => void;
  
  // Journal recommendations
  addJournalRecommendation: (journal: JournalRecommendation) => void;
  removeJournalRecommendation: (journalId: string) => void;
  updateJournalRecommendation: (journalId: string, updates: Partial<JournalRecommendation>) => void;
  setJournalRecommendations: (journals: JournalRecommendation[]) => void;
  
  // Ranking system
  setRankingCriteria: (criteria: RankingCriteria) => void;
  updateRankingWeight: (criterion: keyof RankingCriteria, weight: number) => void;
  calculateJournalRankings: () => JournalRanking[];
  
  // Error handling
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Utility actions
  resetState: () => void;
  resetAnalysis: () => void;
}

const initialState: ArticleFinderState = {
  currentStep: 'input',
  articleContent: null,
  selectedInputType: 'abstract',
  selectedModel: DEFAULT_SEARCH_OPTIONS.model,
  analysisDepth: DEFAULT_SEARCH_OPTIONS.analysisDepth,
  articleAnalysis: null,
  isAnalyzing: false,
  error: null,
  searchProgress: 0,
  rankingCriteria: DEFAULT_RANKING_CRITERIA
};

export const useArticleFinderStore = create<ArticleFinderState & ArticleFinderActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Content management
      setArticleContent: (content: ArticleContent) => {
        set({ articleContent: content, error: null });
      },

      setSelectedInputType: (type: InputType) => {
        set({ selectedInputType: type });
      },

      setSelectedModel: (model: string) => {
        set({ selectedModel: model });
      },

      setAnalysisDepth: (depth: AnalysisDepth) => {
        set({ analysisDepth: depth });
      },

      // Step navigation
      setCurrentStep: (step: ArticleFinderState['currentStep']) => {
        set({ currentStep: step });
      },

      nextStep: () => {
        const { currentStep } = get();
        const steps: ArticleFinderState['currentStep'][] = ['input', 'analyzing', 'results', 'export'];
        const currentIndex = steps.indexOf(currentStep);
        
        if (currentIndex < steps.length - 1) {
          set({ currentStep: steps[currentIndex + 1] });
        }
      },

      previousStep: () => {
        const { currentStep } = get();
        const steps: ArticleFinderState['currentStep'][] = ['input', 'analyzing', 'results', 'export'];
        const currentIndex = steps.indexOf(currentStep);
        
        if (currentIndex > 0) {
          set({ currentStep: steps[currentIndex - 1] });
        }
      },

      // Analysis management
      setArticleAnalysis: (analysis: ArticleAnalysis) => {
        set({ 
          articleAnalysis: analysis,
          currentStep: 'results',
          isAnalyzing: false,
          searchProgress: 100,
          error: null
        });
      },

      setIsAnalyzing: (analyzing: boolean) => {
        set({ 
          isAnalyzing: analyzing,
          currentStep: analyzing ? 'analyzing' : get().currentStep,
          searchProgress: analyzing ? 0 : get().searchProgress
        });
      },

      setSearchProgress: (progress: number) => {
        set({ searchProgress: Math.max(0, Math.min(100, progress)) });
      },

      // Journal recommendations
      addJournalRecommendation: (journal: JournalRecommendation) => {
        const { articleAnalysis } = get();
        if (articleAnalysis) {
          const updatedRecommendations = [...articleAnalysis.recommendedJournals, journal];
          set({
            articleAnalysis: {
              ...articleAnalysis,
              recommendedJournals: updatedRecommendations
            }
          });
        }
      },

      removeJournalRecommendation: (journalId: string) => {
        const { articleAnalysis } = get();
        if (articleAnalysis) {
          const updatedRecommendations = articleAnalysis.recommendedJournals.filter(
            journal => journal.id !== journalId
          );
          set({
            articleAnalysis: {
              ...articleAnalysis,
              recommendedJournals: updatedRecommendations
            }
          });
        }
      },

      updateJournalRecommendation: (journalId: string, updates: Partial<JournalRecommendation>) => {
        const { articleAnalysis } = get();
        if (articleAnalysis) {
          const updatedRecommendations = articleAnalysis.recommendedJournals.map(journal =>
            journal.id === journalId ? { ...journal, ...updates } : journal
          );
          set({
            articleAnalysis: {
              ...articleAnalysis,
              recommendedJournals: updatedRecommendations
            }
          });
        }
      },

      setJournalRecommendations: (journals: JournalRecommendation[]) => {
        const { articleAnalysis } = get();
        if (articleAnalysis) {
          set({
            articleAnalysis: {
              ...articleAnalysis,
              recommendedJournals: journals
            }
          });
        }
      },

      // Ranking system
      setRankingCriteria: (criteria: RankingCriteria) => {
        set({ rankingCriteria: criteria });
      },

      updateRankingWeight: (criterion: keyof RankingCriteria, weight: number) => {
        const { rankingCriteria } = get();
        const updatedCriteria = {
          ...rankingCriteria,
          [criterion]: Math.max(0, Math.min(1, weight))
        };
        
        // Normalize weights to sum to 1
        const totalWeight = Object.values(updatedCriteria).reduce((sum, w) => sum + w, 0);
        if (totalWeight > 0) {
          Object.keys(updatedCriteria).forEach(key => {
            updatedCriteria[key as keyof RankingCriteria] /= totalWeight;
          });
        }
        
        set({ rankingCriteria: updatedCriteria });
      },

      calculateJournalRankings: (): JournalRanking[] => {
        const { articleAnalysis, rankingCriteria } = get();
        if (!articleAnalysis) return [];

        return articleAnalysis.recommendedJournals.map(journal => {
          // Calculate individual scores (0-1 scale)
          const relevanceScore = journal.matchScore;
          const impactScore = Math.min(journal.metrics.impactFactor / 10, 1); // Normalize to 0-1
          const speedScore = Math.max(0, 1 - (journal.metrics.averageReviewTime / 365)); // Faster = higher score
          const costScore = journal.metrics.publicationFees === 0 ? 1 : Math.max(0, 1 - (journal.metrics.publicationFees / 5000)); // Lower cost = higher score
          const accessibilityScore = journal.metrics.isOpenAccess ? 1 : 0.5;

          // Calculate weighted score
          const weightedScore = 
            (relevanceScore * rankingCriteria.relevanceWeight) +
            (impactScore * rankingCriteria.impactWeight) +
            (speedScore * rankingCriteria.speedWeight) +
            (costScore * rankingCriteria.costWeight) +
            (accessibilityScore * rankingCriteria.accessibilityWeight);

          return {
            criteria: rankingCriteria,
            weightedScore,
            breakdown: {
              relevance: relevanceScore,
              impact: impactScore,
              speed: speedScore,
              cost: costScore,
              accessibility: accessibilityScore
            }
          };
        });
      },

      // Error handling
      setError: (error: string | null) => {
        set({ error, isAnalyzing: false });
      },

      clearError: () => {
        set({ error: null });
      },

      // Utility actions
      resetState: () => {
        set(initialState);
      },

      resetAnalysis: () => {
        set({
          articleAnalysis: null,
          isAnalyzing: false,
          searchProgress: 0,
          currentStep: 'input',
          error: null
        });
      }
    }),
    {
      name: 'article-finder-store',
      partialize: (state) => ({
        selectedModel: state.selectedModel,
        analysisDepth: state.analysisDepth,
        rankingCriteria: state.rankingCriteria
      })
    }
  )
);
