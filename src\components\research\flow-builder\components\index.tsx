/**
 * Flow Builder Components Index
 * Export all components for easy importing
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, RefreshCw } from "lucide-react";

// Main components
export { DiagramInputForm } from './DiagramInputForm';
export { DiagramRenderer } from './DiagramRenderer';
export { DiagramEditor } from './DiagramEditor';
export { DiagramExportDialog } from './DiagramExportDialog';
export { DiagramHistoryPanel } from './DiagramHistoryPanel';
export { FlowBuilderHeader } from './FlowBuilderHeader';
export { AIModelSelector } from './AIModelSelector';

// Loading states and UI components
export {
  LoadingSpinner,
  DiagramGenerationLoading,
  ErrorState,
  SuccessState,
  PulsingDots
} from './LoadingStates';

// Error boundary component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
}

export class FlowBuilderErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('FlowBuilder Error Boundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Card className="max-w-2xl mx-auto mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Flow Builder Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-3">
                  <p className="font-medium">Something went wrong with the Flow Builder component.</p>
                  <div className="text-sm text-red-600 bg-red-50 p-3 rounded border">
                    {this.state.error?.message || 'Unknown error occurred'}
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={this.handleReset} variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Again
                    </Button>
                    <Button
                      onClick={() => window.location.reload()}
                      variant="outline"
                      size="sm"
                    >
                      Reload Page
                    </Button>
                  </div>
                  {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                        Show error details (development only)
                      </summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto whitespace-pre-wrap">
                        {this.state.error?.stack}
                        {'\n\nComponent Stack:'}
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}
