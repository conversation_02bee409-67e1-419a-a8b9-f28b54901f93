import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Download, FileType, Printer, Edit, FileText, Eye, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { GeneratedSection, PaperMetadata, Citation } from './types';
import { documentExportService } from './document-export.service';

interface ExportDialogProps {
  metadata: PaperMetadata;
  generatedSections: GeneratedSection[];
  allCitations?: Citation[];
  onEditInEditor: () => void;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  metadata,
  generatedSections,
  allCitations = [],
  onEditInEditor
}) => {
  const [exportError, setExportError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Helper function to generate complete content for export with proper reference processing
  const generateCompleteContent = () => {
    const authorsText = (metadata.authors && metadata.authors.length > 0)
      ? metadata.authors.join(', ')
      : 'Anonymous';

    // Get all completed sections except references (we'll handle references separately)
    const completedSections = generatedSections
      .filter(section => section.status === 'completed' && section.content && section.id !== 'references')
      .sort((a, b) => {
        const sectionOrder: Record<string, number> = {
          'abstract': 1,
          'introduction': 2,
          'literature-review': 3,
          'methodology': 4,
          'results': 5,
          'discussion': 6,
          'conclusion': 7
        };
        return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
      });

    // Generate references using the same logic as the editor
    const generateReferencesFromCitations = () => {
      console.log('Export: Generating references from citations', allCitations.length);

      // First check if we have any citations
      if (allCitations.length === 0) {
        // Fallback to existing references section if available
        const referencesSection = generatedSections.find(s => s.id === 'references');
        if (referencesSection?.content) {
          return referencesSection.content;
        }
        return "No citations were found in this paper.";
      }

      // Collect all references from citations that have reference text
      const citationsWithReferences = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '');

      if (citationsWithReferences.length === 0) {
        // Fallback to existing references section
        const referencesSection = generatedSections.find(s => s.id === 'references');
        if (referencesSection?.content) {
          return referencesSection.content;
        }
        return "No references could be matched with citations.";
      }

      // Sort alphabetically by first author's last name
      const sortedCitations = [...citationsWithReferences].sort((a, b) => {
        const getLastName = (citation: Citation): string => {
          if (citation.authors.length === 0) return '';
          const firstAuthor = citation.authors[0];
          return firstAuthor.split(' ')[0].replace(',', '').toLowerCase();
        };
        return getLastName(a).localeCompare(getLastName(b));
      });

      // Generate statistics
      const totalCitations = allCitations.length;
      const uniqueAuthorsCount = new Set(allCitations.flatMap(c => c.authors.map(a => a.toLowerCase()))).size;

      // Format the references section in proper academic style
      const referencesText = sortedCitations.map(c => `<p>${c.referenceText}</p>`).join('\n') +
        `\n\n<p><em>Citation Statistics: ${totalCitations} total citations | ${citationsWithReferences.length} with references | ${uniqueAuthorsCount} unique authors</em></p>`;

      console.log(`Export: Generated references with ${sortedCitations.length} entries`);
      return referencesText;
    };

    // Add references section
    const referencesContent = generateReferencesFromCitations();
    const referencesSection = {
      id: 'references',
      name: 'References',
      content: referencesContent,
      status: 'completed' as const
    };

    const allSections = [...completedSections, referencesSection];

    return {
      title: metadata.title,
      authors: authorsText,
      sections: allSections,
      wordCount: allSections.reduce((total, section) =>
        total + (section.content?.split(/\s+/).length || 0), 0
      )
    };
  };

  const handleExportToWord = async () => {
    setExportError(null);
    setIsExporting('docx');

    try {
      const content = generateCompleteContent();

      // Create enhanced HTML content with proper formatting
      const sectionsHTML = content.sections.map(section => {
        let sectionContent = section.content || '';

        // Clean up any markdown headers that might have been included
        sectionContent = sectionContent
          .replace(/^#{1,6}\s+/gm, '') // Remove markdown headers
          .replace(/^\*\*(.+?)\*\*$/gm, '$1') // Remove bold markdown from standalone lines
          .replace(/^\*(.+?)\*$/gm, '$1'); // Remove italic markdown from standalone lines

        // Enhanced processing for references section
        if (section.id === 'references') {
          // Preserve reference formatting and ensure proper line breaks
          sectionContent = sectionContent
            .replace(/<p>/g, '')
            .replace(/<\/p>/g, '\n\n')
            .replace(/<em>/g, '<i>')
            .replace(/<\/em>/g, '</i>')
            .replace(/<strong>/g, '<b>')
            .replace(/<\/strong>/g, '</b>')
            // Clean up any remaining markdown
            .replace(/\*\*(.+?)\*\*/g, '<b>$1</b>')
            .replace(/\*(.+?)\*/g, '<i>$1</i>');
        } else {
          // Process other sections with proper paragraph handling
          sectionContent = sectionContent
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br/>');

          if (!sectionContent.startsWith('<p>')) {
            sectionContent = '<p>' + sectionContent;
          }
          if (!sectionContent.endsWith('</p>')) {
            sectionContent = sectionContent + '</p>';
          }
        }

        return `
          <div class="section" style="margin-bottom: 2em;">
            <h2 style="color: #1a1a1a; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5em; margin-bottom: 1em; font-weight: bold;">${section.name}</h2>
            <div class="section-content" style="line-height: 1.6; text-align: justify;">${sectionContent}</div>
          </div>
        `;
      }).join('');

      const htmlContent = `
        <div style="font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 2em;">
          <div style="text-align: center; margin-bottom: 3em;">
            <h1 style="color: #1e40af; font-size: 2em; margin-bottom: 0.5em;">${content.title}</h1>
            <div class="authors" style="color: #6b7280; font-size: 1.1em; font-style: italic;">${content.authors}</div>
          </div>
          ${sectionsHTML}
        </div>
      `;

      // Debug: Log the content being exported
      console.log('Exporting DOCX with content:', {
        title: content.title,
        sectionsCount: content.sections.length,
        htmlLength: htmlContent.length,
        firstSection: content.sections[0]
      });

      await documentExportService.exportToDocx(content.title, htmlContent, `${content.title.replace(/[^a-zA-Z0-9]/g, '_')}.docx`);
      toast.success('DOCX file has been downloaded successfully!');
    } catch (err) {
      setExportError('Failed to export document to DOCX format. Please try again.');
      toast.error('Failed to export to DOCX format');
      console.error('DOCX export error:', err);
    } finally {
      setIsExporting(null);
    }
  };

  const handleExportToPDF = () => {
    setExportError(null);
    setIsExporting('pdf');

    try {
      const content = generateCompleteContent();

      // Enhanced PDF export with better formatting
      documentExportService.exportToPDF(
        content.title,
        metadata.authors || [],
        content.sections
      );
      toast.info('PDF export window opened. Use your browser\'s print dialog to save as PDF.');
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error('Failed to export to PDF format');
      setExportError('Failed to export document to PDF format. Please try again.');
    } finally {
      setIsExporting(null);
    }
  };

  // Get export statistics
  const getExportStats = () => {
    const content = generateCompleteContent();
    return {
      totalSections: content.sections.length,
      wordCount: content.wordCount,
      hasReferences: content.sections.some(s => s.id === 'references'),
      completedSections: content.sections.filter(s => s.status === 'completed').length
    };
  };

  const stats = getExportStats();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          size="lg"
          className="px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 shadow-lg transform hover:scale-105 transition-all duration-200"
        >
          <Download className="h-5 w-5 mr-2" />
          Export Complete Paper
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Export Research Paper
          </DialogTitle>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <Badge variant="secondary" className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {stats.totalSections} sections
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {stats.wordCount} words
            </Badge>
            {stats.hasReferences && (
              <Badge variant="secondary" className="flex items-center gap-1 text-green-600">
                <CheckCircle className="h-3 w-3" />
                References included
              </Badge>
            )}
          </div>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {exportError && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">{exportError}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Paper Preview Section */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-blue-900">Paper Preview</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  {showPreview ? 'Hide' : 'Show'} Preview
                </Button>
              </div>

              {showPreview && (
                <div className="space-y-2 text-sm">
                  <div className="font-medium text-gray-900">{metadata.title}</div>
                  <div className="text-gray-600">
                    {metadata.authors && metadata.authors.length > 0
                      ? metadata.authors.join(', ')
                      : 'Anonymous'}
                  </div>
                  <Separator className="my-2" />
                  <div className="space-y-1">
                    {generateCompleteContent().sections.map((section, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-700">{section.name}</span>
                        <span className="text-gray-500 text-xs">
                          {section.content?.split(/\s+/).length || 0} words
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Choose Export Format</h3>

            {/* DOCX Export */}
            <Card className="hover:shadow-md transition-shadow cursor-pointer border-2 hover:border-blue-300">
              <CardContent className="p-0">
                <Button
                  onClick={handleExportToWord}
                  disabled={isExporting === 'docx'}
                  className="w-full h-auto p-6 bg-transparent hover:bg-blue-50 text-gray-900 justify-start border-0 shadow-none"
                >
                  <div className="flex items-center gap-4 w-full">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      {isExporting === 'docx' ? (
                        <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
                      ) : (
                        <FileType className="h-6 w-6 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-lg">Microsoft Word (.docx)</div>
                      <div className="text-sm text-gray-600 mt-1">
                        Professional formatting with proper headings, paragraphs, and references
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">Editable</Badge>
                        <Badge variant="outline" className="text-xs">Professional</Badge>
                        <Badge variant="outline" className="text-xs">Compatible</Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">Recommended</div>
                      <div className="text-xs text-gray-500">Best quality</div>
                    </div>
                  </div>
                </Button>
              </CardContent>
            </Card>

            {/* PDF Export */}
            <Card className="hover:shadow-md transition-shadow cursor-pointer border-2 hover:border-purple-300">
              <CardContent className="p-0">
                <Button
                  onClick={handleExportToPDF}
                  disabled={isExporting === 'pdf'}
                  className="w-full h-auto p-6 bg-transparent hover:bg-purple-50 text-gray-900 justify-start border-0 shadow-none"
                >
                  <div className="flex items-center gap-4 w-full">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      {isExporting === 'pdf' ? (
                        <Loader2 className="h-6 w-6 text-purple-600 animate-spin" />
                      ) : (
                        <Printer className="h-6 w-6 text-purple-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-lg">PDF Document (.pdf)</div>
                      <div className="text-sm text-gray-600 mt-1">
                        Print-ready format with consistent layout and formatting
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">Print-ready</Badge>
                        <Badge variant="outline" className="text-xs">Universal</Badge>
                        <Badge variant="outline" className="text-xs">Shareable</Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-purple-600">Popular</div>
                      <div className="text-xs text-gray-500">Easy sharing</div>
                    </div>
                  </div>
                </Button>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Edit in Editor Option */}
          <Card className="border-gray-200">
            <CardContent className="p-0">
              <Button
                onClick={onEditInEditor}
                variant="outline"
                className="w-full h-auto p-6 justify-start border-2 hover:border-gray-300 hover:bg-gray-50"
              >
                <div className="flex items-center gap-4 w-full">
                  <div className="p-3 bg-gray-100 rounded-lg">
                    <Edit className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-semibold text-lg">Edit in Main Editor</div>
                    <div className="text-sm text-gray-600 mt-1">
                      Open in the advanced text editor for further refinement and customization
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">Advanced editing</Badge>
                      <Badge variant="outline" className="text-xs">AI assistance</Badge>
                      <Badge variant="outline" className="text-xs">Real-time preview</Badge>
                    </div>
                  </div>
                </div>
              </Button>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
