import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Activity,
  TrendingUp,
  Download,
  Eye,
  Maximize2,
  Share2,
  BookOpen,
  FileText,
  Sparkles
} from "lucide-react";

import { DataAnalysisResult } from '../types';
import { EnhancedVisualizationGallery } from './EnhancedVisualizationGallery';

interface ResearchVisualizationGalleryProps {
  analysisResult: DataAnalysisResult;
  researchContext: {
    researchQuestion?: string;
    studyType?: string;
    researchField?: string;
    hypotheses?: string[];
  };
  className?: string;
}

export const ResearchVisualizationGallery: React.FC<ResearchVisualizationGalleryProps> = ({
  analysisResult,
  researchContext,
  className = ""
}) => {
  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Research Figures & Visualizations
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Publication-ready figures with statistical annotations and research context
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{analysisResult.visualizations.length}</div>
              <div className="text-sm text-gray-600">Figures Generated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analysisResult.researchAnalysis?.findings.filter(f => f.type === 'significant').length || 0}
              </div>
              <div className="text-sm text-gray-600">Significant Findings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {analysisResult.researchAnalysis?.statisticalTests.length || 0}
              </div>
              <div className="text-sm text-gray-600">Statistical Tests</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {analysisResult.insights.keyFindings.length}
              </div>
              <div className="text-sm text-gray-600">Key Insights</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Research Context */}
      {researchContext.researchQuestion && (
        <Card className="border-0 shadow-lg bg-gradient-to-r from-purple-50 to-blue-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-purple-500 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Research Context</h3>
                <p className="text-gray-700 mb-2">
                  <strong>Research Question:</strong> {researchContext.researchQuestion}
                </p>
                {researchContext.studyType && (
                  <p className="text-gray-700 mb-2">
                    <strong>Study Type:</strong> {researchContext.studyType}
                  </p>
                )}
                {researchContext.researchField && (
                  <p className="text-gray-700">
                    <strong>Research Field:</strong> {researchContext.researchField}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Research Findings Summary */}
      {analysisResult.researchAnalysis?.findings && (
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-600" />
              Key Research Findings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analysisResult.researchAnalysis.findings.slice(0, 3).map((finding, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge 
                      variant={finding.type === 'significant' ? 'default' : 'secondary'}
                      className={finding.type === 'significant' ? 'bg-green-500' : ''}
                    >
                      {finding.type}
                    </Badge>
                    {finding.statisticalDetails.pValue && (
                      <Badge variant="outline">
                        p = {finding.statisticalDetails.pValue.toFixed(3)}
                      </Badge>
                    )}
                  </div>
                  <p className="text-gray-800 mb-2">{finding.description}</p>
                  <p className="text-sm text-gray-600">{finding.interpretation}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Visualization Gallery */}
      <EnhancedVisualizationGallery analysisResult={analysisResult} />

      {/* Publication Actions */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-green-50 to-blue-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Publication Ready</h3>
              <p className="text-gray-600">
                Your figures are ready for academic publication with proper statistical annotations.
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export Figures
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
