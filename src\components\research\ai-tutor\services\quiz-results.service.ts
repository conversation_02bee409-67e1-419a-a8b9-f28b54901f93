/**
 * Quiz Results Service
 * Handles quiz taking, scoring, and results management
 * for the Research Comprehension Platform
 */

import { Quiz, QuizResult, QuizAnswer, QuizQuestion } from '../types';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface QuizSession {
  quizId: string;
  userId: string;
  startTime: Date;
  answers: Map<string, QuizAnswer>;
  currentQuestionIndex: number;
  isCompleted: boolean;
}

class QuizResultsService {
  private activeSessions = new Map<string, QuizSession>();

  /**
   * Start a new quiz session
   */
  startQuizSession(quiz: Quiz, userId: string): string {
    const sessionId = crypto.randomUUID();
    
    const session: QuizSession = {
      quizId: quiz.id,
      userId,
      startTime: new Date(),
      answers: new Map(),
      currentQuestionIndex: 0,
      isCompleted: false
    };

    this.activeSessions.set(sessionId, session);
    
    console.log(`Started quiz session ${sessionId} for quiz ${quiz.id}`);
    return sessionId;
  }

  /**
   * Submit answer for a question
   */
  submitAnswer(
    sessionId: string,
    questionId: string,
    userAnswer: string | string[],
    timeSpent: number
  ): boolean {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.error('Quiz session not found:', sessionId);
      return false;
    }

    const answer: QuizAnswer = {
      questionId,
      userAnswer,
      isCorrect: false, // Will be calculated when quiz is completed
      timeSpent,
      attempts: (session.answers.get(questionId)?.attempts || 0) + 1
    };

    session.answers.set(questionId, answer);
    console.log(`Answer submitted for question ${questionId} in session ${sessionId}`);
    
    return true;
  }

  /**
   * Complete quiz and calculate results
   */
  async completeQuiz(sessionId: string, quiz: Quiz): Promise<QuizResult | null> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.error('Quiz session not found:', sessionId);
      return null;
    }

    try {
      // Calculate results
      const answers = Array.from(session.answers.values());
      const scoredAnswers = this.scoreAnswers(answers, quiz.questions);
      
      const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0);
      const earnedPoints = scoredAnswers.reduce((sum, a) => sum + (a.isCorrect ? this.getQuestionPoints(a.questionId, quiz.questions) : 0), 0);
      const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;
      
      const totalTimeSpent = scoredAnswers.reduce((sum, a) => sum + a.timeSpent, 0);

      const result: QuizResult = {
        id: crypto.randomUUID(),
        quizId: quiz.id,
        userId: session.userId,
        answers: scoredAnswers,
        score,
        totalPoints,
        earnedPoints,
        timeSpent: totalTimeSpent,
        completedAt: new Date(),
        feedback: this.generateFeedback(score, scoredAnswers, quiz)
      };

      // Save result to database
      await this.saveQuizResult(result);

      // Clean up session
      session.isCompleted = true;
      this.activeSessions.delete(sessionId);

      console.log(`Quiz completed with score: ${score}%`);
      toast.success(`Quiz completed! Score: ${score}%`);

      return result;

    } catch (error) {
      console.error('Error completing quiz:', error);
      toast.error('Failed to complete quiz');
      return null;
    }
  }

  /**
   * Score answers against correct answers
   */
  private scoreAnswers(answers: QuizAnswer[], questions: QuizQuestion[]): QuizAnswer[] {
    const questionMap = new Map(questions.map(q => [q.id, q]));

    return answers.map(answer => {
      const question = questionMap.get(answer.questionId);
      if (!question) {
        return { ...answer, isCorrect: false };
      }

      const isCorrect = this.checkAnswer(answer.userAnswer, question.correctAnswer, question.type);
      
      return {
        ...answer,
        isCorrect
      };
    });
  }

  /**
   * Check if user answer is correct
   */
  private checkAnswer(
    userAnswer: string | string[],
    correctAnswer: string | string[],
    questionType: QuizQuestion['type']
  ): boolean {
    switch (questionType) {
      case 'multiple-choice':
      case 'true-false':
        return this.normalizeAnswer(userAnswer as string) === this.normalizeAnswer(correctAnswer as string);
      
      case 'short-answer':
        return this.checkShortAnswer(userAnswer as string, correctAnswer as string);
      
      case 'essay':
        // Essay questions require manual grading, return true for now
        return true;
      
      default:
        return false;
    }
  }

  /**
   * Check short answer with some flexibility
   */
  private checkShortAnswer(userAnswer: string, correctAnswer: string): boolean {
    const normalizedUser = this.normalizeAnswer(userAnswer);
    const normalizedCorrect = this.normalizeAnswer(correctAnswer);

    // Exact match
    if (normalizedUser === normalizedCorrect) {
      return true;
    }

    // Check if user answer contains key terms from correct answer
    const correctWords = normalizedCorrect.split(/\s+/).filter(word => word.length > 3);
    const userWords = normalizedUser.split(/\s+/);
    
    const matchedWords = correctWords.filter(word => 
      userWords.some(userWord => userWord.includes(word) || word.includes(userWord))
    );

    // Consider correct if at least 70% of key terms are present
    return matchedWords.length >= correctWords.length * 0.7;
  }

  /**
   * Normalize answer for comparison
   */
  private normalizeAnswer(answer: string): string {
    return answer
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ');
  }

  /**
   * Get points for a specific question
   */
  private getQuestionPoints(questionId: string, questions: QuizQuestion[]): number {
    const question = questions.find(q => q.id === questionId);
    return question?.points || 0;
  }

  /**
   * Generate feedback based on quiz performance
   */
  private generateFeedback(score: number, answers: QuizAnswer[], quiz: Quiz): string {
    const correctCount = answers.filter(a => a.isCorrect).length;
    const totalCount = answers.length;
    
    let feedback = `You answered ${correctCount} out of ${totalCount} questions correctly (${score}%). `;

    if (score >= 90) {
      feedback += "Excellent work! You have a strong understanding of the material.";
    } else if (score >= 80) {
      feedback += "Good job! You understand most of the concepts well.";
    } else if (score >= 70) {
      feedback += "Not bad! Review the areas where you missed questions to improve your understanding.";
    } else if (score >= 60) {
      feedback += "You're getting there! Consider reviewing the document more thoroughly and trying again.";
    } else {
      feedback += "This material seems challenging for you. Consider reading the document again and asking for help if needed.";
    }

    // Add specific feedback for question types
    const questionTypes = quiz.questions.reduce((types, q) => {
      types[q.type] = (types[q.type] || 0) + 1;
      return types;
    }, {} as Record<string, number>);

    const incorrectByType = answers
      .filter(a => !a.isCorrect)
      .reduce((types, answer) => {
        const question = quiz.questions.find(q => q.id === answer.questionId);
        if (question) {
          types[question.type] = (types[question.type] || 0) + 1;
        }
        return types;
      }, {} as Record<string, number>);

    if (incorrectByType['multiple-choice'] > 0) {
      feedback += " Focus on reading questions carefully and eliminating obviously wrong answers.";
    }
    
    if (incorrectByType['short-answer'] > 0) {
      feedback += " For short answers, make sure to include key terms and concepts from the document.";
    }

    return feedback;
  }

  /**
   * Save quiz result to database
   */
  private async saveQuizResult(result: QuizResult): Promise<void> {
    try {
      const { error } = await supabase
        .from('quiz_results')
        .insert({
          id: result.id,
          quiz_id: result.quizId,
          user_id: result.userId,
          answers: result.answers,
          score: result.score,
          total_points: result.totalPoints,
          earned_points: result.earnedPoints,
          time_spent: result.timeSpent,
          completed_at: result.completedAt.toISOString(),
          feedback: result.feedback
        });

      if (error) {
        console.error('Error saving quiz result:', error);
        throw new Error(`Failed to save quiz result: ${error.message}`);
      }
    } catch (error) {
      console.error('Error in saveQuizResult:', error);
      throw error;
    }
  }

  /**
   * Get quiz results for a user
   */
  async getUserQuizResults(userId: string, quizId?: string): Promise<QuizResult[]> {
    try {
      let query = supabase
        .from('quiz_results')
        .select('*')
        .eq('user_id', userId);

      if (quizId) {
        query = query.eq('quiz_id', quizId);
      }

      const { data, error } = await query.order('completed_at', { ascending: false });

      if (error) {
        console.error('Error fetching quiz results:', error);
        return [];
      }

      return (data || []).map(result => ({
        id: result.id,
        quizId: result.quiz_id,
        userId: result.user_id,
        answers: result.answers || [],
        score: result.score,
        totalPoints: result.total_points,
        earnedPoints: result.earned_points,
        timeSpent: result.time_spent,
        completedAt: new Date(result.completed_at),
        feedback: result.feedback
      }));

    } catch (error) {
      console.error('Error in getUserQuizResults:', error);
      return [];
    }
  }

  /**
   * Get quiz statistics
   */
  async getQuizStatistics(quizId: string): Promise<{
    totalAttempts: number;
    averageScore: number;
    averageTime: number;
    passRate: number; // Percentage scoring 70% or higher
  }> {
    try {
      const { data, error } = await supabase
        .from('quiz_results')
        .select('score, time_spent')
        .eq('quiz_id', quizId);

      if (error) {
        console.error('Error fetching quiz statistics:', error);
        return { totalAttempts: 0, averageScore: 0, averageTime: 0, passRate: 0 };
      }

      const results = data || [];
      const totalAttempts = results.length;

      if (totalAttempts === 0) {
        return { totalAttempts: 0, averageScore: 0, averageTime: 0, passRate: 0 };
      }

      const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalAttempts;
      const averageTime = results.reduce((sum, r) => sum + r.time_spent, 0) / totalAttempts;
      const passCount = results.filter(r => r.score >= 70).length;
      const passRate = (passCount / totalAttempts) * 100;

      return {
        totalAttempts,
        averageScore: Math.round(averageScore),
        averageTime: Math.round(averageTime),
        passRate: Math.round(passRate)
      };

    } catch (error) {
      console.error('Error in getQuizStatistics:', error);
      return { totalAttempts: 0, averageScore: 0, averageTime: 0, passRate: 0 };
    }
  }

  /**
   * Get active quiz session
   */
  getQuizSession(sessionId: string): QuizSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Cancel quiz session
   */
  cancelQuizSession(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.activeSessions.delete(sessionId);
      console.log(`Quiz session ${sessionId} cancelled`);
      return true;
    }
    return false;
  }

  /**
   * Get quiz progress
   */
  getQuizProgress(sessionId: string): { current: number; total: number; percentage: number } | null {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return null;
    }

    const answeredCount = session.answers.size;
    // We need to get the total questions from the quiz, but we don't have it in the session
    // This would need to be passed or stored differently in a real implementation
    const totalQuestions = 10; // Placeholder

    return {
      current: answeredCount,
      total: totalQuestions,
      percentage: Math.round((answeredCount / totalQuestions) * 100)
    };
  }
}

export const quizResultsService = new QuizResultsService();
