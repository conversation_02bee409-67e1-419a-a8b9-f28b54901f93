/**
 * Document Parser Service for Article Revision System
 * Handles parsing of PDF and Word documents
 */

import { ParsedArticle, ParsedReviewerComments, ReviewerComment, ArticleSection } from '../types';
import { FILE_CONSTRAINTS, ERROR_MESSAGES, COMMENT_PATTERNS } from '../constants';

export class DocumentParserService {
  /**
   * Parse an article document (PDF or Word)
   */
  static async parseArticle(file: File): Promise<ParsedArticle> {
    try {
      // Validate file
      this.validateFile(file, 'article');
      
      // Extract text based on file type
      let fullText: string;
      if (file.type === 'application/pdf') {
        fullText = await this.parsePDF(file);
      } else if (file.type.includes('word') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
        fullText = await this.parseWord(file);
      } else {
        throw new Error(ERROR_MESSAGES.UNSUPPORTED_FORMAT);
      }
      
      // Extract sections from the text
      const sectionMapping = this.extractSections(fullText);
      
      // Count words
      const wordCount = this.countWords(fullText);
      
      return {
        title: sectionMapping.title || this.extractTitle(fullText),
        abstract: sectionMapping.abstract,
        keywords: this.extractKeywords(sectionMapping.keywords || ''),
        introduction: sectionMapping.introduction,
        methodology: sectionMapping.methodology,
        results: sectionMapping.results,
        discussion: sectionMapping.discussion,
        conclusion: sectionMapping.conclusion,
        references: sectionMapping.references,
        fullText,
        sectionMapping,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        wordCount
      };
    } catch (error) {
      console.error('Error parsing article:', error);
      throw new Error(`${ERROR_MESSAGES.PARSING_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Parse reviewer comments from plain text input
   */
  static parseReviewerCommentsFromText(text: string, reviewerNumber: number, fileName?: string): ParsedReviewerComments {
    try {
      if (!text || text.trim().length < 10) {
        throw new Error('Comment text is too short or empty');
      }

      // Extract and categorize comments
      const comments = this.extractComments(text, reviewerNumber);
      const generalComments = this.extractGeneralComments(text);
      const overallAssessment = this.extractOverallAssessment(text);

      if (comments.length === 0) {
        // If no structured comments found, treat the whole text as one comment
        const singleComment = this.parseComment(text.trim(), reviewerNumber, 0);
        if (singleComment) {
          comments.push(singleComment);
        }
      }

      return {
        fileName: fileName || `Reviewer ${reviewerNumber} Comments (Text Input)`,
        fileType: 'text/plain',
        reviewerNumber,
        comments,
        generalComments,
        overallAssessment
      };
    } catch (error) {
      console.error('Error parsing reviewer comments from text:', error);
      throw new Error(`Failed to parse comments: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse reviewer comments from uploaded files
   */
  static async parseReviewerComments(file: File, reviewerNumber: number): Promise<ParsedReviewerComments> {
    try {
      // Validate file
      this.validateFile(file, 'comments');
      
      // Extract text based on file type
      let fullText: string;
      if (file.type === 'application/pdf') {
        fullText = await this.parsePDF(file);
      } else if (file.type.includes('word') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
        fullText = await this.parseWord(file);
      } else if (file.type === 'text/plain') {
        fullText = await this.parseText(file);
      } else {
        throw new Error(ERROR_MESSAGES.UNSUPPORTED_FORMAT);
      }
      
      // Extract and categorize comments
      const comments = this.extractComments(fullText, reviewerNumber);
      const generalComments = this.extractGeneralComments(fullText);
      const overallAssessment = this.extractOverallAssessment(fullText);
      
      return {
        fileName: file.name,
        fileType: file.type,
        reviewerNumber,
        comments,
        generalComments,
        overallAssessment
      };
    } catch (error) {
      console.error('Error parsing reviewer comments:', error);
      throw new Error(`${ERROR_MESSAGES.PARSING_FAILED}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Validate uploaded file
   */
  private static validateFile(file: File, type: 'article' | 'comments'): void {
    // Check file size
    if (file.size > FILE_CONSTRAINTS.MAX_FILE_SIZE) {
      throw new Error(ERROR_MESSAGES.FILE_TOO_LARGE);
    }
    
    // Check file format
    const supportedFormats = type === 'article' 
      ? FILE_CONSTRAINTS.SUPPORTED_ARTICLE_FORMATS
      : FILE_CONSTRAINTS.SUPPORTED_COMMENT_FORMATS;
    
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isSupported = supportedFormats.includes(fileExtension) || 
                       file.type.includes('pdf') || 
                       file.type.includes('word') ||
                       file.type.includes('text');
    
    if (!isSupported) {
      throw new Error(ERROR_MESSAGES.UNSUPPORTED_FORMAT);
    }
  }
  
  /**
   * Parse PDF file using PDF.js library
   */
  private static async parsePDF(file: File): Promise<string> {
    try {
      // Dynamic import to avoid SSR issues
      const pdfjsLib = await import('pdfjs-dist');

      // Set worker source
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let fullText = '';
      const numPages = pdf.numPages;

      console.log(`Parsing PDF with ${numPages} pages`);

      // Extract text from each page
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        try {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();

          const pageText = textContent.items
            .map((item: any) => item.str)
            .join(' ');

          fullText += pageText + '\n\n';
        } catch (pageError) {
          console.warn(`Error parsing page ${pageNum}:`, pageError);
          continue;
        }
      }

      // Clean up the extracted text
      fullText = fullText
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n\n')
        .trim();

      if (fullText.length < 50) {
        throw new Error('PDF appears to be empty or contains only images. Please try a different format or copy-paste the text.');
      }

      console.log(`Successfully extracted ${fullText.length} characters from PDF`);
      return fullText;

    } catch (error) {
      console.error('PDF parsing error:', error);

      // Fallback to basic extraction
      return this.parsePDFBasic(file);
    }
  }

  /**
   * Basic PDF parsing fallback
   */
  private static async parsePDFBasic(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);
          const decoder = new TextDecoder('utf-8', { fatal: false });
          const pdfString = decoder.decode(uint8Array);

          // Extract text between common PDF text markers
          const textMatches = pdfString.match(/\((.*?)\)/g) || [];
          let text = '';

          textMatches.forEach(match => {
            const cleanText = match.replace(/[()]/g, '').trim();
            if (cleanText.length > 3 && /[a-zA-Z]/.test(cleanText)) {
              text += cleanText + ' ';
            }
          });

          if (text.trim().length < 50) {
            text = `PDF file uploaded: ${file.name}

Note: Basic text extraction was used. For better results:
1. Convert your PDF to Word format first
2. Use the copy-paste option below
3. Ensure the PDF contains selectable text (not scanned images)`;
          }

          resolve(text);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read PDF file'));
      reader.readAsArrayBuffer(file);
    });
  }
  
  /**
   * Parse Word document using mammoth.js library
   */
  private static async parseWord(file: File): Promise<string> {
    try {
      // Dynamic import to avoid SSR issues
      const mammoth = await import('mammoth');

      const arrayBuffer = await file.arrayBuffer();

      console.log(`Parsing Word document: ${file.name}`);

      // Extract text using mammoth.js
      const result = await mammoth.extractRawText({ arrayBuffer });

      if (result.messages && result.messages.length > 0) {
        console.warn('Mammoth parsing messages:', result.messages);
      }

      let text = result.value;

      // Clean up the extracted text
      text = text
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .replace(/\s+/g, ' ')
        .trim();

      if (text.length < 50) {
        throw new Error('Word document appears to be empty or could not be parsed properly.');
      }

      console.log(`Successfully extracted ${text.length} characters from Word document`);
      return text;

    } catch (error) {
      console.error('Word parsing error:', error);

      // Fallback to basic extraction
      return this.parseWordBasic(file);
    }
  }

  /**
   * Basic Word parsing fallback
   */
  private static async parseWordBasic(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);
          let text = '';

          // Look for XML content in modern Word documents (.docx)
          if (file.name.endsWith('.docx')) {
            const decoder = new TextDecoder('utf-8', { fatal: false });
            const docString = decoder.decode(uint8Array);

            // Extract text from XML tags (very basic)
            const textMatches = docString.match(/<w:t[^>]*>(.*?)<\/w:t>/g) || [];
            textMatches.forEach(match => {
              const cleanText = match.replace(/<[^>]*>/g, '').trim();
              if (cleanText.length > 0) {
                text += cleanText + ' ';
              }
            });
          }

          if (text.trim().length < 50) {
            text = `Word document uploaded: ${file.name}

Note: Basic text extraction was used. For better results:
1. Save your document as a newer .docx format
2. Use the copy-paste option below
3. Ensure the document is not password protected`;
          }

          resolve(text);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read Word file'));
      reader.readAsArrayBuffer(file);
    });
  }
  
  /**
   * Parse plain text file
   */
  private static async parseText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        resolve(text);
      };
      reader.onerror = () => reject(new Error('Failed to read text file'));
      reader.readAsText(file);
    });
  }
  
  /**
   * Extract sections from article text
   */
  static extractSections(text: string): Record<ArticleSection, string> {
    const sections: Record<ArticleSection, string> = {
      title: '',
      abstract: '',
      keywords: '',
      introduction: '',
      methodology: '',
      results: '',
      discussion: '',
      conclusion: '',
      references: '',
      overall: text
    };
    
    console.log('Extracting sections from text of length:', text.length);

    // More flexible section header patterns
    const sectionPatterns = {
      title: /^(.+?)(?:\n|$)/i,
      abstract: /(?:abstract|summary)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:keywords?|key\s*words?|introduction|1\.|2\.|$))/i,
      keywords: /(?:keywords?|key\s*words?)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:introduction|1\.|2\.|$))/i,
      introduction: /(?:introduction|1\.?\s*introduction)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:method|methodology|literature|2\.|$))/i,
      methodology: /(?:method|methodology|materials?\s+and\s+method|2\.?\s*method)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:result|finding|3\.|$))/i,
      results: /(?:result|finding|3\.?\s*result)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:discussion|conclusion|4\.|$))/i,
      discussion: /(?:discussion|4\.?\s*discussion)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:conclusion|5\.|reference|$))/i,
      conclusion: /(?:conclusion|5\.?\s*conclusion)[:\s]*\n?([\s\S]*?)(?=\n\s*(?:reference|acknowledgment|bibliography|$))/i,
      references: /(?:reference|bibliography)[:\s]*\n?([\s\S]*)$/i
    };

    // Extract each section
    Object.entries(sectionPatterns).forEach(([section, pattern]) => {
      const match = text.match(pattern);
      if (match) {
        // Use capture group if available, otherwise use full match
        const content = match[1] ? match[1].trim() : match[0].trim();
        if (content.length > 10) { // Only include sections with meaningful content
          sections[section as ArticleSection] = content;
          console.log(`Found ${section} section: ${content.length} characters`);
        }
      }
    });

    // If no sections found, try to split by common patterns
    if (Object.values(sections).every(s => s === '' || s === text)) {
      console.log('No clear sections found, attempting paragraph-based extraction');
      const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 50);

      if (paragraphs.length >= 3) {
        // Assign paragraphs to sections based on position and content
        sections.introduction = paragraphs[0] || '';
        if (paragraphs.length >= 4) {
          sections.methodology = paragraphs[1] || '';
          sections.results = paragraphs[2] || '';
          sections.discussion = paragraphs[paragraphs.length - 1] || '';
        } else {
          sections.results = paragraphs[1] || '';
          sections.discussion = paragraphs[2] || '';
        }
        console.log('Assigned content to sections based on paragraph structure');
      }
    }
    
    return sections;
  }
  
  /**
   * Extract title from text
   */
  private static extractTitle(text: string): string {
    const lines = text.split('\n').filter(line => line.trim());
    return lines[0]?.trim() || 'Untitled Article';
  }
  
  /**
   * Extract keywords from keywords section
   */
  private static extractKeywords(keywordsText: string): string[] {
    if (!keywordsText) return [];
    
    // Remove "Keywords:" label and split by common delimiters
    const cleaned = keywordsText.replace(/^keywords?:?\s*/i, '');
    return cleaned
      .split(/[,;]/)
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0);
  }
  
  /**
   * Extract and categorize comments from reviewer text
   */
  private static extractComments(text: string, reviewerNumber: number): ReviewerComment[] {
    const comments: ReviewerComment[] = [];

    // Clean up the text
    const cleanText = text.replace(/\s+/g, ' ').trim();

    console.log(`Extracting comments for reviewer ${reviewerNumber}, text length: ${cleanText.length}`);

    // If text is too short, treat the whole thing as one comment
    if (cleanText.length < 100) {
      if (cleanText.length > 10) {
        const comment = this.parseComment(cleanText, reviewerNumber, 0);
        if (comment) {
          comments.push(comment);
        }
      }
      return comments;
    }

    // Try different splitting strategies in order of preference
    let commentBlocks: string[] = [];

    // Strategy 1: Split by numbered comments (1., 2., etc.) or (1), (2), etc.
    const numberedPatterns = [
      /(?=\d+\.\s)/,           // 1. Comment
      /(?=\(\d+\))/,           // (1) Comment
      /(?=Comment\s*\d+)/i,    // Comment 1
      /(?=\d+\)\s)/            // 1) Comment
    ];

    for (const pattern of numberedPatterns) {
      const split = text.split(pattern);
      if (split.length > 1) {
        commentBlocks = split.filter(block => block.trim().length > 10);
        console.log(`Found ${commentBlocks.length} numbered comments using pattern`);
        break;
      }
    }

    // Strategy 2: Split by bullet points or dashes
    if (commentBlocks.length === 0) {
      const bulletPatterns = [
        /(?=[-•*]\s)/,           // - Comment
        /(?=>\s)/,               // > Comment
        /(?=\*\s)/               // * Comment
      ];

      for (const pattern of bulletPatterns) {
        const split = text.split(pattern);
        if (split.length > 1) {
          commentBlocks = split.filter(block => block.trim().length > 10);
          console.log(`Found ${commentBlocks.length} bulleted comments`);
          break;
        }
      }
    }

    // Strategy 3: Split by section headers or reviewer indicators
    if (commentBlocks.length === 0) {
      const sectionPatterns = [
        /(?=Major\s+comments?)/i,
        /(?=Minor\s+comments?)/i,
        /(?=Specific\s+comments?)/i,
        /(?=General\s+comments?)/i,
        /(?=Page\s+\d+)/i,
        /(?=Line\s+\d+)/i
      ];

      for (const pattern of sectionPatterns) {
        const split = text.split(pattern);
        if (split.length > 1) {
          commentBlocks = split.filter(block => block.trim().length > 20);
          console.log(`Found ${commentBlocks.length} section-based comments`);
          break;
        }
      }
    }

    // Strategy 4: Split by double line breaks
    if (commentBlocks.length === 0) {
      commentBlocks = text.split(/\n\s*\n/).filter(block => block.trim().length > 20);
      if (commentBlocks.length > 1) {
        console.log(`Found ${commentBlocks.length} paragraph-based comments`);
      }
    }

    // Strategy 5: Split by sentences if we have a long single block
    if (commentBlocks.length <= 1 && cleanText.length > 300) {
      const sentences = text.split(/[.!?]+\s+/).filter(block => block.trim().length > 30);
      if (sentences.length > 2) {
        commentBlocks = sentences;
        console.log(`Split into ${commentBlocks.length} sentence-based comments`);
      }
    }

    // If still no good splits, treat as single comment
    if (commentBlocks.length === 0 && cleanText.length > 10) {
      commentBlocks = [cleanText];
      console.log('Treating entire text as single comment');
    }

    // Process each block
    commentBlocks.forEach((block, index) => {
      const trimmedBlock = block.trim();
      if (trimmedBlock.length > 10 && this.isComment(trimmedBlock)) {
        const comment = this.parseComment(trimmedBlock, reviewerNumber, index);
        if (comment) {
          comments.push(comment);
        }
      }
    });

    console.log(`Extracted ${comments.length} valid comments for reviewer ${reviewerNumber}`);
    return comments;
  }
  
  /**
   * Check if a text block is a comment
   */
  private static isComment(text: string): boolean {
    // Skip very short text
    if (text.trim().length < 10) {
      return false;
    }

    // Skip common non-comment content
    const skipPatterns = [
      /^(page|figure|table|appendix)\s*\d+$/i,
      /^(abstract|introduction|conclusion|references)$/i,
      /^[a-z]+@[a-z]+\.[a-z]+$/i, // email
      /^https?:\/\//i, // URL
      /^\d+$/, // just numbers
    ];

    if (skipPatterns.some(pattern => pattern.test(text.trim()))) {
      return false;
    }

    // Look for comment indicators (more comprehensive)
    const commentIndicators = [
      /comment\s*\d*/i,
      /line\s*\d+/i,
      /page\s*\d+/i,
      /section\s*\d*/i,
      /paragraph\s*\d*/i,
      /suggest/i,
      /recommend/i,
      /should/i,
      /could/i,
      /would/i,
      /please/i,
      /consider/i,
      /improve/i,
      /clarify/i,
      /explain/i,
      /add/i,
      /remove/i,
      /change/i,
      /revise/i,
      /modify/i,
      /unclear/i,
      /confusing/i,
      /missing/i,
      /weak/i,
      /strong/i,
      /good/i,
      /better/i,
      /need/i,
      /require/i,
      /must/i,
      /important/i,
      /critical/i,
      /major/i,
      /minor/i,
      /issue/i,
      /problem/i,
      /concern/i,
      /question/i,
      /why/i,
      /how/i,
      /what/i,
      /where/i,
      /when/i,
      /which/i
    ];

    // If it contains comment indicators, it's likely a comment
    const hasIndicators = commentIndicators.some(pattern => pattern.test(text));

    // Also consider it a comment if it's a reasonable length and contains verbs/suggestions
    const hasVerbs = /\b(is|are|was|were|be|been|have|has|had|do|does|did|will|would|should|could|can|may|might)\b/i.test(text);
    const reasonableLength = text.trim().length > 20 && text.trim().length < 2000;

    return hasIndicators || (hasVerbs && reasonableLength);
  }
  
  /**
   * Parse individual comment
   */
  private static parseComment(text: string, reviewerNumber: number, index: number): ReviewerComment | null {
    try {
      const id = `reviewer-${reviewerNumber}-comment-${index + 1}`;
      
      // Determine comment type and severity
      const severity = this.determineSeverity(text);
      const category = this.determineCategory(text);
      const commentType = this.determineCommentType(text);
      
      // Extract target section if mentioned
      const targetSection = this.extractTargetSection(text);
      
      // Determine if can be auto-fixed
      const canAutoFix = this.canAutoFix(text, category);
      const requiresManualIntervention = this.requiresManualIntervention(text, category);
      
      return {
        id,
        reviewerNumber,
        commentType,
        severity,
        category,
        originalText: text,
        comment: text,
        targetSection,
        canAutoFix,
        requiresManualIntervention
      };
    } catch (error) {
      console.error('Error parsing comment:', error);
      return null;
    }
  }
  
  /**
   * Determine comment severity based on keywords
   */
  private static determineSeverity(text: string): ReviewerComment['severity'] {
    const lowerText = text.toLowerCase();
    
    for (const [severity, keywords] of Object.entries(COMMENT_PATTERNS.SEVERITY_KEYWORDS)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        return severity as ReviewerComment['severity'];
      }
    }
    
    return 'moderate'; // Default
  }
  
  /**
   * Determine comment category based on keywords
   */
  private static determineCategory(text: string): ReviewerComment['category'] {
    const lowerText = text.toLowerCase();
    
    for (const [category, keywords] of Object.entries(COMMENT_PATTERNS.CATEGORY_KEYWORDS)) {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        return category as ReviewerComment['category'];
      }
    }
    
    return 'other'; // Default
  }
  
  /**
   * Determine comment type
   */
  private static determineCommentType(text: string): ReviewerComment['commentType'] {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('figure') || lowerText.includes('table')) {
      return 'figure';
    } else if (lowerText.includes('reference') || lowerText.includes('citation')) {
      return 'reference';
    } else if (lowerText.includes('line') || lowerText.includes('page')) {
      return 'specific';
    } else {
      return 'general';
    }
  }
  
  /**
   * Extract target section from comment
   */
  private static extractTargetSection(text: string): ArticleSection | undefined {
    const lowerText = text.toLowerCase();
    const sections: ArticleSection[] = [
      'introduction', 'methodology', 'results', 'discussion', 
      'conclusion', 'abstract', 'references'
    ];
    
    for (const section of sections) {
      if (lowerText.includes(section)) {
        return section;
      }
    }
    
    return undefined;
  }
  
  /**
   * Determine if comment can be auto-fixed
   */
  private static canAutoFix(text: string, category: ReviewerComment['category']): boolean {
    // Language and citation issues can often be auto-fixed
    if (category === 'language' || category === 'citation') {
      return true;
    }
    
    // Simple content issues might be auto-fixable
    if (category === 'content' && text.length < 200) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Determine if comment requires manual intervention
   */
  private static requiresManualIntervention(text: string, category: ReviewerComment['category']): boolean {
    // Figures, tables, and complex methodology changes need manual work
    if (category === 'figure' || text.toLowerCase().includes('table')) {
      return true;
    }
    
    // Major methodology changes
    if (category === 'methodology' && text.toLowerCase().includes('major')) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Extract general comments
   */
  private static extractGeneralComments(text: string): string[] {
    // Look for general assessment sections
    const generalSections = text.match(/(?:general|overall|summary)[\s\S]*?(?=\n\s*(?:specific|comment|$))/gi);
    return generalSections?.map(section => section.trim()) || [];
  }
  
  /**
   * Extract overall assessment
   */
  private static extractOverallAssessment(text: string): string | undefined {
    const assessmentMatch = text.match(/(?:overall|recommendation|decision)[\s\S]*$/i);
    return assessmentMatch?.[0]?.trim();
  }
  
  /**
   * Count words in text
   */
  private static countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }
}
