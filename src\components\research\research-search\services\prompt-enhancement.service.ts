/**
 * Prompt Enhancement Service for Academic Research
 * Transforms simple user prompts into detailed, comprehensive research specifications
 */

import { ResearchType } from '../types';

export interface EnhancedPrompt {
  originalPrompt: string;
  enhancedPrompt: string;
  researchContext: string;
  suggestedResearchType: string;
  keywords: string[];
  academicLevel: string;
  estimatedScope: string;
  researchQuestions: string[];
  methodology: string[];
  expectedOutcomes: string[];
  timeEstimate: string;
  wordCountEstimate: number;
  citationRequirements: string;
  structureRecommendations: string[];
}

export interface PromptAnalysis {
  topic: string;
  complexity: 'basic' | 'intermediate' | 'advanced' | 'expert';
  domain: string;
  researchTypeRecommendation: string;
  scopeAssessment: string;
  keyTerms: string[];
}

class PromptEnhancementService {
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found. Prompt enhancement will use fallback methods.');
    }
  }

  /**
   * Enhance a simple user prompt into a detailed research specification
   */
  async enhancePrompt(
    originalPrompt: string,
    preferredResearchType?: string,
    model: string = 'google/gemini-2.5-flash'
  ): Promise<EnhancedPrompt> {
    if (!this.apiKey) {
      return this.createFallbackEnhancement(originalPrompt, preferredResearchType);
    }

    try {
      const analysis = await this.analyzePrompt(originalPrompt, model);
      const enhancement = await this.generateEnhancement(originalPrompt, analysis, preferredResearchType, model);
      
      return enhancement;
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      return this.createFallbackEnhancement(originalPrompt, preferredResearchType);
    }
  }

  /**
   * Analyze the user's prompt to understand research requirements
   */
  private async analyzePrompt(prompt: string, model: string): Promise<PromptAnalysis> {
    const systemPrompt = `You are an expert academic research advisor. Analyze the user's research prompt and provide a structured analysis.

Your task is to:
1. Identify the main topic and research domain
2. Assess the complexity level (basic, intermediate, advanced, expert)
3. Recommend the most appropriate research type
4. Assess the scope and scale of the research
5. Extract key terms and concepts

Respond with a JSON object containing:
{
  "topic": "main research topic",
  "complexity": "basic|intermediate|advanced|expert",
  "domain": "academic domain (e.g., psychology, computer science, medicine)",
  "researchTypeRecommendation": "literature_review|systematic_review|meta_analysis|research_paper|policy_brief",
  "scopeAssessment": "brief description of research scope",
  "keyTerms": ["array", "of", "key", "terms"]
}`;

    const userPrompt = `Analyze this research prompt: "${prompt}"

Provide a comprehensive analysis focusing on:
- What type of research would be most appropriate
- The academic domain and complexity level
- Key terms and concepts that should be explored
- The scope and scale of investigation needed`;

    const response = await this.callOpenRouter(model, systemPrompt, userPrompt);
    
    try {
      return JSON.parse(response);
    } catch (error) {
      console.error('Failed to parse prompt analysis:', error);
      return this.createFallbackAnalysis(prompt);
    }
  }

  /**
   * Generate comprehensive enhancement based on analysis
   */
  private async generateEnhancement(
    originalPrompt: string,
    analysis: PromptAnalysis,
    preferredResearchType: string | undefined,
    model: string
  ): Promise<EnhancedPrompt> {
    const researchType = preferredResearchType || analysis.researchTypeRecommendation;
    
    const systemPrompt = `You are an expert academic research consultant. Your task is to transform a simple research prompt into a comprehensive, detailed research specification that will guide high-quality academic research.

Focus on:
1. Creating a detailed, specific research prompt that maintains clarity
2. Providing comprehensive research context and background
3. Suggesting specific research questions and methodology
4. Estimating scope, timeline, and requirements
5. Recommending academic structure and citation requirements

The enhanced prompt should be professional, detailed, but still readable and clear.`;

    const userPrompt = `Transform this research prompt into a comprehensive academic research specification:

Original Prompt: "${originalPrompt}"

Research Analysis:
- Topic: ${analysis.topic}
- Domain: ${analysis.domain}
- Complexity: ${analysis.complexity}
- Recommended Type: ${researchType}
- Key Terms: ${analysis.keyTerms.join(', ')}

Please provide a detailed enhancement that includes:
1. Enhanced Prompt: A comprehensive, detailed version of the original prompt
2. Research Context: Background and significance of the research
3. Research Questions: 3-5 specific research questions to investigate
4. Methodology: Suggested research approaches and methods
5. Expected Outcomes: What the research should achieve
6. Structure Recommendations: How the research should be organized

Make the enhanced prompt detailed but readable, avoiding overly complex academic jargon while maintaining scholarly rigor.`;

    const response = await this.callOpenRouter(model, systemPrompt, userPrompt);
    
    return this.parseEnhancementResponse(originalPrompt, response, analysis, researchType);
  }

  /**
   * Parse the AI response into structured enhancement
   */
  private parseEnhancementResponse(
    originalPrompt: string,
    response: string,
    analysis: PromptAnalysis,
    researchType: string
  ): EnhancedPrompt {
    // Extract sections from the response
    const sections = this.extractSections(response);
    
    return {
      originalPrompt,
      enhancedPrompt: sections.enhancedPrompt || this.createEnhancedPrompt(originalPrompt, analysis),
      researchContext: sections.researchContext || `Research on ${analysis.topic} in the field of ${analysis.domain}`,
      suggestedResearchType: researchType,
      keywords: analysis.keyTerms,
      academicLevel: this.mapComplexityToLevel(analysis.complexity),
      estimatedScope: analysis.scopeAssessment,
      researchQuestions: sections.researchQuestions || this.generateDefaultQuestions(analysis.topic),
      methodology: sections.methodology || this.generateDefaultMethodology(researchType),
      expectedOutcomes: sections.expectedOutcomes || this.generateDefaultOutcomes(analysis.topic),
      timeEstimate: this.estimateTime(analysis.complexity, researchType),
      wordCountEstimate: this.estimateWordCount(researchType, analysis.complexity),
      citationRequirements: this.getCitationRequirements(researchType),
      structureRecommendations: sections.structureRecommendations || this.getDefaultStructure(researchType)
    };
  }

  /**
   * Extract sections from AI response
   */
  private extractSections(response: string): Record<string, string | string[]> {
    const sections: Record<string, string | string[]> = {};
    
    // Extract enhanced prompt
    const enhancedMatch = response.match(/Enhanced Prompt:?\s*\n(.*?)(?=\n\n|\nResearch Context:|$)/s);
    if (enhancedMatch) {
      sections.enhancedPrompt = enhancedMatch[1].trim();
    }
    
    // Extract research context
    const contextMatch = response.match(/Research Context:?\s*\n(.*?)(?=\n\n|\nResearch Questions:|$)/s);
    if (contextMatch) {
      sections.researchContext = contextMatch[1].trim();
    }
    
    // Extract research questions
    const questionsMatch = response.match(/Research Questions:?\s*\n(.*?)(?=\n\n|\nMethodology:|$)/s);
    if (questionsMatch) {
      sections.researchQuestions = this.parseListItems(questionsMatch[1]);
    }
    
    // Extract methodology
    const methodologyMatch = response.match(/Methodology:?\s*\n(.*?)(?=\n\n|\nExpected Outcomes:|$)/s);
    if (methodologyMatch) {
      sections.methodology = this.parseListItems(methodologyMatch[1]);
    }
    
    // Extract expected outcomes
    const outcomesMatch = response.match(/Expected Outcomes:?\s*\n(.*?)(?=\n\n|\nStructure Recommendations:|$)/s);
    if (outcomesMatch) {
      sections.expectedOutcomes = this.parseListItems(outcomesMatch[1]);
    }
    
    // Extract structure recommendations
    const structureMatch = response.match(/Structure Recommendations:?\s*\n(.*?)$/s);
    if (structureMatch) {
      sections.structureRecommendations = this.parseListItems(structureMatch[1]);
    }
    
    return sections;
  }

  /**
   * Parse list items from text
   */
  private parseListItems(text: string): string[] {
    return text
      .split('\n')
      .map(line => line.replace(/^[-*•]\s*/, '').replace(/^\d+\.\s*/, '').trim())
      .filter(line => line.length > 0)
      .slice(0, 10); // Limit to 10 items
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouter(model: string, systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Research Prompt Enhancement'
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 4000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Create fallback enhancement when API is not available
   */
  private createFallbackEnhancement(originalPrompt: string, preferredResearchType?: string): EnhancedPrompt {
    const analysis = this.createFallbackAnalysis(originalPrompt);
    const researchType = preferredResearchType || 'literature_review';
    
    return {
      originalPrompt,
      enhancedPrompt: this.createEnhancedPrompt(originalPrompt, analysis),
      researchContext: `Comprehensive research investigation on ${analysis.topic}`,
      suggestedResearchType: researchType,
      keywords: analysis.keyTerms,
      academicLevel: 'graduate',
      estimatedScope: 'Comprehensive academic research',
      researchQuestions: this.generateDefaultQuestions(analysis.topic),
      methodology: this.generateDefaultMethodology(researchType),
      expectedOutcomes: this.generateDefaultOutcomes(analysis.topic),
      timeEstimate: this.estimateTime('intermediate', researchType),
      wordCountEstimate: this.estimateWordCount(researchType, 'intermediate'),
      citationRequirements: this.getCitationRequirements(researchType),
      structureRecommendations: this.getDefaultStructure(researchType)
    };
  }

  /**
   * Create fallback analysis
   */
  private createFallbackAnalysis(prompt: string): PromptAnalysis {
    const words = prompt.toLowerCase().split(/\s+/);
    const keyTerms = words.filter(word => word.length > 3).slice(0, 5);
    
    return {
      topic: prompt.slice(0, 100),
      complexity: 'intermediate',
      domain: 'general',
      researchTypeRecommendation: 'literature_review',
      scopeAssessment: 'Standard academic research scope',
      keyTerms
    };
  }

  // Helper methods for generating default content
  private createEnhancedPrompt(originalPrompt: string, analysis: PromptAnalysis): string {
    return `Conduct a comprehensive ${analysis.researchTypeRecommendation.replace('_', ' ')} on ${analysis.topic}. This research should examine the current state of knowledge, identify key trends and patterns, analyze methodological approaches, and synthesize findings from multiple sources. The investigation should focus on ${analysis.keyTerms.join(', ')} and provide insights that contribute to understanding in the field of ${analysis.domain}. The research should be thorough, well-structured, and academically rigorous, suitable for ${this.mapComplexityToLevel(analysis.complexity)} level academic work.`;
  }

  private generateDefaultQuestions(topic: string): string[] {
    return [
      `What is the current state of research on ${topic}?`,
      `What are the key findings and trends in ${topic} research?`,
      `What methodological approaches are commonly used in ${topic} studies?`,
      `What gaps exist in the current literature on ${topic}?`,
      `What are the implications and future directions for ${topic} research?`
    ];
  }

  private generateDefaultMethodology(researchType: string): string[] {
    const methodologies: Record<string, string[]> = {
      literature_review: ['Systematic literature search', 'Critical analysis of sources', 'Thematic synthesis', 'Quality assessment'],
      systematic_review: ['PRISMA guidelines', 'Comprehensive database search', 'Study selection criteria', 'Data extraction and analysis'],
      meta_analysis: ['Statistical meta-analysis', 'Effect size calculation', 'Heterogeneity assessment', 'Publication bias testing'],
      research_paper: ['Primary data collection', 'Statistical analysis', 'Hypothesis testing', 'Results interpretation'],
      policy_brief: ['Policy analysis', 'Stakeholder consultation', 'Evidence synthesis', 'Recommendation development']
    };
    
    return methodologies[researchType] || methodologies.literature_review;
  }

  private generateDefaultOutcomes(topic: string): string[] {
    return [
      `Comprehensive understanding of ${topic}`,
      'Identification of research gaps and opportunities',
      'Synthesis of current knowledge and findings',
      'Evidence-based conclusions and recommendations',
      'Contribution to academic discourse and future research'
    ];
  }

  private mapComplexityToLevel(complexity: string): string {
    const mapping: Record<string, string> = {
      basic: 'undergraduate',
      intermediate: 'graduate',
      advanced: 'doctoral',
      expert: 'postdoctoral'
    };
    return mapping[complexity] || 'graduate';
  }

  private estimateTime(complexity: string, researchType: string): string {
    const baseHours: Record<string, number> = {
      literature_review: 20,
      systematic_review: 40,
      meta_analysis: 60,
      research_paper: 80,
      policy_brief: 30
    };
    
    const complexityMultiplier: Record<string, number> = {
      basic: 0.7,
      intermediate: 1.0,
      advanced: 1.5,
      expert: 2.0
    };
    
    const hours = (baseHours[researchType] || 20) * (complexityMultiplier[complexity] || 1.0);
    return `${Math.round(hours)} hours`;
  }

  private estimateWordCount(researchType: string, complexity: string): number {
    const baseWords: Record<string, number> = {
      literature_review: 5000,
      systematic_review: 8000,
      meta_analysis: 6000,
      research_paper: 10000,
      policy_brief: 3000
    };
    
    const complexityMultiplier: Record<string, number> = {
      basic: 0.7,
      intermediate: 1.0,
      advanced: 1.3,
      expert: 1.6
    };
    
    return Math.round((baseWords[researchType] || 5000) * (complexityMultiplier[complexity] || 1.0));
  }

  private getCitationRequirements(researchType: string): string {
    const requirements: Record<string, string> = {
      literature_review: 'Minimum 30-50 high-quality academic sources, APA format',
      systematic_review: 'Comprehensive database search, 50+ peer-reviewed articles',
      meta_analysis: '20+ quantitative studies with statistical data',
      research_paper: '25-40 relevant sources supporting methodology and discussion',
      policy_brief: '15-25 authoritative sources including government and academic publications'
    };
    
    return requirements[researchType] || requirements.literature_review;
  }

  private getDefaultStructure(researchType: string): string[] {
    const structures: Record<string, string[]> = {
      literature_review: ['Introduction', 'Methodology', 'Literature Analysis', 'Discussion', 'Conclusion', 'References'],
      systematic_review: ['Abstract', 'Introduction', 'Methods', 'Results', 'Discussion', 'Conclusion', 'References'],
      meta_analysis: ['Abstract', 'Introduction', 'Methods', 'Statistical Analysis', 'Results', 'Discussion', 'References'],
      research_paper: ['Abstract', 'Introduction', 'Literature Review', 'Methodology', 'Results', 'Discussion', 'Conclusion', 'References'],
      policy_brief: ['Executive Summary', 'Problem Statement', 'Policy Analysis', 'Recommendations', 'Implementation', 'References']
    };
    
    return structures[researchType] || structures.literature_review;
  }
}

export const promptEnhancementService = new PromptEnhancementService();
