import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  DataVisualizationState,
  UploadedFile,
  DataAnalysisResult,
  QueryRequest,
  QueryResponse,
  VisualizationConfig,
  ResearchReport,
  ChatMessage,
  ChatSession
} from '../types';

interface DataVisualizationStore extends DataVisualizationState {
  // File management actions
  addFile: (file: UploadedFile) => void;
  updateFile: (fileId: string, updates: Partial<UploadedFile>) => void;
  removeFile: (fileId: string) => void;
  setCurrentFile: (file: UploadedFile | null) => void;

  // Analysis actions
  setAnalysisResult: (result: DataAnalysisResult) => void;
  setCurrentAnalysis: (analysis: DataAnalysisResult | null) => void;

  // Research report actions
  setCurrentReport: (report: ResearchReport | null) => void;
  setGeneratingReport: (isGenerating: boolean) => void;

  // Query actions
  addQuery: (query: QueryRequest) => void;
  updateQuery: (queryId: string, updates: Partial<QueryRequest>) => void;
  addQueryResponse: (response: QueryResponse) => void;

  // Chat actions
  addChatMessage: (message: ChatMessage) => void;
  updateChatMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  clearChatMessages: () => void;
  setCurrentChatSession: (session: ChatSession | null) => void;
  setChatGenerating: (isGenerating: boolean) => void;

  // UI state actions
  setActiveTab: (tab: DataVisualizationState['activeTab']) => void;
  setSelectedVisualization: (vizId: string | null) => void;
  setUploading: (isUploading: boolean) => void;
  setAnalyzing: (isAnalyzing: boolean) => void;
  setQuerying: (isQuerying: boolean) => void;

  // Error handling
  addError: (error: string) => void;
  clearErrors: () => void;
  removeError: (index: number) => void;

  // Utility actions
  reset: () => void;
  clearCurrentSession: () => void;
}

const initialState: DataVisualizationState = {
  uploadedFiles: [],
  currentFile: null,
  analysisResults: {},
  currentAnalysis: null,
  researchReports: {},
  currentReport: null,
  queries: [],
  queryResponses: {},
  chatMessages: [],
  currentChatSession: null,
  isChatGenerating: false,
  isUploading: false,
  isAnalyzing: false,
  isQuerying: false,
  isGeneratingReport: false,
  activeTab: 'upload',
  selectedVisualization: null,
  errors: []
};

export const useDataVisualizationStore = create<DataVisualizationStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // File management
      addFile: (file: UploadedFile) => {
        set((state) => ({
          uploadedFiles: [...state.uploadedFiles, file]
        }));
      },
      
      updateFile: (fileId: string, updates: Partial<UploadedFile>) => {
        set((state) => ({
          uploadedFiles: state.uploadedFiles.map(file =>
            file.id === fileId ? { ...file, ...updates } : file
          ),
          currentFile: state.currentFile?.id === fileId 
            ? { ...state.currentFile, ...updates } 
            : state.currentFile
        }));
      },
      
      removeFile: (fileId: string) => {
        set((state) => {
          const newFiles = state.uploadedFiles.filter(file => file.id !== fileId);
          const newAnalysisResults = { ...state.analysisResults };
          delete newAnalysisResults[fileId];
          
          return {
            uploadedFiles: newFiles,
            currentFile: state.currentFile?.id === fileId ? null : state.currentFile,
            analysisResults: newAnalysisResults,
            currentAnalysis: state.currentAnalysis?.fileId === fileId ? null : state.currentAnalysis
          };
        });
      },
      
      setCurrentFile: (file: UploadedFile | null) => {
        set({ currentFile: file });
        
        // Auto-load analysis if available
        if (file) {
          const analysis = get().analysisResults[file.id];
          if (analysis) {
            set({ currentAnalysis: analysis });
          }
        }
      },
      
      // Analysis management
      setAnalysisResult: (result: DataAnalysisResult) => {
        set((state) => ({
          analysisResults: {
            ...state.analysisResults,
            [result.fileId]: result
          }
        }));
      },
      
      setCurrentAnalysis: (analysis: DataAnalysisResult | null) => {
        set({ currentAnalysis: analysis });
      },

      // Research report management
      setCurrentReport: (report: ResearchReport | null) => {
        set({ currentReport: report });
      },

      setGeneratingReport: (isGenerating: boolean) => {
        set({ isGeneratingReport: isGenerating });
      },
      
      // Query management
      addQuery: (query: QueryRequest) => {
        set((state) => ({
          queries: [...state.queries, query]
        }));
      },
      
      updateQuery: (queryId: string, updates: Partial<QueryRequest>) => {
        set((state) => ({
          queries: state.queries.map(query =>
            query.id === queryId ? { ...query, ...updates } : query
          )
        }));
      },
      
      addQueryResponse: (response: QueryResponse) => {
        set((state) => ({
          queryResponses: {
            ...state.queryResponses,
            [response.requestId]: response
          }
        }));
      },

      // Chat management
      addChatMessage: (message: ChatMessage) => {
        set((state) => ({
          chatMessages: [...state.chatMessages, message]
        }));
      },

      updateChatMessage: (messageId: string, updates: Partial<ChatMessage>) => {
        set((state) => ({
          chatMessages: state.chatMessages.map(message =>
            message.id === messageId ? { ...message, ...updates } : message
          )
        }));
      },

      clearChatMessages: () => {
        set({ chatMessages: [] });
      },

      setCurrentChatSession: (session: ChatSession | null) => {
        set({ currentChatSession: session });
      },

      setChatGenerating: (isGenerating: boolean) => {
        set({ isChatGenerating: isGenerating });
      },

      // UI state management
      setActiveTab: (tab: DataVisualizationState['activeTab']) => {
        set({ activeTab: tab });
      },
      
      setSelectedVisualization: (vizId: string | null) => {
        set({ selectedVisualization: vizId });
      },
      
      setUploading: (isUploading: boolean) => {
        set({ isUploading });
      },
      
      setAnalyzing: (isAnalyzing: boolean) => {
        set({ isAnalyzing });
      },
      
      setQuerying: (isQuerying: boolean) => {
        set({ isQuerying });
      },
      
      // Error handling
      addError: (error: string) => {
        set((state) => ({
          errors: [...state.errors, error]
        }));
      },
      
      clearErrors: () => {
        set({ errors: [] });
      },
      
      removeError: (index: number) => {
        set((state) => ({
          errors: state.errors.filter((_, i) => i !== index)
        }));
      },
      
      // Utility functions
      reset: () => {
        set(initialState);
      },
      
      clearCurrentSession: () => {
        set({
          currentFile: null,
          currentAnalysis: null,
          selectedVisualization: null,
          activeTab: 'upload',
          errors: []
        });
      }
    }),
    {
      name: 'data-visualization-store'
    }
  )
);
