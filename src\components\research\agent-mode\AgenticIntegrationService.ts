/**
 * AgenticIntegrationService - Integrates the agentic system with the existing editor
 */

import { agenticEditingService } from './agentic-system/AgenticEditingService';
import { changeTrackingAdapter } from './agentic-system/ChangeTrackingAdapter';
import { AgenticEditingOptions, AgenticEditingResult, EditResult } from './agentic-system/types';

export class AgenticIntegrationService {
  private isInitialized = false;

  /**
   * Initialize the integration service
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🔗 Initializing Agentic Integration Service...');

    // Initialize the agentic editing service
    agenticEditingService.initialize();

    // Set up change tracking integration
    agenticEditingService.setChangeTrackingIntegration(changeTrackingAdapter);

    this.isInitialized = true;
    console.log('✅ Agentic Integration Service initialized');
  }

  /**
   * Execute agentic editing with full integration
   */
  async executeAgenticEdit(
    userRequest: string,
    documentContent: string,
    options: AgenticEditingOptions = {}
  ): Promise<AgenticEditingResult> {
    if (!this.isInitialized) {
      this.initialize();
    }

    console.log('🎯 Executing agentic edit with integration:', {
      userRequest,
      documentLength: documentContent.length,
      options
    });

    try {
      // Debug logging
      console.log('🔍 [AgenticIntegration] Starting execution with:', {
        userRequest,
        documentLength: documentContent.length,
        documentPreview: documentContent.substring(0, 100) + '...',
        options
      });

      // Ensure change tracking is enabled if requested
      if (options.enableChangeTracking) {
        const wasEnabled = changeTrackingAdapter.ensureChangeTrackingEnabled(documentContent);
        console.log(`📝 [AgenticIntegration] Change tracking ${wasEnabled ? 'started' : 'already enabled'}`);
      }

      // Validate compatibility with existing changes
      const compatibilityCheck = this.validateCompatibility(documentContent);
      if (!compatibilityCheck.isCompatible) {
        console.warn('⚠️ Compatibility issues detected:', compatibilityCheck.issues);
      }

      // Execute the agentic editing
      console.log('🚀 [AgenticIntegration] Executing agentic editing...');
      const result = await agenticEditingService.executeTargetedEdit(
        userRequest,
        documentContent,
        {
          ...options,
          enableChangeTracking: true,
          enableProgressUpdates: true
        }
      );

      console.log('✅ Agentic edit completed:', {
        success: result.success,
        changesCount: result.changes.length,
        hasChangeIds: !!result.changeIds,
        changeIds: result.changeIds,
        confidence: result.confidence,
        executionTime: result.executionTime,
        toolsUsed: result.toolsUsed,
        reasoning: result.reasoning,
        error: result.error
      });

      return result;

    } catch (error: any) {
      console.error('❌ Agentic edit failed:', error);
      throw error;
    }
  }

  /**
   * Preview agentic edits without applying them
   */
  async previewAgenticEdit(
    userRequest: string,
    documentContent: string,
    options: AgenticEditingOptions = {}
  ): Promise<AgenticEditingResult> {
    if (!this.isInitialized) {
      this.initialize();
    }

    const previewOptions = {
      ...options,
      enableChangeTracking: false,
      previewChanges: true,
      requirePreview: true
    };

    return await agenticEditingService.previewTargetedEdit(
      userRequest,
      documentContent,
      previewOptions
    );
  }

  /**
   * Apply previously previewed changes
   */
  applyPreviewedChanges(changeIds: string[]): void {
    agenticEditingService.applyPreviewedChanges(changeIds);
  }

  /**
   * Reject previously previewed changes
   */
  rejectPreviewedChanges(changeIds: string[]): void {
    agenticEditingService.rejectPreviewedChanges(changeIds);
  }

  /**
   * Get integration status
   */
  getStatus(): {
    isInitialized: boolean;
    agenticServiceStatus: any;
    changeTrackingEnabled: boolean;
    changeTrackingStats: any;
  } {
    return {
      isInitialized: this.isInitialized,
      agenticServiceStatus: agenticEditingService.getStatus(),
      changeTrackingEnabled: changeTrackingAdapter.isChangeTrackingEnabled(),
      changeTrackingStats: changeTrackingAdapter.getChangeStatistics()
    };
  }

  /**
   * Subscribe to agentic service events
   */
  onProgressUpdate(callback: (update: any) => void): () => void {
    agenticEditingService.on('progress_update', callback);
    return () => agenticEditingService.off('progress_update', callback);
  }

  /**
   * Subscribe to workflow completion events
   */
  onWorkflowCompleted(callback: (result: any) => void): () => void {
    agenticEditingService.on('workflow_completed', callback);
    return () => agenticEditingService.off('workflow_completed', callback);
  }

  /**
   * Subscribe to workflow failure events
   */
  onWorkflowFailed(callback: (error: any) => void): () => void {
    agenticEditingService.on('workflow_failed', callback);
    return () => agenticEditingService.off('workflow_failed', callback);
  }

  /**
   * Validate compatibility with existing changes
   */
  private validateCompatibility(documentContent: string): {
    isCompatible: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check if change tracking is in a good state
    const state = changeTrackingAdapter.getChangeTrackingState();
    
    if (state.changes.length > 10) {
      warnings.push('Many pending changes exist - consider reviewing them first');
    }

    if (state.viewMode === 'diff') {
      warnings.push('Currently in diff view mode - may affect editing experience');
    }

    // Check document size
    if (documentContent.length > 50000) {
      warnings.push('Large document - processing may take longer');
    }

    if (documentContent.length < 100) {
      issues.push('Document too small for meaningful agentic editing');
    }

    return {
      isCompatible: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * Create a React hook for using the agentic integration
   */
  createHook() {
    return {
      executeAgenticEdit: this.executeAgenticEdit.bind(this),
      previewAgenticEdit: this.previewAgenticEdit.bind(this),
      applyPreviewedChanges: this.applyPreviewedChanges.bind(this),
      rejectPreviewedChanges: this.rejectPreviewedChanges.bind(this),
      getStatus: this.getStatus.bind(this),
      onProgressUpdate: this.onProgressUpdate.bind(this),
      onWorkflowCompleted: this.onWorkflowCompleted.bind(this),
      onWorkflowFailed: this.onWorkflowFailed.bind(this)
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    agenticEditingService.cleanup();
    console.log('🧹 Agentic Integration Service cleaned up');
  }
}

// Create singleton instance
export const agenticIntegrationService = new AgenticIntegrationService();

/**
 * React hook for using agentic editing in components
 */
export function useAgenticEditing() {
  return agenticIntegrationService.createHook();
}
