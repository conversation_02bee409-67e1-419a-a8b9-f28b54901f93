import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertTriangle,
  Loader2,
  File,
  Database,
  CloudUpload,
  Sparkles,
  BarChart3,
  Brain
} from "lucide-react";
import { FileProcessingService } from '../services/file-processing.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { SUPPORTED_FILE_TYPES, FILE_SIZE_LIMITS } from '../types';
import { DATA_VIZ_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';

interface EnhancedFileUploaderProps {
  onFileProcessed?: (fileId: string) => void;
  className?: string;
}

export const EnhancedFileUploader: React.FC<EnhancedFileUploaderProps> = ({
  onFileProcessed,
  className = ""
}) => {
  const {
    addFile,
    updateFile,
    setCurrentFile,
    setUploading,
    addError,
    isUploading
  } = useDataVisualizationStore();

  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingFile, setProcessingFile] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          addError(`${file.name}: ${ERROR_MESSAGES.FILE_TOO_LARGE}`);
        } else if (error.code === 'file-invalid-type') {
          addError(`${file.name}: ${ERROR_MESSAGES.UNSUPPORTED_FORMAT}`);
        } else {
          addError(`${file.name}: ${error.message}`);
        }
      });
    });

    // Process accepted files
    for (const file of acceptedFiles) {
      await processFile(file);
    }
  }, [addError]);

  const processFile = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // Validate file first
      const validation = FileProcessingService.validateFile(file);
      
      if (!validation.isValid) {
        validation.errors.forEach(error => addError(error));
        return;
      }

      // Show warnings if any
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });

      // Create initial file entry
      const initialFile = {
        id: `temp-${Date.now()}`,
        name: file.name,
        size: file.size,
        type: file.type,
        data: [],
        headers: [],
        uploadedAt: new Date(),
        status: 'uploading' as const
      };

      addFile(initialFile);
      setProcessingFile(initialFile.id);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      // Process the file
      const processedFile = await FileProcessingService.processFile(file);
      
      // Update with processed data
      updateFile(initialFile.id, {
        ...processedFile,
        id: initialFile.id // Keep the same ID
      });

      setUploadProgress(100);
      setCurrentFile({ ...processedFile, id: initialFile.id });
      
      toast.success(SUCCESS_MESSAGES.FILE_UPLOADED);
      
      if (onFileProcessed) {
        onFileProcessed(initialFile.id);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      addError(errorMessage);
      toast.error(errorMessage);
      
      if (processingFile) {
        updateFile(processingFile, {
          status: 'error',
          error: errorMessage
        });
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setProcessingFile(null);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxSize: FILE_SIZE_LIMITS.MAX_FILE_SIZE,
    multiple: false
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Hero Upload Section */}
      <Card className="border-0 shadow-2xl bg-gradient-to-br from-white to-blue-50/50 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5"></div>
        <CardHeader className="relative pb-4">
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <CloudUpload className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Upload Your Data
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Transform your data into beautiful insights with AI-powered analysis
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="relative">
          <div
            {...getRootProps()}
            className={`
              relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-300
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50/50 scale-[1.02]' 
                : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50/30'
              }
              ${isUploading ? 'pointer-events-none opacity-50' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <div className="flex flex-col items-center space-y-6">
              {isUploading ? (
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full animate-ping"></div>
                  <Loader2 className="h-16 w-16 text-blue-500 animate-spin relative z-10" />
                </div>
              ) : (
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                  <div className="relative bg-gradient-to-r from-blue-500 to-purple-500 p-4 rounded-full">
                    <Upload className="h-12 w-12 text-white" />
                  </div>
                </div>
              )}
              
              <div className="space-y-3">
                <h3 className="text-xl font-semibold text-gray-900">
                  {isDragActive
                    ? "Drop your file here"
                    : "Drag & drop your data file here"
                  }
                </h3>
                <p className="text-gray-600">
                  or click to browse and select from your computer
                </p>
                <div className="flex items-center gap-2 justify-center text-sm text-gray-500">
                  <span>Supports CSV, Excel files up to {formatFileSize(FILE_SIZE_LIMITS.MAX_FILE_SIZE)}</span>
                </div>
              </div>

              {!isUploading && (
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <File className="h-5 w-5 mr-2" />
                  Choose File
                </Button>
              )}
            </div>
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="mt-6 space-y-3">
              <div className="flex justify-between text-sm font-medium">
                <span className="text-blue-600">Processing your data...</span>
                <span className="text-blue-600">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full h-2" />
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Sparkles className="h-4 w-4 text-blue-500" />
                <span>Analyzing file structure and preparing for insights</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Features Preview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-blue-900 mb-2">AI-Powered Analysis</h3>
            <p className="text-sm text-blue-700">
              Advanced machine learning algorithms analyze your data patterns and generate intelligent insights
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-green-900 mb-2">Beautiful Visualizations</h3>
            <p className="text-sm text-green-700">
              Interactive charts and graphs that make your data story come alive with stunning visual appeal
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-purple-900 mb-2">Smart Insights</h3>
            <p className="text-sm text-purple-700">
              Ask questions in natural language and get instant answers with relevant visualizations
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Supported Formats */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            Supported File Formats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(SUPPORTED_FILE_TYPES).map(([mimeType, description]) => (
            <div key={mimeType} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-gray-500" />
                <div>
                  <span className="font-medium text-gray-900">{description}</span>
                  <p className="text-xs text-gray-500">
                    {mimeType === 'text/csv' && 'Comma-separated values with headers'}
                    {mimeType.includes('excel') && 'Microsoft Excel spreadsheet format'}
                  </p>
                </div>
              </div>
              <Badge variant="secondary" className="text-xs font-mono">
                {mimeType.split('/').pop()?.toUpperCase()}
              </Badge>
            </div>
          ))}
          
          <Alert className="border-blue-200 bg-blue-50">
            <AlertTriangle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-sm text-blue-800">
              <strong>Requirements:</strong> Files must contain at least {DATA_VIZ_CONFIG.MIN_ROWS_FOR_ANALYSIS} rows of data. 
              The first row should contain column headers for best results.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};
