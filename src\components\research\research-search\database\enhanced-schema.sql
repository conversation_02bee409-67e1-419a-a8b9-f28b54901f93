-- Enhanced Database Schema for Academic Research System
-- This extends the existing research_search tables with comprehensive academic research functionality

-- =====================================================
-- ENHANCED RESEARCH SESSIONS TABLE
-- =====================================================

-- Add new columns to existing research_search_sessions table
ALTER TABLE research_search_sessions 
ADD COLUMN IF NOT EXISTS research_type VARCHAR(50) DEFAULT 'literature_review',
ADD COLUMN IF NOT EXISTS estimated_duration_hours INTEGER DEFAULT 2,
ADD COLUMN IF NOT EXISTS target_word_count INTEGER DEFAULT 5000,
ADD COLUMN IF NOT EXISTS citation_style VARCHAR(20) DEFAULT 'apa',
ADD COLUMN IF NOT EXISTS academic_level VARCHAR(20) DEFAULT 'graduate',
ADD COLUMN IF NOT EXISTS research_status VARCHAR(30) DEFAULT 'planning',
ADD COLUMN IF NOT EXISTS completion_percentage INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS total_citations INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS academic_sources INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS government_sources INTEGER DEFAULT 0;

-- =====================================================
-- RESEARCH OUTLINES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_outlines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    research_type VARCHAR(50) NOT NULL DEFAULT 'literature_review',
    total_points INTEGER NOT NULL DEFAULT 10,
    estimated_word_count INTEGER DEFAULT 5000,
    estimated_time_hours INTEGER DEFAULT 2,
    status VARCHAR(30) DEFAULT 'draft', -- draft, approved, in_progress, completed
    outline_data JSONB NOT NULL, -- Complete outline structure
    keywords TEXT[],
    target_audience TEXT,
    abstract TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RESEARCH POINTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    outline_id UUID REFERENCES research_outlines(id) ON DELETE CASCADE,
    point_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status VARCHAR(30) DEFAULT 'pending', -- pending, researching, completed, error
    search_queries TEXT[],
    content TEXT,
    word_count INTEGER DEFAULT 0,
    citation_count INTEGER DEFAULT 0,
    quality_score DECIMAL(3,2) DEFAULT 0.0,
    ai_assistant_id TEXT,
    ai_assistant_name TEXT,
    subpoints JSONB, -- Array of subpoint objects
    key_findings TEXT[],
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RESEARCH SOURCES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    point_id UUID REFERENCES research_points(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    url TEXT NOT NULL,
    domain TEXT NOT NULL,
    snippet TEXT,
    content TEXT,
    published_date DATE,
    authors TEXT[],
    doi TEXT,
    journal TEXT,
    volume TEXT,
    issue TEXT,
    pages TEXT,
    source_type VARCHAR(30) DEFAULT 'web', -- academic, web, news, book, report, government
    quality_score DECIMAL(3,2) DEFAULT 0.5,
    relevance_score DECIMAL(3,2) DEFAULT 0.5,
    is_peer_reviewed BOOLEAN DEFAULT FALSE,
    is_government BOOLEAN DEFAULT FALSE,
    is_academic BOOLEAN DEFAULT FALSE,
    citation_count INTEGER DEFAULT 0,
    impact_factor DECIMAL(5,3),
    institution_affiliation TEXT,
    methodology TEXT,
    data_quality VARCHAR(20) DEFAULT 'medium', -- high, medium, low
    recency VARCHAR(20) DEFAULT 'recent', -- current, recent, dated
    search_query TEXT,
    tavily_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RESEARCH CITATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_citations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    point_id UUID REFERENCES research_points(id) ON DELETE CASCADE,
    source_id UUID REFERENCES research_sources(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    citation_text TEXT NOT NULL,
    citation_type VARCHAR(30) DEFAULT 'reference', -- direct_quote, paraphrase, reference, data
    position_in_text INTEGER,
    context_text TEXT,
    formatted_citation TEXT,
    citation_key TEXT, -- e.g., "Smith2023"
    in_text_format TEXT, -- e.g., "(Smith, 2023)"
    section_id TEXT,
    page_number TEXT,
    confidence_score DECIMAL(3,2) DEFAULT 0.8,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RESEARCH REFERENCES TABLE (Bibliography)
-- =====================================================

CREATE TABLE IF NOT EXISTS research_references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    citation_key TEXT NOT NULL, -- e.g., "Smith2023"
    formatted_apa TEXT,
    formatted_mla TEXT,
    formatted_chicago TEXT,
    formatted_harvard TEXT,
    source_data JSONB NOT NULL, -- Complete source information
    usage_count INTEGER DEFAULT 0,
    sections_used TEXT[], -- Which sections use this reference
    quality_assessment JSONB, -- Quality metrics and scores
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(session_id, citation_key)
);

-- =====================================================
-- RESEARCH PROGRESS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    point_id UUID REFERENCES research_points(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stage VARCHAR(50) NOT NULL, -- outline, searching, analyzing, writing, reviewing, completed
    progress_percentage INTEGER DEFAULT 0,
    current_task TEXT,
    ai_assistant_id TEXT,
    ai_assistant_name TEXT,
    status VARCHAR(30) DEFAULT 'active', -- active, paused, completed, error
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    error_message TEXT,
    metadata JSONB, -- Additional progress data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RESEARCH REPORTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS research_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES research_search_sessions(id) ON DELETE CASCADE,
    outline_id UUID REFERENCES research_outlines(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    formatted_content TEXT, -- Clean formatted version
    word_count INTEGER DEFAULT 0,
    citation_count INTEGER DEFAULT 0,
    reference_count INTEGER DEFAULT 0,
    quality_metrics JSONB, -- Quality assessment data
    export_formats JSONB, -- Available export formats and metadata
    version INTEGER DEFAULT 1,
    is_final BOOLEAN DEFAULT FALSE,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Research Outlines
CREATE INDEX IF NOT EXISTS idx_research_outlines_session_id ON research_outlines(session_id);
CREATE INDEX IF NOT EXISTS idx_research_outlines_user_id ON research_outlines(user_id);
CREATE INDEX IF NOT EXISTS idx_research_outlines_status ON research_outlines(status);

-- Research Points
CREATE INDEX IF NOT EXISTS idx_research_points_outline_id ON research_points(outline_id);
CREATE INDEX IF NOT EXISTS idx_research_points_status ON research_points(status);
CREATE INDEX IF NOT EXISTS idx_research_points_point_number ON research_points(point_number);

-- Research Sources
CREATE INDEX IF NOT EXISTS idx_research_sources_session_id ON research_sources(session_id);
CREATE INDEX IF NOT EXISTS idx_research_sources_point_id ON research_sources(point_id);
CREATE INDEX IF NOT EXISTS idx_research_sources_user_id ON research_sources(user_id);
CREATE INDEX IF NOT EXISTS idx_research_sources_type ON research_sources(source_type);
CREATE INDEX IF NOT EXISTS idx_research_sources_quality ON research_sources(quality_score);
CREATE INDEX IF NOT EXISTS idx_research_sources_doi ON research_sources(doi);

-- Research Citations
CREATE INDEX IF NOT EXISTS idx_research_citations_session_id ON research_citations(session_id);
CREATE INDEX IF NOT EXISTS idx_research_citations_point_id ON research_citations(point_id);
CREATE INDEX IF NOT EXISTS idx_research_citations_source_id ON research_citations(source_id);
CREATE INDEX IF NOT EXISTS idx_research_citations_key ON research_citations(citation_key);

-- Research References
CREATE INDEX IF NOT EXISTS idx_research_references_session_id ON research_references(session_id);
CREATE INDEX IF NOT EXISTS idx_research_references_key ON research_references(citation_key);

-- Research Progress
CREATE INDEX IF NOT EXISTS idx_research_progress_session_id ON research_progress(session_id);
CREATE INDEX IF NOT EXISTS idx_research_progress_point_id ON research_progress(point_id);
CREATE INDEX IF NOT EXISTS idx_research_progress_stage ON research_progress(stage);

-- Research Reports
CREATE INDEX IF NOT EXISTS idx_research_reports_session_id ON research_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_research_reports_user_id ON research_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_research_reports_final ON research_reports(is_final);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE research_outlines ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_citations ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_references ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_reports ENABLE ROW LEVEL SECURITY;

-- Research Outlines Policies
CREATE POLICY "Users can view their own research outlines" ON research_outlines
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research outlines" ON research_outlines
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research outlines" ON research_outlines
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research outlines" ON research_outlines
    FOR DELETE USING (auth.uid() = user_id);

-- Research Points Policies
CREATE POLICY "Users can view research points for their outlines" ON research_points
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM research_outlines
            WHERE research_outlines.id = research_points.outline_id
            AND research_outlines.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create research points for their outlines" ON research_points
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM research_outlines
            WHERE research_outlines.id = research_points.outline_id
            AND research_outlines.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update research points for their outlines" ON research_points
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM research_outlines
            WHERE research_outlines.id = research_points.outline_id
            AND research_outlines.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete research points for their outlines" ON research_points
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM research_outlines
            WHERE research_outlines.id = research_points.outline_id
            AND research_outlines.user_id = auth.uid()
        )
    );

-- Research Sources Policies
CREATE POLICY "Users can view their own research sources" ON research_sources
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research sources" ON research_sources
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research sources" ON research_sources
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research sources" ON research_sources
    FOR DELETE USING (auth.uid() = user_id);

-- Research Citations Policies
CREATE POLICY "Users can view their own research citations" ON research_citations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research citations" ON research_citations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research citations" ON research_citations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research citations" ON research_citations
    FOR DELETE USING (auth.uid() = user_id);

-- Research References Policies
CREATE POLICY "Users can view their own research references" ON research_references
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research references" ON research_references
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research references" ON research_references
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research references" ON research_references
    FOR DELETE USING (auth.uid() = user_id);

-- Research Progress Policies
CREATE POLICY "Users can view their own research progress" ON research_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research progress" ON research_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research progress" ON research_progress
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research progress" ON research_progress
    FOR DELETE USING (auth.uid() = user_id);

-- Research Reports Policies
CREATE POLICY "Users can view their own research reports" ON research_reports
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own research reports" ON research_reports
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research reports" ON research_reports
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research reports" ON research_reports
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_research_outlines_updated_at BEFORE UPDATE ON research_outlines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_points_updated_at BEFORE UPDATE ON research_points FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_sources_updated_at BEFORE UPDATE ON research_sources FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_citations_updated_at BEFORE UPDATE ON research_citations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_references_updated_at BEFORE UPDATE ON research_references FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_progress_updated_at BEFORE UPDATE ON research_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_research_reports_updated_at BEFORE UPDATE ON research_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
