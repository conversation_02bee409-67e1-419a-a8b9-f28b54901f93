import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  BookOpen,
  Target,
  Lightbulb,
  Network,
  Calendar,
  Database,
  Search,
  BarChart3,
  Brain,
  Settings,
  Download,
  RefreshCw,
  Plus,
  Filter,
  ArrowRight,
  Star,
  TrendingUp,
  Users,
  Clock,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";

import { MultiDocumentUploader } from './components/MultiDocumentUploader';
import { DocumentLibrary } from './components/DocumentLibrary';
import { EnhancedDocumentLibrary } from './components/EnhancedDocumentLibrary';
import { LiteratureReviewGenerator } from './components/LiteratureReviewGenerator';
import { ResearchGapAnalyzer } from './components/ResearchGapAnalyzer';
import { HypothesisGenerator } from './components/HypothesisGenerator';
// Removed unused component imports
import { AIModelSelector } from './components/AIModelSelector';
import { ProgressTracker } from './components/ProgressTracker';
import { AIQuestionBubble } from './components/AIQuestionBubble';
import { EnhancedAIQuestionBubble } from './components/EnhancedAIQuestionBubble';

import { 
  ResearchAnalysisState, 
  ResearchDocument, 
  GenerationProgress,
  AIGenerationOptions 
} from './types';
import { researchAnalysisService } from './services/research-analysis.service';

export function ResearchAnalysisPlatform() {
  const [state, setState] = useState<ResearchAnalysisState>({
    documents: [],
    selectedDocuments: [],
    currentView: 'dashboard',
    isProcessing: false,
    processingMessage: '',
    aiSettings: {
      selectedModel: 'google/gemini-2.5-flash',
      options: {
        temperature: 0.3,
        maxTokens: 4000,
        topP: 0.9
      }
    },
    filters: {
      years: [2020, 2025],
      authors: [],
      journals: [],
      tags: [],
      categories: []
    },
    searchQuery: '',
    searchResults: []
  });

  // Clear any old navigation state on component mount
  useEffect(() => {
    // Reset to dashboard if we're on an invalid view
    const validViews: Array<typeof state.currentView> = ['dashboard', 'upload', 'library', 'literature-review', 'gaps', 'hypotheses', 'methodology', 'citations', 'planner', 'knowledge-base', 'data-extraction'];
    if (!validViews.includes(state.currentView)) {
      setState(prev => ({ ...prev, currentView: 'dashboard' }));
    }
  }, []);

  const [analysisResults, setAnalysisResults] = useState({
    literatureReviews: [],
    gapAnalyses: [],
    hypotheses: []
  });

  // Handle document upload
  const handleDocumentUpload = useCallback(async (files: File[]) => {
    setState(prev => ({ 
      ...prev, 
      isProcessing: true, 
      processingMessage: 'Processing documents...' 
    }));

    try {
      const documents = await researchAnalysisService.processDocuments(
        files,
        (progress) => {
          setState(prev => ({ 
            ...prev, 
            generationProgress: progress,
            processingMessage: progress.message 
          }));
        }
      );

      setState(prev => ({
        ...prev,
        documents: [...prev.documents, ...documents],
        isProcessing: false,
        generationProgress: undefined
      }));

      toast.success(`Successfully processed ${documents.length} documents!`);
    } catch (error) {
      console.error('Document upload error:', error);
      setState(prev => ({ 
        ...prev, 
        isProcessing: false,
        generationProgress: undefined 
      }));
      toast.error('Failed to process documents. Please try again.');
    }
  }, []);

  // Handle document selection
  const handleDocumentSelection = useCallback((documentIds: string[]) => {
    console.log('ResearchAnalysisPlatform - Document selection updated:', documentIds.length, 'documents');
    setState(prev => ({ ...prev, selectedDocuments: documentIds }));
  }, []);

  // Handle view change
  const handleViewChange = useCallback((view: string) => {
    setState(prev => ({ ...prev, currentView: view as any }));
  }, []);

  // Handle AI settings change
  const handleAISettingsChange = useCallback((settings: { selectedModel: string; options: AIGenerationOptions }) => {
    setState(prev => ({ ...prev, aiSettings: settings }));
    toast.success('AI settings updated successfully!');
  }, []);

  // Get filtered documents
  const filteredDocuments = state.documents.filter(doc => {
    const matchesSearch = !state.searchQuery || 
      doc.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
      doc.authors.some(author => author.toLowerCase().includes(state.searchQuery.toLowerCase())) ||
      doc.keywords.some(keyword => keyword.toLowerCase().includes(state.searchQuery.toLowerCase()));
    
    const matchesYear = doc.publicationYear >= state.filters.years[0] && 
                       doc.publicationYear <= state.filters.years[1];
    
    const matchesAuthors = state.filters.authors.length === 0 ||
                          doc.authors.some(author => state.filters.authors.includes(author));
    
    const matchesTags = state.filters.tags.length === 0 ||
                       doc.tags.some(tag => state.filters.tags.includes(tag));

    return matchesSearch && matchesYear && matchesAuthors && matchesTags;
  });

  // Get selected documents
  const selectedDocuments = state.documents.filter(doc => 
    state.selectedDocuments.includes(doc.id)
  );

  // Dashboard statistics
  const stats = {
    totalDocuments: state.documents.length,
    readyDocuments: state.documents.filter(d => d.status === 'ready').length,
    processingDocuments: state.documents.filter(d => d.status === 'processing').length,
    errorDocuments: state.documents.filter(d => d.status === 'error').length,
    totalAuthors: <AUTHORS>
    averageYear: Math.round(state.documents.reduce((sum, doc) => sum + doc.publicationYear, 0) / state.documents.length) || 0,
    totalKeywords: [...new Set(state.documents.flatMap(d => d.keywords))].length,
    literatureReviews: analysisResults.literatureReviews.length,
    researchGaps: analysisResults.gapAnalyses.reduce((sum, analysis) => sum + analysis.gaps.length, 0),
    hypotheses: analysisResults.hypotheses.length
  };

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'blue' },
    { id: 'upload', label: 'Upload Documents', icon: Upload, color: 'green' },
    { id: 'library', label: 'Research Library', icon: BookOpen, color: 'indigo' },
    { id: 'literature-review', label: 'Literature Review', icon: FileText, color: 'purple' },
    { id: 'gaps', label: 'Research Gaps', icon: Target, color: 'orange' },
    { id: 'hypotheses', label: 'Hypotheses', icon: Lightbulb, color: 'yellow' }
  ];

  // Render navigation
  const renderNavigation = () => (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
      {navigationItems.map((item) => {
        const isActive = state.currentView === item.id;
        const Icon = item.icon;
        
        return (
          <Button
            key={item.id}
            variant={isActive ? "default" : "ghost"}
            className={`h-24 flex flex-col items-center justify-center gap-2 transition-all duration-300 rounded-xl shadow-sm ${
              isActive 
                ? `bg-gradient-to-br from-${item.color}-500 to-${item.color}-600 text-white hover:shadow-md` 
                : `hover:bg-${item.color}-50 hover:text-${item.color}-600 border border-transparent hover:border-${item.color}-200 hover:shadow-sm`
            }`}
            onClick={() => handleViewChange(item.id)}
          >
            <Icon className={`h-7 w-7 ${isActive ? 'text-white/90' : ''}`} />
            <span className={`text-sm font-medium text-center ${isActive ? 'text-white/90' : ''}`}>{item.label}</span>
          </Button>
        );
      })}
    </div>
  );

  // Render dashboard overview
  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Documents</p>
                <p className="text-3xl font-bold text-blue-900">{stats.totalDocuments}</p>
              </div>
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center gap-2 text-sm">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-blue-700">{stats.readyDocuments} ready</span>
              {stats.processingDocuments > 0 && (
                <>
                  <RefreshCw className="h-4 w-4 text-orange-500 animate-spin" />
                  <span className="text-orange-700">{stats.processingDocuments} processing</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Literature Reviews</p>
                <p className="text-3xl font-bold text-purple-900">{stats.literatureReviews}</p>
              </div>
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center gap-2 text-sm text-purple-700">
              <TrendingUp className="h-4 w-4" />
              <span>Generated this month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Research Gaps</p>
                <p className="text-3xl font-bold text-orange-900">{stats.researchGaps}</p>
              </div>
              <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center">
                <Target className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center gap-2 text-sm text-orange-700">
              <Star className="h-4 w-4" />
              <span>Opportunities identified</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Hypotheses</p>
                <p className="text-3xl font-bold text-green-900">{stats.hypotheses}</p>
              </div>
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <Lightbulb className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center gap-2 text-sm text-green-700">
              <Zap className="h-4 w-4" />
              <span>Ready for testing</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Plus className="h-5 w-5 text-blue-500" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button 
              className="h-16 flex items-center gap-3 bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
              variant="outline"
              onClick={() => handleViewChange('upload')}
            >
              <Upload className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Upload Documents</div>
                <div className="text-xs text-blue-600">Add new research papers</div>
              </div>
            </Button>

            <Button 
              className="h-16 flex items-center gap-3 bg-indigo-50 hover:bg-indigo-100 text-indigo-700 border-indigo-200"
              variant="outline"
              onClick={() => handleViewChange('library')}
              disabled={state.documents.length === 0}
            >
              <BookOpen className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Research Library</div>
                <div className="text-xs text-indigo-600">Explore your documents</div>
              </div>
            </Button>

            <Button 
              className="h-16 flex items-center gap-3 bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200"
              variant="outline"
              onClick={() => handleViewChange('literature-review')}
              disabled={selectedDocuments.length === 0}
            >
              <FileText className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Generate Review</div>
                <div className="text-xs text-purple-600">Create literature review</div>
              </div>
            </Button>

            <Button 
              className="h-16 flex items-center gap-3 bg-orange-50 hover:bg-orange-100 text-orange-700 border-orange-200"
              variant="outline"
              onClick={() => handleViewChange('gaps')}
              disabled={selectedDocuments.length === 0}
            >
              <Target className="h-5 w-5" />
              <div className="text-left">
                <div className="font-medium">Find Gaps</div>
                <div className="text-xs text-orange-600">Identify research opportunities</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Clock className="h-5 w-5 text-gray-500" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {state.documents.slice(0, 5).map((doc) => (
              <div key={doc.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  doc.status === 'ready' ? 'bg-green-100 text-green-600' :
                  doc.status === 'processing' ? 'bg-orange-100 text-orange-600' :
                  'bg-red-100 text-red-600'
                }`}>
                  {doc.status === 'ready' ? <CheckCircle className="h-5 w-5" /> :
                   doc.status === 'processing' ? <RefreshCw className="h-5 w-5 animate-spin" /> :
                   <AlertTriangle className="h-5 w-5" />}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{doc.title}</div>
                  <div className="text-sm text-gray-500">
                    {doc.authors.join(', ')} • {doc.publicationYear}
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  {new Date(doc.uploadedAt).toLocaleDateString()}
                </div>
              </div>
            ))}
            
            {state.documents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No documents uploaded yet</p>
                <p className="text-sm">Upload your first research paper to get started</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Research Analysis Platform</h1>
                <p className="text-sm text-gray-600">
                  AI-powered research analysis and literature review generation
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <AIModelSelector 
                selectedModel={state.aiSettings.selectedModel}
                options={state.aiSettings.options}
                onSettingsChange={handleAISettingsChange}
              />
              
              {state.selectedDocuments.length > 0 && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  {state.selectedDocuments.length} selected
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Navigation */}
        {renderNavigation()}

        {/* Processing Progress */}
        {state.isProcessing && state.generationProgress && (
          <Card className="mb-8 border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <ProgressTracker progress={state.generationProgress} />
            </CardContent>
          </Card>
        )}

        {/* Content Area */}
        <div className="space-y-8">
          {state.currentView === 'dashboard' && renderDashboard()}
          
          {state.currentView === 'upload' && (
            <div className="max-w-4xl mx-auto">
              <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <div className="p-3 bg-green-500 rounded-xl">
                      <Upload className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-3xl font-bold text-green-900">Upload Research Documents</CardTitle>
                      <p className="text-green-700 mt-2">Add your research papers for AI-powered analysis</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="grid md:grid-cols-3 gap-6 text-center">
                      <div className="p-4 bg-white rounded-lg border border-green-200">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <FileText className="h-6 w-6 text-green-600" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Smart Processing</h3>
                        <p className="text-sm text-gray-600">AI extracts key information, findings, and methodology</p>
                      </div>
                      <div className="p-4 bg-white rounded-lg border border-green-200">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Brain className="h-6 w-6 text-green-600" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Deep Analysis</h3>
                        <p className="text-sm text-gray-600">Comprehensive understanding of research content</p>
                      </div>
                      <div className="p-4 bg-white rounded-lg border border-green-200">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <BookOpen className="h-6 w-6 text-green-600" />
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-2">Ready for Research</h3>
                        <p className="text-sm text-gray-600">Instantly available in your research library</p>
                      </div>
                    </div>
                    
                    <MultiDocumentUploader onUpload={handleDocumentUpload} />
                    
                    {state.documents.length > 0 && (
                      <div className="text-center pt-6 border-t border-green-200">
                        <p className="text-green-700 mb-4">
                          Great! You have {state.documents.length} document{state.documents.length > 1 ? 's' : ''} uploaded.
                        </p>
                        <Button 
                          onClick={() => handleViewChange('library')}
                          className="bg-green-600 hover:bg-green-700 text-white"
                          size="lg"
                        >
                          <BookOpen className="h-5 w-5 mr-2" />
                          View Research Library
                          <ArrowRight className="h-5 w-5 ml-2" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          
          {state.currentView === 'library' && (
            <EnhancedDocumentLibrary 
              documents={filteredDocuments}
              selectedDocuments={state.selectedDocuments}
              onSelectionChange={handleDocumentSelection}
              onSearch={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
              onFilter={(filters) => setState(prev => ({ ...prev, filters }))}
            />
          )}
          
          {state.currentView === 'literature-review' && (
            <LiteratureReviewGenerator 
              documents={selectedDocuments}
              onGenerate={(review) => {
                setAnalysisResults(prev => ({
                  ...prev,
                  literatureReviews: [...prev.literatureReviews, review]
                }));
                toast.success('Literature review generated!');
              }}
              aiSettings={state.aiSettings}
            />
          )}
          
          {state.currentView === 'gaps' && (
            <ResearchGapAnalyzer 
              documents={selectedDocuments}
              onAnalyze={(analysis) => {
                setAnalysisResults(prev => ({
                  ...prev,
                  gapAnalyses: [...prev.gapAnalyses, analysis]
                }));
                toast.success('Research gap analysis completed!');
              }}
              aiSettings={state.aiSettings}
            />
          )}
          
          {state.currentView === 'hypotheses' && (
            <HypothesisGenerator 
              gaps={analysisResults.gapAnalyses.flatMap(a => a.gaps)}
              documents={selectedDocuments}
              onGenerate={(hypotheses) => {
                setAnalysisResults(prev => ({
                  ...prev,
                  hypotheses: [...prev.hypotheses, ...hypotheses]
                }));
                toast.success(`Generated ${hypotheses.length} hypotheses!`);
              }}
              aiSettings={state.aiSettings}
            />
          )}
        </div>
      </div>

      {/* Enhanced AI Question Bubble - Always visible and works with all documents */}
      <EnhancedAIQuestionBubble 
        documents={state.documents}
        selectedDocuments={state.selectedDocuments}
        onDocumentSelect={(documentId) => {
          const newSelection = state.selectedDocuments.includes(documentId)
            ? state.selectedDocuments.filter(id => id !== documentId)
            : [...state.selectedDocuments, documentId];
          setState(prev => ({ ...prev, selectedDocuments: newSelection }));
        }}
      />
    </div>
  );
}
