/**
 * Edges Settings Tab
 * Configure edge appearance and behavior
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SettingsTabProps, CurveStyle, ArrowShape } from '../../types';

const curveStyleOptions: { value: CurveStyle; label: string; description: string }[] = [
  { value: 'bezier', label: 'Bezier', description: 'Smooth curved lines' },
  { value: 'taxi', label: 'Taxi', description: 'Right-angled paths' },
  { value: 'round-taxi', label: 'Round Taxi', description: 'Right-angled with rounded corners' },
];

const arrowShapeOptions: { value: ArrowShape; label: string; description: string }[] = [
  { value: 'none', label: 'None', description: 'No arrow' },
  { value: 'triangle', label: 'Triangle', description: 'Standard triangular arrow' },
  { value: 'vee', label: 'Vee', description: 'V-shaped arrow' },
  { value: 'triangle-backcurve', label: 'Triangle Backcurve', description: 'Curved triangular arrow' },
  { value: 'circle', label: 'Circle', description: 'Circular endpoint' },
];

const EdgesTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const handleCurveStyleChange = (curveStyle: CurveStyle) => {
    onThemeChange({ curveStyle });
  };

  const handleEdgeColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onThemeChange({ edgeColor: e.target.value });
  };

  const handleEdgeWidthChange = (value: number[]) => {
    onThemeChange({ edgeWidth: value[0] });
  };

  const handleSourceArrowChange = (sourceArrowShape: ArrowShape) => {
    onThemeChange({ sourceArrowShape });
  };

  const handleTargetArrowChange = (targetArrowShape: ArrowShape) => {
    onThemeChange({ targetArrowShape });
  };

  const handleSourceDistanceChange = (value: number[]) => {
    onThemeChange({ sourceDistanceFromNode: value[0] });
  };

  const handleTargetDistanceChange = (value: number[]) => {
    onThemeChange({ targetDistanceFromNode: value[0] });
  };

  const handleArrowScaleChange = (value: number[]) => {
    onThemeChange({ arrowScale: value[0] });
  };

  const handleEdgeTextSizeChange = (value: number[]) => {
    onThemeChange({ edgeTextSize: value[0] });
  };

  const handleRotateEdgeLabelToggle = (checked: boolean) => {
    onThemeChange({ rotateEdgeLabel: checked });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Edge Style</CardTitle>
          <CardDescription>
            Configure the basic appearance of connections between nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Curve Style */}
          <div className="space-y-3">
            <Label htmlFor="curve-style">Curve Style</Label>
            <Select value={theme.curveStyle} onValueChange={handleCurveStyleChange}>
              <SelectTrigger id="curve-style">
                <SelectValue placeholder="Select curve style" />
              </SelectTrigger>
              <SelectContent>
                {curveStyleOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Edge Color */}
          <div className="space-y-3">
            <Label htmlFor="edge-color">Edge Color</Label>
            <div className="flex items-center gap-3">
              <div 
                className="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                style={{ backgroundColor: theme.edgeColor }}
                onClick={() => document.getElementById('edge-color-input')?.click()}
              />
              <Input
                id="edge-color-input"
                type="color"
                value={theme.edgeColor}
                onChange={handleEdgeColorChange}
                className="w-16 h-10 p-1 cursor-pointer"
              />
              <Input
                type="text"
                value={theme.edgeColor}
                onChange={handleEdgeColorChange}
                className="flex-1 font-mono text-sm"
              />
            </div>
          </div>

          {/* Edge Width */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="edge-width">Edge Width</Label>
              <Badge variant="outline">{theme.edgeWidth}px</Badge>
            </div>
            <Slider
              id="edge-width"
              min={1}
              max={10}
              step={0.5}
              value={[theme.edgeWidth]}
              onValueChange={handleEdgeWidthChange}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Arrow Heads</CardTitle>
          <CardDescription>
            Configure arrow shapes at the start and end of edges
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Source Arrow */}
          <div className="space-y-3">
            <Label htmlFor="source-arrow">Source Arrow (Start)</Label>
            <Select value={theme.sourceArrowShape} onValueChange={handleSourceArrowChange}>
              <SelectTrigger id="source-arrow">
                <SelectValue placeholder="Select source arrow" />
              </SelectTrigger>
              <SelectContent>
                {arrowShapeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Target Arrow */}
          <div className="space-y-3">
            <Label htmlFor="target-arrow">Target Arrow (End)</Label>
            <Select value={theme.targetArrowShape} onValueChange={handleTargetArrowChange}>
              <SelectTrigger id="target-arrow">
                <SelectValue placeholder="Select target arrow" />
              </SelectTrigger>
              <SelectContent>
                {arrowShapeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Arrow Scale */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="arrow-scale">Arrow Scale</Label>
              <Badge variant="outline">{theme.arrowScale.toFixed(1)}</Badge>
            </div>
            <Slider
              id="arrow-scale"
              min={0.5}
              max={3.0}
              step={0.1}
              value={[theme.arrowScale]}
              onValueChange={handleArrowScaleChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Scale factor for arrow size
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Edge Positioning</CardTitle>
          <CardDescription>
            Control spacing and positioning of edges relative to nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Source Distance */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="source-distance">Source Distance from Node</Label>
              <Badge variant="outline">{theme.sourceDistanceFromNode}px</Badge>
            </div>
            <Slider
              id="source-distance"
              min={0}
              max={20}
              step={1}
              value={[theme.sourceDistanceFromNode]}
              onValueChange={handleSourceDistanceChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Distance between edge start and source node
            </p>
          </div>

          {/* Target Distance */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="target-distance">Target Distance from Node</Label>
              <Badge variant="outline">{theme.targetDistanceFromNode}px</Badge>
            </div>
            <Slider
              id="target-distance"
              min={0}
              max={20}
              step={1}
              value={[theme.targetDistanceFromNode]}
              onValueChange={handleTargetDistanceChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Distance between edge end and target node
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Edge Labels</CardTitle>
          <CardDescription>
            Configure text labels that appear on edges
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Edge Text Size */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="edge-text-size">Text Size</Label>
              <Badge variant="outline">{theme.edgeTextSize}px</Badge>
            </div>
            <Slider
              id="edge-text-size"
              min={8}
              max={24}
              step={1}
              value={[theme.edgeTextSize]}
              onValueChange={handleEdgeTextSizeChange}
              className="w-full"
            />
          </div>

          {/* Rotate Edge Label */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="rotate-edge-label">Rotate Labels with Edge</Label>
              <Switch
                id="rotate-edge-label"
                checked={theme.rotateEdgeLabel}
                onCheckedChange={handleRotateEdgeLabelToggle}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {theme.rotateEdgeLabel 
                ? "Labels will rotate to match edge direction"
                : "Labels will remain horizontal"
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EdgesTab;
