import React, { useState, useEffect } from 'react';
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Sparkles, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  PenTool,
  FileText,
  BookOpen,
  Target
} from 'lucide-react';
import { GeneratedSection } from '../types';
import { TypingAnimation, PulsingDots, FloatingParticles } from './TypingAnimation';

interface EnhancedProgressIndicatorProps {
  sections: GeneratedSection[];
  isGenerating: boolean;
  currentSection?: GeneratedSection;
  className?: string;
}

export const EnhancedProgressIndicator: React.FC<EnhancedProgressIndicatorProps> = ({
  sections,
  isGenerating,
  currentSection,
  className = ""
}) => {
  const completedSections = sections.filter(s => s.status === 'completed');
  const totalSections = sections.length;
  const progressPercentage = (completedSections.length / totalSections) * 100;
  
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progressPercentage);
    }, 100);
    return () => clearTimeout(timer);
  }, [progressPercentage]);

  useEffect(() => {
    if (progressPercentage === 100 && !isGenerating) {
      setShowCelebration(true);
      const timer = setTimeout(() => setShowCelebration(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [progressPercentage, isGenerating]);

  const getProgressColor = () => {
    if (progressPercentage === 100) return 'from-green-500 to-emerald-500';
    if (progressPercentage >= 75) return 'from-blue-500 to-purple-500';
    if (progressPercentage >= 50) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-500';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'generating':
        return <PenTool className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Progress Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-50 via-purple-50 to-green-50 rounded-xl p-6 border border-gray-200 shadow-lg">
        {isGenerating && <FloatingParticles count={15} />}
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Brain className={`h-8 w-8 text-blue-600 ${isGenerating ? 'animate-pulse' : ''}`} />
                {isGenerating && (
                  <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
                )}
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">
                  {showCelebration ? '🎉 Research Paper Complete!' : 'AI Paper Generation'}
                </h3>
                <p className="text-sm text-gray-600">
                  {currentSection ? (
                    <TypingAnimation 
                      text={`Generating ${currentSection.name}...`}
                      speed={50}
                      className="text-blue-700"
                    />
                  ) : isGenerating ? (
                    'Processing your research paper'
                  ) : (
                    `${completedSections.length} of ${totalSections} sections completed`
                  )}
                </p>
              </div>
            </div>
            
            <div className="text-right">
              <div className={`text-3xl font-bold transition-colors duration-500 ${
                showCelebration ? 'text-green-600' : 'text-blue-600'
              }`}>
                {Math.round(animatedProgress)}%
              </div>
              <div className="text-sm text-gray-500">
                {completedSections.length}/{totalSections} sections
              </div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="relative">
            <Progress 
              value={animatedProgress} 
              className="h-4 bg-white/50 shadow-inner"
            />
            <div 
              className={`absolute top-0 left-0 h-4 rounded-full bg-gradient-to-r ${getProgressColor()} transition-all duration-1000 shadow-lg`}
              style={{ width: `${animatedProgress}%` }}
            >
              {isGenerating && (
                <div className="absolute inset-0 bg-white/20 animate-pulse rounded-full"></div>
              )}
            </div>
          </div>

          {/* Current Status */}
          {currentSection && isGenerating && (
            <div className="mt-4 flex items-center gap-3 bg-white/60 backdrop-blur-sm rounded-lg p-3">
              <Zap className="h-5 w-5 text-blue-500 animate-pulse" />
              <span className="text-blue-700 font-medium">
                Crafting {currentSection.name.toLowerCase()} with AI precision
              </span>
              <PulsingDots count={3} color="blue" size="sm" />
            </div>
          )}
        </div>
      </div>

      {/* Section Progress Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sections.map((section, index) => {
          const isActive = section.status === 'generating';
          const isCompleted = section.status === 'completed';
          const isPending = section.status === 'pending';
          const isError = section.status === 'error';

          return (
            <div
              key={section.id}
              className={`
                relative p-4 rounded-lg border transition-all duration-500 transform
                ${isActive ? 'border-blue-300 bg-gradient-to-br from-blue-50 to-purple-50 shadow-lg scale-105' : ''}
                ${isCompleted ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-md' : ''}
                ${isPending ? 'border-gray-200 bg-white' : ''}
                ${isError ? 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50' : ''}
              `}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Active section animation overlay */}
              {isActive && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-100/30 to-purple-100/30 animate-pulse rounded-lg"></div>
              )}

              <div className="relative flex items-center gap-3">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-300
                  ${isActive ? 'bg-blue-100 shadow-md' : ''}
                  ${isCompleted ? 'bg-green-100' : ''}
                  ${isPending ? 'bg-gray-100' : ''}
                  ${isError ? 'bg-red-100' : ''}
                `}>
                  {isActive ? (
                    <PenTool className="h-5 w-5 text-blue-600 animate-pulse" />
                  ) : (
                    <section.icon className={`
                      h-5 w-5 transition-colors duration-300
                      ${isCompleted ? 'text-green-600' : ''}
                      ${isPending ? 'text-gray-600' : ''}
                      ${isError ? 'text-red-600' : ''}
                    `} />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className={`
                    font-semibold text-sm transition-colors duration-300
                    ${isActive ? 'text-blue-900' : ''}
                    ${isCompleted ? 'text-green-900' : ''}
                    ${isPending ? 'text-gray-900' : ''}
                    ${isError ? 'text-red-900' : ''}
                  `}>
                    {section.name}
                  </h4>
                  <p className={`
                    text-xs truncate transition-colors duration-300
                    ${isActive ? 'text-blue-700' : ''}
                    ${isCompleted ? 'text-green-700' : ''}
                    ${isPending ? 'text-gray-500' : ''}
                    ${isError ? 'text-red-700' : ''}
                  `}>
                    {isActive ? 'Generating...' : section.description}
                  </p>
                </div>

                <div className="flex-shrink-0">
                  {getStatusIcon(section.status)}
                </div>
              </div>

              {/* Progress indicator for active section */}
              {isActive && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-b-lg">
                  <div className="h-full bg-gradient-to-r from-blue-400 to-purple-400 animate-pulse rounded-b-lg"></div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Completion Celebration */}
      {showCelebration && (
        <div className="text-center py-8 animate-fade-in">
          <div className="flex justify-center items-center gap-2 mb-4">
            <Sparkles className="h-8 w-8 text-yellow-500 animate-bounce" />
            <span className="text-2xl">🎉</span>
            <Sparkles className="h-8 w-8 text-yellow-500 animate-bounce" />
          </div>
          <h3 className="text-2xl font-bold text-green-800 mb-2">
            Research Paper Generation Complete!
          </h3>
          <p className="text-green-700">
            Your AI-generated research paper is ready for review and export.
          </p>
        </div>
      )}
    </div>
  );
};
