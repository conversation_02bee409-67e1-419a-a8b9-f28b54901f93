/**
 * Research Search Store
 * Zustand store for managing global search state
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { SearchSession, SearchMessage, UserPreferences, SearchStats } from '../types';

interface SearchState {
  // Current session state
  currentSessionId: string | null;
  sessions: SearchSession[];
  
  // UI state
  sidebarCollapsed: boolean;
  selectedModel: string;
  
  // User preferences (persisted)
  preferences: UserPreferences;
  
  // Search statistics
  stats: SearchStats | null;
  
  // Actions
  setCurrentSession: (sessionId: string | null) => void;
  setSessions: (sessions: SearchSession[]) => void;
  addSession: (session: SearchSession) => void;
  updateSession: (sessionId: string, updates: Partial<SearchSession>) => void;
  removeSession: (sessionId: string) => void;
  
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSelectedModel: (model: string) => void;
  
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  setStats: (stats: SearchStats) => void;
  
  // Utility actions
  reset: () => void;
}

const defaultPreferences: UserPreferences = {
  defaultModel: 'google/gemini-2.0-flash-001',
  defaultSearchDepth: 'advanced',
  maxResults: 10,
  autoSave: true,
  showSources: true,
  showCitations: true,
  theme: 'auto'
};

const defaultStats: SearchStats = {
  totalSearches: 0,
  totalSessions: 0,
  averageResponseTime: 0,
  mostUsedModel: '',
  topDomains: []
};

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSessionId: null,
      sessions: [],
      sidebarCollapsed: false,
      selectedModel: defaultPreferences.defaultModel,
      preferences: defaultPreferences,
      stats: defaultStats,

      // Session management
      setCurrentSession: (sessionId) => {
        set({ currentSessionId: sessionId });
      },

      setSessions: (sessions) => {
        set({ sessions });
      },

      addSession: (session) => {
        set((state) => ({
          sessions: [session, ...state.sessions],
          currentSessionId: session.id
        }));
      },

      updateSession: (sessionId, updates) => {
        set((state) => ({
          sessions: state.sessions.map(session =>
            session.id === sessionId ? { ...session, ...updates } : session
          )
        }));
      },

      removeSession: (sessionId) => {
        set((state) => {
          const newSessions = state.sessions.filter(s => s.id !== sessionId);
          const newCurrentId = state.currentSessionId === sessionId 
            ? (newSessions[0]?.id || null) 
            : state.currentSessionId;
          
          return {
            sessions: newSessions,
            currentSessionId: newCurrentId
          };
        });
      },

      // UI state management
      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed });
      },

      setSelectedModel: (model) => {
        set({ selectedModel: model });
      },

      // Preferences management
      updatePreferences: (newPreferences) => {
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences },
          selectedModel: newPreferences.defaultModel || state.selectedModel
        }));
      },

      // Statistics
      setStats: (stats) => {
        set({ stats });
      },

      // Utility
      reset: () => {
        set({
          currentSessionId: null,
          sessions: [],
          sidebarCollapsed: false,
          selectedModel: defaultPreferences.defaultModel,
          preferences: defaultPreferences,
          stats: defaultStats
        });
      }
    }),
    {
      name: 'research-search-store',
      partialize: (state) => ({
        // Only persist user preferences and UI state
        sidebarCollapsed: state.sidebarCollapsed,
        selectedModel: state.selectedModel,
        preferences: state.preferences
      })
    }
  )
);

// Selector hooks for better performance
export const useCurrentSession = () => useSearchStore((state) => 
  state.sessions.find(s => s.id === state.currentSessionId) || null
);

export const useSessionById = (sessionId: string | null) => useSearchStore((state) =>
  sessionId ? state.sessions.find(s => s.id === sessionId) || null : null
);

export const useActiveSessions = () => useSearchStore((state) =>
  state.sessions.filter(s => s.isActive)
);

export const useRecentSessions = (limit: number = 10) => useSearchStore((state) =>
  state.sessions
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, limit)
);

export const useSearchStats = () => useSearchStore((state) => state.stats);

export const useSearchPreferences = () => useSearchStore((state) => state.preferences);

// Action hooks
export const useSearchActions = () => useSearchStore((state) => ({
  setCurrentSession: state.setCurrentSession,
  setSessions: state.setSessions,
  addSession: state.addSession,
  updateSession: state.updateSession,
  removeSession: state.removeSession,
  setSidebarCollapsed: state.setSidebarCollapsed,
  setSelectedModel: state.setSelectedModel,
  updatePreferences: state.updatePreferences,
  setStats: state.setStats,
  reset: state.reset
}));
