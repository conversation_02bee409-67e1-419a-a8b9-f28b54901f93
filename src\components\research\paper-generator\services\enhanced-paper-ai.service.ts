/**
 * Enhanced Paper AI Service with Real Citation Integration
 * Combines Tavily search with AI generation for accurate citations
 */

import { enhancedCitationSearchService, CitationSource, SectionCitationContext } from './enhanced-citation-search.service';
import paperAIService from './paper-ai.service';
import { referenceValidationAIService, ValidatedCitation } from './reference-validation-ai.service';

export interface EnhancedGenerationOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  includeCitations?: boolean;
  maxCitations?: number;
  searchDepth?: 'basic' | 'advanced' | 'comprehensive';
  useRealCitations?: boolean;
}

export interface GenerationResult {
  content: string;
  citations: CitationSource[];
  validatedCitations?: ValidatedCitation[];
  references: string;
  wordCount: number;
  citationCount: number;
  validationScore?: number;
}

export class EnhancedPaperAIService {
  private readonly defaultOptions: EnhancedGenerationOptions = {
    model: "google/gemini-2.5-flash-preview-05-20",
    maxTokens: 2048,
    temperature: 0.7,
    includeCitations: true,
    maxCitations: 8,
    searchDepth: 'advanced'
  };

  /**
   * Generate a paper section with selective use of Tavily search
   */
  async generateSectionWithRealCitations(
    sectionId: string,
    sectionName: string,
    context: SectionCitationContext,
    options: Partial<EnhancedGenerationOptions> = {}
  ): Promise<GenerationResult> {
    const genOptions = { ...this.defaultOptions, ...options };

    try {
      console.log(`Generating ${sectionName}...`);

      // Determine citation strategy for this section
      const shouldUseTavily = this.shouldUseTavilyForSection(sectionId);
      const shouldIncludeCitations = this.shouldIncludeCitations(sectionId);
      const citationLimit = this.getSectionCitationLimits(sectionId);

      console.log(`Section ${sectionName}: Tavily=${shouldUseTavily}, Citations=${shouldIncludeCitations}, Limit=${citationLimit}`);

      // Step 1: Search for academic sources (only for specific sections)
      let citations: CitationSource[] = [];
      if (shouldUseTavily && shouldIncludeCitations && genOptions.includeCitations && sectionId !== 'references') {
        const useRealCitations = genOptions.useRealCitations !== false; // Default to true

        try {
          if (useRealCitations) {
            console.log(`Searching for ${citationLimit} REAL academic sources for ${sectionName}...`);
            citations = await enhancedCitationSearchService.searchRealCitationsForSection(context, {
              maxSources: citationLimit,
              searchDepth: genOptions.searchDepth || 'comprehensive'
            });
            console.log(`Found ${citations.length} REAL, verified sources for ${sectionName}`);
          } else {
            console.log(`Searching for ${citationLimit} academic sources for ${sectionName} (regular search)...`);
            citations = await enhancedCitationSearchService.searchCitationsForSection(context, {
              maxSources: citationLimit,
              searchDepth: genOptions.searchDepth || 'comprehensive'
            });
            console.log(`Found ${citations.length} sources for ${sectionName}`);
          }

          // If no citations found with real search, try fallback with regular search
          if (citations.length === 0 && useRealCitations) {
            console.log(`No real citations found, trying fallback regular search for ${sectionName}...`);
            citations = await enhancedCitationSearchService.searchCitationsForSection(context, {
              maxSources: Math.min(citationLimit, 5), // Reduced limit for fallback
              searchDepth: 'basic'
            });
            console.log(`Fallback search found ${citations.length} sources for ${sectionName}`);
          }
        } catch (searchError) {
          console.error(`Citation search failed for ${sectionName}:`, searchError);
          citations = []; // Continue without citations
        }
      }

      // Step 2: Generate content with appropriate prompt
      let prompt: string;
      if (shouldUseTavily && citations.length > 0) {
        prompt = this.buildEnhancedPrompt(sectionId, context, citations);
      } else if (shouldUseTavily && citations.length === 0) {
        // Use enhanced prompt but without citations
        prompt = this.buildEnhancedPromptWithoutCitations(sectionId, context);
      } else {
        prompt = this.buildUserContentBasedPrompt(sectionId, context, shouldIncludeCitations);
      }

      console.log(`Generating ${sectionName} content with AI...`);

      const generatedContent = await paperAIService.generatePaperSection(prompt, {
        model: genOptions.model,
        maxTokens: genOptions.maxTokens,
        temperature: genOptions.temperature
      });

      // Step 3: Process and format the result
      const processedContent = this.processGeneratedContent(generatedContent, citations);
      const references = this.generateReferencesSection(citations);

      const result: GenerationResult = {
        content: processedContent,
        citations,
        references,
        wordCount: processedContent.split(/\s+/).length,
        citationCount: citations.length
      };

      console.log(`Generated ${sectionName}: ${result.wordCount} words, ${result.citationCount} citations, Tavily used: ${shouldUseTavily}`);
      return result;

    } catch (error) {
      console.error(`Failed to generate ${sectionName}:`, error);

      // Fallback to regular generation without citations
      console.log('Falling back to regular generation...');
      const fallbackPrompt = this.buildFallbackPrompt(sectionId, context);
      const fallbackContent = await paperAIService.generatePaperSection(fallbackPrompt, {
        model: genOptions.model,
        maxTokens: genOptions.maxTokens,
        temperature: genOptions.temperature
      });

      return {
        content: fallbackContent,
        citations: [],
        references: '',
        wordCount: fallbackContent.split(/\s+/).length,
        citationCount: 0
      };
    }
  }

  /**
   * Determine if a section should use Tavily search for citations
   */
  private shouldUseTavilyForSection(sectionId: string): boolean {
    // Only use Tavily for Introduction and Methodology
    return ['introduction', 'methodology'].includes(sectionId);
  }

  /**
   * Determine if a section should include citations at all
   */
  private shouldIncludeCitations(sectionId: string): boolean {
    // No citations in conclusion, abstract, or references
    const noCitationSections = ['conclusion', 'abstract', 'references'];
    return !noCitationSections.includes(sectionId);
  }

  /**
   * Get section-specific citation limits - INCREASED for better coverage
   */
  private getSectionCitationLimits(sectionId: string): number {
    const limits = {
      'introduction': 25,  // Increased for extensive background literature
      'methodology': 15,   // Increased for methodological references
      'results': 8,        // Increased for better support
      'discussion': 12,    // Increased for literature connections
      'conclusion': 0,     // No citations
      'abstract': 0        // No citations
    };
    return limits[sectionId] || 8; // Increased default
  }

  /**
   * Build enhanced prompt with real citation sources and topic relevance emphasis
   */
  private buildEnhancedPrompt(
    sectionId: string,
    context: SectionCitationContext,
    citations: CitationSource[]
  ): string {
    const { title, researchField, keywords, userContent } = context;

    let prompt = `Generate a high-quality ${context.sectionName} section for a research paper titled "${title}" in the field of ${researchField}.

IMPORTANT: Stay strictly focused on the research topic. All content and citations must be directly relevant to "${title}" and ${researchField}.

Research Context:
- Field: ${researchField}
- Keywords: ${keywords.join(', ')}
- Topic Focus: ${title}`;

    if (userContent && userContent !== `[AI will generate ${context.sectionName} content]`) {
      prompt += `\n- User Content: ${userContent}`;
    }

    // Add real citation sources with relevance emphasis
    if (citations.length > 0) {
      prompt += `\n\nREAL ACADEMIC SOURCES TO CITE (these are specifically selected for relevance to your topic):`;
      citations.forEach((citation, index) => {
        prompt += `\n${index + 1}. ${citation.inTextCitation} - "${citation.title}" by ${citation.authors.join(', ')} (${citation.year})`;
        if (citation.abstract) {
          prompt += `\n   Abstract: ${citation.abstract.substring(0, 200)}...`;
        }
        if (citation.journal) {
          prompt += `\n   Published in: ${citation.journal}`;
        }
        prompt += `\n   Relevance: This source is directly related to ${title} and ${researchField}`;
        prompt += `\n   URL: ${citation.url}`;
      });

      prompt += `\n\nCITATION REQUIREMENTS:
- Use ONLY the provided citations above - do not create or invent any citations
- Each citation must be used in context that directly relates to the research topic
- Ensure citations support specific claims about "${title}" or ${researchField}
- Use the exact in-text citation format provided (e.g., ${citations[0]?.inTextCitation || '(Author, Year)'})`;
    }

    // Section-specific instructions with topic focus
    prompt += this.getTopicFocusedSectionInstructions(sectionId, citations.length > 0, title, researchField);

    return prompt;
  }

  /**
   * Build enhanced prompt without citations (fallback when no citations found)
   */
  private buildEnhancedPromptWithoutCitations(
    sectionId: string,
    context: SectionCitationContext
  ): string {
    const { title, researchField, keywords, userContent } = context;

    let prompt = `Generate a high-quality ${context.sectionName} section for a research paper titled "${title}" in the field of ${researchField}.

IMPORTANT: Stay strictly focused on the research topic. All content must be directly relevant to "${title}" and ${researchField}.

Research Context:
- Field: ${researchField}
- Keywords: ${keywords.join(', ')}
- Topic Focus: ${title}`;

    if (userContent && userContent !== `[AI will generate ${context.sectionName} content]`) {
      prompt += `\n- User Content: ${userContent}`;
    }

    prompt += `\n\nNOTE: No specific citations were found for this topic. Generate content based on general academic knowledge in ${researchField}.

CONTENT REQUIREMENTS:
- Write substantial, detailed content (400-600 words)
- Focus strictly on "${title}" and ${researchField}
- Use formal academic language
- Include realistic but minimal in-text citations only where absolutely necessary
- Ensure all content is directly relevant to the research topic
- Do not include generic statements that could apply to any research`;

    // Section-specific instructions without citations
    prompt += this.getTopicFocusedSectionInstructions(sectionId, false, title, researchField);

    return prompt;
  }

  /**
   * Get section-specific instructions with enhanced topic focus
   */
  private getTopicFocusedSectionInstructions(
    sectionId: string,
    hasCitations: boolean,
    title: string,
    researchField: string
  ): string {
    const citationInstructions = hasCitations
      ? `\n- CRITICAL: Use ONLY the provided real citations in the exact format shown
- Each citation must directly support claims about "${title}" or ${researchField}
- Do not invent, create, or modify any citations
- Ensure every cited claim is specifically relevant to the research topic`
      : `\n- Include realistic in-text citations in (Author, Year) format only if absolutely necessary
- Any citations must be directly relevant to "${title}" and ${researchField}`;

    const baseInstructions = `\n\nGeneration Instructions:
- Write in formal academic language without section headings
- Provide substantial, detailed content (400-600 words)
- Use third-person perspective and objective tone
- STAY STRICTLY ON TOPIC: All content must relate directly to "${title}" and ${researchField}
- Avoid generic statements that could apply to any research topic${citationInstructions}
- Ensure content flows logically and coherently
- Do not include phrases like "In this section" or "Here we present"`;

    switch (sectionId) {
      case 'introduction':
        return baseInstructions + `
- Establish research context specifically for "${title}" in ${researchField}
- Present clear problem statement directly related to your research topic
- Define objectives and research questions specific to "${title}"
- Explain significance and expected contributions of this specific research
- Provide brief overview of paper structure focused on your topic
- All background literature must be directly relevant to "${title}" and ${researchField}`;

      case 'methodology':
        return baseInstructions + `
- ENHANCE the user-provided methodology with additional academic rigor and citations
- Detail research design and approach specifically for studying "${title}"
- Expand on data collection methods relevant to ${researchField} research
- Add analytical techniques and justify methodological choices
- Include additional ethical considerations and quality control measures
- Enhance with methodological references and best practices from literature
- Maintain the user's original approach while adding academic depth and citations
- All methodological choices must be justified for your particular research topic`;

      case 'results':
        return baseInstructions + `
- ENHANCE the user-provided results with deeper analysis and supporting citations
- Present findings objectively related to "${title}" research
- Expand on data interpretation and statistical significance
- Add comparative analysis with literature where appropriate
- Include additional context for figures and tables referenced
- Enhance with supporting evidence and methodological validation
- Maintain the user's original findings while adding analytical depth
- Highlight key patterns and significant findings specific to your topic
- Focus on what was found in relation to "${title}" and ${researchField}`;

      case 'discussion':
        return baseInstructions + `
- CONNECT the generated introduction, enhanced methodology, and enhanced results
- Interpret results in context of "${title}" research questions and background literature
- Compare findings with existing literature specifically in ${researchField}
- Explain how results address the problem statement from the introduction
- Discuss theoretical and practical implications for your research area
- Address limitations specific to your methodology and research topic
- Connect findings to the broader research context established in the introduction
- Suggest future research directions directly related to "${title}"
- All comparisons must be relevant to your specific research focus and previous sections`;

      case 'conclusion':
        return baseInstructions + `
- Summarize key findings and contributions specific to "${title}"
- Revisit research objectives and questions from your introduction
- Discuss broader implications for ${researchField} based on your findings
- Acknowledge limitations concisely related to your specific study
- Suggest specific future research directions building on your work
- Provide strong closing statement about your contribution to ${researchField}`;

      default:
        return baseInstructions + `
- Focus on the specific requirements of the ${sectionId} section
- Maintain strict relevance to "${title}" and ${researchField}
- Use academic rigor and scholarly tone throughout`;
    }
  }

  /**
   * Build prompt based on user content with controlled citations
   */
  private buildUserContentBasedPrompt(
    sectionId: string,
    context: SectionCitationContext,
    includeCitations: boolean = true
  ): string {
    const { title, researchField, keywords, userContent, relatedSections } = context;

    let prompt = `Generate a high-quality ${context.sectionName} section for a research paper titled "${title}" in the field of ${researchField}.

Research Context:
- Field: ${researchField}
- Keywords: ${keywords.join(', ')}`;

    if (userContent && userContent !== `[AI will generate ${context.sectionName} content]`) {
      prompt += `\n- User-Provided Content: ${userContent}`;
    }

    // Add related sections for context
    if (relatedSections && relatedSections.length > 0) {
      prompt += `\n\nRelated Sections Context:`;
      relatedSections.forEach((section, index) => {
        prompt += `\n${index + 1}. ${section}`;
      });
    }

    // Section-specific instructions with controlled citations
    prompt += this.getUserContentSectionInstructions(sectionId, !!userContent, includeCitations);

    return prompt;
  }

  /**
   * Get section-specific instructions for user content-based generation
   */
  private getUserContentSectionInstructions(sectionId: string, hasUserContent: boolean, includeCitations: boolean = true): string {
    const citationInstructions = includeCitations
      ? `- Include minimal, realistic in-text citations in (Author, Year) format only where academically necessary
- Use citations sparingly and only to support key claims or methodological choices`
      : `- Do NOT include any in-text citations or references
- Focus entirely on presenting your own findings, conclusions, and contributions
- Write in a clear, authoritative tone without external references`;

    const baseInstructions = `\n\nGeneration Instructions:
- Write in formal academic language without section headings
- Provide substantial, detailed content (400-600 words)
- Use third-person perspective and objective tone
- Base content primarily on the provided user content and research context
${citationInstructions}
- Ensure content flows logically and coherently
- Do not include phrases like "In this section" or "Here we present"`;

    switch (sectionId) {
      case 'results':
        return baseInstructions + `
- Present findings objectively based on the user-provided methodology and data
- Organize results logically based on research questions and methodology
- Include specific data, statistics, and measurements from user content
- Reference figures and tables appropriately if mentioned in user content
- Highlight key patterns and significant findings from the provided information
- Focus on what was found rather than interpretation
- Use quantitative language and precise descriptions`;

      case 'discussion':
        return baseInstructions + `
- Interpret results in context of research questions and objectives
- Connect findings from the results section with the research background
- Explain theoretical and practical implications of the findings
- Compare findings with existing literature using realistic citations
- Address limitations mentioned in the methodology or evident from results
- Discuss unexpected results and their potential explanations
- Suggest future research directions based on current findings`;

      case 'conclusion':
        return baseInstructions + `
- Summarize key findings from the results section without citations
- Revisit research objectives and questions from the introduction
- Discuss broader implications for the field based on YOUR findings
- Acknowledge limitations concisely based on YOUR methodology and results
- Suggest specific future research directions based on YOUR work
- Provide a strong closing statement about YOUR contribution to the field
- Avoid introducing new information not discussed in previous sections
- Do NOT include any citations - this should reflect your own work and conclusions`;

      case 'abstract':
        return baseInstructions + `
- Provide a concise summary of the entire research paper
- Include brief background, YOUR methodology, YOUR key results, and YOUR conclusions
- Limit to 250-300 words maximum
- Use past tense for completed work
- Include 3-5 key findings or contributions from YOUR research
- Make it standalone - readable without the full paper
- Include keywords naturally within the text
- Do NOT include any citations - abstracts should be self-contained summaries`;

      default:
        return baseInstructions + `
- Focus on the specific requirements of the ${sectionId} section
- Use the provided user content as the primary source of information
- Maintain academic rigor and scholarly tone throughout`;
    }
  }

  /**
   * Get section-specific generation instructions
   */
  private getSectionSpecificInstructions(sectionId: string, hasCitations: boolean): string {
    const citationInstructions = hasCitations 
      ? `\n- IMPORTANT: Use ONLY the provided real citations above in the exact format shown (e.g., (Smith, 2023))
- Each major claim should be supported by one of the provided citations
- Do not invent or create any citations - only use the ones provided
- Distribute the citations naturally throughout the content`
      : `\n- Include realistic in-text citations in (Author, Year) format
- Use varied and appropriate citations for academic credibility`;

    const baseInstructions = `\n\nGeneration Instructions:
- Write in formal academic language without section headings
- Provide substantial, detailed content (400-600 words)
- Use third-person perspective and objective tone${citationInstructions}
- Ensure content flows logically and coherently
- Do not include phrases like "In this section" or "Here we present"`;

    switch (sectionId) {
      case 'introduction':
        return baseInstructions + `
- Establish research context and background
- Present clear problem statement and research gap
- Define objectives and research questions
- Explain significance and expected contributions
- Provide brief overview of paper structure`;

      case 'methodology':
        return baseInstructions + `
- Detail research design and approach
- Describe data collection methods and procedures
- Explain analytical techniques and tools
- Address ethical considerations and limitations
- Include sufficient detail for replication`;

      case 'results':
        return baseInstructions + `
- Present findings objectively without interpretation
- Organize results logically based on research questions
- Include specific data, statistics, and measurements
- Reference figures and tables appropriately
- Highlight key patterns and significant findings`;

      case 'discussion':
        return baseInstructions + `
- Interpret results in context of research questions
- Compare findings with existing literature
- Explain theoretical and practical implications
- Address limitations and unexpected results
- Suggest future research directions`;

      case 'conclusion':
        return baseInstructions + `
- Summarize key findings and contributions
- Revisit research objectives and questions
- Discuss broader implications for the field
- Acknowledge limitations concisely
- Suggest specific future research directions`;

      default:
        return baseInstructions;
    }
  }

  /**
   * Build fallback prompt without citations
   */
  private buildFallbackPrompt(sectionId: string, context: SectionCitationContext): string {
    const { title, researchField, keywords, userContent } = context;
    
    let prompt = `Generate a comprehensive ${context.sectionName} section for a research paper titled "${title}" in ${researchField}.

Context:
- Research Field: ${researchField}
- Keywords: ${keywords.join(', ')}`;

    if (userContent) {
      prompt += `\n- User Content: ${userContent}`;
    }

    prompt += `\n\nInstructions:
- Write 400-600 words in formal academic language
- Do not include section headings
- Use realistic in-text citations in (Author, Year) format
- Ensure content is substantial and research-quality
- Use third-person perspective and objective tone`;

    return prompt;
  }

  /**
   * Process generated content to ensure proper formatting
   */
  private processGeneratedContent(content: string, citations: CitationSource[]): string {
    let processed = content.trim();
    
    // Remove any section headings that might have been generated
    processed = processed.replace(/^#+\s*[A-Za-z\s]+\n/gm, '');
    processed = processed.replace(/^[A-Z][A-Za-z\s]+:?\n/gm, '');
    
    // Ensure proper paragraph spacing
    processed = processed.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // Clean up any malformed citations
    processed = processed.replace(/\([A-Z\s,]+\)/g, (match) => {
      return match.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    });

    return processed;
  }

  /**
   * Generate references section from citations
   */
  private generateReferencesSection(citations: CitationSource[]): string {
    if (citations.length === 0) {
      return '';
    }

    // Sort citations alphabetically by first author
    const sortedCitations = [...citations].sort((a, b) => {
      const authorA = a.authors[0]?.split(',')[0] || '';
      const authorB = b.authors[0]?.split(',')[0] || '';
      return authorA.localeCompare(authorB);
    });

    let references = '';
    sortedCitations.forEach(citation => {
      references += `${citation.formattedCitation}\n\n`;
    });

    return references.trim();
  }

  /**
   * Generate section with AI-validated references (enhanced quality)
   */
  async generateSectionWithValidatedReferences(
    sectionId: string,
    sectionName: string,
    context: SectionCitationContext,
    options: Partial<EnhancedGenerationOptions> = {}
  ): Promise<GenerationResult> {
    console.log(`Generating ${sectionName} with AI-validated references...`);

    // First, generate with regular enhanced citations
    const initialResult = await this.generateSectionWithRealCitations(
      sectionId,
      sectionName,
      context,
      options
    );

    // If we have citations, validate them with AI
    if (initialResult.citations.length > 0) {
      try {
        console.log(`Validating ${initialResult.citations.length} citations with AI...`);

        const validationResult = await referenceValidationAIService.validateAndFormatReferences(
          initialResult.citations,
          options.model
        );

        console.log(`AI validation completed: ${validationResult.validationReport.validCitations}/${validationResult.validationReport.totalCitations} valid`);

        // Update the result with validated citations
        const enhancedResult: GenerationResult = {
          ...initialResult,
          validatedCitations: validationResult.validatedCitations,
          validationScore: validationResult.validationReport.averageQuality,
          references: validationResult.formattedReferences
        };

        return enhancedResult;

      } catch (error) {
        console.error('AI reference validation failed:', error);
        console.log('Falling back to original result...');
        return initialResult;
      }
    }

    return initialResult;
  }

  /**
   * Generate complete paper with enhanced citations
   */
  async generateCompletePaper(
    context: SectionCitationContext,
    sectionIds: string[],
    options: Partial<EnhancedGenerationOptions> = {}
  ): Promise<{ [sectionId: string]: GenerationResult }> {
    const results: { [sectionId: string]: GenerationResult } = {};
    
    for (const sectionId of sectionIds) {
      if (sectionId === 'references') continue; // Handle references separately
      
      const sectionContext = {
        ...context,
        sectionId,
        sectionName: this.getSectionName(sectionId)
      };
      
      results[sectionId] = await this.generateSectionWithRealCitations(
        sectionId,
        sectionContext.sectionName,
        sectionContext,
        options
      );
    }

    // Generate consolidated references
    if (sectionIds.includes('references')) {
      const allCitations: CitationSource[] = [];
      Object.values(results).forEach(result => {
        allCitations.push(...result.citations);
      });

      const uniqueCitations = this.removeDuplicateCitations(allCitations);
      results['references'] = {
        content: this.generateReferencesSection(uniqueCitations),
        citations: uniqueCitations,
        references: '',
        wordCount: 0,
        citationCount: uniqueCitations.length
      };
    }

    return results;
  }

  /**
   * Remove duplicate citations
   */
  private removeDuplicateCitations(citations: CitationSource[]): CitationSource[] {
    const seen = new Set<string>();
    return citations.filter(citation => {
      const key = `${citation.authors[0]}-${citation.year}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Get section name by ID
   */
  private getSectionName(sectionId: string): string {
    const sectionNames: { [key: string]: string } = {
      'introduction': 'Introduction',
      'methodology': 'Methodology',
      'results': 'Results',
      'discussion': 'Discussion',
      'conclusion': 'Conclusion',
      'abstract': 'Abstract',
      'references': 'References'
    };
    return sectionNames[sectionId] || sectionId;
  }
}

export const enhancedPaperAIService = new EnhancedPaperAIService();
