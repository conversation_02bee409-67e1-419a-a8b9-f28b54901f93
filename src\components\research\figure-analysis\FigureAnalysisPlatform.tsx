/**
 * Figure Analysis Platform
 * Main component for comprehensive AI-powered figure analysis
 */

import React, { useState, useCallback } from 'react';
import { 
  Upload, BarChart3, FileText, Brain, 
  Settings, Download, Share2, History,
  Play, Pause, RotateCcw, Zap
} from 'lucide-react';
import { FigureUploader } from './components/FigureUploader';
import { FigurePreview } from './components/FigurePreview';
import { QualityAssessment } from './components/QualityAssessment';
import { CaptionGenerator } from './components/CaptionGenerator';
import { AdvancedAnalysis } from './components/AdvancedAnalysis';
import { GeminiVisionService } from './services/gemini-vision.service';
import {
  FigureFile,
  FigureAnalysisResult,
  FigureAnalysisState,
  AnalysisRequest,
  AnalysisType,
  AnalysisSettings,
  CaptionSuggestion,
} from './types';
import { DEFAULT_ANALYSIS_SETTINGS } from './constants';

export const FigureAnalysisPlatform: React.FC = () => {
  const [state, setState] = useState<FigureAnalysisState>({
    uploadedFigures: [],
    selectedFigure: null,
    analysisResults: new Map(),
    isAnalyzing: false,
    analysisProgress: {
      currentStep: '',
      progress: 0,
      estimatedTimeRemaining: 0,
      steps: [],
    },
    settings: DEFAULT_ANALYSIS_SETTINGS,
    exportOptions: {
      format: 'pdf',
      includeImages: true,
      includeRecommendations: true,
      includeRawData: false,
      template: 'academic',
    },
  });

  const [activeTab, setActiveTab] = useState<'upload' | 'preview' | 'quality' | 'captions' | 'advanced' | 'export'>('upload');

  /**
   * Handle file uploads
   */
  const handleFilesUploaded = useCallback((files: FigureFile[]) => {
    setState(prev => ({
      ...prev,
      uploadedFigures: [...prev.uploadedFigures, ...files],
      selectedFigure: prev.selectedFigure || files[0] || null,
    }));
    
    // Auto-switch to preview tab if this is the first upload
    if (state.uploadedFigures.length === 0 && files.length > 0) {
      setActiveTab('preview');
    }
  }, [state.uploadedFigures.length]);

  /**
   * Handle file removal
   */
  const handleFileRemoved = useCallback((fileId: string) => {
    setState(prev => {
      const newFigures = prev.uploadedFigures.filter(f => f.id !== fileId);
      const newResults = new Map(prev.analysisResults);
      newResults.delete(fileId);
      
      return {
        ...prev,
        uploadedFigures: newFigures,
        selectedFigure: prev.selectedFigure?.id === fileId 
          ? (newFigures[0] || null) 
          : prev.selectedFigure,
        analysisResults: newResults,
      };
    });
  }, []);

  /**
   * Handle figure selection
   */
  const handleFigureSelect = useCallback((figure: FigureFile) => {
    setState(prev => ({ ...prev, selectedFigure: figure }));
  }, []);

  /**
   * Start comprehensive analysis
   */
  const startAnalysis = useCallback(async () => {
    if (!state.selectedFigure) return;

    setState(prev => ({
      ...prev,
      isAnalyzing: true,
      analysisProgress: {
        currentStep: 'Initializing analysis...',
        progress: 0,
        estimatedTimeRemaining: 60,
        steps: [
          { name: 'Quality Assessment', status: 'pending', progress: 0 },
          { name: 'Caption Generation', status: 'pending', progress: 0 },
          { name: 'Advanced Analysis', status: 'pending', progress: 0 },
          { name: 'Finalizing Results', status: 'pending', progress: 0 },
        ],
      },
    }));

    try {
      const analysisTypes: AnalysisType[] = ['quality', 'caption'];
      
      if (state.settings.enableAdvancedFeatures) {
        analysisTypes.push('authenticity', 'cross-reference', 'statistical', 'methodology', 'bias', 'impact', 'data-extraction');
      }

      const request: AnalysisRequest = {
        figureId: state.selectedFigure.id,
        analysisType: analysisTypes,
        settings: state.settings,
        context: {
          researchField: state.settings.researchField,
          userInstructions: 'Comprehensive academic figure analysis',
        },
      };

      // Update progress
      setState(prev => ({
        ...prev,
        analysisProgress: {
          ...prev.analysisProgress,
          currentStep: 'Running AI analysis...',
          progress: 25,
          steps: prev.analysisProgress.steps.map((step, index) => 
            index === 0 ? { ...step, status: 'running', progress: 50 } : step
          ),
        },
      }));

      const response = await GeminiVisionService.analyzeFigure(request, state.selectedFigure);

      if (response.success && response.result) {
        setState(prev => {
          const newResults = new Map(prev.analysisResults);
          newResults.set(state.selectedFigure!.id, response.result!);
          
          return {
            ...prev,
            analysisResults: newResults,
            isAnalyzing: false,
            analysisProgress: {
              currentStep: 'Analysis complete!',
              progress: 100,
              estimatedTimeRemaining: 0,
              steps: prev.analysisProgress.steps.map(step => ({ 
                ...step, 
                status: 'completed', 
                progress: 100 
              })),
            },
          };
        });

        // Auto-switch to quality tab
        setActiveTab('quality');
      } else {
        throw new Error(response.error || 'Analysis failed');
      }
    } catch (error: any) {
      console.error('Analysis failed:', error);
      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        analysisProgress: {
          ...prev.analysisProgress,
          currentStep: `Error: ${error.message}`,
          steps: prev.analysisProgress.steps.map(step => 
            step.status === 'running' ? { ...step, status: 'error' } : step
          ),
        },
      }));
    }
  }, [state.selectedFigure, state.settings]);

  /**
   * Handle caption regeneration
   */
  const handleRegenerateCaption = useCallback(async (context: any) => {
    if (!state.selectedFigure) return;

    try {
      const captions = await GeminiVisionService.generateCaptions(state.selectedFigure, {
        citationStyle: context.citationStyle,
        researchField: context.researchField,
        purpose: context.purpose,
        userContext: context.userContext,
      });

      setState(prev => {
        const currentResult = prev.analysisResults.get(state.selectedFigure!.id);
        if (currentResult) {
          const updatedResult = { ...currentResult, captionSuggestions: captions };
          const newResults = new Map(prev.analysisResults);
          newResults.set(state.selectedFigure!.id, updatedResult);
          return { ...prev, analysisResults: newResults };
        }
        return prev;
      });
    } catch (error) {
      console.error('Caption regeneration failed:', error);
    }
  }, [state.selectedFigure]);

  /**
   * Handle caption save
   */
  const handleSaveCaption = useCallback((caption: string) => {
    console.log('Saving caption:', caption);
    // TODO: Implement caption saving logic
  }, []);

  /**
   * Get current analysis result
   */
  const currentAnalysis = state.selectedFigure 
    ? state.analysisResults.get(state.selectedFigure.id)
    : null;

  /**
   * Render tab content
   */
  const renderTabContent = () => {
    switch (activeTab) {
      case 'upload':
        return (
          <FigureUploader
            onFilesUploaded={handleFilesUploaded}
            onFileRemoved={handleFileRemoved}
            uploadedFiles={state.uploadedFigures}
            className="h-full"
          />
        );

      case 'preview':
        return state.selectedFigure ? (
          <FigurePreview
            figure={state.selectedFigure}
            analysisResult={currentAnalysis}
            onAnalyze={startAnalysis}
            className="h-full"
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <Upload className="w-12 h-12 mx-auto mb-4" />
              <p>No figure selected</p>
            </div>
          </div>
        );

      case 'quality':
        return currentAnalysis?.qualityAssessment ? (
          <QualityAssessment assessment={currentAnalysis.qualityAssessment} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 mx-auto mb-4" />
              <p>No quality analysis available</p>
              <button
                onClick={startAnalysis}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Start Analysis
              </button>
            </div>
          </div>
        );

      case 'captions':
        return currentAnalysis?.captionSuggestions ? (
          <CaptionGenerator
            captions={currentAnalysis.captionSuggestions}
            onRegenerateCaption={handleRegenerateCaption}
            onSaveCaption={handleSaveCaption}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <FileText className="w-12 h-12 mx-auto mb-4" />
              <p>No captions generated</p>
              <button
                onClick={startAnalysis}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Generate Captions
              </button>
            </div>
          </div>
        );

      case 'advanced':
        return currentAnalysis?.advancedAnalysis ? (
          <AdvancedAnalysis analysis={currentAnalysis.advancedAnalysis} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <Brain className="w-12 h-12 mx-auto mb-4" />
              <p>No advanced analysis available</p>
              <button
                onClick={startAnalysis}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Run Advanced Analysis
              </button>
            </div>
          </div>
        );

      case 'export':
        return (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Export Analysis Results</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Format</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    <option value="pdf">PDF Report</option>
                    <option value="docx">Word Document</option>
                    <option value="json">JSON Data</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Template</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    <option value="academic">Academic Report</option>
                    <option value="technical">Technical Report</option>
                    <option value="summary">Executive Summary</option>
                  </select>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  Include figure images
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  Include recommendations
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  Include raw analysis data
                </label>
              </div>
              
              <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                Export Analysis
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI Figure Analysis</h1>
            <p className="text-sm text-gray-600 mt-1">
              Comprehensive AI-powered analysis for academic figures
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {state.selectedFigure && (
              <div className="text-sm text-gray-600">
                Selected: <span className="font-medium">{state.selectedFigure.name}</span>
              </div>
            )}
            
            {state.isAnalyzing && (
              <div className="flex items-center space-x-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">{state.analysisProgress.currentStep}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Figure Selection Bar */}
      {state.uploadedFigures.length > 0 && (
        <div className="bg-white border-b border-gray-200 px-6 py-3">
          <div className="flex items-center space-x-4 overflow-x-auto">
            {state.uploadedFigures.map((figure) => (
              <button
                key={figure.id}
                onClick={() => handleFigureSelect(figure)}
                className={`flex-shrink-0 flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                  state.selectedFigure?.id === figure.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="w-8 h-8 bg-gray-200 rounded overflow-hidden">
                  {figure.type.startsWith('image/') && (
                    <img src={figure.url} alt="" className="w-full h-full object-cover" />
                  )}
                </div>
                <span className="text-sm font-medium truncate max-w-32">
                  {figure.name}
                </span>
                {state.analysisResults.has(figure.id) && (
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <div className="w-64 bg-white border-r border-gray-200">
          <nav className="p-4 space-y-2">
            {[
              { id: 'upload', label: 'Upload Figures', icon: Upload },
              { id: 'preview', label: 'Preview', icon: Zap },
              { id: 'quality', label: 'Quality Analysis', icon: BarChart3 },
              { id: 'captions', label: 'Caption Generator', icon: FileText },
              { id: 'advanced', label: 'Advanced Analysis', icon: Brain },
              { id: 'export', label: 'Export Results', icon: Download },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span className="font-medium">{tab.label}</span>
                {tab.id !== 'upload' && tab.id !== 'export' && currentAnalysis && (
                  <div className="ml-auto w-2 h-2 bg-green-500 rounded-full"></div>
                )}
              </button>
            ))}
          </nav>

          {/* Quick Actions */}
          <div className="p-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Quick Actions</h4>
            <div className="space-y-2">
              <button
                onClick={startAnalysis}
                disabled={!state.selectedFigure || state.isAnalyzing}
                className="w-full flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Play className="w-4 h-4" />
                <span className="text-sm">Analyze Figure</span>
              </button>
              
              <button className="w-full flex items-center space-x-2 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Settings className="w-4 h-4" />
                <span className="text-sm">Settings</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-auto">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};
