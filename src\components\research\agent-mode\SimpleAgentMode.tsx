/**
 * Simple Agent Mode Component
 * A basic version of Agent Mode without complex dependencies
 */

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Wand2, 
  Brain, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Lightbulb
} from 'lucide-react';

interface SimpleAgentModeProps {
  documentContent: string;
  onAgentRequest: (prompt: string, editMode: string) => Promise<void>;
  isLoading?: boolean;
  lastResult?: {
    success: boolean;
    summary: string;
    error?: string;
    warnings?: string[];
  };
}

export function SimpleAgentMode({
  documentContent,
  onAgentRequest,
  isLoading = false,
  lastResult
}: SimpleAgentModeProps) {
  const [prompt, setPrompt] = useState('');
  const [editMode, setEditMode] = useState<'conservative' | 'moderate' | 'aggressive'>('moderate');
  const [showExamples, setShowExamples] = useState(false);

  // Example prompts for targeted editing
  const examplePrompts = [
    "Add proper citations to the introduction paragraph",
    "Improve the clarity of technical terms in the methodology section",
    "Enhance the conclusion with stronger impact statements",
    "Add transition sentences between paragraphs for better flow",
    "Strengthen the evidence in the discussion section",
    "Improve the academic tone throughout the document",
    "Add more specific examples to support the main arguments",
    "Enhance the abstract to better highlight key findings"
  ];

  const editModeDescriptions = {
    conservative: "Minimal changes, preserve original style",
    moderate: "Balanced improvements with structural preservation", 
    aggressive: "Substantial improvements, restructure as needed"
  };

  const handleSubmit = async () => {
    if (!prompt.trim() || !documentContent.trim()) return;

    try {
      await onAgentRequest(prompt.trim(), editMode);
    } catch (error) {
      console.error('Agent mode request failed:', error);
    }
  };

  const handleExampleClick = (example: string) => {
    setPrompt(example);
    setShowExamples(false);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Brain className="h-5 w-5 text-purple-600" />
            Agent Mode
            <Badge variant="secondary" className="ml-auto">Beta</Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Make targeted improvements directly in your document without manual copy-paste
          </p>
        </CardHeader>
      </Card>

      {/* Prompt Input */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                What would you like me to do?
              </label>
              <Textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="e.g., Add proper citations to the introduction paragraph"
                className="min-h-[100px] resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Example Prompts */}
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowExamples(!showExamples)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                {showExamples ? 'Hide' : 'Show'} example prompts
              </Button>
              
              {showExamples && (
                <div className="mt-2 space-y-1">
                  {examplePrompts.map((example, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      className="h-auto p-2 text-xs text-left justify-start w-full"
                      onClick={() => handleExampleClick(example)}
                    >
                      "{example}"
                    </Button>
                  ))}
                </div>
              )}
            </div>

            {/* Edit Mode Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">
                Edit Mode
              </label>
              <div className="grid grid-cols-3 gap-2">
                {Object.entries(editModeDescriptions).map(([mode, description]) => (
                  <Button
                    key={mode}
                    variant={editMode === mode ? "default" : "outline"}
                    size="sm"
                    className="h-auto p-2 text-xs"
                    onClick={() => setEditMode(mode as any)}
                    disabled={isLoading}
                  >
                    <div className="text-center">
                      <div className="font-medium capitalize">{mode}</div>
                      <div className="text-xs opacity-70 mt-1">{description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={!prompt.trim() || !documentContent.trim() || isLoading}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Apply Targeted Edits
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Indicator */}
      {isLoading && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm font-medium">
                  Applying targeted edits...
                </span>
              </div>
              <Progress value={50} className="w-full" />
              <p className="text-xs text-gray-500">
                Analyzing document and making precise improvements
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {lastResult && !isLoading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              {lastResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm">{lastResult.summary}</p>

              {lastResult.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {lastResult.error}
                </div>
              )}

              {lastResult.warnings && lastResult.warnings.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-yellow-700">Suggestions:</h4>
                  <div className="space-y-1">
                    {lastResult.warnings.map((warning, index) => (
                      <div key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded border-l-2 border-yellow-300">
                        {warning}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-4">
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-2">🎯 How Agent Mode Works:</h4>
            <ul className="space-y-1 text-xs">
              <li>• Makes targeted edits directly in your document (no copy-paste needed)</li>
              <li>• Focuses on specific improvements you request</li>
              <li>• Preserves your document structure and unchanged content</li>
              <li>• Works with change tracking for easy review and undo</li>
            </ul>
            <h4 className="font-medium mb-2 mt-3">💡 Example requests:</h4>
            <ul className="space-y-1 text-xs">
              <li>• "Add citations to support claims in the introduction"</li>
              <li>• "Improve technical terminology in the methods section"</li>
              <li>• "Strengthen transition sentences between paragraphs"</li>
              <li>• "Enhance academic tone throughout the document"</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
