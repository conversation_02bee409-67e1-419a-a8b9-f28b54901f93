// Data Visualization and Analysis Types

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  data: any[][];
  headers: string[];
  uploadedAt: Date;
  status: 'uploading' | 'processing' | 'ready' | 'error';
  error?: string;
}

// Chat-based visualization types (inspired by VizGPT)
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  vegaSpec?: VegaLiteSpec;
  error?: string;
}

export interface VegaLiteSpec {
  $schema?: string;
  title?: string;
  description?: string;
  data?: any;
  mark?: any;
  encoding?: any;
  transform?: any[];
  width?: number;
  height?: number;
  [key: string]: any;
}

export interface ChatSession {
  id: string;
  fileId: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DataField {
  fid: string;
  name: string;
  semanticType: 'quantitative' | 'nominal' | 'ordinal' | 'temporal';
}

export interface DataAnalysisResult {
  id: string;
  fileId: string;
  summary: {
    rowCount: number;
    columnCount: number;
    dataTypes: Record<string, string>;
    missingValues: Record<string, number>;
    basicStats: Record<string, any>;
  };
  insights: {
    dataQuality: string;
    keyFindings: string[];
    patterns: string[];
    correlations: string[];
    recommendations: string[];
  };
  // Enhanced research-specific analysis
  researchAnalysis?: ResearchAnalysisResult;
  visualizations: VisualizationConfig[];
  generatedAt: Date;
}

// New research-specific analysis types
export interface ResearchAnalysisResult {
  studyType: 'experimental' | 'observational' | 'survey' | 'longitudinal' | 'cross-sectional' | 'mixed';
  sampleAnalysis: {
    sampleSize: number;
    adequacy: 'adequate' | 'marginal' | 'inadequate';
    powerAnalysis?: PowerAnalysisResult;
    demographics?: DemographicAnalysis;
  };
  statisticalTests: StatisticalTestRecommendation[];
  hypotheses: ResearchHypothesis[];
  findings: ResearchFinding[];
  limitations: string[];
  futureResearch: string[];
  methodologyNotes: string[];
}

export interface PowerAnalysisResult {
  estimatedPower: number;
  recommendedSampleSize: number;
  effectSize: number;
  significance: number;
  notes: string[];
}

export interface DemographicAnalysis {
  variables: string[];
  distributions: Record<string, any>;
  representativeness: string;
  biases: string[];
}

export interface StatisticalTestRecommendation {
  test: string;
  purpose: string;
  assumptions: string[];
  variables: string[];
  justification: string;
  priority: 'high' | 'medium' | 'low';
}

export interface ResearchHypothesis {
  type: 'null' | 'alternative' | 'directional' | 'non-directional';
  statement: string;
  variables: string[];
  testable: boolean;
  supportingEvidence?: string;
}

export interface ResearchFinding {
  type: 'significant' | 'non-significant' | 'trend' | 'descriptive';
  description: string;
  statisticalDetails: {
    test?: string;
    statistic?: number;
    pValue?: number;
    effectSize?: number;
    confidenceInterval?: [number, number];
  };
  interpretation: string;
  implications: string[];
  figureReference?: string;
}

export interface VisualizationConfig {
  id: string;
  type: 'histogram' | 'scatter' | 'correlation_heatmap' | 'box_plot' | 'bar_chart' | 'line_chart' | 'pie_chart' |
        'violin_plot' | 'forest_plot' | 'qq_plot' | 'residual_plot' | 'regression_plot' | 'survival_curve' |
        'roc_curve' | 'bland_altman' | 'funnel_plot' | 'heatmap_cluster';
  title: string;
  description: string;
  data: any;
  layout: any;
  config?: any;
  // Research-specific enhancements
  researchContext?: {
    figureNumber: string;
    caption: string;
    statisticalAnnotations: StatisticalAnnotation[];
    interpretation: string;
    methodologyNotes: string[];
    publicationReady: boolean;
  };
}

export interface StatisticalAnnotation {
  type: 'significance' | 'effect_size' | 'confidence_interval' | 'sample_size' | 'correlation' | 'regression';
  value: string | number;
  position: { x: number; y: number };
  label: string;
  significance?: 'p<0.001' | 'p<0.01' | 'p<0.05' | 'ns';
}

export interface QueryRequest {
  id: string;
  fileId: string;
  question: string;
  timestamp: Date;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

export interface QueryResponse {
  id: string;
  requestId: string;
  answer: string;
  code?: string;
  visualizations?: VisualizationConfig[];
  executionTime: number;
  error?: string;
}

export interface GeminiCodeExecutionRequest {
  prompt: string;
  dataContext: {
    fileName: string;
    headers: string[];
    sampleData: any[][];
    dataTypes: Record<string, string>;
  };
}

export interface GeminiCodeExecutionResponse {
  text?: string;
  code?: string;
  result?: any;
  visualizations?: any[];
  images?: string[]; // Base64 encoded images
  error?: string;
}

// Research report generation types
export interface ResearchReport {
  id: string;
  fileId: string;
  analysisId: string;
  title: string;
  abstract: string;
  sections: {
    introduction: string;
    methods: string;
    results: string;
    discussion: string;
    conclusion: string;
    limitations: string;
    futureResearch: string;
  };
  figures: ResearchFigure[];
  tables: ResearchTable[];
  references: string[];
  metadata: {
    generatedAt: Date;
    wordCount: number;
    citationStyle: 'APA' | 'IEEE' | 'Harvard' | 'MLA';
    researchField: string;
  };
}

export interface ResearchFigure {
  id: string;
  number: string;
  title: string;
  caption: string;
  visualization: VisualizationConfig;
  placement: 'inline' | 'appendix';
  quality: 'publication-ready' | 'draft' | 'needs-revision';
}

export interface ResearchTable {
  id: string;
  number: string;
  title: string;
  caption: string;
  data: any[][];
  headers: string[];
  statisticalSummary?: boolean;
}

export interface DataVisualizationState {
  // File management
  uploadedFiles: UploadedFile[];
  currentFile: UploadedFile | null;

  // Analysis results
  analysisResults: Record<string, DataAnalysisResult>;
  currentAnalysis: DataAnalysisResult | null;

  // Research reports
  researchReports: Record<string, ResearchReport>;
  currentReport: ResearchReport | null;

  // Query system
  queries: QueryRequest[];
  queryResponses: Record<string, QueryResponse>;

  // Chat system
  chatMessages: ChatMessage[];
  currentChatSession: ChatSession | null;
  isChatGenerating: boolean;

  // UI state
  isUploading: boolean;
  isAnalyzing: boolean;
  isQuerying: boolean;
  isGeneratingReport: boolean;
  activeTab: 'upload' | 'analysis' | 'visualizations' | 'query' | 'report' | 'chat';
  selectedVisualization: string | null;

  // Error handling
  errors: string[];
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface AnalysisPipelineStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  result?: any;
  error?: string;
}

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  'text/csv': 'CSV',
  'application/vnd.ms-excel': 'Excel (XLS)',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel (XLSX)'
} as const;

export type SupportedFileType = keyof typeof SUPPORTED_FILE_TYPES;

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  WARNING_SIZE: 10 * 1024 * 1024   // 10MB
} as const;

// Visualization types with metadata
export const VISUALIZATION_TYPES = {
  histogram: {
    name: 'Histogram',
    description: 'Distribution of numerical data',
    icon: 'BarChart3',
    requiredDataTypes: ['number']
  },
  scatter: {
    name: 'Scatter Plot',
    description: 'Relationship between two variables',
    icon: 'Circle',
    requiredDataTypes: ['number', 'number']
  },
  correlation_heatmap: {
    name: 'Correlation Heatmap',
    description: 'Correlation matrix of numerical variables',
    icon: 'Grid',
    requiredDataTypes: ['number']
  },
  box_plot: {
    name: 'Box Plot',
    description: 'Distribution and outliers',
    icon: 'Square',
    requiredDataTypes: ['number']
  },
  bar_chart: {
    name: 'Bar Chart',
    description: 'Categorical data comparison',
    icon: 'BarChart3',
    requiredDataTypes: ['string', 'number']
  },
  line_chart: {
    name: 'Line Chart',
    description: 'Trends over time or sequence',
    icon: 'LineChart',
    requiredDataTypes: ['number']
  },
  pie_chart: {
    name: 'Pie Chart',
    description: 'Proportional data representation',
    icon: 'PieChart',
    requiredDataTypes: ['string', 'number']
  }
} as const;
