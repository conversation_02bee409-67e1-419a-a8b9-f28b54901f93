import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, BorderStyle, Table, TableRow, TableCell, WidthType, AlignmentType } from 'docx';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

/**
 * Helper function to parse HTML element and extract formatted text runs
 */
const parseElementFormatting = (element: HTMLElement, font: any): TextRun[] => {
  const runs: TextRun[] = [];

  // If element has no child nodes with formatting, return simple text
  if (!element.querySelector('strong, b, em, i, u, code')) {
    const text = element.textContent?.trim() || '';
    if (text) {
      runs.push(new TextRun({
        text: text,
        font: font,
        size: 24,
        color: "000000"
      }));
    }
    return runs;
  }

  // Process child nodes to handle mixed formatting
  const processNode = (node: Node): void => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim() || '';
      if (text) {
        runs.push(new TextRun({
          text: text,
          font: font,
          size: 24,
          color: "000000"
        }));
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const elem = node as HTMLElement;
      const text = elem.textContent?.trim() || '';
      if (!text) return;

      const tagName = elem.tagName.toLowerCase();
      let textRun: TextRun;

      switch (tagName) {
        case 'strong':
        case 'b':
          textRun = new TextRun({
            text: text,
            bold: true,
            font: font,
            size: 24,
            color: "000000"
          });
          break;
        case 'em':
        case 'i':
          textRun = new TextRun({
            text: text,
            italics: true,
            font: font,
            size: 24,
            color: "000000"
          });
          break;
        case 'u':
          textRun = new TextRun({
            text: text,
            underline: {},
            font: font,
            size: 24,
            color: "000000"
          });
          break;
        case 'code':
          textRun = new TextRun({
            text: text,
            font: { name: "Courier New" },
            size: 22,
            color: "000000",
            highlight: "yellow"
          });
          break;
        default:
          textRun = new TextRun({
            text: text,
            font: font,
            size: 24,
            color: "000000"
          });
      }

      runs.push(textRun);
    }
  };

  // Process all child nodes
  Array.from(element.childNodes).forEach(processNode);

  return runs;
};

/**
 * Converts HTML content from TipTap editor to a Word document
 * Enhanced version that properly handles TipTap's rich text formatting
 */
const htmlToDocx = async (title: string, htmlContent: string): Promise<Document> => {
  // Create document
  const documentChildren = [];

  // Font configuration
  const FONT = {
    name: "Times New Roman",
    ascii: "Times New Roman",
    eastAsia: "Times New Roman",
    hAnsi: "Times New Roman"
  };

  // Add title
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 36,
          color: "000000",
          font: FONT
        }),
      ],
      spacing: { after: 300 },
      alignment: AlignmentType.CENTER
    })
  );

  // Create a sandbox div to parse the HTML content
  const tempDiv = document.createElement('div');

  // Clean and normalize the HTML content for better parsing
  const cleanedHtml = htmlContent
    .replace(/<p><\/p>/g, '<p>&nbsp;</p>') // Empty paragraphs
    .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>') // Paragraphs with just BR
    .replace(/<br\s*\/?>/g, '\n') // Convert BR tags to newlines
    .replace(/&nbsp;/g, ' ') // Convert non-breaking spaces
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  tempDiv.innerHTML = cleanedHtml;
  
  // Process each top-level element 
  Array.from(tempDiv.childNodes).forEach((node) => {
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement;
      const tagName = element.tagName.toLowerCase();
      const text = element.textContent || '';
      
      // Skip empty elements
      if (!text.trim()) return;
      
      // Handle different HTML elements
      switch (tagName) {
        case 'h1':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 32,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 480, after: 240 },
              alignment: AlignmentType.CENTER
            })
          );
          break;
        case 'h2':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 28,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_2,
              spacing: { before: 360, after: 180 },
              alignment: AlignmentType.LEFT
            })
          );
          break;
        case 'h3':
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text,
                  bold: true,
                  size: 26,
                  color: "000000",
                  font: FONT
                })
              ],
              heading: HeadingLevel.HEADING_3,
              spacing: { before: 280, after: 140 },
              alignment: AlignmentType.LEFT
            })
          );
          break;
        case 'p':
          // Enhanced paragraph processing with proper rich text handling
          const paragraphRuns = parseElementFormatting(element, FONT);
          if (paragraphRuns.length > 0) {
            documentChildren.push(
              new Paragraph({
                children: paragraphRuns,
                spacing: { after: 240, before: 0, line: 360 }, // 1.5 line spacing
                alignment: AlignmentType.JUSTIFY,
                indent: { firstLine: 360 } // First line indent
              })
            );
          }
          break;
        case 'ul':
        case 'ol':
          // Handle lists with proper formatting
          const listItems = Array.from(element.children);
          listItems.forEach((li, index) => {
            const listRuns = parseElementFormatting(li as HTMLElement, FONT);
            if (listRuns.length > 0) {
              // Add bullet or number prefix
              const prefix = tagName === 'ul' ? '• ' : `${index + 1}. `;
              const prefixRun = new TextRun({
                text: prefix,
                color: "000000",
                font: FONT,
                size: 24
              });

              documentChildren.push(
                new Paragraph({
                  children: [prefixRun, ...listRuns],
                  indent: { left: 720, hanging: 360 }, // Proper hanging indent
                  spacing: { after: 160, line: 300 }, // Better spacing
                  alignment: AlignmentType.LEFT
                })
              );
            }
          });
          break;
        case 'table':
          // Handle tables
          const tableRows = Array.from(element.querySelectorAll('tr'));
          if (tableRows.length > 0) {
            const docxTable = new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              rows: tableRows.map((row, rowIndex) => {
                const cells = Array.from(row.querySelectorAll('td, th'));
                return new TableRow({
                  children: cells.map(cell => new TableCell({
                    children: [new Paragraph({
                      children: [
                        new TextRun({
                          text: cell.textContent || '',
                          color: "000000",
                          font: FONT,
                          size: 24
                        })
                      ],
                      alignment: AlignmentType.LEFT
                    })],
                    width: {
                      size: 100 / cells.length,
                      type: WidthType.PERCENTAGE,
                    }
                  }))
                });
              })
            });
            documentChildren.push(docxTable);
          }
          break;
        case 'blockquote':
          // Create a quote paragraph with left border
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({ text, color: "000000", font: FONT, size: 24 })
              ],
              border: {
                left: {
                  color: "999999",
                  size: 10,
                  style: BorderStyle.SINGLE,
                  space: 5
                }
              },
              spacing: { after: 120 },
              indent: { left: 240 },
              style: "Quote"
            })
          );
          break;
        default:
          // Default to paragraph for unknown elements
          documentChildren.push(
            new Paragraph({
              children: [
                new TextRun({ text, color: "000000", font: FONT, size: 24 })
              ],
              spacing: { after: 120 }
            })
          );
      }
    }
  });
  
  return new Document({
    sections: [
      {
        properties: {},
        children: documentChildren,
      },
    ],
  });
};

/**
 * Export document as DOCX
 */
const exportToDocx = async (title: string, htmlContent: string, fileName: string = "document.docx"): Promise<void> => {
  try {
    // Create a more reliable method for processing the content
    const doc = await htmlToDocx(title, htmlContent);
    // Use toBlob for browser compatibility
    const blob = await Packer.toBlob(doc);
    const finalFileName = fileName.endsWith('.docx') ? fileName : `${fileName}.docx`;
    
    // Use the proper saveAs function from file-saver
    saveAs(blob, finalFileName);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error exporting to DOCX:', error);
    return Promise.reject(error);
  }
};

/**
 * Export document as PDF using jsPDF
 */
const exportToPdf = async (title: string, htmlContent: string, fileName: string = "document.pdf"): Promise<void> => {
  try {
    // Create a temporary container for rendering
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '0';
    tempContainer.style.width = '210mm'; // A4 width
    tempContainer.style.background = 'white';
    tempContainer.style.padding = '20mm';
    tempContainer.style.fontFamily = 'Georgia, "Times New Roman", serif';
    tempContainer.style.fontSize = '12pt';
    tempContainer.style.lineHeight = '1.6';
    tempContainer.style.color = '#000';
    
    // Add title and content with improved styling
    tempContainer.innerHTML = `
      <style>
        h1 { font-size: 18pt; margin-bottom: 24pt; border-bottom: 2px solid #ccc; padding-bottom: 8pt; text-align: center; font-weight: bold; }
        h2 { font-size: 16pt; margin-top: 20pt; margin-bottom: 12pt; font-weight: bold; }
        h3 { font-size: 14pt; margin-top: 16pt; margin-bottom: 10pt; font-weight: bold; }
        p { margin-bottom: 12pt; text-align: justify; line-height: 1.6; }
        ul, ol { margin-bottom: 12pt; padding-left: 20pt; }
        li { margin-bottom: 6pt; line-height: 1.5; }
        strong, b { font-weight: bold; }
        em, i { font-style: italic; }
        code { font-family: 'Courier New', monospace; background-color: #f5f5f5; padding: 2pt 4pt; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 12pt; }
        th, td { border: 1px solid #ccc; padding: 8pt; text-align: left; }
        th { background-color: #f5f5f5; font-weight: bold; }
      </style>
      <h1>${title}</h1>
      ${htmlContent}
    `;
    
    document.body.appendChild(tempContainer);
    
    // Create PDF using html2canvas and jsPDF
    const canvas = await html2canvas(tempContainer, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: tempContainer.scrollWidth,
      height: tempContainer.scrollHeight
    });
    
    // Remove temporary container
    document.body.removeChild(tempContainer);
    
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;
    
    // Add first page
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;
    
    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }
    
    // Save the PDF
    pdf.save(fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`);
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return Promise.reject(error);
  }
};

export const documentExportService = {
  exportToDocx,
  exportToPdf,
  htmlToDocx
};
