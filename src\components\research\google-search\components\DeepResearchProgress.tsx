/**
 * Deep Research Progress Component
 * Shows multi-phase progress tracking for deep research
 */

import React from 'react';
import { 
  Search, 
  BookOpen, 
  PenTool, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

import { DeepResearchProgress as ProgressType, SubTopic, ResearchSection } from '../types';

interface DeepResearchProgressProps {
  progress: ProgressType;
  subtopics: SubTopic[];
  completedSections: ResearchSection[];
  className?: string;
}

export function DeepResearchProgress({
  progress,
  subtopics,
  completedSections,
  className = ''
}: DeepResearchProgressProps) {
  const phases = [
    {
      id: 'outline',
      name: 'Outline',
      icon: BookOpen,
      description: 'Generate research structure'
    },
    {
      id: 'research',
      name: 'Research',
      icon: Search,
      description: 'Gather information from sources'
    },
    {
      id: 'writing',
      name: 'Writing',
      icon: PenTool,
      description: 'Generate detailed sections'
    },
    {
      id: 'assembly',
      name: 'Assembly',
      icon: FileText,
      description: 'Compile final report'
    }
  ];

  const getPhaseStatus = (phaseId: string) => {
    const currentPhaseIndex = phases.findIndex(p => p.id === progress.phase);
    const phaseIndex = phases.findIndex(p => p.id === phaseId);
    
    if (phaseIndex < currentPhaseIndex) return 'completed';
    if (phaseIndex === currentPhaseIndex) return 'active';
    return 'pending';
  };

  const formatTimeRemaining = (seconds?: number) => {
    if (!seconds) return 'Calculating...';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Research Progress</span>
            <Badge variant={progress.phase === 'completed' ? 'default' : 'secondary'}>
              {Math.round(progress.percentage)}%
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Progress value={progress.percentage} className="h-3" />
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {progress.completedSteps} of {progress.totalSteps} steps completed
            </span>
            {progress.estimatedTimeRemaining && (
              <span className="text-gray-600 flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{formatTimeRemaining(progress.estimatedTimeRemaining)}</span>
              </span>
            )}
          </div>

          {/* Current Step */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
              <span className="text-sm font-medium text-blue-900">
                {progress.currentStep}
              </span>
            </div>
            {progress.currentSubtopic && (
              <div className="mt-1 text-xs text-blue-700">
                Working on: {progress.currentSubtopic}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Phase Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Research Phases</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {phases.map((phase, index) => {
              const status = getPhaseStatus(phase.id);
              const Icon = phase.icon;
              
              return (
                <div key={phase.id} className="flex items-center space-x-4">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center
                    ${status === 'completed' ? 'bg-green-100 text-green-600' : 
                      status === 'active' ? 'bg-blue-100 text-blue-600' : 
                      'bg-gray-100 text-gray-400'}
                  `}>
                    {status === 'completed' ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className={`font-medium ${
                        status === 'active' ? 'text-blue-900' : 
                        status === 'completed' ? 'text-green-900' : 
                        'text-gray-500'
                      }`}>
                        {phase.name}
                      </h4>
                      {status === 'active' && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          In Progress
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{phase.description}</p>
                  </div>
                  
                  {index < phases.length - 1 && (
                    <div className={`w-px h-8 ${
                      status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Section Progress */}
      {subtopics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Section Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {subtopics.map((subtopic, index) => {
                  const isCompleted = completedSections.some(cs => cs.subtopicId === subtopic.id);
                  const isActive = progress.currentSubtopic === subtopic.title;
                  
                  return (
                    <div key={subtopic.id} className="flex items-center space-x-3">
                      <div className={`
                        w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                        ${isCompleted ? 'bg-green-100 text-green-600' : 
                          isActive ? 'bg-blue-100 text-blue-600' : 
                          'bg-gray-100 text-gray-500'}
                      `}>
                        {isCompleted ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          index + 1
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h5 className={`text-sm font-medium ${
                          isActive ? 'text-blue-900' : 
                          isCompleted ? 'text-green-900' : 
                          'text-gray-700'
                        }`}>
                          {subtopic.title}
                        </h5>
                        <p className="text-xs text-gray-500">
                          {subtopic.estimatedWords} words • {subtopic.priority} priority
                        </p>
                      </div>
                      
                      <div>
                        {isCompleted && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Complete
                          </Badge>
                        )}
                        {isActive && (
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                            Writing...
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Errors */}
      {progress.errors && progress.errors.length > 0 && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>Issues Encountered</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {progress.errors.map((error, index) => (
                <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
