/**
 * Inline Citation Component
 * Displays citations inline with text content for better academic formatting
 */

import React, { useState } from 'react';
import { ExternalLink, Quote, Calendar, User, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Citation } from '../types';
import { cn } from '@/lib/utils';

interface InlineCitationProps {
  citation: Citation;
  index: number;
  className?: string;
}

export function InlineCitation({ citation, index, className }: InlineCitationProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpenSource = () => {
    window.open(citation.url, '_blank');
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "inline-flex items-center h-6 px-2 mx-1 text-xs font-bold rounded-lg",
            "bg-gradient-to-r from-indigo-100 to-blue-100 hover:from-indigo-200 hover:to-blue-200",
            "text-indigo-700 border border-indigo-300 hover:border-indigo-400",
            "transition-all duration-200 hover:scale-105 cursor-pointer shadow-sm hover:shadow-md",
            className
          )}
        >
          [{index + 1}]
        </Button>
      </PopoverTrigger>
      
      <PopoverContent
        className="w-96 p-0 border-2 border-indigo-200 shadow-2xl rounded-2xl"
        align="start"
        side="top"
      >
        <Card className="border-0 shadow-none rounded-2xl overflow-hidden">
          <CardContent className="p-6 space-y-4">
            {/* Academic Citation Header */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center shadow-sm">
                <Quote className="w-5 h-5 text-indigo-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-bold text-base text-gray-900 leading-tight mb-2">
                  {citation.title}
                </h4>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs bg-indigo-50 text-indigo-700 border-indigo-200 font-semibold">
                    Reference [{index + 1}]
                  </Badge>
                </div>
              </div>
            </div>

            {/* Academic Citation Details */}
            <div className="space-y-3 text-sm">
              {citation.author && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                  <User className="w-4 h-4 text-gray-500" />
                  <span className="font-semibold text-gray-800">{citation.author}</span>
                </div>
              )}

              {citation.publishedDate && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <span className="font-semibold text-gray-800">
                    Published: {new Date(citation.publishedDate).getFullYear()}
                  </span>
                </div>
              )}

              {citation.journal && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                  <BookOpen className="w-4 h-4 text-gray-500" />
                  <span className="italic font-semibold text-gray-800">{citation.journal}</span>
                </div>
              )}
            </div>

            {/* Academic Format Preview */}
            <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-xs text-gray-700 font-mono leading-relaxed">
                {citation.author && `${citation.author}. `}
                {citation.title}. 
                {citation.journal && ` ${citation.journal}.`}
                {citation.publishedDate && ` ${new Date(citation.publishedDate).getFullYear()}.`}
              </p>
            </div>

            {/* Academic Action Button */}
            <Button
              onClick={handleOpenSource}
              className="w-full h-10 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Access Academic Source
            </Button>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}

/**
 * Enhanced Text with Citations Component
 * Processes text content and adds inline citations
 */
interface TextWithCitationsProps {
  content: string;
  citations?: Citation[];
  className?: string;
}

export function TextWithCitations({ content, citations = [], className }: TextWithCitationsProps) {
  // Enhanced academic text formatting function
  const formatText = (text: string) => {
    // Handle multiple formatting types in sequence
    let formattedText = text;

    // First, handle bold text (**text** or __text__)
    formattedText = formattedText.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-bold text-gray-900">$1</strong>');
    formattedText = formattedText.replace(/__([^_]+)__/g, '<strong class="font-bold text-gray-900">$1</strong>');

    // Handle italic text (*text* or _text_) - but not if already in bold
    formattedText = formattedText.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em class="italic text-gray-800">$1</em>');
    formattedText = formattedText.replace(/(?<!_)_([^_]+)_(?!_)/g, '<em class="italic text-gray-800">$1</em>');

    // Handle inline code
    formattedText = formattedText.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800">$1</code>');

    // Convert back to React elements
    return <span dangerouslySetInnerHTML={{ __html: formattedText }} />;
  };

  // Enhanced academic content processing
  const processAcademicContent = (content: string) => {
    // Split content into sections and clean up
    const sections = content.split(/\n\s*\n/).filter(section => section.trim());

    return sections.map((section, sectionIndex) => {
      const trimmedSection = section.trim();

      // Handle different types of content
      if (trimmedSection.startsWith('#')) {
        return processHeading(trimmedSection, sectionIndex);
      } else if (trimmedSection.match(/^[\d\-\*•]\s/m)) {
        return processList(trimmedSection, sectionIndex);
      } else if (trimmedSection.toLowerCase().includes('references') && sectionIndex > 0) {
        return processReferences(trimmedSection, sectionIndex);
      } else {
        return processParagraph(trimmedSection, sectionIndex);
      }
    });
  };

  const processHeading = (heading: string, index: number) => {
    const level = heading.match(/^#+/)?.[0].length || 1;
    const text = heading.replace(/^#+\s*/, '').trim();

    const getHeadingClass = (level: number) => {
      switch (level) {
        case 1: return "text-2xl font-bold text-gray-900 mb-6 mt-8 first:mt-0 border-b-2 border-gray-300 pb-3";
        case 2: return "text-xl font-bold text-gray-900 mb-4 mt-6 first:mt-0 border-b border-gray-200 pb-2";
        case 3: return "text-lg font-semibold text-gray-900 mb-3 mt-5 first:mt-0";
        case 4: return "text-base font-semibold text-gray-900 mb-2 mt-4 first:mt-0";
        default: return "text-sm font-semibold text-gray-900 mb-2 mt-3 first:mt-0";
      }
    };

    const HeadingTag = `h${Math.min(level + 1, 6)}` as keyof JSX.IntrinsicElements;
    return (
      <HeadingTag key={`heading-${index}`} className={getHeadingClass(level)}>
        {formatText(text)}
      </HeadingTag>
    );
  };

  const processList = (listContent: string, index: number) => {
    const items = listContent.split('\n').filter(item => item.trim());
    const isNumbered = listContent.match(/^\d+\.\s/);

    const ListTag = isNumbered ? 'ol' : 'ul';
    const listClass = isNumbered
      ? "list-decimal list-outside space-y-2 mb-6 text-gray-800 pl-6"
      : "list-disc list-outside space-y-2 mb-6 text-gray-800 pl-6";

    return (
      <ListTag key={`list-${index}`} className={listClass}>
        {items.map((item, itemIndex) => {
          const cleanItem = item.replace(/^[\d\-\*•]+\.?\s*/, '').trim();
          return (
            <li key={itemIndex} className="leading-relaxed text-base text-gray-800 mb-1 pl-1">
              {formatText(cleanItem)}
            </li>
          );
        })}
      </ListTag>
    );
  };

  const processReferences = (referencesContent: string, index: number) => {
    const lines = referencesContent.split('\n').filter(line => line.trim());

    return (
      <div key={`references-${index}`} className="mt-8 pt-6 border-t-2 border-gray-300">
        <h3 className="text-xl font-bold text-gray-900 mb-6">References</h3>
        <div className="space-y-3">
          {lines.slice(1).map((line, lineIndex) => {
            if (line.trim()) {
              return (
                <div key={lineIndex} className="text-sm leading-relaxed text-gray-700 pl-4 -indent-4">
                  {formatText(line.trim())}
                </div>
              );
            }
            return null;
          })}
        </div>
      </div>
    );
  };

  const processParagraph = (paragraph: string, index: number) => {
    // Handle special paragraph types
    if (paragraph.startsWith('>')) {
      // Blockquote
      const cleanText = paragraph.replace(/^>\s*/, '');
      return (
        <blockquote key={`quote-${index}`} className="border-l-4 border-blue-500 pl-4 py-2 mb-6 bg-blue-50 italic text-gray-700">
          {formatText(cleanText)}
        </blockquote>
      );
    }

    return (
      <p key={`paragraph-${index}`} className="mb-6 last:mb-0 text-base leading-relaxed text-gray-800 text-justify">
        {formatText(paragraph)}
      </p>
    );
  };

  if (!citations.length) {
    return (
      <div className={cn("prose prose-lg max-w-none", className)}>
        {processAcademicContent(content)}
      </div>
    );
  }

  // Enhanced academic text processing with proper academic citations
  const processContentWithCitations = (content: string) => {
    // Split content into sections for better processing
    const sections = content.split(/\n\s*\n/).filter(section => section.trim());

    return sections.map((section, sectionIndex) => {
      const trimmedSection = section.trim();

      // Handle different content types
      if (trimmedSection.startsWith('#')) {
        return processHeadingWithCitations(trimmedSection, sectionIndex);
      } else if (trimmedSection.match(/^[\d\-\*•]\s/m)) {
        return processListWithCitations(trimmedSection, sectionIndex);
      } else if (trimmedSection.toLowerCase().includes('references')) {
        return processReferencesWithCitations(trimmedSection, sectionIndex);
      } else {
        return processParagraphWithCitations(trimmedSection, sectionIndex);
      }
    });
  };

  const processHeadingWithCitations = (heading: string, index: number) => {
    const level = heading.match(/^#+/)?.[0].length || 1;
    const text = heading.replace(/^#+\s*/, '').trim();

    const getHeadingClass = (level: number) => {
      switch (level) {
        case 1: return "text-2xl font-bold text-gray-900 mb-6 mt-8 first:mt-0 border-b-2 border-gray-300 pb-3";
        case 2: return "text-xl font-bold text-gray-900 mb-4 mt-6 first:mt-0 border-b border-gray-200 pb-2";
        case 3: return "text-lg font-semibold text-gray-900 mb-3 mt-5 first:mt-0";
        case 4: return "text-base font-semibold text-gray-900 mb-2 mt-4 first:mt-0";
        default: return "text-sm font-semibold text-gray-900 mb-2 mt-3 first:mt-0";
      }
    };

    const HeadingTag = `h${Math.min(level + 1, 6)}` as keyof JSX.IntrinsicElements;
    return (
      <HeadingTag key={`heading-${index}`} className={getHeadingClass(level)}>
        {processTextWithCitations(text, index)}
      </HeadingTag>
    );
  };

  const processListWithCitations = (listContent: string, index: number) => {
    const items = listContent.split('\n').filter(item => item.trim());
    const isNumbered = listContent.match(/^\d+\.\s/);

    const ListTag = isNumbered ? 'ol' : 'ul';
    const listClass = isNumbered
      ? "list-decimal list-outside space-y-2 mb-6 text-gray-800 pl-6"
      : "list-disc list-outside space-y-2 mb-6 text-gray-800 pl-6";

    return (
      <ListTag key={`list-${index}`} className={listClass}>
        {items.map((item, itemIndex) => {
          const cleanItem = item.replace(/^[\d\-\*•]+\.?\s*/, '').trim();
          return (
            <li key={itemIndex} className="leading-relaxed text-base text-gray-800 mb-1 pl-1">
              {processTextWithCitations(cleanItem, `${index}-${itemIndex}`)}
            </li>
          );
        })}
      </ListTag>
    );
  };

  const processReferencesWithCitations = (referencesContent: string, index: number) => {
    const lines = referencesContent.split('\n').filter(line => line.trim());

    return (
      <div key={`references-${index}`} className="mt-8 pt-6 border-t-2 border-gray-300">
        <h3 className="text-xl font-bold text-gray-900 mb-6">References</h3>
        <div className="space-y-3">
          {lines.slice(1).map((line, lineIndex) => {
            if (line.trim()) {
              return (
                <div key={lineIndex} className="text-sm leading-relaxed text-gray-700 pl-4 -indent-4">
                  {processTextWithCitations(line.trim(), `ref-${index}-${lineIndex}`)}
                </div>
              );
            }
            return null;
          })}
        </div>
      </div>
    );
  };

  const processParagraphWithCitations = (paragraph: string, index: number) => {
    // Handle special paragraph types
    if (paragraph.startsWith('>')) {
      const cleanText = paragraph.replace(/^>\s*/, '');
      return (
        <blockquote key={`quote-${index}`} className="border-l-4 border-blue-500 pl-4 py-2 mb-6 bg-blue-50 italic text-gray-700">
          {processTextWithCitations(cleanText, index)}
        </blockquote>
      );
    }

    return (
      <p key={`paragraph-${index}`} className="mb-6 last:mb-0 text-base leading-relaxed text-gray-800 text-justify">
        {processTextWithCitations(paragraph, index)}
      </p>
    );
  };

  const processTextWithCitations = (text: string, index: any) => {
    // Enhanced citation processing for academic format
    const citationPatterns = [
      // Academic parenthetical citations: (Author, Year)
      /(\([A-Z][a-zA-Z\s]+,\s*\d{4}\))/g,
      // Multiple authors: (Smith et al., 2023)
      /(\([A-Z][a-zA-Z\s]+\s+et\s+al\.?,\s*\d{4}\))/g,
      // Organization citations: (WHO, 2023)
      /(\([A-Z]{2,},\s*\d{4}\))/g,
      // Multiple citations: (Smith, 2023; Johnson, 2022)
      /(\([A-Z][a-zA-Z\s,;]+\d{4}[^)]*\))/g,
      // Legacy numbered citations [1] - convert to academic style
      /(\[\d+\])/g
    ];

    let processedText = text;
    let parts: string[] = [text];

    // Split text by all citation patterns
    citationPatterns.forEach(pattern => {
      const newParts: string[] = [];
      parts.forEach(part => {
        if (typeof part === 'string') {
          newParts.push(...part.split(pattern));
        } else {
          newParts.push(part);
        }
      });
      parts = newParts;
    });

    // Process each part
    return parts.map((part, partIndex) => {
      if (!part) return null;

      // Check if this part is a citation
      const isAcademicCitation = part.match(/^\([A-Z][a-zA-Z\s,;]+\d{4}[^)]*\)$/);
      const isNumberedCitation = part.match(/^\[\d+\]$/);

      if (isAcademicCitation) {
        // Keep academic citations as-is with styling
        return (
          <span key={`citation-${index}-${partIndex}`} className="font-medium text-blue-700 bg-blue-50 px-1 rounded">
            {part}
          </span>
        );
      }

      if (isNumberedCitation) {
        // Convert numbered citations to academic style if possible
        const citationNumber = parseInt(part.replace(/[\[\]]/g, ''));
        const citation = citations[citationNumber - 1];

        if (citation && citation.author) {
          const year = citation.publishedDate ? new Date(citation.publishedDate).getFullYear() : 'n.d.';
          const academicStyle = `(${citation.author}, ${year})`;
          return (
            <span key={`converted-citation-${index}-${partIndex}`} className="font-medium text-blue-700 bg-blue-50 px-1 rounded">
              {academicStyle}
            </span>
          );
        }

        // Fallback to original if no matching citation
        return (
          <span key={`fallback-citation-${index}-${partIndex}`} className="font-medium text-red-600">
            {part}
          </span>
        );
      }

      // Regular text - apply formatting
      return (
        <span key={`text-${index}-${partIndex}`}>
          {formatText(part)}
        </span>
      );
    }).filter(Boolean);
  };

  return (
    <div className={cn("prose prose-lg max-w-none", className)}>
      {processContentWithCitations(content)}
    </div>
  );
}
