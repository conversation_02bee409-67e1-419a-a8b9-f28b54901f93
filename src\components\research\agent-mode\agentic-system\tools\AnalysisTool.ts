/**
 * AnalysisTool - Analyzes user requests and determines editing strategy
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { Tool<PERSON>ontext, ToolR<PERSON>ult, AnalysisResult, AnalysisToolResult, SearchResult } from '../types';

export class AnalysisTool extends BaseAgentTool {
  constructor() {
    super(
      'analysis-tool',
      'Request Analysis',
      'Analyzes user requests to understand intent and determine the best editing approach'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { userRequest, documentContent, previousResults } = context;
    
    console.log(`🧠 [AnalysisTool] Analyzing request: "${userRequest}"`);

    try {
      // Get search results from previous step
      const searchResults = this.extractSearchResults(previousResults);
      
      // Analyze the user request
      const intent = this.analyzeIntent(userRequest);
      console.log(`🎯 [AnalysisTool] Detected intent: ${intent}`);

      // Determine edit type and scope
      const editType = this.determineEditType(userRequest, intent);
      const scope = this.determineScope(userRequest, searchResults);
      
      // Analyze complexity
      const complexity = this.analyzeComplexity(userRequest, searchResults, documentContent);
      
      // Extract requirements
      const requirements = this.extractRequirements(userRequest, intent);
      
      // Filter and prioritize target sections
      let targetSections = this.prioritizeTargetSections(searchResults, intent, editType);

      // If no target sections found, create a fallback section for the entire document
      if (targetSections.length === 0 && documentContent.trim().length > 0) {
        console.log(`⚠️ [AnalysisTool] No specific sections found, using entire document as target`);
        targetSections = [{
          text: documentContent.substring(0, Math.min(500, documentContent.length)), // First 500 chars
          startPosition: 0,
          endPosition: Math.min(500, documentContent.length),
          relevanceScore: 0.5,
          context: 'Entire document',
          sectionType: 'document'
        }];
      }

      const analysisResult: AnalysisResult = {
        intent,
        targetSections,
        editType,
        scope,
        complexity,
        requirements
      };

      // Calculate confidence based on analysis quality
      const confidence = this.calculateAnalysisConfidence(
        userRequest,
        targetSections,
        intent,
        complexity
      );

      const reasoning = this.generateAnalysisReasoning(analysisResult);

      return this.createSuccessResult(
        startTime,
        analysisResult,
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Analysis failed: ${error.message}`,
        ['Try being more specific about what you want to change', 'Provide more context about the desired outcome']
      );
    }
  }

  /**
   * Extract search results from previous tool executions
   */
  private extractSearchResults(previousResults?: ToolResult[]): SearchResult[] {
    if (!previousResults) return [];
    
    const searchResult = previousResults.find(r => r.toolId === 'search-tool' && r.success);
    return searchResult?.data?.matches || [];
  }

  /**
   * Analyze the user's intent from their request
   */
  private analyzeIntent(userRequest: string): string {
    const request = userRequest.toLowerCase();
    
    // Intent patterns
    const intentPatterns = {
      'fix_errors': ['fix', 'correct', 'error', 'mistake', 'typo', 'wrong'],
      'improve_clarity': ['clarify', 'clear', 'clearer', 'understand', 'confusing', 'unclear'],
      'enhance_style': ['improve', 'enhance', 'better', 'style', 'tone', 'academic', 'professional'],
      'add_content': ['add', 'include', 'insert', 'expand', 'elaborate', 'more'],
      'remove_content': ['remove', 'delete', 'cut', 'eliminate', 'reduce'],
      'restructure': ['reorganize', 'restructure', 'reorder', 'rearrange', 'move'],
      'format': ['format', 'formatting', 'layout', 'structure', 'organize'],
      'citations': ['citation', 'reference', 'cite', 'source', 'bibliography'],
      'grammar': ['grammar', 'grammatical', 'sentence', 'tense', 'agreement'],
      'vocabulary': ['vocabulary', 'word', 'term', 'terminology', 'language']
    };

    // Find the best matching intent
    let bestIntent = 'general_improvement';
    let maxMatches = 0;

    for (const [intent, keywords] of Object.entries(intentPatterns)) {
      const matches = keywords.filter(keyword => request.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        bestIntent = intent;
      }
    }

    return bestIntent;
  }

  /**
   * Determine the type of edit needed
   */
  private determineEditType(userRequest: string, intent: string): 'replace' | 'insert' | 'enhance' | 'rewrite' {
    const request = userRequest.toLowerCase();

    // Explicit edit type indicators
    if (request.includes('replace') || request.includes('substitute')) return 'replace';
    if (request.includes('add') || request.includes('insert') || request.includes('include')) return 'insert';
    if (request.includes('rewrite') || request.includes('completely') || request.includes('from scratch')) return 'rewrite';

    // Intent-based edit type mapping
    const intentEditTypeMap: Record<string, 'replace' | 'insert' | 'enhance' | 'rewrite'> = {
      'fix_errors': 'replace',
      'improve_clarity': 'enhance',
      'enhance_style': 'enhance',
      'add_content': 'insert',
      'remove_content': 'replace',
      'restructure': 'rewrite',
      'format': 'enhance',
      'citations': 'insert',
      'grammar': 'replace',
      'vocabulary': 'replace'
    };

    return intentEditTypeMap[intent] || 'enhance';
  }

  /**
   * Determine the scope of the edit
   */
  private determineScope(userRequest: string, searchResults: SearchResult[]): 'word' | 'sentence' | 'paragraph' | 'section' | 'document' {
    const request = userRequest.toLowerCase();

    // Explicit scope indicators
    if (request.includes('word') || request.includes('term')) return 'word';
    if (request.includes('sentence')) return 'sentence';
    if (request.includes('paragraph')) return 'paragraph';
    if (request.includes('section') || request.includes('chapter')) return 'section';
    if (request.includes('document') || request.includes('entire') || request.includes('whole')) return 'document';

    // Infer scope from search results
    if (searchResults.length === 0) return 'document';
    
    const avgResultLength = searchResults.reduce((sum, result) => sum + result.text.length, 0) / searchResults.length;
    
    if (avgResultLength < 50) return 'word';
    if (avgResultLength < 200) return 'sentence';
    if (avgResultLength < 500) return 'paragraph';
    if (avgResultLength < 2000) return 'section';
    
    return 'document';
  }

  /**
   * Analyze the complexity of the requested edit
   */
  private analyzeComplexity(
    userRequest: string,
    searchResults: SearchResult[],
    documentContent: string
  ): 'simple' | 'moderate' | 'complex' {
    let complexityScore = 0;

    // Request complexity factors
    const requestWords = userRequest.split(/\s+/).length;
    if (requestWords > 20) complexityScore += 1;
    if (requestWords > 50) complexityScore += 1;

    // Multiple targets increase complexity
    if (searchResults.length > 5) complexityScore += 1;
    if (searchResults.length > 10) complexityScore += 2;

    // Document size affects complexity
    const docComplexity = this.analyzeTextComplexity(documentContent);
    if (docComplexity.complexity === 'complex') complexityScore += 2;
    if (docComplexity.complexity === 'moderate') complexityScore += 1;

    // Intent-based complexity
    const complexIntents = ['restructure', 'rewrite', 'enhance_style'];
    if (complexIntents.some(intent => userRequest.toLowerCase().includes(intent))) {
      complexityScore += 2;
    }

    // Determine final complexity
    if (complexityScore >= 4) return 'complex';
    if (complexityScore >= 2) return 'moderate';
    return 'simple';
  }

  /**
   * Extract specific requirements from the user request
   */
  private extractRequirements(userRequest: string, intent: string): string[] {
    const requirements: string[] = [];
    const request = userRequest.toLowerCase();

    // Style requirements
    if (request.includes('academic')) requirements.push('Use academic tone');
    if (request.includes('formal')) requirements.push('Use formal language');
    if (request.includes('casual') || request.includes('informal')) requirements.push('Use casual tone');
    if (request.includes('professional')) requirements.push('Use professional language');

    // Content requirements
    if (request.includes('citation') || request.includes('reference')) requirements.push('Add proper citations');
    if (request.includes('example')) requirements.push('Include examples');
    if (request.includes('evidence')) requirements.push('Provide supporting evidence');
    if (request.includes('statistic')) requirements.push('Include relevant statistics');

    // Structure requirements
    if (request.includes('transition')) requirements.push('Improve transitions');
    if (request.includes('flow')) requirements.push('Enhance logical flow');
    if (request.includes('coherent')) requirements.push('Ensure coherence');
    if (request.includes('organize')) requirements.push('Improve organization');

    // Quality requirements
    if (request.includes('clear') || request.includes('clarity')) requirements.push('Improve clarity');
    if (request.includes('concise')) requirements.push('Make more concise');
    if (request.includes('detailed')) requirements.push('Add more detail');
    if (request.includes('precise')) requirements.push('Use precise language');

    // Default requirements based on intent
    if (requirements.length === 0) {
      const defaultRequirements: Record<string, string[]> = {
        'fix_errors': ['Correct errors while preserving meaning'],
        'improve_clarity': ['Enhance readability and understanding'],
        'enhance_style': ['Improve writing style and tone'],
        'add_content': ['Add relevant and valuable content'],
        'remove_content': ['Remove while maintaining coherence'],
        'restructure': ['Improve logical organization'],
        'format': ['Enhance formatting and structure'],
        'citations': ['Add proper academic citations'],
        'grammar': ['Fix grammatical issues'],
        'vocabulary': ['Improve word choice and terminology']
      };

      requirements.push(...(defaultRequirements[intent] || ['Make targeted improvements']));
    }

    return requirements;
  }

  /**
   * Prioritize target sections based on relevance and intent
   */
  private prioritizeTargetSections(
    searchResults: SearchResult[],
    intent: string,
    editType: string
  ): SearchResult[] {
    // Sort by relevance score
    const sorted = [...searchResults].sort((a, b) => b.relevanceScore - a.relevanceScore);

    // Apply intent-based filtering
    const filtered = this.filterByIntent(sorted, intent);

    // Limit based on edit type
    const maxSections = this.getMaxSectionsForEditType(editType);
    
    return filtered.slice(0, maxSections);
  }

  /**
   * Filter search results based on intent
   */
  private filterByIntent(results: SearchResult[], intent: string): SearchResult[] {
    // For certain intents, prefer specific section types
    const intentSectionPreferences: Record<string, string[]> = {
      'citations': ['methodology', 'results', 'discussion'],
      'improve_clarity': ['introduction', 'methodology', 'discussion'],
      'enhance_style': ['introduction', 'conclusion'],
      'fix_errors': ['other'], // No specific preference
      'add_content': ['results', 'discussion', 'conclusion']
    };

    const preferredSections = intentSectionPreferences[intent];
    if (!preferredSections) return results;

    // Boost relevance for preferred sections
    return results.map(result => ({
      ...result,
      relevanceScore: preferredSections.includes(result.sectionType || 'other')
        ? result.relevanceScore * 1.2
        : result.relevanceScore
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Get maximum sections to target based on edit type (optimized for targeted editing)
   */
  private getMaxSectionsForEditType(editType: string): number {
    // Reduced limits to ensure more targeted, precise editing
    const limits = {
      'replace': 2,  // Focus on fewer sections for precise replacement
      'insert': 3,   // Limit insertions to maintain document coherence
      'enhance': 3,  // Fewer sections for quality enhancement
      'rewrite': 1   // Only one section at a time for rewrites
    };

    console.log(`🎯 [AnalysisTool] Limiting to ${limits[editType] || 2} sections for targeted ${editType} editing`);
    return limits[editType] || 2;
  }

  /**
   * Calculate confidence in the analysis
   */
  private calculateAnalysisConfidence(
    userRequest: string,
    targetSections: SearchResult[],
    intent: string,
    complexity: string
  ): number {
    const requestClarity = userRequest.length > 10 && userRequest.split(' ').length > 3 ? 0.8 : 0.5;
    const targetQuality = targetSections.length > 0 ? 
      Math.min(targetSections.reduce((sum, t) => sum + t.relevanceScore, 0) / targetSections.length, 1) : 0;
    const intentConfidence = intent !== 'general_improvement' ? 0.8 : 0.6;
    const complexityPenalty = complexity === 'complex' ? 0.8 : 1.0;

    return this.calculateConfidence({
      requestClarity,
      contextRelevance: targetQuality,
      resultQuality: intentConfidence
    }) * complexityPenalty;
  }

  /**
   * Generate reasoning explanation for the analysis
   */
  private generateAnalysisReasoning(analysis: AnalysisResult): string {
    const { intent, targetSections, editType, scope, complexity } = analysis;
    
    return `Analyzed request as "${intent}" requiring ${editType} edits at ${scope} level. ` +
           `Found ${targetSections.length} target section(s) with ${complexity} complexity. ` +
           `Will focus on: ${analysis.requirements.slice(0, 2).join(', ')}.`;
  }
}
