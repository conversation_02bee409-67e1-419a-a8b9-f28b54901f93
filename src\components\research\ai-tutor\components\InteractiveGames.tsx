/**
 * Interactive Games Component
 * Provides engaging educational games and interactive learning experiences
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Gamepad2, 
  Trophy, 
  Target, 
  Brain,
  BookOpen,
  Zap,
  Award,
  Medal,
  Crown,
  Flame,
  CheckCircle,
  Lock,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Shield,
  Heart,
  Play,
  Star,
  Timer,
  RotateCcw,
  Shuffle
} from "lucide-react";
import { ResearchDocument } from '../types';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';

interface InteractiveGamesProps {
  selectedDocument: ResearchDocument | null;
  uploadedDocuments: ResearchDocument[];
}

interface GameCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  category: 'Memory' | 'Logic' | 'Knowledge' | 'Speed' | 'Creative';
  requiresDocument: boolean;
  color: string;
}

const availableGames: GameCard[] = [
  {
    id: 'word-association',
    title: 'Word Association',
    description: 'Connect related concepts and terms from your learning materials',
    icon: Brain,
    difficulty: 'Easy',
    estimatedTime: '5-10 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-blue-500'
  },
  {
    id: 'concept-builder',
    title: 'Concept Builder',
    description: 'Build concept maps by connecting ideas and relationships',
    icon: Target,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Logic',
    requiresDocument: false,
    color: 'bg-green-500'
  },
  {
    id: 'quick-quiz',
    title: 'Lightning Quiz',
    description: 'Fast-paced questions to test your knowledge',
    icon: Zap,
    difficulty: 'Medium',
    estimatedTime: '3-5 min',
    category: 'Speed',
    requiresDocument: false,
    color: 'bg-yellow-500'
  },
  {
    id: 'memory-palace',
    title: 'Memory Palace',
    description: 'Create visual memory aids for complex information',
    icon: Crown,
    difficulty: 'Hard',
    estimatedTime: '15-20 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-purple-500'
  },
  {
    id: 'document-detective',
    title: 'Document Detective',
    description: 'Find key insights and connections in your uploaded documents',
    icon: BookOpen,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Knowledge',
    requiresDocument: true,
    color: 'bg-indigo-500'
  },
  {
    id: 'creative-synthesis',
    title: 'Creative Synthesis',
    description: 'Combine ideas from multiple sources to create new insights',
    icon: Lightbulb,
    difficulty: 'Hard',
    estimatedTime: '20-25 min',
    category: 'Creative',
    requiresDocument: true,
    color: 'bg-pink-500'
  }
];

export function InteractiveGames({ selectedDocument, uploadedDocuments }: InteractiveGamesProps) {
  const [activeGame, setActiveGame] = useState<string | null>(null);
  const [gameContent, setGameContent] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [gameProgress, setGameProgress] = useState(0);
  const [gameStats, setGameStats] = useState({
    gamesPlayed: 12,
    totalScore: 2450,
    averageScore: 85,
    streak: 5,
    achievements: 8
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Memory': return 'bg-blue-100 text-blue-800';
      case 'Logic': return 'bg-green-100 text-green-800';
      case 'Knowledge': return 'bg-purple-100 text-purple-800';
      case 'Speed': return 'bg-orange-100 text-orange-800';
      case 'Creative': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const generateGameContent = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return null;

    setIsGenerating(true);
    setGameProgress(0);

    try {
      let content = null;
      const progressSteps = [20, 40, 60, 80, 100];

      for (let i = 0; i < progressSteps.length; i++) {
        setGameProgress(progressSteps[i]);
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      switch (gameId) {
        case 'word-association':
          content = await generateWordAssociationGame();
          break;
        case 'concept-builder':
          content = await generateConceptBuilderGame();
          break;
        case 'quick-quiz':
          content = await generateQuickQuizGame();
          break;
        case 'memory-palace':
          content = await generateMemoryPalaceGame();
          break;
        case 'document-detective':
          content = await generateDocumentDetectiveGame();
          break;
        case 'creative-synthesis':
          content = await generateCreativeSynthesisGame();
          break;
        default:
          content = { type: 'simple', message: 'Game content not implemented yet.' };
      }

      setGameContent(content);
      return content;
    } catch (error) {
      console.error('Game generation failed:', error);
      toast.error('Failed to generate game content');
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePlayGame = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return;

    if (game.requiresDocument && uploadedDocuments.length === 0) {
      toast.error('This game requires uploaded documents. Please upload a document first.');
      return;
    }

    setActiveGame(gameId);
    toast.success(`Starting ${game.title}...`);

    // Generate actual game content
    const content = await generateGameContent(gameId);
    if (!content) {
      setActiveGame(null);
      return;
    }
  };

  const handleGameComplete = () => {
    setActiveGame(null);
    setGameContent(null);
    toast.success('Game completed! +150 XP earned');
    setGameStats(prev => ({
      ...prev,
      gamesPlayed: prev.gamesPlayed + 1,
      totalScore: prev.totalScore + 150,
      streak: prev.streak + 1
    }));
  };

  // Game generation functions
  const generateWordAssociationGame = async () => {
    try {
      const response = await integratedTutorService.generateResponse(
        'Create a word association game with 10 pairs of related educational terms. Format as JSON with word pairs and explanations.',
        { educationLevel: 'intermediate', useWebSearch: false }
      );

      return {
        type: 'word-association',
        title: 'Word Association Challenge',
        instructions: 'Match the related terms and concepts!',
        content: response.content,
        interactive: true
      };
    } catch (error) {
      return {
        type: 'word-association',
        title: 'Word Association Challenge',
        instructions: 'Match these educational terms with their related concepts!',
        pairs: [
          { word1: 'Photosynthesis', word2: 'Chlorophyll', explanation: 'Chlorophyll is essential for photosynthesis' },
          { word1: 'Democracy', word2: 'Voting', explanation: 'Voting is a key component of democratic systems' },
          { word1: 'Gravity', word2: 'Newton', explanation: 'Newton formulated the law of universal gravitation' }
        ]
      };
    }
  };

  const generateConceptBuilderGame = async () => {
    try {
      const response = await integratedTutorService.generateResponse(
        'Create a concept mapping exercise with a central topic and 8 related concepts. Include connection explanations.',
        { educationLevel: 'intermediate', useWebSearch: false }
      );

      return {
        type: 'concept-builder',
        title: 'Concept Map Builder',
        instructions: 'Build connections between concepts!',
        content: response.content,
        interactive: true
      };
    } catch (error) {
      return {
        type: 'concept-builder',
        title: 'Concept Map Builder',
        instructions: 'Connect these concepts to build understanding!',
        centralConcept: 'Scientific Method',
        concepts: ['Hypothesis', 'Experiment', 'Observation', 'Analysis', 'Conclusion', 'Theory', 'Variables', 'Data']
      };
    }
  };

  const generateQuickQuizGame = async () => {
    try {
      const questions = await integratedTutorService.generatePracticeQuestions(
        'General Knowledge',
        'intermediate',
        5,
        'intermediate'
      );

      return {
        type: 'quick-quiz',
        title: 'Lightning Quiz',
        instructions: 'Answer as quickly as possible!',
        questions: questions,
        timeLimit: 30 // seconds per question
      };
    } catch (error) {
      return {
        type: 'quick-quiz',
        title: 'Lightning Quiz',
        instructions: 'Answer these quick questions!',
        questions: [
          { question: 'What is the capital of France?', options: ['London', 'Berlin', 'Paris', 'Madrid'], correct: 'Paris' },
          { question: 'What is 2 + 2?', options: ['3', '4', '5', '6'], correct: '4' }
        ]
      };
    }
  };

  const generateMemoryPalaceGame = async () => {
    return {
      type: 'memory-palace',
      title: 'Memory Palace Builder',
      instructions: 'Create visual memory aids for complex information!',
      content: 'Build your memory palace by associating information with familiar locations.'
    };
  };

  const generateDocumentDetectiveGame = async () => {
    if (uploadedDocuments.length === 0) {
      return {
        type: 'document-detective',
        title: 'Document Detective',
        instructions: 'Upload documents to play this game!',
        content: 'No documents available for analysis.'
      };
    }

    return {
      type: 'document-detective',
      title: 'Document Detective',
      instructions: 'Find key insights in your documents!',
      documents: uploadedDocuments.slice(0, 3),
      challenges: [
        'Find the main thesis statement',
        'Identify key supporting evidence',
        'Locate methodology details'
      ]
    };
  };

  const generateCreativeSynthesisGame = async () => {
    return {
      type: 'creative-synthesis',
      title: 'Creative Synthesis Challenge',
      instructions: 'Combine ideas to create new insights!',
      content: 'Use multiple sources to generate creative connections and new ideas.'
    };
  };

  if (activeGame) {
    const game = availableGames.find(g => g.id === activeGame);

    if (isGenerating) {
      return (
        <div className="h-full flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
                <h3 className="text-xl font-bold mb-2">Generating {game?.title}</h3>
                <p className="text-gray-600">Creating your personalized game...</p>
              </div>
              <div className="space-y-4">
                <Progress value={gameProgress} className="w-full" />
                <p className="text-sm text-gray-500">{gameProgress}% complete</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setActiveGame(null);
                    setIsGenerating(false);
                  }}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (gameContent) {
      return (
        <div className="h-full p-6 overflow-y-auto">
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {game && <game.icon className="w-8 h-8 text-blue-500" />}
                  <div>
                    <CardTitle className="text-2xl">{gameContent.title || game?.title}</CardTitle>
                    <p className="text-gray-600">{gameContent.instructions}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setActiveGame(null)}
                >
                  Exit Game
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Game Content Display */}
                {gameContent.type === 'word-association' && gameContent.pairs && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {gameContent.pairs.map((pair: any, index: number) => (
                      <Card key={index} className="p-4 hover:shadow-md transition-shadow">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-blue-600">{pair.word1}</span>
                          <span className="text-gray-400">↔</span>
                          <span className="font-medium text-green-600">{pair.word2}</span>
                        </div>
                        <p className="text-sm text-gray-600">{pair.explanation}</p>
                      </Card>
                    ))}
                  </div>
                )}

                {gameContent.type === 'concept-builder' && gameContent.concepts && (
                  <div className="text-center">
                    <div className="mb-6">
                      <div className="inline-block p-4 bg-blue-100 rounded-lg">
                        <h3 className="text-lg font-bold text-blue-800">{gameContent.centralConcept}</h3>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {gameContent.concepts.map((concept: string, index: number) => (
                        <div key={index} className="p-3 bg-gray-100 rounded-lg hover:bg-gray-200 cursor-pointer transition-colors">
                          <span className="text-sm font-medium">{concept}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {gameContent.type === 'quick-quiz' && gameContent.questions && (
                  <div className="space-y-4">
                    {gameContent.questions.map((q: any, index: number) => (
                      <Card key={index} className="p-4">
                        <h4 className="font-medium mb-3">{q.question}</h4>
                        {q.options && (
                          <div className="grid grid-cols-1 gap-2">
                            {q.options.map((option: string, optIndex: number) => (
                              <Button
                                key={optIndex}
                                variant="outline"
                                className="justify-start"
                                onClick={() => toast.info(option === q.correct ? 'Correct!' : 'Try again!')}
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                )}

                {/* Default content display */}
                {gameContent.content && !gameContent.pairs && !gameContent.concepts && !gameContent.questions && (
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap">{gameContent.content}</div>
                  </div>
                )}

                <div className="flex justify-center pt-6">
                  <Button
                    onClick={handleGameComplete}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-2"
                  >
                    <Trophy className="w-4 h-4 mr-2" />
                    Complete Game
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <div className="h-full flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
              <h3 className="text-xl font-bold mb-2">{game?.title}</h3>
              <p className="text-gray-600">Loading game content...</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setActiveGame(null)}
              className="w-full"
            >
              Exit Game
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Interactive Learning Games</h1>
        <p className="text-gray-600">Engage with educational content through fun, interactive experiences</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.gamesPlayed}</p>
            <p className="text-sm text-gray-600">Games Played</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Star className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.totalScore}</p>
            <p className="text-sm text-gray-600">Total Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.averageScore}%</p>
            <p className="text-sm text-gray-600">Avg Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Flame className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.streak}</p>
            <p className="text-sm text-gray-600">Day Streak</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.achievements}</p>
            <p className="text-sm text-gray-600">Achievements</p>
          </CardContent>
        </Card>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableGames.map((game) => (
          <Card key={game.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-lg ${game.color}`}>
                  <game.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex space-x-2">
                  <Badge className={getDifficultyColor(game.difficulty)}>
                    {game.difficulty}
                  </Badge>
                  <Badge className={getCategoryColor(game.category)}>
                    {game.category}
                  </Badge>
                </div>
              </div>
              <CardTitle className="text-lg">{game.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{game.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center">
                  <Timer className="w-4 h-4 mr-1" />
                  {game.estimatedTime}
                </div>
                {game.requiresDocument && (
                  <div className="flex items-center">
                    <BookOpen className="w-4 h-4 mr-1" />
                    Requires Document
                  </div>
                )}
              </div>
              <Button 
                onClick={() => handlePlayGame(game.id)}
                className="w-full"
                disabled={game.requiresDocument && uploadedDocuments.length === 0}
              >
                <Play className="w-4 h-4 mr-2" />
                Play Game
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
