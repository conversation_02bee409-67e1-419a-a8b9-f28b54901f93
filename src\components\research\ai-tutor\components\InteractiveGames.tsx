/**
 * Interactive Games Component
 * Provides engaging educational games and interactive learning experiences
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Gamepad2, 
  Trophy, 
  Target, 
  Brain,
  BookOpen,
  Zap,
  Award,
  Medal,
  Crown,
  Flame,
  CheckCircle,
  Lock,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Shield,
  Heart,
  Play,
  Star,
  Timer,
  RotateCcw,
  Shuffle
} from "lucide-react";
import { ResearchDocument } from '../types';
import { toast } from 'sonner';

interface InteractiveGamesProps {
  selectedDocument: ResearchDocument | null;
  uploadedDocuments: ResearchDocument[];
}

interface GameCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  category: 'Memory' | 'Logic' | 'Knowledge' | 'Speed' | 'Creative';
  requiresDocument: boolean;
  color: string;
}

const availableGames: GameCard[] = [
  {
    id: 'word-association',
    title: 'Word Association',
    description: 'Connect related concepts and terms from your learning materials',
    icon: Brain,
    difficulty: 'Easy',
    estimatedTime: '5-10 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-blue-500'
  },
  {
    id: 'concept-builder',
    title: 'Concept Builder',
    description: 'Build concept maps by connecting ideas and relationships',
    icon: Target,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Logic',
    requiresDocument: false,
    color: 'bg-green-500'
  },
  {
    id: 'quick-quiz',
    title: 'Lightning Quiz',
    description: 'Fast-paced questions to test your knowledge',
    icon: Zap,
    difficulty: 'Medium',
    estimatedTime: '3-5 min',
    category: 'Speed',
    requiresDocument: false,
    color: 'bg-yellow-500'
  },
  {
    id: 'memory-palace',
    title: 'Memory Palace',
    description: 'Create visual memory aids for complex information',
    icon: Crown,
    difficulty: 'Hard',
    estimatedTime: '15-20 min',
    category: 'Memory',
    requiresDocument: false,
    color: 'bg-purple-500'
  },
  {
    id: 'document-detective',
    title: 'Document Detective',
    description: 'Find key insights and connections in your uploaded documents',
    icon: BookOpen,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Knowledge',
    requiresDocument: true,
    color: 'bg-indigo-500'
  },
  {
    id: 'creative-synthesis',
    title: 'Creative Synthesis',
    description: 'Combine ideas from multiple sources to create new insights',
    icon: Lightbulb,
    difficulty: 'Hard',
    estimatedTime: '20-25 min',
    category: 'Creative',
    requiresDocument: true,
    color: 'bg-pink-500'
  }
];

export function InteractiveGames({ selectedDocument, uploadedDocuments }: InteractiveGamesProps) {
  const [activeGame, setActiveGame] = useState<string | null>(null);
  const [gameStats, setGameStats] = useState({
    gamesPlayed: 12,
    totalScore: 2450,
    averageScore: 85,
    streak: 5,
    achievements: 8
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Memory': return 'bg-blue-100 text-blue-800';
      case 'Logic': return 'bg-green-100 text-green-800';
      case 'Knowledge': return 'bg-purple-100 text-purple-800';
      case 'Speed': return 'bg-orange-100 text-orange-800';
      case 'Creative': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePlayGame = (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return;

    if (game.requiresDocument && uploadedDocuments.length === 0) {
      toast.error('This game requires uploaded documents. Please upload a document first.');
      return;
    }

    setActiveGame(gameId);
    toast.success(`Starting ${game.title}...`);
    
    // Simulate game start
    setTimeout(() => {
      setActiveGame(null);
      toast.success('Game completed! +150 XP earned');
      setGameStats(prev => ({
        ...prev,
        gamesPlayed: prev.gamesPlayed + 1,
        totalScore: prev.totalScore + 150,
        streak: prev.streak + 1
      }));
    }, 3000);
  };

  if (activeGame) {
    const game = availableGames.find(g => g.id === activeGame);
    return (
      <div className="h-full flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
              <h3 className="text-xl font-bold mb-2">{game?.title}</h3>
              <p className="text-gray-600">Game in progress...</p>
            </div>
            <div className="space-y-4">
              <Progress value={65} className="w-full" />
              <p className="text-sm text-gray-500">Loading interactive content...</p>
              <Button 
                variant="outline" 
                onClick={() => setActiveGame(null)}
                className="w-full"
              >
                Exit Game
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Interactive Learning Games</h1>
        <p className="text-gray-600">Engage with educational content through fun, interactive experiences</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.gamesPlayed}</p>
            <p className="text-sm text-gray-600">Games Played</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Star className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.totalScore}</p>
            <p className="text-sm text-gray-600">Total Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.averageScore}%</p>
            <p className="text-sm text-gray-600">Avg Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Flame className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.streak}</p>
            <p className="text-sm text-gray-600">Day Streak</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.achievements}</p>
            <p className="text-sm text-gray-600">Achievements</p>
          </CardContent>
        </Card>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableGames.map((game) => (
          <Card key={game.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-lg ${game.color}`}>
                  <game.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex space-x-2">
                  <Badge className={getDifficultyColor(game.difficulty)}>
                    {game.difficulty}
                  </Badge>
                  <Badge className={getCategoryColor(game.category)}>
                    {game.category}
                  </Badge>
                </div>
              </div>
              <CardTitle className="text-lg">{game.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{game.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center">
                  <Timer className="w-4 h-4 mr-1" />
                  {game.estimatedTime}
                </div>
                {game.requiresDocument && (
                  <div className="flex items-center">
                    <BookOpen className="w-4 h-4 mr-1" />
                    Requires Document
                  </div>
                )}
              </div>
              <Button 
                onClick={() => handlePlayGame(game.id)}
                className="w-full"
                disabled={game.requiresDocument && uploadedDocuments.length === 0}
              >
                <Play className="w-4 h-4 mr-2" />
                Play Game
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
