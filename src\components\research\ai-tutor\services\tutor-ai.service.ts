/**
 * AI Tutoring Service
 * Handles AI interactions for personalized tutoring experiences
 * Routes requests to appropriate AI providers (OpenRouter or Google Gemini)
 */

import {
  TutorResponse,
  StreamingTutorResponse,
  EducationLevel,
  TutorAIModel,
  EnhancedTutorMessage
} from '../types';
import { EDUCATION_LEVEL_PROMPTS, TUTOR_AI_MODELS } from '../constants';
import { GeminiTutorService } from './gemini-tutor.service';
import { OpenRouterTutorService } from './openrouter-tutor.service';

interface TutorGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  educationLevel: EducationLevel;
  includeExamples?: boolean;
  learningStyle?: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  conversationHistory?: Array<{ role: string; content: string }>;
}



class TutorAIService {
  private geminiService: GeminiTutorService;
  private openRouterService: OpenRouterTutorService;

  constructor() {
    this.geminiService = new GeminiTutorService();
    this.openRouterService = new OpenRouterTutorService();

    console.log('TutorAIService initialized with providers:', {
      gemini: this.geminiService.isServiceConfigured(),
      openRouter: this.openRouterService.isServiceConfigured()
    });
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return this.geminiService.isServiceConfigured() || this.openRouterService.isServiceConfigured();
  }

  /**
   * Get available AI models for tutoring
   */
  getAvailableModels(): TutorAIModel[] {
    return TUTOR_AI_MODELS;
  }

  /**
   * Get the default model for tutoring
   */
  getDefaultModel(): string {
    // Prefer Gemini if available, otherwise use OpenRouter
    if (this.geminiService.isServiceConfigured()) {
      return "google/gemini-2.5-flash";
    } else if (this.openRouterService.isServiceConfigured()) {
      return "anthropic/claude-3.5-sonnet";
    }
    return "google/gemini-2.5-flash";
  }

  /**
   * Determine which service to use based on the model
   */
  private getServiceForModel(model: string): 'gemini' | 'openrouter' {
    // Find the model in our configuration to get the correct API provider
    const modelConfig = TUTOR_AI_MODELS.find(m => m.id === model);
    if (modelConfig) {
      return modelConfig.apiProvider;
    }

    // Fallback logic for unknown models
    if (model.startsWith('google/gemini-2.5-')) {
      return 'gemini';
    }
    return 'openrouter';
  }

  /**
   * Generate a tutoring response for a given topic and question
   */
  async generateTutoringResponse(
    topic: string,
    question: string,
    options: TutorGenerationOptions
  ): Promise<TutorResponse> {
    if (!this.isConfigured()) {
      throw new Error('AI service not configured. Please check your API keys.');
    }

    const model = options.model || this.getDefaultModel();
    const serviceType = this.getServiceForModel(model);

    console.log('Routing tutoring request:', {
      model,
      serviceType,
      topic,
      educationLevel: options.educationLevel
    });

    try {
      if (serviceType === 'gemini') {
        if (!this.geminiService.isServiceConfigured()) {
          throw new Error('Gemini service not configured. Please check your VITE_GEMINI_API_KEY.');
        }
        return await this.geminiService.generateTutoringResponse(topic, question, options);
      } else {
        if (!this.openRouterService.isServiceConfigured()) {
          throw new Error('OpenRouter service not configured. Please check your VITE_OPENROUTER_API_KEY.');
        }
        return await this.openRouterService.generateTutoringResponse(topic, question, options);
      }
    } catch (error) {
      console.error('Tutoring AI request failed:', error);
      throw new Error(`Failed to generate tutoring response: ${error.message}`);
    }
  }

  /**
   * Generate streaming tutoring response
   */
  async *generateStreamingResponse(
    topic: string,
    question: string,
    options: TutorGenerationOptions
  ): AsyncGenerator<StreamingTutorResponse, void, unknown> {
    if (!this.isConfigured()) {
      throw new Error('AI service not configured. Please check your API keys.');
    }

    const model = options.model || this.getDefaultModel();
    const serviceType = this.getServiceForModel(model);

    console.log('Routing streaming tutoring request:', {
      model,
      serviceType,
      topic,
      educationLevel: options.educationLevel
    });

    try {
      if (serviceType === 'gemini') {
        if (!this.geminiService.isServiceConfigured()) {
          throw new Error('Gemini service not configured. Please check your VITE_GEMINI_API_KEY.');
        }
        yield* this.geminiService.generateStreamingResponse(topic, question, options);
      } else {
        if (!this.openRouterService.isServiceConfigured()) {
          throw new Error('OpenRouter service not configured. Please check your VITE_OPENROUTER_API_KEY.');
        }
        yield* this.openRouterService.generateStreamingResponse(topic, question, options);
      }
    } catch (error) {
      console.error('Streaming tutoring request failed:', error);
      throw new Error(`Failed to generate streaming response: ${error.message}`);
    }
  }





  /**
   * Get API provider information
   */
  getApiProvider(): string {
    if (!this.isConfigured()) return 'none';

    const providers = [];
    if (this.geminiService.isServiceConfigured()) {
      providers.push('Google/Gemini');
    }
    if (this.openRouterService.isServiceConfigured()) {
      providers.push('OpenRouter');
    }

    return providers.length > 0 ? providers.join(' + ') : 'none';
  }
}

export const tutorAIService = new TutorAIService();
export default tutorAIService;
