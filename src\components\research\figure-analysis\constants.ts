/**
 * Figure Analysis Constants
 * Configuration and constants for the figure analysis system
 */

// Supported File Types
export const SUPPORTED_FILE_TYPES = {
  'image/png': { extension: '.png', maxSize: 10 * 1024 * 1024 }, // 10MB
  'image/jpeg': { extension: '.jpg', maxSize: 10 * 1024 * 1024 },
  'image/jpg': { extension: '.jpg', maxSize: 10 * 1024 * 1024 },
  'image/svg+xml': { extension: '.svg', maxSize: 5 * 1024 * 1024 }, // 5MB
  'application/pdf': { extension: '.pdf', maxSize: 20 * 1024 * 1024 }, // 20MB
} as const;

export const ACCEPTED_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.svg', '.pdf'];

// Quality Assessment Thresholds
export const QUALITY_THRESHOLDS = {
  DPI: {
    POOR: 72,
    ACCEPTABLE: 150,
    GOOD: 300,
    EXCELLENT: 600,
  },
  CLARITY: {
    POOR: 30,
    ACCEPTABLE: 50,
    GOOD: 70,
    EXCELLENT: 85,
  },
  CONTRAST: {
    POOR: 3,
    ACCEPTABLE: 4.5, // WCAG AA
    GOOD: 7, // WCAG AAA
    EXCELLENT: 10,
  },
  READABILITY: {
    POOR: 40,
    ACCEPTABLE: 60,
    GOOD: 80,
    EXCELLENT: 95,
  },
} as const;

// Gemini API Configuration
export const GEMINI_CONFIG = {
  MODELS: {
    FLASH: 'gemini-2.5-flash',
    PRO: 'gemini-2.5-pro',
    FLASH_2: 'gemini-2.0-flash',
  },
  MAX_IMAGE_SIZE: 20 * 1024 * 1024, // 20MB
  MAX_TOKENS: {
    FLASH: 8192,
    PRO: 32768,
  },
  TEMPERATURE: {
    ANALYSIS: 0.1, // Low temperature for consistent analysis
    CREATIVE: 0.7, // Higher for caption generation
  },
  TIMEOUT: 60000, // 60 seconds
  RATE_LIMIT: {
    REQUESTS_PER_MINUTE: 60,
    REQUESTS_PER_DAY: 1500,
  },
} as const;

// Analysis Prompts
export const ANALYSIS_PROMPTS = {
  QUALITY_ASSESSMENT: `Analyze this figure for academic publication quality. Evaluate:
1. Resolution and clarity for print/digital publication
2. Text readability and font sizes
3. Color contrast and accessibility
4. Overall visual appeal and professionalism
5. Compliance with academic publication standards

Provide specific scores (0-100) and actionable recommendations.`,

  CAPTION_GENERATION: `Generate an academic-style caption for this figure. Consider:
1. Figure type and content description
2. Key findings or patterns shown
3. Methodology or data source if apparent
4. Statistical significance if relevant
5. Proper academic terminology

Style: {citationStyle}
Research Field: {researchField}
Context: {context}`,

  AUTHENTICITY_CHECK: `Analyze this figure for potential manipulation or fabrication. Look for:
1. Inconsistent lighting, shadows, or perspectives
2. Cloning or copy-paste artifacts
3. Unnatural edges or transitions
4. Statistical anomalies or impossible data
5. Technical indicators of digital manipulation

Provide confidence scores and highlight suspicious areas.`,

  CROSS_REFERENCE: `Compare this figure against known academic literature. Identify:
1. Similar figures or visualizations
2. Potential duplicates or near-duplicates
3. Related work in the same field
4. Novel aspects or contributions
5. Proper attribution requirements

Assess novelty and originality.`,

  STATISTICAL_VALIDATION: `Validate the statistical claims and representations in this figure:
1. Check for proper statistical methods
2. Verify significance claims
3. Assess data presentation accuracy
4. Identify potential statistical errors
5. Evaluate appropriateness of visualization method

Provide validation results and recommendations.`,

  METHODOLOGY_EXTRACTION: `Extract and analyze the methodology used to create this figure:
1. Identify visualization type and techniques
2. Assess appropriateness for the data
3. Suggest alternative visualization methods
4. Evaluate best practices compliance
5. Recommend improvements

Focus on scientific rigor and clarity.`,

  BIAS_DETECTION: `Analyze this figure for potential biases in data presentation:
1. Selection bias in data shown
2. Scale manipulation or misleading axes
3. Cherry-picking of favorable results
4. Confirmation bias in interpretation
5. Visual bias in color or design choices

Provide bias scores and neutrality recommendations.`,

  IMPACT_PREDICTION: `Predict the potential impact and effectiveness of this figure:
1. Visual appeal and clarity
2. Novelty and significance of findings
3. Potential for citation and reference
4. Clarity of message communication
5. Overall academic value

Provide impact scores and improvement suggestions.`,

  DATA_EXTRACTION: `Extract quantitative data from this figure if possible:
1. Identify data points and values
2. Extract axis labels and scales
3. Determine data relationships
4. Capture legends and annotations
5. Structure the extracted information

Provide confidence scores for extracted data.`,
} as const;

// Citation Styles Configuration
export const CITATION_STYLES = {
  APA: {
    name: 'APA (American Psychological Association)',
    format: 'Figure {number}. {description}',
    example: 'Figure 1. Mean response times across experimental conditions.',
  },
  MLA: {
    name: 'MLA (Modern Language Association)',
    format: 'Fig. {number}. {description}',
    example: 'Fig. 1. Mean response times across experimental conditions.',
  },
  Chicago: {
    name: 'Chicago Manual of Style',
    format: 'Figure {number}. {description}',
    example: 'Figure 1. Mean response times across experimental conditions.',
  },
  IEEE: {
    name: 'IEEE (Institute of Electrical and Electronics Engineers)',
    format: 'Fig. {number}. {description}',
    example: 'Fig. 1. Mean response times across experimental conditions.',
  },
  Harvard: {
    name: 'Harvard Referencing Style',
    format: 'Figure {number}: {description}',
    example: 'Figure 1: Mean response times across experimental conditions.',
  },
  Vancouver: {
    name: 'Vancouver Style',
    format: 'Figure {number} {description}',
    example: 'Figure 1 Mean response times across experimental conditions.',
  },
  Nature: {
    name: 'Nature Journal Style',
    format: 'Figure {number} | {description}',
    example: 'Figure 1 | Mean response times across experimental conditions.',
  },
} as const;

// Research Fields
export const RESEARCH_FIELDS = [
  'Biology', 'Chemistry', 'Physics', 'Mathematics', 'Computer Science',
  'Psychology', 'Medicine', 'Engineering', 'Environmental Science',
  'Social Sciences', 'Economics', 'Education', 'Linguistics',
  'Anthropology', 'Political Science', 'Philosophy', 'History',
  'Art History', 'Literature', 'Other'
] as const;

// Figure Types
export const FIGURE_TYPES = {
  CHART: ['bar chart', 'line chart', 'pie chart', 'area chart', 'histogram'],
  GRAPH: ['scatter plot', 'network graph', 'tree diagram', 'flow chart'],
  PLOT: ['box plot', 'violin plot', 'heat map', 'contour plot'],
  DIAGRAM: ['schematic', 'flowchart', 'process diagram', 'concept map'],
  MAP: ['geographic map', 'heat map', 'choropleth', 'topographic'],
  PHOTO: ['microscopy', 'clinical', 'field photo', 'experimental setup'],
  ILLUSTRATION: ['technical drawing', 'anatomical', 'conceptual'],
  TABLE: ['data table', 'comparison table', 'summary statistics'],
  SCREENSHOT: ['software interface', 'web page', 'application'],
  OTHER: ['mixed media', 'composite', 'artistic', 'unknown'],
} as const;

// Analysis Settings
export const DEFAULT_ANALYSIS_SETTINGS = {
  selectedModels: ['gemini-2.5-flash'] as const,
  analysisDepth: 'standard' as const,
  enableAdvancedFeatures: true,
  citationStyle: 'APA' as const,
  researchField: 'Other',
  language: 'en',
  qualityThresholds: {
    minimumDPI: 150,
    minimumClarity: 60,
    minimumContrast: 4.5,
    accessibilityLevel: 'AA' as const,
  },
} as const;

// UI Constants
export const UI_CONFIG = {
  MAX_UPLOAD_FILES: 10,
  PREVIEW_SIZE: {
    THUMBNAIL: 150,
    PREVIEW: 400,
    FULL: 800,
  },
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  PROGRESS_UPDATE_INTERVAL: 1000,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit',
  UNSUPPORTED_FORMAT: 'File format is not supported',
  UPLOAD_FAILED: 'Failed to upload file',
  ANALYSIS_FAILED: 'Analysis failed to complete',
  API_KEY_MISSING: 'Gemini API key is not configured',
  NETWORK_ERROR: 'Network connection error',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded, please try again later',
  INVALID_FILE: 'Invalid or corrupted file',
  PROCESSING_TIMEOUT: 'Processing timeout, please try again',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  ANALYSIS_COMPLETE: 'Analysis completed successfully',
  EXPORT_COMPLETE: 'Export completed successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
} as const;

// Export Templates
export const EXPORT_TEMPLATES = {
  ACADEMIC: {
    name: 'Academic Report',
    description: 'Comprehensive analysis report for academic use',
    sections: ['summary', 'quality', 'recommendations', 'detailed_analysis'],
  },
  TECHNICAL: {
    name: 'Technical Report',
    description: 'Detailed technical analysis with metrics',
    sections: ['metrics', 'technical_details', 'validation', 'raw_data'],
  },
  SUMMARY: {
    name: 'Executive Summary',
    description: 'Brief overview of key findings',
    sections: ['summary', 'key_recommendations', 'quality_score'],
  },
} as const;

// Color Schemes for Accessibility
export const ACCESSIBILITY_COLORS = {
  COLORBLIND_SAFE: [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
  ],
  HIGH_CONTRAST: [
    '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
    '#ffff00', '#ff00ff', '#00ffff', '#800000', '#008000'
  ],
} as const;

// Journal Standards
export const JOURNAL_STANDARDS = {
  NATURE: {
    name: 'Nature',
    dpi: 300,
    formats: ['tiff', 'eps', 'pdf'],
    maxWidth: 183, // mm
    colorSpace: 'RGB',
  },
  SCIENCE: {
    name: 'Science',
    dpi: 300,
    formats: ['tiff', 'eps', 'pdf'],
    maxWidth: 180, // mm
    colorSpace: 'CMYK',
  },
  PLOS: {
    name: 'PLOS ONE',
    dpi: 300,
    formats: ['tiff', 'eps', 'pdf', 'png'],
    maxWidth: 170, // mm
    colorSpace: 'RGB',
  },
  IEEE: {
    name: 'IEEE Journals',
    dpi: 300,
    formats: ['tiff', 'eps', 'pdf'],
    maxWidth: 190, // mm
    colorSpace: 'RGB',
  },
} as const;
