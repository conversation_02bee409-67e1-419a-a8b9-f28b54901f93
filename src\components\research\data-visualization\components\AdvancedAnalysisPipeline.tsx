import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Play,
  CheckCircle,
  AlertTriangle,
  Loader2,
  BarChart3,
  Brain,
  Eye,
  RefreshCw,
  Clock,
  Database,
  TrendingUp,
  Lightbulb,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Target,
  Zap,
  Filter,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Activity,
  Users,
  Calendar,
  FileText,
  Sparkles
} from "lucide-react";
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { AnalysisPipelineStep, UploadedFile, DataAnalysisResult } from '../types';
import { LOADING_STATES, SUCCESS_MESSAGES } from '../constants';

interface AdvancedAnalysisPipelineProps {
  file: UploadedFile;
  onAnalysisComplete?: (result: DataAnalysisResult) => void;
  className?: string;
}

interface AnalysisConfiguration {
  analysisType: 'basic' | 'advanced' | 'research' | 'business';
  focusAreas: string[];
  visualizationTypes: string[];
  colorScheme: 'default' | 'professional' | 'vibrant' | 'minimal';
  analysisDepth: 'quick' | 'standard' | 'comprehensive';
  customPrompt: string;
  includeStatisticalTests: boolean;
  includeCorrelations: boolean;
  includeOutlierAnalysis: boolean;
  includePredictiveInsights: boolean;
}

export const AdvancedAnalysisPipeline: React.FC<AdvancedAnalysisPipelineProps> = ({
  file,
  onAnalysisComplete,
  className = ""
}) => {
  const {
    setAnalysisResult,
    setCurrentAnalysis,
    setAnalyzing,
    addError,
    isAnalyzing
  } = useDataVisualizationStore();

  const [currentStep, setCurrentStep] = useState<'config' | 'analysis'>('config');
  const [config, setConfig] = useState<AnalysisConfiguration>({
    analysisType: 'advanced',
    focusAreas: [],
    visualizationTypes: [],
    colorScheme: 'default',
    analysisDepth: 'standard',
    customPrompt: '',
    includeStatisticalTests: true,
    includeCorrelations: true,
    includeOutlierAnalysis: true,
    includePredictiveInsights: false
  });

  const [steps, setSteps] = useState<AnalysisPipelineStep[]>([
    {
      id: 'data-profiling',
      name: 'Advanced Data Profiling',
      description: 'Deep analysis of data structure, quality, and characteristics',
      status: 'pending',
      progress: 0
    },
    {
      id: 'statistical-analysis',
      name: 'Statistical Analysis',
      description: 'Comprehensive statistical tests and correlations',
      status: 'pending',
      progress: 0
    },
    {
      id: 'pattern-discovery',
      name: 'Pattern Discovery',
      description: 'AI-powered pattern recognition and insights',
      status: 'pending',
      progress: 0
    },
    {
      id: 'visualization-generation',
      name: 'Custom Visualization Generation',
      description: 'Creating personalized visualizations based on your preferences',
      status: 'pending',
      progress: 0
    },
    {
      id: 'insights-synthesis',
      name: 'Insights Synthesis',
      description: 'Generating actionable insights and recommendations',
      status: 'pending',
      progress: 0
    }
  ]);

  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [analysisResult, setLocalAnalysisResult] = useState<DataAnalysisResult | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);

  const analysisTypes = [
    { value: 'basic', label: 'Basic Analysis', description: 'Quick overview and basic statistics' },
    { value: 'advanced', label: 'Advanced Analysis', description: 'Comprehensive analysis with AI insights' },
    { value: 'research', label: 'Research Analysis', description: 'Academic-grade analysis with statistical tests' },
    { value: 'business', label: 'Business Analysis', description: 'Business-focused insights and KPIs' }
  ];

  const focusAreaOptions = [
    'Data Quality Assessment',
    'Correlation Analysis',
    'Trend Analysis',
    'Outlier Detection',
    'Distribution Analysis',
    'Predictive Insights',
    'Comparative Analysis',
    'Time Series Analysis',
    'Categorical Analysis',
    'Statistical Significance'
  ];

  const visualizationOptions = [
    { value: 'histogram', label: 'Histograms', icon: BarChart },
    { value: 'scatter', label: 'Scatter Plots', icon: Activity },
    { value: 'correlation', label: 'Correlation Matrix', icon: Activity },
    { value: 'box', label: 'Box Plots', icon: BarChart3 },
    { value: 'line', label: 'Line Charts', icon: LineChart },
    { value: 'bar', label: 'Bar Charts', icon: BarChart },
    { value: 'pie', label: 'Pie Charts', icon: PieChart },
    { value: 'heatmap', label: 'Heatmaps', icon: Activity }
  ];

  const colorSchemes = [
    { value: 'default', label: 'Default', description: 'Balanced and professional' },
    { value: 'professional', label: 'Professional', description: 'Corporate-friendly colors' },
    { value: 'vibrant', label: 'Vibrant', description: 'Bold and eye-catching' },
    { value: 'minimal', label: 'Minimal', description: 'Clean and simple' }
  ];

  const updateStep = (stepId: string, updates: Partial<AnalysisPipelineStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const handleConfigChange = (key: keyof AnalysisConfiguration, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleFocusAreaToggle = (area: string) => {
    setConfig(prev => ({
      ...prev,
      focusAreas: prev.focusAreas.includes(area)
        ? prev.focusAreas.filter(a => a !== area)
        : [...prev.focusAreas, area]
    }));
  };

  const handleVisualizationToggle = (type: string) => {
    setConfig(prev => ({
      ...prev,
      visualizationTypes: prev.visualizationTypes.includes(type)
        ? prev.visualizationTypes.filter(t => t !== type)
        : [...prev.visualizationTypes, type]
    }));
  };

  const startAnalysis = () => {
    setCurrentStep('analysis');
    runAdvancedAnalysis();
  };

  const runAdvancedAnalysis = async () => {
    if (isAnalyzing) return;

    setAnalyzing(true);
    setStartTime(new Date());
    setCurrentStepIndex(0);

    try {
      // Step 1: Advanced Data Profiling
      updateStep('data-profiling', { status: 'running', progress: 0 });
      
      for (let i = 0; i <= 100; i += 20) {
        updateStep('data-profiling', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      updateStep('data-profiling', { 
        status: 'completed', 
        progress: 100,
        result: 'Advanced data profiling completed with quality assessment'
      });

      // Step 2: Statistical Analysis
      setCurrentStepIndex(1);
      updateStep('statistical-analysis', { status: 'running', progress: 0 });

      // Perform enhanced analysis with configuration
      const result = await GeminiAnalysisService.performAdvancedAnalysis(file, config);

      updateStep('statistical-analysis', {
        status: 'completed',
        progress: 100,
        result: 'Statistical tests and correlations completed'
      });

      // Continue with remaining steps...
      setCurrentStepIndex(2);
      updateStep('pattern-discovery', { status: 'running', progress: 0 });
      
      for (let i = 0; i <= 100; i += 25) {
        updateStep('pattern-discovery', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      updateStep('pattern-discovery', { 
        status: 'completed', 
        progress: 100,
        result: 'AI patterns and insights discovered'
      });

      // Step 4: Custom Visualization Generation
      setCurrentStepIndex(3);
      updateStep('visualization-generation', { status: 'running', progress: 0 });

      for (let i = 0; i <= 100; i += 25) {
        updateStep('visualization-generation', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      updateStep('visualization-generation', { 
        status: 'completed', 
        progress: 100,
        result: `${result.visualizations.length} custom visualizations created`
      });

      // Step 5: Insights Synthesis
      setCurrentStepIndex(4);
      updateStep('insights-synthesis', { status: 'running', progress: 0 });

      for (let i = 0; i <= 100; i += 25) {
        updateStep('insights-synthesis', { progress: i });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      updateStep('insights-synthesis', { 
        status: 'completed', 
        progress: 100,
        result: 'Comprehensive insights and recommendations generated'
      });

      // Store results
      setLocalAnalysisResult(result);
      setAnalysisResult(result);
      setCurrentAnalysis(result);

      toast.success('Advanced analysis completed successfully!');
      
      if (onAnalysisComplete) {
        onAnalysisComplete(result);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Advanced analysis failed';
      console.error('Advanced analysis error:', error);

      if (currentStepIndex >= 0 && currentStepIndex < steps.length) {
        updateStep(steps[currentStepIndex].id, {
          status: 'error',
          error: errorMessage
        });
      }

      addError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setAnalyzing(false);
      setCurrentStepIndex(-1);
    }
  };

  if (currentStep === 'analysis') {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Analysis Progress */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Advanced Analysis in Progress
              </CardTitle>
              <Button
                onClick={() => setCurrentStep('config')}
                variant="outline"
                size="sm"
                disabled={isAnalyzing}
              >
                <Settings className="h-4 w-4 mr-2" />
                Back to Config
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Overall Progress */}
            {isAnalyzing && (
              <div className="space-y-2 mb-6">
                <div className="flex justify-between text-sm">
                  <span>Overall Progress</span>
                  <span>{Math.round(((currentStepIndex + 1) / steps.length) * 100)}%</span>
                </div>
                <Progress 
                  value={((currentStepIndex + 1) / steps.length) * 100} 
                  className="w-full" 
                />
              </div>
            )}

            {/* Steps */}
            <div className="space-y-4">
              {steps.map((step, index) => (
                <div key={step.id} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {step.status === 'running' ? (
                        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                      ) : step.status === 'completed' ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : step.status === 'error' ? (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      ) : (
                        <Clock className="h-4 w-4 text-gray-400" />
                      )}
                      <div>
                        <h4 className="font-medium">{step.name}</h4>
                        <p className="text-sm text-gray-500">{step.description}</p>
                      </div>
                    </div>
                    <Badge variant={
                      step.status === 'completed' ? 'default' : 
                      step.status === 'running' ? 'default' :
                      step.status === 'error' ? 'destructive' : 'secondary'
                    }>
                      {step.status === 'completed' ? 'Completed' :
                       step.status === 'running' ? 'Running' :
                       step.status === 'error' ? 'Error' : 'Pending'}
                    </Badge>
                  </div>

                  {step.status === 'running' && (
                    <div className="ml-7 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{step.progress}%</span>
                      </div>
                      <Progress value={step.progress} className="w-full" />
                    </div>
                  )}

                  {step.result && (
                    <div className="ml-7">
                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>{step.result}</AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {step.error && (
                    <div className="ml-7">
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>{step.error}</AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {index < steps.length - 1 && <Separator />}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Configuration Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Advanced Analysis Configuration
          </CardTitle>
          <p className="text-sm text-gray-500">
            Customize your analysis to get the most relevant insights for your data
          </p>
        </CardHeader>
      </Card>

      {/* Configuration Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="focus">Focus Areas</TabsTrigger>
          <TabsTrigger value="visualizations">Visualizations</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Analysis Type & Depth</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Analysis Type */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Analysis Type</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analysisTypes.map((type) => (
                    <div
                      key={type.value}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        config.analysisType === type.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleConfigChange('analysisType', type.value)}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          config.analysisType === type.value
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`} />
                        <span className="font-medium">{type.label}</span>
                      </div>
                      <p className="text-sm text-gray-600">{type.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Analysis Depth */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Analysis Depth</Label>
                <Select value={config.analysisDepth} onValueChange={(value: any) => handleConfigChange('analysisDepth', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="quick">Quick (5-10 minutes)</SelectItem>
                    <SelectItem value="standard">Standard (10-20 minutes)</SelectItem>
                    <SelectItem value="comprehensive">Comprehensive (20-30 minutes)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Color Scheme */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Color Scheme</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {colorSchemes.map((scheme) => (
                    <div
                      key={scheme.value}
                      className={`p-3 border rounded-lg cursor-pointer text-center transition-all ${
                        config.colorScheme === scheme.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleConfigChange('colorScheme', scheme.value)}
                    >
                      <Palette className="h-6 w-6 mx-auto mb-2 text-gray-600" />
                      <div className="font-medium text-sm">{scheme.label}</div>
                      <div className="text-xs text-gray-500">{scheme.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="focus" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Focus Areas</CardTitle>
              <p className="text-sm text-gray-500">
                Select the areas you want the analysis to focus on
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {focusAreaOptions.map((area) => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={area}
                      checked={config.focusAreas.includes(area)}
                      onCheckedChange={() => handleFocusAreaToggle(area)}
                    />
                    <Label htmlFor={area} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      {area}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="visualizations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Visualization Preferences</CardTitle>
              <p className="text-sm text-gray-500">
                Choose the types of visualizations you want to generate
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {visualizationOptions.map((viz) => {
                  const IconComponent = viz.icon;
                  return (
                    <div
                      key={viz.value}
                      className={`p-4 border rounded-lg cursor-pointer text-center transition-all ${
                        config.visualizationTypes.includes(viz.value)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleVisualizationToggle(viz.value)}
                    >
                      <IconComponent className="h-8 w-8 mx-auto mb-2 text-gray-600" />
                      <div className="font-medium text-sm">{viz.label}</div>
                      {config.visualizationTypes.includes(viz.value) && (
                        <CheckCircle className="h-4 w-4 mx-auto mt-2 text-blue-500" />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Advanced Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Statistical Options */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Statistical Analysis</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="statistical-tests"
                      checked={config.includeStatisticalTests}
                      onCheckedChange={(checked) => handleConfigChange('includeStatisticalTests', checked)}
                    />
                    <Label htmlFor="statistical-tests">Include Statistical Tests</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="correlations"
                      checked={config.includeCorrelations}
                      onCheckedChange={(checked) => handleConfigChange('includeCorrelations', checked)}
                    />
                    <Label htmlFor="correlations">Include Correlation Analysis</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="outliers"
                      checked={config.includeOutlierAnalysis}
                      onCheckedChange={(checked) => handleConfigChange('includeOutlierAnalysis', checked)}
                    />
                    <Label htmlFor="outliers">Include Outlier Detection</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="predictive"
                      checked={config.includePredictiveInsights}
                      onCheckedChange={(checked) => handleConfigChange('includePredictiveInsights', checked)}
                    />
                    <Label htmlFor="predictive">Include Predictive Insights</Label>
                  </div>
                </div>
              </div>

              {/* Custom Prompt */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Custom Analysis Prompt</Label>
                <Textarea
                  placeholder="Provide specific instructions or questions you want the AI to focus on during analysis..."
                  value={config.customPrompt}
                  onChange={(e) => handleConfigChange('customPrompt', e.target.value)}
                  className="min-h-[100px]"
                />
                <p className="text-xs text-gray-500">
                  Optional: Add specific instructions to guide the AI analysis
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Start Analysis */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Ready to Start Analysis</h3>
              <p className="text-sm text-gray-500">
                Configuration complete. Click to begin advanced analysis.
              </p>
            </div>
            <Button
              onClick={startAnalysis}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              disabled={isAnalyzing}
            >
              {isAnalyzing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              Start Advanced Analysis
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
