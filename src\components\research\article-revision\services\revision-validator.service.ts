/**
 * Revision Validator Service
 * Validates AI-generated revisions for accuracy and appropriateness
 */

import { SectionRevision, ArticleChange } from '../types';

export interface ValidationResult {
  isValid: boolean;
  confidence: number;
  issues: ValidationIssue[];
  recommendations: string[];
}

export interface ValidationIssue {
  type: 'accuracy' | 'relevance' | 'formatting' | 'tone' | 'factual';
  severity: 'low' | 'medium' | 'high';
  description: string;
  suggestion: string;
}

export class RevisionValidatorService {
  
  /**
   * Validate a section revision
   */
  static validateSectionRevision(
    originalContent: string,
    revision: SectionRevision,
    comments: any[]
  ): ValidationResult {
    const issues: ValidationIssue[] = [];
    let confidence = 100;
    
    // Validate each change
    revision.changes.forEach((change, index) => {
      const changeValidation = this.validateChange(originalContent, change, comments);
      issues.push(...changeValidation.issues);
      confidence = Math.min(confidence, changeValidation.confidence);
    });
    
    // Check if changes address comments appropriately
    const commentValidation = this.validateCommentAddressing(revision, comments);
    issues.push(...commentValidation.issues);
    confidence = Math.min(confidence, commentValidation.confidence);
    
    // Check academic tone and formatting
    const toneValidation = this.validateAcademicTone(revision.revisedContent);
    issues.push(...toneValidation.issues);
    confidence = Math.min(confidence, toneValidation.confidence);
    
    const recommendations = this.generateRecommendations(issues);
    
    return {
      isValid: confidence >= 70 && issues.filter(i => i.severity === 'high').length === 0,
      confidence,
      issues,
      recommendations
    };
  }
  
  /**
   * Validate individual change
   */
  private static validateChange(
    originalContent: string,
    change: ArticleChange,
    comments: any[]
  ): ValidationResult {
    const issues: ValidationIssue[] = [];
    let confidence = 90;
    
    // Check if original text exists in content
    if (change.originalText && !originalContent.includes(change.originalText)) {
      issues.push({
        type: 'accuracy',
        severity: 'high',
        description: 'Original text not found in section content',
        suggestion: 'Verify the original text exists before making changes'
      });
      confidence -= 30;
    }
    
    // Check if change is relevant to comments
    const isRelevant = this.isChangeRelevantToComments(change, comments);
    if (!isRelevant) {
      issues.push({
        type: 'relevance',
        severity: 'medium',
        description: 'Change does not clearly address any reviewer comment',
        suggestion: 'Ensure changes directly address specific reviewer feedback'
      });
      confidence -= 20;
    }
    
    // Check for potential figure/table references
    if (this.containsFigureTableReference(change.reason)) {
      issues.push({
        type: 'factual',
        severity: 'medium',
        description: 'Change involves figures/tables that cannot be verified',
        suggestion: 'Mark figure/table changes for manual review'
      });
      confidence -= 15;
    }
    
    return { isValid: confidence >= 70, confidence, issues, recommendations: [] };
  }
  
  /**
   * Validate comment addressing
   */
  private static validateCommentAddressing(
    revision: SectionRevision,
    comments: any[]
  ): ValidationResult {
    const issues: ValidationIssue[] = [];
    let confidence = 90;
    
    const addressedCount = revision.addressedComments.length;
    const totalComments = comments.length;
    
    if (addressedCount === 0 && totalComments > 0) {
      issues.push({
        type: 'relevance',
        severity: 'high',
        description: 'No comments were addressed in this revision',
        suggestion: 'Ensure at least some reviewer comments are addressed'
      });
      confidence -= 40;
    }
    
    if (addressedCount < totalComments * 0.5) {
      issues.push({
        type: 'relevance',
        severity: 'medium',
        description: 'Less than half of the comments were addressed',
        suggestion: 'Consider addressing more reviewer comments or explaining why they cannot be addressed'
      });
      confidence -= 20;
    }
    
    return { isValid: confidence >= 70, confidence, issues, recommendations: [] };
  }
  
  /**
   * Validate academic tone
   */
  private static validateAcademicTone(content: string): ValidationResult {
    const issues: ValidationIssue[] = [];
    let confidence = 90;
    
    // Check for informal language
    const informalPatterns = [
      /\b(gonna|wanna|gotta)\b/gi,
      /\b(very|really|quite)\s+\w+/gi,
      /\!/g
    ];
    
    informalPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        issues.push({
          type: 'tone',
          severity: 'low',
          description: 'Content may contain informal language',
          suggestion: 'Use formal academic language throughout'
        });
        confidence -= 5;
      }
    });
    
    return { isValid: confidence >= 70, confidence, issues, recommendations: [] };
  }
  
  /**
   * Check if change is relevant to comments
   */
  private static isChangeRelevantToComments(change: ArticleChange, comments: any[]): boolean {
    const changeText = (change.reason + ' ' + change.revisedText).toLowerCase();
    
    return comments.some(comment => {
      const commentText = comment.comment.toLowerCase();
      const commonWords = this.getCommonWords(changeText, commentText);
      return commonWords.length >= 2; // At least 2 common meaningful words
    });
  }
  
  /**
   * Check for figure/table references
   */
  private static containsFigureTableReference(text: string): boolean {
    const figureTablePatterns = [
      /figure\s*\d+/gi,
      /table\s*\d+/gi,
      /fig\.\s*\d+/gi,
      /chart/gi,
      /graph/gi
    ];
    
    return figureTablePatterns.some(pattern => pattern.test(text));
  }
  
  /**
   * Get common meaningful words between two texts
   */
  private static getCommonWords(text1: string, text2: string): string[] {
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);
    
    const words1 = text1.split(/\W+/).filter(w => w.length > 3 && !stopWords.has(w.toLowerCase()));
    const words2 = text2.split(/\W+/).filter(w => w.length > 3 && !stopWords.has(w.toLowerCase()));
    
    return words1.filter(w1 => 
      words2.some(w2 => w1.toLowerCase() === w2.toLowerCase())
    );
  }
  
  /**
   * Generate recommendations based on issues
   */
  private static generateRecommendations(issues: ValidationIssue[]): string[] {
    const recommendations: string[] = [];
    
    const highSeverityIssues = issues.filter(i => i.severity === 'high');
    if (highSeverityIssues.length > 0) {
      recommendations.push('Review high-severity issues before proceeding');
    }
    
    const accuracyIssues = issues.filter(i => i.type === 'accuracy');
    if (accuracyIssues.length > 0) {
      recommendations.push('Verify all changes against the original content');
    }
    
    const relevanceIssues = issues.filter(i => i.type === 'relevance');
    if (relevanceIssues.length > 0) {
      recommendations.push('Ensure changes directly address reviewer comments');
    }
    
    return recommendations;
  }
}
