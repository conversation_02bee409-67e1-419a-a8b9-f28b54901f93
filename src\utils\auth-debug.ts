// Authentication debugging utility
// This helps debug authentication issues in development and production

export const debugAuthConfig = () => {
  const isDev = !import.meta.env.PROD
  const currentOrigin = window.location.origin
  const expectedRedirectUrl = isDev ? 
    `${currentOrigin}/auth/callback` : 
    'https://verbira.com/auth/callback'

  console.log('🔍 Authentication Configuration Debug:')
  console.log('Environment:', isDev ? 'Development' : 'Production')
  console.log('Current Origin:', currentOrigin)
  console.log('Expected Redirect URL:', expectedRedirectUrl)
  console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)
  
  // Check if we're in the right environment
  if (isDev && !currentOrigin.includes('localhost')) {
    console.warn('⚠️ Development mode but not on localhost!')
  }
  
  if (!isDev && !currentOrigin.includes('verbira.com')) {
    console.warn('⚠️ Production mode but not on verbira.com!')
  }
  
  return {
    isDev,
    currentOrigin,
    expectedRedirectUrl,
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL
  }
}

// Call this in development to check configuration
if (import.meta.env.DEV) {
  debugAuthConfig()
}
