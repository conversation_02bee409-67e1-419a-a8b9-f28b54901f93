/**
 * Citation Enforcement Test
 * Tests the system's ability to ensure all found sources are properly used
 */

import { citationEnforcerService } from './services/citation-enforcer.service';

export class CitationEnforcementTester {
  private testCases = [
    {
      name: "PSInSAR Research - Under-cited",
      content: `Persistent Scatterer Interferometric Synthetic Aperture Radar (PSInSAR) has emerged as a powerful technique for monitoring ground deformation. The method provides millimeter-level precision in measuring surface displacement over large areas (Smith, 2023). Urban subsidence monitoring represents one of the most important applications of this technology.

Recent developments in satellite technology have improved the temporal resolution of interferometric measurements. The availability of Sentinel-1 data has revolutionized the field by providing regular acquisitions with short revisit times. Processing techniques have also evolved to handle the increasing volume of SAR data more efficiently.

Research gaps remain in the validation of PSInSAR measurements against ground truth data. The integration of multiple satellite platforms could potentially improve the accuracy and reliability of deformation monitoring systems.`,
      
      availableSources: [
        {
          id: "1",
          title: "PSInSAR Methodology for Urban Deformation Monitoring",
          authors: ["<PERSON>, J<PERSON>", "Johnson, K."],
          year: 2023,
          citation: "(<PERSON>, 2023)",
          relevantText: "PSInSAR technique provides millimeter-level precision for ground deformation monitoring in urban environments."
        },
        {
          id: "2", 
          title: "Sentinel-1 Data Processing for Interferometric Applications",
          authors: ["<PERSON>, <PERSON>.", "<PERSON>, M."],
          year: 2022,
          citation: "(Brown, 2022)",
          relevantText: "Sentinel-1 constellation offers unprecedented temporal resolution for interferometric monitoring with 6-day revisit time."
        },
        {
          id: "3",
          title: "Validation of PSInSAR Measurements Using GPS Data",
          authors: ["Wilson, R.", "Taylor, S."],
          year: 2023,
          citation: "(Wilson, 2023)",
          relevantText: "Comparison between PSInSAR and GPS measurements shows correlation coefficient of 0.92 for vertical displacement."
        },
        {
          id: "4",
          title: "Multi-platform SAR Data Integration for Deformation Studies",
          authors: ["Garcia, L.", "Martinez, P."],
          year: 2022,
          citation: "(Garcia, 2022)",
          relevantText: "Integration of Sentinel-1 and ALOS-2 data improves deformation measurement accuracy by 25%."
        },
        {
          id: "5",
          title: "Urban Subsidence Monitoring Using Advanced InSAR Techniques",
          authors: ["Lee, H.", "Kim, S.", "Park, J."],
          year: 2023,
          citation: "(Lee et al., 2023)",
          relevantText: "Advanced InSAR processing reveals subsidence rates up to 50 mm/year in metropolitan areas."
        }
      ],
      expectedMinCitations: 15,
      currentCitations: 1
    },
    
    {
      name: "Machine Learning Research - Moderate Citations",
      content: `Machine learning approaches have shown significant promise in medical diagnosis applications. Deep learning models, particularly convolutional neural networks, have achieved remarkable accuracy in image-based diagnostic tasks (Johnson, 2023). The integration of artificial intelligence in healthcare represents a paradigm shift in medical practice.

Recent studies have demonstrated the effectiveness of neural networks in detecting early-stage diseases from medical imaging data (Brown, 2022). Transfer learning techniques have enabled the adaptation of pre-trained models to specific medical domains with limited training data (Wilson, 2023). These advances have opened new possibilities for automated screening and diagnosis.

However, challenges remain in the clinical implementation of AI-based diagnostic systems. Issues related to model interpretability, data privacy, and regulatory approval continue to limit widespread adoption (Garcia, 2022). Future research should focus on developing more transparent and explainable AI models for medical applications.`,
      
      availableSources: [
        {
          id: "1",
          title: "Deep Learning for Medical Image Analysis",
          authors: ["Johnson, A."],
          year: 2023,
          citation: "(Johnson, 2023)",
          relevantText: "CNN models achieve 95% accuracy in medical image classification tasks."
        },
        {
          id: "2",
          title: "AI in Healthcare: Current Applications and Future Prospects",
          authors: ["Brown, M.", "Davis, K."],
          year: 2022,
          citation: "(Brown, 2022)",
          relevantText: "AI applications in healthcare show 30% improvement in diagnostic accuracy."
        },
        {
          id: "3",
          title: "Transfer Learning in Medical AI Applications",
          authors: ["Wilson, S."],
          year: 2023,
          citation: "(Wilson, 2023)",
          relevantText: "Transfer learning reduces training time by 60% while maintaining diagnostic performance."
        },
        {
          id: "4",
          title: "Challenges in Clinical AI Implementation",
          authors: ["Garcia, R.", "Martinez, L."],
          year: 2022,
          citation: "(Garcia, 2022)",
          relevantText: "Regulatory and interpretability challenges limit AI adoption in clinical settings."
        }
      ],
      expectedMinCitations: 15,
      currentCitations: 4
    }
  ];

  /**
   * Test citation enforcement across different scenarios
   */
  async testCitationEnforcement(): Promise<void> {
    console.log('🧪 Testing Citation Enforcement System...\n');
    console.log('═'.repeat(80));

    for (let i = 0; i < this.testCases.length; i++) {
      const testCase = this.testCases[i];
      console.log(`\n📋 Test Case ${i + 1}: ${testCase.name}`);
      console.log('─'.repeat(60));

      await this.testSingleCase(testCase);
      console.log('═'.repeat(80));
    }

    console.log('\n✅ Citation Enforcement Testing Completed!');
  }

  /**
   * Test a single citation enforcement case
   */
  private async testSingleCase(testCase: any): Promise<void> {
    const { name, content, availableSources, expectedMinCitations, currentCitations } = testCase;

    console.log(`📊 Initial State:`);
    console.log(`- Current citations: ${currentCitations}`);
    console.log(`- Available sources: ${availableSources.length}`);
    console.log(`- Target citations: ${expectedMinCitations}`);
    console.log(`- Citations needed: ${expectedMinCitations - currentCitations}`);

    try {
      // Test the citation enforcement
      console.log('\n🔧 Running citation enforcement...');
      
      // Simulate the enforcement process
      const enforcementResult = await this.simulateEnforcement(
        content,
        availableSources,
        expectedMinCitations
      );

      console.log('\n📊 Enforcement Results:');
      console.log(`- Citations added: ${enforcementResult.citationsAdded}`);
      console.log(`- Final citations: ${enforcementResult.finalCitations}`);
      console.log(`- Sources used: ${enforcementResult.sourcesUsed}/${availableSources.length}`);
      console.log(`- Success rate: ${(enforcementResult.finalCitations / expectedMinCitations * 100).toFixed(1)}%`);

      // Validate the results
      const validation = this.validateEnforcementResults(enforcementResult, expectedMinCitations);
      console.log('\n🎯 Validation Results:');
      validation.forEach(result => console.log(`   ${result}`));

      // Show sample enhanced content
      console.log('\n📝 Sample Enhanced Content:');
      console.log(enforcementResult.sampleContent.substring(0, 300) + '...');

    } catch (error) {
      console.error('❌ Citation enforcement test failed:', error);
    }
  }

  /**
   * Simulate citation enforcement (for testing purposes)
   */
  private async simulateEnforcement(
    content: string,
    sources: any[],
    targetCitations: number
  ): Promise<any> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    const currentCitations = this.countCitations(content);
    const citationsNeeded = Math.max(0, targetCitations - currentCitations);
    
    // Simulate realistic enforcement results
    const citationsAdded = Math.min(citationsNeeded, sources.length - 1);
    const finalCitations = currentCitations + citationsAdded;
    const sourcesUsed = Math.min(sources.length, Math.ceil(finalCitations * 0.8));

    // Generate sample enhanced content
    const sampleContent = this.generateSampleEnhancedContent(content, sources, citationsAdded);

    return {
      citationsAdded,
      finalCitations,
      sourcesUsed,
      sampleContent,
      enforcementStrategies: [
        'Added citations to existing sentences',
        'Inserted new sentences with citations',
        'Enhanced paragraph transitions with references'
      ]
    };
  }

  /**
   * Count citations in content
   */
  private countCitations(content: string): number {
    const citationPattern = /\([^)]+,\s*\d{4}\)/g;
    const matches = content.match(citationPattern);
    return matches ? matches.length : 0;
  }

  /**
   * Generate sample enhanced content for demonstration
   */
  private generateSampleEnhancedContent(
    originalContent: string,
    sources: any[],
    citationsAdded: number
  ): string {
    let enhanced = originalContent;
    
    // Add sample citations to demonstrate enhancement
    const sampleCitations = sources.slice(1, citationsAdded + 1).map(source => source.citation);
    
    // Insert citations into the content (simplified simulation)
    const sentences = enhanced.split('. ');
    for (let i = 0; i < Math.min(citationsAdded, sentences.length - 1); i++) {
      if (i < sampleCitations.length && !sentences[i + 1].includes('(')) {
        sentences[i + 1] = sentences[i + 1] + ' ' + sampleCitations[i];
      }
    }
    
    return sentences.join('. ');
  }

  /**
   * Validate enforcement results
   */
  private validateEnforcementResults(results: any, targetCitations: number): string[] {
    const validation: string[] = [];
    
    // Check citation target achievement
    const achievementRate = results.finalCitations / targetCitations;
    if (achievementRate >= 0.9) {
      validation.push('✅ Citation target achieved (90%+ of target)');
    } else if (achievementRate >= 0.7) {
      validation.push('⚠️ Citation target partially achieved (70-89% of target)');
    } else {
      validation.push('❌ Citation target not achieved (<70% of target)');
    }
    
    // Check source utilization
    const sourceUtilization = results.sourcesUsed / results.finalCitations;
    if (sourceUtilization >= 0.8) {
      validation.push('✅ Good source utilization (80%+ sources used)');
    } else if (sourceUtilization >= 0.6) {
      validation.push('⚠️ Moderate source utilization (60-79% sources used)');
    } else {
      validation.push('❌ Poor source utilization (<60% sources used)');
    }
    
    // Check citation density
    const citationDensity = results.finalCitations / 1000; // Assuming ~1000 words
    if (citationDensity >= 1.5) {
      validation.push('✅ Appropriate citation density (1.5+ citations per 100 words)');
    } else if (citationDensity >= 1.0) {
      validation.push('⚠️ Moderate citation density (1.0-1.4 citations per 100 words)');
    } else {
      validation.push('❌ Low citation density (<1.0 citations per 100 words)');
    }
    
    return validation;
  }

  /**
   * Test citation distribution validation
   */
  async testCitationDistribution(): Promise<void> {
    console.log('\n🔍 Testing Citation Distribution Validation...\n');

    const testContent = `This is the first paragraph with some content but no citations. It discusses the background of the research topic and provides context for the study.

This is the second paragraph that has one citation (Smith, 2023). It builds upon the previous paragraph and introduces more specific concepts related to the research area.

The third paragraph contains multiple citations (Brown, 2022; Wilson, 2023; Garcia, 2022). This paragraph demonstrates proper citation density and shows how multiple sources can support complex arguments.

The final paragraph has minimal citations (Johnson, 2023) and concludes the introduction section. It summarizes the key points and transitions to the methodology.`;

    const validation = citationEnforcerService.validateCitationDistribution(testContent, 2);
    
    console.log('📊 Citation Distribution Analysis:');
    console.log(`- Valid distribution: ${validation.isValid ? 'Yes' : 'No'}`);
    
    if (validation.issues.length > 0) {
      console.log('\n⚠️ Issues found:');
      validation.issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    if (validation.suggestions.length > 0) {
      console.log('\n💡 Suggestions:');
      validation.suggestions.forEach(suggestion => console.log(`   - ${suggestion}`));
    }
  }
}

// Export test runner
export async function runCitationEnforcementTest(): Promise<void> {
  const tester = new CitationEnforcementTester();
  await tester.testCitationEnforcement();
  await tester.testCitationDistribution();
}

// Add to window for manual testing
if (typeof window !== 'undefined') {
  (window as any).testCitationEnforcement = runCitationEnforcementTest;
}
