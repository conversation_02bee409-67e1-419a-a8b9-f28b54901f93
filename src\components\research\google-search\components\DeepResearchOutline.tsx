/**
 * Deep Research Outline Component
 * Allows users to view and edit the generated research outline
 */

import React, { useState } from 'react';
import { 
  Edit3, 
  Plus, 
  Trash2, 
  ArrowUp, 
  ArrowDown, 
  BookOpen, 
  Target, 
  Users,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';

import { ResearchOutline, SubTopic, DeepResearchOptions } from '../types';

interface DeepResearchOutlineProps {
  outline: ResearchOutline;
  options: DeepResearchOptions;
  isEditable: boolean;
  onOutlineChange: (outline: ResearchOutline) => void;
  onOptionsChange: (options: DeepResearchOptions) => void;
  onProceedToResearch: () => void;
  onRegenerateOutline: () => void;
  isGenerating?: boolean;
}

export function DeepResearchOutline({
  outline,
  options,
  isEditable,
  onOutlineChange,
  onOptionsChange,
  onProceedToResearch,
  onRegenerateOutline,
  isGenerating = false
}: DeepResearchOutlineProps) {
  const [editingSubtopic, setEditingSubtopic] = useState<string | null>(null);
  const [editingMain, setEditingMain] = useState(false);

  const updateMainTopic = (field: keyof ResearchOutline, value: any) => {
    onOutlineChange({ ...outline, [field]: value });
  };

  const updateSubtopic = (subtopicId: string, updates: Partial<SubTopic>) => {
    const updatedSubtopics = outline.subtopics.map(st =>
      st.id === subtopicId ? { ...st, ...updates } : st
    );
    onOutlineChange({ ...outline, subtopics: updatedSubtopics });
  };

  const addSubtopic = () => {
    const newSubtopic: SubTopic = {
      id: `subtopic-${Date.now()}`,
      title: 'New Subtopic',
      description: 'Description for the new subtopic',
      keyQuestions: ['What are the key aspects?'],
      estimatedWords: options.wordsPerSection,
      priority: 'medium',
      searchTerms: ['new subtopic'],
      order: outline.subtopics.length + 1
    };
    
    onOutlineChange({
      ...outline,
      subtopics: [...outline.subtopics, newSubtopic]
    });
    setEditingSubtopic(newSubtopic.id);
  };

  const removeSubtopic = (subtopicId: string) => {
    const updatedSubtopics = outline.subtopics
      .filter(st => st.id !== subtopicId)
      .map((st, index) => ({ ...st, order: index + 1 }));
    
    onOutlineChange({ ...outline, subtopics: updatedSubtopics });
  };

  const moveSubtopic = (subtopicId: string, direction: 'up' | 'down') => {
    const currentIndex = outline.subtopics.findIndex(st => st.id === subtopicId);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === outline.subtopics.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const updatedSubtopics = [...outline.subtopics];
    [updatedSubtopics[currentIndex], updatedSubtopics[newIndex]] = 
    [updatedSubtopics[newIndex], updatedSubtopics[currentIndex]];
    
    // Update order numbers
    updatedSubtopics.forEach((st, index) => {
      st.order = index + 1;
    });

    onOutlineChange({ ...outline, subtopics: updatedSubtopics });
  };

  const canProceed = outline.subtopics.length >= 3 && outline.subtopics.length <= 8;
  const estimatedTotalWords = outline.subtopics.length * options.wordsPerSection;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BookOpen className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-semibold text-gray-900">Research Outline</h2>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {outline.subtopics.length} sections
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={onRegenerateOutline}
            disabled={isGenerating}
          >
            <Edit3 className="h-4 w-4 mr-2" />
            Regenerate
          </Button>
          <Button
            onClick={onProceedToResearch}
            disabled={!canProceed || isGenerating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Target className="h-4 w-4 mr-2" />
            Start Research
          </Button>
        </div>
      </div>

      {/* Research Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Research Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Target Audience
            </label>
            <Select
              value={options.targetAudience}
              onValueChange={(value: any) => onOptionsChange({ ...options, targetAudience: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="academic">Academic</SelectItem>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="general">General Public</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Words per Section
            </label>
            <Select
              value={options.wordsPerSection.toString()}
              onValueChange={(value) => onOptionsChange({ ...options, wordsPerSection: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="500">500 words</SelectItem>
                <SelectItem value="700">700 words</SelectItem>
                <SelectItem value="1000">1000 words</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Research Depth
            </label>
            <Select
              value={options.researchDepth}
              onValueChange={(value: any) => onOptionsChange({ ...options, researchDepth: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
                <SelectItem value="comprehensive">Comprehensive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Topic */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Main Topic</span>
            {isEditable && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingMain(!editingMain)}
              >
                <Edit3 className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {editingMain ? (
            <div className="space-y-3">
              <Input
                value={outline.mainTopic}
                onChange={(e) => updateMainTopic('mainTopic', e.target.value)}
                placeholder="Main research topic"
              />
              <Textarea
                value={outline.description}
                onChange={(e) => updateMainTopic('description', e.target.value)}
                placeholder="Research description and objectives"
                rows={3}
              />
              <Button
                size="sm"
                onClick={() => setEditingMain(false)}
              >
                Save Changes
              </Button>
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {outline.mainTopic}
              </h3>
              <p className="text-gray-600">{outline.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Subtopics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Research Sections</span>
            {isEditable && (
              <Button
                variant="outline"
                size="sm"
                onClick={addSubtopic}
                disabled={outline.subtopics.length >= 8}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {outline.subtopics.map((subtopic, index) => (
                <SubtopicCard
                  key={subtopic.id}
                  subtopic={subtopic}
                  index={index}
                  isEditing={editingSubtopic === subtopic.id}
                  isEditable={isEditable}
                  canMoveUp={index > 0}
                  canMoveDown={index < outline.subtopics.length - 1}
                  onEdit={() => setEditingSubtopic(subtopic.id)}
                  onSave={() => setEditingSubtopic(null)}
                  onUpdate={(updates) => updateSubtopic(subtopic.id, updates)}
                  onRemove={() => removeSubtopic(subtopic.id)}
                  onMoveUp={() => moveSubtopic(subtopic.id, 'up')}
                  onMoveDown={() => moveSubtopic(subtopic.id, 'down')}
                />
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {outline.subtopics.length}
              </div>
              <div className="text-sm text-gray-600">Research Sections</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                ~{estimatedTotalWords.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Estimated Words</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {outline.researchQuestions.length}
              </div>
              <div className="text-sm text-gray-600">Research Questions</div>
            </div>
          </div>
          
          {!canProceed && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                Please include 3-8 research sections to proceed.
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface SubtopicCardProps {
  subtopic: SubTopic;
  index: number;
  isEditing: boolean;
  isEditable: boolean;
  canMoveUp: boolean;
  canMoveDown: boolean;
  onEdit: () => void;
  onSave: () => void;
  onUpdate: (updates: Partial<SubTopic>) => void;
  onRemove: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

function SubtopicCard({
  subtopic,
  index,
  isEditing,
  isEditable,
  canMoveUp,
  canMoveDown,
  onEdit,
  onSave,
  onUpdate,
  onRemove,
  onMoveUp,
  onMoveDown
}: SubtopicCardProps) {
  const [localSubtopic, setLocalSubtopic] = useState(subtopic);

  const handleSave = () => {
    onUpdate(localSubtopic);
    onSave();
  };

  const addKeyQuestion = () => {
    setLocalSubtopic({
      ...localSubtopic,
      keyQuestions: [...localSubtopic.keyQuestions, 'New question?']
    });
  };

  const updateKeyQuestion = (questionIndex: number, value: string) => {
    const updatedQuestions = [...localSubtopic.keyQuestions];
    updatedQuestions[questionIndex] = value;
    setLocalSubtopic({ ...localSubtopic, keyQuestions: updatedQuestions });
  };

  const removeKeyQuestion = (questionIndex: number) => {
    const updatedQuestions = localSubtopic.keyQuestions.filter((_, i) => i !== questionIndex);
    setLocalSubtopic({ ...localSubtopic, keyQuestions: updatedQuestions });
  };

  const priorityColors = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800'
  };

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
              {index + 1}
            </div>
            <div>
              {isEditing ? (
                <Input
                  value={localSubtopic.title}
                  onChange={(e) => setLocalSubtopic({ ...localSubtopic, title: e.target.value })}
                  className="font-medium"
                />
              ) : (
                <h4 className="font-medium text-gray-900">{subtopic.title}</h4>
              )}
            </div>
            <Badge className={priorityColors[subtopic.priority]}>
              {subtopic.priority}
            </Badge>
          </div>

          {isEditable && (
            <div className="flex items-center space-x-1">
              {canMoveUp && (
                <Button variant="ghost" size="sm" onClick={onMoveUp}>
                  <ArrowUp className="h-4 w-4" />
                </Button>
              )}
              {canMoveDown && (
                <Button variant="ghost" size="sm" onClick={onMoveDown}>
                  <ArrowDown className="h-4 w-4" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={isEditing ? handleSave : onEdit}
              >
                {isEditing ? <CheckCircle className="h-4 w-4" /> : <Edit3 className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="sm" onClick={onRemove}>
                <Trash2 className="h-4 w-4 text-red-500" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Description */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-1 block">
            Description
          </label>
          {isEditing ? (
            <Textarea
              value={localSubtopic.description}
              onChange={(e) => setLocalSubtopic({ ...localSubtopic, description: e.target.value })}
              rows={2}
            />
          ) : (
            <p className="text-gray-600 text-sm">{subtopic.description}</p>
          )}
        </div>

        {/* Key Questions */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">
              Key Questions
            </label>
            {isEditing && (
              <Button variant="ghost" size="sm" onClick={addKeyQuestion}>
                <Plus className="h-3 w-3" />
              </Button>
            )}
          </div>
          <div className="space-y-2">
            {(isEditing ? localSubtopic.keyQuestions : subtopic.keyQuestions).map((question, qIndex) => (
              <div key={qIndex} className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0 mt-1"></div>
                {isEditing ? (
                  <div className="flex-1 flex items-center space-x-2">
                    <Input
                      value={question}
                      onChange={(e) => updateKeyQuestion(qIndex, e.target.value)}
                      className="text-sm"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeKeyQuestion(qIndex)}
                    >
                      <Trash2 className="h-3 w-3 text-red-500" />
                    </Button>
                  </div>
                ) : (
                  <span className="text-sm text-gray-600">{question}</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Priority and Word Count */}
        {isEditing && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Priority
              </label>
              <Select
                value={localSubtopic.priority}
                onValueChange={(value: any) => setLocalSubtopic({ ...localSubtopic, priority: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Target Words
              </label>
              <Input
                type="number"
                value={localSubtopic.estimatedWords}
                onChange={(e) => setLocalSubtopic({
                  ...localSubtopic,
                  estimatedWords: parseInt(e.target.value) || 500
                })}
                min="300"
                max="1500"
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
