/**
 * Google Gemini Flowchart Service
 * Handles AI-powered flowchart generation using Google's Gemini API
 */

import { GoogleGenAI } from '@google/genai';

export interface FlowchartGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  mode?: 'prompt' | 'convert' | 'edit';
}

export interface FlowchartGenerationRequest {
  prompt: string;
  mode: 'prompt' | 'convert' | 'edit';
  currentText?: string;
}

export interface FlowchartGenerationResponse {
  success: boolean;
  flowchartText?: string;
  error?: string;
}

export class GeminiFlowchartService {
  private ai: GoogleGenAI;
  private isConfigured: boolean = false;
  private lastRequestTime: number = 0;
  private readonly RATE_LIMIT_DELAY = 2000; // 2 seconds between requests

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    
    if (!apiKey || apiKey.length < 20) {
      console.warn('Google Gemini API key not found or invalid');
      this.isConfigured = false;
      return;
    }

    try {
      this.ai = new GoogleGenAI({ apiKey });
      this.isConfigured = true;
      console.log('Gemini Flowchart Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Gemini Flowchart Service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is properly configured
   */
  isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get available Gemini models for flowchart generation
   */
  getAvailableModels(): string[] {
    return [
      'gemini-2.5-flash',
      'gemini-2.5-pro'
    ];
  }

  /**
   * Get the default Gemini model
   */
  getDefaultModel(): string {
    return 'gemini-2.5-pro';
  }

  /**
   * Enforce rate limiting between requests
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const waitTime = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      console.log(`Rate limiting: waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Generate flowchart text using Gemini
   */
  async generateFlowchart(
    request: FlowchartGenerationRequest,
    options: FlowchartGenerationOptions = {}
  ): Promise<FlowchartGenerationResponse> {
    if (!this.isConfigured) {
      return {
        success: false,
        error: 'Gemini service not configured. Please check your API key.'
      };
    }

    await this.enforceRateLimit();

    const {
      model = 'gemini-2.5-pro',
      temperature = 0.7,
      maxTokens = 4096
    } = options;

    try {
      const prompt = this.buildPrompt(request);
      
      console.log('Making Gemini flowchart request:', {
        model,
        mode: request.mode,
        promptLength: prompt.length
      });

      const response = await this.ai.models.generateContent({
        model: model.replace('google/', ''), // Remove google/ prefix for direct API
        config: {
          generationConfig: {
            maxOutputTokens: maxTokens,
            temperature: temperature
          }
        },
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }]
      });

      const content = response.text || '';

      if (!content) {
        throw new Error('No content received from Gemini service');
      }

      // Extract flowchart text from response
      const flowchartText = this.extractFlowchartText(content);

      return {
        success: true,
        flowchartText
      };

    } catch (error) {
      console.error('Gemini flowchart generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Generate streaming flowchart response
   */
  async *generateStreamingFlowchart(
    request: FlowchartGenerationRequest,
    options: FlowchartGenerationOptions = {}
  ): AsyncGenerator<string, void, unknown> {
    if (!this.isConfigured) {
      throw new Error('Gemini service not configured. Please check your API key.');
    }

    await this.enforceRateLimit();

    const {
      model = 'gemini-2.5-pro',
      temperature = 0.7,
      maxTokens = 4096
    } = options;

    const prompt = this.buildPrompt(request);
    
    console.log('Making streaming Gemini flowchart request:', {
      model,
      mode: request.mode,
      promptLength: prompt.length
    });

    const response = await this.ai.models.generateContentStream({
      model: model.replace('google/', ''),
      config: {
        generationConfig: {
          maxOutputTokens: maxTokens,
          temperature: temperature
        }
      },
      contents: [{
        role: 'user',
        parts: [{ text: prompt }]
      }]
    });

    for await (const chunk of response) {
      if (chunk.text) {
        yield chunk.text;
      }
    }
  }

  /**
   * Build the appropriate prompt based on the request mode
   */
  private buildPrompt(request: FlowchartGenerationRequest): string {
    const { mode, prompt, currentText } = request;

    switch (mode) {
      case 'prompt':
        return this.buildPromptModePrompt(prompt);
      case 'convert':
        return this.buildConvertModePrompt(prompt);
      case 'edit':
        return this.buildEditModePrompt(prompt, currentText || '');
      default:
        return this.buildPromptModePrompt(prompt);
    }
  }

  /**
   * Build prompt for creating new flowcharts from description
   */
  private buildPromptModePrompt(userPrompt: string): string {
    return `You are an expert at creating flowcharts using a simple indentation-based text syntax. Create a detailed flowchart based on the following description:

"${userPrompt}"

IMPORTANT SYNTAX RULES:
- Each line represents a node/step
- Use 2-space indentation to show hierarchy and flow
- Child nodes are indented under their parent
- Use ":" to add connection labels or descriptions
- Keep node names clear and concise
- Create logical flow with proper branching

EXAMPLES:

Simple Process:
Start
  Gather Requirements
  Design System
  Implement Features
  Test Application
  Deploy to Production
End

Decision Flow:
User Login
  Enter Credentials
  Validate Input: Check database
    Valid Credentials: Success
      Dashboard
      User Profile
    Invalid Credentials: Error
      Show Error Message
      Retry Login: Back to Enter Credentials
Account Locked

Research Process:
Literature Review
  Search Strategy: Database search
    PubMed: Medical articles
    Scopus: Multidisciplinary
    Web of Science: Citation analysis
  Screening Process
    Title Review: Initial filter
    Abstract Review: Detailed assessment
    Full Text Review: Final selection
  Data Extraction
    Study Characteristics: Author, year, design
    Outcome Measures: Primary and secondary
  Analysis
    Descriptive Statistics: Summary data
    Meta-analysis: Pooled results
  Reporting: PRISMA guidelines

Generate ONLY the flowchart text using this exact syntax. No explanations, no markdown code blocks, no additional text.`;
  }

  /**
   * Build prompt for converting existing text to flowchart format
   */
  private buildConvertModePrompt(textToConvert: string): string {
    return `Convert the following text into a properly structured flowchart using indentation-based syntax:

TEXT TO CONVERT:
"${textToConvert}"

CONVERSION RULES:
- Transform lists, steps, or processes into flowchart nodes
- Use 2-space indentation to show hierarchy
- Identify decision points and create branches
- Convert numbered/bulleted lists into sequential steps
- Add descriptive labels using ":" for connections
- Maintain logical flow and relationships

SYNTAX:
- Each line = one node/step
- Indentation = hierarchy (2 spaces per level)
- Use ":" for connection labels or descriptions
- Keep node names clear and actionable

EXAMPLE CONVERSION:
Input: "1. Start process 2. Check requirements 3. If valid, proceed to design 4. If invalid, return to step 1 5. Complete design 6. Review and approve"

Output:
Start Process
  Check Requirements
  Validation: Review criteria
    Valid Requirements: Proceed
      Design Phase
        Create Mockups
        Technical Specifications
      Review Process
        Stakeholder Review
        Approval Decision
    Invalid Requirements: Return
      (Check Requirements)

Generate ONLY the converted flowchart text. No explanations or additional formatting.`;
  }

  /**
   * Build prompt for editing existing flowcharts
   */
  private buildEditModePrompt(editInstructions: string, currentFlowchart: string): string {
    return `Modify the existing flowchart based on the provided instructions. Maintain the current structure while implementing the requested changes.

EDIT INSTRUCTIONS:
"${editInstructions}"

CURRENT FLOWCHART:
${currentFlowchart}

EDITING GUIDELINES:
- Preserve existing structure and flow where possible
- Add new nodes/steps as requested
- Modify existing nodes if specified
- Maintain proper indentation (2 spaces per level)
- Keep logical flow and relationships intact
- Use ":" for connection labels or descriptions
- Ensure all changes align with the instructions

COMMON EDIT TYPES:
- Add error handling: Insert error checking and fallback paths
- Add validation: Include validation steps before processing
- Add logging: Insert logging/tracking nodes at key points
- Add parallel processing: Create concurrent branches
- Modify flow: Change sequence or decision logic
- Add details: Expand existing nodes with sub-steps

Generate ONLY the modified flowchart text. Maintain the same syntax and formatting style as the original.`;
  }

  /**
   * Extract clean flowchart text from AI response
   */
  private extractFlowchartText(response: string): string {
    // Remove markdown code blocks if present
    let text = response.replace(/```[\w]*\n?/g, '').trim();
    
    // Remove any explanatory text before or after the flowchart
    const lines = text.split('\n');
    const flowchartLines: string[] = [];
    let inFlowchart = false;
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip empty lines at the beginning
      if (!inFlowchart && trimmedLine === '') continue;
      
      // Start collecting when we see flowchart-like content
      if (!inFlowchart && (line.match(/^[A-Za-z0-9]/) || line.match(/^\s+[A-Za-z0-9]/))) {
        inFlowchart = true;
      }
      
      if (inFlowchart) {
        flowchartLines.push(line);
      }
    }
    
    return flowchartLines.join('\n').trim();
  }
}
