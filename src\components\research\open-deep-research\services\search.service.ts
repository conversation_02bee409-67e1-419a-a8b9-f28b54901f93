/**
 * Search Service
 * Handles search operations, result analysis, and research optimization
 * Uses Tavily for academic search and OpenRouter for AI processing
 */

import {
  SearchResult,
  SearchResponse,
  OptimizeResearchResponse,
  AnalyzeResultsResponse,
  ModelVariant
} from '../types';
import { ERROR_MESSAGES } from '../constants';

// Import existing services
import { tavilySearchService } from '../../research-search/services/tavily-search.service';
import { researchAIService } from '../../research-search/services/research-ai.service';

interface SearchParams {
  query: string;
  timeFilter: string;
  provider: 'tavily' | 'google' | 'bing' | 'exa';
}

interface OptimizeResearchParams {
  prompt: string;
  model: ModelVariant;
}

interface AnalyzeResultsParams {
  prompt: string;
  results: SearchResult[];
  model: ModelVariant;
}

class SearchService {
  /**
   * Clean and extract JSO<PERSON> from markdown-wrapped response
   */
  private cleanJsonResponse(response: string): string {
    // Remove markdown code blocks if present
    let cleaned = response.trim();

    // Remove ```json and ``` markers
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.replace(/^```json\s*/, '');
    }
    if (cleaned.startsWith('```')) {
      cleaned = cleaned.replace(/^```\s*/, '');
    }
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.replace(/\s*```$/, '');
    }

    // Find JSON object boundaries
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }

    return cleaned.trim();
  }

  private async makeRequest<T>(
    endpoint: string, 
    data: any, 
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        body: JSON.stringify(data),
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(ERROR_MESSAGES.networkError);
    }
  }

  private retryWithBackoff = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: any;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (error instanceof Error && error.message.includes('429')) {
          const delay = baseDelay * Math.pow(2, i);
          console.log(`Rate limited, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        throw error;
      }
    }
    
    throw lastError;
  };

  /**
   * Perform web search using Tavily for academic results
   */
  async search(params: SearchParams): Promise<SearchResult[]> {
    try {
      // Check if Tavily service is configured
      if (!tavilySearchService.isConfigured()) {
        throw new Error('Tavily API key not configured');
      }

      // Enhance query for academic focus
      const enhancedQuery = this.enhanceQueryForAcademic(params.query);

      // Convert time filter to search options
      const searchDepth = params.timeFilter === 'all' ? 'advanced' : 'comprehensive';

      // Use Tavily's academic search
      const tavilyResults = await tavilySearchService.searchAcademic(enhancedQuery, {
        maxResults: 15, // Get more results for better filtering
        searchDepth: searchDepth,
        includeImages: false,
        includeAnswer: true
      });

      // Transform Tavily results to our format
      return tavilyResults.results.map((result, index) => ({
        id: `search-${Date.now()}-${index}-${result.url}`,
        url: result.url,
        name: result.title,
        snippet: result.content,
        score: result.score || 0,
      }));
    } catch (error) {
      console.error('Search error:', error);
      throw new Error(ERROR_MESSAGES.searchFailed);
    }
  }

  /**
   * Enhance query for academic focus
   */
  private enhanceQueryForAcademic(query: string): string {
    const lowerQuery = query.toLowerCase();

    // Preserve exact quoted terms
    const quotedTerms = query.match(/"[^"]+"/g) || [];
    const hasQuotedTerms = quotedTerms.length > 0;

    // Check if query already has academic context
    const academicKeywords = ['research', 'study', 'analysis', 'academic', 'peer-reviewed',
                             'journal', 'methodology', 'paper', 'scholarly'];
    const hasAcademicKeywords = academicKeywords.some(keyword => lowerQuery.includes(keyword));

    // If query already has academic context or quoted terms, use it with minimal enhancement
    if (hasAcademicKeywords || hasQuotedTerms) {
      return `${query} academic research`;
    }

    // Otherwise, enhance with academic focus
    return `${query} scholarly research academic papers`;
  }

  /**
   * Optimize research query and strategy using OpenRouter AI
   */
  async optimizeResearch(params: OptimizeResearchParams): Promise<OptimizeResearchResponse> {
    try {
      // Check if OpenRouter service is configured
      if (!researchAIService.isConfigured()) {
        throw new Error('OpenRouter API key not configured');
      }

      // Create optimization prompt
      const optimizationPrompt = `
You are an academic research assistant helping optimize a research strategy.

RESEARCH TOPIC: "${params.prompt}"

Your task is to:
1. Analyze the research topic and identify key concepts, themes, and potential research questions
2. Create an optimized search query that will yield high-quality academic results
3. Provide a comprehensive research prompt that will guide the analysis
4. Explain your optimization strategy
5. Suggest a logical structure for the final research report

FORMAT YOUR RESPONSE AS JSON:
{
  "query": "optimized search query for academic sources",
  "optimizedPrompt": "comprehensive research prompt",
  "explanation": "explanation of your optimization strategy",
  "suggestedStructure": ["section1", "section2", "etc"]
}
`;

      // Call OpenRouter API
      const response = await researchAIService.callOpenRouterAPI(
        optimizationPrompt,
        params.model
      );

      // Parse JSON response
      try {
        const cleanedResponse = this.cleanJsonResponse(response);
        const parsedResponse = JSON.parse(cleanedResponse);
        return {
          query: parsedResponse.query || params.prompt,
          optimizedPrompt: parsedResponse.optimizedPrompt || `Provide a comprehensive analysis of ${params.prompt}`,
          explanation: parsedResponse.explanation || 'Optimized for academic research',
          suggestedStructure: parsedResponse.suggestedStructure || [
            'Introduction',
            'Literature Review',
            'Methodology',
            'Findings',
            'Discussion',
            'Conclusion'
          ]
        };
      } catch (parseError) {
        console.error('Failed to parse optimization response:', parseError);
        // Fallback to basic structure if parsing fails
        return {
          query: `${params.prompt} academic research scholarly articles`,
          optimizedPrompt: `Provide a comprehensive academic analysis of ${params.prompt}. Focus on recent developments, key findings, methodologies, and implications for the field. Include relevant statistics, expert opinions, and future research directions.`,
          explanation: `Optimized search strategy to focus on academic sources and comprehensive analysis.`,
          suggestedStructure: [
            'Executive Summary',
            'Current State of Research',
            'Key Findings and Developments',
            'Methodologies and Approaches',
            'Implications and Applications',
            'Future Research Directions',
            'Conclusion'
          ]
        };
      }
    } catch (error) {
      console.error('Research optimization error:', error);
      throw new Error('Failed to optimize research strategy');
    }
  }

  /**
   * Analyze and rank search results using OpenRouter AI
   */
  async analyzeResults(params: AnalyzeResultsParams): Promise<AnalyzeResultsResponse> {
    try {
      // Check if OpenRouter service is configured
      if (!researchAIService.isConfigured()) {
        throw new Error('OpenRouter API key not configured');
      }

      // Create analysis prompt
      const analysisPrompt = `
You are an academic research assistant analyzing search results for relevance and quality.

RESEARCH TOPIC: "${params.prompt}"

SEARCH RESULTS TO ANALYZE:
${params.results.map((result, index) => `
${index + 1}. Title: ${result.name}
   URL: ${result.url}
   Snippet: ${result.snippet}
`).join('\n')}

Your task is to:
1. Analyze each source for relevance to the research topic
2. Consider academic quality, credibility, and content depth
3. Rank sources from most to least relevant (score 0.0-1.0)
4. Provide reasoning for each ranking
5. Give an overall analysis summary

FORMAT YOUR RESPONSE AS JSON:
{
  "rankings": [
    {
      "url": "source_url",
      "score": 0.85,
      "reasoning": "explanation of relevance and quality"
    }
  ],
  "analysis": "overall analysis summary"
}
`;

      // Call OpenRouter API
      const response = await researchAIService.callOpenRouterAPI(
        analysisPrompt,
        params.model
      );

      // Parse JSON response
      try {
        const cleanedResponse = this.cleanJsonResponse(response);
        const parsedResponse = JSON.parse(cleanedResponse);
        return {
          rankings: parsedResponse.rankings || this.createFallbackRankings(params.results, params.prompt),
          analysis: parsedResponse.analysis || `Analyzed ${params.results.length} sources for relevance to the research topic.`
        };
      } catch (parseError) {
        console.error('Failed to parse analysis response:', parseError);
        // Fallback to simple ranking if parsing fails
        return {
          rankings: this.createFallbackRankings(params.results, params.prompt),
          analysis: `Analyzed ${params.results.length} sources for relevance to the research topic using fallback analysis.`
        };
      }
    } catch (error) {
      console.error('Result analysis error:', error);
      throw new Error('Failed to analyze search results');
    }
  }

  /**
   * Create fallback rankings when AI analysis fails
   */
  private createFallbackRankings(results: SearchResult[], prompt: string): any[] {
    return results.map(result => {
      const titleScore = this.calculateRelevanceScore(result.name, prompt);
      const snippetScore = this.calculateRelevanceScore(result.snippet, prompt);
      const score = (titleScore + snippetScore) / 2;

      return {
        url: result.url,
        score: Math.max(0.3, Math.min(1.0, score)),
        reasoning: `Relevance score based on keyword matching and content analysis.`
      };
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * Calculate relevance score between text and prompt
   */
  private calculateRelevanceScore(text: string, prompt: string): number {
    const textWords = text.toLowerCase().split(/\W+/);
    const promptWords = prompt.toLowerCase().split(/\W+/);

    let matches = 0;
    promptWords.forEach(word => {
      if (word.length > 3 && textWords.includes(word)) {
        matches++;
      }
    });

    return Math.min(1.0, matches / Math.max(1, promptWords.length));
  }

  /**
   * Add custom URL to results
   */
  createCustomResult(url: string): SearchResult {
    try {
      new URL(url); // Validate URL format
      
      const timestamp = Date.now();
      return {
        id: `custom-${timestamp}-${url}`,
        url,
        name: 'Custom URL',
        snippet: 'Custom URL added by user',
        isCustomUrl: true,
      };
    } catch {
      throw new Error(ERROR_MESSAGES.invalidUrl);
    }
  }

  /**
   * Validate search query
   */
  validateQuery(query: string): boolean {
    return query.trim().length >= 3 && query.trim().length <= 500;
  }

  /**
   * Get search suggestions (placeholder for future implementation)
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    // TODO: Implement search suggestions
    return [];
  }

  /**
   * Get trending topics (placeholder for future implementation)
   */
  async getTrendingTopics(): Promise<string[]> {
    // TODO: Implement trending topics
    return [];
  }
}

export const searchService = new SearchService();
