import { Progress } from "@/components/ui/progress";
import { ArticleSection, ReviewProgress } from '../types';

interface ArticleReviewProgressProps {
  currentSection: ArticleSection | null;
  progress: ReviewProgress;
}

export function ArticleReviewProgress({ currentSection, progress }: ArticleReviewProgressProps) {
  // Map section names to display names
  const sectionDisplayNames: Record<ArticleSection, string> = {
    title: 'Title',
    abstract: 'Abstract',
    keywords: 'Keywords',
    introduction: 'Introduction',
    methodology: 'Methodology',
    results: 'Results',
    discussion: 'Discussion',
    conclusion: 'Conclusion',
    references: 'References',
    overall: 'Overall Analysis'
  };
  
  // Format estimated time remaining
  const formatTimeRemaining = (seconds?: number): string => {
    if (!seconds) return '';
    
    if (seconds < 60) {
      return `About ${seconds} seconds remaining`;
    } else {
      const minutes = Math.ceil(seconds / 60);
      return `About ${minutes} minute${minutes !== 1 ? 's' : ''} remaining`;
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <div className="font-medium">
            {currentSection ? sectionDisplayNames[currentSection] : 'Preparing'}
          </div>
          <div className="text-gray-500">
            {progress.completedSteps}/{progress.totalSteps} sections
          </div>
        </div>
        <Progress value={progress.percentage} />
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span className="text-gray-500">Current task:</span>
          <span className="font-medium ml-1">{progress.currentStep}</span>
        </div>
        {progress.estimatedTimeRemaining !== undefined && (
          <div className="text-right text-gray-500">
            {formatTimeRemaining(progress.estimatedTimeRemaining)}
          </div>
        )}
      </div>
    </div>
  );
}
