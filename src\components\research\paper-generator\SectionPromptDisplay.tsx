import React, { useState } from 'react';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger 
} from "@/components/ui/accordion";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  FileText, 
  MessageSquare, 
  Copy, 
  Download, 
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { PaperSection } from './types';

interface SectionPromptDisplayProps {
  section: PaperSection;
  onRegenerateSection?: (sectionId: string, prompt: string) => void;
  className?: string;
}

export const SectionPromptDisplay: React.FC<SectionPromptDisplayProps> = ({
  section,
  onRegenerateSection,
  className = ""
}) => {
  const [isRegenerating, setIsRegenerating] = useState(false);

  const copyToClipboard = async (text: string, type: 'prompt' | 'content') => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type === 'prompt' ? 'Prompt' : 'Content'} copied to clipboard`);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleRegenerate = async () => {
    if (!onRegenerateSection) return;
    
    setIsRegenerating(true);
    try {
      await onRegenerateSection(section.section_id, section.prompt_text);
      toast.success('Section regeneration started');
    } catch (error) {
      console.error('Error regenerating section:', error);
      toast.error('Failed to regenerate section');
    } finally {
      setIsRegenerating(false);
    }
  };

  const getStatusIcon = () => {
    switch (section.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'generating':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (section.status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'generating':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Accordion type="single" collapsible className={`border border-gray-200 rounded-xl bg-white ${className}`}>
      <AccordionItem value={section.id} className="border-none">
        <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-gray-50">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div className="text-left">
                <h3 className="font-semibold text-lg">{section.section_name}</h3>
                <p className="text-sm text-gray-500">
                  Generated with {section.ai_model} • {formatDate(section.updated_at)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor()}>
                {section.status}
              </Badge>
              {section.generation_metadata?.tokens && (
                <Badge variant="outline" className="text-xs">
                  {section.generation_metadata.tokens} tokens
                </Badge>
              )}
            </div>
          </div>
        </AccordionTrigger>
        
        <AccordionContent className="px-6 pb-4">
          <Tabs defaultValue="prompt" className="mt-2">
            <TabsList className="mb-4">
              <TabsTrigger value="prompt">
                <MessageSquare className="mr-2 h-4 w-4" />
                Prompt
              </TabsTrigger>
              {section.generated_content && (
                <TabsTrigger value="content">
                  <FileText className="mr-2 h-4 w-4" />
                  Generated Content
                </TabsTrigger>
              )}
              {section.generation_metadata && Object.keys(section.generation_metadata).length > 0 && (
                <TabsTrigger value="metadata">
                  <FileText className="mr-2 h-4 w-4" />
                  Generation Details
                </TabsTrigger>
              )}
            </TabsList>
            
            {/* Prompt Tab */}
            <TabsContent value="prompt" className="mt-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">AI Prompt Used</h4>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(section.prompt_text, 'prompt')}
                      className="flex items-center gap-2"
                    >
                      <Copy className="h-3 w-3" />
                      Copy Prompt
                    </Button>
                    {onRegenerateSection && section.status === 'completed' && (
                      <Button
                        size="sm"
                        onClick={handleRegenerate}
                        disabled={isRegenerating}
                        className="flex items-center gap-2"
                      >
                        <RefreshCw className={`h-3 w-3 ${isRegenerating ? 'animate-spin' : ''}`} />
                        Regenerate
                      </Button>
                    )}
                  </div>
                </div>
                <ScrollArea className="h-[300px] w-full">
                  <div className="text-sm text-gray-700 whitespace-pre-wrap bg-gray-50 p-4 rounded-md border font-mono">
                    {section.prompt_text}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
            
            {/* Generated Content Tab */}
            {section.generated_content && (
              <TabsContent value="content" className="mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">Generated Content</h4>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(section.generated_content!, 'content')}
                      className="flex items-center gap-2"
                    >
                      <Copy className="h-3 w-3" />
                      Copy Content
                    </Button>
                  </div>
                  <ScrollArea className="h-[400px] w-full">
                    <div className="text-sm text-gray-700 whitespace-pre-wrap bg-white p-4 rounded-md border">
                      {section.generated_content}
                    </div>
                  </ScrollArea>
                </div>
              </TabsContent>
            )}
            
            {/* Generation Metadata Tab */}
            {section.generation_metadata && Object.keys(section.generation_metadata).length > 0 && (
              <TabsContent value="metadata" className="mt-0">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Generation Details</h4>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(section.generation_metadata).map(([key, value]) => (
                      <div key={key} className="bg-gray-50 p-3 rounded-md">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                        <div className="text-sm text-gray-900 mt-1">
                          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {section.error_message && (
                    <div className="bg-red-50 border border-red-200 p-3 rounded-md">
                      <div className="text-xs font-medium text-red-500 uppercase tracking-wide">
                        Error Message
                      </div>
                      <div className="text-sm text-red-700 mt-1">
                        {section.error_message}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
