import { GoogleGenAI } from '@google/genai';
import {
  UploadedFile,
  DataAnalysisResult,
  ResearchAnalysisResult,
  StatisticalTestRecommendation,
  ResearchHypothesis,
  ResearchFinding,
  PowerAnalysisResult,
  DemographicAnalysis
} from '../types';
import { FileProcessingService } from './file-processing.service';

export class ResearchAnalysisService {
  private static ai: GoogleGenAI | null = null;

  /**
   * Initialize Gemini AI client
   */
  private static getAI(): GoogleGenAI {
    if (!this.ai) {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('Gemini API key is not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
      }
      this.ai = new GoogleGenAI({ apiKey });
    }
    return this.ai;
  }

  /**
   * Perform comprehensive research-oriented data analysis using sequential thinking
   */
  static async performResearchAnalysis(
    file: UploadedFile, 
    dataDescription: string = '',
    researchContext: {
      researchQuestion?: string;
      studyType?: string;
      researchField?: string;
      hypotheses?: string[];
    } = {}
  ): Promise<ResearchAnalysisResult> {
    try {
      console.log('Starting research analysis with sequential thinking approach...');

      // Step 1: Data Profiling and Research Context Detection
      const dataProfile = await this.analyzeDataProfile(file, dataDescription);
      
      // Step 2: Study Type Classification
      const studyType = await this.classifyStudyType(file, dataDescription, researchContext);
      
      // Step 3: Sample Analysis and Power Calculation
      const sampleAnalysis = await this.analyzeSample(file, studyType);
      
      // Step 4: Statistical Test Recommendations
      const statisticalTests = await this.recommendStatisticalTests(file, studyType, researchContext);
      
      // Step 5: Hypothesis Generation and Testing
      const hypotheses = await this.generateResearchHypotheses(file, researchContext, statisticalTests);
      
      // Step 6: Statistical Analysis and Findings
      const findings = await this.performStatisticalAnalysis(file, statisticalTests, hypotheses);
      
      // Step 7: Research Limitations and Future Directions
      const limitations = await this.identifyLimitations(file, studyType, sampleAnalysis);
      const futureResearch = await this.suggestFutureResearch(findings, limitations);
      
      // Step 8: Methodology Documentation
      const methodologyNotes = await this.generateMethodologyNotes(file, statisticalTests, studyType);

      return {
        studyType,
        sampleAnalysis,
        statisticalTests,
        hypotheses,
        findings,
        limitations,
        futureResearch,
        methodologyNotes
      };

    } catch (error) {
      console.error('Research analysis failed:', error);
      throw new Error(`Research analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Step 1: Analyze data profile for research characteristics
   */
  private static async analyzeDataProfile(file: UploadedFile, dataDescription: string) {
    const prompt = `
As a research methodology expert, analyze this dataset for research characteristics:

Dataset: ${file.name}
Rows: ${file.data.length}
Columns: ${file.headers.join(', ')}
Description: ${dataDescription}

Sample data (first 5 rows):
${file.data.slice(0, 5).map(row => file.headers.map((h, i) => `${h}: ${row[i]}`).join(', ')).join('\n')}

Analyze and identify:
1. Research variables (independent, dependent, confounding)
2. Data collection method indicators
3. Temporal aspects (cross-sectional vs longitudinal)
4. Measurement scales (nominal, ordinal, interval, ratio)
5. Research design indicators

Provide structured analysis focusing on research methodology aspects.
`;

    return await this.executeGeminiAnalysis(prompt);
  }

  /**
   * Step 2: Classify study type based on data characteristics
   */
  private static async classifyStudyType(
    file: UploadedFile, 
    dataDescription: string, 
    researchContext: any
  ): Promise<ResearchAnalysisResult['studyType']> {
    const prompt = `
As a research design expert, classify this study type:

Dataset characteristics:
- Variables: ${file.headers.join(', ')}
- Sample size: ${file.data.length}
- Description: ${dataDescription}
- Research context: ${JSON.stringify(researchContext)}

Based on the data structure and context, classify as:
1. experimental - controlled manipulation of variables
2. observational - no manipulation, observing natural phenomena
3. survey - questionnaire/interview based data collection
4. longitudinal - data collected over time
5. cross-sectional - data collected at single time point
6. mixed - combination of approaches

Return only the classification: experimental|observational|survey|longitudinal|cross-sectional|mixed
`;

    const result = await this.executeGeminiAnalysis(prompt);
    const classification = result.toLowerCase().trim();
    
    if (['experimental', 'observational', 'survey', 'longitudinal', 'cross-sectional', 'mixed'].includes(classification)) {
      return classification as ResearchAnalysisResult['studyType'];
    }
    
    return 'observational'; // Default fallback
  }

  /**
   * Step 3: Analyze sample characteristics and power
   */
  private static async analyzeSample(file: UploadedFile, studyType: string) {
    const sampleSize = file.data.length;
    
    // Basic sample adequacy assessment
    let adequacy: 'adequate' | 'marginal' | 'inadequate';
    if (sampleSize >= 100) adequacy = 'adequate';
    else if (sampleSize >= 30) adequacy = 'marginal';
    else adequacy = 'inadequate';

    // Power analysis (simplified)
    const powerAnalysis: PowerAnalysisResult = {
      estimatedPower: sampleSize >= 100 ? 0.8 : sampleSize >= 30 ? 0.6 : 0.4,
      recommendedSampleSize: Math.max(100, sampleSize * 1.5),
      effectSize: 0.5, // Medium effect size assumption
      significance: 0.05,
      notes: [
        `Current sample size: ${sampleSize}`,
        `Adequacy: ${adequacy}`,
        studyType === 'experimental' ? 'Consider randomization and control groups' : 'Observational study limitations noted'
      ]
    };

    return {
      sampleSize,
      adequacy,
      powerAnalysis
    };
  }

  /**
   * Step 4: Recommend appropriate statistical tests
   */
  private static async recommendStatisticalTests(
    file: UploadedFile, 
    studyType: string, 
    researchContext: any
  ): Promise<StatisticalTestRecommendation[]> {
    const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);
    
    const prompt = `
As a biostatistician, recommend appropriate statistical tests for this research:

Study type: ${studyType}
Variables and types: ${JSON.stringify(dataTypes)}
Sample size: ${file.data.length}
Research context: ${JSON.stringify(researchContext)}

For each recommended test, provide:
1. Test name
2. Purpose/research question it addresses
3. Key assumptions
4. Variables involved
5. Justification for selection
6. Priority level (high/medium/low)

Focus on tests appropriate for the data types and research design.
Return as JSON array of test recommendations.
`;

    const result = await this.executeGeminiAnalysis(prompt);
    
    // Parse and return structured recommendations
    try {
      return JSON.parse(result);
    } catch {
      // Fallback recommendations based on data types
      return this.generateFallbackTestRecommendations(dataTypes, studyType);
    }
  }

  /**
   * Execute Gemini analysis with error handling
   */
  private static async executeGeminiAnalysis(prompt: string): Promise<string> {
    try {
      const ai = this.getAI();
      
      const response = await ai.models.generateContent({
        model: 'gemini-2.0-flash-exp',
        config: {
          generationConfig: {
            maxOutputTokens: 2048,
            temperature: 0.3
          }
        },
        contents: [{
          role: 'user',
          parts: [{ text: prompt }]
        }]
      });

      return response.text || '';
    } catch (error) {
      console.error('Gemini analysis error:', error);
      throw error;
    }
  }

  /**
   * Generate fallback test recommendations
   */
  private static generateFallbackTestRecommendations(
    dataTypes: Record<string, string>, 
    studyType: string
  ): StatisticalTestRecommendation[] {
    const recommendations: StatisticalTestRecommendation[] = [];
    
    const numericVars = Object.entries(dataTypes).filter(([_, type]) => type === 'number');
    const categoricalVars = Object.entries(dataTypes).filter(([_, type]) => type === 'string');

    // Basic descriptive statistics
    recommendations.push({
      test: 'Descriptive Statistics',
      purpose: 'Summarize data characteristics',
      assumptions: ['Valid data entry', 'Appropriate measurement scale'],
      variables: Object.keys(dataTypes),
      justification: 'Essential first step in any analysis',
      priority: 'high'
    });

    // Correlation analysis for numeric variables
    if (numericVars.length >= 2) {
      recommendations.push({
        test: 'Pearson Correlation',
        purpose: 'Examine relationships between continuous variables',
        assumptions: ['Linear relationship', 'Normal distribution', 'Homoscedasticity'],
        variables: numericVars.map(([name]) => name),
        justification: 'Multiple numeric variables present',
        priority: 'high'
      });
    }

    return recommendations;
  }

  /**
   * Step 5: Generate research hypotheses based on data and context
   */
  private static async generateResearchHypotheses(
    file: UploadedFile,
    researchContext: any,
    statisticalTests: StatisticalTestRecommendation[]
  ): Promise<ResearchHypothesis[]> {
    const prompt = `
As a research methodology expert, generate testable hypotheses for this study:

Variables: ${file.headers.join(', ')}
Research context: ${JSON.stringify(researchContext)}
Recommended tests: ${statisticalTests.map(t => t.test).join(', ')}

Generate 3-5 research hypotheses that are:
1. Testable with available data
2. Specific and measurable
3. Theoretically grounded
4. Include both null and alternative forms

For each hypothesis, specify:
- Type (null/alternative/directional/non-directional)
- Statement
- Variables involved
- Testability assessment

Return as JSON array.
`;

    try {
      const result = await this.executeGeminiAnalysis(prompt);
      return JSON.parse(result);
    } catch {
      return this.generateFallbackHypotheses(file, researchContext);
    }
  }

  /**
   * Step 6: Perform statistical analysis and generate findings
   */
  private static async performStatisticalAnalysis(
    file: UploadedFile,
    statisticalTests: StatisticalTestRecommendation[],
    hypotheses: ResearchHypothesis[]
  ): Promise<ResearchFinding[]> {
    const findings: ResearchFinding[] = [];

    // Calculate basic statistics for numeric variables
    const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);
    const basicStats = FileProcessingService.calculateBasicStats(file.data, file.headers, dataTypes);

    // Generate findings based on statistical tests
    for (const test of statisticalTests.filter(t => t.priority === 'high')) {
      const finding = await this.generateFindingForTest(test, file, basicStats);
      if (finding) findings.push(finding);
    }

    return findings;
  }

  /**
   * Step 7: Identify study limitations
   */
  private static async identifyLimitations(
    file: UploadedFile,
    studyType: string,
    sampleAnalysis: any
  ): Promise<string[]> {
    const limitations: string[] = [];

    // Sample size limitations
    if (sampleAnalysis.adequacy === 'inadequate') {
      limitations.push(`Small sample size (n=${sampleAnalysis.sampleSize}) may limit generalizability and statistical power.`);
    }

    // Study design limitations
    if (studyType === 'observational') {
      limitations.push('Observational design limits causal inference; associations do not imply causation.');
    }

    if (studyType === 'cross-sectional') {
      limitations.push('Cross-sectional design prevents assessment of temporal relationships and causality.');
    }

    // Data quality limitations
    const missingDataCount = file.data.reduce((count, row) => {
      return count + row.filter(cell => cell == null || cell === '').length;
    }, 0);

    if (missingDataCount > 0) {
      limitations.push(`Missing data points (${missingDataCount}) may introduce bias in results.`);
    }

    return limitations;
  }

  /**
   * Step 8: Suggest future research directions
   */
  private static async suggestFutureResearch(
    findings: ResearchFinding[],
    limitations: string[]
  ): Promise<string[]> {
    const suggestions: string[] = [];

    // Based on significant findings
    const significantFindings = findings.filter(f => f.type === 'significant');
    if (significantFindings.length > 0) {
      suggestions.push('Replicate findings in larger, more diverse samples to confirm generalizability.');
      suggestions.push('Investigate underlying mechanisms through experimental or longitudinal designs.');
    }

    // Based on limitations
    if (limitations.some(l => l.includes('sample size'))) {
      suggestions.push('Conduct power analysis and recruit larger sample for more robust statistical conclusions.');
    }

    if (limitations.some(l => l.includes('cross-sectional'))) {
      suggestions.push('Implement longitudinal design to establish temporal relationships and causality.');
    }

    return suggestions;
  }

  /**
   * Generate methodology notes for research documentation
   */
  private static async generateMethodologyNotes(
    file: UploadedFile,
    statisticalTests: StatisticalTestRecommendation[],
    studyType: string
  ): Promise<string[]> {
    const notes: string[] = [];

    notes.push(`Study design: ${studyType} with n=${file.data.length} participants/observations.`);
    notes.push(`Data analysis conducted using ${statisticalTests.length} statistical procedures.`);
    notes.push(`Variables analyzed: ${file.headers.join(', ')}.`);

    const highPriorityTests = statisticalTests.filter(t => t.priority === 'high');
    if (highPriorityTests.length > 0) {
      notes.push(`Primary analyses: ${highPriorityTests.map(t => t.test).join(', ')}.`);
    }

    notes.push('Statistical significance set at α = 0.05 for all analyses.');
    notes.push('Effect sizes calculated where appropriate to assess practical significance.');

    return notes;
  }

  /**
   * Helper methods
   */
  private static generateFallbackHypotheses(file: UploadedFile, researchContext: any): ResearchHypothesis[] {
    return [{
      type: 'null',
      statement: 'There are no significant relationships between the measured variables.',
      variables: file.headers.slice(0, 2),
      testable: true
    }];
  }

  private static async generateFindingForTest(
    test: StatisticalTestRecommendation,
    file: UploadedFile,
    basicStats: any
  ): Promise<ResearchFinding | null> {
    // Simplified finding generation based on test type
    if (test.test.includes('Descriptive')) {
      return {
        type: 'descriptive',
        description: `Descriptive analysis of ${test.variables.length} variables completed.`,
        statisticalDetails: {},
        interpretation: 'Basic data characteristics documented for further analysis.',
        implications: ['Provides foundation for inferential statistics'],
        figureReference: 'Table 1'
      };
    }

    return null;
  }
}
