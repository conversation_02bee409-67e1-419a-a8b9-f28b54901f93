/**
 * Deep Research Demo Component
 * Demonstrates the Deep Research functionality with example usage
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  Search, 
  DollarSign, 
  Clock, 
  Zap,
  FileText,
  Download,
  Play
} from 'lucide-react';

import { GoogleSearchInterface } from './components/GoogleSearchInterface';
import { 
  deepResearchCostService,
  googleSearchService 
} from './services';

export function DeepResearchDemo() {
  const [activeDemo, setActiveDemo] = useState<'interface' | 'cost' | 'service'>('interface');
  const [costEstimate, setCostEstimate] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const demoQueries = [
    'Impact of artificial intelligence on healthcare',
    'Climate change effects on global agriculture',
    'Blockchain technology in financial services',
    'Remote work trends and productivity',
    'Renewable energy adoption strategies'
  ];

  const handleCostDemo = async (query: string) => {
    setIsLoading(true);
    try {
      const options = {
        maxSubtopics: 6,
        wordsPerSection: 700,
        researchDepth: 'advanced' as const,
        includeExecutiveSummary: true,
        citationStyle: 'apa' as const,
        targetAudience: 'professional' as const,
        allowOutlineEditing: true
      };

      const estimate = deepResearchCostService.estimateCost(query, options);
      setCostEstimate(estimate);
    } catch (error) {
      console.error('Cost estimation failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOutlineDemo = async (query: string) => {
    setIsLoading(true);
    try {
      const options = {
        maxSubtopics: 5,
        researchDepth: 'advanced' as const,
        targetAudience: 'professional' as const,
        wordsPerSection: 700
      };

      const outline = await googleSearchService.generateResearchOutline(query, options);
      console.log('Generated outline:', outline);
      alert(`Outline generated! Check console for details. Main topic: ${outline.mainTopic}`);
    } catch (error) {
      console.error('Outline generation failed:', error);
      alert('Outline generation failed. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">Deep Research Demo</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Experience the power of AI-driven comprehensive research reports. 
          Generate structured, multi-section reports with proper citations and references.
        </p>
        <div className="flex justify-center space-x-4">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <BookOpen className="h-3 w-3 mr-1" />
            3000-6000 Words
          </Badge>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <Search className="h-3 w-3 mr-1" />
            5-7 Sections
          </Badge>
          <Badge variant="secondary" className="bg-purple-100 text-purple-800">
            <FileText className="h-3 w-3 mr-1" />
            Academic Citations
          </Badge>
        </div>
      </div>

      {/* Demo Tabs */}
      <Tabs value={activeDemo} onValueChange={(value: any) => setActiveDemo(value)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="interface">Full Interface</TabsTrigger>
          <TabsTrigger value="cost">Cost Estimation</TabsTrigger>
          <TabsTrigger value="service">Service Demo</TabsTrigger>
        </TabsList>

        {/* Full Interface Demo */}
        <TabsContent value="interface" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="h-5 w-5" />
                <span>Complete Deep Research Interface</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Try the full Deep Research experience. Toggle to "Deep Research" mode and enter a research topic.
              </p>
              <div className="h-96 border rounded-lg overflow-hidden">
                <GoogleSearchInterface userId="demo-user" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Cost Estimation Demo */}
        <TabsContent value="cost" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Cost Estimation Demo</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                See how much a Deep Research report would cost before generating it.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Try these research topics:</h4>
                  <div className="space-y-2">
                    {demoQueries.map((query, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleCostDemo(query)}
                        disabled={isLoading}
                        className="w-full text-left justify-start"
                      >
                        <Play className="h-3 w-3 mr-2" />
                        {query}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  {costEstimate ? (
                    <div className="space-y-4">
                      <h4 className="font-medium">Cost Estimate Results:</h4>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <DollarSign className="h-5 w-5 text-green-600 mx-auto mb-1" />
                          <div className="text-lg font-bold text-green-600">
                            ${costEstimate.estimatedCost.toFixed(4)}
                          </div>
                          <div className="text-xs text-gray-600">Estimated Cost</div>
                        </div>
                        
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <Clock className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                          <div className="text-lg font-bold text-blue-600">
                            {Math.floor(costEstimate.estimatedTime / 60)}m {costEstimate.estimatedTime % 60}s
                          </div>
                          <div className="text-xs text-gray-600">Estimated Time</div>
                        </div>
                        
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <Zap className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                          <div className="text-lg font-bold text-purple-600">
                            {costEstimate.totalTokens.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-600">Total Tokens</div>
                        </div>
                        
                        <div className="text-center p-3 bg-orange-50 rounded-lg">
                          <FileText className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                          <div className="text-lg font-bold text-orange-600">
                            {costEstimate.apiCalls}
                          </div>
                          <div className="text-xs text-gray-600">API Calls</div>
                        </div>
                      </div>

                      <div className="text-xs text-gray-500 space-y-1">
                        <div>Breakdown:</div>
                        <div>• Outline: {costEstimate.breakdown.outlineGeneration.toLocaleString()} tokens</div>
                        <div>• Research: {costEstimate.breakdown.batchResearch.toLocaleString()} tokens</div>
                        <div>• Writing: {costEstimate.breakdown.sectionGeneration.toLocaleString()} tokens</div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <DollarSign className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Click a research topic to see cost estimate</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Service Demo */}
        <TabsContent value="service" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>Service API Demo</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Test individual Deep Research service methods. Check browser console for detailed results.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Outline Generation:</h4>
                  <div className="space-y-2">
                    {demoQueries.slice(0, 3).map((query, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleOutlineDemo(query)}
                        disabled={isLoading}
                        className="w-full text-left justify-start"
                      >
                        <BookOpen className="h-3 w-3 mr-2" />
                        Generate Outline
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Code Examples:</h4>
                  <div className="bg-gray-50 p-3 rounded-lg text-xs font-mono">
                    <div className="text-gray-600">// Generate outline</div>
                    <div>const outline = await googleSearchService</div>
                    <div>.generateResearchOutline(query, options);</div>
                    <br />
                    <div className="text-gray-600">// Batch research</div>
                    <div>const data = await googleSearchService</div>
                    <div>.batchResearchSubtopics(subtopics);</div>
                    <br />
                    <div className="text-gray-600">// Generate section</div>
                    <div>const section = await googleSearchService</div>
                    <div>.generateResearchSection(subtopic, data);</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <span>Structured Research</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              AI-generated outlines with 5-7 subtopics, editable structure, and logical flow for comprehensive coverage.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-purple-600" />
              <span>Efficient Processing</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Batch research using Gemini's 1M token context, followed by sequential writing for optimal coherence.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Download className="h-5 w-5 text-green-600" />
              <span>Professional Output</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Export to PDF, Word, or Markdown with proper citations, references, and academic formatting.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
