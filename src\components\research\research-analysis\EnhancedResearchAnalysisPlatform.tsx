import React, { useState, use<PERSON><PERSON>back, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  BookOpen,
  Target,
  Lightbulb,
  Network,
  Calendar,
  Database,
  Search,
  BarChart3,
  Brain,
  Settings,
  Download,
  RefreshCw,
  Plus,
  Filter,
  ArrowRight,
  Star,
  TrendingUp,
  Users,
  Clock,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info,
  Award,
  Sparkles,
  GraduationCap,
  Library,
  FlaskConical,
  Microscope,
  LineChart,
  PieChart,
  Activity,
  Layers,
  Grid,
  Eye,
  Archive,
  History,
  MessageSquare,
  Share2,
  Bookmark,
  Globe,
  Building,
  Tag,
  Hash,
  ExternalLink,
  FileCheck,
  AlertCircle,
  Quote,
  Cpu,
  Workflow,
  Telescope,
  Atom,
  Beaker,
  Calculator,
  ChartBar,
  Compass,
  Fingerprint,
  Gamepad2,
  Headphones,
  Image,
  Lightbulb as LightbulbIcon,
  Map,
  Music,
  Palette,
  Rocket,
  Smartphone,
  Stethoscope,
  Thermometer,
  Truck,
  Umbrella,
  Video,
  Wifi,
  Zap as ZapIcon
} from "lucide-react";

import { MultiDocumentUploader } from './components/MultiDocumentUploader';
import { BeautifulDocumentLibrary } from './components/BeautifulDocumentLibrary';
import { EnhancedAIQuestionBubble } from './components/EnhancedAIQuestionBubble';
import { LiteratureReviewGenerator } from './components/LiteratureReviewGenerator';
import { ResearchGapAnalyzer } from './components/ResearchGapAnalyzer';
import { HypothesisGenerator } from './components/HypothesisGenerator';
import { AIModelSelector } from './components/AIModelSelector';
import { ProgressTracker } from './components/ProgressTracker';
import { ResearchHistoryDashboard } from './components/ResearchHistoryDashboard';
import { AnalysisResultViewer } from './components/AnalysisResultViewer';

import { 
  ResearchAnalysisState, 
  ResearchDocument, 
  GenerationProgress,
  AIGenerationOptions,
  LiteratureReview,
  GapAnalysis,
  ResearchHypothesis,
  ResearchProject
} from './types';
import { researchAnalysisService } from './services/research-analysis.service';
import { researchDocumentStorageService } from './services/research-document-storage.service';
import { analysisResultsStorageService } from './services/analysis-results-storage.service';
import { researchFileStorageService } from './services/research-file-storage.service';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from "@/lib/utils";

interface EnhancedResearchAnalysisPlatformProps {
  className?: string;
}

export function EnhancedResearchAnalysisPlatform({ className }: EnhancedResearchAnalysisPlatformProps) {
  const { user, isLoading } = useAuth();

  const [state, setState] = useState<ResearchAnalysisState>({
    documents: [],
    selectedDocuments: [],
    currentView: 'dashboard',
    isProcessing: false,
    processingMessage: '',
    aiSettings: {
      selectedModel: 'google/gemini-2.5-flash',
      options: {
        temperature: 0.3,
        maxTokens: 4000,
        topP: 0.9
      }
    },
    filters: {
      years: [2020, 2025],
      authors: [],
      journals: [],
      tags: [],
      categories: []
    },
    searchQuery: '',
    searchResults: []
  });

  const [analysisResults, setAnalysisResults] = useState({
    literatureReviews: [] as LiteratureReview[],
    gapAnalyses: [] as GapAnalysis[],
    hypotheses: [] as ResearchHypothesis[],
    projects: [] as ResearchProject[]
  });

  const [processingProgress, setProcessingProgress] = useState<GenerationProgress | null>(null);
  const [recentActivity, setRecentActivity] = useState<Array<{
    id: string;
    type: 'upload' | 'analysis' | 'generation' | 'export';
    message: string;
    timestamp: Date;
    status: 'success' | 'error' | 'processing';
  }>>([]);

  // New state for history and analysis viewing
  const [selectedAnalysisResult, setSelectedAnalysisResult] = useState<any>(null);
  const [selectedAnalysisType, setSelectedAnalysisType] = useState<'literature_review' | 'gap_analysis' | 'hypothesis' | null>(null);
  const [showHistoryDashboard, setShowHistoryDashboard] = useState(false);

  // Session management state
  const [currentSessionId, setCurrentSessionId] = useState<string>(() => crypto.randomUUID());
  const [sessionName, setSessionName] = useState<string>(() => `Research Session - ${new Date().toLocaleDateString()}`);



  // Computed values
  const selectedDocuments = useMemo(() => 
    state.documents.filter(doc => state.selectedDocuments.includes(doc.id)),
    [state.documents, state.selectedDocuments]
  );

  const filteredDocuments = useMemo(() => {
    let filtered = state.documents;
    
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase();
      filtered = filtered.filter(doc => 
        doc.title.toLowerCase().includes(query) ||
        doc.authors.some(author => author.toLowerCase().includes(query)) ||
        doc.keywords.some(keyword => keyword.toLowerCase().includes(query))
      );
    }
    
    return filtered;
  }, [state.documents, state.searchQuery]);

  const libraryStats = useMemo(() => {
    const total = state.documents.length;
    const ready = state.documents.filter(d => d.status === 'ready').length;
    const processing = state.documents.filter(d => d.status === 'processing').length;
    const errors = state.documents.filter(d => d.status === 'error').length;
    const selected = state.selectedDocuments.length;
    const withAnalysis = state.documents.filter(d => d.keyFindings.length > 0).length;
    
    return { total, ready, processing, errors, selected, withAnalysis };
  }, [state.documents, state.selectedDocuments]);

  // Add activity log
  const addActivity = useCallback((activity: {
    type: 'upload' | 'analysis' | 'generation' | 'export';
    message: string;
    status: 'success' | 'error' | 'processing';
  }) => {
    setRecentActivity(prev => [{
      id: Date.now().toString(),
      timestamp: new Date(),
      ...activity
    }, ...prev.slice(0, 9)]); // Keep only last 10 activities
  }, []);

  // Load existing documents when user is authenticated
  useEffect(() => {
    const loadExistingDocuments = async () => {
      if (!user || isLoading) return;

      try {
        console.log('🔄 Loading existing documents for user:', user.id);
        const { data: existingDocs, error } = await researchDocumentStorageService.getUserResearchDocuments(100);

        if (error) {
          console.error('❌ Error loading existing documents:', error);
          return;
        }

        if (existingDocs && existingDocs.length > 0) {
          console.log('✅ Loaded existing documents:', existingDocs.length);
          setState(prev => ({
            ...prev,
            documents: existingDocs
          }));

          addActivity({
            type: 'upload',
            message: `Loaded ${existingDocs.length} existing documents`,
            status: 'success'
          });
        }

        // Also load existing analysis results
        const [reviewsResult, gapsResult, hypsResult] = await Promise.all([
          analysisResultsStorageService.getUserLiteratureReviews(20),
          analysisResultsStorageService.getUserGapAnalyses(20),
          analysisResultsStorageService.getUserResearchHypotheses(50)
        ]);

        const totalAnalyses = (reviewsResult.data?.length || 0) +
                             (gapsResult.data?.length || 0) +
                             (hypsResult.data?.length || 0);

        if (totalAnalyses > 0) {
          setAnalysisResults({
            literatureReviews: reviewsResult.data || [],
            gapAnalyses: gapsResult.data || [],
            hypotheses: hypsResult.data || [],
            projects: []
          });

          addActivity({
            type: 'analysis',
            message: `Loaded ${totalAnalyses} existing analysis results`,
            status: 'success'
          });
        }
      } catch (error) {
        console.error('❌ Error loading existing documents:', error);
      }
    };

    loadExistingDocuments();
  }, [user, isLoading, addActivity]);

  // Start a new research session
  const startNewSession = useCallback(() => {
    const newSessionId = crypto.randomUUID();
    const newSessionName = `Research Session - ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;

    setCurrentSessionId(newSessionId);
    setSessionName(newSessionName);

    // Clear current session data but keep it in history
    setState(prev => ({
      ...prev,
      documents: [],
      isProcessing: false,
      processingMessage: ''
    }));

    setAnalysisResults({
      literatureReviews: [],
      gapAnalyses: [],
      hypotheses: [],
      projects: []
    });

    setRecentActivity([]);
    setProcessingProgress(null);

    addActivity({
      type: 'generation',
      message: `Started new research session: ${newSessionName}`,
      status: 'success'
    });

    toast.success(`New research session started: ${newSessionName}`);
  }, [addActivity]);

  // Continue session from history
  const handleContinueSession = useCallback((documents: ResearchDocument[], reviews: LiteratureReview[], analyses: GapAnalysis[]) => {
    // Load documents into current session
    setState(prev => ({
      ...prev,
      documents: documents
    }));

    // Load analysis results into current session
    setAnalysisResults(prev => ({
      ...prev,
      literatureReviews: reviews,
      gapAnalyses: analyses
    }));

    // Close history dashboard
    setShowHistoryDashboard(false);

    addActivity({
      type: 'generation',
      message: `Continued session with ${documents.length} documents, ${reviews.length} reviews, and ${analyses.length} analyses`,
      status: 'success'
    });
  }, [addActivity]);

  // Handle document upload with enhanced progress tracking and database storage
  const handleDocumentUpload = useCallback(async (files: File[]) => {
    setState(prev => ({
      ...prev,
      isProcessing: true,
      processingMessage: 'Processing documents...'
    }));

    addActivity({
      type: 'upload',
      message: `Uploading ${files.length} document(s)`,
      status: 'processing'
    });

    try {
      const documents = await researchAnalysisService.processDocuments(
        files,
        (progress) => {
          setProcessingProgress(progress);
          setState(prev => ({
            ...prev,
            processingMessage: progress.message
          }));
        }
      );

      // Save successfully processed documents to database
      const savedDocuments = [];
      for (let i = 0; i < documents.length; i++) {
        const document = documents[i];
        const file = files[i];

        if (document.status === 'ready') {
          try {
            const { data: savedDoc, error: saveError } = await researchDocumentStorageService.saveResearchDocument(
              document,
              file
            );

            if (saveError) {
              console.error('Error saving document to database:', saveError);
              console.error('Save error details:', saveError.message || saveError);
              // Continue with local storage - don't block the user experience
              savedDocuments.push(document);

              // Show specific error message
              if (saveError.message?.includes('not authenticated')) {
                toast.error(`Authentication required to save: ${document.title}`);
              } else if (saveError.message?.includes('storage')) {
                toast.warning(`Document processed but file not uploaded: ${document.title}`);
              } else {
                toast.warning(`Document processed but not saved to database: ${document.title}`);
              }
            } else {
              console.log('Document saved to database:', savedDoc);
              savedDocuments.push(document);
              toast.success(`Document saved: ${document.title}`);
            }
          } catch (saveError) {
            console.error('Error saving document:', saveError);
            // Continue with local storage
            savedDocuments.push(document);
          }
        } else {
          // Add error documents to local state
          savedDocuments.push(document);
        }
      }

      setState(prev => ({
        ...prev,
        documents: [...prev.documents, ...savedDocuments],
        isProcessing: false,
        processingMessage: ''
      }));

      setProcessingProgress(null);

      const successCount = savedDocuments.filter(d => d.status === 'ready').length;
      const errorCount = savedDocuments.filter(d => d.status === 'error').length;

      addActivity({
        type: 'upload',
        message: `Successfully processed ${successCount} documents${errorCount > 0 ? `, ${errorCount} failed` : ''}`,
        status: successCount > 0 ? 'success' : 'error'
      });

      toast.success(`Successfully processed ${successCount} documents`);

      if (errorCount > 0) {
        toast.error(`${errorCount} documents failed to process`);
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingMessage: ''
      }));

      setProcessingProgress(null);

      addActivity({
        type: 'upload',
        message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        status: 'error'
      });

      toast.error('Failed to process documents');
      console.error('Document upload error:', error);
    }
  }, [addActivity]);

  // Handle document selection
  const handleDocumentSelection = useCallback((documentIds: string[]) => {
    setState(prev => ({ ...prev, selectedDocuments: documentIds }));
  }, []);

  // Handle view changes
  const handleViewChange = useCallback((view: string) => {
    setState(prev => ({ ...prev, currentView: view as ResearchAnalysisState['currentView'] }));
  }, []);

  // Handle literature review generation with database storage
  const handleLiteratureReviewGenerated = useCallback(async (review: LiteratureReview) => {
    try {
      // Save to database
      const { data: savedReview, error: saveError } = await analysisResultsStorageService.saveLiteratureReview(review);

      if (saveError) {
        console.error('Error saving literature review:', saveError);
        toast.error('Literature review generated but failed to save to database');
      } else {
        console.log('Literature review saved to database:', savedReview);
        toast.success('Literature review generated and saved!');
      }

      // Update local state (check for duplicates)
      setAnalysisResults(prev => {
        const existingReview = prev.literatureReviews.find(r => r.id === review.id);
        if (existingReview) {
          console.log('📚 Literature review already in local state:', review.id);
          return prev;
        }
        return {
          ...prev,
          literatureReviews: [...prev.literatureReviews, review]
        };
      });

      addActivity({
        type: 'generation',
        message: `Generated literature review: "${review.title}"`,
        status: 'success'
      });
    } catch (error) {
      console.error('Error handling literature review:', error);
      toast.error('Failed to save literature review');
    }
  }, [addActivity]);

  // Handle gap analysis with database storage
  const handleGapAnalysisCompleted = useCallback(async (analysis: GapAnalysis) => {
    try {
      // Save to database
      const { data: savedAnalysis, error: saveError } = await analysisResultsStorageService.saveGapAnalysis(analysis);

      if (saveError) {
        console.error('Error saving gap analysis:', saveError);
        toast.error('Gap analysis completed but failed to save to database');
      } else {
        console.log('Gap analysis saved to database:', savedAnalysis);
        toast.success('Research gap analysis completed and saved!');
      }

      // Update local state (check for duplicates)
      setAnalysisResults(prev => {
        const existingAnalysis = prev.gapAnalyses.find(a => a.id === analysis.id);
        if (existingAnalysis) {
          console.log('🎯 Gap analysis already in local state:', analysis.id);
          return prev;
        }
        return {
          ...prev,
          gapAnalyses: [...prev.gapAnalyses, analysis]
        };
      });

      addActivity({
        type: 'analysis',
        message: `Completed gap analysis with ${analysis.gaps.length} gaps identified`,
        status: 'success'
      });
    } catch (error) {
      console.error('Error handling gap analysis:', error);
      toast.error('Failed to save gap analysis');
    }
  }, [addActivity]);

  // Handle hypothesis generation with database storage
  const handleHypothesesGenerated = useCallback(async (hypotheses: ResearchHypothesis[]) => {
    try {
      // Save to database
      const { data: savedHypotheses, error: saveError } = await analysisResultsStorageService.saveResearchHypotheses(hypotheses);

      if (saveError) {
        console.error('Error saving hypotheses:', saveError);
        toast.error('Hypotheses generated but failed to save to database');
      } else {
        console.log('Hypotheses saved to database:', savedHypotheses);
        toast.success(`Generated and saved ${hypotheses.length} hypotheses!`);
      }

      // Update local state
      setAnalysisResults(prev => ({
        ...prev,
        hypotheses: [...prev.hypotheses, ...hypotheses]
      }));

      addActivity({
        type: 'generation',
        message: `Generated ${hypotheses.length} research hypotheses`,
        status: 'success'
      });
    } catch (error) {
      console.error('Error handling hypotheses:', error);
      toast.error('Failed to save hypotheses');
    }
  }, [addActivity]);

  // Handle analysis result selection from history
  const handleAnalysisSelect = useCallback((analysis: any) => {
    // Determine the type of analysis
    if (analysis.sections && analysis.citationStyle) {
      setSelectedAnalysisType('literature_review');
    } else if (analysis.gaps && analysis.themes) {
      setSelectedAnalysisType('gap_analysis');
    } else if (analysis.statement && analysis.variables) {
      setSelectedAnalysisType('hypothesis');
    }

    setSelectedAnalysisResult(analysis);
  }, []);

  // Handle document selection from history
  const handleDocumentSelect = useCallback((document: ResearchDocument) => {
    // Add to current documents if not already present
    setState(prev => {
      const exists = prev.documents.some(doc => doc.id === document.id);
      if (!exists) {
        return {
          ...prev,
          documents: [...prev.documents, document],
          currentView: 'library'
        };
      }
      return { ...prev, currentView: 'library' };
    });

    setShowHistoryDashboard(false);
    toast.success('Document loaded from history');
  }, []);

  // Enhanced dashboard rendering
  const renderEnhancedDashboard = () => {
    const stats = libraryStats;
    
    return (
      <div className="space-y-8">
        {/* Welcome Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <Microscope className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Research Analysis Platform
              </h1>
              <p className="text-xl text-gray-600">AI-Powered Academic Research Assistant</p>
            </div>
          </div>
          <p className="text-gray-500 max-w-2xl mx-auto">
            Upload research papers, analyze literature, identify gaps, and generate insights with advanced AI assistance
          </p>
        </div>

        {/* Session Management */}
        <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <Layers className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-purple-900">Current Session</h3>
                  <p className="text-sm text-purple-700">{sessionName}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistoryDashboard(true)}
                  className="border-purple-300 text-purple-700 hover:bg-purple-50"
                >
                  <History className="h-4 w-4 mr-2" />
                  View History
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={startNewSession}
                  className="border-indigo-300 text-indigo-700 hover:bg-indigo-50"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Session
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{stats.total}</div>
              <div className="text-sm text-blue-800 font-medium">Total Documents</div>
              <div className="text-xs text-blue-600 mt-1">
                <Database className="h-3 w-3 inline mr-1" />
                Library size
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{stats.ready}</div>
              <div className="text-sm text-green-800 font-medium">Ready</div>
              <div className="text-xs text-green-600 mt-1">
                <CheckCircle className="h-3 w-3 inline mr-1" />
                Analyzed
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">{stats.processing}</div>
              <div className="text-sm text-orange-800 font-medium">Processing</div>
              <div className="text-xs text-orange-600 mt-1">
                <RefreshCw className="h-3 w-3 inline mr-1" />
                In progress
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">{stats.errors}</div>
              <div className="text-sm text-red-800 font-medium">Errors</div>
              <div className="text-xs text-red-600 mt-1">
                <AlertTriangle className="h-3 w-3 inline mr-1" />
                Failed
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{stats.selected}</div>
              <div className="text-sm text-purple-800 font-medium">Selected</div>
              <div className="text-xs text-purple-600 mt-1">
                <CheckCircle className="h-3 w-3 inline mr-1" />
                Active
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-indigo-50 to-indigo-100 border-indigo-200">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-indigo-600 mb-2">{stats.withAnalysis}</div>
              <div className="text-sm text-indigo-800 font-medium">With Analysis</div>
              <div className="text-xs text-indigo-600 mt-1">
                <Brain className="h-3 w-3 inline mr-1" />
                AI processed
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="border-2 border-dashed border-gray-300 bg-gradient-to-r from-gray-50 to-gray-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-24 flex-col gap-2 bg-white hover:bg-green-50 hover:border-green-300"
                onClick={() => handleViewChange('upload')}
              >
                <Upload className="h-6 w-6 text-green-500" />
                <div className="text-center">
                  <div className="font-medium">Upload Documents</div>
                  <div className="text-xs text-gray-500">Add new papers</div>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-24 flex-col gap-2 bg-white hover:bg-indigo-50 hover:border-indigo-300"
                onClick={() => handleViewChange('library')}
                disabled={libraryStats.total === 0}
              >
                <BookOpen className="h-6 w-6 text-indigo-500" />
                <div className="text-center">
                  <div className="font-medium">Research Library</div>
                  <div className="text-xs text-gray-500">Explore documents</div>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-24 flex-col gap-2 bg-white hover:bg-purple-50 hover:border-purple-300"
                onClick={() => handleViewChange('literature-review')}
                disabled={libraryStats.ready === 0}
              >
                <FileText className="h-6 w-6 text-purple-500" />
                <div className="text-center">
                  <div className="font-medium">Literature Review</div>
                  <div className="text-xs text-gray-500">Generate reviews</div>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-24 flex-col gap-2 bg-white hover:bg-orange-50 hover:border-orange-300"
                onClick={() => handleViewChange('gaps')}
                disabled={libraryStats.ready === 0}
              >
                <Target className="h-6 w-6 text-orange-500" />
                <div className="text-center">
                  <div className="font-medium">Find Gaps</div>
                  <div className="text-xs text-gray-500">Identify opportunities</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-500" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                {recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No recent activity</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                        <div className={cn(
                          "w-2 h-2 rounded-full mt-2 flex-shrink-0",
                          activity.status === 'success' ? 'bg-green-500' :
                          activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                        )} />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                          <p className="text-xs text-gray-500">
                            {activity.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-500" />
                Analysis Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Literature Reviews</span>
                  <Badge variant="secondary">{analysisResults.literatureReviews.length}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Gap Analyses</span>
                  <Badge variant="secondary">{analysisResults.gapAnalyses.length}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Hypotheses Generated</span>
                  <Badge variant="secondary">{analysisResults.hypotheses.length}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Research Projects</span>
                  <Badge variant="secondary">{analysisResults.projects.length}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Processing Progress */}
        {processingProgress && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <RefreshCw className="h-6 w-6 text-blue-500 animate-spin" />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-900">{processingProgress.stage}</span>
                    <span className="text-sm text-blue-700">{Math.round(processingProgress.progress)}%</span>
                  </div>
                  <Progress value={processingProgress.progress} className="h-2" />
                  <p className="text-xs text-blue-600 mt-1">{processingProgress.message}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className={cn("min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center", className)}>
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading Research Analysis Platform...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show authentication prompt if not authenticated
  if (!user) {
    return (
      <div className={cn("min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center", className)}>
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
            <p className="text-gray-600 mb-4">
              Please sign in to access the Research Analysis Platform and save your work.
            </p>
            <Button
              onClick={() => window.location.href = '/auth'}
              className="w-full"
            >
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-gray-50 to-gray-100", className)}>
      <div className="container mx-auto p-6">
        {/* Global AI Model Selector Header */}
        <div className="mb-6">
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <Brain className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">AI Analysis Engine</h3>
                    <p className="text-sm text-gray-600">
                      Choose your preferred AI model for document analysis and research tasks
                      {user && (
                        <span className="ml-2 text-blue-600">
                          • Signed in as {user.email}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-700">Current Model</div>
                    <div className="text-xs text-gray-500">Used for all analysis tasks</div>
                  </div>
                  <AIModelSelector
                    selectedModel={state.aiSettings.selectedModel}
                    options={state.aiSettings.options}
                    onSettingsChange={(newSettings) => {
                      setState(prev => ({
                        ...prev,
                        aiSettings: newSettings
                      }));
                      toast.success(`AI model changed to ${newSettings.selectedModel.split('/')[1]}`);
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Navigation and Content */}
        <Tabs value={state.currentView} onValueChange={handleViewChange} className="w-full">
          <div className="mb-8">
            <TabsList className="grid w-full grid-cols-7 bg-white border border-gray-200 rounded-xl p-1">
              <TabsTrigger
                value="dashboard"
                className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger
                value="upload"
                className="flex items-center gap-2 data-[state=active]:bg-green-500 data-[state=active]:text-white"
              >
                <Upload className="h-4 w-4" />
                Upload
              </TabsTrigger>
              <TabsTrigger
                value="library"
                className="flex items-center gap-2 data-[state=active]:bg-indigo-500 data-[state=active]:text-white"
                disabled={libraryStats.total === 0}
              >
                <BookOpen className="h-4 w-4" />
                Library
                {libraryStats.total > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {libraryStats.total}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger
                value="literature-review"
                className="flex items-center gap-2 data-[state=active]:bg-purple-500 data-[state=active]:text-white"
                disabled={libraryStats.ready === 0}
              >
                <FileText className="h-4 w-4" />
                Literature Review
              </TabsTrigger>
              <TabsTrigger
                value="gaps"
                className="flex items-center gap-2 data-[state=active]:bg-orange-500 data-[state=active]:text-white"
                disabled={libraryStats.ready === 0}
              >
                <Target className="h-4 w-4" />
                Research Gaps
              </TabsTrigger>
              <TabsTrigger
                value="hypotheses"
                className="flex items-center gap-2 data-[state=active]:bg-pink-500 data-[state=active]:text-white"
                disabled={analysisResults.gapAnalyses.length === 0}
              >
                <Lightbulb className="h-4 w-4" />
                Hypotheses
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="flex items-center gap-2 data-[state=active]:bg-teal-500 data-[state=active]:text-white"
              >
                <Archive className="h-4 w-4" />
                History
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Content Area */}
          <div className="space-y-6">
            <TabsContent value="dashboard" className="mt-0">
              {renderEnhancedDashboard()}
            </TabsContent>
            
            <TabsContent value="upload" className="mt-0">
              <div className="max-w-4xl mx-auto">
                <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
                  <CardHeader className="text-center">
                    <div className="flex items-center justify-center gap-3 mb-4">
                      <div className="p-3 bg-green-500 rounded-xl">
                        <Upload className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-3xl font-bold text-green-900">Upload Research Documents</CardTitle>
                        <p className="text-green-700 mt-2">Add your research papers for AI-powered analysis</p>
                      </div>
                    </div>

                    {/* AI Model Indicator */}
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 rounded-full border border-green-300">
                      <Brain className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">
                        Analysis powered by {state.aiSettings.selectedModel.split('/')[1]?.replace('-', ' ') || 'AI'}
                      </span>
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      <div className="grid md:grid-cols-3 gap-6 text-center">
                        <div className="p-4 bg-white rounded-lg border border-green-200">
                          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <FileText className="h-6 w-6 text-green-600" />
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-2">Smart Processing</h3>
                          <p className="text-sm text-gray-600">AI extracts key information, findings, and methodology</p>
                        </div>
                        <div className="p-4 bg-white rounded-lg border border-green-200">
                          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <Brain className="h-6 w-6 text-green-600" />
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-2">Deep Analysis</h3>
                          <p className="text-sm text-gray-600">Comprehensive understanding of research content</p>
                        </div>
                        <div className="p-4 bg-white rounded-lg border border-green-200">
                          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <BookOpen className="h-6 w-6 text-green-600" />
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-2">Ready for Research</h3>
                          <p className="text-sm text-gray-600">Instantly available in your research library</p>
                        </div>
                      </div>
                      
                      <MultiDocumentUploader onUpload={handleDocumentUpload} />
                      
                      {libraryStats.total > 0 && (
                        <div className="text-center pt-6 border-t border-green-200">
                          <p className="text-green-700 mb-4">
                            Great! You have {libraryStats.total} document{libraryStats.total > 1 ? 's' : ''} uploaded.
                          </p>
                          <Button 
                            onClick={() => handleViewChange('library')}
                            className="bg-green-600 hover:bg-green-700 text-white"
                            size="lg"
                          >
                            <BookOpen className="h-5 w-5 mr-2" />
                            View Research Library
                            <ArrowRight className="h-5 w-5 ml-2" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="library" className="mt-0">
              <BeautifulDocumentLibrary 
                documents={filteredDocuments}
                selectedDocuments={state.selectedDocuments}
                onSelectionChange={handleDocumentSelection}
                onSearch={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
                onFilter={(filters) => setState(prev => ({ ...prev, filters }))}
              />
            </TabsContent>
            
            <TabsContent value="documents" className="mt-0">
              <div className="space-y-8">
                <div className="max-w-4xl mx-auto">
                  <MultiDocumentUploader onUpload={handleDocumentUpload} />
                </div>
                <BeautifulDocumentLibrary 
                  documents={filteredDocuments}
                  selectedDocuments={state.selectedDocuments}
                  onSelectionChange={handleDocumentSelection}
                  onSearch={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
                  onFilter={(filters) => setState(prev => ({ ...prev, filters }))}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="literature-review" className="mt-0">
              <LiteratureReviewGenerator
                documents={selectedDocuments}
                onGenerate={handleLiteratureReviewGenerated}
                aiSettings={state.aiSettings}
              />
            </TabsContent>

            <TabsContent value="gaps" className="mt-0">
              <ResearchGapAnalyzer
                documents={selectedDocuments}
                onAnalyze={handleGapAnalysisCompleted}
                aiSettings={state.aiSettings}
              />
            </TabsContent>

            <TabsContent value="hypotheses" className="mt-0">
              <HypothesisGenerator
                gaps={analysisResults.gapAnalyses.flatMap(a => a.gaps)}
                documents={selectedDocuments}
                onGenerate={handleHypothesesGenerated}
                aiSettings={state.aiSettings}
              />
            </TabsContent>

            <TabsContent value="history" className="mt-0">
              <ResearchHistoryDashboard
                onDocumentSelect={handleDocumentSelect}
                onAnalysisSelect={handleAnalysisSelect}
                onContinueSession={handleContinueSession}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Enhanced AI Assistant - Always available */}
      <EnhancedAIQuestionBubble
        documents={state.documents}
        selectedDocuments={state.selectedDocuments}
        aiSettings={state.aiSettings}
        onAISettingsChange={(newSettings) => {
          setState(prev => ({
            ...prev,
            aiSettings: newSettings
          }));
        }}
        onDocumentSelect={(documentId) => {
          const newSelection = state.selectedDocuments.includes(documentId)
            ? state.selectedDocuments.filter(id => id !== documentId)
            : [...state.selectedDocuments, documentId];
          setState(prev => ({ ...prev, selectedDocuments: newSelection }));
        }}
      />

      {/* Analysis Result Viewer */}
      <AnalysisResultViewer
        result={selectedAnalysisResult}
        type={selectedAnalysisType}
        onClose={() => {
          setSelectedAnalysisResult(null);
          setSelectedAnalysisType(null);
        }}
      />
    </div>
  );
}
