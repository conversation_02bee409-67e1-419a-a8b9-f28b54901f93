# Research Search Interface

A beautiful, modern research search interface that combines Tavily API for academic search with multiple AI models for comprehensive research assistance.

## 🚀 Quick Start

1. **Access the Interface**: Navigate to Research Search from the sidebar or AI Dashboard
2. **Start Searching**: Type your research question and press Enter
3. **Explore Results**: View AI responses with citations and expandable sources
4. **Continue Conversations**: Build on previous queries in the same session

## 🔧 Troubleshooting

### "Failed to initialize research search interface"

This error typically occurs due to authentication or database connection issues. Here's how to fix it:

#### Solution 1: Check Authentication
```javascript
// Open browser console and run:
window.testResearchSearch.runAllTests()
```

#### Solution 2: Verify Database Tables
The following tables should exist in your Supabase database:
- `research_search_sessions`
- `research_search_messages`
- `research_search_preferences`

#### Solution 3: Check API Keys
Ensure these environment variables are set:
- `VITE_OPENROUTER_API_KEY` - Your OpenRouter API key
- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key

#### Solution 4: Clear Browser Data
1. Open Developer Tools (F12)
2. Go to Application/Storage tab
3. Clear Local Storage and Session Storage
4. Refresh the page

### API Connection Issues

#### Tavily API Issues
- The Tavily API key is hardcoded: `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`
- If searches fail, check network connectivity
- Verify the API key hasn't expired

#### OpenRouter API Issues
- Check your OpenRouter API key in environment variables
- Ensure you have sufficient credits
- Try switching to a different AI model

### Performance Issues

#### Slow Search Responses
- Try switching to "basic" search depth in preferences
- Reduce the number of results (default: 10)
- Check your internet connection

#### Memory Issues
- Clear old search sessions from the sidebar
- Refresh the page periodically during long research sessions

## Features

### 🔍 **Academic Search**
- Powered by Tavily API for high-quality academic sources
- Real-time search with academic paper prioritization
- Source verification and relevance scoring
- Support for advanced search parameters

### 🤖 **AI-Powered Analysis**
- Multiple AI model support (Gemini 2.0 Flash, Claude Haiku, GPT-4o, etc.)
- Intelligent response generation with proper citations
- Context-aware conversations
- Research-focused prompting

### 💬 **Beautiful Chat Interface**
- Modern, responsive chat UI
- Message history with search context
- Source display with expandable details
- Citation management and linking
- Copy functionality for messages

### 📚 **Session Management**
- Persistent search sessions with Supabase
- Session history and organization
- Continue previous conversations
- Search statistics and analytics

### ⚙️ **User Preferences**
- Customizable AI model selection
- Search depth preferences (basic/advanced)
- Results count configuration
- UI preferences (show sources, citations, etc.)

## Components

### Main Components
- `ResearchSearchInterface` - Main chat interface
- `SearchMessage` - Individual message display
- `SearchInput` - Query input with suggestions
- `SearchHistory` - Session management sidebar
- `AIModelSelector` - Model selection component

### Services
- `TavilySearchService` - Academic search integration
- `ResearchAIService` - AI response generation
- `SearchHistoryService` - Session persistence

### Hooks & Store
- `useResearchSearch` - Main functionality hook
- `searchStore` - Zustand state management

## Database Schema

The feature uses the following Supabase tables:

```sql
-- Search sessions
research_search_sessions (
  id, user_id, title, model, total_queries, 
  is_active, session_metadata, created_at, updated_at
)

-- Individual messages
research_search_messages (
  id, session_id, type, content, search_query,
  sources, citations, message_metadata, created_at
)

-- User preferences
research_search_preferences (
  id, user_id, default_model, default_search_depth,
  max_results, auto_save, show_sources, show_citations,
  theme, preferences_metadata, created_at, updated_at
)
```

## Configuration

### Environment Variables
```env
VITE_OPENROUTER_API_KEY=your_openrouter_api_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Tavily API
The service uses the provided Tavily API key: `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`

## Usage

### Basic Usage
```tsx
import { ResearchSearchInterface } from '@/components/research/research-search';

function App() {
  return <ResearchSearchInterface />;
}
```

### With Custom Hook
```tsx
import { useResearchSearch } from '@/components/research/research-search/hooks/useResearchSearch';

function CustomSearchComponent() {
  const {
    currentSession,
    messages,
    isLoading,
    performSearch,
    createNewSession
  } = useResearchSearch();

  // Your custom implementation
}
```

## AI Models Supported

- **Gemini 2.0 Flash** - Fast, efficient (Default)
- **Claude 3 Haiku** - Balanced analysis
- **GPT-4o** - Advanced comprehension
- **Claude 3.5 Sonnet** - Premium analysis
- **Gemini 2.5 Flash Preview** - Latest features

## Search Features

### Academic Focus
- Prioritizes peer-reviewed sources
- Academic domain recognition
- Citation extraction and formatting
- Research methodology analysis

### Source Types
- Academic papers and journals
- News articles
- Books and publications
- Web sources with quality scoring

### Search Options
- Basic vs Advanced search depth
- Configurable result limits
- Domain filtering
- Date range filtering
- Image inclusion options

## Integration

The Research Search interface is integrated into the main research dashboard:

1. **Navigation**: Available in the sidebar as "Research Search"
2. **Dashboard**: Accessible from the AI Welcome Dashboard
3. **Routing**: Integrated with the existing ActiveView system

## Security & Privacy

- Row Level Security (RLS) enabled on all tables
- User-specific data isolation
- Secure API key management
- No sensitive data logging

## Performance

- Optimized with React hooks and Zustand store
- Efficient message rendering with virtualization
- Debounced search suggestions
- Cached user preferences

## Future Enhancements

- [ ] Export search results to various formats
- [ ] Advanced filtering and sorting
- [ ] Collaborative search sessions
- [ ] Integration with reference managers
- [ ] Voice search capabilities
- [ ] Mobile app support
