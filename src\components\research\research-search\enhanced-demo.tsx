/**
 * Enhanced Deep Research System Demo
 * Demonstrates the new academic research capabilities
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen, 
  FileText, 
  GraduationCap, 
  Search, 
  Brain,
  Target,
  CheckCircle,
  Clock,
  Users
} from 'lucide-react';

import { runEnhancedSystemTests } from './test-enhanced-system';
import { researchPlanningService } from './services/research-planning.service';
import { academicFormattingService } from './services/academic-formatting.service';

export function EnhancedResearchDemo() {
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const handleRunTests = async () => {
    setIsRunningTests(true);
    try {
      const results = await runEnhancedSystemTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test execution failed:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  const researchTypes = researchPlanningService.getResearchTypeTemplates();

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 flex items-center justify-center gap-3">
          <GraduationCap className="w-10 h-10 text-blue-600" />
          Enhanced Academic Research System
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive academic research with intelligent planning, dynamic outlines, 
          enhanced citations, and professional formatting for literature reviews, research papers, and academic books.
        </p>
      </div>

      {/* Key Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <Brain className="w-5 h-5" />
              Intelligent Planning
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-800">
            <ul className="space-y-2 text-sm">
              <li>• AI-powered research type analysis</li>
              <li>• Dynamic outline generation</li>
              <li>• Adaptive section planning</li>
              <li>• Word count optimization</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-900">
              <BookOpen className="w-5 h-5" />
              Enhanced Citations
            </CardTitle>
          </CardHeader>
          <CardContent className="text-green-800">
            <ul className="space-y-2 text-sm">
              <li>• Automatic citation extraction</li>
              <li>• Reference quality scoring</li>
              <li>• Consolidated reference lists</li>
              <li>• Academic source detection</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-900">
              <FileText className="w-5 h-5" />
              Professional Output
            </CardTitle>
          </CardHeader>
          <CardContent className="text-purple-800">
            <ul className="space-y-2 text-sm">
              <li>• Clean academic formatting</li>
              <li>• Word document export</li>
              <li>• Editor integration</li>
              <li>• Markdown-free output</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Research Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-600" />
            Available Research Types
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {researchTypes.map((type) => (
              <div key={type.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-8 h-8 rounded-lg bg-${type.color}-100 flex items-center justify-center`}>
                    <span className={`text-${type.color}-600 text-sm font-bold`}>
                      {type.name.charAt(0)}
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900">{type.name}</h3>
                </div>
                <p className="text-sm text-gray-600 mb-3">{type.description}</p>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Target Words:</span>
                    <span className="font-medium">{type.config.totalWordTarget.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Sections:</span>
                    <span className="font-medium">{type.config.minSections}-{type.config.maxSections}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Level:</span>
                    <Badge variant="outline" className="text-xs">
                      {type.config.academicLevel}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Citations:</span>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${
                        type.config.citationDensity === 'heavy' ? 'border-red-300 text-red-700' :
                        type.config.citationDensity === 'moderate' ? 'border-yellow-300 text-yellow-700' :
                        'border-green-300 text-green-700'
                      }`}
                    >
                      {type.config.citationDensity}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            System Validation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-600">
              Run comprehensive tests to validate all enhanced research system components.
            </p>
            
            <Button 
              onClick={handleRunTests}
              disabled={isRunningTests}
              className="flex items-center gap-2"
            >
              {isRunningTests ? (
                <>
                  <Clock className="w-4 h-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <Search className="w-4 h-4" />
                  Run System Tests
                </>
              )}
            </Button>

            {testResults && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Test Results:</h4>
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${testResults.success ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="font-medium">
                    {testResults.passed}/{testResults.total} tests passed ({Math.round(testResults.passed/testResults.total*100)}%)
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {testResults.success 
                    ? '🎉 All systems operational! Enhanced research functionality is ready for use.'
                    : '⚠️ Some components need attention. Check console for details.'
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-indigo-600" />
            How to Use Enhanced Research
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">1. Start Research</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Click "Academic Research" button in the search interface to begin.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Enter your research topic</li>
                  <li>• System analyzes query complexity</li>
                  <li>• Suggests appropriate research types</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">2. Select Research Type</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Choose from Quick Research, Literature Review, Research Paper, Academic Book, or Policy Brief.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Each type has optimized settings</li>
                  <li>• Word count targets vary by type</li>
                  <li>• Citation density adapts to requirements</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">3. Review Outline</h4>
                <p className="text-sm text-gray-600 mb-2">
                  AI generates intelligent outline based on your research type and topic.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Dynamic section planning</li>
                  <li>• Logical flow structure</li>
                  <li>• Academic standards compliance</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">4. Get Results</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Receive comprehensive research with proper formatting and consolidated references.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Clean academic formatting</li>
                  <li>• Consolidated reference list</li>
                  <li>• Word export ready</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 pt-8 border-t border-gray-200">
        <p>Enhanced Academic Research System - Comprehensive research with professional output</p>
      </div>
    </div>
  );
}

export default EnhancedResearchDemo;
