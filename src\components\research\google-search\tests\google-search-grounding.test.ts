/**
 * Google Search Grounding Validation Tests
 * Tests to ensure proper Google Search integration and academic source validation
 */

import { googleSearchService } from '../services/google-search.service';
import { deepResearchCostService } from '../services/deep-research-cost.service';

// Mock testing functions for environments without Jest/Vitest
const mockDescribe = (name: string, fn: () => void) => {
  console.log(`\n📋 Test Suite: ${name}`);
  try {
    fn();
    console.log(`✅ Test Suite "${name}" completed`);
  } catch (error) {
    console.error(`❌ Test Suite "${name}" failed:`, error);
  }
};

const mockTest = (name: string, fn: () => void) => {
  console.log(`  🧪 ${name}`);
  try {
    fn();
    console.log(`    ✅ PASSED`);
  } catch (error) {
    console.error(`    ❌ FAILED:`, error.message);
  }
};

const mockExpect = (actual: any) => ({
  toBe: (expected: any) => {
    if (actual !== expected) {
      throw new Error(`Expected ${expected}, but got ${actual}`);
    }
  },
  toBeGreaterThan: (expected: any) => {
    if (actual <= expected) {
      throw new Error(`Expected ${actual} to be greater than ${expected}`);
    }
  },
  toBeLessThan: (expected: any) => {
    if (actual >= expected) {
      throw new Error(`Expected ${actual} to be less than ${expected}`);
    }
  },
  toContain: (expected: any) => {
    if (!actual.includes(expected)) {
      throw new Error(`Expected "${actual}" to contain "${expected}"`);
    }
  }
});

// Use mock functions if testing framework is not available
const describe = typeof window !== 'undefined' && !(window as any).describe ? mockDescribe : (window as any).describe;
const test = typeof window !== 'undefined' && !(window as any).test ? mockTest : (window as any).test;
const expect = typeof window !== 'undefined' && !(window as any).expect ? mockExpect : (window as any).expect;

// Validation functions that can be called directly
export const runGoogleSearchValidationTests = () => {
  console.log('🚀 Starting Google Search Grounding Validation Tests...\n');

describe('Google Search Grounding Validation', () => {
  
  describe('URL Validation', () => {
    test('should validate real academic URLs', () => {
      const validUrls = [
        'https://scholar.google.com/scholar?q=artificial+intelligence',
        'https://pubmed.ncbi.nlm.nih.gov/12345678/',
        'https://arxiv.org/abs/2301.12345',
        'https://www.nature.com/articles/s41586-023-12345-6',
        'https://science.org/doi/10.1126/science.abc1234',
        'https://ieeexplore.ieee.org/document/12345678',
        'https://dl.acm.org/doi/10.1145/12345.67890',
        'https://www.jstor.org/stable/12345678',
        'https://www.sciencedirect.com/science/article/pii/S123456789',
        'https://link.springer.com/article/10.1007/s12345-023-01234-5'
      ];

      validUrls.forEach(url => {
        expect(googleSearchService['isValidUrl'](url)).toBe(true);
      });
    });

    test('should reject fake or placeholder URLs', () => {
      const invalidUrls = [
        'https://example.com/fake-article',
        'https://placeholder.com/research',
        'https://fake-url.com/study',
        'https://dummy-link.org/paper',
        'https://test.com/article',
        'http://localhost:3000/research',
        'https://your-domain.com/study',
        'https://real-site.com/article...',
        'https://site.com/{{placeholder}}',
        'not-a-url-at-all'
      ];

      invalidUrls.forEach(url => {
        expect(googleSearchService['isValidUrl'](url)).toBe(false);
      });
    });
  });

  describe('Academic Source Detection', () => {
    test('should identify academic domains correctly', () => {
      const academicSources = [
        { url: 'https://mit.edu/research/ai', domain: 'mit.edu' },
        { url: 'https://stanford.edu/study', domain: 'stanford.edu' },
        { url: 'https://nih.gov/research', domain: 'nih.gov' },
        { url: 'https://nsf.gov/funding', domain: 'nsf.gov' },
        { url: 'https://scholar.google.com/paper', domain: 'scholar.google.com' },
        { url: 'https://pubmed.ncbi.nlm.nih.gov/123', domain: 'pubmed.ncbi.nlm.nih.gov' },
        { url: 'https://arxiv.org/abs/123', domain: 'arxiv.org' },
        { url: 'https://nature.com/articles/123', domain: 'nature.com' }
      ];

      academicSources.forEach(source => {
        expect(googleSearchService['isAcademicSource'](source.url, source.domain)).toBe(true);
      });
    });

    test('should calculate higher scores for academic sources', () => {
      const academicSource = {
        domain: 'mit.edu',
        title: 'Research Study on Machine Learning Applications',
        snippet: 'This peer-reviewed research investigates machine learning methodologies',
        authors: ['Dr. John Smith', 'Dr. Jane Doe'],
        journal: 'Journal of AI Research',
        year: 2023
      };

      const webSource = {
        domain: 'techblog.com',
        title: 'AI is Cool',
        snippet: 'Some thoughts about artificial intelligence',
        year: 2023
      };

      const academicScore = googleSearchService['calculateAcademicScore'](academicSource);
      const webScore = googleSearchService['calculateAcademicScore'](webSource);

      expect(academicScore).toBeGreaterThan(0.7);
      expect(webScore).toBeLessThan(0.5);
      expect(academicScore).toBeGreaterThan(webScore);
    });
  });

  describe('Academic Prompt Generation', () => {
    test('should generate academic-focused prompts', () => {
      const query = 'machine learning in healthcare';
      const options = {
        maxResults: 10,
        searchDepth: 'advanced' as const,
        includeAcademicSources: true,
        citationStyle: 'apa' as const,
        academicFocus: true
      };

      const prompt = googleSearchService['createGroundedAcademicPrompt'](query, options);

      // Check for academic requirements
      expect(prompt).toContain('academic sources');
      expect(prompt).toContain('peer-reviewed');
      expect(prompt).toContain('scholarly');
      expect(prompt).toContain('site:edu');
      expect(prompt).toContain('research paper');
      expect(prompt).toContain('university');
      expect(prompt).toContain('REAL, WORKING sources');
      expect(prompt).toContain('Google Search');
      expect(prompt).toContain('APA format');
    });

    test('should include academic search terms', () => {
      const query = 'climate change impacts';
      const options = {
        maxResults: 8,
        searchDepth: 'comprehensive' as const,
        academicFocus: true,
        citationStyle: 'harvard' as const
      };

      const prompt = googleSearchService['createGroundedAcademicPrompt'](query, options);

      const expectedTerms = [
        '"climate change impacts" site:edu',
        '"climate change impacts" research paper',
        '"climate change impacts" scholarly article',
        '"climate change impacts" peer reviewed'
      ];

      expectedTerms.forEach(term => {
        expect(prompt).toContain(term);
      });
    });
  });

  describe('Deep Research Academic Integration', () => {
    test('should generate academic outline with proper search terms', () => {
      const query = 'artificial intelligence ethics';
      const options = {
        maxSubtopics: 5,
        wordsPerSection: 700,
        researchDepth: 'advanced' as const,
        targetAudience: 'academic' as const
      };

      const outlinePrompt = googleSearchService['createOutlinePrompt'](query, options);

      expect(outlinePrompt).toContain('academic research');
      expect(outlinePrompt).toContain('peer-reviewed');
      expect(outlinePrompt).toContain('scholarly');
      expect(outlinePrompt).toContain('site:edu');
      expect(outlinePrompt).toContain('research publication');
      expect(outlinePrompt).toContain('academic publication standards');
    });

    test('should create academic batch research prompts', () => {
      const subtopics = [
        {
          id: '1',
          title: 'AI Ethics Frameworks',
          description: 'Theoretical foundations of AI ethics',
          keyQuestions: ['What are the main ethical frameworks?'],
          estimatedWords: 700,
          priority: 'high' as const,
          searchTerms: ['AI ethics framework', 'artificial intelligence ethics theory'],
          order: 1
        }
      ];

      const options = {
        maxResults: 8,
        searchDepth: 'advanced' as const,
        includeAcademicSources: true,
        academicFocus: true
      };

      const batchPrompt = googleSearchService['createBatchResearchPrompt'](subtopics, options);

      expect(batchPrompt).toContain('academic researcher');
      expect(batchPrompt).toContain('scholarly report');
      expect(batchPrompt).toContain('REAL academic sources');
      expect(batchPrompt).toContain('.edu domains');
      expect(batchPrompt).toContain('peer-reviewed journals');
      expect(batchPrompt).toContain('site:edu');
      expect(batchPrompt).toContain('research paper');
      expect(batchPrompt).toContain('Verified Academic Sources');
    });

    test('should create academic section writing prompts', () => {
      const subtopic = {
        id: '1',
        title: 'Machine Learning Applications',
        description: 'Applications of ML in various domains',
        keyQuestions: ['What are the main applications?'],
        estimatedWords: 700,
        priority: 'high' as const,
        searchTerms: ['machine learning applications'],
        order: 1
      };

      const researchData = {
        id: '1',
        subtopicId: '1',
        query: 'machine learning applications',
        sources: [
          {
            id: '1',
            title: 'ML Applications in Healthcare',
            url: 'https://mit.edu/research/ml-healthcare',
            domain: 'mit.edu',
            snippet: 'Research shows ML applications in medical diagnosis',
            score: 0.9,
            type: 'academic' as const,
            publishedDate: '2023-01-01'
          }
        ],
        summary: 'Research on ML applications',
        keyFindings: ['ML improves diagnostic accuracy'],
        searchTime: 1000,
        totalResults: 1
      };

      const options = {
        wordsPerSection: 700,
        citationStyle: 'apa' as const,
        targetAudience: 'academic' as const
      };

      const sectionPrompt = googleSearchService['createSectionPrompt'](
        subtopic,
        researchData,
        [],
        options
      );

      expect(sectionPrompt).toContain('academic section');
      expect(sectionPrompt).toContain('scholarly research report');
      expect(sectionPrompt).toContain('ACADEMIC WRITING STANDARDS');
      expect(sectionPrompt).toContain('Evidence-Based Arguments');
      expect(sectionPrompt).toContain('VERIFIED ACADEMIC SOURCES');
      expect(sectionPrompt).toContain('graduate-level academic standard');
      expect(sectionPrompt).toContain('APA format');
    });
  });

  describe('Cost Estimation for Academic Research', () => {
    test('should estimate costs for academic deep research', () => {
      const query = 'sustainable energy technologies';
      const options = {
        maxSubtopics: 6,
        wordsPerSection: 700,
        researchDepth: 'advanced' as const,
        includeExecutiveSummary: true,
        citationStyle: 'apa' as const,
        targetAudience: 'academic' as const,
        allowOutlineEditing: true
      };

      const estimate = deepResearchCostService.estimateCost(query, options);

      expect(estimate.totalTokens).toBeGreaterThan(10000);
      expect(estimate.estimatedCost).toBeGreaterThan(0.01);
      expect(estimate.apiCalls).toBeGreaterThan(5);
      expect(estimate.estimatedTime).toBeGreaterThan(60);
      expect(estimate.breakdown.outlineGeneration).toBeGreaterThan(0);
      expect(estimate.breakdown.batchResearch).toBeGreaterThan(0);
      expect(estimate.breakdown.sectionGeneration).toBeGreaterThan(0);
    });
  });
});

}; // End of runGoogleSearchValidationTests

// Integration test helper
export const testAcademicSearchIntegration = async () => {
  console.log('🧪 Testing Academic Search Integration...');
  
  try {
    // Test basic academic search
    const result = await googleSearchService.searchAcademic(
      'machine learning applications in healthcare',
      {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAcademicSources: true,
        academicFocus: true,
        citationStyle: 'apa'
      }
    );

    console.log('✅ Academic search completed');
    console.log(`📊 Found ${result.sources.length} sources`);
    
    // Validate sources
    const academicSources = result.sources.filter(s => 
      s.domain.includes('.edu') || 
      s.domain.includes('.gov') || 
      s.domain.includes('scholar.google') ||
      s.domain.includes('pubmed') ||
      s.domain.includes('nature.com') ||
      s.domain.includes('science.org')
    );

    console.log(`🎓 Academic sources: ${academicSources.length}/${result.sources.length}`);
    
    // Check for real URLs
    const validUrls = result.sources.filter(s => {
      try {
        new URL(s.url);
        return !s.url.includes('example.com') && !s.url.includes('placeholder');
      } catch {
        return false;
      }
    });

    console.log(`🔗 Valid URLs: ${validUrls.length}/${result.sources.length}`);
    
    if (academicSources.length > 0 && validUrls.length > 0) {
      console.log('✅ Academic search integration working correctly');
      return true;
    } else {
      console.log('❌ Academic search integration needs improvement');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Academic search integration test failed:', error);
    return false;
  }
};
