/**
 * Service for extracting and managing citations from AI-generated paper sections
 */

import { Citation } from './types';

/**
 * Extract citations from text in (Author, Year) format
 */
export function extractCitationsFromText(text: string, sectionId: string): { 
  citations: Citation[];
  matches: string[]
} {
  // Regular expression to match citations in various formats:
  // (<PERSON>, 2023)
  // (<PERSON> et al., 2023)
  // (<PERSON> and <PERSON>, 2023)
  // (<PERSON> & <PERSON>, 2023)
  // (Smith, <PERSON>, & <PERSON>, 2023)
  // (Smith, 2023; Jones, 2022)
  const citationRegex = /\(([^)]+?,\s*\d{4}[^)]*)\)/g;
  
  const matches = [...text.matchAll(citationRegex)].map(match => match[0]);
  const uniqueMatches = Array.from(new Set(matches));
  
  let allCitations: Citation[] = [];
  
  // Create citation objects from matches
  uniqueMatches.forEach(match => {
    // Clean up the citation text
    const citationText = match.replace(/[()]/g, '').trim();
    
    // Handle multiple citations separated by semicolons (<PERSON>, 2023; <PERSON>, 2022)
    const multipleCitations = citationText.split(';').map(part => part.trim());
    
    multipleCitations.forEach(singleCitation => {
      // Parse author and year information
      const lastCommaIndex = singleCitation.lastIndexOf(',');
      if (lastCommaIndex === -1) return; // Skip invalid citations
      
      let authorPart = singleCitation.substring(0, lastCommaIndex).trim();
      const yearPart = singleCitation.substring(lastCommaIndex + 1).trim();
      
      // Extract year as a number
      const yearMatch = yearPart.match(/\d{4}/);
      if (!yearMatch) return; // Skip if no valid year found
      
      const year = parseInt(yearMatch[0]);
      
      // Process authors
      let authors: string[] = [];
      if (authorPart.includes(' et al.')) {
        authorPart = authorPart.replace(' et al.', '');
        authors = [authorPart];
      } else if (authorPart.includes(' and ')) {
        authors = authorPart.split(' and ').map(author => author.trim());
      } else if (authorPart.includes(' & ')) {
        authors = authorPart.split(' & ').map(author => author.trim());
      } else if (authorPart.includes(', ') && (authorPart.includes(' and ') || authorPart.includes(' & '))) {
        // Handle format like "Smith, Jones, & Lee"
        let parts = authorPart.split(', ');
        
        // Last part might contain "and" or "&"
        const lastPart = parts.pop() || '';
        if (lastPart.includes(' and ')) {
          const lastAuthors = lastPart.split(' and ').map(a => a.trim());
          parts.push(...lastAuthors);
        } else if (lastPart.includes(' & ')) {
          const lastAuthors = lastPart.split(' & ').map(a => a.trim());
          parts.push(...lastAuthors);
        } else {
          parts.push(lastPart);
        }
        
        authors = parts.filter(p => p.trim() !== '');
      } else {
        authors = [authorPart];
      }
      
      // Filter out any empty author names
      authors = authors.filter(author => author.trim() !== '');
      if (authors.length === 0) return; // Skip if no valid authors
      
      // Generate a unique ID based on authors and year
      const authorsKey = authors.join('-').toLowerCase().replace(/[^a-z0-9]/g, '');
      const id = `citation-${authorsKey}-${year}`;
      
      // Get the in-text format - for multiple citations, use just this citation's part
      const inTextFormat = multipleCitations.length > 1 ? 
        `(${singleCitation})` : match;
      
      allCitations.push({
        id,
        inTextFormat,
        authors,
        year,
        title: '', // These will be filled in later when matched with references
        source: '',
        sectionIds: [sectionId],
        referenceText: '' // Will be populated when matched with references
      });
    });
  });
  
  return { 
    citations: allCitations,
    matches: uniqueMatches
  };
}

/**
 * Generate a consolidated references section from all citations
 */
export function generateReferencesSection(citations: Citation[]): string {
  // Filter to citations that have reference text
  const citationsWithReferences = citations.filter(c => c.referenceText);
  
  if (citationsWithReferences.length === 0) {
    return "No references found in this paper.";
  }
  
  // Sort alphabetically by first author's last name
  const sortedCitations = [...citationsWithReferences].sort((a, b) => {
    // Get the first author's last name (before any comma)
    const getLastName = (citation: Citation): string => {
      if (citation.authors.length === 0) return '';
      const firstAuthor = citation.authors[0];
      return firstAuthor.split(' ')[0].replace(',', '').toLowerCase();
    };
    
    return getLastName(a).localeCompare(getLastName(b));
  });
  
  // Generate the references text
  return sortedCitations.map(c => c.referenceText).join('\n\n');
}

/**
 * Split a reference text into individual references
 */
export function splitReferencesIntoEntries(referencesText: string): string[] {
  // Split by combinations of newlines that might indicate separate references
  let entries = referencesText
    .split(/\n\n+|\r\n\r\n+/) // Split by double newlines
    .map(entry => entry.trim())
    .filter(entry => entry.length > 0);
  
  // If we got only one entry but it contains multiple references
  // try to split it further based on patterns
  if (entries.length === 1 && entries[0].length > 300) {
    // Look for patterns like "Author, A. (YEAR)." at the start of a line,
    // which usually indicates the start of a new reference
    const secondaryEntries = entries[0]
      .split(/\n(?=[A-Za-z]+,\s+[A-Z]\.\s+\(\d{4}\))/)
      .map(entry => entry.trim())
      .filter(entry => entry.length > 0);
    
    if (secondaryEntries.length > 1) {
      entries = secondaryEntries;
    }
  }
  
  return entries;
}

/**
 * Match citations with their corresponding references
 */
export function matchCitationsWithReferences(
  citations: Citation[], 
  referencesText: string
): Citation[] {
  // If no references or citations, return as is
  if (!referencesText || !citations.length) {
    return citations;
  }
  
  const referenceEntries = splitReferencesIntoEntries(referencesText);
  console.log(`Split references into ${referenceEntries.length} entries`);
  
  // Create a map of existing citations by their key attributes
  // We'll use multiple keys to increase match chances
  const citationMap = new Map<string, Citation>();
  const yearMap = new Map<number, Citation[]>();
  const authorLastNameMap = new Map<string, Citation[]>();
  
  citations.forEach(citation => {
    // Add to the year map for fallback matching
    if (!yearMap.has(citation.year)) {
      yearMap.set(citation.year, []);
    }
    yearMap.get(citation.year)?.push(citation);
    
    // Create keys from authors and year
    citation.authors.forEach(author => {
      // Extract last name
      const lastName = author.split(' ')[0].replace(',', '').toLowerCase();
      
      // Add to last name map for fallback matching
      if (!authorLastNameMap.has(lastName)) {
        authorLastNameMap.set(lastName, []);
      }
      authorLastNameMap.get(lastName)?.push(citation);
      
      // Create several possible keys to increase match chances
      const exactKey = `${lastName}-${citation.year}`;
      citationMap.set(exactKey, citation);
      
      // Also create a key with just the first 4 chars of lastname for fuzzy matching
      if (lastName.length > 3) {
        const fuzzyKey = `${lastName.substring(0, 4)}-${citation.year}`;
        citationMap.set(fuzzyKey, citation);
      }
      
      // For citations with 'et al.' create alternative keys
      if (citation.inTextFormat.includes('et al.')) {
        const etAlKey = `${lastName}-etal-${citation.year}`;
        citationMap.set(etAlKey, citation);
      }
    });
  });
  
  // Updated citations array to return
  const updatedCitations = [...citations];
  const matchedCitations = new Set<string>(); // Track which citations were matched
  
  // For each reference entry, try to match it to a citation
  referenceEntries.forEach(entry => {
    // Skip very short entries that are likely not complete references
    if (entry.length < 30) return;
    
    // Try different patterns to extract author and year for more robust matching
    const patterns = [
      // Standard pattern: Smith, J. (2023)
      /([A-Za-z]+)(?:,|\s+et\s+al).*?(\d{4})/,
      // Alternative patterns for different reference styles
      /([A-Za-z]+),\s+[A-Z]\..*?[(](\d{4})[)]/,
      /([A-Za-z]+)\s+[A-Z]\..*?[(](\d{4})[)]/,
      /([A-Za-z]+),\s+(?:&|and).*?[(](\d{4})[)]/
    ];
    
    let found = false;
    for (const pattern of patterns) {
      const authorYearMatch = entry.match(pattern);
      
      if (authorYearMatch) {
        const [, authorLastName, yearStr] = authorYearMatch;
        const year = parseInt(yearStr);
        const lastName = authorLastName.toLowerCase();
        
        // Try multiple matching strategies in priority order
        
        // 1. Try exact author-year match
        const exactKey = `${lastName}-${year}`;
        let matchingCitation = citationMap.get(exactKey);
        
        // 2. Try fuzzy match if no exact match
        if (!matchingCitation && lastName.length > 3) {
          const fuzzyKey = `${lastName.substring(0, 4)}-${year}`;
          matchingCitation = citationMap.get(fuzzyKey);
        }
        
        // 3. Try matching by et al. pattern
        if (!matchingCitation) {
          const etAlKey = `${lastName}-etal-${year}`;
          matchingCitation = citationMap.get(etAlKey);
        }
        
        // 4. Try matching by author last name and then filtering by year
        if (!matchingCitation) {
          const citationsForAuthor = authorLastNameMap.get(lastName) || [];
          const citationsByThisAuthorAndYear = citationsForAuthor.filter(c => c.year === year);
          if (citationsByThisAuthorAndYear.length === 1) {
            matchingCitation = citationsByThisAuthorAndYear[0];
          }
        }
        
        // 5. If still no match, try matching just by year as a last resort
        if (!matchingCitation) {
          const citationsForYear = yearMap.get(year) || [];
          // Only use year matching if we have a single citation for this year
          // to avoid incorrect matches
          if (citationsForYear.length === 1) {
            matchingCitation = citationsForYear[0];
          }
        }
        
        if (matchingCitation) {
          // Find the index of the matching citation in the array
          const citationIndex = updatedCitations.findIndex(c => c.id === matchingCitation?.id);
          
          if (citationIndex !== -1) {
            matchedCitations.add(matchingCitation.id);
            
            // Update the reference text
            updatedCitations[citationIndex] = {
              ...updatedCitations[citationIndex],
              referenceText: entry,
              
              // Try to extract title and source if possible
              title: extractTitle(entry) || updatedCitations[citationIndex].title,
              source: extractSource(entry) || updatedCitations[citationIndex].source
            };
            
            found = true;
            break; // Exit pattern loop once we've found a match
          }
        }
      }
    }
    
    // If no match found with patterns, try a more aggressive matching for any unmatched citations
    if (!found) {
      // Find any unmatched citations
      const unmatchedCitations = updatedCitations.filter(c => !c.referenceText || c.referenceText.trim() === '');
      
      // If there are unmatched citations and we can't match using patterns, assign this reference arbitrarily
      // to the first unmatched citation - better than having no reference at all
      if (unmatchedCitations.length > 0) {
        const citationIndex = updatedCitations.findIndex(c => c.id === unmatchedCitations[0].id);
        
        updatedCitations[citationIndex] = {
          ...updatedCitations[citationIndex],
          referenceText: entry,
          title: extractTitle(entry) || updatedCitations[citationIndex].title,
          source: extractSource(entry) || updatedCitations[citationIndex].source
        };
      }
    }
  });
  
  console.log(`Matched ${matchedCitations.size} citations out of ${citations.length} total`);
  
  return updatedCitations;
}

/**
 * Helper function to extract a title from a reference entry
 */
function extractTitle(entry: string): string {
  // Try to extract title - typically after the year and before the next period
  const titleMatch = entry.match(/\(\d{4}[^)]*\)\.\s*([^.]+)\./);
  return titleMatch ? titleMatch[1].trim() : '';
}

/**
 * Helper function to extract a source from a reference entry
 */
function extractSource(entry: string): string {
  // Try to extract source - typically italic or after the title
  const sourceMatch = entry.match(/\.\s*(?:<em>|_)([^<_]+)(?:<\/em>|_)/);
  if (sourceMatch) {
    return sourceMatch[1].trim();
  }
  
  // Alternative: look for the second period-delimited section after the year
  const secondSectionMatch = entry.match(/\(\d{4}[^)]*\)\.[^.]+\.\s*([^.]+)\./);
  return secondSectionMatch ? secondSectionMatch[1].trim() : '';
}
