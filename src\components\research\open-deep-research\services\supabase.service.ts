/**
 * Supabase Service
 * Handles database operations and file storage for Open Deep Research
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import {
  DeepResearchSession,
  DeepResearchReport,
  DeepResearchSource,
  DeepResearchFile,
  DeepResearchExport,
  ResearchHistoryItem,
  HistorySearchFilters,
  Report,
  SearchResult
} from '../types';

class SupabaseService {
  /**
   * Check if Supabase client is configured
   */
  isConfigured(): boolean {
    try {
      return Boolean(supabase && import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY);
    } catch (error) {
      console.error('Failed to check Supabase configuration:', error);
      return false;
    }
  }

  /**
   * Get Supabase client
   */
  getClient(): SupabaseClient {
    if (!this.isConfigured()) {
      throw new Error('Supabase client not configured');
    }

    return supabase;
  }

  /**
   * Get current user ID
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      const { data: { user } } = await this.getClient().auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Create a new research session
   */
  async createSession(session: Omit<DeepResearchSession, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<DeepResearchSession | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await this.getClient()
        .from('deep_research_sessions')
        .insert({
          ...session,
          user_id: userId,
        })
        .select('*')
        .single();

      if (error) throw error;
      return data as DeepResearchSession;
    } catch (error) {
      console.error('Failed to create research session:', error);
      return null;
    }
  }

  /**
   * Update session status
   */
  async updateSessionStatus(sessionId: string, status: DeepResearchSession['status']): Promise<boolean> {
    try {
      const { error } = await this.getClient()
        .from('deep_research_sessions')
        .update({ status })
        .eq('id', sessionId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Failed to update session status:', error);
      return false;
    }
  }

  /**
   * Save report to database
   */
  async saveReport(sessionId: string, report: Report): Promise<DeepResearchReport | null> {
    try {
      // Calculate word count
      const allText = report.summary + ' ' + report.sections.map(s => s.content).join(' ');
      const wordCount = allText.trim().split(/\s+/).length;

      const { data, error } = await this.getClient()
        .from('deep_research_reports')
        .insert({
          session_id: sessionId,
          title: report.title,
          summary: report.summary,
          sections: report.sections,
          sources: report.sources,
          used_sources: report.usedSources,
          word_count: wordCount,
        })
        .select('*')
        .single();

      if (error) throw error;
      return data as DeepResearchReport;
    } catch (error) {
      console.error('Failed to save report:', error);
      return null;
    }
  }

  /**
   * Save search results to database
   */
  async saveSources(sessionId: string, results: SearchResult[], selectedIds: string[] = []): Promise<boolean> {
    try {
      const sources = results.map(result => ({
        session_id: sessionId,
        url: result.url,
        title: result.name,
        snippet: result.snippet,
        content: result.content,
        is_selected: selectedIds.includes(result.id),
        score: result.score,
      }));

      const { error } = await this.getClient()
        .from('deep_research_sources')
        .insert(sources);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Failed to save sources:', error);
      return false;
    }
  }

  /**
   * Upload file to Supabase storage
   */
  async uploadFile(sessionId: string, file: File, content?: string): Promise<DeepResearchFile | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Upload file to storage
      const filePath = `${userId}/${sessionId}/${file.name}`;
      const { data: uploadData, error: uploadError } = await this.getClient()
        .storage
        .from('deep_research_files')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Save file metadata to database
      const { data, error } = await this.getClient()
        .from('deep_research_files')
        .insert({
          session_id: sessionId,
          original_filename: file.name,
          file_path: uploadData.path,
          file_type: file.type,
          file_size: file.size,
          content: content,
        })
        .select('*')
        .single();

      if (error) throw error;
      return data as DeepResearchFile;
    } catch (error) {
      console.error('Failed to upload file:', error);
      return null;
    }
  }

  /**
   * Save export record and file
   */
  async saveExport(
    sessionId: string, 
    format: 'pdf' | 'docx' | 'txt', 
    fileName: string, 
    fileBlob: Blob,
    options: any = {}
  ): Promise<DeepResearchExport | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Upload file to storage
      const filePath = `${userId}/${sessionId}/exports/${fileName}`;
      const { data: uploadData, error: uploadError } = await this.getClient()
        .storage
        .from('deep_research_exports')
        .upload(filePath, fileBlob);

      if (uploadError) throw uploadError;

      // Save export metadata to database
      const { data, error } = await this.getClient()
        .from('deep_research_exports')
        .insert({
          session_id: sessionId,
          export_format: format,
          file_name: fileName,
          file_path: uploadData.path,
          file_size: fileBlob.size,
          export_options: options,
        })
        .select('*')
        .single();

      if (error) throw error;
      return data as DeepResearchExport;
    } catch (error) {
      console.error('Failed to save export:', error);
      return null;
    }
  }

  /**
   * Get research history
   */
  async getResearchHistory(limit: number = 20, offset: number = 0): Promise<ResearchHistoryItem[]> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Get sessions
      const { data: sessions, error: sessionsError } = await this.getClient()
        .from('deep_research_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (sessionsError) throw sessionsError;
      if (!sessions || sessions.length === 0) return [];

      // Get related data for each session
      const sessionIds = sessions.map(session => session.id);
      
      const { data: reports } = await this.getClient()
        .from('deep_research_reports')
        .select('*')
        .in('session_id', sessionIds);
      
      const { data: sources } = await this.getClient()
        .from('deep_research_sources')
        .select('*')
        .in('session_id', sessionIds);
      
      const { data: files } = await this.getClient()
        .from('deep_research_files')
        .select('*')
        .in('session_id', sessionIds);
      
      const { data: exports } = await this.getClient()
        .from('deep_research_exports')
        .select('*')
        .in('session_id', sessionIds);

      // Combine data into history items
      return sessions.map(session => {
        const sessionReports = reports?.filter(r => r.session_id === session.id) || [];
        const sessionSources = sources?.filter(s => s.session_id === session.id) || [];
        const sessionFiles = files?.filter(f => f.session_id === session.id) || [];
        const sessionExports = exports?.filter(e => e.session_id === session.id) || [];

        return {
          session: session as DeepResearchSession,
          report: sessionReports[0] as DeepResearchReport | undefined,
          sources: sessionSources as DeepResearchSource[],
          files: sessionFiles as DeepResearchFile[],
          exports: sessionExports as DeepResearchExport[],
        };
      });
    } catch (error) {
      console.error('Failed to get research history:', error);
      return [];
    }
  }

  /**
   * Search research history
   */
  async searchHistory(filters: HistorySearchFilters, limit: number = 20): Promise<ResearchHistoryItem[]> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      let query = this.getClient()
        .from('deep_research_sessions')
        .select('*')
        .eq('user_id', userId);

      // Apply filters
      if (filters.query) {
        query = query.or(`title.ilike.%${filters.query}%,query.ilike.%${filters.query}%`);
      }

      if (filters.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start.toISOString())
          .lte('created_at', filters.dateRange.end.toISOString());
      }

      if (filters.model) {
        query = query.eq('selected_model', filters.model);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.isAgentMode !== undefined) {
        query = query.eq('is_agent_mode', filters.isAgentMode);
      }

      // Execute query
      const { data: sessions, error } = await query
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      if (!sessions || sessions.length === 0) return [];

      // Get related data (simplified for brevity)
      const sessionIds = sessions.map(session => session.id);
      
      const { data: reports } = await this.getClient()
        .from('deep_research_reports')
        .select('*')
        .in('session_id', sessionIds);

      // Return simplified history items
      return sessions.map(session => {
        const sessionReport = reports?.find(r => r.session_id === session.id);
        
        return {
          session: session as DeepResearchSession,
          report: sessionReport as DeepResearchReport | undefined,
          sources: [],
          files: [],
          exports: [],
        };
      });
    } catch (error) {
      console.error('Failed to search research history:', error);
      return [];
    }
  }
}

export const supabaseService = new SupabaseService();
