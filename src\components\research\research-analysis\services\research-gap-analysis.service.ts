import { 
  ResearchDocument, 
  GapAnalysis, 
  ResearchGap, 
  GenerationProgress, 
  AIGenerationOptions 
} from '../types';

class ResearchGapAnalysisService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }

  /**
   * Check if the OpenRouter API key is configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Test the API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.apiKey) {
        return { success: false, error: 'API key not configured' };
      }

      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.api<PERSON>ey}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        return { success: true };
      } else {
        const errorText = await response.text();
        return { success: false, error: `API test failed: ${response.status} - ${errorText}` };
      }
    } catch (error) {
      return { success: false, error: `Network error: ${error}` };
    }
  }

  /**
   * Identify research gaps from documents
   */
  async identifyResearchGaps(
    documents: ResearchDocument[],
    onProgress?: (progress: GenerationProgress) => void,
    model: string = 'google/gemini-2.5-flash'
  ): Promise<GapAnalysis> {
    // Check for API key
    if (!this.isConfigured()) {
      throw new Error('OpenRouter API key not configured in .env file (VITE_OPENROUTER_API_KEY)');
    }

    const documentContext = documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      authors: doc.authors,
      year: doc.publicationYear,
      abstract: doc.abstract,
      keyFindings: doc.keyFindings,
      methodology: doc.methodology,
      limitations: doc.limitations,
      futureWork: doc.futureWork
    }));

    const prompt = `Analyze the provided research papers to identify research gaps and opportunities.

Research Papers:
${JSON.stringify(documentContext, null, 2)}

Please identify:
1. Unexplored research questions
2. Methodological gaps
3. Contradictory findings requiring investigation
4. Opportunities for interdisciplinary approaches
5. Emerging trends not yet fully explored

For each identified gap, provide:
- Clear title and description
- Category (methodological, theoretical, empirical, interdisciplinary)
- Priority level (low, medium, high, critical)
- Feasibility score (1-10)
- Impact score (1-10)
- Suggested methods
- Required resources
- Timeline estimate
- Related documents

Also identify key themes and research opportunities.

Return in JSON format:
{
  "gaps": [
    {
      "title": "string",
      "description": "string",
      "category": "methodological" | "theoretical" | "empirical" | "interdisciplinary",
      "priority": "low" | "medium" | "high" | "critical",
      "feasibility": number,
      "impact": number,
      "relatedDocuments": ["string"],
      "suggestedMethods": ["string"],
      "requiredResources": ["string"],
      "timelineEstimate": "string",
      "collaborationPotential": number
    }
  ],
  "themes": [
    {
      "name": "string",
      "description": "string",
      "frequency": number,
      "relatedDocuments": ["string"],
      "keyTerms": ["string"]
    }
  ],
  "opportunities": [
    {
      "title": "string",
      "description": "string",
      "type": "extension" | "replication" | "combination" | "application" | "innovation",
      "difficulty": "beginner" | "intermediate" | "advanced" | "expert",
      "potentialImpact": "low" | "medium" | "high" | "transformative",
      "requiredExpertise": ["string"],
      "suggestedApproach": ["string"]
    }
  ],
  "summary": "string"
}`;

    onProgress?.({
      stage: 'Analyzing documents',
      progress: 30,
      message: 'Identifying patterns and gaps...',
      estimatedTimeRemaining: 180 // Estimate 3 minutes
    });

    try {
      const response = await this.callAI(prompt, {
        model: model,
        maxTokens: 5000,
        temperature: 0.4
      });

      onProgress?.({
        stage: 'Processing analysis',
        progress: 80,
        message: 'Organizing research gaps...',
        estimatedTimeRemaining: 30 // Less than a minute remaining
      });

      const result = this.parseJSONResponse(response);
      
      const gapAnalysis: GapAnalysis = {
        id: crypto.randomUUID(),
        documentIds: documents.map(d => d.id),
        gaps: result.gaps?.map((gap: any) => ({
          id: crypto.randomUUID(),
          title: gap.title,
          description: gap.description,
          category: gap.category,
          priority: gap.priority,
          feasibility: gap.feasibility,
          impact: gap.impact,
          relatedDocuments: gap.relatedDocuments || [],
          suggestedMethods: gap.suggestedMethods || [],
          requiredResources: gap.requiredResources || [],
          timelineEstimate: gap.timelineEstimate,
          collaborationPotential: gap.collaborationPotential || 5
        })) || [],
        themes: result.themes?.map((theme: any) => ({
          id: crypto.randomUUID(),
          name: theme.name,
          description: theme.description,
          frequency: theme.frequency,
          relatedDocuments: theme.relatedDocuments || [],
          keyTerms: theme.keyTerms || [],
          evolution: []
        })) || [],
        opportunities: result.opportunities?.map((opp: any) => ({
          id: crypto.randomUUID(),
          title: opp.title,
          description: opp.description,
          type: opp.type,
          difficulty: opp.difficulty,
          potentialImpact: opp.potentialImpact,
          requiredExpertise: opp.requiredExpertise || [],
          suggestedApproach: opp.suggestedApproach || []
        })) || [],
        generatedAt: new Date(),
        summary: result.summary || 'Gap analysis completed successfully'
      };

      onProgress?.({
        stage: 'Complete',
        progress: 100,
        message: 'Research gap analysis completed'
      });

      return gapAnalysis;
    } catch (error) {
      console.error('Error identifying research gaps:', error);
      
      // Create error-state gap analysis
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const isApiKeyError = errorMessage.includes('API key') || 
                           errorMessage.includes('authorization') || 
                           errorMessage.includes('401');

      return {
        id: crypto.randomUUID(),
        documentIds: documents.map(d => d.id),
        gaps: [{
          id: crypto.randomUUID(),
          title: 'Error',
          description: isApiKeyError 
            ? 'OpenRouter API key error. Please check your API key in the .env file (VITE_OPENROUTER_API_KEY).' 
            : `An error occurred during gap analysis: ${errorMessage}`,
          category: 'methodological',
          priority: 'critical',
          feasibility: 0,
          impact: 0,
          relatedDocuments: [],
          suggestedMethods: [],
          requiredResources: ['Valid API key'],
          timelineEstimate: 'Immediate',
          collaborationPotential: 0
        }],
        themes: [],
        opportunities: [],
        generatedAt: new Date(),
        summary: isApiKeyError 
          ? 'Error: OpenRouter API key not configured or invalid.' 
          : `Error: ${errorMessage}`
      };
    }
  }

  /**
   * Call the OpenRouter API
   */
  private async callAI(prompt: string, options: AIGenerationOptions = {}): Promise<string> {
    if (!this.apiKey) {
      console.error('❌ OpenRouter API key not configured');
      console.error('🔧 Please check VITE_OPENROUTER_API_KEY in your environment variables');
      throw new Error('OpenRouter API key not configured. Please check your environment variables.');
    }

    console.log('🔗 Making API call to OpenRouter for gap analysis...');
    console.log('🔑 API Key configured:', this.apiKey ? 'Yes' : 'No');
    console.log('🌐 Base URL:', this.baseUrl);

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Paper Genius - Research Gap Analysis'
      },
      body: JSON.stringify({
        model: options.model || "google/gemini-2.5-flash",
        messages: [
          {
            role: "system",
            content: "You are an expert research analyst specializing in identifying research gaps and opportunities in academic literature. Provide detailed, accurate, and scholarly analysis."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.4,
        top_p: options.topP || 0.9,
        frequency_penalty: options.frequencyPenalty || 0,
        presence_penalty: options.presencePenalty || 0
      })
    });

    console.log('📡 API Response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API request failed:', response.status, response.statusText);
      console.error('❌ Error details:', errorText);
      throw new Error(`AI API call failed: ${response.status} ${response.statusText}. Details: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ API response received successfully');
    return data.choices[0]?.message?.content || 'No response generated';
  }

  /**
   * Parse JSON response from the AI
   */
  private parseJSONResponse(response: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // If no JSON found, try to parse the entire response
      return JSON.parse(response);
    } catch (error) {
      console.error('Error parsing JSON response:', response);
      // Return a minimal structure if JSON parsing fails
      return {
        gaps: [],
        themes: [],
        opportunities: [],
        summary: 'Error parsing AI response into valid JSON'
      };
    }
  }
}

export const researchGapAnalysisService = new ResearchGapAnalysisService();
export default researchGapAnalysisService;
