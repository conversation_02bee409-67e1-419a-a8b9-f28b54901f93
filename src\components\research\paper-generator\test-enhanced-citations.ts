/**
 * Test script for Enhanced Citation System
 * This file contains tests to validate the enhanced citation functionality
 */

import { enhancedCitationSearchService } from './services/enhanced-citation-search.service';
import { enhancedPaperAIService } from './services/enhanced-paper-ai.service';
import { citationValidationService } from './services/citation-validation.service';

// Test data
const testContext = {
  sectionId: 'introduction',
  sectionName: 'Introduction',
  title: 'Machine Learning Applications in Climate Change Research',
  researchField: 'Environmental Science',
  keywords: ['machine learning', 'climate change', 'environmental modeling', 'data analysis'],
  userContent: 'This research explores the application of machine learning algorithms for climate change prediction and environmental monitoring. We focus on deep learning approaches for analyzing satellite data and weather patterns.',
  relatedSections: ['Methodology: We use convolutional neural networks and time series analysis']
};

/**
 * Test the enhanced citation search service
 */
export async function testCitationSearch() {
  console.log('🔍 Testing Enhanced Citation Search...');
  
  try {
    const citations = await enhancedCitationSearchService.searchCitationsForSection(testContext, {
      maxSources: 5,
      searchDepth: 'basic',
      includeRecentOnly: true
    });

    console.log(`✅ Found ${citations.length} citations`);
    
    if (citations.length > 0) {
      console.log('📄 Sample citation:');
      const sample = citations[0];
      console.log(`  Title: ${sample.title}`);
      console.log(`  Authors: <AUTHORS>
      console.log(`  Year: ${sample.year}`);
      console.log(`  In-text: ${sample.inTextCitation}`);
      console.log(`  URL: ${sample.url}`);
      console.log(`  Relevance: ${Math.round(sample.relevanceScore * 100)}%`);
    }

    return citations;
  } catch (error) {
    console.error('❌ Citation search failed:', error);
    return [];
  }
}

/**
 * Test the citation validation service
 */
export async function testCitationValidation() {
  console.log('🔍 Testing Citation Validation...');
  
  // First get some citations to validate
  const citations = await testCitationSearch();
  
  if (citations.length === 0) {
    console.log('⚠️ No citations to validate');
    return;
  }

  try {
    const validation = citationValidationService.validateAndCleanCitations(citations);
    
    console.log(`✅ Validation complete:`);
    console.log(`  Valid citations: ${validation.valid.length}`);
    console.log(`  Invalid citations: ${validation.invalid.length}`);
    console.log(`  Duplicate citations: ${validation.duplicates.length}`);

    if (validation.valid.length > 0) {
      const sample = validation.valid[0];
      const result = validation.validationResults[sample.id];
      console.log(`📊 Sample validation result:`);
      console.log(`  Score: ${Math.round(result.score * 100)}%`);
      console.log(`  Errors: ${result.errors.length}`);
      console.log(`  Warnings: ${result.warnings.length}`);
      
      // Test formatting
      const formatted = citationValidationService.formatCitation(sample, {
        style: 'apa',
        includeLinks: true,
        includeDOI: true
      });
      console.log(`📝 Formatted citation: ${formatted}`);
    }

    return validation;
  } catch (error) {
    console.error('❌ Citation validation failed:', error);
  }
}

/**
 * Test the enhanced paper AI service
 */
export async function testEnhancedPaperGeneration() {
  console.log('🔍 Testing Enhanced Paper Generation...');
  
  try {
    const result = await enhancedPaperAIService.generateSectionWithRealCitations(
      'introduction',
      'Introduction',
      testContext,
      {
        model: 'google/gemini-2.5-flash-preview-05-20',
        maxTokens: 1024,
        temperature: 0.7,
        maxCitations: 5
      }
    );

    console.log(`✅ Generation complete:`);
    console.log(`  Content length: ${result.content.length} characters`);
    console.log(`  Word count: ${result.wordCount}`);
    console.log(`  Citations found: ${result.citationCount}`);
    console.log(`  References generated: ${result.references.length > 0 ? 'Yes' : 'No'}`);

    if (result.citations.length > 0) {
      console.log(`📄 Sample citation from generated content:`);
      const sample = result.citations[0];
      console.log(`  ${sample.inTextCitation} - ${sample.title}`);
    }

    // Show a snippet of the generated content
    const snippet = result.content.substring(0, 200) + '...';
    console.log(`📝 Content snippet: ${snippet}`);

    return result;
  } catch (error) {
    console.error('❌ Enhanced paper generation failed:', error);
    console.log('🔄 This might be due to API limitations or network issues');
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🚀 Starting Enhanced Citation System Tests...\n');
  
  const results = {
    citationSearch: null as any,
    validation: null as any,
    generation: null as any
  };

  // Test 1: Citation Search
  console.log('='.repeat(50));
  results.citationSearch = await testCitationSearch();
  console.log('');

  // Test 2: Citation Validation
  console.log('='.repeat(50));
  results.validation = await testCitationValidation();
  console.log('');

  // Test 3: Enhanced Paper Generation
  console.log('='.repeat(50));
  results.generation = await testEnhancedPaperGeneration();
  console.log('');

  // Summary
  console.log('='.repeat(50));
  console.log('📊 Test Summary:');
  console.log(`  Citation Search: ${results.citationSearch?.length > 0 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Citation Validation: ${results.validation?.valid?.length > 0 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`  Enhanced Generation: ${results.generation?.content ? '✅ PASS' : '❌ FAIL'}`);
  console.log('='.repeat(50));

  return results;
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testEnhancedCitations = {
    runAllTests,
    testCitationSearch,
    testCitationValidation,
    testEnhancedPaperGeneration
  };
  
  console.log('🧪 Enhanced Citation Tests available in browser console:');
  console.log('  - testEnhancedCitations.runAllTests()');
  console.log('  - testEnhancedCitations.testCitationSearch()');
  console.log('  - testEnhancedCitations.testCitationValidation()');
  console.log('  - testEnhancedCitations.testEnhancedPaperGeneration()');
}
