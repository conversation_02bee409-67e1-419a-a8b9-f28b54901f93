import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import {
  Lightbulb,
  Brain,
  Target,
  CheckCircle,
  RefreshCw,
  TrendingUp,
  Zap
} from "lucide-react";

import { ResearchGap, ResearchDocument, ResearchHypothesis, AIGenerationOptions } from '../types';

interface HypothesisGeneratorProps {
  gaps: ResearchGap[];
  documents: ResearchDocument[];
  onGenerate: (hypotheses: ResearchHypothesis[]) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function HypothesisGenerator({
  gaps,
  documents,
  onGenerate,
  aiSettings
}: HypothesisGeneratorProps) {
  const [hypothesisCount, setHypothesisCount] = useState(10);
  const [focusArea, setFocusArea] = useState('all');
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentHypotheses, setCurrentHypotheses] = useState<ResearchHypothesis[]>([]);

  const handleGenerateHypotheses = useCallback(async () => {
    if (gaps.length === 0) {
      toast.error('No research gaps available. Please analyze documents first.');
      return;
    }

    setIsGenerating(true);

    try {
      // This is a placeholder implementation
      // In real implementation, you'd call the service
      toast.success(`Generated ${hypothesisCount} hypotheses!`);
      
      // Mock hypotheses for demonstration
      const mockHypotheses: ResearchHypothesis[] = Array.from({ length: hypothesisCount }, (_, i) => ({
        id: `hyp_${Date.now()}_${i}`,
        statement: `Research hypothesis ${i + 1} based on identified gaps`,
        type: 'directional',
        variables: {
          independent: [{
            name: `Independent Variable ${i + 1}`,
            type: 'continuous',
            description: 'Description of independent variable',
            measurement: 'Scale measurement',
            operationalization: 'How to measure this variable'
          }],
          dependent: [{
            name: `Dependent Variable ${i + 1}`,
            type: 'continuous',
            description: 'Description of dependent variable',
            measurement: 'Scale measurement',
            operationalization: 'How to measure this variable'
          }],
          control: []
        },
        testability: Math.floor(Math.random() * 3) + 8, // 8-10 range
        novelty: Math.floor(Math.random() * 3) + 7, // 7-9 range
        significance: Math.floor(Math.random() * 3) + 7, // 7-9 range
        suggestedMethodology: ['Experimental design', 'Statistical analysis'],
        expectedOutcomes: ['Positive correlation expected', 'Significant effect anticipated'],
        limitations: ['Sample size limitations', 'Temporal constraints'],
        requiredResources: ['Research participants', 'Data collection tools'],
        timeline: '6-12 months',
        relatedGaps: gaps.slice(0, 2).map(g => g.id)
      }));

      setCurrentHypotheses(mockHypotheses);
      onGenerate(mockHypotheses);
    } catch (error) {
      console.error('Error generating hypotheses:', error);
      toast.error('Failed to generate hypotheses. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  }, [gaps, hypothesisCount, onGenerate]);

  const getTestabilityColor = (score: number) => {
    if (score >= 8) return 'text-green-600 bg-green-100';
    if (score >= 6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-2xl">
                <Lightbulb className="h-7 w-7 text-yellow-500" />
                Hypothesis Generator
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Generate testable research hypotheses based on identified gaps and existing literature
              </p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white/80 rounded-full border border-yellow-300">
              <Brain className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                {aiSettings.selectedModel.split('/')[1]?.replace('-', ' ') || 'AI Model'}
              </span>
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Brain className="h-5 w-5 text-blue-500" />
            Generation Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{gaps.length}</div>
                <div className="text-sm text-blue-700">Research Gaps</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{documents.length}</div>
                <div className="text-sm text-green-700">Source Documents</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {gaps.filter(g => g.priority === 'high' || g.priority === 'critical').length}
                </div>
                <div className="text-sm text-purple-700">High Priority Gaps</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">{currentHypotheses.length}</div>
                <div className="text-sm text-orange-700">Generated Hypotheses</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Number of Hypotheses</label>
              <Input
                type="number"
                min="1"
                max="20"
                value={hypothesisCount}
                onChange={(e) => setHypothesisCount(parseInt(e.target.value) || 10)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Focus Area</label>
              <Select value={focusArea} onValueChange={setFocusArea}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Areas</SelectItem>
                  <SelectItem value="methodological">Methodological</SelectItem>
                  <SelectItem value="theoretical">Theoretical</SelectItem>
                  <SelectItem value="empirical">Empirical</SelectItem>
                  <SelectItem value="interdisciplinary">Interdisciplinary</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleGenerateHypotheses}
                disabled={isGenerating || gaps.length === 0}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Generate Hypotheses
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generated Hypotheses */}
      {currentHypotheses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Generated Hypotheses ({currentHypotheses.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {currentHypotheses.map((hypothesis, index) => (
                <div key={hypothesis.id} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold">Hypothesis {index + 1}</h3>
                    <div className="flex items-center gap-2">
                      <Badge className={getTestabilityColor(hypothesis.testability)}>
                        Testability: {hypothesis.testability}/10
                      </Badge>
                      <Badge variant="outline">
                        {hypothesis.type}
                      </Badge>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg mb-4">
                    <p className="font-medium text-blue-900">{hypothesis.statement}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center p-3 bg-green-50 rounded">
                      <div className="text-lg font-bold text-green-600">{hypothesis.testability}</div>
                      <div className="text-sm text-green-700">Testability</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded">
                      <div className="text-lg font-bold text-purple-600">{hypothesis.novelty}</div>
                      <div className="text-sm text-purple-700">Novelty</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded">
                      <div className="text-lg font-bold text-orange-600">{hypothesis.significance}</div>
                      <div className="text-sm text-orange-700">Significance</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">Variables</h4>
                      <div className="space-y-2 text-sm">
                        {hypothesis.variables.independent.map((variable, i) => (
                          <div key={i} className="p-2 bg-blue-50 rounded">
                            <strong>Independent:</strong> {variable.name}
                          </div>
                        ))}
                        {hypothesis.variables.dependent.map((variable, i) => (
                          <div key={i} className="p-2 bg-green-50 rounded">
                            <strong>Dependent:</strong> {variable.name}
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Methodology</h4>
                      <div className="space-y-1">
                        {hypothesis.suggestedMethodology.map((method, i) => (
                          <Badge key={i} variant="secondary" className="mr-1 mb-1">
                            {method}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Expected Outcomes:</strong>
                        <ul className="list-disc list-inside mt-1 text-gray-700">
                          {hypothesis.expectedOutcomes.map((outcome, i) => (
                            <li key={i}>{outcome}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong>Timeline:</strong>
                        <p className="mt-1 text-gray-700">{hypothesis.timeline}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {gaps.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Target className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Research Gaps Available</h3>
            <p className="text-gray-500">
              Please analyze your documents for research gaps first before generating hypotheses.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
