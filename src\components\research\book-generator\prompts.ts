import { BookMetadata, BookContext, ChapterOutline, UserChapter } from './types';

// Outline generation prompts
export const OUTLINE_GENERATION_PROMPTS = {
  // Generate comprehensive outlines for all chapters
  generateAllOutlines: (metadata: BookMetadata, userChapters: UserChapter[]) =>
    `Create HIGHLY SPECIALIZED, TOPIC-SPECIFIC outlines for all chapters of the book "${metadata.title}".

BOOK METADATA:
- Title: ${metadata.title}
- Genre: ${metadata.genre}
- Target Audience: ${metadata.targetAudience}
- Tone: ${metadata.tone}
- Description: ${metadata.description}
- Keywords: ${metadata.keywords?.join(', ') || 'Not provided'}

CHAPTER DETAILS:
${userChapters.map((ch, index) =>
  `CHAPTER ${index + 1}: ${ch.outline.title}
  Description: ${ch.outline.description}
  User Content: ${ch.items.map(item => item.content).join(' | ')}
  Estimated Words: ${ch.outline.estimatedWordCount}`
).join('\n\n')}

ABSOLUTE REQUIREMENTS:

1. TOPIC SPECIFICITY: Each outline MUST be UNIQUELY created for its SPECIFIC topic. Every section title must contain terminology directly related to that chapter's topic. DO NOT use generic templates or standard section names under any circumstances.

2. SPECIALIZED EXPERTISE: Write as if you are a subject matter expert in each specific topic. Use field-appropriate terminology and concepts throughout all section titles and descriptions.

3. CUSTOM SECTION TITLES: Every section title must be highly specific to the chapter topic. Examples of BAD generic titles to AVOID: "Introduction," "Background," "Overview," "Analysis," "Conclusion." Instead, use titles that directly incorporate topic-specific terms and concepts.

4. TOPIC-SPECIFIC CONTENT: Every section description must explain the SPECIFIC content that will be covered relevant to that exact topic - not general descriptions that could apply to any subject.

5. RELEVANT KEY POINTS: All key points must directly reference concepts, ideas, or elements specific to the chapter topic - not generic learning objectives.

6. SPECIALIZED STRUCTURE: Create 8-15 main sections per chapter, each with 3-5 key points that demonstrate deep knowledge of the specific subject matter.

7. CONTENT INCORPORATION: Explicitly incorporate terminology and ideas from the user-provided content into section titles and descriptions.

8. KEYWORD UTILIZATION: Integrate the book's keywords naturally into section titles and descriptions where relevant.

9. SPECIALIZED LANGUAGE: Use precise, field-specific terminology appropriate for the topic throughout all sections.

10. DETAILED ESTIMATES: Provide realistic word count estimates for each section based on the complexity of that specific topic element (300-800 words per section).

SIMPLIFIED JSON STRUCTURE:
{
  "chapters": [
    {
      "title": "CHAPTER TITLE",
      "description": "CHAPTER DESCRIPTION",
      "sections": [
        {
          "title": "SECTION TITLE - MUST BE TOPIC SPECIFIC",
          "description": "SECTION DESCRIPTION",
          "level": 1,
          "order": 1,
          "estimatedWordCount": 400,
          "keyPoints": ["KEY POINT 1", "KEY POINT 2", "KEY POINT 3"]
        },
        // Additional sections...
      ],
      "estimatedWordCount": 3000,
      "keyPoints": ["CHAPTER KEY POINT 1", "CHAPTER KEY POINT 2"]
    }
    // Additional chapters...
  ]
}`,

  // Generate outline for a single chapter
  generateChapterOutline: (metadata: BookMetadata, userChapter: UserChapter, previousChapters: string[]) =>
    `Create a HIGHLY SPECIALIZED, TOPIC-SPECIFIC outline for Chapter ${userChapter.outline.order}: "${userChapter.outline.title}".

BOOK CONTEXT:
- Title: ${metadata.title}
- Genre: ${metadata.genre}
- Target Audience: ${metadata.targetAudience}
- Tone: ${metadata.tone}
- Keywords: ${metadata.keywords?.join(', ') || 'Not provided'}
- Description: ${metadata.description}

CHAPTER DETAILS:
- Title: ${userChapter.outline.title}
- Description: ${userChapter.outline.description}
- Estimated Word Count: ${userChapter.outline.estimatedWordCount}
- User Content: ${userChapter.items.map(item => `${item.type}: ${item.content}`).join('\n')}

Previous Chapters:
${previousChapters.length > 0 ? previousChapters.join('\n') : 'This is the first chapter.'}

ABSOLUTE REQUIREMENTS:

1. SUBJECT MATTER EXPERTISE: Write as if you are a recognized expert in this specific topic. Use specialized terminology and concepts appropriate to this exact field.

2. TOPIC-SPECIFIC SECTION TITLES: Every section title MUST directly incorporate terminology from the specific chapter topic. AVOID generic titles like "Introduction," "Background," "Analysis," "Methods," or "Conclusion" - instead create titles that would ONLY make sense for this specific topic.

3. SPECIALIZED LANGUAGE: Use precise, field-specific terminology throughout all section titles, descriptions, and key points that demonstrates deep understanding of this exact subject matter.

4. CUSTOM STRUCTURE: Create 8-15 main sections with titles that DIRECTLY reference concepts from the chapter topic. Each section should have:
   - A detailed description explaining the SPECIFIC content related to this topic
   - 3-5 key points that demonstrate specialized knowledge of this subject
   - Realistic word count estimate (300-800 words per section)

5. USER CONTENT INTEGRATION: Explicitly incorporate terminology, concepts, and ideas from the user-provided content into section titles and descriptions.

6. KEYWORD UTILIZATION: Integrate relevant book keywords naturally into section titles and descriptions.

7. SPECIALIZED PROGRESSION: Structure the outline to build specialized knowledge in a way that makes sense for this specific topic - not following generic academic patterns.

8. TOPIC-SPECIFIC KEY POINTS: Every key point must directly reference specific concepts, ideas, or elements from the chapter topic - not generic learning objectives.

9. TECHNICAL PRECISION: Demonstrate technical accuracy and precision in how you describe specialized concepts related to this topic.

10. AVOID ALL TEMPLATES: Do not use any standardized outline structures. The outline structure itself should reflect the unique nature of this specific topic.

SIMPLIFIED JSON STRUCTURE:
{
  "title": "CHAPTER TITLE",
  "description": "CHAPTER DESCRIPTION",
  "sections": [
    {
      "title": "SECTION TITLE - MUST BE TOPIC SPECIFIC",
      "description": "SECTION DESCRIPTION",
      "level": 1,
      "order": 1,
      "estimatedWordCount": 400,
      "keyPoints": ["KEY POINT 1", "KEY POINT 2", "KEY POINT 3"]
    }
    // Additional sections...
  ],
  "estimatedWordCount": 3000,
  "keyPoints": ["CHAPTER KEY POINT 1", "CHAPTER KEY POINT 2"]
}

REMEMBER: This outline must show deep SUBJECT-SPECIFIC expertise that would only apply to this exact topic.`
};

// Book section generation prompts
export const BOOK_SECTION_PROMPTS = {
  metadata: (metadata: BookMetadata) => 
    `Book Title: ${metadata.title}
     ${metadata.subtitle ? `Subtitle: ${metadata.subtitle}` : ''}
     Genre: ${metadata.genre}
     Target Audience: ${metadata.targetAudience}
     Tone: ${metadata.tone}
     Keywords: ${metadata.keywords.join(', ')}
     Description: ${metadata.description}`,

  preface: (metadata: BookMetadata) =>
    `Write a compelling preface for the book "${metadata.title}" in the ${metadata.genre} genre, targeting ${metadata.targetAudience}. 
    
    The preface should:
    - Explain why you wrote this book and your personal connection to the topic
    - Set expectations for what readers will gain
    - Acknowledge key influences or inspirations
    - Use a ${metadata.tone} tone throughout
    - Be approximately 500-1000 words
    
    Book description: ${metadata.description}
    
    Write only the main body content without any section headings or titles.`,

  foreword: (metadata: BookMetadata) =>
    `Write a professional foreword for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    The foreword should:
    - Be written from the perspective of a respected expert in the field
    - Endorse the book's value and the author's expertise
    - Highlight the book's unique contributions
    - Explain why this book is needed now
    - Use a ${metadata.tone} tone
    - Be approximately 300-800 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    
    Write only the main body content without any section headings or titles.`,

  introduction: (metadata: BookMetadata, context?: BookContext) =>
    `Write a comprehensive introduction for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    The introduction should:
    - Hook the reader with an engaging opening
    - Clearly state the book's purpose and objectives
    - Outline what readers will learn or gain
    - Provide a roadmap of the book's structure
    - Establish credibility and context
    - Use a ${metadata.tone} tone throughout
    - Be approximately 1000-2000 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    ${context ? `Book outline: ${context.bookOutline}` : ''}
    
    Write only the main body content without any section headings or titles.`,

  chapter: (
    metadata: BookMetadata, 
    chapterOutline: ChapterOutline, 
    context?: BookContext,
    userContent?: string
  ) => {
    const contextSection = context && context.previousChapters.length > 0 
      ? `
    PREVIOUS CHAPTERS CONTEXT:
    ${context.previousChapters.map(ch => 
      `Chapter: ${ch.chapterId}
      Summary: ${ch.summary}
      Key Points: ${ch.keyPoints.join(', ')}`
    ).join('\n\n')}
    
    BOOK OUTLINE:
    ${context.bookOutline}
    ` 
      : context?.bookOutline ? `BOOK OUTLINE:\n${context.bookOutline}` : '';

    const userContentSection = userContent 
      ? `
    USER PROVIDED CONTENT:
    ${userContent}
    
    Please incorporate and expand upon this user content while maintaining narrative flow.`
      : '';

    return `Write Chapter ${chapterOutline.order}: "${chapterOutline.title}" for the book "${metadata.title}".
    
    Chapter Description: ${chapterOutline.description}
    Expected Word Count: ${chapterOutline.estimatedWordCount} words
    
    Chapter Structure:
    ${chapterOutline.subSections.map(sub => 
      `${sub.order}. ${sub.title} - ${sub.description}`
    ).join('\n')}
    
    ${contextSection}
    ${userContentSection}
    
    Requirements:
    - Write substantial, book-quality content (${chapterOutline.estimatedWordCount} words minimum)
    - Use hierarchical headings for sub-sections (## for main sections, ### for subsections)
    - Maintain consistency with previous chapters and overall book narrative
    - Include relevant examples, case studies, or illustrations where appropriate
    - Use a ${metadata.tone} tone throughout
    - Include proper citations where academic references would be appropriate
    - Ensure smooth transitions between sections
    - Target audience: ${metadata.targetAudience}
    
    Write only the main chapter content without the chapter title heading.`;
  },

  conclusion: (metadata: BookMetadata, context?: BookContext) => {
    const contextSection = context && context.previousChapters.length > 0
      ? `
    BOOK CONTENT SUMMARY:
    ${context.previousChapters.map(ch => 
      `Chapter: ${ch.chapterId}
      Key Points: ${ch.keyPoints.join(', ')}`
    ).join('\n\n')}
    
    Total Word Count: ${context.totalWordCount} words`
      : '';

    return `Write a compelling conclusion for the book "${metadata.title}" in the ${metadata.genre} genre.
    
    ${contextSection}
    
    The conclusion should:
    - Synthesize the main themes and insights from all chapters
    - Reinforce the book's key messages and takeaways
    - Provide actionable next steps for readers
    - Address the book's original objectives stated in the introduction
    - End with a memorable and inspiring final thought
    - Use a ${metadata.tone} tone throughout
    - Be approximately 1000-2000 words
    
    Book description: ${metadata.description}
    Target audience: ${metadata.targetAudience}
    
    Write only the main body content without any section headings or titles.`;
  },

  appendix: (metadata: BookMetadata, appendixTitle: string, appendixDescription: string) =>
    `Write an appendix section titled "${appendixTitle}" for the book "${metadata.title}".
    
    Description: ${appendixDescription}
    
    The appendix should:
    - Provide supplementary material that supports the main content
    - Be well-organized and easy to reference
    - Include detailed information that would interrupt the flow if placed in main chapters
    - Use clear formatting and structure
    - Be approximately 500-1500 words
    
    Target audience: ${metadata.targetAudience}
    Tone: ${metadata.tone}
    
    Write only the main content without any section headings or titles.`,

  // Summary generation for context management
  chapterSummary: (chapterTitle: string, chapterContent: string) =>
    `Create a concise summary of the following chapter content for use in maintaining context for subsequent chapters.
    
    Chapter Title: ${chapterTitle}
    
    Chapter Content:
    ${chapterContent}
    
    Requirements:
    - Maximum 400 words
    - Focus on key concepts, main arguments, and important conclusions
    - Include 3-5 bullet points of the most important takeaways
    - Maintain the essential information needed for narrative continuity
    - Use clear, concise language
    
    Format your response as:
    Summary: [Your summary here]
    
    Key Points:
    • [Point 1]
    • [Point 2]
    • [Point 3]
    • [Point 4]
    • [Point 5]`,

  // Glossary generation
  glossary: (metadata: BookMetadata, allChapterContent: string) =>
    `Generate a comprehensive glossary for the book "${metadata.title}" based on the following content.
    
    Book Content:
    ${allChapterContent}
    
    Requirements:
    - Extract 15-30 key terms that readers might need defined
    - Provide clear, concise definitions (1-3 sentences each)
    - Focus on technical terms, jargon, or concepts specific to the book's subject
    - Arrange alphabetically
    - Use language appropriate for ${metadata.targetAudience}
    
    Format each entry as:
    **Term**: Definition here.
    
    Write only the glossary entries without any introductory text or headings.`,

  // Bibliography generation
  bibliography: (allCitations: string[]) =>
    `Generate a properly formatted bibliography based on the following citations found throughout the book:
    
    Citations:
    ${allCitations.join('\n')}
    
    Requirements:
    - Convert in-text citations to full bibliographic entries
    - Use consistent academic formatting (APA style preferred)
    - Arrange alphabetically by author surname
    - Include all necessary publication details
    - Ensure proper formatting for different source types (books, journals, websites, etc.)
    
    Write only the bibliography entries without any introductory text or headings.`
};

// Text placeholders for different content types
export const TEXT_PLACEHOLDERS = {
  chapter: "Describe the main concepts, arguments, and insights for this chapter. Include examples, case studies, or personal experiences that illustrate your points...",
  preface: "Share your personal motivation for writing this book and what readers can expect to gain...",
  introduction: "Provide an overview of the book's purpose, scope, and structure. Hook the reader with compelling opening thoughts...",
  conclusion: "Synthesize the main themes and provide actionable takeaways for readers...",
  appendix: "Include supplementary material, detailed examples, or reference information that supports your main content..."
};

// Figure placeholders for different content types
export const FIGURE_PLACEHOLDERS = {
  chapter: "Describe how this visual supports the chapter's main concepts or provides illustrative examples...",
  introduction: "Explain how this figure sets the context or introduces key concepts for the entire book...",
  conclusion: "Use this visual to summarize key insights or provide a memorable closing illustration..."
};
