# AI Tutor Setup Guide

A comprehensive AI-powered tutoring platform with chat, quiz generation, and document analysis capabilities.

## ✨ Features

### 🎯 **Core Features (Works Immediately)**
- ✅ **AI Chat Tutoring** - Ask questions on any topic
- ✅ **Beautiful Quiz Generation** - Create interactive quizzes from AI knowledge, documents, or web search
- ✅ **PDF Document Processing** - Upload and analyze PDF documents
- ✅ **Education Level Adaptation** - Automatically adjusts to student level
- ✅ **Code Execution & Visualizations** - Interactive STEM examples
- ✅ **Web Search Integration** - Current information from educational sources

### 🚀 **Enhanced Features**
- ✅ **Multiple Quiz Types** - Multiple choice, true/false, short answer, essay
- ✅ **Interactive Quiz Interface** - Beautiful, timed quizzes with explanations
- ✅ **Document-Based Learning** - Generate quizzes from uploaded content
- ✅ **Progress Tracking** - Monitor learning progress and quiz results
- ✅ **Streaming Responses** - Real-time AI response generation

## Quick Start (Minimum Setup)

The AI tutor works immediately with just a Gemini API key - no database or complex setup required!

### 1. Get Gemini API Key (Required)

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add to your environment variables:

```env
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
# OR
GEMINI_API_KEY=your_gemini_api_key
```

### 2. Start Using

That's it! The AI tutor will work immediately with:
- ✅ **AI Chat** - Ask questions on any topic
- ✅ **Quiz Generation** - Create beautiful interactive quizzes
- ✅ **PDF Processing** - Upload and analyze documents
- ✅ **Education Level Adaptation** - Automatic difficulty adjustment
- ✅ **Code Execution** - Interactive STEM examples
- ✅ **Streaming Responses** - Real-time AI interaction

## 🎯 How to Use

### **Chat Mode**
1. Select your education level
2. Ask any question about any subject
3. Enable code execution for STEM topics
4. Get real-time streaming responses

### **Quiz Mode**
1. Click the "Quiz" tab
2. Choose your quiz source:
   - **AI Knowledge** - Generate from AI's knowledge base
   - **Document Content** - Upload a PDF and generate quiz from it
   - **Web Search** - Get current information and create quiz
   - **Mixed Sources** - Combine document and web search
3. Enter your topic (e.g., "Photosynthesis", "World War II", "Calculus")
4. Configure settings:
   - Education level and difficulty
   - Number of questions and time limit
   - Question types (multiple choice, true/false, short answer)
   - Include explanations and hints
5. Generate and take your quiz!

### **Document Mode**
1. Click the "Documents" tab
2. Upload PDF files
3. The system will extract text automatically
4. Use the content for chat or quiz generation

## Enhanced Setup (Optional)

For additional features, you can add these optional services:

### Web Search Integration (Optional)

Enable real-time web search for current information:

1. Get API key from [Tavily](https://tavily.com)
2. Add to environment:

```env
NEXT_PUBLIC_TAVILY_API_KEY=your_tavily_api_key
# OR
TAVILY_API_KEY=your_tavily_api_key
```

### Document Storage & Chat History (Optional)

For document uploads and chat history:

1. Set up a Supabase project
2. Add to environment:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

3. Run the SQL schema in your Supabase project:

```sql
-- Copy and paste the contents of database/schema.sql
-- This creates tables for chat sessions, messages, and document storage
```

## Features by Setup Level

### Basic Setup (Gemini API only)
- ✅ AI tutoring for any subject
- ✅ Education level adaptation (Elementary to Graduate)
- ✅ Code execution with visualizations
- ✅ Interactive examples and explanations
- ✅ Real-time streaming responses

### Enhanced Setup (+ Tavily API)
- ✅ All basic features
- ✅ Web search for current information
- ✅ Academic source integration
- ✅ Real-world examples and applications

### Full Setup (+ Supabase)
- ✅ All enhanced features
- ✅ Document upload and analysis
- ✅ Chat history and session management
- ✅ Progress tracking
- ✅ User authentication

## Usage Examples

### Basic Math Tutoring
```
User: "Explain calculus derivatives for high school level"
AI: Provides step-by-step explanation with examples
```

### Science with Code Execution
```
User: "How does photosynthesis work?" (with code execution enabled)
AI: Explains process + generates interactive diagram
```

### Current Events Research
```
User: "Latest developments in renewable energy?" (with web search)
AI: Searches recent sources + provides comprehensive answer
```

## Troubleshooting

### "AI service is currently unavailable"

This means the Gemini API key is not configured. Check:

1. API key is correctly set in environment variables
2. API key has the correct permissions
3. No typos in the environment variable name

### Web search not working

1. Verify Tavily API key is set
2. Check API key permissions
3. Web search is optional - AI tutoring will still work

### Database errors

1. Database is optional for basic functionality
2. Check Supabase configuration if using document features
3. Verify SQL schema has been executed

## Environment Variables Reference

```env
# Required for AI tutoring
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key

# Optional for web search
NEXT_PUBLIC_TAVILY_API_KEY=your_tavily_api_key

# Optional for document storage and chat history
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Architecture

The AI tutor is designed with a fallback strategy:

1. **Full Mode**: All APIs configured, all features available
2. **Limited Mode**: Only Gemini API, core tutoring available
3. **Graceful Degradation**: Clear instructions when services unavailable

### Key Services

- **Integrated Tutor Service**: Orchestrates all AI capabilities
- **Gemini Service**: Handles AI responses with code execution
- **Tavily Service**: Manages web search (optional)
- **Database Service**: Document and chat storage (optional)

### Components

- **FlexibleTutorChat**: Main interface (works with minimal setup)
- **DocumentUploader**: Optional document analysis
- **Settings**: Configure education level and features

## Development Notes

- The platform prioritizes working out-of-the-box
- Graceful degradation when services are unavailable
- Clear setup instructions provided in the UI
- No complex configuration required for basic functionality
