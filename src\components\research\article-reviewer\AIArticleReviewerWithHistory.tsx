import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileCheck, 
  FileX, 
  Upload, 
  FileText, 
  CheckCircle, 
  Eye, 
  AlertCircle, 
  FileSearch, 
  Highlighter, 
  Lightbulb, 
  Book,
  History,
  Plus,
  Save
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  ArticleUploader,
  ArticleReviewResult,
  ArticleReviewProgress,
  AIModelSelector,
  ReviewScorecard,
  ArticleReviewHistory,
  SavedReviewDetail
} from './components';
import { ArticleSection } from './types';
import { useArticleReview } from './hooks';
import { articleReviewStorageService, articleFileStorageService } from './services';
import { useAuth } from '@/contexts/AuthContext';


type ViewMode = 'new-review' | 'history' | 'review-detail';

/**
 * Enhanced AI Article Reviewer Component with History
 * 
 * This component allows users to upload academic papers for AI-powered review,
 * save reviews to database, and manage review history.
 */
export function AIArticleReviewerWithHistory() {
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<ViewMode>('new-review');
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [reviewStartTime, setReviewStartTime] = useState<number | null>(null);

  // Get review functionality from the hook
  const {
    articleFile,
    setArticleFile,
    parsedContent,
    reviewResults,
    currentSection,
    isAnalyzing,
    progress,
    selectedModel,
    availableModels,
    setSelectedModel,
    startReview,
    cancelReview,
  } = useArticleReview();
  
  // Active tab for the new review UI
  const [activeTab, setActiveTab] = useState<'upload' | 'review'>('upload');

  // Handle review completion to switch to results tab and save
  useEffect(() => {
    if (reviewResults && Object.keys(reviewResults).length > 0 && !isSaving) {
      setActiveTab('review');
      handleSaveReview();
    }
  }, [reviewResults]);

  // Track review start time
  useEffect(() => {
    if (isAnalyzing && !reviewStartTime) {
      setReviewStartTime(Date.now());
    } else if (!isAnalyzing && reviewStartTime) {
      setReviewStartTime(null);
    }
  }, [isAnalyzing]);

  const handleSaveReview = async () => {
    if (!user || !parsedContent || !reviewResults || isSaving) {
      return;
    }

    try {
      setIsSaving(true);
      
      // Calculate processing time
      const processingTime = reviewStartTime 
        ? Math.round((Date.now() - reviewStartTime) / 1000)
        : undefined;

      // Save the review to database
      const { data: savedReview, error } = await articleReviewStorageService.saveArticleReview(
        parsedContent,
        reviewResults,
        selectedModel,
        processingTime
      );

      if (error) {
        console.error('Error saving review:', error);
        toast.error('Failed to save review to history');
        return;
      }

      if (savedReview) {
        toast.success('Review saved to history successfully!');
        
        // Optionally upload the file to storage
        if (articleFile) {
          try {
            const { filePath, error: uploadError } = await articleFileStorageService.uploadArticleFile(
              articleFile,
              user.id,
              savedReview.id
            );

            if (uploadError) {
              console.error('Error uploading file:', uploadError);
              // Don't show error to user as the review is already saved
            } else if (filePath) {
              // Update the review with the file path
              // This could be done with an update call if needed
              console.log('File uploaded successfully:', filePath);
            }
          } catch (uploadError) {
            console.error('Error uploading file:', uploadError);
          }
        }
      }
    } catch (error) {
      console.error('Error saving review:', error);
      toast.error('Failed to save review');
    } finally {
      setIsSaving(false);
    }
  };

  const handleViewReview = (reviewId: string) => {
    setSelectedReviewId(reviewId);
    setViewMode('review-detail');
  };

  const handleCreateNew = () => {
    setViewMode('new-review');
    setActiveTab('upload');
    // Reset the review state if needed
    setArticleFile(null);
  };

  const handleBackToHistory = () => {
    setViewMode('history');
    setSelectedReviewId(null);
  };

  const handleBackToNew = () => {
    setViewMode('new-review');
  };

  // Render based on current view mode
  if (viewMode === 'history') {
    return (
      <div className="flex flex-col h-screen p-6 bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="bg-gradient-to-br from-blue-600 to-indigo-700 p-3 rounded-xl shadow-md mr-4 transform -rotate-3">
              <History className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Article Review History
              </h1>
              <p className="text-sm text-gray-600">
                View and manage your saved article reviews
              </p>
            </div>
          </div>
          
          <Button onClick={handleCreateNew} className="bg-gradient-to-r from-blue-600 to-indigo-600">
            <Plus className="h-4 w-4 mr-2" />
            New Review
          </Button>
        </div>
        
        <ArticleReviewHistory 
          onViewReview={handleViewReview}
          onCreateNew={handleCreateNew}
        />
      </div>
    );
  }

  if (viewMode === 'review-detail' && selectedReviewId) {
    return (
      <div className="flex flex-col h-screen p-6 bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 overflow-y-auto">
        <SavedReviewDetail 
          reviewId={selectedReviewId}
          onBack={handleBackToHistory}
        />
      </div>
    );
  }

  // Default: New Review Mode
  return (
    <div className="flex flex-col h-screen p-6 bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 overflow-y-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="bg-gradient-to-br from-blue-600 to-indigo-700 p-3 rounded-xl shadow-md mr-4 transform -rotate-3">
            <FileCheck className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
              AI Article Reviewer
            </h1>
            <p className="text-sm text-gray-600 flex items-center">
              Powered by <span className="font-medium ml-1 bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">{selectedModel.split('/').pop()?.toUpperCase() || 'AI'}</span>
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Status badges */}
          {isAnalyzing && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 shadow-sm">
              <Eye className="h-3 w-3 mr-1 animate-pulse" />
              Analyzing Article
            </Badge>
          )}
          
          {reviewResults && (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 shadow-sm">
              <CheckCircle className="h-3 w-3 mr-1" />
              Review Complete
              {isSaving && <Save className="h-3 w-3 ml-1 animate-spin" />}
            </Badge>
          )}
          
          {!isAnalyzing && !reviewResults && articleFile && (
            <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 shadow-sm">
              <AlertCircle className="h-3 w-3 mr-1" />
              Ready to Analyze
            </Badge>
          )}

          <Button
            variant="outline"
            onClick={() => setViewMode('history')}
            className="flex items-center gap-2"
          >
            <History className="h-4 w-4" />
            History
          </Button>


        </div>
      </div>
      
      <div className="text-gray-700 mb-6 bg-gradient-to-r from-blue-50/80 to-indigo-50/50 p-6 rounded-xl shadow-sm border border-blue-200/30">
        <div className="mb-4 border-l-4 border-blue-500 pl-4 pb-1">
          <p className="text-xl font-medium bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
            In-depth Academic Paper Analysis
          </p>
          <p className="text-sm text-gray-600">
            Upload your research paper for a comprehensive AI-powered review with detailed sentence-level insights
          </p>
        </div>
        <p className="mb-4 text-gray-700 leading-relaxed">
          Our sophisticated AI reviewer analyzes your academic paper sequentially from introduction to conclusion, providing 
          detailed feedback on structure, clarity, academic rigor, and logical flow. Benefit from <span className="text-indigo-700 font-medium">sentence-by-sentence analysis</span> that 
          identifies specific problems and offers actionable suggestions for improvement. <span className="text-green-700 font-medium">All reviews are automatically saved to your history.</span>
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-5">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 p-4 rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center mb-2">
              <div className="p-1.5 rounded-md bg-blue-100/80 shadow-inner">
                <FileSearch className="h-4 w-4 text-blue-700" />
              </div>
              <span className="ml-2 font-medium text-blue-800">Sequential Analysis</span>
            </div>
            <p className="text-xs text-blue-700 leading-relaxed">Step-by-step review of each section, from introduction to conclusion, with context from previous sections</p>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100/50 p-4 rounded-lg border border-green-200 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center mb-2">
              <div className="p-1.5 rounded-md bg-green-100/80 shadow-inner">
                <Highlighter className="h-4 w-4 text-green-700" />
              </div>
              <span className="ml-2 font-medium text-green-800">Sentence-level Feedback</span>
            </div>
            <p className="text-xs text-green-700">Identify specific problematic sentences with highlighted issues and suggestions</p>
          </div>
          <div className="bg-amber-50 p-3 rounded-lg border border-amber-100">
            <div className="flex items-center mb-2">
              <div className="p-1 rounded-md bg-amber-100">
                <CheckCircle className="h-4 w-4 text-amber-700" />
              </div>
              <span className="ml-2 font-medium text-amber-800">Academic Rigor</span>
            </div>
            <p className="text-xs text-amber-700">Assessment of methodology, citation practices, and logical consistency</p>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg border border-purple-100">
            <div className="flex items-center mb-2">
              <div className="p-1 rounded-md bg-purple-100">
                <CheckCircle className="h-4 w-4 text-purple-700" />
              </div>
              <span className="ml-2 font-medium text-purple-800">Saved History</span>
            </div>
            <p className="text-xs text-purple-700">All reviews automatically saved with searchable history and detailed analytics</p>
          </div>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'upload' | 'review')} className="w-full">
        <TabsList className={`grid w-full ${process.env.NODE_ENV === 'development' ? 'grid-cols-3' : 'grid-cols-2'} mb-6`}>
          <TabsTrigger value="upload" disabled={isAnalyzing}>
            <Upload className="h-4 w-4 mr-2" /> Upload Article
          </TabsTrigger>
          <TabsTrigger value="review" disabled={!reviewResults && !isAnalyzing}>
            <FileText className="h-4 w-4 mr-2" /> Review Results
          </TabsTrigger>

        </TabsList>
        
        <TabsContent value="upload" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left column: Upload panel */}
            <div className="md:col-span-2">
              <ArticleUploader 
                articleFile={articleFile}
                setArticleFile={setArticleFile}
                parsedContent={parsedContent}
                onStartReview={startReview}
                isAnalyzing={isAnalyzing}
                onCancelReview={cancelReview}
              />
            </div>
            
            {/* Right column: Model selection and settings */}
            <div>
              <AIModelSelector 
                selectedModel={selectedModel}
                availableModels={availableModels}
                setSelectedModel={setSelectedModel}
                isDisabled={isAnalyzing}
              />
              
              {isAnalyzing && (
                <Card className="mt-6">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium flex items-center">
                      <Eye className="h-4 w-4 mr-2 text-blue-500" />
                      Review Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ArticleReviewProgress 
                      currentSection={currentSection}
                      progress={progress}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="review" className="mt-0">
          {reviewResults ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Left sidebar: Review scorecard */}
              <div className="md:col-span-1">
                <ReviewScorecard 
                  results={reviewResults} 
                />
              </div>
              
              {/* Main content: Detailed review results */}
              <div className="md:col-span-3">
                <ArticleReviewResult 
                  articleFile={articleFile} 
                  reviewResults={reviewResults}
                />
              </div>
            </div>
          ) : isAnalyzing ? (
            <div className="p-6 text-center">
              <ArticleReviewProgress 
                currentSection={currentSection}
                progress={progress}
              />
            </div>
          ) : (
            <div className="p-6 text-center">
              <FileX className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium">No Review Results Yet</h3>
              <p className="text-gray-500 mt-2">
                Upload an article and start the review process to see results here.
              </p>
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  );
}
