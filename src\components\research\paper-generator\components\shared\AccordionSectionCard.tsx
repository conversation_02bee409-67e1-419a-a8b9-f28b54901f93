import React, { useState, useEffect } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { FileText, BookOpen, Quote, ExternalLink, AlertOctagon, MessageSquare } from "lucide-react";
import { GeneratedSection, Citation, PaperSection } from './types';
import { extractCitationsFromText } from './citation-extraction.enhanced';
import { SectionCitationDisplay } from './SectionCitationDisplay';
import { ReferencesDisplay } from './ReferencesDisplay';
import { formatSectionContentForDisplay, separateSectionReferences } from './section-utils';

interface AccordionSectionCardProps {
  section: GeneratedSection;
  allCitations?: Citation[];
  sectionCitations?: Record<string, string[]>;
  sectionData?: PaperSection; // Optional section data with prompt information
  onRegenerateSection?: (sectionId: string, prompt: string) => void;
}

export const AccordionSectionCard: React.FC<AccordionSectionCardProps> = ({
  section,
  allCitations = [],
  sectionCitations = {},
  sectionData,
  onRegenerateSection
}) => {
  if (!section.content) return null;
  
  // Format the section content for display, potentially removing references section
  const { displayContent, hasHiddenReferences } = formatSectionContentForDisplay(section);
  
  // Extract any references from section content
  const { extractedReferences } = separateSectionReferences(section.content || '');
  
  // Don't show citations for references section
  const showCitations = section.id !== 'references' && 
    sectionCitations[section.id]?.length > 0;
  
  // Special handling for references section
  const isReferencesSection = section.id === 'references';
  
  return (
    <Accordion type="single" collapsible className="border border-gray-200 rounded-xl bg-white">
      <AccordionItem value={section.id} className="border-none">
        <AccordionTrigger className="px-6 py-3 hover:no-underline hover:bg-gray-50">
          <div className="flex items-center gap-3">
            <section.icon className="h-5 w-5 text-gray-600" />
            <h3 className="font-semibold text-lg">{section.name}</h3>
            {showCitations && (
              <Badge variant="secondary" className="ml-2">
                {sectionCitations[section.id]?.length || 0} citation{(sectionCitations[section.id]?.length || 0) !== 1 ? 's' : ''}
              </Badge>
            )}
            {isReferencesSection && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 ml-2">
                {allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} references
              </Badge>
            )}
            {hasHiddenReferences && !isReferencesSection && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 ml-2">
                {extractedReferences.length} embedded ref{extractedReferences.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-6 pb-4">
          {isReferencesSection ? (
            // Use ReferencesDisplay for the references section
            <ReferencesDisplay 
              referencesContent={section.content}
              citations={allCitations}
            />
          ) : (
            // For non-reference sections, use a tabbed layout for different content types
            <Tabs defaultValue="content" className="mt-2">
              <TabsList className="mb-2">
                <TabsTrigger value="content">
                  <FileText className="mr-2 h-4 w-4" />
                  Content
                </TabsTrigger>
                {sectionData && (
                  <TabsTrigger value="prompt">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Prompt & Details
                  </TabsTrigger>
                )}
                {showCitations && (
                  <TabsTrigger value="citations">
                    <Quote className="mr-2 h-4 w-4" />
                    Citations ({sectionCitations[section.id]?.length || 0})
                  </TabsTrigger>
                )}
                {hasHiddenReferences && (
                  <TabsTrigger value="section-refs">
                    <BookOpen className="mr-2 h-4 w-4" />
                    Embedded References ({extractedReferences.length})
                  </TabsTrigger>
                )}
              </TabsList>
              
              {/* Content tab - show the main content without references section */}
              <TabsContent value="content" className="mt-0">
                <div className="text-gray-700 whitespace-pre-line bg-white p-4 rounded-md border">
                  {displayContent}
                </div>
              </TabsContent>

              {/* Prompt & Details tab - show the prompt and generation details */}
              {sectionData && (
                <TabsContent value="prompt" className="mt-0">
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-md border">
                      <h4 className="font-medium text-gray-900 mb-2">AI Prompt Used</h4>
                      <div className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                        {sectionData.prompt_text}
                      </div>
                    </div>

                    {sectionData.generation_metadata && Object.keys(sectionData.generation_metadata).length > 0 && (
                      <div className="bg-blue-50 p-4 rounded-md border">
                        <h4 className="font-medium text-gray-900 mb-2">Generation Details</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          {Object.entries(sectionData.generation_metadata).map(([key, value]) => (
                            <div key={key}>
                              <span className="font-medium text-gray-600">{key}:</span>{' '}
                              <span className="text-gray-800">{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {onRegenerateSection && (
                      <div className="flex justify-end">
                        <button
                          onClick={() => onRegenerateSection(section.id, sectionData.prompt_text)}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          Regenerate Section
                        </button>
                      </div>
                    )}
                  </div>
                </TabsContent>
              )}
              
              {/* Citations tab - show citations found in this section */}
              {showCitations && (
                <TabsContent value="citations" className="mt-0">
                  <SectionCitationDisplay
                    sectionId={section.id}
                    sectionName={section.name}
                    allCitations={allCitations}
                    sectionCitations={sectionCitations}
                    className="mt-0"
                  />
                </TabsContent>
              )}
              
              {/* Embedded references tab - show references found within this section */}
              {hasHiddenReferences && (
                <TabsContent value="section-refs" className="mt-0">
                  <div className="border border-gray-100 rounded-lg p-4 bg-white">
                    <div className="flex items-center gap-2 text-amber-600 mb-3">
                      <AlertOctagon className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        References embedded in this section will be automatically consolidated in the final References section
                      </span>
                    </div>
                    <div className="space-y-4">
                      {extractedReferences.map((reference, idx) => (
                        <div key={idx} className="text-sm border-l-2 border-blue-200 pl-3 py-1 bg-gray-50">
                          {reference}
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              )}
            </Tabs>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
