import { supabase } from '@/lib/supabase';
import { Database } from '@/lib/database.types';
import { BookMetadata, GeneratedChapter, Citation } from '@/components/research/book-generator/types';

// Type definitions for book history
type UserBook = Database['public']['Tables']['user_books']['Row'];
type UserBookInsert = Database['public']['Tables']['user_books']['Insert'];
type UserBookUpdate = Database['public']['Tables']['user_books']['Update'];

type BookChapter = Database['public']['Tables']['book_chapters']['Row'];
type BookChapterInsert = Database['public']['Tables']['book_chapters']['Insert'];
type BookChapterUpdate = Database['public']['Tables']['book_chapters']['Update'];

type BookSection = Database['public']['Tables']['book_sections']['Row'];
type BookSectionInsert = Database['public']['Tables']['book_sections']['Insert'];

type BookExport = Database['public']['Tables']['book_exports']['Row'];
type BookExportInsert = Database['public']['Tables']['book_exports']['Insert'];

type BookActivity = Database['public']['Tables']['book_activity']['Row'];

export interface BookWithDetails extends UserBook {
  chapters?: BookChapter[];
  sections?: BookSection[];
  exports?: BookExport[];
  recent_activity?: BookActivity[];
}

export interface CreateBookData {
  metadata: BookMetadata;
  chapters: GeneratedChapter[];
  citations?: Citation[];
}

export interface BookHistoryFilters {
  genre?: string;
  status?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'created_at' | 'updated_at' | 'title' | 'total_word_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

class BookHistoryService {
  /**
   * Create a new book record with metadata
   */
  async createBook(data: CreateBookData): Promise<{ book: UserBook | null; error: any }> {
    try {
      const { metadata, chapters, citations = [] } = data;
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { book: null, error: 'User not authenticated' };
      }

      // Create book record
      const bookData: UserBookInsert = {
        user_id: user.id,
        title: metadata.title,
        subtitle: metadata.subtitle,
        description: metadata.description,
        genre: metadata.genre,
        target_audience: metadata.targetAudience,
        authors: metadata.authors,
        keywords: metadata.keywords,
        estimated_length: metadata.estimatedLength,
        tone: metadata.tone,
        chapter_count: chapters.length,
        outline_model: metadata.outlineModel,
        content_model: metadata.contentModel,
        generation_status: 'generating',
        generation_started_at: new Date().toISOString()
      };

      const { data: book, error: bookError } = await supabase
        .from('user_books')
        .insert(bookData)
        .select()
        .single();

      if (bookError || !book) {
        return { book: null, error: bookError };
      }

      // Log creation activity
      await this.logActivity(book.id, 'created', {
        chapter_count: chapters.length,
        total_citations: citations.length
      });

      return { book, error: null };
    } catch (error) {
      console.error('Error creating book:', error);
      return { book: null, error };
    }
  }

  /**
   * Save book chapters to database
   */
  async saveBookChapters(bookId: string, chapters: GeneratedChapter[]): Promise<{ success: boolean; error: any }> {
    try {
      const chaptersData: BookChapterInsert[] = chapters.map((chapter, index) => ({
        book_id: bookId,
        chapter_number: index + 1,
        title: chapter.title,
        description: chapter.description,
        content: chapter.content,
        status: chapter.status === 'completed' ? 'completed' : 'pending',
        order_index: chapter.order,
        outline: chapter.outline ? JSON.parse(JSON.stringify(chapter.outline)) : null,
        citations: chapter.citations ? JSON.parse(JSON.stringify(chapter.citations)) : [],
        generation_metadata: {
          word_count: chapter.wordCount,
          summary: chapter.summary,
          user_approved: chapter.userApproved
        }
      }));

      const { error } = await supabase
        .from('book_chapters')
        .insert(chaptersData);

      if (error) {
        return { success: false, error };
      }

      // Log chapters saved activity
      await this.logActivity(bookId, 'chapters_saved', {
        chapter_count: chapters.length,
        completed_chapters: chapters.filter(c => c.status === 'completed').length
      });

      return { success: true, error: null };
    } catch (error) {
      console.error('Error saving book chapters:', error);
      return { success: false, error };
    }
  }

  /**
   * Update book status when generation is complete
   */
  async completeBookGeneration(bookId: string, totalWordCount: number): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await supabase
        .from('user_books')
        .update({
          generation_status: 'completed',
          generation_completed_at: new Date().toISOString(),
          total_word_count: totalWordCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', bookId);

      if (error) {
        return { success: false, error };
      }

      // Log completion activity
      await this.logActivity(bookId, 'completed', {
        total_word_count: totalWordCount,
        completion_time: new Date().toISOString()
      });

      return { success: true, error: null };
    } catch (error) {
      console.error('Error completing book generation:', error);
      return { success: false, error };
    }
  }

  /**
   * Get user's book history with optional filters
   */
  async getUserBooks(filters: BookHistoryFilters = {}): Promise<{ books: UserBook[]; error: any; total?: number }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { books: [], error: 'User not authenticated' };
      }

      let query = supabase
        .from('user_books')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id);

      // Apply filters
      if (filters.genre) {
        query = query.eq('genre', filters.genre);
      }
      if (filters.status) {
        query = query.eq('generation_status', filters.status);
      }
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data: books, error, count } = await query;

      if (error) {
        return { books: [], error };
      }

      return { books: books || [], error: null, total: count || 0 };
    } catch (error) {
      console.error('Error getting user books:', error);
      return { books: [], error };
    }
  }

  /**
   * Get detailed book information including chapters and sections
   */
  async getBookDetails(bookId: string): Promise<{ book: BookWithDetails | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { book: null, error: 'User not authenticated' };
      }

      // Get book with all related data
      const { data: book, error: bookError } = await supabase
        .from('user_books')
        .select(`
          *,
          chapters:book_chapters(*),
          sections:book_sections(*),
          exports:book_exports(*),
          recent_activity:book_activity(*)
        `)
        .eq('id', bookId)
        .eq('user_id', user.id)
        .single();

      if (bookError) {
        return { book: null, error: bookError };
      }

      // Log view activity
      await this.logActivity(bookId, 'viewed');

      return { book: book as BookWithDetails, error: null };
    } catch (error) {
      console.error('Error getting book details:', error);
      return { book: null, error };
    }
  }

  /**
   * Save book export information
   */
  async saveBookExport(
    bookId: string, 
    format: string, 
    fileName: string, 
    filePath?: string, 
    fileSize?: number,
    exportOptions?: any
  ): Promise<{ export: BookExport | null; error: any }> {
    try {
      const exportData: BookExportInsert = {
        book_id: bookId,
        export_format: format,
        file_name: fileName,
        file_path: filePath,
        file_size: fileSize,
        export_options: exportOptions ? JSON.parse(JSON.stringify(exportOptions)) : {}
      };

      const { data: exportRecord, error } = await supabase
        .from('book_exports')
        .insert(exportData)
        .select()
        .single();

      if (error) {
        return { export: null, error };
      }

      // Log export activity
      await this.logActivity(bookId, 'exported', {
        format,
        file_name: fileName,
        file_size: fileSize
      });

      return { export: exportRecord, error: null };
    } catch (error) {
      console.error('Error saving book export:', error);
      return { export: null, error };
    }
  }

  /**
   * Delete a book and all related data
   */
  async deleteBook(bookId: string): Promise<{ success: boolean; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Delete book (cascading deletes will handle related records)
      const { error } = await supabase
        .from('user_books')
        .delete()
        .eq('id', bookId)
        .eq('user_id', user.id);

      if (error) {
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting book:', error);
      return { success: false, error };
    }
  }

  /**
   * Log book activity
   */
  private async logActivity(
    bookId: string, 
    activityType: string, 
    details: any = {}
  ): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await supabase
        .from('book_activity')
        .insert({
          book_id: bookId,
          user_id: user.id,
          activity_type: activityType,
          activity_details: JSON.parse(JSON.stringify(details))
        });
    } catch (error) {
      console.error('Error logging book activity:', error);
      // Don't throw error for logging failures
    }
  }

  /**
   * Get book statistics for dashboard
   */
  async getBookStatistics(): Promise<{ stats: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { stats: null, error: 'User not authenticated' };
      }

      const { data: books, error } = await supabase
        .from('user_books')
        .select('generation_status, total_word_count, genre, created_at')
        .eq('user_id', user.id);

      if (error) {
        return { stats: null, error };
      }

      const stats = {
        total_books: books.length,
        completed_books: books.filter(b => b.generation_status === 'completed').length,
        total_words: books.reduce((sum, b) => sum + (b.total_word_count || 0), 0),
        genres: [...new Set(books.map(b => b.genre))],
        recent_books: books.filter(b => {
          const createdAt = new Date(b.created_at);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return createdAt > weekAgo;
        }).length
      };

      return { stats, error: null };
    } catch (error) {
      console.error('Error getting book statistics:', error);
      return { stats: null, error };
    }
  }
}

export const bookHistoryService = new BookHistoryService();
