import React, { useState, useEffect } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { FileText, BookOpen, Quote, ExternalLink, AlertOctagon, MessageSquare, Bot } from "lucide-react";
import { GeneratedSection, Citation, PaperSection } from './types';
import { extractCitationsFromText } from './citation-extraction.enhanced';
import { SectionCitationDisplay } from './SectionCitationDisplay';
import { ReferencesDisplay } from './ReferencesDisplay';
import { formatSectionContentForDisplay, separateSectionReferences } from './section-utils';
import { EnhancedSectionDisplay } from './components/EnhancedSectionDisplay';

interface AccordionSectionCardProps {
  section: GeneratedSection;
  allCitations?: Citation[];
  sectionCitations?: Record<string, string[]>;
  sectionData?: PaperSection; // Optional section data with prompt information
  onRegenerateSection?: (sectionId: string, prompt: string) => void;
  enhancedCitations?: any[]; // Enhanced citations from Tavily search
  useEnhancedDisplay?: boolean; // Whether to use enhanced display
}

export const AccordionSectionCard: React.FC<AccordionSectionCardProps> = ({
  section,
  allCitations = [],
  sectionCitations = {},
  sectionData,
  onRegenerateSection,
  enhancedCitations = [],
  useEnhancedDisplay = false
}) => {
  if (!section.content) return null;
  
  // Format the section content for display, potentially removing references section
  const { displayContent, hasHiddenReferences } = formatSectionContentForDisplay(section);
  
  // Extract any references from section content
  const { extractedReferences } = separateSectionReferences(section.content || '');
  
  // Don't show citations for references section
  const showCitations = section.id !== 'references' && 
    sectionCitations[section.id]?.length > 0;
  
  // Special handling for references section
  const isReferencesSection = section.id === 'references';

  // Use enhanced display if available and enabled
  if (useEnhancedDisplay && enhancedCitations.length > 0 && !isReferencesSection) {
    return (
      <EnhancedSectionDisplay
        sectionId={section.id}
        sectionName={section.name}
        content={displayContent}
        citations={enhancedCitations}
        onRegenerate={onRegenerateSection ? () => onRegenerateSection(section.id, sectionData?.prompt || '') : undefined}
      />
    );
  }

  return (
    <Accordion type="single" collapsible className="border border-gray-200 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-300">
      <AccordionItem value={section.id} className="border-none">
        <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 group">
          <div className="flex items-center gap-4 w-full">
            <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-purple-50 group-hover:from-blue-100 group-hover:to-purple-100 transition-all duration-300">
              <section.icon className="h-6 w-6 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
            </div>
            <div className="flex-1 text-left">
              <h3 className="font-bold text-xl text-gray-900 group-hover:text-blue-900 transition-colors duration-300">
                {section.name}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {isReferencesSection
                  ? `${allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} academic references compiled`
                  : `Generated with AI • ${section.content?.length || 0} characters`
                }
              </p>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              {showCitations && (
                <Badge variant="secondary" className="bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 border-blue-200 shadow-sm">
                  <FileText className="h-3 w-3 mr-1" />
                  {sectionCitations[section.id]?.length || 0} citation{(sectionCitations[section.id]?.length || 0) !== 1 ? 's' : ''}
                </Badge>
              )}
              {isReferencesSection && (
                <Badge variant="outline" className="bg-gradient-to-r from-green-50 to-green-100 text-green-700 border-green-200 shadow-sm">
                  <BookOpen className="h-3 w-3 mr-1" />
                  {allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} references
                </Badge>
              )}
              {hasHiddenReferences && !isReferencesSection && (
                <Badge variant="outline" className="bg-gradient-to-r from-amber-50 to-amber-100 text-amber-700 border-amber-200 shadow-sm">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  {extractedReferences.length} embedded ref{extractedReferences.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-6 pb-6">
          {isReferencesSection ? (
            // Use ReferencesDisplay for the references section
            <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg p-6 border border-gray-100">
              <ReferencesDisplay
                referencesContent={section.content}
                citations={allCitations}
              />
            </div>
          ) : (
            // For non-reference sections, use a tabbed layout for different content types
            <Tabs defaultValue="content" className="mt-4">
              <TabsList className="mb-4 bg-gray-100 p-1 rounded-lg">
                <TabsTrigger
                  value="content"
                  className="data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Content
                </TabsTrigger>
                {sectionData && (
                  <TabsTrigger
                    value="prompt"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Prompt & Details
                  </TabsTrigger>
                )}
                {showCitations && (
                  <TabsTrigger
                    value="citations"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    <Quote className="mr-2 h-4 w-4" />
                    Citations ({sectionCitations[section.id]?.length || 0})
                  </TabsTrigger>
                )}
                {hasHiddenReferences && (
                  <TabsTrigger
                    value="section-refs"
                    className="data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Embedded References ({extractedReferences.length})
                  </TabsTrigger>
                )}
              </TabsList>

              {/* Content tab - show the main content without references section */}
              <TabsContent value="content" className="mt-0">
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-4 py-3 border-b border-gray-200">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-700">Generated Content</span>
                      <span className="text-xs text-gray-500">• {section.content?.length || 0} characters</span>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="prose prose-gray max-w-none">
                      <div
                        className="text-gray-800 leading-relaxed whitespace-pre-line font-serif text-base"
                        style={{
                          lineHeight: '1.8',
                          fontFamily: 'Georgia, "Times New Roman", serif'
                        }}
                      >
                        {displayContent}
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Prompt & Details tab - show the prompt and generation details */}
              {sectionData && (
                <TabsContent value="prompt" className="mt-0">
                  <div className="space-y-6">
                    <div className="bg-gradient-to-br from-gray-50 to-blue-50 p-6 rounded-lg border border-gray-200 shadow-sm">
                      <div className="flex items-center gap-2 mb-4">
                        <MessageSquare className="h-5 w-5 text-blue-600" />
                        <h4 className="font-bold text-gray-900">AI Prompt Used</h4>
                      </div>
                      <div className="bg-white p-4 rounded-md border border-gray-200 shadow-sm">
                        <div className="text-sm text-gray-700 whitespace-pre-wrap font-mono leading-relaxed">
                          {sectionData.prompt_text}
                        </div>
                      </div>
                    </div>

                    {sectionData.generation_metadata && Object.keys(sectionData.generation_metadata).length > 0 && (
                      <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-lg border border-blue-200 shadow-sm">
                        <div className="flex items-center gap-2 mb-4">
                          <Bot className="h-5 w-5 text-purple-600" />
                          <h4 className="font-bold text-gray-900">Generation Details</h4>
                        </div>
                        <div className="bg-white p-4 rounded-md border border-gray-200 shadow-sm">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            {Object.entries(sectionData.generation_metadata).map(([key, value]) => (
                              <div key={key} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span className="font-medium text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
                                <span className="text-gray-800 font-mono">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {onRegenerateSection && (
                      <div className="flex justify-end">
                        <button
                          onClick={() => onRegenerateSection(section.id, sectionData.prompt_text)}
                          className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 flex items-center gap-2"
                        >
                          <Bot className="h-4 w-4" />
                          Regenerate Section
                        </button>
                      </div>
                    )}
                  </div>
                </TabsContent>
              )}

              {/* Citations tab - show citations found in this section */}
              {showCitations && (
                <TabsContent value="citations" className="mt-0">
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-lg border border-blue-200 shadow-sm">
                    <div className="flex items-center gap-2 mb-4">
                      <Quote className="h-5 w-5 text-blue-600" />
                      <h4 className="font-bold text-gray-900">Citations in this Section</h4>
                    </div>
                    <SectionCitationDisplay
                      sectionId={section.id}
                      sectionName={section.name}
                      allCitations={allCitations}
                      sectionCitations={sectionCitations}
                      className="mt-0"
                    />
                  </div>
                </TabsContent>
              )}

              {/* Embedded references tab - show references found within this section */}
              {hasHiddenReferences && (
                <TabsContent value="section-refs" className="mt-0">
                  <div className="bg-gradient-to-br from-amber-50 to-orange-50 p-6 rounded-lg border border-amber-200 shadow-sm">
                    <div className="flex items-center gap-2 text-amber-700 mb-4">
                      <AlertOctagon className="h-5 w-5" />
                      <h4 className="font-bold text-gray-900">Embedded References</h4>
                    </div>
                    <div className="bg-white p-4 rounded-md border border-amber-200 shadow-sm mb-4">
                      <p className="text-sm text-amber-700 leading-relaxed">
                        📝 These references were found embedded within this section and will be automatically consolidated in the final References section for proper academic formatting.
                      </p>
                    </div>
                    <div className="space-y-3">
                      {extractedReferences.map((reference, idx) => (
                        <div key={idx} className="bg-white p-4 rounded-md border border-gray-200 shadow-sm">
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                              <span className="text-xs font-bold text-amber-700">{idx + 1}</span>
                            </div>
                            <div className="text-sm text-gray-700 leading-relaxed font-serif">
                              {reference}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              )}
            </Tabs>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
