{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/api/(.*)", "destination": "/api/server.js"}, {"source": "/((?!api/).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Origin, X-Requested-With, Content-Type, Accept, Authorization"}]}], "env": {"NODE_ENV": "production"}, "build": {"env": {"VITE_NODE_ENV": "production"}}}