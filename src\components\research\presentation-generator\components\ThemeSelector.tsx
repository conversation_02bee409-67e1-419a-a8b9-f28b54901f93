import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

import { PRESENTATION_THEMES, AI_MODELS, IMAGE_MODELS } from '../constants';
import { PresentationTheme } from '../types';

interface ThemeSelectorProps {
  selectedTheme: string;
  selectedModel: string;
  selectedImageModel: string;
  selectedStyle: string;
  onThemeSelect: (themeId: string) => void;
  onModelSelect: (model: string) => void;
  onImageModelSelect: (model: string) => void;
  onStyleSelect: (style: string) => void;
  onContinue: () => void;
  isLoading?: boolean;
}

const PRESENTATION_STYLES = [
  { id: 'professional', name: 'Professional', description: 'Formal and business-oriented' },
  { id: 'casual', name: 'Casual', description: 'Relaxed and conversational' },
  { id: 'academic', name: 'Academic', description: 'Scholarly and detailed' },
  { id: 'creative', name: 'Creative', description: 'Artistic and engaging' }
];

export function ThemeSelector({
  selectedTheme,
  selectedModel,
  selectedImageModel,
  selectedStyle,
  onThemeSelect,
  onModelSelect,
  onImageModelSelect,
  onStyleSelect,
  onContinue,
  isLoading = false
}: ThemeSelectorProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Choose Your Presentation Style
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Theme Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Presentation Theme</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {PRESENTATION_THEMES.map((theme) => (
                <Card
                  key={theme.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTheme === theme.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => onThemeSelect(theme.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{theme.displayName}</h3>
                      {selectedTheme === theme.id && (
                        <Check className="w-4 h-4 text-blue-500" />
                      )}
                    </div>
                    
                    {/* Theme Preview */}
                    <div className="space-y-2">
                      <div className="flex gap-1">
                        <div 
                          className="w-4 h-4 rounded-sm"
                          style={{ backgroundColor: theme.colors.primary }}
                        />
                        <div 
                          className="w-4 h-4 rounded-sm"
                          style={{ backgroundColor: theme.colors.secondary }}
                        />
                        <div 
                          className="w-4 h-4 rounded-sm"
                          style={{ backgroundColor: theme.colors.accent }}
                        />
                      </div>
                      <div className="text-xs text-gray-600">
                        {theme.fonts.heading} • {theme.fonts.body}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* AI Model Selection */}
          <div className="space-y-2">
            <Label htmlFor="model-select">AI Model</Label>
            <Select value={selectedModel} onValueChange={onModelSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent>
                {AI_MODELS.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex items-center gap-2">
                      <span>{model.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {model.provider}
                      </Badge>
                      {model.recommended && (
                        <Badge variant="default" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Image Model Selection */}
          <div className="space-y-2">
            <Label htmlFor="image-model-select">Image Generation Model</Label>
            <Select value={selectedImageModel} onValueChange={onImageModelSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Select image model" />
              </SelectTrigger>
              <SelectContent>
                {IMAGE_MODELS.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Presentation Style */}
          <div className="space-y-2">
            <Label htmlFor="style-select">Presentation Style</Label>
            <Select value={selectedStyle} onValueChange={onStyleSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Select presentation style" />
              </SelectTrigger>
              <SelectContent>
                {PRESENTATION_STYLES.map((style) => (
                  <SelectItem key={style.id} value={style.id}>
                    <div>
                      <div className="font-medium">{style.name}</div>
                      <div className="text-sm text-gray-500">{style.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Continue Button */}
          <div className="flex justify-end pt-4">
            <Button 
              onClick={onContinue}
              disabled={!selectedTheme || !selectedModel || !selectedImageModel || !selectedStyle || isLoading}
              className="min-w-[150px]"
            >
              {isLoading ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Slides'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
