/**
 * Career Export Dialog Component
 * Handles exporting career analysis in various formats
 */

import React, { useState } from 'react';
import { CareerAnalysis, CareerExportOptions } from '../types';
import { EXPORT_FORMATS } from '../constants';
import { careerExportService } from '../services/career-export.service';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  Download, 
  FileText, 
  FileEdit, 
  Database,
  CheckCircle
} from "lucide-react";

interface CareerExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  careerAnalysis: CareerAnalysis | null;
}

export function CareerExportDialog({ 
  isOpen, 
  onClose, 
  careerAnalysis 
}: CareerExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [exportOptions, setExportOptions] = useState<CareerExportOptions>({
    format: 'pdf',
    includeRoadmaps: true,
    includeAnalysis: true,
    includeVisualization: false
  });
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!careerAnalysis) return;

    setIsExporting(true);

    try {
      const finalOptions: CareerExportOptions = {
        ...exportOptions,
        format: selectedFormat as 'pdf' | 'docx' | 'json'
      };

      // Export based on selected format
      switch (selectedFormat) {
        case 'pdf':
          await careerExportService.exportToPDF(careerAnalysis, finalOptions);
          break;
        case 'docx':
          await careerExportService.exportToWord(careerAnalysis, finalOptions);
          break;
        case 'json':
          await careerExportService.exportToJSON(careerAnalysis, finalOptions);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      toast.success(`Career analysis exported as ${selectedFormat.toUpperCase()}`);
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (formatId: string) => {
    switch (formatId) {
      case 'pdf':
        return <FileText className="h-5 w-5" />;
      case 'docx':
        return <FileEdit className="h-5 w-5" />;
      case 'json':
        return <Database className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  if (!careerAnalysis) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Download className="h-5 w-5 mr-2 text-blue-600" />
            Export Career Analysis
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Choose Export Format
            </Label>
            <RadioGroup 
              value={selectedFormat} 
              onValueChange={setSelectedFormat}
              className="grid grid-cols-1 gap-3"
            >
              {EXPORT_FORMATS.map((format) => (
                <div key={format.id}>
                  <RadioGroupItem 
                    value={format.id} 
                    id={format.id}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={format.id}
                    className="flex items-center space-x-3 p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-colors"
                  >
                    <div className="text-blue-600">
                      {getFormatIcon(format.id)}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{format.name}</p>
                      <p className="text-sm text-gray-600">{format.description}</p>
                    </div>
                    {selectedFormat === format.id && (
                      <CheckCircle className="h-5 w-5 text-blue-600" />
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Export Options */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              What to Include
            </Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-analysis"
                  checked={exportOptions.includeAnalysis}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeAnalysis: !!checked }))
                  }
                />
                <Label htmlFor="include-analysis" className="text-sm">
                  Overall career analysis and recommendations
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-roadmaps"
                  checked={exportOptions.includeRoadmaps}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeRoadmaps: !!checked }))
                  }
                />
                <Label htmlFor="include-roadmaps" className="text-sm">
                  Detailed career roadmaps and timelines
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-visualization"
                  checked={exportOptions.includeVisualization}
                  onCheckedChange={(checked) =>
                    setExportOptions(prev => ({ ...prev, includeVisualization: !!checked }))
                  }
                  disabled={selectedFormat === 'json'}
                />
                <Label 
                  htmlFor="include-visualization" 
                  className={`text-sm ${selectedFormat === 'json' ? 'text-gray-400' : ''}`}
                >
                  Career visualization diagram {selectedFormat === 'json' && '(not available for JSON)'}
                </Label>
              </div>
            </div>
          </div>

          {/* Preview */}
          <Card className="bg-gray-50">
            <CardContent className="p-4">
              <h4 className="font-medium text-gray-900 mb-2">Export Preview</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>• {careerAnalysis.careerPaths.length} career paths</p>
                {exportOptions.includeAnalysis && <p>• Overall analysis and recommendations</p>}
                {exportOptions.includeRoadmaps && <p>• Detailed roadmaps for each career</p>}
                {exportOptions.includeVisualization && selectedFormat !== 'json' && <p>• Career visualization diagram</p>}
                <p>• Generated on {careerAnalysis.generatedAt.toLocaleDateString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <Download className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export {selectedFormat.toUpperCase()}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
