import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  CheckCircle, 
  XCircle, 
  Edit, 
  BookOpen, 
  Clock, 
  Target,
  FileText,
  Users,
  Lightbulb
} from "lucide-react";
import { GeminiBookOutline, GeminiChapterOutline } from '../types';

interface GeminiOutlineApprovalProps {
  outline: GeminiBookOutline;
  onApprove: () => void;
  onReject: (feedback?: string) => void;
  onEdit?: (editedOutline: GeminiBookOutline) => void;
  isLoading?: boolean;
}

export const GeminiOutlineApproval: React.FC<GeminiOutlineApprovalProps> = ({
  outline,
  onApprove,
  onReject,
  onEdit,
  isLoading = false
}) => {
  const [feedback, setFeedback] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editedOutline, setEditedOutline] = useState<GeminiBookOutline>(outline);

  const handleApprove = () => {
    if (isEditing && onEdit) {
      onEdit(editedOutline);
    }
    onApprove();
  };

  const handleReject = () => {
    onReject(feedback.trim() || undefined);
    setFeedback('');
  };

  const updateChapterTitle = (chapterIndex: number, newTitle: string) => {
    const updatedChapters = [...editedOutline.chapters];
    updatedChapters[chapterIndex] = {
      ...updatedChapters[chapterIndex],
      title: newTitle
    };
    setEditedOutline({
      ...editedOutline,
      chapters: updatedChapters
    });
  };

  const updateChapterDescription = (chapterIndex: number, newDescription: string) => {
    const updatedChapters = [...editedOutline.chapters];
    updatedChapters[chapterIndex] = {
      ...updatedChapters[chapterIndex],
      description: newDescription
    };
    setEditedOutline({
      ...editedOutline,
      chapters: updatedChapters
    });
  };

  const currentOutline = isEditing ? editedOutline : outline;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            Review Book Outline
          </CardTitle>
          <p className="text-gray-600">
            Please review the generated book outline below. You can approve it to proceed with chapter generation,
            request changes, or edit it directly.
          </p>
        </CardHeader>
      </Card>

      {/* Book Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{currentOutline.title}</CardTitle>
          <p className="text-gray-600">{currentOutline.description}</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-blue-500" />
              <span className="text-sm">
                <strong>{currentOutline.totalEstimatedWords.toLocaleString()}</strong> words
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-green-500" />
              <span className="text-sm">
                <strong>{currentOutline.estimatedReadingTime}</strong> reading time
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-500" />
              <span className="text-sm">
                <strong>{currentOutline.chapters.length}</strong> chapters
              </span>
            </div>
          </div>

          {currentOutline.keyThemes.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Key Themes:</h4>
              <div className="flex flex-wrap gap-2">
                {currentOutline.keyThemes.map((theme, index) => (
                  <Badge key={index} variant="secondary">
                    {theme}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Chapter Outlines */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Chapter Outlines</h3>
        {currentOutline.chapters.map((chapter, index) => (
          <Card key={chapter.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {isEditing ? (
                    <input
                      type="text"
                      value={chapter.title}
                      onChange={(e) => updateChapterTitle(index, e.target.value)}
                      className="text-lg font-semibold bg-transparent border-b border-gray-300 focus:border-blue-500 outline-none w-full"
                    />
                  ) : (
                    <CardTitle className="text-lg">
                      Chapter {chapter.order}: {chapter.title}
                    </CardTitle>
                  )}
                  {isEditing ? (
                    <textarea
                      value={chapter.description}
                      onChange={(e) => updateChapterDescription(index, e.target.value)}
                      className="mt-2 w-full p-2 border border-gray-300 rounded-md focus:border-blue-500 outline-none resize-none"
                      rows={2}
                    />
                  ) : (
                    <p className="text-gray-600 mt-1">{chapter.description}</p>
                  )}
                </div>
                <Badge variant="outline" className="ml-4">
                  {chapter.estimatedWords.toLocaleString()} words
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Sections:</h5>
                  <div className="space-y-2">
                    {chapter.sections.map((section) => (
                      <div key={section.id} className="flex items-start gap-3 p-2 bg-gray-50 rounded-md">
                        <span className="text-xs text-gray-500 mt-1">{section.order}.</span>
                        <div className="flex-1">
                          <h6 className="text-sm font-medium">{section.title}</h6>
                          <p className="text-xs text-gray-600">{section.description}</p>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {section.estimatedWords} words
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {chapter.keyPoints.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Key Points:</h5>
                    <ul className="space-y-1">
                      {chapter.keyPoints.map((point, pointIndex) => (
                        <li key={pointIndex} className="flex items-start gap-2 text-sm">
                          <Lightbulb className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                          <span>{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feedback Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Feedback & Changes</CardTitle>
          <p className="text-sm text-gray-600">
            If you'd like to request changes to the outline, please provide specific feedback below.
          </p>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Describe any changes you'd like to see in the outline (optional)..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            rows={3}
            className="w-full"
          />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-end">
        <Button
          variant="outline"
          onClick={() => setIsEditing(!isEditing)}
          className="flex items-center gap-2"
        >
          <Edit className="h-4 w-4" />
          {isEditing ? 'Stop Editing' : 'Edit Outline'}
        </Button>
        
        <Button
          variant="outline"
          onClick={handleReject}
          disabled={isLoading}
          className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
        >
          <XCircle className="h-4 w-4" />
          Request Changes
        </Button>
        
        <Button
          onClick={handleApprove}
          disabled={isLoading}
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-4 w-4" />
          {isEditing ? 'Save & Approve' : 'Approve & Generate Chapters'}
        </Button>
      </div>
    </div>
  );
};
