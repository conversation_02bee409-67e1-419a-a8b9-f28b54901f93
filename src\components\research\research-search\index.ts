/**
 * Research Search Module Exports
 */

// Main Components
export { ResearchSearchInterface } from './components/ResearchSearchInterface';
export { ResearchTypeSelector } from './components/ResearchTypeSelector';
export { EnhancedResearchDemo } from './enhanced-demo';

// Export enhanced services
export { researchPlanningService } from './services/research-planning.service';
export { referenceManagementService } from './services/reference-management.service';
export { academicFormattingService } from './services/academic-formatting.service';

// Export test utilities
export { runEnhancedSystemTests } from './test-enhanced-system';
export { SearchMessage } from './components/SearchMessage';
export { SearchInput } from './components/SearchInput';
export { SearchHistory } from './components/SearchHistory';
export { AIModelSelector } from './components/AIModelSelector';
export { DeepResearchMessage } from './components/DeepResearchMessage';
export { OutlineApproval } from './components/OutlineApproval';
export { DeepResearchProgress } from './components/DeepResearchProgress';

// Services
export { tavilySearchService } from './services/tavily-search.service';
export { researchAIService } from './services/research-ai.service';
export { searchHistoryService } from './services/search-history.service';
export { deepResearchService } from './services/deep-research.service';

// Types
export type {
  SearchMessage as SearchMessageType,
  SearchSource,
  Citation,
  SearchSession,
  TavilySearchResult,
  TavilyResult,
  AIModel,
  SearchOptions,
  SearchProgress,
  SearchError,
  SearchStats,
  UserPreferences
} from './types';

// Default export
export { ResearchSearchInterface as default } from './components/ResearchSearchInterface';
