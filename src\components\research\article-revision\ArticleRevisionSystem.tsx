/**
 * Main Article Revision System Component
 * Orchestrates the complete article revision workflow
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  FileEdit, 
  Upload, 
  Bot, 
  Eye, 
  Download, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';

import { useRevisionWorkflowStore } from './stores/revision-workflow.store';
import { ArticleRevisionUploader } from './components/ArticleRevisionUploader';
import { RevisionWorkflow } from './components/RevisionWorkflow';
import { RevisionResults } from './components/RevisionResults';
import { ExportService } from './services/export.service';
import { ExportConfiguration } from './types';

type WorkflowStep = 'upload' | 'processing' | 'results' | 'export';

export function ArticleRevisionSystem() {
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('upload');
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    originalArticle,
    reviewerComments,
    progress,
    revisedArticle,
    responseLetter,
    manualInterventions,
    initializeWorkflow,
    resetWorkflow,
    canStartRevision
  } = useRevisionWorkflowStore();

  // Initialize workflow on component mount
  useEffect(() => {
    initializeWorkflow();
  }, [initializeWorkflow]);

  // Update current step based on workflow state
  useEffect(() => {
    if (progress.phase === 'completed' && revisedArticle) {
      setCurrentStep('results');
      setIsProcessing(false);
    } else if (progress.phase !== 'parsing' && progress.percentage > 0) {
      setCurrentStep('processing');
    } else if (originalArticle && reviewerComments.length > 0) {
      setCurrentStep('upload');
    }
  }, [progress.phase, progress.percentage, originalArticle, reviewerComments.length, revisedArticle]);

  const handleStartRevision = () => {
    if (!canStartRevision()) {
      toast.error('Please upload both article and reviewer comments');
      return;
    }
    
    setCurrentStep('processing');
    setIsProcessing(true);
    toast.info('Starting article revision process...');
  };

  const handleViewResults = () => {
    setCurrentStep('results');
  };

  const handleExport = async () => {
    if (!revisedArticle) {
      toast.error('No revision results to export');
      return;
    }

    try {
      setCurrentStep('export');
      toast.info('Preparing files for export...');

      const exportConfig: ExportConfiguration = {
        format: 'word',
        includeTrackChanges: true,
        highlightChanges: true,
        includeComments: true,
        includeResponseLetter: true,
        includeManualSuggestions: true
      };

      const { files } = await ExportService.exportAllFiles(
        revisedArticle,
        responseLetter,
        manualInterventions,
        exportConfig
      );

      // Download all files
      ExportService.downloadFiles(files);
      
      toast.success(`Successfully exported ${files.length} files`);
      
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export files. Please try again.');
    }
  };

  const handleStartNew = () => {
    resetWorkflow();
    setCurrentStep('upload');
    setIsProcessing(false);
    toast.info('Started new revision workflow');
  };

  const getStepStatus = (step: WorkflowStep): 'completed' | 'current' | 'pending' => {
    const stepOrder: WorkflowStep[] = ['upload', 'processing', 'results', 'export'];
    const currentIndex = stepOrder.indexOf(currentStep);
    const stepIndex = stepOrder.indexOf(step);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  const getStepIcon = (step: WorkflowStep, status: string) => {
    const iconClass = "h-5 w-5";
    
    if (status === 'completed') {
      return <CheckCircle className={`${iconClass} text-green-600`} />;
    }
    
    if (status === 'current' && isProcessing) {
      return <Clock className={`${iconClass} text-blue-600 animate-pulse`} />;
    }
    
    switch (step) {
      case 'upload': return <Upload className={iconClass} />;
      case 'processing': return <Bot className={iconClass} />;
      case 'results': return <Eye className={iconClass} />;
      case 'export': return <Download className={iconClass} />;
      default: return <AlertCircle className={iconClass} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            AI Article Revision System
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Automatically revise your research article based on reviewer comments and generate professional response letters
          </p>
        </div>

        {/* Progress Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileEdit className="h-5 w-5" />
              Revision Workflow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              {[
                { step: 'upload' as WorkflowStep, label: 'Upload Files', description: 'Article & Comments' },
                { step: 'processing' as WorkflowStep, label: 'AI Processing', description: 'Revision & Analysis' },
                { step: 'results' as WorkflowStep, label: 'Review Results', description: 'Changes & Response' },
                { step: 'export' as WorkflowStep, label: 'Export Files', description: 'Download Results' }
              ].map((item, index) => {
                const status = getStepStatus(item.step);
                return (
                  <div key={item.step} className="flex items-center">
                    <div className="flex flex-col items-center">
                      <div className={`
                        flex items-center justify-center w-12 h-12 rounded-full border-2 mb-2
                        ${status === 'completed' ? 'bg-green-100 border-green-500' : 
                          status === 'current' ? 'bg-blue-100 border-blue-500' : 
                          'bg-gray-100 border-gray-300'}
                      `}>
                        {getStepIcon(item.step, status)}
                      </div>
                      <div className="text-center">
                        <div className={`font-medium text-sm ${
                          status === 'completed' ? 'text-green-700' :
                          status === 'current' ? 'text-blue-700' :
                          'text-gray-500'
                        }`}>
                          {item.label}
                        </div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </div>
                    {index < 3 && (
                      <div className={`flex-1 h-0.5 mx-4 ${
                        getStepStatus(['upload', 'processing', 'results', 'export'][index + 1]) === 'completed' 
                          ? 'bg-green-500' 
                          : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Current Status */}
        {(originalArticle || reviewerComments.length > 0) && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Current Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${originalArticle ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm">
                    Article: {originalArticle ? originalArticle.fileName : 'Not uploaded'}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${reviewerComments.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm">
                    Reviewers: {reviewerComments.length}/5 uploaded
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${revisedArticle ? 'bg-green-500' : 'bg-gray-300'}`} />
                  <span className="text-sm">
                    Revision: {revisedArticle ? 'Completed' : 'Pending'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {progress.errors.length > 0 && (
          <Alert variant="destructive" className="mb-8">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Processing Errors</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {progress.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {currentStep === 'upload' && (
            <ArticleRevisionUploader
              onStartRevision={handleStartRevision}
              isProcessing={isProcessing}
            />
          )}

          {currentStep === 'processing' && (
            <RevisionWorkflow
              onViewResults={handleViewResults}
              onExport={handleExport}
            />
          )}

          {currentStep === 'results' && (
            <RevisionResults onExport={handleExport} />
          )}

          {currentStep === 'export' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Export Complete
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
                <p className="text-lg">Files have been downloaded successfully!</p>
                <div className="flex justify-center gap-4">
                  <Button onClick={() => setCurrentStep('results')} variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    View Results Again
                  </Button>
                  <Button onClick={handleStartNew}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Start New Revision
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        {currentStep !== 'upload' && (
          <div className="fixed bottom-6 right-6 flex flex-col gap-2">
            <Button
              onClick={handleStartNew}
              variant="outline"
              size="sm"
              className="shadow-lg"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              New Revision
            </Button>
            {revisedArticle && (
              <Button
                onClick={handleExport}
                size="sm"
                className="shadow-lg"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
