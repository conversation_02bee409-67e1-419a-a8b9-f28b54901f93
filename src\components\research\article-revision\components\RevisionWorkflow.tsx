/**
 * Revision Workflow Component
 * Manages the AI-powered article revision process
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  FileText, 
  Users, 
  Bot,
  Download,
  Eye,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';
import { AIOrchestrator } from '../services/ai-orchestrator.service';
import { AI_ASSISTANTS } from '../constants';

interface RevisionWorkflowProps {
  onViewResults: () => void;
  onExport: () => void;
}

export function RevisionWorkflow({
  onViewResults,
  onExport
}: RevisionWorkflowProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<number | null>(null);
  const [currentSection, setCurrentSection] = useState<string | null>(null);
  const [processedSections, setProcessedSections] = useState<string[]>([]);
  const [sectionProgress, setSectionProgress] = useState<Record<string, number>>({});
  const [activityLog, setActivityLog] = useState<Array<{id: string, message: string, timestamp: Date, type: 'info' | 'success' | 'warning'}>>([]);

  const {
    originalArticle,
    reviewerComments,
    progress,
    aiAssistants,
    revisedArticle,
    responseLetter,
    manualInterventions,
    setRevisedArticle,
    setResponseLetter,
    addManualIntervention,
    updateProgress,
    setCurrentPhase,
    addError,
    clearErrors,
    getOverallProgress,
    getActiveAssistants,
    getCompletedAssistants,
    getFailedAssistants
  } = useRevisionWorkflowStore();

  // Monitor progress changes for real-time updates
  React.useEffect(() => {
    if (progress.currentStep) {
      // Add to activity log
      const logEntry = {
        id: Date.now().toString(),
        message: progress.currentStep,
        timestamp: new Date(),
        type: progress.currentStep.includes('error') || progress.currentStep.includes('failed') ? 'warning' as const :
              progress.currentStep.includes('completed') || progress.currentStep.includes('success') ? 'success' as const :
              'info' as const
      };

      setActivityLog(prev => [logEntry, ...prev.slice(0, 9)]); // Keep last 10 entries

      // Extract section information from progress step
      const sectionMatch = progress.currentStep.match(/Revising (\w+) section/i);
      if (sectionMatch) {
        const section = sectionMatch[1];
        setCurrentSection(section);

        // Update section progress
        setSectionProgress(prev => ({
          ...prev,
          [section]: 50 // Processing
        }));
      }

      // Check for completed sections
      const completedMatch = progress.currentStep.match(/Completed revision of (\w+) section/i);
      if (completedMatch) {
        const section = completedMatch[1];
        setProcessedSections(prev => [...prev, section]);
        setSectionProgress(prev => ({
          ...prev,
          [section]: 100 // Completed
        }));
        setCurrentSection(null);
      }
    }
  }, [progress.currentStep]);

  // Start the revision process
  const startRevision = async () => {
    if (!originalArticle || reviewerComments.length === 0) {
      toast.error('Please upload article and reviewer comments first');
      return;
    }

    setIsProcessing(true);
    setStartTime(new Date());

    // Estimate processing time based on article length and comments
    const wordCount = originalArticle.wordCount;
    const commentCount = reviewerComments.reduce((sum, rc) => sum + rc.comments.length, 0);
    const estimatedMinutes = Math.ceil((wordCount / 1000) * 2 + commentCount * 0.5);
    setEstimatedTime(estimatedMinutes);

    try {
      // Clear any previous errors
      clearErrors();

      updateProgress({
        phase: 'comment-analysis',
        currentStep: 'Initializing revision process...',
        percentage: 5,
        completedSteps: 0
      });

      toast.info('Starting AI revision process...');

      const orchestrator = AIOrchestrator.getInstance();
      const results = await orchestrator.orchestrateRevision(originalArticle, reviewerComments);

      // Store results
      setRevisedArticle(results.revisedArticle);
      setResponseLetter(results.responseLetter);
      results.manualInterventions.forEach(intervention => {
        addManualIntervention(intervention);
      });

      setCurrentPhase('completed');
      updateProgress({
        currentStep: 'Revision completed successfully!',
        percentage: 100,
        completedSteps: progress.totalSteps
      });

      toast.success('Article revision completed successfully!');

    } catch (error) {
      console.error('Revision process failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      addError(errorMessage);
      toast.error(`Revision failed: ${errorMessage}`);

      // Reset phase to allow retry
      setCurrentPhase('comment-analysis');
      updateProgress({
        currentStep: 'Ready to retry - click Start AI Revision again',
        percentage: 0
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Cancel the revision process
  const cancelRevision = () => {
    const orchestrator = AIOrchestrator.getInstance();
    orchestrator.cancelAllRequests();
    setIsProcessing(false);
    toast.info('Revision process cancelled');
  };

  // Calculate elapsed time
  const getElapsedTime = (): string => {
    if (!startTime) return '0:00';
    const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get status color for assistant
  const getAssistantStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'processing': return 'text-blue-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-500';
    }
  };

  // Get status icon for assistant
  const getAssistantStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'processing': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const activeAssistants = getActiveAssistants();
  const completedAssistants = getCompletedAssistants();
  const failedAssistants = getFailedAssistants();
  const overallProgress = getOverallProgress();

  return (
    <div className="space-y-6">
      {/* Process Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Revision Process
            </span>
            {isProcessing && (
              <Button variant="outline" size="sm" onClick={cancelRevision}>
                Cancel
              </Button>
            )}
          </CardTitle>
          <CardDescription>
            AI-powered article revision based on reviewer comments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Progress Overview */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-gray-500">{progress.percentage}%</span>
            </div>
            <Progress value={progress.percentage} className="h-3" />
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="font-medium text-blue-600">{completedAssistants.length}</div>
                <div className="text-gray-500">Completed</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-orange-600">{activeAssistants.length}</div>
                <div className="text-gray-500">Active</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-red-600">{failedAssistants.length}</div>
                <div className="text-gray-500">Failed</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-600">{getElapsedTime()}</div>
                <div className="text-gray-500">Elapsed</div>
              </div>
            </div>

            {/* Current Status */}
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Badge variant={progress.phase === 'completed' ? 'default' : 'secondary'}>
                  {progress.phase.replace('-', ' ').toUpperCase()}
                </Badge>
                <span className="text-sm text-gray-600">{progress.currentStep}</span>
              </div>
              {estimatedTime && isProcessing && (
                <div className="text-xs text-gray-500 mt-1">
                  Estimated time: ~{estimatedTime} minutes
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section Progress */}
      {(isProcessing || processedSections.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Section Progress
            </CardTitle>
            <CardDescription>
              Real-time progress of article section revisions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {['introduction', 'methodology', 'results', 'discussion', 'conclusion'].map(section => {
                const isProcessing = currentSection === section;
                const isCompleted = processedSections.includes(section);
                const sectionProg = sectionProgress[section] || 0;

                return (
                  <div key={section} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        isCompleted ? 'bg-green-500' :
                        isProcessing ? 'bg-blue-500 animate-pulse' :
                        'bg-gray-300'
                      }`} />
                      <div>
                        <div className="font-medium capitalize">{section}</div>
                        <div className="text-sm text-gray-500">
                          {isCompleted ? 'Completed' :
                           isProcessing ? 'Processing...' :
                           'Pending'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{sectionProg}%</div>
                      {(isProcessing || isCompleted) && (
                        <Progress value={sectionProg} className="w-20 h-2 mt-1" />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Live Activity Feed */}
      {(isProcessing && activityLog.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Live Activity
            </CardTitle>
            <CardDescription>
              Real-time processing updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {activityLog.map(entry => (
                <div key={entry.id} className="flex items-start gap-3 p-2 rounded-lg bg-gray-50">
                  <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                    entry.type === 'success' ? 'bg-green-500' :
                    entry.type === 'warning' ? 'bg-orange-500' :
                    'bg-blue-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{entry.message}</p>
                    <p className="text-xs text-gray-500">
                      {entry.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Assistants Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            AI Assistants
          </CardTitle>
          <CardDescription>
            Status of individual AI assistants working on your article
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.values(AI_ASSISTANTS).map(assistantConfig => {
              const assistant = aiAssistants[assistantConfig.id];
              if (!assistant) return null;

              return (
                <div key={assistant.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={getAssistantStatusColor(assistant.status)}>
                      {getAssistantStatusIcon(assistant.status)}
                    </div>
                    <div>
                      <div className="font-medium">{assistant.name}</div>
                      <div className="text-sm text-gray-500">{assistantConfig.description}</div>
                      {assistant.currentTask && (
                        <div className="text-xs text-blue-600">{assistant.currentTask}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{assistant.progress}%</div>
                    {assistant.status === 'processing' && (
                      <Progress value={assistant.progress} className="w-20 h-2 mt-1" />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Errors and Warnings */}
      {(progress.errors.length > 0 || progress.warnings.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {progress.errors.map((error, index) => (
                <Alert key={`error-${index}`} variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ))}
              {progress.warnings.map((warning, index) => (
                <Alert key={`warning-${index}`}>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Warning</AlertTitle>
                  <AlertDescription>{warning}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {!isProcessing && !revisedArticle && (
          <Button onClick={startRevision} size="lg" className="px-8">
            <Bot className="h-4 w-4 mr-2" />
            Start AI Revision
          </Button>
        )}

        {isProcessing && (
          <Button disabled size="lg" className="px-8">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Processing...
          </Button>
        )}

        {revisedArticle && (
          <>
            <Button onClick={onViewResults} variant="outline" size="lg">
              <Eye className="h-4 w-4 mr-2" />
              View Results
            </Button>
            <Button onClick={onExport} size="lg">
              <Download className="h-4 w-4 mr-2" />
              Export Files
            </Button>
            <Button onClick={() => window.location.reload()} variant="outline" size="lg">
              <RefreshCw className="h-4 w-4 mr-2" />
              Start New Revision
            </Button>
          </>
        )}
      </div>

      {/* Results Summary */}
      {revisedArticle && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Revision Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {revisedArticle.metadata.totalChanges}
                </div>
                <div className="text-sm text-gray-500">Total Changes</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {revisedArticle.metadata.addressedComments}
                </div>
                <div className="text-sm text-gray-500">Comments Addressed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {manualInterventions.length}
                </div>
                <div className="text-sm text-gray-500">Manual Actions</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {responseLetter ? '1' : '0'}
                </div>
                <div className="text-sm text-gray-500">Response Letter</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
