import { GoogleGenAI } from '@google/genai';
import { PresentationMetadata, OutlineItem, Slide, SlideLayout } from '../types';

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class PresentationAIService {
  private genAI: GoogleGenAI | null = null;
  private geminiApiKey: string;
  private openaiApiKey: string;

  constructor() {
    this.geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    this.openaiApiKey = import.meta.env.OPENAI_API_KEY || '';

    if (this.geminiApiKey) {
      try {
        this.genAI = new GoogleGenAI({ apiKey: this.geminiApiKey });
      } catch (error) {
        console.error('Failed to initialize Gemini AI:', error);
      }
    }
  }

  private isGeminiConfigured(): boolean {
    return this.genAI !== null && this.geminiApiKey.length > 0;
  }

  private isOpenAIConfigured(): boolean {
    return this.openaiApiKey.length > 0;
  }

  private isConfigured(): boolean {
    return this.isGeminiConfigured() || this.isOpenAIConfigured();
  }

  /**
   * Generate presentation outline based on topic and metadata
   */
  async generateOutline(metadata: PresentationMetadata, selectedModel: string = 'gemini-2.5-flash'): Promise<OutlineItem[]> {
    if (!this.isConfigured()) {
      console.warn('No AI service configured, returning fallback outline');
      return this.generateFallbackOutline(metadata);
    }

    // Determine which API to use based on model selection
    const useOpenAI = selectedModel.includes('gpt') || selectedModel.includes('o1') || !this.isGeminiConfigured();

    try {
      const prompt = `Create a detailed presentation outline for the following topic:

Topic: ${metadata.topic}
Title: ${metadata.title}
Number of slides: ${metadata.slideCount}
Style: ${metadata.style}
Audience: ${metadata.audience}
Language: ${metadata.language}
${metadata.description ? `Description: ${metadata.description}` : ''}

Please generate a JSON array of outline items with the following structure for each slide:
{
  "title": "Slide title",
  "description": "Brief description of slide content",
  "slideLayout": "one of: title-slide, content-slide, bullet-points, two-column, image-text, quote, conclusion",
  "estimatedDuration": number (in minutes)
}

Guidelines:
- First slide should be "title-slide" layout
- Last slide should be "conclusion" layout
- Use varied layouts throughout the presentation
- Make titles engaging and specific to the topic
- Descriptions should be detailed enough to guide content generation
- Estimated duration should be realistic (2-5 minutes per slide)
- Ensure the outline flows logically from introduction to conclusion

Return only the JSON array, no additional text.`;

      let text = '';

      if (useOpenAI && this.isOpenAIConfigured()) {
        text = await this.callOpenAI(prompt, selectedModel);
      } else if (this.isGeminiConfigured()) {
        text = await this.callGemini(prompt, selectedModel);
      } else {
        throw new Error('No AI service available');
      }

      // Parse the JSON response
      let outlineData;
      try {
        // Clean the response text to extract JSON
        const jsonMatch = text.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          outlineData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON array found in response');
        }
      } catch (parseError) {
        console.error('Failed to parse outline JSON:', parseError);
        console.log('Raw response:', text);
        // Fallback to mock data if parsing fails
        return this.generateFallbackOutline(metadata);
      }

      // Convert to OutlineItem format
      const outline: OutlineItem[] = outlineData.map((item: any, index: number) => ({
        id: crypto.randomUUID(),
        title: item.title || `Slide ${index + 1}`,
        description: item.description || 'Content to be generated',
        order: index,
        slideLayout: this.validateSlideLayout(item.slideLayout) || 'content-slide',
        estimatedDuration: item.estimatedDuration || 3
      }));

      return outline.slice(0, metadata.slideCount);
    } catch (error) {
      console.error('Failed to generate outline:', error);
      // Return fallback outline instead of throwing
      return this.generateFallbackOutline(metadata);
    }
  }

  /**
   * Generate slide content based on outline item
   */
  async generateSlideContent(
    outlineItem: OutlineItem,
    metadata: PresentationMetadata,
    selectedModel: string = 'gemini-2.5-flash',
    context?: string
  ): Promise<Slide> {
    if (!this.isConfigured()) {
      console.warn('No AI service configured, returning fallback slide');
      return this.generateFallbackSlide(outlineItem, metadata);
    }

    try {
      const useOpenAI = selectedModel.includes('gpt') || selectedModel.includes('o1') || !this.isGeminiConfigured();

      const prompt = `Generate detailed, professional slide content for a presentation slide with the following specifications:

Title: ${outlineItem.title}
Description: ${outlineItem.description}
Layout: ${outlineItem.slideLayout}
Presentation Topic: ${metadata.topic}
Style: ${metadata.style}
Audience: ${metadata.audience}
Language: ${metadata.language}
${context ? `Context from previous slides: ${context}` : ''}

IMPORTANT: Generate content in a structured format that matches the slide layout:

For ${outlineItem.slideLayout} layout, provide:
${this.getLayoutInstructions(outlineItem.slideLayout)}

IMPORTANT - Include layout trigger words to enable advanced visual layouts:
- For comparison content: Use words like "comparison", "vs", "versus"
- For process content: Use words like "process", "workflow", "cycle"
- For timeline content: Use words like "timeline", "chronological", "history"
- For hierarchical content: Use words like "hierarchy", "pyramid", "levels"
- For step-by-step content: Use words like "steps", "progression", "advancement"
- For flow content: Use words like "flow", "cause", "effect"
- For feature content: Use words like "concepts", "features", "benefits"

Requirements:
1. Content must be engaging and appropriate for ${metadata.audience} audience
2. Use ${metadata.style} tone and style
3. Write in ${metadata.language}
4. Provide substantial, informative content (minimum 3-5 key points for content slides)
5. Include specific examples, statistics, or case studies when relevant
6. Make content actionable and memorable
7. Use clear, concise language with strong headlines
8. Include layout trigger words to enable beautiful visual layouts

Format the response as structured content with clear headings and bullet points where appropriate.`;

      let contentText = '';

      if (useOpenAI && this.isOpenAIConfigured()) {
        contentText = await this.callOpenAI(prompt, selectedModel);
      } else if (this.isGeminiConfigured()) {
        contentText = await this.callGemini(prompt, selectedModel);
      } else {
        throw new Error('No AI service available');
      }

      // Convert the AI-generated content to slide format
      const slide: Slide = {
        id: crypto.randomUUID(),
        title: outlineItem.title,
        content: this.convertTextToSlideContent(contentText, outlineItem.slideLayout),
        layout: outlineItem.slideLayout,
        order: outlineItem.order,
        notes: `Speaker notes for ${outlineItem.title}: ${outlineItem.description}`,
      };

      return slide;

    } catch (error) {
      console.error('Failed to generate slide content:', error);
      // Return fallback slide instead of throwing
      return this.generateFallbackSlide(outlineItem, metadata);
    }
  }

  /**
   * Generate multiple slides sequentially
   */
  async generateSlides(
    outline: OutlineItem[],
    metadata: PresentationMetadata,
    selectedModel: string = 'gemini-2.5-flash',
    onProgress?: (progress: number, currentSlide: number) => void
  ): Promise<Slide[]> {
    const slides: Slide[] = [];

    for (let i = 0; i < outline.length; i++) {
      const outlineItem = outline[i];

      // Update progress
      if (onProgress) {
        const progress = (i / outline.length) * 100;
        onProgress(progress, i + 1);
      }

      try {
        // Build context from previous slides
        const context = slides.length > 0
          ? `Previous slides covered: ${slides.map(s => s.title).join(', ')}`
          : undefined;

        const slide = await this.generateSlideContent(outlineItem, metadata, selectedModel, context);
        slides.push(slide);
      } catch (error) {
        console.error(`Failed to generate slide ${i + 1}:`, error);
        // Generate fallback slide and continue
        const fallbackSlide = this.generateFallbackSlide(outlineItem, metadata);
        slides.push(fallbackSlide);
      }
    }

    // Final progress update
    if (onProgress) {
      onProgress(100, outline.length);
    }

    return slides;
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(prompt: string, model: string): Promise<string> {
    const apiModel = model.includes('gpt-4.1') ? 'gpt-4o' :
                    model.includes('o4-mini') ? 'gpt-4o-mini' :
                    model.includes('gpt') ? model : 'gpt-4o-mini';

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiApiKey}`
      },
      body: JSON.stringify({
        model: apiModel,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data: OpenAIResponse = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  /**
   * Call Gemini API
   */
  private async callGemini(prompt: string, model: string): Promise<string> {
    const config = {
      generationConfig: {
        maxOutputTokens: 4000,
        temperature: 0.7
      }
    };

    const contents = [{
      role: 'user' as const,
      parts: [{ text: prompt }]
    }];

    const response = await this.genAI!.models.generateContentStream({
      model: model,
      config,
      contents
    });

    let text = '';
    for await (const chunk of response) {
      if (chunk.text) {
        text += chunk.text;
      }
    }

    return text;
  }

  /**
   * Get layout-specific instructions for content generation
   */
  private getLayoutInstructions(layout: SlideLayout): string {
    switch (layout) {
      case 'title-slide':
        return `- Main title (compelling and descriptive)
- Subtitle (supporting information)
- Presenter name or organization (if applicable)`;

      case 'bullet-points':
        return `- Clear, descriptive heading
- 3-5 bullet points with specific, actionable content
- Each bullet should be concise but informative
- Use parallel structure for consistency`;

      case 'two-column':
        return `- Main heading
- Left column: Key concepts or text content
- Right column: Supporting information, examples, or complementary content
- Ensure both columns are balanced and related`;

      case 'image-text':
        return `- Compelling headline
- Descriptive text that would complement an image
- Include specific details that would be enhanced by visual elements
- Focus on storytelling or explanation`;

      case 'quote':
        return `- Powerful, relevant quote related to the topic
- Attribution to the source
- Brief context or explanation of why this quote is significant
- Connection to the overall presentation theme`;

      case 'conclusion':
        return `- Summary of key takeaways (3-5 main points)
- Call to action or next steps
- Memorable closing statement
- Contact information or resources (if applicable)`;

      default:
        return `- Clear, descriptive heading
- Well-structured content with multiple paragraphs
- Include specific examples, data, or case studies
- Organize information logically with smooth transitions`;
    }
  }

  /**
   * Validate slide layout type
   */
  private validateSlideLayout(layout: string): SlideLayout | null {
    const validLayouts: SlideLayout[] = [
      'title-slide', 'content-slide', 'bullet-points', 'two-column',
      'image-text', 'quote', 'conclusion', 'section-divider'
    ];
    return validLayouts.includes(layout as SlideLayout) ? layout as SlideLayout : null;
  }

  /**
   * Generate fallback outline when AI generation fails
   */
  private generateFallbackOutline(metadata: PresentationMetadata): OutlineItem[] {
    const baseOutline: OutlineItem[] = [
      {
        id: crypto.randomUUID(),
        title: 'Introduction',
        description: `Welcome and overview of ${metadata.topic}`,
        order: 0,
        slideLayout: 'title-slide' as SlideLayout,
        estimatedDuration: 2
      },
      {
        id: crypto.randomUUID(),
        title: 'Key Points',
        description: 'Main arguments and supporting evidence',
        order: 1,
        slideLayout: 'bullet-points' as SlideLayout,
        estimatedDuration: 4
      },
      {
        id: crypto.randomUUID(),
        title: 'Analysis',
        description: 'Detailed examination of the topic',
        order: 2,
        slideLayout: 'content-slide' as SlideLayout,
        estimatedDuration: 5
      }
    ];

    // Add more slides based on requested count
    const additionalSlides = Math.max(0, metadata.slideCount - 4); // 3 base + conclusion
    for (let i = 0; i < additionalSlides; i++) {
      baseOutline.push({
        id: crypto.randomUUID(),
        title: `Topic ${i + 1}`,
        description: `Additional content related to ${metadata.topic}`,
        order: 3 + i,
        slideLayout: 'content-slide' as SlideLayout,
        estimatedDuration: 3
      });
    }

    // Add conclusion
    baseOutline.push({
      id: crypto.randomUUID(),
      title: 'Conclusion',
      description: 'Summary and key takeaways',
      order: baseOutline.length,
      slideLayout: 'conclusion' as SlideLayout,
      estimatedDuration: 2
    });

    return baseOutline.slice(0, metadata.slideCount);
  }

  /**
   * Generate fallback slide when AI generation fails
   */
  private generateFallbackSlide(outlineItem: OutlineItem, metadata: PresentationMetadata): Slide {
    const mockContent = this.generateMockContent(outlineItem, metadata);

    return {
      id: crypto.randomUUID(),
      title: outlineItem.title,
      content: mockContent,
      layout: outlineItem.slideLayout,
      order: outlineItem.order,
      notes: `Speaker notes for ${outlineItem.title}: ${outlineItem.description}`,
    };
  }

  /**
   * Convert text content to slide content format
   */
  private convertTextToSlideContent(text: string, layout: SlideLayout): any[] {
    // Parse the text and convert to appropriate slide format
    const lines = text.split('\n').filter(line => line.trim());

    if (lines.length === 0) {
      return this.generateMockContentForLayout(layout);
    }

    // Try to extract structured content based on layout
    switch (layout) {
      case 'title-slide':
        return [
          { type: 'h1', children: [{ text: lines[0] || 'Title' }] },
          { type: 'h2', children: [{ text: lines[1] || 'Subtitle' }] },
          { type: 'p', children: [{ text: lines[2] || 'Presenter' }] }
        ];

      case 'bullet-points':
        const title = lines[0] || 'Key Points';
        // Look for bullet points in the text
        const bulletLines = lines.slice(1).filter(line =>
          line.match(/^[-•*]\s/) || line.match(/^\d+\./) || line.includes('•')
        );

        const bullets = bulletLines.length > 0
          ? bulletLines.map(line => ({
              type: 'li',
              children: [{ text: line.replace(/^[-•*\d+\.]\s*/, '').trim() }]
            }))
          : lines.slice(1).slice(0, 5).map(line => ({
              type: 'li',
              children: [{ text: line.trim() }]
            }));

        return [
          { type: 'h2', children: [{ text: title }] },
          {
            type: 'ul',
            children: bullets.length > 0 ? bullets : [
              { type: 'li', children: [{ text: 'Key point 1' }] },
              { type: 'li', children: [{ text: 'Key point 2' }] },
              { type: 'li', children: [{ text: 'Key point 3' }] }
            ]
          }
        ];

      case 'quote':
        // Look for quote content
        const quoteText = lines.find(line => line.includes('"') || line.includes('"')) || lines[1] || 'Inspiring quote here';
        const attribution = lines.find(line => line.includes('-') && !line.includes('"')) || lines[2] || '- Author';

        return [
          { type: 'blockquote', children: [{ text: quoteText.replace(/["""]/g, '') }] },
          { type: 'p', children: [{ text: attribution }] }
        ];

      case 'two-column':
        const midpoint = Math.ceil(lines.length / 2);
        return [
          { type: 'h2', children: [{ text: lines[0] || 'Two Column Content' }] },
          { type: 'div', className: 'two-column-left', children:
            lines.slice(1, midpoint).map(line => ({ type: 'p', children: [{ text: line }] }))
          },
          { type: 'div', className: 'two-column-right', children:
            lines.slice(midpoint).map(line => ({ type: 'p', children: [{ text: line }] }))
          }
        ];

      default:
        // For content slides, try to identify headings and paragraphs
        const content = [];
        let currentParagraph = [];

        for (const line of lines) {
          if (line.match(/^#/) || line.toUpperCase() === line || line.endsWith(':')) {
            // This looks like a heading
            if (currentParagraph.length > 0) {
              content.push({ type: 'p', children: [{ text: currentParagraph.join(' ') }] });
              currentParagraph = [];
            }
            content.push({ type: 'h2', children: [{ text: line.replace(/^#+\s*/, '') }] });
          } else {
            currentParagraph.push(line);
          }
        }

        if (currentParagraph.length > 0) {
          content.push({ type: 'p', children: [{ text: currentParagraph.join(' ') }] });
        }

        return content.length > 0 ? content : [
          { type: 'h2', children: [{ text: lines[0] || 'Slide Title' }] },
          ...lines.slice(1).map(line => ({
            type: 'p',
            children: [{ text: line }]
          }))
        ];
    }
  }

  /**
   * Convert AI-generated content to Plate.js format
   */
  private convertToPlateFormat(content: any[], layout: SlideLayout): any[] {
    if (!Array.isArray(content)) {
      return this.generateMockContentForLayout(layout);
    }

    return content.map(item => {
      switch (item.type) {
        case 'h1':
          return { type: 'h1', children: [{ text: item.text || '' }] };
        case 'h2':
          return { type: 'h2', children: [{ text: item.text || '' }] };
        case 'h3':
          return { type: 'h3', children: [{ text: item.text || '' }] };
        case 'p':
          return { type: 'p', children: [{ text: item.text || '' }] };
        case 'ul':
          return {
            type: 'ul',
            children: (item.items || []).map((listItem: string) => ({
              type: 'li',
              children: [{ text: listItem }]
            }))
          };
        case 'quote':
          return [
            {
              type: 'blockquote',
              className: 'text-center text-2xl italic',
              children: [{ text: `"${item.text || ''}"` }]
            },
            {
              type: 'p',
              className: 'text-center text-lg mt-4',
              children: [{ text: `— ${item.author || 'Unknown'}` }]
            }
          ];
        case 'columns':
          return {
            type: 'div',
            className: 'grid grid-cols-2 gap-6',
            children: [
              {
                type: 'div',
                children: [
                  { type: 'h3', children: [{ text: item.left?.title || 'Left Column' }] },
                  { type: 'p', children: [{ text: item.left?.content || '' }] }
                ]
              },
              {
                type: 'div',
                children: [
                  { type: 'h3', children: [{ text: item.right?.title || 'Right Column' }] },
                  { type: 'p', children: [{ text: item.right?.content || '' }] }
                ]
              }
            ]
          };
        case 'image-text':
          return {
            type: 'div',
            className: 'flex items-center gap-6',
            children: [
              {
                type: 'div',
                className: 'flex-1',
                children: [{ type: 'p', children: [{ text: item.text || '' }] }]
              },
              {
                type: 'div',
                className: 'w-1/3',
                children: [{ type: 'p', children: [{ text: `[${item.imageDescription || 'Relevant Image'}]` }] }]
              }
            ]
          };
        default:
          return { type: 'p', children: [{ text: item.text || '' }] };
      }
    }).flat();
  }

  /**
   * Generate mock content for a specific layout
   */
  private generateMockContentForLayout(layout: SlideLayout): any[] {
    switch (layout) {
      case 'title-slide':
        return [
          { type: 'h1', children: [{ text: 'Presentation Title' }] },
          { type: 'h2', children: [{ text: 'Subtitle' }] },
          { type: 'p', children: [{ text: 'Presenter Name' }] }
        ];
      case 'bullet-points':
        return [
          { type: 'h2', children: [{ text: 'Key Points' }] },
          {
            type: 'ul',
            children: [
              { type: 'li', children: [{ text: 'First key point' }] },
              { type: 'li', children: [{ text: 'Second important point' }] },
              { type: 'li', children: [{ text: 'Third supporting point' }] }
            ]
          }
        ];
      default:
        return [
          { type: 'h2', children: [{ text: 'Slide Title' }] },
          { type: 'p', children: [{ text: 'Content will be generated here.' }] }
        ];
    }
  }

  /**
   * Generate mock content based on slide layout
   */
  private generateMockContent(outlineItem: OutlineItem, metadata: PresentationMetadata): any {
    const { slideLayout, title, description } = outlineItem;

    switch (slideLayout) {
      case 'title-slide':
        return [
          {
            type: 'h1',
            children: [{ text: title }]
          },
          {
            type: 'h2',
            children: [{ text: description }]
          },
          {
            type: 'p',
            children: [{ text: `Presented by: [Your Name]` }]
          }
        ];

      case 'bullet-points':
        return [
          {
            type: 'h2',
            children: [{ text: title }]
          },
          {
            type: 'ul',
            children: [
              { type: 'li', children: [{ text: 'First key point about the topic' }] },
              { type: 'li', children: [{ text: 'Second important consideration' }] },
              { type: 'li', children: [{ text: 'Third supporting argument' }] },
              { type: 'li', children: [{ text: 'Fourth relevant detail' }] }
            ]
          }
        ];

      case 'two-column':
        return [
          {
            type: 'h2',
            children: [{ text: title }]
          },
          {
            type: 'div',
            className: 'grid grid-cols-2 gap-6',
            children: [
              {
                type: 'div',
                children: [
                  { type: 'h3', children: [{ text: 'Key Benefits' }] },
                  { type: 'p', children: [{ text: 'Detailed explanation of the main advantages and positive outcomes.' }] }
                ]
              },
              {
                type: 'div',
                children: [
                  { type: 'h3', children: [{ text: 'Considerations' }] },
                  { type: 'p', children: [{ text: 'Important factors to keep in mind and potential challenges.' }] }
                ]
              }
            ]
          }
        ];

      case 'image-text':
        return [
          {
            type: 'h2',
            children: [{ text: title }]
          },
          {
            type: 'div',
            className: 'flex items-center gap-6',
            children: [
              {
                type: 'div',
                className: 'flex-1',
                children: [
                  { type: 'p', children: [{ text: description }] },
                  { type: 'p', children: [{ text: 'Additional context and supporting information that provides depth to the visual content.' }] }
                ]
              },
              {
                type: 'div',
                className: 'w-1/3',
                children: [{ type: 'p', children: [{ text: '[Relevant Image or Chart]' }] }]
              }
            ]
          }
        ];

      case 'quote':
        return [
          {
            type: 'blockquote',
            className: 'text-center text-2xl italic',
            children: [{ text: `"${description}"` }]
          },
          {
            type: 'p',
            className: 'text-center text-lg mt-4',
            children: [{ text: '— Expert in the field' }]
          }
        ];

      case 'conclusion':
        return [
          {
            type: 'h2',
            children: [{ text: title }]
          },
          {
            type: 'h3',
            children: [{ text: 'Key Takeaways' }]
          },
          {
            type: 'ul',
            children: [
              { type: 'li', children: [{ text: 'Main insight from the presentation' }] },
              { type: 'li', children: [{ text: 'Important conclusion to remember' }] },
              { type: 'li', children: [{ text: 'Action items for the audience' }] }
            ]
          },
          {
            type: 'h3',
            children: [{ text: 'Thank You' }]
          },
          {
            type: 'p',
            children: [{ text: 'Questions and Discussion' }]
          }
        ];

      default:
        return [
          {
            type: 'h2',
            children: [{ text: title }]
          },
          {
            type: 'p',
            children: [{ text: description }]
          },
          {
            type: 'p',
            children: [{ text: 'Additional content and details will be generated here based on your specific topic and requirements.' }]
          }
        ];
    }
  }

  /**
   * Enhance existing slide content
   */
  async enhanceSlide(slide: Slide, instructions: string, selectedModel: string = 'gemini-2.5-flash'): Promise<Slide> {
    if (!this.isConfigured()) {
      console.warn('Gemini AI not configured, returning original slide');
      return slide;
    }

    try {
      // For now, return the original slide
      // TODO: Implement AI-powered enhancement when Gemini integration is stable
      console.log('Enhancing slide:', slide.title, 'with instructions:', instructions);
      return slide;
    } catch (error) {
      console.error('Failed to enhance slide:', error);
      return slide;
    }
  }

  /**
   * Generate speaker notes for a slide
   */
  async generateSpeakerNotes(slide: Slide, selectedModel: string = 'gemini-2.5-flash'): Promise<string> {
    if (!this.isConfigured()) {
      console.warn('Gemini AI not configured, returning fallback speaker notes');
    }

    try {
      // Return fallback notes for now
      return `Speaker notes for "${slide.title}":

1. Start by introducing the main topic of this slide
2. Explain the key points in detail
3. Provide examples or anecdotes to illustrate the concepts
4. Engage the audience with questions or interactive elements
5. Transition smoothly to the next slide

Remember to maintain eye contact with the audience and speak clearly.
Allow time for questions if appropriate.`;
    } catch (error) {
      console.error('Failed to generate speaker notes:', error);
      return `Speaker notes for "${slide.title}": Present the key points clearly and engage with the audience.`;
    }
  }
}

export const presentationAIService = new PresentationAIService();
