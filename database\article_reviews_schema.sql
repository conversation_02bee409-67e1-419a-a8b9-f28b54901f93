-- Create article review tables for storing user article reviews and files
-- This schema supports the AI Article Reviewer with history functionality

-- Table for storing article review metadata and files
CREATE TABLE IF NOT EXISTS public.article_reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT, -- Path in Supabase storage for the uploaded file
    file_type TEXT NOT NULL, -- pdf, docx
    file_size INTEGER NOT NULL,
    ai_model_used TEXT NOT NULL, -- Which AI model was used for the review
    review_status TEXT NOT NULL DEFAULT 'completed', -- pending, processing, completed, error
    overall_score DECIMAL(5,2), -- Overall review score (0-100)
    
    -- Review metrics
    clarity_score DECIMAL(5,2),
    structure_score DECIMAL(5,2),
    methodology_score DECIMAL(5,2),
    significance_score DECIMAL(5,2),
    originality_score DECIMAL(5,2),
    
    -- Summary fields
    review_summary TEXT,
    major_strengths TEXT[],
    major_weaknesses TEXT[],
    recommendations TEXT[],
    
    -- Metadata
    word_count INTEGER DEFAULT 0,
    sections_analyzed TEXT[], -- List of sections that were analyzed
    processing_time_seconds INTEGER, -- How long the review took
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing individual section reviews
CREATE TABLE IF NOT EXISTS public.article_section_reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    article_review_id UUID REFERENCES public.article_reviews(id) ON DELETE CASCADE NOT NULL,
    section_name TEXT NOT NULL, -- introduction, methodology, results, etc.
    section_content TEXT, -- The original content that was reviewed
    analysis TEXT NOT NULL, -- Detailed analysis of the section
    strengths TEXT[], -- Array of strengths identified
    weaknesses TEXT[], -- Array of weaknesses identified
    suggestions TEXT[], -- Array of improvement suggestions
    score DECIMAL(5,2), -- Section score (0-100)
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(article_review_id, section_name)
);

-- Table for storing detailed feedback items (sentence-level feedback)
CREATE TABLE IF NOT EXISTS public.article_detailed_feedback (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    section_review_id UUID REFERENCES public.article_section_reviews(id) ON DELETE CASCADE NOT NULL,
    original_text TEXT NOT NULL, -- The original text segment with issue
    issue_description TEXT NOT NULL, -- Description of the issue
    suggestion TEXT NOT NULL, -- Suggested improvement
    feedback_type TEXT NOT NULL, -- grammar, clarity, structure, content, style, citation, logic
    severity TEXT NOT NULL, -- minor, moderate, major
    line_number INTEGER, -- Approximate line number if available
    highlight_color TEXT, -- Color for highlighting this issue
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing article review tags and categories
CREATE TABLE IF NOT EXISTS public.article_review_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    article_review_id UUID REFERENCES public.article_reviews(id) ON DELETE CASCADE NOT NULL,
    tag_name TEXT NOT NULL,
    tag_type TEXT DEFAULT 'user', -- user, system, auto-generated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(article_review_id, tag_name)
);

-- Table for storing article review exports
CREATE TABLE IF NOT EXISTS public.article_review_exports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    article_review_id UUID REFERENCES public.article_reviews(id) ON DELETE CASCADE NOT NULL,
    export_format TEXT NOT NULL, -- pdf, docx, html
    file_name TEXT NOT NULL,
    file_path TEXT, -- Path in Supabase storage
    file_size INTEGER,
    export_options JSONB DEFAULT '{}', -- Store export configuration
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_article_reviews_user_id ON public.article_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_article_reviews_created_at ON public.article_reviews(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_article_reviews_status ON public.article_reviews(review_status);
CREATE INDEX IF NOT EXISTS idx_article_reviews_score ON public.article_reviews(overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_article_section_reviews_article_id ON public.article_section_reviews(article_review_id);
CREATE INDEX IF NOT EXISTS idx_article_detailed_feedback_section_id ON public.article_detailed_feedback(section_review_id);
CREATE INDEX IF NOT EXISTS idx_article_review_tags_article_id ON public.article_review_tags(article_review_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.article_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_section_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_detailed_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_review_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.article_review_exports ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for article_reviews
CREATE POLICY "Users can view their own article reviews" ON public.article_reviews
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own article reviews" ON public.article_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own article reviews" ON public.article_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own article reviews" ON public.article_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for article_section_reviews
CREATE POLICY "Users can view their own section reviews" ON public.article_section_reviews
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_section_reviews.article_review_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own section reviews" ON public.article_section_reviews
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_section_reviews.article_review_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own section reviews" ON public.article_section_reviews
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_section_reviews.article_review_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own section reviews" ON public.article_section_reviews
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_section_reviews.article_review_id 
            AND user_id = auth.uid()
        )
    );

-- Create RLS policies for article_detailed_feedback
CREATE POLICY "Users can view their own detailed feedback" ON public.article_detailed_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.article_section_reviews asr
            JOIN public.article_reviews ar ON ar.id = asr.article_review_id
            WHERE asr.id = article_detailed_feedback.section_review_id 
            AND ar.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own detailed feedback" ON public.article_detailed_feedback
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.article_section_reviews asr
            JOIN public.article_reviews ar ON ar.id = asr.article_review_id
            WHERE asr.id = article_detailed_feedback.section_review_id 
            AND ar.user_id = auth.uid()
        )
    );

-- Create RLS policies for article_review_tags
CREATE POLICY "Users can manage their own review tags" ON public.article_review_tags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_review_tags.article_review_id 
            AND user_id = auth.uid()
        )
    );

-- Create RLS policies for article_review_exports
CREATE POLICY "Users can manage their own review exports" ON public.article_review_exports
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.article_reviews 
            WHERE id = article_review_exports.article_review_id 
            AND user_id = auth.uid()
        )
    );

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_article_reviews_updated_at 
    BEFORE UPDATE ON public.article_reviews 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically calculate word counts
CREATE OR REPLACE FUNCTION calculate_section_word_count()
RETURNS TRIGGER AS $$
BEGIN
    NEW.word_count = array_length(string_to_array(trim(NEW.section_content), ' '), 1);
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_section_word_count_trigger
    BEFORE INSERT OR UPDATE ON public.article_section_reviews
    FOR EACH ROW EXECUTE FUNCTION calculate_section_word_count();

-- Create function to update article review word count when sections change
CREATE OR REPLACE FUNCTION update_article_word_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.article_reviews 
    SET word_count = (
        SELECT COALESCE(SUM(word_count), 0) 
        FROM public.article_section_reviews 
        WHERE article_review_id = COALESCE(NEW.article_review_id, OLD.article_review_id)
    )
    WHERE id = COALESCE(NEW.article_review_id, OLD.article_review_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_article_word_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.article_section_reviews
    FOR EACH ROW EXECUTE FUNCTION update_article_word_count();
