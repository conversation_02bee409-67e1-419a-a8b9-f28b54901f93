import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  MessageSquare,
  X,
  Send,
  Bot,
  User,
  Loader2,
  Maximize2,
  Minimize2,
  AtSign,
  FileText,
  ChevronUp,
  ChevronDown,
  ArrowLeftRight,
  Info,
  Clock,
  Sparkles,
  Brain,
  Search,
  Copy,
  ThumbsUp,
  Thum<PERSON>Down,
  RotateCcw,
  Plus,
  Mic,
  Paperclip,
  Wand2,
  BookOpen,
  Target,
  Lightbulb,
  Users,
  Calendar,
  Building,
  ExternalLink,
  Hash,
  CheckCircle,
  AlertCircle,
  Zap,
  Settings,
  ChevronDown as ChevronDownIcon
} from "lucide-react";
import { ResearchDocument, AIModel, AIGenerationOptions } from '../types';
import { researchAnalysisService } from '../services/research-analysis.service';
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface EnhancedAIQuestionBubbleProps {
  documents: ResearchDocument[];
  selectedDocuments?: string[];
  onDocumentSelect?: (documentId: string) => void;
  aiSettings?: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
  onAISettingsChange?: (settings: { selectedModel: string; options: AIGenerationOptions }) => void;
}

interface Message {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  referencedDocuments?: string[];
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    confidence?: number;
  };
}

interface MentionSuggestion {
  id: string;
  title: string;
  authors: string[];
  year: number;
  type: 'document' | 'author' | 'keyword';
  relevance?: number;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  prompt: string;
  requiresDocuments?: boolean;
  color: string;
}

const quickActions: QuickAction[] = [
  {
    id: 'summarize',
    label: 'Summarize',
    icon: FileText,
    prompt: 'Please provide a comprehensive summary of the selected documents, highlighting key findings and methodologies.',
    requiresDocuments: true,
    color: 'bg-blue-500'
  },
  {
    id: 'compare',
    label: 'Compare',
    icon: ArrowLeftRight,
    prompt: 'Compare and contrast the methodologies, findings, and conclusions of the selected documents.',
    requiresDocuments: true,
    color: 'bg-purple-500'
  },
  {
    id: 'gaps',
    label: 'Find Gaps',
    icon: Target,
    prompt: 'Identify research gaps and opportunities based on the analysis of these documents.',
    requiresDocuments: true,
    color: 'bg-green-500'
  },
  {
    id: 'methodology',
    label: 'Methodology',
    icon: Brain,
    prompt: 'Analyze the research methodologies used in the selected documents and suggest improvements.',
    requiresDocuments: true,
    color: 'bg-orange-500'
  },
  {
    id: 'trends',
    label: 'Trends',
    icon: Lightbulb,
    prompt: 'What are the emerging trends and patterns across these research documents?',
    requiresDocuments: true,
    color: 'bg-yellow-500'
  },
  {
    id: 'citations',
    label: 'Citations',
    icon: BookOpen,
    prompt: 'Help me understand the citation patterns and key references in these documents.',
    requiresDocuments: true,
    color: 'bg-indigo-500'
  }
];

const availableModels: AIModel[] = [
  {
    id: 'google/gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'Google',
    description: 'Fast and efficient model, great for general research analysis',
    strengths: ['Speed', 'Cost-effective', 'Good reasoning'],
    bestFor: ['Document analysis', 'Literature reviews', 'Quick insights'],
    maxTokens: 8192,
    pricing: 'low'
  },
  {
    id: 'google/gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    description: 'Balanced model with strong analytical capabilities',
    strengths: ['Balanced performance', 'Good at complex analysis', 'Reliable'],
    bestFor: ['Research gap analysis', 'Hypothesis generation', 'Methodology analysis'],
    maxTokens: 32768,
    pricing: 'medium'
  },
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for detailed analysis and academic writing',
    strengths: ['Academic writing', 'Critical thinking', 'Detailed analysis'],
    bestFor: ['Literature reviews', 'Research planning', 'Academic writing'],
    maxTokens: 200000,
    pricing: 'high'
  },
  {
    id: 'anthropic/claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    description: 'Fast and cost-effective model for quick analysis',
    strengths: ['Speed', 'Cost-effective', 'Good for summaries'],
    bestFor: ['Quick summaries', 'Document extraction', 'Fast analysis'],
    maxTokens: 200000,
    pricing: 'low'
  },
  {
    id: 'openai/gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    description: 'Latest GPT-4 model with enhanced capabilities',
    strengths: ['Latest features', 'Strong reasoning', 'Multimodal'],
    bestFor: ['Complex analysis', 'Research planning', 'Creative insights'],
    maxTokens: 128000,
    pricing: 'high'
  },
  {
    id: 'openai/gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    description: 'Powerful model with strong reasoning and analysis capabilities',
    strengths: ['Strong reasoning', 'Creative insights', 'Comprehensive analysis'],
    bestFor: ['Complex analysis', 'Hypothesis generation', 'Research planning'],
    maxTokens: 128000,
    pricing: 'high'
  },
  {
    id: 'openai/gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    description: 'Fast and cost-effective model for general tasks',
    strengths: ['Speed', 'Cost-effective', 'Reliable'],
    bestFor: ['Quick analysis', 'Document summaries', 'General questions'],
    maxTokens: 16385,
    pricing: 'low'
  },
  {
    id: 'meta-llama/llama-3.1-405b',
    name: 'Llama 3.1 405B',
    provider: 'Meta',
    description: 'Large open-source model with excellent analytical capabilities',
    strengths: ['Open source', 'Large context', 'Strong analysis'],
    bestFor: ['Complex research tasks', 'Large document analysis', 'Detailed insights'],
    maxTokens: 131072,
    pricing: 'medium'
  },
  {
    id: 'mistralai/mistral-large',
    name: 'Mistral Large',
    provider: 'Mistral AI',
    description: 'Efficient European model with strong analytical performance',
    strengths: ['Efficiency', 'Privacy-focused', 'Good reasoning'],
    bestFor: ['Document analysis', 'Research synthesis', 'European compliance'],
    maxTokens: 32768,
    pricing: 'medium'
  }
];

const getPricingColor = (pricing: string) => {
  switch (pricing) {
    case 'low': return 'bg-green-100 text-green-700';
    case 'medium': return 'bg-yellow-100 text-yellow-700';
    case 'high': return 'bg-red-100 text-red-700';
    default: return 'bg-gray-100 text-gray-700';
  }
};

export function EnhancedAIQuestionBubble({
  documents,
  selectedDocuments = [],
  onDocumentSelect,
  aiSettings = {
    selectedModel: 'google/gemini-2.5-flash',
    options: {
      temperature: 0.3,
      maxTokens: 4000,
      topP: 0.9
    }
  },
  onAISettingsChange
}: EnhancedAIQuestionBubbleProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [currentAISettings, setCurrentAISettings] = useState(aiSettings);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome',
      type: 'ai',
      content: `👋 **Hi! I'm your AI Research Assistant.**

I can help you analyze papers, compare studies, find research gaps, and answer questions about your documents.

**Quick tip:** Use @ to mention specific papers or try the action buttons above!`,
      timestamp: new Date(),
      metadata: {
        model: 'system',
        confidence: 1.0
      }
    }
  ]);
  
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<MentionSuggestion[]>([]);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionStartPos, setMentionStartPos] = useState<number | null>(null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const [chatHistory, setChatHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle input changes and @ mentions
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;
    
    setInput(value);
    
    // Check for @ mentions
    const beforeCursor = value.slice(0, cursorPos);
    const lastAtIndex = beforeCursor.lastIndexOf('@');
    
    if (lastAtIndex !== -1) {
      const afterAt = beforeCursor.slice(lastAtIndex + 1);
      const spaceIndex = afterAt.indexOf(' ');
      
      if (spaceIndex === -1 || spaceIndex === afterAt.length - 1) {
        // We're in the middle of a mention
        const query = afterAt.trim();
        setMentionQuery(query);
        setMentionStartPos(lastAtIndex);
        setShowSuggestions(true);
        updateSuggestions(query);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  }, []);

  // Update suggestions based on query
  const updateSuggestions = useCallback((query: string) => {
    const documentSuggestions: MentionSuggestion[] = documents
      .filter(doc => 
        doc.title.toLowerCase().includes(query.toLowerCase()) ||
        doc.authors.some(author => author.toLowerCase().includes(query.toLowerCase())) ||
        doc.keywords.some(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
      )
      .map(doc => ({
        id: doc.id,
        title: doc.title,
        authors: doc.authors,
        year: doc.publicationYear,
        type: 'document' as const,
        relevance: calculateRelevance(doc, query)
      }))
      .sort((a, b) => (b.relevance || 0) - (a.relevance || 0))
      .slice(0, 5);

    // Add author suggestions
    const authorSuggestions: MentionSuggestion[] = Array.from(
      new Set(documents.flatMap(doc => doc.authors))
    )
      .filter(author => author.toLowerCase().includes(query.toLowerCase()))
      .map(author => ({
        id: `author-${author}`,
        title: author,
        authors: [author],
        year: 0,
        type: 'author' as const,
        relevance: author.toLowerCase().indexOf(query.toLowerCase()) === 0 ? 1 : 0.5
      }))
      .slice(0, 3);

    // Add keyword suggestions
    const keywordSuggestions: MentionSuggestion[] = Array.from(
      new Set(documents.flatMap(doc => doc.keywords))
    )
      .filter(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
      .map(keyword => ({
        id: `keyword-${keyword}`,
        title: keyword,
        authors: [],
        year: 0,
        type: 'keyword' as const,
        relevance: keyword.toLowerCase().indexOf(query.toLowerCase()) === 0 ? 1 : 0.5
      }))
      .slice(0, 3);

    const allSuggestions = [...documentSuggestions, ...authorSuggestions, ...keywordSuggestions]
      .sort((a, b) => (b.relevance || 0) - (a.relevance || 0));

    setSuggestions(allSuggestions);
    setSelectedSuggestionIndex(0);
  }, [documents]);

  // Calculate relevance score for suggestions
  const calculateRelevance = (document: ResearchDocument, query: string): number => {
    const queryLower = query.toLowerCase();
    let score = 0;
    
    if (document.title.toLowerCase().includes(queryLower)) score += 3;
    if (document.authors.some(author => author.toLowerCase().includes(queryLower))) score += 2;
    if (document.keywords.some(keyword => keyword.toLowerCase().includes(queryLower))) score += 1;
    if (document.abstract.toLowerCase().includes(queryLower)) score += 1;
    
    return score;
  };

  // Handle suggestion selection
  const selectSuggestion = useCallback((suggestion: MentionSuggestion) => {
    if (mentionStartPos === null) return;
    
    const beforeMention = input.slice(0, mentionStartPos);
    const afterMention = input.slice(mentionStartPos + 1 + mentionQuery.length);
    const mentionText = suggestion.type === 'document' 
      ? `[${suggestion.title}](${suggestion.id})`
      : suggestion.title;
    
    const newInput = beforeMention + mentionText + ' ' + afterMention;
    setInput(newInput);
    setShowSuggestions(false);
    setMentionStartPos(null);
    setMentionQuery('');
    
    // Focus back on textarea
    if (textareaRef.current) {
      textareaRef.current.focus();
      const newPos = beforeMention.length + mentionText.length + 1;
      textareaRef.current.setSelectionRange(newPos, newPos);
    }
  }, [input, mentionStartPos, mentionQuery]);

  // Handle keyboard navigation in suggestions
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions && suggestions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestionIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestionIndex(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Enter':
          if (e.shiftKey) {
            // Allow new line with Shift+Enter
            break;
          }
          e.preventDefault();
          if (suggestions[selectedSuggestionIndex]) {
            selectSuggestion(suggestions[selectedSuggestionIndex]);
          } else {
            handleSendMessage();
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowSuggestions(false);
          break;
        case 'Tab':
          e.preventDefault();
          if (suggestions[selectedSuggestionIndex]) {
            selectSuggestion(suggestions[selectedSuggestionIndex]);
          }
          break;
      }
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    } else if (e.key === 'ArrowUp' && input === '' && chatHistory.length > 0) {
      e.preventDefault();
      const newIndex = historyIndex === -1 ? chatHistory.length - 1 : Math.max(0, historyIndex - 1);
      setHistoryIndex(newIndex);
      setInput(chatHistory[newIndex]);
    } else if (e.key === 'ArrowDown' && historyIndex !== -1) {
      e.preventDefault();
      const newIndex = historyIndex < chatHistory.length - 1 ? historyIndex + 1 : -1;
      setHistoryIndex(newIndex);
      setInput(newIndex === -1 ? '' : chatHistory[newIndex]);
    }
  }, [showSuggestions, suggestions, selectedSuggestionIndex, selectSuggestion, input, chatHistory, historyIndex]);

  // Handle sending messages
  const handleSendMessage = useCallback(async () => {
    if (!input.trim() || isProcessing) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: input,
      timestamp: new Date(),
      referencedDocuments: extractReferencedDocuments(input)
    };

    setMessages(prev => [...prev, userMessage]);
    setChatHistory(prev => [...prev, input]);
    setInput('');
    setHistoryIndex(-1);
    setIsProcessing(true);

    try {
      const documentsToQuery = userMessage.referencedDocuments?.length 
        ? documents.filter(d => userMessage.referencedDocuments!.includes(d.id))
        : selectedDocuments.length > 0 
          ? documents.filter(d => selectedDocuments.includes(d.id))
          : documents;

      const startTime = Date.now();
      const response = await researchAnalysisService.answerQuestion(
        input,
        documentsToQuery,
        (progress) => {
          // Optional: Show progress updates
          console.log('AI processing progress:', progress);
        },
        currentAISettings.selectedModel
      );
      const processingTime = Date.now() - startTime;

      const aiMessage: Message = {
        id: `ai-${Date.now()}`,
        type: 'ai',
        content: response,
        timestamp: new Date(),
        referencedDocuments: userMessage.referencedDocuments,
        metadata: {
          model: currentAISettings.selectedModel,
          processingTime,
          confidence: 0.85
        }
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,
        timestamp: new Date(),
        metadata: {
          model: 'error',
          confidence: 0
        }
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  }, [input, isProcessing, documents, selectedDocuments]);

  // Extract referenced documents from input
  const extractReferencedDocuments = useCallback((text: string): string[] => {
    const matches = text.match(/\[([^\]]+)\]\(([^)]+)\)/g);
    return matches ? matches.map(match => {
      const idMatch = match.match(/\(([^)]+)\)/);
      return idMatch ? idMatch[1] : '';
    }).filter(Boolean) : [];
  }, []);

  // Handle quick actions
  const handleQuickAction = useCallback((action: QuickAction) => {
    if (action.requiresDocuments && selectedDocuments.length === 0 && documents.length === 0) {
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `Please select some documents first to use the "${action.label}" action.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    setInput(action.prompt);
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [selectedDocuments, documents]);

  // Copy message content
  const copyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      toast.success('📋 Message copied to clipboard!');
    }).catch(() => {
      toast.error('❌ Failed to copy message');
    });
  }, []);

  // Handle AI model change
  const handleModelChange = useCallback((modelId: string) => {
    const newSettings = {
      ...currentAISettings,
      selectedModel: modelId
    };
    setCurrentAISettings(newSettings);
    onAISettingsChange?.(newSettings);
    setIsModelSelectorOpen(false);

    // Add a system message about the model change
    const modelInfo = availableModels.find(m => m.id === modelId);
    if (modelInfo) {
      const systemMessage: Message = {
        id: `model-change-${Date.now()}`,
        type: 'system',
        content: `🤖 **AI Model changed to ${modelInfo.name}**\n\n${modelInfo.description}`,
        timestamp: new Date(),
        metadata: {
          model: modelId,
          confidence: 1.0
        }
      };
      setMessages(prev => [...prev, systemMessage]);
    }
  }, [currentAISettings, onAISettingsChange]);

  // Clear chat
  const clearChat = useCallback(() => {
    setMessages([
      {
        id: 'welcome',
        type: 'ai',
        content: "Chat cleared! How can I help you with your research documents?",
        timestamp: new Date()
      }
    ]);
  }, []);

  // Get document name from ID
  const getDocumentName = useCallback((id: string) => {
    const doc = documents.find(d => d.id === id);
    return doc ? doc.title : 'Unknown Document';
  }, [documents]);

  // Enhanced markdown formatting for AI responses
  const formatMessageContent = (content: string, isUserMessage = false) => {
    if (isUserMessage) {
      // Simple formatting for user messages
      return content
        .replace(/\n/g, '<br/>')
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
        .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>');
    }

    // Enhanced formatting for AI messages with proper containment
    let formatted = content;

    // Process headers with proper styling and contained widths
    formatted = formatted
      .replace(/^### (.*$)/gm, '<h3 class="text-base font-bold text-gray-900 mb-2 mt-3 pb-1 border-b border-gray-200 break-words">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-lg font-bold text-gray-900 mb-3 mt-4 pb-2 border-b-2 border-blue-200 break-words">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-xl font-bold text-gray-900 mb-4 mt-4 pb-2 border-b-2 border-blue-300 break-words">$1</h1>');

    // Process bold and italic text first (before other formatting)
    formatted = formatted
      .replace(/\*\*([^*]+)\*\*/g, '<strong class="font-semibold text-gray-900 bg-yellow-50 px-1 rounded">$1</strong>')
      .replace(/\*([^*]+)\*/g, '<em class="italic text-gray-700">$1</em>');

    // Process lists with proper containment
    formatted = formatted
      .replace(/^[-*+] (.+)$/gm, '<div class="flex items-start gap-2 mb-2 text-sm"><span class="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span><span class="text-gray-800 leading-relaxed break-words">$1</span></div>')
      .replace(/^(\d+)\. (.+)$/gm, '<div class="flex items-start gap-2 mb-2 text-sm"><span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full min-w-[20px] text-center mt-0.5 flex-shrink-0">$1</span><span class="text-gray-800 leading-relaxed break-words">$2</span></div>');

    // Process inline code with containment
    formatted = formatted.replace(/`([^`]+)`/g, '<code class="bg-blue-50 text-blue-800 px-1.5 py-0.5 rounded text-xs font-mono border border-blue-200 break-all">$1</code>');

    // Process code blocks with proper containment
    formatted = formatted.replace(/```[\s\S]*?```/g, (match) => {
      const codeContent = match.replace(/```(\w+)?\n?/, '').replace(/```$/, '');
      return `<div class="my-3 rounded-lg border border-gray-300 overflow-hidden">
        <div class="bg-gray-100 px-3 py-2 text-xs text-gray-700 border-b border-gray-300">Code</div>
        <pre class="bg-gray-50 p-3 text-xs font-mono text-gray-800 overflow-x-auto whitespace-pre-wrap break-words"><code>${codeContent.trim()}</code></pre>
      </div>`;
    });

    // Process links with containment
    formatted = formatted.replace(
      /\[([^\]]+)\]\(([^)]+)\)/g, 
      '<a href="$2" class="text-blue-600 hover:text-blue-800 underline break-words" target="_blank" rel="noopener noreferrer">$1</a>'
    );

    // Process blockquotes
    formatted = formatted.replace(
      /^> (.+)$/gm, 
      '<div class="border-l-3 border-blue-400 bg-blue-50 p-2 my-2 italic text-gray-700 rounded-r text-sm break-words">$1</div>'
    );

    // Handle line breaks and paragraphs with proper spacing
    formatted = formatted
      .replace(/\n\n+/g, '</p><p class="mb-3 text-sm leading-relaxed text-gray-800">')
      .replace(/\n/g, '<br/>');

    // Wrap everything in a paragraph if not already formatted
    if (!formatted.includes('<h') && !formatted.includes('<div') && !formatted.includes('<p>')) {
      formatted = `<p class="text-sm leading-relaxed text-gray-800 break-words">${formatted}</p>`;
    } else if (formatted.includes('</p><p>')) {
      formatted = `<p class="mb-3 text-sm leading-relaxed text-gray-800 break-words">${formatted}</p>`;
    }

    return formatted;
  };

  return (
    <>
      {/* Floating Chat Button - Improved positioning */}
      <div className="fixed bottom-8 right-8 z-40">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => setIsOpen(true)}
                className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 border-2 border-white/20"
              >
                <div className="relative">
                  <Bot className="h-5 w-5 text-white" />
                  {selectedDocuments.length > 0 && (
                    <div className="absolute -top-1 -right-1 bg-emerald-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium">
                      {selectedDocuments.length}
                    </div>
                  )}
                  {documents.length === 0 && (
                    <div className="absolute -top-1 -right-1 bg-amber-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                      !
                    </div>
                  )}
                </div>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="max-w-xs">
              <div className="text-center">
                <p className="font-medium">AI Research Assistant</p>
                <p className="text-xs text-gray-600 mt-1">
                  {selectedDocuments.length > 0
                    ? `Analyze ${selectedDocuments.length} selected documents`
                    : documents.length > 0
                      ? `Ask questions about ${documents.length} documents`
                      : 'Upload documents to get started'
                  }
                </p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Enhanced Chat Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent 
          className={cn(
            "p-0 gap-0 transition-all duration-300 overflow-hidden",
            isFullScreen ? "max-w-[95vw] max-h-[95vh] h-[95vh] w-[95vw]" : "max-w-4xl max-h-[85vh] h-[85vh] w-full"
          )}
          aria-describedby="ai-chat-description"
        >
          <DialogHeader className="sr-only">
            <DialogTitle>AI Research Assistant</DialogTitle>
            <DialogDescription id="ai-chat-description">
              Chat with your AI research assistant to analyze documents, find connections, and get insights.
            </DialogDescription>
          </DialogHeader>
          
          {/* Fixed Header */}
          <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 shrink-0">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div>
                <h2 className="text-sm font-semibold text-gray-900">AI Research Assistant</h2>
                <p className="text-xs text-gray-600">
                  {selectedDocuments.length > 0 
                    ? `${selectedDocuments.length} selected`
                    : `${documents.length} available`
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* AI Model Selector */}
              <Popover open={isModelSelectorOpen} onOpenChange={setIsModelSelectorOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-3 text-xs font-medium bg-white hover:bg-gray-50 border-gray-200"
                  >
                    <Brain className="h-3 w-3 mr-2 text-blue-600" />
                    <span className="max-w-24 truncate">
                      {availableModels.find(m => m.id === currentAISettings.selectedModel)?.name || 'Select Model'}
                    </span>
                    <ChevronDownIcon className="h-3 w-3 ml-2 text-gray-400" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 p-0" align="end">
                  <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
                    <h4 className="font-semibold text-sm text-gray-900">Select AI Model</h4>
                    <p className="text-xs text-gray-600 mt-1">Choose the best AI model for your research analysis</p>
                  </div>
                  <div className="max-h-80 overflow-y-auto">
                    {availableModels.map((model) => (
                      <div
                        key={model.id}
                        className={cn(
                          "p-4 cursor-pointer hover:bg-gray-50 border-b last:border-b-0 transition-all duration-200",
                          currentAISettings.selectedModel === model.id && "bg-blue-50 border-blue-200 shadow-sm"
                        )}
                        onClick={() => handleModelChange(model.id)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-semibold text-sm text-gray-900">{model.name}</span>
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full font-medium",
                                getPricingColor(model.pricing)
                              )}>
                                {model.pricing}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600 mb-2 leading-relaxed">{model.description}</p>
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-xs font-medium text-gray-700">{model.provider}</span>
                              <span className="text-xs text-gray-400">•</span>
                              <span className="text-xs text-gray-600">{model.maxTokens.toLocaleString()} tokens</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {model.strengths.slice(0, 3).map((strength, idx) => (
                                <span key={idx} className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded">
                                  {strength}
                                </span>
                              ))}
                            </div>
                          </div>
                          {currentAISettings.selectedModel === model.id && (
                            <CheckCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-1" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearChat}
                      className="h-8 w-8 p-0"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Clear chat</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsFullScreen(!isFullScreen)}
                      className="h-8 w-8 p-0"
                    >
                      {isFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isFullScreen ? 'Exit fullscreen' : 'Fullscreen'}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="px-4 py-2 border-b bg-gray-50/50 shrink-0">
            <div className="flex flex-wrap gap-1">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction(action)}
                  className={cn(
                    "text-xs h-7 px-2 bg-white hover:bg-gray-50 border-gray-200 transition-all",
                    action.requiresDocuments && selectedDocuments.length === 0 && documents.length === 0 
                      ? "opacity-50 cursor-not-allowed" 
                      : "hover:border-blue-300"
                  )}
                  disabled={action.requiresDocuments && selectedDocuments.length === 0 && documents.length === 0}
                >
                  <action.icon className="h-3 w-3 mr-1" />
                  {action.label}
                </Button>
              ))}
            </div>
          </div>

          {/* ChatGPT-style Messages Area */}
          <div className="flex-1 overflow-y-auto" ref={scrollAreaRef}>
            <div className="w-full">
              {messages.map((message, index) => (
                <div
                  key={message.id}
                  className={cn(
                    "w-full border-b border-gray-100 hover:bg-gray-50/50 transition-colors group",
                    message.type === 'user' && "bg-gray-50"
                  )}
                >
                  <div className="max-w-4xl mx-auto px-4 py-6">
                    <div className="flex gap-4">
                      {/* Avatar */}
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                        message.type === 'user'
                          ? 'bg-emerald-600 text-white'
                          : message.type === 'ai' 
                            ? 'bg-purple-600 text-white'
                            : 'bg-orange-600 text-white'
                      )}>
                        {message.type === 'user' ? (
                          <User className="h-4 w-4" />
                        ) : message.type === 'ai' ? (
                          <Bot className="h-4 w-4" />
                        ) : (
                          <AlertCircle className="h-4 w-4" />
                        )}
                      </div>
                      
                      {/* Message Content */}
                      <div className="flex-1 min-w-0">
                        {/* Message header with timestamp */}
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-semibold text-gray-900">
                            {message.type === 'user' ? 'You' : 'Assistant'}
                          </span>
                          <span className="text-xs text-gray-500">
                            {message.timestamp.toLocaleTimeString([], { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </span>
                          {message.metadata?.confidence && (
                            <Badge variant="secondary" className="text-xs">
                              {Math.round(message.metadata.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        
                        {/* Message content with proper word wrapping */}
                        <div className="prose prose-sm max-w-none">
                          <div 
                            className={cn(
                              "text-gray-800 leading-relaxed",
                              message.type === 'user' && "whitespace-pre-wrap"
                            )}
                            style={{
                              wordBreak: 'break-word',
                              overflowWrap: 'break-word',
                              maxWidth: '100%'
                            }}
                            dangerouslySetInnerHTML={{ 
                              __html: formatMessageContent(message.content, message.type === 'user')
                            }}
                          />
                        </div>
                        
                        {/* Referenced documents */}
                        {message.referencedDocuments && message.referencedDocuments.length > 0 && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center gap-2 text-sm font-medium text-blue-800 mb-2">
                              <FileText className="h-4 w-4" />
                              Referenced Documents
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {message.referencedDocuments.map(docId => (
                                <Badge 
                                  key={docId} 
                                  variant="secondary" 
                                  className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer"
                                  onClick={() => onDocumentSelect?.(docId)}
                                >
                                  {getDocumentName(docId)}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Message actions */}
                        <div className="flex items-center gap-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyMessage(message.content)}
                            className="h-8 px-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy
                          </Button>
                          
                          {message.type === 'ai' && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toast.success('👍 Thanks for the feedback!')}
                                className="h-8 px-2 text-gray-500 hover:text-green-600 hover:bg-green-50"
                              >
                                <ThumbsUp className="h-3 w-3 mr-1" />
                                Good
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toast.info('👎 Feedback noted')}
                                className="h-8 px-2 text-gray-500 hover:text-red-600 hover:bg-red-50"
                              >
                                <ThumbsDown className="h-3 w-3 mr-1" />
                                Bad
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Typing indicator */}
              {isProcessing && (
                <div className="w-full border-b border-gray-100 bg-gray-50">
                  <div className="max-w-4xl mx-auto px-4 py-6">
                    <div className="flex gap-4">
                      <div className="w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center">
                        <Bot className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-semibold text-gray-900">Assistant</span>
                          <span className="text-xs text-gray-500">thinking...</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">Analyzing your request...</span>
                          <div className="flex gap-1 ml-2">
                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Fixed Input Area */}
          <div className="border-t bg-white shrink-0">
            <div className="p-4">
              <div className="relative">
                {/* Suggestions dropdown */}
                {showSuggestions && suggestions.length > 0 && (
                  <div 
                    ref={suggestionsRef}
                    className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto z-10"
                  >
                    <div className="p-2 border-b bg-gray-50">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <AtSign className="h-3 w-3" />
                        <span>Suggestions</span>
                      </div>
                    </div>
                    {suggestions.map((suggestion, index) => (
                      <div
                        key={suggestion.id}
                        className={cn(
                          "p-3 cursor-pointer hover:bg-gray-50 border-b last:border-b-0",
                          index === selectedSuggestionIndex && "bg-blue-50"
                        )}
                        onClick={() => selectSuggestion(suggestion)}
                      >
                        <div className="flex items-center gap-2">
                          {suggestion.type === 'document' && <FileText className="h-4 w-4 text-blue-500" />}
                          {suggestion.type === 'author' && <Users className="h-4 w-4 text-green-500" />}
                          {suggestion.type === 'keyword' && <Hash className="h-4 w-4 text-purple-500" />}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{suggestion.title}</div>
                            {suggestion.type === 'document' && (
                              <div className="text-xs text-gray-500 truncate">
                                {suggestion.authors.join(', ')} • {suggestion.year}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Input field */}
                <div className="flex items-end gap-3">
                  <div className="flex-1 relative">
                    <Textarea
                      ref={textareaRef}
                      value={input}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyDown}
                      placeholder="Ask me anything about your research documents... Use @ to mention papers."
                      className={cn(
                        "min-h-[50px] max-h-32 resize-none pr-12 bg-gray-50 border-gray-200 rounded-xl focus:bg-white focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 transition-all",
                        isProcessing && "opacity-50 cursor-not-allowed"
                      )}
                      disabled={isProcessing}
                    />
                    <div className="absolute right-3 bottom-3 text-xs text-gray-400">
                      {input.length}/2000
                    </div>
                  </div>
                  
                  <Button
                    onClick={handleSendMessage}
                    disabled={!input.trim() || isProcessing}
                    className={cn(
                      "h-12 w-12 rounded-xl transition-all shadow-lg",
                      !input.trim() || isProcessing
                        ? "bg-gray-300 hover:bg-gray-300 cursor-not-allowed"
                        : "bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 hover:scale-105"
                    )}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-5 w-5 animate-spin text-white" />
                    ) : (
                      <Send className="h-5 w-5 text-white" />
                    )}
                  </Button>
                </div>
                
                {/* Input hints */}
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-3 text-xs text-gray-500">
                    <span>@ for mentions</span>
                    <span>Shift+Enter for new line</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {selectedDocuments.length > 0 && (
                      <span>{selectedDocuments.length} documents selected</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
