import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Database,
  BarChart3,
  MessageSquare,
  Brain,
  Upload,
  FileText,
  TrendingUp,
  Lightbulb,
  Eye,
  Settings,
  RefreshCw,
  Download,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react";

import { FileUploader } from './components/FileUploader';
import { AnalysisPipeline } from './components/AnalysisPipeline';
import { AdvancedAnalysisPipeline } from './components/AdvancedAnalysisPipeline';
import { EnhancedVisualizationGallery } from './components/EnhancedVisualizationGallery';
import { EnhancedQueryInterface } from './components/EnhancedQueryInterface';
import { GeminiApiTest } from './components/GeminiApiTest';
import { VizChatInterface } from './components/chat/VizChatInterface';
import { DataVisualizationErrorBoundary } from './components/ErrorBoundary';
import { useDataVisualizationStore } from './stores/data-visualization.store';
import { UploadedFile, DataAnalysisResult } from './types';

const DataVisualizationPlatformContent: React.FC = () => {
  const {
    uploadedFiles,
    currentFile,
    currentAnalysis,
    activeTab,
    setActiveTab,
    setCurrentFile,
    isUploading,
    isAnalyzing,
    isQuerying,
    isChatGenerating,
    errors,
    clearErrors
  } = useDataVisualizationStore();

  const [stats, setStats] = useState({
    filesProcessed: 0,
    visualizationsCreated: 0,
    queriesAnswered: 0,
    insightsGenerated: 0
  });

  const [showApiTest, setShowApiTest] = useState(!import.meta.env.VITE_GEMINI_API_KEY);

  useEffect(() => {
    // Update stats based on store state
    setStats({
      filesProcessed: uploadedFiles.filter(f => f.status === 'ready').length,
      visualizationsCreated: currentAnalysis?.visualizations.length || 0,
      queriesAnswered: 0, // TODO: Track from query responses
      insightsGenerated: currentAnalysis?.insights.keyFindings.length || 0
    });
  }, [uploadedFiles, currentAnalysis]);

  const handleFileProcessed = (fileId: string) => {
    console.log('File processed:', fileId);
    const file = uploadedFiles.find(f => f.id === fileId);
    console.log('Found file:', file);
    if (file) {
      setCurrentFile(file);
      setActiveTab('analysis');
      toast.success('File uploaded successfully! You can now proceed to analysis.');
    }
  };

  const handleAnalysisComplete = (result: DataAnalysisResult) => {
    setActiveTab('visualizations');
    toast.success('Analysis complete! Check out your visualizations.');
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'upload':
        return <Upload className="h-4 w-4" />;
      case 'analysis':
        return <Brain className="h-4 w-4" />;
      case 'visualizations':
        return <BarChart3 className="h-4 w-4" />;
      case 'query':
        return <MessageSquare className="h-4 w-4" />;
      case 'chat':
        return <Sparkles className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getTabStatus = (tab: string) => {
    switch (tab) {
      case 'upload':
        return uploadedFiles.length > 0 ? 'completed' : 'pending';
      case 'analysis':
        return currentAnalysis ? 'completed' : currentFile ? 'available' : 'disabled';
      case 'visualizations':
        return currentAnalysis?.visualizations.length ? 'completed' : 'disabled';
      case 'query':
        return currentFile ? 'available' : 'disabled';
      default:
        return 'pending';
    }
  };

  const isTabDisabled = (tab: string) => {
    switch (tab) {
      case 'analysis':
        return !currentFile;
      case 'visualizations':
        return !currentAnalysis;
      case 'query':
        return !currentFile;
      case 'chat':
        return !currentFile;
      default:
        return false;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Database className={`h-8 w-8 text-blue-600 ${(isUploading || isAnalyzing || isQuerying) ? 'animate-pulse' : ''}`} />
                {(isUploading || isAnalyzing || isQuerying) && (
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full animate-ping" />
                )}
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Data Visualization & Analysis</h1>
                <p className="text-sm text-gray-500">AI-powered data insights and interactive visualizations</p>
              </div>
            </div>

            {/* Stats */}
            <div className="hidden md:flex items-center gap-6">
              <div className="text-center">
                <p className="text-lg font-bold text-blue-600">{stats.filesProcessed}</p>
                <p className="text-xs text-gray-500">Files</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-green-600">{stats.visualizationsCreated}</p>
                <p className="text-xs text-gray-500">Charts</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-purple-600">{stats.insightsGenerated}</p>
                <p className="text-xs text-gray-500">Insights</p>
              </div>
            </div>

            {/* API Test Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowApiTest(!showApiTest)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              {showApiTest ? 'Hide' : 'Show'} API Test
            </Button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {errors.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearErrors}
                className="mt-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* API Test Panel */}
      {showApiTest && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <GeminiApiTest />
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
          {/* Tab Navigation */}
          <Card>
            <CardContent className="p-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger 
                  value="upload" 
                  disabled={isTabDisabled('upload')}
                  className="flex items-center gap-2"
                >
                  {getTabIcon('upload')}
                  <span className="hidden sm:inline">Upload Data</span>
                  {getTabStatus('upload') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500 ml-1" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="analysis" 
                  disabled={isTabDisabled('analysis')}
                  className="flex items-center gap-2"
                >
                  {getTabIcon('analysis')}
                  <span className="hidden sm:inline">Analysis</span>
                  {isAnalyzing && <Clock className="h-3 w-3 text-blue-500 ml-1 animate-spin" />}
                  {getTabStatus('analysis') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500 ml-1" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="visualizations" 
                  disabled={isTabDisabled('visualizations')}
                  className="flex items-center gap-2"
                >
                  {getTabIcon('visualizations')}
                  <span className="hidden sm:inline">Visualizations</span>
                  {getTabStatus('visualizations') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500 ml-1" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger
                  value="query"
                  disabled={isTabDisabled('query')}
                  className="flex items-center gap-2"
                >
                  {getTabIcon('query')}
                  <span className="hidden sm:inline">Ask Questions</span>
                  {isQuerying && <Clock className="h-3 w-3 text-blue-500 ml-1 animate-spin" />}
                </TabsTrigger>

                <TabsTrigger
                  value="chat"
                  disabled={isTabDisabled('chat')}
                  className="flex items-center gap-2"
                >
                  {getTabIcon('chat')}
                  <span className="hidden sm:inline">Chat Viz</span>
                  {isChatGenerating && <Clock className="h-3 w-3 text-blue-500 ml-1 animate-spin" />}
                </TabsTrigger>
              </TabsList>

              {/* Current File Info */}
              {currentFile && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="font-medium text-blue-900">{currentFile.name}</p>
                      <p className="text-sm text-blue-600">
                        {currentFile.data.length} rows × {currentFile.headers.length} columns
                      </p>
                    </div>
                    <Badge variant="outline" className="ml-auto">
                      {currentFile.status}
                    </Badge>
                  </div>
                </div>
              )}

              {/* Debug Info */}
              <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs">
                <p><strong>Debug:</strong></p>
                <p>Uploaded Files: {uploadedFiles.length}</p>
                <p>Current File: {currentFile ? currentFile.name : 'None'}</p>
                <p>Active Tab: {activeTab}</p>
                <p>Analysis Tab Disabled: {isTabDisabled('analysis') ? 'Yes' : 'No'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Tab Content */}
          <TabsContent value="upload" className="space-y-6">
            <FileUploader onFileProcessed={handleFileProcessed} />
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            {currentFile ? (
              <AdvancedAnalysisPipeline
                file={currentFile}
                onAnalysisComplete={handleAnalysisComplete}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Upload a data file to start the analysis
                      </p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="visualizations" className="space-y-6">
            {currentAnalysis && currentAnalysis.visualizations ? (
              <EnhancedVisualizationGallery analysisResult={currentAnalysis} />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Analysis Available</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Run the analysis pipeline to generate visualizations
                      </p>
                    </div>
                    <Button 
                      onClick={() => setActiveTab('analysis')} 
                      variant="outline"
                      disabled={!currentFile}
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="query" className="space-y-6">
            {currentFile ? (
              <EnhancedQueryInterface file={currentFile} />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Upload a data file to start asking questions
                      </p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="chat" className="space-y-6">
            <div className="h-[800px]">
              <VizChatInterface />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// Main component wrapped with error boundary
export const DataVisualizationPlatform: React.FC = () => {
  return (
    <DataVisualizationErrorBoundary>
      <DataVisualizationPlatformContent />
    </DataVisualizationErrorBoundary>
  );
};
