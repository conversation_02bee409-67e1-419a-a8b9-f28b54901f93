import React, { useState, use<PERSON>emo } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  BarChart3,
  TrendingUp,
  PieChart,
  Activity,
  Search,
  Filter,
  Download,
  Eye,
  Maximize2,
  Grid3X3,
  <PERSON>,
  Sparkles,
  Brain,
  Target,
  Lightbulb,
  Settings,
  Palette,
  Refresh<PERSON><PERSON>,
  Edit3,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './PlotlyChart';
import { DataAnalysisResult, VisualizationConfig } from '../types';

interface EnhancedVisualizationGalleryProps {
  analysisResult: DataAnalysisResult;
  className?: string;
}

interface VisualizationCustomization {
  colorScheme: string;
  chartSize: 'small' | 'medium' | 'large';
  showGrid: boolean;
  showLegend: boolean;
  theme: 'light' | 'dark';
  title?: string;
  subtitle?: string;
}

export const EnhancedVisualizationGallery: React.FC<EnhancedVisualizationGalleryProps> = ({
  analysisResult,
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedVisualization, setSelectedVisualization] = useState<VisualizationConfig | null>(null);
  const [customizations, setCustomizations] = useState<Record<string, VisualizationCustomization>>({});

  const colorSchemes = [
    { value: 'default', label: 'Default', colors: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B'] },
    { value: 'professional', label: 'Professional', colors: ['#1E40AF', '#DC2626', '#059669', '#D97706'] },
    { value: 'vibrant', label: 'Vibrant', colors: ['#2563EB', '#F87171', '#34D399', '#FBBF24'] },
    { value: 'minimal', label: 'Minimal', colors: ['#6B7280', '#374151', '#4B5563', '#9CA3AF'] },
    { value: 'nature', label: 'Nature', colors: ['#16A34A', '#84CC16', '#22C55E', '#65A30D'] },
    { value: 'sunset', label: 'Sunset', colors: ['#F97316', '#EAB308', '#DC2626', '#EC4899'] }
  ];

  const getCustomization = (vizId: string): VisualizationCustomization => {
    return customizations[vizId] || {
      colorScheme: 'default',
      chartSize: 'medium',
      showGrid: true,
      showLegend: true,
      theme: 'light'
    };
  };

  const updateCustomization = (vizId: string, updates: Partial<VisualizationCustomization>) => {
    setCustomizations(prev => ({
      ...prev,
      [vizId]: { ...getCustomization(vizId), ...updates }
    }));
  };

  const applyCustomization = (viz: VisualizationConfig, customization: VisualizationCustomization) => {
    const colorScheme = colorSchemes.find(cs => cs.value === customization.colorScheme);
    const colors = colorScheme?.colors || colorSchemes[0].colors;

    // Apply color scheme
    const updatedData = viz.data.map((trace: any, index: number) => ({
      ...trace,
      marker: {
        ...trace.marker,
        color: colors[index % colors.length],
        opacity: trace.marker?.opacity || 0.8
      }
    }));

    // Apply layout customizations
    const updatedLayout = {
      ...viz.layout,
      title: customization.title || viz.title,
      showlegend: customization.showLegend,
      xaxis: {
        ...viz.layout.xaxis,
        showgrid: customization.showGrid
      },
      yaxis: {
        ...viz.layout.yaxis,
        showgrid: customization.showGrid
      },
      paper_bgcolor: customization.theme === 'dark' ? '#1F2937' : 'white',
      plot_bgcolor: customization.theme === 'dark' ? '#1F2937' : 'white',
      font: {
        ...viz.layout.font,
        color: customization.theme === 'dark' ? '#E5E7EB' : '#374151'
      }
    };

    return {
      ...viz,
      data: updatedData,
      layout: updatedLayout
    };
  };

  const getSizeClass = (size: string) => {
    switch (size) {
      case 'small': return 'h-64';
      case 'medium': return 'h-80';
      case 'large': return 'h-96';
      default: return 'h-80';
    }
  };

  const downloadVisualization = (viz: VisualizationConfig) => {
    toast.success(`Downloading ${viz.title}...`);
  };

  const regenerateVisualization = (viz: VisualizationConfig) => {
    toast.success(`Regenerating ${viz.title} with AI...`);
  };

  // Filter visualizations
  const filteredVisualizations = useMemo(() => {
    return analysisResult.visualizations.filter(viz => {
      const matchesSearch = viz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           viz.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedType === 'all' || viz.type === selectedType;
      return matchesSearch && matchesType;
    });
  }, [analysisResult.visualizations, searchTerm, selectedType]);

  // Get unique visualization types
  const visualizationTypes = useMemo(() => {
    const types = [...new Set(analysisResult.visualizations.map(viz => viz.type))];
    return types;
  }, [analysisResult.visualizations]);

  const getVisualizationIconJSX = (type: string) => {
    switch (type) {
      case 'histogram':
        return <BarChart3 className="h-4 w-4" />;
      case 'scatter':
        return <Activity className="h-4 w-4" />;
      case 'correlation_heatmap':
        return <Activity className="h-4 w-4" />;
      case 'box_plot':
        return <BarChart3 className="h-4 w-4" />;
      case 'bar_chart':
        return <BarChart3 className="h-4 w-4" />;
      case 'line_chart':
        return <LineChart className="h-4 w-4" />;
      case 'pie_chart':
        return <PieChart className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'bar_chart':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'line_chart':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'pie_chart':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'scatter':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Safety check for analysis result structure
  if (!analysisResult || !analysisResult.visualizations) {
    return (
      <div className={`space-y-8 ${className}`}>
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center space-y-3">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <h3 className="font-medium text-gray-900">No Analysis Data</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Please run analysis first to view visualizations
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Ensure insights structure exists
  const safeInsights = {
    keyFindings: analysisResult.insights?.keyFindings || [],
    recommendations: analysisResult.insights?.recommendations || [],
    dataQuality: analysisResult.insights?.dataQuality || 'No quality assessment available',
    patterns: analysisResult.insights?.patterns || [],
    correlations: analysisResult.insights?.correlations || []
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl">
              <Eye className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Beautiful Visualizations
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Interactive charts and insights generated from your data analysis
              </p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Analysis Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{analysisResult.visualizations.length}</div>
            <div className="text-sm font-medium text-blue-800">Total Charts</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">{safeInsights.keyFindings.length}</div>
            <div className="text-sm font-medium text-green-800">Key Insights</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">{visualizationTypes.length}</div>
            <div className="text-sm font-medium text-purple-800">Chart Types</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">{analysisResult.summary.rowCount}</div>
            <div className="text-sm font-medium text-orange-800">Data Points</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="visualizations" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 h-12 bg-gray-100/50 rounded-xl">
          <TabsTrigger value="visualizations" className="flex items-center gap-2 h-10">
            <BarChart3 className="h-4 w-4" />
            Interactive Charts
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2 h-10">
            <Brain className="h-4 w-4" />
            AI Insights
          </TabsTrigger>
        </TabsList>

        {/* Visualizations Tab */}
        <TabsContent value="visualizations">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-green-600" />
                  Data Visualizations
                  <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                    {filteredVisualizations.length} charts
                  </Badge>
                </CardTitle>
                
                <div className="flex items-center gap-3">
                  {/* Search */}
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Input
                      placeholder="Search visualizations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>

                  {/* Type Filter */}
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-40">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {visualizationTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          <div className="flex items-center gap-2">
                            {getVisualizationIconJSX(type)}
                            <span className="capitalize">{type.replace('_', ' ')}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* View Mode */}
                  <div className="flex items-center border rounded-lg">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-r-none"
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {filteredVisualizations.length === 0 ? (
                <div className="text-center py-12">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-900 mb-2">No visualizations found</h3>
                  <p className="text-sm text-gray-500">
                    {searchTerm || selectedType !== 'all' 
                      ? 'Try adjusting your search or filter criteria'
                      : 'Run analysis to generate visualizations'
                    }
                  </p>
                </div>
              ) : (
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 lg:grid-cols-2 gap-8'
                    : 'space-y-6'
                }>
                  {filteredVisualizations.map((visualization) => (
                    <div key={visualization.id} className="group">
                      <PlotlyChart
                        visualization={visualization}
                        className="cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                        onError={(error) => console.warn('Visualization error:', error)}
                      />
                      
                      {/* Chart Info */}
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-gray-900">{visualization.title}</h3>
                          <Badge className={getTypeColor(visualization.type)}>
                            {getVisualizationIconJSX(visualization.type)}
                            <span className="ml-1 capitalize">{visualization.type.replace('_', ' ')}</span>
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">{visualization.description}</p>
                        
                        <div className="flex items-center gap-2 mt-3">
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </Button>
                          <Button variant="outline" size="sm">
                            <Maximize2 className="h-4 w-4 mr-2" />
                            Fullscreen
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights">
          <div className="space-y-6">
            {/* Key Findings */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  Key Findings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {safeInsights.keyFindings.map((finding, index) => (
                    <div key={index} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <p className="text-blue-900 leading-relaxed">
                          {typeof finding === 'string' ? finding : JSON.stringify(finding)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recommendations */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-orange-500" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {safeInsights.recommendations.map((recommendation, index) => (
                    <div key={index} className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="flex items-start gap-3">
                        <Lightbulb className="h-5 w-5 text-orange-500 mt-0.5" />
                        <p className="text-orange-900 leading-relaxed">
                          {typeof recommendation === 'string' ? recommendation : JSON.stringify(recommendation)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Data Summary */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-green-600" />
                  Data Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{analysisResult.summary.rowCount}</div>
                    <div className="text-sm text-gray-600">Total Rows</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{analysisResult.summary.columnCount}</div>
                    <div className="text-sm text-gray-600">Columns</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {Object.values(analysisResult.summary.dataTypes).filter(type => type === 'number').length}
                    </div>
                    <div className="text-sm text-gray-600">Numeric</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {Object.values(analysisResult.summary.missingValues).reduce((sum, count) => sum + count, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Missing Values</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
