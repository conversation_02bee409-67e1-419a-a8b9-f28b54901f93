/**
 * Outline Approval Component
 * Displays generated research outline and allows user approval or editing
 */

import React, { useState } from 'react';
import {
  CheckCircle,
  Edit3,
  Clock,
  BookOpen,
  Search,
  Plus,
  Trash2,
  ArrowUp,
  ArrowDown,
  AlertCircle,
  Loader2,
  Target
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

import { DeepResearchOutline, ResearchPoint, OutlineEditRequest } from '../types';

interface OutlineApprovalProps {
  outline: DeepResearchOutline;
  isLoading?: boolean;
  onApprove: (outline: DeepResearchOutline) => void;
  onEdit: (editRequest: OutlineEditRequest) => void;
  onRegenerate: () => void;
  className?: string;
}

export function OutlineApproval({
  outline,
  isLoading = false,
  onApprove,
  onEdit,
  onRegenerate,
  className
}: OutlineApprovalProps) {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editedTitle, setEditedTitle] = useState('');
  const [editedDescription, setEditedDescription] = useState('');
  const [isExpanded, setIsExpanded] = useState(true);

  const handleEditPoint = (point: ResearchPoint) => {
    setEditingSection(point.id);
    setEditedTitle(point.title);
    setEditedDescription(point.description);
  };

  const handleSaveEdit = () => {
    if (!editingSection) return;

    onEdit({
      action: 'edit_point',
      pointId: editingSection,
      editedContent: {
        title: editedTitle,
        description: editedDescription
      }
    });

    setEditingSection(null);
    setEditedTitle('');
    setEditedDescription('');
  };

  const handleCancelEdit = () => {
    setEditingSection(null);
    setEditedTitle('');
    setEditedDescription('');
  };

  const handleRemoveSection = (sectionId: string) => {
    onEdit({
      action: 'remove_section',
      sectionId
    });
  };

  const handleMoveSection = (sectionId: string, direction: 'up' | 'down') => {
    const currentIndex = outline.sections.findIndex(s => s.id === sectionId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= outline.sections.length) return;

    const newOrder = [...outline.sections];
    [newOrder[currentIndex], newOrder[newIndex]] = [newOrder[newIndex], newOrder[currentIndex]];
    
    onEdit({
      action: 'reorder_sections',
      newOrder: newOrder.map(s => s.id)
    });
  };

  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BookOpen className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl text-blue-900">Research Outline</CardTitle>
                <p className="text-sm text-blue-700 mt-1">
                  Review and approve the generated research structure
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-white border-blue-200 text-blue-800">
                <Clock className="w-3 h-3 mr-1" />
                Est. 45-60 min
              </Badge>
              <Badge variant="outline" className="bg-white border-blue-200 text-blue-800">
                {outline.totalPoints} points
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Outline Details */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">{outline.title}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">{outline.description}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {outline.points.map((point, index) => (
                  <Card key={point.id} className="border-gray-200">
                    <CardContent className="p-4">
                      {editingSection === point.id ? (
                        // Edit Mode
                        <div className="space-y-3">
                          <Input
                            value={editedTitle}
                            onChange={(e) => setEditedTitle(e.target.value)}
                            placeholder="Section title"
                            className="font-medium"
                          />
                          <Textarea
                            value={editedDescription}
                            onChange={(e) => setEditedDescription(e.target.value)}
                            placeholder="Section description"
                            rows={2}
                          />
                          <div className="flex items-center gap-2">
                            <Button size="sm" onClick={handleSaveEdit}>
                              Save
                            </Button>
                            <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // View Mode
                        <div>
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="text-xs font-bold">
                                {point.pointNumber}
                              </Badge>
                              <h4 className="font-medium text-gray-900">{point.title}</h4>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMoveSection(point.id, 'up')}
                                disabled={index === 0}
                                className="h-6 w-6 p-0"
                              >
                                <ArrowUp className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMoveSection(point.id, 'down')}
                                disabled={index === outline.points.length - 1}
                                className="h-6 w-6 p-0"
                              >
                                <ArrowDown className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditPoint(point)}
                                className="h-6 w-6 p-0"
                              >
                                <Edit3 className="w-3 h-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveSection(point.id)}
                                disabled={outline.points.length <= 8}
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>

                          <p className="text-sm text-gray-600 mb-3">{point.description}</p>

                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              <Target className="w-3 h-3" />
                              Point {point.pointNumber} of {outline.totalPoints}
                            </div>
                            <div className="flex items-center gap-1">
                              <BookOpen className="w-3 h-3" />
                              {point.subpoints.length} subpoints
                            </div>
                          </div>

                          {/* Subpoints */}
                          {point.subpoints && point.subpoints.length > 0 && (
                            <div className="mt-3 pl-4 border-l-2 border-blue-100">
                              <p className="text-xs font-medium text-blue-600 mb-2">Subpoints to cover:</p>
                              <div className="space-y-2">
                                {point.subpoints.map((subpoint) => (
                                  <div key={subpoint.id} className="bg-blue-50 p-2 rounded">
                                    <div className="text-xs font-medium text-blue-800">
                                      {subpoint.subpointNumber}. {subpoint.title}
                                    </div>
                                    <div className="text-xs text-blue-600 mt-1">
                                      {subpoint.description}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        )}
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Alert className="border-amber-200 bg-amber-50">
            <AlertCircle className="w-4 h-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Review the outline carefully. This will guide your deep research process.
            </AlertDescription>
          </Alert>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={onRegenerate}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Plus className="w-4 h-4 mr-2" />
            )}
            Regenerate
          </Button>

          <Button
            onClick={() => onApprove(outline)}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Approve & Start Research
          </Button>
        </div>
      </div>
    </div>
  );
}
