/**
 * WriteTool - <PERSON>les cases where complete section rewrites are necessary
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { ToolContext, ToolResult, WriteToolResult, AnalysisResult } from '../types';
import { enhancedAIService } from '../../../paper-generator/enhanced-ai.service';

export class WriteTool extends BaseAgentTool {
  constructor() {
    super(
      'write-tool',
      'Content Writer',
      'Handles cases where complete section rewrites are necessary'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { userRequest, documentContent, previousResults, metadata } = context;
    
    console.log(`✍️ [WriteTool] Generating new content for: "${userRequest}"`);

    try {
      // Get analysis results from previous step
      const analysisResult = this.extractAnalysisResult(previousResults);
      if (!analysisResult) {
        throw new Error('Analysis result not found from previous steps');
      }

      // Check if this tool should be used
      if (!this.shouldUseWriteTool(analysisResult, userRequest)) {
        return this.createSuccessResult(
          startTime,
          {
            newContent: '',
            sections: []
          } as WriteToolResult['data'],
          'WriteTool not needed for this request',
          0.5
        );
      }

      console.log(`📝 [WriteTool] Generating content for ${analysisResult.targetSections.length} section(s)`);

      // Generate new content
      const writeResult = await this.generateNewContent(
        analysisResult,
        userRequest,
        documentContent,
        metadata?.options
      );

      const confidence = this.calculateWriteConfidence(writeResult, analysisResult);
      const reasoning = this.generateWriteReasoning(writeResult, analysisResult);

      return this.createSuccessResult(
        startTime,
        writeResult,
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Content generation failed: ${error.message}`,
        ['Try a more specific request', 'Check if the document provides enough context', 'Consider breaking down the request into smaller parts']
      );
    }
  }

  /**
   * Extract analysis result from previous tool executions
   */
  private extractAnalysisResult(previousResults?: ToolResult[]): AnalysisResult | null {
    if (!previousResults) return null;
    
    const analysisResult = previousResults.find(r => r.toolId === 'analysis-tool' && r.success);
    return analysisResult?.data || null;
  }

  /**
   * Determine if WriteTool should be used
   */
  private shouldUseWriteTool(analysis: AnalysisResult, userRequest: string): boolean {
    const request = userRequest.toLowerCase();
    
    // Explicit write requests
    if (request.includes('write') || request.includes('create') || request.includes('generate')) {
      return true;
    }

    // Analysis indicates rewrite is needed
    if (analysis.editType === 'rewrite') {
      return true;
    }

    // Complex requests that might need new content
    if (analysis.complexity === 'complex' && analysis.scope === 'section') {
      return true;
    }

    // Large-scale content additions
    if (analysis.editType === 'insert' && analysis.scope === 'section') {
      return true;
    }

    return false;
  }

  /**
   * Generate new content based on analysis
   */
  private async generateNewContent(
    analysis: AnalysisResult,
    userRequest: string,
    documentContent: string,
    options: any
  ): Promise<WriteToolResult['data']> {
    const sections: Array<{
      title: string;
      content: string;
      startPosition: number;
      endPosition: number;
    }> = [];

    let newContent = '';

    // Generate content for each target section
    for (const targetSection of analysis.targetSections) {
      try {
        const sectionContent = await this.generateSectionContent(
          targetSection,
          analysis,
          userRequest,
          documentContent,
          options
        );

        if (sectionContent) {
          sections.push({
            title: this.generateSectionTitle(targetSection, analysis),
            content: sectionContent,
            startPosition: targetSection.startPosition,
            endPosition: targetSection.endPosition
          });

          newContent += sectionContent + '\n\n';
        }
      } catch (error: any) {
        console.warn(`⚠️ [WriteTool] Failed to generate content for section: ${error.message}`);
      }
    }

    return {
      newContent: newContent.trim(),
      sections
    };
  }

  /**
   * Generate content for a specific section
   */
  private async generateSectionContent(
    targetSection: any,
    analysis: AnalysisResult,
    userRequest: string,
    documentContent: string,
    options: any
  ): Promise<string | null> {
    const editMode = options?.editMode || 'moderate';
    
    // Build comprehensive prompt for content generation
    const prompt = this.buildContentGenerationPrompt(
      targetSection,
      analysis,
      userRequest,
      documentContent,
      editMode
    );

    try {
      const generatedContent = await enhancedAIService.generateText(prompt);
      
      if (!generatedContent || generatedContent.trim().length === 0) {
        console.log(`⏭️ [WriteTool] No content generated for section`);
        return null;
      }

      return this.postProcessGeneratedContent(generatedContent, targetSection, analysis);

    } catch (error: any) {
      console.error(`❌ [WriteTool] Content generation failed: ${error.message}`);
      return null;
    }
  }

  /**
   * Build comprehensive prompt for content generation
   */
  private buildContentGenerationPrompt(
    targetSection: any,
    analysis: AnalysisResult,
    userRequest: string,
    documentContent: string,
    editMode: string
  ): string {
    const editModeInstructions = {
      conservative: 'Generate content that closely follows the existing style and structure.',
      moderate: 'Generate well-structured content that improves upon the original while maintaining consistency.',
      aggressive: 'Generate comprehensive, high-quality content that significantly enhances the document.'
    };

    const contextBefore = this.extractContext(documentContent, targetSection.startPosition, 'before');
    const contextAfter = this.extractContext(documentContent, targetSection.endPosition, 'after');

    return `You are an expert academic writer generating high-quality content for a research document.

EDIT MODE: ${editModeInstructions[editMode as keyof typeof editModeInstructions]}

USER REQUEST: "${userRequest}"

SECTION TYPE: ${targetSection.sectionType || 'general'}

REQUIREMENTS:
${analysis.requirements.map(req => `- ${req}`).join('\n')}

CONTEXT BEFORE:
"${contextBefore}"

ORIGINAL SECTION:
"${targetSection.text}"

CONTEXT AFTER:
"${contextAfter}"

INSTRUCTIONS:
1. Generate content that directly addresses the user's request
2. Maintain consistency with the surrounding context
3. Follow academic writing standards
4. Ensure smooth transitions with adjacent content
5. Include relevant details and examples where appropriate
6. Match the tone and style of the existing document
7. Return only the new content, no explanations or meta-text

GENERATED CONTENT:`;
  }

  /**
   * Extract context around a position
   */
  private extractContext(content: string, position: number, direction: 'before' | 'after'): string {
    const contextLength = 300;
    
    if (direction === 'before') {
      const start = Math.max(0, position - contextLength);
      return content.substring(start, position).trim();
    } else {
      const end = Math.min(content.length, position + contextLength);
      return content.substring(position, end).trim();
    }
  }

  /**
   * Post-process generated content
   */
  private postProcessGeneratedContent(
    content: string,
    targetSection: any,
    analysis: AnalysisResult
  ): string {
    let processed = content.trim();

    // Remove any meta-text or explanations
    processed = this.removeMetaText(processed);

    // Ensure proper formatting
    processed = this.ensureProperFormatting(processed, analysis);

    // Validate content quality
    if (!this.validateGeneratedContent(processed, targetSection)) {
      console.warn(`⚠️ [WriteTool] Generated content failed validation`);
      return targetSection.text; // Fallback to original
    }

    return processed;
  }

  /**
   * Remove meta-text from generated content
   */
  private removeMetaText(content: string): string {
    // Remove common meta-text patterns
    const metaPatterns = [
      /^(Here is|Here's|The following is|Below is).*/i,
      /^(Generated content|New content|Improved version).*/i,
      /\*\*[^*]+\*\*/g, // Bold formatting
      /\[Note:.*?\]/gi,
      /\(Note:.*?\)/gi
    ];

    let cleaned = content;
    metaPatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '').trim();
    });

    return cleaned;
  }

  /**
   * Ensure proper formatting
   */
  private ensureProperFormatting(content: string, analysis: AnalysisResult): string {
    let formatted = content;

    // Ensure proper paragraph breaks
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    // Ensure sentences end with proper punctuation
    formatted = formatted.replace(/([^.!?])\s*$/g, '$1.');

    // Capitalize first letter of each sentence
    formatted = formatted.replace(/(^|\. )([a-z])/g, (match, prefix, letter) => 
      prefix + letter.toUpperCase()
    );

    return formatted;
  }

  /**
   * Validate generated content quality
   */
  private validateGeneratedContent(content: string, targetSection: any): boolean {
    // Basic quality checks
    if (content.length < 10) return false;
    if (content.length > targetSection.text.length * 5) return false;
    
    // Check for reasonable sentence structure
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length === 0) return false;
    
    // Check for reasonable word count
    const words = content.split(/\s+/).filter(w => w.length > 0);
    if (words.length < 5) return false;

    return true;
  }

  /**
   * Generate section title
   */
  private generateSectionTitle(targetSection: any, analysis: AnalysisResult): string {
    const sectionType = targetSection.sectionType || 'content';
    const intent = analysis.intent.replace(/_/g, ' ');
    
    return `${sectionType} (${intent})`.toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Calculate write confidence
   */
  private calculateWriteConfidence(
    writeResult: WriteToolResult['data'],
    analysis: AnalysisResult
  ): number {
    if (writeResult.sections.length === 0) return 0;

    const contentQuality = this.assessContentQuality(writeResult.newContent);
    const coverageScore = writeResult.sections.length / Math.max(analysis.targetSections.length, 1);
    const complexityPenalty = analysis.complexity === 'complex' ? 0.9 : 1.0;

    return this.calculateConfidence({
      resultQuality: contentQuality,
      contextRelevance: Math.min(coverageScore, 1),
      requestClarity: 0.8
    }) * complexityPenalty;
  }

  /**
   * Assess content quality
   */
  private assessContentQuality(content: string): number {
    if (!content || content.length === 0) return 0;

    let score = 0.5; // Base score

    // Check content length (reasonable but not excessive)
    if (content.length > 50 && content.length < 5000) score += 0.1;

    // Check sentence structure
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length > 0) {
      const avgSentenceLength = content.split(/\s+/).length / sentences.length;
      if (avgSentenceLength > 5 && avgSentenceLength < 30) score += 0.1;
    }

    // Check for academic language patterns
    const academicPatterns = [
      /\b(therefore|however|furthermore|moreover|consequently)\b/gi,
      /\b(research|study|analysis|findings|results)\b/gi,
      /\b(significant|important|crucial|essential)\b/gi
    ];

    academicPatterns.forEach(pattern => {
      if (pattern.test(content)) score += 0.1;
    });

    return Math.min(1, score);
  }

  /**
   * Generate write reasoning
   */
  private generateWriteReasoning(
    writeResult: WriteToolResult['data'],
    analysis: AnalysisResult
  ): string {
    const sectionsGenerated = writeResult.sections.length;
    const totalSections = analysis.targetSections.length;
    const contentLength = writeResult.newContent.length;

    if (sectionsGenerated === 0) {
      return 'No new content was generated. The existing content may already be sufficient.';
    }

    return `Generated ${sectionsGenerated}/${totalSections} section(s) with ${contentLength} characters of new content. ` +
           `Focused on ${analysis.intent.replace(/_/g, ' ')} with ${analysis.editType} approach.`;
  }
}
