import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Bot, FileText, Clock, CheckCircle, Download, Edit, Loader2, BookOpen, Plus, Sparkles, Zap, Brain, PenTool } from "lucide-react";
import { GeneratedSection, PaperMetadata, Citation, PaperSection } from './types';
import { AccordionSectionCard } from './AccordionSectionCard';
import { ExportDialog } from './ExportDialog';
import { SectionCitationDisplay } from './SectionCitationDisplay';
import { CitationDisplay } from './CitationDisplay';

interface GenerationPanelProps {
  generatedSections: GeneratedSection[];
  isGenerating: boolean;
  onExport: () => void;
  onEditInEditor: () => void;
  paperMetadata: PaperMetadata;
  allCitations?: Citation[];
  sectionCitations?: Record<string, string[]>;
  paperSections?: PaperSection[];
  onRegenerateSection?: (sectionId: string) => void;
  onNewPaper?: () => void;
  enhancedCitations?: Record<string, any[]>; // Enhanced citations by section
  useEnhancedDisplay?: boolean; // Whether to use enhanced display
}

export const GenerationPanel: React.FC<GenerationPanelProps> = ({
  generatedSections,
  isGenerating,
  onExport,
  onEditInEditor,
  paperMetadata,
  allCitations = [],
  sectionCitations = {},
  paperSections = [],
  onRegenerateSection,
  onNewPaper,
  enhancedCitations = {},
  useEnhancedDisplay = false
}) => {
  const completedSections = generatedSections.filter(s => s.status === 'completed');
  const allCompleted = generatedSections.every(s => s.status === 'completed');
  const currentlyGenerating = generatedSections.find(s => s.status === 'generating');
  const progressPercentage = (completedSections.length / generatedSections.length) * 100;

  // Animation states
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const [showCompletionAnimation, setShowCompletionAnimation] = useState(false);

  // Animate progress bar
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progressPercentage);
    }, 100);
    return () => clearTimeout(timer);
  }, [progressPercentage]);

  // Show completion animation when all sections are done
  useEffect(() => {
    if (allCompleted && !isGenerating && completedSections.length > 0) {
      setShowCompletionAnimation(true);
      const timer = setTimeout(() => setShowCompletionAnimation(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [allCompleted, isGenerating, completedSections.length]);

  return (
    <div className="space-y-8 h-full">
      {/* Overall Progress Header */}
      {isGenerating && (
        <Card className="shadow-xl border-0 bg-gradient-to-r from-blue-50 via-purple-50 to-green-50 backdrop-blur-sm overflow-hidden">
          <CardContent className="p-6 relative">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-green-200 animate-gradient-shift"></div>
            </div>

            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Brain className="h-10 w-10 text-blue-600 animate-pulse" />
                    <Sparkles className="h-5 w-5 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
                    <div className="absolute inset-0 bg-blue-400 rounded-full opacity-20 animate-ping"></div>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">🧠 AI Research Paper Generation</h2>
                    <p className="text-sm text-gray-600">
                      {currentlyGenerating ? (
                        <span className="flex items-center gap-2">
                          <PenTool className="h-4 w-4 text-blue-600 animate-pulse" />
                          Generating {currentlyGenerating.name} with academic precision...
                        </span>
                      ) : 'Processing your research paper with advanced AI'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-blue-600 mb-1">{Math.round(animatedProgress)}%</div>
                  <div className="text-sm text-gray-500">{completedSections.length}/{generatedSections.length} sections</div>
                  <div className="text-xs text-blue-600 mt-1">
                    {isGenerating ? '⚡ AI Working...' : '✅ Ready'}
                  </div>
                </div>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="relative mb-4">
                <Progress
                  value={animatedProgress}
                  className="h-4 bg-white/50 shadow-inner"
                />
                <div
                  className="absolute top-0 left-0 h-4 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 transition-all duration-1000 shadow-lg"
                  style={{ width: `${animatedProgress}%` }}
                >
                  <div className="absolute inset-0 bg-white/20 animate-pulse rounded-full"></div>
                </div>
              </div>

              {currentlyGenerating && (
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                  <div className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-blue-500 animate-pulse" />
                    <span className="text-blue-700 font-medium">
                      Crafting {currentlyGenerating.name.toLowerCase()} with AI precision
                    </span>
                    <div className="flex gap-1">
                      {[0, 1, 2].map((i) => (
                        <div
                          key={i}
                          className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                          style={{ animationDelay: `${i * 0.2}s` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid lg:grid-cols-2 gap-8 items-start">
      {/* Left Panel - Generation Progress */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm flex flex-col h-full max-h-[calc(100vh-200px)] overflow-hidden">
        <CardHeader className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-100">
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="relative">
              <div className="w-3 h-10 bg-gradient-to-b from-blue-500 to-green-500 rounded-full animate-pulse shadow-lg"></div>
              {isGenerating && (
                <div className="absolute inset-0 w-3 h-10 bg-gradient-to-b from-blue-400 to-green-400 rounded-full animate-ping opacity-50"></div>
              )}
            </div>
            <div className="flex-1">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Generation Progress
              </span>
              {showCompletionAnimation && (
                <div className="flex items-center gap-2 mt-1">
                  <Sparkles className="h-5 w-5 text-yellow-500 animate-bounce" />
                  <span className="text-green-600 text-lg font-bold animate-pulse">🎉 Complete!</span>
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">
                {completedSections.length}/{generatedSections.length}
              </div>
              <div className="text-xs text-blue-600">
                {isGenerating ? 'In Progress' : 'Ready'}
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 overflow-hidden">
          <ScrollArea className="h-full pr-4">
            <div className="space-y-3">
              {generatedSections.map((section, index) => {
                const isActive = section.status === 'generating';
                const isCompleted = section.status === 'completed';
                const isPending = section.status === 'pending';
                const isError = section.status === 'error';

                return (
                  <div
                    key={section.id}
                    className={`
                      relative overflow-hidden rounded-xl border transition-all duration-500 transform
                      ${isActive ? 'border-blue-300 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg scale-105' : ''}
                      ${isCompleted ? 'border-green-300 bg-gradient-to-r from-green-50 to-emerald-50 shadow-md' : ''}
                      ${isPending ? 'border-gray-200 bg-white hover:shadow-sm' : ''}
                      ${isError ? 'border-red-300 bg-gradient-to-r from-red-50 to-pink-50' : ''}
                    `}
                  >
                    {/* Animated background for active section */}
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50 animate-pulse"></div>
                    )}

                    <div className="relative p-4 flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`
                          flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
                          ${isActive ? 'bg-blue-100 shadow-lg' : ''}
                          ${isCompleted ? 'bg-green-100' : ''}
                          ${isPending ? 'bg-gray-100' : ''}
                          ${isError ? 'bg-red-100' : ''}
                        `}>
                          {isActive ? (
                            <PenTool className="h-6 w-6 text-blue-600 animate-pulse" />
                          ) : (
                            <section.icon className={`
                              h-6 w-6 transition-colors duration-300
                              ${isCompleted ? 'text-green-600' : ''}
                              ${isPending ? 'text-gray-600' : ''}
                              ${isError ? 'text-red-600' : ''}
                            `} />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className={`
                            font-semibold transition-colors duration-300
                            ${isActive ? 'text-blue-900' : ''}
                            ${isCompleted ? 'text-green-900' : ''}
                            ${isPending ? 'text-gray-900' : ''}
                            ${isError ? 'text-red-900' : ''}
                          `}>
                            {section.name}
                          </h3>
                          <p className={`
                            text-sm truncate transition-colors duration-300
                            ${isActive ? 'text-blue-700' : ''}
                            ${isCompleted ? 'text-green-700' : ''}
                            ${isPending ? 'text-gray-500' : ''}
                            ${isError ? 'text-red-700' : ''}
                          `}>
                            {isActive ? 'AI is crafting this section...' : section.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        {isPending && (
                          <Badge variant="outline" className="flex items-center gap-2 border-gray-300">
                            <Clock className="h-3 w-3" />
                            Pending
                          </Badge>
                        )}
                        {isActive && (
                          <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 flex items-center gap-2 shadow-lg">
                            <Zap className="h-3 w-3 animate-pulse" />
                            Generating...
                          </Badge>
                        )}
                        {isCompleted && (
                          <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 flex items-center gap-2 shadow-md">
                            <CheckCircle className="h-3 w-3" />
                            Complete
                          </Badge>
                        )}
                        {isError && (
                          <Badge className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 flex items-center gap-2">
                            <CheckCircle className="h-3 w-3" />
                            Error
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Progress indicator for active section */}
                    {isActive && (
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500">
                        <div className="h-full bg-gradient-to-r from-blue-400 to-purple-400 animate-pulse"></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Right Panel - Generated Content */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm flex flex-col h-full max-h-[calc(100vh-200px)] overflow-hidden">
        <CardHeader className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-purple-50 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3 text-2xl">
              <div className="relative">
                <div className={`
                  w-3 h-10 rounded-full transition-all duration-500 shadow-lg
                  ${isGenerating ? 'bg-gradient-to-b from-blue-500 to-purple-500 animate-pulse' : 'bg-gradient-to-b from-green-500 to-purple-500'}
                `}></div>
                {isGenerating && (
                  <div className="absolute inset-0 w-3 h-10 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full animate-ping opacity-50"></div>
                )}
              </div>
              <div className="flex-1">
                <span className="bg-gradient-to-r from-green-600 to-purple-600 bg-clip-text text-transparent">
                  Generated Content
                </span>
                {allCompleted && !isGenerating && completedSections.length > 0 && (
                  <div className="flex items-center gap-1 mt-1">
                    <Sparkles className="h-4 w-4 text-yellow-500 animate-bounce" />
                    <span className="text-green-600 text-sm font-medium">All sections ready!</span>
                  </div>
                )}
              </div>
            </CardTitle>
            {onNewPaper && (
              <Button
                onClick={onNewPaper}
                variant="outline"
                className="flex items-center gap-2 hover:shadow-md hover:scale-105 transition-all duration-200 bg-white/80 backdrop-blur-sm border-purple-200 hover:border-purple-300"
              >
                <Plus className="h-4 w-4" />
                New Paper
              </Button>
            )}
          </div>
          <CardDescription className={`
            text-lg transition-all duration-300
            ${isGenerating ? 'text-blue-600' : 'text-gray-600'}
          `}>
            {completedSections.length === 0
              ? (isGenerating ? "✨ AI is crafting your research paper sections..." : "Watch as AI generates your paper sections")
              : `📄 Review the ${completedSections.length} generated section${completedSections.length !== 1 ? 's' : ''}`}
          </CardDescription>

          {/* Citation statistics */}
          {allCitations.length > 0 && !isGenerating && (
            <div className="mt-3 flex flex-wrap gap-3 animate-fade-in">
              <Badge variant="outline" className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-700 shadow-sm">
                <BookOpen className="h-3 w-3 mr-1" />
                {allCitations.length} Citations
              </Badge>
              <Badge variant="outline" className="bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-700 shadow-sm">
                <FileText className="h-3 w-3 mr-1" />
                {allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length} References
              </Badge>
            </div>
          )}
        </CardHeader>
        <CardContent className="flex-1 overflow-hidden">
          <ScrollArea className="h-full pr-4">
            <div className="space-y-6">
              {completedSections.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[400px] text-center">
                  {isGenerating ? (
                    <div className="relative">
                      {/* Animated background circles */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-32 h-32 bg-blue-100 rounded-full animate-ping opacity-20"></div>
                        <div className="w-24 h-24 bg-purple-100 rounded-full animate-ping opacity-30 absolute"></div>
                        <div className="w-16 h-16 bg-green-100 rounded-full animate-ping opacity-40 absolute"></div>
                      </div>

                      {/* Main content */}
                      <div className="relative z-10">
                        <div className="relative mb-8">
                          <Brain className="h-20 w-20 text-blue-500 animate-pulse mx-auto" />
                          <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-2 -right-2 animate-bounce" />
                          <Zap className="h-5 w-5 text-purple-500 absolute -bottom-1 -left-1 animate-pulse" />
                        </div>

                        <h3 className="text-2xl font-bold text-gray-800 mb-3 animate-pulse">
                          🧠 AI Research Assistant at Work
                        </h3>
                        <p className="text-gray-600 max-w-md mx-auto mb-4 leading-relaxed">
                          Our advanced AI is carefully analyzing your research and crafting each section with academic precision.
                        </p>

                        {currentlyGenerating && (
                          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mx-auto max-w-sm">
                            <div className="flex items-center gap-3 justify-center">
                              <PenTool className="h-5 w-5 text-blue-600 animate-pulse" />
                              <span className="text-blue-700 font-medium">
                                Currently writing: {currentlyGenerating.name}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="relative">
                      <div className="mb-8">
                        <Bot className="h-20 w-20 text-gray-400 mx-auto mb-4" />
                        <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto"></div>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-700 mb-3">🚀 Ready to Generate</h3>
                      <p className="text-gray-500 max-w-md mx-auto leading-relaxed">
                        Once generation begins, you'll see your paper sections appear here one by one with beautiful animations.
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {completedSections.map((section, index) => {
                    const sectionData = paperSections.find(s => s.section_id === section.id);
                    return (
                      <div
                        key={section.id}
                        className="animate-fade-in-up"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <AccordionSectionCard
                          section={section}
                          allCitations={allCitations}
                          sectionCitations={sectionCitations}
                          sectionData={sectionData}
                          onRegenerateSection={onRegenerateSection}
                          enhancedCitations={enhancedCitations[section.id] || []}
                          useEnhancedDisplay={useEnhancedDisplay}
                        />
                      </div>
                    );
                  })}

                  {/* Show currently generating section placeholder */}
                  {currentlyGenerating && (
                    <div className="border-2 border-dashed border-blue-300 rounded-xl p-6 bg-gradient-to-r from-blue-50/50 to-purple-50/50 animate-pulse">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                          <PenTool className="h-6 w-6 text-blue-600 animate-pulse" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-blue-900 mb-1">
                            {currentlyGenerating.name} (Generating...)
                          </h3>
                          <p className="text-blue-700 text-sm">
                            AI is crafting this section with academic precision...
                          </p>
                        </div>
                        <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
      </div>

      {/* Export and Editor Actions */}
      {!isGenerating && completedSections.length > 0 && (
        <Card className={`
          shadow-xl border-0 backdrop-blur-sm transition-all duration-500 overflow-hidden
          ${showCompletionAnimation
            ? 'bg-gradient-to-r from-green-50 via-blue-50 to-purple-50 border-2 border-green-200 shadow-2xl'
            : 'bg-white/70'
          }
        `}>
          {/* Animated background for celebration */}
          {showCompletionAnimation && (
            <div className="absolute inset-0 bg-gradient-to-r from-green-100/30 via-blue-100/30 to-purple-100/30 animate-gradient-shift"></div>
          )}

          <CardContent className="p-8 relative z-10">
            <div className="text-center space-y-8">
              <div className="relative">
                {/* Celebration confetti effect */}
                {showCompletionAnimation && (
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(6)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-bounce"
                        style={{
                          left: `${20 + i * 15}%`,
                          animationDelay: `${i * 0.2}s`,
                          animationDuration: '1s'
                        }}
                      />
                    ))}
                  </div>
                )}

                <h2 className={`
                  text-4xl font-bold mb-4 transition-all duration-500
                  ${showCompletionAnimation ? 'text-green-800 scale-105 text-glow' : 'text-gray-900'}
                `}>
                  {showCompletionAnimation ? '🎉✨ Research Paper Complete! ✨🎉' : '🎉 Your Research Paper is Ready!'}
                </h2>

                {showCompletionAnimation && (
                  <div className="flex justify-center items-center gap-4 mb-4">
                    <Sparkles className="h-8 w-8 text-yellow-500 animate-bounce" />
                    <span className="text-2xl animate-enhanced-bounce">🎊</span>
                    <Sparkles className="h-8 w-8 text-yellow-500 animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                )}
              </div>

              <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-green-50 rounded-xl p-8 max-w-4xl mx-auto border border-gray-200 shadow-lg">
                <div className="text-center mb-6">
                  <p className="text-gray-800 mb-6 text-xl leading-relaxed font-medium">
                    🎯 <strong className="text-blue-700">{completedSections.length} section{completedSections.length !== 1 ? 's' : ''}</strong> successfully generated with AI precision!
                  </p>

                  {/* Enhanced Stats Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-blue-200">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <FileText className="h-6 w-6 text-blue-500" />
                        <span className="font-bold text-blue-700">Professional</span>
                      </div>
                      <p className="text-sm text-gray-600">Academic formatting & structure</p>
                    </div>

                    <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-green-200">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <BookOpen className="h-6 w-6 text-green-500" />
                        <span className="font-bold text-green-700">{allCitations.length} Citations</span>
                      </div>
                      <p className="text-sm text-gray-600">Properly formatted references</p>
                    </div>

                    <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-purple-200">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Sparkles className="h-6 w-6 text-purple-500" />
                        <span className="font-bold text-purple-700">AI Enhanced</span>
                      </div>
                      <p className="text-sm text-gray-600">Advanced language processing</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto">
                <ExportDialog
                  metadata={paperMetadata}
                  generatedSections={generatedSections}
                  allCitations={allCitations}
                  onEditInEditor={onEditInEditor}
                />
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 border-2 hover:shadow-xl hover:scale-105 transition-all duration-300 bg-white/80 backdrop-blur-sm border-purple-200 hover:border-purple-300 hover:bg-purple-50 group"
                  onClick={onEditInEditor}
                >
                  <Edit className="h-5 w-5 mr-2 group-hover:text-purple-600 transition-colors" />
                  Edit in Main Editor
                </Button>
              </div>

              <div className="bg-white/40 backdrop-blur-sm rounded-lg p-4 max-w-2xl mx-auto border border-gray-200">
                <p className="text-sm text-gray-600 leading-relaxed text-center">
                  ✨ Your research paper is ready for review, editing, and export. All citations and references have been automatically formatted according to academic standards.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
