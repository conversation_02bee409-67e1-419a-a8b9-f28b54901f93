import React, { useState, useEffect } from 'react';
import { toast } from "sonner";
import { <PERSON><PERSON>, Sparkles, GripVertical, FilePlus, Upload, FileDown, Save, Clock, Edit, FileText, BookOpen, BarChart3 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/AuthContext";

import { UserInputs, UserSection, ContentItem, GeneratedSection, PaperMetadata } from './types';
import { SECTION_TYPES, SECTION_PROMPTS, AI_MODELS } from './constants';
import { AIModelSelector } from './AIModelSelector';
import { PaperMetadataForm } from './PaperMetadataForm';
import { SectionCard } from './SectionCard';
import { GenerationPanel } from './GenerationPanel';
import paperAIService from './paper-ai.service';
import { editorService } from './editor.service';
import { MainEditor } from '../MainEditor';
// Using the enhanced citation extraction service for better reference matching
import { extractCitationsFromText, matchCitationsWithReferences } from './citation-extraction.enhanced';
import { Citation } from './types';
import { separateSectionReferences } from './section-utils';
// Import document services
import documentStorageService, { Document, DocumentMetadata } from '@/services/documentStorage';
import documentImportService from '@/services/documentImport';
import { DocumentHistory } from '../DocumentHistory';

export function EnhancedPaperGenerator() {
  const { user } = useAuth();

  // State for user inputs with metadata
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      researchField: "",
      keywords: [],
      authors: []
    },
    userSections: []
  });

  // Track which sections have been generated
  const [generatedSections, setGeneratedSections] = useState<GeneratedSection[]>(
    SECTION_TYPES
      .filter(type => type.id !== 'keywords') // Keywords aren't a generated section
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        status: 'pending',
        icon: type.icon
      }))
  );

  // Document state
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [isNewDocumentDialogOpen, setIsNewDocumentDialogOpen] = useState(false);
  const [newDocumentType, setNewDocumentType] = useState<'paper' | 'book' | 'article_review' | 'research_analysis'>('paper');
  const [newDocumentTitle, setNewDocumentTitle] = useState("");
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [historyTab, setHistoryTab] = useState<string>("editor");

  // UI state
  const [currentStep, setCurrentStep] = useState<'input' | 'generation' | 'editor'>('input');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0].id);
  const [editorContent, setEditorContent] = useState<{ title: string; content: string } | null>(null);
  
  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [sectionCitations, setSectionCitations] = useState<Record<string, string[]>>({});

  // Initialize document storage when user logs in
  useEffect(() => {
    if (user) {
      documentStorageService.setUserId(user.id);
    }
  }, [user]);

  // Helper functions to check if we can proceed with generation
  const hasRequiredSections = () => {
    const requiredSectionIds = SECTION_TYPES
      .filter(type => type.required)
      .map(type => type.id);
      
    return requiredSectionIds.every(id => 
      userInputs.userSections.some(section => 
        section.name.toLowerCase() === SECTION_TYPES.find(t => t.id === id)?.name.toLowerCase()
      )
    );
  };

  const sectionsHaveContent = () => {
    return userInputs.userSections.every(section => section.items.length > 0);
  };

  const hasTitle = () => {
    return userInputs.metadata.title.trim() !== "";
  };

  // Only title is required to proceed
  const canProceed = hasTitle();

  // Metadata management
  const updateMetadata = (metadata: Partial<PaperMetadata>) => {
    setUserInputs(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        ...metadata
      }
    }));
  };

  // Section management
  const addUserSection = (sectionType: string) => {
    const sectionTemplate = SECTION_TYPES.find(s => s.id === sectionType);
    if (!sectionTemplate) return;

    // Check if section already exists to prevent duplicates
    if (userInputs.userSections.some(s => s.name === sectionTemplate.name)) {
      toast.error(`${sectionTemplate.name} section already exists.`);
      return;
    }

    const newSection: UserSection = {
      id: Date.now().toString(),
      name: sectionTemplate.name,
      items: []
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: [...prev.userSections, newSection]
    }));
  };

  const removeUserSection = (sectionId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.filter(section => section.id !== sectionId)
    }));
  };

  // Content item management
  const addContentItem = (sectionId: string, type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: Date.now().toString(),
      type,
      content: '',
      order: 0,
      ...(type === 'figure' && { title: '', caption: '' })
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: [...section.items, { ...newItem, order: section.items.length }] }
          : section
      )
    }));
  };

  const updateContentItem = (sectionId: string, itemId: string, updates: Partial<ContentItem>) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              items: section.items.map(item => 
                item.id === itemId ? { ...item, ...updates } : item
              )
            }
          : section
      )
    }));
  };

  const removeContentItem = (sectionId: string, itemId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: section.items.filter(item => item.id !== itemId) }
          : section
      )
    }));
  };

  const moveContentItem = (sectionId: string, itemId: string, direction: 'up' | 'down') => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => {
        if (section.id !== sectionId) return section;
        
        const items = [...section.items];
        const index = items.findIndex(item => item.id === itemId);
        
        if (direction === 'up' && index > 0) {
          [items[index], items[index - 1]] = [items[index - 1], items[index]];
        } else if (direction === 'down' && index < items.length - 1) {
          [items[index], items[index + 1]] = [items[index + 1], items[index]];
        }
        
        return { ...section, items };
      })
    }));
  };

  // Document management
  const createNewDocument = async () => {
    if (!user) {
      toast.error("Please log in to create documents");
      return;
    }

    if (!newDocumentTitle.trim()) {
      toast.error("Please enter a document title");
      return;
    }

    try {
      const newDocument: Document = {
        title: newDocumentTitle,
        document_type: newDocumentType,
        status: 'draft',
        metadata: {
          researchField: "",
          keywords: [],
          authors: []
        },
        content: "",
        word_count: 0
      };

      const { data, error } = await documentStorageService.createDocument(newDocument);
      
      if (error) {
        toast.error("Failed to create document");
        console.error("Error creating document:", error);
        return;
      }

      setCurrentDocument(data);
      
      // Reset editor state
      setUserInputs({
        metadata: {
          title: data?.title || "",
          researchField: "",
          keywords: [],
          authors: []
        },
        userSections: []
      });
      
      setNewDocumentTitle("");
      setIsNewDocumentDialogOpen(false);
      
      toast.success("New document created");
      
    } catch (error) {
      console.error("Error in createNewDocument:", error);
      toast.error("An unexpected error occurred");
    }
  };

  const handleSelectDocument = (document: Document) => {
    setCurrentDocument(document);

    // Load document data into editor
    const metadata: PaperMetadata = {
      title: document.title || "",
      researchField: document.metadata?.researchField || "",
      keywords: document.metadata?.keywords || [],
      authors: document.metadata?.authors || []
    };

    // Always load documents with content directly into the editor
    // This ensures that saved generated papers open in the editor for viewing/editing
    if (document.content && document.content.trim()) {
      setEditorContent({
        title: document.title,
        content: document.content
      });
      setCurrentStep('editor');
      toast.success("Document loaded in editor");
    } else {
      // Only start in input mode if there's no generated content
      setUserInputs({
        metadata,
        userSections: document.metadata?.sections?.map(section => ({
          id: section.id,
          name: section.name,
          items: section.content ? [{
            id: Date.now().toString(),
            type: 'text',
            content: section.content,
            order: 0
          }] : []
        })) || []
      });
      toast.info("Document loaded for editing");
    }
  };

  const saveCurrentDocument = async () => {
    if (!user) {
      toast.error("Please log in to save documents");
      return;
    }

    if (!currentDocument) {
      // If no current document, create a new one
      setIsNewDocumentDialogOpen(true);
      return;
    }

    try {
      let content = "";
      let metadata: DocumentMetadata = { ...currentDocument.metadata };
      let status = currentDocument.status || 'draft';

      // Prioritize saving generated content from editor
      if (currentStep === 'editor' && editorContent) {
        // Save the generated content from the editor (this is the final output)
        content = editorContent.content;
        status = 'completed'; // Mark as completed when we have generated content

        // Update metadata with generation info
        metadata = {
          ...metadata,
          researchField: userInputs.metadata.researchField,
          keywords: userInputs.metadata.keywords,
          authors: userInputs.metadata.authors,
          generatedBy: 'AI Paper Generator',
          lastGenerated: new Date().toISOString(),
          contentType: 'generated_output' // Mark this as generated output
        };

        // Update title if changed in editor
        if (editorContent.title !== currentDocument.title) {
          metadata = {
            ...metadata,
            title: editorContent.title
          };
        }
      } else {
        // Save the input data only as a draft (for work in progress)
        content = userInputs.userSections
          .map(section => {
            const sectionContent = section.items
              .filter(item => item.type === 'text')
              .map(item => item.content)
              .join('\n\n');

            return sectionContent ? `## ${section.name}\n\n${sectionContent}` : '';
          })
          .filter(Boolean)
          .join('\n\n');

        status = 'draft'; // Keep as draft for input data

        // Update metadata for draft
        metadata = {
          ...metadata,
          researchField: userInputs.metadata.researchField,
          keywords: userInputs.metadata.keywords,
          authors: userInputs.metadata.authors,
          contentType: 'input_draft', // Mark this as input draft
          sections: userInputs.userSections.map(section => ({
            id: section.id,
            name: section.name,
            content: section.items
              .filter(item => item.type === 'text')
              .map(item => item.content)
              .join('\n\n')
          }))
        };
      }
      
      const updates: Partial<Document> = {
        title: userInputs.metadata.title || currentDocument.title,
        content,
        metadata,
        status,
        word_count: content.split(/\s+/).length,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await documentStorageService.updateDocument(currentDocument.id!, updates);

      if (error) {
        toast.error("Failed to save document");
        console.error("Error saving document:", error);
        return;
      }

      setCurrentDocument(data);

      if (status === 'completed') {
        toast.success("Generated paper saved successfully");
      } else {
        toast.success("Draft saved successfully");
      }
      
    } catch (error) {
      console.error("Error in saveCurrentDocument:", error);
      toast.error("An unexpected error occurred");
    }
  };

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    if (!user) {
      toast.error("Please log in to import documents");
      return;
    }
    
    try {
      const importedDocument = await documentImportService.importFromFile(file);
      if (!importedDocument) return;
      
      const savedDocument = await documentImportService.saveImportedDocument(importedDocument);
      if (savedDocument) {
        setCurrentDocument(savedDocument);
        setEditorContent({
          title: savedDocument.title,
          content: savedDocument.content || ""
        });
        setCurrentStep('editor');
        setImportDialogOpen(false);
      }
    } catch (error) {
      console.error("Error importing file:", error);
      toast.error("Failed to import document");
    }
    
    // Clear the file input
    event.target.value = "";
  };

  // Helper to get section content as a string
  const getSectionContent = (sectionName: string): string => {
    const section = userInputs.userSections.find(
      s => s.name.toLowerCase() === sectionName.toLowerCase()
    );
    
    if (!section || !section.items.length) {
      return `[AI will generate ${sectionName} content]`;
    }
    
    return section.items
      .filter(item => item.type === 'text')
      .map(item => item.content)
      .join('\n\n');
  };

  // Citation extraction
  const analyzeContentForCitations = async (sectionId: string, itemId: string) => {
    const analyzeKey = `${sectionId}-${itemId}`;
    setAnalyzingItems(prev => new Set(prev).add(analyzeKey));
    
    try {
      const section = userInputs.userSections.find(s => s.id === sectionId);
      if (!section) return;
      
      const item = section.items.find(i => i.id === itemId);
      if (!item || item.type !== 'text' || !item.content.trim()) return;
      
      const extractedCitations = await extractCitationsFromText(item.content);
      
      if (extractedCitations.length > 0) {
        // Add to section citations mapping
        setSectionCitations(prev => ({
          ...prev,
          [section.id]: [...(prev[section.id] || []), ...extractedCitations.map(c => c.id)]
        }));
        
        // Add to all citations if not already present
        setAllCitations(prev => {
          const existingIds = new Set(prev.map(c => c.id));
          const newCitations = extractedCitations.filter(c => !existingIds.has(c.id));
          return [...prev, ...newCitations];
        });
        
        toast.success(`Found ${extractedCitations.length} citations in ${section.name}`);
      } else {
        toast.info("No citations found in the text");
      }
    } catch (error) {
      console.error("Error analyzing content for citations:", error);
      toast.error("Failed to analyze content for citations");
    } finally {
      setAnalyzingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(`${sectionId}-${itemId}`);
        return newSet;
      });
    }
  };

  // Proceed to generation step
  const startGeneration = () => {
    if (!hasTitle()) {
      toast.error("Please provide a title for your paper");
      return;
    }
    
    // If we have a current document, save it before proceeding
    if (currentDocument) {
      saveCurrentDocument();
    }
    
    setCurrentStep('generation');
  };

  // Handle AI generation complete
  const handleGenerationComplete = async (generatedContent: string) => {
    // Set the editor content first
    setEditorContent({
      title: userInputs.metadata.title,
      content: generatedContent
    });
    setCurrentStep('editor');

    if (currentDocument) {
      // Save the generated content to the current document
      try {
        const updates = {
          content: generatedContent,
          status: 'completed' as const,
          word_count: generatedContent.split(/\s+/).length,
          metadata: {
            ...currentDocument.metadata,
            researchField: userInputs.metadata.researchField,
            keywords: userInputs.metadata.keywords,
            authors: userInputs.metadata.authors,
            generatedBy: 'AI Paper Generator',
            lastGenerated: new Date().toISOString(),
            contentType: 'generated_output'
          }
        };

        const { data, error } = await documentStorageService.updateDocument(currentDocument.id!, updates);

        if (error) {
          console.error("Error saving generated content:", error);
          toast.error("Generated content loaded in editor, but failed to save to database");
        } else {
          setCurrentDocument(data);
          toast.success("Paper generated and saved successfully!");
        }
      } catch (error) {
        console.error("Error saving generated content:", error);
        toast.error("Generated content loaded in editor, but failed to save");
      }
    } else {
      // No current document, just show the editor
      toast.success("Paper generated! Please save to preserve your work.");
    }
  };

  // Handle editor content changes
  const handleEditorContentChange = (content: string) => {
    if (editorContent) {
      setEditorContent({
        ...editorContent,
        content
      });
    }
  };

  // Handle editor title changes
  const handleEditorTitleChange = (title: string) => {
    if (editorContent) {
      setEditorContent({
        ...editorContent,
        title
      });
    }
  };

  // Reset editor state and go back to input step
  const handleReset = () => {
    if (window.confirm("This will reset all your progress. Are you sure?")) {
      setUserInputs({
        metadata: {
          title: "",
          researchField: "",
          keywords: [],
          authors: []
        },
        userSections: []
      });
      setEditorContent(null);
      setCurrentDocument(null);
      setCurrentStep('input');
    }
  };

  // Render the appropriate step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'input':
        return (
          <div className="space-y-6">
            <PaperMetadataForm 
              metadata={userInputs.metadata}
              onUpdate={updateMetadata}
            />
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Paper Sections</h3>
              <p className="text-sm text-muted-foreground">
                Add and customize sections for your paper.
              </p>
              
              <div className="grid grid-cols-2 gap-2 mb-4">
                {SECTION_TYPES.filter(type => type.id !== 'keywords').map(type => (
                  <Button
                    key={type.id}
                    variant="outline"
                    className="justify-start"
                    onClick={() => addUserSection(type.id)}
                  >
                    {type.icon && <span className="mr-2">{type.icon}</span>}
                    {type.name}
                    {type.required && <Badge variant="outline" className="ml-2">Required</Badge>}
                  </Button>
                ))}
              </div>
              
              <div className="space-y-4 mt-6">
                {userInputs.userSections.map(section => (
                  <SectionCard
                    key={section.id}
                    section={section}
                    onRemove={removeUserSection}
                    onAddItem={addContentItem}
                    onUpdateItem={updateContentItem}
                    onRemoveItem={removeContentItem}
                    onMoveItem={moveContentItem}
                    onAnalyzeCitations={analyzeContentForCitations}
                    isAnalyzing={(itemId) => analyzingItems.has(`${section.id}-${itemId}`)}
                  />
                ))}
              </div>
            </div>
            
            <div className="flex justify-between mt-6">
              <Button variant="outline" onClick={handleReset}>
                Reset
              </Button>
              <div className="space-x-2">
                <Button onClick={saveCurrentDocument}>
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
                <Button 
                  onClick={startGeneration} 
                  disabled={!canProceed}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Paper
                </Button>
              </div>
            </div>
          </div>
        );
        
      case 'generation':
        return (
          <GenerationPanel
            metadata={userInputs.metadata}
            sections={userInputs.userSections}
            generatedSections={generatedSections}
            setGeneratedSections={setGeneratedSections}
            selectedModel={selectedModel}
            onSelectModel={setSelectedModel}
            onComplete={handleGenerationComplete}
            onBack={() => setCurrentStep('input')}
          />
        );
        
      case 'editor':
        return editorContent ? (
          <MainEditor 
            initialTitle={editorContent.title}
            initialContent={editorContent.content}
            onTitleChange={handleEditorTitleChange}
            onContentChange={handleEditorContentChange}
            onSave={saveCurrentDocument}
          />
        ) : null;
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <div className="bg-background border-b px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <h1 className="text-lg font-medium">AI Paper Generator</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => setIsNewDocumentDialogOpen(true)}>
            <FilePlus className="h-4 w-4 mr-2" />
            New
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="ghost" size="sm" onClick={saveCurrentDocument}>
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Main Panel */}
        <div className="flex-1 p-4">
          {renderCurrentStep()}
        </div>
        
        {/* Right Sidebar */}
        <div className="w-80 border-l p-4">
          <Tabs 
            value={historyTab} 
            onValueChange={setHistoryTab} 
            className="w-full"
          >
            <TabsList className="w-full">
              <TabsTrigger value="editor" className="flex-1">Editor</TabsTrigger>
              <TabsTrigger value="history" className="flex-1">History</TabsTrigger>
            </TabsList>
            <TabsContent value="editor" className="mt-4">
              {currentStep === 'editor' ? (
                <div className="space-y-4">
                  <h3 className="font-medium">Editor Tools</h3>
                  <Button variant="outline" className="w-full justify-start" onClick={() => setCurrentStep('input')}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Input Data
                  </Button>
                  <Button variant="outline" className="w-full justify-start" onClick={saveCurrentDocument}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Document
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export Document
                  </Button>
                </div>
              ) : (
                <div className="p-4 text-center">
                  <p className="text-muted-foreground">Editor tools will be available after generation</p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="history" className="mt-4">
              <DocumentHistory
                documentType="paper"
                onNewDocument={() => setIsNewDocumentDialogOpen(true)}
                onSelectDocument={handleSelectDocument}
                currentDocumentId={currentDocument?.id}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* New Document Dialog */}
      <Dialog open={isNewDocumentDialogOpen} onOpenChange={setIsNewDocumentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Document</DialogTitle>
            <DialogDescription>
              Enter a title for your new document.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="document-title">Document Title</Label>
              <Input 
                id="document-title"
                placeholder="Enter a title..."
                value={newDocumentTitle}
                onChange={(e) => setNewDocumentTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Document Type</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button 
                  type="button"
                  variant={newDocumentType === 'paper' ? 'default' : 'outline'}
                  className="justify-start"
                  onClick={() => setNewDocumentType('paper')}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Paper
                </Button>
                <Button 
                  type="button"
                  variant={newDocumentType === 'book' ? 'default' : 'outline'}
                  className="justify-start"
                  onClick={() => setNewDocumentType('book')}
                >
                  <BookOpen className="w-4 h-4 mr-2" />
                  Book
                </Button>
                <Button 
                  type="button"
                  variant={newDocumentType === 'article_review' ? 'default' : 'outline'}
                  className="justify-start"
                  onClick={() => setNewDocumentType('article_review')}
                >
                  <Bot className="w-4 h-4 mr-2" />
                  Article Review
                </Button>
                <Button 
                  type="button"
                  variant={newDocumentType === 'research_analysis' ? 'default' : 'outline'}
                  className="justify-start"
                  onClick={() => setNewDocumentType('research_analysis')}
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Research Analysis
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNewDocumentDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createNewDocument} disabled={!newDocumentTitle.trim()}>
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Document</DialogTitle>
            <DialogDescription>
              Upload a document to import. Supported formats: .docx, .txt
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="file-upload" className="block mb-2">Select File</Label>
            <Input 
              id="file-upload" 
              type="file" 
              accept=".docx,.txt" 
              onChange={handleFileImport}
            />
            <p className="text-sm text-muted-foreground mt-2">
              <strong>Note:</strong> PDF import is coming soon.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setImportDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
