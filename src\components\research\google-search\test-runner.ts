/**
 * Google Search Test Runner
 * Simple test runner that can be used in browser console or development
 */

import { 
  runAllValidationTests,
  testAcademicSearchIntegration,
  validateUrls,
  validateAcademicSources,
  validateAcademicScoring,
  validateApiConfiguration,
  validateCostEstimation
} from './utils/validation-utils';

/**
 * Run all tests and display results in console
 */
export const runGoogleSearchTests = () => {
  console.clear();
  console.log('🚀 Google Search Grounding Test Runner');
  console.log('=====================================\n');

  try {
    const results = runAllValidationTests();
    
    console.log('\n📋 Detailed Test Results:');
    console.log('==========================');
    
    results.forEach((result, index) => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      console.log(`\n${index + 1}. ${icon} ${result.test}`);
      console.log(`   Status: ${result.status.toUpperCase()}`);
      console.log(`   Message: ${result.message}`);
      
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });

    const passedTests = results.filter(r => r.status === 'pass').length;
    const warningTests = results.filter(r => r.status === 'warning').length;
    const failedTests = results.filter(r => r.status === 'fail').length;
    const totalTests = results.length;

    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`⚠️ Warnings: ${warningTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests === 0 && warningTests === 0) {
      console.log('\n🎉 All tests passed! Google Search grounding is working perfectly.');
    } else if (failedTests === 0) {
      console.log('\n✅ All critical tests passed. Some warnings to review.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }

    return results;

  } catch (error) {
    console.error('❌ Test runner failed:', error);
    return [];
  }
};

/**
 * Run integration test only
 */
export const runIntegrationTest = async () => {
  console.clear();
  console.log('🧪 Google Search Integration Test');
  console.log('=================================\n');

  try {
    const success = await testAcademicSearchIntegration();
    
    if (success) {
      console.log('\n🎉 Integration test PASSED! Google Search is working correctly.');
    } else {
      console.log('\n❌ Integration test FAILED. Check the issues above.');
    }

    return success;

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    return false;
  }
};

/**
 * Run individual test by name
 */
export const runIndividualTest = (testName: string) => {
  console.log(`🧪 Running individual test: ${testName}`);
  console.log('=====================================\n');

  let result;

  switch (testName.toLowerCase()) {
    case 'api':
    case 'api-config':
    case 'configuration':
      result = validateApiConfiguration();
      break;
    
    case 'url':
    case 'urls':
    case 'url-validation':
      result = validateUrls();
      break;
    
    case 'academic':
    case 'academic-sources':
    case 'sources':
      result = validateAcademicSources();
      break;
    
    case 'scoring':
    case 'academic-scoring':
    case 'score':
      result = validateAcademicScoring();
      break;
    
    case 'cost':
    case 'cost-estimation':
    case 'estimation':
      result = validateCostEstimation();
      break;
    
    default:
      console.log(`❌ Unknown test: ${testName}`);
      console.log('Available tests: api, url, academic, scoring, cost');
      return null;
  }

  const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
  console.log(`${icon} ${result.test}: ${result.message}`);
  
  if (result.details) {
    console.log('Details:', result.details);
  }

  return result;
};

/**
 * Quick health check
 */
export const quickHealthCheck = () => {
  console.log('🏥 Google Search Quick Health Check');
  console.log('===================================\n');

  const apiResult = validateApiConfiguration();
  const urlResult = validateUrls();
  const academicResult = validateAcademicSources();

  const results = [apiResult, urlResult, academicResult];
  const allPassed = results.every(r => r.status === 'pass');

  results.forEach(result => {
    const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
    console.log(`${icon} ${result.test}: ${result.status.toUpperCase()}`);
  });

  console.log(`\n${allPassed ? '🎉' : '⚠️'} Health Check: ${allPassed ? 'HEALTHY' : 'NEEDS ATTENTION'}`);

  return allPassed;
};

/**
 * Display help information
 */
export const showTestHelp = () => {
  console.log('📚 Google Search Test Runner Help');
  console.log('=================================\n');

  console.log('Available Commands:');
  console.log('------------------');
  console.log('runGoogleSearchTests()     - Run all validation tests');
  console.log('runIntegrationTest()       - Run integration test with real API calls');
  console.log('runIndividualTest(name)    - Run specific test (api, url, academic, scoring, cost)');
  console.log('quickHealthCheck()         - Quick health check of core functionality');
  console.log('showTestHelp()             - Show this help message');

  console.log('\nExample Usage:');
  console.log('-------------');
  console.log('import { runGoogleSearchTests } from "@/components/research/google-search/test-runner";');
  console.log('runGoogleSearchTests();');
  console.log('');
  console.log('// Or in browser console:');
  console.log('window.runGoogleSearchTests();');

  console.log('\nTroubleshooting:');
  console.log('---------------');
  console.log('1. If API test fails: Set VITE_GEMINI_API_KEY environment variable');
  console.log('2. If URL test fails: Check isValidUrl() method implementation');
  console.log('3. If academic test fails: Check isAcademicSource() method');
  console.log('4. If integration test fails: Check API key and network connection');
};

// Make functions available globally for browser console usage
if (typeof window !== 'undefined') {
  (window as any).runGoogleSearchTests = runGoogleSearchTests;
  (window as any).runIntegrationTest = runIntegrationTest;
  (window as any).runIndividualTest = runIndividualTest;
  (window as any).quickHealthCheck = quickHealthCheck;
  (window as any).showTestHelp = showTestHelp;
  
  console.log('🔧 Google Search test functions are now available globally:');
  console.log('   - runGoogleSearchTests()');
  console.log('   - runIntegrationTest()');
  console.log('   - runIndividualTest(name)');
  console.log('   - quickHealthCheck()');
  console.log('   - showTestHelp()');
}
