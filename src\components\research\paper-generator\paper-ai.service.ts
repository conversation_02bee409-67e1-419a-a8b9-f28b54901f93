import { AIGenerationOptions, AIAnalysisResult } from './types';

/**
 * Service for handling AI text and image analysis requests
 */
export class PaperAIService {
  private apiKey: string;
  
  constructor() {
    // Try multiple API key sources in order of preference
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || 
                 import.meta.env.VITE_AI_API_KEY || 
                 import.meta.env.VITE_OPENAI_API_KEY || 
                 import.meta.env.VITE_GEMINI_API_KEY || 
                 import.meta.env.VITE_API_KEY || '';
    
    const isValidKey = this.apiKey && 
                       !this.apiKey.includes('your_') && 
                       this.apiKey.length > 20;
                       
    // Determine the API provider based on key format
    let provider = 'unknown';
    if (this.apiKey.startsWith('sk-or-')) {
      provider = 'OpenRouter';
    } else if (this.apiKey.startsWith('sk-')) {
      provider = 'OpenAI';
    } else if (this.apiKey.startsWith('AIza')) {
      provider = 'Google/Gemini';
    }
    
    console.log('PaperAIService initialized:', {
      hasApiKey: !!this.apiKey,
      provider: isValidKey ? provider : 'none',
      keyPrefix: this.apiKey ? this.apiKey.substring(0, 6) + '...' : 'none',
      isValid: isValidKey
    });
    
    if (!isValidKey) {
      console.warn('No valid AI API key found in environment variables - AI features will not work');
    }
  }
  
  /**
   * Analyze text using the specified AI model
   */
  async analyzeText(
    prompt: string, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;
      const temperature = options.temperature || 0.7;
      
      console.log('Making AI request:', {
        model,
        maxTokens,
        hasApiKey: !!this.apiKey,
        keyFormat: this.apiKey ? `${this.apiKey.substring(0, 5)}...` : 'none'
      });

      // Check if API key is properly configured
      const isValidKey = this.apiKey && 
                        !this.apiKey.includes('your_') && 
                        this.apiKey.length > 20;
      
      if (!isValidKey) {
        throw new Error('No valid API key configured. Please set up your OpenRouter API key in the .env file.');
      }
      
      // Determine API provider based on model name or key format
      const isOpenRouter = model.includes('/') || this.apiKey.startsWith('sk-or-');
      const isGemini = model.includes('gemini') || this.apiKey.startsWith('AIza');
      const isOpenAI = model.includes('gpt') || (this.apiKey.startsWith('sk-') && !this.apiKey.startsWith('sk-or-'));
      
      console.log('Provider detection:', { isOpenRouter, isGemini, isOpenAI });
      
      // Default to OpenRouter as it supports multiple models
      let apiEndpoint = "https://openrouter.ai/api/v1/chat/completions"; 
      let requestBody: any = {
        model,
        messages: [
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: maxTokens,
        temperature
      };
      
      // Configure headers
      let headers: Record<string, string> = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      };
      
      // Adjust endpoint and request format based on provider
      if (isGemini && !isOpenRouter) {
        const modelId = model.includes('/') ? model.split('/')[1] : model;
        apiEndpoint = `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent`;
        requestBody = {
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            maxOutputTokens: maxTokens,
            temperature
          }
        };
        // For Google API, the key goes in the URL instead of Authorization header
        apiEndpoint += `?key=${this.apiKey}`;
        headers = { "Content-Type": "application/json" };
      } else if (isOpenAI && !isOpenRouter) {
        apiEndpoint = "https://api.openai.com/v1/chat/completions";
        // OpenAI-specific model naming if not specified with provider prefix
        if (!model.includes('/')) {
          requestBody.model = model;
        }
      }
      
      // Add request logging
      console.log(`Sending request to ${apiEndpoint.split('?')[0]}`);
      
      // Make the API request
      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        
        // Provide more helpful error messages
        if (response.status === 401 || response.status === 403) {
          throw new Error('API key invalid or unauthorized. Please check your API key configuration.');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later or check API quotas.');
        } else {
          throw new Error(`API Error (${response.status}): ${errorText.substring(0, 100)}`);
        }
      }
      
      const data = await response.json();
      
      // Extract content based on API provider format
      let content = '';
      if (isGemini && !isOpenRouter) {
        content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      } else {
        content = data.choices?.[0]?.message?.content || '';
      }
      
      // If no content was returned, provide a meaningful error
      if (!content) {
        console.error('Empty response from API:', data);
        return 'The AI model returned an empty response. Please try again or use a different model.';
      }
      
      return content;
    } catch (error: any) {
      console.error('AI Text Analysis Error:', error);
      
      // Return a friendly error message that can be displayed to users
      return `Error: ${error.message || 'Failed to generate text analysis. Please check your API configuration or try again later.'}`;
    }
  }

  /**
   * Analyze an image using the specified AI model (legacy method - converts URL to base64)
   */
  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 512 } = options;

      // Check if API key is properly configured
      if (!this.hasValidApiKey()) {
        throw new Error('No valid API key configured. Please set up your OpenRouter API key in the .env file.');
      }

      const dataUrl = await this.convertImageToBase64(imageUrl);

      return this.analyzeImageWithBase64(dataUrl, prompt, options);
    } catch (error: any) {
      console.error('AI Image Analysis Error:', error);
      throw new Error(error.message || 'Failed to analyze image. Please check your API configuration.');
    }
  }

  /**
   * Analyze an image using base64 data directly
   */
  async analyzeImageWithBase64(
    base64Data: string,
    prompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "google/gemini-2.5-flash-lite-preview-06-17", maxTokens = 1024 } = options;

      console.log('analyzeImageWithBase64 called with:', {
        model,
        maxTokens,
        base64DataPrefix: base64Data.substring(0, 50) + '...',
        promptLength: prompt.length
      });

      // Check if API key is properly configured
      if (!this.hasValidApiKey()) {
        throw new Error('No valid API key configured. Please set up your OpenRouter API key in the .env file.');
      }

      // Ensure base64 data has proper format
      if (!base64Data.startsWith('data:')) {
        throw new Error('Invalid base64 data format. Expected data URL format.');
      }

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: prompt },
                { type: "image_url", image_url: { url: base64Data } }
              ]
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        // Provide more helpful error messages
        if (response.status === 401 || response.status === 403) {
          throw new Error('API key invalid or unauthorized. Please check your API key configuration.');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later or check API quotas.');
        } else if (response.status === 400) {
          throw new Error('Bad request. The image format may not be supported or the request is malformed.');
        } else {
          throw new Error(`API Error (${response.status}): ${errorText.substring(0, 200)}`);
        }
      }

      const data = await response.json();
      console.log('API response received:', {
        hasChoices: !!data.choices,
        choicesLength: data.choices?.length,
        hasContent: !!data.choices?.[0]?.message?.content
      });

      const content = data.choices?.[0]?.message?.content || '';

      if (!content) {
        console.error('Empty response from API:', data);
        throw new Error('The AI model returned an empty response. Please try again with a different model or check the image format.');
      }

      return content;
    } catch (error: any) {
      console.error('AI Image Analysis Error:', error);
      throw new Error(error.message || 'Failed to analyze image. Please check your API configuration.');
    }
  }
  
  /**
   * Generate a complete research paper section based on prompts and previous sections
   */
  async generatePaperSection(
    sectionPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { model = "anthropic/claude-3.5-sonnet", maxTokens = 1024 } = options;
      
      // Check if API key is properly configured
      if (!this.hasValidApiKey()) {
        throw new Error('No valid API key configured. Please set up your OpenRouter API key in the .env file.');
      }
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are a helpful academic writing assistant that generates well-structured, scholarly research paper sections. Use formal academic language, cite sources appropriately, and organize content logically."
            },
            {
              role: "user",
              content: sectionPrompt
            }
          ],
          max_tokens: maxTokens
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`API Error (${response.status}): ${errorText.substring(0, 100)}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      if (!content) {
        throw new Error('The AI model returned an empty response. Please try again.');
      }
      
      return content;
    } catch (error: any) {
      console.error('AI Section Generation Error:', error);
      throw new Error(error.message || 'Failed to generate paper section. Please check your API configuration.');
    }
  }
  
  /**
   * Convert an image URL to a base64 data URL
   */
  private async convertImageToBase64(url: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.onload = function() {
        const reader = new FileReader();
        reader.onloadend = function() {
          resolve(reader.result as string);
        };
        reader.onerror = reject;
        reader.readAsDataURL(xhr.response);
      };
      xhr.onerror = reject;
      xhr.open('GET', url);
      xhr.responseType = 'blob';
      xhr.send();
    });
  }

  /**
   * Check if the current API key is valid
   * @returns Boolean indicating if a valid API key is configured
   */
  public hasValidApiKey(): boolean {
    return !!this.apiKey && 
           !this.apiKey.includes('your_') && 
           this.apiKey.length > 20;
  }

  /**
   * Get the current API provider based on the configured key
   * @returns Provider name as string
   */
  public getApiProvider(): string {
    if (!this.hasValidApiKey()) return 'none';
    
    if (this.apiKey.startsWith('sk-or-')) {
      return 'OpenRouter';
    } else if (this.apiKey.startsWith('sk-')) {
      return 'OpenAI';
    } else if (this.apiKey.startsWith('AIza')) {
      return 'Google/Gemini';
    }
    
    return 'unknown';
  }
}

export const paperAIService = new PaperAIService();

export default paperAIService;
