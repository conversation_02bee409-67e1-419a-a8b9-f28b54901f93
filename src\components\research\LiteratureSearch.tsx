
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, Bookmark, ExternalLink, Calendar, Users } from "lucide-react";

interface Paper {
  id: string;
  title: string;
  authors: string[];
  abstract: string;
  year: number;
  citations: number;
  doi: string;
  source: string;
  relevanceScore: number;
}

export function LiteratureSearch() {
  const [searchQuery, setSearchQuery] = useState("");
  const [papers] = useState<Paper[]>([
    {
      id: "1",
      title: "Deep Learning Approaches for Climate Change Prediction: A Comprehensive Review",
      authors: ["<PERSON>, <PERSON>", "Johnson, M.", "Williams, K."],
      abstract: "This paper presents a comprehensive review of deep learning approaches used in climate change prediction models. We analyze various neural network architectures and their effectiveness in processing climate data...",
      year: 2023,
      citations: 145,
      doi: "10.1000/xyz123",
      source: "Nature Climate Change",
      relevanceScore: 0.95
    },
    {
      id: "2",
      title: "Machine Learning for Environmental Data Analysis: Recent Advances and Future Directions",
      authors: ["<PERSON>, <PERSON>", "Davis, R."],
      abstract: "Environmental data analysis has been revolutionized by machine learning techniques. This review covers recent advances in the field and discusses future research directions...",
      year: 2023,
      citations: 89,
      doi: "10.1000/abc456",
      source: "Environmental Science & Technology",
      relevanceScore: 0.87
    },
    {
      id: "3",
      title: "Neural Networks in Atmospheric Science: A Systematic Review",
      authors: ["Lee, S.", "Zhang, L.", "Kumar, P.", "Anderson, T."],
      abstract: "This systematic review examines the application of neural networks in atmospheric science, focusing on weather prediction, climate modeling, and atmospheric data analysis...",
      year: 2022,
      citations: 203,
      doi: "10.1000/def789",
      source: "Journal of Atmospheric Sciences",
      relevanceScore: 0.82
    }
  ]);

  const handleSearch = () => {
    console.log("Searching for:", searchQuery);
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Search Header */}
      <div className="p-6 border-b">
        <h1 className="text-2xl font-bold mb-4">Literature Search</h1>
        <div className="flex space-x-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search for papers, authors, keywords..."
              className="pl-10"
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
          <Button onClick={handleSearch}>
            Search
          </Button>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Search Results */}
      <div className="flex-1 overflow-auto p-6">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Found {papers.length} papers • Showing most relevant
          </p>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Button variant="outline" size="sm">Relevance</Button>
          </div>
        </div>

        <div className="space-y-6">
          {papers.map((paper) => (
            <Card key={paper.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2 text-blue-600 hover:text-blue-800 cursor-pointer">
                      {paper.title}
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {paper.authors.join(", ")}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {paper.year}
                      </div>
                      <div>
                        {paper.citations} citations
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">{paper.source}</Badge>
                      <Badge variant="outline">Relevance: {Math.round(paper.relevanceScore * 100)}%</Badge>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {paper.abstract}
                </p>
                <div className="mt-4 flex items-center space-x-4">
                  <Button variant="outline" size="sm">
                    Add to Library
                  </Button>
                  <Button variant="outline" size="sm">
                    Cite
                  </Button>
                  <Button variant="outline" size="sm">
                    View PDF
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
