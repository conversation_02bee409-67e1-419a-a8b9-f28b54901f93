/**
 * Achievement System Component
 * Manages and displays student achievements and badges
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Trophy, 
  Star, 
  Target, 
  Brain,
  BookOpen,
  Zap,
  Award,
  Medal,
  Crown,
  Flame,
  CheckCircle,
  Lock,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Shield,
  Heart
} from "lucide-react";

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'learning' | 'engagement' | 'streak' | 'mastery' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedAt?: Date;
  progress: number;
  maxProgress: number;
  xpReward: number;
  requirements: string[];
}

interface AchievementSystemProps {
  achievements: Achievement[];
  onClaimReward?: (achievementId: string) => void;
}

export const AchievementSystem: React.FC<AchievementSystemProps> = ({
  achievements,
  onClaimReward
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);

  // Achievement categories
  const categories = [
    { id: 'all', name: 'All', icon: Trophy, count: achievements.length },
    { id: 'learning', name: 'Learning', icon: Brain, count: achievements.filter(a => a.category === 'learning').length },
    { id: 'engagement', name: 'Engagement', icon: Star, count: achievements.filter(a => a.category === 'engagement').length },
    { id: 'streak', name: 'Streaks', icon: Flame, count: achievements.filter(a => a.category === 'streak').length },
    { id: 'mastery', name: 'Mastery', icon: Crown, count: achievements.filter(a => a.category === 'mastery').length },
    { id: 'social', name: 'Social', icon: Users, count: achievements.filter(a => a.category === 'social').length },
    { id: 'special', name: 'Special', icon: Rocket, count: achievements.filter(a => a.category === 'special').length }
  ];

  // Filter achievements
  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(a => a.category === selectedCategory);

  // Get rarity styling
  const getRarityStyle = (rarity: string, unlocked: boolean) => {
    const baseStyle = unlocked ? '' : 'opacity-50 grayscale';
    
    switch (rarity) {
      case 'common':
        return `${baseStyle} border-gray-300 bg-gradient-to-br from-gray-50 to-gray-100`;
      case 'rare':
        return `${baseStyle} border-blue-300 bg-gradient-to-br from-blue-50 to-blue-100`;
      case 'epic':
        return `${baseStyle} border-purple-300 bg-gradient-to-br from-purple-50 to-purple-100`;
      case 'legendary':
        return `${baseStyle} border-yellow-300 bg-gradient-to-br from-yellow-50 to-yellow-100`;
      default:
        return baseStyle;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-600';
      case 'rare': return 'text-blue-600';
      case 'epic': return 'text-purple-600';
      case 'legendary': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'shadow-yellow-200 shadow-lg';
      case 'epic': return 'shadow-purple-200 shadow-md';
      case 'rare': return 'shadow-blue-200 shadow-sm';
      default: return '';
    }
  };

  // Calculate completion stats
  const unlockedCount = achievements.filter(a => a.unlockedAt).length;
  const completionPercentage = (unlockedCount / achievements.length) * 100;

  return (
    <div className="space-y-6">
      {/* Achievement Overview */}
      <Card className="shadow-xl border-0 bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg"
              >
                <Trophy className="w-8 h-8 text-white" />
              </motion.div>
              
              <div>
                <CardTitle className="text-2xl bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                  Achievement Gallery
                </CardTitle>
                <p className="text-gray-600 mt-1">
                  {unlockedCount} of {achievements.length} achievements unlocked
                </p>
              </div>
            </div>

            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">
                {Math.round(completionPercentage)}%
              </div>
              <div className="text-sm text-gray-600">Complete</div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="space-y-4">
            <Progress value={completionPercentage} className="h-3" />
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['common', 'rare', 'epic', 'legendary'].map((rarity) => {
                const count = achievements.filter(a => a.rarity === rarity && a.unlockedAt).length;
                const total = achievements.filter(a => a.rarity === rarity).length;
                
                return (
                  <div key={rarity} className="text-center p-3 bg-white/60 backdrop-blur-sm rounded-lg">
                    <div className={`text-lg font-bold ${getRarityColor(rarity)}`}>
                      {count}/{total}
                    </div>
                    <div className="text-xs text-gray-600 capitalize">{rarity}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achievement Categories */}
      <Card className="shadow-lg border-0">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5 text-blue-500" />
            <span>Browse Achievements</span>
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-3 md:grid-cols-7 w-full mb-6">
              {categories.map((category) => (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  className="flex items-center space-x-1 text-xs"
                >
                  <category.icon className="w-3 h-3" />
                  <span className="hidden sm:inline">{category.name}</span>
                  <Badge variant="secondary" className="text-xs ml-1">
                    {category.count}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={selectedCategory} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredAchievements.map((achievement) => (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedAchievement(achievement)}
                    className={`cursor-pointer border-2 rounded-xl p-4 transition-all duration-300 ${getRarityStyle(achievement.rarity, !!achievement.unlockedAt)} ${getRarityGlow(achievement.rarity)}`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                        achievement.unlockedAt 
                          ? `bg-gradient-to-r ${achievement.rarity === 'legendary' ? 'from-yellow-400 to-orange-500' : 
                              achievement.rarity === 'epic' ? 'from-purple-400 to-pink-500' :
                              achievement.rarity === 'rare' ? 'from-blue-400 to-cyan-500' :
                              'from-gray-400 to-gray-500'}`
                          : 'bg-gray-200'
                      }`}>
                        {achievement.unlockedAt ? (
                          <achievement.icon className="w-6 h-6 text-white" />
                        ) : (
                          <Lock className="w-6 h-6 text-gray-500" />
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className={`font-semibold truncate ${
                            achievement.unlockedAt ? 'text-gray-900' : 'text-gray-500'
                          }`}>
                            {achievement.title}
                          </h3>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getRarityColor(achievement.rarity)}`}
                          >
                            {achievement.rarity}
                          </Badge>
                        </div>

                        <p className={`text-sm mb-2 line-clamp-2 ${
                          achievement.unlockedAt ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {achievement.description}
                        </p>

                        {/* Progress Bar */}
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-500">Progress</span>
                            <span className="text-gray-500">
                              {achievement.progress}/{achievement.maxProgress}
                            </span>
                          </div>
                          <Progress 
                            value={(achievement.progress / achievement.maxProgress) * 100} 
                            className="h-2"
                          />
                        </div>

                        {/* XP Reward */}
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Zap className="w-3 h-3" />
                            <span>{achievement.xpReward} XP</span>
                          </div>
                          
                          {achievement.unlockedAt && (
                            <div className="flex items-center space-x-1 text-xs text-green-600">
                              <CheckCircle className="w-3 h-3" />
                              <span>Unlocked</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Achievement Detail Modal */}
      <AnimatePresence>
        {selectedAchievement && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedAchievement(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl p-6 max-w-md w-full max-h-[80vh] overflow-y-auto"
            >
              <div className="text-center mb-6">
                <div className={`w-20 h-20 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
                  selectedAchievement.unlockedAt 
                    ? `bg-gradient-to-r ${selectedAchievement.rarity === 'legendary' ? 'from-yellow-400 to-orange-500' : 
                        selectedAchievement.rarity === 'epic' ? 'from-purple-400 to-pink-500' :
                        selectedAchievement.rarity === 'rare' ? 'from-blue-400 to-cyan-500' :
                        'from-gray-400 to-gray-500'}`
                    : 'bg-gray-200'
                }`}>
                  {selectedAchievement.unlockedAt ? (
                    <selectedAchievement.icon className="w-10 h-10 text-white" />
                  ) : (
                    <Lock className="w-10 h-10 text-gray-500" />
                  )}
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {selectedAchievement.title}
                </h3>
                
                <Badge className={`${getRarityColor(selectedAchievement.rarity)} mb-4`}>
                  {selectedAchievement.rarity.toUpperCase()}
                </Badge>

                <p className="text-gray-600 mb-4">
                  {selectedAchievement.description}
                </p>
              </div>

              {/* Progress */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Progress</span>
                  <span>{selectedAchievement.progress}/{selectedAchievement.maxProgress}</span>
                </div>
                <Progress 
                  value={(selectedAchievement.progress / selectedAchievement.maxProgress) * 100} 
                  className="h-3"
                />
              </div>

              {/* Requirements */}
              <div className="space-y-3 mb-6">
                <h4 className="font-semibold text-gray-900">Requirements:</h4>
                <ul className="space-y-2">
                  {selectedAchievement.requirements.map((req, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{req}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Reward */}
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-blue-500" />
                  <span className="font-semibold text-blue-900">
                    Reward: {selectedAchievement.xpReward} XP
                  </span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setSelectedAchievement(null)}
                  className="flex-1"
                >
                  Close
                </Button>
                
                {selectedAchievement.unlockedAt && onClaimReward && (
                  <Button
                    onClick={() => {
                      onClaimReward(selectedAchievement.id);
                      setSelectedAchievement(null);
                    }}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500"
                  >
                    Claim Reward
                  </Button>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AchievementSystem;
