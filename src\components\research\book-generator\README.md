# AI Book Generator

A comprehensive AI-powered book generation module that creates full-length books with context-aware chapter generation, hierarchical structure, and academic citation management.

## Features

### 🎯 Core Capabilities
- **Context-Aware Generation**: Rolling context system maintains narrative coherence across chapters
- **Hierarchical Structure**: Support for chapters, sub-sections, and detailed outlines
- **Citation Management**: Automatic extraction and formatting of academic references
- **Multi-Format Export**: Export to DOCX, PDF, EPUB, and HTML formats
- **Real-time Progress**: Live generation tracking with progress indicators

### 📚 Book Structure Support
- **Front Matter**: Preface, Foreword, Introduction
- **Main Content**: Numbered chapters with sub-sections
- **Back Matter**: Conclusion, Appendix, Glossary, Bibliography, Index
- **Flexible Numbering**: Numeric, Roman, or no chapter numbering

### 🤖 AI Integration
- **Multiple AI Models**: Support for Claude, GPT-4, Gemini, and Llama
- **Context Management**: Intelligent token limit handling with chapter summaries
- **Content Enhancement**: AI-powered content analysis and suggestions
- **Image Analysis**: AI description and integration of visual content

## Architecture

### Components Structure
```
book-generator/
├── AIBookGenerator.tsx          # Main component
├── components/
│   ├── BookMetadataForm.tsx     # Book information input
│   ├── ChapterCard.tsx          # Chapter outline and content
│   ├── ContentItemRenderer.tsx  # Text and figure handling
│   ├── AIModelSelector.tsx      # AI model selection
│   └── BookGenerationPanel.tsx  # Generation progress and preview
├── services/
│   └── book-ai.service.ts       # AI generation service
├── stores/
│   └── book-context.store.ts    # Zustand context management
├── types.ts                     # TypeScript interfaces
├── constants.ts                 # Configuration and constants
└── prompts.ts                   # AI generation prompts
```

### Context Management System

The book generator implements a sophisticated rolling context system to handle the length constraints of book generation:

1. **Initial Context**: Book outline and metadata
2. **Chapter Generation**: Each chapter uses summaries of previous chapters
3. **Context Optimization**: Automatically removes old context when limits are reached
4. **Summary Creation**: AI generates concise summaries for context continuity

### State Management

Uses Zustand for efficient state management:
- **Book Context**: Rolling context with chapter summaries
- **Generation Queue**: Sequential chapter generation tracking
- **Progress Monitoring**: Real-time generation progress updates
- **Token Management**: Automatic context size optimization

## Usage

### Basic Implementation

```tsx
import { AIBookGenerator } from '@/components/research/book-generator';

function App() {
  return <AIBookGenerator />;
}
```

### Integration with Research Dashboard

```tsx
import { AIBookGenerator } from '@/components/research/book-generator';

// Add to research dashboard routing
const routes = [
  { path: '/book-generator', component: AIBookGenerator }
];
```

## Configuration

### AI Models
Configure available AI models in `constants.ts`:

```typescript
export const AI_MODELS = [
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash (Default)",
    provider: "Google",
    capabilities: ["Very fast generation", "Long content", "Context retention"],
    maxTokens: 8192
  },
  {
    id: "moonshotai/kimi-k2",
    name: "Kimi K2",
    provider: "Moonshot AI",
    capabilities: ["Very long context", "Detailed content"],
    maxTokens: 32768
  }
];
```

### Context Settings
Adjust context management in `constants.ts`:

```typescript
export const CONTEXT_SETTINGS = {
  MAX_CONTEXT_TOKENS: 6000,
  SUMMARY_MAX_WORDS: 400,
  MAX_PREVIOUS_CHAPTERS: 5,
  CHAPTER_GENERATION_TOKENS: 4096
};
```

## API Requirements

### Environment Variables
```env
VITE_OPENROUTER_API_KEY=your_openrouter_api_key
```

### API Endpoints
- OpenRouter API for AI model access
- Support for multiple providers (Anthropic, OpenAI, Google, Meta)

## Book Generation Process

### 1. Metadata Collection
- Title, subtitle, genre, target audience
- Writing tone and estimated length
- Keywords and author information

### 2. Chapter Planning
- Detailed chapter outlines with sub-sections
- User-provided content and images
- Hierarchical numbering system

### 3. Context-Aware Generation
- Sequential chapter generation
- Rolling context with summaries
- Citation extraction and management

### 4. Export and Editing
- Multiple format support
- Integration with main editor
- Citation formatting and bibliography

## Best Practices

### Content Organization
- Provide detailed chapter outlines for better AI generation
- Include user content to guide AI direction
- Use hierarchical sub-sections for structure

### Context Management
- Keep chapter summaries concise (≤400 words)
- Monitor token usage for optimal performance
- Use appropriate AI models for content type

### Citation Handling
- Ensure academic-style citations in generated content
- Review and validate extracted references
- Use proper citation formats for target audience

## Troubleshooting

### Common Issues

**Generation Fails**
- Check API key configuration
- Verify internet connection
- Ensure sufficient API credits

**Context Overflow**
- Reduce chapter count in context
- Shorten chapter summaries
- Use models with higher token limits

**Poor Content Quality**
- Provide more detailed outlines
- Include relevant user content
- Select appropriate AI model for content type

## Future Enhancements

- **Collaborative Editing**: Multi-user book creation
- **Version Control**: Chapter revision tracking
- **Advanced Citations**: Integration with academic databases
- **Custom Templates**: Genre-specific book structures
- **AI Illustrations**: Automatic figure generation
- **Voice Narration**: Text-to-speech integration

## Dependencies

- React 18+
- Zustand for state management
- Lucide React for icons
- Tailwind CSS for styling
- OpenRouter API for AI access
