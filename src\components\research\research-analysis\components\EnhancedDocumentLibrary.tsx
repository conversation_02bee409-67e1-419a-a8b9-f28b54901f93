import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  FileText,
  Calendar,
  Users,
  Tag,
  Star,
  Eye,
  Download,
  MessageSquare,
  MoreHorizontal,
  SortAsc,
  SortDesc,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Heart,
  Bookmark,
  Share,
  Trash2,
  Brain,
  Info,
  BookOpen,
  Award,
  Target,
  Lightbulb,
  TrendingUp,
  Database,
  ChevronRight,
  ExternalLink,
  Copy,
  Zap,
  Sparkles,
  GraduationCap,
  Clock,
  Globe,
  Building,
  Quote,
  ChevronDown,
  ChevronUp,
  Link,
  FileCheck
} from "lucide-react";

import { ResearchDocument } from '../types';
import { researchAnalysisService } from '../services/research-analysis.service';
import { cn } from "@/lib/utils";

interface EnhancedDocumentLibraryProps {
  documents: ResearchDocument[];
  selectedDocuments: string[];
  onSelectionChange: (documentIds: string[]) => void;
  onSearch: (query: string) => void;
  onFilter: (filters: any) => void;
}

interface DocumentPreview {
  document: ResearchDocument;
  summary?: string;
  isLoading: boolean;
}

export function EnhancedDocumentLibrary({
  documents,
  selectedDocuments,
  onSelectionChange,
  onSearch,
  onFilter
}: EnhancedDocumentLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'detailed'>('detailed');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'year' | 'authors' | 'relevance'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<DocumentPreview | null>(null);
  const [expandedDocuments, setExpandedDocuments] = useState<Set<string>>(new Set());

  // Enhanced filters state
  const [filters, setFilters] = useState({
    years: [2015, 2025] as [number, number],
    authors: [] as string[],
    journals: [] as string[],
    tags: [] as string[],
    categories: [] as string[],
    status: 'all' as 'all' | 'ready' | 'processing' | 'error',
    hasKeyFindings: false,
    hasMethodology: false,
    hasDOI: false,
    confidenceThreshold: 0.5
  });

  // Handle search with advanced features
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    onSearch(query);
  }, [onSearch]);

  // Handle document selection with bulk actions
  const handleDocumentSelection = useCallback((documentId: string, checked: boolean) => {
    let newSelection: string[];
    if (checked) {
      newSelection = [...selectedDocuments, documentId];
    } else {
      newSelection = selectedDocuments.filter(id => id !== documentId);
    }
    onSelectionChange(newSelection);
  }, [selectedDocuments, onSelectionChange]);

  // Advanced sorting and filtering
  const filteredDocuments = useMemo(() => {
    let filtered = [...documents];

    // Apply filters
    if (filters.status !== 'all') {
      filtered = filtered.filter(doc => doc.status === filters.status);
    }
    
    if (filters.authors.length > 0) {
      filtered = filtered.filter(doc => 
        doc.authors.some(author => filters.authors.includes(author))
      );
    }
    
    if (filters.tags.length > 0) {
      filtered = filtered.filter(doc => 
        doc.tags.some(tag => filters.tags.includes(tag))
      );
    }
    
    if (filters.hasKeyFindings) {
      filtered = filtered.filter(doc => doc.keyFindings.length > 0);
    }
    
    if (filters.hasMethodology) {
      filtered = filtered.filter(doc => doc.methodology.length > 0);
    }
    
    if (filters.hasDOI) {
      filtered = filtered.filter(doc => doc.doi);
    }
    
    if (filters.confidenceThreshold > 0) {
      filtered = filtered.filter(doc => 
        (doc.metadata?.confidence || 0) >= filters.confidenceThreshold
      );
    }

    // Apply search
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(doc => 
        doc.title.toLowerCase().includes(searchLower) ||
        doc.authors.some(author => author.toLowerCase().includes(searchLower)) ||
        doc.abstract.toLowerCase().includes(searchLower) ||
        doc.keywords.some(keyword => keyword.toLowerCase().includes(searchLower))
      );
    }

    return filtered;
  }, [documents, filters, searchQuery]);

  // Handle select all/none with smart filtering
  const handleSelectAll = useCallback(() => {
    const filteredDocumentIds = filteredDocuments.map(d => d.id);
    if (selectedDocuments.length === filteredDocumentIds.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredDocumentIds);
    }
  }, [filteredDocuments, selectedDocuments, onSelectionChange]);

  const sortedDocuments = useMemo(() => {
    const sorted = [...filteredDocuments];

    // Apply sorting
    sorted.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'year':
          comparison = a.publicationYear - b.publicationYear;
          break;
        case 'authors':
          comparison = a.authors[0]?.localeCompare(b.authors[0] || '') || 0;
          break;
        case 'relevance':
          comparison = (b.metadata?.confidence || 0) - (a.metadata?.confidence || 0);
          break;
        case 'date':
        default:
          comparison = new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime();
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [filteredDocuments, sortBy, sortOrder]);

  // Toggle document expansion
  const toggleDocumentExpansion = useCallback((documentId: string) => {
    setExpandedDocuments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  }, []);

  // Enhanced preview with full document analysis
  const handlePreviewDocument = useCallback(async (document: ResearchDocument) => {
    setPreviewDocument({ document, isLoading: true });
    
    try {
      const summary = await researchAnalysisService.generateDocumentSummary(document);
      setPreviewDocument({ document, summary, isLoading: false });
    } catch (error) {
      console.error('Error generating summary:', error);
      setPreviewDocument({ document, isLoading: false });
      toast.error('Failed to generate document summary');
    }
  }, []);

  // Copy document information
  const copyDocumentInfo = useCallback((document: ResearchDocument) => {
    const info = `${document.title}\n${document.authors.join(', ')}\n${document.publicationYear}\n${document.abstract}`;
    navigator.clipboard.writeText(info);
    toast.success('Document information copied to clipboard');
  }, []);

  // Get enhanced status display
  const getStatusDisplay = (document: ResearchDocument) => {
    const confidence = document.metadata?.confidence || 0;
    const confidenceColor = confidence > 0.8 ? 'text-green-600' : confidence > 0.5 ? 'text-yellow-600' : 'text-red-600';
    
    return (
      <div className="flex items-center gap-2">
        {document.status === 'ready' && <CheckCircle className="h-4 w-4 text-green-500" />}
        {document.status === 'processing' && <RefreshCw className="h-4 w-4 text-orange-500 animate-spin" />}
        {document.status === 'error' && <AlertTriangle className="h-4 w-4 text-red-500" />}
        <span className="text-sm text-gray-600 capitalize">{document.status}</span>
        {confidence > 0 && (
          <Badge variant="outline" className={cn("text-xs", confidenceColor)}>
            {Math.round(confidence * 100)}% confidence
          </Badge>
        )}
      </div>
    );
  };

  // Get research quality indicators
  const getQualityIndicators = (document: ResearchDocument) => {
    const indicators = [];
    
    if (document.doi) indicators.push({ icon: Link, label: 'DOI Available', color: 'text-blue-500' });
    if (document.keyFindings.length > 5) indicators.push({ icon: Target, label: 'Rich Findings', color: 'text-green-500' });
    if (document.methodology.length > 3) indicators.push({ icon: Brain, label: 'Detailed Methods', color: 'text-purple-500' });
    if (document.journal) indicators.push({ icon: Award, label: 'Journal Publication', color: 'text-orange-500' });
    if (document.sections.length > 8) indicators.push({ icon: BookOpen, label: 'Comprehensive', color: 'text-indigo-500' });
    
    return indicators;
  };

  // Enhanced document card with full analysis display
  const renderEnhancedDocumentCard = (document: ResearchDocument) => {
    const isExpanded = expandedDocuments.has(document.id);
    const isSelected = selectedDocuments.includes(document.id);
    const qualityIndicators = getQualityIndicators(document);
    
    return (
      <Card 
        key={document.id} 
        className={cn(
          "group hover:shadow-xl transition-all duration-300 border-2 overflow-hidden",
          isSelected ? "border-blue-500 bg-blue-50/30" : "border-gray-200 hover:border-gray-300",
          document.status === 'error' && "border-red-200 bg-red-50/20"
        )}
      >
        {/* Status indicator bar */}
        <div className={cn(
          "h-2 w-full transition-all duration-300",
          document.status === 'ready' ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 
          document.status === 'processing' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' : 
          'bg-gradient-to-r from-red-500 to-orange-500'
        )} />
        
        <CardHeader className="pb-4">
          <div className="flex items-start gap-4">
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => handleDocumentSelection(document.id, checked as boolean)}
              className="mt-1 h-5 w-5"
            />
            
            <div className="flex-1 min-w-0 space-y-3">
              {/* Title and status */}
              <div className="space-y-2">
                <CardTitle className="text-xl font-bold text-gray-900 leading-tight line-clamp-2">
                  {document.title}
                </CardTitle>
                {getStatusDisplay(document)}
              </div>
              
              {/* Authors and publication info */}
              <div className="flex flex-wrap items-center gap-4 text-sm">
                <div className="flex items-center gap-2 text-gray-700">
                  <Users className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">{document.authors.join(', ') || 'Unknown authors'}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600">
                  <Calendar className="h-4 w-4 text-green-500" />
                  <span>{document.publicationYear}</span>
                </div>
                {document.journal && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Building className="h-4 w-4 text-purple-500" />
                    <span className="italic">{document.journal}</span>
                  </div>
                )}
              </div>
              
              {/* Quality indicators */}
              {qualityIndicators.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {qualityIndicators.map((indicator, index) => (
                    <TooltipProvider key={index}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                            <indicator.icon className={cn("h-3 w-3", indicator.color)} />
                            {indicator.label}
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{indicator.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
              )}
            </div>
            
            {/* Action buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleDocumentExpansion(document.id)}
                className="h-8 w-8 p-0"
              >
                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewDocument(document)}
                      className="h-8 px-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 hover:from-blue-600 hover:to-purple-700"
                    >
                      <Brain className="h-4 w-4 mr-1" />
                      AI Summary
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Generate AI-powered comprehensive summary</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0 space-y-4">
          {/* Abstract */}
          {document.abstract && (
            <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500">
              <div className="flex items-center gap-2 mb-2">
                <Quote className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-semibold text-gray-700">Abstract</span>
              </div>
              <p className={cn(
                "text-sm text-gray-700 leading-relaxed",
                !isExpanded && "line-clamp-3"
              )}>
                {document.abstract}
              </p>
            </div>
          )}
          
          {/* Keywords */}
          {document.keywords.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-indigo-500" />
                <span className="text-sm font-semibold text-gray-700">Keywords</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {document.keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="text-xs bg-indigo-50 text-indigo-700 border-indigo-200">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {/* Expanded content */}
          {isExpanded && (
            <div className="space-y-4 border-t pt-4">
              {/* Key Findings */}
              {document.keyFindings.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-semibold text-gray-700">Key Findings</span>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg space-y-2">
                    {document.keyFindings.map((finding, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-sm text-gray-700">{finding}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Methodology */}
              {document.methodology.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-semibold text-gray-700">Methodology</span>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg space-y-2">
                    {document.methodology.map((method, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-sm text-gray-700">{method}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Limitations */}
              {document.limitations.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-semibold text-gray-700">Limitations</span>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg space-y-2">
                    {document.limitations.map((limitation, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-sm text-gray-700">{limitation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Future Work */}
              {document.futureWork.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-semibold text-gray-700">Future Work</span>
                  </div>
                  <div className="bg-yellow-50 p-3 rounded-lg space-y-2">
                    {document.futureWork.map((future, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                        <p className="text-sm text-gray-700">{future}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Document sections */}
              {document.sections.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-semibold text-gray-700">Document Structure</span>
                  </div>
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                      {document.sections.map((section, index) => (
                        <div key={index} className="flex items-center gap-2 text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                          <span className="capitalize">{section.type?.replace('_', ' ') || `Section ${index + 1}`}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Footer with metadata and actions */}
          <div className="flex items-center justify-between pt-3 border-t">
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <FileCheck className="h-3 w-3" />
                <span>{document.sections.length} sections</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{new Date(document.uploadedAt).toLocaleDateString()}</span>
              </div>
              {document.fileSize && (
                <div className="flex items-center gap-1">
                  <Database className="h-3 w-3" />
                  <span>{(document.fileSize / 1024 / 1024).toFixed(1)}MB</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={() => copyDocumentInfo(document)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Copy document info</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={() => toast.info('Share functionality coming soon')}>
                      <Share className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Share document</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Heart className={cn("h-4 w-4", document.favorite ? "text-red-500 fill-current" : "text-gray-400")} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Add to favorites</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Statistics summary
  const getLibraryStats = () => {
    const total = documents.length;
    const ready = documents.filter(d => d.status === 'ready').length;
    const processing = documents.filter(d => d.status === 'processing').length;
    const errors = documents.filter(d => d.status === 'error').length;
    const withFindings = documents.filter(d => d.keyFindings.length > 0).length;
    const withMethods = documents.filter(d => d.methodology.length > 0).length;
    
    return { total, ready, processing, errors, withFindings, withMethods };
  };

  const stats = getLibraryStats();

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Statistics */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 rounded-lg">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Research Library</h2>
                <p className="text-sm text-gray-600">AI-Enhanced Document Analysis</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'detailed' ? 'grid' : 'detailed')}
                className="bg-white"
              >
                {viewMode === 'detailed' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="bg-white"
              >
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Statistics Dashboard */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-xs text-gray-600">Total Documents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.ready}</div>
              <div className="text-xs text-gray-600">Ready</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.processing}</div>
              <div className="text-xs text-gray-600">Processing</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.errors}</div>
              <div className="text-xs text-gray-600">Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.withFindings}</div>
              <div className="text-xs text-gray-600">With Findings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">{stats.withMethods}</div>
              <div className="text-xs text-gray-600">With Methods</div>
            </div>
          </div>
          
          {/* Enhanced Search and Controls */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search documents, authors, keywords, or content..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 bg-white"
                />
              </div>
              <Select value={sortBy} onValueChange={(value: string) => setSortBy(value as typeof sortBy)}>
                <SelectTrigger className="w-40 bg-white">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Upload Date</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="year">Publication Year</SelectItem>
                  <SelectItem value="authors">Authors</SelectItem>
                  <SelectItem value="relevance">Relevance</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="bg-white"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
            
            {/* Selection Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="bg-white"
                >
                  {selectedDocuments.length === sortedDocuments.length ? 'Deselect All' : 'Select All'}
                </Button>
                {selectedDocuments.length > 0 && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {selectedDocuments.length} selected
                  </Badge>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Showing {sortedDocuments.length} of {documents.length} documents
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Documents Display */}
      <div className="space-y-4">
        {sortedDocuments.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto">
                <BookOpen className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">No documents found</h3>
                <p className="text-gray-600 mt-1">
                  {searchQuery || Object.values(filters).some(f => f !== 'all' && f !== false && f !== 0.5) 
                    ? 'Try adjusting your search or filters'
                    : 'Upload some documents to get started'
                  }
                </p>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-4">
            {sortedDocuments.map(document => renderEnhancedDocumentCard(document))}
          </div>
        )}
      </div>

      {/* Enhanced Document Preview Dialog */}
      <Dialog open={!!previewDocument} onOpenChange={() => setPreviewDocument(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          {previewDocument && (
            <>
              <DialogHeader className="border-b pb-4">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <Brain className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight">
                      AI-Generated Summary
                    </DialogTitle>
                    <p className="text-gray-600 mt-1">
                      Comprehensive analysis of "{previewDocument.document.title}"
                    </p>
                  </div>
                  {previewDocument.isLoading && (
                    <div className="flex items-center gap-2 text-blue-600">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Analyzing...</span>
                    </div>
                  )}
                </div>
              </DialogHeader>
              
              <ScrollArea className="max-h-[70vh]">
                <div className="space-y-6 p-6">
                  {previewDocument.isLoading ? (
                    <div className="flex flex-col items-center justify-center py-12 space-y-6">
                      <div className="relative">
                        <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <Brain className="h-10 w-10 text-white animate-pulse" />
                        </div>
                        <div className="absolute -inset-2 rounded-full border-4 border-blue-200 animate-spin border-t-blue-500"></div>
                      </div>
                      <div className="text-center space-y-2">
                        <h3 className="text-lg font-semibold text-gray-900">AI Analysis in Progress</h3>
                        <p className="text-gray-600 max-w-md">
                          Our advanced AI is carefully analyzing the document structure, content, methodology, 
                          and key findings to generate a comprehensive summary...
                        </p>
                        <div className="flex items-center justify-center gap-1 mt-4">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-8">
                      {/* Document Information Card */}
                      <Card className="border-2 border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2 text-lg">
                            <FileText className="h-5 w-5 text-blue-500" />
                            Document Information
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid md:grid-cols-2 gap-6">
                            <div className="space-y-3">
                              <div>
                                <span className="font-semibold text-gray-700">Title:</span>
                                <p className="text-gray-900 mt-1">{previewDocument.document.title}</p>
                              </div>
                              <div>
                                <span className="font-semibold text-gray-700">Authors: <AUTHORS>
                                <p className="text-gray-900 mt-1">{previewDocument.document.authors.join(', ')}</p>
                              </div>
                              <div>
                                <span className="font-semibold text-gray-700">Publication Year:</span>
                                <p className="text-gray-900 mt-1">{previewDocument.document.publicationYear}</p>
                              </div>
                            </div>
                            <div className="space-y-3">
                              {previewDocument.document.journal && (
                                <div>
                                  <span className="font-semibold text-gray-700">Journal:</span>
                                  <p className="text-gray-900 mt-1">{previewDocument.document.journal}</p>
                                </div>
                              )}
                              {previewDocument.document.doi && (
                                <div>
                                  <span className="font-semibold text-gray-700">DOI:</span>
                                  <p className="text-gray-900 mt-1 font-mono text-sm">{previewDocument.document.doi}</p>
                                </div>
                              )}
                              <div>
                                <span className="font-semibold text-gray-700">Analysis Confidence:</span>
                                <div className="flex items-center gap-2 mt-1">
                                  <Progress value={(previewDocument.document.metadata?.confidence || 0) * 100} className="flex-1" />
                                  <span className="text-sm font-medium">
                                    {Math.round((previewDocument.document.metadata?.confidence || 0) * 100)}%
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* AI Summary */}
                      {previewDocument.summary && (
                        <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-blue-900">
                              <Sparkles className="h-5 w-5 text-blue-600" />
                              AI-Generated Comprehensive Summary
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="prose prose-blue max-w-none">
                              <div className="bg-white p-6 rounded-lg border border-blue-200 shadow-sm">
                                <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                                  {previewDocument.summary}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                      
                      {/* Key Findings */}
                      {previewDocument.document.keyFindings.length > 0 && (
                        <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-green-900">
                              <Target className="h-5 w-5 text-green-600" />
                              Key Findings ({previewDocument.document.keyFindings.length})
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {previewDocument.document.keyFindings.map((finding, index) => (
                                <div key={index} className="flex items-start gap-3 p-4 bg-white rounded-lg border border-green-200">
                                  <div className="w-6 h-6 rounded-full bg-green-500 text-white flex items-center justify-center text-sm font-bold flex-shrink-0">
                                    {index + 1}
                                  </div>
                                  <p className="text-gray-800 leading-relaxed">{finding}</p>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Methodology */}
                      {previewDocument.document.methodology.length > 0 && (
                        <Card className="border-2 border-purple-200 bg-gradient-to-r from-purple-50 to-violet-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-purple-900">
                              <Brain className="h-5 w-5 text-purple-600" />
                              Research Methodology ({previewDocument.document.methodology.length})
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {previewDocument.document.methodology.map((method, index) => (
                                <div key={index} className="flex items-start gap-3 p-4 bg-white rounded-lg border border-purple-200">
                                  <div className="w-6 h-6 rounded-full bg-purple-500 text-white flex items-center justify-center text-sm font-bold flex-shrink-0">
                                    {index + 1}
                                  </div>
                                  <p className="text-gray-800 leading-relaxed">{method}</p>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Limitations */}
                      {previewDocument.document.limitations.length > 0 && (
                        <Card className="border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-orange-900">
                              <AlertTriangle className="h-5 w-5 text-orange-600" />
                              Study Limitations ({previewDocument.document.limitations.length})
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {previewDocument.document.limitations.map((limitation, index) => (
                                <div key={index} className="flex items-start gap-3 p-4 bg-white rounded-lg border border-orange-200">
                                  <div className="w-6 h-6 rounded-full bg-orange-500 text-white flex items-center justify-center text-sm font-bold flex-shrink-0">
                                    {index + 1}
                                  </div>
                                  <p className="text-gray-800 leading-relaxed">{limitation}</p>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Future Work */}
                      {previewDocument.document.futureWork.length > 0 && (
                        <Card className="border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-amber-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-yellow-900">
                              <Lightbulb className="h-5 w-5 text-yellow-600" />
                              Future Research Directions ({previewDocument.document.futureWork.length})
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {previewDocument.document.futureWork.map((future, index) => (
                                <div key={index} className="flex items-start gap-3 p-4 bg-white rounded-lg border border-yellow-200">
                                  <div className="w-6 h-6 rounded-full bg-yellow-500 text-white flex items-center justify-center text-sm font-bold flex-shrink-0">
                                    {index + 1}
                                  </div>
                                  <p className="text-gray-800 leading-relaxed">{future}</p>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Abstract */}
                      {previewDocument.document.abstract && (
                        <Card className="border-2 border-gray-200">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-gray-900">
                              <Quote className="h-5 w-5 text-gray-600" />
                              Original Abstract
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                              <p className="text-gray-800 leading-relaxed italic">
                                {previewDocument.document.abstract}
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Keywords */}
                      {previewDocument.document.keywords.length > 0 && (
                        <Card className="border-2 border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50">
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-lg text-indigo-900">
                              <Tag className="h-5 w-5 text-indigo-600" />
                              Keywords & Topics
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-wrap gap-2">
                              {previewDocument.document.keywords.map((keyword, index) => (
                                <Badge key={index} variant="secondary" className="bg-indigo-100 text-indigo-800 border-indigo-200 px-3 py-1">
                                  #{keyword}
                                </Badge>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
