/**
 * OpenRouter Agent Service for Targeted Document Editing
 * Provides context-aware, targeted editing capabilities using OpenRouter API
 */

import { DocumentSection, EditInstruction, TargetedEditResult } from './GeminiAgentService';

export interface OpenRouterModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
}

export const OPENROUTER_MODELS: OpenRouterModel[] = [
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    description: 'Most capable model for complex tasks',
    maxTokens: 200000,
    costPer1kTokens: 0.003
  },
  {
    id: 'anthropic/claude-3-haiku',
    name: 'Claude 3 Haiku',
    description: 'Fast and efficient for simple tasks',
    maxTokens: 200000,
    costPer1kTokens: 0.00025
  },
  {
    id: 'openai/gpt-4o',
    name: 'GPT-4o',
    description: 'Latest GPT-4 model with improved capabilities',
    maxTokens: 128000,
    costPer1kTokens: 0.005
  },
  {
    id: 'openai/gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: 'Smaller, faster GPT-4 variant',
    maxTokens: 128000,
    costPer1kTokens: 0.00015
  },
  {
    id: 'meta-llama/llama-3.1-70b-instruct',
    name: 'Llama 3.1 70B',
    description: 'Open source model with strong performance',
    maxTokens: 131072,
    costPer1kTokens: 0.00059
  },
  {
    id: 'google/gemini-pro-1.5',
    name: 'Gemini Pro 1.5',
    description: 'Google\'s advanced model via OpenRouter',
    maxTokens: 2000000,
    costPer1kTokens: 0.00125
  }
];

export class OpenRouterAgentService {
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1/chat/completions';
  private currentModel: string = 'anthropic/claude-3.5-sonnet';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    console.log('🤖 OpenRouterAgentService initialized');
    console.log('🔑 API Key available:', this.hasValidApiKey());
  }

  /**
   * Check if API key is configured
   */
  hasValidApiKey(): boolean {
    return !!this.apiKey && this.apiKey.length > 20;
  }

  /**
   * Set the model to use
   */
  setModel(modelId: string): void {
    this.currentModel = modelId;
    console.log('🔄 Switched to model:', modelId);
  }

  /**
   * Get current model info
   */
  getCurrentModel(): OpenRouterModel | undefined {
    return OPENROUTER_MODELS.find(m => m.id === this.currentModel);
  }

  /**
   * Test the API connection
   */
  async testApiConnection(): Promise<boolean> {
    try {
      console.log('🧪 Testing OpenRouter API connection...');
      const response = await this.callOpenRouterAPI('Say "Hello" in one word.');
      console.log('✅ OpenRouter API test successful:', response);
      return true;
    } catch (error) {
      console.error('❌ OpenRouter API test failed:', error);
      return false;
    }
  }

  /**
   * Execute targeted editing using OpenRouter
   */
  async executeTargetedEdit(
    userRequest: string,
    documentContent: string,
    options: {
      editMode?: 'conservative' | 'moderate' | 'aggressive';
      maxSections?: number;
      preserveFormatting?: boolean;
      model?: string;
    } = {}
  ): Promise<TargetedEditResult> {
    if (!this.hasValidApiKey()) {
      throw new Error('OpenRouter API key not configured. Please set VITE_OPENROUTER_API_KEY in your environment.');
    }

    try {
      console.log('🎯 Starting targeted edit with OpenRouter:', {
        userRequest,
        documentLength: documentContent.length,
        model: options.model || this.currentModel,
        options
      });

      // Set model if specified
      if (options.model) {
        this.setModel(options.model);
      }

      // Step 1: Analyze document structure (reuse from Gemini service)
      const sections = this.analyzeDocumentStructure(documentContent);
      
      // Step 2: Identify relevant sections
      const relevantSections = this.identifyRelevantSections(userRequest, sections);
      
      // Step 3: Generate targeted edits
      const edits = await this.generateEditsForSections(userRequest, relevantSections, options);
      
      // Step 4: Prepare result
      const result: TargetedEditResult = {
        success: true,
        edits,
        summary: `Applied ${edits.length} targeted edit(s) using ${this.getCurrentModel()?.name || this.currentModel}`,
        reasoning: `Analyzed ${sections.length} sections, identified ${relevantSections.length} relevant sections, and made ${edits.length} targeted improvements.`,
        confidence: this.calculateConfidence(edits),
        sectionsAnalyzed: sections.length,
        sectionsModified: edits.length,
        warnings: edits.length === 0 ? ['No changes were needed or could be made based on your request.'] : undefined
      };

      console.log('✅ OpenRouter targeted edit completed:', result);
      return result;

    } catch (error: any) {
      console.error('❌ OpenRouter targeted edit failed:', error);
      return {
        success: false,
        edits: [],
        summary: 'Failed to complete targeted editing with OpenRouter',
        reasoning: error.message || 'Unknown error occurred',
        confidence: 0,
        sectionsAnalyzed: 0,
        sectionsModified: 0,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  /**
   * Analyze document structure (reuse logic from Gemini service)
   */
  private analyzeDocumentStructure(htmlContent: string): DocumentSection[] {
    const sections: DocumentSection[] = [];
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    let sectionId = 0;
    const elements = doc.body.querySelectorAll('h1, h2, h3, h4, h5, h6, p, ul, ol, table, blockquote, div');
    
    elements.forEach((element) => {
      const textContent = element.textContent?.trim() || '';
      if (textContent.length < 5) return;
      
      if (element.tagName === 'DIV' && element.children.length > 0) {
        const hasSignificantText = textContent.length > 20;
        const hasOnlyInlineChildren = Array.from(element.children).every(child => 
          ['SPAN', 'A', 'STRONG', 'EM', 'B', 'I'].includes(child.tagName)
        );
        
        if (!hasSignificantText || !hasOnlyInlineChildren) {
          return;
        }
      }
      
      let type: DocumentSection['type'] = 'other';
      let level: number | undefined;
      
      if (element.tagName.match(/^H[1-6]$/)) {
        type = 'heading';
        level = parseInt(element.tagName.charAt(1));
      } else if (element.tagName === 'P' || (element.tagName === 'DIV' && textContent.length > 20)) {
        type = 'paragraph';
      } else if (element.tagName === 'UL' || element.tagName === 'OL') {
        type = 'list';
      } else if (element.tagName === 'TABLE') {
        type = 'table';
      }
      
      sections.push({
        id: `section_${sectionId++}`,
        type,
        content: textContent,
        htmlContent: element.outerHTML,
        startIndex: 0,
        endIndex: 0,
        level
      });
    });
    
    console.log(`📊 Analyzed document structure: ${sections.length} sections found`);
    return sections;
  }

  /**
   * Identify relevant sections using heuristics
   */
  private identifyRelevantSections(userRequest: string, sections: DocumentSection[]): DocumentSection[] {
    const request = userRequest.toLowerCase();
    console.log('🎯 Identifying relevant sections for request:', request);
    
    // Special handling for reference/citation removal
    if (request.includes('remove') && (request.includes('reference') || request.includes('citation'))) {
      console.log('🎯 Looking for sections with references/citations');
      const relevantSections = sections.filter(section => {
        const content = section.content.toLowerCase();
        return content.includes('reference') || 
               content.includes('citation') || 
               content.includes('bibliography') ||
               content.includes('et al') ||
               content.includes('(') && content.includes(')') ||
               /\[\d+\]/.test(content) ||
               /\(\w+,?\s*\d{4}\)/.test(content);
      });
      
      if (relevantSections.length > 0) {
        console.log(`📍 Found ${relevantSections.length} sections with references/citations`);
        return relevantSections;
      }
    }
    
    // Default: return substantial text sections
    const relevantSections = sections.filter(section => 
      (section.type === 'paragraph' || section.type === 'heading') && 
      section.content.length > 30
    ).slice(0, 8);
    
    console.log(`📍 Using default selection: ${relevantSections.length} sections`);
    return relevantSections;
  }

  /**
   * Generate edits for sections using OpenRouter
   */
  private async generateEditsForSections(
    userRequest: string,
    sections: DocumentSection[],
    options: any
  ): Promise<EditInstruction[]> {
    const edits: EditInstruction[] = [];
    const maxSections = Math.min(sections.length, options.maxSections || 5);
    
    console.log(`🔧 Generating edits for ${maxSections} sections using ${this.currentModel}`);
    
    for (let i = 0; i < maxSections; i++) {
      const section = sections[i];
      try {
        console.log(`📝 Processing section ${i + 1}/${maxSections}: ${section.type} (${section.content.length} chars)`);
        
        const editPrompt = this.createEditPrompt(userRequest, section);
        const response = await this.callOpenRouterAPI(editPrompt);
        
        const edit = this.parseEditResponse(response, section);
        if (edit) {
          console.log(`✅ Created edit for section ${section.id}`);
          edits.push(edit);
        } else {
          console.log(`❌ No meaningful edit created for section ${section.id}`);
        }
        
        // Add delay to respect rate limits
        if (i < maxSections - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
      } catch (error: any) {
        console.error(`❌ Failed to edit section ${section.id}:`, error);
      }
    }
    
    console.log(`🎯 Generated ${edits.length} edits out of ${maxSections} sections processed`);
    return edits;
  }

  /**
   * Create edit prompt for OpenRouter
   */
  private createEditPrompt(userRequest: string, section: DocumentSection): string {
    let specificInstructions = '';
    const request = userRequest.toLowerCase();
    
    if (request.includes('remove') && (request.includes('reference') || request.includes('citation'))) {
      specificInstructions = `
TASK: Remove all references and citations from this text.
- Remove in-text citations like (Author, Year), [1], etc.
- Remove reference lists completely
- Remove any mention of "References", "Bibliography"
- Keep all other content intact`;
    } else {
      specificInstructions = `TASK: ${userRequest}`;
    }

    return `${specificInstructions}

Original text:
${section.content}

Return only the edited text with no explanations:`;
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouterAPI(prompt: string): Promise<string> {
    console.log('🚀 Calling OpenRouter API with model:', this.currentModel);
    
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Paper Genius Agent Mode'
      },
      body: JSON.stringify({
        model: this.currentModel,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 4096
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error (${response.status}): ${errorText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;
    
    if (!content) {
      throw new Error('No content returned from OpenRouter API');
    }
    
    return content.trim();
  }

  /**
   * Parse edit response
   */
  private parseEditResponse(response: string, originalSection: DocumentSection): EditInstruction | null {
    const editedContent = response.trim();
    
    if (!editedContent) return null;
    
    const normalizedOriginal = originalSection.content.replace(/\s+/g, ' ').trim();
    const normalizedEdited = editedContent.replace(/\s+/g, ' ').trim();
    
    if (normalizedEdited === normalizedOriginal) return null;
    
    return {
      sectionId: originalSection.id,
      originalContent: originalSection.content,
      editedContent,
      changeType: 'modify',
      reasoning: 'Content improved based on user request'
    };
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(edits: EditInstruction[]): number {
    if (edits.length === 0) return 0.5;
    return Math.min(0.95, 0.7 + (edits.length * 0.05));
  }
}

export const openRouterAgentService = new OpenRouterAgentService();
