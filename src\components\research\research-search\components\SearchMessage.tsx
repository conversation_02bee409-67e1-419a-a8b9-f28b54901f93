/**
 * Enhanced Search Message Component
 * Displays individual messages with beautiful formatting for long-form content
 */

import React, { useState } from 'react';
import {
  User,
  Bot,
  ExternalLink,
  Copy,
  Check,
  ChevronDown,
  ChevronUp,
  Link,
  Calendar,
  Star,
  BookOpen,
  FileText,
  Quote,
  Maximize2,
  Minimize2,
  GraduationCap,
  Edit,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SearchMessage as SearchMessageType, SearchSource, Citation } from '../types';
import { TextWithCitations } from './InlineCitation';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { editorService } from '../../paper-generator/editor.service';
import { documentExportService } from '../../paper-generator/document-export.service';

interface SearchMessageProps {
  message: SearchMessageType;
  showSources?: boolean;
  showCitations?: boolean;
  isLongForm?: boolean;
}

export function SearchMessage({
  message,
  showSources = true,
  showCitations = true,
  isLongForm = false
}: SearchMessageProps) {
  const [copied, setCopied] = useState(false);
  const [sourcesExpanded, setSourcesExpanded] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [citationsExpanded, setCitationsExpanded] = useState(false);

  const isUser = message.type === 'user';
  const isLongContent = message.content.length > 1500 || isLongForm;
  const hasMultipleParagraphs = message.content.split('\n\n').length > 3;

  // Enhanced content display for academic writing - no truncation for academic content
  const TRUNCATE_LENGTH = 2000; // Increased for academic content
  const isAcademicContent = message.content.includes('References') ||
                           message.content.includes('## ') ||
                           message.content.includes('(') && message.content.includes(', 20');

  // Only truncate non-academic content or very long content
  const shouldTruncate = !isUser &&
                        !isAcademicContent &&
                        message.content.length > TRUNCATE_LENGTH &&
                        !isExpanded;

  const displayContent = shouldTruncate
    ? message.content.substring(0, TRUNCATE_LENGTH) + '...'
    : message.content;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      toast.success('Message copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error('Failed to copy message');
    }
  };

  const handleSendToEditor = async () => {
    try {
      const title = `Research: ${message.content.substring(0, 50)}...`;
      const success = await editorService.sendToMainEditor({
        title,
        content: message.content
      });

      if (success) {
        toast.success('Content sent to editor successfully!');
      } else {
        toast.error('Failed to send content to editor');
      }
    } catch (error) {
      console.error('Error sending to editor:', error);
      toast.error('Failed to send content to editor');
    }
  };

  const handleDownloadWord = async () => {
    try {
      const title = `Research_${new Date().toISOString().split('T')[0]}`;
      const fileName = `${title}.docx`;

      await documentExportService.exportMarkdownToDocx(
        'Research Content',
        message.content,
        fileName
      );

      toast.success('Word document downloaded successfully!');
    } catch (error) {
      console.error('Error downloading Word document:', error);
      toast.error('Failed to download Word document');
    }
  };

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getSourceTypeColor = (type: string) => {
    switch (type) {
      case 'academic': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'news': return 'bg-green-100 text-green-800 border-green-200';
      case 'book': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatContent = (content: string) => {
    const paragraphs = content.split('\n\n').filter(p => p.trim());

    return paragraphs.map((paragraph, index) => {
      if (paragraph.startsWith('#')) {
        const level = paragraph.match(/^#+/)?.[0].length || 1;
        const text = paragraph.replace(/^#+\s*/, '');
        const headingClass = level === 1 ? 'text-xl font-bold mb-3 text-gray-900' :
                           level === 2 ? 'text-lg font-semibold mb-2 text-gray-800' :
                           'text-base font-medium mb-2 text-gray-700';

        return (
          <h3 key={index} className={headingClass}>
            {text}
          </h3>
        );
      }

      if (paragraph.match(/^[\d\-\*]\s/)) {
        const items = paragraph.split('\n').filter(item => item.trim());
        return (
          <ul key={index} className="list-disc list-inside space-y-1 mb-4 text-gray-700">
            {items.map((item, itemIndex) => (
              <li key={itemIndex} className="leading-relaxed">
                {item.replace(/^[\d\-\*]\s*/, '')}
              </li>
            ))}
          </ul>
        );
      }

      return (
        <p key={index} className="mb-4 leading-relaxed text-gray-700 last:mb-0">
          {paragraph}
        </p>
      );
    });
  };

  return (
    <div className={cn(
      "group relative mb-6",
      isUser ? "flex justify-end" : "flex justify-start"
    )}>
      <div className={cn(
        "relative w-full",
        isUser ? "max-w-2xl" : "max-w-none"
      )}>
        {/* Clean Academic Message Container */}
        <div className={cn(
          "relative rounded-2xl transition-all duration-300",
          isUser
            ? "bg-gradient-to-br from-indigo-600 to-purple-700 text-white ml-auto p-4 shadow-lg"
            : "bg-white border border-gray-200/60 p-6 shadow-sm hover:shadow-md"
        )}>
          {/* Simple Avatar */}
          {!isUser && (
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-sm">
                <Bot className="w-4 h-4 text-white" />
              </div>
            </div>
          )}
          {/* Clean Academic Content Area */}
          <div className={cn(
            "prose prose-lg max-w-none",
            isUser ? "prose-invert" : "prose-gray",
            "prose-headings:font-semibold prose-headings:text-gray-900 prose-headings:mb-3",
            "prose-p:text-gray-800 prose-p:leading-relaxed prose-p:text-base prose-p:mb-4",
            "prose-strong:text-gray-900 prose-strong:font-semibold",
            "prose-em:text-gray-700 prose-em:italic",
            "prose-ul:text-gray-800 prose-ol:text-gray-800",
            "prose-li:text-gray-800 prose-li:leading-relaxed prose-li:mb-1",
            "prose-blockquote:border-l-gray-300 prose-blockquote:bg-gray-50 prose-blockquote:p-4 prose-blockquote:rounded-r-lg prose-blockquote:italic"
          )}>
            <TextWithCitations
              content={displayContent}
              citations={message.citations}
              className={isUser ? "text-white" : "text-gray-800"}
            />
          </div>

          {/* Expand/Collapse Button for Long Content - Only for non-academic content */}
          {!isUser && shouldTruncate && (
            <div className="mt-4 pt-3 border-t border-gray-100">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 text-sm font-medium transition-colors"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-2" />
                    Show Less
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-2" />
                    Show Full Content ({Math.round((message.content.length - TRUNCATE_LENGTH) / 100) * 100}+ more characters)
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Academic Content Indicator */}
          {!isUser && isAcademicContent && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <GraduationCap className="w-4 h-4 text-blue-600" />
                <span className="font-medium">Academic Content - Full text displayed</span>
                <span className="text-gray-500">({message.content.length.toLocaleString()} characters)</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          {!isUser && (
            <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-8 px-3 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {copied ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
                  {copied ? 'Copied' : 'Copy'}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSendToEditor}
                  className="h-8 px-3 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Send to Editor
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownloadWord}
                  className="h-8 px-3 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download Word
                </Button>
              </div>

              <div className="text-xs text-gray-500">
                {formatTime(message.timestamp)}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
