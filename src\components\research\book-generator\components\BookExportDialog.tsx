import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>ead<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  FileType, 
  Printer, 
  Edit, 
  BookOpen,
  FileText,
  Globe,
  Smartphone,
  Settings
} from "lucide-react";
import { GeneratedChapter, BookMetadata, BookExportOptions, Citation } from '../types';
import { documentExportService } from '../../paper-generator/document-export.service';
import { editorService } from '../../paper-generator/editor.service';
import { bookHistoryService } from '@/services/bookHistoryService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface BookExportDialogProps {
  bookMetadata: BookMetadata;
  generatedChapters: GeneratedChapter[];
  generatedSections: GeneratedChapter[];
  allCitations: Citation[];
  onEditInEditor: () => void;
  bookId?: string; // Optional book ID for saving exports
}

export const BookExportDialog: React.FC<BookExportDialogProps> = ({
  bookMetadata,
  generatedChapters,
  generatedSections,
  allCitations,
  onEditInEditor,
  bookId
}) => {
  const { user } = useAuth();
  const [exportOptions, setExportOptions] = useState<BookExportOptions>({
    format: 'docx',
    includeTableOfContents: true,
    includePreface: true,
    includeAcknowledgments: false,
    includeGlossary: false,
    includeIndex: false,
    includeBibliography: true,
    includeAboutAuthor: false,
    chapterNumbering: 'numeric',
    pageNumbering: true,
    fontSize: 'medium',
    lineSpacing: '1.5'
  });
  const [isExporting, setIsExporting] = useState(false);

  const formatOptions = [
    { 
      id: 'docx', 
      name: 'Microsoft Word (.docx)', 
      icon: <FileType className="h-4 w-4" />,
      description: 'Editable document format'
    },
    { 
      id: 'pdf', 
      name: 'PDF Document (.pdf)', 
      icon: <FileText className="h-4 w-4" />,
      description: 'Print-ready format'
    },
    { 
      id: 'html', 
      name: 'Web Page (.html)', 
      icon: <Globe className="h-4 w-4" />,
      description: 'Web-friendly format'
    },
    { 
      id: 'epub', 
      name: 'E-book (.epub)', 
      icon: <Smartphone className="h-4 w-4" />,
      description: 'E-reader compatible'
    }
  ];

  const chapterNumberingOptions = [
    { id: 'numeric', name: 'Numeric (1, 2, 3...)' },
    { id: 'roman', name: 'Roman (I, II, III...)' },
    { id: 'none', name: 'No numbering' }
  ];

  const updateExportOption = <K extends keyof BookExportOptions>(
    key: K, 
    value: BookExportOptions[K]
  ) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const generateBookContent = (): string => {
    // Combine content but avoid duplicates by using a Map to deduplicate by ID
    const contentMap = new Map();

    // Add sections first
    generatedSections.forEach(section => {
      if (section.content) {
        contentMap.set(section.id, section);
      }
    });

    // Add chapters (will overwrite sections with same ID, which is what we want)
    generatedChapters.forEach(chapter => {
      if (chapter.content) {
        contentMap.set(chapter.id, chapter);
      }
    });

    const allContent = Array.from(contentMap.values()).sort((a, b) => a.order - b.order);

    console.log('📖 Export: All content for book generation:', allContent.map(item => ({
      id: item.id,
      title: item.title,
      hasContent: !!item.content,
      contentLength: item.content?.length || 0
    })));

    let bookContent = '';

    // Title page
    bookContent += `# ${bookMetadata.title}\n\n`;
    if (bookMetadata.subtitle) {
      bookContent += `## ${bookMetadata.subtitle}\n\n`;
    }
    if (bookMetadata.authors.length > 0) {
      bookContent += `**By:** ${bookMetadata.authors.join(', ')}\n\n`;
    }
    if (bookMetadata.description) {
      bookContent += `*${bookMetadata.description}*\n\n`;
    }
    bookContent += `---\n\n`;

    // Preface (if enabled)
    if (exportOptions.includePreface) {
      bookContent += `# Preface\n\n`;
      bookContent += `This book explores ${bookMetadata.title.toLowerCase()} with a focus on ${bookMetadata.targetAudience.toLowerCase()} readers. `;
      bookContent += `The content is organized into ${allContent.length} comprehensive chapters that build upon each other to provide `;
      bookContent += `a thorough understanding of the subject matter.\n\n`;

      if (bookMetadata.keywords.length > 0) {
        bookContent += `Key topics covered include: ${bookMetadata.keywords.join(', ')}.\n\n`;
      }

      bookContent += `Each chapter is designed to be both informative and practical, providing readers with the knowledge `;
      bookContent += `and tools needed to understand and apply the concepts presented.\n\n`;

      if (bookMetadata.authors.length > 0) {
        bookContent += `We hope this book serves as a valuable resource in your journey to master ${bookMetadata.title.toLowerCase()}.\n\n`;
        bookContent += `${bookMetadata.authors.join(', ')}\n\n`;
      }

      bookContent += `---\n\n`;
    }

    // Acknowledgments (if enabled)
    if (exportOptions.includeAcknowledgments) {
      bookContent += `# Acknowledgments\n\n`;
      bookContent += `We would like to express our gratitude to all those who contributed to the creation of this book. `;
      bookContent += `Special thanks to the experts in the field who provided valuable insights and feedback during the writing process.\n\n`;
      bookContent += `We also acknowledge the countless researchers, practitioners, and thought leaders whose work has shaped `;
      bookContent += `our understanding of ${bookMetadata.title.toLowerCase()} and made this book possible.\n\n`;
      bookContent += `---\n\n`;
    }

    // Table of Contents (if enabled)
    if (exportOptions.includeTableOfContents) {
      bookContent += `# Table of Contents\n\n`;

      // Add front matter to TOC
      if (exportOptions.includePreface) {
        bookContent += `Preface\n\n`;
      }

      if (exportOptions.includeAcknowledgments) {
        bookContent += `Acknowledgments\n\n`;
      }

      // Add chapters to TOC
      allContent.forEach((item, index) => {
        const chapterNum = exportOptions.chapterNumbering === 'numeric'
          ? `${index + 1}. `
          : exportOptions.chapterNumbering === 'roman'
          ? `${toRoman(index + 1)}. `
          : '';
        bookContent += `${chapterNum}${item.title}\n`;

        // Add subsections to TOC if they exist
        if (item.subSections && item.subSections.length > 0) {
          item.subSections.forEach(subSection => {
            bookContent += `    ${subSection.title}\n`;
          });
        }
      });

      // Add back matter to TOC
      if (exportOptions.includeBibliography && allCitations.length > 0) {
        bookContent += `\nBibliography\n`;
      }

      if (exportOptions.includeGlossary) {
        bookContent += `Glossary\n`;
      }

      if (exportOptions.includeIndex) {
        bookContent += `Index\n`;
      }

      if (exportOptions.includeAboutAuthor) {
        bookContent += `About the Author\n`;
      }

      bookContent += `\n---\n\n`;
    }

    return bookContent;
  };

  const generateEnhancedBookContent = (): string => {
    const basicContent = generateBookContent();

    // Use the same deduplication logic as generateBookContent
    const contentMap = new Map();

    generatedSections.forEach(section => {
      if (section.content) {
        contentMap.set(section.id, section);
      }
    });

    generatedChapters.forEach(chapter => {
      if (chapter.content) {
        contentMap.set(chapter.id, chapter);
      }
    });

    const allContent = Array.from(contentMap.values()).sort((a, b) => a.order - b.order);

    let enhancedContent = basicContent;

    // Main content with enhanced formatting
    allContent.forEach((item, index) => {
      const chapterNum = exportOptions.chapterNumbering === 'numeric'
        ? `${index + 1}. `
        : exportOptions.chapterNumbering === 'roman'
        ? `${toRoman(index + 1)}. `
        : '';

      enhancedContent += `# Chapter ${chapterNum}${item.title}\n\n`;

      // Process and format chapter content
      const formattedContent = formatChapterContent(item.content || '');
      enhancedContent += `${formattedContent}\n\n`;

      enhancedContent += `---\n\n`;
    });

    // Bibliography (if enabled and citations exist)
    if (exportOptions.includeBibliography && allCitations.length > 0) {
      enhancedContent += `# Bibliography\n\n`;

      // Get unique citations
      const uniqueCitations = allCitations.reduce((acc, citation) => {
        const existing = acc.find(c =>
          c.authors.join(',') === citation.authors.join(',') &&
          c.year === citation.year &&
          c.title === citation.title
        );

        if (!existing) {
          acc.push(citation);
        }

        return acc;
      }, [] as Citation[]);

      uniqueCitations
        .sort((a, b) => a.authors[0]?.localeCompare(b.authors[0]) || 0)
        .forEach(citation => {
          const authors = citation.authors.length > 3
            ? `${citation.authors[0]} et al.`
            : citation.authors.join(', ');

          enhancedContent += `${authors} (${citation.year}). *${citation.title}*. ${citation.source}`;
          if (citation.doi) {
            enhancedContent += `. DOI: ${citation.doi}`;
          }
          enhancedContent += `\n\n`;
        });

      enhancedContent += `---\n\n`;
    }

    // Glossary (if enabled)
    if (exportOptions.includeGlossary) {
      enhancedContent += `# Glossary\n\n`;
      enhancedContent += `*This section would contain key terms and definitions used throughout the book.*\n\n`;
      enhancedContent += `---\n\n`;
    }

    // Index (if enabled)
    if (exportOptions.includeIndex) {
      enhancedContent += `# Index\n\n`;
      enhancedContent += `*This section would contain an alphabetical index of important topics and page references.*\n\n`;
      enhancedContent += `---\n\n`;
    }

    // About the Author (if enabled)
    if (exportOptions.includeAboutAuthor && bookMetadata.authors.length > 0) {
      enhancedContent += `# About the Author${bookMetadata.authors.length > 1 ? 's' : ''}\n\n`;

      bookMetadata.authors.forEach(author => {
        enhancedContent += `**${author}** is an expert in ${bookMetadata.genre.toLowerCase()} with extensive experience in the field. `;
        enhancedContent += `This book represents their deep knowledge and practical insights gained through years of research and practice.\n\n`;
      });
    }

    return enhancedContent;
  };

  const formatChapterContent = (content: string): string => {
    if (!content) return '';

    // First, clean the entire content of markdown artifacts
    let cleanedContent = content
      // Remove markdown headers but preserve text
      .replace(/^#{1,6}\s+(.+)$/gm, '$1')
      // Remove bold markdown
      .replace(/\*\*(.*?)\*\*/g, '$1')
      // Remove italic markdown
      .replace(/\*(.*?)\*/g, '$1')
      // Remove code markdown
      .replace(/`(.*?)`/g, '$1')
      // Remove underline markdown
      .replace(/_{2,}(.*?)_{2,}/g, '$1')
      // Remove strikethrough markdown
      .replace(/~~(.*?)~~/g, '$1')
      // Remove links, keep text
      .replace(/\[(.*?)\]\(.*?\)/g, '$1')
      // Remove horizontal rules
      .replace(/^[-=]{3,}$/gm, '')
      // Remove bullet points and numbered lists formatting
      .replace(/^\s*[-*+]\s+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove blockquotes
      .replace(/^\s*>\s+/gm, '')
      // Remove table separators
      .replace(/\|/g, ' ')
      // Clean up multiple newlines
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    // Split content into paragraphs and process each one
    const paragraphs = cleanedContent.split('\n\n').filter(p => p.trim());
    let formattedContent = '';

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim();

      // Skip empty paragraphs
      if (!paragraph) continue;

      // Check if this looks like a heading
      if (isLikelyHeading(paragraph)) {
        // Determine heading level based on context and length
        const headingLevel = determineHeadingLevel(paragraph, i, paragraphs);
        const cleanHeading = cleanParagraphText(paragraph);
        formattedContent += `${'#'.repeat(headingLevel + 1)} ${cleanHeading}\n\n`;
      } else {
        // Regular paragraph - ensure proper formatting
        const cleanParagraph = cleanParagraphText(paragraph);
        if (cleanParagraph) { // Only add non-empty paragraphs
          formattedContent += `${cleanParagraph}\n\n`;
        }
      }
    }

    return formattedContent.trim();
  };

  const isLikelyHeading = (text: string): boolean => {
    // Heuristics to identify headings
    const trimmed = text.trim();

    // Too long to be a heading
    if (trimmed.length > 100) return false;

    // Starts with a capital letter
    if (!/^[A-Z]/.test(trimmed)) return false;

    // Doesn't end with a period (most headings don't)
    if (trimmed.endsWith('.') && trimmed.length > 50) return false;

    // Contains common heading words
    const headingWords = ['introduction', 'overview', 'conclusion', 'summary', 'background', 'methodology', 'results', 'discussion', 'analysis', 'implementation', 'approach', 'framework', 'principles', 'concepts', 'applications', 'benefits', 'challenges', 'future', 'recommendations'];
    const lowerText = trimmed.toLowerCase();

    if (headingWords.some(word => lowerText.includes(word))) return true;

    // Short and doesn't end with period
    if (trimmed.length < 60 && !trimmed.endsWith('.')) return true;

    return false;
  };

  const determineHeadingLevel = (text: string, index: number, allParagraphs: string[]): number => {
    // Start with level 2 (## in markdown) since chapter title is level 1
    let level = 2;

    // If it's very short and likely a major section, make it level 2
    if (text.length < 30) return 2;

    // If it's longer, make it level 3 (subsection)
    if (text.length > 30) return 3;

    return level;
  };

  const cleanParagraphText = (text: string): string => {
    return text
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Ensure proper sentence spacing
      .replace(/([.!?])\s*([A-Z])/g, '$1 $2')
      // Remove any remaining markdown symbols
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
      .replace(/\*(.*?)\*/g, '$1') // Remove italic markdown
      .replace(/`(.*?)`/g, '$1') // Remove code markdown
      .replace(/_{2,}(.*?)_{2,}/g, '$1') // Remove underline markdown
      .replace(/~~(.*?)~~/g, '$1') // Remove strikethrough markdown
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
      .replace(/#{1,6}\s*/g, '') // Remove markdown headers
      .replace(/^\s*[-*+]\s+/gm, '') // Remove bullet points
      .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered lists
      .replace(/^\s*>\s+/gm, '') // Remove blockquotes
      .replace(/\|/g, ' ') // Remove table separators
      .replace(/[-=]{3,}/g, '') // Remove horizontal rules
      // Clean up any remaining special characters that might cause formatting issues
      .replace(/[`~^]/g, '')
      // Normalize whitespace again
      .replace(/\s+/g, ' ')
      .trim();
  };

  const handleExport = async () => {
    setIsExporting(true);

    try {
      const bookContent = generateEnhancedBookContent();
      const fileName = `${bookMetadata.title.replace(/[^a-zA-Z0-9]/g, '_')}_book`;

      switch (exportOptions.format) {
        case 'docx':
          await documentExportService.exportEnhancedBookToDocx(
            bookContent,
            bookMetadata,
            exportOptions,
            fileName
          );
          break;
        case 'pdf':
          await documentExportService.exportEnhancedBookToPdf(
            bookContent,
            bookMetadata,
            exportOptions,
            fileName
          );
          break;
        case 'html':
          // Create HTML export
          const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${bookMetadata.title}</title>
    <style>
        body { font-family: Georgia, serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 2px solid #333; }
        h2 { color: #666; }
        .metadata { background: #f5f5f5; padding: 15px; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="metadata">
        <h1>${bookMetadata.title}</h1>
        ${bookMetadata.subtitle ? `<h2>${bookMetadata.subtitle}</h2>` : ''}
        <p><strong>Genre:</strong> ${bookMetadata.genre}</p>
        <p><strong>Target Audience:</strong> ${bookMetadata.targetAudience}</p>
        ${bookMetadata.authors.length > 0 ? `<p><strong>Authors: <AUTHORS>
    </div>
    ${bookContent.replace(/\n/g, '<br>').replace(/# /g, '<h1>').replace(/<h1>/g, '</h1><h1>').replace(/## /g, '<h2>').replace(/<h2>/g, '</h2><h2>')}
</body>
</html>`;
          
          const blob = new Blob([htmlContent], { type: 'text/html' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${fileName}.html`;
          a.click();
          URL.revokeObjectURL(url);
          break;
        case 'epub':
          // For now, export as text file with EPUB structure
          const epubContent = `EPUB Structure for: ${bookMetadata.title}\n\n${bookContent}`;
          const epubBlob = new Blob([epubContent], { type: 'text/plain' });
          const epubUrl = URL.createObjectURL(epubBlob);
          const epubLink = document.createElement('a');
          epubLink.href = epubUrl;
          epubLink.download = `${fileName}.txt`;
          epubLink.click();
          URL.revokeObjectURL(epubUrl);
          break;
      }

      // Save export record to database if user is authenticated and bookId is available
      if (user && bookId) {
        try {
          await bookHistoryService.saveBookExport(
            bookId,
            exportOptions.format,
            `${fileName}.${exportOptions.format}`,
            undefined, // file_path - would be set if using Supabase storage
            undefined, // file_size - would be calculated if storing file
            exportOptions
          );
          console.log('Export record saved to database');
        } catch (error) {
          console.error('Failed to save export record:', error);
          // Don't show error to user as export was successful
        }
      }

      toast.success(`Book exported successfully as ${exportOptions.format.toUpperCase()}!`);
    } catch (error: any) {
      console.error('Export error:', error);
      toast.error(`Export failed: ${error.message || 'Please try again'}`);
    } finally {
      setIsExporting(false);
    }
  };

  const handleEditInEditor = () => {
    const bookContent = generateEnhancedBookContent();

    // Convert markdown to HTML for the editor
    const htmlContent = convertMarkdownToHTML(bookContent);

    editorService.sendToMainEditor({
      title: bookMetadata.title,
      content: htmlContent
    });

    // Trigger navigation to editor in the parent component
    onEditInEditor();
    toast.success("Book loaded in editor for further editing.");
  };

  const convertMarkdownToHTML = (markdown: string): string => {
    // First clean the markdown content thoroughly
    let cleanedMarkdown = markdown
      // Remove markdown headers but preserve text for proper HTML conversion
      .replace(/^#{1,6}\s+(.+)$/gm, '$1')
      // Remove bold and italic markdown
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      // Remove code markdown
      .replace(/`(.*?)`/g, '$1')
      // Remove other markdown symbols
      .replace(/_{2,}(.*?)_{2,}/g, '$1')
      .replace(/~~(.*?)~~/g, '$1')
      .replace(/\[(.*?)\]\(.*?\)/g, '$1')
      // Remove horizontal rules
      .replace(/^[-=]{3,}$/gm, '')
      // Remove bullet points and numbered lists
      .replace(/^\s*[-*+]\s+/gm, '')
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove blockquotes
      .replace(/^\s*>\s+/gm, '')
      // Clean up multiple newlines
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    // Convert to HTML with proper structure
    return cleanedMarkdown
      .split('\n\n')
      .filter(paragraph => paragraph.trim())
      .map(paragraph => {
        const trimmed = paragraph.trim();

        // Skip empty paragraphs
        if (!trimmed) return '';

        // Check if it's likely a heading
        if (isLikelyHeading(trimmed)) {
          const level = determineHeadingLevel(trimmed, 0, []);
          return `<h${level} style="font-weight: bold; margin-top: 1.5rem; margin-bottom: 1rem;">${trimmed}</h${level}>`;
        }

        // Regular paragraph with proper styling
        return `<p style="margin-bottom: 1rem; line-height: 1.6; text-align: justify;">${trimmed}</p>`;
      })
      .filter(html => html.trim())
      .join('\n');
  };

  // Helper function for Roman numerals
  const toRoman = (num: number): string => {
    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
    let result = '';
    
    for (let i = 0; i < values.length; i++) {
      while (num >= values[i]) {
        result += symbols[i];
        num -= values[i];
      }
    }
    
    return result;
  };

  const selectedFormat = formatOptions.find(f => f.id === exportOptions.format);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="lg" className="flex-1">
          <Download className="h-4 w-4 mr-2" />
          Export Book
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Export Book: {bookMetadata.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">Export Format</Label>
            <Select 
              value={exportOptions.format} 
              onValueChange={(value) => updateExportOption('format', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {formatOptions.map((format) => (
                  <SelectItem key={format.id} value={format.id}>
                    <div className="flex items-center gap-2">
                      {format.icon}
                      <div>
                        <div className="font-medium">{format.name}</div>
                        <div className="text-sm text-gray-500">{format.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedFormat && (
              <p className="text-sm text-gray-600">{selectedFormat.description}</p>
            )}
          </div>

          <Separator />

          {/* Book Structure Options */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Book Structure</Label>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="toc"
                  checked={exportOptions.includeTableOfContents}
                  onCheckedChange={(checked) => updateExportOption('includeTableOfContents', !!checked)}
                />
                <Label htmlFor="toc" className="text-sm">Table of Contents</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="preface"
                  checked={exportOptions.includePreface}
                  onCheckedChange={(checked) => updateExportOption('includePreface', !!checked)}
                />
                <Label htmlFor="preface" className="text-sm">Preface</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="acknowledgments"
                  checked={exportOptions.includeAcknowledgments}
                  onCheckedChange={(checked) => updateExportOption('includeAcknowledgments', !!checked)}
                />
                <Label htmlFor="acknowledgments" className="text-sm">Acknowledgments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bibliography"
                  checked={exportOptions.includeBibliography}
                  onCheckedChange={(checked) => updateExportOption('includeBibliography', !!checked)}
                />
                <Label htmlFor="bibliography" className="text-sm">Bibliography</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="glossary"
                  checked={exportOptions.includeGlossary}
                  onCheckedChange={(checked) => updateExportOption('includeGlossary', !!checked)}
                />
                <Label htmlFor="glossary" className="text-sm">Glossary</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="index"
                  checked={exportOptions.includeIndex}
                  onCheckedChange={(checked) => updateExportOption('includeIndex', !!checked)}
                />
                <Label htmlFor="index" className="text-sm">Index</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="aboutAuthor"
                  checked={exportOptions.includeAboutAuthor}
                  onCheckedChange={(checked) => updateExportOption('includeAboutAuthor', !!checked)}
                />
                <Label htmlFor="aboutAuthor" className="text-sm">About the Author</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Formatting Options */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Formatting</Label>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm">Chapter Numbering</Label>
                <Select
                  value={exportOptions.chapterNumbering}
                  onValueChange={(value) => updateExportOption('chapterNumbering', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {chapterNumberingOptions.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm">Font Size</Label>
                <Select
                  value={exportOptions.fontSize}
                  onValueChange={(value) => updateExportOption('fontSize', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small (10pt)</SelectItem>
                    <SelectItem value="medium">Medium (12pt)</SelectItem>
                    <SelectItem value="large">Large (14pt)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm">Line Spacing</Label>
                <Select
                  value={exportOptions.lineSpacing}
                  onValueChange={(value) => updateExportOption('lineSpacing', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">Single</SelectItem>
                    <SelectItem value="1.5">1.5 Lines</SelectItem>
                    <SelectItem value="double">Double</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id="pageNumbers"
                  checked={exportOptions.pageNumbering}
                  onCheckedChange={(checked) => updateExportOption('pageNumbering', !!checked)}
                />
                <Label htmlFor="pageNumbers" className="text-sm">Page Numbers</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button 
              onClick={handleExport}
              disabled={isExporting}
              className="flex-1"
            >
              {isExporting ? (
                <>
                  <Settings className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {selectedFormat?.name.split(' ')[0]}
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleEditInEditor}
              className="flex-1"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit in Editor
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
