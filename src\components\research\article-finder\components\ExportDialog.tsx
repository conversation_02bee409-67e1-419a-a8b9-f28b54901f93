/**
 * Export Dialog Component
 * Handles exporting journal recommendations and analysis data
 */

import React, { useState } from 'react';
import { ExportDialogProps, ExportOptions, ExportFormat } from '../types';
import { EXPORT_FORMATS } from '../constants';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Download, 
  FileText, 
  FileEdit, 
  Database, 
  Table,
  CheckCircle,
  Info,
  Settings
} from "lucide-react";

export const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  data,
  onExport
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeAnalysis: true,
    includeRecommendations: true,
    includeMetrics: true,
    includeRankings: true,
    maxRecommendations: 10
  });

  const [isExporting, setIsExporting] = useState(false);

  /**
   * Handle export option change
   */
  const handleOptionChange = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  /**
   * Handle export execution
   */
  const handleExport = async () => {
    setIsExporting(true);
    try {
      await onExport(exportOptions);
      // Close dialog after successful export
      setTimeout(() => {
        setIsExporting(false);
        onClose();
      }, 1000);
    } catch (error) {
      console.error('Export failed:', error);
      setIsExporting(false);
    }
  };

  /**
   * Get format icon
   */
  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case 'pdf': return FileText;
      case 'docx': return FileEdit;
      case 'json': return Database;
      case 'csv': return Table;
      default: return FileText;
    }
  };

  /**
   * Calculate estimated file size
   */
  const getEstimatedSize = (): string => {
    const baseSize = 50; // KB
    let size = baseSize;
    
    if (exportOptions.includeAnalysis) size += 20;
    if (exportOptions.includeRecommendations) size += exportOptions.maxRecommendations * 5;
    if (exportOptions.includeMetrics) size += exportOptions.maxRecommendations * 2;
    if (exportOptions.includeRankings) size += exportOptions.maxRecommendations * 1;
    
    if (exportOptions.format === 'pdf') size *= 1.5;
    if (exportOptions.format === 'docx') size *= 1.2;
    
    if (size < 1024) return `${Math.round(size)} KB`;
    return `${(size / 1024).toFixed(1)} MB`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Download className="w-5 h-5 mr-2" />
            Export Journal Recommendations
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div>
            <Label className="text-base font-semibold text-gray-900 mb-3 block">
              Export Format
            </Label>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(EXPORT_FORMATS).map(([format, info]) => {
                const Icon = getFormatIcon(format as ExportFormat);
                return (
                  <Card 
                    key={format}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      exportOptions.format === format 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleOptionChange('format', format)}
                  >
                    <CardContent className="p-4 text-center">
                      <Icon className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                      <h3 className="font-semibold text-gray-900 mb-1">{info.label}</h3>
                      <p className="text-xs text-gray-600">{info.description}</p>
                      {exportOptions.format === format && (
                        <Badge className="mt-2 bg-blue-600">Selected</Badge>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Content Options */}
          <div>
            <Label className="text-base font-semibold text-gray-900 mb-3 block">
              Content to Include
            </Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeAnalysis"
                  checked={exportOptions.includeAnalysis}
                  onCheckedChange={(checked) => handleOptionChange('includeAnalysis', checked)}
                />
                <Label htmlFor="includeAnalysis" className="text-sm font-medium">
                  Article Analysis
                </Label>
                <Badge variant="outline" className="text-xs">
                  Research domain, methodology, key topics
                </Badge>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRecommendations"
                  checked={exportOptions.includeRecommendations}
                  onCheckedChange={(checked) => handleOptionChange('includeRecommendations', checked)}
                />
                <Label htmlFor="includeRecommendations" className="text-sm font-medium">
                  Journal Recommendations
                </Label>
                <Badge variant="outline" className="text-xs">
                  Journal details and descriptions
                </Badge>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeMetrics"
                  checked={exportOptions.includeMetrics}
                  onCheckedChange={(checked) => handleOptionChange('includeMetrics', checked)}
                />
                <Label htmlFor="includeMetrics" className="text-sm font-medium">
                  Journal Metrics
                </Label>
                <Badge variant="outline" className="text-xs">
                  Impact factor, acceptance rates, timelines
                </Badge>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRankings"
                  checked={exportOptions.includeRankings}
                  onCheckedChange={(checked) => handleOptionChange('includeRankings', checked)}
                />
                <Label htmlFor="includeRankings" className="text-sm font-medium">
                  Ranking Analysis
                </Label>
                <Badge variant="outline" className="text-xs">
                  Scoring breakdown and criteria weights
                </Badge>
              </div>
            </div>
          </div>

          {/* Number of Recommendations */}
          {exportOptions.includeRecommendations && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <Label className="text-base font-semibold text-gray-900">
                  Number of Recommendations
                </Label>
                <Badge variant="outline">
                  {exportOptions.maxRecommendations} journals
                </Badge>
              </div>
              <Slider
                value={[exportOptions.maxRecommendations]}
                onValueChange={(value) => handleOptionChange('maxRecommendations', value[0])}
                min={1}
                max={Math.min(data.recommendations.length, 20)}
                step={1}
                className="mb-2"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1 journal</span>
                <span>{Math.min(data.recommendations.length, 20)} journals</span>
              </div>
            </div>
          )}

          {/* Export Preview */}
          <Card className="bg-gray-50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Export Preview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Format:</span>
                  <span className="ml-2 text-gray-900">{EXPORT_FORMATS[exportOptions.format].label}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Estimated Size:</span>
                  <span className="ml-2 text-gray-900">{getEstimatedSize()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Journals:</span>
                  <span className="ml-2 text-gray-900">
                    {exportOptions.includeRecommendations ? exportOptions.maxRecommendations : 0}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Sections:</span>
                  <span className="ml-2 text-gray-900">
                    {[
                      exportOptions.includeAnalysis && 'Analysis',
                      exportOptions.includeRecommendations && 'Recommendations',
                      exportOptions.includeMetrics && 'Metrics',
                      exportOptions.includeRankings && 'Rankings'
                    ].filter(Boolean).length}
                  </span>
                </div>
              </div>

              {/* Content Summary */}
              <div className="pt-3 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-2">Content Summary:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {exportOptions.includeAnalysis && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      Research analysis and key findings
                    </li>
                  )}
                  {exportOptions.includeRecommendations && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      {exportOptions.maxRecommendations} journal recommendations with details
                    </li>
                  )}
                  {exportOptions.includeMetrics && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      Impact factors, acceptance rates, and publication timelines
                    </li>
                  )}
                  {exportOptions.includeRankings && (
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                      Ranking scores and criteria breakdown
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Info Alert */}
          <Alert className="border-blue-200 bg-blue-50">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              Your export will include all selected content formatted for easy sharing and reference. 
              The file will be downloaded to your default downloads folder.
            </AlertDescription>
          </Alert>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              Cancel
            </Button>
            <Button 
              onClick={handleExport} 
              disabled={isExporting || (!exportOptions.includeAnalysis && !exportOptions.includeRecommendations)}
            >
              {isExporting ? (
                <>
                  <Download className="w-4 h-4 mr-2 animate-pulse" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {EXPORT_FORMATS[exportOptions.format].label}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
