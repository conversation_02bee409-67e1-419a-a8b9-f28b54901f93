import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  <PERSON>, 
  Sparkles, 
  FileText, 
  BookOpen, 
  Target,
  TrendingUp,
  Zap,
  Clock
} from 'lucide-react';

interface EnhancedLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  stats?: {
    sections: number;
    citations: number;
    references: number;
    progress: number;
  };
  isGenerating?: boolean;
  className?: string;
}

export const EnhancedLayout: React.FC<EnhancedLayoutProps> = ({
  children,
  title = "AI Research Paper Generator",
  subtitle = "Generate academic papers with AI precision",
  stats,
  isGenerating = false,
  className = ""
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 ${className}`}>
      {/* Enhan<PERSON> Header */}
      <div className="sticky top-0 z-50 bg-white/80 backdrop-blur-lg border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Brain className={`h-8 w-8 text-blue-600 ${isGenerating ? 'animate-pulse' : ''}`} />
                {isGenerating && (
                  <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
                )}
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{title}</h1>
                <p className="text-sm text-gray-600">{subtitle}</p>
              </div>
            </div>

            {/* Stats Display */}
            {stats && (
              <div className="hidden md:flex items-center gap-4">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  <FileText className="h-3 w-3 mr-1" />
                  {stats.sections} Sections
                </Badge>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <BookOpen className="h-3 w-3 mr-1" />
                  {stats.citations} Citations
                </Badge>
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                  <Target className="h-3 w-3 mr-1" />
                  {stats.references} References
                </Badge>
                {isGenerating && (
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    <Zap className="h-3 w-3 mr-1 animate-pulse" />
                    {stats.progress}% Complete
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </div>

      {/* Enhanced Footer */}
      <footer className="bg-white/60 backdrop-blur-sm border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Brain className="h-4 w-4" />
              <span>Powered by Advanced AI Technology</span>
            </div>
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                <span>Academic Quality</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Real-time Generation</span>
              </div>
              <div className="flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span>AI Enhanced</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

interface SectionLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'outline';
    color?: string;
  };
  actions?: React.ReactNode;
  className?: string;
}

export const SectionLayout: React.FC<SectionLayoutProps> = ({
  children,
  title,
  description,
  icon: Icon,
  badge,
  actions,
  className = ""
}) => {
  return (
    <Card className={`shadow-lg border-0 bg-white/70 backdrop-blur-sm ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-50 to-purple-50">
                <Icon className="h-5 w-5 text-blue-600" />
              </div>
            )}
            <div>
              <CardTitle className="flex items-center gap-2 text-xl">
                {title}
                {badge && (
                  <Badge 
                    variant={badge.variant || 'outline'} 
                    className={badge.color || 'bg-blue-50 text-blue-700 border-blue-200'}
                  >
                    {badge.text}
                  </Badge>
                )}
              </CardTitle>
              {description && (
                <p className="text-sm text-gray-600 mt-1">{description}</p>
              )}
            </div>
          </div>
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};

interface GridLayoutProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const GridLayout: React.FC<GridLayoutProps> = ({
  children,
  columns = 2,
  gap = 'md',
  className = ""
}) => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 lg:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  return (
    <div className={`grid ${columnClasses[columns]} ${gapClasses[gap]} ${className}`}>
      {children}
    </div>
  );
};

interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'full',
  padding = 'md',
  className = ""
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-7xl'
  };

  const paddingClasses = {
    sm: 'px-4 py-2',
    md: 'px-6 py-4',
    lg: 'px-8 py-6'
  };

  return (
    <div className={`${maxWidthClasses[maxWidth]} mx-auto ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};
