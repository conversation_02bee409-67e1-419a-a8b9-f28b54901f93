import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ThumbsUp, 
  ThumbsDown, 
  RefreshCw, 
  Edit, 
  Eye, 
  MessageSquare,
  FileText,
  BarChart3,
  Quote,
  Loader2
} from "lucide-react";
import { GeneratedChapter, GeneratedOutline } from '../types';
import { toast } from 'sonner';

interface ChapterReviewCardProps {
  chapter: GeneratedChapter;
  chapterIndex: number;
  outline?: GeneratedOutline;
  onApprove: () => void;
  onRegenerate: (feedback?: string) => void;
  onEdit: (content: string) => void;
  isRegenerating?: boolean;
}

export const ChapterReviewCard: React.FC<ChapterReviewCardProps> = ({
  chapter,
  chapterIndex,
  outline,
  onApprove,
  onRegenerate,
  onEdit,
  isRegenerating = false
}) => {
  // Add safety checks for chapter
  if (!chapter) {
    console.error(`ChapterReviewCard: Chapter is undefined for index ${chapterIndex}`);
    return (
      <Card className="shadow-2xl border-0 bg-gradient-to-br from-red-50 via-red-100/30 to-red-50/20 backdrop-blur-sm overflow-hidden">
        <CardHeader className="pb-6 bg-gradient-to-r from-red-600/10 via-red-600/10 to-red-600/10">
          <CardTitle className="text-red-800">Chapter Loading Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">Chapter data is not available. Please try regenerating the chapter.</p>
          <Button
            onClick={() => onRegenerate()}
            className="mt-4 bg-red-600 hover:bg-red-700"
            disabled={isRegenerating}
          >
            {isRegenerating ? 'Regenerating...' : 'Regenerate Chapter'}
          </Button>
        </CardContent>
      </Card>
    );
  }

  const [feedback, setFeedback] = useState('');
  const [editedContent, setEditedContent] = useState(chapter.content || '');
  const [isEditing, setIsEditing] = useState(false);

  // Debug logging
  console.log(`ChapterReviewCard for chapter ${chapterIndex + 1}:`, {
    hasContent: !!chapter.content,
    contentLength: chapter.content?.length || 0,
    contentPreview: chapter.content?.substring(0, 100) || 'No content',
    chapterStatus: chapter.status,
    chapterTitle: chapter.title
  });

  const handleRegenerate = () => {
    if (feedback.trim()) {
      onRegenerate(feedback);
      setFeedback('');
    } else {
      onRegenerate();
    }
  };

  const handleSaveEdit = () => {
    onEdit(editedContent);
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedContent(chapter.content || '');
    setIsEditing(false);
  };

  const wordCount = chapter.content?.split(' ').length || 0;
  const estimatedReadTime = Math.ceil(wordCount / 200); // Average reading speed

  // Handle case where chapter exists but content is missing
  if (!chapter.content && chapter.status !== 'content-generating') {
    return (
      <Card className="shadow-2xl border-0 bg-gradient-to-br from-yellow-50 via-yellow-100/30 to-yellow-50/20 backdrop-blur-sm overflow-hidden">
        <CardHeader className="pb-6 bg-gradient-to-r from-yellow-600/10 via-yellow-600/10 to-yellow-600/10">
          <CardTitle className="flex items-center gap-4">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="px-3 py-1 bg-white/80 backdrop-blur-sm border-yellow-200 text-yellow-700 font-semibold">
                  Chapter {chapterIndex + 1}
                </Badge>
                <span className="text-xl font-bold text-gray-800">{chapter.title}</span>
              </div>
              <span className="text-sm text-gray-600 font-medium mt-1">Content not yet generated</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-yellow-700">
              This chapter hasn't been generated yet or the content is missing.
              {outline ? ' The outline is ready.' : ' No outline available.'}
            </p>

            {outline && (
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 className="font-semibold text-yellow-800 mb-2">Chapter Outline</h4>
                <p className="text-sm text-yellow-700 mb-2">{outline.description}</p>
                {outline.sections && outline.sections.length > 0 && (
                  <div className="space-y-2">
                    {outline.sections.map((section) => (
                      <div key={section.id} className="pl-4 border-l-2 border-yellow-300">
                        <div className="font-medium text-sm text-yellow-800">{section.title}</div>
                        <div className="text-xs text-yellow-600">{section.description}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            <Button
              onClick={() => onRegenerate()}
              className="w-full bg-yellow-600 hover:bg-yellow-700"
              disabled={isRegenerating}
            >
              {isRegenerating ? 'Generating Content...' : 'Generate Chapter Content'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-2xl border-0 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/20 backdrop-blur-sm overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-4">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="px-3 py-1 bg-white/80 backdrop-blur-sm border-blue-200 text-blue-700 font-semibold">
                  Chapter {chapterIndex + 1}
                </Badge>
                <span className="text-xl font-bold text-gray-800">{chapter.title}</span>
              </div>
              <span className="text-sm text-gray-600 font-medium mt-1">
                {outline?.description || 'AI-generated chapter content'}
              </span>
            </div>
          </CardTitle>

          <div className="flex items-center gap-3 flex-wrap">
            <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200 shadow-sm">
              <BarChart3 className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-semibold text-gray-700">
                {wordCount.toLocaleString()} words
              </span>
            </div>

            <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200 shadow-sm">
              <Eye className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-semibold text-gray-700">
                ~{estimatedReadTime} min read
              </span>
            </div>

            {chapter.citations && chapter.citations.length > 0 && (
              <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm rounded-lg px-3 py-2 border border-gray-200 shadow-sm">
                <Quote className="h-4 w-4 text-green-600" />
                <span className="text-sm font-semibold text-gray-700">
                  {chapter.citations.length} citations
                </span>
              </div>
            )}

            <Badge
              variant={chapter.status === 'completed' ? 'default' : 'secondary'}
              className={`px-4 py-2 font-semibold text-sm ${
                chapter.status === 'completed'
                  ? 'bg-green-100 text-green-700 border-green-200 shadow-sm'
                  : 'bg-yellow-100 text-yellow-700 border-yellow-200 shadow-sm'
              }`}
            >
              {chapter.status === 'completed' ? '✅ Complete' : '⏳ Processing'}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="content" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="outline">Outline</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
          </TabsList>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-4">
            {isEditing ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Edit Chapter Content</h4>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveEdit} size="sm">
                      Save Changes
                    </Button>
                    <Button onClick={handleCancelEdit} variant="outline" size="sm">
                      Cancel
                    </Button>
                  </div>
                </div>
                <Textarea
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  rows={20}
                  className="font-mono text-sm"
                />
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">Generated Content</h4>
                  <Button
                    onClick={() => setIsEditing(true)}
                    variant="outline"
                    size="sm"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Content
                  </Button>
                </div>
                <ScrollArea className="h-96 border rounded-lg p-4 bg-gray-50">
                  <div className="prose max-w-none">
                    {chapter.content ? (
                      chapter.content.split('\n').map((paragraph, index) => (
                        <p key={index} className="mb-3 text-sm leading-relaxed">
                          {paragraph}
                        </p>
                      ))
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p className="text-lg font-medium">No content available</p>
                        <p className="text-sm">Chapter content has not been generated yet.</p>
                        <p className="text-xs mt-2">Status: {chapter.status}</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            )}
          </TabsContent>

          {/* Outline Tab */}
          <TabsContent value="outline" className="space-y-4">
            {outline ? (
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold mb-2">Chapter Overview</h4>
                  <p className="text-sm text-gray-700">{outline.description}</p>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {outline.keyPoints.map((point, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {point}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold">Section Structure</h4>
                  {outline.sections.map((section) => (
                    <div key={section.id} className="pl-4 border-l-2 border-blue-200">
                      <div className="font-medium text-sm">{section.title}</div>
                      <div className="text-xs text-gray-600 mb-1">{section.description}</div>
                      <div className="text-xs text-gray-500">
                        Target: {section.estimatedWordCount} words
                      </div>
                      {section.keyPoints.length > 0 && (
                        <div className="mt-1 flex flex-wrap gap-1">
                          {section.keyPoints.map((point, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {point}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No outline available for this chapter</p>
              </div>
            )}
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="stats" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">{wordCount.toLocaleString()}</div>
                <div className="text-sm text-blue-800">Words Written</div>
              </div>
              <div className="p-4 bg-green-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">
                  {outline?.estimatedWordCount.toLocaleString() || 'N/A'}
                </div>
                <div className="text-sm text-green-800">Target Words</div>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {chapter.citations?.length || 0}
                </div>
                <div className="text-sm text-purple-800">Citations</div>
              </div>
              <div className="p-4 bg-orange-50 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">{estimatedReadTime}</div>
                <div className="text-sm text-orange-800">Minutes to Read</div>
              </div>
            </div>

            {outline && (
              <div className="space-y-3">
                <h4 className="font-semibold">Progress vs Target</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Word Count Progress</span>
                    <span>{Math.round((wordCount / outline.estimatedWordCount) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((wordCount / outline.estimatedWordCount) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Feedback Section */}
        <div className="mt-6 space-y-4 border-t pt-4">
          <div className="space-y-3">
            <label className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Feedback for Improvement (Optional)
            </label>
            <Textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Provide specific feedback if you'd like the chapter regenerated with improvements..."
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              onClick={onApprove}
              size="lg"
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              disabled={isRegenerating}
            >
              <ThumbsUp className="h-5 w-5 mr-2" />
              ✅ Approve & Continue
            </Button>

            <Button
              onClick={() => setIsEditing(!isEditing)}
              variant="outline"
              size="lg"
              className="border-2 border-blue-300 text-blue-700 hover:bg-blue-50 font-semibold shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              disabled={isRegenerating}
            >
              <Edit className="h-5 w-5 mr-2" />
              ✏️ Edit Content
            </Button>

            <Button
              onClick={handleRegenerate}
              variant="outline"
              size="lg"
              className="border-2 border-purple-300 text-purple-700 hover:bg-purple-50 font-semibold shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
              disabled={isRegenerating}
            >
              {isRegenerating ? (
                <>
                  <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  🔄 Regenerating...
                </>
              ) : (
                <>
                  <RefreshCw className="h-5 w-5 mr-2" />
                  🔄 {feedback.trim() ? 'Regenerate with Feedback' : 'Regenerate Chapter'}
                </>
              )}
            </Button>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center justify-center gap-4 pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-600 font-medium">Quick Actions:</div>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              onClick={() => {
                const element = document.getElementById(`chapter-content-${chapterIndex}`);
                element?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
              onClick={() => {
                navigator.clipboard.writeText(chapter.content || '');
                toast.success('Chapter content copied to clipboard!');
              }}
            >
              📋 Copy
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
