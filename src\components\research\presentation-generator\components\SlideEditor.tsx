import React, { useState } from 'react';
import {
  Edit3,
  Download,
  Play,
  Plus,
  <PERSON>ting<PERSON>,
  <PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>own,
  Eye,
  FileText
} from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

import { Presentation } from '../types';
import { usePresentationStore } from '../stores/presentation.store';
import { SLIDE_TEMPLATES } from '../constants';
import { SlideRenderer } from './SlideRenderer';
import { PresentationMode } from './PresentationMode';

interface SlideEditorProps {
  presentation: Presentation | null;
  selectedSlideId: string | null;
  onExport: () => void;
}

export function SlideEditor({ presentation, selectedSlideId, onExport }: SlideEditorProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showThemePanel, setShowThemePanel] = useState(false);
  const [isPresentationMode, setIsPresentationMode] = useState(false);

  const {
    setSelectedSlide,
    deleteSlide,
    duplicateSlide,
    reorderSlides,
    selectedTheme,
    setTheme
  } = usePresentationStore();

  if (!presentation) {
    return (
      <div className="text-center py-12">
        <Edit3 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold mb-2">No Presentation Selected</h3>
        <p className="text-gray-600">
          Please create a presentation first to start editing.
        </p>
      </div>
    );
  }

  const handleSlideAction = (slideId: string, action: 'edit' | 'delete' | 'duplicate' | 'moveUp' | 'moveDown') => {
    const slideIndex = presentation.slides.findIndex(s => s.id === slideId);

    switch (action) {
      case 'edit':
        setSelectedSlide(slideId);
        break;
      case 'delete':
        if (confirm('Are you sure you want to delete this slide?')) {
          deleteSlide(slideId);
        }
        break;
      case 'duplicate':
        duplicateSlide(slideId);
        break;
      case 'moveUp':
        if (slideIndex > 0) {
          reorderSlides(slideIndex, slideIndex - 1);
        }
        break;
      case 'moveDown':
        if (slideIndex < presentation.slides.length - 1) {
          reorderSlides(slideIndex, slideIndex + 1);
        }
        break;
    }
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{presentation.metadata.title}</h2>
          <div className="flex items-center gap-4 mt-1">
            <p className="text-gray-600">{presentation.slides.length} slides</p>
            <Badge variant="secondary">{selectedTheme.displayName} Theme</Badge>
            <Badge variant="outline">{presentation.status}</Badge>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              Grid
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              List
            </Button>
          </div>

          <Separator orientation="vertical" className="h-8" />

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowThemePanel(!showThemePanel)}
          >
            <Palette className="w-4 h-4 mr-2" />
            Themes
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPresentationMode(true)}
          >
            <Play className="w-4 h-4 mr-2" />
            Present
          </Button>
          <Button onClick={onExport}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Slides Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Slides Panel */}
        <div className="lg:col-span-3">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {presentation.slides.map((slide, index) => (
                <Card
                  key={slide.id}
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    selectedSlideId === slide.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => setSelectedSlide(slide.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {index + 1}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {SLIDE_TEMPLATES.find(t => t.layout === slide.layout)?.name || slide.layout}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'moveUp');
                          }}
                          disabled={index === 0}
                        >
                          <ArrowUp className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'moveDown');
                          }}
                          disabled={index === presentation.slides.length - 1}
                        >
                          <ArrowDown className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="aspect-video rounded-lg mb-3 overflow-hidden border">
                      <SlideRenderer
                        slide={slide}
                        theme={selectedTheme}
                        isPreview={true}
                        className="h-full"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-gray-600 line-clamp-1 flex-1">
                        {slide.title}
                      </p>
                      <div className="flex items-center gap-1 ml-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'duplicate');
                          }}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'delete');
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Add New Slide */}
              <Card className="border-2 border-dashed border-gray-300 hover:border-blue-500 cursor-pointer transition-colors">
                <CardContent className="flex items-center justify-center h-full min-h-[200px]">
                  <div className="text-center">
                    <Plus className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">Add New Slide</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="space-y-3">
              {presentation.slides.map((slide, index) => (
                <Card
                  key={slide.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedSlideId === slide.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => setSelectedSlide(slide.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-16 h-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded border flex items-center justify-center">
                          <span className="text-xs font-medium text-gray-600">{index + 1}</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900">{slide.title}</h4>
                          <Badge variant="secondary" className="text-xs">
                            {SLIDE_TEMPLATES.find(t => t.layout === slide.layout)?.name || slide.layout}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">
                          {slide.notes ? slide.notes.substring(0, 100) + '...' : 'No notes available'}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'edit');
                          }}
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'duplicate');
                          }}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlideAction(slide.id, 'delete');
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Side Panel */}
        <div className="lg:col-span-1">
          <div className="space-y-4">
            {/* Selected Slide Info */}
            {selectedSlideId && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    Slide Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(() => {
                    const selectedSlide = presentation.slides.find(s => s.id === selectedSlideId);
                    if (!selectedSlide) return null;

                    return (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Title</label>
                          <p className="text-sm text-gray-900 mt-1">{selectedSlide.title}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Layout</label>
                          <p className="text-sm text-gray-900 mt-1">
                            {SLIDE_TEMPLATES.find(t => t.layout === selectedSlide.layout)?.name || selectedSlide.layout}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Position</label>
                          <p className="text-sm text-gray-900 mt-1">
                            Slide {selectedSlide.order + 1} of {presentation.slides.length}
                          </p>
                        </div>
                        {selectedSlide.notes && (
                          <div>
                            <label className="text-sm font-medium text-gray-700">Speaker Notes</label>
                            <ScrollArea className="h-24 mt-1">
                              <p className="text-sm text-gray-600 pr-4">{selectedSlide.notes}</p>
                            </ScrollArea>
                          </div>
                        )}
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="flex-1">
                            <Edit3 className="w-3 h-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSlideAction(selectedSlide.id, 'duplicate')}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </>
                    );
                  })()}
                </CardContent>
              </Card>
            )}

            {/* Presentation Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Slides</span>
                  <span className="text-sm font-medium">{presentation.slides.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Theme</span>
                  <span className="text-sm font-medium">{selectedTheme.displayName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <Badge variant="secondary" className="text-xs">
                    {presentation.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Created</span>
                  <span className="text-sm font-medium">
                    {new Date(presentation.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Slide
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Palette className="w-4 h-4 mr-2" />
                  Change Theme
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Play className="w-4 h-4 mr-2" />
                  Preview Mode
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start" onClick={onExport}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Editor Panel (Placeholder for selected slide) */}
      {selectedSlideId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Edit3 className="w-5 h-5" />
              Slide Editor
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-12 text-center border-2 border-dashed border-blue-200">
              <Edit3 className="w-16 h-16 mx-auto text-blue-400 mb-6" />
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                Rich Text Editor Coming Soon
              </h4>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                The full slide editor with Plate.js integration, drag-and-drop functionality,
                and real-time collaboration will be implemented in the next development phase.
              </p>
              <div className="flex justify-center gap-3">
                <Button variant="outline">
                  <FileText className="w-4 h-4 mr-2" />
                  View Content
                </Button>
                <Button>
                  <Edit3 className="w-4 h-4 mr-2" />
                  Edit in Text Mode
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Presentation Mode */}
      {isPresentationMode && (
        <PresentationMode
          presentation={presentation}
          onExit={() => setIsPresentationMode(false)}
        />
      )}
    </div>
  );
}
