import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { 
  FileText, 
  Search, 
  Calendar, 
  User, 
  BookOpen, 
  Trash2, 
  Eye,
  Download,
  Loader2,
  Plus
} from "lucide-react";
import { toast } from "sonner";
import { paperGenerationService } from './paper-generation.service';
import { PaperGeneration } from './types';
import { useAuth } from '@/contexts/AuthContext';

interface SavedPapersPanelProps {
  onLoadPaper: (paperGeneration: PaperGeneration) => void;
  onCreateNew: () => void;
  className?: string;
}

export const SavedPapersPanel: React.FC<SavedPapersPanelProps> = ({
  onLoadPaper,
  onCreateNew,
  className = ""
}) => {
  const { user } = useAuth();
  const [papers, setPapers] = useState<PaperGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPapers, setFilteredPapers] = useState<PaperGeneration[]>([]);

  // Load saved papers on component mount
  useEffect(() => {
    if (user) {
      loadSavedPapers();
    }
  }, [user]);

  // Filter papers based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredPapers(papers);
    } else {
      const filtered = papers.filter(paper =>
        paper.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        paper.research_field?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        paper.keywords.some(keyword => 
          keyword.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
      setFilteredPapers(filtered);
    }
  }, [papers, searchQuery]);

  const loadSavedPapers = async () => {
    try {
      setLoading(true);
      const savedPapers = await paperGenerationService.getUserPaperGenerations();
      setPapers(savedPapers);
    } catch (error) {
      console.error('Error loading saved papers:', error);
      toast.error('Failed to load saved papers');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePaper = async (paperId: string, paperTitle: string) => {
    if (!confirm(`Are you sure you want to delete "${paperTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const success = await paperGenerationService.deletePaperGeneration(paperId);
      if (success) {
        setPapers(prev => prev.filter(p => p.id !== paperId));
        toast.success('Paper deleted successfully');
      } else {
        toast.error('Failed to delete paper');
      }
    } catch (error) {
      console.error('Error deleting paper:', error);
      toast.error('Failed to delete paper');
    }
  };

  const handleLoadPaper = async (paper: PaperGeneration) => {
    try {
      toast.info('Loading paper generation...');
      onLoadPaper(paper);
    } catch (error) {
      console.error('Error loading paper:', error);
      toast.error('Failed to load paper');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'generating':
        return 'bg-blue-100 text-blue-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card className={`shadow-lg border-0 bg-white/70 backdrop-blur-sm ${className}`}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <User className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">Sign In Required</h3>
          <p className="text-gray-500 text-center">
            Please sign in to save and access your paper generations.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`shadow-lg border-0 bg-white/70 backdrop-blur-sm ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-blue-500 rounded-full"></div>
            Saved Papers
          </CardTitle>
          <Button 
            onClick={onCreateNew}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Paper
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search papers by title, field, or keywords..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">Loading saved papers...</span>
            </div>
          ) : filteredPapers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileText className="h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                {searchQuery ? 'No papers found' : 'No saved papers yet'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchQuery
                  ? 'Try adjusting your search terms or create a new paper'
                  : 'Start by creating your first AI-generated research paper'
                }
              </p>
              <Button onClick={onCreateNew} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {searchQuery ? 'Create New Paper' : 'Create Your First Paper'}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredPapers.map((paper) => (
                <div
                  key={paper.id}
                  className="border border-gray-200 rounded-xl p-4 bg-white hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg text-gray-900 truncate">
                        {paper.title}
                      </h3>
                      {paper.research_field && (
                        <p className="text-sm text-gray-600 mt-1">
                          {paper.research_field}
                        </p>
                      )}
                    </div>
                    <Badge className={getStatusColor(paper.status)}>
                      {paper.status}
                    </Badge>
                  </div>

                  {/* Keywords */}
                  {paper.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {paper.keywords.slice(0, 3).map((keyword, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                      {paper.keywords.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{paper.keywords.length - 3} more
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Metadata */}
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(paper.updated_at)}
                    </div>
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-3 w-3" />
                      {paper.ai_model}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleLoadPaper(paper)}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-3 w-3" />
                      Load Paper
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeletePaper(paper.id, paper.title)}
                      className="flex items-center gap-2 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
