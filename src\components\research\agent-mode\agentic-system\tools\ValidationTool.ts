/**
 * ValidationTool - Ensures no unintended changes were made outside the target area
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { ToolContext, ToolResult, ValidationResult, ValidationToolResult, EditResult } from '../types';

export class ValidationTool extends BaseAgentTool {
  constructor() {
    super(
      'validation-tool',
      'Change Validator',
      'Ensures no unintended changes were made outside the target area'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { documentContent, previousResults } = context;
    
    console.log(`🔒 [ValidationTool] Validating changes for unintended modifications`);

    try {
      // Extract edit results from previous steps
      const editResults = this.extractEditResults(previousResults);
      
      if (!editResults || editResults.length === 0) {
        return this.createSuccessResult(
          startTime,
          {
            hasUnintendedChanges: false,
            preservesStructure: true,
            maintainsStyle: true,
            issues: [],
            score: 1.0
          } as ValidationResult,
          'No changes to validate',
          1.0
        );
      }

      console.log(`🔍 [ValidationTool] Validating ${editResults.length} edit(s)`);

      // Perform validation checks
      const validationResult = await this.performValidationChecks(
        editResults,
        documentContent
      );

      const confidence = this.calculateValidationConfidence(validationResult);
      const reasoning = this.generateValidationReasoning(validationResult, editResults);

      return this.createSuccessResult(
        startTime,
        validationResult,
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Validation failed: ${error.message}`,
        ['Validation process encountered an error', 'Manual verification recommended']
      );
    }
  }

  /**
   * Extract edit results from previous tool executions
   */
  private extractEditResults(previousResults?: ToolResult[]): EditResult[] {
    if (!previousResults) return [];
    
    const editResult = previousResults.find(r => r.toolId === 'edit-tool' && r.success);
    return editResult?.data?.edits || [];
  }

  /**
   * Perform comprehensive validation checks
   */
  private async performValidationChecks(
    edits: EditResult[],
    documentContent: string
  ): Promise<ValidationResult> {
    const issues: string[] = [];
    let score = 1.0;

    // Check for unintended changes
    const unintendedChanges = this.checkForUnintendedChanges(edits, documentContent);
    if (unintendedChanges.found) {
      issues.push(...unintendedChanges.issues);
      score -= 0.3;
    }

    // Check structure preservation
    const structurePreservation = this.checkStructurePreservation(edits, documentContent);
    if (!structurePreservation.preserved) {
      issues.push(...structurePreservation.issues);
      score -= 0.2;
    }

    // Check style consistency
    const styleConsistency = this.checkStyleConsistency(edits);
    if (!styleConsistency.consistent) {
      issues.push(...styleConsistency.issues);
      score -= 0.2;
    }

    // Check for overlapping edits
    const overlapCheck = this.checkForOverlappingEdits(edits);
    if (overlapCheck.hasOverlaps) {
      issues.push(...overlapCheck.issues);
      score -= 0.3;
    }

    return {
      hasUnintendedChanges: unintendedChanges.found,
      preservesStructure: structurePreservation.preserved,
      maintainsStyle: styleConsistency.consistent,
      issues,
      score: Math.max(0, score)
    };
  }

  /**
   * Check for unintended changes outside target areas
   */
  private checkForUnintendedChanges(
    edits: EditResult[],
    documentContent: string
  ): { found: boolean; issues: string[] } {
    const issues: string[] = [];
    let found = false;

    // Create a map of all intended change areas
    const changeAreas = edits.map(edit => ({
      start: edit.startPosition,
      end: edit.endPosition,
      originalText: edit.originalText
    }));

    // Check for gaps or overlaps that might indicate unintended changes
    const sortedAreas = changeAreas.sort((a, b) => a.start - b.start);
    
    for (let i = 0; i < sortedAreas.length - 1; i++) {
      const current = sortedAreas[i];
      const next = sortedAreas[i + 1];
      
      // Check for overlapping areas
      if (current.end > next.start) {
        issues.push(`Overlapping edit areas detected between positions ${current.start}-${current.end} and ${next.start}-${next.end}`);
        found = true;
      }
    }

    // Check for edits that are too large compared to original text
    edits.forEach((edit, index) => {
      const lengthRatio = edit.newText.length / edit.originalText.length;
      
      if (lengthRatio > 5) {
        issues.push(`Edit ${index + 1} has excessive length increase (${(lengthRatio * 100).toFixed(0)}% of original)`);
        found = true;
      }
      
      if (lengthRatio < 0.1) {
        issues.push(`Edit ${index + 1} has excessive length decrease (${(lengthRatio * 100).toFixed(0)}% of original)`);
        found = true;
      }
    });

    return { found, issues };
  }

  /**
   * Check if document structure is preserved
   */
  private checkStructurePreservation(
    edits: EditResult[],
    documentContent: string
  ): { preserved: boolean; issues: string[] } {
    const issues: string[] = [];
    let preserved = true;

    // Check for structural elements that should be preserved
    const structuralElements = [
      { pattern: /<h[1-6][^>]*>/gi, name: 'headings' },
      { pattern: /<p[^>]*>/gi, name: 'paragraphs' },
      { pattern: /<ul[^>]*>/gi, name: 'unordered lists' },
      { pattern: /<ol[^>]*>/gi, name: 'ordered lists' },
      { pattern: /<table[^>]*>/gi, name: 'tables' },
      { pattern: /<blockquote[^>]*>/gi, name: 'blockquotes' }
    ];

    // Count structural elements before and after edits
    const originalCounts = this.countStructuralElements(documentContent, structuralElements);
    
    // Simulate document after edits
    let modifiedContent = documentContent;
    edits.forEach(edit => {
      const beforeEdit = modifiedContent.substring(0, edit.startPosition);
      const afterEdit = modifiedContent.substring(edit.endPosition);
      modifiedContent = beforeEdit + edit.newText + afterEdit;
    });

    const modifiedCounts = this.countStructuralElements(modifiedContent, structuralElements);

    // Compare counts
    structuralElements.forEach((element, index) => {
      const originalCount = originalCounts[index];
      const modifiedCount = modifiedCounts[index];
      
      if (Math.abs(originalCount - modifiedCount) > 0) {
        issues.push(`Structural change detected: ${element.name} count changed from ${originalCount} to ${modifiedCount}`);
        preserved = false;
      }
    });

    return { preserved, issues };
  }

  /**
   * Count structural elements in content
   */
  private countStructuralElements(
    content: string,
    elements: Array<{ pattern: RegExp; name: string }>
  ): number[] {
    return elements.map(element => {
      const matches = content.match(element.pattern);
      return matches ? matches.length : 0;
    });
  }

  /**
   * Check style consistency across edits
   */
  private checkStyleConsistency(edits: EditResult[]): { consistent: boolean; issues: string[] } {
    const issues: string[] = [];
    let consistent = true;

    if (edits.length < 2) {
      return { consistent: true, issues: [] };
    }

    // Analyze style patterns across edits
    const styleMetrics = edits.map(edit => this.analyzeTextStyle(edit.newText));
    
    // Check for consistency in various style aspects
    const avgSentenceLength = styleMetrics.reduce((sum, m) => sum + m.avgSentenceLength, 0) / styleMetrics.length;
    const avgWordLength = styleMetrics.reduce((sum, m) => sum + m.avgWordLength, 0) / styleMetrics.length;
    
    styleMetrics.forEach((metric, index) => {
      // Check sentence length consistency
      if (Math.abs(metric.avgSentenceLength - avgSentenceLength) > avgSentenceLength * 0.5) {
        issues.push(`Edit ${index + 1} has inconsistent sentence length (${metric.avgSentenceLength.toFixed(1)} vs avg ${avgSentenceLength.toFixed(1)})`);
        consistent = false;
      }
      
      // Check word length consistency
      if (Math.abs(metric.avgWordLength - avgWordLength) > avgWordLength * 0.3) {
        issues.push(`Edit ${index + 1} has inconsistent vocabulary complexity`);
        consistent = false;
      }
    });

    return { consistent, issues };
  }

  /**
   * Analyze text style metrics
   */
  private analyzeTextStyle(text: string): {
    avgSentenceLength: number;
    avgWordLength: number;
    formalityScore: number;
  } {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    
    const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
    const avgWordLength = words.length > 0 ? 
      words.reduce((sum, word) => sum + word.length, 0) / words.length : 0;
    
    // Simple formality score based on word length and complexity
    const formalWords = words.filter(word => word.length > 6).length;
    const formalityScore = words.length > 0 ? formalWords / words.length : 0;

    return {
      avgSentenceLength,
      avgWordLength,
      formalityScore
    };
  }

  /**
   * Check for overlapping edits
   */
  private checkForOverlappingEdits(edits: EditResult[]): { hasOverlaps: boolean; issues: string[] } {
    const issues: string[] = [];
    let hasOverlaps = false;

    // Sort edits by start position
    const sortedEdits = [...edits].sort((a, b) => a.startPosition - b.startPosition);

    // Check for overlaps
    for (let i = 0; i < sortedEdits.length - 1; i++) {
      const current = sortedEdits[i];
      const next = sortedEdits[i + 1];

      if (current.endPosition > next.startPosition) {
        issues.push(`Overlapping edits detected: positions ${current.startPosition}-${current.endPosition} and ${next.startPosition}-${next.endPosition}`);
        hasOverlaps = true;
      }
    }

    // Check for duplicate edits
    const duplicates = this.findDuplicateEdits(edits);
    if (duplicates.length > 0) {
      issues.push(`Duplicate edits detected at positions: ${duplicates.join(', ')}`);
      hasOverlaps = true;
    }

    return { hasOverlaps, issues };
  }

  /**
   * Find duplicate edits
   */
  private findDuplicateEdits(edits: EditResult[]): string[] {
    const seen = new Set<string>();
    const duplicates: string[] = [];

    edits.forEach(edit => {
      const key = `${edit.startPosition}-${edit.endPosition}`;
      if (seen.has(key)) {
        duplicates.push(key);
      } else {
        seen.add(key);
      }
    });

    return duplicates;
  }

  /**
   * Calculate validation confidence
   */
  private calculateValidationConfidence(validation: ValidationResult): number {
    let confidence = validation.score;

    // Boost confidence for clean validation
    if (validation.issues.length === 0) {
      confidence = Math.min(1.0, confidence + 0.1);
    }

    // Penalize for serious issues
    if (validation.hasUnintendedChanges) {
      confidence *= 0.7;
    }

    if (!validation.preservesStructure) {
      confidence *= 0.8;
    }

    if (!validation.maintainsStyle) {
      confidence *= 0.9;
    }

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Generate validation reasoning
   */
  private generateValidationReasoning(validation: ValidationResult, edits: EditResult[]): string {
    const issueCount = validation.issues.length;
    const scorePercent = (validation.score * 100).toFixed(0);

    let reasoning = `Validated ${edits.length} edit(s) with ${scorePercent}% validation score.`;

    if (issueCount === 0) {
      reasoning += ' No validation issues detected.';
    } else {
      reasoning += ` Found ${issueCount} validation issue(s).`;
    }

    if (validation.preservesStructure && validation.maintainsStyle && !validation.hasUnintendedChanges) {
      reasoning += ' All changes appear safe to apply.';
    } else {
      reasoning += ' Some changes may require manual review.';
    }

    return reasoning;
  }
}
