/**
 * Mermaid Loader Utility
 * Handles dynamic loading of Mermaid with fallback strategies
 */

import { MermaidConfig } from '../types';

let mermaidInstance: any = null;
let loadingPromise: Promise<any> | null = null;

/**
 * Load Mermaid with multiple fallback strategies
 */
export async function loadMermaid(): Promise<any> {
  // Return existing instance if already loaded
  if (mermaidInstance) {
    return mermaidInstance;
  }

  // Return existing loading promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }

  loadingPromise = loadMermaidInternal();
  return loadingPromise;
}

async function loadMermaidInternal(): Promise<any> {
  console.log('🔄 MermaidLoader: Starting Mermaid load process...');

  // Check if already available on window (CDN loaded)
  if ((window as any).mermaid) {
    console.log('✅ MermaidLoader: Found existing Mermaid on window');
    mermaidInstance = (window as any).mermaid;
    return mermaidInstance;
  }

  // Strategy 1: Load from CDN (most reliable for this version)
  try {
    console.log('🔄 MermaidLoader: Loading from CDN...');
    await loadMermaidFromCDN();
    mermaidInstance = (window as any).mermaid;

    if (mermaidInstance && typeof mermaidInstance.initialize === 'function') {
      console.log('✅ MermaidLoader: CDN load successful');
      return mermaidInstance;
    }
  } catch (error) {
    console.warn('⚠️ MermaidLoader: CDN load failed:', error);
  }

  // Strategy 2: Try standard ES module import as fallback
  try {
    console.log('🔄 MermaidLoader: Trying ES module import as fallback...');
    const mermaidModule = await import('mermaid');
    mermaidInstance = mermaidModule.default || mermaidModule;

    if (mermaidInstance && typeof mermaidInstance.initialize === 'function') {
      console.log('✅ MermaidLoader: ES module import successful');
      return mermaidInstance;
    }
  } catch (error) {
    console.warn('⚠️ MermaidLoader: ES module import failed:', error);
  }

  throw new Error('Failed to load Mermaid library with all strategies');
}

/**
 * Load Mermaid from CDN
 */
function loadMermaidFromCDN(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if ((window as any).mermaid) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    // Use a stable version that doesn't have dayjs issues
    script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js';
    script.async = true;
    script.onload = () => {
      console.log('✅ MermaidLoader: CDN script loaded');
      // Give a small delay for the script to initialize
      setTimeout(() => {
        if ((window as any).mermaid) {
          resolve();
        } else {
          reject(new Error('Mermaid not available after CDN load'));
        }
      }, 100);
    };
    script.onerror = () => {
      console.error('❌ MermaidLoader: CDN script failed to load');
      reject(new Error('Failed to load Mermaid from CDN'));
    };

    document.head.appendChild(script);
  });
}

/**
 * Initialize Mermaid with configuration
 */
export function initializeMermaid(config: MermaidConfig): void {
  if (!mermaidInstance) {
    throw new Error('Mermaid not loaded. Call loadMermaid() first.');
  }

  console.log('🔧 MermaidLoader: Initializing Mermaid with config:', config);
  
  try {
    mermaidInstance.initialize({
      startOnLoad: false,
      theme: config.theme || 'default',
      themeVariables: config.themeVariables || {},
      flowchart: config.flowchart || {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      },
      sequence: config.sequence || {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65
      },
      gantt: config.gantt || {
        numberSectionStyles: 4,
        axisFormat: '%m/%d/%Y',
        tickInterval: '1day'
      },
      securityLevel: 'loose',
      fontFamily: 'Inter, system-ui, sans-serif',
      logLevel: 'error'
    });
    
    console.log('✅ MermaidLoader: Mermaid initialized successfully');
  } catch (error) {
    console.error('❌ MermaidLoader: Failed to initialize Mermaid:', error);
    throw error;
  }
}

/**
 * Get the loaded Mermaid instance
 */
export function getMermaidInstance(): any {
  return mermaidInstance;
}

/**
 * Reset the loader state (for testing)
 */
export function resetMermaidLoader(): void {
  mermaidInstance = null;
  loadingPromise = null;
}
