import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  Presentation,
  Slide,
  PresentationMetadata,
  PresentationTheme,
  GenerationState,
  PresentationGenerationOptions,
  OutlineItem,
  ExportOptions
} from '../types';
import { PRESENTATION_THEMES, DEFAULT_GENERATION_OPTIONS } from '../constants';
import { presentationAIService } from '../services/presentation-ai.service';

interface PresentationStore {
  // Current state
  currentPresentation: Presentation | null;
  presentations: Presentation[];
  selectedTheme: PresentationTheme;
  generationState: GenerationState;
  generationOptions: PresentationGenerationOptions;

  // Generation settings
  selectedModel: string;
  selectedImageModel: string;
  selectedStyle: string;

  // UI state
  currentStep: 'input' | 'outline' | 'theme' | 'generation' | 'editing' | 'export';
  selectedSlideId: string | null;
  isExporting: boolean;

  // Outline management
  outline: OutlineItem[];
  isOutlineApproved: boolean;
  
  // Actions - Presentation Management
  createPresentation: (metadata: PresentationMetadata) => void;
  updatePresentation: (updates: Partial<Presentation>) => void;
  deletePresentation: (id: string) => void;
  setCurrentPresentation: (presentation: Presentation | null) => void;
  
  // Actions - Generation
  startOutlineGeneration: (topic: string, slideCount: number) => void;
  setOutline: (outline: OutlineItem[]) => void;
  approveOutline: () => void;
  startSlideGeneration: () => void;
  updateGenerationProgress: (progress: number, currentSlide?: number) => void;
  completeGeneration: (slides: Slide[]) => void;
  setGenerationError: (error: string) => void;
  resetGeneration: () => void;
  
  // Actions - Slide Management
  addSlide: (slide: Slide, index?: number) => void;
  updateSlide: (slideId: string, updates: Partial<Slide>) => void;
  deleteSlide: (slideId: string) => void;
  reorderSlides: (fromIndex: number, toIndex: number) => void;
  duplicateSlide: (slideId: string) => void;
  setSelectedSlide: (slideId: string | null) => void;
  
  // Actions - Theme Management
  setTheme: (theme: PresentationTheme) => void;
  updateTheme: (updates: Partial<PresentationTheme>) => void;

  // Actions - Generation Settings
  setSelectedModel: (model: string) => void;
  setSelectedImageModel: (model: string) => void;
  setSelectedStyle: (style: string) => void;

  // Actions - Options and Settings
  setGenerationOptions: (options: Partial<PresentationGenerationOptions>) => void;
  setCurrentStep: (step: PresentationStore['currentStep']) => void;
  
  // Actions - Export
  exportPresentation: (options: ExportOptions) => Promise<void>;
  
  // Utility actions
  resetStore: () => void;
}

const initialGenerationState: GenerationState = {
  isGeneratingOutline: false,
  isGeneratingSlides: false,
  currentSlideIndex: 0,
  totalSlides: 0,
  progress: 0,
  stage: 'idle'
};

export const usePresentationStore = create<PresentationStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentPresentation: null,
      presentations: [],
      selectedTheme: PRESENTATION_THEMES[0], // Default to 'modern' theme
      generationState: initialGenerationState,
      generationOptions: DEFAULT_GENERATION_OPTIONS,
      selectedModel: 'gemini-2.5-flash',
      selectedImageModel: 'black-forest-labs/FLUX.1-schnell',
      selectedStyle: 'professional',
      currentStep: 'input',
      selectedSlideId: null,
      isExporting: false,
      outline: [],
      isOutlineApproved: false,

      // Presentation Management
      createPresentation: (metadata) => {
        const newPresentation: Presentation = {
          id: crypto.randomUUID(),
          metadata,
          slides: [],
          outline: [],
          theme: get().selectedTheme,
          status: 'draft',
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        set((state) => ({
          currentPresentation: newPresentation,
          presentations: [...state.presentations, newPresentation],
          currentStep: 'outline'
        }));
      },

      updatePresentation: (updates) => {
        const current = get().currentPresentation;
        if (!current) return;

        const updatedPresentation = {
          ...current,
          ...updates,
          updatedAt: new Date()
        };

        set((state) => ({
          currentPresentation: updatedPresentation,
          presentations: state.presentations.map(p => 
            p.id === current.id ? updatedPresentation : p
          )
        }));
      },

      deletePresentation: (id) => {
        set((state) => ({
          presentations: state.presentations.filter(p => p.id !== id),
          currentPresentation: state.currentPresentation?.id === id ? null : state.currentPresentation
        }));
      },

      setCurrentPresentation: (presentation) => {
        set({ currentPresentation: presentation });
      },

      // Generation Management
      startOutlineGeneration: async (topic, slideCount) => {
        const current = get().currentPresentation;
        if (!current) return;

        set({
          generationState: {
            ...initialGenerationState,
            isGeneratingOutline: true,
            totalSlides: slideCount,
            stage: 'outline'
          }
        });

        get().updatePresentation({ status: 'generating-outline' });

        try {
          const { selectedModel } = get();
          const outline = await presentationAIService.generateOutline(current.metadata, selectedModel);
          get().setOutline(outline);
        } catch (error) {
          console.error('Failed to generate outline:', error);
          get().setGenerationError('Failed to generate presentation outline. Please try again.');
        }
      },

      setOutline: (outline) => {
        set({ 
          outline,
          generationState: {
            ...get().generationState,
            isGeneratingOutline: false,
            stage: 'complete'
          }
        });
        
        get().updatePresentation({ 
          outline: outline.map(item => item.title),
          status: 'draft'
        });
      },

      approveOutline: () => {
        set({
          isOutlineApproved: true
        });
      },

      startSlideGeneration: async () => {
        const outline = get().outline;
        const current = get().currentPresentation;
        if (!current || outline.length === 0) return;

        set({
          currentStep: 'generation',
          generationState: {
            ...get().generationState,
            isGeneratingSlides: true,
            totalSlides: outline.length,
            currentSlideIndex: 0,
            progress: 0,
            stage: 'slides'
          }
        });

        get().updatePresentation({ status: 'generating-slides' });

        try {
          const { selectedModel } = get();
          const slides = await presentationAIService.generateSlides(
            outline,
            current.metadata,
            selectedModel,
            (progress, currentSlide) => {
              get().updateGenerationProgress(progress, currentSlide);
            }
          );

          get().completeGeneration(slides);
        } catch (error) {
          console.error('Failed to generate slides:', error);
          get().setGenerationError('Failed to generate presentation slides. Please try again.');
        }
      },

      updateGenerationProgress: (progress, currentSlide) => {
        set((state) => ({
          generationState: {
            ...state.generationState,
            progress,
            currentSlideIndex: currentSlide ?? state.generationState.currentSlideIndex
          }
        }));
      },

      completeGeneration: (slides) => {
        set({
          generationState: {
            ...get().generationState,
            isGeneratingSlides: false,
            progress: 100,
            stage: 'complete'
          },
          currentStep: 'editing'
        });
        
        get().updatePresentation({ 
          slides,
          status: 'completed'
        });
      },

      setGenerationError: (error) => {
        set({
          generationState: {
            ...get().generationState,
            isGeneratingOutline: false,
            isGeneratingSlides: false,
            error,
            stage: 'error'
          }
        });
        
        get().updatePresentation({ status: 'error' });
      },

      resetGeneration: () => {
        set({
          generationState: initialGenerationState,
          outline: [],
          isOutlineApproved: false,
          currentStep: 'input'
        });
      },

      // Slide Management
      addSlide: (slide, index) => {
        const current = get().currentPresentation;
        if (!current) return;

        const slides = [...current.slides];
        const insertIndex = index ?? slides.length;
        slides.splice(insertIndex, 0, slide);

        // Update order for all slides
        const reorderedSlides = slides.map((s, i) => ({ ...s, order: i }));
        
        get().updatePresentation({ slides: reorderedSlides });
      },

      updateSlide: (slideId, updates) => {
        const current = get().currentPresentation;
        if (!current) return;

        const updatedSlides = current.slides.map(slide =>
          slide.id === slideId ? { ...slide, ...updates } : slide
        );

        get().updatePresentation({ slides: updatedSlides });
      },

      deleteSlide: (slideId) => {
        const current = get().currentPresentation;
        if (!current) return;

        const filteredSlides = current.slides
          .filter(slide => slide.id !== slideId)
          .map((slide, index) => ({ ...slide, order: index }));

        get().updatePresentation({ slides: filteredSlides });
        
        // Clear selection if deleted slide was selected
        if (get().selectedSlideId === slideId) {
          set({ selectedSlideId: null });
        }
      },

      reorderSlides: (fromIndex, toIndex) => {
        const current = get().currentPresentation;
        if (!current) return;

        const slides = [...current.slides];
        const [movedSlide] = slides.splice(fromIndex, 1);
        slides.splice(toIndex, 0, movedSlide);

        // Update order for all slides
        const reorderedSlides = slides.map((slide, index) => ({ ...slide, order: index }));
        
        get().updatePresentation({ slides: reorderedSlides });
      },

      duplicateSlide: (slideId) => {
        const current = get().currentPresentation;
        if (!current) return;

        const slideIndex = current.slides.findIndex(s => s.id === slideId);
        if (slideIndex === -1) return;

        const originalSlide = current.slides[slideIndex];
        const duplicatedSlide: Slide = {
          ...originalSlide,
          id: crypto.randomUUID(),
          title: `${originalSlide.title} (Copy)`,
          order: slideIndex + 1
        };

        get().addSlide(duplicatedSlide, slideIndex + 1);
      },

      setSelectedSlide: (slideId) => {
        set({ selectedSlideId: slideId });
      },

      // Theme Management
      setTheme: (theme) => {
        set({ selectedTheme: theme });
        get().updatePresentation({ theme });
      },

      updateTheme: (updates) => {
        const updatedTheme = { ...get().selectedTheme, ...updates };
        set({ selectedTheme: updatedTheme });
        get().updatePresentation({ theme: updatedTheme });
      },

      // Generation Settings
      setSelectedModel: (model) => {
        set({ selectedModel: model });
      },

      setSelectedImageModel: (model) => {
        set({ selectedImageModel: model });
      },

      setSelectedStyle: (style) => {
        set({ selectedStyle: style });
      },

      // Options and Settings
      setGenerationOptions: (options) => {
        set((state) => ({
          generationOptions: { ...state.generationOptions, ...options }
        }));
      },

      setCurrentStep: (step) => {
        set({ currentStep: step });
      },

      // Export
      exportPresentation: async (options) => {
        set({ isExporting: true });
        try {
          // Export logic will be implemented in the service layer
          console.log('Exporting presentation with options:', options);
          // TODO: Implement actual export functionality
        } catch (error) {
          console.error('Export failed:', error);
          throw error;
        } finally {
          set({ isExporting: false });
        }
      },

      // Utility
      resetStore: () => {
        set({
          currentPresentation: null,
          selectedTheme: PRESENTATION_THEMES[0],
          generationState: initialGenerationState,
          generationOptions: DEFAULT_GENERATION_OPTIONS,
          selectedModel: 'gemini-2.5-flash',
          selectedImageModel: 'black-forest-labs/FLUX.1-schnell',
          selectedStyle: 'professional',
          currentStep: 'input',
          selectedSlideId: null,
          isExporting: false,
          outline: [],
          isOutlineApproved: false
        });
      }
    }),
    {
      name: 'presentation-store'
    }
  )
);
