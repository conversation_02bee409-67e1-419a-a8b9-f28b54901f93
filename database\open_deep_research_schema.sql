-- Open Deep Research Database Schema
-- This schema supports the Open Deep Research component with history and file storage

-- Table for storing research sessions
CREATE TABLE IF NOT EXISTS public.deep_research_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    query TEXT NOT NULL,
    report_prompt TEXT NOT NULL,
    selected_model TEXT NOT NULL,
    is_agent_mode BOOLEAN DEFAULT false,
    status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, completed, error
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Table for storing generated reports
CREATE TABLE IF NOT EXISTS public.deep_research_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.deep_research_sessions(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    summary TEXT NOT NULL,
    sections JSONB NOT NULL DEFAULT '[]', -- Array of section objects
    sources JSONB NOT NULL DEFAULT '[]', -- Array of source objects
    used_sources INTEGER[] DEFAULT '{}', -- Array of source indices
    word_count INTEGER DEFAULT 0,
    processing_time_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing search results/sources
CREATE TABLE IF NOT EXISTS public.deep_research_sources (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.deep_research_sessions(id) ON DELETE CASCADE NOT NULL,
    url TEXT NOT NULL,
    title TEXT NOT NULL,
    snippet TEXT NOT NULL,
    content TEXT, -- Full extracted content
    is_selected BOOLEAN DEFAULT false,
    score DECIMAL(5,4), -- Relevance score 0-1
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing uploaded files
CREATE TABLE IF NOT EXISTS public.deep_research_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.deep_research_sessions(id) ON DELETE CASCADE NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT NOT NULL, -- Path in Supabase storage
    file_type TEXT NOT NULL, -- pdf, docx, txt
    file_size INTEGER NOT NULL,
    content TEXT, -- Extracted text content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing report exports
CREATE TABLE IF NOT EXISTS public.deep_research_exports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.deep_research_sessions(id) ON DELETE CASCADE NOT NULL,
    export_format TEXT NOT NULL, -- pdf, docx, txt
    file_name TEXT NOT NULL,
    file_path TEXT, -- Path in Supabase storage
    file_size INTEGER,
    export_options JSONB DEFAULT '{}', -- Export configuration
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_user_id ON public.deep_research_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_created_at ON public.deep_research_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_status ON public.deep_research_sessions(status);
CREATE INDEX IF NOT EXISTS idx_deep_research_reports_session_id ON public.deep_research_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_sources_session_id ON public.deep_research_sources(session_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_files_session_id ON public.deep_research_files(session_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_exports_session_id ON public.deep_research_exports(session_id);

-- Row Level Security (RLS) policies
ALTER TABLE public.deep_research_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deep_research_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deep_research_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deep_research_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deep_research_exports ENABLE ROW LEVEL SECURITY;

-- Policies for deep_research_sessions
CREATE POLICY "Users can view their own research sessions" ON public.deep_research_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own research sessions" ON public.deep_research_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research sessions" ON public.deep_research_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research sessions" ON public.deep_research_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for deep_research_reports
CREATE POLICY "Users can view reports from their sessions" ON public.deep_research_reports
    FOR SELECT USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert reports for their sessions" ON public.deep_research_reports
    FOR INSERT WITH CHECK (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

-- Policies for deep_research_sources
CREATE POLICY "Users can view sources from their sessions" ON public.deep_research_sources
    FOR SELECT USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage sources for their sessions" ON public.deep_research_sources
    FOR ALL USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

-- Policies for deep_research_files
CREATE POLICY "Users can view files from their sessions" ON public.deep_research_files
    FOR SELECT USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage files for their sessions" ON public.deep_research_files
    FOR ALL USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

-- Policies for deep_research_exports
CREATE POLICY "Users can view exports from their sessions" ON public.deep_research_exports
    FOR SELECT USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage exports for their sessions" ON public.deep_research_exports
    FOR ALL USING (
        session_id IN (
            SELECT id FROM public.deep_research_sessions WHERE user_id = auth.uid()
        )
    );

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_deep_research_sessions_updated_at 
    BEFORE UPDATE ON public.deep_research_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE public.deep_research_sessions IS 'Stores research session metadata and configuration';
COMMENT ON TABLE public.deep_research_reports IS 'Stores generated research reports with sections and sources';
COMMENT ON TABLE public.deep_research_sources IS 'Stores search results and sources for each session';
COMMENT ON TABLE public.deep_research_files IS 'Stores uploaded files and their extracted content';
COMMENT ON TABLE public.deep_research_exports IS 'Stores export history and file references';
