import { nanoid } from 'nanoid';
import { diffWords, diffSentences } from 'diff';
import { ChangeRecord, ChangeTrackingState, ChangeNavigationState, ChangeSection } from './types';

class ChangeTrackingService {
  private state: ChangeTrackingState = {
    originalContent: '',
    currentContent: '',
    changes: [],
    isTrackingEnabled: false,
    currentChangeIndex: -1,
    viewMode: 'normal',
    selectedSections: new Set<string>(),
    selectionMode: 'none'
  };

  private listeners: Set<(state: ChangeTrackingState) => void> = new Set();

  // Initialize tracking for a new editing session
  startTracking(initialContent: string): void {
    this.state = {
      originalContent: initialContent,
      currentContent: initialContent,
      changes: [],
      isTrackingEnabled: true,
      currentChangeIndex: -1,
      viewMode: 'normal',
      selectedSections: new Set<string>(),
      selectionMode: 'none'
    };
    this.notifyListeners();
  }

  // Stop tracking and clear all changes
  stopTracking(): void {
    this.state.isTrackingEnabled = false;
    this.state.changes = [];
    this.state.currentChangeIndex = -1;
    this.state.selectedSections.clear();
    this.state.selectionMode = 'none';
    this.notifyListeners();
  }

  // Record a new change from AI modification
  recordChange(
    originalText: string,
    newText: string,
    startPosition: number,
    endPosition: number,
    aiActionType: 'replace' | 'insert' | 'display',
    prompt?: string
  ): string {
    if (!this.state.isTrackingEnabled) return nanoid();

    const changeId = nanoid();

    // Break down the change into sections (sentences or paragraphs)
    const sections = this.createChangeSections(changeId, originalText, newText, startPosition);

    const change: ChangeRecord = {
      id: changeId,
      timestamp: new Date(),
      originalText,
      newText,
      startPosition,
      endPosition,
      aiActionType,
      status: 'pending',
      prompt,
      isSelected: false,
      sections
    };

    this.state.changes.push(change);
    this.notifyListeners();
    return changeId;
  }

  // Create sections from a change for granular control
  private createChangeSections(changeId: string, originalText: string, newText: string, startPosition: number): ChangeSection[] {
    const sections: ChangeSection[] = [];

    // Split by sentences or paragraphs for better granularity
    const originalSentences = originalText.split(/[.!?]+/).filter(s => s.trim());
    const newSentences = newText.split(/[.!?]+/).filter(s => s.trim());

    // Create sections based on sentence-level differences
    const maxSentences = Math.max(originalSentences.length, newSentences.length);

    for (let i = 0; i < maxSentences; i++) {
      const originalSentence = originalSentences[i] || '';
      const newSentence = newSentences[i] || '';

      if (originalSentence !== newSentence) {
        sections.push({
          id: nanoid(),
          changeId,
          originalText: originalSentence,
          newText: newSentence,
          startPosition: startPosition + originalText.indexOf(originalSentence),
          endPosition: startPosition + originalText.indexOf(originalSentence) + originalSentence.length,
          status: 'pending',
          isSelected: false
        });
      }
    }

    // If no sentence-level differences, create one section for the entire change
    if (sections.length === 0) {
      sections.push({
        id: nanoid(),
        changeId,
        originalText,
        newText,
        startPosition,
        endPosition: startPosition + originalText.length,
        status: 'pending',
        isSelected: false
      });
    }

    return sections;
  }

  // Accept a specific change
  acceptChange(changeId: string): void {
    const change = this.state.changes.find(c => c.id === changeId);
    if (change) {
      change.status = 'accepted';
      this.notifyListeners();
    }
  }

  // Reject a specific change and revert it
  rejectChange(changeId: string): string {
    const change = this.state.changes.find(c => c.id === changeId);
    if (change) {
      change.status = 'rejected';
      // Return the original text to replace the new text
      this.notifyListeners();
      return change.originalText;
    }
    return '';
  }

  // Accept all pending changes
  acceptAllChanges(): void {
    this.state.changes.forEach(change => {
      if (change.status === 'pending') {
        change.status = 'accepted';
      }
    });
    this.notifyListeners();
  }

  // Reject all pending changes and return content with all changes reverted
  rejectAllChanges(): string {
    this.state.changes.forEach(change => {
      if (change.status === 'pending') {
        change.status = 'rejected';
      }
    });
    
    // Return original content
    this.state.currentContent = this.state.originalContent;
    this.notifyListeners();
    return this.state.originalContent;
  }

  // Navigate to next change
  navigateToNextChange(): ChangeNavigationState {
    const pendingChanges = this.state.changes.filter(c => c.status === 'pending');
    if (pendingChanges.length === 0) {
      return this.getNavigationState();
    }

    if (this.state.currentChangeIndex < pendingChanges.length - 1) {
      this.state.currentChangeIndex++;
    }
    
    this.notifyListeners();
    return this.getNavigationState();
  }

  // Navigate to previous change
  navigateToPreviousChange(): ChangeNavigationState {
    if (this.state.currentChangeIndex > 0) {
      this.state.currentChangeIndex--;
    }
    
    this.notifyListeners();
    return this.getNavigationState();
  }

  // Get current navigation state
  getNavigationState(): ChangeNavigationState {
    const pendingChanges = this.state.changes.filter(c => c.status === 'pending');
    return {
      currentIndex: this.state.currentChangeIndex,
      totalChanges: pendingChanges.length,
      hasNext: this.state.currentChangeIndex < pendingChanges.length - 1,
      hasPrevious: this.state.currentChangeIndex > 0
    };
  }

  // Get current change being reviewed
  getCurrentChange(): ChangeRecord | null {
    const pendingChanges = this.state.changes.filter(c => c.status === 'pending');
    if (this.state.currentChangeIndex >= 0 && this.state.currentChangeIndex < pendingChanges.length) {
      return pendingChanges[this.state.currentChangeIndex];
    }
    return null;
  }

  // Toggle view mode between normal and diff
  toggleViewMode(): void {
    this.state.viewMode = this.state.viewMode === 'normal' ? 'diff' : 'normal';
    this.notifyListeners();
  }

  // Get current state
  getState(): ChangeTrackingState {
    return { ...this.state };
  }

  // Check if there are pending changes
  hasPendingChanges(): boolean {
    return this.state.changes.some(c => c.status === 'pending');
  }

  // Get pending changes count
  getPendingChangesCount(): number {
    return this.state.changes.filter(c => c.status === 'pending').length;
  }

  // Section selection methods
  toggleSectionSelection(sectionId: string): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    if (this.state.selectedSections.has(sectionId)) {
      this.state.selectedSections.delete(sectionId);
    } else {
      this.state.selectedSections.add(sectionId);
    }

    // Update selection mode based on current selections
    this.updateSelectionMode();
    this.notifyListeners();
  }

  selectAllSections(): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    this.state.selectedSections.clear();
    this.state.changes.forEach(change => {
      if (change.sections) {
        change.sections.forEach(section => {
          if (section.status === 'pending') {
            this.state.selectedSections.add(section.id);
          }
        });
      }
    });
    this.state.selectionMode = 'all';
    this.notifyListeners();
  }

  clearSectionSelection(): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    this.state.selectedSections.clear();
    this.state.selectionMode = 'none';
    this.notifyListeners();
  }

  private updateSelectionMode(): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    const totalPendingSections = this.getTotalPendingSections();
    const selectedCount = this.state.selectedSections.size;

    if (selectedCount === 0) {
      this.state.selectionMode = 'none';
    } else if (selectedCount === totalPendingSections) {
      this.state.selectionMode = 'all';
    } else {
      this.state.selectionMode = 'individual';
    }
  }

  private getTotalPendingSections(): number {
    return this.state.changes.reduce((total, change) => {
      return total + (change.sections?.filter(s => s.status === 'pending').length || 0);
    }, 0);
  }

  // Accept/reject selected sections
  acceptSelectedSections(): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    this.state.changes.forEach(change => {
      if (change.sections) {
        change.sections.forEach(section => {
          if (this.state.selectedSections.has(section.id)) {
            section.status = 'accepted';
          }
        });

        // Update change status if all sections are accepted
        if (change.sections.every(s => s.status === 'accepted')) {
          change.status = 'accepted';
        }
      }
    });

    this.state.selectedSections.clear();
    this.state.selectionMode = 'none';
    this.notifyListeners();
  }

  rejectSelectedSections(): void {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    this.state.changes.forEach(change => {
      if (change.sections) {
        change.sections.forEach(section => {
          if (this.state.selectedSections.has(section.id)) {
            section.status = 'rejected';
          }
        });

        // Update change status if all sections are rejected
        if (change.sections.every(s => s.status === 'rejected')) {
          change.status = 'rejected';
        }
      }
    });

    this.state.selectedSections.clear();
    this.state.selectionMode = 'none';
    this.notifyListeners();
  }

  // Get selected sections
  getSelectedSections(): ChangeSection[] {
    const selectedSections: ChangeSection[] = [];

    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }

    this.state.changes.forEach(change => {
      if (change.sections) {
        change.sections.forEach(section => {
          if (this.state.selectedSections.has(section.id)) {
            selectedSections.push(section);
          }
        });
      }
    });
    return selectedSections;
  }

  // Check if a section is selected
  isSectionSelected(sectionId: string): boolean {
    // Ensure selectedSections Set exists
    if (!this.state.selectedSections) {
      this.state.selectedSections = new Set<string>();
    }
    return this.state.selectedSections.has(sectionId);
  }

  // Subscribe to state changes
  subscribe(listener: (state: ChangeTrackingState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  // Update current content
  updateCurrentContent(content: string): void {
    this.state.currentContent = content;
    this.notifyListeners();
  }

  // Generate diff data for visualization
  generateDiffData() {
    if (!this.state.isTrackingEnabled) return null;

    const diff = diffSentences(this.state.originalContent, this.state.currentContent);
    return diff.map((part, index) => ({
      id: index,
      added: part.added || false,
      removed: part.removed || false,
      value: part.value,
      count: part.count || 0
    }));
  }
}

// Export singleton instance
export const changeTrackingService = new ChangeTrackingService();
