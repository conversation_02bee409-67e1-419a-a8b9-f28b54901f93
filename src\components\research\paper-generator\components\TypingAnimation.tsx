import React, { useState, useEffect } from 'react';
import { Loader2, <PERSON>Tool, <PERSON>, <PERSON>rkles } from 'lucide-react';

interface TypingAnimationProps {
  text: string;
  speed?: number;
  showCursor?: boolean;
  onComplete?: () => void;
  className?: string;
}

export const TypingAnimation: React.FC<TypingAnimationProps> = ({
  text,
  speed = 50,
  showCursor = true,
  onComplete,
  className = ""
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timer);
    } else if (!isComplete) {
      setIsComplete(true);
      onComplete?.();
    }
  }, [currentIndex, text, speed, onComplete, isComplete]);

  return (
    <span className={className}>
      {displayedText}
      {showCursor && !isComplete && (
        <span className="animate-pulse text-blue-500">|</span>
      )}
    </span>
  );
};

interface GenerationStatusProps {
  status: 'pending' | 'generating' | 'completed' | 'error';
  sectionName: string;
  progress?: number;
}

export const GenerationStatus: React.FC<GenerationStatusProps> = ({
  status,
  sectionName,
  progress = 0
}) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (status === 'generating') {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev.length >= 3) return '';
          return prev + '.';
        });
      }, 500);

      return () => clearInterval(interval);
    }
  }, [status]);

  const getStatusContent = () => {
    switch (status) {
      case 'pending':
        return (
          <div className="flex items-center gap-2 text-gray-500">
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <span>Waiting to generate {sectionName}</span>
          </div>
        );
      
      case 'generating':
        return (
          <div className="flex items-center gap-3">
            <div className="relative">
              <Brain className="h-5 w-5 text-blue-600 animate-pulse" />
              <Sparkles className="h-3 w-3 text-yellow-500 absolute -top-1 -right-1 animate-bounce" />
            </div>
            <div className="flex-1">
              <TypingAnimation
                text={`Generating ${sectionName} with AI precision`}
                speed={30}
                className="text-blue-700 font-medium"
              />
              <span className="text-blue-500 font-bold">{dots}</span>
            </div>
            {progress > 0 && (
              <div className="text-sm text-blue-600 font-medium">
                {Math.round(progress)}%
              </div>
            )}
          </div>
        );
      
      case 'completed':
        return (
          <div className="flex items-center gap-2 text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="font-medium">✅ {sectionName} completed</span>
          </div>
        );
      
      case 'error':
        return (
          <div className="flex items-center gap-2 text-red-600">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>❌ Error generating {sectionName}</span>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="py-2">
      {getStatusContent()}
    </div>
  );
};

interface FloatingParticlesProps {
  count?: number;
  className?: string;
}

export const FloatingParticles: React.FC<FloatingParticlesProps> = ({
  count = 20,
  className = ""
}) => {
  const particles = Array.from({ length: count }, (_, i) => ({
    id: i,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2,
    x: Math.random() * 100,
    y: Math.random() * 100,
  }));

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-30"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animation: `float ${particle.duration}s ease-in-out infinite`,
            animationDelay: `${particle.delay}s`,
          }}
        />
      ))}
    </div>
  );
};

interface PulsingDotsProps {
  count?: number;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const PulsingDots: React.FC<PulsingDotsProps> = ({
  count = 3,
  color = 'blue',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    yellow: 'bg-yellow-500'
  };

  return (
    <div className="flex items-center gap-1">
      {Array.from({ length: count }, (_, i) => (
        <div
          key={i}
          className={`${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses]} rounded-full animate-pulse`}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

interface WaveLoaderProps {
  className?: string;
}

export const WaveLoader: React.FC<WaveLoaderProps> = ({ className = "" }) => {
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {[0, 1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className="w-1 bg-blue-500 rounded-full animate-pulse"
          style={{
            height: '20px',
            animationDelay: `${i * 0.1}s`,
            animationDuration: '0.8s'
          }}
        />
      ))}
    </div>
  );
};
