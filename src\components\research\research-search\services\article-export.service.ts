/**
 * Article Export Service
 * Handles exporting complete articles to various formats with proper academic formatting
 */

import { CompleteArticle } from './complete-article-generation.service';

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'txt' | 'html';
  includeMetadata: boolean;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  fontSize: number;
  lineSpacing: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

class ArticleExportService {
  /**
   * Export article to specified format
   */
  async exportArticle(
    article: CompleteArticle,
    options: Partial<ExportOptions> = {}
  ): Promise<Blob> {
    const defaultOptions: ExportOptions = {
      format: 'pdf',
      includeMetadata: true,
      citationStyle: 'apa',
      fontSize: 12,
      lineSpacing: 1.5,
      margins: {
        top: 1,
        bottom: 1,
        left: 1,
        right: 1
      }
    };

    const exportOptions = { ...defaultOptions, ...options };

    switch (exportOptions.format) {
      case 'txt':
        return this.exportToText(article, exportOptions);
      case 'html':
        return this.exportToHtml(article, exportOptions);
      case 'docx':
        return this.exportToDocx(article, exportOptions);
      case 'pdf':
        return this.exportToPdf(article, exportOptions);
      default:
        throw new Error(`Unsupported export format: ${exportOptions.format}`);
    }
  }

  /**
   * Export to plain text with proper academic formatting
   */
  private exportToText(article: CompleteArticle, options: ExportOptions): Blob {
    let content = '';
    
    // Title page
    content += this.centerText(article.title.toUpperCase()) + '\n\n';
    
    if (options.includeMetadata) {
      content += this.centerText('Academic Review Article') + '\n\n';
      content += this.centerText(`Generated on ${article.generatedAt.toLocaleDateString()}`) + '\n\n';
      content += this.centerText(`Word Count: ${article.wordCount.toLocaleString()}`) + '\n\n';
      content += this.centerText(`Citations: ${article.citationCount}`) + '\n\n';
    }
    
    content += '='.repeat(80) + '\n\n';
    
    // Abstract
    if (article.abstract) {
      content += 'ABSTRACT\n\n';
      content += this.wrapText(article.abstract, 80) + '\n\n';
      content += 'Keywords: ' + article.metadata.keywords.join(', ') + '\n\n';
      content += '-'.repeat(80) + '\n\n';
    }
    
    // Table of Contents
    content += 'TABLE OF CONTENTS\n\n';
    article.sections.forEach((section, index) => {
      content += `${index + 1}. ${section.title}\n`;
      if (section.subsections) {
        section.subsections.forEach((subsection, subIndex) => {
          content += `   ${index + 1}.${subIndex + 1} ${subsection.title}\n`;
        });
      }
    });
    content += '\n' + '-'.repeat(80) + '\n\n';
    
    // Main content
    article.sections.forEach((section, index) => {
      content += `${index + 1}. ${section.title.toUpperCase()}\n\n`;
      content += this.wrapText(section.content, 80) + '\n\n';
      
      // Subsections
      if (section.subsections) {
        section.subsections.forEach((subsection, subIndex) => {
          content += `${index + 1}.${subIndex + 1} ${subsection.title}\n\n`;
          content += this.wrapText(subsection.content, 80) + '\n\n';
        });
      }
    });
    
    // References
    if (article.references.length > 0) {
      content += 'REFERENCES\n\n';
      article.references.forEach((ref, index) => {
        const citation = options.citationStyle === 'apa' ? ref.formattedAPA : ref.formattedMLA;
        content += `[${index + 1}] ${this.wrapText(citation, 76, '    ')}\n\n`;
      });
    }
    
    return new Blob([content], { type: 'text/plain;charset=utf-8' });
  }

  /**
   * Export to HTML with academic styling
   */
  private exportToHtml(article: CompleteArticle, options: ExportOptions): Blob {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${article.title}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            font-size: ${options.fontSize}pt;
            line-height: ${options.lineSpacing};
            margin: ${options.margins.top}in ${options.margins.right}in ${options.margins.bottom}in ${options.margins.left}in;
            color: #000;
            background: #fff;
        }
        .title-page {
            text-align: center;
            margin-bottom: 2in;
        }
        .title {
            font-size: 16pt;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 1in;
        }
        .metadata {
            font-size: 12pt;
            margin-bottom: 0.5in;
        }
        .abstract {
            margin: 1in 0;
        }
        .abstract-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 0.5in;
        }
        .keywords {
            margin-top: 0.5in;
            font-style: italic;
        }
        .section-title {
            font-weight: bold;
            margin-top: 1in;
            margin-bottom: 0.5in;
        }
        .subsection-title {
            font-weight: bold;
            margin-top: 0.5in;
            margin-bottom: 0.25in;
        }
        .references {
            margin-top: 1in;
        }
        .reference {
            margin-bottom: 0.25in;
            text-indent: -0.5in;
            padding-left: 0.5in;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <!-- Title Page -->
    <div class="title-page">
        <div class="title">${article.title}</div>
        ${options.includeMetadata ? `
        <div class="metadata">
            <p>Academic Review Article</p>
            <p>Generated on ${article.generatedAt.toLocaleDateString()}</p>
            <p>Word Count: ${article.wordCount.toLocaleString()}</p>
            <p>Citations: ${article.citationCount}</p>
        </div>
        ` : ''}
    </div>

    ${article.abstract ? `
    <!-- Abstract -->
    <div class="abstract">
        <div class="abstract-title">ABSTRACT</div>
        <p>${article.abstract}</p>
        <div class="keywords">Keywords: ${article.metadata.keywords.join(', ')}</div>
    </div>
    <div class="page-break"></div>
    ` : ''}

    <!-- Main Content -->
    ${article.sections.map((section, index) => `
    <div class="section">
        <h2 class="section-title">${index + 1}. ${section.title}</h2>
        <div>${this.formatTextForHtml(section.content)}</div>
        
        ${section.subsections ? section.subsections.map((subsection, subIndex) => `
        <div class="subsection">
            <h3 class="subsection-title">${index + 1}.${subIndex + 1} ${subsection.title}</h3>
            <div>${this.formatTextForHtml(subsection.content)}</div>
        </div>
        `).join('') : ''}
    </div>
    `).join('')}

    <!-- References -->
    ${article.references.length > 0 ? `
    <div class="references page-break">
        <h2 class="section-title">REFERENCES</h2>
        ${article.references.map((ref, index) => {
          const citation = options.citationStyle === 'apa' ? ref.formattedAPA : ref.formattedMLA;
          return `<div class="reference">[${index + 1}] ${citation}</div>`;
        }).join('')}
    </div>
    ` : ''}
</body>
</html>`;

    return new Blob([html], { type: 'text/html;charset=utf-8' });
  }

  /**
   * Export to DOCX format (simplified implementation)
   */
  private exportToDocx(article: CompleteArticle, options: ExportOptions): Blob {
    // This is a simplified implementation
    // In a production environment, you would use a library like docx or mammoth
    const htmlContent = this.exportToHtml(article, options);
    
    // Convert HTML to a basic DOCX-like format
    const docxHeader = `
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
<w:body>
`;
    
    const docxFooter = `
</w:body>
</w:document>`;
    
    // This is a very basic conversion - in reality you'd need proper DOCX generation
    const content = docxHeader + this.convertHtmlToDocxContent(article) + docxFooter;
    
    return new Blob([content], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
  }

  /**
   * Export to PDF format (simplified implementation)
   */
  private exportToPdf(article: CompleteArticle, options: ExportOptions): Blob {
    // This is a simplified implementation
    // In a production environment, you would use a library like jsPDF, puppeteer, or html2pdf
    const htmlBlob = this.exportToHtml(article, options);
    
    // For now, return HTML content that can be printed to PDF
    return new Blob([htmlBlob], { type: 'application/pdf' });
  }

  // Helper methods
  private centerText(text: string, width: number = 80): string {
    const padding = Math.max(0, Math.floor((width - text.length) / 2));
    return ' '.repeat(padding) + text;
  }

  private wrapText(text: string, width: number, indent: string = ''): string {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = indent;

    for (const word of words) {
      if (currentLine.length + word.length + 1 <= width) {
        currentLine += (currentLine === indent ? '' : ' ') + word;
      } else {
        lines.push(currentLine);
        currentLine = indent + word;
      }
    }
    
    if (currentLine.length > indent.length) {
      lines.push(currentLine);
    }

    return lines.join('\n');
  }

  private formatTextForHtml(text: string): string {
    return text
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^/, '<p>')
      .replace(/$/, '</p>');
  }

  private convertHtmlToDocxContent(article: CompleteArticle): string {
    // Very basic HTML to DOCX conversion
    // In reality, you'd need proper DOCX XML generation
    let content = `<w:p><w:r><w:t>${article.title}</w:t></w:r></w:p>`;
    
    if (article.abstract) {
      content += `<w:p><w:r><w:t>ABSTRACT</w:t></w:r></w:p>`;
      content += `<w:p><w:r><w:t>${article.abstract}</w:t></w:r></w:p>`;
    }
    
    article.sections.forEach((section, index) => {
      content += `<w:p><w:r><w:t>${index + 1}. ${section.title}</w:t></w:r></w:p>`;
      content += `<w:p><w:r><w:t>${section.content}</w:t></w:r></w:p>`;
    });
    
    return content;
  }

  /**
   * Save article to local storage or database
   */
  async saveArticle(article: CompleteArticle, userId?: string): Promise<string> {
    try {
      const articleData = {
        id: article.id,
        userId: userId || 'anonymous',
        title: article.title,
        content: JSON.stringify(article),
        createdAt: article.generatedAt.toISOString(),
        wordCount: article.wordCount,
        citationCount: article.citationCount,
        metadata: article.metadata
      };

      // Save to localStorage for now (in production, save to database)
      const savedArticles = JSON.parse(localStorage.getItem('savedArticles') || '[]');
      savedArticles.push(articleData);
      localStorage.setItem('savedArticles', JSON.stringify(savedArticles));

      return article.id;
    } catch (error) {
      console.error('Error saving article:', error);
      throw new Error('Failed to save article');
    }
  }

  /**
   * Load saved articles
   */
  async loadSavedArticles(userId?: string): Promise<CompleteArticle[]> {
    try {
      const savedArticles = JSON.parse(localStorage.getItem('savedArticles') || '[]');
      const userArticles = userId 
        ? savedArticles.filter((article: any) => article.userId === userId)
        : savedArticles;

      return userArticles.map((articleData: any) => JSON.parse(articleData.content));
    } catch (error) {
      console.error('Error loading saved articles:', error);
      return [];
    }
  }

  /**
   * Delete saved article
   */
  async deleteArticle(articleId: string): Promise<void> {
    try {
      const savedArticles = JSON.parse(localStorage.getItem('savedArticles') || '[]');
      const filteredArticles = savedArticles.filter((article: any) => article.id !== articleId);
      localStorage.setItem('savedArticles', JSON.stringify(filteredArticles));
    } catch (error) {
      console.error('Error deleting article:', error);
      throw new Error('Failed to delete article');
    }
  }
}

export const articleExportService = new ArticleExportService();
