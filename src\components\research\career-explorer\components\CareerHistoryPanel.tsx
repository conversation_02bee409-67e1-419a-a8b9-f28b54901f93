/**
 * Career History Panel Component
 * Displays and manages previous career explorations
 */

import React, { useState, useEffect } from 'react';
import { CareerExploration } from '../types';
import { useAuth } from '@/contexts/AuthContext';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  History, 
  Calendar, 
  Briefcase, 
  Plus,
  Trash2,
  Download,
  Eye
} from "lucide-react";

interface CareerHistoryPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadExploration: (exploration: CareerExploration) => void;
  onCreateNew: () => void;
}

export function CareerHistoryPanel({ 
  isOpen, 
  onClose, 
  onLoadExploration, 
  onCreateNew 
}: CareerHistoryPanelProps) {
  const { user } = useAuth();
  const [explorations, setExplorations] = useState<CareerExploration[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load career explorations when panel opens
  useEffect(() => {
    if (isOpen && user) {
      loadExplorations();
    }
  }, [isOpen, user]);

  const loadExplorations = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from Supabase
      // For now, we'll use mock data
      const mockExplorations: CareerExploration[] = [
        {
          id: '1',
          user_id: user?.id || '',
          title: 'Software Engineering Transition',
          metadata: {
            name: 'John Doe',
            field: 'Technology',
            interests: ['Programming', 'AI', 'Web Development'],
            experience: '5 years in marketing',
            goals: 'Transition to software engineering'
          },
          resume_data: {
            rawText: 'Sample resume text...',
            extractedInfo: {
              skills: ['JavaScript', 'Python', 'Marketing'],
              experience: ['Marketing Manager', 'Digital Marketing'],
              education: ['MBA', 'Computer Science'],
              certifications: ['Google Analytics']
            },
            fileName: 'resume.pdf',
            uploadedAt: new Date('2024-01-15')
          },
          career_analysis: {
            careerPaths: [],
            overallAnalysis: 'Strong potential for tech transition',
            recommendations: [],
            nextSteps: [],
            generatedAt: new Date('2024-01-15')
          },
          ai_model: 'gpt-4-turbo',
          status: 'completed',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          user_id: user?.id || '',
          title: 'Healthcare Career Exploration',
          metadata: {
            name: 'Jane Smith',
            field: 'Healthcare',
            interests: ['Patient Care', 'Medical Technology'],
            experience: '3 years in nursing',
            goals: 'Advance in healthcare administration'
          },
          resume_data: {
            rawText: 'Sample resume text...',
            extractedInfo: {
              skills: ['Patient Care', 'Medical Records', 'Communication'],
              experience: ['Registered Nurse', 'Clinical Assistant'],
              education: ['BSN', 'Healthcare Administration'],
              certifications: ['RN License', 'BLS']
            },
            fileName: 'healthcare_resume.pdf',
            uploadedAt: new Date('2024-01-10')
          },
          career_analysis: {
            careerPaths: [],
            overallAnalysis: 'Excellent foundation for healthcare leadership',
            recommendations: [],
            nextSteps: [],
            generatedAt: new Date('2024-01-10')
          },
          ai_model: 'claude-3-sonnet',
          status: 'completed',
          created_at: '2024-01-10T14:00:00Z',
          updated_at: '2024-01-10T14:45:00Z'
        }
      ];
      
      setExplorations(mockExplorations);
    } catch (error) {
      console.error('Failed to load career explorations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteExploration = async (id: string) => {
    try {
      // In a real implementation, this would delete from Supabase
      setExplorations(prev => prev.filter(exp => exp.id !== id));
    } catch (error) {
      console.error('Failed to delete exploration:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <History className="h-5 w-5 mr-2 text-blue-600" />
              Career Exploration History
            </div>
            <Button onClick={onCreateNew} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Exploration
            </Button>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <History className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
                <p className="text-gray-600">Loading your career explorations...</p>
              </div>
            </div>
          ) : explorations.length === 0 ? (
            <div className="text-center py-8">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Career Explorations Yet
              </h3>
              <p className="text-gray-600 mb-4">
                Start your first career exploration to see your history here.
              </p>
              <Button onClick={onCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Exploration
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {explorations.map((exploration) => (
                <Card key={exploration.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{exploration.title}</CardTitle>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(exploration.created_at)}
                          </div>
                          <div className="flex items-center">
                            <Briefcase className="h-4 w-4 mr-1" />
                            {exploration.metadata.field}
                          </div>
                          <Badge className={getStatusColor(exploration.status)}>
                            {exploration.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onLoadExploration(exploration)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {/* Handle export */}}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteExploration(exploration.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-900 mb-1">Interests</h4>
                        <div className="flex flex-wrap gap-1">
                          {exploration.metadata.interests.slice(0, 3).map((interest, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {interest}
                            </Badge>
                          ))}
                          {exploration.metadata.interests.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{exploration.metadata.interests.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-900 mb-1">AI Model</h4>
                        <p className="text-sm text-gray-600">{exploration.ai_model}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-900 mb-1">Career Paths</h4>
                        <p className="text-sm text-gray-600">
                          {exploration.career_analysis.careerPaths.length} paths analyzed
                        </p>
                      </div>
                    </div>
                    
                    {exploration.career_analysis.overallAnalysis && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <p className="text-sm text-gray-700 line-clamp-2">
                          {exploration.career_analysis.overallAnalysis}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
