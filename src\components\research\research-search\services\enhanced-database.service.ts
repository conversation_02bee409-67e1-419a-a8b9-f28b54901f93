/**
 * Enhanced Database Service for Academic Research
 * Handles comprehensive database operations for academic research functionality
 */

import { supabase } from '@/lib/supabase';
import {
  DeepResearchSession,
  DeepResearchOutline,
  ResearchPoint,
  SearchSource,
  Citation,
  ResearchReference,
  DeepResearchProgress,
  ResearchMetadata,
  EnhancedSearchSource
} from '../types';

export interface DatabaseResearchOutline {
  id: string;
  session_id: string;
  user_id: string;
  title: string;
  description: string;
  research_type: string;
  total_points: number;
  estimated_word_count: number;
  estimated_time_hours: number;
  status: string;
  outline_data: any;
  keywords: string[];
  target_audience: string;
  abstract: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseResearchPoint {
  id: string;
  outline_id: string;
  point_number: number;
  title: string;
  description: string;
  status: string;
  search_queries: string[];
  content: string;
  word_count: number;
  citation_count: number;
  quality_score: number;
  ai_assistant_id: string;
  ai_assistant_name: string;
  subpoints: any;
  key_findings: string[];
  completed_at: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseResearchSource {
  id: string;
  session_id: string;
  point_id: string;
  user_id: string;
  title: string;
  url: string;
  domain: string;
  snippet: string;
  content: string;
  published_date: string;
  authors: string[];
  doi: string;
  journal: string;
  volume: string;
  issue: string;
  pages: string;
  source_type: string;
  quality_score: number;
  relevance_score: number;
  is_peer_reviewed: boolean;
  is_government: boolean;
  is_academic: boolean;
  citation_count: number;
  impact_factor: number;
  institution_affiliation: string;
  methodology: string;
  data_quality: string;
  recency: string;
  search_query: string;
  tavily_score: number;
  created_at: string;
  updated_at: string;
}

export class EnhancedDatabaseService {
  private currentUserId: string | null = null;

  constructor() {
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  private async initializeAuth() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.warn('Auth session error:', error);
        this.currentUserId = null;
      } else {
        this.currentUserId = session?.user?.id || null;
      }

      supabase.auth.onAuthStateChange((event, session) => {
        this.currentUserId = session?.user?.id || null;
      });
    } catch (error) {
      console.warn('Failed to initialize auth:', error);
      this.currentUserId = null;
    }
  }

  /**
   * Save research outline to database
   */
  async saveResearchOutline(
    sessionId: string,
    outline: DeepResearchOutline
  ): Promise<string> {
    if (!this.currentUserId) {
      throw new Error('User must be authenticated');
    }

    try {
      // First check if the table exists and create it if needed
      await this.ensureTablesExist();

      const outlineData = {
        session_id: sessionId,
        user_id: this.currentUserId,
        title: outline.title,
        description: outline.description,
        research_type: outline.researchType?.id || 'literature_review',
        total_points: outline.totalPoints,
        estimated_word_count: outline.estimatedWordCount || 5000,
        estimated_time_hours: outline.estimatedTimeHours || 2,
        status: 'draft',
        outline_data: {
          points: outline.points,
          abstract: outline.abstract,
          keywords: outline.keywords,
          targetAudience: outline.targetAudience
        },
        keywords: outline.keywords || [],
        target_audience: outline.targetAudience || '',
        abstract: outline.abstract || ''
      };

      const { data, error } = await supabase
        .from('research_outlines')
        .insert([outlineData])
        .select()
        .single();

      if (error) {
        console.error('Error saving research outline:', error);
        throw new Error('Failed to save research outline');
      }

      return data.id;
    } catch (error) {
      console.error('Error in saveResearchOutline:', error);
      throw error;
    }
  }

  /**
   * Ensure required tables exist
   */
  private async ensureTablesExist(): Promise<void> {
    try {
      // Check if research_outlines table exists
      const { error: checkError } = await supabase
        .from('research_outlines')
        .select('id')
        .limit(1);

      if (checkError && checkError.code === 'PGRST116') {
        // Table doesn't exist, create it
        console.log('Creating research_outlines table...');
        await this.createResearchTables();
      }
    } catch (error) {
      console.error('Error checking table existence:', error);
      // Try to create tables anyway
      await this.createResearchTables();
    }
  }

  /**
   * Create research tables if they don't exist
   */
  private async createResearchTables(): Promise<void> {
    try {
      // Execute the schema creation SQL
      const schemaSQL = `
        -- Create research_outlines table
        CREATE TABLE IF NOT EXISTS research_outlines (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            session_id UUID,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            description TEXT,
            research_type VARCHAR(50) NOT NULL DEFAULT 'literature_review',
            total_points INTEGER NOT NULL DEFAULT 10,
            estimated_word_count INTEGER DEFAULT 5000,
            estimated_time_hours INTEGER DEFAULT 2,
            status VARCHAR(30) DEFAULT 'draft',
            outline_data JSONB NOT NULL,
            keywords TEXT[],
            target_audience TEXT,
            abstract TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create research_points table
        CREATE TABLE IF NOT EXISTS research_points (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            outline_id UUID REFERENCES research_outlines(id) ON DELETE CASCADE,
            point_number INTEGER NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            status VARCHAR(30) DEFAULT 'pending',
            search_queries TEXT[],
            content TEXT,
            word_count INTEGER DEFAULT 0,
            citation_count INTEGER DEFAULT 0,
            quality_score DECIMAL(3,2) DEFAULT 0.0,
            ai_assistant_id TEXT,
            ai_assistant_name TEXT,
            subpoints JSONB,
            key_findings TEXT[],
            completed_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Enable RLS
        ALTER TABLE research_outlines ENABLE ROW LEVEL SECURITY;
        ALTER TABLE research_points ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies
        CREATE POLICY IF NOT EXISTS "Users can view their own research outlines" ON research_outlines
            FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can create their own research outlines" ON research_outlines
            FOR INSERT WITH CHECK (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can update their own research outlines" ON research_outlines
            FOR UPDATE USING (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can delete their own research outlines" ON research_outlines
            FOR DELETE USING (auth.uid() = user_id);

        CREATE POLICY IF NOT EXISTS "Users can view research points for their outlines" ON research_points
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM research_outlines
                    WHERE research_outlines.id = research_points.outline_id
                    AND research_outlines.user_id = auth.uid()
                )
            );

        CREATE POLICY IF NOT EXISTS "Users can create research points for their outlines" ON research_points
            FOR INSERT WITH CHECK (
                EXISTS (
                    SELECT 1 FROM research_outlines
                    WHERE research_outlines.id = research_points.outline_id
                    AND research_outlines.user_id = auth.uid()
                )
            );
      `;

      const { error } = await supabase.rpc('exec_sql', { sql: schemaSQL });

      if (error) {
        console.error('Error creating research tables:', error);
        // If RPC doesn't work, try alternative approach
        throw error;
      }

      console.log('Research tables created successfully');
    } catch (error) {
      console.error('Failed to create research tables:', error);
      // Continue anyway - the error might be that tables already exist
    }
  }

  /**
   * Save research point to database
   */
  async saveResearchPoint(
    outlineId: string,
    point: ResearchPoint,
    content?: string,
    sources?: SearchSource[],
    citations?: Citation[]
  ): Promise<string> {
    const pointData = {
      outline_id: outlineId,
      point_number: point.pointNumber,
      title: point.title,
      description: point.description,
      status: point.status,
      search_queries: point.searchQueries || [],
      content: content || point.content || '',
      word_count: content ? content.split(' ').length : (point.wordCount || 0),
      citation_count: citations?.length || 0,
      quality_score: 0.8, // Default quality score
      ai_assistant_id: '',
      ai_assistant_name: '',
      subpoints: point.subpoints || [],
      key_findings: [],
      completed_at: point.status === 'completed' ? new Date().toISOString() : null
    };

    const { data, error } = await supabase
      .from('research_points')
      .insert([pointData])
      .select()
      .single();

    if (error) {
      console.error('Error saving research point:', error);
      throw new Error('Failed to save research point');
    }

    // Save associated sources if provided
    if (sources && sources.length > 0) {
      await this.saveResearchSources(data.id, sources);
    }

    // Save associated citations if provided
    if (citations && citations.length > 0) {
      await this.saveResearchCitations(data.id, citations);
    }

    return data.id;
  }

  /**
   * Save research sources to database
   */
  async saveResearchSources(
    pointId: string,
    sources: SearchSource[]
  ): Promise<void> {
    if (!this.currentUserId || sources.length === 0) return;

    const sourcesData = sources.map(source => ({
      point_id: pointId,
      user_id: this.currentUserId,
      title: source.title,
      url: source.url,
      domain: source.domain,
      snippet: source.snippet || '',
      content: '', // Will be populated if available
      published_date: source.publishedDate || null,
      authors: [], // Will be extracted from content
      doi: '', // Will be extracted from content
      journal: '', // Will be extracted from content
      volume: '',
      issue: '',
      pages: '',
      source_type: source.type || 'web',
      quality_score: 0.7, // Default quality score
      relevance_score: source.score || 0.5,
      is_peer_reviewed: source.type === 'academic',
      is_government: source.domain.includes('.gov'),
      is_academic: source.type === 'academic',
      citation_count: 0,
      impact_factor: null,
      institution_affiliation: '',
      methodology: '',
      data_quality: 'medium',
      recency: this.assessRecency(source.publishedDate),
      search_query: '',
      tavily_score: source.score || 0.5
    }));

    const { error } = await supabase
      .from('research_sources')
      .insert(sourcesData);

    if (error) {
      console.error('Error saving research sources:', error);
      throw new Error('Failed to save research sources');
    }
  }

  /**
   * Save research citations to database
   */
  async saveResearchCitations(
    pointId: string,
    citations: Citation[]
  ): Promise<void> {
    if (!this.currentUserId || citations.length === 0) return;

    const citationsData = citations.map(citation => ({
      point_id: pointId,
      user_id: this.currentUserId,
      citation_text: citation.text,
      citation_type: 'reference',
      position_in_text: citation.position,
      context_text: '',
      formatted_citation: citation.text,
      citation_key: this.generateCitationKey(citation.title),
      in_text_format: citation.text,
      section_id: pointId,
      page_number: '',
      confidence_score: 0.8
    }));

    const { error } = await supabase
      .from('research_citations')
      .insert(citationsData);

    if (error) {
      console.error('Error saving research citations:', error);
      throw new Error('Failed to save research citations');
    }
  }

  /**
   * Save research progress to database
   */
  async saveResearchProgress(
    sessionId: string,
    pointId: string,
    progress: DeepResearchProgress
  ): Promise<void> {
    if (!this.currentUserId) return;

    const progressData = {
      session_id: sessionId,
      point_id: pointId,
      user_id: this.currentUserId,
      stage: progress.status,
      progress_percentage: progress.progress,
      current_task: progress.message,
      ai_assistant_id: progress.assistantId,
      ai_assistant_name: progress.assistantName,
      status: progress.status === 'completed' ? 'completed' : 'active',
      start_time: progress.startTime?.toISOString() || new Date().toISOString(),
      end_time: progress.endTime?.toISOString() || null,
      duration_minutes: progress.endTime && progress.startTime 
        ? Math.round((progress.endTime.getTime() - progress.startTime.getTime()) / 60000)
        : null,
      error_message: progress.error || null,
      metadata: {
        currentSubpoint: progress.currentSubpoint,
        searchResults: progress.searchResults?.length || 0
      }
    };

    const { error } = await supabase
      .from('research_progress')
      .insert([progressData]);

    if (error) {
      console.error('Error saving research progress:', error);
      // Don't throw error for progress tracking failures
    }
  }

  /**
   * Load research outline from database
   */
  async loadResearchOutline(outlineId: string): Promise<DeepResearchOutline | null> {
    const { data, error } = await supabase
      .from('research_outlines')
      .select('*')
      .eq('id', outlineId)
      .single();

    if (error || !data) {
      console.error('Error loading research outline:', error);
      return null;
    }

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      researchType: { id: data.research_type } as any,
      estimatedWordCount: data.estimated_word_count,
      estimatedTimeHours: data.estimated_time_hours,
      points: data.outline_data?.points || [],
      totalPoints: data.total_points,
      createdAt: new Date(data.created_at),
      abstract: data.abstract,
      keywords: data.keywords,
      targetAudience: data.target_audience
    };
  }

  /**
   * Load research points for an outline
   */
  async loadResearchPoints(outlineId: string): Promise<ResearchPoint[]> {
    const { data, error } = await supabase
      .from('research_points')
      .select('*')
      .eq('outline_id', outlineId)
      .order('point_number');

    if (error || !data) {
      console.error('Error loading research points:', error);
      return [];
    }

    return data.map(point => ({
      id: point.id,
      pointNumber: point.point_number,
      title: point.title,
      description: point.description,
      subpoints: point.subpoints || [],
      status: point.status as any,
      searchQueries: point.search_queries,
      content: point.content,
      wordCount: point.word_count,
      completedAt: point.completed_at ? new Date(point.completed_at) : undefined
    }));
  }

  /**
   * Assess recency of a source
   */
  private assessRecency(publishedDate?: string): string {
    if (!publishedDate) return 'dated';
    
    const published = new Date(publishedDate);
    const now = new Date();
    const yearsDiff = now.getFullYear() - published.getFullYear();
    
    if (yearsDiff <= 1) return 'current';
    if (yearsDiff <= 3) return 'recent';
    return 'dated';
  }

  /**
   * Generate citation key from title
   */
  private generateCitationKey(title: string): string {
    const words = title.split(' ');
    const firstWord = words[0] || 'Unknown';
    const year = new Date().getFullYear();
    return `${firstWord}${year}`;
  }
}

// Export singleton instance
export const enhancedDatabaseService = new EnhancedDatabaseService();
