/**
 * Google Gemini Tutor Service
 * Handles AI tutoring interactions using Google's Gemini API with code execution
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import {
  TutorResponse,
  StreamingTutorResponse,
  EducationLevel,
  GradeLevel
} from '../types';
import { EDUCATION_LEVEL_PROMPTS } from '../constants';

interface TutorGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  educationLevel: EducationLevel;
  includeExamples?: boolean;
  learningStyle?: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  conversationHistory?: Array<{ role: string; content: string }>;
  gradeLevel?: GradeLevel;
}

export class GeminiTutorService {
  private genAI: GoogleGenerativeAI | null = null;
  private isConfigured: boolean = false;

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY ||
                   process.env.NEXT_PUBLIC_GEMINI_API_KEY ||
                   process.env.GEMINI_API_KEY;

    if (!apiKey || apiKey.length < 20) {
      console.warn('Google Gemini API key not found or invalid');
      this.isConfigured = false;
      return;
    }

    try {
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.isConfigured = true;
      console.log('Gemini Tutor Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Gemini Tutor Service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Check if the service is properly configured
   */
  isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get available Gemini models for tutoring
   */
  getAvailableModels(): string[] {
    return [
      'google/gemini-2.5-flash',
      'google/gemini-2.5-pro'
    ];
  }

  /**
   * Get the default Gemini model
   */
  getDefaultModel(): string {
    return 'google/gemini-2.5-flash';
  }

  /**
   * Generate a tutoring response using Gemini with streaming
   */
  async generateTutoringResponseStream(
    topic: string,
    question: string,
    options: TutorGenerationOptions,
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse> {
    if (!this.isConfigured || !this.genAI) {
      throw new Error('Gemini service not configured. Please check your API key.');
    }

    const {
      model = 'gemini-2.5-flash',
      temperature = 0.7,
      maxTokens = 8192,
      educationLevel,
      includeExamples = true,
      learningStyle = 'visual',
      conversationHistory = []
    } = options;

    try {
      const systemPrompt = this.buildSystemPrompt(topic, educationLevel, learningStyle, includeExamples);
      const fullPrompt = this.buildFullPrompt(systemPrompt, question, conversationHistory);

      const genModel = this.genAI.getGenerativeModel({
        model: model.replace('google/', ''),
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        }
      });

      const result = await genModel.generateContentStream(fullPrompt);
      let fullContent = '';

      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        if (chunkText) {
          fullContent += chunkText;
          onChunk?.(chunkText);
        }
      }

      return {
        content: fullContent,
        sources: [],
        confidence: this.calculateConfidence(fullContent),
        tokens: this.estimateTokens(fullContent),
        model: `google/${model}`,
        suggestions: this.extractSuggestions(fullContent)
      };

    } catch (error) {
      console.error('Gemini tutoring request failed:', error);
      throw new Error(`Failed to generate tutoring response: ${error.message}`);
    }
  }

  /**
   * Generate response with code execution capabilities
   */
  async generateWithCodeExecution(
    topic: string,
    question: string,
    options: TutorGenerationOptions,
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse & { codeOutput?: string; visualizations?: any[] }> {
    if (!this.isConfigured || !this.genAI) {
      throw new Error('Gemini service not configured. Please check your API key.');
    }

    const {
      temperature = 0.7,
      maxTokens = 8192,
      educationLevel,
      includeExamples = true,
      learningStyle = 'visual',
      conversationHistory = []
    } = options;

    try {
      const systemPrompt = this.buildSystemPromptWithCode(topic, educationLevel, learningStyle, includeExamples);
      const fullPrompt = this.buildFullPrompt(systemPrompt, question, conversationHistory);

      const model = this.genAI.getGenerativeModel({
        model: 'gemini-2.5-pro',
        tools: [{ codeExecution: {} }],
        generationConfig: {
          temperature,
          maxOutputTokens: maxTokens,
        }
      });

      const result = await model.generateContentStream(fullPrompt);

      let fullContent = '';
      let codeOutput = '';
      const visualizations: any[] = [];

      for await (const chunk of result.stream) {
        if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
          continue;
        }

        for (const part of chunk.candidates[0].content.parts) {
          if (part.text) {
            fullContent += part.text;
            onChunk?.(part.text);
          }
          if (part.executableCode) {
            const code = part.executableCode.code;
            codeOutput += `Code:\n${code}\n\n`;
            visualizations.push({
              type: 'code',
              code: code,
              language: part.executableCode.language || 'python'
            });
            onChunk?.(`\n\n**Code:**\n\`\`\`${part.executableCode.language || 'python'}\n${code}\n\`\`\`\n\n`);
          }
          if (part.codeExecutionResult) {
            const output = part.codeExecutionResult.output;
            codeOutput += `Output:\n${output}\n\n`;
            onChunk?.(`**Output:**\n\`\`\`\n${output}\n\`\`\`\n\n`);
          }
        }
      }

      return {
        content: fullContent,
        sources: [],
        confidence: this.calculateConfidence(fullContent),
        tokens: this.estimateTokens(fullContent),
        model: 'google/gemini-2.5-pro',
        suggestions: this.extractSuggestions(fullContent),
        codeOutput: codeOutput || undefined,
        visualizations
      };

    } catch (error) {
      console.error('Gemini code execution request failed:', error);
      throw new Error(`Failed to generate response with code execution: ${error.message}`);
    }
  }

  /**
   * Generate streaming tutoring response using Gemini
   */
  async *generateStreamingResponse(
    topic: string,
    question: string,
    options: TutorGenerationOptions
  ): AsyncGenerator<StreamingTutorResponse, void, unknown> {
    if (!this.isConfigured) {
      throw new Error('Gemini service not configured. Please check your API key.');
    }

    const {
      model = 'gemini-2.5-flash',
      temperature = 0.7,
      maxTokens = 2048,
      educationLevel,
      includeExamples = true,
      learningStyle = 'visual',
      conversationHistory = []
    } = options;

    try {
      const systemPrompt = this.buildSystemPrompt(topic, educationLevel, learningStyle, includeExamples);
      const fullPrompt = this.buildFullPrompt(systemPrompt, question, conversationHistory);

      const response = await this.ai.models.generateContentStream({
        model: model.replace('google/', ''),
        config: {
          generationConfig: {
            maxOutputTokens: maxTokens,
            temperature: temperature
          }
        },
        contents: [{
          role: 'user',
          parts: [{ text: fullPrompt }]
        }]
      });

      for await (const chunk of response) {
        if (chunk.text) {
          yield {
            chunk: chunk.text,
            isComplete: false
          };
        }
      }

      yield {
        chunk: '',
        isComplete: true,
        metadata: { tokens: 0, model: `google/${model}` }
      };

    } catch (error) {
      console.error('Gemini streaming request failed:', error);
      throw new Error(`Failed to generate streaming response: ${error.message}`);
    }
  }

  /**
   * Build system prompt with code execution capabilities
   */
  private buildSystemPromptWithCode(
    topic: string,
    educationLevel: EducationLevel,
    learningStyle: string,
    includeExamples: boolean
  ): string {
    const basePrompt = this.buildSystemPrompt(topic, educationLevel, learningStyle, includeExamples);

    return `${basePrompt}

Code Execution Guidelines:
- Use code to demonstrate concepts, perform calculations, or create visualizations
- Write clean, well-commented code that students can understand
- Explain what the code does and why it's useful
- Create interactive examples when possible
- Use appropriate libraries for visualizations (matplotlib, plotly, etc.)
- Always explain the output and its significance
- Focus on educational value over complexity`;
  }

  /**
   * Build system prompt based on education level and learning preferences
   */
  private buildSystemPrompt(
    topic: string,
    educationLevel: EducationLevel,
    learningStyle: string,
    includeExamples: boolean
  ): string {
    const basePrompt = EDUCATION_LEVEL_PROMPTS[educationLevel] || 'You are an expert AI tutor.';

    let prompt = `${basePrompt}

You are helping a student learn about: ${topic}

Teaching Guidelines:
- Adapt your explanations to the ${educationLevel.replace('-', ' ')} level
- Use a ${learningStyle} learning approach when possible
- ${includeExamples ? 'Include relevant examples and analogies' : 'Focus on clear explanations without extensive examples'}
- Be encouraging and supportive
- Break down complex concepts into manageable parts
- Provide practical applications and real-world connections
- Ask follow-up questions to check understanding
- Break complex concepts into digestible parts
- Use appropriate vocabulary for the education level

Remember to:
1. Start with what the student already knows
2. Build understanding step by step
3. Use real-world connections when helpful
4. Encourage questions and curiosity
5. Provide clear, accurate information
6. Be patient and supportive`;

    if (learningStyle === 'visual') {
      prompt += '\n- Describe visual elements, diagrams, or imagery when helpful';
    } else if (learningStyle === 'auditory') {
      prompt += '\n- Use verbal explanations and encourage discussion';
    } else if (learningStyle === 'kinesthetic') {
      prompt += '\n- Suggest hands-on activities or practical applications';
    } else if (learningStyle === 'reading') {
      prompt += '\n- Provide structured, text-based explanations';
    }

    return prompt;
  }

  /**
   * Build full prompt combining system prompt, history, and current question
   */
  private buildFullPrompt(
    systemPrompt: string,
    question: string,
    conversationHistory: Array<{ role: string; content: string }>
  ): string {
    let fullPrompt = systemPrompt + '\n\n';

    // Add conversation history (limit to last 5 messages to stay within token limits)
    const recentHistory = conversationHistory.slice(-5);
    if (recentHistory.length > 0) {
      fullPrompt += 'Previous conversation:\n';
      for (const msg of recentHistory) {
        fullPrompt += `${msg.role === 'user' ? 'Student' : 'Tutor'}: ${msg.content}\n`;
      }
      fullPrompt += '\n';
    }

    // Add current question
    fullPrompt += `Student: ${question}\n\nTutor:`;

    return fullPrompt;
  }

  /**
   * Calculate confidence score from response content
   */
  private calculateConfidence(content: string): number {
    if (content.length > 100) {
      return 0.9;
    } else if (content.length > 50) {
      return 0.7;
    } else if (content.length > 20) {
      return 0.5;
    }
    return 0.3;
  }

  /**
   * Estimate token count (rough approximation)
   */
  private estimateTokens(content: string): number {
    return Math.ceil(content.length / 4);
  }

  /**
   * Extract follow-up suggestions from response content
   */
  private extractSuggestions(content: string): string[] {
    const suggestions: string[] = [];
    
    // Look for question patterns in the response
    const questionPatterns = [
      /Would you like to learn about ([^?]+)\?/gi,
      /Do you want to explore ([^?]+)\?/gi,
      /Should we discuss ([^?]+)\?/gi,
      /What about ([^?]+)\?/gi
    ];

    for (const pattern of questionPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && suggestions.length < 3) {
          suggestions.push(match[1].trim());
        }
      }
    }

    // Add some generic follow-up suggestions if none found
    if (suggestions.length === 0) {
      suggestions.push(
        "Can you explain this in more detail?",
        "What are some real-world examples?",
        "How does this relate to other concepts?"
      );
    }

    return suggestions.slice(0, 3);
  }

  /**
   * Get API provider information
   */
  getApiProvider(): string {
    return this.isConfigured ? 'Google/Gemini' : 'none';
  }
}

export const geminiTutorService = new GeminiTutorService();
