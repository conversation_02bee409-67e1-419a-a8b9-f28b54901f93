import { LucideIcon } from "lucide-react";

// Core Document Types
export interface ResearchDocument {
  id: string;
  title: string;
  authors: string[];
  abstract: string;
  publicationYear: number;
  journal?: string;
  doi?: string;
  keywords: string[];
  filename: string;
  fileSize: number;
  uploadedAt: Date;
  lastModified: Date;
  status: 'processing' | 'ready' | 'error';
  processingProgress?: number;
  
  // Extracted content
  sections: DocumentSection[];
  rawContent: string;
  metadata: DocumentMetadata;
  
  // Analysis results
  summary?: string;
  keyFindings: string[];
  methodology: string[];
  limitations: string[];
  futureWork: string[];
  
  // Organization
  tags: string[];
  categories: string[];
  favorite: boolean;
  notes: string;
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  type: 'title' | 'abstract' | 'introduction' | 'literature_review' | 'methodology' | 
         'results' | 'discussion' | 'conclusion' | 'references' | 'appendix' | 'other';
  order: number;
  wordCount: number;
  startPage?: number;
  endPage?: number;
}

export interface DocumentMetadata {
  extractedAt: Date;
  confidence: number;
  sources: string[];
  language: string;
  citationCount?: number;
  impactFactor?: number;
}

// Literature Review Types
export interface LiteratureReview {
  id: string;
  title: string;
  documents: string[]; // document IDs
  sections: ReviewSection[];
  citationStyle: CitationStyle;
  status: 'draft' | 'generating' | 'completed';
  generatedAt: Date;
  wordCount: number;
  exportOptions: ExportFormat[];
}

export interface ReviewSection {
  id: string;
  title: string;
  content: string;
  type: 'introduction' | 'chronological' | 'thematic' | 'methodological' | 'gaps' | 'conclusion';
  order: number;
  sourceDocuments: string[];
  citations: Citation[];
}

export interface Citation {
  id: string;
  documentId: string;
  style: CitationStyle;
  inText: string;
  fullReference: string;
  page?: number;
}

export type CitationStyle = 'APA' | 'MLA' | 'Chicago' | 'Harvard' | 'IEEE' | 'Vancouver';
export type ExportFormat = 'PDF' | 'Word' | 'LaTeX' | 'HTML';

// Research Gap Analysis Types
export interface ResearchGap {
  id: string;
  title: string;
  description: string;
  category: 'methodological' | 'theoretical' | 'empirical' | 'interdisciplinary';
  priority: 'low' | 'medium' | 'high' | 'critical';
  feasibility: number; // 1-10 scale
  impact: number; // 1-10 scale
  relatedDocuments: string[];
  suggestedMethods: string[];
  requiredResources: string[];
  timelineEstimate: string;
  collaborationPotential: number;
}

export interface GapAnalysis {
  id: string;
  documentIds: string[];
  gaps: ResearchGap[];
  themes: ResearchTheme[];
  opportunities: ResearchOpportunity[];
  generatedAt: Date;
  summary: string;
}

export interface ResearchTheme {
  id: string;
  name: string;
  description: string;
  frequency: number;
  relatedDocuments: string[];
  keyTerms: string[];
  evolution: ThemeEvolution[];
}

export interface ThemeEvolution {
  year: number;
  development: string;
  keyPapers: string[];
}

export interface ResearchOpportunity {
  id: string;
  title: string;
  description: string;
  type: 'extension' | 'replication' | 'combination' | 'application' | 'innovation';
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  potentialImpact: 'low' | 'medium' | 'high' | 'transformative';
  requiredExpertise: string[];
  suggestedApproach: string[];
}

// Hypothesis Generation Types
export interface ResearchHypothesis {
  id: string;
  statement: string;
  type: 'directional' | 'non-directional' | 'null';
  variables: {
    independent: Variable[];
    dependent: Variable[];
    control: Variable[];
    moderating?: Variable[];
    mediating?: Variable[];
  };
  testability: number; // 1-10 scale
  novelty: number; // 1-10 scale
  significance: number; // 1-10 scale
  suggestedMethodology: string[];
  expectedOutcomes: string[];
  limitations: string[];
  requiredResources: string[];
  timeline: string;
  relatedGaps: string[];
}

export interface Variable {
  name: string;
  type: 'categorical' | 'continuous' | 'ordinal' | 'binary';
  description: string;
  measurement: string;
  operationalization: string;
}

// Methodology Analysis Types
export interface MethodologyAnalysis {
  id: string;
  documentIds: string[];
  methods: ExtractedMethod[];
  comparison: MethodComparison[];
  recommendations: MethodRecommendation[];
  trends: MethodTrend[];
  generatedAt: Date;
}

export interface ExtractedMethod {
  id: string;
  name: string;
  category: 'quantitative' | 'qualitative' | 'mixed-methods';
  description: string;
  advantages: string[];
  disadvantages: string[];
  usageCount: number;
  successRate?: number;
  sampleSizes: number[];
  tools: string[];
  datasets: string[];
  validationApproaches: string[];
  reproducibility: 'low' | 'medium' | 'high';
  documents: string[];
}

export interface MethodComparison {
  methods: string[];
  criteria: ComparisonCriteria[];
  scores: { [methodId: string]: { [criterion: string]: number } };
  recommendation: string;
}

export interface ComparisonCriteria {
  name: string;
  weight: number;
  description: string;
}

export interface MethodRecommendation {
  researchQuestion: string;
  primaryMethod: string;
  supportingMethods: string[];
  justification: string;
  expectedOutcomes: string[];
  considerations: string[];
}

export interface MethodTrend {
  method: string;
  yearlyUsage: { year: number; count: number }[];
  evolutionDescription: string;
  futureOutlook: string;
}

// Citation Network Types
export interface CitationNetwork {
  id: string;
  documents: string[];
  nodes: CitationNode[];
  edges: CitationEdge[];
  clusters: CitationCluster[];
  metrics: NetworkMetrics;
  generatedAt: Date;
}

export interface CitationNode {
  id: string;
  documentId: string;
  title: string;
  citationCount: number;
  centrality: number;
  influence: number;
  cluster: string;
  position: { x: number; y: number };
}

export interface CitationEdge {
  source: string;
  target: string;
  weight: number;
  type: 'cites' | 'cited_by' | 'co_cited';
}

export interface CitationCluster {
  id: string;
  name: string;
  description: string;
  documents: string[];
  centralDocument: string;
  theme: string;
  color: string;
}

export interface NetworkMetrics {
  density: number;
  modularity: number;
  averagePathLength: number;
  clusteringCoefficient: number;
  influentialPapers: string[];
  emergingTopics: string[];
}

// Research Project Planning Types
export interface ResearchProject {
  id: string;
  title: string;
  description: string;
  objectives: string[];
  hypotheses: string[];
  methodology: string[];
  timeline: ProjectMilestone[];
  resources: ResourceRequirement[];
  risks: RiskAssessment[];
  collaborators: Collaborator[];
  deliverables: Deliverable[];
  budget?: ProjectBudget;
  status: 'planning' | 'proposal' | 'approved' | 'active' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectMilestone {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  dependencies: string[];
  deliverables: string[];
  status: 'pending' | 'active' | 'completed' | 'delayed';
  progress: number;
}

export interface ResourceRequirement {
  type: 'human' | 'financial' | 'technical' | 'institutional';
  description: string;
  quantity: number;
  unit: string;
  cost?: number;
  availability: 'available' | 'needs_acquisition' | 'uncertain';
  criticality: 'low' | 'medium' | 'high' | 'critical';
}

export interface RiskAssessment {
  id: string;
  description: string;
  category: 'technical' | 'financial' | 'personnel' | 'external' | 'ethical';
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigation: string[];
  contingencyPlan: string;
}

export interface Collaborator {
  name: string;
  affiliation: string;
  expertise: string[];
  role: string;
  contribution: string[];
  contactInfo: string;
}

export interface Deliverable {
  id: string;
  title: string;
  type: 'paper' | 'presentation' | 'dataset' | 'software' | 'report';
  description: string;
  dueDate: Date;
  status: 'pending' | 'in_progress' | 'completed';
  dependencies: string[];
}

export interface ProjectBudget {
  totalAmount: number;
  currency: string;
  breakdown: BudgetItem[];
  fundingSources: FundingSource[];
}

export interface BudgetItem {
  category: string;
  description: string;
  amount: number;
  justification: string;
}

export interface FundingSource {
  name: string;
  amount: number;
  status: 'applied' | 'awarded' | 'declined' | 'pending';
  requirements: string[];
  deadline?: Date;
}

// Knowledge Base Types
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  documents: string[];
  concepts: Concept[];
  relationships: ConceptRelationship[];
  tags: Tag[];
  searchIndex: SearchIndex;
  createdAt: Date;
  updatedAt: Date;
}

export interface Concept {
  id: string;
  name: string;
  definition: string;
  category: string;
  frequency: number;
  relatedDocuments: string[];
  synonyms: string[];
  relatedConcepts: string[];
}

export interface ConceptRelationship {
  source: string;
  target: string;
  type: 'related_to' | 'is_a' | 'part_of' | 'causes' | 'enables' | 'opposes';
  strength: number;
  evidence: string[];
}

export interface Tag {
  id: string;
  name: string;
  color: string;
  description: string;
  documents: string[];
  createdAt: Date;
}

export interface SearchIndex {
  terms: { [term: string]: DocumentMatch[] };
  concepts: { [concept: string]: DocumentMatch[] };
  semanticVectors: { [documentId: string]: number[] };
  lastUpdated: Date;
}

export interface DocumentMatch {
  documentId: string;
  relevance: number;
  contexts: string[];
  positions: number[];
}

// Data Extraction Types
export interface ExtractedData {
  id: string;
  documentId: string;
  type: 'quantitative' | 'qualitative' | 'mixed';
  tables: DataTable[];
  figures: DataFigure[];
  statistics: StatisticalResult[];
  qualitativeFindings: QualitativeResult[];
  extractedAt: Date;
  confidence: number;
}

export interface DataTable {
  id: string;
  title: string;
  caption: string;
  headers: string[];
  rows: (string | number)[][];
  metadata: TableMetadata;
}

export interface TableMetadata {
  variables: string[];
  sampleSize?: number;
  units: string[];
  notes: string[];
}

export interface DataFigure {
  id: string;
  title: string;
  type: 'chart' | 'graph' | 'diagram' | 'image';
  description: string;
  extractedText: string[];
  metadata: FigureMetadata;
}

export interface FigureMetadata {
  axes?: { x: string; y: string };
  scale?: string;
  methodology?: string;
  sampleSize?: number;
}

export interface StatisticalResult {
  id: string;
  test: string;
  statistic: number;
  pValue?: number;
  confidenceInterval?: [number, number];
  effectSize?: number;
  interpretation: string;
  context: string;
}

export interface QualitativeResult {
  id: string;
  theme: string;
  description: string;
  supportingQuotes: string[];
  frequency: number;
  categories: string[];
}

// AI and Generation Types
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  strengths: string[];
  bestFor: string[];
  maxTokens: number;
  pricing: 'free' | 'low' | 'medium' | 'high';
}

export interface AIGenerationOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

export interface GenerationProgress {
  stage: string;
  progress: number;
  message: string;
  estimatedTimeRemaining?: number;
}

// UI State Types
export interface ResearchAnalysisState {
  documents: ResearchDocument[];
  selectedDocuments: string[];
  currentView: 'dashboard' | 'upload' | 'library' | 'literature-review' | 'gaps' |
                'hypotheses' | 'methodology' | 'citations' | 'planner' |
                'knowledge-base' | 'data-extraction' | 'history';
  isProcessing: boolean;
  processingMessage: string;
  generationProgress?: GenerationProgress;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
  filters: {
    years: [number, number];
    authors: string[];
    journals: string[];
    tags: string[];
    categories: string[];
  };
  searchQuery: string;
  searchResults: ResearchDocument[];
}

// Analysis Results Types
export interface AnalysisResults {
  literatureReviews: LiteratureReview[];
  gapAnalyses: GapAnalysis[];
  hypotheses: ResearchHypothesis[];
  methodologyAnalyses: MethodologyAnalysis[];
  citationNetworks: CitationNetwork[];
  researchProjects: ResearchProject[];
  knowledgeBases: KnowledgeBase[];
  extractedData: ExtractedData[];
}

// Export and Integration Types
export interface ExportOptions {
  format: ExportFormat;
  includeSections: string[];
  citationStyle?: CitationStyle;
  includeAppendices: boolean;
  includeRawData: boolean;
}

export interface IntegrationSettings {
  mendeley?: { apiKey: string; enabled: boolean };
  zotero?: { apiKey: string; enabled: boolean };
  orcid?: { id: string; enabled: boolean };
  crossref?: { enabled: boolean };
  pubmed?: { enabled: boolean };
}

// Notification and Progress Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  type: 'primary' | 'secondary';
}

export interface ProgressTracker {
  id: string;
  title: string;
  description: string;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'error';
  startTime: Date;
  estimatedEndTime?: Date;
  steps: ProgressStep[];
}

export interface ProgressStep {
  name: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  progress: number;
  message: string;
}
