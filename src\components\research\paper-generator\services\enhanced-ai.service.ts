import { AIGenerationOptions } from './types';

/**
 * Wraps any prompt with academic writing instructions to ensure proper formatting
 */
function wrapAcademicPrompt(
  corePrompt: string,
  sectionHeading: string,
  includeReferences = true,
  citationStyle = 'APA'
): string {
  const refInstruction = includeReferences
    ? `\n- At the end, include a "References" section listing all cited works in ${citationStyle} format.`
    : '';
  
  return `
You are a professional academic writing assistant. Follow these instructions precisely:

FORMATTING RULES:
- Do NOT use any markdown syntax (no #, ##, *, -, etc.)
- Write in plain text with proper paragraph breaks
- Begin with the section heading as a plain line: "${sectionHeading}"
- Use formal, objective academic tone suitable for peer-reviewed journals
- Write complete, well-structured paragraphs

CITATION REQUIREMENTS:
- Include relevant inline citations in ${citationStyle} format (e.g., Smith, 2022; <PERSON> & <PERSON>, 2023)
- Use recent, realistic academic sources (2018-2024)
- Ensure citations support key claims and statements${refInstruction}

CONTENT REQUIREMENTS:
- Focus on substance and academic rigor
- Avoid unnecessary explanations or meta-commentary
- Provide direct, actionable content
- Maintain scholarly objectivity

${corePrompt}
`.trim();
}

/**
 * Creates a concise prompt for quick text improvements without full academic structure
 */
function wrapEnhancementPrompt(corePrompt: string): string {
  return `
You are an academic writing editor. Follow these rules:
- Return ONLY the improved text without explanations
- Do NOT use markdown syntax (no #, *, etc.)
- Maintain formal academic tone
- Keep the original meaning intact
- Make minimal but effective improvements

${corePrompt}
`.trim();
}

export interface AIModel {
  id: string;
  name: string;
  provider: string;
  maxTokens: number;
  supportsImages: boolean;
  cost: 'low' | 'medium' | 'high';
  description: string;
  category: 'fast' | 'balanced' | 'premium';
}

export const AI_MODELS: AIModel[] = [
  // Google Models
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash (Default)",
    provider: "Google",
    maxTokens: 8192,
    supportsImages: true,
    cost: 'low',
    description: "Latest fast model with improved performance for writing tasks",
    category: 'fast'
  },
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    provider: "Google",
    maxTokens: 8192,
    supportsImages: true,
    cost: 'low',
    description: "Fast and efficient model for quick writing and analysis",
    category: 'fast'
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "Google",
    maxTokens: 8192,
    supportsImages: true,
    cost: 'medium',
    description: "Advanced reasoning for complex research tasks",
    category: 'premium'
  },
  
  // Anthropic Models
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    maxTokens: 4096,
    supportsImages: true,
    cost: 'high',
    description: "Excellent for academic writing and analysis",
    category: 'premium'
  },
  {
    id: "anthropic/claude-3.5-haiku",
    name: "Claude 3.5 Haiku",
    provider: "Anthropic",
    maxTokens: 4096,
    supportsImages: false,
    cost: 'low',
    description: "Fast and cost-effective for simple tasks",
    category: 'fast'
  },
  
  // OpenAI Models
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    maxTokens: 4096,
    supportsImages: true,
    cost: 'high',
    description: "Most capable model for complex reasoning",
    category: 'premium'
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    maxTokens: 4096,
    supportsImages: true,
    cost: 'medium',
    description: "Balanced performance and cost",
    category: 'balanced'
  }
];

export interface ResearchTool {
  id: string;
  name: string;
  description: string;
  category: 'generation' | 'enhancement' | 'analysis' | 'review';
  icon: string;
  prompt: (context?: any) => string;
  requiresSelection: boolean;
  mode: 'replace' | 'insert' | 'display';
}

export const RESEARCH_TOOLS: ResearchTool[] = [
  // Content Generation Tools
  {
    id: 'generate-title',
    name: 'Generate Title',
    description: 'Create compelling research paper titles',
    category: 'generation',
    icon: 'Type',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Based on the following research content, generate 5 compelling and academically appropriate titles for this research paper. Make them specific, clear, and engaging:

${context || 'Research paper content'}

Provide only the titles, numbered 1-5, without additional explanation.`,
      'Suggested Titles',
      false
    )
  },
  {
    id: 'generate-keywords',
    name: 'Generate Keywords',
    description: 'Extract relevant keywords and phrases',
    category: 'generation',
    icon: 'Target',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Analyze the following research content and extract 8-12 relevant keywords and key phrases that would be appropriate for academic indexing:

${context || 'Research paper content'}

Provide only the keywords, separated by commas, without additional explanation.`,
      'Keywords',
      false
    )
  },
  {
    id: 'write-literature-review',
    name: 'Literature Review',
    description: 'Generate comprehensive literature review',
    category: 'generation',
    icon: 'BookOpen',
    requiresSelection: false,
    mode: 'display',
    prompt: (topic) => wrapAcademicPrompt(
      `Write a comprehensive literature review on the topic: "${topic}". 

Structure your review to include:
- Current state of research in this field with supporting citations
- Key theoretical frameworks and models with proper attribution
- Major empirical findings and ongoing debates in the literature
- Identification of research gaps and limitations in existing studies
- Synthesis of how different studies relate to each other

Length: 1000-1500 words. Ensure each major claim is supported by appropriate citations.`,
      'Literature Review',
      true
    )
  },
  {
    id: 'write-introduction',
    name: 'Write Introduction',
    description: 'Craft engaging research introduction',
    category: 'generation',
    icon: 'Sparkles',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Write a compelling introduction for a research paper based on this content:

${context}

Structure your introduction following academic conventions:
- Begin with broad contextual background, citing relevant foundational studies
- Progressively narrow focus to identify the specific research problem or gap
- Clearly articulate the research objectives, questions, or hypotheses
- Briefly preview the methodological approach
- Establish the significance and potential contributions of this research
- Conclude with a clear thesis statement or research proposition

Length: 500-800 words. Ensure proper flow between paragraphs and include supporting citations for key background claims.`,
      'Introduction',
      true
    )
  },
  {
    id: 'enhance-methodology',
    name: 'Enhance Methodology',
    description: 'Improve and expand methodology section',
    category: 'enhancement',
    icon: 'FlaskConical',
    requiresSelection: true,
    mode: 'replace',
    prompt: (content) => wrapAcademicPrompt(
      `Enhance this methodology section by adding more detail, clarity, and academic rigor:

${content}

Improve by:
- Adding specific details about research procedures and protocols
- Clarifying data collection methods with proper justification
- Explaining analytical approaches and statistical methods
- Addressing potential limitations and validity concerns
- Including sample size calculations or power analysis where relevant
- Using precise academic terminology and citing methodological sources

Ensure the enhanced methodology provides sufficient detail for replication.`,
      'Methodology',
      true
    )
  },
  {
    id: 'suggest-methodology',
    name: 'Suggest Methodology',
    description: 'Propose new methodological approaches',
    category: 'generation',
    icon: 'Lightbulb',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Based on this research context, suggest appropriate methodological approaches:

${context}

Provide a comprehensive methodological analysis including:
- 3-4 different methodological options with theoretical justification
- Detailed explanation of each approach including data collection and analysis methods
- Critical evaluation of advantages and limitations for each approach
- Consideration of ethical implications and practical constraints
- Evidence-based recommendation for the most suitable method with supporting citations
- Discussion of how the chosen methodology aligns with research objectives

Focus on methodologically sound, implementable approaches that are well-established in the literature.`,
      'Methodological Recommendations',
      true
    )
  },
  {
    id: 'find-similar-articles',
    name: 'Find Similar Articles',
    description: 'Suggest related research papers',
    category: 'analysis',
    icon: 'Search',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => `Based on this research content, suggest similar academic articles and research papers that would be relevant:

${context}

Provide:
- 8-10 relevant paper titles with hypothetical but realistic authors and publication years
- Brief description of how each relates to the current research
- Key concepts that connect them

Format as a bibliography-style list.`
  },
  {
    id: 'write-discussion',
    name: 'Write Discussion',
    description: 'Generate comprehensive discussion section',
    category: 'generation',
    icon: 'TrendingUp',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Write a comprehensive discussion section based on these results and methodology:

${context}

Structure your discussion to include:
- Interpretation of key findings in the context of existing literature with comparative analysis
- Direct addressing of original research questions or hypotheses with evidence-based conclusions
- Discussion of theoretical and practical implications of the results
- Critical analysis of how findings contribute to or challenge current understanding
- Honest acknowledgment of study limitations and their potential impact on conclusions
- Specific suggestions for future research directions based on identified gaps or questions
- Consideration of broader significance and potential applications

Length: 800-1200 words. Ensure each interpretation is supported by appropriate citations and logical reasoning.`,
      'Discussion',
      true
    )
  },
  {
    id: 'write-conclusion',
    name: 'Write Conclusion',
    description: 'Create strong conclusion section',
    category: 'generation',
    icon: 'CheckCircle',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Write a strong conclusion section based on this research:

${context}

Structure your conclusion to include:
- Concise summary of the most important findings and their significance
- Restatement of how the research addresses the original objectives or hypotheses
- Clear articulation of the main theoretical and practical contributions to the field
- Discussion of potential applications and implications for practice or policy
- Acknowledgment of key limitations that may affect generalizability
- Specific recommendations for future research based on this study's findings and limitations
- Final statement on the broader impact and importance of this work

Length: 300-500 words. Be concise yet comprehensive, avoiding repetition of detailed results.`,
      'Conclusion',
      false
    )
  },
  {
    id: 'write-abstract',
    name: 'Write Abstract',
    description: 'Generate comprehensive abstract',
    category: 'generation',
    icon: 'FileText',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => wrapAcademicPrompt(
      `Write a comprehensive abstract for this research paper:

${context}

Structure your abstract following standard academic format:
- Background and objective: Briefly establish the research context and clearly state the study's purpose (1-2 sentences)
- Methods: Concisely describe the research design, participants/materials, and analytical approach (1-2 sentences)
- Key results: Present the most important findings with specific details or statistics where appropriate (2-3 sentences)
- Conclusions and implications: Summarize the main conclusions and their significance for theory, practice, or future research (1-2 sentences)

Length: 200-300 words. Ensure the abstract can stand alone and provides a complete overview of the study. Avoid citations in the abstract.`,
      'Abstract',
      false
    )
  },
  {
    id: 'write-results-discussion',
    name: 'Results & Discussion',
    description: 'Combined results and discussion section',
    category: 'generation',
    icon: 'BarChart3',
    requiresSelection: false,
    mode: 'display',
    prompt: (context) => `Write a combined Results and Discussion section based on this research:

${context}

Structure:
- Present results clearly with subheadings
- Immediately discuss implications of each result
- Compare with existing literature
- Address limitations where relevant

Length: 1200-1800 words. Integrate results presentation with analytical discussion.`
  },
  
  // Review and Analysis Tools
  {
    id: 'review-article',
    name: 'Review Complete Article',
    description: 'Comprehensive article review and feedback',
    category: 'review',
    icon: 'FileCheck',
    requiresSelection: false,
    mode: 'display',
    prompt: (content) => `Provide a comprehensive academic review of this research paper:

${content}

Review criteria:
1. Clarity and organization
2. Argument strength and logic
3. Evidence and methodology
4. Academic writing quality
5. Contribution to field

Provide specific, actionable feedback with suggestions for improvement.`
  },
  {
    id: 'review-introduction',
    name: 'Review Introduction',
    description: 'Focused review of introduction section',
    category: 'review',
    icon: 'Sparkles',
    requiresSelection: true,
    mode: 'display',
    prompt: (content) => `Review this introduction section and provide specific feedback:

${content}

Evaluate:
- Hook and engagement
- Context and background
- Problem statement clarity
- Research objectives
- Structure and flow

Provide actionable suggestions for improvement.`
  },
  {
    id: 'review-abstract',
    name: 'Review Abstract',
    description: 'Evaluate abstract completeness and clarity',
    category: 'review',
    icon: 'FileText',
    requiresSelection: true,
    mode: 'display',
    prompt: (content) => `Review this abstract and provide detailed feedback:

${content}

Check for:
- All essential elements (background, methods, results, conclusions)
- Clarity and conciseness
- Appropriate length
- Standalone comprehensibility

Provide specific suggestions for improvement.`
  },
  {
    id: 'review-conclusion',
    name: 'Review Conclusion',
    description: 'Assess conclusion effectiveness',
    category: 'review',
    icon: 'CheckCircle',
    requiresSelection: true,
    mode: 'display',
    prompt: (content) => `Review this conclusion section:

${content}

Evaluate:
- Summary of key findings
- Research significance
- Contribution clarity
- Future research suggestions
- Overall closure

Provide feedback on effectiveness and completeness.`
  },
  
  // Quick Enhancement Tools
  {
    id: 'improve-clarity',
    name: 'Improve Clarity',
    description: 'Make text clearer and more concise',
    category: 'enhancement',
    icon: 'Edit',
    requiresSelection: true,
    mode: 'replace',
    prompt: (content) => wrapEnhancementPrompt(
      `Improve the clarity and conciseness of this text while maintaining academic tone:

${content}

Make it clearer, more direct, and easier to understand without losing meaning or formality.`
    )
  },
  {
    id: 'fix-grammar',
    name: 'Fix Grammar',
    description: 'Correct grammar and style issues',
    category: 'enhancement',
    icon: 'CheckCircle',
    requiresSelection: true,
    mode: 'replace',
    prompt: (content) => wrapEnhancementPrompt(
      `Fix grammar, spelling, punctuation, and improve academic writing style:

${content}`
    )
  },
  {
    id: 'academic-tone',
    name: 'Academic Tone',
    description: 'Convert to formal academic language',
    category: 'enhancement',
    icon: 'PenTool',
    requiresSelection: true,
    mode: 'replace',
    prompt: (content) => wrapEnhancementPrompt(
      `Rewrite this text in formal academic language suitable for scholarly publication:

${content}

Use objective tone, precise terminology, and proper academic conventions.`
    )
  },
  {
    id: 'expand-point',
    name: 'Expand Point',
    description: 'Add depth and supporting details',
    category: 'enhancement',
    icon: 'AlignLeft',
    requiresSelection: true,
    mode: 'replace',
    prompt: (content) => wrapEnhancementPrompt(
      `Expand this point with additional detail, examples, or analysis:

${content}

Add scholarly depth while maintaining focus and relevance. Include supporting citations where appropriate.`
    )
  }
];

/**
 * Enhanced AI service with support for multiple models and research-specific tools
 */
export class EnhancedAIService {
  private apiKey: string;
  private defaultModel: string = "google/gemini-2.5-flash";
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || 
                 import.meta.env.VITE_AI_API_KEY || 
                 import.meta.env.VITE_OPENAI_API_KEY || 
                 import.meta.env.VITE_GEMINI_API_KEY || 
                 import.meta.env.VITE_API_KEY || '';
    
    console.log('EnhancedAIService initialized with default model:', this.defaultModel);
  }
  
  /**
   * Execute a research tool with the specified model
   */
  async executeResearchTool(
    toolId: string, 
    context: string, 
    model: string = this.defaultModel,
    additionalOptions: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    const tool = RESEARCH_TOOLS.find(t => t.id === toolId);
    if (!tool) {
      throw new Error(`Research tool '${toolId}' not found`);
    }
    
    const prompt = tool.prompt(context);
    return this.generateText(prompt, model, {
      maxTokens: 2048,
      temperature: 0.7,
      ...additionalOptions
    });
  }
  
  /**
   * Generate text with specified model and options
   */
  async generateText(
    prompt: string, 
    model: string = this.defaultModel, 
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    if (!this.hasValidApiKey()) {
      throw new Error('No valid API key configured. Please set up your OpenRouter API key.');
    }
    
    const modelInfo = AI_MODELS.find(m => m.id === model);
    const maxTokens = Math.min(options.maxTokens || 1024, modelInfo?.maxTokens || 4096);
    
    try {
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "Paper Genius AI Assistant"
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert academic writing assistant specializing in scholarly research papers. Always provide precise, professional responses in proper academic format. Use clear formatting: # for main headings, ## for subheadings, ### for sub-subheadings, **text** for bold emphasis, *text* for italics, and - for bullet points. Write in complete paragraphs with proper spacing. Focus on substance and academic rigor."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error:', response.status, errorText);
        throw new Error(`API Error (${response.status}): ${errorText.substring(0, 100)}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      if (!content.trim()) {
        throw new Error('Empty response from AI model');
      }
      
      return content.trim();
    } catch (error: any) {
      console.error('AI generation error:', error);
      throw new Error(error.message || 'Failed to generate content');
    }
  }
  
  /**
   * Quick enhancement tools - minimal responses
   */
  async quickEnhance(
    text: string,
    enhancement: 'clarity' | 'grammar' | 'academic' | 'expand',
    model: string = this.defaultModel
  ): Promise<string> {
    // Check API key first
    if (!this.hasValidApiKey()) {
      console.warn('No valid API key configured for quickEnhance');
      return text; // Return original text if no API key
    }

    const prompts = {
      clarity: `Make this clearer and more concise:\n\n${text}`,
      grammar: `Fix grammar and style:\n\n${text}`,
      academic: `Convert to academic tone:\n\n${text}`,
      expand: `Expand with more detail:\n\n${text}`
    };

    const systemPrompt = "You are an academic writing editor. Return only the improved text without explanations, comments, or additional formatting. Never use markdown syntax (no #, *, etc.). Use HTML tags for formatting if needed (like <strong>, <em>). Maintain formal academic tone.";

    try {
      console.log('Making quickEnhance request:', { model, enhancement, hasApiKey: !!this.apiKey });

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "Paper Genius AI Assistant"
        },
        body: JSON.stringify({
          model,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: prompts[enhancement] }
          ],
          max_tokens: Math.min(text.length * 2, 1024),
          temperature: 0.3
        })
      });

      console.log('API Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error details:', errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('API Response data:', data);

      return data.choices?.[0]?.message?.content?.trim() || text;
    } catch (error) {
      console.error('Quick enhance error:', error);
      return text; // Return original text on error
    }
  }
  
  /**
   * Chat completion for conversational AI
   */
  async chatCompletion(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    model: string = this.defaultModel
  ): Promise<string> {
    if (!this.hasValidApiKey()) {
      throw new Error('No valid API key configured');
    }
    
    try {
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "Paper Genius AI Assistant"
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert research writing assistant. Help users with academic writing, research methodology, paper structure, and scholarly communication. Be helpful, accurate, and professional."
            },
            ...messages
          ],
          max_tokens: 1024,
          temperature: 0.7
        })
      });
      
      if (!response.ok) {
        throw new Error(`Chat API Error: ${response.status}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '';
    } catch (error: any) {
      console.error('Chat completion error:', error);
      throw new Error(error.message || 'Failed to get AI response');
    }
  }
  
  /**
   * Check if API key is valid
   */
  hasValidApiKey(): boolean {
    return !!this.apiKey && 
           !this.apiKey.includes('your_') && 
           this.apiKey.length > 20;
  }
  
  /**
   * Get available models
   */
  getAvailableModels(): AIModel[] {
    return AI_MODELS;
  }
  
  /**
   * Get research tools by category
   */
  getResearchToolsByCategory(category?: string): ResearchTool[] {
    if (!category) return RESEARCH_TOOLS;
    return RESEARCH_TOOLS.filter(tool => tool.category === category);
  }
  
  /**
   * Set default model
   */
  setDefaultModel(model: string): void {
    if (AI_MODELS.some(m => m.id === model)) {
      this.defaultModel = model;
    }
  }
  
  /**
   * Get current default model
   */
  getDefaultModel(): string {
    return this.defaultModel;
  }
}

export const enhancedAIService = new EnhancedAIService();
export default enhancedAIService;
