import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Database } from "lucide-react";

import { ResearchDocument, KnowledgeBase as KnowledgeBaseType, AIGenerationOptions } from '../types';

interface KnowledgeBaseProps {
  documents: ResearchDocument[];
  onCreateBase: (base: KnowledgeBaseType) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function KnowledgeBase({ documents }: KnowledgeBaseProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Database className="h-6 w-6 text-cyan-500" />
          Knowledge Base
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Knowledge base feature coming soon!</p>
          <Badge variant="secondary">
            {documents.length} documents selected
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
