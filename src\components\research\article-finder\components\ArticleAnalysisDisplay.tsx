/**
 * Article Analysis Display Component
 * Shows the AI analysis results of the submitted article
 */

import React from 'react';
import { ArticleAnalysisDisplayProps } from '../types';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Brain, 
  Target, 
  TrendingUp, 
  BookOpen, 
  Lightbulb,
  Award,
  Clock,
  CheckCircle
} from "lucide-react";

export const ArticleAnalysisDisplay: React.FC<ArticleAnalysisDisplayProps> = ({
  analysis,
  isLoading
}) => {
  if (isLoading) {
    return <AnalysisLoadingSkeleton />;
  }

  // Calculate journal metrics to avoid JSX parsing issues
  const highImpactCount = analysis?.recommendedJournals?.filter(j => j.metrics.impactFactor > 3).length || 0;
  const openAccessCount = analysis?.recommendedJournals?.filter(j => j.metrics.isOpenAccess).length || 0;
  const fastReviewCount = analysis?.recommendedJournals?.filter(j => j.metrics.averageReviewTime < 90).length || 0;

  if (!analysis) {
    return null;
  }

  /**
   * Get novelty score color
   */
  const getNoveltyColor = (score: number): string => {
    if (score >= 0.8) return 'text-green-600 bg-green-100';
    if (score >= 0.6) return 'text-blue-600 bg-blue-100';
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  /**
   * Get contribution level color
   */
  const getContributionColor = (level: string): string => {
    switch (level.toLowerCase()) {
      case 'high': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  /**
   * Get confidence color
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-blue-600';
    if (confidence >= 0.4) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Brain className="w-8 h-8 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
        </div>
        <p className="text-gray-600">
          AI analysis of your research content and journal recommendations
        </p>
      </div>

      {/* Analysis Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Research Domain */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <Target className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Research Domain</h3>
            </div>
            <p className="text-sm text-gray-600">{analysis.researchDomain}</p>
          </CardContent>
        </Card>

        {/* Academic Field */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <BookOpen className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Academic Field</h3>
            </div>
            <p className="text-sm text-gray-600">{analysis.academicField}</p>
          </CardContent>
        </Card>

        {/* Novelty Score */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <Lightbulb className="w-5 h-5 text-yellow-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Novelty Score</h3>
            </div>
            <div className="flex items-center">
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getNoveltyColor(analysis.noveltyScore)}`}>
                {Math.round(analysis.noveltyScore * 100)}%
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contribution Level */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center mb-2">
              <Award className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="font-semibold text-gray-900">Contribution</h3>
            </div>
            <Badge className={getContributionColor(analysis.contributionLevel)}>
              {analysis.contributionLevel}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Methodology */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 text-blue-600 mr-2" />
              Methodology
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{analysis.methodology}</p>
          </CardContent>
        </Card>

        {/* Analysis Confidence */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              Analysis Confidence
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Confidence Level</span>
                <span className={`text-sm font-semibold ${getConfidenceColor(analysis.analysisConfidence)}`}>
                  {Math.round(analysis.analysisConfidence * 100)}%
                </span>
              </div>
              <Progress 
                value={analysis.analysisConfidence * 100} 
                className="h-2"
              />
              <p className="text-xs text-gray-500">
                Based on content quality, domain clarity, and available data
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Topics and Themes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Key Topics */}
        <Card>
          <CardHeader>
            <CardTitle>Key Topics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {analysis.keyTopics.map((topic, index) => (
                <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                  {topic}
                </Badge>
              ))}
            </div>
            {analysis.keyTopics.length === 0 && (
              <p className="text-gray-500 text-sm">No key topics identified</p>
            )}
          </CardContent>
        </Card>

        {/* Research Themes */}
        <Card>
          <CardHeader>
            <CardTitle>Research Themes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {analysis.researchThemes.map((theme, index) => (
                <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                  {theme}
                </Badge>
              ))}
            </div>
            {analysis.researchThemes.length === 0 && (
              <p className="text-gray-500 text-sm">No research themes identified</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Journal Recommendations Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Journal Recommendations</span>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              {analysis.recommendedJournals.length} journals found
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* High Impact Journals */}
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {highImpactCount}
              </div>
              <div className="text-sm text-green-700">High Impact (IF &gt; 3)</div>
            </div>

            {/* Open Access Journals */}
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {openAccessCount}
              </div>
              <div className="text-sm text-blue-700">Open Access</div>
            </div>

            {/* Fast Review Journals */}
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {fastReviewCount}
              </div>
              <div className="text-sm text-purple-700">Fast Review (&lt;3 months)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Timestamp */}
      <div className="text-center text-sm text-gray-500">
        <Clock className="w-4 h-4 inline mr-1" />
        Analysis completed on {analysis.generatedAt.toLocaleDateString()} at {analysis.generatedAt.toLocaleTimeString()}
      </div>
    </div>
  );
};

/**
 * Loading skeleton for analysis display
 */
const AnalysisLoadingSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="text-center">
        <Skeleton className="h-8 w-64 mx-auto mb-4" />
        <Skeleton className="h-4 w-96 mx-auto" />
      </div>

      {/* Overview Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <Skeleton className="h-5 w-32 mb-2" />
              <Skeleton className="h-4 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analysis Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Topics Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-24" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {[...Array(5)].map((_, j) => (
                  <Skeleton key={j} className="h-6 w-16" />
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
