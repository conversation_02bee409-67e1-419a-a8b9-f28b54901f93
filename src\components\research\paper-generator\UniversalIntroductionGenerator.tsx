/**
 * Universal Introduction Generator Component
 * Enhanced introduction generation with source tracking for any research topic
 */

import React, { useState } from 'react';
import { 
  universalIntroductionGeneratorService, 
  ResearchContext, 
  IntroductionResult,
  ContentSource,
  SearchStep 
} from './services/universal-introduction-generator.service';

interface UniversalIntroductionGeneratorProps {
  context: ResearchContext;
  onIntroductionGenerated: (introduction: string, sources: ContentSource[]) => void;
  className?: string;
}

export const UniversalIntroductionGenerator: React.FC<UniversalIntroductionGeneratorProps> = ({
  context,
  onIntroductionGenerated,
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<IntroductionResult | null>(null);
  const [showSources, setShowSources] = useState(false);
  const [showSearchLog, setShowSearchLog] = useState(false);
  const [selectedSource, setSelectedSource] = useState<ContentSource | null>(null);
  const [generationStep, setGenerationStep] = useState<string>('');

  const handleGenerateIntroduction = async () => {
    setIsGenerating(true);
    setGenerationStep('Analyzing your research context...');
    
    try {
      // Step-by-step generation with progress updates
      setGenerationStep('Creating introduction outline...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Show progress
      
      setGenerationStep('Searching for academic sources...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setGenerationStep('Extracting bibliography...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setGenerationStep('Writing detailed introduction...');
      
      const introResult = await universalIntroductionGeneratorService.generateUniversalIntroduction(
        context,
        {
          minSources: 15,
          maxLength: 1500,
          includeSourceTracking: true
        }
      );

      setResult(introResult);
      onIntroductionGenerated(introResult.sourcedContent.content, introResult.sourcedContent.sources);
      setGenerationStep('');
      
    } catch (error) {
      console.error('Introduction generation failed:', error);
      setGenerationStep('Generation failed. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const renderOutlineSection = (section: any, index: number) => (
    <div key={section.id} className="mb-4 p-4 bg-gray-50 rounded-lg">
      <h4 className="font-semibold text-gray-800 mb-2">
        {index + 1}. {section.title}
      </h4>
      <p className="text-gray-600 text-sm mb-2">{section.description}</p>
      <div className="flex items-center text-xs text-gray-500">
        <span className="mr-4">Sources needed: {section.requiredSources}</span>
        <span>Search terms: {section.searchTerms.join(', ')}</span>
      </div>
    </div>
  );

  const renderSourceCard = (source: ContentSource, index: number) => (
    <div 
      key={source.id} 
      className="mb-3 p-4 bg-white border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => setSelectedSource(source)}
    >
      <h4 className="font-semibold text-gray-800 mb-2 text-sm">
        [{index + 1}] {source.title}
      </h4>
      <p className="text-gray-600 text-xs mb-2">
        {source.authors.join(', ')} ({source.year})
      </p>
      <p className="text-gray-500 text-xs mb-2">
        {source.relevantText.substring(0, 150)}...
      </p>
      <div className="flex items-center justify-between text-xs">
        <span className="text-blue-600">Confidence: {source.confidence.toFixed(2)}</span>
        {source.usedInSentences.length > 0 && (
          <span className="text-green-600">Used in {source.usedInSentences.length} sentences</span>
        )}
      </div>
    </div>
  );

  const renderSearchStep = (step: SearchStep, index: number) => (
    <div key={index} className="mb-2 p-3 bg-gray-50 rounded text-sm">
      <div className="flex items-center justify-between mb-1">
        <span className="font-medium">Step {step.step}: {step.query}</span>
        <span className="text-gray-500">{step.timestamp.toLocaleTimeString()}</span>
      </div>
      <div className="text-gray-600 text-xs">
        Found: {step.resultsFound} results → {step.relevantSources} relevant sources
      </div>
    </div>
  );

  const renderQualityMetrics = () => {
    if (!result) return null;
    
    const { qualityMetrics } = result;
    
    return (
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{qualityMetrics.totalSources}</div>
          <div className="text-xs text-gray-600">Total Sources</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{qualityMetrics.academicSources}</div>
          <div className="text-xs text-gray-600">Academic Sources</div>
        </div>
        <div className="text-center p-3 bg-purple-50 rounded-lg">
          <div className="text-2xl font-bold text-purple-600">{qualityMetrics.averageRelevance.toFixed(2)}</div>
          <div className="text-xs text-gray-600">Avg Relevance</div>
        </div>
        <div className="text-center p-3 bg-orange-50 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">{(qualityMetrics.coverageScore * 100).toFixed(0)}%</div>
          <div className="text-xs text-gray-600">Coverage Score</div>
        </div>
        <div className="text-center p-3 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{qualityMetrics.citationDensity.toFixed(1)}</div>
          <div className="text-xs text-gray-600">Citations/100 words</div>
        </div>
      </div>
    );
  };

  return (
    <div className={`universal-introduction-generator ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">
          Universal Introduction Generator
        </h3>
        
        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">Research Context</h4>
          <p className="text-blue-700 text-sm mb-2">
            <strong>Title:</strong> {context.title}
          </p>
          <p className="text-blue-700 text-sm mb-2">
            <strong>Field:</strong> {context.researchField}
          </p>
          <p className="text-blue-700 text-sm">
            <strong>Keywords:</strong> {context.keywords.join(', ')}
          </p>
        </div>

        <button
          onClick={handleGenerateIntroduction}
          disabled={isGenerating}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isGenerating ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
              {generationStep || 'Generating Introduction...'}
            </div>
          ) : (
            'Generate Comprehensive Introduction'
          )}
        </button>
      </div>

      {result && (
        <div className="space-y-6">
          {/* Quality Metrics */}
          {renderQualityMetrics()}

          {/* Generated Introduction */}
          <div className="bg-white border rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-800">Generated Introduction</h4>
              <div className="text-sm text-gray-600">
                {result.sourcedContent.wordCount} words • {result.sourcedContent.citationCount} citations
              </div>
            </div>
            <div className="prose max-w-none text-gray-700 leading-relaxed">
              {result.sourcedContent.content.split('\n').map((paragraph, index) => (
                <p key={index} className="mb-4">{paragraph}</p>
              ))}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex space-x-4 border-b">
            <button
              onClick={() => setShowSources(!showSources)}
              className={`py-2 px-4 border-b-2 transition-colors ${
                showSources ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-600 hover:text-gray-800'
              }`}
            >
              Sources ({result.bibliography.length})
            </button>
            <button
              onClick={() => setShowSearchLog(!showSearchLog)}
              className={`py-2 px-4 border-b-2 transition-colors ${
                showSearchLog ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-600 hover:text-gray-800'
              }`}
            >
              Search Log ({result.searchLog.length} steps)
            </button>
          </div>

          {/* Sources Panel */}
          {showSources && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">Bibliography & Sources</h4>
              <div className="grid gap-4">
                {result.bibliography.map((source, index) => renderSourceCard(source, index))}
              </div>
            </div>
          )}

          {/* Search Log Panel */}
          {showSearchLog && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">Search Process Log</h4>
              <div className="space-y-2">
                {result.searchLog.map((step, index) => renderSearchStep(step, index))}
              </div>
            </div>
          )}

          {/* Outline Display */}
          <div className="bg-white border rounded-lg p-6">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Introduction Outline</h4>
            <div className="space-y-4">
              {result.outline.sections.map((section, index) => renderOutlineSection(section, index))}
            </div>
          </div>
        </div>
      )}

      {/* Source Detail Modal */}
      {selectedSource && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Source Details</h3>
              <button
                onClick={() => setSelectedSource(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-800">{selectedSource.title}</h4>
                <p className="text-gray-600">{selectedSource.authors.join(', ')} ({selectedSource.year})</p>
              </div>
              <div>
                <h5 className="font-medium text-gray-700 mb-2">Abstract/Content:</h5>
                <p className="text-gray-600 text-sm">{selectedSource.relevantText}</p>
              </div>
              <div>
                <h5 className="font-medium text-gray-700 mb-2">URL:</h5>
                <a 
                  href={selectedSource.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline text-sm break-all"
                >
                  {selectedSource.url}
                </a>
              </div>
              {selectedSource.usedInSentences.length > 0 && (
                <div>
                  <h5 className="font-medium text-gray-700 mb-2">Used in sentences:</h5>
                  <ul className="list-disc list-inside space-y-1">
                    {selectedSource.usedInSentences.map((sentence, index) => (
                      <li key={index} className="text-gray-600 text-sm">{sentence}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UniversalIntroductionGenerator;
