# Flowchart Fun Module

A powerful text-to-flowchart visualization tool integrated into the AI research platform. Create beautiful, interactive flowcharts using simple text syntax with real-time preview and multiple export options.

## Features

### 🎯 Core Features
- **Text-to-Flowchart**: Convert simple text syntax into beautiful flowcharts
- **Real-time Preview**: See your flowchart update as you type
- **Interactive Editor**: Monaco-based editor with syntax highlighting and error detection
- **Multiple Layouts**: Choose from hierarchical, force-directed, grid, and circular layouts
- **Export Options**: Export to PNG, SVG, PDF, Word, JSON, and text formats
- **Theme Support**: Light and dark themes with customizable styling

### 📝 Text Syntax
The module uses an intuitive indentation-based syntax:

```
Research Process
  Literature Review: Systematic Review
    Search Strategy: Database Search
    Inclusion Criteria: Peer-reviewed articles
  Data Collection: Survey Design
    Questionnaire: Validated instruments
    Sampling: Random sampling
  Data Analysis: Statistical Analysis
    Descriptive: Mean, SD, frequencies
    Inferential: t-tests, ANOVA
  Results: Findings
  Discussion: Interpretation
  Conclusion: Summary
```

### 🤖 AI-Powered Features
- **AI Prompt Mode**: Create flowcharts from natural language descriptions
- **AI Convert Mode**: Convert existing text/documents into flowchart format
- **AI Edit Mode**: Modify and enhance existing flowcharts with AI assistance
- **Streaming Generation**: Real-time AI response streaming with Google Gemini Pro 2.5
- **Diff Preview**: Preview AI changes before applying them
- **Smart Examples**: Built-in example prompts for quick start

### 🔧 Technical Features
- **Cytoscape.js Integration**: Powerful graph visualization engine
- **Monaco Editor**: Professional code editor with IntelliSense
- **Zustand State Management**: Efficient state management with persistence
- **Google Gemini Integration**: AI-powered flowchart generation
- **TypeScript**: Full type safety and IntelliSense support
- **Responsive Design**: Works on desktop and mobile devices

## Architecture

### File Structure
```
src/components/research/flowchart-fun/
├── FlowchartFun.tsx              # Main component
├── types.ts                      # TypeScript definitions
├── constants.ts                  # Configuration and defaults
├── index.ts                      # Module exports
├── components/
│   ├── TextEditor.tsx           # Monaco-based text editor
│   ├── GraphViewer.tsx          # Cytoscape graph visualization
│   ├── ExportDialog.tsx         # Export functionality
│   └── index.ts                 # Component exports
├── services/
│   ├── text-parser.service.ts   # Text parsing logic
│   └── gemini-flowchart.service.ts # AI service integration
├── stores/
│   ├── flowchart-fun.store.ts   # Zustand state management
│   └── ai-prompt.store.ts       # AI state management
├── constants/
│   └── ai-examples.ts           # AI examples and configurations
└── README.md                    # This file
```

### Key Components

#### FlowchartFun (Main Component)
- Split-pane layout with text editor and graph viewer
- Toolbar with undo/redo, layout options, theme toggle
- Export and sharing functionality
- Real-time synchronization between text and graph

#### FlowchartTextEditor
- Monaco editor with custom syntax highlighting
- Error detection and inline error display
- Auto-formatting and text manipulation tools
- Import/export text files

#### FlowchartGraphViewer
- Cytoscape.js-based graph rendering
- Interactive node and edge selection
- Zoom, pan, and fit controls
- Multiple layout algorithms

#### FlowchartExportDialog
- Multiple export formats (PNG, SVG, PDF, Word, JSON, text)
- Customizable export options (size, quality, background)
- Progress tracking and error handling

### State Management
Uses Zustand for efficient state management with:
- Text content and parsing state
- Graph data and visualization state
- UI state (selections, cursor position, etc.)
- Export state and progress
- Settings (theme, layout, auto-save)
- History management (undo/redo)

## Usage

### Basic Usage
```tsx
import { FlowchartFun } from '@/components/research/flowchart-fun';

function MyComponent() {
  return (
    <FlowchartFun
      initialText="Start Node\n  Child Node 1\n  Child Node 2"
      onTextChange={(text) => console.log('Text changed:', text)}
      onGraphChange={(graph) => console.log('Graph changed:', graph)}
    />
  );
}
```

### Advanced Usage
```tsx
import { 
  FlowchartFun, 
  useFlowchartFunStore,
  FlowchartTextEditor,
  FlowchartGraphViewer 
} from '@/components/research/flowchart-fun';

function AdvancedComponent() {
  const { parsedGraph, setText } = useFlowchartFunStore();
  
  return (
    <div>
      <FlowchartTextEditor />
      <FlowchartGraphViewer 
        onNodeClick={(node) => console.log('Node clicked:', node)}
      />
    </div>
  );
}
```

## Integration

### Navigation Integration
The module is integrated into the research platform navigation:
- Added to `ResearchDashboard.tsx` with route handling
- Added to `Sidebar.tsx` with menu item
- Added to `AIWelcomeDashboard.tsx` as a featured tool

### Dependencies
- `cytoscape`: Graph visualization engine
- `cytoscape-dagre`: Hierarchical layout algorithm
- `cytoscape-cose-bilkent`: Force-directed layout algorithm
- `@monaco-editor/react`: Code editor component
- `allotment`: Split pane layout
- `zustand`: State management
- `nanoid`: Unique ID generation

## Configuration

### Default Settings
- **Layout**: Hierarchical (Dagre)
- **Theme**: Light
- **Auto-save**: Enabled
- **Export Format**: PNG
- **Max Nodes**: 1000
- **Max Edges**: 2000

### Customization
Settings can be customized through the store:
```tsx
const { setTheme, setLayoutAlgorithm } = useFlowchartFunStore();

// Change theme
setTheme('dark');

// Change layout
setLayoutAlgorithm('cose');
```

## Export Formats

### Image Formats
- **PNG**: High-quality raster images
- **SVG**: Scalable vector graphics
- **PDF**: Portable document format

### Document Formats
- **Word**: Microsoft Word document with embedded flowchart
- **JSON**: Machine-readable graph data
- **Text**: Plain text flowchart syntax

## Error Handling

The module includes comprehensive error handling:
- Syntax validation with inline error display
- Parse error reporting with line numbers
- Export error handling with user feedback
- Network error handling for external dependencies

## Performance

### Optimizations
- Debounced text parsing to reduce CPU usage
- Efficient graph rendering with Cytoscape.js
- Lazy loading of heavy dependencies
- Optimized state updates with Zustand

### Limits
- Maximum 1000 nodes per flowchart
- Maximum 2000 edges per flowchart
- Maximum 10 levels of indentation
- File size limits for exports

## Future Enhancements

### Planned Features
- AI-powered flowchart generation from natural language
- Collaborative editing with real-time synchronization
- Template library with pre-built flowchart patterns
- Advanced styling and theming options
- Integration with external data sources
- Version control and branching

### Potential Integrations
- Integration with existing Flow Builder module
- Connection to research paper generator for methodology diagrams
- Integration with data visualization for process flows
- Export to presentation generator for slide creation

## Contributing

When contributing to this module:
1. Follow the established TypeScript patterns
2. Add proper type definitions for new features
3. Include comprehensive error handling
4. Write unit tests for new functionality
5. Update this README for significant changes
6. Follow the existing code style and conventions

## License

This module is part of the AI Research Platform and follows the same licensing terms.
