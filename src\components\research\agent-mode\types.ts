/**
 * Type definitions for Agent Mode functionality
 * Enables contextual document editing through natural language prompts
 */

export interface DocumentStructure {
  headings: Heading[];
  paragraphs: Paragraph[];
  sections: Section[];
  wordCount: number;
  characterCount: number;
  structure: DocumentType;
  lastAnalyzed: Date;
}

export interface Heading {
  id: string;
  level: number; // 1-6 for h1-h6
  text: string;
  startPosition: number;
  endPosition: number;
  htmlTag: string; // 'h1', 'h2', etc.
}

export interface Paragraph {
  id: string;
  text: string;
  startPosition: number;
  endPosition: number;
  wordCount: number;
  sectionId?: string; // Reference to parent section
}

export interface Section {
  id: string;
  title: string;
  content: string;
  startPosition: number;
  endPosition: number;
  type: SectionType;
  headingLevel: number;
  paragraphIds: string[];
  wordCount: number;
}

export interface TargetSection {
  section: Section;
  confidence: number; // 0-1 how confident we are this matches the prompt
  reason: string; // why this section was selected
  matchedKeywords: string[];
  relevanceScore: number;
}

export interface EditInstruction {
  id: string;
  sectionId: string;
  originalText: string;
  newText: string;
  startPosition: number;
  endPosition: number;
  editType: EditType;
  confidence: number;
  reasoning: string;
  prompt: string;
}

export interface AgentModeResult {
  success: boolean;
  targetSections: TargetSection[];
  edits: EditInstruction[];
  summary: string;
  processingTime: number;
  tokensUsed?: number;
  error?: string;
  warnings?: string[];
}

export interface AgentModeRequest {
  prompt: string;
  documentContent: string;
  model: string;
  options: AgentModeOptions;
}

export interface AgentModeOptions {
  maxSections?: number; // Maximum sections to target
  confidenceThreshold?: number; // Minimum confidence for section matching
  editMode: 'conservative' | 'moderate' | 'aggressive';
  preserveFormatting?: boolean;
  includeReasoningInChanges?: boolean;
}

export interface DocumentAnalysisProgress {
  stage: AnalysisStage;
  progress: number; // 0-100
  message: string;
  currentSection?: string;
}

export interface SectionMatchResult {
  sections: TargetSection[];
  totalAnalyzed: number;
  averageConfidence: number;
  processingTime: number;
}

export interface EditGenerationResult {
  edits: EditInstruction[];
  totalEdits: number;
  averageConfidence: number;
  processingTime: number;
  tokensUsed: number;
}

// Enums and Union Types
export type DocumentType = 'academic' | 'article' | 'report' | 'essay' | 'general';

export type SectionType = 
  | 'title'
  | 'abstract' 
  | 'introduction' 
  | 'methodology' 
  | 'methods'
  | 'results' 
  | 'discussion' 
  | 'conclusion' 
  | 'references'
  | 'acknowledgments'
  | 'appendix'
  | 'chapter'
  | 'section'
  | 'subsection'
  | 'other';

export type EditType = 
  | 'replace' 
  | 'insert' 
  | 'enhance' 
  | 'restructure'
  | 'clarify'
  | 'expand'
  | 'condense';

export type AnalysisStage = 
  | 'parsing-structure'
  | 'identifying-sections'
  | 'matching-prompt'
  | 'generating-edits'
  | 'finalizing-results'
  | 'complete'
  | 'error';

// Agent Mode State Management
export interface AgentModeState {
  isActive: boolean;
  isProcessing: boolean;
  currentRequest?: AgentModeRequest;
  lastResult?: AgentModeResult;
  documentStructure?: DocumentStructure;
  progress?: DocumentAnalysisProgress;
  error?: string;
}

// UI Component Props
export interface AgentModePanelProps {
  documentContent: string;
  isVisible: boolean;
  onAgentModeRequest: (request: AgentModeRequest) => Promise<void>;
  onClose: () => void;
  isLoading: boolean;
  lastResult?: AgentModeResult;
  availableModels: string[];
  selectedModel: string;
  onModelChange: (model: string) => void;
}

// Utility Types
export interface SectionKeywords {
  [key in SectionType]: string[];
}

export interface PromptAnalysis {
  intent: string;
  targetSectionTypes: SectionType[];
  actionType: EditType;
  keywords: string[];
  confidence: number;
}

// Error Types
export interface AgentModeError {
  code: string;
  message: string;
  details?: any;
  suggestions?: string[];
}

// Constants
export const DEFAULT_AGENT_MODE_OPTIONS: AgentModeOptions = {
  maxSections: 3,
  confidenceThreshold: 0.6,
  editMode: 'moderate',
  preserveFormatting: true,
  includeReasoningInChanges: true,
};

export const SECTION_KEYWORDS: SectionKeywords = {
  title: ['title', 'heading', 'name'],
  abstract: ['abstract', 'summary', 'overview'],
  introduction: ['introduction', 'intro', 'background', 'overview'],
  methodology: ['methodology', 'methods', 'approach', 'procedure'],
  methods: ['methods', 'methodology', 'approach', 'procedure'],
  results: ['results', 'findings', 'outcomes', 'data'],
  discussion: ['discussion', 'analysis', 'interpretation'],
  conclusion: ['conclusion', 'summary', 'final', 'closing'],
  references: ['references', 'bibliography', 'citations'],
  acknowledgments: ['acknowledgments', 'thanks', 'credits'],
  appendix: ['appendix', 'supplement', 'additional'],
  chapter: ['chapter'],
  section: ['section'],
  subsection: ['subsection'],
  other: []
};
