import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Brain,
  Play,
  CheckCircle,
  Clock,
  AlertTriangle,
  BarChart3,
  Database,
  Sparkles,
  TrendingUp,
  Eye,
  Zap,
  RefreshCw
} from "lucide-react";
import { AnalysisPipeline } from './AnalysisPipeline';
import { UploadedFile, DataAnalysisResult } from '../types';

interface EnhancedAnalysisPipelineProps {
  file: UploadedFile;
  dataDescription?: string;
  onAnalysisComplete?: (result: DataAnalysisResult) => void;
  className?: string;
}

export const EnhancedAnalysisPipeline: React.FC<EnhancedAnalysisPipelineProps> = ({
  file,
  dataDescription = '',
  onAnalysisComplete,
  className = ""
}) => {
  const [isStarted, setIsStarted] = useState(false);

  const handleAnalysisComplete = (result: DataAnalysisResult) => {
    setIsStarted(false);
    if (onAnalysisComplete) {
      onAnalysisComplete(result);
    }
  };

  const handleStartAnalysis = () => {
    setIsStarted(true);
  };

  if (isStarted) {
    return (
      <AnalysisPipeline
        file={file}
        dataDescription={dataDescription}
        onAnalysisComplete={handleAnalysisComplete}
        className={className}
      />
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-indigo-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                AI-Powered Analysis
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Advanced machine learning analysis of your data patterns and insights
              </p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Analysis Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Database className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-blue-900 mb-2">Data Structure Analysis</h3>
            <p className="text-sm text-blue-700">
              Analyze data types, missing values, and statistical distributions
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-purple-900 mb-2">AI Insights Generation</h3>
            <p className="text-sm text-purple-700">
              Generate intelligent insights and identify hidden patterns
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100/50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <h3 className="font-semibold text-green-900 mb-2">Visualization Creation</h3>
            <p className="text-sm text-green-700">
              Create beautiful, interactive charts and visualizations
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analysis Configuration */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-orange-500" />
            Analysis Configuration
          </CardTitle>
          <p className="text-sm text-gray-600">
            Review your data and configuration before starting the analysis
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* File Information */}
          <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-xl">
            <h3 className="font-semibold text-gray-900 mb-3">Dataset Information</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{file.data.length}</div>
                <div className="text-sm text-gray-600">Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{file.headers.length}</div>
                <div className="text-sm text-gray-600">Columns</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {(file.size / 1024).toFixed(1)}KB
                </div>
                <div className="text-sm text-gray-600">Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">CSV</div>
                <div className="text-sm text-gray-600">Format</div>
              </div>
            </div>
          </div>

          {/* Data Description */}
          {dataDescription && (
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
              <h3 className="font-semibold text-blue-900 mb-2 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Data Context
              </h3>
              <p className="text-sm text-blue-800 leading-relaxed">{dataDescription}</p>
              <Badge variant="outline" className="mt-2 bg-blue-100 text-blue-700 border-blue-300">
                Context provided for better AI analysis
              </Badge>
            </div>
          )}

          {/* Analysis Features */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">What you'll get:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm text-green-800">Statistical analysis and data profiling</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <span className="text-sm text-blue-800">AI-generated insights and patterns</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <CheckCircle className="h-5 w-5 text-purple-600" />
                <span className="text-sm text-purple-800">Interactive visualizations and charts</span>
              </div>
              <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                <CheckCircle className="h-5 w-5 text-orange-600" />
                <span className="text-sm text-orange-800">Actionable recommendations</span>
              </div>
            </div>
          </div>

          {/* Start Analysis Button */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleStartAnalysis}
              size="lg"
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Play className="h-6 w-6 mr-3" />
              <span className="text-lg font-semibold">Start AI Analysis</span>
            </Button>
          </div>

          {/* Estimated Time */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              <span>Estimated time: 30-60 seconds</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-yellow-50 to-orange-50">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-yellow-500 rounded-lg">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-yellow-900 mb-2">Pro Tips for Better Analysis</h3>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Ensure your data has clear column headers</li>
                <li>• Provide context about your data for more relevant insights</li>
                <li>• Clean data produces better visualizations</li>
                <li>• The AI works best with at least 10 rows of data</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
