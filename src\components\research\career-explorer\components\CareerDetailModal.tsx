/**
 * Career Detail Modal Component
 * Displays comprehensive career information in a modal dialog
 */

import React from 'react';
import { CareerPath, CareerAnalysis } from '../types';
import { DIFFICULTY_LEVELS, EXPORT_FORMATS } from '../constants';
import { careerExportService } from '../services/career-export.service';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Briefcase, 
  Clock, 
  DollarSign, 
  TrendingUp,
  CheckCircle,
  Target,
  BookOpen,
  Award,
  Download,
  ExternalLink
} from "lucide-react";

interface CareerDetailModalProps {
  career: CareerPath | null;
  isOpen: boolean;
  onClose: () => void;
  onExport: (career: CareerPath, format: string) => void;
}

export function CareerDetailModal({ 
  career, 
  isOpen, 
  onClose, 
  onExport 
}: CareerDetailModalProps) {
  if (!career) return null;

  const getDifficultyColor = (difficulty: string) => {
    return DIFFICULTY_LEVELS[difficulty as keyof typeof DIFFICULTY_LEVELS]?.color || 'text-gray-600';
  };

  const handleExport = async (format: string) => {
    try {
      // Create a minimal career analysis for single career export
      const singleCareerAnalysis: CareerAnalysis = {
        careerPaths: [career],
        overallAnalysis: `Detailed analysis for ${career.jobTitle}`,
        recommendations: ['Review the career roadmap carefully', 'Start building required skills'],
        nextSteps: ['Create a learning plan', 'Network with professionals in this field'],
        generatedAt: new Date()
      };

      const exportOptions = {
        format: format as 'pdf' | 'docx' | 'json',
        includeRoadmaps: true,
        includeAnalysis: true,
        includeVisualization: false
      };

      switch (format) {
        case 'pdf':
          await careerExportService.exportToPDF(singleCareerAnalysis, exportOptions);
          break;
        case 'docx':
          await careerExportService.exportToWord(singleCareerAnalysis, exportOptions);
          break;
        case 'json':
          await careerExportService.exportToJSON(singleCareerAnalysis, exportOptions);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      toast.success(`${career.jobTitle} exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export career details');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Briefcase className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{career.jobTitle}</h2>
                <p className="text-sm text-gray-600 mt-1">{career.jobDescription}</p>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 px-6">
          <div className="space-y-6 pb-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <p className="text-sm font-medium text-blue-700 mb-1">Timeline</p>
                  <p className="font-bold text-blue-900 text-lg">{career.timeline}</p>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                  <p className="text-sm font-medium text-green-700 mb-1">Salary Range</p>
                  <p className="font-bold text-green-900 text-lg">{career.salary}</p>
                </CardContent>
              </Card>

              <Card className={`bg-gradient-to-br ${
                career.difficulty === 'Low' ? 'from-green-50 to-green-100 border-green-200' :
                career.difficulty === 'Medium' ? 'from-orange-50 to-orange-100 border-orange-200' :
                'from-red-50 to-red-100 border-red-200'
              }`}>
                <CardContent className="p-4 text-center">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${
                    career.difficulty === 'Low' ? 'bg-green-500' :
                    career.difficulty === 'Medium' ? 'bg-orange-500' :
                    'bg-red-500'
                  }`}>
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                  <p className={`text-sm font-medium mb-1 ${
                    career.difficulty === 'Low' ? 'text-green-700' :
                    career.difficulty === 'Medium' ? 'text-orange-700' :
                    'text-red-700'
                  }`}>Difficulty</p>
                  <p className={`font-bold text-lg ${
                    career.difficulty === 'Low' ? 'text-green-900' :
                    career.difficulty === 'Medium' ? 'text-orange-900' :
                    'text-red-900'
                  }`}>{career.difficulty}</p>
                </CardContent>
              </Card>

              {career.workRequired && (
                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                  <CardContent className="p-4 text-center">
                    <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Target className="h-6 w-6 text-white" />
                    </div>
                    <p className="text-sm font-medium text-purple-700 mb-1">Study Time</p>
                    <p className="font-bold text-purple-900 text-lg">{career.workRequired}</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* About the Role */}
            {career.aboutTheRole && (
              <Card className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-blue-700">
                    <BookOpen className="h-5 w-5 mr-2" />
                    Role Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed text-base">{career.aboutTheRole}</p>
                </CardContent>
              </Card>
            )}

            {/* Why It's a Good Fit */}
            {career.whyItsGoodFit && career.whyItsGoodFit.length > 0 && (
              <Card className="border-l-4 border-l-green-500 bg-green-50">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-green-700">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Perfect Match for You
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {career.whyItsGoodFit.map((reason, index) => (
                      <div key={index} className="flex items-start bg-white rounded-lg p-3 shadow-sm">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{reason}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Skills and Certifications */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {career.skills && career.skills.length > 0 && (
                <Card className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center text-purple-700">
                      <Target className="h-5 w-5 mr-2" />
                      Essential Skills
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-2">
                      {career.skills.map((skill, index) => (
                        <div key={index} className="flex items-center bg-purple-50 rounded-lg p-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                          <span className="text-gray-700 font-medium">{skill}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {career.certifications && career.certifications.length > 0 && (
                <Card className="border-l-4 border-l-yellow-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center text-yellow-700">
                      <Award className="h-5 w-5 mr-2" />
                      Valuable Certifications
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {career.certifications.map((cert, index) => (
                        <div key={index} className="flex items-center bg-yellow-50 rounded-lg p-3">
                          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <Award className="h-4 w-4 text-yellow-600" />
                          </div>
                          <span className="text-gray-700 font-medium">{cert}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Career Roadmap */}
            {career.roadmap && career.roadmap.length > 0 && (
              <Card className="border-l-4 border-l-indigo-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center text-indigo-700">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    Your Transition Roadmap
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Step-by-step guide to achieve your career goal</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {career.roadmap.map((step, index) => (
                      <div key={index} className="relative">
                        <div className="flex items-start">
                          <div className="flex flex-col items-center mr-4">
                            <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                              <span className="text-sm font-bold text-white">{index + 1}</span>
                            </div>
                            {index < career.roadmap!.length - 1 && (
                              <div className="w-0.5 h-12 bg-gradient-to-b from-indigo-300 to-purple-300 mt-2" />
                            )}
                          </div>
                          <div className="flex-1 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 shadow-sm">
                            <div className="flex items-center mb-2">
                              <Clock className="h-4 w-4 text-indigo-600 mr-2" />
                              <h4 className="font-bold text-indigo-900">{step.timeframe}</h4>
                            </div>
                            <p className="text-gray-700 leading-relaxed mb-3">{step.description}</p>
                            {step.tasks && step.tasks.length > 0 && (
                              <div className="bg-white rounded-md p-3 border border-indigo-100">
                                <p className="text-xs font-semibold text-indigo-700 mb-2 uppercase tracking-wide">Action Items</p>
                                <ul className="space-y-1">
                                  {step.tasks.map((task, taskIndex) => (
                                    <li key={taskIndex} className="flex items-start text-sm text-gray-600">
                                      <CheckCircle className="h-3 w-3 text-indigo-500 mr-2 mt-0.5 flex-shrink-0" />
                                      {task}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </ScrollArea>

        <Separator />

        <DialogFooter className="p-6 pt-4">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Export this career plan:</span>
              {EXPORT_FORMATS.map((format) => (
                <Button
                  key={format.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport(format.id)}
                  className="text-xs"
                >
                  <Download className="h-3 w-3 mr-1" />
                  {format.name}
                </Button>
              ))}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button onClick={() => window.open(`https://www.google.com/search?q=${encodeURIComponent(career.jobTitle + ' career guide')}`, '_blank')}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Learn More
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
