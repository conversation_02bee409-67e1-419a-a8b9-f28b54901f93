import MarkdownIt from 'markdown-it';
import { convert } from 'html-to-text';

// Initialize markdown parser
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true,
  typographer: true
});

/**
 * Converts Markdown to HTML
 */
const markdownToHtml = (markdown: string): string => {
  if (!markdown) return '';
  return md.render(markdown);
};

/**
 * Converts HTML to Markdown
 */
const htmlToMarkdown = (html: string): string => {
  if (!html) return '';
  
  // Use html-to-text to convert HTML to plain text
  const text = convert(html, {
    selectors: [
      { selector: 'h1', format: 'headerBlock', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: false } },
      { selector: 'h2', format: 'headerBlock', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: false } },
      { selector: 'h3', format: 'headerBlock', options: { leadingLineBreaks: 2, trailingLineBreaks: 1, uppercase: false } },
      { selector: 'p', options: { leadingLineBreaks: 2, trailingLineBreaks: 1 } },
      { selector: 'ul', options: { itemPrefix: '- ' } },
      { selector: 'ol', options: { itemPrefix: '1. ' } },
      { selector: 'blockquote', format: 'blockquote' },
      { selector: 'pre', format: 'pre' },
      { selector: 'hr', format: 'horizontalLine' },
      { selector: 'a', format: 'anchor' },
      { selector: 'img', format: 'image' }
    ],
    formatters: {
      headerBlock: (elem, walk, options) => {
        const { leadingLineBreaks = 2, uppercase = false } = options;
        let prefix = '';
        for (let i = 0; i < leadingLineBreaks; i++) {
          prefix += '\n';
        }
        
        const level = parseInt(elem.tagName.charAt(1));
        let headerPrefix = '';
        for (let i = 0; i < level; i++) {
          headerPrefix += '#';
        }
        
        let text = walk(elem.children);
        if (uppercase) {
          text = text.toUpperCase();
        }
        
        return prefix + headerPrefix + ' ' + text;
      },
      blockquote: (elem, walk, options) => {
        return '> ' + walk(elem.children);
      },
      pre: (elem, walk, options) => {
        return '```\n' + walk(elem.children) + '\n```';
      },
      horizontalLine: (elem, walk, options) => {
        return '\n---\n';
      },
    }
  });
  
  // Post-process to fix some formatting
  const markdown = text
    .replace(/^(\d+)\. /gm, '$1\\. ') // Fix ordered lists
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .replace(/^# (.*?)$/gm, '# $1') // Fix header formatting
    .replace(/^## (.*?)$/gm, '## $1')
    .replace(/^### (.*?)$/gm, '### $1');
    
  return markdown;
};

/**
 * Detects if content is in markdown format
 */
const isMarkdown = (content: string): boolean => {
  // Check for common markdown indicators
  return (
    /^#+ /.test(content) || // Headers
    /\*\*.*\*\*/.test(content) || // Bold
    /\*.*\*/.test(content) || // Italic
    /^\- .*$/m.test(content) || // Unordered list
    /^\d+\. .*$/m.test(content) || // Ordered list
    /^\> .*$/m.test(content) || // Blockquotes
    /\[.*\]\(.*\)/.test(content) // Links
  );
};

export const formatConverter = {
  markdownToHtml,
  htmlToMarkdown,
  isMarkdown
};
