// This service handles integration with the main editor

interface EditorContent {
  title: string;
  content: string;
}

/**
 * Since we can't directly use React navigation hooks outside of components,
 * we'll create a custom solution to handle paper content for the main editor
 * 
 * This implementation uses localStorage to store the paper content temporarily,
 * which will be picked up by the MainEditor component when it loads
 */

// Use a specific key for the editor content
const EDITOR_CONTENT_KEY = 'paper_genius_editor_content';

/**
 * Sends paper content to the main editor using localStorage
 * The MainEditor component should check for content using this key on mount
 */
const sendToMainEditor = (content: EditorContent): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      // Store the content in localStorage
      localStorage.setItem(EDITOR_CONTENT_KEY, JSON.stringify(content));
      console.log('Content stored for main editor:', content);
      
      // Dispatch a custom event that the MainEditor component can listen for
      const event = new CustomEvent('paperGenius_editorContentUpdated', { 
        detail: content 
      });
      window.dispatchEvent(event);
      
      // Instead of navigation which is causing 404 errors, 
      // we'll let the EnhancedPaperGenerator component handle showing the editor
      resolve(true);
    } catch (error) {
      console.error('Error preparing content for editor:', error);
      resolve(false);
    }
  });
};

/**
 * Retrieves content intended for the editor
 * The MainEditor component should call this on mount
 */
const getEditorContent = (): EditorContent | null => {
  try {
    const storedContent = localStorage.getItem(EDITOR_CONTENT_KEY);
    if (storedContent) {
      return JSON.parse(storedContent);
    }
    return null;
  } catch (error) {
    console.error('Error retrieving editor content:', error);
    return null;
  }
};

/**
 * Clears the stored editor content
 * Should be called after content is successfully loaded
 */
const clearEditorContent = (): void => {
  try {
    localStorage.removeItem(EDITOR_CONTENT_KEY);
  } catch (error) {
    console.error('Error clearing editor content:', error);
  }
};

export const editorService = {
  sendToMainEditor,
  getEditorContent,
  clearEditorContent
};
