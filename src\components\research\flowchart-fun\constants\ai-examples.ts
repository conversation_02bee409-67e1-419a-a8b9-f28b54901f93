/**
 * AI Examples and Constants for Flowchart Fun
 * Provides example prompts and configurations for AI-powered flowchart generation
 */

export const AI_EXAMPLE_PROMPTS = {
  prompt: [
    "Create a flowchart for a user login process with authentication and error handling",
    "Design a research methodology flowchart for conducting a systematic literature review",
    "Map out the steps for developing and deploying a web application",
    "Show the process of debugging a software issue from detection to resolution",
    "Create a workflow for handling customer complaints in a service organization",
    "Design a decision tree for choosing the right statistical test for research data",
    "Create a flowchart for the peer review process in academic publishing",
    "Map out the process of conducting a clinical trial from design to publication"
  ],
  convert: [
    "Start → Check credentials → Valid? → Yes: Login successful → End, No: Show error → Start",
    "1. Gather requirements\n2. Design system architecture\n3. Implement features\n4. Test thoroughly\n5. Deploy to production\n6. Monitor and maintain",
    "User clicks button\nSystem validates input\nIf valid, save data\nIf invalid, show error\nReturn to form",
    "Literature search: PubMed, Scopus, Web of Science\nScreen titles and abstracts\nFull-text review\nData extraction\nQuality assessment\nMeta-analysis\nReport writing",
    "Patient enrollment → Baseline assessment → Randomization → Intervention group vs Control group → Follow-up visits → Data collection → Statistical analysis → Results interpretation"
  ],
  edit: [
    "Add error handling and validation steps to the current flowchart",
    "Include a retry mechanism for failed operations with maximum attempt limits",
    "Add data validation and sanitization steps before processing",
    "Include comprehensive logging and monitoring at each major step",
    "Add parallel processing branches to improve efficiency",
    "Insert quality control checkpoints throughout the process",
    "Add user notification and feedback mechanisms",
    "Include rollback procedures for failed operations"
  ]
};

export const AI_MODE_DESCRIPTIONS = {
  prompt: "Create a new flowchart from your description",
  convert: "Convert existing text into flowchart format", 
  edit: "Modify and enhance your current flowchart"
};

export const AI_PLACEHOLDERS = {
  prompt: "Describe the flowchart you want to create...",
  convert: "Paste your text or document here to convert...",
  edit: "Describe how you want to modify the current flowchart..."
};

export const GEMINI_CONFIG = {
  MODEL: 'gemini-2.5-pro',
  FALLBACK_MODEL: 'gemini-2.5-flash',
  RATE_LIMIT_DELAY: 2000, // 2 seconds between requests
  MAX_TOKENS: 4096,
  TEMPERATURE: 0.7,
  TIMEOUT: 30000 // 30 seconds
};

export const AI_ERROR_MESSAGES = {
  NO_API_KEY: 'Gemini API key not configured. Please check your environment variables.',
  SERVICE_ERROR: 'Failed to generate flowchart. Please try again.',
  RATE_LIMIT: 'Too many requests. Please wait a moment and try again.',
  TIMEOUT: 'Request timed out. Please try again with a shorter prompt.',
  INVALID_RESPONSE: 'Received invalid response from AI service.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.'
};

export const FLOWCHART_SYNTAX_GUIDE = `
Flowchart Fun Syntax Guide:
- Each line represents a node
- Indentation shows hierarchy and flow
- Use ":" to add labels to connections
- Use parentheses to reference existing nodes: (Node Name)
- Use classes for styling: .className

Example:
Start
  Decision: Is it working?
    Yes: (End)
    No: Fix it
      (Decision)
End
`;

export const AI_TIPS = [
  "Be specific in your descriptions for better results",
  "Use simple, clear language to describe your process",
  "Break complex workflows into smaller steps",
  "Include decision points and alternative paths",
  "Mention any specific styling or formatting needs"
];

export const SAMPLE_FLOWCHARTS = {
  simple: `Start
  Process Data
  Decision: Valid?
    Yes: Save Data
    No: Show Error
  End`,

  login: `User Login
  Enter Credentials
  Validate Input: Check database
    Valid Credentials: Success
      Load Dashboard
      Display User Profile
    Invalid Credentials: Error
      Show Error Message
      Retry Attempt: Check count
        Less than 3: Return to Enter Credentials
        3 or more: Lock Account
  End`,

  research: `Research Process
  Literature Review: Systematic search
    Database Search: PubMed, Scopus
    Screen Titles: Initial filter
    Abstract Review: Detailed assessment
    Full Text Review: Final selection
  Data Collection: Primary research
    Survey Design: Questionnaire development
    Participant Recruitment: Sampling strategy
    Data Gathering: Online/offline collection
  Data Analysis: Statistical processing
    Data Cleaning: Handle missing values
    Descriptive Statistics: Summary measures
    Inferential Testing: Hypothesis testing
  Results Interpretation: Draw conclusions
    Effect Sizes: Clinical significance
    Limitations: Study constraints
  Reporting: Manuscript preparation
    Introduction: Background and aims
    Methods: Detailed procedures
    Results: Findings presentation
    Discussion: Implications and future work
  End`,

  development: `Software Development
  Requirements Gathering
    Stakeholder Interviews: User needs
    Functional Requirements: Feature specifications
    Non-functional Requirements: Performance criteria
  System Design
    Architecture Planning: High-level design
    Database Design: Schema and relationships
    API Design: Endpoint specifications
  Implementation
    Frontend Development: User interface
      Component Development: Reusable UI elements
      State Management: Data flow
      Testing: Unit and integration tests
    Backend Development: Server logic
      API Implementation: Endpoint development
      Database Integration: Data persistence
      Security Implementation: Authentication and authorization
  Quality Assurance
    Code Review: Peer assessment
    Testing: Comprehensive test suite
      Unit Tests: Component testing
      Integration Tests: System interaction
      End-to-End Tests: User workflow validation
  Deployment
    Staging Environment: Pre-production testing
    Production Deployment: Live release
    Monitoring Setup: Performance tracking
  Maintenance
    Bug Fixes: Issue resolution
    Feature Updates: Continuous improvement
    Performance Optimization: System tuning
  End`
};
