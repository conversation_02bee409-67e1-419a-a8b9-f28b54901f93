-- Google Search Database Schema
-- Tables for storing Google search sessions and messages

-- Google Search Sessions Table
CREATE TABLE IF NOT EXISTS google_search_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL DEFAULT 'Google Search Session',
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_queries INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Google Search Messages Table
CREATE TABLE IF NOT EXISTS google_search_messages (
    id TEXT PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES google_search_sessions(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('user', 'assistant', 'system', 'google_search_result')),
    content TEXT NOT NULL,
    search_query TEXT,
    sources JSONB,
    citations JSONB,
    references <PERSON><PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_google_search_sessions_user_id ON google_search_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_google_search_sessions_updated_at ON google_search_sessions(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_google_search_messages_session_id ON google_search_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_google_search_messages_created_at ON google_search_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_google_search_messages_type ON google_search_messages(type);

-- Row Level Security (RLS) Policies
ALTER TABLE google_search_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE google_search_messages ENABLE ROW LEVEL SECURITY;

-- Policies for google_search_sessions
CREATE POLICY "Users can view their own Google search sessions" ON google_search_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own Google search sessions" ON google_search_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own Google search sessions" ON google_search_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own Google search sessions" ON google_search_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for google_search_messages
CREATE POLICY "Users can view messages from their own Google search sessions" ON google_search_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM google_search_sessions 
            WHERE google_search_sessions.id = google_search_messages.session_id 
            AND google_search_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages to their own Google search sessions" ON google_search_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM google_search_sessions 
            WHERE google_search_sessions.id = google_search_messages.session_id 
            AND google_search_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update messages in their own Google search sessions" ON google_search_messages
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM google_search_sessions 
            WHERE google_search_sessions.id = google_search_messages.session_id 
            AND google_search_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete messages from their own Google search sessions" ON google_search_messages
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM google_search_sessions 
            WHERE google_search_sessions.id = google_search_messages.session_id 
            AND google_search_sessions.user_id = auth.uid()
        )
    );

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_google_search_session_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_google_search_sessions_updated_at
    BEFORE UPDATE ON google_search_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_google_search_session_updated_at();

-- Function to increment total_queries when a user message is added
CREATE OR REPLACE FUNCTION increment_google_search_queries()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.type = 'user' THEN
        UPDATE google_search_sessions 
        SET total_queries = total_queries + 1,
            updated_at = NOW()
        WHERE id = NEW.session_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to increment query count
CREATE TRIGGER increment_google_search_session_queries
    AFTER INSERT ON google_search_messages
    FOR EACH ROW
    EXECUTE FUNCTION increment_google_search_queries();

-- ===== DEEP RESEARCH TABLES =====

-- Deep Research Sessions Table
CREATE TABLE IF NOT EXISTS deep_research_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'outline' CHECK (status IN ('outline', 'researching', 'writing', 'completed', 'error')),
    progress JSONB NOT NULL DEFAULT '{}',
    estimated_cost DECIMAL(10,4),
    actual_cost DECIMAL(10,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Research Outlines Table
CREATE TABLE IF NOT EXISTS research_outlines (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES deep_research_sessions(id) ON DELETE CASCADE,
    main_topic TEXT NOT NULL,
    description TEXT,
    research_questions JSONB DEFAULT '[]',
    estimated_length INTEGER DEFAULT 0,
    target_audience TEXT DEFAULT 'professional',
    research_depth TEXT DEFAULT 'advanced' CHECK (research_depth IN ('basic', 'advanced', 'comprehensive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Research Subtopics Table
CREATE TABLE IF NOT EXISTS research_subtopics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    outline_id UUID NOT NULL REFERENCES research_outlines(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    key_questions JSONB DEFAULT '[]',
    estimated_words INTEGER DEFAULT 700,
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
    search_terms JSONB DEFAULT '[]',
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Research Data Table (stores batch research results)
CREATE TABLE IF NOT EXISTS research_data (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES deep_research_sessions(id) ON DELETE CASCADE,
    subtopic_id UUID NOT NULL REFERENCES research_subtopics(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    sources JSONB DEFAULT '[]',
    summary TEXT,
    key_findings JSONB DEFAULT '[]',
    search_time INTEGER DEFAULT 0,
    total_results INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Research Sections Table (stores generated content)
CREATE TABLE IF NOT EXISTS research_sections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES deep_research_sessions(id) ON DELETE CASCADE,
    subtopic_id UUID NOT NULL REFERENCES research_subtopics(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    citations JSONB DEFAULT '[]',
    references JSONB DEFAULT '[]',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'generating', 'completed', 'error')),
    order_index INTEGER NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Deep Research Options Table (stores user preferences per session)
CREATE TABLE IF NOT EXISTS deep_research_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID NOT NULL REFERENCES deep_research_sessions(id) ON DELETE CASCADE,
    max_subtopics INTEGER DEFAULT 6,
    words_per_section INTEGER DEFAULT 700,
    research_depth TEXT DEFAULT 'advanced' CHECK (research_depth IN ('basic', 'advanced', 'comprehensive')),
    include_executive_summary BOOLEAN DEFAULT true,
    citation_style TEXT DEFAULT 'apa' CHECK (citation_style IN ('apa', 'mla', 'chicago', 'harvard')),
    target_audience TEXT DEFAULT 'professional' CHECK (target_audience IN ('academic', 'professional', 'general')),
    allow_outline_editing BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for Deep Research tables
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_user_id ON deep_research_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_status ON deep_research_sessions(status);
CREATE INDEX IF NOT EXISTS idx_deep_research_sessions_updated_at ON deep_research_sessions(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_research_outlines_session_id ON research_outlines(session_id);
CREATE INDEX IF NOT EXISTS idx_research_subtopics_outline_id ON research_subtopics(outline_id);
CREATE INDEX IF NOT EXISTS idx_research_subtopics_order ON research_subtopics(order_index);
CREATE INDEX IF NOT EXISTS idx_research_data_session_id ON research_data(session_id);
CREATE INDEX IF NOT EXISTS idx_research_data_subtopic_id ON research_data(subtopic_id);
CREATE INDEX IF NOT EXISTS idx_research_sections_session_id ON research_sections(session_id);
CREATE INDEX IF NOT EXISTS idx_research_sections_subtopic_id ON research_sections(subtopic_id);
CREATE INDEX IF NOT EXISTS idx_research_sections_order ON research_sections(order_index);
CREATE INDEX IF NOT EXISTS idx_deep_research_options_session_id ON deep_research_options(session_id);

-- Grant necessary permissions
GRANT ALL ON google_search_sessions TO authenticated;
GRANT ALL ON google_search_messages TO authenticated;
GRANT ALL ON deep_research_sessions TO authenticated;
GRANT ALL ON research_outlines TO authenticated;
GRANT ALL ON research_subtopics TO authenticated;
GRANT ALL ON research_data TO authenticated;
GRANT ALL ON research_sections TO authenticated;
GRANT ALL ON deep_research_options TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- ===== ROW LEVEL SECURITY FOR DEEP RESEARCH TABLES =====

-- Enable RLS on Deep Research tables
ALTER TABLE deep_research_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_outlines ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_subtopics ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE deep_research_options ENABLE ROW LEVEL SECURITY;

-- Policies for deep_research_sessions
CREATE POLICY "Users can view their own deep research sessions" ON deep_research_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own deep research sessions" ON deep_research_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own deep research sessions" ON deep_research_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own deep research sessions" ON deep_research_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for research_outlines
CREATE POLICY "Users can view outlines from their own sessions" ON research_outlines
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_outlines.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert outlines to their own sessions" ON research_outlines
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_outlines.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update outlines in their own sessions" ON research_outlines
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_outlines.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete outlines from their own sessions" ON research_outlines
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_outlines.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

-- Policies for research_subtopics
CREATE POLICY "Users can view subtopics from their own outlines" ON research_subtopics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM research_outlines ro
            JOIN deep_research_sessions drs ON ro.session_id = drs.id
            WHERE ro.id = research_subtopics.outline_id
            AND drs.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert subtopics to their own outlines" ON research_subtopics
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM research_outlines ro
            JOIN deep_research_sessions drs ON ro.session_id = drs.id
            WHERE ro.id = research_subtopics.outline_id
            AND drs.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update subtopics in their own outlines" ON research_subtopics
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM research_outlines ro
            JOIN deep_research_sessions drs ON ro.session_id = drs.id
            WHERE ro.id = research_subtopics.outline_id
            AND drs.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete subtopics from their own outlines" ON research_subtopics
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM research_outlines ro
            JOIN deep_research_sessions drs ON ro.session_id = drs.id
            WHERE ro.id = research_subtopics.outline_id
            AND drs.user_id = auth.uid()
        )
    );

-- Policies for research_data
CREATE POLICY "Users can view research data from their own sessions" ON research_data
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_data.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert research data to their own sessions" ON research_data
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_data.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update research data in their own sessions" ON research_data
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_data.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete research data from their own sessions" ON research_data
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_data.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

-- Policies for research_sections
CREATE POLICY "Users can view research sections from their own sessions" ON research_sections
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_sections.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert research sections to their own sessions" ON research_sections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_sections.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update research sections in their own sessions" ON research_sections
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_sections.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete research sections from their own sessions" ON research_sections
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = research_sections.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

-- Policies for deep_research_options
CREATE POLICY "Users can view options from their own sessions" ON deep_research_options
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = deep_research_options.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert options to their own sessions" ON deep_research_options
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = deep_research_options.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update options in their own sessions" ON deep_research_options
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = deep_research_options.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete options from their own sessions" ON deep_research_options
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM deep_research_sessions
            WHERE deep_research_sessions.id = deep_research_options.session_id
            AND deep_research_sessions.user_id = auth.uid()
        )
    );

-- ===== TRIGGERS AND FUNCTIONS FOR DEEP RESEARCH =====

-- Function to update the updated_at timestamp for deep research sessions
CREATE OR REPLACE FUNCTION update_deep_research_session_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at for deep research sessions
CREATE TRIGGER update_deep_research_sessions_updated_at
    BEFORE UPDATE ON deep_research_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_deep_research_session_updated_at();

-- Function to update the updated_at timestamp for research outlines
CREATE OR REPLACE FUNCTION update_research_outline_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at for research outlines
CREATE TRIGGER update_research_outlines_updated_at
    BEFORE UPDATE ON research_outlines
    FOR EACH ROW
    EXECUTE FUNCTION update_research_outline_updated_at();
