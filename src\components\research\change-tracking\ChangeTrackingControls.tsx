import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  GitBranch, 
  Eye, 
  EyeOff, 
  Play, 
  Square,
  RotateCcw,
  CheckCheck,
  AlertCircle
} from 'lucide-react';
import { changeTrackingService } from './ChangeTrackingService';
import { ChangeTrackingState } from './types';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ChangeTrackingControlsProps {
  onToggleTracking: (enabled: boolean) => void;
  onToggleDiffView: () => void;
  onAcceptAllChanges: () => void;
  onRejectAllChanges: () => void;
  className?: string;
}

export function ChangeTrackingControls({
  onToggleTracking,
  onToggleDiffView,
  onAcceptAllChanges,
  onRejectAllChanges,
  className
}: ChangeTrackingControlsProps) {
  const [state, setState] = useState<ChangeTrackingState>(changeTrackingService.getState());

  useEffect(() => {
    const unsubscribe = changeTrackingService.subscribe((newState) => {
      setState(newState);
    });

    return unsubscribe;
  }, []);

  const pendingChangesCount = changeTrackingService.getPendingChangesCount();
  const hasChanges = pendingChangesCount > 0;

  const handleToggleTracking = () => {
    if (state.isTrackingEnabled) {
      if (hasChanges) {
        // Warn user about pending changes
        const confirmed = window.confirm(
          `You have ${pendingChangesCount} pending changes. Stopping tracking will clear all changes. Continue?`
        );
        if (!confirmed) return;
      }
      changeTrackingService.stopTracking();
      onToggleTracking(false);
      toast.info('Change tracking stopped');
    } else {
      // Start tracking with current content
      onToggleTracking(true);
      toast.success('Change tracking started');
    }
  };

  const handleAcceptAll = () => {
    if (pendingChangesCount === 0) return;
    
    const confirmed = window.confirm(
      `Accept all ${pendingChangesCount} changes? This action cannot be undone.`
    );
    
    if (confirmed) {
      onAcceptAllChanges();
      toast.success(`Accepted ${pendingChangesCount} changes`);
    }
  };

  const handleRejectAll = () => {
    if (pendingChangesCount === 0) return;
    
    const confirmed = window.confirm(
      `Reject all ${pendingChangesCount} changes and revert to original content? This action cannot be undone.`
    );
    
    if (confirmed) {
      onRejectAllChanges();
      toast.success('All changes rejected, content reverted');
    }
  };

  return (
    <div className={cn("flex items-center gap-3 p-3 bg-gray-50 border rounded-lg", className)}>
      {/* Tracking Status */}
      <div className="flex items-center gap-2">
        <GitBranch className="h-4 w-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">Change Tracking</span>
        <Badge 
          variant={state.isTrackingEnabled ? "default" : "secondary"}
          className={cn(
            "text-xs",
            state.isTrackingEnabled ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"
          )}
        >
          {state.isTrackingEnabled ? 'Active' : 'Inactive'}
        </Badge>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Tracking Toggle */}
      <Button
        variant={state.isTrackingEnabled ? "destructive" : "default"}
        size="sm"
        onClick={handleToggleTracking}
        className="flex items-center gap-2"
      >
        {state.isTrackingEnabled ? (
          <>
            <Square className="h-4 w-4" />
            Stop
          </>
        ) : (
          <>
            <Play className="h-4 w-4" />
            Start
          </>
        )}
      </Button>

      {/* Changes Info */}
      {state.isTrackingEnabled && (
        <>
          <Separator orientation="vertical" className="h-6" />
          
          <div className="flex items-center gap-2">
            {hasChanges ? (
              <>
                <AlertCircle className="h-4 w-4 text-amber-500" />
                <Badge variant="secondary" className="bg-amber-100 text-amber-800">
                  {pendingChangesCount} pending
                </Badge>
              </>
            ) : (
              <>
                <CheckCheck className="h-4 w-4 text-green-500" />
                <span className="text-sm text-gray-600">No changes</span>
              </>
            )}
          </div>

          {/* View Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleDiffView}
            className="flex items-center gap-2"
            disabled={!hasChanges}
          >
            {state.viewMode === 'diff' ? (
              <>
                <EyeOff className="h-4 w-4" />
                Normal View
              </>
            ) : (
              <>
                <Eye className="h-4 w-4" />
                Diff View
              </>
            )}
          </Button>

          {/* Bulk Actions */}
          {hasChanges && (
            <>
              <Separator orientation="vertical" className="h-6" />
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRejectAll}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reject All
                </Button>
                
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleAcceptAll}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                >
                  <CheckCheck className="h-4 w-4" />
                  Accept All
                </Button>
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
}
