/**
 * Types for the AI Article Reviewer
 */

// Available models for article review
export interface AIModelOption {
  id: string;
  name: string;
  description?: string;
  provider: 'openrouter' | 'openai' | 'anthropic' | 'google';
  contextSize?: number;
  isAvailable?: boolean;
}

// Article section types
export type ArticleSection = 
  | 'title'
  | 'abstract' 
  | 'keywords'
  | 'introduction'
  | 'methodology'
  | 'results'
  | 'discussion'
  | 'conclusion'
  | 'references'
  | 'overall';

// The parsed article content by section
export interface ParsedArticle {
  title?: string;
  abstract?: string;
  keywords?: string[];
  introduction?: string;
  methodology?: string;
  results?: string;
  discussion?: string;
  conclusion?: string;
  references?: string;
  fullText: string;
  sectionMapping: Record<ArticleSection, string>;
  fileName: string;
  fileType: string;
  fileSize: number;
}

// Detailed feedback for specific text segments
export interface DetailedFeedbackItem {
  originalText: string;     // The original text segment with issue
  issue: string;           // Description of the issue
  suggestion: string;      // Suggested improvement
  type: 'grammar' | 'clarity' | 'structure' | 'content' | 'style' | 'citation' | 'logic';  // Type of issue
  severity: 'minor' | 'moderate' | 'major';  // Severity of the issue
  lineNumber?: number;     // Approximate line number (if available)
  highlightColor?: string; // Color for highlighting this issue
}

// Review results for a section
export interface SectionReview {
  content: string;         // The original content being reviewed
  analysis: string;        // Detailed analysis of the section
  strengths: string[];     // Strengths of the section
  weaknesses: string[];    // Weaknesses/issues identified
  suggestions: string[];   // Suggestions for improvement
  score: number;           // Score from 0-100
  detailedFeedback: DetailedFeedbackItem[]; // Sentence-level feedback
}

// Overall review metrics
export interface ReviewMetrics {
  clarity: number;         // How clear the writing is (0-100)
  structure: number;       // How well structured the paper is (0-100)
  methodology: number;     // Quality of methods (0-100)
  significance: number;    // Significance of research (0-100)
  originality: number;     // Novelty/originality (0-100)
  overallScore: number;    // Overall weighted score (0-100)
  summary: string;         // Brief summary of the review
  majorStrengths: string[];
  majorWeaknesses: string[];
  recommendations: string[];
}

// Complete review results
export interface ArticleReviewResults {
  [key: string]: SectionReview | ReviewMetrics;
  overall: ReviewMetrics;
}

// Progress tracking
export interface ReviewProgress {
  percentage: number;
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  estimatedTimeRemaining?: number;
}
