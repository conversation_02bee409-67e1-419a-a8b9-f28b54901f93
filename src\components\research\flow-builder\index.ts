/**
 * Flow Builder Module Index
 * Main export file for the Flow Chart Builder module
 */

// Main component
export { FlowBuilder } from './FlowBuilder';

// Types
export type {
  DiagramType,
  DiagramDirection,
  ExportFormat,
  DiagramMetadata,
  DiagramInput,
  GeneratedDiagram,
  DiagramGenerationRequest,
  DiagramGenerationResponse,
  DiagramHistory,
  DiagramExportOptions,
  FlowAIModel,
  FlowGenerationWorkflow,
  DiagramEditingState,
  MermaidConfig,
  DiagramTemplate,
  DiagramValidationResult,
  FlowBuilderState,
  FlowBuilderActions,
  FlowBuilderStore,
  FlowBuilderProps
} from './types';

// Components
export {
  DiagramInputForm,
  DiagramRenderer,
  DiagramEditor,
  DiagramExportDialog,
  DiagramHistoryPanel,
  FlowBuilderHeader,
  FlowBuilderErrorBoundary,
  AIModelSelector
} from './components';

// Services
export { flowAIService } from './services/flow-ai.service';

// Store
export { useFlowBuilderStore } from './stores/flow-builder.store';

// Constants
export {
  FLOW_AI_MODELS,
  DEFAULT_MERMAID_CONFIG,
  DIAGRAM_TYPE_CONFIG,
  EXPORT_FORMATS,
  DEFAULT_EXPORT_OPTIONS,
  GENERATION_CONFIG,
  VALIDATION_RULES,
  ERROR_MESSAGES,
  UI_CONFIG,
  STORAGE_KEYS,
  DEFAULT_TEMPLATES,
  MERMAID_THEMES
} from './constants';

// Prompts
export { FLOW_GENERATION_PROMPTS, RESEARCH_PROMPTS, RESEARCH_PATTERNS } from './prompts';
