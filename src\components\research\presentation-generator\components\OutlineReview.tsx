import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>cle, 
  Edit3, 
  <PERSON><PERSON><PERSON>, 
  ArrowDown, 
  Plus, 
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Clock
} from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { OutlineItem, SlideLayout } from '../types';
import { SLIDE_TEMPLATES } from '../constants';

interface OutlineReviewProps {
  outline: OutlineItem[];
  isApproved: boolean;
  onApprove: () => void;
  onEdit: () => void;
  isGenerating: boolean;
}

export function OutlineReview({ 
  outline, 
  isApproved, 
  onApprove, 
  onEdit, 
  isGenerating 
}: OutlineReviewProps) {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [localOutline, setLocalOutline] = useState<OutlineItem[]>(outline);

  // Handle item editing
  const handleEditItem = (id: string, updates: Partial<OutlineItem>) => {
    setLocalOutline(prev => 
      prev.map(item => 
        item.id === id ? { ...item, ...updates } : item
      )
    );
  };

  // Handle item reordering
  const handleMoveItem = (id: string, direction: 'up' | 'down') => {
    const currentIndex = localOutline.findIndex(item => item.id === id);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= localOutline.length) return;

    const newOutline = [...localOutline];
    [newOutline[currentIndex], newOutline[newIndex]] = [newOutline[newIndex], newOutline[currentIndex]];
    
    // Update order numbers
    newOutline.forEach((item, index) => {
      item.order = index;
    });

    setLocalOutline(newOutline);
  };

  // Handle item deletion
  const handleDeleteItem = (id: string) => {
    setLocalOutline(prev => 
      prev.filter(item => item.id !== id)
        .map((item, index) => ({ ...item, order: index }))
    );
  };

  // Handle adding new item
  const handleAddItem = (afterIndex: number) => {
    const newItem: OutlineItem = {
      id: crypto.randomUUID(),
      title: 'New Slide',
      description: 'Add your slide description here...',
      order: afterIndex + 1,
      slideLayout: 'content-slide',
      estimatedDuration: 2
    };

    const newOutline = [...localOutline];
    newOutline.splice(afterIndex + 1, 0, newItem);
    
    // Update order numbers
    newOutline.forEach((item, index) => {
      item.order = index;
    });

    setLocalOutline(newOutline);
    setEditingItem(newItem.id);
  };

  // Calculate total estimated duration
  const totalDuration = localOutline.reduce((sum, item) => sum + (item.estimatedDuration || 2), 0);

  if (isGenerating) {
    return (
      <div className="text-center py-12">
        <Sparkles className="w-16 h-16 mx-auto text-blue-500 animate-spin mb-4" />
        <h3 className="text-xl font-semibold mb-2">Generating Outline...</h3>
        <p className="text-gray-600">
          Our AI is analyzing your topic and creating a structured presentation outline.
        </p>
      </div>
    );
  }

  if (outline.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold mb-2">No Outline Generated</h3>
        <p className="text-gray-600 mb-6">
          There was an issue generating the outline. Please try again.
        </p>
        <Button onClick={onEdit} variant="outline">
          <Edit3 className="w-4 h-4 mr-2" />
          Edit Settings
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FileText className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Review Your Presentation Outline
        </h2>
        <p className="text-gray-600">
          Review and customize your presentation structure before generating slides.
        </p>
      </div>

      {/* Summary Stats */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">{localOutline.length}</div>
              <div className="text-sm text-gray-600">Total Slides</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 flex items-center justify-center gap-1">
                <Clock className="w-5 h-5" />
                {totalDuration}m
              </div>
              <div className="text-sm text-gray-600">Estimated Duration</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {new Set(localOutline.map(item => item.slideLayout)).size}
              </div>
              <div className="text-sm text-gray-600">Layout Types</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Outline Items */}
      <div className="space-y-4">
        {localOutline.map((item, index) => (
          <Card key={item.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="text-xs">
                    Slide {index + 1}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {SLIDE_TEMPLATES.find(t => t.layout === item.slideLayout)?.name || item.slideLayout}
                  </Badge>
                  {item.estimatedDuration && (
                    <Badge variant="outline" className="text-xs flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {item.estimatedDuration}m
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveItem(item.id, 'up')}
                    disabled={index === 0}
                  >
                    <ArrowUp className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveItem(item.id, 'down')}
                    disabled={index === localOutline.length - 1}
                  >
                    <ArrowDown className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                  >
                    <Edit3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteItem(item.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {editingItem === item.id ? (
                <div className="space-y-4">
                  <Input
                    value={item.title}
                    onChange={(e) => handleEditItem(item.id, { title: e.target.value })}
                    placeholder="Slide title"
                  />
                  <Textarea
                    value={item.description}
                    onChange={(e) => handleEditItem(item.id, { description: e.target.value })}
                    placeholder="Slide description"
                    className="min-h-[80px]"
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <Select
                      value={item.slideLayout}
                      onValueChange={(value) => handleEditItem(item.id, { slideLayout: value as SlideLayout })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {SLIDE_TEMPLATES.map((template) => (
                          <SelectItem key={template.id} value={template.layout}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={item.estimatedDuration || 2}
                      onChange={(e) => handleEditItem(item.id, { estimatedDuration: parseInt(e.target.value) })}
                      placeholder="Duration (min)"
                    />
                  </div>
                  <Button
                    size="sm"
                    onClick={() => setEditingItem(null)}
                  >
                    Done Editing
                  </Button>
                </div>
              ) : (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </div>
              )}
            </CardContent>
            
            {/* Add button */}
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAddItem(index)}
                className="bg-white border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-blue-50"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 pt-6">
        <Button variant="outline" onClick={onEdit}>
          <Edit3 className="w-4 h-4 mr-2" />
          Edit Settings
        </Button>
        
        <Button 
          onClick={onApprove}
          disabled={isApproved || localOutline.length === 0}
          size="lg"
        >
          <CheckCircle className="w-5 h-5 mr-2" />
          {isApproved ? 'Outline Approved' : 'Approve & Generate Slides'}
        </Button>
      </div>
    </div>
  );
}
