import React from 'react';
import {
  ChevronUp,
  ChevronDown,
  X,
  FileText,
  PenTool,
  Zap,
  ImageIcon,
  FileImage,
  Upload,
  Eye,
  CheckCircle,
  Bot,
  Microscope,
  Search,
  Loader2,
  TrendingUp
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { ContentItem } from "../types";
import bookAIService from "../services/book-ai.service";

interface ContentItemProps {
  sectionId: string;
  sectionName: string;
  item: ContentItem;
  index: number;
  totalItems: number;
  updateContentItem: (itemId: string, updates: Partial<ContentItem>) => void;
  removeContentItem: (itemId: string) => void;
  moveContentItem: (itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: (items: Set<string>) => void;
  selectedModel: string;
  textPlaceholders: Record<string, string>;
  figurePlaceholders: Record<string, string>;
  defaultTextPrompt: string;
  defaultFigurePrompt: string;
}

export const ContentItemRenderer: React.FC<ContentItemProps> = ({
  sectionId,
  sectionName,
  item,
  index,
  totalItems,
  updateContentItem,
  removeContentItem,
  moveContentItem,
  analyzingItems,
  setAnalyzingItems,
  selectedModel,
  textPlaceholders,
  figurePlaceholders,
  defaultTextPrompt,
  defaultFigurePrompt
}) => {
  
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      updateContentItem(item.id, { content: fileUrl });
      console.log('File uploaded:', file.name);
    }
  };

  const analyzeContent = async () => {
    if (!item.content.trim()) {
      toast.error("Please add some content before analyzing");
      return;
    }

    const itemKey = `${sectionId}-${item.id}`;
    setAnalyzingItems(new Set([...analyzingItems, itemKey]));

    try {
      let analysisPrompt = '';
      
      if (item.type === 'text') {
        analysisPrompt = `Analyze and enhance the following text content for a book chapter titled "${sectionName}". 
        
        Content: ${item.content}
        
        Please provide:
        1. An enhanced, more detailed version of this content
        2. Suggestions for improvement
        3. Additional insights or examples that could strengthen the content
        
        Focus on making the content more engaging and comprehensive for book readers.`;
      } else {
        analysisPrompt = `Analyze this figure/image for a book chapter titled "${sectionName}". 
        
        Caption: ${item.caption || 'No caption provided'}
        
        Please provide:
        1. A detailed description of what this figure should illustrate
        2. How it relates to the chapter content
        3. Suggestions for improving the figure or caption
        4. Additional context that would help readers understand its significance`;
      }

      const analysis = await bookAIService.analyzeContent(analysisPrompt, {
        model: selectedModel,
        maxTokens: 1024
      });

      updateContentItem(item.id, { aiAnalysis: analysis });
      toast.success("Content analysis completed!");
      
    } catch (error: any) {
      console.error('Analysis error:', error);
      toast.error("Analysis failed: " + (error.message || "Please try again"));
    } finally {
      setAnalyzingItems(new Set([...analyzingItems].filter(key => key !== itemKey)));
    }
  };

  const analyzeImage = async () => {
    if (!item.content) {
      toast.error("Please upload an image first");
      return;
    }

    const itemKey = `${sectionId}-${item.id}`;
    setAnalyzingItems(new Set([...analyzingItems, itemKey]));

    try {
      const analysisPrompt = `Analyze this image for a book chapter titled "${sectionName}". 
      
      Describe:
      1. What you see in the image
      2. How it relates to the chapter topic
      3. What insights or concepts it illustrates
      4. Suggestions for how to reference it in the text
      
      Provide a comprehensive analysis that would help integrate this image effectively into the book content.`;

      const analysis = await bookAIService.analyzeImage(item.content, analysisPrompt, {
        model: selectedModel,
        maxTokens: 512
      });

      updateContentItem(item.id, { aiAnalysis: analysis });
      toast.success("Image analysis completed!");
      
    } catch (error: any) {
      console.error('Image analysis error:', error);
      toast.error("Image analysis failed: " + (error.message || "Please try again"));
    } finally {
      setAnalyzingItems(new Set([...analyzingItems].filter(key => key !== itemKey)));
    }
  };

  const isAnalyzing = analyzingItems.has(`${sectionId}-${item.id}`);
  const placeholder = item.type === 'text' 
    ? (textPlaceholders[sectionName] || defaultTextPrompt)
    : (figurePlaceholders[sectionName] || defaultFigurePrompt);

  return (
    <div className="border-2 border-gray-200 rounded-xl p-6 bg-white hover:shadow-md transition-all duration-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            item.type === 'text' ? 'bg-blue-100' : 'bg-green-100'
          }`}>
            {item.type === 'text' ? (
              <FileText className="h-5 w-5 text-blue-600" />
            ) : (
              <ImageIcon className="h-5 w-5 text-green-600" />
            )}
          </div>
          <div>
            <h4 className="font-semibold text-lg">
              {item.type === 'text' ? 'Text Content' : 'Figure'}
            </h4>
            <p className="text-sm text-gray-500">
              Item {index + 1} of {totalItems}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {item.aiAnalysis && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              Analyzed
            </Badge>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => moveContentItem(item.id, 'up')}
            disabled={index === 0}
          >
            <ChevronUp className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => moveContentItem(item.id, 'down')}
            disabled={index === totalItems - 1}
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeContentItem(item.id)}
            className="text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content based on type */}
      {item.type === 'text' ? (
        <div className="space-y-4">
          <Textarea
            value={item.content}
            onChange={(e) => updateContentItem(item.id, { content: e.target.value })}
            placeholder={placeholder}
            rows={6}
            className="resize-none"
          />
          
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {item.content.length} characters
            </div>
            <Button
              onClick={analyzeContent}
              disabled={isAnalyzing || !item.content.trim()}
              size="sm"
              variant="outline"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Bot className="h-4 w-4 mr-2" />
                  Enhance Content
                </>
              )}
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Figure Title */}
          <div>
            <label className="text-sm font-medium mb-2 block">Figure Title</label>
            <Input
              value={item.title || ''}
              onChange={(e) => updateContentItem(item.id, { title: e.target.value })}
              placeholder="Enter figure title..."
            />
          </div>

          {/* Image Upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {item.content ? (
              <div className="space-y-3">
                <img 
                  src={item.content} 
                  alt={item.title || 'Uploaded figure'} 
                  className="max-w-full max-h-64 mx-auto rounded-lg shadow-sm"
                />
                <div className="flex justify-center gap-2">
                  <Button
                    onClick={analyzeImage}
                    disabled={isAnalyzing}
                    size="sm"
                    variant="outline"
                  >
                    {isAnalyzing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Microscope className="h-4 w-4 mr-2" />
                        Analyze Image
                      </>
                    )}
                  </Button>
                  <label className="cursor-pointer">
                    <Button size="sm" variant="outline" asChild>
                      <span>
                        <Upload className="h-4 w-4 mr-2" />
                        Replace
                      </span>
                    </Button>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
            ) : (
              <label className="cursor-pointer">
                <div className="space-y-3">
                  <FileImage className="h-12 w-12 mx-auto text-gray-400" />
                  <div>
                    <p className="text-lg font-medium text-gray-700">Upload Figure</p>
                    <p className="text-sm text-gray-500">Click to select an image file</p>
                  </div>
                </div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            )}
          </div>

          {/* Figure Caption */}
          <div>
            <label className="text-sm font-medium mb-2 block">Figure Caption</label>
            <Textarea
              value={item.caption || ''}
              onChange={(e) => updateContentItem(item.id, { caption: e.target.value })}
              placeholder="Describe what this figure shows and its relevance to the chapter..."
              rows={3}
            />
          </div>
        </div>
      )}

      {/* AI Analysis Display */}
      {item.aiAnalysis && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800">AI Analysis & Suggestions</span>
          </div>
          <div className="text-sm text-blue-700 whitespace-pre-wrap">
            {item.aiAnalysis}
          </div>
        </div>
      )}
    </div>
  );
};
