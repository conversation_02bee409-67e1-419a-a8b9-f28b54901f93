﻿import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import {
  X,
  Plus,
  BookOpen,
  Users,
  Target,
  Lightbulb,
  FileText,
  Sparkles,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  Zap,
  Brain,
  Wand2,
  <PERSON><PERSON><PERSON>,
  Eye,
  Clock,
  TrendingUp,
  BookMarked,
  Layers,
  ArrowRight
} from "lucide-react";
import { BookMetadata, UserChapter } from '../types';
import { BOOK_GENRES, TARGET_AUDIENCES, BOOK_TONES, BOOK_LENGTHS } from '../constants';
import { topicAnalysisService, TopicAnalysisResult } from '../services/topic-analysis.service';
import { smartChapterService, ChapterEstimation } from '../services/smart-chapter.service';

interface BookMetadataFormProps {
  metadata: BookMetadata;
  onMetadataChange: (metadata: BookMetadata) => void;
  onNext: () => void;
  onAutoGenerate?: (metadata: BookMetadata, chapters: UserChapter[]) => void;
}

export const BookMetadataForm: React.FC<BookMetadataFormProps> = ({
  metadata,
  onMetadataChange,
  onNext,
  onAutoGenerate
}) => {
  const [newKeyword, setNewKeyword] = useState('');
  const [newAuthor, setNewAuthor] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [isAnalyzingTopic, setIsAnalyzingTopic] = useState(false);
  const [topicSuggestions, setTopicSuggestions] = useState<string[]>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [topicAnalysis, setTopicAnalysis] = useState<TopicAnalysisResult | null>(null);

  // Smart chapter management
  const [chapterEstimation, setChapterEstimation] = useState<ChapterEstimation | null>(null);
  const [useManualChapters, setUseManualChapters] = useState(false);
  const [manualChapters, setManualChapters] = useState<UserChapter[]>([]);
  const [activeTab, setActiveTab] = useState('basic');
  const [isGeneratingEstimation, setIsGeneratingEstimation] = useState(false);

  const handleInputChange = (field: keyof BookMetadata, value: any) => {
    onMetadataChange({
      ...metadata,
      [field]: value
    });
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !metadata.keywords.includes(newKeyword.trim())) {
      handleInputChange('keywords', [...metadata.keywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    handleInputChange('keywords', metadata.keywords.filter(k => k !== keyword));
  };

  const addAuthor = () => {
    if (newAuthor.trim() && !metadata.authors.includes(newAuthor.trim())) {
      handleInputChange('authors', [...metadata.authors, newAuthor.trim()]);
      setNewAuthor('');
    }
  };

  const removeAuthor = (author: string) => {
    handleInputChange('authors', metadata.authors.filter(a => a !== author));
  };

  // Smart chapter estimation
  const generateChapterEstimation = async () => {
    if (!metadata.title.trim() || !metadata.description.trim() || !metadata.estimatedLength) return;

    setIsGeneratingEstimation(true);
    try {
      const estimation = smartChapterService.estimateChapters(metadata);
      setChapterEstimation(estimation);

      // Auto-generate chapter structure
      const autoChapters: UserChapter[] = estimation.suggestedChapterTitles.map((title, index) => ({
        id: `chapter-${index + 1}`,
        outline: {
          title,
          description: `Chapter covering ${title.toLowerCase()} with detailed analysis and practical insights.`,
          estimatedWordCount: estimation.estimatedWordsPerChapter,
          subSections: []
        },
        items: []
      }));

      setManualChapters(autoChapters);
    } catch (error) {
      console.error('Chapter estimation failed:', error);
    } finally {
      setIsGeneratingEstimation(false);
    }
  };

  // AI Topic Analysis Function
  const analyzeTopicAndSuggest = async () => {
    if (!metadata.title.trim() || !metadata.description.trim()) return;

    setIsAnalyzingTopic(true);
    try {
      const analysis = await topicAnalysisService.analyzeBookTopic(metadata);
      setTopicAnalysis(analysis);
      setTopicSuggestions(analysis.suggestions);

      // Auto-add suggested keywords if user has few keywords
      if (metadata.keywords.length < 3 && analysis.keywords.length > 0) {
        const newKeywords = analysis.keywords.filter(
          keyword => !metadata.keywords.includes(keyword)
        ).slice(0, 5);

        if (newKeywords.length > 0) {
          handleInputChange('keywords', [...metadata.keywords, ...newKeywords]);
        }
      }

      // Auto-generate chapter estimation if basic info is complete
      if (metadata.estimatedLength) {
        await generateChapterEstimation();
      }
    } catch (error) {
      console.error('Topic analysis failed:', error);
      // Fallback to basic suggestions
      const suggestions = [
        `Key concepts in ${metadata.title.toLowerCase()}`,
        `Advanced techniques for ${metadata.genre.toLowerCase()}`,
        `Case studies and practical applications`,
        `Implementation strategies and best practices`,
        `Common challenges and solutions`
      ];
      setTopicSuggestions(suggestions);
    } finally {
      setIsAnalyzingTopic(false);
    }
  };

  // Auto-generate estimation when key fields change
  useEffect(() => {
    if (metadata.title && metadata.description && metadata.estimatedLength && metadata.genre) {
      const timer = setTimeout(() => {
        generateChapterEstimation();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [metadata.title, metadata.description, metadata.estimatedLength, metadata.genre]);

  const getFormCompletionPercentage = () => {
    const fields = [
      metadata.title.trim(),
      metadata.genre,
      metadata.targetAudience,
      metadata.tone,
      metadata.description.trim(),
      metadata.estimatedLength,
      metadata.keywords.length > 0 ? 'keywords' : '',
      metadata.authors.length > 0 ? 'authors' : ''
    ];

    const completedFields = fields.filter(field => field !== '').length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const isFormValid = () => {
    return metadata.title.trim() !== '' &&
           metadata.genre !== '' &&
           metadata.targetAudience !== '' &&
           metadata.tone !== '' &&
           metadata.description.trim() !== '' &&
           metadata.estimatedLength !== '';
  };

  const selectedTone = BOOK_TONES.find(tone => tone.id === metadata.tone);
  const selectedLength = BOOK_LENGTHS.find(length => length.id === metadata.estimatedLength);
  const completionPercentage = getFormCompletionPercentage();

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Modern Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-4 mb-6">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-yellow-800" />
            </div>
          </div>
          <div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              AI Book Generator
            </h1>
            <p className="text-lg text-gray-500 mt-2">Powered by Advanced AI • Generate Complete Books</p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto mb-8">
          <p className="text-xl text-gray-600 mb-6">
            Transform your ideas into comprehensive, professionally-structured books with our intelligent AI system.
            From outline generation to complete content creation.
          </p>

          {/* Enhanced Progress Indicator */}
          <div className="max-w-lg mx-auto">
            <div className="flex items-center justify-between text-sm font-medium text-gray-700 mb-3">
              <span className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Setup Progress
              </span>
              <span className="flex items-center gap-2">
                {completionPercentage}%
                {completionPercentage === 100 && <CheckCircle className="h-4 w-4 text-green-600" />}
              </span>
            </div>
            <div className="relative">
              <Progress value={completionPercentage} className="h-3 bg-gray-100" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-20"></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>Basic Info</span>
              <span>AI Analysis</span>
              <span>Ready to Generate</span>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Tabbed Interface */}
      <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-lg">
        <CardHeader className="pb-0">
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
            Book Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <BookMarked className="h-4 w-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="structure" className="flex items-center gap-2">
                <Layers className="h-4 w-4" />
                Book Structure
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Advanced Options
              </TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Title */}
                  <div className="space-y-3">
                    <Label htmlFor="title" className="text-base font-semibold flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                      Book Title *
                    </Label>
                    <Input
                      id="title"
                      placeholder="Enter your book title..."
                      value={metadata.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className="text-lg h-12"
                    />
                  </div>

                  {/* Subtitle */}
                  <div className="space-y-3">
                    <Label htmlFor="subtitle" className="text-base font-semibold">
                      Subtitle (Optional)
                    </Label>
                    <Input
                      id="subtitle"
                      placeholder="Add a descriptive subtitle..."
                      value={metadata.subtitle}
                      onChange={(e) => handleInputChange('subtitle', e.target.value)}
                      className="h-11"
                    />
                  </div>

                  {/* Genre */}
                  <div className="space-y-3">
                    <Label className="text-base font-semibold flex items-center gap-2">
                      <Target className="h-5 w-5 text-purple-600" />
                      Genre *
                    </Label>
                    <Select value={metadata.genre} onValueChange={(value) => handleInputChange('genre', value)}>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select book genre" />
                      </SelectTrigger>
                      <SelectContent>
                        {BOOK_GENRES.map((genre) => (
                          <SelectItem key={genre.id} value={genre.id}>
                            <div className="flex items-center gap-2">
                              <span>{genre.icon}</span>
                              <span>{genre.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Target Audience */}
                  <div className="space-y-3">
                    <Label className="text-base font-semibold flex items-center gap-2">
                      <Users className="h-5 w-5 text-green-600" />
                      Target Audience *
                    </Label>
                    <Select value={metadata.targetAudience} onValueChange={(value) => handleInputChange('targetAudience', value)}>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select target audience" />
                      </SelectTrigger>
                      <SelectContent>
                        {TARGET_AUDIENCES.map((audience) => (
                          <SelectItem key={audience.id} value={audience.id}>
                            {audience.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Book Length */}
                  <div className="space-y-3">
                    <Label className="text-base font-semibold flex items-center gap-2">
                      <Clock className="h-5 w-5 text-orange-600" />
                      Estimated Length *
                    </Label>
                    <Select value={metadata.estimatedLength} onValueChange={(value) => handleInputChange('estimatedLength', value)}>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select book length" />
                      </SelectTrigger>
                      <SelectContent>
                        {BOOK_LENGTHS.map((length) => (
                          <SelectItem key={length.id} value={length.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{length.name}</span>
                              <span className="text-xs text-gray-500">{length.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Writing Tone */}
                  <div className="space-y-3">
                    <Label className="text-base font-semibold">Writing Tone *</Label>
                    <Select value={metadata.tone} onValueChange={(value) => handleInputChange('tone', value)}>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select writing tone" />
                      </SelectTrigger>
                      <SelectContent>
                        {BOOK_TONES.map((tone) => (
                          <SelectItem key={tone.id} value={tone.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{tone.name}</span>
                              <span className="text-xs text-gray-500">{tone.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* AI Analysis Trigger */}
                  {metadata.title.trim() && metadata.description.trim() && (
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Brain className="h-5 w-5 text-blue-600" />
                          <span className="font-medium text-blue-800">AI Topic Analysis</span>
                        </div>
                        <Button
                          onClick={analyzeTopicAndSuggest}
                          disabled={isAnalyzingTopic}
                          size="sm"
                          variant="outline"
                        >
                          {isAnalyzingTopic ? (
                            <>
                              <Zap className="h-4 w-4 mr-2 animate-pulse" />
                              Analyzing...
                            </>
                          ) : (
                            <>
                              <Sparkles className="h-4 w-4 mr-2" />
                              Analyze Topic
                            </>
                          )}
                        </Button>
                      </div>
                      <p className="text-sm text-blue-700">
                        Get AI-powered insights and suggestions to improve your book's structure and content quality.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Book Description */}
              <div className="md:col-span-2 space-y-3">
                <Label htmlFor="description" className="text-base font-semibold flex items-center gap-2">
                  <FileText className="h-5 w-5 text-indigo-600" />
                  Book Description *
                  <HelpCircle className="h-4 w-4 text-gray-400" title="Detailed description helps AI generate better outlines" />
                </Label>
                <Textarea
                  id="description"
                  placeholder="Describe your book's purpose, target outcomes, main themes, and key concepts. Be specific about what readers will learn and how the book is structured. Example: 'This book teaches advanced machine learning techniques for data scientists, covering neural networks, deep learning, and practical implementation strategies...'"
                  value={metadata.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={6}
                  className="resize-none text-base"
                />
                <div className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-800 mb-1">💡 Pro Tip for Better AI Outlines:</p>
                    <ul className="text-blue-700 space-y-1 text-xs">
                      <li>• Mention specific topics, concepts, or methodologies you want covered</li>
                      <li>• Include your target learning outcomes or book objectives</li>
                      <li>• Reference any particular approach or framework you'll use</li>
                      <li>• Note if there are specific industries, use cases, or examples to include</li>
                    </ul>
                  </div>
                </div>
                <div className="text-xs text-gray-500 flex items-center justify-between">
                  <span>Character count: {metadata.description.length}</span>
                  <span className={metadata.description.length >= 200 ? "text-green-600" : "text-orange-500"}>
                    {metadata.description.length >= 200 ? "✓ Good detail level" : "Add more details for better AI outlines"}
                  </span>
                </div>
              </div>
            </TabsContent>

            {/* Book Structure Tab */}
            <TabsContent value="structure" className="space-y-6">
              {/* Smart Chapter Estimation */}
              {chapterEstimation && (
                <Card className="border-2 border-green-200 bg-green-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg text-green-800">
                      <TrendingUp className="h-5 w-5" />
                      AI Chapter Analysis
                      <Badge variant="outline" className="ml-auto">
                        {chapterEstimation.recommendedChapters} chapters recommended
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-white rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{chapterEstimation.recommendedChapters}</div>
                        <div className="text-sm text-green-700">Chapters</div>
                      </div>
                      <div className="text-center p-3 bg-white rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{chapterEstimation.estimatedPages}</div>
                        <div className="text-sm text-blue-700">Pages</div>
                      </div>
                      <div className="text-center p-3 bg-white rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{chapterEstimation.estimatedWordsPerChapter.toLocaleString()}</div>
                        <div className="text-sm text-purple-700">Words/Chapter</div>
                      </div>
                    </div>

                    <div className="p-3 bg-white rounded-lg">
                      <p className="text-sm text-green-700">
                        <strong>AI Reasoning:</strong> {chapterEstimation.reasoning}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Chapter Management Options */}
              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Layers className="h-5 w-5" />
                    Chapter Structure
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium">Manual Chapter Definition</h4>
                      <p className="text-sm text-gray-600">Define your own chapter titles and descriptions</p>
                    </div>
                    <Switch
                      checked={useManualChapters}
                      onCheckedChange={setUseManualChapters}
                    />
                  </div>

                  {!useManualChapters && chapterEstimation && (
                    <div className="space-y-3">
                      <h4 className="font-medium">AI-Suggested Chapter Titles:</h4>
                      <div className="grid gap-2">
                        {chapterEstimation.suggestedChapterTitles.map((title, index) => (
                          <div key={index} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                            <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                              {index + 1}
                            </Badge>
                            <span className="text-sm font-medium">{title}</span>
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-gray-500">
                        These chapters will be automatically created with AI-generated descriptions. You can customize them later.
                      </p>
                    </div>
                  )}

                  {useManualChapters && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Custom Chapters</h4>
                        <Button
                          onClick={() => {
                            const newChapter: UserChapter = {
                              id: `chapter-${manualChapters.length + 1}`,
                              outline: {
                                title: `Chapter ${manualChapters.length + 1}`,
                                description: '',
                                estimatedWordCount: chapterEstimation?.estimatedWordsPerChapter || 3000,
                                subSections: []
                              },
                              items: []
                            };
                            setManualChapters([...manualChapters, newChapter]);
                          }}
                          size="sm"
                          variant="outline"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Chapter
                        </Button>
                      </div>

                      <div className="space-y-3 max-h-64 overflow-y-auto">
                        {manualChapters.map((chapter, index) => (
                          <div key={chapter.id} className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline">{index + 1}</Badge>
                              <Input
                                placeholder="Chapter title..."
                                value={chapter.outline.title}
                                onChange={(e) => {
                                  const updated = [...manualChapters];
                                  updated[index].outline.title = e.target.value;
                                  setManualChapters(updated);
                                }}
                                className="flex-1"
                              />
                              <Button
                                onClick={() => {
                                  setManualChapters(manualChapters.filter((_, i) => i !== index));
                                }}
                                size="sm"
                                variant="ghost"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                            <Textarea
                              placeholder="Chapter description..."
                              value={chapter.outline.description}
                              onChange={(e) => {
                                const updated = [...manualChapters];
                                updated[index].outline.description = e.target.value;
                                setManualChapters(updated);
                              }}
                              rows={2}
                              className="text-sm"
                            />
                          </div>
                        ))}
                      </div>

                      {manualChapters.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <Layers className="h-12 w-12 mx-auto mb-3 opacity-50" />
                          <p>No chapters defined yet. Click "Add Chapter" to start.</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Advanced Options Tab */}
            <TabsContent value="advanced" className="space-y-6">
              {/* Keywords Section */}
              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Keywords & Topics
                    <Badge variant="outline" className="ml-auto">
                      {metadata.keywords.length}/10 recommended
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add specific terms, concepts, or topics (e.g., 'machine learning', 'data visualization', 'Python programming')..."
                      value={newKeyword}
                      onChange={(e) => setNewKeyword(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                      className="flex-1"
                    />
                    <Button onClick={addKeyword} size="sm" variant="outline">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Keyword suggestions from topic analysis */}
                  {topicSuggestions.length > 0 && metadata.keywords.length < 5 && (
                    <div className="p-3 bg-yellow-50 rounded-lg">
                      <p className="text-sm font-medium text-yellow-800 mb-2">💡 Suggested keywords from your topic:</p>
                      <div className="flex flex-wrap gap-2">
                        {topicSuggestions.slice(0, 4).map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="text-xs h-7"
                            onClick={() => {
                              const keyword = suggestion.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
                              if (keyword && !metadata.keywords.includes(keyword)) {
                                handleInputChange('keywords', [...metadata.keywords, keyword]);
                              }
                            }}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            {suggestion.split(' ').slice(0, 3).join(' ')}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}

                  {metadata.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {metadata.keywords.map((keyword) => (
                        <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                          {keyword}
                          <X
                            className="h-3 w-3 cursor-pointer hover:text-red-500"
                            onClick={() => removeKeyword(keyword)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}

                  <p className="text-xs text-gray-500">
                    Keywords help the AI understand your book's focus and generate more relevant chapter outlines.
                    Include technical terms, methodologies, and key concepts.
                  </p>
                </CardContent>
              </Card>

              {/* Authors Section */}
              <Card className="border border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Authors
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add author name..."
                      value={newAuthor}
                      onChange={(e) => setNewAuthor(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addAuthor()}
                      className="flex-1"
                    />
                    <Button onClick={addAuthor} size="sm" variant="outline">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {metadata.authors.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {metadata.authors.map((author) => (
                        <Badge key={author} variant="secondary" className="flex items-center gap-1">
                          {author}
                          <X
                            className="h-3 w-3 cursor-pointer hover:text-red-500"
                            onClick={() => removeAuthor(author)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Enhanced Topic Analysis Results */}
      {topicAnalysis && (
        <Card className={`border-2 ${
          topicAnalysis.quality === 'excellent' ? 'border-green-200 bg-green-50' :
          topicAnalysis.quality === 'good' ? 'border-blue-200 bg-blue-50' :
          'border-orange-200 bg-orange-50'
        }`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 text-lg ${
              topicAnalysis.quality === 'excellent' ? 'text-green-800' :
              topicAnalysis.quality === 'good' ? 'text-blue-800' :
              'text-orange-800'
            }`}>
              {topicAnalysis.quality === 'excellent' ? <CheckCircle className="h-5 w-5" /> :
               topicAnalysis.quality === 'good' ? <Target className="h-5 w-5" /> :
               <AlertCircle className="h-5 w-5" />}
              AI Topic Analysis Complete
              <Badge variant="outline" className="ml-auto">
                Score: {topicAnalysis.score}/100
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Quality Assessment */}
            <div className={`p-3 rounded-lg ${
              topicAnalysis.quality === 'excellent' ? 'bg-green-100' :
              topicAnalysis.quality === 'good' ? 'bg-blue-100' :
              'bg-orange-100'
            }`}>
              <p className={`font-medium ${
                topicAnalysis.quality === 'excellent' ? 'text-green-800' :
                topicAnalysis.quality === 'good' ? 'text-blue-800' :
                'text-orange-800'
              }`}>
                {topicAnalysis.quality === 'excellent' ? '🎯 Excellent Topic Quality' :
                 topicAnalysis.quality === 'good' ? '👍 Good Topic Quality' :
                 '⚠️ Topic Needs Improvement'}
              </p>
              <p className={`text-sm mt-1 ${
                topicAnalysis.quality === 'excellent' ? 'text-green-700' :
                topicAnalysis.quality === 'good' ? 'text-blue-700' :
                'text-orange-700'
              }`}>
                {topicAnalysis.quality === 'excellent' ?
                  'Your topic is well-defined and will generate excellent AI outlines with specific, detailed content.' :
                 topicAnalysis.quality === 'good' ?
                  'Your topic is good and will generate quality AI outlines. Consider the suggestions below for even better results.' :
                  'Your topic needs more detail for the AI to generate high-quality, specific outlines. Please review the improvements below.'}
              </p>
            </div>

            {/* Improvements (if needed) */}
            {topicAnalysis.improvements.length > 0 && (
              <div className="space-y-2">
                <p className="font-medium text-orange-800">🔧 Recommended Improvements:</p>
                <ul className="space-y-1">
                  {topicAnalysis.improvements.map((improvement, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-orange-700">
                      <span className="text-orange-500 mt-0.5">•</span>
                      {improvement}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Topic Suggestions */}
            {topicSuggestions.length > 0 && (
              <div className="space-y-2">
                <p className="font-medium text-gray-800">💡 Chapter Topic Suggestions:</p>
                <div className="grid md:grid-cols-2 gap-2">
                  {topicSuggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-white rounded border">
                      <Lightbulb className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">{suggestion}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Enhanced Action Buttons */}
      <Card className="border-2 border-dashed border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardContent className="pt-6">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2">
              {isFormValid() ? (
                <>
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <span className="text-lg font-semibold text-green-800">Ready to Generate!</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-6 w-6 text-orange-500" />
                  <span className="text-lg font-semibold text-orange-800">Complete Required Fields</span>
                </>
              )}
            </div>

            {!isFormValid() && (
              <div className="text-sm text-gray-600">
                <p className="mb-2">Please complete these required fields:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {!metadata.title.trim() && <Badge variant="outline">Title</Badge>}
                  {!metadata.genre && <Badge variant="outline">Genre</Badge>}
                  {!metadata.targetAudience && <Badge variant="outline">Target Audience</Badge>}
                  {!metadata.tone && <Badge variant="outline">Writing Tone</Badge>}
                  {!metadata.description.trim() && <Badge variant="outline">Description</Badge>}
                  {!metadata.estimatedLength && <Badge variant="outline">Estimated Length</Badge>}
                </div>
              </div>
            )}

            {isFormValid() && (
              <div className="space-y-4">
                <div className="bg-white p-4 rounded-lg border">
                  <p className="text-gray-800 font-medium mb-2">🎯 Choose Your Generation Mode:</p>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="text-left">
                      <p className="font-medium text-blue-800">📝 Step-by-Step Mode</p>
                      <p className="text-sm text-blue-700">Review outlines, customize chapters, and control each step</p>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-purple-800">🚀 Auto-Generation Mode</p>
                      <p className="text-sm text-purple-700">One-click complete book generation with AI intelligence</p>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={onNext}
                    size="lg"
                    variant="outline"
                    className="px-8 border-2 border-blue-500 text-blue-700 hover:bg-blue-50"
                  >
                    <Eye className="h-5 w-5 mr-2" />
                    Step-by-Step Generation
                  </Button>

                  {onAutoGenerate && (
                    <Button
                      onClick={() => {
                        const chapters = useManualChapters ? manualChapters :
                          chapterEstimation ? chapterEstimation.suggestedChapterTitles.map((title, index) => ({
                            id: `chapter-${index + 1}`,
                            outline: {
                              title,
                              description: `Chapter covering ${title.toLowerCase()} with detailed analysis and practical insights.`,
                              estimatedWordCount: chapterEstimation.estimatedWordsPerChapter,
                              subSections: []
                            },
                            items: []
                          })) : [];

                        onAutoGenerate(metadata, chapters);
                      }}
                      size="lg"
                      className="px-8 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      <Wand2 className="h-5 w-5 mr-2" />
                      Auto-Generate Complete Book
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                </div>

                {chapterEstimation && (
                  <div className="text-xs text-gray-500 max-w-md mx-auto">
                    Will generate {chapterEstimation.recommendedChapters} chapters • ~{chapterEstimation.estimatedPages} pages •
                    {useManualChapters ? ' Using your custom chapters' : ' Using AI-suggested structure'}
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
