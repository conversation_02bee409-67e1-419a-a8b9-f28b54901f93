/**
 * Results List Component
 * Displays search results with selection functionality
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, ExternalLink, FileText, Star } from 'lucide-react';

import { ResultsListProps } from '../types';

export const ResultsList: React.FC<ResultsListProps> = ({
  results,
  selectedResults,
  maxSelections,
  onResultSelect,
  onRemoveResult,
}) => {
  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-2">
          <FileText className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-600 mb-2">No Results Found</h3>
        <p className="text-gray-500">
          Try adjusting your search query or using different keywords.
        </p>
      </div>
    );
  }

  // Separate custom URLs and regular results
  const customResults = results.filter(r => r.isCustomUrl);
  const searchResults = results.filter(r => !r.isCustomUrl);

  return (
    <div className="space-y-4">
      {/* Selection Info */}
      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="text-sm text-blue-800">
          <span className="font-medium">
            {selectedResults.length} of {maxSelections} sources selected
          </span>
          {selectedResults.length > 0 && (
            <span className="ml-2 text-blue-600">
              • Ready to generate report
            </span>
          )}
        </div>
        {selectedResults.length === maxSelections && (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            Maximum reached
          </Badge>
        )}
      </div>

      {/* Custom URLs Section */}
      {customResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Custom Sources ({customResults.length})
          </h3>
          {customResults.map((result) => (
            <ResultCard
              key={result.id}
              result={result}
              isSelected={selectedResults.includes(result.id)}
              canSelect={selectedResults.includes(result.id) || selectedResults.length < maxSelections}
              onSelect={() => onResultSelect(result.id)}
              onRemove={() => onRemoveResult(result.id)}
              showRemove={true}
            />
          ))}
        </div>
      )}

      {/* Search Results Section */}
      {searchResults.length > 0 && (
        <div className="space-y-3">
          {customResults.length > 0 && (
            <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              Web Results ({searchResults.length})
            </h3>
          )}
          {searchResults.map((result) => (
            <ResultCard
              key={result.id}
              result={result}
              isSelected={selectedResults.includes(result.id)}
              canSelect={selectedResults.includes(result.id) || selectedResults.length < maxSelections}
              onSelect={() => onResultSelect(result.id)}
              onRemove={() => onRemoveResult(result.id)}
              showRemove={false}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface ResultCardProps {
  result: any;
  isSelected: boolean;
  canSelect: boolean;
  onSelect: () => void;
  onRemove: () => void;
  showRemove: boolean;
}

const ResultCard: React.FC<ResultCardProps> = ({
  result,
  isSelected,
  canSelect,
  onSelect,
  onRemove,
  showRemove,
}) => {
  return (
    <Card 
      className={`overflow-hidden transition-all duration-200 ${
        isSelected 
          ? 'border-blue-300 bg-blue-50 shadow-md' 
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      } ${result.isCustomUrl ? 'border-l-4 border-l-blue-500' : ''}`}
    >
      <CardContent className="p-4">
        <div className="flex gap-4">
          {/* Selection Checkbox */}
          <div className="pt-1">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onSelect}
              disabled={!canSelect}
              className="w-5 h-5"
            />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <a
                href={result.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
              >
                <h3 
                  className="text-lg font-semibold line-clamp-2"
                  dangerouslySetInnerHTML={{ __html: result.name }}
                />
              </a>
              
              <div className="flex items-center gap-2 flex-shrink-0">
                {/* Score Badge */}
                {result.score && result.score > 0 && (
                  <Badge 
                    variant="outline" 
                    className="text-xs flex items-center gap-1"
                  >
                    <Star className="h-3 w-3" />
                    {(result.score * 100).toFixed(0)}%
                  </Badge>
                )}
                
                {/* Custom URL Badge */}
                {result.isCustomUrl && (
                  <Badge variant="secondary" className="text-xs">
                    Custom
                  </Badge>
                )}
                
                {/* Remove Button */}
                {showRemove && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRemove}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* URL */}
            <p className="text-green-700 text-sm truncate mb-2">
              {result.url}
            </p>

            {/* Snippet */}
            <p 
              className="text-gray-600 text-sm line-clamp-3"
              dangerouslySetInnerHTML={{ __html: result.snippet }}
            />

            {/* Metadata */}
            {(result.content || result.isCustomUrl) && (
              <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                {result.content && (
                  <Badge variant="outline" className="text-xs">
                    Full content available
                  </Badge>
                )}
                {result.isCustomUrl && (
                  <Badge variant="outline" className="text-xs">
                    User added
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
