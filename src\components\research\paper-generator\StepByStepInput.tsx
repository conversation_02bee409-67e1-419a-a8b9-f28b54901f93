import React from 'react';
import { ChevronLeft, ChevronRight, CheckCircle, AlertCircle, Sparkles, Bot, Zap, FileText, FlaskConical, BarChart3, BookOpen, Lightbulb, PenTool, Brain } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserInputs, UserSection, PaperMetadata } from './types';
import { PaperMetadataForm } from './PaperMetadataForm';
import { EnhancedSectionInput } from './EnhancedSectionInput';
import { SECTION_TYPES } from './constants';
import { AIModelSelector } from './AIModelSelector';
import { AI_MODELS } from './enhanced-ai.service';

interface StepByStepInputProps {
  userInputs: UserInputs;
  updateMetadata: (metadata: Partial<PaperMetadata>) => void;
  addUserSection: (sectionType: string) => void;
  removeUserSection: (sectionId: string) => void;
  addContentItem: (sectionId: string, type: 'text' | 'figure') => void;
  updateContentItem: (sectionId: string, itemId: string, updates: any) => void;
  removeContentItem: (sectionId: string, itemId: string) => void;
  moveContentItem: (sectionId: string, itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  inputStep: 'title' | 'methodology' | 'results' | 'additional';
  setInputStep: (step: 'title' | 'methodology' | 'results' | 'additional') => void;
  onProceedToGeneration: () => void;
  sectionAnalysis: Record<string, { feedback: string; suggestions: string[]; isAnalyzing: boolean }>;
  analyzeSectionContent: (sectionId: string, sectionName: string) => void;
  fixSectionContent: (sectionId: string, sectionName: string) => void;
  isFixing: Set<string>;
  addFigureWithData?: (sectionId: string, figureData: {
    content: string;
    title: string;
    caption: string;
    analysis: string;
    originalFile: File;
  }) => void;
  useEnhancedCitations?: boolean;
  setUseEnhancedCitations?: (value: boolean) => void;
  useAIValidation?: boolean;
  setUseAIValidation?: (value: boolean) => void;
  useRealCitations?: boolean;
  setUseRealCitations?: (value: boolean) => void;
}

export const StepByStepInput: React.FC<StepByStepInputProps> = ({
  userInputs,
  updateMetadata,
  addUserSection,
  removeUserSection,
  addContentItem,
  updateContentItem,
  removeContentItem,
  moveContentItem,
  analyzingItems,
  setAnalyzingItems,
  selectedModel,
  setSelectedModel,
  inputStep,
  setInputStep,
  onProceedToGeneration,
  sectionAnalysis,
  analyzeSectionContent,
  fixSectionContent,
  isFixing,
  addFigureWithData,
  useEnhancedCitations = true,
  setUseEnhancedCitations,
  useAIValidation = true,
  setUseAIValidation,
  useRealCitations = true,
  setUseRealCitations
}) => {
  const steps = [
    { id: 'title', name: 'Title & Info', icon: FileText, description: 'Basic paper information' },
    { id: 'methodology', name: 'Methodology', icon: FlaskConical, description: 'Research methods & procedures' },
    { id: 'results', name: 'Results', icon: BarChart3, description: 'Findings & data analysis' },
    { id: 'additional', name: 'Additional Sections', icon: BookOpen, description: 'Optional sections' }
  ];

  const currentStepIndex = steps.findIndex(step => step.id === inputStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const hasTitle = userInputs.metadata.title.trim() !== '';
  const hasMethodology = userInputs.userSections.some(s => s.name === 'Methodology' && s.items.length > 0);
  const hasResults = userInputs.userSections.some(s => s.name === 'Results' && s.items.length > 0);

  const canProceedFromTitle = hasTitle;
  const canProceedFromMethodology = hasMethodology;
  const canProceedFromResults = hasResults;
  const canGenerate = hasTitle && hasMethodology && hasResults;

  const getStepStatus = (stepId: string) => {
    switch (stepId) {
      case 'title': return hasTitle ? 'complete' : 'current';
      case 'methodology': return hasMethodology ? 'complete' : (inputStep === 'methodology' ? 'current' : 'pending');
      case 'results': return hasResults ? 'complete' : (inputStep === 'results' ? 'current' : 'pending');
      case 'additional': return inputStep === 'additional' ? 'current' : 'pending';
      default: return 'pending';
    }
  };

  const nextStep = () => {
    const nextIndex = Math.min(currentStepIndex + 1, steps.length - 1);
    setInputStep(steps[nextIndex].id as any);
  };

  const prevStep = () => {
    const prevIndex = Math.max(currentStepIndex - 1, 0);
    setInputStep(steps[prevIndex].id as any);
  };

  const renderStepContent = () => {
    switch (inputStep) {
      case 'title':
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Paper Information</h2>
              <p className="text-gray-600 text-lg">Start by providing basic information about your research paper</p>
            </div>
            
            <PaperMetadataForm 
              metadata={userInputs.metadata}
              updateMetadata={updateMetadata}
            />
          </div>
        );

      case 'methodology':
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FlaskConical className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Methodology Section</h2>
              <p className="text-gray-600 text-lg">Describe your research methods, procedures, and experimental setup</p>
              <Badge variant="secondary" className="mt-2 bg-red-100 text-red-800">Required</Badge>
            </div>
            
            <EnhancedSectionInput
              sectionName="Methodology"
              sectionType={SECTION_TYPES.find(s => s.name === 'Methodology')!}
              userInputs={userInputs}
              addUserSection={addUserSection}
              removeUserSection={removeUserSection}
              addContentItem={addContentItem}
              updateContentItem={updateContentItem}
              removeContentItem={removeContentItem}
              moveContentItem={moveContentItem}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
              sectionAnalysis={sectionAnalysis}
              analyzeSectionContent={analyzeSectionContent}
              fixSectionContent={fixSectionContent}
              isFixing={isFixing}
              addFigureWithData={addFigureWithData}
            />
          </div>
        );

      case 'results':
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Results Section</h2>
              <p className="text-gray-600 text-lg">Present your findings, data analysis, and key observations</p>
              <Badge variant="secondary" className="mt-2 bg-red-100 text-red-800">Required</Badge>
            </div>
            
            <EnhancedSectionInput
              sectionName="Results"
              sectionType={SECTION_TYPES.find(s => s.name === 'Results')!}
              userInputs={userInputs}
              addUserSection={addUserSection}
              removeUserSection={removeUserSection}
              addContentItem={addContentItem}
              updateContentItem={updateContentItem}
              removeContentItem={removeContentItem}
              moveContentItem={moveContentItem}
              analyzingItems={analyzingItems}
              setAnalyzingItems={setAnalyzingItems}
              selectedModel={selectedModel}
              sectionAnalysis={sectionAnalysis}
              analyzeSectionContent={analyzeSectionContent}
              fixSectionContent={fixSectionContent}
              isFixing={isFixing}
              addFigureWithData={addFigureWithData}
            />
          </div>
        );

      case 'additional':
        const additionalSections = SECTION_TYPES.filter(s => 
          !['Methodology', 'Results', 'Keywords', 'References'].includes(s.name)
        );
        
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-10 w-10 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Additional Sections</h2>
              <p className="text-gray-600 text-lg">Add optional sections to enhance your research paper</p>
              <Badge variant="outline" className="mt-2">Optional</Badge>
            </div>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {additionalSections.map((sectionType) => {
                const isAdded = userInputs.userSections.some(s => s.name === sectionType.name);
                return (
                  <Card key={sectionType.id} className={`cursor-pointer transition-all duration-200 ${
                    isAdded ? 'border-green-300 bg-green-50' : 'hover:border-blue-300 hover:shadow-md'
                  }`}>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          isAdded ? 'bg-green-200' : sectionType.color
                        }`}>
                          <sectionType.icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-lg">{sectionType.name}</CardTitle>
                          <p className="text-sm text-gray-600">{sectionType.description}</p>
                        </div>
                        {isAdded && (
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <Button
                        onClick={() => isAdded ? removeUserSection(userInputs.userSections.find(s => s.name === sectionType.name)?.id || '') : addUserSection(sectionType.id)}
                        variant={isAdded ? "outline" : "default"}
                        className="w-full"
                      >
                        {isAdded ? 'Remove Section' : 'Add Section'}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Show added sections */}
            {userInputs.userSections.filter(s => !['Methodology', 'Results'].includes(s.name)).map(section => {
              const sectionType = SECTION_TYPES.find(s => s.name === section.name);
              if (!sectionType) return null;
              
              return (
                <EnhancedSectionInput
                  key={section.id}
                  sectionName={section.name}
                  sectionType={sectionType}
                  userInputs={userInputs}
                  addUserSection={addUserSection}
                  removeUserSection={removeUserSection}
                  addContentItem={addContentItem}
                  updateContentItem={updateContentItem}
                  removeContentItem={removeContentItem}
                  moveContentItem={moveContentItem}
                  analyzingItems={analyzingItems}
                  setAnalyzingItems={setAnalyzingItems}
                  selectedModel={selectedModel}
                  sectionAnalysis={sectionAnalysis}
                  analyzeSectionContent={analyzeSectionContent}
                  fixSectionContent={fixSectionContent}
                  isFixing={isFixing}
                  addFigureWithData={addFigureWithData}
                />
              );
            })}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Progress Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200">
        <CardHeader>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-800">Create Your Research Paper</h1>
            <Badge variant="outline" className="bg-white">
              Step {currentStepIndex + 1} of {steps.length}
            </Badge>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const status = getStepStatus(step.id);
              const StepIcon = step.icon;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                    status === 'complete' ? 'bg-green-100 text-green-800' :
                    status === 'current' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    {status === 'complete' ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <StepIcon className="h-5 w-5" />
                    )}
                    <div className="hidden md:block">
                      <div className="font-medium text-sm">{step.name}</div>
                      <div className="text-xs opacity-75">{step.description}</div>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
                  )}
                </div>
              );
            })}
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      <div className="min-h-[600px]">
        {renderStepContent()}
      </div>

      {/* Navigation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <Button
              onClick={prevStep}
              disabled={currentStepIndex === 0}
              variant="outline"
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center gap-4">
              {inputStep === 'additional' && canGenerate && (
                <div className="flex flex-col items-center gap-4">
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                    <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                      <Brain className="h-4 w-4 text-blue-600" />
                      AI Model for Paper Generation
                    </h4>
                    <AIModelSelector
                      model={selectedModel}
                      models={AI_MODELS}
                      setModel={setSelectedModel}
                      className="w-full"
                    />
                  </div>

                  {/* Enhanced Citations Toggle */}
                  {setUseEnhancedCitations && (
                    <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Sparkles className="h-4 w-4 text-green-600" />
                          <div>
                            <h4 className="text-sm font-semibold text-gray-800">Enhanced Citations</h4>
                            <p className="text-xs text-gray-600">Use real academic sources from Tavily search</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={useEnhancedCitations}
                            onChange={(e) => setUseEnhancedCitations(e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                      </div>
                      {useEnhancedCitations && (
                        <div className="mt-3 p-3 bg-white rounded-lg border border-green-100">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-xs text-green-700">
                              <CheckCircle className="h-3 w-3" />
                              <span>Enhanced citation system enabled</span>
                            </div>
                            <div className="text-xs text-gray-600">
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span><strong>Tavily Search:</strong> Introduction (20 sources), Methodology (10 sources)</span>
                              </div>
                              <div className="flex items-center gap-2 mb-1">
                                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span><strong>User Content:</strong> Results (3 citations), Discussion (5 citations)</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                <span><strong>No Citations:</strong> Conclusion, Abstract</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                  {/* AI Reference Validation Toggle */}
                  {setUseAIValidation && useEnhancedCitations && (
                    <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4 border border-purple-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Brain className="h-4 w-4 text-purple-600" />
                          <div>
                            <h4 className="text-sm font-semibold text-gray-800">AI Reference Validation</h4>
                            <p className="text-xs text-gray-600">Use AI to validate and format references correctly</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={useAIValidation}
                            onChange={(e) => setUseAIValidation(e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                      {useAIValidation && (
                        <div className="mt-3 p-3 bg-white rounded-lg border border-purple-100">
                          <div className="flex items-center gap-2 text-xs text-purple-700">
                            <CheckCircle className="h-3 w-3" />
                            <span>AI will validate citations and extract correct publication information</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Real Citations Toggle */}
                  {setUseRealCitations && useEnhancedCitations && (
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <div>
                            <h4 className="text-sm font-semibold text-gray-800">Real Citations Only</h4>
                            <p className="text-xs text-gray-600">Extract only verified, real academic sources - NO fake references</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={useRealCitations}
                            onChange={(e) => setUseRealCitations(e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                      </div>
                      {useRealCitations && (
                        <div className="mt-3 p-3 bg-white rounded-lg border border-green-100">
                          <div className="flex items-center gap-2 text-xs text-green-700">
                            <CheckCircle className="h-3 w-3" />
                            <span>Only real, verifiable academic sources will be extracted from search results</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                    </div>
                  )}
                  <Button
                    onClick={onProceedToGeneration}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg font-semibold"
                  >
                    <Sparkles className="h-5 w-5 mr-2" />
                    Generate Research Paper
                  </Button>
                </div>
              )}
              
              {currentStepIndex < steps.length - 1 && (
                <Button
                  onClick={nextStep}
                  disabled={
                    (inputStep === 'title' && !canProceedFromTitle) ||
                    (inputStep === 'methodology' && !canProceedFromMethodology) ||
                    (inputStep === 'results' && !canProceedFromResults)
                  }
                  className="flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Requirements Check */}
          {!canGenerate && (
            <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2 text-amber-800 mb-2">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">Requirements to Generate Paper:</span>
              </div>
              <div className="space-y-1 text-sm text-amber-700">
                <div className={`flex items-center gap-2 ${hasTitle ? 'text-green-700' : ''}`}>
                  {hasTitle ? <CheckCircle className="h-4 w-4" /> : <div className="w-4 h-4 border-2 border-amber-400 rounded-full" />}
                  Paper title and basic information
                </div>
                <div className={`flex items-center gap-2 ${hasMethodology ? 'text-green-700' : ''}`}>
                  {hasMethodology ? <CheckCircle className="h-4 w-4" /> : <div className="w-4 h-4 border-2 border-amber-400 rounded-full" />}
                  Methodology section with content
                </div>
                <div className={`flex items-center gap-2 ${hasResults ? 'text-green-700' : ''}`}>
                  {hasResults ? <CheckCircle className="h-4 w-4" /> : <div className="w-4 h-4 border-2 border-amber-400 rounded-full" />}
                  Results section with content
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
