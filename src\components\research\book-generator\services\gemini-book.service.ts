import { GoogleGenAI } from '@google/genai';
import {
  BookMetadata,
  UserChapter,
  GeminiBookOutline,
  GeminiChapterOutline,
  GeminiSectionOutline,
  GeminiBookContext,
  GeminiGeneratedChapter,
  GeminiRateLimitConfig
} from '../types';
import { GEMINI_CONFIG, GEMINI_PROMPTS } from '../constants';
import { extractCitationsFromText } from '../../paper-generator/citation-extraction.enhanced';

/**
 * Google Gemini Book Generation Service
 * Provides comprehensive book generation using Google's Gemini API
 */
export class GeminiBookService {
  private ai: GoogleGenAI;
  private rateLimitConfig: GeminiRateLimitConfig;
  private lastRequestTime: Date | null = null;

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('Google Gemini API key not found. Please add VITE_GEMINI_API_KEY to your .env file.');
    }

    if (apiKey.length < 20) {
      throw new Error('Google Gemini API key appears to be invalid. Please check your VITE_GEMINI_API_KEY.');
    }

    this.ai = new GoogleGenAI({ apiKey });

    // Initialize rate limit config with explicit values
    this.rateLimitConfig = {
      delayBetweenRequests: GEMINI_CONFIG.RATE_LIMIT.DELAY_BETWEEN_REQUESTS || 10000,
      maxRetries: GEMINI_CONFIG.RATE_LIMIT.MAX_RETRIES || 3,
      backoffMultiplier: GEMINI_CONFIG.RATE_LIMIT.BACKOFF_MULTIPLIER || 2,
      maxBackoffDelay: GEMINI_CONFIG.RATE_LIMIT.MAX_BACKOFF_DELAY || 60000
    };

    console.log('Gemini service initialized with config:', this.rateLimitConfig);
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    return Boolean(apiKey && apiKey.length > 20);
  }

  /**
   * Test the API connection with a simple request
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const requestConfig = {
        generationConfig: {
          maxOutputTokens: 100,
          temperature: 0.1
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: 'Say "Hello, API test successful!" and nothing else.' }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: GEMINI_CONFIG.MODEL,
        config: requestConfig,
        contents,
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
        }
      }

      if (fullText && fullText.trim().length > 0) {
        return { success: true };
      } else {
        return { success: false, error: 'Empty response from API' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate comprehensive book outline using Gemini
   */
  async generateBookOutline(
    metadata: BookMetadata, 
    userChapters: UserChapter[]
  ): Promise<GeminiBookOutline> {
    await this.enforceRateLimit();

    const prompt = this.createOutlinePrompt(metadata, userChapters);
    
    try {
      const response = await this.makeGeminiRequest(prompt, {
        maxOutputTokens: GEMINI_CONFIG.GENERATION.OUTLINE_MAX_TOKENS,
        temperature: GEMINI_CONFIG.GENERATION.TEMPERATURE
      });

      console.log('Raw Gemini response for outline generation:', response.substring(0, 500) + '...');

      const outlineData = this.extractAndParseJSON(response, metadata, userChapters);
      return this.validateAndProcessOutline(outlineData, metadata);
    } catch (error) {
      console.error('Error generating book outline:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('JSON')) {
          throw new Error(`Failed to parse book outline: The AI response was not in valid JSON format. This may be due to a complex request - try simplifying your book description or reducing the number of chapters.`);
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          throw new Error(`API quota exceeded: Please wait a few minutes before trying again, or check your Gemini API quota.`);
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error(`Network error: Please check your internet connection and try again.`);
        } else {
          throw new Error(`Failed to generate book outline: ${error.message}`);
        }
      } else {
        throw new Error(`Failed to generate book outline: Unknown error occurred`);
      }
    }
  }

  /**
   * Generate a single chapter with context
   */
  async generateChapter(
    chapterOutline: GeminiChapterOutline,
    context: GeminiBookContext,
    userContent?: string
  ): Promise<GeminiGeneratedChapter> {
    console.log(`=== GEMINI SERVICE: Generating chapter ===`);
    console.log(`Chapter outline:`, {
      id: chapterOutline.id,
      title: chapterOutline.title,
      order: chapterOutline.order,
      estimatedWords: chapterOutline.estimatedWords
    });
    console.log(`Context:`, {
      currentChapterIndex: context.currentChapterIndex,
      totalChapters: context.totalChapters,
      previousSummariesCount: context.previousChapterSummaries.length
    });

    await this.enforceRateLimit();

    const prompt = this.createChapterPrompt(chapterOutline, context, userContent);
    
    try {
      const response = await this.makeGeminiRequest(prompt, {
        maxOutputTokens: GEMINI_CONFIG.GENERATION.CHAPTER_MAX_TOKENS,
        temperature: GEMINI_CONFIG.GENERATION.TEMPERATURE
      });

      // Generate chapter summary for context
      const summary = await this.generateChapterSummary(chapterOutline.title, response);
      
      // Extract citations with error handling
      let citations: string[] = [];
      try {
        const citationResult = extractCitationsFromText(response, chapterOutline.id);
        if (citationResult && Array.isArray(citationResult.citations)) {
          citations = citationResult.citations.map(c => {
            if (typeof c === 'string') return c;
            return c.inTextFormat || c.text || `${c.author || 'Unknown'}, ${c.year || 'Unknown'}`;
          });
        }
      } catch (citationError) {
        console.warn('Citation extraction failed for chapter:', chapterOutline.title, citationError);
        citations = []; // Use empty array if citation extraction fails
      }

      const generatedChapter = {
        id: chapterOutline.id,
        title: chapterOutline.title,
        content: response,
        summary,
        wordCount: this.countWords(response),
        order: chapterOutline.order,
        generatedAt: new Date(),
        citations
      };

      console.log(`=== GEMINI SERVICE: Chapter generated successfully ===`);
      console.log(`Generated chapter:`, {
        id: generatedChapter.id,
        title: generatedChapter.title,
        order: generatedChapter.order,
        wordCount: generatedChapter.wordCount,
        contentLength: generatedChapter.content.length
      });

      return generatedChapter;
    } catch (error) {
      console.error('Error generating chapter:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('citations') || error.message.includes('map')) {
          throw new Error(`Failed to process citations for chapter "${chapterOutline.title}": Citation extraction error. The chapter content was generated but citation processing failed.`);
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          throw new Error(`API quota exceeded while generating "${chapterOutline.title}": Please wait a few minutes before continuing.`);
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error(`Network error while generating "${chapterOutline.title}": Please check your connection and try again.`);
        } else {
          throw new Error(`Failed to generate chapter "${chapterOutline.title}": ${error.message}`);
        }
      } else {
        throw new Error(`Failed to generate chapter "${chapterOutline.title}": Unknown error occurred`);
      }
    }
  }

  /**
   * Generate chapter summary for context management
   */
  async generateChapterSummary(chapterTitle: string, chapterContent: string): Promise<string> {
    await this.enforceRateLimit();

    const prompt = `${GEMINI_PROMPTS.SUMMARY_SYSTEM}

Create a concise summary of the following chapter for maintaining narrative coherence in subsequent chapters.

Chapter Title: ${chapterTitle}

Chapter Content:
${chapterContent}

Requirements:
- Maximum 400 words
- Focus on key concepts, main arguments, and important conclusions
- Include essential information needed for narrative continuity
- Maintain the essential plot points, character development, or key insights
- Use clear, concise language

Provide only the summary without any additional formatting or headers.`;

    try {
      const response = await this.makeGeminiRequest(prompt, {
        maxOutputTokens: GEMINI_CONFIG.GENERATION.SUMMARY_MAX_TOKENS,
        temperature: 0.3 // Lower temperature for more consistent summaries
      });

      return response.trim();
    } catch (error) {
      console.error('Error generating chapter summary:', error);
      // Return a fallback summary if generation fails
      return `Chapter ${chapterTitle}: ${chapterContent.substring(0, 300)}...`;
    }
  }

  /**
   * Create outline generation prompt
   */
  private createOutlinePrompt(metadata: BookMetadata, userChapters: UserChapter[]): string {
    const chapterCount = metadata.chapterCount || userChapters.length || 10;

    return `You are an expert book author creating a detailed outline. Create a comprehensive book outline for "${metadata.title}".

BOOK DETAILS:
Title: ${metadata.title}
Genre: ${metadata.genre}
Target Audience: ${metadata.targetAudience}
Description: ${metadata.description}
Required Chapters: ${chapterCount}
Tone: ${metadata.tone}

${userChapters.length > 0 ? `
USER PROVIDED CHAPTERS:
${userChapters.map((ch, index) =>
  `${index + 1}. ${ch.outline.title}: ${ch.outline.description}`
).join('\n')}
` : ''}

Create exactly ${chapterCount} chapters. Each chapter should have 4-6 sections.

RESPOND WITH ONLY THIS JSON FORMAT (no markdown, no code blocks):

{
  "title": "${metadata.title}",
  "description": "Detailed description of the book",
  "chapters": [
    {
      "id": "chapter-1",
      "title": "Chapter 1 Title",
      "description": "What this chapter covers",
      "sections": [
        {
          "id": "section-1-1",
          "title": "Section Title",
          "description": "Section description",
          "estimatedWords": 800,
          "order": 1
        }
      ],
      "estimatedWords": 4000,
      "keyPoints": ["Key point 1", "Key point 2"],
      "order": 1
    }
  ],
  "totalEstimatedWords": ${chapterCount * 4000},
  "estimatedReadingTime": "4-6 hours",
  "keyThemes": ["Theme 1", "Theme 2"]
}

Return only valid JSON. Start with { and end with }.`;
  }

  /**
   * Create chapter generation prompt
   */
  private createChapterPrompt(
    chapterOutline: GeminiChapterOutline,
    context: GeminiBookContext,
    userContent?: string
  ): string {
    const contextSection = context.previousChapterSummaries.length > 0 
      ? `
PREVIOUS CHAPTERS CONTEXT:
${context.previousChapterSummaries.map((summary, index) => 
  `Chapter ${index + 1} Summary: ${summary}`
).join('\n\n')}

BOOK OUTLINE CONTEXT:
${context.bookOutline.description}
Key Themes: ${context.bookOutline.keyThemes.join(', ')}
` 
      : `
BOOK OUTLINE CONTEXT:
${context.bookOutline.description}
Key Themes: ${context.bookOutline.keyThemes.join(', ')}
`;

    const userContentSection = userContent 
      ? `
USER PROVIDED CONTENT:
${userContent}

Please incorporate and expand upon this user content while maintaining narrative flow.`
      : '';

    return `${GEMINI_PROMPTS.CHAPTER_SYSTEM}

CRITICAL: You are writing Chapter ${chapterOutline.order} (Chapter Number ${chapterOutline.order}) titled "${chapterOutline.title}" for the book "${context.bookMetadata.title}".

THIS IS CHAPTER ${chapterOutline.order} - NOT CHAPTER 1. Make sure the content is appropriate for Chapter ${chapterOutline.order} in the sequence.

CHAPTER DETAILS:
- Chapter Number: ${chapterOutline.order}
- Chapter Title: ${chapterOutline.title}
- Chapter Description: ${chapterOutline.description}
- Target Word Count: ${chapterOutline.estimatedWords} words
- Chapter ID: ${chapterOutline.id}

CHAPTER STRUCTURE TO FOLLOW:
${chapterOutline.sections.map(section =>
  `${section.order}. ${section.title}
     Description: ${section.description}
     Estimated Words: ${section.estimatedWords}`
).join('\n\n')}

Key Points to Cover in THIS Chapter:
${chapterOutline.keyPoints.map(point => `• ${point}`).join('\n')}

${contextSection}
${userContentSection}

CRITICAL REQUIREMENTS:
- This is Chapter ${chapterOutline.order} - write content appropriate for this chapter number in the book sequence
- Write substantial, book-quality content (minimum ${chapterOutline.estimatedWords} words)
- Use hierarchical headings for sections (## for main sections, ### for subsections)
- Maintain consistency with previous chapters and overall book narrative
- Include relevant examples, case studies, or illustrations where appropriate
- Use a ${context.bookMetadata.tone} tone throughout
- Include proper citations where academic references would be appropriate
- Ensure smooth transitions between sections
- Target audience: ${context.bookMetadata.targetAudience}
- Genre: ${context.bookMetadata.genre}
- DO NOT repeat content from Chapter 1 or other chapters

IMPORTANT: Write content specifically for Chapter ${chapterOutline.order}: "${chapterOutline.title}". Do not write generic introductory content.

Write only the main chapter content without the chapter title heading. Start directly with the content for Chapter ${chapterOutline.order}.`;
  }

  /**
   * Make a request to Gemini with error handling and retries
   */
  private async makeGeminiRequest(
    prompt: string,
    config: {
      maxOutputTokens: number;
      temperature: number;
      responseMimeType?: string;
    }
  ): Promise<string> {
    let lastError: Error | null = null;

    console.log('Making Gemini request with config:', {
      model: GEMINI_CONFIG.MODEL,
      maxOutputTokens: config.maxOutputTokens,
      temperature: config.temperature,
      responseMimeType: config.responseMimeType,
      maxRetries: this.rateLimitConfig.maxRetries,
      promptLength: prompt.length
    });

    for (let attempt = 1; attempt <= this.rateLimitConfig.maxRetries; attempt++) {
      try {
        console.log(`Gemini API attempt ${attempt}/${this.rateLimitConfig.maxRetries}`);

        // Use the same pattern as the working google-search service
        const requestConfig = {
          generationConfig: {
            maxOutputTokens: config.maxOutputTokens,
            temperature: config.temperature,
            ...(config.responseMimeType && { responseMimeType: config.responseMimeType })
          }
        };

        const contents = [{
          role: 'user' as const,
          parts: [{ text: prompt }],
        }];

        const response = await this.ai.models.generateContentStream({
          model: GEMINI_CONFIG.MODEL,
          config: requestConfig,
          contents,
        });

        let fullText = '';
        for await (const chunk of response) {
          if (chunk.text) {
            fullText += chunk.text;
          }
        }

        if (!fullText || fullText.trim().length === 0) {
          throw new Error('Empty response from Gemini API');
        }

        console.log(`Gemini API request successful on attempt ${attempt}, response length: ${fullText.length}`);
        return fullText;
      } catch (error) {
        lastError = error as Error;
        console.error(`Gemini API request attempt ${attempt} failed:`, {
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined,
          attempt,
          maxRetries: this.rateLimitConfig.maxRetries
        });

        if (attempt < this.rateLimitConfig.maxRetries) {
          const delay = Math.min(
            this.rateLimitConfig.delayBetweenRequests * Math.pow(this.rateLimitConfig.backoffMultiplier, attempt - 1),
            this.rateLimitConfig.maxBackoffDelay
          );
          console.log(`Retrying in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }

    const errorMessage = `Gemini API request failed after ${this.rateLimitConfig.maxRetries} attempts: ${lastError?.message || 'Unknown error'}`;
    console.error('Final Gemini API error:', errorMessage);
    throw new Error(errorMessage);
  }

  /**
   * Enforce rate limiting between requests
   */
  private async enforceRateLimit(): Promise<void> {
    if (this.lastRequestTime) {
      const timeSinceLastRequest = Date.now() - this.lastRequestTime.getTime();
      const remainingDelay = this.rateLimitConfig.delayBetweenRequests - timeSinceLastRequest;

      if (remainingDelay > 0) {
        console.log(`Rate limiting: waiting ${remainingDelay}ms before next request`);
        await this.sleep(remainingDelay);
      }
    }

    this.lastRequestTime = new Date();
  }

  /**
   * Validate and process the generated outline
   */
  private validateAndProcessOutline(outlineData: any, metadata: BookMetadata): GeminiBookOutline {
    if (!outlineData.chapters || !Array.isArray(outlineData.chapters)) {
      throw new Error('Invalid outline format: missing or invalid chapters array');
    }

    // Ensure all chapters have required fields and generate IDs if missing
    console.log(`=== OUTLINE PROCESSING: Raw chapters from AI ===`);
    console.log(`Number of chapters: ${outlineData.chapters.length}`);
    outlineData.chapters.forEach((ch: any, idx: number) => {
      console.log(`Raw chapter ${idx}:`, { id: ch.id, title: ch.title, order: ch.order });
    });

    const processedChapters: GeminiChapterOutline[] = outlineData.chapters.map((chapter: any, index: number) => {
      const processedChapter = {
        id: chapter.id || `chapter-${index + 1}`,
        title: chapter.title || `Chapter ${index + 1}`,
        description: chapter.description || '',
        sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
          id: section.id || `section-${index + 1}-${sectionIndex + 1}`,
          title: section.title || `Section ${sectionIndex + 1}`,
          description: section.description || '',
          estimatedWords: section.estimatedWords || 500,
          order: section.order || sectionIndex + 1
        })),
        estimatedWords: chapter.estimatedWords || 5000,
        keyPoints: chapter.keyPoints || [],
        order: chapter.order || index + 1
      };

      console.log(`Processed chapter ${index}:`, {
        id: processedChapter.id,
        title: processedChapter.title,
        order: processedChapter.order
      });

      return processedChapter;
    });

    return {
      title: outlineData.title || metadata.title,
      description: outlineData.description || metadata.description,
      chapters: processedChapters,
      totalEstimatedWords: outlineData.totalEstimatedWords || processedChapters.reduce((sum, ch) => sum + ch.estimatedWords, 0),
      estimatedReadingTime: outlineData.estimatedReadingTime || 'Variable',
      keyThemes: outlineData.keyThemes || []
    };
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Extract and parse JSON from AI responses with robust error handling
   */
  private extractAndParseJSON(content: string, metadata?: BookMetadata, userChapters?: UserChapter[]): any {
    console.log("Attempting to parse Gemini response:", content.substring(0, 200) + "...");

    // Extract JSON content from various formats
    let jsonContent = this.extractJSONContent(content);

    // Pre-process for common error patterns
    jsonContent = this.fixMixedQuotePatterns(jsonContent);

    console.log("Cleaned JSON content:", jsonContent.substring(0, 200) + "...");

    // Try parsing the cleaned JSON
    try {
      return JSON.parse(jsonContent);
    } catch (error) {
      console.log("Primary JSON parse failed, attempting recovery...");
      return this.attemptJSONRecovery(jsonContent, error as Error, metadata, userChapters);
    }
  }

  /**
   * Extract JSON content from various AI response formats
   */
  private extractJSONContent(content: string): string {
    let jsonContent = content.trim();

    // First try to parse directly
    try {
      JSON.parse(jsonContent);
      return jsonContent; // If it parses, return as-is
    } catch (e) {
      // Continue with extraction
    }

    // Match content inside code blocks with or without json specifier
    const codeBlockRegexes = [
      /```(?:json)?\s*([\s\S]*?)\s*```/,     // Standard markdown code blocks
      /`{3,}(?:json)?\s*([\s\S]*?)\s*`{3,}/, // Handle cases with more than 3 backticks
      /\{[\s\S]*\}/                          // Just try to find a JSON object directly
    ];

    for (const regex of codeBlockRegexes) {
      const match = content.match(regex);
      if (match && match[1]) {
        jsonContent = match[1].trim();
        console.log("Extracted from code block:", jsonContent.substring(0, 100) + "...");
        break;
      }
    }

    return jsonContent;
  }

  /**
   * Fix common mixed quote patterns in JSON
   */
  private fixMixedQuotePatterns(content: string): string {
    let fixed = content;

    // Fix the specific pattern: "property': value
    fixed = fixed.replace(/"([^"']*)':\s*/g, '"$1": ');

    // Fix the reverse pattern: 'property": value
    fixed = fixed.replace(/'([^"']*)":\s*/g, '"$1": ');

    // Fix both quotes wrong: 'property': value
    fixed = fixed.replace(/'([^"']*)':\s*/g, '"$1": ');

    // Fix property names that lost their opening quote: summary': value
    fixed = fixed.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)':\s*/g, '$1"$2": ');

    return fixed;
  }

  /**
   * Attempt JSON recovery with various strategies
   */
  private attemptJSONRecovery(jsonContent: string, error: Error, metadata?: BookMetadata, userChapters?: UserChapter[]): any {
    console.log("Attempting JSON recovery strategies...");

    // Strategy 1: Try to find and extract a valid JSON object
    const jsonObjectMatch = jsonContent.match(/\{[\s\S]*\}/);
    if (jsonObjectMatch) {
      try {
        const extracted = jsonObjectMatch[0];
        console.log("Trying extracted JSON object:", extracted.substring(0, 100) + "...");
        return JSON.parse(extracted);
      } catch (e) {
        console.log("Extracted object parsing failed");
      }
    }

    // Strategy 2: Try to fix common JSON issues
    let repaired = jsonContent
      .replace(/,\s*}/g, '}')  // Remove trailing commas
      .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // Quote unquoted keys
      .replace(/:\s*'([^']*)'/g, ': "$1"')  // Replace single quotes with double quotes
      .replace(/\n/g, ' ')  // Remove newlines
      .replace(/\s+/g, ' '); // Normalize whitespace

    try {
      console.log("Trying repaired JSON:", repaired.substring(0, 100) + "...");
      return JSON.parse(repaired);
    } catch (e) {
      console.log("Repaired JSON parsing failed");
    }

    // Strategy 3: Create a fallback structure with actual metadata
    console.log("All parsing strategies failed, creating fallback structure with user data");
    return this.createFallbackOutline(metadata, userChapters);
  }

  /**
   * Create a fallback outline structure when parsing fails
   */
  private createFallbackOutline(metadata?: BookMetadata, userChapters?: UserChapter[]): any {
    const chapterCount = metadata?.chapterCount || userChapters?.length || 10;
    const chapters = [];

    // Create chapters based on user input or generic structure
    for (let i = 1; i <= chapterCount; i++) {
      const userChapter = userChapters?.[i - 1];
      chapters.push({
        id: `chapter-${i}`,
        title: userChapter?.outline.title || `Chapter ${i}: ${metadata?.title ? `Exploring ${metadata.title}` : 'Main Topic'}`,
        description: userChapter?.outline.description || `Detailed exploration of key concepts in ${metadata?.title || 'the main topic'}`,
        sections: [
          {
            id: `section-${i}-1`,
            title: `Introduction to Chapter ${i}`,
            description: `Overview and introduction to the main concepts`,
            estimatedWords: 800,
            order: 1
          },
          {
            id: `section-${i}-2`,
            title: `Core Concepts and Analysis`,
            description: `Deep dive into the fundamental principles`,
            estimatedWords: 1200,
            order: 2
          },
          {
            id: `section-${i}-3`,
            title: `Practical Applications`,
            description: `Real-world examples and case studies`,
            estimatedWords: 1000,
            order: 3
          }
        ],
        estimatedWords: 3000,
        keyPoints: [`Key concepts for ${metadata?.title || 'the topic'}`, "Practical applications", "Important insights"],
        order: i
      });
    }

    const totalWords = chapters.length * 3000;

    return {
      title: metadata?.title || "Generated Book Outline",
      description: metadata?.description || "A comprehensive book outline generated by AI",
      chapters,
      totalEstimatedWords: totalWords,
      estimatedReadingTime: totalWords < 30000 ? "2-3 hours" : totalWords < 60000 ? "4-6 hours" : "6+ hours",
      keyThemes: metadata?.keywords || ["Introduction", "Core Concepts", "Applications"]
    };
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const geminiBookService = new GeminiBookService();
export default geminiBookService;
