import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Database,
  BarChart3,
  MessageSquare,
  Brain,
  Upload,
  FileText,
  TrendingUp,
  Lightbulb,
  Eye,
  Settings,
  RefreshCw,
  Download,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Clock,
  Microscope,
  BookOpen,
  Target,
  Users,
  Calendar,
  Globe,
  FlaskConical,
  LineChart,
  PieChart,
  BarChart,
  Activity
} from "lucide-react";

import { EnhancedFileUploader } from './EnhancedFileUploader';
import { DataDescriptionForm } from './DataDescriptionForm';
import { DataPreview } from './DataPreview';
import { DataVisualizationErrorBoundary } from './ErrorBoundary';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { UploadedFile, DataAnalysisResult, ResearchReport } from '../types';

// New research-specific components (to be created)
import { ResearchAnalysisPipeline } from './ResearchAnalysisPipeline';
import { ResearchVisualizationGallery } from './ResearchVisualizationGallery';
import { ResearchReportGenerator } from './ResearchReportGenerator';
import { ResearchQueryInterface } from './ResearchQueryInterface';

const ResearchDataPlatformContent: React.FC = () => {
  const {
    uploadedFiles,
    currentFile,
    currentAnalysis,
    currentReport,
    activeTab,
    setActiveTab,
    setCurrentFile,
    isUploading,
    isAnalyzing,
    isQuerying,
    isGeneratingReport,
    errors,
    clearErrors
  } = useDataVisualizationStore();

  const [dataDescription, setDataDescription] = useState('');
  const [researchContext, setResearchContext] = useState({
    researchQuestion: '',
    studyType: '',
    researchField: '',
    hypotheses: [] as string[]
  });

  const [stats, setStats] = useState({
    filesProcessed: 0,
    analysesCompleted: 0,
    reportsGenerated: 0,
    figuresCreated: 0
  });

  useEffect(() => {
    // Update stats based on store state
    setStats({
      filesProcessed: uploadedFiles.filter(f => f.status === 'ready').length,
      analysesCompleted: currentAnalysis ? 1 : 0,
      reportsGenerated: currentReport ? 1 : 0,
      figuresCreated: currentAnalysis?.visualizations.length || 0
    });
  }, [uploadedFiles, currentAnalysis, currentReport]);

  const handleFileProcessed = (fileId: string) => {
    const file = uploadedFiles.find(f => f.id === fileId);
    if (file) {
      setCurrentFile(file);
      setActiveTab('context');
    }
  };

  const handleAnalysisComplete = (result: DataAnalysisResult) => {
    setActiveTab('visualizations');
    toast.success('Research analysis complete! Review your findings and visualizations.');
  };

  const handleReportGenerated = (report: ResearchReport) => {
    setActiveTab('report');
    toast.success('Research report generated! Review and export your findings.');
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'upload':
        return <Upload className="h-4 w-4" />;
      case 'context':
        return <FileText className="h-4 w-4" />;
      case 'analysis':
        return <FlaskConical className="h-4 w-4" />;
      case 'visualizations':
        return <BarChart3 className="h-4 w-4" />;
      case 'query':
        return <MessageSquare className="h-4 w-4" />;
      case 'report':
        return <BookOpen className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getTabStatus = (tab: string) => {
    switch (tab) {
      case 'upload':
        return uploadedFiles.length > 0 ? 'completed' : 'pending';
      case 'context':
        return dataDescription ? 'completed' : currentFile ? 'available' : 'disabled';
      case 'analysis':
        return currentAnalysis ? 'completed' : (currentFile && dataDescription) ? 'available' : 'disabled';
      case 'visualizations':
        return currentAnalysis?.visualizations.length ? 'completed' : 'disabled';
      case 'query':
        return currentFile ? 'available' : 'disabled';
      case 'report':
        return currentReport ? 'completed' : currentAnalysis ? 'available' : 'disabled';
      default:
        return 'pending';
    }
  };

  const isTabDisabled = (tab: string) => {
    switch (tab) {
      case 'context':
        return !currentFile;
      case 'analysis':
        return !currentFile || !dataDescription;
      case 'visualizations':
        return !currentAnalysis;
      case 'query':
        return !currentFile;
      case 'report':
        return !currentAnalysis;
      default:
        return false;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Enhanced Header for Research Platform */}
      <div className="sticky top-0 z-40 bg-white/90 backdrop-blur-xl border-b border-gray-200 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
                  <Microscope className={`h-8 w-8 text-white ${(isUploading || isAnalyzing || isQuerying || isGeneratingReport) ? 'animate-pulse' : ''}`} />
                </div>
                {(isUploading || isAnalyzing || isQuerying || isGeneratingReport) && (
                  <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full animate-ping" />
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Research Data Analysis Platform
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  AI-powered statistical analysis and automated research report generation
                </p>
              </div>
            </div>

            {/* Enhanced Stats Dashboard */}
            <div className="hidden lg:flex items-center gap-8">
              <div className="text-center">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-blue-500" />
                  <span className="text-lg font-bold text-blue-600">{stats.filesProcessed}</span>
                </div>
                <p className="text-xs text-gray-500">Datasets</p>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-2">
                  <FlaskConical className="h-4 w-4 text-green-500" />
                  <span className="text-lg font-bold text-green-600">{stats.analysesCompleted}</span>
                </div>
                <p className="text-xs text-gray-500">Analyses</p>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-purple-500" />
                  <span className="text-lg font-bold text-purple-600">{stats.figuresCreated}</span>
                </div>
                <p className="text-xs text-gray-500">Figures</p>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-orange-500" />
                  <span className="text-lg font-bold text-orange-600">{stats.reportsGenerated}</span>
                </div>
                <p className="text-xs text-gray-500">Reports</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {errors.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearErrors}
                className="mt-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
          {/* Enhanced Tab Navigation */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <TabsList className="grid w-full grid-cols-6 h-14">
                <TabsTrigger 
                  value="upload" 
                  disabled={isTabDisabled('upload')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('upload')}
                    <span className="hidden sm:inline text-xs">Upload</span>
                  </div>
                  {getTabStatus('upload') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="context" 
                  disabled={isTabDisabled('context')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('context')}
                    <span className="hidden sm:inline text-xs">Context</span>
                  </div>
                  {getTabStatus('context') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="analysis" 
                  disabled={isTabDisabled('analysis')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('analysis')}
                    <span className="hidden sm:inline text-xs">Analysis</span>
                  </div>
                  {isAnalyzing && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                  {getTabStatus('analysis') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="visualizations" 
                  disabled={isTabDisabled('visualizations')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('visualizations')}
                    <span className="hidden sm:inline text-xs">Figures</span>
                  </div>
                  {getTabStatus('visualizations') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="query" 
                  disabled={isTabDisabled('query')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('query')}
                    <span className="hidden sm:inline text-xs">Query</span>
                  </div>
                  {isQuerying && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="report" 
                  disabled={isTabDisabled('report')}
                  className="flex flex-col items-center gap-1 h-12"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('report')}
                    <span className="hidden sm:inline text-xs">Report</span>
                  </div>
                  {isGeneratingReport && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                  {getTabStatus('report') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
              </TabsList>

              {/* Current File Info */}
              {currentFile && (
                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold text-blue-900">{currentFile.name}</p>
                      <p className="text-sm text-blue-700">
                        {currentFile.data.length} observations × {currentFile.headers.length} variables
                      </p>
                    </div>
                    <Badge variant="outline" className="bg-white border-blue-300 text-blue-700">
                      {currentFile.status}
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tab Content - Components will be implemented next */}
          <TabsContent value="upload" className="space-y-6">
            <EnhancedFileUploader onFileProcessed={handleFileProcessed} />
          </TabsContent>

          <TabsContent value="context" className="space-y-6">
            {currentFile ? (
              <DataDescriptionForm
                file={currentFile}
                description={dataDescription}
                onDescriptionChange={setDataDescription}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Upload a dataset to begin research analysis
                      </p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Dataset
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            {currentFile && dataDescription ? (
              <ResearchAnalysisPipeline
                file={currentFile}
                dataDescription={dataDescription}
                researchContext={researchContext}
                onAnalysisComplete={handleAnalysisComplete}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <FlaskConical className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">Ready for Analysis</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Complete data upload and context description to begin research analysis
                      </p>
                    </div>
                    <Button
                      onClick={() => setActiveTab(currentFile ? 'context' : 'upload')}
                      variant="outline"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      {currentFile ? 'Add Context' : 'Upload Data'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="visualizations" className="space-y-6">
            {currentAnalysis ? (
              <ResearchVisualizationGallery
                analysisResult={currentAnalysis}
                researchContext={researchContext}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Analysis Available</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Complete the research analysis to generate publication-ready figures
                      </p>
                    </div>
                    <Button
                      onClick={() => setActiveTab('analysis')}
                      variant="outline"
                      disabled={!currentFile || !dataDescription}
                    >
                      <FlaskConical className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="query" className="space-y-6">
            {currentFile ? (
              <ResearchQueryInterface
                file={currentFile}
                analysis={currentAnalysis}
                researchContext={researchContext}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Dataset Available</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Upload a dataset to start asking research questions
                      </p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Dataset
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="report" className="space-y-6">
            {currentAnalysis ? (
              <ResearchReportGenerator
                file={currentFile!}
                analysis={currentAnalysis}
                researchContext={researchContext}
                onReportGenerated={handleReportGenerated}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">Analysis Required</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Complete the research analysis to generate automated research reports
                      </p>
                    </div>
                    <Button
                      onClick={() => setActiveTab('analysis')}
                      variant="outline"
                      disabled={!currentFile || !dataDescription}
                    >
                      <FlaskConical className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// Main component wrapped with error boundary
export const ResearchDataPlatform: React.FC = () => {
  return (
    <DataVisualizationErrorBoundary>
      <ResearchDataPlatformContent />
    </DataVisualizationErrorBoundary>
  );
};
