import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowUp, ArrowDown, Info, Star } from "lucide-react";
import { ArticleReviewResults } from "../types";

interface ReviewScorecardProps {
  results: ArticleReviewResults;
}

export function ReviewScorecard({ results }: ReviewScorecardProps) {
  // Extract metrics from the overall results
  const metrics = results.overall;
  
  // Define categories to display and their descriptions
  const categories = [
    { 
      id: 'overallScore',
      name: 'Overall Score', 
      value: metrics.overallScore,
      description: 'Combined score across all evaluation dimensions'
    },
    { 
      id: 'clarity',
      name: 'Clarity', 
      value: metrics.clarity,
      description: 'How clearly written and organized the article is'
    },
    { 
      id: 'structure',
      name: 'Structure', 
      value: metrics.structure,
      description: 'How well the article follows academic structure conventions'
    },
    { 
      id: 'methodology',
      name: 'Methodology', 
      value: metrics.methodology,
      description: 'Quality and appropriateness of research methods'
    },
    { 
      id: 'significance',
      name: 'Significance', 
      value: metrics.significance,
      description: 'Impact and importance of the research'
    },
    { 
      id: 'originality',
      name: 'Originality', 
      value: metrics.originality,
      description: 'Novelty and uniqueness of the research'
    },
  ];
  
  // Helper function to determine color based on score
  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-amber-600';
    return 'text-red-600';
  };
  
  // Helper function to determine progress color based on score
  const getProgressColor = (score: number): string => {
    if (score >= 90) return 'bg-green-600';
    if (score >= 75) return 'bg-blue-600';
    if (score >= 60) return 'bg-amber-600';
    return 'bg-red-600';
  };
  
  return (
    <Card className="overflow-hidden border-gray-200 shadow-md bg-white">
      <CardHeader className="pb-2 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5 border-b">
        <CardTitle className="flex items-center">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-1.5 rounded-md shadow-sm mr-2">
            <Star className="h-4 w-4" />
          </div>
          <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Review Scores
          </span>
        </CardTitle>
        <CardDescription>
          Comprehensive quality assessment metrics
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pt-5">
        {/* Show overall score prominently */}
        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm">
          <div className="text-7xl font-bold mb-3 flex items-center justify-center">
            <div className={`${getScoreColor(metrics.overallScore)} relative bg-clip-text text-transparent bg-gradient-to-r ${
              metrics.overallScore >= 90 ? 'from-green-500 to-emerald-600' : 
              metrics.overallScore >= 75 ? 'from-blue-500 to-indigo-600' :
              metrics.overallScore >= 60 ? 'from-amber-500 to-orange-600' :
              'from-red-500 to-rose-600'
            }`}>
              {metrics.overallScore}
              <div className="absolute -top-2 -right-6 text-base bg-white text-gray-700 rounded-full px-2 py-0.5 font-normal shadow-sm border border-gray-200">
                /100
              </div>
            </div>
          </div>
          <div className="text-gray-600 text-sm font-medium uppercase tracking-wide">Overall Paper Quality</div>
          <div className="mt-2 text-xs text-gray-500">{metrics.overallScore >= 80 ? 'Excellent' : metrics.overallScore >= 70 ? 'Good' : metrics.overallScore >= 60 ? 'Acceptable' : 'Needs Improvement'}</div>
        </div>
        
        {/* Show all metrics as progress bars with improved styling */}
        <div className="space-y-5 p-4 rounded-lg border border-gray-100 bg-gradient-to-br from-gray-50/50 to-gray-50/80">
          {categories.slice(1).map((category) => (
            <div key={category.id} className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <div className="font-medium flex items-center">
                  <div 
                    className="w-2 h-2 rounded-full mr-2"
                    style={{ backgroundColor: getProgressColor(category.value) }}
                  ></div>
                  {category.name}
                </div>
                <div className={`font-medium ${getScoreColor(category.value)}`}>
                  {category.value}
                </div>
              </div>
              <div className="relative">
                <Progress 
                  value={category.value} 
                  className="h-2 rounded-full"
                  style={{ 
                    backgroundColor: 'rgba(0,0,0,0.05)',
                    '--progress-background': getProgressColor(category.value)
                  } as React.CSSProperties}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/20 rounded-full"></div>
              </div>
              <div className="text-xs text-gray-500 mt-0.5">
                {category.description}
              </div>
            </div>
          ))}
        </div>
        
        {/* Summary of major points */}
        <div className="pt-4 border-t space-y-3">
          <div>
            <div className="text-sm font-medium flex items-center">
              <ArrowUp className="h-4 w-4 text-green-600 mr-1" />
              Strengths
            </div>
            <ul className="list-disc pl-5 mt-1 text-sm text-gray-700">
              {metrics.majorStrengths.slice(0, 3).map((strength, index) => (
                <li key={index} className="text-xs">{strength}</li>
              ))}
            </ul>
          </div>
          
          <div>
            <div className="text-sm font-medium flex items-center">
              <ArrowDown className="h-4 w-4 text-red-600 mr-1" />
              Areas to Improve
            </div>
            <ul className="list-disc pl-5 mt-1 text-sm text-gray-700">
              {metrics.majorWeaknesses.slice(0, 3).map((weakness, index) => (
                <li key={index} className="text-xs">{weakness}</li>
              ))}
            </ul>
          </div>
          
          <div className="bg-blue-50 p-3 rounded-md text-xs text-blue-700">
            <div className="flex items-center mb-1">
              <Info className="h-3 w-3 mr-1" />
              <span className="font-medium">How to interpret scores</span>
            </div>
            <p>
              90-100: Excellent, 75-89: Good,
              60-74: Needs improvement, &lt;60: Significant issues
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
