/**
 * Content Service
 * Handles content extraction from web pages and file processing
 */

import { SearchResult } from '../types';
import { API_ENDPOINTS, ERROR_MESSAGES, SUPPORTED_FILE_TYPES } from '../constants';

interface ContentResponse {
  content: string;
  title?: string;
  metadata?: {
    wordCount: number;
    readingTime: number;
    language?: string;
  };
}

interface ParseDocumentResponse {
  content: string;
  metadata?: {
    pages?: number;
    wordCount?: number;
    title?: string;
  };
}

class ContentService {
  private async makeRequest<T>(
    endpoint: string, 
    data: any, 
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        body: JSON.stringify(data),
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Request failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(ERROR_MESSAGES.networkError);
    }
  }

  /**
   * Fetch content from a URL
   */
  async fetchContent(url: string): Promise<string> {
    try {
      // For now, use mock content until backend API is implemented
      // TODO: Replace with actual content fetching API when backend is ready
      return await this.getMockContent(url);
    } catch (error) {
      console.error('Content fetch error for URL:', url, error);
      throw new Error(`Failed to fetch content from ${url}`);
    }
  }

  /**
   * Mock content fetching for development
   * TODO: Replace with actual content fetching API when backend is ready
   */
  private async getMockContent(url: string): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Extract domain and path for more specific content
    const urlObj = new URL(url);
    const domain = urlObj.hostname;
    const path = urlObj.pathname;

    // Extract potential topic from URL
    const pathSegments = path.split('/').filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1] || '';
    const potentialTopic = lastSegment
      .replace(/[-_]/g, ' ')
      .replace(/\.\w+$/, '') // Remove file extension
      .trim();

    // Try to extract topic from URL
    let topic = potentialTopic || 'advanced research methodologies';

    // Special case for common research domains
    if (domain.includes('sciencedirect')) {
      topic = 'scientific research and methodology';
    } else if (domain.includes('researchgate')) {
      topic = 'academic research and collaboration';
    } else if (domain.includes('ncbi.nlm.nih')) {
      topic = 'biomedical research and clinical studies';
    } else if (domain.includes('ieee')) {
      topic = 'engineering and technical research';
    }

    // Generate more specific content based on URL
    const mockContent = `
# Comprehensive Research Article on ${topic.charAt(0).toUpperCase() + topic.slice(1)}

## Abstract
This comprehensive research article explores significant developments in ${topic}, presenting novel methodologies and findings that substantially advance our understanding of this critical field. The study integrates multiple data sources and analytical approaches to provide a holistic perspective on current challenges and emerging solutions. Through rigorous investigation and systematic analysis, this research contributes valuable insights that address existing knowledge gaps and establish a foundation for future investigations.

## Introduction
Recent technological advancements and methodological innovations have transformed research approaches in ${topic}, opening new avenues for investigation and practical application. This article examines the evolution of key concepts, theoretical frameworks, and implementation strategies that define current best practices in the field. By synthesizing insights from diverse disciplines and research traditions, this study provides a comprehensive overview that contextualizes recent developments within the broader academic discourse.

## Literature Review
The existing body of research on ${topic} demonstrates significant progress in both theoretical understanding and practical implementation. Previous studies have established fundamental principles and methodological approaches that inform current research directions. Notable contributions include Smith et al. (2022), who developed a comprehensive framework for analyzing complex data patterns, and Johnson (2023), whose longitudinal study identified critical success factors for implementation in diverse contexts. This review identifies important knowledge gaps that the current study addresses through innovative research design and analytical techniques.

## Methodology
This research employed a sophisticated mixed-methods approach that combines quantitative analysis with qualitative insights to provide a comprehensive understanding of ${topic}. Data collection involved multiple sources, including:

1. Systematic literature review of 87 peer-reviewed articles published between 2018-2023
2. Quantitative analysis of longitudinal datasets from three independent sources
3. Qualitative assessment through structured interviews with 24 domain experts
4. Computational modeling using advanced statistical techniques and machine learning algorithms

The research design incorporated rigorous validation procedures to ensure reliability and reproducibility, including triangulation of data sources, peer review of analytical procedures, and sensitivity analysis of key findings.

## Results and Analysis
The analysis revealed several significant findings that advance our understanding of ${topic}:

1. Statistical analysis demonstrated strong correlations (r=0.78, p<0.001) between implementation quality and outcome effectiveness across diverse contexts.
2. Longitudinal data revealed consistent improvement patterns when standardized methodologies were applied, with average performance increases of 37% over traditional approaches.
3. Qualitative assessment identified five critical success factors that significantly influence implementation outcomes: (a) stakeholder engagement, (b) resource allocation, (c) technical infrastructure, (d) organizational culture, and (e) continuous evaluation.
4. Computational modeling successfully predicted outcome variables with 83% accuracy, providing a robust framework for future applications.

These findings collectively demonstrate the value of integrated approaches that combine theoretical rigor with practical application considerations.

## Discussion
The research findings have significant implications for both theoretical understanding and practical implementation of ${topic}. The strong correlations between implementation quality and outcomes highlight the importance of methodological rigor and standardized procedures. The identified success factors provide valuable guidance for practitioners seeking to optimize their approaches and maximize effectiveness.

The results support existing theoretical frameworks while introducing important refinements that address context-specific considerations. The computational model developed through this research offers a powerful tool for predicting outcomes and optimizing resource allocation in future implementations.

Limitations of this study include the focus on specific geographical regions and organizational contexts, which may limit generalizability to all settings. Additionally, the rapid evolution of technologies in this field necessitates ongoing validation of the findings as new tools and approaches emerge.

## Future Research Directions
This study establishes a foundation for several promising research directions:

1. Longitudinal studies examining the sustainability of implementation effects over extended time periods
2. Cross-cultural investigations to validate findings across diverse geographical and organizational contexts
3. Integration of emerging technologies to enhance methodological approaches and analytical capabilities
4. Development of standardized assessment tools based on the identified success factors
5. Exploration of potential applications in adjacent fields and disciplines

These research directions offer valuable opportunities to extend and refine the findings presented in this study.

## Conclusion
This comprehensive investigation advances our understanding of ${topic} through rigorous methodology and systematic analysis. The findings provide valuable insights for researchers and practitioners, establishing evidence-based guidelines for implementation and identifying critical success factors. By integrating theoretical perspectives with practical considerations, this research contributes to the growing body of knowledge and provides a solid foundation for continued advancement in the field.

## References
[1] Smith, J., Johnson, A., & Williams, B. (2022). Advanced methodological approaches in ${topic}. Journal of Research Methodology, 45(2), 123-145.
[2] Johnson, A. (2023). Longitudinal assessment of implementation factors in ${topic}. Research Quarterly, 37(3), 287-304.
[3] Brown, K. & Davis, L. (2023). Computational modeling techniques for predictive analysis. Technical Research Journal, 78(4), 567-589.
[4] Zhang, Y., et al. (2022). Cross-cultural validation of implementation frameworks. International Journal of Research Applications, 12(1), 45-67.
[5] Anderson, M., & Thompson, R. (2023). Stakeholder engagement strategies for successful implementation. Practical Research & Application, 29(4), 412-428.
    `.trim();

    return mockContent;
  }

  /**
   * Process uploaded file
   */
  async processFile(file: File): Promise<SearchResult> {
    try {
      // Validate file type
      if (!this.isValidFileType(file)) {
        throw new Error('Unsupported file type. Only TXT, PDF, and DOCX files are supported.');
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size too large. Maximum size is 10MB.');
      }

      let content = '';

      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        content = await file.text();
      } else {
        // For PDF and DOCX files, use the parsing endpoint
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(API_ENDPOINTS.parseDocument, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to parse document');
        }

        const data: ParseDocumentResponse = await response.json();
        content = data.content;
      }

      if (!content.trim()) {
        throw new Error('No content found in the uploaded file');
      }

      // Create snippet (first 500 characters)
      const snippet = content.slice(0, 500) + (content.length > 500 ? '...' : '');

      // Create search result from file
      const timestamp = Date.now();
      const result: SearchResult = {
        id: `file-${timestamp}-${file.name}`,
        url: URL.createObjectURL(file),
        name: file.name,
        snippet,
        isCustomUrl: true,
        content, // Store full content for report generation
      };

      return result;
    } catch (error) {
      console.error('File processing error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(ERROR_MESSAGES.fileUploadFailed);
    }
  }

  /**
   * Validate file type
   */
  private isValidFileType(file: File): boolean {
    const validTypes = [
      'text/plain',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const validExtensions = ['.txt', '.pdf', '.docx'];

    return (
      validTypes.includes(file.type) ||
      validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
    );
  }

  /**
   * Extract metadata from content
   */
  extractMetadata(content: string): {
    wordCount: number;
    readingTime: number;
    characterCount: number;
    paragraphCount: number;
  } {
    const wordCount = content.trim().split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / 200); // Assuming 200 words per minute
    const characterCount = content.length;
    const paragraphCount = content.split(/\n\s*\n/).length;

    return {
      wordCount,
      readingTime,
      characterCount,
      paragraphCount,
    };
  }

  /**
   * Clean and normalize content
   */
  cleanContent(content: string): string {
    return content
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove HTML tags if any
      .replace(/<[^>]*>/g, '')
      // Remove special characters that might interfere with processing
      .replace(/[^\w\s\.\,\!\?\;\:\-\(\)]/g, '')
      // Normalize line breaks
      .replace(/\n+/g, '\n')
      .trim();
  }

  /**
   * Truncate content to a specific length
   */
  truncateContent(content: string, maxLength: number = 5000): string {
    if (content.length <= maxLength) {
      return content;
    }

    // Try to truncate at a sentence boundary
    const truncated = content.slice(0, maxLength);
    const lastSentence = truncated.lastIndexOf('.');
    
    if (lastSentence > maxLength * 0.8) {
      return truncated.slice(0, lastSentence + 1);
    }

    // If no good sentence boundary, truncate at word boundary
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > maxLength * 0.8) {
      return truncated.slice(0, lastSpace) + '...';
    }

    return truncated + '...';
  }

  /**
   * Batch fetch content from multiple URLs
   */
  async batchFetchContent(urls: string[]): Promise<Array<{
    url: string;
    content: string | null;
    error?: string;
  }>> {
    const results = await Promise.allSettled(
      urls.map(async (url) => {
        try {
          const content = await this.fetchContent(url);
          return { url, content };
        } catch (error) {
          return { 
            url, 
            content: null, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          };
        }
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          url: urls[index],
          content: null,
          error: result.reason?.message || 'Failed to fetch content',
        };
      }
    });
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): string {
    return SUPPORTED_FILE_TYPES;
  }
}

export const contentService = new ContentService();
