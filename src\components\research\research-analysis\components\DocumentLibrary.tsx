import React, { useState, use<PERSON><PERSON>back, use<PERSON>emo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  FileText,
  Calendar,
  Users,
  Tag,
  Star,
  Eye,
  Download,
  MessageSquare,
  MoreHorizontal,
  SortAsc,
  SortDesc,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Heart,
  Bookmark,
  Share,
  Trash2,
  Brain,
  Info
} from "lucide-react";

import { ResearchDocument } from '../types';
import { researchAnalysisService } from '../services/research-analysis.service';

interface DocumentLibraryProps {
  documents: ResearchDocument[];
  selectedDocuments: string[];
  onSelectionChange: (documentIds: string[]) => void;
  onSearch: (query: string) => void;
  onFilter: (filters: any) => void;
}

interface DocumentPreview {
  document: ResearchDocument;
  summary?: string;
  isLoading: boolean;
}

export function DocumentLibrary({
  documents,
  selectedDocuments,
  onSelectionChange,
  onSearch,
  onFilter
}: DocumentLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'year' | 'authors'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<DocumentPreview | null>(null);
  const [questionInput, setQuestionInput] = useState('');
  const [questionAnswer, setQuestionAnswer] = useState('');
  const [isAnsweringQuestion, setIsAnsweringQuestion] = useState(false);

  // Filters state
  const [filters, setFilters] = useState({
    years: [2020, 2025] as [number, number],
    authors: [] as string[],
    journals: [] as string[],
    tags: [] as string[],
    categories: [] as string[],
    status: 'all' as 'all' | 'ready' | 'processing' | 'error'
  });

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    onSearch(query);
  }, [onSearch]);

  // Handle document selection
  const handleDocumentSelection = useCallback((documentId: string, checked: boolean) => {
    let newSelection: string[];
    if (checked) {
      newSelection = [...selectedDocuments, documentId];
    } else {
      newSelection = selectedDocuments.filter(id => id !== documentId);
    }
    console.log('Document selection updated:', newSelection.length, 'documents selected');
    onSelectionChange(newSelection);
  }, [selectedDocuments, onSelectionChange]);

  // Handle select all/none
  const handleSelectAll = useCallback(() => {
    if (selectedDocuments.length === documents.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(documents.map(d => d.id));
    }
  }, [documents, selectedDocuments, onSelectionChange]);

  // Sorted and filtered documents
  const sortedDocuments = useMemo(() => {
    const sorted = [...documents].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'year':
          comparison = a.publicationYear - b.publicationYear;
          break;
        case 'authors':
          comparison = a.authors[0]?.localeCompare(b.authors[0] || '') || 0;
          break;
        case 'date':
        default:
          comparison = new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime();
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted.filter(doc => {
      if (filters.status !== 'all' && doc.status !== filters.status) return false;
      if (filters.authors.length > 0 && !doc.authors.some(author => filters.authors.includes(author))) return false;
      if (filters.tags.length > 0 && !doc.tags.some(tag => filters.tags.includes(tag))) return false;
      return true;
    });
  }, [documents, sortBy, sortOrder, filters]);

  // Preview document
  const handlePreviewDocument = useCallback(async (document: ResearchDocument) => {
    setPreviewDocument({ document, isLoading: true });
    
    try {
      const summary = await researchAnalysisService.generateDocumentSummary(document);
      setPreviewDocument({ document, summary, isLoading: false });
    } catch (error) {
      console.error('Error generating summary:', error);
      setPreviewDocument({ document, isLoading: false });
      toast.error('Failed to generate document summary');
    }
  }, []);

  // Ask question about documents
  const handleAskQuestion = useCallback(async () => {
    if (!questionInput.trim()) return;
    
    const documentsToQuery = selectedDocuments.length > 0 
      ? documents.filter(d => selectedDocuments.includes(d.id))
      : documents;
    
    if (documentsToQuery.length === 0) {
      toast.error('Please select documents to query');
      return;
    }

    setIsAnsweringQuestion(true);
    setQuestionAnswer('');

    try {
      const answer = await researchAnalysisService.answerQuestion(
        questionInput,
        documentsToQuery
      );
      setQuestionAnswer(answer);
    } catch (error) {
      console.error('Error answering question:', error);
      toast.error('Failed to answer question');
    } finally {
      setIsAnsweringQuestion(false);
    }
  }, [questionInput, selectedDocuments, documents]);

  // Get unique values for filters
  const uniqueAuthors = useMemo(() => 
    [...new Set(documents.flatMap(d => d.authors))].sort(), 
    [documents]
  );
  
  const uniqueJournals = useMemo(() => 
    [...new Set(documents.map(d => d.journal).filter(Boolean))].sort(), 
    [documents]
  );

  const uniqueTags = useMemo(() => 
    [...new Set(documents.flatMap(d => d.tags))].sort(), 
    [documents]
  );

  // Get status icon
  const getStatusIcon = (status: ResearchDocument['status']) => {
    switch (status) {
      case 'ready': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing': return <RefreshCw className="h-4 w-4 text-orange-500 animate-spin" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  // Render document card
  const renderDocumentCard = (document: ResearchDocument) => (
    <Card key={document.id} className="group hover:shadow-lg transition-all duration-300 border border-gray-200 overflow-hidden">
      <div className={`h-1 w-full ${document.status === 'ready' ? 'bg-green-500' : document.status === 'processing' ? 'bg-blue-500' : 'bg-red-500'}`}></div>
      <CardHeader className="pb-3 pt-4">
        <div className="flex items-start gap-3">
          <Checkbox
            checked={selectedDocuments.includes(document.id)}
            onCheckedChange={(checked) => handleDocumentSelection(document.id, checked as boolean)}
            className="mt-1"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(document.status)}
              <CardTitle className="text-lg line-clamp-2 leading-tight font-semibold text-gray-800">
                {document.title}
              </CardTitle>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Users className="h-4 w-4 text-indigo-500" />
              <span className="truncate">{document.authors.join(', ') || 'Unknown authors'}</span>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1 bg-gray-100 px-2 py-0.5 rounded-md">
                <Calendar className="h-3 w-3 text-gray-600" />
                <span>{document.publicationYear}</span>
              </div>
              {document.journal && (
                <div className="truncate italic text-gray-600">{document.journal}</div>
              )}
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="rounded-full h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-500 transition-colors"
            onClick={() => handlePreviewDocument(document)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-5">
        <div className="space-y-3">
          {document.abstract && (
            <p className="text-sm text-gray-700 line-clamp-3 leading-relaxed">
              {document.abstract}
            </p>
          )}
          
          {document.keywords.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {document.keywords.slice(0, 3).map((keyword, index) => (
                <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors">
                  # {keyword}
                </Badge>
              ))}
              {document.keywords.length > 3 && (
                <Badge variant="outline" className="text-xs bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200 transition-colors">
                  +{document.keywords.length - 3} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <FileText className="h-3 w-3" />
              <span>{document.sections.length} sections</span>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Toggle favorite
                  toast.info('Favorite functionality would be implemented here');
                }}
              >
                <Heart className={`h-4 w-4 ${document.favorite ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // More options
                  toast.info('More options would be implemented here');
                }}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Render document list item
  const renderDocumentListItem = (document: ResearchDocument) => (
    <div key={document.id} className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
      <Checkbox
        checked={selectedDocuments.includes(document.id)}
        onCheckedChange={(checked) => {
          console.log(`Document ${document.title} (${document.id}) selection changed:`, checked);
          handleDocumentSelection(document.id, checked as boolean);
        }}
      />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          {getStatusIcon(document.status)}
          <h3 className="font-medium text-gray-900 truncate">{document.title}</h3>
        </div>
        
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span className="truncate">{document.authors.join(', ')}</span>
          <span>{document.publicationYear}</span>
          {document.journal && <span className="truncate">{document.journal}</span>}
        </div>
        
        {document.keywords.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {document.keywords.slice(0, 5).map((keyword, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {keyword}
              </Badge>
            ))}
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePreviewDocument(document)}
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            toast.info('Download functionality would be implemented here');
          }}
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-3">
              <FileText className="h-6 w-6 text-blue-500" />
              Document Library ({documents.length} documents)
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Actions */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents by title, authors, or keywords..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="year">Year</SelectItem>
                <SelectItem value="authors">Authors</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
          </div>

          {/* Selection Actions */}
          {selectedDocuments.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium text-blue-900">
                {selectedDocuments.length} documents selected
              </span>
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                {selectedDocuments.length === documents.length ? 'Deselect All' : 'Select All'}
              </Button>
              <Button variant="default" size="sm">
                Generate Literature Review
              </Button>
              <Button variant="outline" size="sm">
                Analyze Research Gaps
              </Button>
            </div>
          )}

          {/* Filters Panel */}
          {showFilters && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium mb-2 block">Status</Label>
                  <Select value={filters.status} onValueChange={(value: any) => setFilters(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="ready">Ready</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-2 block">Authors</Label>
                  <Select onValueChange={(value) => {
                    if (!filters.authors.includes(value)) {
                      setFilters(prev => ({ ...prev, authors: [...prev.authors, value] }));
                    }
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select authors" />
                    </SelectTrigger>
                    <SelectContent>
                      {uniqueAuthors.map(author => (
                        <SelectItem key={author} value={author}>{author}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {filters.authors.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {filters.authors.map(author => (
                        <Badge key={author} variant="secondary" className="text-xs">
                          {author}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 ml-1"
                            onClick={() => setFilters(prev => ({ ...prev, authors: prev.authors.filter(a => a !== author) }))}
                          >
                            ×
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium mb-2 block">Tags</Label>
                  <Select onValueChange={(value) => {
                    if (!filters.tags.includes(value)) {
                      setFilters(prev => ({ ...prev, tags: [...prev.tags, value] }));
                    }
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tags" />
                    </SelectTrigger>
                    <SelectContent>
                      {uniqueTags.map(tag => (
                        <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Removed the original Q&A section since we now use the AIQuestionBubble */}

      {/* Documents Display */}
      <div className="space-y-4">
        {sortedDocuments.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileText className="h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
              <p className="text-gray-500 text-center">
                {documents.length === 0 
                  ? "Upload your first research papers to get started"
                  : "Try adjusting your search or filter criteria"
                }
              </p>
            </CardContent>
          </Card>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedDocuments.map(renderDocumentCard)}
          </div>
        ) : (
          <div className="space-y-3">
            {sortedDocuments.map(renderDocumentListItem)}
          </div>
        )}
      </div>

      {/* Document Preview Dialog */}
      <Dialog open={!!previewDocument} onOpenChange={() => setPreviewDocument(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-blue-500" />
              Document Preview
            </DialogTitle>
          </DialogHeader>
          
          {previewDocument && (
            <ScrollArea className="max-h-[60vh]">
              <div className="space-y-6">
                {/* Document Info */}
                <div className="space-y-4">
                  <h2 className="text-xl font-bold">{previewDocument.document.title}</h2>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Authors: <AUTHORS>
                    </div>
                    <div>
                      <strong>Year:</strong> {previewDocument.document.publicationYear}
                    </div>
                    {previewDocument.document.journal && (
                      <div>
                        <strong>Journal:</strong> {previewDocument.document.journal}
                      </div>
                    )}
                    <div>
                      <strong>Status:</strong> 
                      <span className="ml-2 inline-flex items-center gap-1">
                        {getStatusIcon(previewDocument.document.status)}
                        {previewDocument.document.status}
                      </span>
                    </div>
                  </div>

                  {previewDocument.document.abstract && (
                    <div>
                      <strong>Abstract:</strong>
                      <p className="mt-2 text-gray-700">{previewDocument.document.abstract}</p>
                    </div>
                  )}

                  {previewDocument.document.keywords.length > 0 && (
                    <div>
                      <strong>Keywords:</strong>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {previewDocument.document.keywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* AI Summary */}
                <div className="border-t pt-4">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Brain className="h-4 w-4 text-blue-500" />
                    AI-Generated Summary
                  </h3>
                  
                  {previewDocument.isLoading ? (
                    <div className="flex items-center gap-2 text-gray-500">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Generating summary...
                    </div>
                  ) : previewDocument.summary ? (
                    <p className="text-gray-700 whitespace-pre-wrap">{previewDocument.summary}</p>
                  ) : (
                    <p className="text-gray-500">Summary not available</p>
                  )}
                </div>

                {/* Key Findings */}
                {previewDocument.document.keyFindings.length > 0 && (
                  <div className="border-t pt-4">
                    <h3 className="font-semibold mb-3">Key Findings</h3>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {previewDocument.document.keyFindings.map((finding, index) => (
                        <li key={index}>{finding}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Methodology */}
                {previewDocument.document.methodology.length > 0 && (
                  <div className="border-t pt-4">
                    <h3 className="font-semibold mb-3">Methodology</h3>
                    <ul className="list-disc list-inside space-y-1 text-gray-700">
                      {previewDocument.document.methodology.map((method, index) => (
                        <li key={index}>{method}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
