/**
 * Tavily Search Service
 * Handles web search using Tavily API for educational content
 */

interface TavilySearchOptions {
  query: string;
  searchDepth?: 'basic' | 'advanced';
  includeImages?: boolean;
  includeAnswer?: boolean;
  maxResults?: number;
  includeDomains?: string[];
  excludeDomains?: string[];
}

interface TavilySearchResult {
  title: string;
  url: string;
  content: string;
  score: number;
  publishedDate?: string;
}

interface TavilyResponse {
  answer?: string;
  query: string;
  followUpQuestions?: string[];
  images?: Array<{
    url: string;
    description: string;
  }>;
  results: TavilySearchResult[];
  responseTime: number;
}

class TavilySearchService {
  private apiKey: string | null = null;
  private baseUrl = 'https://api.tavily.com';

  constructor() {
    this.apiKey = import.meta.env.VITE_TAVILY_API_KEY ||
                  process.env.NEXT_PUBLIC_TAVILY_API_KEY ||
                  process.env.TAVILY_API_KEY || null;

    if (!this.apiKey) {
      console.warn('Tavily API key not found. Web search functionality will be limited.');
    }
  }

  /**
   * Check if <PERSON><PERSON> is available
   */
  isAvailable(): boolean {
    return Boolean(this.apiKey);
  }

  /**
   * Search for educational content
   */
  async searchEducational(
    query: string,
    educationLevel: string = 'high-school',
    options: Partial<TavilySearchOptions> = {}
  ): Promise<TavilyResponse> {
    if (!this.isAvailable()) {
      throw new Error('Tavily API key not configured');
    }

    const searchOptions: TavilySearchOptions = {
      query: `${query} ${educationLevel} education learning`,
      searchDepth: 'advanced',
      includeImages: false,
      includeAnswer: true,
      maxResults: 10,
      includeDomains: [
        'edu',
        'wikipedia.org',
        'khanacademy.org',
        'coursera.org',
        'edx.org',
        'mit.edu',
        'stanford.edu',
        'harvard.edu',
        'britannica.com',
        'nationalgeographic.com',
        'scientificamerican.com',
        'nature.com',
        'science.org'
      ],
      excludeDomains: [
        'youtube.com',
        'tiktok.com',
        'instagram.com',
        'facebook.com',
        'twitter.com'
      ],
      ...options
    };

    try {
      const response = await this.makeSearchRequest(searchOptions);
      return this.processSearchResponse(response, query);
    } catch (error) {
      console.error('Tavily search failed:', error);
      throw new Error(`Search failed: ${error.message}`);
    }
  }

  /**
   * Search for specific academic topics
   */
  async searchAcademic(
    query: string,
    subject: string,
    options: Partial<TavilySearchOptions> = {}
  ): Promise<TavilyResponse> {
    if (!this.isAvailable()) {
      throw new Error('Tavily API key not configured');
    }

    const academicQuery = `${query} ${subject} academic research study`;
    
    const searchOptions: TavilySearchOptions = {
      query: academicQuery,
      searchDepth: 'advanced',
      includeImages: false,
      includeAnswer: true,
      maxResults: 8,
      includeDomains: [
        'edu',
        'scholar.google.com',
        'pubmed.ncbi.nlm.nih.gov',
        'arxiv.org',
        'jstor.org',
        'springer.com',
        'sciencedirect.com',
        'ieee.org',
        'acm.org',
        'researchgate.net'
      ],
      ...options
    };

    try {
      const response = await this.makeSearchRequest(searchOptions);
      return this.processSearchResponse(response, academicQuery);
    } catch (error) {
      console.error('Academic search failed:', error);
      throw new Error(`Academic search failed: ${error.message}`);
    }
  }

  /**
   * Get quick answer for a question
   */
  async getQuickAnswer(question: string): Promise<string | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const searchOptions: TavilySearchOptions = {
        query: question,
        searchDepth: 'basic',
        includeAnswer: true,
        maxResults: 3
      };

      const response = await this.makeSearchRequest(searchOptions);
      const processed = this.processSearchResponse(response, question);
      
      return processed.answer || null;
    } catch (error) {
      console.error('Quick answer search failed:', error);
      return null;
    }
  }

  /**
   * Make the actual API request to Tavily
   */
  private async makeSearchRequest(options: TavilySearchOptions): Promise<any> {
    const requestBody = {
      api_key: this.apiKey,
      query: options.query,
      search_depth: options.searchDepth || 'basic',
      include_images: options.includeImages || false,
      include_answer: options.includeAnswer || false,
      max_results: options.maxResults || 5,
      include_domains: options.includeDomains || [],
      exclude_domains: options.excludeDomains || []
    };

    const response = await fetch(`${this.baseUrl}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Tavily API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Process and format the search response
   */
  private processSearchResponse(response: any, originalQuery: string): TavilyResponse {
    const startTime = Date.now();
    
    const results: TavilySearchResult[] = (response.results || []).map((result: any) => ({
      title: result.title || '',
      url: result.url || '',
      content: result.content || '',
      score: result.score || 0,
      publishedDate: result.published_date
    }));

    // Sort by relevance score
    results.sort((a, b) => b.score - a.score);

    return {
      answer: response.answer || undefined,
      query: originalQuery,
      followUpQuestions: response.follow_up_questions || [],
      images: response.images || [],
      results,
      responseTime: Date.now() - startTime
    };
  }

  /**
   * Format search results for display
   */
  formatResultsForDisplay(response: TavilyResponse): string {
    let formatted = '';

    if (response.answer) {
      formatted += `**Answer:** ${response.answer}\n\n`;
    }

    if (response.results.length > 0) {
      formatted += '**Sources:**\n';
      response.results.slice(0, 5).forEach((result, index) => {
        formatted += `${index + 1}. **${result.title}**\n`;
        formatted += `   ${result.url}\n`;
        if (result.content) {
          const snippet = result.content.length > 200 
            ? result.content.substring(0, 200) + '...' 
            : result.content;
          formatted += `   ${snippet}\n`;
        }
        formatted += '\n';
      });
    }

    return formatted;
  }

  /**
   * Extract key information from search results
   */
  extractKeyInformation(response: TavilyResponse): {
    summary: string;
    keyPoints: string[];
    sources: Array<{ title: string; url: string }>;
  } {
    const summary = response.answer || 'No summary available';
    
    const keyPoints: string[] = [];
    const sources: Array<{ title: string; url: string }> = [];

    response.results.forEach(result => {
      sources.push({
        title: result.title,
        url: result.url
      });

      // Extract key sentences from content
      if (result.content) {
        const sentences = result.content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        keyPoints.push(...sentences.slice(0, 2).map(s => s.trim()));
      }
    });

    return {
      summary,
      keyPoints: [...new Set(keyPoints)].slice(0, 10), // Remove duplicates and limit
      sources: sources.slice(0, 8)
    };
  }
}

export const tavilySearchService = new TavilySearchService();
