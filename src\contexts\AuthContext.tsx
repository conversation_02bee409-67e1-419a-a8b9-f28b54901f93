import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import type { Database } from '@/lib/database.types'
import { toast } from 'sonner'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: Profile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: any }>
  signInWithGoogle: () => Promise<{ error: any }>
  signOut: () => Promise<{ error: any }>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)



  useEffect(() => {
    // Track if the component is still mounted
    let isMounted = true;
    
    const initializeAuth = async () => {
      try {
        console.log('Initializing authentication...');
        
        // Get initial session
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          if (isMounted) setLoading(false);
          return;
        }
        
        if (data.session) {
          console.log('Initial session found:', data.session.user?.email);
          if (isMounted) {
            setSession(data.session);
            setUser(data.session.user);
            
            // Fetch profile in a separate step to avoid blocking auth initialization
            if (data.session.user) {
              fetchProfile(data.session.user.id);
            } else {
              setLoading(false);
            }
          }
        } else {
          console.log('No initial session found');
          if (isMounted) setLoading(false);
        }
      } catch (err) {
        console.error('Unexpected error during auth initialization:', err);
        if (isMounted) setLoading(false);
      }
    };

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        console.log('Auth state changed:', { event, user: currentSession?.user?.email });
        
        if (!isMounted) return;
        
        switch(event) {
          case 'SIGNED_IN':
            console.log('User signed in, updating state');
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            if (currentSession?.user) {
              fetchProfile(currentSession.user.id);
            } else {
              setLoading(false);
            }
            break;
            
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed, updating session');
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            if (currentSession?.user) {
              fetchProfile(currentSession.user.id);
            } else {
              setLoading(false);
            }
            break;
            
          case 'SIGNED_OUT':
            console.log('User signed out, clearing state');
            setSession(null);
            setUser(null);
            setProfile(null);
            setLoading(false);
            break;
            
          case 'USER_UPDATED':
            console.log('User updated, refreshing profile');
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            if (currentSession?.user) {
              fetchProfile(currentSession.user.id);
            } else {
              setLoading(false);
            }
            break;
            
          default:
            // For any other events, just update the session and user
            console.log(`Auth event: ${event}`);
            setSession(currentSession);
            setUser(currentSession?.user ?? null);
            setLoading(false);
        }
      }
    );

    // Initialize authentication
    initializeAuth();

    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (isMounted && loading) {
        console.log('Auth initialization timeout reached, forcing loading state to false');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    // Cleanup function
    return () => {
      console.log('Auth context cleanup');
      isMounted = false;
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    };
  }, [])

  const fetchProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId);
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        
        // If profile doesn't exist, create it
        if (error.code === 'PGRST116') {
          createProfile(userId);
          return;
        }
      } else {
        console.log('Profile fetched successfully:', data);
        setProfile(data);
      }
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create a profile if it doesn't exist
  const createProfile = async (userId: string) => {
    try {
      console.log('Creating new profile for user:', userId);
      
      const { data: userData } = await supabase.auth.getUser();
      const user = userData?.user;
      
      if (!user) {
        console.error('Cannot create profile: User data not available');
        return;
      }
      
      const newProfile = {
        id: userId,
        email: user.email || '',
        full_name: user.user_metadata?.full_name || '',
        avatar_url: user.user_metadata?.avatar_url || '',
        updated_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('profiles')
        .insert(newProfile)
        .select()
        .single();
      
      if (error) {
        console.error('Error creating profile:', error);
      } else {
        console.log('Profile created successfully:', data);
        setProfile(data);
      }
    } catch (error) {
      console.error('Unexpected error creating profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) {
        console.error('Sign in error:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error: any) {
      console.error('Unexpected error during sign in:', error);
      return { error };
    } finally {
      // Don't set loading to false here as it will be handled by the auth state change listener
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });
      
      if (error) {
        console.error('Sign up error:', error);
        return { error };
      }
      
      // If email confirmation is required, show a message
      if (data.user && !data.session) {
        toast.info('Please check your email for a confirmation link to complete your registration.');
      }
      
      return { error: null };
    } catch (error: any) {
      console.error('Unexpected error during sign up:', error);
      return { error };
    } finally {
      // Loading state will be updated by auth state change listener
    }
  };

  const signInWithGoogle = async () => {
    setLoading(true);
    try {
      // Use the absolute redirect URL to ensure consistency
      const redirectUrl = `${window.location.origin}/auth/callback`;
      console.log('Attempting Google sign-in with redirectTo:', redirectUrl);

      // Add required scopes for Google Workspace and stricter accounts
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          scopes: 'https://www.googleapis.com/auth/userinfo.email',
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        }
      });

      if (error) {
        console.error('Google sign-in error:', error);
        toast.error(`Google sign-in failed: ${error.message}`, {
          description: "Try the direct login method on the OAuth diagnostics page",
          action: {
            label: "Diagnostics",
            onClick: () => window.location.href = "/oauth-diagnostics"
          }
        });
        setLoading(false);
        return { error };
      }

      // No need to redirect here - Supabase will handle the redirect automatically
      return { error: null };
    } catch (error: any) {
      console.error('Unexpected error during Google sign-in:', error);
      setLoading(false);
      return { error };
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      console.log('Attempting to sign out user');
      
      // DON'T manually clear Supabase auth tokens - let Supabase handle this
      // Only remove our custom state if it exists
      localStorage.removeItem('oauth_state');
      
      // Call Supabase signOut method which will properly clean up the session
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Sign out error:', error);
        return { error };
      }
      
      // Clear context state
      setUser(null);
      setSession(null);
      setProfile(null);
      
      // Use a short timeout to ensure Supabase has time to clean up session
      setTimeout(() => {
        // Then redirect to login page
        console.log('User signed out successfully, redirecting to login');
        window.location.href = '/login';
      }, 100);
      
      return { error: null };
    } catch (error: any) {
      console.error('Unexpected error during sign out:', error);
      // Even if an error occurs, try to redirect to login
      window.location.href = '/login';
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: new Error('No user logged in') };
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Update profile error:', error);
        return { error };
      }
      
      if (data) {
        setProfile({ ...profile, ...data });
      }
      
      return { error: null };
    } catch (error: any) {
      console.error('Unexpected error updating profile:', error);
      return { error };
    }
  };

  const value = {
    user,
    session,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
