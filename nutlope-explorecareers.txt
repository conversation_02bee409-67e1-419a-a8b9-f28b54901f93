Directory structure:
└── nutlope-explorecareers/
    ├── README.md
    ├── components.json
    ├── LICENSE
    ├── next.config.mjs
    ├── package.json
    ├── postcss.config.mjs
    ├── tailwind.config.ts
    ├── tsconfig.json
    ├── .example.env
    ├── app/
    │   ├── globals.css
    │   ├── layout.tsx
    │   ├── page.tsx
    │   ├── api/
    │   │   ├── getCareers/
    │   │   │   └── route.ts
    │   │   └── parsePdf/
    │   │       └── route.ts
    │   └── careers/
    │       └── page.tsx
    ├── components/
    │   ├── CareerNode.tsx
    │   ├── CTA.tsx
    │   ├── Footer.tsx
    │   ├── GradientWrapper.tsx
    │   ├── Header.tsx
    │   ├── Hero.tsx
    │   ├── NavLink.tsx
    │   └── ui/
    │       ├── button.tsx
    │       ├── dialog.tsx
    │       ├── input.tsx
    │       ├── label.tsx
    │       ├── loading-dots.module.css
    │       ├── loadingdots.tsx
    │       └── textarea.tsx
    └── lib/
        ├── types.ts
        └── utils.ts

================================================
FILE: README.md
================================================
<a href="https://www.explorecareers.io">
  <img alt="Explore Careers." src="./public/og-image.png">
  <h1 align="center">ExploreCareers</h1>
</a>

<p align="center">
  Explore careers relevant for you in seconds. Powered by Together.ai.
</p>

<p align="center">
  <a href="#tech-stack"><strong>Tech Stack</strong></a> ·
  <a href="#future-tasks"><strong>Future Tasks</strong></a> ·
    <a href="#inspiration"><strong>Inspiration</strong></a>
</p>
<br/>

## Tech Stack

This app lets people upload their resumes, fill in some of their interests, and get a list of possible careers they could do.

- [Llama-3-70B](https://llama.meta.com/llama3/) for the LLM
- [Together.ai](https://togetherai.link/) for the AI API (inference)
- [Plausible](https://plausible.io/) for website analytics
- [Reactflow](https://reactflow.dev/) for the data visualization
- [Helicone](https://helicone.ai/) for LLM observability
- Next.js [App Router](https://nextjs.org/docs/app) for the framework
- [S3 / ByteScale](https://www.bytescale.com/) for the PDF storage
- [Vercel](https://vercel.com/) for hosting & functions
- [Tailwind CSS](https://tailwindcss.com/) for styling

## Future tasks

- [ ] Build in a PII remover so that we can remove people's personal info from their resume
- [ ] Add loading state to inform people that it may take up to 15-20 seconds to generate
- [ ] Clean up the code to not have default react node data that I have right now
- [ ] Add authentication with Clerk to make people create accounts and remember their resumes
- [ ] Improve the prompts using synthetic data from GPT-4o for the examples that I specify
- [ ] Add sharability to the URLs with nanoid
- [ ] Add rate limiting with Redis Upstash
- [ ] Add multi-step form where people can add their interests and add this to the prompt of the first endpoint to use someone's interests in there
- [ ] Add a settings screen where users can go update their resume and interests overall
- [ ] Add functionality to let users suggest new careers themselves and get a developed plan for it
- [ ] Use Crew AI agents to make this workflow more robust overall: An agent that takes resume & extra info then outputs a very nice summary with keywords. An agent that comes up with multiple possible career paths from that summary. An agent that then constructs a plan for each career.
- [ ] Add functionality for people to alternatively paste in their LinkedIn URL instead of resume with [scrapedin](https://github.com/linkedtales/scrapedin/tree/master)
- [ ] Who do I want to be more like functionality where users can add a famous person & get suggestions for how to be more like them. This might be good as a separate app
- [ ] Fine-tune Llama-3-8B on making the job plan. Maybe fine-tune Llama-3-8B on GPT-4 outputs
- [ ] Improve PDF parsing functionality to include image detection and OCR to be able to read resumes even better

## Inspiration

I took inspiration from [wanderer.space](https://www.wanderer.space) for this app. Check it out for a production version of what I built with even more features!



================================================
FILE: components.json
================================================
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "app/globals.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}


================================================
FILE: LICENSE
================================================
MIT License Copyright (c) 2024 Hassan El Mghari

Permission is hereby granted, free of
charge, to any person obtaining a copy of this software and associated
documentation files (the "Software"), to deal in the Software without
restriction, including without limitation the rights to use, copy, modify, merge,
publish, distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to the
following conditions:

The above copyright notice and this permission notice
(including the next paragraph) shall be included in all copies or substantial
portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF
ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.



================================================
FILE: next.config.mjs
================================================
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['pdf-parse'],
  },
};

export default nextConfig;



================================================
FILE: package.json
================================================
{
  "name": "career-exploration",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@bytescale/sdk": "^3.39.0",
    "@bytescale/upload-widget-react": "^4.19.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-slot": "^1.0.2",
    "@vercel/analytics": "^1.2.2",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.1",
    "lucide-react": "^0.378.0",
    "next": "14.2.3",
    "next-plausible": "^3.12.0",
    "openai": "^4.45.0",
    "pdf-parse": "^1.1.1",
    "react": "^18",
    "react-dom": "^18",
    "react-hot-toast": "^2.4.1",
    "reactflow": "^11.11.3",
    "tailwind-merge": "^2.3.0",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/pdf-parse": "^1.1.4",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "postcss": "^8",
    "tailwindcss": "^3.4.1",
    "typescript": "^5"
  }
}



================================================
FILE: postcss.config.mjs
================================================
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
  },
};

export default config;



================================================
FILE: tailwind.config.ts
================================================
import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;



================================================
FILE: tsconfig.json
================================================
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}



================================================
FILE: .example.env
================================================
NEXT_PUBLIC_BYTESCALE_API_KEY=
TOGETHER_API_KEY=
HELICONE_API_KEY=



================================================
FILE: app/globals.css
================================================
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.custom-screen {
  @apply max-w-[1400px] mx-auto px-4 md:px-8;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}



================================================
FILE: app/layout.tsx
================================================
import Header from '@/components/Header';
import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Footer from '@/components/Footer';
import { Analytics } from '@vercel/analytics/react';
import PlausibleProvider from 'next-plausible';

const inter = Inter({ subsets: ['latin'] });

let title = 'Explore Careers';
let description = 'Explore careers based on your interests & skills';
let url = 'https://explorecareers.io/';
let ogimage = 'https://explorecareers.io/og-image.png';
let sitename = 'ExploreCareers.io';

export const metadata: Metadata = {
  metadataBase: new URL(url),
  title,
  description,
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    images: [ogimage],
    title,
    description,
    url: url,
    siteName: sitename,
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    images: [ogimage],
    title,
    description,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en'>
      <head>
        <PlausibleProvider domain='explorecareers.io' />
      </head>
      <body className={inter.className}>
        <Header />
        <main>{children}</main>
        <Analytics />
        <Footer />
      </body>
    </html>
  );
}



================================================
FILE: app/page.tsx
================================================
import CTA from '@/components/CTA';
import GradientWrapper from '@/components/GradientWrapper';
import Hero from '@/components/Hero';

export default function Home() {
  return (
    <>
      <Hero />
      <GradientWrapper />
      <CTA />
    </>
  );
}



================================================
FILE: app/api/getCareers/route.ts
================================================
import { NextRequest } from 'next/server';
import OpenAI from 'openai';

const together = new OpenAI({
  baseURL: 'https://together.hconeai.com/v1',
  apiKey: process.env.TOGETHER_API_KEY,
  defaultHeaders: {
    'Helicone-Auth': `Bearer ${process.env.HELICONE_API_KEY}`,
  },
});

interface GetCareersRequest {
  resumeInfo: string;
  context: string;
}

export async function POST(request: NextRequest) {
  const { resumeInfo, context } = (await request.json()) as GetCareersRequest;

  const chatCompletion = await together.chat.completions.create({
    messages: [
      {
        role: 'system',
        content: 'You are a helpful career expert that ONLY responds in JSON.',
      },
      {
        role: 'user',
        content: `Give me 6 career paths that the following user could transition into based on their resume and any additional context. Respond like this in JSON: {jobTitle: string, jobDescription: string, timeline: string, salary: string, difficulty: string}.

      <example>
      [
        {
        "jobTitle": "UX Designer",
        "jobDescription": "Creates user-centered design solutions to improve product usability and user experience.",
        "timeline": "3-6 months",
        "salary": "$85k - $110k",
        "difficulty": "Medium"
        },
        {
        "jobTitle": "Digital Marketing Specialist",
        "jobDescription": "Develops and implements online marketing campaigns to drive business growth.",
        "timeline": "2-4 months",
        "salary": "$50k - $70k",
        "difficulty": "Low"
        },
        {
        "jobTitle": "Software Engineer",
        "jobDescription": "Designs, develops, and tests software applications to meet business needs.",
        "timeline": "6-12 months",
        "salary": "$100k - $140k",
        "difficulty": "High"
        },
        {
        "jobTitle": "Business Analyst",
        "jobDescription": "Analyzes business needs and develops solutions to improve operations and processes.",
        "timeline": "3-6 months",
        "salary": "$65k - $90k",
        "difficulty": "Medium"
        },
        {
        "jobTitle": "Cybersecurity Specialist",
        "jobDescription": "Protects computer systems and networks from cyber threats by developing and implementing security protocols.",
        "timeline": "6-12 months",
        "salary": "$80k - $120k",
        "difficulty": "High"
        }
        ]
      </example>

      <resume>
      ${resumeInfo}
      </resume>

      <additionalContext>
      ${context}
      </additionalContext>

    ONLY respond with JSON, nothing else.
      `,
      },
    ],
    model: 'meta-llama/Llama-3-70b-chat-hf',
  });
  const careers = chatCompletion.choices[0].message.content;

  const careerInfoJSON = JSON.parse(careers!);

  let finalResults = await Promise.all(
    careerInfoJSON.map(async (career: any) => {
      try {
        const completion = await together.chat.completions.create({
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful career expert that ONLY responds in JSON.',
            },
            {
              role: 'user',
              content: `You are helping a person transition into the ${career.jobTitle} role in ${career.timeline}. Given the context about the person, return more information about the ${career.jobTitle} role in JSON as follows: {workRequired: string, aboutTheRole: string, whyItsagoodfit: array[], roadmap: [{string: string}, ...]

          <example>
          {"role": "DevOps Engineer",
          "workRequired": "20-30 hrs/week",
          "whyItsagoodfit": [
            "Leverages your extensive experience in software engineering and developer advocacy.",
            "Utilizes your skills in Python, JavaScript, Node.js, React, and cloud services like AWS.",
            "Aligns with your experience in building and maintaining large-scale applications and infrastructure.",
            "Allows you to continue working with cutting-edge technologies and practices."
          ],
          "aboutTheRole": "As a DevOps Engineer, you will work closely with development, operations, and QA teams to streamline the software development lifecycle. Your responsibilities will include automating infrastructure provisioning, monitoring system performance, and ensuring security and compliance. The goal is to enhance the efficiency, reliability, and scalability of software deployments.",
          "roadmap": [
            {"Weeks 1-2": "Learn the basics of DevOps tools and practices, including Docker and Kubernetes. Start with online courses or tutorials to build foundational knowledge."},
            {"Weeks 3-4": "Set up a local development environment with Docker and Kubernetes. Practice creating and managing containers and clusters."},
            {"Weeks 5-6": "Explore continuous integration and continuous delivery (CI/CD) concepts. Implement a simple CI/CD pipeline using tools like Jenkins or GitLab CI."},
            {"Weeks 7-8": "Familiarize yourself with configuration management tools like Ansible or Terraform. Practice writing scripts to automate infrastructure provisioning."},
            {"Weeks 9-10": "Obtain a relevant certification such as AWS Certified DevOps Engineer or Google Cloud Professional DevOps Engineer to validate your skills."},
            {"Weeks 11-12": "Set up monitoring and logging solutions using tools like Prometheus, Grafana, and ELK stack. Learn to monitor system performance and troubleshoot issues."},
            {"Weeks 13-14": "Optimize your CI/CD pipelines for efficiency, scalability, and reliability. Implement advanced deployment strategies such as blue-green deployments or canary releases."},
            {"Weeks 15-16": "Collaborate with development and operations teams on real projects to apply your skills in a practical setting. Seek feedback and continuously improve your processes."}
          ]}
          </example>

          <context>
          ${resumeInfo}
          ${context}
          </context>

          ONLY respond with JSON, nothing else.`,
            },
          ],
          model: 'meta-llama/Llama-3-70b-chat-hf',
        });
        const specificCareer = completion.choices[0].message.content;
        const specificCareerJSON = JSON.parse(specificCareer!);

        const individualCareerInfo = { ...career, ...specificCareerJSON };
        return individualCareerInfo;
      } catch (error) {
        console.log('Career that errored: ', career.jobTitle);
        console.log({ error });
        return new Response(JSON.stringify({ error }), {
          status: 500,
        });
      }
    })
  );

  return new Response(JSON.stringify(finalResults), {
    status: 200,
  });
}



================================================
FILE: app/api/parsePdf/route.ts
================================================
import { normalizeText } from '@/lib/utils';
import { NextRequest } from 'next/server';
import pdfParse from 'pdf-parse';

interface PDFParseRequest {
  resumeUrl: string;
}

export async function POST(request: NextRequest) {
  const { resumeUrl } = (await request.json()) as PDFParseRequest;

  const response = await fetch(resumeUrl);
  const arrayBuffer = await response.arrayBuffer();
  const pdfData = await pdfParse(Buffer.from(arrayBuffer));
  const normalizedText = normalizeText(pdfData.text);

  return new Response(JSON.stringify(normalizedText), {
    status: 200,
  });
}



================================================
FILE: app/careers/page.tsx
================================================
'use client';

import toast, { Toaster } from 'react-hot-toast';
import CareerNode from '@/components/CareerNode';
import { uploaderOptions } from '@/lib/utils';
import { UrlBuilder } from '@bytescale/sdk';
import { UploadDropzone } from '@bytescale/upload-widget-react';
import { useCallback, useEffect, useState } from 'react';
import ReactFlow, {
  Controls,
  addEdge,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';
import type { Node, NodeTypes } from 'reactflow';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import LoadingDots from '@/components/ui/loadingdots';
import { finalCareerInfo } from '@/lib/types';

const nodeTypes = {
  careerNode: CareerNode,
} satisfies NodeTypes;

// TODO: Clean this up
const initialNodes = [
  {
    id: '1',
    position: { x: 650, y: 450 },
    data: { label: 'Careers' },
    style: { background: '#000', color: '#fff', fontSize: '20px' },
  },
  {
    id: '2',
    type: 'careerNode',
    position: { x: 50, y: 550 },
    data: {
      jobTitle: 'SEO Specialist',
      jobDescription: `Uses research to improve a website's ranking in search engine results`,
      timeline: '2-3 months',
      salary: '$59k - $77k',
      difficulty: 'Low',
      connectPosition: 'top',
    },
  },
  {
    id: '3',
    type: 'careerNode',
    position: { x: 1050, y: 550 },
    data: {
      jobTitle: 'UX Designer',
      jobDescription:
        'Creates user-centered design solutions to improve product usability and user experience.',
      timeline: '3-6 months',
      salary: '$85k - $110k',
      difficulty: 'Medium',
      connectPosition: 'top',
    },
  },
  {
    id: '4',
    type: 'careerNode',
    position: { x: 50, y: 150 },
    data: {
      jobTitle: 'Digital Marketing Specialist',
      jobDescription:
        'Develops online marketing campaigns to drive business growth.',
      timeline: '2-4 months',
      salary: '$50k - $70k',
      difficulty: 'Low',
      connectPosition: 'bottom',
    },
  },
  {
    id: '5',
    type: 'careerNode',
    position: { x: 1050, y: 150 },
    data: {
      jobTitle: 'Software Engineer',
      jobDescription:
        'Designs, develops, and tests software applications to meet business needs.',
      timeline: '6-12 months',
      salary: '$100k - $140k',
      difficulty: 'High',
      connectPosition: 'bottom',
    },
  },
  {
    id: '6',
    type: 'careerNode',
    position: { x: 550, y: 700 },
    data: {
      jobTitle: 'Cybersecurity Specialist',
      jobDescription:
        'Protects computer systems and networks from cyber threats by developing and implementing security protocols.',
      timeline: '6-12 months',
      salary: '$80k - $120k',
      difficulty: 'High',
      connectPosition: 'top',
    },
  },
  {
    id: '7',
    type: 'careerNode',
    position: { x: 550, y: 0 },
    data: {
      jobTitle: 'Business Analyst',
      jobDescription:
        'Analyzes business needs and develops solutions to improve operations and processes.',
      timeline: '3-6 months',
      salary: '$65k - $90k',
      difficulty: 'Medium',
      connectPosition: 'bottom',
    },
  },
] satisfies Node[];

const initialEdges = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    animated: true,
    style: { stroke: '#000' },
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    animated: true,
    style: { stroke: '#000' },
  },
  {
    id: 'e1-4',
    source: '1',
    target: '4',
    animated: true,
    style: { stroke: '#000' },
  },
  {
    id: 'e1-5',
    source: '1',
    target: '5',
    animated: true,
    style: { stroke: '#000' },
  },
  {
    id: 'e1-6',
    source: '1',
    target: '6',
    animated: true,
    style: { stroke: '#000' },
  },
  {
    id: 'e1-7',
    source: '1',
    target: '7',
    animated: true,
    style: { stroke: '#000' },
  },
];

export default function Start() {
  const [_, setName] = useState('');
  const [url, setUrl] = useState('');
  const [nodes, setNodes, onNodesChange] = useNodesState(
    initialNodes as Node[]
  );
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [careerInfo, setCareerInfo] = useState<finalCareerInfo[]>([]);
  const [additionalContext, setAdditionalContext] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setNodes((initialNodes) =>
      initialNodes.map((node) => {
        if (node.id === '1') {
          node.data = {
            label: 'Careers',
          };
        } else {
          let realdata = careerInfo[Number(node.id) - 2];

          if (node.id === '2' || node.id === '3' || node.id === '6') {
            // @ts-ignore
            node.data = { ...realdata, connectPosition: 'top' };
          } else {
            // @ts-ignore
            node.data = { ...realdata, connectPosition: 'bottom' };
          }
        }
        return node;
      })
    );
  }, [careerInfo]);

  const onConnect = useCallback(
    (params: any) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const notify = () => toast.error('Failed to generate, please try again.');

  async function parsePdf() {
    setLoading(true);
    let response = await fetch('/api/parsePdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ resumeUrl: url }),
    });
    let data = await response.json();

    let response2 = await fetch('/api/getCareers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resumeInfo: data,
        context: additionalContext,
      }),
    });

    if (!response2.ok) {
      console.error('Failed to fetch');
      setLoading(false);
      notify();
      return;
    }

    let data2 = await response2.json();
    setCareerInfo(data2);
    setLoading(false);
  }

  return (
    <div>
      {careerInfo.length !== 0 ? (
        <div className='w-screen h-[1200px] mx-auto'>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
          >
            <Controls />
          </ReactFlow>
        </div>
      ) : (
        <div className='p-10 mt-16 flex justify-center items-center flex-col '>
          <h1 className='text-center text-5xl mb-5 font-bold'>
            Upload your resume
          </h1>
          <p className='mb-8 text-center text-gray-600 max-w-3xl'>
            Upload your resume to get started and add any extra context below.
            We'll analyze your resume along with the interests you provide and
            provide you with 6 personalized career paths for you.
          </p>
          <UploadDropzone
            options={uploaderOptions}
            onUpdate={({ uploadedFiles }) => {
              if (uploadedFiles.length !== 0) {
                const file = uploadedFiles[0];
                const fileName = file.originalFile.file.name;
                const fileUrl = UrlBuilder.url({
                  accountId: file.accountId,
                  filePath: file.filePath,
                });
                setName(fileName);
                setUrl(fileUrl);
              }
            }}
            onComplete={() => console.log('upload complete')}
            width='695px'
            height='350px'
          />
          <Textarea
            placeholder='Describe any of your career interests and passions. This will help us match you with the right job paths (optional).'
            value={additionalContext}
            onChange={(e) => setAdditionalContext(e.target.value)}
            className='mt-5 max-w-2xl text-base border border-gray-400 focus:border-black'
            rows={6}
          />
          <Button
            onClick={parsePdf}
            className='mt-10 text-base px-5 py-7 w-60'
            disabled={url ? false : true}
          >
            {loading ? (
              <LoadingDots style='big' color='white' />
            ) : (
              'Find your ideal career'
            )}
          </Button>
        </div>
      )}
      <Toaster />
    </div>
  );
}



================================================
FILE: components/CareerNode.tsx
================================================
import { memo } from 'react';
import { Handle, Position } from 'reactflow';
import type { NodeProps } from 'reactflow';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from './ui/button';

type CareerNodeProps = {
  jobTitle?: string;
  jobDescription?: string;
  timeline?: string;
  salary?: string;
  difficulty?: string;
  connectPosition?: string;
  label?: string;
  workRequired?: string;
  aboutTheRole?: string;
  whyItsagoodfit?: string[];
  roadmap?: { [key: string]: string }[];
};

function CareerNode({ data }: NodeProps<CareerNodeProps>) {
  const {
    jobTitle,
    jobDescription,
    timeline,
    salary,
    difficulty,
    connectPosition,
    workRequired,
    aboutTheRole,
    whyItsagoodfit,
    roadmap,
  } = data;
  const position = connectPosition === 'top' ? Position.Top : Position.Bottom;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className='border border-gray-300 rounded-2xl py-4 px-7 max-w-[350px] bg-gray-50'>
          <Handle type='target' position={position} />
          <h1 className='text-2xl font-bold mb-2'>{jobTitle}</h1>
          <p className='mb-4 font-light'>{jobDescription}</p>
          <div className='flex flex-col gap-1'>
            <div className='flex justify-between'>
              <div className='font-light'>TIMELINE:</div>
              <div className='font-medium text-lg'>{timeline}</div>
            </div>
            <div className='flex justify-between'>
              <div className='font-light'>SALARY:</div>
              <div className='font-medium text-lg'>{salary}</div>
            </div>
            <div className='flex justify-between'>
              <div className='font-light'>DIFFICULTY:</div>
              <div
                className={`font-semibold ${
                  difficulty?.toLowerCase() == 'low'
                    ? 'text-green-600'
                    : difficulty?.toLowerCase() == 'high'
                    ? 'text-red-600'
                    : 'text-orange-600'
                } text-lg`}
              >
                {difficulty}
              </div>
            </div>
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className='sm:max-w-6xl'>
        <DialogHeader>
          <DialogTitle className='flex justify-between'>
            <div className='flex items-center gap-3'>
              <span className='text-2xl'>{jobTitle ?? 'SEO Specialist'}</span>
              <span className='border rounded-3xl border-gray-200 px-3 py-1 text-sm'>
                {timeline}
              </span>
              <span className='border rounded-3xl border-gray-200 px-3 py-1 text-sm'>
                {salary}
              </span>
              <span
                className={`border rounded-3xl border-gray-200 px-3 py-1 text-sm font-semibold ${
                  difficulty?.toLowerCase() == 'low'
                    ? 'text-green-600'
                    : difficulty?.toLowerCase() == 'high'
                    ? 'text-red-600'
                    : 'text-orange-600'
                } text-lg`}
              >
                {difficulty}
              </span>
            </div>
            <div className='flex items-center gap-3 mr-5'>
              <div className='font-bold'>Work Required:</div>
              <span className='border rounded-3xl border-gray-200 px-3 py-1 text-sm'>
                {workRequired ?? '10-20 hrs/week'}
              </span>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className='flex gap-7 border-t border-black pt-6'>
          <div className='flex flex-col gap-4 w-2/5'>
            <div>
              <h2 className='text-lg font-semibold mb-2'>
                What's a {jobTitle}?
              </h2>
              <p>
                {aboutTheRole ??
                  `SEO Specialists optimize websites to rank higher in search
                engine results, aiming to increase online visibility, drive
                organic traffic, and improve user engagement. They conduct
                keyword research, analyze competitors, and implement SEO
                strategies that include on-page optimization, link building, and
                content creation.`}
              </p>
            </div>
            <div>
              <h2 className='text-lg font-semibold mb-2 mt-6'>
                Why it's a good fit
              </h2>
              <ul className='list-disc ml-4'>
                {whyItsagoodfit?.map((reason, index) => (
                  <li key={index}>{reason}</li>
                ))}
              </ul>
            </div>
          </div>
          <div className='w-3/5'>
            <h2 className='text-lg font-semibold mb-2'>Roadmap</h2>
            <div className='flex flex-col gap-2'>
              {roadmap?.map((step, index) => (
                <div key={index} className='flex gap-3'>
                  <div className='font-light min-w-28'>
                    {Object.keys(step)[0]}:
                  </div>
                  <div>{Object.values(step)[0]}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button type='submit'>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default memo(CareerNode);



================================================
FILE: components/CTA.tsx
================================================
const CTA = () => (
  <SectionWrapper>
    <div className='custom-screen'>
      <div className='max-w-2xl mx-auto text-center'>
        <h2
          className='text-gray-800 text-3xl font-semibold sm:text-4xl'
          id='oss'
        >
          ExploreCareers is Proudly Open Source
        </h2>
        <p className='mt-3 text-gray-600'>
          Our source code is available on GitHub.
        </p>
        <a
          href='https://github.com/Nutlope/ExploreCareers'
          className='mt-4 inline-flex justify-center items-center gap-2 font-medium text-sm text-white bg-gray-800 hover:bg-gray-600 active:bg-gray-900 max-w-[200px] py-2.5 px-4 text-center rounded-lg duration-150'
        >
          <svg
            aria-hidden='true'
            className='h-6 w-6 fill-slate-200 group-hover:fill-slate-700'
          >
            <path d='M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0 1 12 6.844a9.59 9.59 0 0 1 2.504.337c1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.02 10.02 0 0 0 22 12.017C22 6.484 17.522 2 12 2Z' />
          </svg>
          <span>Star on GitHub</span>
        </a>
      </div>
    </div>
  </SectionWrapper>
);

const SectionWrapper = ({ children, ...props }: any) => (
  <section {...props} className={`py-16 ${props.className || ''}`}>
    {children}
  </section>
);

export default CTA;



================================================
FILE: components/Footer.tsx
================================================
const Footer = () => (
  <footer>
    <div className='custom-screen pt-16'>
      <div className='mt-10 py-10 border-t items-center justify-between flex'>
        <p className='text-gray-600'>
          Created by{' '}
          <a
            href='https://twitter.com/nutlope'
            className='hover:underline transition'
          >
            Hassan
          </a>{' '}
          and{' '}
          <a
            href='https://twitter.com/youssefuiux'
            className='hover:underline transition'
          >
            Youssef
          </a>
          .{' '}
        </p>
        <div className='flex items-center gap-x-6 text-gray-400'>
          <a
            className='tracking-tight flex gap-1 hover:underline transition'
            href='https://togetherai.link'
            target='_blank'
          >
            <span className='text-gray-500'>
              Powered by Together.ai and Llama-3.
            </span>
          </a>
        </div>
      </div>
    </div>
  </footer>
);

export default Footer;



================================================
FILE: components/GradientWrapper.tsx
================================================
const GradientWrapper = ({ children, ...props }: any) => (
  <div
    {...props}
    className={`relative overflow-hidden my-16 border-t sm:my-28 ${
      props.className || ''
    }`}
  >
    <div
      className="blur-[100px] absolute inset-0 w-full h-full"
      style={{
        background:
          'linear-gradient(202.72deg, rgba(237, 78, 80, 0.05) 14.76%, rgba(152, 103, 240, 0.04) 34.37%, rgba(152, 103, 240, 0) 86.62%)',
      }}
    />
    <div className="relative">{children}</div>
  </div>
);

export default GradientWrapper;



================================================
FILE: components/Header.tsx
================================================
'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import NavLink from './NavLink';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const Header = () => {
  const [state, setState] = useState(false);

  const navigation = [
    { title: 'Homepage', path: '/' },
    { title: 'Explore Careers', path: '/careers' },
  ];

  const pathname = usePathname();

  useEffect(() => {
    // Add closing the navbar menu when navigating
    const handleState = () => {
      document.body.classList.remove('overflow-hidden');
      setState(false);
    };

    handleState();
  }, [pathname]);

  const handleNavMenu = () => {
    setState(!state);
    document.body.classList.toggle('overflow-hidden');
  };

  return (
    <header>
      <nav
        className={`bg-white w-full md:static md:text-sm ${
          state ? 'fixed z-10 h-full' : ''
        }`}
      >
        <div className='custom-screen items-center mx-auto md:flex'>
          <div className='flex items-center justify-between py-3 md:py-5 md:block'>
            <Link href='/' className='flex items-center gap-3'>
              {/* <Image src='/box.svg' alt='logo' width={30} height={30} />
              <div className='font-bold text-lg'>ExploreCareers</div> */}
              <Image src='/logo.svg' alt='logo' width={230} height={100} />
            </Link>
            <div className='md:hidden'>
              <button
                role='button'
                aria-label='Open the menu'
                className='text-gray-500 hover:text-gray-800'
                onClick={handleNavMenu}
              >
                {state ? (
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-6 w-6'
                    viewBox='0 0 20 20'
                    fill='currentColor'
                  >
                    <path
                      fillRule='evenodd'
                      d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                      clipRule='evenodd'
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    fill='none'
                    viewBox='0 0 24 24'
                    strokeWidth={1.5}
                    stroke='currentColor'
                    className='w-6 h-6'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      d='M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5'
                    />
                  </svg>
                )}
              </button>
            </div>
          </div>
          <div
            className={`flex-1 pb-3 mt-8 md:pb-0 md:mt-0 md:block ${
              state ? '' : 'hidden'
            }`}
          >
            <ul className='text-gray-700 justify-end items-center space-y-6 md:flex md:space-x-6 md:space-y-0 md:text-gray-600 md:font-medium'>
              {navigation.map((item, idx) => {
                return (
                  <li key={idx} className='duration-150 hover:text-gray-900'>
                    <Link href={item.path} className='block'>
                      {item.title}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;



================================================
FILE: components/Hero.tsx
================================================
'use client';

import Image from 'next/image';
import NavLink from './NavLink';

export default function Hero() {
  return (
    <section>
      <div className='custom-screen sm:pt-56 pt-28 text-gray-600 flex justify-between gap-8 sm:flex-row flex-col'>
        <div className='space-y-5 max-w-4xl mx-auto text-center sm:w-1/2'>
          <button
            className={`border py-2 rounded-2xl hover:bg-gray-100 transition px-5 text-sm text-gray-500 hover:text-gray-600`}
          >
            Used by 2,000+ professionals for career inspiration
          </button>
          <h1 className='text-4xl text-gray-800 font-extrabold mx-auto sm:text-6xl'>
            Find the right career for you using AI
          </h1>
          <p className='max-w-xl mx-auto'>
            Explore Careers allows you to explore careers based on your skills
            and interests <span className='font-semibold'>using AI</span> in
            seconds, completely for free.
          </p>
          <div className='flex items-center justify-center gap-x-3 font-medium text-sm'>
            <NavLink
              href='/careers'
              className='text-white bg-gray-800 hover:bg-gray-600 active:bg-gray-900 '
            >
              Explore alternative careers
            </NavLink>
            <NavLink
              target='_blank'
              href='https://github.com/Nutlope/explorecareers'
              className='text-gray-700 border hover:bg-gray-50'
              scroll={false}
            >
              Learn more
            </NavLink>
          </div>
        </div>
        <div className=''>
          <Image
            src='/careers-screenshot.png'
            className='rounded-2xl border'
            alt='hero'
            width={700}
            height={700}
          />
        </div>
      </div>
    </section>
  );
}



================================================
FILE: components/NavLink.tsx
================================================
import Link from 'next/link';

const NavLink = ({ children, href, ...props }: any) => (
  <Link
    href={href}
    {...props}
    className={`py-2.5 px-4 text-center rounded-lg duration-150 ${
      props?.className || ''
    }`}
  >
    {children}
  </Link>
);

export default NavLink;



================================================
FILE: components/ui/button.tsx
================================================
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }



================================================
FILE: components/ui/dialog.tsx
================================================
"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}



================================================
FILE: components/ui/input.tsx
================================================
import * as React from 'react';

import { cn } from '@/lib/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground  disabled:cursor-not-allowed disabled:opacity-50',
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = 'Input';

export { Input };



================================================
FILE: components/ui/label.tsx
================================================
"use client"

import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }



================================================
FILE: components/ui/loading-dots.module.css
================================================
.loading {
  display: inline-flex;
  align-items: center;
}

.loading .spacer {
  margin-right: 2px;
}

.loading span {
  animation-name: blink;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-fill-mode: both;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
}

.loading span:nth-of-type(2) {
  animation-delay: 0.2s;
}

.loading span:nth-of-type(3) {
  animation-delay: 0.4s;
}

.loading2 {
  display: inline-flex;
  align-items: center;
}

.loading2 .spacer {
  margin-right: 2px;
}

.loading2 span {
  animation-name: blink;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-fill-mode: both;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
}

.loading2 span:nth-of-type(2) {
  animation-delay: 0.2s;
}

.loading2 span:nth-of-type(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}



================================================
FILE: components/ui/loadingdots.tsx
================================================
import styles from './loading-dots.module.css';

const LoadingDots = ({
  color = '#000',
  style = 'small',
}: {
  color?: string;
  style: string;
}) => {
  return (
    <span className={style == 'small' ? styles.loading2 : styles.loading}>
      <span style={{ backgroundColor: color }} />
      <span style={{ backgroundColor: color }} />
      <span style={{ backgroundColor: color }} />
    </span>
  );
};

export default LoadingDots;

LoadingDots.defaultProps = {
  style: 'small',
};



================================================
FILE: components/ui/textarea.tsx
================================================
import * as React from 'react';

import { cn } from '@/lib/utils';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Textarea.displayName = 'Textarea';

export { Textarea };



================================================
FILE: lib/types.ts
================================================
export interface finalCareerInfo {
  jobTitle: string;
  jobDescription: string;
  timeline: string;
  salary: string;
  difficulty: string;
  workRequired: string;
  aboutTheRole: string;
  whyItsagoodfit: string[];
  roadmap: { [key: string]: string }[];
}



================================================
FILE: lib/utils.ts
================================================
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { customAlphabet } from 'nanoid';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// TODO: Add sharability later on
export const nanoid = customAlphabet(
  '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
  7
);

export function normalizeText(input: string): string {
  // Replace multiple spaces with a single space
  let normalized = input.replace(/\s+/g, ' ');
  // Replace multiple line breaks with a single line break
  normalized = normalized.replace(/\n+/g, '\n');
  // Trim leading/trailing whitespace
  return normalized.trim();
}

export const uploaderOptions = {
  apiKey: !!process.env.NEXT_PUBLIC_BYTESCALE_API_KEY
    ? process.env.NEXT_PUBLIC_BYTESCALE_API_KEY
    : 'free',
  maxFileCount: 1,
  mimeTypes: ['application/pdf'],
  editor: { images: { crop: false } },
  styles: {
    colors: {
      primary: '#000',
    },
  },
  tags: ['career_explorer'],
  locale: {
    orDragDropFile: 'Your resume is automatically deleted after 24h',
    uploadFileBtn: 'Upload your Resume',
  },
};


