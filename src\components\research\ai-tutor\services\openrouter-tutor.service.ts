/**
 * OpenRouter Tutor Service
 * Handles AI tutoring interactions using OpenRouter API
 */

import {
  TutorResponse,
  StreamingTutorResponse,
  EducationLevel,
  GradeLevel
} from '../types';
import { EDUCATION_LEVEL_PROMPTS } from '../constants';

interface TutorGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  educationLevel: EducationLevel;
  includeExamples?: boolean;
  learningStyle?: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  conversationHistory?: Array<{ role: string; content: string }>;
  gradeLevel?: GradeLevel;
}

export class OpenRouterTutorService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';
  private isConfigured: boolean = false;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    this.isConfigured = Boolean(this.apiKey && this.apiKey.length > 20 && !this.apiKey.includes('your_'));
    
    if (this.isConfigured) {
      console.log('OpenRouter Tutor Service initialized successfully');
    } else {
      console.warn('OpenRouter API key not found or invalid');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Get available OpenRouter models for tutoring
   */
  getAvailableModels(): string[] {
    return [
      'anthropic/claude-3.5-sonnet',
      'anthropic/claude-3-haiku',
      'openai/gpt-4o',
      'openai/gpt-4o-mini',
      'google/gemini-2.0-flash-001',
      'google/gemini-pro',
      'meta-llama/llama-3.1-405b-instruct',
      'meta-llama/llama-3.1-70b-instruct',
      'deepseek/deepseek-chat',
      'qwen/qwen-2.5-72b-instruct'
    ];
  }

  /**
   * Get the default OpenRouter model
   */
  getDefaultModel(): string {
    return 'anthropic/claude-3.5-sonnet';
  }

  /**
   * Generate a tutoring response using OpenRouter
   */
  async generateTutoringResponse(
    topic: string,
    question: string,
    options: TutorGenerationOptions
  ): Promise<TutorResponse> {
    if (!this.isConfigured) {
      throw new Error('OpenRouter service not configured. Please check your API key.');
    }

    const {
      model = this.getDefaultModel(),
      temperature = 0.7,
      maxTokens = 2048,
      educationLevel,
      includeExamples = true,
      learningStyle = 'visual',
      conversationHistory = []
    } = options;

    try {
      const systemPrompt = this.buildSystemPrompt(topic, educationLevel, learningStyle, includeExamples);
      const messages = this.buildMessages(systemPrompt, question, conversationHistory);

      console.log('Making OpenRouter tutoring request:', {
        model,
        topic,
        educationLevel,
        messageCount: messages.length
      });

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Research Platform - Tutoring'
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens: maxTokens,
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      if (!content) {
        throw new Error('No content received from OpenRouter service');
      }

      return {
        content,
        sources: [], // Sources will be added by the sources service
        confidence: this.calculateConfidence(data),
        tokens: data.usage?.total_tokens || 0,
        model,
        suggestions: this.extractSuggestions(content)
      };

    } catch (error) {
      console.error('OpenRouter tutoring request failed:', error);
      throw new Error(`Failed to generate tutoring response: ${error.message}`);
    }
  }

  /**
   * Generate streaming tutoring response using OpenRouter
   */
  async *generateStreamingResponse(
    topic: string,
    question: string,
    options: TutorGenerationOptions
  ): AsyncGenerator<StreamingTutorResponse, void, unknown> {
    if (!this.isConfigured) {
      throw new Error('OpenRouter service not configured. Please check your API key.');
    }

    const {
      model = this.getDefaultModel(),
      temperature = 0.7,
      maxTokens = 2048,
      educationLevel,
      includeExamples = true,
      learningStyle = 'visual',
      conversationHistory = []
    } = options;

    try {
      const systemPrompt = this.buildSystemPrompt(topic, educationLevel, learningStyle, includeExamples);
      const messages = this.buildMessages(systemPrompt, question, conversationHistory);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Research Platform - Tutoring'
        },
        body: JSON.stringify({
          model,
          messages,
          temperature,
          max_tokens: maxTokens,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`OpenRouter streaming request failed: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                yield {
                  chunk: '',
                  isComplete: true,
                  metadata: { tokens: 0, model }
                };
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const chunk = parsed.choices?.[0]?.delta?.content || '';
                if (chunk) {
                  yield {
                    chunk,
                    isComplete: false
                  };
                }
              } catch (e) {
                // Skip invalid JSON
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.error('OpenRouter streaming request failed:', error);
      throw new Error(`Failed to generate streaming response: ${error.message}`);
    }
  }

  /**
   * Build system prompt based on education level and learning preferences
   */
  private buildSystemPrompt(
    topic: string,
    educationLevel: EducationLevel,
    learningStyle: string,
    includeExamples: boolean
  ): string {
    const basePrompt = EDUCATION_LEVEL_PROMPTS[educationLevel];
    
    let prompt = `${basePrompt}

You are helping a student learn about: ${topic}

Teaching Guidelines:
- Adapt your explanations to the ${educationLevel.replace('-', ' ')} level
- Use a ${learningStyle} learning approach when possible
- ${includeExamples ? 'Include relevant examples and analogies' : 'Focus on clear explanations without extensive examples'}
- Be encouraging and supportive
- Ask follow-up questions to check understanding
- Break complex concepts into digestible parts
- Use appropriate vocabulary for the education level

Remember to:
1. Start with what the student already knows
2. Build understanding step by step
3. Use real-world connections when helpful
4. Encourage questions and curiosity
5. Provide clear, accurate information
6. Be patient and supportive`;

    if (learningStyle === 'visual') {
      prompt += '\n- Describe visual elements, diagrams, or imagery when helpful';
    } else if (learningStyle === 'auditory') {
      prompt += '\n- Use verbal explanations and encourage discussion';
    } else if (learningStyle === 'kinesthetic') {
      prompt += '\n- Suggest hands-on activities or practical applications';
    } else if (learningStyle === 'reading') {
      prompt += '\n- Provide structured, text-based explanations';
    }

    return prompt;
  }

  /**
   * Build message array for API request
   */
  private buildMessages(
    systemPrompt: string,
    question: string,
    conversationHistory: Array<{ role: string; content: string }>
  ): Array<{ role: string; content: string }> {
    const messages = [
      { role: 'system', content: systemPrompt }
    ];

    // Add conversation history (limit to last 10 messages to stay within token limits)
    const recentHistory = conversationHistory.slice(-10);
    messages.push(...recentHistory);

    // Add current question
    messages.push({ role: 'user', content: question });

    return messages;
  }

  /**
   * Calculate confidence score from API response
   */
  private calculateConfidence(data: any): number {
    // Simple confidence calculation based on response completeness
    const content = data.choices?.[0]?.message?.content || '';
    const finishReason = data.choices?.[0]?.finish_reason;
    
    if (finishReason === 'stop' && content.length > 50) {
      return 0.9;
    } else if (finishReason === 'length') {
      return 0.7;
    } else if (content.length > 20) {
      return 0.6;
    }
    
    return 0.3;
  }

  /**
   * Extract follow-up suggestions from response content
   */
  private extractSuggestions(content: string): string[] {
    const suggestions: string[] = [];
    
    // Look for question patterns in the response
    const questionPatterns = [
      /Would you like to learn about ([^?]+)\?/gi,
      /Do you want to explore ([^?]+)\?/gi,
      /Should we discuss ([^?]+)\?/gi,
      /What about ([^?]+)\?/gi
    ];

    for (const pattern of questionPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && suggestions.length < 3) {
          suggestions.push(match[1].trim());
        }
      }
    }

    // Add some generic follow-up suggestions if none found
    if (suggestions.length === 0) {
      suggestions.push(
        "Can you explain this in more detail?",
        "What are some real-world examples?",
        "How does this relate to other concepts?"
      );
    }

    return suggestions.slice(0, 3);
  }

  /**
   * Get API provider information
   */
  getApiProvider(): string {
    return this.isConfigured ? 'OpenRouter' : 'none';
  }
}

export const openRouterTutorService = new OpenRouterTutorService();
