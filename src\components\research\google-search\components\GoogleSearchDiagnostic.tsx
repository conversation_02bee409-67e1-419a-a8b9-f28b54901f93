/**
 * Google Search Diagnostic Component
 * UI component to run and display Google Search diagnostic results
 */

import React, { useState } from 'react';
import { 
  Search, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Play,
  RefreshCw,
  ExternalLink,
  Eye,
  EyeOff
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

import { runGoogleSearchDiagnostic, DiagnosticResult } from '../diagnostics/google-search-diagnostic';

export function GoogleSearchDiagnostic() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());

  const runDiagnostic = async () => {
    setIsRunning(true);
    setResults([]);
    
    try {
      const diagnosticResults = await runGoogleSearchDiagnostic();
      setResults(diagnosticResults);
    } catch (error) {
      console.error('Diagnostic failed:', error);
      setResults([{
        test: 'Diagnostic Error',
        status: 'fail',
        message: `Diagnostic failed: ${error.message}`,
        details: { error: error.message }
      }]);
    } finally {
      setIsRunning(false);
    }
  };

  const toggleExpanded = (testName: string) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(testName)) {
      newExpanded.delete(testName);
    } else {
      newExpanded.add(testName);
    }
    setExpandedResults(newExpanded);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'fail': return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      default: return <AlertTriangle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const passedTests = results.filter(r => r.status === 'pass').length;
  const failedTests = results.filter(r => r.status === 'fail').length;
  const totalTests = results.length;

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Google Search Diagnostic</h1>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Comprehensive diagnostic to verify if Google Search is actually being used vs AI training data.
          This will test for real URLs, current information, and authentic search results.
        </p>
      </div>

      {/* Controls */}
      <div className="flex justify-center">
        <Button 
          onClick={runDiagnostic}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700"
          size="lg"
        >
          {isRunning ? (
            <>
              <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
              Running Diagnostic...
            </>
          ) : (
            <>
              <Play className="h-5 w-5 mr-2" />
              Run Google Search Diagnostic
            </>
          )}
        </Button>
      </div>

      {/* Results Summary */}
      {results.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-green-600">
                {passedTests}
              </div>
              <div className="text-sm text-gray-600">Tests Passed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-red-600">
                {failedTests}
              </div>
              <div className="text-sm text-gray-600">Tests Failed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Overall Status */}
      {results.length > 0 && (
        <Alert className={failedTests > 0 ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {failedTests > 0 ? (
            <XCircle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={failedTests > 0 ? 'text-red-800' : 'text-green-800'}>
            {failedTests > 0 ? (
              <div>
                <strong>Google Search Issues Detected!</strong>
                <br />
                {failedTests} test(s) failed. Google Search may not be working properly or may be using AI training data instead of real search results.
              </div>
            ) : (
              <div>
                <strong>Google Search Working Correctly!</strong>
                <br />
                All tests passed. Google Search is functioning properly and returning real search results.
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Diagnostic Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {results.map((result, index) => (
                <Collapsible key={index}>
                  <div className="border rounded-lg p-4">
                    <CollapsibleTrigger 
                      className="w-full"
                      onClick={() => toggleExpanded(result.test)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(result.status)}
                          <div className="text-left">
                            <h4 className="font-medium">{result.test}</h4>
                            <p className="text-sm text-gray-600">{result.message}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getStatusColor(result.status)}>
                            {result.status.toUpperCase()}
                          </Badge>
                          {expandedResults.has(result.test) ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </div>
                      </div>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      {expandedResults.has(result.test) && (
                        <div className="mt-4 space-y-3 border-t pt-4">
                          {/* Evidence */}
                          {result.evidence && result.evidence.length > 0 && (
                            <div>
                              <h5 className="font-medium text-sm text-gray-700 mb-2">Evidence:</h5>
                              <div className="space-y-1">
                                {result.evidence.map((evidence, evidenceIndex) => (
                                  <div key={evidenceIndex} className="text-xs font-mono bg-gray-50 p-2 rounded">
                                    {evidence}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Details */}
                          {result.details && (
                            <div>
                              <h5 className="font-medium text-sm text-gray-700 mb-2">Details:</h5>
                              <ScrollArea className="h-32">
                                <pre className="text-xs bg-gray-50 p-3 rounded overflow-auto">
                                  {JSON.stringify(result.details, null, 2)}
                                </pre>
                              </ScrollArea>
                            </div>
                          )}

                          {/* URLs if available */}
                          {result.details?.urlAnalysis?.allUrls && result.details.urlAnalysis.allUrls.length > 0 && (
                            <div>
                              <h5 className="font-medium text-sm text-gray-700 mb-2">
                                Found URLs ({result.details.urlAnalysis.allUrls.length}):
                              </h5>
                              <div className="space-y-1 max-h-32 overflow-y-auto">
                                {result.details.urlAnalysis.allUrls.map((url: string, urlIndex: number) => (
                                  <div key={urlIndex} className="flex items-center space-x-2 text-xs">
                                    <span className="font-mono bg-gray-50 p-1 rounded flex-1 truncate">
                                      {url}
                                    </span>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => window.open(url, '_blank')}
                                      className="h-6 w-6 p-0"
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Interpret Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-green-700 mb-2">✅ Google Search Working</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Real URLs from academic sources</li>
                <li>• Current year information</li>
                <li>• Recent dates and events</li>
                <li>• Specific search result formatting</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-red-700 mb-2">❌ Using Training Data</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Fake URLs (example.com, etc.)</li>
                <li>• Generic article titles</li>
                <li>• No current information</li>
                <li>• Vague or outdated references</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-yellow-700 mb-2">⚠️ Mixed Results</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Some real, some fake URLs</li>
                <li>• Partial current information</li>
                <li>• Inconsistent search behavior</li>
                <li>• May need configuration fixes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
