import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  AlertTriangle, 
  XCircle, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw, 
  Settings, 
  Eye,
  Zap,
  Brain
} from "lucide-react";
import { ErrorDetectionResult, RecoveryAction } from '../services/error-detection.service';

interface ErrorRecoveryPanelProps {
  errors: ErrorDetectionResult[];
  onRecoveryAction: (actionType: RecoveryAction['type'], index?: number) => Promise<void>;
  isProcessing: boolean;
}

export const ErrorRecoveryPanel: React.FC<ErrorRecoveryPanelProps> = ({
  errors,
  onRecoveryAction,
  isProcessing
}) => {
  const [expandedError, setExpandedError] = useState<number | null>(null);
  const [processingAction, setProcessingAction] = useState<string | null>(null);

  const getSeverityColor = (severity: ErrorDetectionResult['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getSeverityIcon = (severity: ErrorDetectionResult['severity']) => {
    switch (severity) {
      case 'critical': return <XCircle className="h-5 w-5" />;
      case 'high': return <AlertTriangle className="h-5 w-5" />;
      case 'medium': return <AlertCircle className="h-5 w-5" />;
      default: return <CheckCircle className="h-5 w-5" />;
    }
  };

  const getErrorTypeLabel = (errorType: ErrorDetectionResult['errorType']) => {
    switch (errorType) {
      case 'fake_outline': return 'Fake/Generic Outline';
      case 'generic_content': return 'Generic Content';
      case 'context_mismatch': return 'Context Mismatch';
      case 'quality_low': return 'Low Quality';
      default: return 'No Issues';
    }
  };

  const getActionIcon = (actionType: RecoveryAction['type']) => {
    switch (actionType) {
      case 'regenerate': return <RefreshCw className="h-4 w-4" />;
      case 'enhance_prompt': return <Brain className="h-4 w-4" />;
      case 'change_model': return <Zap className="h-4 w-4" />;
      case 'add_context': return <Settings className="h-4 w-4" />;
      case 'manual_review': return <Eye className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const handleRecoveryAction = async (action: RecoveryAction, errorIndex?: number) => {
    setProcessingAction(`${action.type}-${errorIndex}`);
    try {
      await onRecoveryAction(action.type, errorIndex);
    } finally {
      setProcessingAction(null);
    }
  };

  const criticalErrors = errors.filter(e => e.severity === 'critical');
  const highErrors = errors.filter(e => e.severity === 'high');
  const mediumErrors = errors.filter(e => e.severity === 'medium');

  if (errors.length === 0 || errors.every(e => !e.hasErrors)) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-green-800">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">No Quality Issues Detected</span>
          </div>
          <p className="text-sm text-green-700 mt-1">
            All generated content meets quality standards.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary */}
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-800">
            <AlertTriangle className="h-5 w-5" />
            Quality Issues Detected
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-red-100 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{criticalErrors.length}</div>
              <div className="text-sm text-red-700">Critical</div>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{highErrors.length}</div>
              <div className="text-sm text-orange-700">High</div>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{mediumErrors.length}</div>
              <div className="text-sm text-yellow-700">Medium</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Details */}
      {errors.map((error, index) => {
        if (!error.hasErrors) return null;

        const isExpanded = expandedError === index;
        
        return (
          <Card key={index} className={`border-2 ${getSeverityColor(error.severity)}`}>
            <CardHeader 
              className="cursor-pointer"
              onClick={() => setExpandedError(isExpanded ? null : index)}
            >
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getSeverityIcon(error.severity)}
                  <span>Issue #{index + 1}: {getErrorTypeLabel(error.errorType)}</span>
                  <Badge variant="outline" className="ml-2">
                    {error.confidence}% confidence
                  </Badge>
                </div>
                <Badge className={getSeverityColor(error.severity)}>
                  {error.severity.toUpperCase()}
                </Badge>
              </CardTitle>
            </CardHeader>
            
            {isExpanded && (
              <CardContent className="space-y-4">
                {/* Issues */}
                <div>
                  <h4 className="font-medium mb-2">Detected Issues:</h4>
                  <ul className="space-y-1">
                    {error.issues.map((issue, issueIndex) => (
                      <li key={issueIndex} className="flex items-start gap-2 text-sm">
                        <span className="text-red-500 mt-0.5">•</span>
                        {issue}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Suggestions */}
                {error.suggestions.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Suggestions:</h4>
                    <ul className="space-y-1">
                      {error.suggestions.map((suggestion, suggestionIndex) => (
                        <li key={suggestionIndex} className="flex items-start gap-2 text-sm">
                          <span className="text-blue-500 mt-0.5">•</span>
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Recovery Actions */}
                {error.recoveryActions.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Recovery Actions:</h4>
                    <div className="space-y-2">
                      {error.recoveryActions
                        .sort((a, b) => a.priority - b.priority)
                        .map((action, actionIndex) => (
                          <div key={actionIndex} className="flex items-center justify-between p-3 bg-white rounded border">
                            <div className="flex items-center gap-2">
                              {getActionIcon(action.type)}
                              <div>
                                <div className="font-medium text-sm">{action.description}</div>
                                <div className="text-xs text-gray-500">
                                  Priority: {action.priority} • {action.automated ? 'Automated' : 'Manual'}
                                </div>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant={action.priority === 1 ? "default" : "outline"}
                              onClick={() => handleRecoveryAction(action, index)}
                              disabled={isProcessing || processingAction === `${action.type}-${index}`}
                            >
                              {processingAction === `${action.type}-${index}` ? (
                                <>
                                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                'Execute'
                              )}
                            </Button>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        );
      })}

      {/* Quick Actions */}
      {criticalErrors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <Alert>
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>
                    <strong>Critical issues detected.</strong> These require immediate attention to ensure quality output.
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onRecoveryAction('regenerate')}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Regenerating...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Regenerate All
                      </>
                    )}
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
