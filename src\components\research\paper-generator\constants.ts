import {
  <PERSON><PERSON>pen,
  FlaskConical,
  BarChart3,
  Lightbulb,
  PenTool,
  Target,
  FileText,
  Search
} from "lucide-react";
import { SectionType, AIModelOption } from "./types";

// Research paper section types
export const SECTION_TYPES: SectionType[] = [
  { 
    id: 'methodology', 
    name: 'Methodology', 
    icon: FlaskConical, 
    color: 'bg-blue-500', 
    description: 'Research methods and procedures',
    order: 2,
    required: false
  },
  { 
    id: 'results', 
    name: 'Results', 
    icon: BarChart3, 
    color: 'bg-green-500', 
    description: 'Findings and data analysis',
    order: 3,
    required: false
  },
  { 
    id: 'introduction', 
    name: 'Introduction', 
    icon: BookOpen, 
    color: 'bg-purple-500', 
    description: 'Background, problem statement, and objectives',
    order: 1,
    required: false
  },

  { 
    id: 'discussion', 
    name: 'Discussion', 
    icon: Lightbulb, 
    color: 'bg-yellow-500', 
    description: 'Interpretation of results and implications',
    order: 4,
    required: false
  },
  { 
    id: 'conclusion', 
    name: 'Conclusion', 
    icon: PenTool, 
    color: 'bg-red-500', 
    description: 'Summary, limitations, and future work',
    order: 5,
    required: false
  },
  { 
    id: 'abstract', 
    name: 'Abstract', 
    icon: Target, 
    color: 'bg-indigo-500', 
    description: 'Concise summary of the entire research',
    order: 6,
    required: false
  },
  { 
    id: 'keywords', 
    name: 'Keywords', 
    icon: Search, 
    color: 'bg-teal-500', 
    description: 'Key terms representing the paper content',
    order: 0,
    required: false
  },
  { 
    id: 'references', 
    name: 'References', 
    icon: FileText, 
    color: 'bg-gray-500', 
    description: 'Citations and references used throughout the paper',
    order: 7,
    required: false
  }
];

// AI generation prompts for each section
export const SECTION_PROMPTS = {
  metadata: (title: string, field: string, keywords: string[]) => 
    `Paper Title: ${title}
     Research Field: ${field}
     Keywords: ${keywords.join(', ')}`,
  
  introduction: (title: string, field: string, keywords: string[]) =>
    `Create the main body (without any section heading) for the Introduction of a research paper titled "${title}" in ${field}. Keywords: ${keywords.join(', ')}.

     Instructions:
     - Do NOT include a heading like 'Introduction' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Establish context and background
     - Present a clear research problem and motivations
     - Define aims, objectives, and research questions
     - Explain significance and contributions
     - Provide a brief overview of paper structure
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - Each in-text citation must correspond to a real academic paper with genuine authors and publication years
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this paper, we present" or "Here is an introduction to"`,  
  methodology: (title: string, field: string, userContent: string) =>
    `Create the main body (without any section heading) for the Methodology section of a research paper titled "${title}" in ${field}.

     Context (if provided):
     ${userContent}

     Instructions:
     - Do NOT include a heading like 'Methodology' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Detail research design and approach
     - Describe data collection methods and procedures
     - Explain participant selection and sampling strategies if applicable
     - Detail analytical techniques
     - Address ethical considerations
     - Acknowledge limitations
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - All citations should reference real methodological papers, techniques, or frameworks
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this methodology section" or "Here are the methods used"`,
  
  results: (title: string, methodologyContent: string, userContent: string) => 
    `Create the main body (without any section heading) for the Results section of a research paper titled "${title}".
     
     Methodology context:
     ${methodologyContent}
     
     Results context (if provided):
     ${userContent}
     
     Instructions:
     - Do NOT include a heading like 'Results' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Present findings objectively without interpretation
     - Organize results logically based on the methodology
     - Include appropriate statistical analyses
     - Reference tables and figures in academic style
     - Use in-text citations (Author, Year) format if comparing with other studies
     - Use a variety of sources and avoid repeating the same citation
     - DO NOT include a References section at the end; all references will be compiled separately
     - Highlight key findings addressing research questions
     - Avoid phrases like "In this section" or "Here are the results"`,
  
  discussion: (title: string, resultsContent: string, methodologyContent: string) =>
    `Create the main body (without any section heading) for the Discussion section of a research paper titled "${title}".

     Methodology context:
     ${methodologyContent}

     Results context:
     ${resultsContent}

     Instructions:
     - Do NOT include a heading like 'Discussion' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Interpret results in context of research questions
     - Compare findings with previous research
     - Explain unexpected results
     - Discuss theoretical and practical implications
     - Acknowledge limitations
     - IMPORTANT: Include at least 6-8 in-text citations in proper academic format (Author, Year) - NOT in capital letters
     - Each major claim or statement should be supported by a citation
     - Use a variety of sources and avoid repeating the same citation
     - Use standard academic citation format: (Smith, 2023) or (Smith et al., 2023) - never (SMITH, 2023)
     - Use citations from real academic papers with accurate author names and publication years
     - THEN, after your main content, include a "References" section with the COMPLETE reference entries for EACH citation used
     - Each reference must be in full APA 7th edition format with realistic journal names, titles, volume, pages, DOI, etc.
     - Use real-sounding academic journal names and realistic publication details
     - Ensure proper capitalization: only first letter of names and proper nouns should be capitalized
     - Avoid phrases like "In this discussion" or "Here, we discuss"`,
  
  conclusion: (title: string, resultsContent: string, discussionContent: string) =>
    `Create the main body (without any section heading) for the Conclusion section of a research paper titled "${title}".
     
     Results context:
     ${resultsContent}
     
     Discussion context:
     ${discussionContent}
     
     Instructions:
     - Do NOT include a heading like 'Conclusion' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - Summarize key findings concisely
     - Revisit research objectives
     - Discuss broader implications for the field
     - Suggest directions for future research
     - Use minimal in-text citations, focusing on synthesizing the work
     - DO NOT include a References section at the end; all references will be compiled separately
     - Avoid phrases like "In conclusion" or "To summarize"`,
  
  abstract: (title: string, introContent: string, methodologyContent: string, resultsContent: string, conclusionContent: string) =>
    `Create the main body (without any section heading) for the Abstract of a research paper titled "${title}".
     
     Context from paper sections:
     - Introduction: ${introContent}
     - Methodology: ${methodologyContent}
     - Results: ${resultsContent}
     - Conclusion: ${conclusionContent}
     
     Instructions:
     - Do NOT include a heading like 'Abstract' at the start; only provide the main content.
     - Use concise, formal academic language without unnecessary phrases
     - 200-250 words maximum
     - Present background context and research problem
     - Briefly describe methodology
     - Highlight key findings
     - State main conclusions and implications
     - Do not include citations in the abstract
     - Write as a single paragraph without headings or bullet points
     - Avoid phrases like "This abstract presents" or "In this study"`,
     
  references: (introContent: string, methodContent: string, resultsContent: string, discussionContent: string, conclusionContent: string) =>
    `Create a complete and properly formatted references section for an academic research paper.

    Analyze the following content from the paper to extract all citations that need references:

    Introduction:
    ${introContent}

    Methodology:
    ${methodContent}

    Results:
    ${resultsContent}

    Discussion:
    ${discussionContent}

    Conclusion:
    ${conclusionContent}

    CRITICAL INSTRUCTIONS FOR APA 7TH EDITION FORMAT:
    - Extract ALL in-text citations in the format (Author, Year) or (Author et al., Year) from the provided sections.
    - For each citation found, create a complete, realistic academic reference in strict APA 7th edition format.
    - EXACT APA FORMAT: Author, A. A. (Year). Title of article. Title of Journal, Volume(Issue), pages. https://doi.org/xx.xxxx/xxxx
    - Use proper capitalization: Only first word of title, first word after colon, and proper nouns capitalized
    - Journal names should be in italics (use realistic journal names appropriate to the research field)
    - Examples of good journal names: "Remote Sensing of Environment", "IEEE Transactions on Geoscience and Remote Sensing", "Journal of Applied Geophysics", "International Journal of Remote Sensing"
    - Create realistic article titles that match the research topic and citation context
    - Use realistic DOIs in format: https://doi.org/10.1016/j.rse.2023.xxxxx
    - Alphabetize references by first author's last name
    - Do not number the references
    - Use hanging indent format (first line flush left, subsequent lines indented)
    - Ensure each citation in the text has a corresponding reference entry
    - Use different journals and publishers for variety
    - Make references look authentic and professionally formatted
    - DO NOT include any title, heading, explanatory text, or instructions in your output
    - Your response should start directly with the first reference entry
    - If no citations are found, return: "No citations found in the paper content."`
};

// Research-focused text placeholders for content sections
export const TEXT_PLACEHOLDERS = {
  Methodology:
    "Describe your experimental design, data collection procedures, analytical methods, sample preparation, equipment specifications, statistical approaches, and quality control measures. Include specific protocols, parameters, and technical details that would allow replication of your study...",
  Results:
    "Present your key findings with specific data values, statistical measures, observed patterns, and quantitative results. Include measurements, percentages, correlations, significance levels, and objective observations from your analysis. Reference any tables, figures, or statistical tests performed...",
  Introduction:
    "Provide comprehensive background literature, clearly define the research problem, state specific objectives and hypotheses, explain the significance of your study, and establish the theoretical framework. Include relevant citations and context that justify your research approach...",
  Discussion:
    "Interpret your results in context of existing literature, explain mechanisms or theoretical implications, compare findings with previous studies, address limitations and potential sources of error, discuss broader applications, and suggest future research directions...",
  Conclusion:
    "Synthesize your main findings, restate how objectives were met, discuss broader implications for the field, acknowledge key limitations, and propose specific directions for future research. Emphasize the contribution and significance of your work...",
  Abstract:
    "Provide a structured summary including background context, research objectives, methodology overview, key findings with specific results, and main conclusions. Keep concise but comprehensive, highlighting the significance and novelty of your research...",
  Keywords:
    "Enter 4-6 specific terms that accurately represent your research domain, methods, key concepts, and subject matter. Use standard academic terminology that researchers in your field would use to find your work...",
  References:
    "All in-text citations from the paper will be automatically extracted and formatted into a properly formatted reference list here..."
};

// Research-focused figure placeholders for content sections
export const FIGURE_PLACEHOLDERS = {
  Methodology:
    "Provide detailed description of the experimental setup, equipment configuration, workflow diagram, or procedural steps shown. Include technical specifications, parameters, materials used, and how this relates to your overall research methodology. Explain any protocols or procedures illustrated...",
  Results:
    "Analyze the data patterns, trends, statistical relationships, or quantitative findings displayed. Report specific values, ranges, significance levels, and key observations. Describe what the results demonstrate about your research questions and how they support your conclusions...",
  Introduction:
    "Explain how this figure provides essential context, theoretical framework, or background information for your research. Describe the conceptual model, geographical context, or foundational concepts illustrated and their relevance to your study objectives...",
  Discussion:
    "Interpret the broader implications of what is shown, connect to existing literature, explain theoretical significance, and discuss how this figure supports your conclusions. Address any limitations or alternative interpretations of the visual data presented...",
  Conclusion:
    "Summarize how this figure encapsulates key findings, future research directions, or practical applications emerging from your study. Connect to the broader significance and impact of your research contributions..."
};

// Enhanced section guidance with motivation and specific instructions
export const EMPTY_SECTION_PROMPTS = {
  Methodology:
    'Share your research approach! Describe your experimental design, data collection methods, and analytical procedures. Upload figures showing your setup or workflow.',
  Results:
    'Show us what you discovered! Upload your data visualizations, charts, and graphs. Describe your key findings and statistical analyses.',
  Introduction:
    'Set the stage for your research! Explain the background, problem statement, and objectives. Include any figures that provide context or show the research gap.',
  Discussion:
    'Make sense of your findings! Interpret your results, compare with existing literature, and discuss implications. Use figures to highlight key insights.',
  Conclusion:
    'Wrap it up powerfully! Summarize your main contributions, acknowledge limitations, and suggest future research directions.',
  Abstract:
    'Create a compelling summary! Provide a concise overview covering your research problem, methods, key findings, and significance.',
  Keywords:
    'Help others find your work! Add 4-6 key terms that best represent your research topics and methods.'
};

// Detailed section guidance for better user understanding
export const SECTION_GUIDANCE = {
  Methodology: {
    title: "Share Your Research Methods",
    description: "Help us understand how you conducted your research",
    tips: [
      "Describe your experimental design and approach",
      "Explain data collection procedures and tools used",
      "Include sample sizes, selection criteria, or parameters",
      "Upload figures showing experimental setup or workflow",
      "Mention any statistical methods or analytical techniques"
    ],
    examples: [
      "Survey methodology with participant demographics",
      "Laboratory procedures with equipment specifications",
      "Data analysis pipeline with software tools used"
    ]
  },
  Results: {
    title: "Showcase Your Findings",
    description: "Present your data and key discoveries",
    tips: [
      "Upload charts, graphs, and data visualizations",
      "Describe statistical significance and trends",
      "Present findings objectively without interpretation",
      "Include tables or figures with clear captions",
      "Highlight unexpected or particularly important results"
    ],
    examples: [
      "Bar charts showing group comparisons",
      "Line graphs displaying trends over time",
      "Scatter plots revealing correlations"
    ]
  },
  Introduction: {
    title: "Set Your Research Context",
    description: "Establish the foundation and motivation for your study",
    tips: [
      "Provide relevant background information",
      "Clearly state your research problem or question",
      "Explain why this research is important",
      "Include figures that illustrate the research context",
      "Define your objectives and hypotheses"
    ],
    examples: [
      "Conceptual diagrams showing the research framework",
      "Maps or images providing geographical context",
      "Flowcharts outlining the research process"
    ]
  },
  Discussion: {
    title: "Interpret Your Results",
    description: "Explain what your findings mean and their implications",
    tips: [
      "Connect results back to your research questions",
      "Compare findings with previous studies",
      "Discuss practical and theoretical implications",
      "Address any limitations or unexpected results",
      "Use figures to support your interpretations"
    ],
    examples: [
      "Comparison charts with literature values",
      "Conceptual models explaining mechanisms",
      "Trend analysis supporting conclusions"
    ]
  }
};

// Research-focused default prompts for text and figure content items
export const DEFAULT_TEXT_PROMPT = `Enter your research content here...

• Provide specific methodological details, quantitative results, or analytical insights
• Include precise measurements, statistical data, or technical specifications
• Use formal academic language and third-person perspective
• Reference relevant literature, theoretical frameworks, or established protocols
• Focus on factual, objective information that contributes to research understanding
• Ensure content is substantial and research-quality for academic publication`;

export const DEFAULT_FIGURE_PROMPT = `Provide comprehensive research analysis of this figure...

• Describe specific data patterns, quantitative relationships, or technical details shown
• Report exact values, statistical measures, or technical specifications visible
• Explain the research significance and how this relates to your study objectives
• Discuss methodology, experimental conditions, or analytical approaches illustrated
• Address any limitations, error sources, or interpretive considerations
• Connect findings to existing literature or theoretical expectations
• Use formal academic language appropriate for research publication

💡 Tip: Upload your figure with a descriptive title and click 'Generate Research Analysis' for professional academic content!`;

// Available AI models
export const AI_MODELS: AIModelOption[] = [
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash (Default)",
    provider: "Google",
    capabilities: ["text", "image analysis"],
    maxTokens: 8192
  },
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    provider: "Google",
    capabilities: ["text", "image analysis"],
    maxTokens: 8192
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "Google", 
    capabilities: ["text", "image analysis"],
    maxTokens: 8192
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    capabilities: ["text", "image analysis"],
    maxTokens: 4096
  },
  {
    id: "anthropic/claude-3.5-haiku",
    name: "Claude 3.5 Haiku",
    provider: "Anthropic",
    capabilities: ["text"],
    maxTokens: 4096
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    capabilities: ["text", "image analysis"],
    maxTokens: 4096
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    capabilities: ["text", "image analysis"],
    maxTokens: 4096
  },
  {
    id: "deepseek/deepseek-r1-0528",
    name: "DeepSeek R1",
    provider: "DeepSeek",
    capabilities: ["text"],
    maxTokens: 8192
  },
  {
    id: "deepseek/deepseek-chat-v3-0324",
    name: "DeepSeek Chat V3",
    provider: "DeepSeek",
    capabilities: ["text"],
    maxTokens: 8192
  },
  {
    id: "moonshotai/kimi-k2",
    name: "Kimi K2",
    provider: "Moonshot AI",
    capabilities: ["text"],
    maxTokens: 8192
  }
];
