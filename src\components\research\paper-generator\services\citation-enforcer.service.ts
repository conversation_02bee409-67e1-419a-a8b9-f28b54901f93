/**
 * Citation Enforcer Service
 * Ensures all found sources are properly used in the introduction
 */

import { paperAIService } from './paper-ai.service';

export interface CitationEnforcementResult {
  content: string;
  citationsAdded: number;
  sourcesUsed: number;
  totalCitations: number;
  enforcementLog: string[];
}

export interface SourceToEnforce {
  id: string;
  title: string;
  authors: string[];
  year: number;
  citation: string;
  relevantText: string;
}

export class CitationEnforcerService {
  /**
   * Enforce minimum citation usage in content
   */
  async enforceMinimumCitations(
    content: string,
    availableSources: SourceToEnforce[],
    usedSources: SourceToEnforce[],
    minCitations: number,
    model: string = "google/gemini-2.5-flash-preview-05-20"
  ): Promise<CitationEnforcementResult> {
    const enforcementLog: string[] = [];
    let enhancedContent = content;
    let citationsAdded = 0;
    
    // Find unused sources
    const unusedSources = availableSources.filter(source => 
      !usedSources.find(used => used.id === source.id)
    );
    
    const currentCitations = this.countCitations(content);
    const citationsNeeded = Math.max(0, minCitations - currentCitations);
    
    enforcementLog.push(`📊 Citation Analysis:`);
    enforcementLog.push(`- Current citations: ${currentCitations}`);
    enforcementLog.push(`- Target citations: ${minCitations}`);
    enforcementLog.push(`- Citations needed: ${citationsNeeded}`);
    enforcementLog.push(`- Unused sources: ${unusedSources.length}`);
    
    if (citationsNeeded <= 0) {
      enforcementLog.push(`✅ Citation target already met`);
      return {
        content: enhancedContent,
        citationsAdded: 0,
        sourcesUsed: usedSources.length,
        totalCitations: currentCitations,
        enforcementLog
      };
    }
    
    // Strategy 1: Add citations to existing sentences
    if (unusedSources.length > 0) {
      enforcementLog.push(`🔧 Strategy 1: Adding citations to existing sentences`);
      
      const citationResult = await this.addCitationsToExistingSentences(
        enhancedContent,
        unusedSources.slice(0, Math.min(citationsNeeded, 8)),
        model
      );
      
      if (citationResult.success) {
        enhancedContent = citationResult.content;
        citationsAdded += citationResult.citationsAdded;
        enforcementLog.push(`✅ Added ${citationResult.citationsAdded} citations to existing text`);
      }
    }
    
    // Strategy 2: Add new sentences with citations if still needed
    const remainingNeeded = minCitations - this.countCitations(enhancedContent);
    if (remainingNeeded > 0 && unusedSources.length > citationsAdded) {
      enforcementLog.push(`🔧 Strategy 2: Adding new sentences with citations`);
      
      const newSentenceResult = await this.addNewSentencesWithCitations(
        enhancedContent,
        unusedSources.slice(citationsAdded, citationsAdded + remainingNeeded),
        model
      );
      
      if (newSentenceResult.success) {
        enhancedContent = newSentenceResult.content;
        citationsAdded += newSentenceResult.citationsAdded;
        enforcementLog.push(`✅ Added ${newSentenceResult.citationsAdded} new sentences with citations`);
      }
    }
    
    const finalCitations = this.countCitations(enhancedContent);
    const finalUsedSources = this.extractUsedSources(enhancedContent, availableSources);
    
    enforcementLog.push(`📊 Final Results:`);
    enforcementLog.push(`- Final citations: ${finalCitations}`);
    enforcementLog.push(`- Sources used: ${finalUsedSources.length}/${availableSources.length}`);
    enforcementLog.push(`- Citations added: ${citationsAdded}`);
    
    return {
      content: enhancedContent,
      citationsAdded,
      sourcesUsed: finalUsedSources.length,
      totalCitations: finalCitations,
      enforcementLog
    };
  }

  /**
   * Add citations to existing sentences
   */
  private async addCitationsToExistingSentences(
    content: string,
    sources: SourceToEnforce[],
    model: string
  ): Promise<{ success: boolean; content: string; citationsAdded: number }> {
    const sourceInfo = sources.map((source, index) => 
      `${index + 1}. ${source.citation} - "${source.title}" - ${source.relevantText.substring(0, 150)}...`
    ).join('\n');

    const prompt = `You are an academic editor. Add citations to the existing text by integrating the provided sources.

CURRENT TEXT:
${content}

SOURCES TO INTEGRATE:
${sourceInfo}

TASK:
1. Find sentences in the current text that could be supported by these sources
2. Add the appropriate citations in format (Author, Year) 
3. Do NOT change the meaning or structure significantly
4. Do NOT add new sentences, only add citations to existing ones
5. Use ALL ${sources.length} sources provided
6. Ensure citations are relevant to the claims they support

Return the enhanced text with citations added:`;

    try {
      const response = await paperAIService.generatePaperSection(prompt, {
        model,
        maxTokens: Math.ceil(content.length * 1.2),
        temperature: 0.3
      });

      const originalCitations = this.countCitations(content);
      const newCitations = this.countCitations(response);
      const citationsAdded = Math.max(0, newCitations - originalCitations);

      return {
        success: citationsAdded > 0,
        content: response,
        citationsAdded
      };

    } catch (error) {
      console.error('Failed to add citations to existing sentences:', error);
      return { success: false, content, citationsAdded: 0 };
    }
  }

  /**
   * Add new sentences with citations
   */
  private async addNewSentencesWithCitations(
    content: string,
    sources: SourceToEnforce[],
    model: string
  ): Promise<{ success: boolean; content: string; citationsAdded: number }> {
    const sourceInfo = sources.map((source, index) => 
      `${index + 1}. ${source.citation} - "${source.title}" - ${source.relevantText.substring(0, 150)}...`
    ).join('\n');

    const prompt = `You are an academic writer. Enhance this introduction by adding 1-2 new sentences that incorporate the provided sources.

CURRENT INTRODUCTION:
${content}

SOURCES TO INCORPORATE:
${sourceInfo}

TASK:
1. Add ${sources.length} new sentences that naturally fit into the existing text
2. Each new sentence should cite one of the provided sources
3. Maintain the academic tone and flow
4. Insert sentences in logical positions within paragraphs
5. Use proper citation format: (Author, Year)
6. Ensure new content is relevant and adds value

Return the enhanced introduction with new sentences added:`;

    try {
      const response = await paperAIService.generatePaperSection(prompt, {
        model,
        maxTokens: Math.ceil(content.length * 1.3),
        temperature: 0.4
      });

      const originalCitations = this.countCitations(content);
      const newCitations = this.countCitations(response);
      const citationsAdded = Math.max(0, newCitations - originalCitations);

      return {
        success: citationsAdded > 0,
        content: response,
        citationsAdded
      };

    } catch (error) {
      console.error('Failed to add new sentences with citations:', error);
      return { success: false, content, citationsAdded: 0 };
    }
  }

  /**
   * Count citations in content
   */
  private countCitations(content: string): number {
    const citationPatterns = [
      /\([^)]+,\s*\d{4}\)/g,                    // (Author, Year)
      /\([^)]+\s+et\s+al\.,\s*\d{4}\)/g,       // (Author et al., Year)
      /\([^)]+\s+&\s+[^)]+,\s*\d{4}\)/g        // (Author & Author, Year)
    ];

    let totalCitations = 0;
    for (const pattern of citationPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        totalCitations += matches.length;
      }
    }

    return totalCitations;
  }

  /**
   * Extract used sources from content
   */
  private extractUsedSources(content: string, availableSources: SourceToEnforce[]): SourceToEnforce[] {
    const usedSources: SourceToEnforce[] = [];
    
    const citationPattern = /\(([^)]+),\s*(\d{4})\)/g;
    let match;
    
    while ((match = citationPattern.exec(content)) !== null) {
      const authorName = match[1];
      const year = parseInt(match[2]);
      
      const matchingSource = availableSources.find(source => {
        if (source.year !== year) return false;
        
        return source.authors.some(author => {
          const authorLastName = author.split(',')[0].toLowerCase().trim();
          const citationAuthor = authorName.toLowerCase().trim();
          
          return (
            authorLastName.includes(citationAuthor) ||
            citationAuthor.includes(authorLastName)
          );
        });
      });
      
      if (matchingSource && !usedSources.find(s => s.id === matchingSource.id)) {
        usedSources.push(matchingSource);
      }
    }
    
    return usedSources;
  }

  /**
   * Validate citation distribution across content
   */
  validateCitationDistribution(content: string, minCitationsPerParagraph: number = 2): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 100);
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    paragraphs.forEach((paragraph, index) => {
      const citations = this.countCitations(paragraph);
      if (citations < minCitationsPerParagraph) {
        issues.push(`Paragraph ${index + 1} has only ${citations} citations (minimum: ${minCitationsPerParagraph})`);
        suggestions.push(`Add more citations to paragraph ${index + 1} to support claims`);
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }
}

// Export singleton instance
export const citationEnforcerService = new CitationEnforcerService();
