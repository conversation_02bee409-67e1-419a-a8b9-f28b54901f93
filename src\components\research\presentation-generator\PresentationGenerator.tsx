import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  Presentation, 
  FileText, 
  Sparkles, 
  Settings, 
  Download,
  <PERSON>L<PERSON><PERSON>,
  ArrowRight,
  Play
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

import { usePresentationStore } from './stores/presentation.store';
import { PresentationMetadata, GenerationState } from './types';
import { PRESENTATION_THEMES } from './constants';

// Import test function for development
if (import.meta.env.DEV) {
  import('./test-gemini');
}

// Import components (will be created next)
import { PresentationMetadataForm } from './components/PresentationMetadataForm';
import { OutlineReview } from './components/OutlineReview';
import { ThemeSelector } from './components/ThemeSelector';
import { GenerationPanel } from './components/GenerationPanel';
import { SlideEditor } from './components/SlideEditor';
import { ExportDialog } from './components/ExportDialog';

export function PresentationGenerator() {
  // Store state
  const {
    currentPresentation,
    currentStep,
    generationState,
    selectedSlideId,
    isExporting,
    outline,
    isOutlineApproved,
    selectedTheme,
    selectedModel,
    selectedImageModel,
    selectedStyle,

    // Actions
    createPresentation,
    setCurrentStep,
    startOutlineGeneration,
    approveOutline,
    startSlideGeneration,
    setTheme,
    setSelectedModel,
    setSelectedImageModel,
    setSelectedStyle,
    resetStore
  } = usePresentationStore();

  // Local state
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Reset store on component mount
  useEffect(() => {
    return () => {
      // Optional: Reset store when component unmounts
      // resetStore();
    };
  }, []);

  // Handle metadata form submission
  const handleMetadataSubmit = async (metadata: PresentationMetadata) => {
    try {
      createPresentation(metadata);

      // Start outline generation
      await startOutlineGeneration(metadata.topic, metadata.slideCount);

      toast.success('Starting presentation generation...');
    } catch (error) {
      console.error('Failed to create presentation:', error);
      toast.error('Failed to start presentation generation');
    }
  };

  // Handle outline approval
  const handleOutlineApprove = () => {
    approveOutline();
    setCurrentStep('theme');
    toast.success('Outline approved! Now choose your theme and settings.');
  };

  // Handle theme selection and start slide generation
  const handleThemeSelectionComplete = async () => {
    await startSlideGeneration();
    toast.success('Generating slides...');
  };

  // Handle step navigation
  const handleStepChange = (step: typeof currentStep) => {
    setCurrentStep(step);
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { id: 'input', label: 'Setup', icon: Settings },
      { id: 'outline', label: 'Outline', icon: FileText },
      { id: 'theme', label: 'Theme', icon: Presentation },
      { id: 'generation', label: 'Generate', icon: Sparkles },
      { id: 'editing', label: 'Edit', icon: Play },
      { id: 'export', label: 'Export', icon: Download }
    ];

    return (
      <div className="flex items-center justify-center space-x-4 mb-8">
        {steps.map((step, index) => {
          const isActive = currentStep === step.id;
          const isCompleted = steps.findIndex(s => s.id === currentStep) > index;
          const Icon = step.icon;

          return (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                  isActive
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : isCompleted
                    ? 'bg-green-600 border-green-600 text-white'
                    : 'bg-gray-100 border-gray-300 text-gray-500'
                }`}
              >
                <Icon className="w-5 h-5" />
              </div>
              <span
                className={`ml-2 text-sm font-medium ${
                  isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div
                  className={`w-8 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-600' : 'bg-gray-300'
                  }`}
                />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render generation progress
  const renderGenerationProgress = () => {
    if (!generationState.isGeneratingOutline && !generationState.isGeneratingSlides) {
      return null;
    }

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 animate-spin" />
            {generationState.isGeneratingOutline ? 'Generating Outline...' : 'Generating Slides...'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={generationState.progress} className="w-full" />
            <div className="flex justify-between text-sm text-gray-600">
              <span>
                {generationState.isGeneratingSlides && (
                  `Slide ${generationState.currentSlideIndex + 1} of ${generationState.totalSlides}`
                )}
              </span>
              <span>{Math.round(generationState.progress)}%</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'input':
        return (
          <PresentationMetadataForm
            onSubmit={handleMetadataSubmit}
            isLoading={generationState.isGeneratingOutline}
          />
        );

      case 'outline':
        return (
          <OutlineReview
            outline={outline}
            isApproved={isOutlineApproved}
            onApprove={handleOutlineApprove}
            onEdit={() => setCurrentStep('input')}
            isGenerating={generationState.isGeneratingOutline}
          />
        );

      case 'theme':
        return (
          <ThemeSelector
            selectedTheme={selectedTheme.id}
            selectedModel={selectedModel}
            selectedImageModel={selectedImageModel}
            selectedStyle={selectedStyle}
            onThemeSelect={(themeId) => {
              const theme = PRESENTATION_THEMES.find(t => t.id === themeId);
              if (theme) setTheme(theme);
            }}
            onModelSelect={setSelectedModel}
            onImageModelSelect={setSelectedImageModel}
            onStyleSelect={setSelectedStyle}
            onContinue={handleThemeSelectionComplete}
            isLoading={generationState.isGeneratingSlides}
          />
        );

      case 'generation':
        return (
          <GenerationPanel
            generationState={generationState}
            onComplete={() => setCurrentStep('editing')}
          />
        );

      case 'editing':
        return (
          <SlideEditor
            presentation={currentPresentation}
            selectedSlideId={selectedSlideId}
            onExport={() => setShowExportDialog(true)}
          />
        );

      case 'export':
        return (
          <div className="text-center py-12">
            <Download className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold mb-2">Export Your Presentation</h3>
            <p className="text-gray-600 mb-6">
              Choose your preferred format to download your presentation.
            </p>
            <Button onClick={() => setShowExportDialog(true)}>
              <Download className="w-4 h-4 mr-2" />
              Export Presentation
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI Presentation Generator
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Create professional presentations with AI. Just describe your topic and let our AI 
            generate a complete presentation with slides, content, and speaker notes.
          </p>
        </div>

        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Generation Progress */}
        {renderGenerationProgress()}

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        {currentPresentation && (
          <div className="flex justify-between items-center mt-8">
            <Button
              variant="outline"
              onClick={() => {
                const steps = ['input', 'outline', 'generation', 'editing', 'export'];
                const currentIndex = steps.indexOf(currentStep);
                if (currentIndex > 0) {
                  setCurrentStep(steps[currentIndex - 1] as typeof currentStep);
                }
              }}
              disabled={currentStep === 'input' || generationState.isGeneratingOutline || generationState.isGeneratingSlides}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <div className="flex items-center gap-4">
              {currentPresentation && (
                <Badge variant="secondary" className="px-3 py-1">
                  {currentPresentation.slides.length} slides
                </Badge>
              )}
              
              {currentStep === 'editing' && (
                <Button
                  variant="outline"
                  onClick={() => {
                    // TODO: Implement presentation mode
                    toast.info('Presentation mode coming soon!');
                  }}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Present
                </Button>
              )}
            </div>

            <Button
              onClick={() => {
                const steps = ['input', 'outline', 'generation', 'editing', 'export'];
                const currentIndex = steps.indexOf(currentStep);
                if (currentIndex < steps.length - 1) {
                  setCurrentStep(steps[currentIndex + 1] as typeof currentStep);
                }
              }}
              disabled={
                currentStep === 'export' || 
                (currentStep === 'outline' && !isOutlineApproved) ||
                generationState.isGeneratingOutline || 
                generationState.isGeneratingSlides
              }
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}

        {/* Export Dialog */}
        {showExportDialog && (
          <ExportDialog
            presentation={currentPresentation}
            isOpen={showExportDialog}
            onClose={() => setShowExportDialog(false)}
            isExporting={isExporting}
          />
        )}
      </div>
    </div>
  );
}
