import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Check, 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Eye, 
  EyeOff,
  RotateCcw,
  CheckCheck,
  Square,
  CheckSquare,
  MousePointer
} from 'lucide-react';
import { changeTrackingService } from './ChangeTrackingService';
import { ChangeTrackingState, ChangeNavigationState, ChangeSection } from './types';
import { cn } from '@/lib/utils';

interface SectionBasedDiffViewerProps {
  onAcceptSelectedSections: () => void;
  onRejectSelectedSections: () => void;
  onAcceptAllChanges: () => void;
  onRejectAllChanges: () => void;
  onNavigateChange: (direction: 'next' | 'previous') => void;
  className?: string;
}

export function SectionBasedDiffViewer({
  onAcceptSelectedSections,
  onRejectSelectedSections,
  onAcceptAllChanges,
  onRejectAllChanges,
  onNavigateChange,
  className
}: SectionBasedDiffViewerProps) {
  const [state, setState] = useState<ChangeTrackingState>(changeTrackingService.getState());
  const [navigationState, setNavigationState] = useState<ChangeNavigationState>(
    changeTrackingService.getNavigationState()
  );
  const [showDiffDetails, setShowDiffDetails] = useState(true);

  useEffect(() => {
    const unsubscribe = changeTrackingService.subscribe((newState) => {
      setState(newState);
      setNavigationState(changeTrackingService.getNavigationState());
    });

    return unsubscribe;
  }, []);

  const pendingChangesCount = changeTrackingService.getPendingChangesCount();
  const selectedSections = changeTrackingService.getSelectedSections();
  const hasSelectedSections = selectedSections.length > 0;

  if (!state.isTrackingEnabled || pendingChangesCount === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center">
          <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Changes to Review
          </h3>
          <p className="text-gray-600">
            AI modifications will appear here for review when made.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleSectionToggle = (sectionId: string) => {
    changeTrackingService.toggleSectionSelection(sectionId);
  };

  const handleSelectAll = () => {
    if (state.selectionMode === 'all') {
      changeTrackingService.clearSectionSelection();
    } else {
      changeTrackingService.selectAllSections();
    }
  };

  const renderChangeSection = (section: ChangeSection, changeIndex: number, sectionIndex: number) => {
    const isSelected = changeTrackingService.isSectionSelected(section.id);
    const isPending = section.status === 'pending';
    
    if (!isPending) return null;

    return (
      <div
        key={section.id}
        className={cn(
          "border rounded-lg p-3 cursor-pointer transition-all duration-200",
          isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300",
          "relative"
        )}
        onClick={() => handleSectionToggle(section.id)}
      >
        {/* Selection indicator */}
        <div className="absolute top-2 right-2">
          <Checkbox
            checked={isSelected}
            onChange={() => handleSectionToggle(section.id)}
            className="pointer-events-none"
          />
        </div>

        {/* Section header */}
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="outline" className="text-xs">
            Section {changeIndex + 1}.{sectionIndex + 1}
          </Badge>
          {isSelected && (
            <Badge variant="default" className="text-xs bg-blue-600">
              Selected
            </Badge>
          )}
        </div>

        {/* Content comparison */}
        <div className="space-y-2">
          {section.originalText && (
            <div className="p-2 rounded bg-red-50 border-l-4 border-red-500">
              <div className="text-xs text-red-600 font-medium mb-1">Original:</div>
              <div className="text-sm text-red-800 line-through">
                {section.originalText}
              </div>
            </div>
          )}
          
          {section.newText && (
            <div className="p-2 rounded bg-green-50 border-l-4 border-green-500">
              <div className="text-xs text-green-600 font-medium mb-1">New:</div>
              <div className="text-sm text-green-800">
                {section.newText}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderChangeWithSections = (change: any, index: number) => {
    const pendingSections = change.sections?.filter((s: ChangeSection) => s.status === 'pending') || [];
    
    if (pendingSections.length === 0) return null;

    return (
      <div key={change.id} className="space-y-3">
        {/* Change header */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                Change {index + 1}: {change.aiActionType}
              </Badge>
              <span className="text-xs text-gray-500">
                {change.timestamp.toLocaleTimeString()}
              </span>
            </div>
            {change.prompt && (
              <p className="text-sm text-gray-600 italic">
                "{change.prompt}"
              </p>
            )}
          </div>
          
          <div className="text-sm text-gray-600">
            {pendingSections.length} section{pendingSections.length !== 1 ? 's' : ''}
          </div>
        </div>

        {/* Sections */}
        <div className="space-y-2 ml-4">
          {pendingSections.map((section: ChangeSection, sectionIndex: number) => 
            renderChangeSection(section, index, sectionIndex)
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg">Section-Based Change Review</CardTitle>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {pendingChangesCount} changes
            </Badge>
            {hasSelectedSections && (
              <Badge variant="default" className="bg-green-100 text-green-800">
                {selectedSections.length} selected
              </Badge>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDiffDetails(!showDiffDetails)}
            className="flex items-center gap-2"
          >
            {showDiffDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showDiffDetails ? 'Hide' : 'Show'} Details
          </Button>
        </div>

        {/* Selection Controls */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="flex items-center gap-2"
            >
              {state.selectionMode === 'all' ? (
                <>
                  <Square className="h-4 w-4" />
                  Deselect All
                </>
              ) : (
                <>
                  <CheckSquare className="h-4 w-4" />
                  Select All
                </>
              )}
            </Button>
            
            <span className="text-sm text-gray-600">
              Click sections to select/deselect
            </span>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {hasSelectedSections ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRejectSelectedSections}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                  Reject Selected ({selectedSections.length})
                </Button>
                
                <Button
                  variant="default"
                  size="sm"
                  onClick={onAcceptSelectedSections}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                >
                  <Check className="h-4 w-4" />
                  Accept Selected ({selectedSections.length})
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRejectAllChanges}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reject All
                </Button>
                
                <Button
                  variant="default"
                  size="sm"
                  onClick={onAcceptAllChanges}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                >
                  <CheckCheck className="h-4 w-4" />
                  Accept All
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Instructions */}
        <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <MousePointer className="h-4 w-4 text-blue-600" />
          <span className="text-sm text-blue-800">
            Click on any section below to select it for acceptance or rejection
          </span>
        </div>

        {/* Changes with sections */}
        {showDiffDetails && (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {state.changes
              .filter(change => change.status === 'pending')
              .map((change, index) => renderChangeWithSections(change, index))}
          </div>
        )}

        {/* Legend */}
        <div className="flex items-center gap-4 text-xs text-gray-600 pt-2 border-t">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-50 border-l-2 border-green-500 rounded-sm"></div>
            <span>Added</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-50 border-l-2 border-red-500 rounded-sm"></div>
            <span>Removed</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-blue-50 border border-blue-500 rounded-sm"></div>
            <span>Selected</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
