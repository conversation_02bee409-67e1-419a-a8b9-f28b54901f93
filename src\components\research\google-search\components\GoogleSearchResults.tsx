/**
 * Google Search Results Component
 * Displays search results in a structured format
 */

import React, { useState } from 'react';
import { ExternalLink, FileText, Quote, Download, Share2, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

import {
  GoogleSearchResult,
  GoogleSearchSource,
  GoogleReference
} from '../types';

interface GoogleSearchResultsProps {
  result: GoogleSearchResult;
  references?: GoogleReference[];
  onSourceClick?: (url: string) => void;
  className?: string;
}

export function GoogleSearchResults({
  result,
  references = [],
  onSourceClick,
  className = ''
}: GoogleSearchResultsProps) {
  const [activeTab, setActiveTab] = useState('answer');

  const handleSourceClick = (source: GoogleSearchSource) => {
    if (onSourceClick) {
      onSourceClick(source.url);
    } else {
      window.open(source.url, '_blank');
    }
  };

  const handleExportResults = () => {
    const exportData = {
      query: result.query,
      answer: result.answer,
      sources: result.sources,
      references: references,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `google-search-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  const handleShareResults = async () => {
    const shareText = `Google Search Results for: "${result.query}"\n\n${result.answer}\n\nSources: ${result.sources.length}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Google Search: ${result.query}`,
          text: shareText,
        });
      } catch (error) {
        // Fallback to clipboard
        await navigator.clipboard.writeText(shareText);
        toast.success('Results copied to clipboard');
      }
    } else {
      await navigator.clipboard.writeText(shareText);
      toast.success('Results copied to clipboard');
    }
  };

  const getSourceTypeColor = (type: string) => {
    switch (type) {
      case 'academic': return 'bg-blue-100 text-blue-800';
      case 'journal': return 'bg-green-100 text-green-800';
      case 'news': return 'bg-orange-100 text-orange-800';
      case 'book': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
              Search Results
            </CardTitle>
            <p className="text-sm text-gray-600">
              Query: <span className="font-medium">"{result.query}"</span>
            </p>
            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
              <span>{result.sources.length} sources found</span>
              <span>{result.responseTime}ms response time</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleShareResults}>
              <Share2 className="h-4 w-4 mr-1" />
              Share
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportResults}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="answer" className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Answer</span>
            </TabsTrigger>
            <TabsTrigger value="sources" className="flex items-center space-x-1">
              <ExternalLink className="h-4 w-4" />
              <span>Sources ({result.sources.length})</span>
            </TabsTrigger>
            <TabsTrigger value="references" className="flex items-center space-x-1">
              <Quote className="h-4 w-4" />
              <span>References ({references.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="answer" className="mt-4">
            <div className="prose prose-sm max-w-none">
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                  {result.answer}
                </p>
              </div>
              
              {result.followUpQuestions && result.followUpQuestions.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-3">Follow-up Questions:</h4>
                  <div className="space-y-2">
                    {result.followUpQuestions.map((question, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <span className="text-blue-600 font-medium text-sm mt-0.5">
                          {index + 1}.
                        </span>
                        <p className="text-sm text-gray-700">{question}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="sources" className="mt-4">
            <div className="space-y-4">
              {result.sources.map((source, index) => (
                <Card
                  key={source.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleSourceClick(source)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm font-medium text-gray-900 truncate">
                            {source.title}
                          </span>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getSourceTypeColor(source.type)}`}
                          >
                            {source.type}
                          </Badge>
                        </div>
                        
                        <p className="text-xs text-blue-600 mb-2 truncate">
                          {source.domain}
                        </p>
                        
                        <p className="text-sm text-gray-700 line-clamp-3">
                          {source.snippet}
                        </p>
                        
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>Score: {(source.score * 100).toFixed(0)}%</span>
                          {source.publishedDate && (
                            <span>Published: {source.publishedDate}</span>
                          )}
                        </div>
                      </div>
                      
                      <ExternalLink className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="references" className="mt-4">
            <div className="space-y-4">
              {references.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No references available</h3>
                  <p className="text-gray-600">
                    References will be generated automatically for academic sources.
                  </p>
                </div>
              ) : (
                references.map((reference, index) => (
                  <div key={reference.id} className="border-l-4 border-blue-500 pl-4">
                    <div className="flex items-start space-x-2">
                      <span className="text-blue-600 font-mono text-sm font-medium mt-0.5">
                        [{index + 1}]
                      </span>
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 leading-relaxed">
                          {reference.apaFormat}
                        </p>
                        {reference.url && (
                          <Button
                            variant="link"
                            size="sm"
                            className="h-auto p-0 mt-1 text-xs text-blue-600"
                            onClick={() => window.open(reference.url, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Source
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

export default GoogleSearchResults;
