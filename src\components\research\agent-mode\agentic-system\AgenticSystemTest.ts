/**
 * Comprehensive test suite for the Agentic System
 */

import { agenticEditingService } from './AgenticEditingService';
import { changeTrackingAdapter } from './ChangeTrackingAdapter';
import { errorHandler } from './ErrorHandler';
import { AgenticEditingOptions } from './types';

export interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  details: string;
  error?: string;
}

export interface TestSuite {
  name: string;
  tests: TestResult[];
  overallSuccess: boolean;
  totalDuration: number;
}

export class AgenticSystemTest {
  private testDocument = `
# Research Paper: AI in Healthcare

## Introduction
Artificial intelligence (AI) has emerged as a transformative technology in healthcare, offering unprecedented opportunities to improve patient outcomes, reduce costs, and enhance the efficiency of medical practices. This paper examines the current applications of AI in healthcare and discusses future prospects.

## Methodology
Our research methodology involved a comprehensive literature review of peer-reviewed articles published between 2020 and 2024. We analyzed 150 studies focusing on AI applications in various healthcare domains.

## Results
The analysis revealed significant improvements in diagnostic accuracy when AI tools were integrated into clinical workflows. Machine learning algorithms demonstrated 95% accuracy in detecting certain types of cancer from medical imaging.

## Discussion
The findings suggest that AI technologies can significantly enhance healthcare delivery. However, challenges remain in terms of data privacy, algorithm bias, and regulatory compliance.

## Conclusion
AI represents a promising frontier in healthcare innovation. Continued research and development are essential to realize its full potential while addressing existing challenges.
  `.trim();

  /**
   * Run all test suites
   */
  async runAllTests(): Promise<TestSuite[]> {
    console.log('🧪 Starting comprehensive agentic system tests...');
    
    const testSuites: TestSuite[] = [];
    
    // Initialize the system
    agenticEditingService.initialize();
    agenticEditingService.setChangeTrackingIntegration(changeTrackingAdapter);

    try {
      testSuites.push(await this.runBasicFunctionalityTests());
      testSuites.push(await this.runToolIntegrationTests());
      testSuites.push(await this.runChangeTrackingTests());
      testSuites.push(await this.runErrorHandlingTests());
      testSuites.push(await this.runPerformanceTests());
    } catch (error: any) {
      console.error('❌ Test suite execution failed:', error);
    }

    this.printTestSummary(testSuites);
    return testSuites;
  }

  /**
   * Test basic functionality
   */
  private async runBasicFunctionalityTests(): Promise<TestSuite> {
    const tests: TestResult[] = [];
    const suiteName = 'Basic Functionality';
    
    console.log(`🔬 Running ${suiteName} tests...`);

    // Test 1: Simple edit request
    tests.push(await this.runTest(
      'Simple Grammar Fix',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          'Fix any grammatical errors in the introduction',
          this.testDocument,
          { editMode: 'conservative', enableChangeTracking: false }
        );
        
        if (!result.success) {
          throw new Error(`Edit failed: ${result.error}`);
        }
        
        return `Successfully processed request with ${result.changes.length} changes`;
      }
    ));

    // Test 2: Content enhancement
    tests.push(await this.runTest(
      'Content Enhancement',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          'Enhance the academic tone in the methodology section',
          this.testDocument,
          { editMode: 'moderate', enableChangeTracking: false }
        );
        
        return `Enhanced content with confidence ${(result.confidence * 100).toFixed(0)}%`;
      }
    ));

    // Test 3: Section-specific editing
    tests.push(await this.runTest(
      'Section-Specific Editing',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          'Add more specific examples to the results section',
          this.testDocument,
          { editMode: 'aggressive', enableChangeTracking: false }
        );
        
        return `Targeted section editing completed`;
      }
    ));

    return this.createTestSuite(suiteName, tests);
  }

  /**
   * Test tool integration
   */
  private async runToolIntegrationTests(): Promise<TestSuite> {
    const tests: TestResult[] = [];
    const suiteName = 'Tool Integration';
    
    console.log(`🔧 Running ${suiteName} tests...`);

    // Test 1: All tools workflow
    tests.push(await this.runTest(
      'Complete Workflow',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          'Improve clarity and add citations to the discussion section',
          this.testDocument,
          { 
            editMode: 'moderate',
            enableValidation: true,
            requirePreview: true,
            enableChangeTracking: false
          }
        );
        
        const toolsUsed = result.toolsUsed.length;
        return `Workflow completed using ${toolsUsed} tools`;
      }
    ));

    // Test 2: Tool failure handling
    tests.push(await this.runTest(
      'Tool Failure Handling',
      async () => {
        // This test simulates tool failure scenarios
        const result = await agenticEditingService.executeTargetedEdit(
          'This is an intentionally problematic request that should trigger error handling',
          '',  // Empty document to trigger errors
          { editMode: 'conservative', enableChangeTracking: false }
        );
        
        // Should handle gracefully even with errors
        return `Error handling worked correctly`;
      }
    ));

    return this.createTestSuite(suiteName, tests);
  }

  /**
   * Test change tracking integration
   */
  private async runChangeTrackingTests(): Promise<TestSuite> {
    const tests: TestResult[] = [];
    const suiteName = 'Change Tracking Integration';
    
    console.log(`📝 Running ${suiteName} tests...`);

    // Test 1: Change recording
    tests.push(await this.runTest(
      'Change Recording',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          'Fix grammar in the introduction',
          this.testDocument,
          { editMode: 'conservative', enableChangeTracking: true }
        );
        
        const hasChangeIds = result.changeIds && result.changeIds.length > 0;
        return `Changes recorded: ${hasChangeIds ? 'Yes' : 'No'}`;
      }
    ));

    // Test 2: Preview functionality
    tests.push(await this.runTest(
      'Preview Functionality',
      async () => {
        const result = await agenticEditingService.previewTargetedEdit(
          'Enhance academic tone',
          this.testDocument,
          { editMode: 'moderate' }
        );
        
        return `Preview generated with ${result.changes.length} changes`;
      }
    ));

    return this.createTestSuite(suiteName, tests);
  }

  /**
   * Test error handling
   */
  private async runErrorHandlingTests(): Promise<TestSuite> {
    const tests: TestResult[] = [];
    const suiteName = 'Error Handling';
    
    console.log(`🚨 Running ${suiteName} tests...`);

    // Test 1: Invalid input handling
    tests.push(await this.runTest(
      'Invalid Input Handling',
      async () => {
        const result = await agenticEditingService.executeTargetedEdit(
          '',  // Empty prompt
          this.testDocument,
          { editMode: 'conservative', enableChangeTracking: false }
        );
        
        // Should handle gracefully
        return `Invalid input handled gracefully`;
      }
    ));

    // Test 2: Error recovery
    tests.push(await this.runTest(
      'Error Recovery',
      async () => {
        const stats = errorHandler.getErrorStatistics();
        return `Error handler has ${stats.totalErrors} recorded errors with ${(stats.recoveryRate * 100).toFixed(0)}% recovery rate`;
      }
    ));

    return this.createTestSuite(suiteName, tests);
  }

  /**
   * Test performance
   */
  private async runPerformanceTests(): Promise<TestSuite> {
    const tests: TestResult[] = [];
    const suiteName = 'Performance';
    
    console.log(`⚡ Running ${suiteName} tests...`);

    // Test 1: Execution time
    tests.push(await this.runTest(
      'Execution Time',
      async () => {
        const startTime = Date.now();
        const result = await agenticEditingService.executeTargetedEdit(
          'Improve clarity in the methodology section',
          this.testDocument,
          { editMode: 'moderate', enableChangeTracking: false }
        );
        const endTime = Date.now();
        
        const executionTime = endTime - startTime;
        const isAcceptable = executionTime < 30000; // 30 seconds
        
        if (!isAcceptable) {
          throw new Error(`Execution time too long: ${executionTime}ms`);
        }
        
        return `Execution completed in ${executionTime}ms`;
      }
    ));

    // Test 2: Memory usage (simplified)
    tests.push(await this.runTest(
      'Memory Usage',
      async () => {
        // Simple memory usage check
        const status = agenticEditingService.getStatus();
        return `System status: ${status.initialized ? 'Healthy' : 'Issues detected'}`;
      }
    ));

    return this.createTestSuite(suiteName, tests);
  }

  /**
   * Run a single test
   */
  private async runTest(testName: string, testFunction: () => Promise<string>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const details = await testFunction();
      const duration = Date.now() - startTime;
      
      console.log(`  ✅ ${testName}: ${details} (${duration}ms)`);
      
      return {
        testName,
        success: true,
        duration,
        details
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      console.log(`  ❌ ${testName}: ${error.message} (${duration}ms)`);
      
      return {
        testName,
        success: false,
        duration,
        details: 'Test failed',
        error: error.message
      };
    }
  }

  /**
   * Create test suite result
   */
  private createTestSuite(name: string, tests: TestResult[]): TestSuite {
    const overallSuccess = tests.every(test => test.success);
    const totalDuration = tests.reduce((sum, test) => sum + test.duration, 0);
    
    return {
      name,
      tests,
      overallSuccess,
      totalDuration
    };
  }

  /**
   * Print test summary
   */
  private printTestSummary(testSuites: TestSuite[]): void {
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    let totalTests = 0;
    let passedTests = 0;
    let totalDuration = 0;
    
    testSuites.forEach(suite => {
      const passed = suite.tests.filter(t => t.success).length;
      const total = suite.tests.length;
      
      totalTests += total;
      passedTests += passed;
      totalDuration += suite.totalDuration;
      
      const status = suite.overallSuccess ? '✅' : '❌';
      console.log(`${status} ${suite.name}: ${passed}/${total} tests passed (${suite.totalDuration}ms)`);
    });
    
    const overallSuccess = passedTests === totalTests;
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : '0';
    
    console.log('================');
    console.log(`Overall: ${passedTests}/${totalTests} tests passed (${successRate}%)`);
    console.log(`Total execution time: ${totalDuration}ms`);
    console.log(`Status: ${overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  }
}

// Export test runner
export const agenticSystemTest = new AgenticSystemTest();
