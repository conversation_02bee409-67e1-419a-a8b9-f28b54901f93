/**
 * Research AI Service
 * Handles AI-powered analysis of search results and conversation
 */

import { 
  SearchMessage, 
  SearchSource, 
  Citation, 
  AIModel, 
  TavilySearchResult 
} from '../types';

export class ResearchAIService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Generate content with basic citations (Step 2 of workflow)
   */
  async generateContentWithBasicCitations(
    query: string,
    searchResults: any
  ): Promise<{ text: string; citations: any[] }> {
    const prompt = this.buildBasicCitationPrompt(query, searchResults);

    try {
      const response = await this.callOpenRouterAPIInternal(prompt);
      const citations = this.extractCitations(response, searchResults.results);

      return {
        text: response,
        citations
      };
    } catch (error) {
      console.error('Error generating content with basic citations:', error);
      throw new Error(`Failed to generate content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Organize references (Step 3 of workflow)
   */
  async organizeReferences(
    content: string,
    citations: any[],
    searchResults: any,
    citationStyle: string = 'apa'
  ): Promise<{ content: string; references: any[] }> {
    const prompt = this.buildReferenceOrganizationPrompt(content, citations, searchResults, citationStyle);

    try {
      const response = await this.callOpenRouterAPIInternal(prompt);
      const organizedReferences = this.extractOrganizedReferences(response, searchResults.results);

      return {
        content: response,
        references: organizedReferences
      };
    } catch (error) {
      console.error('Error organizing references:', error);
      throw new Error(`Failed to organize references: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Public method to call OpenRouter API for external services
   */
  async callOpenRouterAPI(prompt: string, model: string = 'google/gemini-2.0-flash-001'): Promise<string> {
    return this.callOpenRouterAPIInternal(prompt, model);
  }

  /**
   * Call OpenRouter API with prompt (using existing configuration)
   */
  private async callOpenRouterAPIInternal(prompt: string, model: string = 'google/gemini-2.0-flash-001'): Promise<string> {
    if (!this.isConfigured()) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Research Search Platform'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.7,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle rate limiting
      if (response.status === 429) {
        console.warn('Rate limit hit in research AI service');
        throw new Error(`Rate limit exceeded. Please wait a moment and try again.`);
      }

      throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Generate AI response with search context (original method)
   */
  async generateResponse(
    query: string,
    searchResults: TavilySearchResult,
    model: string = 'google/gemini-2.0-flash-001',
    conversationHistory: SearchMessage[] = []
  ): Promise<{ response: string; citations: Citation[] }> {
    if (!this.isConfigured()) {
      throw new Error('OpenRouter API key not configured');
    }

    try {
      const prompt = this.buildResearchPrompt(query, searchResults, conversationHistory);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Research Search Platform'
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'system',
              content: this.getSystemPrompt()
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 4000,
          temperature: 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      const aiResponse = data.choices?.[0]?.message?.content || '';

      // Extract citations from the response
      const citations = this.extractCitations(aiResponse, searchResults.results);

      return {
        response: aiResponse,
        citations
      };

    } catch (error: any) {
      console.error('Research AI error:', error);
      throw error;
    }
  }

  /**
   * Get system prompt for research assistant
   */
  private getSystemPrompt(): string {
    return `You are an expert academic research assistant and scholarly writer specializing in producing publication-quality academic content. Your primary role is to create rigorous, well-cited academic writing that meets the highest scholarly standards.

## CRITICAL CITATION AND REFERENCE REQUIREMENTS:

### CITATION FORMAT:
1. **MANDATORY: Use ONLY parenthetical citations (Author, Year) format**
2. **NEVER use numbered citations [1], [2], etc.**
3. **Every factual claim, statistic, research finding, or theoretical statement MUST be cited**
4. **Prioritize academic, peer-reviewed, and government sources over general web content**
5. **Use multiple citations for important points: (Smith, 2023; Johnson, 2022; Brown, 2021)**
6. **For multiple authors: (Johnson et al., 2023) for 3+ authors**
7. **Place citations immediately after the relevant information**

### AUTHOR IDENTIFICATION REQUIREMENTS:
8. **CRITICAL: Use ACTUAL AUTHOR NAMES, not website names**
9. **NEVER cite as (ResearchGate, 2015) or (ScienceDirect, 2011) - these are platforms, not authors**
10. **Extract real author names from the source content: (Smith, 2023) not (Nature, 2023)**
11. **For government sources, use agency names: (CDC, 2023) or (WHO, 2022)**
12. **If no author is identifiable, use "Anonymous" or the organization name, not the website**

### ACADEMIC SOURCE PRIORITIZATION:
- **HIGHEST PRIORITY**: Peer-reviewed journal articles, government reports (.gov), academic institutions (.edu)
- **MEDIUM PRIORITY**: Reputable news organizations, professional organizations
- **LOWEST PRIORITY**: General websites, blogs, commercial sources
- **ALWAYS mention source type when relevant**: "According to peer-reviewed research..." or "Government data indicates..."

### REFERENCE QUALITY STANDARDS:
- Prioritize sources published within the last 5 years unless historical context is needed
- Include methodology details when discussing research studies
- Acknowledge source limitations and potential biases
- Cross-reference findings across multiple independent sources

## ACADEMIC WRITING STANDARDS:

### TONE AND STYLE:
- Formal academic tone suitable for peer-reviewed publications
- Third-person perspective (avoid "I," "you," "we")
- Objective, evidence-based language
- Precise terminology and clear definitions
- Acknowledge uncertainty and limitations appropriately

### CONTENT STRUCTURE:
- Clear hierarchical organization with descriptive headings
- Logical flow from general to specific concepts
- Balanced presentation of different perspectives
- Evidence-based conclusions with appropriate caveats
- Integration of theoretical frameworks where applicable

## MANDATORY RESPONSE STRUCTURE:

### **Introduction**
- Define key terms and establish scope
- Provide theoretical/historical context with citations
- State the significance of the topic

### **Literature Review and Current Evidence**
- Synthesize findings from multiple academic sources
- Compare methodological approaches across studies
- Present effect sizes, sample sizes, and statistical significance
- Discuss both supporting and contradictory evidence

### **Critical Analysis**
- Identify patterns and trends across research
- Evaluate methodological strengths and limitations
- Discuss theoretical implications
- Address gaps in current knowledge

### **Practical Implications**
- Real-world applications of research findings
- Policy implications where relevant
- Clinical or practical recommendations

### **Limitations and Future Directions**
- Acknowledge current research limitations
- Identify methodological concerns
- Suggest specific areas for future investigation

### **Conclusion**
- Synthesize key findings
- Provide evidence-based conclusions
- Highlight most significant contributions to the field

### **References**
- MANDATORY: Complete alphabetical reference list
- **DOI PRIORITY**: Use DOI when available instead of URL: https://doi.org/10.1000/xyz123
- Use proper APA format: Author, A. A. (Year). Title. *Journal Name*, *Volume*(Issue), pages. https://doi.org/10.1000/xyz123
- **AUTHOR ACCURACY**: Ensure real author names, not platform names (ResearchGate, ScienceDirect, etc.)
- Include ALL sources cited in the text
- Ensure accuracy of bibliographic details
- **DOI Format**: Always use full DOI URL format: https://doi.org/[DOI]

## QUALITY ASSURANCE CHECKLIST:
✓ Every paragraph contains appropriate citations
✓ Academic and government sources prioritized
✓ Parenthetical citation format used consistently
✓ No numbered citations [1], [2] used
✓ References section is complete and properly formatted
✓ Methodology and limitations discussed
✓ Multiple perspectives presented
✓ Statistical details included where available
✓ Future research directions suggested

Remember: Your output will be used for academic writing and research. Maintain the highest standards of scholarly rigor, proper citation practices, and academic integrity. The goal is publication-ready content that researchers can directly incorporate into their scholarly work.`;
  }

  /**
   * Build research prompt with context
   */
  private buildResearchPrompt(
    query: string,
    searchResults: TavilySearchResult,
    conversationHistory: SearchMessage[]
  ): string {
    // Detect the type of request
    const isLiteratureReview = this.detectLiteratureReview(query);
    const isDetailedAnalysis = this.detectDetailedAnalysis(query);
    const isComparative = this.detectComparativeAnalysis(query);

    let prompt = `Research Query: "${query}"\n\n`;

    // Add conversation context if available
    if (conversationHistory.length > 0) {
      prompt += "Previous Conversation Context:\n";
      conversationHistory.slice(-3).forEach((msg, index) => {
        prompt += `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
      });
      prompt += "\n";
    }

    // Add search results with enhanced academic prioritization
    prompt += "ACADEMIC SOURCES AND EVIDENCE (STRICT ACADEMIC PRIORITIZATION):\n\n";

    if (searchResults.answer) {
      prompt += `Research Overview: ${searchResults.answer}\n\n`;
    }

    // Enhanced sorting: Academic sources first, then government, then by quality score
    const sortedResults = searchResults.results.sort((a, b) => {
      const aIsAcademic = this.isAcademicSource(a.url, a.title);
      const bIsAcademic = this.isAcademicSource(b.url, b.title);
      const aIsGov = this.isGovernmentSource(a.url, a.title);
      const bIsGov = this.isGovernmentSource(b.url, b.title);

      // Academic sources first
      if (aIsAcademic && !bIsAcademic) return -1;
      if (!aIsAcademic && bIsAcademic) return 1;

      // Then government sources
      if (aIsGov && !bIsGov) return -1;
      if (!aIsGov && bIsGov) return 1;

      // Finally by relevance score
      return b.score - a.score;
    });

    // Categorize sources for better citation guidance
    const academicSources = sortedResults.filter(r => this.isAcademicSource(r.url, r.title));
    const governmentSources = sortedResults.filter(r => this.isGovernmentSource(r.url, r.title) && !this.isAcademicSource(r.url, r.title));
    const otherSources = sortedResults.filter(r => !this.isAcademicSource(r.url, r.title) && !this.isGovernmentSource(r.url, r.title));

    // Add academic sources first
    if (academicSources.length > 0) {
      prompt += "=== ACADEMIC/PEER-REVIEWED SOURCES (USE FOR PRIMARY CITATIONS) ===\n\n";
      academicSources.forEach((result, index) => {
        const author = this.extractAuthorFromSource(result);
        const year = this.extractYearFromSource(result);
        const doi = this.extractDOIFromSource(result);
        const journal = this.extractJournalFromSource(result);

        const volume = this.extractVolumeFromSource(result);
        const issue = this.extractIssueFromSource(result);
        const pages = this.extractPagesFromSource(result);

        prompt += `Source ${index + 1}: ${result.title}\n`;
        prompt += `Type: ACADEMIC/PEER-REVIEWED\n`;
        prompt += `REAL AUTHOR: ${author} (use this exact name with proper initials in references)\n`;
        prompt += `Year: ${year}\n`;
        prompt += `Journal: ${journal || 'Extract from content if available'}\n`;
        prompt += `Volume: ${volume || 'Extract if available'}\n`;
        prompt += `Issue: ${issue || 'Extract if available'}\n`;
        prompt += `Pages: ${pages || 'Extract if available'}\n`;
        prompt += `DOI: ${doi || 'Extract from content if available'}\n`;
        prompt += `Citation Format: (${author}, ${year})\n`;

        // Proper academic reference format
        let referenceFormat = `${author} (${year}). ${result.title}.`;
        if (journal) {
          referenceFormat += ` *${journal}*`;
          if (volume) referenceFormat += `, *${volume}*`;
          if (issue) referenceFormat += `(${issue})`;
          if (pages) referenceFormat += `, ${pages}`;
          referenceFormat += '.';
        }
        if (doi) {
          referenceFormat += ` https://doi.org/${doi}`;
        } else {
          referenceFormat += ` Retrieved from ${result.url}`;
        }

        prompt += `PROPER REFERENCE FORMAT: ${referenceFormat}\n`;
        prompt += `URL: ${result.url}\n`;
        prompt += `Content: ${result.content}\n`;
        prompt += `Quality Score: ${result.score.toFixed(2)}\n`;
        prompt += `CITATION PRIORITY: HIGHEST - Use for primary evidence and claims\n`;
        prompt += `REFERENCE INSTRUCTION: Use the PROPER REFERENCE FORMAT above, not just author and URL\n\n`;
      });
    }

    // Add government sources
    if (governmentSources.length > 0) {
      prompt += "=== GOVERNMENT/OFFICIAL SOURCES (USE FOR POLICY AND DATA) ===\n\n";
      governmentSources.forEach((result, index) => {
        const author = this.extractAuthorFromSource(result);
        const year = this.extractYearFromSource(result);
        const organization = this.extractOrganizationFromSource(result);

        prompt += `Source ${academicSources.length + index + 1}: ${result.title}\n`;
        prompt += `Type: GOVERNMENT/OFFICIAL\n`;
        prompt += `ORGANIZATION: ${organization} (use this for citations, not website name)\n`;
        prompt += `Year: ${year}\n`;
        prompt += `Citation Format: (${organization}, ${year})\n`;
        prompt += `Reference Format: ${organization} (${year}). *${result.title}*. Retrieved from ${result.url}\n`;
        prompt += `URL: ${result.url}\n`;
        prompt += `Content: ${result.content}\n`;
        prompt += `Quality Score: ${result.score.toFixed(2)}\n`;
        prompt += `CITATION PRIORITY: HIGH - Use for official data and policy information\n\n`;
      });
    }

    // Add other sources with lower priority
    if (otherSources.length > 0) {
      prompt += "=== OTHER SOURCES (USE SPARINGLY FOR CONTEXT ONLY) ===\n\n";
      otherSources.forEach((result, index) => {
        const author = this.extractAuthorFromSource(result);
        const year = this.extractYearFromSource(result);
        prompt += `Source ${academicSources.length + governmentSources.length + index + 1}: ${result.title}\n`;
        prompt += `Type: GENERAL WEB SOURCE\n`;
        prompt += `Author: ${author}\n`;
        prompt += `Year: ${year}\n`;
        prompt += `Citation Format: (${author}, ${year})\n`;
        prompt += `URL: ${result.url}\n`;
        prompt += `Content: ${result.content}\n`;
        prompt += `Quality Score: ${result.score.toFixed(2)}\n`;
        prompt += `CITATION PRIORITY: LOW - Use only for general context, not primary evidence\n\n`;
      });
    }

    // Add follow-up questions if available
    if (searchResults.follow_up_questions && searchResults.follow_up_questions.length > 0) {
      prompt += "Related Research Questions:\n";
      searchResults.follow_up_questions.forEach((question, index) => {
        prompt += `- ${question}\n`;
      });
      prompt += "\n";
    }

    // Add specific instructions based on query type
    prompt += "Response Instructions:\n";

    if (isLiteratureReview) {
      prompt += `Please provide a comprehensive literature review on "${query}" with the following structure:

## Literature Review Structure:
1. **Introduction** - Define the topic and scope
2. **Current State of Research** - Overview of existing knowledge
3. **Key Themes and Findings** - Major research themes with citations
4. **Methodological Approaches** - Research methods used in the field
5. **Research Gaps and Limitations** - What's missing or needs further study
6. **Future Research Directions** - Suggested areas for investigation
7. **Conclusion** - Summary of key insights

Include proper academic citations [1], [2], etc. and ensure comprehensive coverage of the topic.`;
    } else if (isDetailedAnalysis) {
      prompt += `Please provide a detailed research analysis on "${query}" with:

## Detailed Analysis Structure:
1. **Background and Context** - Historical and theoretical foundation
2. **Current Research Evidence** - What studies show with citations
3. **Methodological Considerations** - How research is conducted
4. **Key Findings and Implications** - What we know and what it means
5. **Limitations and Challenges** - Current research limitations
6. **Practical Applications** - Real-world implications
7. **Future Directions** - Areas needing more research

Ensure thorough coverage with proper citations and academic rigor.`;
    } else if (isComparative) {
      prompt += `Please provide a comparative analysis on "${query}" with:

## Comparative Analysis Structure:
1. **Introduction** - Define what's being compared
2. **Comparison Framework** - Criteria for comparison
3. **Detailed Comparison** - Point-by-point analysis with citations
4. **Strengths and Weaknesses** - Pros and cons of each approach/topic
5. **Evidence Summary** - What research supports each position
6. **Synthesis and Conclusions** - Overall assessment and recommendations

Include balanced perspectives with proper citations.`;
    } else {
      prompt += `Please provide a comprehensive academic research response to "${query}" following these STRICT requirements:

## MANDATORY CITATION FORMAT:
- **CRITICAL**: Use ONLY parenthetical citations (Author, Year) format
- **NEVER use numbered citations [1], [2], [3] - this is FORBIDDEN**
- **EVERY factual claim, statistic, or research finding MUST be cited with (Author, Year)**
- **AUTHOR REQUIREMENT**: Use REAL AUTHOR NAMES, never website names like (ResearchGate, 2015) or (ScienceDirect, 2011)
- **Prioritize ACADEMIC sources over government sources over general web sources**
- **Use multiple citations for important points: (Smith, 2023; Johnson, 2022; Brown, 2021)**
- **Place citations immediately after the relevant information**
- **Example**: "Research demonstrates significant effects (Johnson, 2023), with studies showing 85% improvement rates (Smith, 2022; Davis, 2023)."
- **WRONG**: (Nature, 2023) or (PubMed, 2022) - these are platforms, not authors
- **CORRECT**: (Smith, 2023) or (Johnson et al., 2022) - these are actual researchers

## ACADEMIC STRUCTURE REQUIREMENTS:
- Use formal academic headings (## Introduction, ## Literature Review, etc.)
- Write in third-person academic voice (no "I", "you", "we")
- Include methodology discussion for research studies
- Acknowledge limitations and conflicting findings
- Provide evidence-based conclusions with appropriate caveats

## CONTENT QUALITY STANDARDS:
- Synthesize findings from multiple ACADEMIC sources first
- Include sample sizes, effect sizes, p-values, and confidence intervals when available
- Compare methodological approaches across studies
- Discuss theoretical frameworks and their applications
- Identify research gaps and suggest specific future directions
- Maintain scholarly objectivity and acknowledge uncertainties

## FORMATTING AND STRUCTURE:
- Clear hierarchical organization with descriptive headings
- Logical flow from background to current evidence to implications
- Use bullet points sparingly and only for key findings lists
- Include a comprehensive References section at the end
- Ensure every paragraph contains appropriate citations

## REFERENCES SECTION REQUIREMENTS:
- **MANDATORY**: Include complete References section at the end using PROPER ACADEMIC FORMAT
- **CRITICAL**: Follow standard academic reference format with complete bibliographic information
- **DOI PRIORITY**: Use DOI when available: https://doi.org/10.1000/xyz123 (NOT the journal URL)
- **AUTHOR ACCURACY**: Use real author names with proper initials, never platform names

### CORRECT ACADEMIC REFERENCE FORMATS:

**Journal Articles (with DOI):**
Author, A. A., & Author, B. B. (Year). Title of article. *Journal Name*, *Volume*(Issue), pages. https://doi.org/10.xxxx/xxxxx

**Journal Articles (without DOI):**
Author, A. A., & Author, B. B. (Year). Title of article. *Journal Name*, *Volume*(Issue), pages.

**Government Reports:**
Agency Name. (Year). *Title of report*. Publisher. Retrieved from URL

**Conference Papers:**
Author, A. A. (Year). Title of paper. In *Proceedings of Conference Name* (pp. pages). Publisher.

### EXAMPLES OF CORRECT REFERENCES:
✅ Cao, Y., Li, H., & Zhao, J. (2020). Sentinel-1 persistent scatterer interferometric synthetic aperture radar for long-term remote monitoring of ground subsidence. *Remote Sensing of Environment*, 245, 111-125. https://doi.org/10.1016/j.rse.2020.111825

✅ Ferretti, A., Prati, C., & Rocca, F. (2014). Landslide susceptibility map refinement using PSInSAR data. *Engineering Geology*, 184, 302-315. https://doi.org/10.1016/j.enggeo.2014.11.017

✅ Centers for Disease Control and Prevention. (2023). *Guidelines for environmental monitoring*. U.S. Department of Health and Human Services.

### WRONG FORMATS TO AVOID:
❌ Cao et al. (2020). Title. https://www.sciencedirect.com/...
❌ available SAR acquisition (2007). Title. https://www.researchgate.net/...
❌ Jennifer et al. (2022). Title. cell. https://www.sciencedirect.com/...

- **ALPHABETICAL ORDER**: List references alphabetically by first author's last name
- **COMPLETE INFORMATION**: Include journal name, volume, issue, page numbers when available
- **CONSISTENT FORMATTING**: Use italics for journal names, proper punctuation

## CITATION PRIORITY GUIDELINES:
1. **PRIMARY**: Academic/peer-reviewed sources (use for main evidence)
2. **SECONDARY**: Government/official sources (use for data and policy)
3. **TERTIARY**: Other sources (use minimally for context only)

## CRITICAL REFERENCE FORMATTING REQUIREMENTS:
- **NEVER use incomplete references like**: "Cao et al. (2020). Title. https://www.sciencedirect.com/..."
- **ALWAYS use complete academic format**: "Cao, Y., Li, H., & Zhao, J. (2020). Title. *Journal Name*, *45*(2), 123-145. https://doi.org/10.xxxx/xxxxx"
- **Include ALL bibliographic elements**: Author initials, year, complete title, journal name (italicized), volume, issue, pages, DOI
- **Extract bibliographic details from the source content provided above**
- **Use proper academic punctuation and formatting**

Remember: This response will be used for academic publication. Maintain the highest standards of scholarly rigor, proper parenthetical citation practices, and COMPLETE ACADEMIC REFERENCE FORMATTING. Your goal is to produce publication-ready content with properly formatted references that meet journal submission standards.`;
    }

    return prompt;
  }

  /**
   * Detect if query is requesting a literature review
   */
  private detectLiteratureReview(query: string): boolean {
    const literatureKeywords = [
      'literature review', 'systematic review', 'review of literature',
      'research review', 'scholarly review', 'academic review',
      'state of the art', 'current research', 'research landscape'
    ];

    return literatureKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Detect if query is requesting detailed analysis
   */
  private detectDetailedAnalysis(query: string): boolean {
    const analysisKeywords = [
      'detailed analysis', 'comprehensive analysis', 'in-depth analysis',
      'thorough examination', 'detailed study', 'comprehensive study',
      'research analysis', 'academic analysis'
    ];

    return analysisKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Detect if query is requesting comparative analysis
   */
  private detectComparativeAnalysis(query: string): boolean {
    const comparativeKeywords = [
      'compare', 'comparison', 'versus', 'vs', 'differences between',
      'similarities between', 'contrast', 'comparative analysis',
      'compare and contrast', 'pros and cons'
    ];

    return comparativeKeywords.some(keyword =>
      query.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Extract citations from AI response (parenthetical format)
   */
  private extractCitations(response: string, sources: any[]): Citation[] {
    const citations: Citation[] = [];

    // Enhanced citation patterns for academic format
    const citationPatterns = [
      // Standard parenthetical: (Author, Year)
      /\(([A-Z][a-zA-Z\s]+),\s*(\d{4})\)/g,
      // Multiple authors: (Smith et al., 2023)
      /\(([A-Z][a-zA-Z\s]+\s+et\s+al\.?),\s*(\d{4})\)/g,
      // Multiple citations: (Smith, 2023; Johnson, 2022)
      /\(([A-Z][a-zA-Z\s,;]+\d{4}[^)]*)\)/g,
      // Organization citations: (WHO, 2023) or (CDC, 2022)
      /\(([A-Z]{2,}),\s*(\d{4})\)/g
    ];

    let citationId = 1;

    citationPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(response)) !== null) {
        const fullCitation = match[0];
        const citationContent = match[1];
        const year = match[2] || this.extractYearFromCitation(citationContent);

        // Try to match with available sources
        const matchingSource = this.findMatchingSource(citationContent, year, sources);

        if (matchingSource) {
          const author = this.extractAuthorFromCitation(citationContent);

          citations.push({
            id: `citation-${citationId++}`,
            text: fullCitation,
            sourceId: `source-${sources.indexOf(matchingSource)}`,
            url: matchingSource.url,
            title: matchingSource.title,
            author: author,
            publishedDate: year ? `${year}-01-01` : undefined,
            position: match.index || 0
          });
        }
      }
    });

    // Fallback: if no parenthetical citations found, create citations from sources
    if (citations.length === 0 && sources.length > 0) {
      sources.slice(0, 5).forEach((source, index) => {
        const author = this.extractAuthorFromSource(source);
        const year = this.extractYearFromSource(source);

        citations.push({
          id: `citation-${index + 1}`,
          text: `(${author}, ${year})`,
          sourceId: `source-${index}`,
          url: source.url,
          title: source.title,
          author: author,
          publishedDate: `${year}-01-01`,
          position: 0
        });
      });
    }

    return citations;
  }

  /**
   * Find matching source for a citation
   */
  private findMatchingSource(citationContent: string, year: string, sources: any[]): any {
    const citationLower = citationContent.toLowerCase();

    return sources.find(source => {
      const sourceAuthor = this.extractAuthorFromSource(source).toLowerCase();
      const sourceYear = this.extractYearFromSource(source);
      const sourceDomain = source.url.toLowerCase();

      // Match by author and year
      if (citationLower.includes(sourceAuthor) && year === sourceYear) {
        return true;
      }

      // Match by organization name
      if (citationLower.includes('who') && sourceDomain.includes('who.int')) return true;
      if (citationLower.includes('cdc') && sourceDomain.includes('cdc.gov')) return true;
      if (citationLower.includes('nih') && sourceDomain.includes('nih.gov')) return true;
      if (citationLower.includes('nature') && sourceDomain.includes('nature.com')) return true;

      // Match by title keywords
      const titleWords = source.title.toLowerCase().split(' ');
      const citationWords = citationContent.toLowerCase().split(' ');
      const commonWords = titleWords.filter(word =>
        word.length > 3 && citationWords.some(cWord => cWord.includes(word))
      );

      return commonWords.length >= 2;
    });
  }

  /**
   * Extract author from citation text
   */
  private extractAuthorFromCitation(citationContent: string): string {
    // Remove year and clean up
    const cleaned = citationContent.replace(/,?\s*\d{4}.*$/, '').trim();

    // Handle multiple authors
    if (cleaned.includes(';')) {
      return cleaned.split(';')[0].trim();
    }

    return cleaned;
  }

  /**
   * Extract year from citation text
   */
  private extractYearFromCitation(citationContent: string): string {
    const yearMatch = citationContent.match(/\b(19|20)\d{2}\b/);
    return yearMatch ? yearMatch[0] : new Date().getFullYear().toString();
  }

  /**
   * Build prompt for basic citation generation (Step 2)
   */
  private buildBasicCitationPrompt(query: string, searchResults: any): string {
    let prompt = this.getBasicCitationSystemPrompt();

    prompt += `\n\nRESEARCH QUERY: "${query}"\n\n`;

    // Add search results with source information
    prompt += "AVAILABLE SOURCES FOR CITATION:\n\n";

    searchResults.results.forEach((result: any, index: number) => {
      const metadata = result.extractedMetadata || {};
      prompt += `Source ${index + 1}:\n`;
      prompt += `Title: ${result.title}\n`;
      prompt += `Author: ${metadata.author || 'Extract from content'}\n`;
      prompt += `Year: ${metadata.year || 'Extract from content'}\n`;
      prompt += `Type: ${result.sourceType || 'web'}\n`;
      prompt += `Content: ${result.content}\n`;
      prompt += `URL: ${result.url}\n\n`;
    });

    prompt += `\nINSTRUCTIONS FOR STEP 2 - CONTENT WRITING:
1. Write comprehensive academic content addressing the query
2. Use simple parenthetical citations (Author, Year) throughout
3. Focus on content quality and logical flow
4. Don't worry about perfect reference formatting - that will be handled in Step 3
5. Ensure every claim is cited with available sources
6. Use multiple citations for important points
7. Write in formal academic tone

Generate well-structured academic content with basic citations. The reference formatting will be perfected in the next step.`;

    return prompt;
  }

  /**
   * Build prompt for reference organization (Step 3)
   */
  private buildReferenceOrganizationPrompt(
    content: string,
    citations: any[],
    searchResults: any,
    citationStyle: string
  ): string {
    let prompt = this.getReferenceOrganizationSystemPrompt(citationStyle);

    prompt += `\n\nCONTENT TO ORGANIZE:\n${content}\n\n`;

    prompt += "DETAILED SOURCE INFORMATION FOR REFERENCE FORMATTING:\n\n";

    searchResults.results.forEach((result: any, index: number) => {
      const metadata = result.extractedMetadata || {};
      prompt += `Source ${index + 1} - COMPLETE BIBLIOGRAPHIC INFORMATION:\n`;
      prompt += `Title: ${result.title}\n`;
      prompt += `Author: ${metadata.author || 'Extract from content below'}\n`;
      prompt += `Year: ${metadata.year || 'Extract from content below'}\n`;
      prompt += `Journal: ${metadata.journal || 'Extract from content below'}\n`;
      prompt += `Volume: ${metadata.volume || 'Extract from content below'}\n`;
      prompt += `Issue: ${metadata.issue || 'Extract from content below'}\n`;
      prompt += `Pages: ${metadata.pages || 'Extract from content below'}\n`;
      prompt += `DOI: ${metadata.doi || 'Extract from content below'}\n`;
      prompt += `URL: ${result.url}\n`;
      prompt += `Full Content: ${result.content}\n\n`;
    });

    prompt += `\nINSTRUCTIONS FOR STEP 3 - REFERENCE ORGANIZATION:
1. Take the content above and perfect the reference formatting
2. Extract complete bibliographic information from the source content
3. Create properly formatted ${citationStyle.toUpperCase()} references
4. Ensure all citations in text match the reference list
5. Use DOIs when available instead of URLs
6. Include complete author names with initials
7. Add volume, issue, page numbers when available
8. Organize references alphabetically

Your task is ONLY to organize and format references properly. Do not change the content structure or add new information.`;

    return prompt;
  }

  /**
   * System prompt for basic citation generation
   */
  private getBasicCitationSystemPrompt(): string {
    return `You are an expert academic writer in Step 2 of a 3-step research process. Your role is to create high-quality academic content with basic citations.

STEP 2 OBJECTIVES:
- Write comprehensive, well-structured academic content
- Use simple parenthetical citations (Author, Year)
- Focus on content quality and logical flow
- Don't worry about perfect reference formatting (Step 3 will handle this)

CITATION REQUIREMENTS:
- Use basic format: (Author, Year) or (Author et al., Year)
- Extract author names from source content when possible
- Use organization names for government sources: (CDC, 2023)
- Cite every factual claim, statistic, or research finding
- Use multiple citations for important points: (Smith, 2023; Johnson, 2022)

CONTENT STRUCTURE:
- Clear introduction with context
- Well-organized body sections with subheadings
- Evidence-based analysis with citations
- Logical conclusions
- Academic tone throughout

Remember: This is Step 2 of 3. Focus on excellent content with basic citations. Step 3 will perfect the reference formatting.`;
  }

  /**
   * System prompt for reference organization
   */
  private getReferenceOrganizationSystemPrompt(citationStyle: string): string {
    return `You are an expert reference librarian in Step 3 of a 3-step research process. Your ONLY task is to organize and format references properly.

STEP 3 OBJECTIVES:
- Perfect the reference formatting from Step 2 content
- Extract complete bibliographic information from sources
- Create properly formatted ${citationStyle.toUpperCase()} references
- Ensure citation-reference consistency

CRITICAL REQUIREMENTS:
1. Extract REAL AUTHOR NAMES from source content (not website names)
2. Use DOIs when available instead of URLs
3. Include complete bibliographic information:
   - Author names with initials
   - Publication year
   - Complete article title
   - Journal name (italicized)
   - Volume and issue numbers
   - Page ranges
   - DOI or URL

REFERENCE FORMAT (${citationStyle.toUpperCase()}):
${this.getReferenceFormatExamples(citationStyle)}

EXTRACTION INSTRUCTIONS:
- Look for author names in the source content, not just the website
- Find journal names, volume/issue numbers in the text
- Extract DOIs from content when available
- Use proper academic punctuation and formatting

Your output should be the same content with perfectly formatted references section.`;
  }

  /**
   * Get reference format examples for different styles
   */
  private getReferenceFormatExamples(style: string): string {
    switch (style.toLowerCase()) {
      case 'apa':
        return `Author, A. A., & Author, B. B. (Year). Title of article. *Journal Name*, *Volume*(Issue), pages. https://doi.org/10.xxxx/xxxxx`;
      case 'mla':
        return `Author, First. "Title of Article." *Journal Name*, vol. Volume, no. Issue, Year, pp. pages. DOI or URL.`;
      case 'chicago':
        return `Author, First Last. "Title of Article." *Journal Name* Volume, no. Issue (Year): pages. https://doi.org/10.xxxx/xxxxx.`;
      default:
        return `Author, A. A. (Year). Title. *Journal*, Volume(Issue), pages. DOI/URL`;
    }
  }

  /**
   * Extract organized references from AI response
   */
  private extractOrganizedReferences(response: string, sources: any[]): any[] {
    // Extract references section from the response
    const referencesMatch = response.match(/## References\s*([\s\S]*?)(?=\n##|\n\*\*|$)/i);
    if (!referencesMatch) return [];

    const referencesText = referencesMatch[1];
    const referenceLines = referencesText.split('\n').filter(line => line.trim());

    return referenceLines.map((line, index) => ({
      id: `ref-${index + 1}`,
      text: line.trim(),
      sourceIndex: index < sources.length ? index : -1
    }));
  }

  /**
   * Get available AI models for research
   */
  getAvailableModels(): AIModel[] {
    return [
      {
        id: 'google/gemini-2.0-flash-001',
        name: 'Gemini 2.0 Flash',
        provider: 'Google',
        description: 'Fast and efficient model, excellent for research analysis and synthesis',
        maxTokens: 8192,
        cost: 'low',
        strengths: ['Speed', 'Research synthesis', 'Citation accuracy'],
        bestFor: ['Quick research queries', 'Literature reviews', 'Academic analysis'],
        supportsImages: true
      },
      {
        id: 'anthropic/claude-3-haiku',
        name: 'Claude 3 Haiku',
        provider: 'Anthropic',
        description: 'Balanced model with strong analytical capabilities for research',
        maxTokens: 4096,
        cost: 'low',
        strengths: ['Analysis', 'Academic writing', 'Critical thinking'],
        bestFor: ['Research analysis', 'Academic writing', 'Methodology review'],
        supportsImages: false
      },
      {
        id: 'openai/gpt-4o',
        name: 'GPT-4o',
        provider: 'OpenAI',
        description: 'Advanced model with excellent research comprehension',
        maxTokens: 4096,
        cost: 'medium',
        strengths: ['Comprehension', 'Research synthesis', 'Academic rigor'],
        bestFor: ['Complex research queries', 'Multi-source analysis', 'Academic writing'],
        supportsImages: true
      },
      {
        id: 'anthropic/claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet',
        provider: 'Anthropic',
        description: 'Premium model for complex research and analysis tasks',
        maxTokens: 8192,
        cost: 'high',
        strengths: ['Deep analysis', 'Academic rigor', 'Complex reasoning'],
        bestFor: ['Complex research projects', 'Systematic reviews', 'Advanced analysis'],
        supportsImages: true
      },
      {
        id: 'google/gemini-2.5-flash-preview-05-20',
        name: 'Gemini 2.5 Flash Preview',
        provider: 'Google',
        description: 'Latest preview model with enhanced research capabilities',
        maxTokens: 8192,
        cost: 'medium',
        strengths: ['Latest features', 'Research synthesis', 'Academic analysis'],
        bestFor: ['Cutting-edge research', 'Advanced queries', 'Multi-modal analysis'],
        supportsImages: true
      },
      {
        id: 'deepseek/deepseek-r1-0528',
        name: 'DeepSeek R1',
        provider: 'DeepSeek',
        description: 'Advanced reasoning model with strong analytical capabilities for research',
        maxTokens: 8192,
        cost: 'medium',
        strengths: ['Deep reasoning', 'Academic analysis', 'Research synthesis'],
        bestFor: ['Complex research problems', 'Academic writing', 'Analytical tasks'],
        supportsImages: false
      },
      {
        id: 'deepseek/deepseek-chat-v3-0324',
        name: 'DeepSeek Chat V3',
        provider: 'DeepSeek',
        description: 'Conversational model optimized for research discussions and analysis',
        maxTokens: 4096,
        cost: 'low',
        strengths: ['Research dialogue', 'Academic discussion', 'Citation accuracy'],
        bestFor: ['Research conversations', 'Academic Q&A', 'Literature analysis'],
        supportsImages: false
      },
      {
        id: 'moonshotai/kimi-k2',
        name: 'Kimi K2',
        provider: 'Moonshot AI',
        description: 'High-performance model with excellent research comprehension and synthesis',
        maxTokens: 8192,
        cost: 'medium',
        strengths: ['Research comprehension', 'Academic synthesis', 'Multi-language support'],
        bestFor: ['International research', 'Cross-cultural analysis', 'Academic translation'],
        supportsImages: false
      }
    ];
  }

  /**
   * Suggest research improvements
   */
  async suggestResearchImprovements(query: string, model: string = 'google/gemini-2.0-flash-001'): Promise<string[]> {
    try {
      const prompt = `Analyze this research query and suggest 5 ways to improve or expand it for better academic research results: "${query}"

      Provide suggestions that would help find more comprehensive, relevant, and high-quality academic sources.`;

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Research Search Platform'
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.8
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get research suggestions');
      }

      const data = await response.json();
      const suggestions = data.choices?.[0]?.message?.content || '';
      
      // Parse suggestions (assuming they're in a list format)
      return suggestions.split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .slice(0, 5);

    } catch (error) {
      console.error('Error getting research suggestions:', error);
      return [
        'Add specific time periods or date ranges',
        'Include methodology keywords (systematic review, meta-analysis)',
        'Specify research domains or disciplines',
        'Add geographic or demographic constraints',
        'Include outcome measures or variables of interest'
      ];
    }
  }

  /**
   * Determine if a source is academic/peer-reviewed
   */
  private isAcademicSource(url: string, title: string): boolean {
    const academicDomains = [
      'pubmed.ncbi.nlm.nih.gov',
      'scholar.google.com',
      'arxiv.org',
      'researchgate.net',
      'academia.edu',
      'jstor.org',
      'springer.com',
      'sciencedirect.com',
      'wiley.com',
      'nature.com',
      'science.org',
      'plos.org',
      'bmj.com',
      'nejm.org',
      'thelancet.com',
      'cell.com',
      'nih.gov',
      'ncbi.nlm.nih.gov',
      'doi.org',
      'ieee.org',
      'acm.org',
      'apa.org',
      'acs.org',
      'rsc.org',
      'tandfonline.com',
      'sagepub.com',
      'cambridge.org',
      'oxford.org',
      'elsevier.com',
      'frontiersin.org',
      'mdpi.com',
      'hindawi.com',
      'biomedcentral.com'
    ];

    const academicKeywords = [
      'journal',
      'research',
      'study',
      'analysis',
      'peer-reviewed',
      'systematic review',
      'meta-analysis',
      'clinical trial',
      'proceedings',
      'conference',
      'university',
      'academic',
      'scholarly',
      'peer review',
      'research article',
      'scientific study'
    ];

    // Check domain
    const domain = url.toLowerCase();
    if (academicDomains.some(acadDomain => domain.includes(acadDomain))) {
      return true;
    }

    // Check title for academic keywords
    const titleLower = title.toLowerCase();
    return academicKeywords.some(keyword => titleLower.includes(keyword));
  }

  /**
   * Determine if a source is from government/official sources
   */
  private isGovernmentSource(url: string, title: string): boolean {
    const governmentDomains = [
      '.gov',
      '.edu',
      'who.int',
      'cdc.gov',
      'fda.gov',
      'nih.gov',
      'nsf.gov',
      'nasa.gov',
      'epa.gov',
      'usda.gov',
      'census.gov',
      'bls.gov',
      'treasury.gov',
      'state.gov',
      'justice.gov',
      'dhs.gov',
      'energy.gov',
      'hhs.gov',
      'va.gov',
      'oecd.org',
      'worldbank.org',
      'un.org',
      'europa.eu',
      'ec.europa.eu'
    ];

    const governmentKeywords = [
      'government',
      'federal',
      'state',
      'department',
      'agency',
      'bureau',
      'administration',
      'commission',
      'ministry',
      'official',
      'policy',
      'regulation',
      'legislation',
      'congressional',
      'parliamentary'
    ];

    // Check domain
    const domain = url.toLowerCase();
    if (governmentDomains.some(govDomain => domain.includes(govDomain))) {
      return true;
    }

    // Check title for government keywords
    const titleLower = title.toLowerCase();
    return governmentKeywords.some(keyword => titleLower.includes(keyword));
  }

  /**
   * Extract author information from source (enhanced for real authors)
   */
  private extractAuthorFromSource(source: any): string {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    // Enhanced author patterns to find real authors, not website names
    const authorPatterns = [
      // Standard academic patterns
      /(?:by|author[s]?:|written by)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+)+)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.(?:\s*[A-Z]\.)*(?:\s*,?\s*&?\s*[A-Z][a-z]+,?\s+[A-Z]\.)*(?:\s+et\s+al\.?)?/,
      /([A-Z][a-z]+\s+et\s+al\.?)/i,
      // First author in citation format
      /([A-Z][a-z]+),?\s+[A-Z]\.?\s*(?:\([0-9]{4}\))?/,
      // Author in academic format
      /^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\(/,
      // Multiple authors
      /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*,?\s*(?:&|and)\s*[A-Z][a-z]+/i
    ];

    for (const pattern of authorPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        const author = match[1].trim();
        // Avoid website names
        if (!this.isWebsiteName(author)) {
          return this.formatAuthorName(author);
        }
      }
    }

    // For government sources, extract organization
    if (this.isGovernmentSource(source.url, source.title)) {
      return this.extractOrganizationFromSource(source);
    }

    // Last resort: use "Anonymous" instead of website name
    return 'Anonymous';
  }

  /**
   * Check if a name is likely a website/platform name
   */
  private isWebsiteName(name: string): boolean {
    const websiteNames = [
      'researchgate', 'sciencedirect', 'springer', 'nature', 'science',
      'pubmed', 'arxiv', 'jstor', 'wiley', 'elsevier', 'google',
      'scholar', 'academia', 'frontiers', 'mdpi', 'plos'
    ];

    return websiteNames.some(site =>
      name.toLowerCase().includes(site) ||
      name.toLowerCase() === site
    );
  }

  /**
   * Format author name properly
   */
  private formatAuthorName(name: string): string {
    // Handle "et al." cases
    if (name.toLowerCase().includes('et al')) {
      return name;
    }

    // Clean up the name
    const cleanName = name.replace(/[^\w\s\.]/g, '').trim();

    // If it's a single word or very short, return as is
    if (cleanName.split(' ').length === 1 || cleanName.length < 3) {
      return cleanName;
    }

    return cleanName;
  }

  /**
   * Extract DOI from source
   */
  private extractDOIFromSource(source: any): string | null {
    const content = source.content || '';
    const url = source.url || '';
    const title = source.title || '';
    const fullText = `${title} ${content} ${url}`;

    // DOI patterns
    const doiPatterns = [
      /doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /doi:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /https?:\/\/doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /DOI:\s*([0-9]+\.[0-9]+\/[^\s]+)/i
    ];

    for (const pattern of doiPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Extract journal name from source
   */
  private extractJournalFromSource(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    // Journal patterns
    const journalPatterns = [
      /published in\s+([A-Z][a-zA-Z\s&]+(?:Journal|Review|Letters|Proceedings)[a-zA-Z\s]*)/i,
      /([A-Z][a-zA-Z\s&]+(?:Journal|Review|Letters|Proceedings)[a-zA-Z\s]*)/i,
      /(Nature|Science|Cell|PNAS|BMJ|NEJM|Lancet|JAMA|PLoS\s+\w+)/i,
      /Journal of\s+([A-Z][a-zA-Z\s&]+)/i,
      /([A-Z][a-zA-Z\s]+)\s+Journal/i
    ];

    for (const pattern of journalPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        const journal = match[1] || match[0];
        // Avoid website names
        if (!this.isWebsiteName(journal)) {
          return journal.trim();
        }
      }
    }

    return null;
  }

  /**
   * Extract organization from government/official sources
   */
  private extractOrganizationFromSource(source: any): string {
    const url = source.url || '';
    const title = source.title || '';
    const content = source.content || '';

    // Government organization mapping
    const orgMap: { [key: string]: string } = {
      'nih.gov': 'National Institutes of Health',
      'cdc.gov': 'Centers for Disease Control and Prevention',
      'fda.gov': 'Food and Drug Administration',
      'who.int': 'World Health Organization',
      'nasa.gov': 'National Aeronautics and Space Administration',
      'epa.gov': 'Environmental Protection Agency',
      'nsf.gov': 'National Science Foundation',
      'usda.gov': 'U.S. Department of Agriculture',
      'energy.gov': 'U.S. Department of Energy',
      'hhs.gov': 'U.S. Department of Health and Human Services',
      'oecd.org': 'Organisation for Economic Co-operation and Development',
      'worldbank.org': 'World Bank',
      'un.org': 'United Nations'
    };

    // Check URL for organization
    for (const [domain, orgName] of Object.entries(orgMap)) {
      if (url.includes(domain)) {
        return orgName;
      }
    }

    // Extract from title or content
    const orgPatterns = [
      /(Centers? for Disease Control)/i,
      /(World Health Organization)/i,
      /(National Institutes? of Health)/i,
      /(Food and Drug Administration)/i,
      /(Environmental Protection Agency)/i,
      /(Department of [A-Z][a-zA-Z\s]+)/i,
      /(Ministry of [A-Z][a-zA-Z\s]+)/i
    ];

    for (const pattern of orgPatterns) {
      const match = title.match(pattern) || content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    // Fallback to domain-based extraction
    const domain = url.split('/')[2] || '';
    const domainParts = domain.split('.');
    if (domainParts.length >= 2) {
      const orgName = domainParts[domainParts.length - 2];
      return orgName.charAt(0).toUpperCase() + orgName.slice(1);
    }

    return 'Government Agency';
  }

  /**
   * Extract domain name for warnings
   */
  private extractDomainName(url: string): string {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return domain.split('.')[0].charAt(0).toUpperCase() + domain.split('.')[0].slice(1);
    } catch {
      return 'Website';
    }
  }

  /**
   * Extract volume information from source
   */
  private extractVolumeFromSource(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const volumePatterns = [
      /volume\s*(\d+)/i,
      /vol\.?\s*(\d+)/i,
      /v\.?\s*(\d+)/i,
      /\s(\d+)\s*\(\d+\)/,  // Pattern like "45(2)"
      /,\s*(\d+)\s*\(/      // Pattern like ", 45("
    ];

    for (const pattern of volumePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Extract issue information from source
   */
  private extractIssueFromSource(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const issuePatterns = [
      /issue\s*(\d+)/i,
      /no\.?\s*(\d+)/i,
      /number\s*(\d+)/i,
      /\((\d+)\)/,          // Pattern like "(2)"
      /vol\.?\s*\d+\s*\((\d+)\)/i  // Pattern like "vol. 45(2)"
    ];

    for (const pattern of issuePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Extract page information from source
   */
  private extractPagesFromSource(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const pagePatterns = [
      /pages?\s*(\d+[-–]\d+)/i,
      /pp\.?\s*(\d+[-–]\d+)/i,
      /p\.?\s*(\d+[-–]\d+)/i,
      /(\d+)[-–](\d+)/,     // Pattern like "123-145"
      /,\s*(\d+[-–]\d+)\./  // Pattern like ", 123-145."
    ];

    for (const pattern of pagePatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1] || `${match[1]}-${match[2]}`;
      }
    }

    return null;
  }

  /**
   * Extract publication year from source
   */
  private extractYearFromSource(source: any): string {
    // Try published_date first
    if (source.published_date) {
      const year = new Date(source.published_date).getFullYear();
      if (year && year > 1900 && year <= new Date().getFullYear()) {
        return year.toString();
      }
    }

    // Look for year patterns in title and content
    const content = (source.content || '') + ' ' + (source.title || '');
    const yearPatterns = [
      /\b(20[0-2][0-9])\b/g,
      /\b(19[8-9][0-9])\b/g,
      /\((\d{4})\)/g
    ];

    for (const pattern of yearPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        // Get the most recent year found
        const years = matches.map(match => parseInt(match.replace(/[()]/g, '')))
          .filter(year => year > 1900 && year <= new Date().getFullYear())
          .sort((a, b) => b - a);

        if (years.length > 0) {
          return years[0].toString();
        }
      }
    }

    // Fallback to current year
    return new Date().getFullYear().toString();
  }
}

export const researchAIService = new ResearchAIService();
