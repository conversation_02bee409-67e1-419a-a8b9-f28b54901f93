/**
 * Reference Collection Service
 * Collects all citations from generated sections and formats them properly
 */

import { CitationSource } from './enhanced-citation-search.service';
import { citationValidationService } from './citation-validation.service';
import { referenceValidationAIService, ValidatedCitation } from './reference-validation-ai.service';

export interface CollectedReference {
  id: string;
  formattedCitation: string;
  authors: string[];
  year: number;
  title: string;
  journal?: string;
  doi?: string;
  url: string;
  sectionSource: string;
  relevanceScore: number;
}

export interface ReferenceCollectionResult {
  references: CollectedReference[];
  formattedReferencesText: string;
  statistics: {
    totalReferences: number;
    uniqueAuthors: <AUTHORS>
    averageRelevance: number;
    sectionBreakdown: { [sectionId: string]: number };
  };
}

export class ReferenceCollectionService {
  /**
   * Collect and format all references from enhanced citations
   */
  async collectReferencesFromSections(
    enhancedCitations: Record<string, CitationSource[]>
  ): Promise<ReferenceCollectionResult> {
    console.log('Collecting references from all sections...');
    
    const allCitations: CitationSource[] = [];
    const sectionBreakdown: { [sectionId: string]: number } = {};
    
    // Collect all citations from all sections
    Object.entries(enhancedCitations).forEach(([sectionId, citations]) => {
      allCitations.push(...citations);
      sectionBreakdown[sectionId] = citations.length;
      console.log(`Section ${sectionId}: ${citations.length} citations`);
    });
    
    // Remove duplicates and validate
    const { valid: validCitations } = citationValidationService.validateAndCleanCitations(allCitations);
    console.log(`Validated ${validCitations.length} unique citations from ${allCitations.length} total`);
    
    // Convert to collected references
    const collectedReferences: CollectedReference[] = validCitations.map(citation => ({
      id: citation.id,
      formattedCitation: this.formatReferenceForCollection(citation),
      authors: citation.authors,
      year: citation.year,
      title: citation.title,
      journal: citation.journal,
      doi: citation.doi,
      url: citation.url,
      sectionSource: this.findSectionSource(citation, enhancedCitations),
      relevanceScore: citation.relevanceScore
    }));
    
    // Sort alphabetically by first author
    collectedReferences.sort((a, b) => {
      const authorA = a.authors[0]?.split(',')[0] || '';
      const authorB = b.authors[0]?.split(',')[0] || '';
      return authorA.localeCompare(authorB);
    });
    
    // Generate formatted text
    const formattedText = this.generateFormattedReferencesText(collectedReferences);
    
    // Calculate statistics
    const statistics = this.calculateStatistics(collectedReferences, sectionBreakdown);
    
    console.log(`Reference collection complete: ${collectedReferences.length} references`);
    
    return {
      references: collectedReferences,
      formattedReferencesText: formattedText,
      statistics
    };
  }

  /**
   * Collect and validate references with AI for enhanced accuracy
   */
  async collectAndValidateReferencesWithAI(
    enhancedCitations: Record<string, CitationSource[]>,
    model: string = "google/gemini-2.5-flash-preview-05-20"
  ): Promise<ReferenceCollectionResult> {
    console.log('Collecting and validating references with AI...');

    const allCitations: CitationSource[] = [];
    const sectionBreakdown: { [sectionId: string]: number } = {};

    // Collect all citations from all sections
    Object.entries(enhancedCitations).forEach(([sectionId, citations]) => {
      allCitations.push(...citations);
      sectionBreakdown[sectionId] = citations.length;
      console.log(`Section ${sectionId}: ${citations.length} citations`);
    });

    if (allCitations.length === 0) {
      console.log('No citations found to validate');
      return {
        references: [],
        formattedReferencesText: '<p>No references were found in the generated sections.</p>',
        statistics: {
          totalReferences: 0,
          uniqueAuthors: <AUTHORS>
          averageRelevance: 0,
          sectionBreakdown
        }
      };
    }

    try {
      // Use AI to validate and format references
      console.log(`Starting AI validation for ${allCitations.length} citations...`);
      const validationResult = await referenceValidationAIService.validateAndFormatReferences(
        allCitations,
        model
      );

      console.log(`AI validation completed: ${validationResult.validationReport.validCitations}/${validationResult.validationReport.totalCitations} valid`);

      // Convert validated citations to collected references
      const collectedReferences: CollectedReference[] = validationResult.validatedCitations.map(vc => ({
        id: vc.id,
        formattedCitation: vc.formattedCitation,
        authors: vc.extractedInfo.authors,
        year: vc.extractedInfo.year,
        title: vc.extractedInfo.title,
        journal: vc.extractedInfo.journal,
        doi: vc.extractedInfo.doi,
        url: vc.extractedInfo.url,
        sectionSource: this.findSectionSource(vc.originalCitation, enhancedCitations),
        relevanceScore: vc.validationScore
      }));

      // Calculate enhanced statistics
      const statistics = this.calculateEnhancedStatistics(collectedReferences, sectionBreakdown, validationResult.validationReport);

      return {
        references: collectedReferences,
        formattedReferencesText: validationResult.formattedReferences,
        statistics
      };

    } catch (error) {
      console.error('AI reference validation failed:', error);
      console.log('Falling back to regular collection...');

      // Fallback to regular collection
      return this.collectReferencesFromSections(enhancedCitations);
    }
  }

  /**
   * Calculate enhanced statistics including AI validation metrics
   */
  private calculateEnhancedStatistics(
    references: CollectedReference[],
    sectionBreakdown: { [sectionId: string]: number },
    validationReport: any
  ): ReferenceCollectionResult['statistics'] {
    const totalReferences = references.length;
    const uniqueAuthors = new Set(
      references.flatMap(ref => ref.authors.map(author => author.toLowerCase()))
    ).size;
    const averageRelevance = totalReferences > 0
      ? references.reduce((sum, ref) => sum + ref.relevanceScore, 0) / totalReferences
      : 0;

    return {
      totalReferences,
      uniqueAuthors,
      averageRelevance,
      sectionBreakdown: {
        ...sectionBreakdown,
        validationScore: Math.round(validationReport.averageQuality * 100),
        highQualityRefs: validationReport.validCitations,
        issuesFound: validationReport.issuesFound
      }
    };
  }
  
  /**
   * Format a single reference for collection
   */
  private formatReferenceForCollection(citation: CitationSource): string {
    // Use the citation validation service for consistent formatting
    return citationValidationService.formatCitation(citation, {
      style: 'apa',
      includeLinks: false,
      includeDOI: true,
      maxAuthors: 3
    });
  }
  
  /**
   * Find which section a citation came from
   */
  private findSectionSource(
    citation: CitationSource, 
    enhancedCitations: Record<string, CitationSource[]>
  ): string {
    for (const [sectionId, citations] of Object.entries(enhancedCitations)) {
      if (citations.some(c => c.id === citation.id)) {
        return sectionId;
      }
    }
    return 'unknown';
  }
  
  /**
   * Generate formatted references text for display
   */
  private generateFormattedReferencesText(references: CollectedReference[]): string {
    if (references.length === 0) {
      return '<p>No references were found in the generated sections.</p>';
    }
    
    let formattedText = '';
    
    // Add each reference as a paragraph
    references.forEach((ref, index) => {
      formattedText += `<p>${ref.formattedCitation}</p>\n`;
    });
    
    // Add statistics footer
    const totalRefs = references.length;
    const avgRelevance = Math.round(
      references.reduce((sum, ref) => sum + ref.relevanceScore, 0) / totalRefs * 100
    );
    const uniqueAuthors = new Set(
      references.flatMap(ref => ref.authors.map(author => author.toLowerCase()))
    ).size;
    
    formattedText += `\n<p><em>Reference Statistics: ${totalRefs} total references | ${uniqueAuthors} unique authors | Average relevance: ${avgRelevance}% | All sources verified with DOIs</em></p>`;
    
    return formattedText.trim();
  }
  
  /**
   * Calculate comprehensive statistics
   */
  private calculateStatistics(
    references: CollectedReference[], 
    sectionBreakdown: { [sectionId: string]: number }
  ): ReferenceCollectionResult['statistics'] {
    const totalReferences = references.length;
    const uniqueAuthors = new Set(
      references.flatMap(ref => ref.authors.map(author => author.toLowerCase()))
    ).size;
    const averageRelevance = totalReferences > 0 
      ? references.reduce((sum, ref) => sum + ref.relevanceScore, 0) / totalReferences
      : 0;
    
    return {
      totalReferences,
      uniqueAuthors,
      averageRelevance,
      sectionBreakdown
    };
  }
  
  /**
   * Generate a detailed reference report
   */
  generateReferenceReport(result: ReferenceCollectionResult): string {
    const { references, statistics } = result;
    
    let report = `# Reference Collection Report\n\n`;
    report += `## Statistics\n`;
    report += `- Total References: ${statistics.totalReferences}\n`;
    report += `- Unique Authors: <AUTHORS>
    report += `- Average Relevance: ${Math.round(statistics.averageRelevance * 100)}%\n\n`;
    
    report += `## Section Breakdown\n`;
    Object.entries(statistics.sectionBreakdown).forEach(([section, count]) => {
      report += `- ${section}: ${count} references\n`;
    });
    
    report += `\n## Quality Assessment\n`;
    const highQuality = references.filter(ref => ref.relevanceScore > 0.7).length;
    const mediumQuality = references.filter(ref => ref.relevanceScore > 0.4 && ref.relevanceScore <= 0.7).length;
    const lowQuality = references.filter(ref => ref.relevanceScore <= 0.4).length;
    
    report += `- High Quality (>70%): ${highQuality} references\n`;
    report += `- Medium Quality (40-70%): ${mediumQuality} references\n`;
    report += `- Low Quality (<40%): ${lowQuality} references\n`;
    
    return report;
  }
  
  /**
   * Export references in different formats
   */
  exportReferences(references: CollectedReference[], format: 'bibtex' | 'endnote' | 'ris'): string {
    switch (format) {
      case 'bibtex':
        return this.exportToBibTeX(references);
      case 'endnote':
        return this.exportToEndNote(references);
      case 'ris':
        return this.exportToRIS(references);
      default:
        return '';
    }
  }
  
  /**
   * Export to BibTeX format
   */
  private exportToBibTeX(references: CollectedReference[]): string {
    return references.map(ref => {
      const key = `${ref.authors[0]?.split(',')[0] || 'Unknown'}${ref.year}`;
      let bibtex = `@article{${key},\n`;
      bibtex += `  title={${ref.title}},\n`;
      bibtex += `  author={${ref.authors.join(' and ')}},\n`;
      bibtex += `  year={${ref.year}},\n`;
      if (ref.journal) bibtex += `  journal={${ref.journal}},\n`;
      if (ref.doi) bibtex += `  doi={${ref.doi}},\n`;
      bibtex += `  url={${ref.url}}\n`;
      bibtex += `}\n`;
      return bibtex;
    }).join('\n');
  }
  
  /**
   * Export to EndNote format
   */
  private exportToEndNote(references: CollectedReference[]): string {
    return references.map(ref => {
      let endnote = `%0 Journal Article\n`;
      endnote += `%T ${ref.title}\n`;
      ref.authors.forEach(author => {
        endnote += `%A ${author}\n`;
      });
      endnote += `%D ${ref.year}\n`;
      if (ref.journal) endnote += `%J ${ref.journal}\n`;
      if (ref.doi) endnote += `%R ${ref.doi}\n`;
      endnote += `%U ${ref.url}\n`;
      return endnote;
    }).join('\n');
  }
  
  /**
   * Export to RIS format
   */
  private exportToRIS(references: CollectedReference[]): string {
    return references.map(ref => {
      let ris = `TY  - JOUR\n`;
      ris += `TI  - ${ref.title}\n`;
      ref.authors.forEach(author => {
        ris += `AU  - ${author}\n`;
      });
      ris += `PY  - ${ref.year}\n`;
      if (ref.journal) ris += `JO  - ${ref.journal}\n`;
      if (ref.doi) ris += `DO  - ${ref.doi}\n`;
      ris += `UR  - ${ref.url}\n`;
      ris += `ER  - \n`;
      return ris;
    }).join('\n');
  }
}

export const referenceCollectionService = new ReferenceCollectionService();
