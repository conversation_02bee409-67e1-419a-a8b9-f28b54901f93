/* Ensure the editor content is never affected by the sidebar */
.editor-content {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden;
}

/* Ensure proper layout that doesn't change with sidebar state */
.editor-container {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  margin-right: 0 !important; /* Never adjust margin for sidebar */
}

/* Ensure consistent editor area regardless of sidebar */
.prose {
  max-width: none !important;
  width: 100% !important;
}

/* Main editor container should never be affected by sidebar positioning */
.main-editor-layout {
  position: relative;
  width: 100%;
  min-height: 100vh;
  /* Never adjust for sidebar - sidebar is completely overlay */
}

/* Enhanced editor stability - prevent layout shifts */
.editor-main-content {
  position: relative !important;
  width: 100% !important;
  transition: none !important; /* Disable any transitions that might cause jumping */
}

/* Prevent toolbar from causing layout shifts */
.editor-toolbar {
  position: sticky !important;
  top: 0 !important;
  z-index: 40 !important;
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure editor content area maintains consistent positioning */
.editor-content-area {
  position: relative !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  padding: 1.5rem !important;
  transition: none !important; /* Prevent any layout transitions */
}

/* Enhanced AI toggle button styling */
.ai-toggle-button {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.ai-toggle-button:hover {
  background: #f8fafc !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

/* Prevent any interference from sidebar with the editor */
.ProseMirror {
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
  width: 100% !important;
}

/* Ensure the editor focuses correctly */
.ProseMirror:focus {
  outline: none;
}

/* Make sure selection-based features and floating toolbars work properly */
.ProseMirror .ProseMirror-gapcursor:after,
.ProseMirror .ProseMirror-selectednode {
  z-index: 40;
}

/* Fix floating toolbars in the editor - ensure they appear above content but below sidebar */
.floating-menu,
.selection-toolbar,
.ai-action-toolbar {
  position: absolute !important;
  z-index: 45 !important; /* Below sidebar (z-50) but above content */
  background: white !important;
  border-radius: 5px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure resizable images work properly */
.resizable-image-wrapper {
  display: inline-flex;
  position: relative;
  max-width: 100%;
}

/* Fix selection highlighting */
::selection {
  background: rgba(0, 100, 255, 0.2);
}

/* Sidebar overlay styles - completely independent of main content */
.ai-sidebar-overlay {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  height: 100vh !important;
  width: 320px !important; /* Reduced from 400px for more compact design */
  background: transparent;
  z-index: 10000 !important;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.ai-sidebar-overlay.visible {
  transform: translateX(0) !important;
  pointer-events: auto !important;
}

/* Force proper sidebar visibility */
.ai-sidebar-content {
  width: 320px !important; /* Reduced from 400px for more compact design */
  height: 100vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  background: white !important;
  border-left: 1px solid #e5e7eb !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  z-index: 10000 !important;
}

/* Ensure the sidebar content is never clipped or hidden */
.ai-sidebar-content * {
  z-index: inherit !important;
}

/* CLEAN TABS STRUCTURE - CRITICAL FIXES */

/* Main tabs root must take full available height */
.ai-sidebar-content [data-radix-tabs-root] {
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  min-height: 0 !important;
}

/* Tabs list - fixed height, no shrinking */
.ai-sidebar-content [data-radix-tabs-list] {
  flex-shrink: 0 !important;
  height: auto !important;
  min-height: fit-content !important;
}

/* CRITICAL: Each tab content must take remaining space */
.ai-sidebar-content [data-radix-tabs-content] {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  min-height: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Only show active tabs */
.ai-sidebar-content [data-radix-tabs-content][data-state="active"] {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  height: 100% !important;
}

.ai-sidebar-content [data-radix-tabs-content][data-state="inactive"] {
  display: none !important;
}

/* Scroll areas must take full height */
.ai-sidebar-content [data-radix-scroll-area-root] {
  flex: 1 !important;
  height: 100% !important;
  min-height: 0 !important;
  overflow: hidden !important;
}

.ai-sidebar-content [data-radix-scroll-area-viewport] {
  height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Make sure the sidebar never gets smaller than intended */
.ai-sidebar-overlay {
  min-width: 320px !important;
  max-width: 320px !important;
}

.ai-sidebar-content {
  min-width: 320px !important;
  max-width: 320px !important;
}

/* Simple scrolling area rules */

/* Stats grid styling */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

/* Ensure stats content is always at the top */
.stats-content-container > [data-radix-scroll-area-viewport] > div {
  padding-top: 0 !important;
}

/* Remove all the complex conflicting rules and replace with simple, working ones */

/* Clean scrolling rules */

/* Tool cards styling */
.tool-card {
  transition: all 0.2s ease;
}

.tool-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Updated chat input area styling */

/* Loading states */
.ai-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Better visibility for tools section */
.tools-section {
  padding: 1rem;
}

.tools-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* ENHANCED SCROLLING STYLES FOR TABS */

/* Enhanced ScrollArea styling for better scrolling */
[data-radix-scroll-area-root] {
  flex: 1 !important;
  overflow: hidden !important;
  min-height: 0 !important;
  height: 100% !important;
}

[data-radix-scroll-area-viewport] {
  height: 100% !important;
  width: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* ENHANCED SCROLLING - GENERAL */

/* Enhanced tools scrolling */
.tools-list-container [data-radix-scroll-area-viewport] {
  padding-right: 8px; /* Add space for scrollbar */
}

/* Enhanced stats scrolling */
.stats-content-container [data-radix-scroll-area-viewport] {
  padding-right: 8px; /* Add space for scrollbar */
}

/* Custom scrollbar styling */
[data-radix-scroll-area-scrollbar] {
  display: flex !important;
  user-select: none;
  touch-action: none;
  padding: 2px;
  background: rgba(0, 0, 0, 0.05);
  transition: background 160ms ease-out;
  width: 12px;
}

[data-radix-scroll-area-scrollbar]:hover {
  background: rgba(0, 0, 0, 0.08);
}

[data-radix-scroll-area-thumb] {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  position: relative;
}

[data-radix-scroll-area-thumb]:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Clean tab content structure */

/* SIMPLIFIED CONTAINER RULES */
.tools-list-container,
.stats-content-container {
  flex: 1 !important;
  min-height: 0 !important;
  overflow: hidden !important;
}

/* FIXED CONTAINER HEIGHTS */
.tools-filter-container {
  flex-shrink: 0 !important;
}

/* FINAL CRITICAL FIXES FOR FULL HEIGHT DISPLAY */

/* Ensure all tabs use full available height */
.ai-sidebar-content .tools-tab-content,
.ai-sidebar-content .stats-tab-content {
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure header doesn't grow beyond needed space */
.ai-sidebar-content .flex-shrink-0 {
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
}

/* Force scroll areas to take full remaining height */
.ai-sidebar-content .flex-1.overflow-hidden {
  flex: 1 !important;
  height: 100% !important;
  min-height: 0 !important;
}

/* Ensure no content gets cut off */
.ai-sidebar-content .px-4.py-4.space-y-4,
.ai-sidebar-content .p-4.space-y-6 {
  min-height: fit-content !important;
}

/* Fix for tools grid to show properly */
.stats-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 1rem !important;
  width: 100% !important;
}

/* Ensure tools list shows completely */
.ai-sidebar-content .space-y-4 > * {
  flex-shrink: 0 !important;
}

/* Final height enforcement */
.ai-sidebar-content {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  max-height: 100vh !important;
  overflow: hidden !important;
}

/* Enhanced smooth animations */
.ai-sidebar-overlay {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Improved tool cards for compact sidebar */
.tool-card {
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
  padding: 0.5rem !important;
}

.tool-card:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  background: #f8fafc !important;
}

/* Compact button styling for reduced width */
.compact-button {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}

/* Enhanced scrollbar for compact design */
[data-radix-scroll-area-scrollbar] {
  width: 8px !important; /* Reduced from 12px for compact design */
}

/* Responsive adjustments for smaller sidebar */
@media (max-width: 1024px) {
  .ai-sidebar-overlay,
  .ai-sidebar-content {
    width: 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
  }
}

/* Fix for AI model dropdown z-index issues */
/* Ensure all dropdowns and select components appear above everything else */
[data-radix-select-content] {
  z-index: 10001 !important;
  position: fixed !important;
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow: 0 10px 38px -10px rgba(22, 23, 24, 0.35), 0 10px 20px -15px rgba(22, 23, 24, 0.2) !important;
}

[data-radix-select-viewport] {
  z-index: 10001 !important;
  background: white !important;
}

[data-radix-select-item] {
  z-index: 10001 !important;
  background: white !important;
}

[data-radix-select-item]:hover,
[data-radix-select-item][data-highlighted] {
  background: #f1f5f9 !important;
}

/* Ensure the select trigger is properly positioned */
[data-radix-select-trigger] {
  position: relative !important;
  z-index: 1 !important;
}

/* Force all select components in AI sidebar to have highest z-index */
.ai-sidebar-content [data-radix-select-content] {
  z-index: 10002 !important;
}

.ai-sidebar-content [data-radix-select-viewport] {
  z-index: 10002 !important;
}

.ai-sidebar-content [data-radix-select-item] {
  z-index: 10002 !important;
}
