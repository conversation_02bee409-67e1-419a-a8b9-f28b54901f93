import { useState, useEffect, useCallback } from 'react';
import { changeTrackingService } from './ChangeTrackingService';
import { ChangeTrackingState, ChangeNavigationState } from './types';

export function useChangeTracking() {
  const [state, setState] = useState<ChangeTrackingState>(changeTrackingService.getState());
  const [navigationState, setNavigationState] = useState<ChangeNavigationState>(
    changeTrackingService.getNavigationState()
  );

  useEffect(() => {
    const unsubscribe = changeTrackingService.subscribe((newState) => {
      setState(newState);
      setNavigationState(changeTrackingService.getNavigationState());
    });

    return unsubscribe;
  }, []);

  // Start tracking with initial content
  const startTracking = useCallback((initialContent: string) => {
    changeTrackingService.startTracking(initialContent);
  }, []);

  // Stop tracking
  const stopTracking = useCallback(() => {
    changeTrackingService.stopTracking();
  }, []);

  // Record a change from AI modification
  const recordChange = useCallback((
    originalText: string,
    newText: string,
    startPosition: number,
    endPosition: number,
    aiActionType: 'replace' | 'insert' | 'display',
    prompt?: string
  ) => {
    return changeTrackingService.recordChange(
      originalText,
      newText,
      startPosition,
      endPosition,
      aiActionType,
      prompt
    );
  }, []);

  // Accept a specific change
  const acceptChange = useCallback((changeId: string) => {
    changeTrackingService.acceptChange(changeId);
  }, []);

  // Reject a specific change
  const rejectChange = useCallback((changeId: string) => {
    const originalText = changeTrackingService.rejectChange(changeId);
    return originalText;
  }, []);

  // Accept all changes
  const acceptAllChanges = useCallback(() => {
    changeTrackingService.acceptAllChanges();
  }, []);

  // Reject all changes
  const rejectAllChanges = useCallback(() => {
    const originalContent = changeTrackingService.rejectAllChanges();
    return originalContent;
  }, []);

  // Navigate between changes
  const navigateToNextChange = useCallback(() => {
    return changeTrackingService.navigateToNextChange();
  }, []);

  const navigateToPreviousChange = useCallback(() => {
    return changeTrackingService.navigateToPreviousChange();
  }, []);

  // Toggle view mode
  const toggleViewMode = useCallback(() => {
    changeTrackingService.toggleViewMode();
  }, []);

  // Update current content
  const updateCurrentContent = useCallback((content: string) => {
    changeTrackingService.updateCurrentContent(content);
  }, []);

  // Get current change
  const getCurrentChange = useCallback(() => {
    return changeTrackingService.getCurrentChange();
  }, []);

  // Check for pending changes
  const hasPendingChanges = useCallback(() => {
    return changeTrackingService.hasPendingChanges();
  }, []);

  // Get pending changes count
  const getPendingChangesCount = useCallback(() => {
    return changeTrackingService.getPendingChangesCount();
  }, []);

  // Generate diff data
  const generateDiffData = useCallback(() => {
    return changeTrackingService.generateDiffData();
  }, []);

  // Section-based operations
  const toggleSectionSelection = useCallback((sectionId: string) => {
    changeTrackingService.toggleSectionSelection(sectionId);
  }, []);

  const selectAllSections = useCallback(() => {
    changeTrackingService.selectAllSections();
  }, []);

  const clearSectionSelection = useCallback(() => {
    changeTrackingService.clearSectionSelection();
  }, []);

  const acceptSelectedSections = useCallback(() => {
    changeTrackingService.acceptSelectedSections();
  }, []);

  const rejectSelectedSections = useCallback(() => {
    changeTrackingService.rejectSelectedSections();
  }, []);

  const getSelectedSections = useCallback(() => {
    return changeTrackingService.getSelectedSections();
  }, []);

  const isSectionSelected = useCallback((sectionId: string) => {
    return changeTrackingService.isSectionSelected(sectionId);
  }, []);

  return {
    // State
    state,
    navigationState,

    // Actions
    startTracking,
    stopTracking,
    recordChange,
    acceptChange,
    rejectChange,
    acceptAllChanges,
    rejectAllChanges,
    navigateToNextChange,
    navigateToPreviousChange,
    toggleViewMode,
    updateCurrentContent,

    // Section-based actions
    toggleSectionSelection,
    selectAllSections,
    clearSectionSelection,
    acceptSelectedSections,
    rejectSelectedSections,

    // Getters
    getCurrentChange,
    hasPendingChanges,
    getPendingChangesCount,
    generateDiffData,
    getSelectedSections,
    isSectionSelected,

    // Computed values
    isTrackingEnabled: state.isTrackingEnabled,
    viewMode: state.viewMode,
    pendingChangesCount: changeTrackingService.getPendingChangesCount(),
    hasChanges: changeTrackingService.hasPendingChanges(),
    selectedSections: changeTrackingService.getSelectedSections(),
    selectionMode: state.selectionMode
  };
}
