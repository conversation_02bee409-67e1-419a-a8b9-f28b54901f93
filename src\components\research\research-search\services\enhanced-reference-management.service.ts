/**
 * Enhanced Reference Management Service
 * Advanced citation tracking, DOI extraction, and academic formatting
 */

import { 
  ConsolidatedReference, 
  InTextCitation, 
  EnhancedSearchSource,
  SearchSource,
  Citation,
  DeepResearchSession
} from '../types';

export interface EnhancedReference extends ConsolidatedReference {
  doi?: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  authors: string[];
  institutionAffiliation?: string;
  impactFactor?: number;
  citationCount?: number;
  qualityAssessment: {
    overallScore: number;
    academicCredibility: number;
    recency: number;
    relevance: number;
    methodologicalRigor: number;
  };
  extractedMetadata: {
    abstract?: string;
    keywords: string[];
    methodology?: string;
    sampleSize?: string;
    studyType?: string;
    limitations?: string[];
  };
}

export class EnhancedReferenceManagementService {
  private references: Map<string, EnhancedReference> = new Map();
  private citationCounter = 0;
  private doiCache: Map<string, any> = new Map(); // Cache for DOI lookups

  /**
   * Add an enhanced source with comprehensive metadata extraction
   */
  addEnhancedSource(source: EnhancedSearchSource, sectionId: string): EnhancedReference {
    const citationKey = this.generateEnhancedCitationKey(source);
    
    let reference = this.references.get(citationKey);
    
    if (!reference) {
      reference = {
        id: `ref_${Date.now()}_${this.citationCounter++}`,
        citationKey,
        formattedCitation: this.formatEnhancedReference(source),
        inTextCitations: [],
        source: source,
        usageCount: 0,
        sections: [],
        doi: this.extractDOI(source),
        journal: this.extractJournal(source),
        volume: this.extractVolume(source),
        issue: this.extractIssue(source),
        pages: this.extractPages(source),
        authors: this.extractAuthors(source),
        institutionAffiliation: source.institutionAffiliation,
        impactFactor: source.impactFactor,
        citationCount: source.citationCount,
        qualityAssessment: this.assessReferenceQuality(source),
        extractedMetadata: this.extractMetadata(source)
      };
      this.references.set(citationKey, reference);
    }
    
    if (!reference.sections.includes(sectionId)) {
      reference.sections.push(sectionId);
    }
    
    return reference;
  }

  /**
   * Generate comprehensive bibliography with enhanced formatting
   */
  generateEnhancedBibliography(
    citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard' = 'apa',
    includeQualityMetrics: boolean = true
  ): string {
    const sortedReferences = Array.from(this.references.values())
      .filter(ref => ref.usageCount > 0)
      .sort((a, b) => {
        // Sort by quality score first, then alphabetically
        const qualityDiff = b.qualityAssessment.overallScore - a.qualityAssessment.overallScore;
        if (Math.abs(qualityDiff) > 0.1) return qualityDiff;
        
        const aAuthor = a.authors[0] || this.extractFirstAuthor(a.source.title);
        const bAuthor = b.authors[0] || this.extractFirstAuthor(b.source.title);
        return aAuthor.localeCompare(bAuthor);
      });

    let bibliography = '# References\n\n';

    // Group references by type and quality
    const academicRefs = sortedReferences.filter(r => r.source.isPeerReviewed);
    const governmentRefs = sortedReferences.filter(r => r.source.isGovernment && !r.source.isPeerReviewed);
    const highQualityRefs = sortedReferences.filter(r => !r.source.isPeerReviewed && !r.source.isGovernment && r.qualityAssessment.overallScore > 0.7);
    const otherRefs = sortedReferences.filter(r => !r.source.isPeerReviewed && !r.source.isGovernment && r.qualityAssessment.overallScore <= 0.7);

    // Academic sources section
    if (academicRefs.length > 0) {
      bibliography += '## Academic and Peer-Reviewed Sources\n\n';
      academicRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref, citationStyle);
        bibliography += `${index + 1}. ${formattedRef}\n`;
        
        if (includeQualityMetrics && ref.qualityAssessment.overallScore > 0.8) {
          bibliography += `   *Quality Score: ${ref.qualityAssessment.overallScore.toFixed(2)}/1.0*\n`;
        }
        
        if (ref.doi) {
          bibliography += `   DOI: https://doi.org/${ref.doi}\n`;
        }
        
        bibliography += '\n';
      });
    }

    // Government sources section
    if (governmentRefs.length > 0) {
      bibliography += '## Government and Official Sources\n\n';
      governmentRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref, citationStyle);
        bibliography += `${index + 1}. ${formattedRef}\n\n`;
      });
    }

    // High-quality web sources
    if (highQualityRefs.length > 0) {
      bibliography += '## High-Quality Web Sources\n\n';
      highQualityRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref, citationStyle);
        bibliography += `${index + 1}. ${formattedRef}\n\n`;
      });
    }

    // Other sources
    if (otherRefs.length > 0) {
      bibliography += '## Additional Sources\n\n';
      otherRefs.forEach((ref, index) => {
        const formattedRef = this.formatReferenceByStyle(ref, citationStyle);
        bibliography += `${index + 1}. ${formattedRef}\n\n`;
      });
    }

    // Enhanced statistics and quality assessment
    if (includeQualityMetrics) {
      bibliography += this.generateQualityReport(sortedReferences, citationStyle);
    }

    return bibliography;
  }

  /**
   * Extract DOI from source content
   */
  private extractDOI(source: EnhancedSearchSource): string | undefined {
    const content = `${source.title} ${source.snippet} ${source.url}`;
    
    const doiPatterns = [
      /doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /doi:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /https?:\/\/doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /DOI:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /digital object identifier:\s*([0-9]+\.[0-9]+\/[^\s]+)/i
    ];

    for (const pattern of doiPatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim().replace(/[.,;]$/, '');
      }
    }

    return undefined;
  }

  /**
   * Extract journal name from source
   */
  private extractJournal(source: EnhancedSearchSource): string | undefined {
    const content = `${source.title} ${source.snippet}`;
    
    const journalPatterns = [
      /published in\s+([A-Z][a-zA-Z\s&]+Journal[a-zA-Z\s]*)/i,
      /([A-Z][a-zA-Z\s&]+Journal[a-zA-Z\s]*)/i,
      /(Nature|Science|Cell|PNAS|BMJ|NEJM|Lancet|JAMA)/i,
      /Journal of ([A-Z][a-zA-Z\s&]+)/i,
      /([A-Z][a-zA-Z\s]+) Journal/i
    ];

    for (const pattern of journalPatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  /**
   * Extract volume from source
   */
  private extractVolume(source: EnhancedSearchSource): string | undefined {
    const content = `${source.title} ${source.snippet}`;
    
    const volumePatterns = [
      /volume\s*(\d+)/i,
      /vol\.?\s*(\d+)/i,
      /v\.?\s*(\d+)/i,
      /\s(\d+)\s*\(\d+\)/,
      /,\s*(\d+)\s*\(/
    ];

    for (const pattern of volumePatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return undefined;
  }

  /**
   * Extract issue from source
   */
  private extractIssue(source: EnhancedSearchSource): string | undefined {
    const content = `${source.title} ${source.snippet}`;
    
    const issuePatterns = [
      /issue\s*(\d+)/i,
      /no\.?\s*(\d+)/i,
      /number\s*(\d+)/i,
      /\((\d+)\)/,
      /vol\.?\s*\d+\s*\((\d+)\)/i
    ];

    for (const pattern of issuePatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return undefined;
  }

  /**
   * Extract pages from source
   */
  private extractPages(source: EnhancedSearchSource): string | undefined {
    const content = `${source.title} ${source.snippet}`;
    
    const pagePatterns = [
      /pages?\s*(\d+[-–]\d+)/i,
      /pp\.?\s*(\d+[-–]\d+)/i,
      /p\.?\s*(\d+[-–]\d+)/i,
      /(\d+)[-–](\d+)/,
      /,\s*(\d+[-–]\d+)\./
    ];

    for (const pattern of pagePatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1] || `${match[1]}-${match[2]}`;
      }
    }

    return undefined;
  }

  /**
   * Extract authors from source
   */
  private extractAuthors(source: EnhancedSearchSource): string[] {
    const content = `${source.title} ${source.snippet}`;
    const authors: string[] = [];

    const authorPatterns = [
      /(?:by|author[s]?:|written by)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+)+)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.(?:\s*[A-Z]\.)*(?:\s*,?\s*&?\s*[A-Z][a-z]+,?\s+[A-Z]\.)*(?:\s+et\s+al\.?)?/,
      /([A-Z][a-z]+\s+et\s+al\.?)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.?\s*(?:\([0-9]{4}\))?/,
      /^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\(/
    ];

    for (const pattern of authorPatterns) {
      const match = content.match(pattern);
      if (match) {
        const author = match[1].trim();
        if (!this.isWebsiteName(author)) {
          authors.push(this.formatAuthorName(author));
          break;
        }
      }
    }

    return authors.length > 0 ? authors : ['Unknown Author'];
  }

  /**
   * Assess reference quality with multiple metrics
   */
  private assessReferenceQuality(source: EnhancedSearchSource): {
    overallScore: number;
    academicCredibility: number;
    recency: number;
    relevance: number;
    methodologicalRigor: number;
  } {
    let academicCredibility = 0.5;
    let recency = 0.5;
    let relevance = source.score || 0.5;
    let methodologicalRigor = 0.5;

    // Academic credibility assessment
    if (source.isPeerReviewed) academicCredibility += 0.4;
    if (source.isGovernment) academicCredibility += 0.3;
    if (source.institutionAffiliation) academicCredibility += 0.1;
    if (source.impactFactor && source.impactFactor > 2) academicCredibility += 0.2;

    // Recency assessment
    if (source.publishedDate) {
      const publishedYear = new Date(source.publishedDate).getFullYear();
      const currentYear = new Date().getFullYear();
      const yearsDiff = currentYear - publishedYear;
      
      if (yearsDiff <= 1) recency = 1.0;
      else if (yearsDiff <= 3) recency = 0.8;
      else if (yearsDiff <= 5) recency = 0.6;
      else if (yearsDiff <= 10) recency = 0.4;
      else recency = 0.2;
    }

    // Methodological rigor assessment
    if (source.methodology) methodologicalRigor += 0.3;
    if (source.snippet.toLowerCase().includes('systematic review')) methodologicalRigor += 0.3;
    if (source.snippet.toLowerCase().includes('meta-analysis')) methodologicalRigor += 0.4;
    if (source.snippet.toLowerCase().includes('randomized')) methodologicalRigor += 0.2;

    // Ensure scores don't exceed 1.0
    academicCredibility = Math.min(academicCredibility, 1.0);
    recency = Math.min(recency, 1.0);
    relevance = Math.min(relevance, 1.0);
    methodologicalRigor = Math.min(methodologicalRigor, 1.0);

    // Calculate overall score with weighted average
    const overallScore = (
      academicCredibility * 0.35 +
      relevance * 0.25 +
      recency * 0.20 +
      methodologicalRigor * 0.20
    );

    return {
      overallScore,
      academicCredibility,
      recency,
      relevance,
      methodologicalRigor
    };
  }

  /**
   * Extract metadata from source content
   */
  private extractMetadata(source: EnhancedSearchSource): {
    abstract?: string;
    keywords: string[];
    methodology?: string;
    sampleSize?: string;
    studyType?: string;
    limitations?: string[];
  } {
    const content = source.snippet || '';

    return {
      abstract: this.extractAbstract(content),
      keywords: this.extractKeywords(content),
      methodology: source.methodology,
      sampleSize: this.extractSampleSize(content),
      studyType: this.extractStudyType(content),
      limitations: this.extractLimitations(content)
    };
  }

  /**
   * Generate enhanced citation key
   */
  private generateEnhancedCitationKey(source: EnhancedSearchSource): string {
    const authors = this.extractAuthors(source);
    const firstAuthor = authors[0] || this.extractFirstAuthor(source.title);
    const year = this.extractYear(source.publishedDate || source.title);

    // Clean author name for citation key
    const cleanAuthor = firstAuthor.replace(/[^a-zA-Z]/g, '').substring(0, 10);

    return `${cleanAuthor}${year}`;
  }

  /**
   * Format reference according to enhanced academic standards
   */
  private formatEnhancedReference(source: EnhancedSearchSource): string {
    return this.formatReferenceByStyle(source, 'apa');
  }

  /**
   * Format reference by citation style with enhanced metadata
   */
  private formatReferenceByStyle(
    reference: EnhancedReference | EnhancedSearchSource,
    style: string
  ): string {
    const isEnhancedRef = 'qualityAssessment' in reference;
    const source = isEnhancedRef ? reference.source : reference;
    const authors = isEnhancedRef ? reference.authors : this.extractAuthors(source);
    const year = this.extractYear(source.publishedDate || source.title);
    const title = this.cleanTitle(source.title);
    const url = source.url;
    const domain = this.extractDomainName(source.domain);

    // Enhanced metadata
    const doi = isEnhancedRef ? reference.doi : this.extractDOI(source);
    const journal = isEnhancedRef ? reference.journal : this.extractJournal(source);
    const volume = isEnhancedRef ? reference.volume : this.extractVolume(source);
    const issue = isEnhancedRef ? reference.issue : this.extractIssue(source);
    const pages = isEnhancedRef ? reference.pages : this.extractPages(source);

    const authorString = authors.length > 0 ? authors[0] : 'Unknown Author';
    const accessDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    switch (style) {
      case 'apa':
        if (source.isPeerReviewed && journal) {
          // Academic journal format
          let reference = `${authorString} (${year}). ${title}. *${journal}*`;

          if (volume) {
            reference += `, *${volume}*`;
            if (issue) reference += `(${issue})`;
            if (pages) reference += `, ${pages}`;
          }

          reference += '.';

          if (doi) {
            reference += ` https://doi.org/${doi}`;
          } else {
            reference += ` Retrieved from ${url}`;
          }

          return reference;
        } else if (source.isGovernment) {
          // Government source format
          const orgName = this.extractOrganizationName(domain);
          return `${orgName} (${year}). *${title}*. Retrieved from ${url}`;
        } else {
          // Web source format
          return `${authorString} (${year}). ${title}. *${domain}*. Retrieved ${accessDate} from ${url}`;
        }

      case 'mla':
        if (source.isPeerReviewed && journal) {
          return `${authorString}. "${title}" *${journal}*${volume ? `, vol. ${volume}` : ''}${issue ? `, no. ${issue}` : ''}, ${year}${pages ? `, pp. ${pages}` : ''}. ${doi ? `DOI: ${doi}` : url}.`;
        } else {
          return `${authorString}. "${title}" *${domain}*, ${year}, ${url}. Accessed ${accessDate}.`;
        }

      case 'chicago':
        if (source.isPeerReviewed && journal) {
          return `${authorString}. "${title}" *${journal}*${volume ? ` ${volume}` : ''}${issue ? `, no. ${issue}` : ''} (${year})${pages ? `: ${pages}` : ''}. ${doi ? `https://doi.org/${doi}` : url}.`;
        } else {
          return `${authorString}. "${title}" *${domain}*. Accessed ${accessDate}. ${url}.`;
        }

      case 'harvard':
        return `${authorString} ${year}, '${title}', *${journal || domain}*${volume ? `, vol. ${volume}` : ''}${issue ? `, no. ${issue}` : ''}, viewed ${accessDate}, <${url}>.`;

      default:
        return `${authorString} (${year}). ${title}. Retrieved from ${url}`;
    }
  }

  /**
   * Generate quality report for bibliography
   */
  private generateQualityReport(references: EnhancedReference[], citationStyle: string): string {
    const totalRefs = references.length;
    const academicRefs = references.filter(r => r.source.isPeerReviewed).length;
    const governmentRefs = references.filter(r => r.source.isGovernment).length;
    const highQualityRefs = references.filter(r => r.qualityAssessment.overallScore > 0.7).length;
    const recentRefs = references.filter(r => r.qualityAssessment.recency > 0.6).length;
    const doiRefs = references.filter(r => r.doi).length;

    const avgQuality = references.reduce((sum, ref) => sum + ref.qualityAssessment.overallScore, 0) / totalRefs;
    const avgCredibility = references.reduce((sum, ref) => sum + ref.qualityAssessment.academicCredibility, 0) / totalRefs;
    const avgRecency = references.reduce((sum, ref) => sum + ref.qualityAssessment.recency, 0) / totalRefs;

    let report = `---\n\n## Reference Quality Assessment\n\n`;

    report += `### Quantitative Analysis\n\n`;
    report += `- **Total References:** ${totalRefs}\n`;
    report += `- **Academic/Peer-Reviewed Sources:** ${academicRefs} (${((academicRefs / totalRefs) * 100).toFixed(1)}%)\n`;
    report += `- **Government/Official Sources:** ${governmentRefs} (${((governmentRefs / totalRefs) * 100).toFixed(1)}%)\n`;
    report += `- **High-Quality Sources (>0.7):** ${highQualityRefs} (${((highQualityRefs / totalRefs) * 100).toFixed(1)}%)\n`;
    report += `- **Recent Sources (≤5 years):** ${recentRefs} (${((recentRefs / totalRefs) * 100).toFixed(1)}%)\n`;
    report += `- **Sources with DOI:** ${doiRefs} (${((doiRefs / totalRefs) * 100).toFixed(1)}%)\n\n`;

    report += `### Quality Metrics\n\n`;
    report += `- **Average Overall Quality:** ${avgQuality.toFixed(3)}/1.0\n`;
    report += `- **Average Academic Credibility:** ${avgCredibility.toFixed(3)}/1.0\n`;
    report += `- **Average Recency Score:** ${avgRecency.toFixed(3)}/1.0\n`;
    report += `- **Citation Style:** ${citationStyle.toUpperCase()}\n\n`;

    // Quality recommendations
    report += `### Quality Assessment\n\n`;

    if (avgQuality >= 0.8) {
      report += `✅ **Excellent:** This bibliography demonstrates exceptional quality with high-credibility sources and comprehensive coverage.\n\n`;
    } else if (avgQuality >= 0.7) {
      report += `✅ **Good:** This bibliography meets high academic standards with quality sources and appropriate coverage.\n\n`;
    } else if (avgQuality >= 0.6) {
      report += `⚠️ **Adequate:** This bibliography meets basic academic standards but could benefit from additional high-quality sources.\n\n`;
    } else {
      report += `❌ **Needs Improvement:** This bibliography requires additional high-quality, peer-reviewed sources to meet academic standards.\n\n`;
    }

    // Specific recommendations
    if (academicRefs / totalRefs < 0.6) {
      report += `📚 **Recommendation:** Increase the proportion of peer-reviewed academic sources to strengthen scholarly credibility.\n\n`;
    }

    if (avgRecency < 0.5) {
      report += `📅 **Recommendation:** Include more recent sources (published within the last 5 years) to ensure current relevance.\n\n`;
    }

    if (doiRefs / totalRefs < 0.3) {
      report += `🔗 **Recommendation:** Prioritize sources with DOIs for better academic traceability and verification.\n\n`;
    }

    return report;
  }

  /**
   * Helper methods for content extraction
   */
  private extractAbstract(content: string): string | undefined {
    const abstractMatch = content.match(/abstract[:\s]+([^.]+(?:\.[^.]+){0,3})/i);
    return abstractMatch ? abstractMatch[1].trim() : undefined;
  }

  private extractKeywords(content: string): string[] {
    const keywordMatch = content.match(/keywords?[:\s]+([^.]+)/i);
    if (keywordMatch) {
      return keywordMatch[1].split(/[,;]/).map(k => k.trim()).filter(k => k.length > 0);
    }
    return [];
  }

  private extractSampleSize(content: string): string | undefined {
    const sampleMatch = content.match(/(?:n\s*=\s*|sample size[:\s]+|participants[:\s]+)(\d+)/i);
    return sampleMatch ? sampleMatch[1] : undefined;
  }

  private extractStudyType(content: string): string | undefined {
    const studyTypes = ['systematic review', 'meta-analysis', 'randomized controlled trial', 'cohort study', 'case study', 'cross-sectional'];
    for (const type of studyTypes) {
      if (content.toLowerCase().includes(type)) {
        return type;
      }
    }
    return undefined;
  }

  private extractLimitations(content: string): string[] {
    const limitationMatch = content.match(/limitations?[:\s]+([^.]+(?:\.[^.]+){0,2})/i);
    if (limitationMatch) {
      return [limitationMatch[1].trim()];
    }
    return [];
  }

  // Utility methods
  private extractFirstAuthor(title: string): string {
    const words = title.split(' ');
    return words[0] || 'Unknown';
  }

  private extractYear(dateOrContent: string): string {
    const yearMatch = dateOrContent.match(/\b(19|20)\d{2}\b/);
    return yearMatch ? yearMatch[0] : new Date().getFullYear().toString();
  }

  private cleanTitle(title: string): string {
    return title
      .replace(/^(Abstract|Summary|Introduction|Overview):\s*/i, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private extractDomainName(domain: string): string {
    return domain
      .replace(/^www\./, '')
      .replace(/\.com$|\.org$|\.gov$|\.edu$/, '')
      .split('.')[0]
      .charAt(0).toUpperCase() + domain.split('.')[0].slice(1);
  }

  private extractOrganizationName(domain: string): string {
    const orgMap: { [key: string]: string } = {
      'nih.gov': 'National Institutes of Health',
      'cdc.gov': 'Centers for Disease Control and Prevention',
      'who.int': 'World Health Organization',
      'nature.com': 'Nature Publishing Group',
      'science.org': 'American Association for the Advancement of Science'
    };

    for (const [domainKey, orgName] of Object.entries(orgMap)) {
      if (domain.includes(domainKey)) {
        return orgName;
      }
    }

    return this.extractDomainName(domain);
  }

  private isWebsiteName(name: string): boolean {
    const websiteNames = [
      'researchgate', 'sciencedirect', 'springer', 'nature', 'science',
      'pubmed', 'arxiv', 'jstor', 'wiley', 'elsevier', 'google'
    ];

    return websiteNames.some(site =>
      name.toLowerCase().includes(site) ||
      name.toLowerCase() === site
    );
  }

  private formatAuthorName(name: string): string {
    if (name.toLowerCase().includes('et al')) {
      return name;
    }

    if (!name.includes(' ') || name.length < 4) {
      return name;
    }

    const parts = name.split(' ');
    if (parts.length >= 2) {
      const lastName = parts[parts.length - 1];
      const firstInitial = parts[0].charAt(0).toUpperCase();
      return `${lastName}, ${firstInitial}.`;
    }

    return name;
  }

  /**
   * Clear all references
   */
  clear(): void {
    this.references.clear();
    this.citationCounter = 0;
    this.doiCache.clear();
  }

  /**
   * Get all enhanced references
   */
  getAllEnhancedReferences(): EnhancedReference[] {
    return Array.from(this.references.values());
  }
}

// Export singleton instance
export const enhancedReferenceManagementService = new EnhancedReferenceManagementService();
