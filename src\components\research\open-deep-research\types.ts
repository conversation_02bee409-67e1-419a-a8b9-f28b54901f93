/**
 * Open Deep Research Types
 * TypeScript interfaces and types for the Open Deep Research component
 */

export interface SearchResult {
  id: string;
  url: string;
  name: string;
  snippet: string;
  isCustomUrl?: boolean;
  score?: number;
  content?: string;
}

export interface Report {
  title: string;
  summary: string;
  sections: {
    title: string;
    content: string;
  }[];
  sources: {
    id: string;
    url: string;
    name: string;
  }[];
  usedSources?: number[];
}

export interface KnowledgeBaseReport {
  id: string;
  timestamp: number;
  query: string;
  report: Report;
}

export interface RankingResult {
  url: string;
  score: number;
  reasoning: string;
}

export interface Article {
  url: string;
  title: string;
  content: string;
}

export type ModelVariant =
  | 'anthropic/claude-3.5-sonnet'
  | 'anthropic/claude-3-haiku'
  | 'openai/gpt-4o'
  | 'openai/gpt-4o-mini'
  | 'google/gemini-2.0-flash-001'
  | 'google/gemini-pro'
  | 'meta-llama/llama-3.1-405b-instruct'
  | 'meta-llama/llama-3.1-70b-instruct'
  | 'deepseek/deepseek-chat'
  | 'qwen/qwen-2.5-72b-instruct';

export interface Status {
  loading: boolean;
  generatingReport: boolean;
  agentStep: 'idle' | 'processing' | 'searching' | 'analyzing' | 'generating';
  fetchStatus: {
    total: number;
    successful: number;
    fallback: number;
    sourceStatuses: Record<string, 'fetched' | 'preview'>;
  };
  agentInsights: string[];
  searchQueries: string[];
}

export interface OpenDeepResearchState {
  // Search state
  query: string;
  timeFilter: string;
  results: SearchResult[];
  selectedResults: string[];
  
  // Report state
  reportPrompt: string;
  report: Report | null;
  
  // UI state
  error: string | null;
  newUrl: string;
  isSourcesOpen: boolean;
  selectedModel: ModelVariant;
  isAgentMode: boolean;
  sidebarOpen: boolean;
  activeTab: 'search' | 'report' | 'history';
  
  // Status
  status: Status;
}

export interface SearchConfig {
  resultsPerPage: number;
  maxSelectableResults: number;
  provider: 'tavily' | 'google' | 'bing' | 'exa';
  safeSearch: {
    google: 'active' | 'off';
    bing: 'moderate' | 'strict' | 'off';
  };
  market: string;
}

export interface PlatformModel {
  value: string;
  label: string;
  platform: string;
  disabled: boolean;
}

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'txt';
  includeMetadata: boolean;
  includeSources: boolean;
}

// API Response Types
export interface SearchResponse {
  webPages?: {
    value: SearchResult[];
  };
}

export interface OptimizeResearchResponse {
  query: string;
  optimizedPrompt: string;
  explanation: string;
  suggestedStructure: string[];
}

export interface AnalyzeResultsResponse {
  rankings: RankingResult[];
  analysis: string;
}

export interface ReportGenerationResponse {
  report: Report;
  success: boolean;
  error?: string;
}

// Component Props
export interface OpenDeepResearchProps {
  className?: string;
}

export interface SearchInterfaceProps {
  query: string;
  timeFilter: string;
  selectedModel: ModelVariant;
  isAgentMode: boolean;
  reportPrompt: string;
  newUrl: string;
  status: Status;
  onQueryChange: (query: string) => void;
  onTimeFilterChange: (filter: string) => void;
  onModelChange: (model: ModelVariant) => void;
  onAgentModeChange: (enabled: boolean) => void;
  onReportPromptChange: (prompt: string) => void;
  onNewUrlChange: (url: string) => void;
  onSearch: (e: React.FormEvent) => void;
  onAgentSearch: (e: React.FormEvent) => void;
  onAddCustomUrl: (e: React.FormEvent) => void;
  onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onGenerateReport: () => void;
}

export interface ResultsListProps {
  results: SearchResult[];
  selectedResults: string[];
  maxSelections: number;
  onResultSelect: (resultId: string) => void;
  onRemoveResult: (resultId: string) => void;
}

export interface ReportViewerProps {
  report: Report | null;
  reportPrompt: string;
}

export interface AgentProgressProps {
  status: Status;
}

export interface KnowledgeBaseSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Supabase Database Types
export interface DeepResearchSession {
  id: string;
  user_id: string;
  title: string;
  query: string;
  report_prompt: string;
  selected_model: ModelVariant;
  is_agent_mode: boolean;
  status: 'pending' | 'processing' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
  metadata?: any;
}

export interface DeepResearchReport {
  id: string;
  session_id: string;
  title: string;
  summary: string;
  sections: any; // JSON field
  sources: any; // JSON field
  used_sources?: number[];
  word_count: number;
  processing_time_seconds?: number;
  created_at: string;
}

export interface DeepResearchSource {
  id: string;
  session_id: string;
  url: string;
  title: string;
  snippet: string;
  content?: string;
  is_selected: boolean;
  score?: number;
  created_at: string;
}

export interface DeepResearchFile {
  id: string;
  session_id: string;
  original_filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  content?: string;
  created_at: string;
}

export interface DeepResearchExport {
  id: string;
  session_id: string;
  export_format: 'pdf' | 'docx' | 'txt';
  file_name: string;
  file_path?: string;
  file_size?: number;
  export_options: any; // JSON field
  download_count: number;
  created_at: string;
}

// History and Search Types
export interface ResearchHistoryItem {
  session: DeepResearchSession;
  report?: DeepResearchReport;
  sources: DeepResearchSource[];
  files: DeepResearchFile[];
  exports: DeepResearchExport[];
}

export interface HistorySearchFilters {
  query?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  model?: ModelVariant;
  status?: DeepResearchSession['status'];
  isAgentMode?: boolean;
}
