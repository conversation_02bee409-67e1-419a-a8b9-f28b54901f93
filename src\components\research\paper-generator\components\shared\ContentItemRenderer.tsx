import React from 'react';
import {
  ChevronUp,
  ChevronDown,
  X,
  FileText,
  PenTool,
  Zap,
  ImageIcon,
  FileImage,
  Upload,
  Eye,
  CheckCircle,
  Bot,
  Microscope,
  Search,
  Loader2,
  TrendingUp
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { ContentItem } from "./types";
import paperAIService from "./paper-ai.service";

// Helper functions to create research-focused prompts
const createResearchTextPrompt = (sectionName: string, userContent: string): string => {
  const basePrompt = `Based on the following user input, generate professional research content for the ${sectionName} section of an academic paper.

User input: "${userContent}"

Instructions:
- Generate ONLY the research content, no meta-commentary or conversational text
- Use formal academic language and structure
- Focus on factual, analytical content that contributes to the research paper
- Include specific details, measurements, or observations where relevant
- Avoid phrases like "good question", "interesting point", or conversational elements
- Write in third person, past tense for completed research
- Ensure content is substantial and research-quality (minimum 100 words)`;

  switch (sectionName.toLowerCase()) {
    case 'methodology':
      return `${basePrompt}
- Describe specific procedures, protocols, and methods used
- Include technical details about equipment, software, or analytical techniques
- Mention sample sizes, parameters, or experimental conditions
- Explain data collection and analysis procedures
- Address any quality control or validation measures`;

    case 'results':
      return `${basePrompt}
- Present findings objectively without interpretation
- Include specific numerical data, statistics, or measurements
- Describe patterns, trends, or significant observations
- Reference data visualizations or statistical analyses
- Use precise scientific language for reporting results`;

    case 'introduction':
      return `${basePrompt}
- Provide relevant background context and literature review
- Clearly state research objectives and hypotheses
- Explain the significance and novelty of the research
- Define key concepts or terminology
- Establish the research gap being addressed`;

    case 'discussion':
      return `${basePrompt}
- Interpret results in context of research objectives
- Compare findings with existing literature
- Explain mechanisms or theoretical implications
- Address limitations and potential sources of error
- Discuss broader significance and applications`;

    default:
      return `${basePrompt}
- Ensure content is appropriate for the ${sectionName} section
- Maintain academic rigor and professional tone
- Focus on research-relevant information`;
  }
};

const createResearchImagePrompt = (sectionName: string, figureTitle: string): string => {
  const basePrompt = `Analyze this research figure and generate a professional academic caption and analysis.

Figure title: "${figureTitle}"
Section: ${sectionName}

Instructions:
- Generate ONLY the figure analysis content, no conversational text
- Use formal academic language appropriate for research papers
- Focus on what the figure shows, key findings, and research implications
- Include specific observations about data, trends, or patterns visible
- Avoid meta-commentary like "this is interesting" or "good visualization"
- Write in third person, present tense for describing the figure
- Ensure analysis is substantial and research-quality (minimum 80 words)`;

  switch (sectionName.toLowerCase()) {
    case 'methodology':
      return `${basePrompt}
- Describe the experimental setup, equipment, or workflow shown
- Explain the methodology or procedure illustrated
- Detail any technical specifications or parameters visible
- Describe how this relates to the overall research approach`;

    case 'results':
      return `${basePrompt}
- Describe the data patterns, trends, or relationships shown
- Report specific values, ranges, or statistical measures visible
- Identify significant findings or notable observations
- Explain what the results demonstrate or reveal`;

    case 'introduction':
      return `${basePrompt}
- Explain how this figure provides context for the research
- Describe the conceptual framework or background information shown
- Connect the figure to the research problem or objectives`;

    case 'discussion':
      return `${basePrompt}
- Interpret the implications of what is shown in the figure
- Connect the figure to broader research findings or literature
- Explain the significance or meaning of the visual data
- Discuss how this supports or relates to the research conclusions`;

    default:
      return `${basePrompt}
- Provide detailed analysis appropriate for the ${sectionName} section
- Focus on research-relevant observations and implications`;
  }
};

interface ContentItemProps {
  sectionId: string;
  sectionName: string;
  item: ContentItem;
  index: number;
  totalItems: number;
  updateContentItem: (sectionId: string, itemId: string, updates: Partial<ContentItem>) => void;
  removeContentItem: (sectionId: string, itemId: string) => void;
  moveContentItem: (sectionId: string, itemId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedModel: string;
  textPlaceholders: Record<string, string>;
  figurePlaceholders: Record<string, string>;
  defaultTextPrompt: string;
  defaultFigurePrompt: string;
  figureNumber?: number; // Add figure number prop
}

export const ContentItemRenderer: React.FC<ContentItemProps> = ({
  sectionId,
  sectionName,
  item,
  index,
  totalItems,
  updateContentItem,
  removeContentItem,
  moveContentItem,
  analyzingItems,
  setAnalyzingItems,
  selectedModel,
  textPlaceholders,
  figurePlaceholders,
  defaultTextPrompt,
  defaultFigurePrompt,
  figureNumber
}) => {
  
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      updateContentItem(sectionId, item.id, { content: fileUrl });
      console.log('File uploaded:', file.name);
    }
  };

  const handleAITextAnalysis = async () => {
    updateContentItem(sectionId, item.id, { content: 'Generating analysis...' });
    try {
      // Create a research-focused prompt based on section type
      const researchPrompt = createResearchTextPrompt(sectionName, item.content);
      const aiText = await paperAIService.analyzeText(researchPrompt, { model: selectedModel });
      updateContentItem(sectionId, item.id, { content: aiText });
      toast.success("AI text analysis generated!");
    } catch (err: any) {
      updateContentItem(sectionId, item.id, { content: '' });
      toast.error(err.message || 'Failed to generate text analysis.');
    }
  };

  const handleAIImageAnalysis = async () => {
    if (!item.title?.trim()) {
      toast.error("Please add a figure title before generating AI analysis");
      return;
    }

    setAnalyzingItems(prev => new Set(prev).add(item.id));

    try {
      // Create a research-focused prompt for image analysis
      const researchPrompt = createResearchImagePrompt(sectionName, item.title);
      const analysis = await paperAIService.analyzeImage(
        item.content,
        researchPrompt,
        { model: selectedModel }
      );
      updateContentItem(sectionId, item.id, { caption: analysis });
      toast.success("AI analysis generated successfully!");
    } catch (error) {
      toast.error("Failed to generate AI analysis. Please try again.");
      console.error('AI Analysis Error:', error);
    } finally {
      setAnalyzingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(item.id);
        return newSet;
      });
    }
  };

  return (
    <div className="group relative border border-gray-200 rounded-2xl p-6 bg-gradient-to-br from-white to-gray-50/30 hover:from-blue-50/20 hover:to-purple-50/20 hover:border-blue-200 transition-all duration-300 shadow-sm hover:shadow-md">
      {/* Enhanced Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-blue-100 group-hover:to-purple-100 transition-all duration-200 shadow-sm">
            {item.type === 'text' ? 
              <FileText className="h-5 w-5 text-gray-600" /> : 
              <ImageIcon className="h-5 w-5 text-gray-600" />
            }
          </div>
          <div className="flex flex-col gap-2">
            <Badge 
              variant={item.type === 'text' ? 'default' : 'secondary'}
              className="flex items-center gap-2 px-3 py-1.5 font-medium"
            >
              {item.type === 'text' ? 
                <FileText className="h-3.5 w-3.5" /> : 
                <ImageIcon className="h-3.5 w-3.5" />
              }
              {item.type === 'text' ? 'Text Analysis' : 'Figure & Analysis'}
            </Badge>
            <span className="text-sm font-semibold text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
              Item #{index + 1}
            </span>
          </div>
        </div>
        
        {/* Enhanced Action Buttons */}
        <div className="flex items-center gap-1 bg-white/80 backdrop-blur-sm rounded-lg p-1 shadow-sm border">
          <Button 
            onClick={() => moveContentItem(sectionId, item.id, 'up')}
            disabled={index === 0}
            variant="ghost" 
            size="sm"
            className="h-8 w-8 p-0 hover:bg-blue-100 disabled:opacity-40"
            title="Move up"
          >
            <ChevronUp className="h-4 w-4" />
          </Button>
          <Button 
            onClick={() => moveContentItem(sectionId, item.id, 'down')}
            disabled={index === totalItems - 1}
            variant="ghost" 
            size="sm"
            className="h-8 w-8 p-0 hover:bg-blue-100 disabled:opacity-40"
            title="Move down"
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
          <div className="w-px h-6 bg-gray-200 mx-1"></div>
          <Button 
            onClick={() => removeContentItem(sectionId, item.id)}
            variant="ghost" 
            size="sm"
            className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-600"
            title="Remove item"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {item.type === 'text' ? (
        <div className="space-y-4">
          <div className="relative">
            <div className="absolute top-3 left-3 flex items-center gap-2 z-10">
              <div className="flex items-center gap-1 bg-blue-100 text-blue-700 px-2 py-1 rounded-md text-xs font-medium">
                <PenTool className="h-3 w-3" />
                Analysis
              </div>
            </div>
            <Textarea
              placeholder={(textPlaceholders[sectionName] || defaultTextPrompt)}
              value={item.content}
              onChange={(e) =>
                updateContentItem(sectionId, item.id, { content: e.target.value })
              }
              className="min-h-[160px] pt-12 border-2 border-gray-200 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/70 backdrop-blur-sm resize-none"
            />
            <div className="mt-2 flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleAITextAnalysis}
                disabled={!item.content || item.content === 'Generating analysis...'}
                className="hover:bg-blue-50 border-blue-200"
              >
                <Zap className="h-4 w-4 mr-1 text-blue-600" />
                Generate Research Content
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Enhanced Figure Title with Auto-numbering */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
              <FileImage className="h-4 w-4 text-blue-600" />
              Figure Title {figureNumber && <Badge variant="secondary" className="bg-blue-100 text-blue-800">Figure {figureNumber}</Badge>}
            </label>
            <div className="relative">
              {figureNumber && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-md z-10">
                  Figure {figureNumber}:
                </div>
              )}
              <Input
                placeholder={figureNumber ? "Experimental Setup for Data Collection, Statistical Analysis Results..." : "e.g., Experimental Setup for Data Collection, Statistical Analysis Results..."}
                value={item.title || ''}
                onChange={(e) => updateContentItem(sectionId, item.id, { title: e.target.value })}
                className={`border-2 border-gray-200 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/70 backdrop-blur-sm h-12 ${figureNumber ? 'pl-24' : ''}`}
              />
            </div>
          </div>

          {/* Enhanced Image Upload Section */}
          <div className="space-y-4">
            <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
              <Upload className="h-4 w-4 text-green-600" />
              Upload Figure
            </label>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="relative">
                  <Input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={handleFileUpload}
                    className="border-2 border-dashed border-gray-300 hover:border-blue-400 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/70 backdrop-blur-sm h-14 file:bg-blue-50 file:text-blue-700 file:border-0 file:rounded-lg file:px-4 file:py-2 file:mr-4 file:font-medium hover:file:bg-blue-100"
                  />
                  <div className="absolute top-1/2 right-4 transform -translate-y-1/2 pointer-events-none">
                    <Upload className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  Supports: JPG, PNG, PDF, SVG files
                </p>
              </div>
              
              {/* Enhanced Image Preview */}
              {item.content && (
                <div className="relative group">
                  <div className="relative overflow-hidden rounded-xl border-2 border-gray-200 bg-white shadow-sm">
                    <img 
                      src={item.content} 
                      alt={item.title || "Figure preview"} 
                      className="w-full h-32 object-cover transition-transform duration-200 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                    <div className="absolute top-2 right-2">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 left-2 right-2">
                      <div className="bg-white/90 backdrop-blur-sm rounded-md px-2 py-1 text-xs font-medium text-gray-700 truncate">
                        {figureNumber ? `Figure ${figureNumber}: ${item.title || 'Untitled'}` : (item.title || 'Untitled Figure')}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* AI Analysis Button - Made more prominent */}
          {item.content && (
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 border-2 border-purple-200 rounded-xl p-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Bot className="h-5 w-5 text-purple-600" />
                  <h4 className="font-semibold text-lg text-gray-800">AI Research Analysis</h4>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Generate professional academic analysis and caption for your research figure
                </p>
                
                {item.title && item.title.trim() ? (
                  <Button
                    onClick={handleAIImageAnalysis}
                    disabled={analyzingItems.has(item.id)}
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-4 text-base font-semibold"
                  >
                    {analyzingItems.has(item.id) ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                        Analyzing Figure...
                      </>
                    ) : (
                      <>
                        <Zap className="h-5 w-5 mr-2" />
                        Generate Research Analysis
                      </>
                    )}
                  </Button>
                ) : (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-amber-800">
                      <FileImage className="h-4 w-4" />
                      <span className="font-medium">Figure title required</span>
                    </div>
                    <p className="text-sm text-amber-700 mt-1">
                      Please add a figure title above to enable AI analysis
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Enhanced Figure Analysis Section */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-semibold text-gray-700">
              <Microscope className="h-4 w-4 text-purple-600" />
              Figure Analysis & Interpretation
              {analyzingItems.has(item.id) && (
                <div className="flex items-center gap-2 ml-2">
                  <Loader2 className="h-3 w-3 animate-spin text-purple-600" />
                  <span className="text-xs text-purple-600 font-medium">AI Analyzing...</span>
                </div>
              )}
            </label>
            <div className="relative">
              <div className="absolute top-3 left-3 flex items-center gap-2 z-10">
                <div className="flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-1 rounded-md text-xs font-medium">
                  <Search className="h-3 w-3" />
                  Analysis
                </div>
              </div>
              <Textarea
                placeholder={figurePlaceholders[sectionName] || defaultFigurePrompt}
                value={item.caption || ''}
                onChange={(e) =>
                  updateContentItem(sectionId, item.id, { caption: e.target.value })
                }
                className="min-h-[140px] pt-12 border-2 border-gray-200 focus:border-purple-400 focus:ring-purple-400 rounded-xl bg-white/70 backdrop-blur-sm resize-none"
                disabled={analyzingItems.has(item.id)}
              />
              {analyzingItems.has(item.id) && (
                <div className="absolute inset-0 bg-white/50 backdrop-blur-sm rounded-xl flex items-center justify-center">
                  <div className="flex items-center gap-3 bg-white rounded-lg px-4 py-2 shadow-lg border">
                    <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
                    <span className="font-medium text-gray-700">AI is analyzing your figure...</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Analysis Quality Indicators */}
          {item.caption && !analyzingItems.has(item.id) && (
            <div className="flex items-center gap-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-gray-700">
                  Analysis Quality: 
                  <span className={`ml-1 ${item.caption.length > 100 ? 'text-green-600' : item.caption.length > 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {item.caption.length > 100 ? 'Detailed' : item.caption.length > 50 ? 'Good' : 'Basic'}
                  </span>
                </span>
              </div>
              <div className="text-xs text-gray-500">
                {item.caption.length} characters
              </div>
              {item.caption.includes('Statistical significance') && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                  <Bot className="h-3 w-3 mr-1" />
                  AI Generated
                </Badge>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
