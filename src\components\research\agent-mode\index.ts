/**
 * Agent Mode Module Exports
 * Contextual document editing through natural language prompts
 */

export { AgentModeService } from './AgentModeService';
export { DocumentAnalyzer } from './DocumentAnalyzer';
export { AgentModePanel } from './AgentModePanel';
export * as AgentModeUtils from './utils';

export type {
  DocumentStructure,
  Heading,
  Paragraph,
  Section,
  TargetSection,
  EditInstruction,
  AgentModeResult,
  AgentModeRequest,
  AgentModeOptions,
  DocumentAnalysisProgress,
  SectionMatchResult,
  EditGenerationResult,
  DocumentType,
  SectionType,
  EditType,
  AnalysisStage,
  AgentModeState,
  AgentModePanelProps,
  SectionKeywords,
  PromptAnalysis,
  AgentModeError
} from './types';

export { 
  DEFAULT_AGENT_MODE_OPTIONS,
  SECTION_KEYWORDS 
} from './types';
