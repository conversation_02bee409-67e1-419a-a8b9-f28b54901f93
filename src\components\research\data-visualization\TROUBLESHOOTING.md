# Data Visualization Platform - Troubleshooting Guide

## Common Import/Export Errors

### ❌ "does not provide an export named '<PERSON>atter'"
**Problem**: Using non-existent Lucide React icons
**Solution**: ✅ Fixed - Updated to use existing icons:
- `Scatter` → `Circle`
- `Box` → `Square`
- All other icons verified to exist in lucide-react

### ❌ "Identifier 'Database' has already been declared"
**Problem**: Icon name conflicts with type imports
**Solution**: ✅ Fixed - Renamed import to `DatabaseIcon`

### ❌ "Textarea is not defined"
**Problem**: Missing Textarea component import
**Solution**: ✅ Fixed - Added proper import from `@/components/ui/textarea`

## Quick Verification Steps

### 1. Test Basic Component Loading
Navigate to the data visualization module and check if it loads without errors:

```typescript
// Test component available at:
// src/components/research/data-visualization/components/TestComponent.tsx
```

### 2. Verify Environment Setup
Check your `.env.local` file:

```bash
VITE_GEMINI_API_KEY=your_api_key_here
```

### 3. Check Dependencies
Ensure all packages are installed:

```bash
npm install
# or
npm install @google/genai plotly.js-dist-min react-plotly.js xlsx papaparse react-dropzone
```

## Runtime Errors

### ❌ "Gemini API key not found"
**Symptoms**: Analysis fails, API errors in console
**Solutions**:
1. Add `VITE_GEMINI_API_KEY` to `.env.local`
2. Restart development server after adding env vars
3. Get API key from: https://makersuite.google.com/app/apikey

### ❌ "Failed to load Plotly library"
**Symptoms**: Visualizations don't render, blank chart areas
**Solutions**:
1. Clear browser cache and reload
2. Check network connectivity
3. Verify plotly.js-dist-min is installed
4. Try: `npm install plotly.js-dist-min --force`

### ❌ File upload fails
**Symptoms**: Files rejected, upload errors
**Solutions**:
1. Check file size (must be < 50MB)
2. Verify file format (CSV, XLSX, XLS only)
3. Ensure file has data (minimum 10 rows)
4. Try with a simple test CSV:
   ```csv
   name,age,city
   John,25,NYC
   Jane,30,LA
   Bob,35,Chicago
   ```

## Development Issues

### ❌ TypeScript errors
**Common issues**:
1. Missing type definitions
2. Import path issues
3. Component prop mismatches

**Solutions**:
```bash
# Restart TypeScript server in VS Code
Ctrl+Shift+P → "TypeScript: Restart TS Server"

# Check for type issues
npm run type-check
```

### ❌ Build errors
**Common issues**:
1. Dynamic imports not handled correctly
2. Environment variables not available in build
3. Missing dependencies

**Solutions**:
```bash
# Clean build
rm -rf node_modules package-lock.json
npm install
npm run build
```

## Browser Compatibility

### Supported Browsers
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### Known Issues
- ❌ Internet Explorer (not supported)
- ⚠️ Safari < 14 (limited support)

## Performance Issues

### Large File Handling
**Symptoms**: Slow upload, browser freezing
**Solutions**:
1. Split large files into smaller chunks
2. Use CSV instead of Excel for better performance
3. Limit to essential columns only
4. Consider data sampling for initial analysis

### Memory Issues
**Symptoms**: Browser crashes, out of memory errors
**Solutions**:
1. Close other browser tabs
2. Refresh page to clear memory
3. Use smaller datasets for testing
4. Increase browser memory limits if possible

## API Rate Limits

### Gemini API Limits
**Symptoms**: Analysis fails after working initially
**Solutions**:
1. Check API quota in Google AI Studio
2. Implement request throttling
3. Use simpler queries to reduce token usage
4. Consider upgrading API plan

## Getting Help

### Debug Information to Collect
When reporting issues, include:

1. **Browser Console Errors**:
   - Open Developer Tools (F12)
   - Check Console tab for red errors
   - Copy full error messages

2. **Network Errors**:
   - Check Network tab in Developer Tools
   - Look for failed API requests
   - Note response codes and messages

3. **Environment Details**:
   - Browser version
   - Operating system
   - Node.js version
   - Package versions

4. **Steps to Reproduce**:
   - Exact steps taken
   - File used (if possible to share)
   - Expected vs actual behavior

### Test with Sample Data
Before reporting issues, test with this simple CSV:

```csv
product,price,category,rating
Laptop,999,Electronics,4.5
Phone,699,Electronics,4.2
Desk,299,Furniture,4.0
Chair,199,Furniture,4.3
```

### Quick Fixes Checklist
- [ ] Restart development server
- [ ] Clear browser cache
- [ ] Check environment variables
- [ ] Verify API key is valid
- [ ] Test with sample data
- [ ] Check browser console for errors
- [ ] Try in incognito/private mode
- [ ] Update dependencies

## Emergency Fallbacks

### If Module Won't Load
1. Comment out the data visualization route temporarily
2. Remove from sidebar menu
3. Check for conflicting dependencies
4. Gradually re-enable features

### If Analysis Fails
1. Use mock data for testing
2. Disable AI analysis temporarily
3. Focus on file upload and basic stats
4. Re-enable AI features once stable

### If Visualizations Break
1. Fall back to simple HTML tables
2. Use basic charts from recharts library
3. Disable Plotly temporarily
4. Show raw data as backup

Remember: The platform is designed with graceful degradation - core functionality should work even if some features fail.
