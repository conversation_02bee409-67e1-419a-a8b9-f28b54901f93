/**
 * Flow Builder Constants
 * Configuration constants for the Flow Chart Builder module
 */

import { DiagramType, FlowAIModel, MermaidConfig, DiagramTemplate } from './types';

// AI Models configuration - Only the 3 best models for flowchart generation
export const FLOW_AI_MODELS: FlowAIModel[] = [
  {
    id: 'google/gemini-2.5-pro',
    name: 'Gemini Pro',
    provider: 'google',
    description: 'Advanced AI for complex research diagrams',
    maxTokens: 8192,
    supportsStreaming: true,
    recommended: true
  },
  {
    id: 'openai/gpt-4.1-preview',
    name: 'GPT-4.1',
    provider: 'openrouter',
    description: 'Latest GPT model for precise flowcharts',
    maxTokens: 8192,
    supportsStreaming: true
  },
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: '<PERSON> Sonnet 3.5',
    provider: 'openrouter',
    description: 'Excellent for research methodology diagrams',
    maxTokens: 8192,
    supportsStreaming: true
  }
];

// Enhanced Mermaid configuration for better layout and compact display
export const DEFAULT_MERMAID_CONFIG: MermaidConfig = {
  theme: 'default',
  themeVariables: {
    primaryColor: '#3b82f6',
    primaryTextColor: '#1f2937',
    primaryBorderColor: '#2563eb',
    lineColor: '#6b7280',
    secondaryColor: '#f3f4f6',
    tertiaryColor: '#ffffff'
  },
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true,
    curve: 'basis',
    // Enhanced flowchart settings for compact display
    nodeSpacing: 30,
    rankSpacing: 40,
    padding: 15,
    diagramPadding: 8
  },
  sequence: {
    diagramMarginX: 30,
    diagramMarginY: 8,
    actorMargin: 30,
    width: 120,
    height: 50,
    boxMargin: 8,
    boxTextMargin: 4,
    noteMargin: 8,
    messageMargin: 30
  },
  gantt: {
    numberSectionStyles: 4,
    axisFormat: '%m/%d/%Y',
    tickInterval: '1day',
    topPadding: 30,
    bottomPadding: 30,
    gridLineStartPadding: 35,
    fontSize: 11,
    sectionFontSize: 24
  },
  // Global settings for compact display
  maxTextSize: 90000,
  maxEdges: 500,
  wrap: true,
  fontSize: 14
};

// Diagram type configurations
export const DIAGRAM_TYPE_CONFIG = {
  flowchart: {
    name: 'Flowchart',
    description: 'Process flows and decision trees',
    icon: '🔄',
    defaultDirection: 'TD' as const,
    category: 'process'
  },
  sequence: {
    name: 'Sequence Diagram',
    description: 'Interactions between entities over time',
    icon: '📊',
    category: 'analysis'
  },
  gantt: {
    name: 'Gantt Chart',
    description: 'Project timelines and schedules',
    icon: '📅',
    category: 'methodology'
  },
  pie: {
    name: 'Pie Chart',
    description: 'Data distribution visualization',
    icon: '🥧',
    category: 'analysis'
  },
  mindmap: {
    name: 'Mind Map',
    description: 'Hierarchical concept mapping',
    icon: '🧠',
    category: 'research'
  },
  timeline: {
    name: 'Timeline',
    description: 'Chronological event sequences',
    icon: '⏰',
    category: 'methodology'
  },
  'user-journey': {
    name: 'User Journey',
    description: 'User experience workflows',
    icon: '🚶',
    category: 'process'
  },
  er: {
    name: 'Entity Relationship',
    description: 'Database and data relationships',
    icon: '🗃️',
    category: 'analysis'
  },
  state: {
    name: 'State Diagram',
    description: 'System states and transitions',
    icon: '🔀',
    category: 'process'
  },
  class: {
    name: 'Class Diagram',
    description: 'Object-oriented system structure',
    icon: '📦',
    category: 'analysis'
  }
} as const;

// Export format configurations
export const EXPORT_FORMATS = {
  png: {
    name: 'PNG Image',
    description: 'High-quality raster image',
    extension: 'png',
    mimeType: 'image/png'
  },
  svg: {
    name: 'SVG Vector',
    description: 'Scalable vector graphics',
    extension: 'svg',
    mimeType: 'image/svg+xml'
  },
  pdf: {
    name: 'PDF Document',
    description: 'Portable document format',
    extension: 'pdf',
    mimeType: 'application/pdf'
  },
  mermaid: {
    name: 'Mermaid Code',
    description: 'Raw Mermaid syntax',
    extension: 'mmd',
    mimeType: 'text/plain'
  }
} as const;

// Default export options
export const DEFAULT_EXPORT_OPTIONS = {
  format: 'png' as const,
  width: 1200,
  height: 800,
  backgroundColor: '#ffffff',
  theme: 'default' as const
};

// Generation settings
export const GENERATION_CONFIG = {
  DEFAULT_MODEL: 'google/gemini-2.5-pro',
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_MAX_TOKENS: 4096,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // milliseconds
  TIMEOUT: 45000 // 45 seconds for better AI generation
};

// Validation rules
export const VALIDATION_RULES = {
  MAX_DESCRIPTION_LENGTH: 2000,
  MIN_DESCRIPTION_LENGTH: 10,
  MAX_TITLE_LENGTH: 100,
  MIN_TITLE_LENGTH: 3,
  MAX_MERMAID_CODE_LENGTH: 10000,
  ALLOWED_DIAGRAM_TYPES: [
    'flowchart', 'sequence', 'gantt', 'pie', 'mindmap', 
    'timeline', 'user-journey', 'er', 'state', 'class'
  ] as DiagramType[]
};

// Error messages
export const ERROR_MESSAGES = {
  INVALID_DIAGRAM_TYPE: 'Invalid diagram type specified',
  DESCRIPTION_TOO_SHORT: `Description must be at least ${VALIDATION_RULES.MIN_DESCRIPTION_LENGTH} characters`,
  DESCRIPTION_TOO_LONG: `Description must be less than ${VALIDATION_RULES.MAX_DESCRIPTION_LENGTH} characters`,
  TITLE_TOO_SHORT: `Title must be at least ${VALIDATION_RULES.MIN_TITLE_LENGTH} characters`,
  TITLE_TOO_LONG: `Title must be less than ${VALIDATION_RULES.MAX_TITLE_LENGTH} characters`,
  GENERATION_FAILED: 'Failed to generate diagram. Please try again.',
  MERMAID_RENDER_FAILED: 'Failed to render diagram. Please check the syntax.',
  EXPORT_FAILED: 'Failed to export diagram. Please try again.',
  INVALID_MERMAID_SYNTAX: 'Invalid Mermaid syntax detected',
  API_KEY_MISSING: 'API key not configured. Please check your settings.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
};

// UI Constants
export const UI_CONFIG = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  AUTO_SAVE_DELAY: 2000,
  MAX_HISTORY_ITEMS: 50,
  PREVIEW_UPDATE_DELAY: 1000
};

// Local storage keys
export const STORAGE_KEYS = {
  DIAGRAM_HISTORY: 'flow-builder-history',
  USER_PREFERENCES: 'flow-builder-preferences',
  MERMAID_CONFIG: 'flow-builder-mermaid-config',
  EXPORT_OPTIONS: 'flow-builder-export-options',
  SELECTED_MODEL: 'flow-builder-selected-model'
};

// Default diagram templates
export const DEFAULT_TEMPLATES: DiagramTemplate[] = [
  {
    id: 'research-methodology',
    name: 'Research Methodology',
    description: 'Standard research process flowchart',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Research Question] --> B[Literature Review]
    B --> C[Hypothesis Formation]
    C --> D[Research Design]
    D --> E[Data Collection]
    E --> F[Data Analysis]
    F --> G[Results Interpretation]
    G --> H[Conclusion]
    H --> I[Publication]`
  },
  {
    id: 'data-analysis-process',
    name: 'Data Analysis Process',
    description: 'Data analysis workflow',
    type: 'flowchart',
    category: 'analysis',
    mermaidCode: `flowchart LR
    A[Raw Data] --> B[Data Cleaning]
    B --> C[Exploratory Analysis]
    C --> D[Statistical Testing]
    D --> E[Visualization]
    E --> F[Interpretation]
    F --> G[Report Generation]`
  },
  {
    id: 'experimental-design',
    name: 'Experimental Design',
    description: 'Scientific experiment workflow',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Problem Identification] --> B[Hypothesis]
    B --> C[Variable Definition]
    C --> D[Control Group]
    C --> E[Experimental Group]
    D --> F[Data Collection]
    E --> F
    F --> G[Statistical Analysis]
    G --> H[Results]
    H --> I{Hypothesis Supported?}
    I -->|Yes| J[Accept Hypothesis]
    I -->|No| K[Reject Hypothesis]`
  },
  {
    id: 'literature-review-process',
    name: 'Literature Review Process',
    description: 'Systematic literature review workflow',
    type: 'flowchart',
    category: 'research',
    mermaidCode: `flowchart TD
    A[Research Question] --> B[Search Strategy]
    B --> C[Database Search]
    C --> D[Initial Screening]
    D --> E[Full-text Review]
    E --> F[Quality Assessment]
    F --> G[Data Extraction]
    G --> H[Synthesis]
    H --> I[Report Writing]`
  },
  {
    id: 'survey-research',
    name: 'Survey Research Methodology',
    description: 'Complete survey research process',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Research Objectives] --> B[Survey Design]
    B --> C[Questionnaire Development]
    C --> D[Pilot Testing]
    D --> E{Revisions Needed?}
    E -->|Yes| C
    E -->|No| F[Sample Selection]
    F --> G[Data Collection]
    G --> H[Data Validation]
    H --> I[Statistical Analysis]
    I --> J[Results Interpretation]
    J --> K[Report Generation]`
  },
  {
    id: 'qualitative-research',
    name: 'Qualitative Research Process',
    description: 'Qualitative research methodology workflow',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Research Question] --> B[Theoretical Framework]
    B --> C[Participant Selection]
    C --> D[Data Collection Methods]
    D --> E[Interviews/Focus Groups]
    E --> F[Data Transcription]
    F --> G[Coding Process]
    G --> H[Theme Development]
    H --> I[Data Validation]
    I --> J[Findings Interpretation]
    J --> K[Theory Development]`
  },
  {
    id: 'mixed-methods',
    name: 'Mixed Methods Research',
    description: 'Sequential explanatory mixed methods design',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Research Problem] --> B[Mixed Methods Design]
    B --> C[Quantitative Phase]
    C --> D[Survey/Experiment]
    D --> E[Statistical Analysis]
    E --> F[Initial Results]
    F --> G[Qualitative Phase]
    G --> H[Interviews/Observations]
    H --> I[Qualitative Analysis]
    I --> J[Data Integration]
    J --> K[Comprehensive Interpretation]
    K --> L[Final Report]`
  },
  {
    id: 'systematic-review',
    name: 'Systematic Review Protocol',
    description: 'PRISMA-based systematic review process',
    type: 'flowchart',
    category: 'research',
    mermaidCode: `flowchart TD
    A[Protocol Development] --> B[Search Strategy]
    B --> C[Database Searching]
    C --> D[Record Identification]
    D --> E[Duplicate Removal]
    E --> F[Title/Abstract Screening]
    F --> G[Full-text Assessment]
    G --> H[Eligibility Criteria]
    H --> I[Quality Assessment]
    I --> J[Data Extraction]
    J --> K[Meta-analysis]
    K --> L[GRADE Assessment]
    L --> M[Report Writing]`
  },
  {
    id: 'case-study-research',
    name: 'Case Study Research',
    description: 'Multiple case study research design',
    type: 'flowchart',
    category: 'methodology',
    mermaidCode: `flowchart TD
    A[Research Questions] --> B[Case Selection]
    B --> C[Data Collection Protocol]
    C --> D[Multiple Data Sources]
    D --> E[Interviews]
    D --> F[Documents]
    D --> G[Observations]
    E --> H[Within-case Analysis]
    F --> H
    G --> H
    H --> I[Cross-case Analysis]
    I --> J[Pattern Matching]
    J --> K[Theory Building]
    K --> L[Validation]`
  },
  {
    id: 'research-timeline',
    name: 'Research Project Timeline',
    description: 'PhD research project timeline',
    type: 'gantt',
    category: 'methodology',
    mermaidCode: `gantt
    title PhD Research Timeline
    dateFormat  YYYY-MM-DD
    section Literature Review
    Initial Review     :done, lit1, 2024-01-01, 2024-03-31
    Systematic Review  :active, lit2, 2024-02-01, 2024-05-31
    section Methodology
    Design Development :method1, after lit1, 60d
    Pilot Study       :method2, after method1, 30d
    section Data Collection
    Phase 1 Data      :data1, after method2, 90d
    Phase 2 Data      :data2, after data1, 60d
    section Analysis
    Quantitative      :analysis1, after data1, 45d
    Qualitative       :analysis2, after data2, 60d
    section Writing
    Draft Chapters    :writing1, after analysis1, 120d
    Thesis Writing    :writing2, after analysis2, 90d
    Defense Prep      :defense, after writing2, 30d`
  },
  {
    id: 'peer-review-process',
    name: 'Peer Review Process',
    description: 'Academic journal peer review workflow',
    type: 'flowchart',
    category: 'process',
    mermaidCode: `flowchart TD
    A[Manuscript Submission] --> B[Editorial Screening]
    B --> C{Initial Assessment}
    C -->|Reject| D[Desk Rejection]
    C -->|Accept| E[Reviewer Assignment]
    E --> F[Peer Review Process]
    F --> G[Reviewer Reports]
    G --> H[Editorial Decision]
    H --> I{Decision Type}
    I -->|Accept| J[Accepted]
    I -->|Minor Revisions| K[Author Revisions]
    I -->|Major Revisions| L[Substantial Revisions]
    I -->|Reject| M[Rejected]
    K --> N[Revised Submission]
    L --> N
    N --> O[Re-review Process]
    O --> H`
  }
];

// Theme configurations
export const MERMAID_THEMES = {
  default: {
    name: 'Default',
    description: 'Clean and professional',
    primaryColor: '#3b82f6'
  },
  dark: {
    name: 'Dark',
    description: 'Dark theme for presentations',
    primaryColor: '#60a5fa'
  },
  forest: {
    name: 'Forest',
    description: 'Green nature theme',
    primaryColor: '#10b981'
  },
  neutral: {
    name: 'Neutral',
    description: 'Minimal grayscale theme',
    primaryColor: '#6b7280'
  }
} as const;
