/**
 * Constants for the AI Article Revision System
 */

import { AIModelOption } from './types';

// AI Models available for article revision
export const AI_MODELS: AIModelOption[] = [
  // Google Gemini Models
  {
    id: "gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    description: "Best for academic writing and detailed analysis",
    provider: "google",
    contextSize: 2000000,
    isAvailable: true
  },
  {
    id: "gemini-2.5-flash",
    name: "Gemini 2.5 Flash",
    description: "Fast and efficient for revisions",
    provider: "google",
    contextSize: 1000000,
    isAvailable: true
  },
  {
    id: "gemini-2.0-flash-exp",
    name: "Gemini 2.0 Flash Experimental",
    description: "Latest experimental model for revisions",
    provider: "google",
    contextSize: 1000000,
    isAvailable: true
  },
  {
    id: "gemini-1.5-pro",
    name: "Gemini 1.5 Pro",
    description: "Reliable model for comprehensive revisions",
    provider: "google",
    contextSize: 2000000,
    isAvailable: true
  }
];

// Default AI model for different tasks
export const DEFAULT_MODELS = {
  DOCUMENT_PARSING: "gemini-2.5-pro",
  COMMENT_ANALYSIS: "gemini-2.5-pro",
  SECTION_REVISION: "gemini-2.5-pro",
  RESPONSE_GENERATION: "gemini-2.5-pro",
  INTEGRATION: "gemini-2.5-pro"
};

// File upload constraints
export const FILE_CONSTRAINTS = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  SUPPORTED_ARTICLE_FORMATS: ['.pdf', '.docx', '.doc'],
  SUPPORTED_COMMENT_FORMATS: ['.pdf', '.docx', '.doc', '.txt'],
  MAX_REVIEWER_FILES: 10,
  MAX_WORD_COUNT: 50000
};

// Processing timeouts and delays
export const PROCESSING_CONFIG = {
  AI_REQUEST_TIMEOUT: 300000, // 5 minutes
  RATE_LIMIT_DELAY: 2000, // 2 seconds between requests
  MAX_RETRIES: 3,
  BATCH_SIZE: 3, // Process 3 sections in parallel
  CONTEXT_WINDOW_OVERLAP: 500 // Words to overlap between sections
};

// Comment categorization patterns
export const COMMENT_PATTERNS = {
  SEVERITY_KEYWORDS: {
    critical: ['must', 'required', 'essential', 'critical', 'major issue', 'fundamental'],
    major: ['should', 'important', 'significant', 'major', 'substantial'],
    moderate: ['could', 'consider', 'suggest', 'recommend', 'minor issue'],
    minor: ['might', 'perhaps', 'optional', 'cosmetic', 'style']
  },
  CATEGORY_KEYWORDS: {
    content: ['content', 'argument', 'logic', 'reasoning', 'evidence', 'data'],
    structure: ['structure', 'organization', 'flow', 'sequence', 'order'],
    language: ['language', 'grammar', 'style', 'clarity', 'writing', 'expression'],
    methodology: ['method', 'approach', 'procedure', 'analysis', 'statistical'],
    citation: ['citation', 'reference', 'bibliography', 'source', 'literature'],
    figure: ['figure', 'table', 'chart', 'graph', 'image', 'visual']
  }
};

// Section processing order (important for context flow)
export const SECTION_PROCESSING_ORDER: string[] = [
  'title',
  'abstract',
  'keywords',
  'introduction',
  'methodology',
  'results',
  'discussion',
  'conclusion',
  'references'
];

// AI Assistant roles and responsibilities
export const AI_ASSISTANTS = {
  DOCUMENT_PARSER: {
    id: 'document-parser',
    name: 'Document Parser',
    description: 'Extracts and structures content from PDF/Word documents',
    maxConcurrency: 1
  },
  COMMENT_ANALYZER: {
    id: 'comment-analyzer',
    name: 'Comment Analyzer',
    description: 'Processes and categorizes reviewer comments',
    maxConcurrency: 1
  },
  SECTION_REVISOR: {
    id: 'section-revisor',
    name: 'Section Revisor',
    description: 'Revises individual sections based on comments',
    maxConcurrency: 3
  },
  INTEGRATION_MANAGER: {
    id: 'integration-manager',
    name: 'Integration Manager',
    description: 'Combines revised sections and ensures coherence',
    maxConcurrency: 1
  },
  RESPONSE_GENERATOR: {
    id: 'response-generator',
    name: 'Response Generator',
    description: 'Creates detailed reviewer response letters',
    maxConcurrency: 1
  },
  SUGGESTION_ASSISTANT: {
    id: 'suggestion-assistant',
    name: 'Suggestion Assistant',
    description: 'Identifies manual intervention requirements',
    maxConcurrency: 1
  }
};

// Export templates and formatting
export const EXPORT_TEMPLATES = {
  RESPONSE_LETTER: {
    HEADER: `Response to the Decision Letter
Manuscript Number: {manuscriptNumber}
Title: {title}
Authors: <AUTHORS>

Dear Editors and Reviewers:

We would like to resubmit the revised manuscript entitled "{title}" by {authors} to {journalName}.

In response to the decision on our manuscript ({manuscriptNumber}) and the detailed comments provided by the reviewers, we have carefully revised the paper. These revisions address not only the specific feedback from the reviewers but also improve the language, expression, structure, and presentation throughout the manuscript.

We would like to express our sincere appreciation to the editor and reviewers for their thoughtful and constructive comments, which have significantly strengthened our work. Each of the reviewer's comments has been carefully addressed in the revised manuscript. Below, we have provided a point-by-point response to each suggestion, with corresponding changes highlighted in bold within the manuscript for easy identification.`,
    
    SUMMARY_SECTION: `Summary of Changes

{summaryPoints}`,
    
    RESPONSE_SECTION: `Response to Comments

We would like to thank the editor for managing the review of our manuscript, and the reviewers for their insightful comments, which have helped us to improve the quality of the paper significantly. All feedback from the editor and reviewers has been taken into account, including the writing, the structure, the presentation, and the analysis. All modifications are marked in red in the revised paper.

Legend in the revised manuscript:
Blue color: Our response to the comments.
Red color: Location of change in the manuscript.
Green color: Our revised Text and Figures.`,
    
    FOOTER: `We believe that these revisions have significantly improved the manuscript and hope that it now meets the standards for publication in {journalName}.

Thank you for your consideration.

Sincerely,
{authors}`
  }
};

// Quality thresholds
export const QUALITY_THRESHOLDS = {
  MIN_REVISION_CONFIDENCE: 70, // Minimum confidence for auto-revision
  MIN_SECTION_QUALITY: 80, // Minimum quality score for section
  MAX_CHANGES_PER_SECTION: 50, // Maximum changes per section
  MIN_COMMENT_COVERAGE: 85 // Minimum percentage of comments addressed
};

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
  UNSUPPORTED_FORMAT: 'File format not supported. Please use PDF, DOC, or DOCX files',
  PARSING_FAILED: 'Failed to parse the document. Please check the file format and try again',
  NO_COMMENTS_FOUND: 'No reviewer comments found in the uploaded files',
  PROCESSING_TIMEOUT: 'Processing timeout. Please try again with a smaller document',
  AI_SERVICE_ERROR: 'AI service temporarily unavailable. Please try again later',
  INSUFFICIENT_CONTEXT: 'Document too large for current AI model context window'
};

// Success messages
export const SUCCESS_MESSAGES = {
  DOCUMENT_PARSED: 'Document successfully parsed and analyzed',
  COMMENTS_PROCESSED: 'Reviewer comments successfully processed',
  REVISION_COMPLETED: 'Article revision completed successfully',
  EXPORT_READY: 'Files are ready for download'
};
