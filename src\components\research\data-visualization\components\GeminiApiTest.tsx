import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  <PERSON>ader2, 
  Key, 
  <PERSON><PERSON>,
  Brain
} from "lucide-react";
import { GeminiAnalysisService } from '../services/gemini-analysis.service';

interface ApiTestResult {
  status: 'idle' | 'testing' | 'success' | 'error';
  message: string;
  details?: any;
}

export const GeminiApiTest: React.FC = () => {
  const [testResult, setTestResult] = useState<ApiTestResult>({
    status: 'idle',
    message: 'Click "Test API" to verify your Gemini API key'
  });

  const testGeminiApi = async () => {
    setTestResult({
      status: 'testing',
      message: 'Testing Gemini API connection...'
    });

    try {
      // Check if API key is available
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('VITE_GEMINI_API_KEY not found in environment variables');
      }

      // Test with a simple request
      const testRequest = {
        prompt: 'Hello! Please respond with "API connection successful" to confirm the connection is working.',
        dataContext: {
          fileName: 'test.csv',
          headers: ['test'],
          sampleData: [['test']],
          dataTypes: { test: 'string' }
        }
      };

      const response = await GeminiAnalysisService.executeWithGemini(testRequest);

      if (response.text) {
        setTestResult({
          status: 'success',
          message: 'Gemini API connection successful!',
          details: {
            response: response.text.substring(0, 200) + (response.text.length > 200 ? '...' : ''),
            hasCode: !!response.code,
            hasResult: !!response.result
          }
        });
      } else {
        throw new Error('No response received from Gemini API');
      }

    } catch (error) {
      console.error('Gemini API test failed:', error);
      
      let errorMessage = 'Unknown error occurred';
      let details = {};

      if (error instanceof Error) {
        errorMessage = error.message;
        
        // Provide specific guidance based on error type
        if (error.message.includes('API key')) {
          details = {
            suggestion: 'Check that VITE_GEMINI_API_KEY is set in your .env file',
            action: 'Restart your development server after adding the API key'
          };
        } else if (error.message.includes('fetch')) {
          details = {
            suggestion: 'Check your internet connection',
            action: 'Verify the API key is valid at https://makersuite.google.com/app/apikey'
          };
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          details = {
            suggestion: 'API quota exceeded or rate limit hit',
            action: 'Wait a moment and try again, or check your API usage'
          };
        }
      }

      setTestResult({
        status: 'error',
        message: errorMessage,
        details
      });
    }
  };

  const getStatusIcon = () => {
    switch (testResult.status) {
      case 'testing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Key className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (testResult.status) {
      case 'testing':
        return <Badge variant="default">Testing...</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">Connected</Badge>;
      case 'error':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="secondary">Not Tested</Badge>;
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Gemini API Connection Test
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* API Key Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Key className="h-4 w-4 text-gray-600" />
            <span className="font-medium">API Key Status:</span>
          </div>
          <div className="flex items-center gap-2">
            {import.meta.env.VITE_GEMINI_API_KEY ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600">Configured</span>
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-600">Missing</span>
              </>
            )}
          </div>
        </div>

        {/* Test Button */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">Connection Test:</span>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge()}
            <Button 
              onClick={testGeminiApi}
              disabled={testResult.status === 'testing' || !import.meta.env.VITE_GEMINI_API_KEY}
              size="sm"
            >
              <Zap className="h-4 w-4 mr-2" />
              Test API
            </Button>
          </div>
        </div>

        {/* Test Result */}
        <Alert variant={testResult.status === 'error' ? 'destructive' : 'default'}>
          {getStatusIcon()}
          <AlertDescription>
            <div className="space-y-2">
              <p>{testResult.message}</p>
              
              {testResult.details && (
                <div className="text-sm space-y-1">
                  {testResult.details.response && (
                    <div>
                      <strong>Response:</strong> {testResult.details.response}
                    </div>
                  )}
                  
                  {testResult.details.suggestion && (
                    <div>
                      <strong>Suggestion:</strong> {testResult.details.suggestion}
                    </div>
                  )}
                  
                  {testResult.details.action && (
                    <div>
                      <strong>Action:</strong> {testResult.details.action}
                    </div>
                  )}
                  
                  {testResult.status === 'success' && (
                    <div className="flex gap-4 mt-2">
                      <span>✅ Text Response: Yes</span>
                      <span>🔧 Code Execution: {testResult.details.hasCode ? 'Yes' : 'No'}</span>
                      <span>📊 Results: {testResult.details.hasResult ? 'Yes' : 'No'}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>

        {/* Environment Info */}
        {!import.meta.env.VITE_GEMINI_API_KEY && (
          <Alert>
            <Key className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p><strong>API Key Missing:</strong> Add your Gemini API key to the .env file:</p>
                <code className="block bg-gray-100 p-2 rounded text-sm">
                  VITE_GEMINI_API_KEY=your_api_key_here
                </code>
                <p className="text-sm">
                  Get your API key from: <a 
                    href="https://makersuite.google.com/app/apikey" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    Google AI Studio
                  </a>
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Success Instructions */}
        {testResult.status === 'success' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p><strong>Great! Your Gemini API is working.</strong></p>
                <p className="text-sm">You can now:</p>
                <ul className="text-sm list-disc list-inside space-y-1">
                  <li>Upload CSV or Excel files for analysis</li>
                  <li>Get AI-powered insights about your data</li>
                  <li>Ask natural language questions</li>
                  <li>Generate interactive visualizations</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
