import { supabase } from '@/lib/supabase';
import { 
  PaperGeneration, 
  PaperSection, 
  PaperCitation,
  PaperGenerationInsert,
  PaperSectionInsert,
  PaperCitationInsert
} from './types';

/**
 * Service for managing paper generations in Supabase
 */
export class PaperGenerationService {
  
  /**
   * Create a new paper generation session
   */
  async createPaperGeneration(data: PaperGenerationInsert): Promise<PaperGeneration | null> {
    try {
      // Validate user is authenticated
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User must be authenticated to create paper generations');
      }

      // Ensure user_id matches authenticated user
      if (data.user_id !== user.id) {
        throw new Error('Cannot create paper generation for another user');
      }

      // Validate required fields
      if (!data.title?.trim()) {
        throw new Error('Paper title is required');
      }

      const { data: result, error } = await supabase
        .from('paper_generations')
        .insert(data)
        .select()
        .single();

      if (error) {
        console.error('Error creating paper generation:', error);
        throw error;
      }

      return result;
    } catch (error) {
      console.error('Failed to create paper generation:', error);
      return null;
    }
  }

  /**
   * Get all paper generations for the current user
   */
  async getUserPaperGenerations(limit = 50): Promise<PaperGeneration[]> {
    try {
      const { data, error } = await supabase
        .from('paper_generations')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching paper generations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch paper generations:', error);
      return [];
    }
  }

  /**
   * Get a specific paper generation by ID
   */
  async getPaperGeneration(id: string): Promise<PaperGeneration | null> {
    try {
      const { data, error } = await supabase
        .from('paper_generations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching paper generation:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Failed to fetch paper generation:', error);
      return null;
    }
  }

  /**
   * Update a paper generation
   */
  async updatePaperGeneration(id: string, updates: Partial<PaperGeneration>): Promise<PaperGeneration | null> {
    try {
      const { data, error } = await supabase
        .from('paper_generations')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating paper generation:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to update paper generation:', error);
      return null;
    }
  }

  /**
   * Delete a paper generation
   */
  async deletePaperGeneration(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('paper_generations')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting paper generation:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to delete paper generation:', error);
      return false;
    }
  }

  /**
   * Create a new paper section
   */
  async createPaperSection(data: PaperSectionInsert): Promise<PaperSection | null> {
    try {
      // Validate user is authenticated
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User must be authenticated to create paper sections');
      }

      // Validate paper ownership using database function
      const { data: isOwner, error: ownershipError } = await supabase
        .rpc('validate_paper_ownership', { paper_id: data.paper_generation_id });

      if (ownershipError) {
        console.error('Error validating paper ownership:', ownershipError);
        throw new Error('Failed to validate paper ownership');
      }

      if (!isOwner) {
        throw new Error('Cannot create sections for papers you do not own');
      }

      // Validate required fields
      if (!data.section_name?.trim()) {
        throw new Error('Section name is required');
      }
      if (!data.prompt_text?.trim()) {
        throw new Error('Prompt text is required');
      }

      const { data: result, error } = await supabase
        .from('paper_sections')
        .insert(data)
        .select()
        .single();

      if (error) {
        console.error('Error creating paper section:', error);
        throw error;
      }

      return result;
    } catch (error) {
      console.error('Failed to create paper section:', error);
      return null;
    }
  }

  /**
   * Get all sections for a paper generation
   */
  async getPaperSections(paperGenerationId: string): Promise<PaperSection[]> {
    try {
      const { data, error } = await supabase
        .from('paper_sections')
        .select('*')
        .eq('paper_generation_id', paperGenerationId)
        .order('generation_order', { ascending: true });

      if (error) {
        console.error('Error fetching paper sections:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch paper sections:', error);
      return [];
    }
  }

  /**
   * Update a paper section
   */
  async updatePaperSection(id: string, updates: Partial<PaperSection>): Promise<PaperSection | null> {
    try {
      const { data, error } = await supabase
        .from('paper_sections')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating paper section:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to update paper section:', error);
      return null;
    }
  }

  /**
   * Create paper citations
   */
  async createPaperCitations(citations: PaperCitationInsert[]): Promise<PaperCitation[]> {
    try {
      const { data, error } = await supabase
        .from('paper_citations')
        .insert(citations)
        .select();

      if (error) {
        console.error('Error creating paper citations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to create paper citations:', error);
      return [];
    }
  }

  /**
   * Get all citations for a paper generation
   */
  async getPaperCitations(paperGenerationId: string): Promise<PaperCitation[]> {
    try {
      const { data, error } = await supabase
        .from('paper_citations')
        .select('*')
        .eq('paper_generation_id', paperGenerationId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching paper citations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch paper citations:', error);
      return [];
    }
  }

  /**
   * Get paper generation with all sections and citations
   */
  async getPaperGenerationWithDetails(id: string): Promise<{
    generation: PaperGeneration | null;
    sections: PaperSection[];
    citations: PaperCitation[];
  }> {
    try {
      const [generation, sections, citations] = await Promise.all([
        this.getPaperGeneration(id),
        this.getPaperSections(id),
        this.getPaperCitations(id)
      ]);

      return {
        generation,
        sections,
        citations
      };
    } catch (error) {
      console.error('Failed to fetch paper generation details:', error);
      return {
        generation: null,
        sections: [],
        citations: []
      };
    }
  }

  /**
   * Search paper generations by title
   */
  async searchPaperGenerations(query: string): Promise<PaperGeneration[]> {
    try {
      const { data, error } = await supabase
        .from('paper_generations')
        .select('*')
        .ilike('title', `%${query}%`)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error searching paper generations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to search paper generations:', error);
      return [];
    }
  }

  /**
   * Get the current user's paper count
   */
  async getUserPaperCount(): Promise<number> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return 0;
      }

      const { data, error } = await supabase.rpc('get_user_paper_count');

      if (error) {
        console.error('Error getting user paper count:', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      console.error('Failed to get user paper count:', error);
      return 0;
    }
  }

  /**
   * Validate that the current user owns a paper generation
   */
  async validatePaperOwnership(paperId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      const { data, error } = await supabase
        .rpc('validate_paper_ownership', { paper_id: paperId });

      if (error) {
        console.error('Error validating paper ownership:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Failed to validate paper ownership:', error);
      return false;
    }
  }
}

export const paperGenerationService = new PaperGenerationService();
