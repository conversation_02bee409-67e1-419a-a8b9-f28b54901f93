/**
 * Reference Validation AI Service
 * Uses AI to validate, extract, and format references correctly from Tavily data
 */

import { CitationSource } from './enhanced-citation-search.service';
import paperAIService from './paper-ai.service';

export interface ReferenceValidationResult {
  validatedCitations: ValidatedCitation[];
  formattedReferences: string;
  validationReport: ValidationReport;
}

export interface ValidatedCitation {
  id: string;
  originalCitation: CitationSource;
  extractedInfo: ExtractedReferenceInfo;
  formattedCitation: string;
  inTextCitation: string;
  validationScore: number;
  issues: string[];
}

export interface ExtractedReferenceInfo {
  authors: string[];
  year: number;
  title: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  publisher?: string;
  url: string;
}

export interface ValidationReport {
  totalCitations: number;
  validCitations: number;
  issuesFound: number;
  averageQuality: number;
  recommendations: string[];
}

export class ReferenceValidationAIService {
  /**
   * Validate and format all references using AI
   */
  async validateAndFormatReferences(
    citations: CitationSource[],
    model: string = "google/gemini-2.5-flash"
  ): Promise<ReferenceValidationResult> {
    console.log(`Starting AI reference validation for ${citations.length} citations...`);
    
    const validatedCitations: ValidatedCitation[] = [];
    const batchSize = 5; // Process in batches to avoid overwhelming the AI
    
    // Process citations in batches
    for (let i = 0; i < citations.length; i += batchSize) {
      const batch = citations.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(citations.length / batchSize)}`);
      
      try {
        const batchResults = await this.processCitationBatch(batch, model);
        validatedCitations.push(...batchResults);
      } catch (error) {
        console.error(`Failed to process batch ${Math.floor(i / batchSize) + 1}:`, error);
        // Add fallback processing for failed batch
        const fallbackResults = batch.map(citation => this.createFallbackValidation(citation));
        validatedCitations.push(...fallbackResults);
      }
    }
    
    // Generate final formatted references
    const formattedReferences = this.generateFinalReferences(validatedCitations);
    
    // Create validation report
    const validationReport = this.generateValidationReport(validatedCitations);
    
    console.log(`Reference validation completed: ${validationReport.validCitations}/${validationReport.totalCitations} valid`);
    
    return {
      validatedCitations,
      formattedReferences,
      validationReport
    };
  }
  
  /**
   * Process a batch of citations with AI
   */
  private async processCitationBatch(
    citations: CitationSource[],
    model: string
  ): Promise<ValidatedCitation[]> {
    const prompt = this.buildValidationPrompt(citations);
    
    const response = await paperAIService.generatePaperSection(prompt, {
      model,
      maxTokens: 4096,
      temperature: 0.1 // Low temperature for accuracy
    });
    
    return this.parseAIValidationResponse(response, citations);
  }
  
  /**
   * Build AI prompt for reference validation
   */
  private buildValidationPrompt(citations: CitationSource[]): string {
    let prompt = `You are an expert academic reference validator. Your task is to extract and format correct bibliographic information from the provided sources.

For each source, extract the following information accurately:
- Authors (in "LastName, FirstInitial." format)
- Publication year
- Complete title (cleaned of platform prefixes/suffixes)
- Journal name (if applicable)
- Volume and issue numbers (if available)
- DOI (if present)
- Publisher (if applicable)

IMPORTANT RULES:
1. Extract information ONLY from the provided content - do not invent details
2. Clean titles by removing "(PDF)", "ResearchGate", "ScienceDirect" suffixes
3. Format authors consistently as "LastName, F." or "LastName, F. M."
4. Identify real journal names from content, not generic placeholders
5. Extract real DOIs when present in the content
6. If information is missing or unclear, mark it as "Not Available"

Sources to validate:

`;

    citations.forEach((citation, index) => {
      prompt += `\n--- SOURCE ${index + 1} ---
Title: ${citation.title}
Authors: <AUTHORS>
Year: ${citation.year}
URL: ${citation.url}
Content Preview: ${citation.abstract?.substring(0, 300) || 'No content available'}
Current Journal: ${citation.journal || 'Not specified'}
Current DOI: ${citation.doi || 'Not specified'}

`;
    });
    
    prompt += `\nFor each source, provide the extracted information in this exact JSON format:
{
  "sourceIndex": 1,
  "extractedInfo": {
    "authors": ["LastName, F.", "LastName, F."],
    "year": 2023,
    "title": "Clean title without prefixes/suffixes",
    "journal": "Actual Journal Name or Not Available",
    "volume": "Volume number or Not Available",
    "issue": "Issue number or Not Available",
    "pages": "Page range or Not Available",
    "doi": "10.xxxx/xxxxx or Not Available",
    "publisher": "Publisher name or Not Available"
  },
  "validationScore": 0.85,
  "issues": ["List any issues found"],
  "recommendations": ["Suggestions for improvement"]
}

Provide the JSON for all sources, separated by "---NEXT---"`;

    return prompt;
  }
  
  /**
   * Parse AI validation response
   */
  private parseAIValidationResponse(
    response: string,
    originalCitations: CitationSource[]
  ): ValidatedCitation[] {
    const validatedCitations: ValidatedCitation[] = [];
    
    try {
      // Split response by separator
      const sections = response.split('---NEXT---');
      
      sections.forEach((section, index) => {
        if (index >= originalCitations.length) return;
        
        try {
          // Extract JSON from the section
          const jsonMatch = section.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsed = JSON.parse(jsonMatch[0]);
            const originalCitation = originalCitations[index];
            
            const validatedCitation: ValidatedCitation = {
              id: originalCitation.id,
              originalCitation,
              extractedInfo: {
                authors: parsed.extractedInfo.authors || originalCitation.authors,
                year: parsed.extractedInfo.year || originalCitation.year,
                title: parsed.extractedInfo.title || originalCitation.title,
                journal: parsed.extractedInfo.journal !== "Not Available" ? parsed.extractedInfo.journal : undefined,
                volume: parsed.extractedInfo.volume !== "Not Available" ? parsed.extractedInfo.volume : undefined,
                issue: parsed.extractedInfo.issue !== "Not Available" ? parsed.extractedInfo.issue : undefined,
                pages: parsed.extractedInfo.pages !== "Not Available" ? parsed.extractedInfo.pages : undefined,
                doi: parsed.extractedInfo.doi !== "Not Available" ? parsed.extractedInfo.doi : undefined,
                publisher: parsed.extractedInfo.publisher !== "Not Available" ? parsed.extractedInfo.publisher : undefined,
                url: originalCitation.url
              },
              formattedCitation: '',
              inTextCitation: '',
              validationScore: parsed.validationScore || 0.5,
              issues: parsed.issues || []
            };
            
            // Generate formatted citation and in-text citation
            validatedCitation.formattedCitation = this.formatValidatedCitation(validatedCitation.extractedInfo);
            validatedCitation.inTextCitation = this.formatInTextCitation(validatedCitation.extractedInfo);
            
            validatedCitations.push(validatedCitation);
          } else {
            // Fallback if JSON parsing fails
            validatedCitations.push(this.createFallbackValidation(originalCitations[index]));
          }
        } catch (error) {
          console.warn(`Failed to parse validation for citation ${index}:`, error);
          validatedCitations.push(this.createFallbackValidation(originalCitations[index]));
        }
      });
    } catch (error) {
      console.error('Failed to parse AI validation response:', error);
      // Return fallback validations for all citations
      return originalCitations.map(citation => this.createFallbackValidation(citation));
    }
    
    return validatedCitations;
  }
  
  /**
   * Create fallback validation when AI processing fails
   */
  private createFallbackValidation(citation: CitationSource): ValidatedCitation {
    return {
      id: citation.id,
      originalCitation: citation,
      extractedInfo: {
        authors: citation.authors,
        year: citation.year,
        title: this.cleanTitle(citation.title),
        journal: citation.journal,
        doi: citation.doi,
        url: citation.url
      },
      formattedCitation: citation.formattedCitation,
      inTextCitation: citation.inTextCitation,
      validationScore: 0.6,
      issues: ['AI validation failed - using original data']
    };
  }
  
  /**
   * Format validated citation in APA style
   */
  private formatValidatedCitation(info: ExtractedReferenceInfo): string {
    let citation = '';
    
    // Authors
    if (info.authors.length === 1) {
      citation += info.authors[0];
    } else if (info.authors.length === 2) {
      citation += `${info.authors[0]}, & ${info.authors[1]}`;
    } else if (info.authors.length > 2) {
      const lastAuthor = info.authors[info.authors.length - 1];
      const otherAuthors = info.authors.slice(0, -1).join(', ');
      citation += `${otherAuthors}, & ${lastAuthor}`;
    }
    
    // Year
    citation += ` (${info.year}). `;
    
    // Title
    citation += `${info.title}. `;
    
    // Journal with volume/issue
    if (info.journal) {
      citation += `*${info.journal}*`;
      if (info.volume) {
        citation += `, ${info.volume}`;
        if (info.issue) {
          citation += `(${info.issue})`;
        }
      }
      if (info.pages) {
        citation += `, ${info.pages}`;
      }
      citation += '. ';
    }
    
    // DOI or URL
    if (info.doi) {
      citation += `https://doi.org/${info.doi}`;
    } else {
      citation += info.url;
    }
    
    return citation;
  }
  
  /**
   * Format in-text citation
   */
  private formatInTextCitation(info: ExtractedReferenceInfo): string {
    if (info.authors.length === 1) {
      const surname = info.authors[0].split(',')[0];
      return `(${surname}, ${info.year})`;
    } else if (info.authors.length === 2) {
      const surname1 = info.authors[0].split(',')[0];
      const surname2 = info.authors[1].split(',')[0];
      return `(${surname1} & ${surname2}, ${info.year})`;
    } else {
      const surname = info.authors[0].split(',')[0];
      return `(${surname} et al., ${info.year})`;
    }
  }
  
  /**
   * Clean title by removing platform-specific prefixes/suffixes
   */
  private cleanTitle(title: string): string {
    return title
      .replace(/^\(PDF\)\s*/i, '')
      .replace(/\s*-\s*ResearchGate$/i, '')
      .replace(/\s*\|\s*ScienceDirect.*$/i, '')
      .replace(/\s*-\s*Google Scholar$/i, '')
      .replace(/\s*\.\.\.\s*$/, '')
      .replace(/\s+/g, ' ')
      .trim();
  }
  
  /**
   * Generate final formatted references
   */
  private generateFinalReferences(validatedCitations: ValidatedCitation[]): string {
    // Sort alphabetically by first author
    const sorted = validatedCitations.sort((a, b) => {
      const authorA = a.extractedInfo.authors[0]?.split(',')[0] || '';
      const authorB = b.extractedInfo.authors[0]?.split(',')[0] || '';
      return authorA.localeCompare(authorB);
    });
    
    let references = '';
    sorted.forEach(citation => {
      references += `<p>${citation.formattedCitation}</p>\n`;
    });
    
    // Add validation statistics
    const validCount = sorted.filter(c => c.validationScore > 0.7).length;
    const avgScore = Math.round(
      sorted.reduce((sum, c) => sum + c.validationScore, 0) / sorted.length * 100
    );
    
    references += `\n<p><em>Reference Validation: ${validCount}/${sorted.length} high-quality references | Average validation score: ${avgScore}% | AI-validated and formatted</em></p>`;
    
    return references.trim();
  }
  
  /**
   * Generate validation report
   */
  private generateValidationReport(validatedCitations: ValidatedCitation[]): ValidationReport {
    const totalCitations = validatedCitations.length;
    const validCitations = validatedCitations.filter(c => c.validationScore > 0.7).length;
    const issuesFound = validatedCitations.reduce((sum, c) => sum + c.issues.length, 0);
    const averageQuality = validatedCitations.reduce((sum, c) => sum + c.validationScore, 0) / totalCitations;
    
    const recommendations: string[] = [];
    if (validCitations / totalCitations < 0.8) {
      recommendations.push('Consider using more specific search terms for better source quality');
    }
    if (issuesFound > totalCitations * 0.5) {
      recommendations.push('Review sources manually for accuracy');
    }
    
    return {
      totalCitations,
      validCitations,
      issuesFound,
      averageQuality,
      recommendations
    };
  }
}

export const referenceValidationAIService = new ReferenceValidationAIService();
