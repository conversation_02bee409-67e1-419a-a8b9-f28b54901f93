import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Loader2,
  Database,
  Brain,
  BarChart3,
  MessageSquare,
  Upload,
  FileText,
  Clock,
  Zap,
  TrendingUp
} from "lucide-react";

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text,
  className = ""
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-500`} />
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </div>
  );
};

interface FileUploadLoadingProps {
  fileName: string;
  progress: number;
  stage: string;
}

export const FileUploadLoading: React.FC<FileUploadLoadingProps> = ({
  fileName,
  progress,
  stage
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5 text-blue-500" />
          Processing File
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <FileText className="h-5 w-5 text-gray-500" />
          <div className="flex-1">
            <p className="font-medium truncate">{fileName}</p>
            <p className="text-sm text-gray-500">{stage}</p>
          </div>
          <Badge variant="outline">{progress}%</Badge>
        </div>
        
        <Progress value={progress} className="w-full" />
        
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Clock className="h-4 w-4" />
          <span>This may take a few moments...</span>
        </div>
      </CardContent>
    </Card>
  );
};

interface AnalysisLoadingProps {
  currentStep: string;
  totalSteps: number;
  currentStepIndex: number;
  stepProgress?: number;
}

export const AnalysisLoading: React.FC<AnalysisLoadingProps> = ({
  currentStep,
  totalSteps,
  currentStepIndex,
  stepProgress = 0
}) => {
  const overallProgress = ((currentStepIndex + (stepProgress / 100)) / totalSteps) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-500 animate-pulse" />
          AI Analysis in Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="w-full" />
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-blue-500" />
            <span className="font-medium">{currentStep}</span>
          </div>
          <div className="flex justify-between text-sm text-gray-500">
            <span>Step {currentStepIndex + 1} of {totalSteps}</span>
            <span>{stepProgress}%</span>
          </div>
          <Progress value={stepProgress} className="w-full h-2" />
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Clock className="h-4 w-4" />
          <span>Analyzing your data with AI...</span>
        </div>
      </CardContent>
    </Card>
  );
};

interface QueryLoadingProps {
  query: string;
  stage: 'understanding' | 'generating' | 'executing' | 'formatting';
}

export const QueryLoading: React.FC<QueryLoadingProps> = ({
  query,
  stage
}) => {
  const stageInfo = {
    understanding: { icon: Brain, text: 'Understanding your question...', color: 'text-blue-500' },
    generating: { icon: Zap, text: 'Generating analysis code...', color: 'text-purple-500' },
    executing: { icon: TrendingUp, text: 'Executing analysis...', color: 'text-green-500' },
    formatting: { icon: BarChart3, text: 'Formatting results...', color: 'text-orange-500' }
  };

  const { icon: Icon, text, color } = stageInfo[stage];

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon className={`h-5 w-5 ${color} animate-pulse`} />
            <span className="font-medium">{text}</span>
          </div>
          
          <div className="bg-gray-100 p-3 rounded-lg">
            <p className="text-sm text-gray-700 italic">"{query}"</p>
          </div>
          
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <MessageSquare className="h-3 w-3" />
            <span>AI is processing your request...</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface VisualizationLoadingProps {
  count: number;
  currentIndex: number;
}

export const VisualizationLoading: React.FC<VisualizationLoadingProps> = ({
  count,
  currentIndex
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-green-500 animate-pulse" />
          Creating Visualizations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{currentIndex} of {count}</span>
          </div>
          <Progress value={(currentIndex / count) * 100} className="w-full" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: count }, (_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <TrendingUp className="h-4 w-4" />
          <span>Generating interactive charts...</span>
        </div>
      </CardContent>
    </Card>
  );
};

interface EmptyStateProps {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: React.ComponentType<{ className?: string }>;
  };
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  title,
  description,
  action,
  className = ""
}) => {
  return (
    <Card className={className}>
      <CardContent className="flex items-center justify-center h-64">
        <div className="text-center space-y-3">
          <Icon className="h-12 w-12 text-gray-400 mx-auto" />
          <div>
            <h3 className="font-medium text-gray-900">{title}</h3>
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          </div>
          {action && (
            <button
              onClick={action.onClick}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              {action.icon && <action.icon className="h-4 w-4" />}
              {action.label}
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Skeleton components for different sections
export const DataTableSkeleton: React.FC = () => (
  <div className="space-y-3">
    <div className="flex gap-4">
      {Array.from({ length: 4 }, (_, i) => (
        <Skeleton key={i} className="h-8 flex-1" />
      ))}
    </div>
    {Array.from({ length: 5 }, (_, i) => (
      <div key={i} className="flex gap-4">
        {Array.from({ length: 4 }, (_, j) => (
          <Skeleton key={j} className="h-6 flex-1" />
        ))}
      </div>
    ))}
  </div>
);

export const ChartSkeleton: React.FC = () => (
  <div className="space-y-3">
    <Skeleton className="h-6 w-1/3" />
    <Skeleton className="h-64 w-full" />
    <Skeleton className="h-4 w-1/2" />
  </div>
);

export const InsightsSkeleton: React.FC = () => (
  <div className="space-y-4">
    <Skeleton className="h-6 w-1/4" />
    {Array.from({ length: 3 }, (_, i) => (
      <div key={i} className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    ))}
  </div>
);
