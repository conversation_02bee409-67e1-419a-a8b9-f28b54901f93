/**
 * AI Prompt Store for Flowchart Fun
 * Manages AI generation state and interactions
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  GeminiFlowchartService, 
  FlowchartGenerationRequest,
  FlowchartGenerationOptions 
} from '../services/gemini-flowchart.service';

export type AIMode = 'prompt' | 'convert' | 'edit';

export interface AIPromptState {
  // UI State
  isOpen: boolean;
  mode: AIMode;
  currentText: string;
  
  // Generation State
  isGenerating: boolean;
  error: string | null;
  lastResult: string | null;
  
  // Diff State (for showing changes before applying)
  diff: string | null;
  
  // Service
  geminiService: GeminiFlowchartService;
}

export interface AIPromptActions {
  // UI Actions
  setIsOpen: (isOpen: boolean) => void;
  setMode: (mode: AIMode) => void;
  setCurrentText: (text: string) => void;
  
  // Generation Actions
  generateFlowchart: (flowchartText?: string) => Promise<void>;
  generateStreamingFlowchart: (flowchartText?: string, onChunk?: (chunk: string) => void) => Promise<void>;
  cancelGeneration: () => void;
  
  // Diff Actions
  setDiff: (diff: string | null) => void;
  acceptDiff: () => void;
  rejectDiff: () => void;
  
  // Error Actions
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Result Actions
  setLastResult: (result: string | null) => void;
}

export type AIPromptStore = AIPromptState & AIPromptActions;

// Create the store
export const useAIPromptStore = create<AIPromptStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      isOpen: false,
      mode: 'prompt',
      currentText: '',
      isGenerating: false,
      error: null,
      lastResult: null,
      diff: null,
      geminiService: new GeminiFlowchartService(),

      // UI Actions
      setIsOpen: (isOpen: boolean) => {
        set({ isOpen }, false, 'setIsOpen');
      },

      setMode: (mode: AIMode) => {
        set({ mode, currentText: '' }, false, 'setMode');
      },

      setCurrentText: (text: string) => {
        set({ currentText: text }, false, 'setCurrentText');
      },

      // Generation Actions
      generateFlowchart: async (flowchartText?: string) => {
        const state = get();
        
        if (state.isGenerating) {
          console.warn('Generation already in progress');
          return;
        }

        if (!state.geminiService.isServiceConfigured()) {
          set({ error: 'Gemini service not configured. Please check your API key.' }, false, 'generateFlowchart:error');
          return;
        }

        set({ 
          isGenerating: true, 
          error: null,
          isOpen: false 
        }, false, 'generateFlowchart:start');

        try {
          const request: FlowchartGenerationRequest = {
            prompt: state.currentText,
            mode: state.mode,
            currentText: flowchartText
          };

          const options: FlowchartGenerationOptions = {
            model: 'gemini-2.5-pro',
            temperature: 0.7,
            maxTokens: 4096
          };

          const response = await state.geminiService.generateFlowchart(request, options);

          if (response.success && response.flowchartText) {
            set({ 
              lastResult: response.flowchartText,
              diff: response.flowchartText,
              isGenerating: false 
            }, false, 'generateFlowchart:success');
          } else {
            set({ 
              error: response.error || 'Failed to generate flowchart',
              isGenerating: false 
            }, false, 'generateFlowchart:error');
          }
        } catch (error) {
          console.error('Flowchart generation error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            isGenerating: false 
          }, false, 'generateFlowchart:error');
        }
      },

      generateStreamingFlowchart: async (flowchartText?: string, onChunk?: (chunk: string) => void) => {
        const state = get();
        
        if (state.isGenerating) {
          console.warn('Generation already in progress');
          return;
        }

        if (!state.geminiService.isServiceConfigured()) {
          set({ error: 'Gemini service not configured. Please check your API key.' }, false, 'generateStreamingFlowchart:error');
          return;
        }

        set({ 
          isGenerating: true, 
          error: null,
          isOpen: false,
          diff: null
        }, false, 'generateStreamingFlowchart:start');

        try {
          const request: FlowchartGenerationRequest = {
            prompt: state.currentText,
            mode: state.mode,
            currentText: flowchartText
          };

          const options: FlowchartGenerationOptions = {
            model: 'gemini-2.5-pro',
            temperature: 0.7,
            maxTokens: 4096
          };

          let accumulatedText = '';
          
          for await (const chunk of state.geminiService.generateStreamingFlowchart(request, options)) {
            accumulatedText += chunk;
            
            // Call the chunk callback if provided
            if (onChunk) {
              onChunk(chunk);
            }
            
            // Update the diff with accumulated text
            set({ diff: accumulatedText }, false, 'generateStreamingFlowchart:chunk');
          }

          set({ 
            lastResult: accumulatedText,
            isGenerating: false 
          }, false, 'generateStreamingFlowchart:complete');

        } catch (error) {
          console.error('Streaming flowchart generation error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            isGenerating: false 
          }, false, 'generateStreamingFlowchart:error');
        }
      },

      cancelGeneration: () => {
        set({ 
          isGenerating: false,
          error: 'Generation cancelled by user'
        }, false, 'cancelGeneration');
      },

      // Diff Actions
      setDiff: (diff: string | null) => {
        set({ diff }, false, 'setDiff');
      },

      acceptDiff: () => {
        const state = get();
        if (state.diff) {
          // The diff will be applied by the parent component
          set({ 
            diff: null, 
            currentText: '',
            lastResult: state.diff
          }, false, 'acceptDiff');
        }
      },

      rejectDiff: () => {
        set({ 
          diff: null, 
          currentText: '' 
        }, false, 'rejectDiff');
      },

      // Error Actions
      setError: (error: string | null) => {
        set({ error }, false, 'setError');
      },

      clearError: () => {
        set({ error: null }, false, 'clearError');
      },

      // Result Actions
      setLastResult: (result: string | null) => {
        set({ lastResult: result }, false, 'setLastResult');
      }
    }),
    {
      name: 'ai-prompt-store',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);

// Convenience hooks for specific actions
export const useAIGeneration = () => {
  const store = useAIPromptStore();
  return {
    generateFlowchart: store.generateFlowchart,
    generateStreamingFlowchart: store.generateStreamingFlowchart,
    cancelGeneration: store.cancelGeneration,
    isGenerating: store.isGenerating,
    error: store.error,
    lastResult: store.lastResult
  };
};

export const useAIDiff = () => {
  const store = useAIPromptStore();
  return {
    diff: store.diff,
    acceptDiff: store.acceptDiff,
    rejectDiff: store.rejectDiff,
    setDiff: store.setDiff
  };
};

export const useAIUI = () => {
  const store = useAIPromptStore();
  return {
    isOpen: store.isOpen,
    mode: store.mode,
    currentText: store.currentText,
    setIsOpen: store.setIsOpen,
    setMode: store.setMode,
    setCurrentText: store.setCurrentText
  };
};
