/**
 * Quiz Generator Component
 * Beautiful interface for generating quizzes from various sources
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Wand2,
  BookOpen,
  Search,
  Brain,
  FileText,
  Settings,
  Loader2,
  Sparkles,
  Target,
  Clock,
  HelpCircle,
  Lightbulb
} from "lucide-react";
import { Quiz, EducationLevel, DifficultyLevel } from '../types';
import { quizGeneratorService } from '../services/quiz-generator.service';
import { toast } from 'sonner';

interface QuizGeneratorProps {
  onQuizGenerated: (quiz: Quiz) => void;
  documentContent?: string;
  documentTitle?: string;
}

interface QuizOptions {
  source: 'ai-knowledge' | 'document' | 'web-search' | 'mixed';
  topic: string;
  educationLevel: EducationLevel;
  difficulty: DifficultyLevel;
  questionCount: number;
  questionTypes: string[];
  includeExplanations: boolean;
  includeHints: boolean;
  timeLimit: number;
}

const educationLevels = [
  { value: 'basic', label: 'Basic Level' },
  { value: 'intermediate', label: 'Intermediate Level' },
  { value: 'advanced', label: 'Advanced Level' },
  { value: 'expert', label: 'Expert Level' }
];

const difficultyLevels = [
  { value: 'easy', label: 'Easy', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'hard', label: 'Hard', color: 'bg-red-100 text-red-800' }
];

const questionTypes = [
  { value: 'multiple-choice', label: 'Multiple Choice', icon: Target },
  { value: 'true-false', label: 'True/False', icon: HelpCircle },
  { value: 'short-answer', label: 'Short Answer', icon: FileText },
  { value: 'essay', label: 'Essay', icon: BookOpen }
];

const sourceOptions = [
  { 
    value: 'ai-knowledge', 
    label: 'AI Knowledge', 
    description: 'Generate quiz from AI\'s knowledge base',
    icon: Brain 
  },
  { 
    value: 'document', 
    label: 'Document Content', 
    description: 'Generate quiz from uploaded document',
    icon: FileText 
  },
  { 
    value: 'web-search', 
    label: 'Web Search', 
    description: 'Generate quiz with current web information',
    icon: Search 
  },
  { 
    value: 'mixed', 
    label: 'Mixed Sources', 
    description: 'Combine document and web search',
    icon: Sparkles 
  }
];

export function QuizGenerator({ onQuizGenerated, documentContent, documentTitle }: QuizGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [options, setOptions] = useState<QuizOptions>({
    source: 'ai-knowledge',
    topic: '',
    educationLevel: 'high-school',
    difficulty: 'medium',
    questionCount: 10,
    questionTypes: ['multiple-choice', 'true-false'],
    includeExplanations: true,
    includeHints: false,
    timeLimit: 30
  });

  const handleGenerateQuiz = async () => {
    if (!options.topic.trim()) {
      toast.error('Please enter a topic for the quiz');
      return;
    }

    if (options.source === 'document' && !documentContent) {
      toast.error('No document content available. Please upload a document first.');
      return;
    }

    setIsGenerating(true);

    try {
      let quiz: Quiz;

      switch (options.source) {
        case 'document':
          // For now, use topic-based generation with document context
          quiz = await quizGeneratorService.generateQuizFromTopic(
            options.topic,
            options.educationLevel,
            {
              questionCount: options.questionCount,
              questionTypes: options.questionTypes as any,
              difficulty: options.difficulty,
              educationLevel: options.educationLevel,
              includeExplanations: options.includeExplanations,
              timeLimit: options.timeLimit
            }
          );
          break;

        case 'web-search':
          quiz = await quizGeneratorService.generateQuizWithSearch(
            options.topic,
            options.educationLevel,
            {
              questionCount: options.questionCount,
              questionTypes: options.questionTypes as any,
              difficulty: options.difficulty,
              educationLevel: options.educationLevel,
              includeExplanations: options.includeExplanations,
              timeLimit: options.timeLimit
            }
          );
          break;

        case 'mixed':
          quiz = await quizGeneratorService.generateQuizWithSearch(
            options.topic,
            options.educationLevel,
            {
              questionCount: options.questionCount,
              questionTypes: options.questionTypes as any,
              difficulty: options.difficulty,
              educationLevel: options.educationLevel,
              includeExplanations: options.includeExplanations,
              timeLimit: options.timeLimit
            }
          );
          break;

        case 'ai-knowledge':
        default:
          quiz = await quizGeneratorService.generateQuizFromTopic(
            options.topic,
            options.educationLevel,
            {
              questionCount: options.questionCount,
              questionTypes: options.questionTypes as any,
              difficulty: options.difficulty,
              educationLevel: options.educationLevel,
              includeExplanations: options.includeExplanations,
              timeLimit: options.timeLimit
            }
          );
          break;
      }

      onQuizGenerated(quiz);
      toast.success('Quiz generated successfully!');

    } catch (error) {
      console.error('Quiz generation failed:', error);
      toast.error('Failed to generate quiz. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleQuestionTypeChange = (type: string, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      questionTypes: checked
        ? [...prev.questionTypes, type]
        : prev.questionTypes.filter(t => t !== type)
    }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
              <Wand2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl">Quiz Generator</CardTitle>
              <p className="text-gray-600">Create beautiful, interactive quizzes for learning</p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Source Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Quiz Source</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {sourceOptions.map((source) => (
                <Card
                  key={source.value}
                  className={`cursor-pointer transition-all ${
                    options.source === source.value
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setOptions(prev => ({ ...prev, source: source.value as any }))}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <source.icon className="w-5 h-5 text-blue-600" />
                      <div>
                        <div className="font-medium">{source.label}</div>
                        <div className="text-sm text-gray-600">{source.description}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Separator />

          {/* Topic */}
          <div className="space-y-2">
            <Label htmlFor="topic">Topic</Label>
            <Input
              id="topic"
              value={options.topic}
              onChange={(e) => setOptions(prev => ({ ...prev, topic: e.target.value }))}
              placeholder="Enter the topic for your quiz (e.g., 'Photosynthesis', 'World War II', 'Calculus')"
              className="text-base"
            />
          </div>

          {/* Education Level and Difficulty */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Education Level</Label>
              <Select
                value={options.educationLevel}
                onValueChange={(value) => setOptions(prev => ({ ...prev, educationLevel: value as EducationLevel }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Difficulty</Label>
              <Select
                value={options.difficulty}
                onValueChange={(value) => setOptions(prev => ({ ...prev, difficulty: value as DifficultyLevel }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {difficultyLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      <div className="flex items-center space-x-2">
                        <Badge className={level.color}>{level.label}</Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Question Count and Time Limit */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="questionCount">Number of Questions</Label>
              <Input
                id="questionCount"
                type="number"
                min="1"
                max="50"
                value={options.questionCount}
                onChange={(e) => setOptions(prev => ({ ...prev, questionCount: parseInt(e.target.value) || 10 }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
              <Input
                id="timeLimit"
                type="number"
                min="5"
                max="120"
                value={options.timeLimit}
                onChange={(e) => setOptions(prev => ({ ...prev, timeLimit: parseInt(e.target.value) || 30 }))}
              />
            </div>
          </div>

          {/* Question Types */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Question Types</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {questionTypes.map((type) => (
                <div key={type.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={type.value}
                    checked={options.questionTypes.includes(type.value)}
                    onCheckedChange={(checked) => handleQuestionTypeChange(type.value, checked as boolean)}
                  />
                  <Label htmlFor={type.value} className="flex items-center space-x-2 cursor-pointer">
                    <type.icon className="w-4 h-4" />
                    <span>{type.label}</span>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Options</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="explanations"
                  checked={options.includeExplanations}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeExplanations: checked as boolean }))}
                />
                <Label htmlFor="explanations" className="flex items-center space-x-2 cursor-pointer">
                  <Lightbulb className="w-4 h-4" />
                  <span>Include explanations for answers</span>
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hints"
                  checked={options.includeHints}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeHints: checked as boolean }))}
                />
                <Label htmlFor="hints" className="flex items-center space-x-2 cursor-pointer">
                  <HelpCircle className="w-4 h-4" />
                  <span>Include hints for difficult questions</span>
                </Label>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="pt-4">
            <Button
              onClick={handleGenerateQuiz}
              disabled={isGenerating || !options.topic.trim() || options.questionTypes.length === 0}
              className="w-full h-12 text-base"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Quiz...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                  Generate Quiz
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
