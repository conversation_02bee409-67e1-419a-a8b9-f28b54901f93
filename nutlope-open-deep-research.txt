Directory structure:
└── nutlope-open-deep-research/
    ├── README.md
    ├── components.json
    ├── drizzle.config.ts
    ├── next.config.ts
    ├── package.json
    ├── pnpm-lock.yaml
    ├── postcss.config.mjs
    ├── tsconfig.json
    ├── .example.env
    ├── .prettierrc
    └── src/
        ├── middleware.ts
        ├── app/
        │   ├── globals.css
        │   ├── LandingHomepage.tsx
        │   ├── layout.tsx
        │   ├── page.tsx
        │   ├── api/
        │   │   ├── cancel/
        │   │   │   └── route.ts
        │   │   ├── chats/
        │   │   │   └── route.ts
        │   │   ├── pdf/
        │   │   │   └── route.ts
        │   │   ├── research/
        │   │   │   └── route.ts
        │   │   ├── storeAnswers/
        │   │   │   └── route.ts
        │   │   ├── user/
        │   │   │   └── limits/
        │   │   │       └── route.ts
        │   │   ├── validate-key/
        │   │   │   └── route.ts
        │   │   └── workflows/
        │   │       └── [...any]/
        │   │           └── route.ts
        │   └── chat/
        │       └── [chatId]/
        │           ├── loading.tsx
        │           └── page.tsx
        ├── components/
        │   ├── ChatInput.tsx
        │   ├── CustomMarkdown.tsx
        │   ├── deploy-button.tsx
        │   ├── FaviconImage.tsx
        │   ├── Footer.tsx
        │   ├── Header.tsx
        │   ├── Heading.tsx
        │   ├── icons.tsx
        │   ├── LandingCard.tsx
        │   ├── LandingHero.tsx
        │   ├── star-button.tsx
        │   ├── app/
        │   │   ├── ApiKeyControls.tsx
        │   │   ├── AppSidebar.tsx
        │   │   ├── ChatPage.tsx
        │   │   ├── DownloadPdfButton.tsx
        │   │   ├── FinalReportPage.tsx
        │   │   ├── ReportBody.tsx
        │   │   ├── ReportLoadingPage.tsx
        │   │   ├── TableOfContents.tsx
        │   │   ├── citations/
        │   │   │   ├── CitationNumber.tsx
        │   │   │   └── CitationTooltip.tsx
        │   │   ├── questions/
        │   │   │   ├── AnswerInput.tsx
        │   │   │   └── QuestionsPage.tsx
        │   │   ├── reportLoading/
        │   │   │   ├── ReportSteps.tsx
        │   │   │   ├── StreamingReportBody.tsx
        │   │   │   ├── TimelineEvent.tsx
        │   │   │   ├── TimelineEventLoader.tsx
        │   │   │   ├── TimelineProgress.tsx
        │   │   │   └── WebResultCard.tsx
        │   │   └── tooltip/
        │   │       └── TooltipUsage.tsx
        │   └── ui/
        │       ├── accordion.tsx
        │       ├── badge.tsx
        │       ├── button.tsx
        │       ├── input.tsx
        │       ├── popover.tsx
        │       ├── scroll-area.tsx
        │       ├── separator.tsx
        │       ├── sheet.tsx
        │       ├── sidebar.tsx
        │       ├── skeleton.tsx
        │       └── tooltip.tsx
        ├── db/
        │   ├── action.ts
        │   ├── index.ts
        │   └── schema.ts
        ├── deepresearch/
        │   ├── apiClients.ts
        │   ├── config.ts
        │   ├── schemas.ts
        │   ├── startResearch.ts
        │   ├── storage.ts
        │   ├── webSearch.ts
        │   └── workflows/
        │       ├── gather-search-workflow.ts
        │       └── start-research-workflow.ts
        ├── hooks/
        │   └── use-mobile.ts
        └── lib/
            ├── clients.ts
            ├── getChats.ts
            ├── limits.ts
            └── utils.ts

================================================
FILE: README.md
================================================
<a href="https://www.opendeepresearch.dev/">
<img alt="Open Deep Research" src="./public/og.jpg">
</a>

<div align="center">
    <h1>Open Deep Research</h1>
    <p>
        AI-powered research reports. Ask a question, get a comprehensive, sourced answer.
    </p>
</div>

## Tech Stack

- **Next.js 15** with App Router for modern web development
- **Together.ai** for advanced LLM research, planning, and summarization
- **Clerk** for authentication
- **Drizzle ORM** and **Neon** for database management
- **Amazon S3** for secure image storage
- **Upstash QStash/Redis** for workflow orchestration and state
- **Vercel** for seamless deployment and hosting

## How it works

1. User asks a research question
2. The app generates a research plan and search queries using Together.ai
3. It performs iterative web searches, summarizes results, and evaluates if more research is needed
4. The app generates a comprehensive report, including sources and a cover image
5. The final report is stored and displayed to the user

## Cloning & running

1. Fork or clone the repo
2. Create accounts at [Together.ai](https://togetherai.link) and [AWS](https://aws.amazon.com/) for LLM and S3
3. Set up Clerk for authentication ([Clerk.dev](https://clerk.dev/))
4. Create a `.env` file (use `.example.env` for reference) and add your API keys
5. Run `pnpm install` and `pnpm run dev` to install dependencies and start the app locally



================================================
FILE: components.json
================================================
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "",
    "css": "src/app/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  },
  "iconLibrary": "lucide"
}


================================================
FILE: drizzle.config.ts
================================================
import "dotenv/config";
import { defineConfig } from "drizzle-kit";

export default defineConfig({
  out: "./drizzle",
  schema: "./src/db/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
});



================================================
FILE: next.config.ts
================================================
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
};

export default nextConfig;



================================================
FILE: package.json
================================================
{
  "name": "deepresearch",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db": "npx drizzle-kit push",
    "workflow": "npx @upstash/qstash-cli dev"
  },
  "dependencies": {
    "@ai-sdk/react": "^1.2.12",
    "@ai-sdk/togetherai": "^0.2.14",
    "@aws-sdk/client-s3": "^3.817.0",
    "@clerk/nextjs": "^6.20.2",
    "@mendable/firecrawl-js": "^1.25.5",
    "@neondatabase/serverless": "^1.0.0",
    "@radix-ui/react-accordion": "^1.2.11",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-popover": "^1.1.14",
    "@radix-ui/react-scroll-area": "^1.2.9",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-tooltip": "^1.2.7",
    "@sparticuz/chromium": "^133.0.0",
    "@upstash/qstash": "^2.8.1",
    "@upstash/ratelimit": "^2.0.5",
    "@upstash/redis": "^1.34.9",
    "@upstash/workflow": "^0.2.13",
    "ai": "^4.3.16",
    "class-variance-authority": "^0.7.1",
    "classnames": "^2.5.1",
    "clsx": "^2.1.1",
    "date-fns": "^4.1.0",
    "dedent": "^1.6.0",
    "dotenv": "^16.5.0",
    "drizzle-orm": "^0.43.1",
    "framer-motion": "^12.9.1",
    "lucide-react": "^0.503.0",
    "nanoid": "^5.1.5",
    "next": "15.3.2",
    "next-plausible": "^3.12.4",
    "puppeteer-core": "^24.10.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-icons": "^5.5.0",
    "react-markdown": "^10.1.0",
    "remark-gfm": "^4.0.1",
    "sonner": "^2.0.3",
    "tailwind-merge": "^3.3.0",
    "together-ai": "^0.16.0",
    "zod": "^3.25.20"
  },
  "devDependencies": {
    "@tailwindcss/postcss": "^4",
    "@types/node": "^20",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "drizzle-kit": "^0.31.1",
    "tailwindcss": "^4.1.4",
    "tsx": "^4.19.4",
    "tw-animate-css": "^1.3.1",
    "typescript": "^5.8.3"
  }
}



================================================
FILE: pnpm-lock.yaml
================================================
lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@ai-sdk/react':
        specifier: ^1.2.12
        version: 1.2.12(react@19.1.0)(zod@3.25.20)
      '@ai-sdk/togetherai':
        specifier: ^0.2.14
        version: 0.2.14(zod@3.25.20)
      '@aws-sdk/client-s3':
        specifier: ^3.817.0
        version: 3.817.0
      '@clerk/nextjs':
        specifier: ^6.20.2
        version: 6.20.2(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@mendable/firecrawl-js':
        specifier: ^1.25.5
        version: 1.25.5
      '@neondatabase/serverless':
        specifier: ^1.0.0
        version: 1.0.0
      '@radix-ui/react-accordion':
        specifier: ^1.2.11
        version: 1.2.11(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dialog':
        specifier: ^1.1.14
        version: 1.1.14(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-popover':
        specifier: ^1.1.14
        version: 1.1.14(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-scroll-area':
        specifier: ^1.2.9
        version: 1.2.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-separator':
        specifier: ^1.1.7
        version: 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot':
        specifier: ^1.2.3
        version: 1.2.3(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-tooltip':
        specifier: ^1.2.7
        version: 1.2.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@sparticuz/chromium':
        specifier: ^133.0.0
        version: 133.0.0
      '@upstash/qstash':
        specifier: ^2.8.1
        version: 2.8.1
      '@upstash/ratelimit':
        specifier: ^2.0.5
        version: 2.0.5(@upstash/redis@1.34.9)
      '@upstash/redis':
        specifier: ^1.34.9
        version: 1.34.9
      '@upstash/workflow':
        specifier: ^0.2.13
        version: 0.2.13(react@19.1.0)
      ai:
        specifier: ^4.3.16
        version: 4.3.16(react@19.1.0)(zod@3.25.20)
      class-variance-authority:
        specifier: ^0.7.1
        version: 0.7.1
      classnames:
        specifier: ^2.5.1
        version: 2.5.1
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      date-fns:
        specifier: ^4.1.0
        version: 4.1.0
      dedent:
        specifier: ^1.6.0
        version: 1.6.0
      dotenv:
        specifier: ^16.5.0
        version: 16.5.0
      drizzle-orm:
        specifier: ^0.43.1
        version: 0.43.1(@neondatabase/serverless@1.0.0)(@opentelemetry/api@1.9.0)(@types/pg@8.15.2)
      framer-motion:
        specifier: ^12.9.1
        version: 12.9.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      lucide-react:
        specifier: ^0.503.0
        version: 0.503.0(react@19.1.0)
      nanoid:
        specifier: ^5.1.5
        version: 5.1.5
      next:
        specifier: 15.3.2
        version: 15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      next-plausible:
        specifier: ^3.12.4
        version: 3.12.4(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      puppeteer-core:
        specifier: ^24.10.0
        version: 24.10.0
      react:
        specifier: ^19.1.0
        version: 19.1.0
      react-dom:
        specifier: ^19.1.0
        version: 19.1.0(react@19.1.0)
      react-icons:
        specifier: ^5.5.0
        version: 5.5.0(react@19.1.0)
      react-markdown:
        specifier: ^10.1.0
        version: 10.1.0(@types/react@19.1.2)(react@19.1.0)
      remark-gfm:
        specifier: ^4.0.1
        version: 4.0.1
      sonner:
        specifier: ^2.0.3
        version: 2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      tailwind-merge:
        specifier: ^3.3.0
        version: 3.3.0
      together-ai:
        specifier: ^0.16.0
        version: 0.16.0
      zod:
        specifier: ^3.25.20
        version: 3.25.20
    devDependencies:
      '@tailwindcss/postcss':
        specifier: ^4
        version: 4.1.3
      '@types/node':
        specifier: ^20
        version: 20.17.30
      '@types/react':
        specifier: ^19.1.2
        version: 19.1.2
      '@types/react-dom':
        specifier: ^19.1.2
        version: 19.1.2(@types/react@19.1.2)
      drizzle-kit:
        specifier: ^0.31.1
        version: 0.31.1
      tailwindcss:
        specifier: ^4.1.4
        version: 4.1.4
      tsx:
        specifier: ^4.19.4
        version: 4.19.4
      tw-animate-css:
        specifier: ^1.3.1
        version: 1.3.1
      typescript:
        specifier: ^5.8.3
        version: 5.8.3

packages:

  '@ai-sdk/openai-compatible@0.2.14':
    resolution: {integrity: sha512-icjObfMCHKSIbywijaoLdZ1nSnuRnWgMEMLgwoxPJgxsUHMx0aVORnsLUid4SPtdhHI3X2masrt6iaEQLvOSFw==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0

  '@ai-sdk/openai@1.3.19':
    resolution: {integrity: sha512-VB0ZutZwb6LJBIAV5pJZsSuL3O+GeTAeG4OifRpf014nJF/QlbZIWDvEWfcTq65d85fO/vg1NTv8jVKYRi+JFQ==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0

  '@ai-sdk/provider-utils@2.2.7':
    resolution: {integrity: sha512-kM0xS3GWg3aMChh9zfeM+80vEZfXzR3JEUBdycZLtbRZ2TRT8xOj3WodGHPb06sUK5yD7pAXC/P7ctsi2fvUGQ==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.23.8

  '@ai-sdk/provider-utils@2.2.8':
    resolution: {integrity: sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.23.8

  '@ai-sdk/provider@1.1.3':
    resolution: {integrity: sha512-qZMxYJ0qqX/RfnuIaab+zp8UAeJn/ygXXAffR5I4N0n1IrvA6qBsjc8hXLmBiMV2zoXlifkacF7sEFnYnjBcqg==}
    engines: {node: '>=18'}

  '@ai-sdk/react@1.2.12':
    resolution: {integrity: sha512-jK1IZZ22evPZoQW3vlkZ7wvjYGYF+tRBKXtrcolduIkQ/m/sOAVcVeVDUDvh1T91xCnWCdUGCPZg2avZ90mv3g==}
    engines: {node: '>=18'}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      zod: ^3.23.8
    peerDependenciesMeta:
      zod:
        optional: true

  '@ai-sdk/togetherai@0.2.14':
    resolution: {integrity: sha512-tdCe5kawsQrnMZB8aF1iL1P3+NfrPwILH9UudAZ5cNVY5dTFl+mXk3R37YJS+mkWeAoedr8Cb7mAuGxR47bsFw==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0

  '@ai-sdk/ui-utils@1.2.11':
    resolution: {integrity: sha512-3zcwCc8ezzFlwp3ZD15wAPjf2Au4s3vAbKsXQVyhxODHcmu0iyPO2Eua6D/vicq/AUm/BAo60r97O6HU+EI0+w==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.23.8

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  '@aws-crypto/crc32@5.2.0':
    resolution: {integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/crc32c@5.2.0':
    resolution: {integrity: sha512-+iWb8qaHLYKrNvGRbiYRHSdKRWhto5XlZUEBwDjYNf+ly5SVYG6zEoYIdxvf5R3zyeP16w4PLBn3rH1xc74Rag==}

  '@aws-crypto/sha1-browser@5.2.0':
    resolution: {integrity: sha512-OH6lveCFfcDjX4dbAvCFSYUjJZjDr/3XJ3xHtjn3Oj5b9RjojQo8npoLeA/bNwkOkrSQ0wgrHzXk4tDRxGKJeg==}

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-s3@3.817.0':
    resolution: {integrity: sha512-nZyjhlLMEXDs0ofWbpikI8tKoeKuuSgYcIb6eEZJk90Nt5HkkXn6nkWOs/kp2FdhpoGJyTILOVsDgdm7eutnLA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/client-sso@3.817.0':
    resolution: {integrity: sha512-fCh5rUHmWmWDvw70NNoWpE5+BRdtNi45kDnIoeoszqVg7UKF79SlG+qYooUT52HKCgDNHqgbWaXxMOSqd2I/OQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/core@3.816.0':
    resolution: {integrity: sha512-Lx50wjtyarzKpMFV6V+gjbSZDgsA/71iyifbClGUSiNPoIQ4OCV0KVOmAAj7mQRVvGJqUMWKVM+WzK79CjbjWA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-env@3.816.0':
    resolution: {integrity: sha512-wUJZwRLe+SxPxRV9AENYBLrJZRrNIo+fva7ZzejsC83iz7hdfq6Rv6B/aHEdPwG/nQC4+q7UUvcRPlomyrpsBA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-http@3.816.0':
    resolution: {integrity: sha512-gcWGzMQ7yRIF+ljTkR8Vzp7727UY6cmeaPrFQrvcFB8PhOqWpf7g0JsgOf5BSaP8CkkSQcTQHc0C5ZYAzUFwPg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-ini@3.817.0':
    resolution: {integrity: sha512-kyEwbQyuXE+phWVzloMdkFv6qM6NOon+asMXY5W0fhDKwBz9zQLObDRWBrvQX9lmqq8BbDL1sCfZjOh82Y+RFw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-node@3.817.0':
    resolution: {integrity: sha512-b5mz7av0Lhavs1Bz3Zb+jrs0Pki93+8XNctnVO0drBW98x1fM4AR38cWvGbM/w9F9Q0/WEH3TinkmrMPrP4T/w==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-process@3.816.0':
    resolution: {integrity: sha512-9Tm+AxMoV2Izvl5b9tyMQRbBwaex8JP06HN7ZeCXgC5sAsSN+o8dsThnEhf8jKN+uBpT6CLWKN1TXuUMrAmW1A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-sso@3.817.0':
    resolution: {integrity: sha512-gFUAW3VmGvdnueK1bh6TOcRX+j99Xm0men1+gz3cA4RE+rZGNy1Qjj8YHlv0hPwI9OnTPZquvPzA5fkviGREWg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.817.0':
    resolution: {integrity: sha512-A2kgkS9g6NY0OMT2f2EdXHpL17Ym81NhbGnQ8bRXPqESIi7TFypFD2U6osB2VnsFv+MhwM+Ke4PKXSmLun22/A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-bucket-endpoint@3.808.0':
    resolution: {integrity: sha512-wEPlNcs8dir9lXbuviEGtSzYSxG/NRKQrJk5ybOc7OpPGHovsN+QhDOdY3lcjOFdwMTiMIG9foUkPz3zBpLB1A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-expect-continue@3.804.0':
    resolution: {integrity: sha512-YW1hySBolALMII6C8y7Z0CRG2UX1dGJjLEBNFeefhO/xP7ZuE1dvnmfJGaEuBMnvc3wkRS63VZ3aqX6sevM1CA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-flexible-checksums@3.816.0':
    resolution: {integrity: sha512-kftcwDxB/VoCBsUiRgkm5CIuKbTfCN1WLPbis9LRwX3kQhKgGVxG2gG78SHk4TBB0qviWVAd/t+i/KaUgwiAcA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-host-header@3.804.0':
    resolution: {integrity: sha512-bum1hLVBrn2lJCi423Z2fMUYtsbkGI2s4N+2RI2WSjvbaVyMSv/WcejIrjkqiiMR+2Y7m5exgoKeg4/TODLDPQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-location-constraint@3.804.0':
    resolution: {integrity: sha512-AMtKnllIWKgoo7hiJfphLYotEwTERfjVMO2+cKAncz9w1g+bnYhHxiVhJJoR94y047c06X4PU5MsTxvdQ73Znw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-logger@3.804.0':
    resolution: {integrity: sha512-w/qLwL3iq0KOPQNat0Kb7sKndl9BtceigINwBU7SpkYWX9L/Lem6f8NPEKrC9Tl4wDBht3Yztub4oRTy/horJA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.804.0':
    resolution: {integrity: sha512-zqHOrvLRdsUdN/ehYfZ9Tf8svhbiLLz5VaWUz22YndFv6m9qaAcijkpAOlKexsv3nLBMJdSdJ6GUTAeIy3BZzw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-sdk-s3@3.816.0':
    resolution: {integrity: sha512-jJ+EAXM7gnOwiCM6rrl4AUNY5urmtIsX7roTkxtb4DevJxcS+wFYRRg3/j33fQbuxQZrvk21HqxyZYx5UH70PA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-ssec@3.804.0':
    resolution: {integrity: sha512-Tk8jK0gOIUBvEPTz/wwSlP1V70zVQ3QYqsLPAjQRMO6zfOK9ax31dln3MgKvFDJxBydS2tS3wsn53v+brxDxTA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-user-agent@3.816.0':
    resolution: {integrity: sha512-bHRSlWZ0xDsFR8E2FwDb//0Ff6wMkVx4O+UKsfyNlAbtqCiiHRt5ANNfKPafr95cN2CCxLxiPvFTFVblQM5TsQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/nested-clients@3.817.0':
    resolution: {integrity: sha512-vQ2E06A48STJFssueJQgxYD8lh1iGJoLJnHdshRDWOQb8gy1wVQR+a7MkPGhGR6lGoS0SCnF/Qp6CZhnwLsqsQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/region-config-resolver@3.808.0':
    resolution: {integrity: sha512-9x2QWfphkARZY5OGkl9dJxZlSlYM2l5inFeo2bKntGuwg4A4YUe5h7d5yJ6sZbam9h43eBrkOdumx03DAkQF9A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/signature-v4-multi-region@3.816.0':
    resolution: {integrity: sha512-idcr9NW86sSIXASSej3423Selu6fxlhhJJtMgpAqoCH/HJh1eQrONJwNKuI9huiruPE8+02pwxuePvLW46X2mw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/token-providers@3.817.0':
    resolution: {integrity: sha512-CYN4/UO0VaqyHf46ogZzNrVX7jI3/CfiuktwKlwtpKA6hjf2+ivfgHSKzPpgPBcSEfiibA/26EeLuMnB6cpSrQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/types@3.804.0':
    resolution: {integrity: sha512-A9qnsy9zQ8G89vrPPlNG9d1d8QcKRGqJKqwyGgS0dclJpwy6d1EWgQLIolKPl6vcFpLoe6avLOLxr+h8ur5wpg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-arn-parser@3.804.0':
    resolution: {integrity: sha512-wmBJqn1DRXnZu3b4EkE6CWnoWMo1ZMvlfkqU5zPz67xx1GMaXlDCchFvKAXMjk4jn/L1O3tKnoFDNsoLV1kgNQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-endpoints@3.808.0':
    resolution: {integrity: sha512-N6Lic98uc4ADB7fLWlzx+1uVnq04VgVjngZvwHoujcRg9YDhIg9dUDiTzD5VZv13g1BrPYmvYP1HhsildpGV6w==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-locate-window@3.804.0':
    resolution: {integrity: sha512-zVoRfpmBVPodYlnMjgVjfGoEZagyRF5IPn3Uo6ZvOZp24chnW/FRstH7ESDHDDRga4z3V+ElUQHKpFDXWyBW5A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-user-agent-browser@3.804.0':
    resolution: {integrity: sha512-KfW6T6nQHHM/vZBBdGn6fMyG/MgX5lq82TDdX4HRQRRuHKLgBWGpKXqqvBwqIaCdXwWHgDrg2VQups6GqOWW2A==}

  '@aws-sdk/util-user-agent-node@3.816.0':
    resolution: {integrity: sha512-Q6dxmuj4hL7pudhrneWEQ7yVHIQRBFr0wqKLF1opwOi1cIePuoEbPyJ2jkel6PDEv1YMfvsAKaRshp6eNA8VHg==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/xml-builder@3.804.0':
    resolution: {integrity: sha512-JbGWp36IG9dgxtvC6+YXwt5WDZYfuamWFtVfK6fQpnmL96dx+GUPOXPKRWdw67WLKf2comHY28iX2d3z35I53Q==}
    engines: {node: '>=18.0.0'}

  '@clerk/backend@1.34.0':
    resolution: {integrity: sha512-9rZ8hQJVpX5KX2bEpiuVXfpjhojQCiqCWADJDdCI0PCeKxn58Ep0JPYiIcczg4VKUc3a7jve9vXylykG2XajLQ==}
    engines: {node: '>=18.17.0'}
    peerDependencies:
      svix: ^1.62.0
    peerDependenciesMeta:
      svix:
        optional: true

  '@clerk/clerk-react@5.31.8':
    resolution: {integrity: sha512-GPhOdI7drAaamiKIhzfWiOVe4zw4wUi1sKp6khgUzcjr9hRopdZvzMts0fU+XLHFnYUSX8IPw4c0CDXY1wBKuw==}
    engines: {node: '>=18.17.0'}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-0
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-0

  '@clerk/nextjs@6.20.2':
    resolution: {integrity: sha512-rBtAdx2PFxexBDU41GEmEQwSsfbTU7J7OVBKRtmXAXFMYdknGNw41674sFBTaG+wjbTYrhW7wsXcyphEUyVMoQ==}
    engines: {node: '>=18.17.0'}
    peerDependencies:
      next: ^13.5.7 || ^14.2.25 || ^15.2.3
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-0
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-0

  '@clerk/shared@3.9.5':
    resolution: {integrity: sha512-KeIug5qV4LnzZD+16SLkJvdONPs2HQ7I1A7jbHYOGB37vQrQrus64Wu5XeNzbWFTN1Z5fAPSGuja8MfT2cBT4A==}
    engines: {node: '>=18.17.0'}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-0
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-0
    peerDependenciesMeta:
      react:
        optional: true
      react-dom:
        optional: true

  '@clerk/types@4.59.3':
    resolution: {integrity: sha512-xwOO/hfABzbFr3f1RaVXHsDDQ0+jYpm84GiaUDxo+mLsYUgD9f2GmGjKkgWybXzvsBsgZlycSwRXkeDD6utFqg==}
    engines: {node: '>=18.17.0'}

  '@drizzle-team/brocli@0.10.2':
    resolution: {integrity: sha512-z33Il7l5dKjUgGULTqBsQBQwckHh5AbIuxhdsIxDDiZAzBOrZO6q9ogcWC65kU382AfynTfgNumVcNIjuIua6w==}

  '@emnapi/runtime@1.4.0':
    resolution: {integrity: sha512-64WYIf4UYcdLnbKn/umDlNjQDSS8AgZrI/R9+x5ilkUVFxXcA1Ebl+gQLc/6mERA4407Xof0R7wEyEuj091CVw==}

  '@esbuild-kit/core-utils@3.3.2':
    resolution: {integrity: sha512-sPRAnw9CdSsRmEtnsl2WXWdyquogVpB3yZ3dgwJfe8zrOzTsV7cJvmwrKVa+0ma5BoiGJ+BoqkMvawbayKUsqQ==}
    deprecated: 'Merged into tsx: https://tsx.is'

  '@esbuild-kit/esm-loader@2.6.5':
    resolution: {integrity: sha512-FxEMIkJKnodyA1OaCUoEvbYRkoZlLZ4d/eXFu9Fh8CbBBgP5EmZxrfTRyN0qpXZ4vOvqnE5YdRdcrmUUXuU+dA==}
    deprecated: 'Merged into tsx: https://tsx.is'

  '@esbuild/aix-ppc64@0.25.4':
    resolution: {integrity: sha512-1VCICWypeQKhVbE9oW/sJaAmjLxhVqacdkvPLEjwlttjfwENRSClS8EjBz0KzRyFSCPDIkuXW34Je/vk7zdB7Q==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.18.20':
    resolution: {integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.25.4':
    resolution: {integrity: sha512-bBy69pgfhMGtCnwpC/x5QhfxAz/cBgQ9enbtwjf6V9lnPI/hMyT9iWpR1arm0l3kttTr4L0KSLpKmLp/ilKS9A==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.18.20':
    resolution: {integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.25.4':
    resolution: {integrity: sha512-QNdQEps7DfFwE3hXiU4BZeOV68HHzYwGd0Nthhd3uCkkEKK7/R6MTgM0P7H7FAs5pU/DIWsviMmEGxEoxIZ+ZQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.18.20':
    resolution: {integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.25.4':
    resolution: {integrity: sha512-TVhdVtQIFuVpIIR282btcGC2oGQoSfZfmBdTip2anCaVYcqWlZXGcdcKIUklfX2wj0JklNYgz39OBqh2cqXvcQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.18.20':
    resolution: {integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.25.4':
    resolution: {integrity: sha512-Y1giCfM4nlHDWEfSckMzeWNdQS31BQGs9/rouw6Ub91tkK79aIMTH3q9xHvzH8d0wDru5Ci0kWB8b3up/nl16g==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.18.20':
    resolution: {integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.4':
    resolution: {integrity: sha512-CJsry8ZGM5VFVeyUYB3cdKpd/H69PYez4eJh1W/t38vzutdjEjtP7hB6eLKBoOdxcAlCtEYHzQ/PJ/oU9I4u0A==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.18.20':
    resolution: {integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.25.4':
    resolution: {integrity: sha512-yYq+39NlTRzU2XmoPW4l5Ifpl9fqSk0nAJYM/V/WUGPEFfek1epLHJIkTQM6bBs1swApjO5nWgvr843g6TjxuQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.18.20':
    resolution: {integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.4':
    resolution: {integrity: sha512-0FgvOJ6UUMflsHSPLzdfDnnBBVoCDtBTVyn/MrWloUNvq/5SFmh13l3dvgRPkDihRxb77Y17MbqbCAa2strMQQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.18.20':
    resolution: {integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.25.4':
    resolution: {integrity: sha512-+89UsQTfXdmjIvZS6nUnOOLoXnkUTB9hR5QAeLrQdzOSWZvNSAXAtcRDHWtqAUtAmv7ZM1WPOOeSxDzzzMogiQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.18.20':
    resolution: {integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.25.4':
    resolution: {integrity: sha512-kro4c0P85GMfFYqW4TWOpvmF8rFShbWGnrLqlzp4X1TNWjRY3JMYUfDCtOxPKOIY8B0WC8HN51hGP4I4hz4AaQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.18.20':
    resolution: {integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.25.4':
    resolution: {integrity: sha512-yTEjoapy8UP3rv8dB0ip3AfMpRbyhSN3+hY8mo/i4QXFeDxmiYbEKp3ZRjBKcOP862Ua4b1PDfwlvbuwY7hIGQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.18.20':
    resolution: {integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.25.4':
    resolution: {integrity: sha512-NeqqYkrcGzFwi6CGRGNMOjWGGSYOpqwCjS9fvaUlX5s3zwOtn1qwg1s2iE2svBe4Q/YOG1q6875lcAoQK/F4VA==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.18.20':
    resolution: {integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.4':
    resolution: {integrity: sha512-IcvTlF9dtLrfL/M8WgNI/qJYBENP3ekgsHbYUIzEzq5XJzzVEV/fXY9WFPfEEXmu3ck2qJP8LG/p3Q8f7Zc2Xg==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.18.20':
    resolution: {integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.4':
    resolution: {integrity: sha512-HOy0aLTJTVtoTeGZh4HSXaO6M95qu4k5lJcH4gxv56iaycfz1S8GO/5Jh6X4Y1YiI0h7cRyLi+HixMR+88swag==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.18.20':
    resolution: {integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.4':
    resolution: {integrity: sha512-i8JUDAufpz9jOzo4yIShCTcXzS07vEgWzyX3NH2G7LEFVgrLEhjwL3ajFE4fZI3I4ZgiM7JH3GQ7ReObROvSUA==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.18.20':
    resolution: {integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.25.4':
    resolution: {integrity: sha512-jFnu+6UbLlzIjPQpWCNh5QtrcNfMLjgIavnwPQAfoGx4q17ocOU9MsQ2QVvFxwQoWpZT8DvTLooTvmOQXkO51g==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.18.20':
    resolution: {integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.25.4':
    resolution: {integrity: sha512-6e0cvXwzOnVWJHq+mskP8DNSrKBr1bULBvnFLpc1KY+d+irZSgZ02TGse5FsafKS5jg2e4pbvK6TPXaF/A6+CA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.4':
    resolution: {integrity: sha512-vUnkBYxZW4hL/ie91hSqaSNjulOnYXE1VSLusnvHg2u3jewJBz3YzB9+oCw8DABeVqZGg94t9tyZFoHma8gWZQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.18.20':
    resolution: {integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.4':
    resolution: {integrity: sha512-XAg8pIQn5CzhOB8odIcAm42QsOfa98SBeKUdo4xa8OvX8LbMZqEtgeWE9P/Wxt7MlG2QqvjGths+nq48TrUiKw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.4':
    resolution: {integrity: sha512-Ct2WcFEANlFDtp1nVAXSNBPDxyU+j7+tId//iHXU2f/lN5AmO4zLyhDcpR5Cz1r08mVxzt3Jpyt4PmXQ1O6+7A==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.18.20':
    resolution: {integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.4':
    resolution: {integrity: sha512-xAGGhyOQ9Otm1Xu8NT1ifGLnA6M3sJxZ6ixylb+vIUVzvvd6GOALpwQrYrtlPouMqd/vSbgehz6HaVk4+7Afhw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.18.20':
    resolution: {integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.25.4':
    resolution: {integrity: sha512-Mw+tzy4pp6wZEK0+Lwr76pWLjrtjmJyUB23tHKqEDP74R3q95luY/bXqXZeYl4NYlvwOqoRKlInQialgCKy67Q==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.18.20':
    resolution: {integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.25.4':
    resolution: {integrity: sha512-AVUP428VQTSddguz9dO9ngb+E5aScyg7nOeJDrF1HPYu555gmza3bDGMPhmVXL8svDSoqPCsCPjb265yG/kLKQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.18.20':
    resolution: {integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.25.4':
    resolution: {integrity: sha512-i1sW+1i+oWvQzSgfRcxxG2k4I9n3O9NRqy8U+uugaT2Dy7kLO9Y7wI72haOahxceMX8hZAzgGou1FhndRldxRg==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.18.20':
    resolution: {integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.25.4':
    resolution: {integrity: sha512-nOT2vZNw6hJ+z43oP1SPea/G/6AbN6X+bGNhNuq8NtRHy4wsMhw765IKLNmnjek7GvjWBYQ8Q5VBoYTFg9y1UQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@floating-ui/core@1.7.1':
    resolution: {integrity: sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==}

  '@floating-ui/dom@1.7.1':
    resolution: {integrity: sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==}

  '@floating-ui/react-dom@2.1.3':
    resolution: {integrity: sha512-huMBfiU9UnQ2oBwIhgzyIiSpVgvlDstU8CX0AF+wS+KzmYMs0J2a3GwuFHV1Lz+jlrQGeC1fF+Nv0QoumyV0bA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@img/sharp-darwin-arm64@0.34.1':
    resolution: {integrity: sha512-pn44xgBtgpEbZsu+lWf2KNb6OAf70X68k+yk69Ic2Xz11zHR/w24/U49XT7AeRwJ0Px+mhALhU5LPci1Aymk7A==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-darwin-x64@0.34.1':
    resolution: {integrity: sha512-VfuYgG2r8BpYiOUN+BfYeFo69nP/MIwAtSJ7/Zpxc5QF3KS22z8Pvg3FkrSFJBPNQ7mmcUcYQFBmEQp7eu1F8Q==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    resolution: {integrity: sha512-HZ/JUmPwrJSoM4DIQPv/BfNh9yrOA8tlBbqbLz4JZ5uew2+o22Ik+tHQJcih7QJuSa0zo5coHTfD5J8inqj9DA==}
    cpu: [arm64]
    os: [darwin]

  '@img/sharp-libvips-darwin-x64@1.1.0':
    resolution: {integrity: sha512-Xzc2ToEmHN+hfvsl9wja0RlnXEgpKNmftriQp6XzY/RaSfwD9th+MSh0WQKzUreLKKINb3afirxW7A0fz2YWuQ==}
    cpu: [x64]
    os: [darwin]

  '@img/sharp-libvips-linux-arm64@1.1.0':
    resolution: {integrity: sha512-IVfGJa7gjChDET1dK9SekxFFdflarnUB8PwW8aGwEoF3oAsSDuNUTYS+SKDOyOJxQyDC1aPFMuRYLoDInyV9Ew==}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linux-arm@1.1.0':
    resolution: {integrity: sha512-s8BAd0lwUIvYCJyRdFqvsj+BJIpDBSxs6ivrOPm/R7piTs5UIwY5OjXrP2bqXC9/moGsyRa37eYWYCOGVXxVrA==}
    cpu: [arm]
    os: [linux]

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    resolution: {integrity: sha512-tiXxFZFbhnkWE2LA8oQj7KYR+bWBkiV2nilRldT7bqoEZ4HiDOcePr9wVDAZPi/Id5fT1oY9iGnDq20cwUz8lQ==}
    cpu: [ppc64]
    os: [linux]

  '@img/sharp-libvips-linux-s390x@1.1.0':
    resolution: {integrity: sha512-xukSwvhguw7COyzvmjydRb3x/09+21HykyapcZchiCUkTThEQEOMtBj9UhkaBRLuBrgLFzQ2wbxdeCCJW/jgJA==}
    cpu: [s390x]
    os: [linux]

  '@img/sharp-libvips-linux-x64@1.1.0':
    resolution: {integrity: sha512-yRj2+reB8iMg9W5sULM3S74jVS7zqSzHG3Ol/twnAAkAhnGQnpjj6e4ayUz7V+FpKypwgs82xbRdYtchTTUB+Q==}
    cpu: [x64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    resolution: {integrity: sha512-jYZdG+whg0MDK+q2COKbYidaqW/WTz0cc1E+tMAusiDygrM4ypmSCjOJPmFTvHHJ8j/6cAGyeDWZOsK06tP33w==}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    resolution: {integrity: sha512-wK7SBdwrAiycjXdkPnGCPLjYb9lD4l6Ze2gSdAGVZrEL05AOUJESWU2lhlC+Ffn5/G+VKuSm6zzbQSzFX/P65A==}
    cpu: [x64]
    os: [linux]

  '@img/sharp-linux-arm64@0.34.1':
    resolution: {integrity: sha512-kX2c+vbvaXC6vly1RDf/IWNXxrlxLNpBVWkdpRq5Ka7OOKj6nr66etKy2IENf6FtOgklkg9ZdGpEu9kwdlcwOQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linux-arm@0.34.1':
    resolution: {integrity: sha512-anKiszvACti2sGy9CirTlNyk7BjjZPiML1jt2ZkTdcvpLU1YH6CXwRAZCA2UmRXnhiIftXQ7+Oh62Ji25W72jA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm]
    os: [linux]

  '@img/sharp-linux-s390x@0.34.1':
    resolution: {integrity: sha512-7s0KX2tI9mZI2buRipKIw2X1ufdTeaRgwmRabt5bi9chYfhur+/C1OXg3TKg/eag1W+6CCWLVmSauV1owmRPxA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [s390x]
    os: [linux]

  '@img/sharp-linux-x64@0.34.1':
    resolution: {integrity: sha512-wExv7SH9nmoBW3Wr2gvQopX1k8q2g5V5Iag8Zk6AVENsjwd+3adjwxtp3Dcu2QhOXr8W9NusBU6XcQUohBZ5MA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]

  '@img/sharp-linuxmusl-arm64@0.34.1':
    resolution: {integrity: sha512-DfvyxzHxw4WGdPiTF0SOHnm11Xv4aQexvqhRDAoD00MzHekAj9a/jADXeXYCDFH/DzYruwHbXU7uz+H+nWmSOQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]

  '@img/sharp-linuxmusl-x64@0.34.1':
    resolution: {integrity: sha512-pax/kTR407vNb9qaSIiWVnQplPcGU8LRIJpDT5o8PdAx5aAA7AS3X9PS8Isw1/WfqgQorPotjrZL3Pqh6C5EBg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]

  '@img/sharp-wasm32@0.34.1':
    resolution: {integrity: sha512-YDybQnYrLQfEpzGOQe7OKcyLUCML4YOXl428gOOzBgN6Gw0rv8dpsJ7PqTHxBnXnwXr8S1mYFSLSa727tpz0xg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [wasm32]

  '@img/sharp-win32-ia32@0.34.1':
    resolution: {integrity: sha512-WKf/NAZITnonBf3U1LfdjoMgNO5JYRSlhovhRhMxXVdvWYveM4kM3L8m35onYIdh75cOMCo1BexgVQcCDzyoWw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [ia32]
    os: [win32]

  '@img/sharp-win32-x64@0.34.1':
    resolution: {integrity: sha512-hw1iIAHpNE8q3uMIRCgGOeDoz9KtFNarFLQclLxr/LK1VBkj8nby18RjFvr6aP7USRYAjTZW6yisnBWMX571Tw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [win32]

  '@mendable/firecrawl-js@1.25.5':
    resolution: {integrity: sha512-cozEZipMdtyfAQKX8IFC8DW06cYd7CxL8cxwMaD61sQrWpLkGcpivoMEeSFuyJTKmobAYAT5EB5ZExUJSVJkXw==}
    engines: {node: '>=22.0.0'}

  '@neondatabase/serverless@1.0.0':
    resolution: {integrity: sha512-XWmEeWpBXIoksZSDN74kftfTnXFEGZ3iX8jbANWBc+ag6dsiQuvuR4LgB0WdCOKMb5AQgjqgufc0TgAsZubUYw==}
    engines: {node: '>=19.0.0'}

  '@next/env@15.3.2':
    resolution: {integrity: sha512-xURk++7P7qR9JG1jJtLzPzf0qEvqCN0A/T3DXf8IPMKo9/6FfjxtEffRJIIew/bIL4T3C2jLLqBor8B/zVlx6g==}

  '@next/swc-darwin-arm64@15.3.2':
    resolution: {integrity: sha512-2DR6kY/OGcokbnCsjHpNeQblqCZ85/1j6njYSkzRdpLn5At7OkSdmk7WyAmB9G0k25+VgqVZ/u356OSoQZ3z0g==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@next/swc-darwin-x64@15.3.2':
    resolution: {integrity: sha512-ro/fdqaZWL6k1S/5CLv1I0DaZfDVJkWNaUU3un8Lg6m0YENWlDulmIWzV96Iou2wEYyEsZq51mwV8+XQXqMp3w==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@next/swc-linux-arm64-gnu@15.3.2':
    resolution: {integrity: sha512-covwwtZYhlbRWK2HlYX9835qXum4xYZ3E2Mra1mdQ+0ICGoMiw1+nVAn4d9Bo7R3JqSmK1grMq/va+0cdh7bJA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-arm64-musl@15.3.2':
    resolution: {integrity: sha512-KQkMEillvlW5Qk5mtGA/3Yz0/tzpNlSw6/3/ttsV1lNtMuOHcGii3zVeXZyi4EJmmLDKYcTcByV2wVsOhDt/zg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@next/swc-linux-x64-gnu@15.3.2':
    resolution: {integrity: sha512-uRBo6THWei0chz+Y5j37qzx+BtoDRFIkDzZjlpCItBRXyMPIg079eIkOCl3aqr2tkxL4HFyJ4GHDes7W8HuAUg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-linux-x64-musl@15.3.2':
    resolution: {integrity: sha512-+uxFlPuCNx/T9PdMClOqeE8USKzj8tVz37KflT3Kdbx/LOlZBRI2yxuIcmx1mPNK8DwSOMNCr4ureSet7eyC0w==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@next/swc-win32-arm64-msvc@15.3.2':
    resolution: {integrity: sha512-LLTKmaI5cfD8dVzh5Vt7+OMo+AIOClEdIU/TSKbXXT2iScUTSxOGoBhfuv+FU8R9MLmrkIL1e2fBMkEEjYAtPQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@next/swc-win32-x64-msvc@15.3.2':
    resolution: {integrity: sha512-aW5B8wOPioJ4mBdMDXkt5f3j8pUr9W8AnlX0Df35uRWNT1Y6RIybxjnSUe+PhM+M1bwgyY8PHLmXZC6zT1o5tA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}

  '@puppeteer/browsers@2.10.5':
    resolution: {integrity: sha512-eifa0o+i8dERnngJwKrfp3dEq7ia5XFyoqB17S4gK8GhsQE4/P8nxOfQSE0zQHxzzLo/cmF+7+ywEQ7wK7Fb+w==}
    engines: {node: '>=18'}
    hasBin: true

  '@radix-ui/number@1.1.1':
    resolution: {integrity: sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==}

  '@radix-ui/primitive@1.1.2':
    resolution: {integrity: sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==}

  '@radix-ui/react-accordion@1.2.11':
    resolution: {integrity: sha512-l3W5D54emV2ues7jjeG1xcyN7S3jnK3zE2zHqgn0CmMsy9lNJwmgcrmaxS+7ipw15FAivzKNzH3d5EcGoFKw0A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.7':
    resolution: {integrity: sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collapsible@1.1.11':
    resolution: {integrity: sha512-2qrRsVGSCYasSz1RFOorXwl0H7g7J1frQtgpQgYrt+MOidtPAINHn9CPovQXb83r8ahapdx3Tu0fa/pdFFSdPg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.7':
    resolution: {integrity: sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.2':
    resolution: {integrity: sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.2':
    resolution: {integrity: sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.14':
    resolution: {integrity: sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.1':
    resolution: {integrity: sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.10':
    resolution: {integrity: sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.2':
    resolution: {integrity: sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.7':
    resolution: {integrity: sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.1':
    resolution: {integrity: sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-popover@1.1.14':
    resolution: {integrity: sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.7':
    resolution: {integrity: sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.9':
    resolution: {integrity: sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.4':
    resolution: {integrity: sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.1.3':
    resolution: {integrity: sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-scroll-area@1.2.9':
    resolution: {integrity: sha512-YSjEfBXnhUELsO2VzjdtYYD4CfQjvao+lhhrX5XsHD7/cyUNzljF1FHEbgTPN7LH2MClfwRMIsYlqTYpKTTe2A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.7':
    resolution: {integrity: sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.2.3':
    resolution: {integrity: sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-tooltip@1.2.7':
    resolution: {integrity: sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.1':
    resolution: {integrity: sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.2.2':
    resolution: {integrity: sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-effect-event@0.0.2':
    resolution: {integrity: sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.1':
    resolution: {integrity: sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.1':
    resolution: {integrity: sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.1':
    resolution: {integrity: sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.1':
    resolution: {integrity: sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.2.3':
    resolution: {integrity: sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.1':
    resolution: {integrity: sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==}

  '@smithy/abort-controller@4.0.3':
    resolution: {integrity: sha512-AqXFf6DXnuRBXy4SoK/n1mfgHaKaq36bmkphmD1KO0nHq6xK/g9KHSW4HEsPQUBCGdIEfuJifGHwxFXPIFay9Q==}
    engines: {node: '>=18.0.0'}

  '@smithy/chunked-blob-reader-native@4.0.0':
    resolution: {integrity: sha512-R9wM2yPmfEMsUmlMlIgSzOyICs0x9uu7UTHoccMyt7BWw8shcGM8HqB355+BZCPBcySvbTYMs62EgEQkNxz2ig==}
    engines: {node: '>=18.0.0'}

  '@smithy/chunked-blob-reader@5.0.0':
    resolution: {integrity: sha512-+sKqDBQqb036hh4NPaUiEkYFkTUGYzRsn3EuFhyfQfMy6oGHEUJDurLP9Ufb5dasr/XiAmPNMr6wa9afjQB+Gw==}
    engines: {node: '>=18.0.0'}

  '@smithy/config-resolver@4.1.3':
    resolution: {integrity: sha512-N5e7ofiyYDmHxnPnqF8L4KtsbSDwyxFRfDK9bp1d9OyPO4ytRLd0/XxCqi5xVaaqB65v4woW8uey6jND6zxzxQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/core@3.4.0':
    resolution: {integrity: sha512-dDYISQo7k0Ml/rXlFIjkTmTcQze/LxhtIRAEmZ6HJ/EI0inVxVEVnrUXJ7jPx6ZP0GHUhFm40iQcCgS5apXIXA==}
    engines: {node: '>=18.0.0'}

  '@smithy/credential-provider-imds@4.0.5':
    resolution: {integrity: sha512-saEAGwrIlkb9XxX/m5S5hOtzjoJPEK6Qw2f9pYTbIsMPOFyGSXBBTw95WbOyru8A1vIS2jVCCU1Qhz50QWG3IA==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-codec@4.0.3':
    resolution: {integrity: sha512-V22KIPXZsE2mc4zEgYGANM/7UbL9jWlOACEolyGyMuTY+jjHJ2PQ0FdopOTS1CS7u6PlAkALmypkv2oQ4aftcg==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-browser@4.0.3':
    resolution: {integrity: sha512-oe1d/tfCGVZBMX8O6HApaM4G+fF9JNdyLP7tWXt00epuL/kLOdp/4o9VqheLFeJaXgao+9IaBgs/q/oM48hxzg==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-config-resolver@4.1.1':
    resolution: {integrity: sha512-XXCPGjRNwpFWHKQJMKIjGLfFKYULYckFnxGcWmBC2mBf3NsrvUKgqHax4NCqc0TfbDAimPDHOc6HOKtzsXK9Gw==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-node@4.0.3':
    resolution: {integrity: sha512-HOEbRmm9TrikCoFrypYu0J/gC4Lsk8gl5LtOz1G3laD2Jy44+ht2Pd2E9qjNQfhMJIzKDZ/gbuUH0s0v4kWQ0A==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-universal@4.0.3':
    resolution: {integrity: sha512-ShOP512CZrYI9n+h64PJ84udzoNHUQtPddyh1j175KNTKsSnMEDNscOWJWyEoLQiuhWWw51lSa+k6ea9ZGXcRg==}
    engines: {node: '>=18.0.0'}

  '@smithy/fetch-http-handler@5.0.3':
    resolution: {integrity: sha512-yBZwavI31roqTndNI7ONHqesfH01JmjJK6L3uUpZAhyAmr86LN5QiPzfyZGIxQmed8VEK2NRSQT3/JX5V1njfQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/hash-blob-browser@4.0.3':
    resolution: {integrity: sha512-37wZYU/XI2cOF4hgNDNMzZNAuNtJTkZFWxcpagQrnf6PYU/6sJ6y5Ey9Bp4vzi9nteex/ImxAugfsF3XGLrqWA==}
    engines: {node: '>=18.0.0'}

  '@smithy/hash-node@4.0.3':
    resolution: {integrity: sha512-W5Uhy6v/aYrgtjh9y0YP332gIQcwccQ+EcfWhllL0B9rPae42JngTTUpb8W6wuxaNFzqps4xq5klHckSSOy5fw==}
    engines: {node: '>=18.0.0'}

  '@smithy/hash-stream-node@4.0.3':
    resolution: {integrity: sha512-CAwAvztwGYHHZGGcXtbinNxytaj5FNZChz8V+o7eNUAi5BgVqnF91Z3cJSmaE9O7FYUQVrIzGAB25Aok9T5KHQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/invalid-dependency@4.0.3':
    resolution: {integrity: sha512-1Bo8Ur1ZGqxvwTqBmv6DZEn0rXtwJGeqiiO2/JFcCtz3nBakOqeXbJBElXJMMzd0ghe8+eB6Dkw98nMYctgizg==}
    engines: {node: '>=18.0.0'}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@4.0.0':
    resolution: {integrity: sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==}
    engines: {node: '>=18.0.0'}

  '@smithy/md5-js@4.0.3':
    resolution: {integrity: sha512-m95Z+1UJFPq4cv/R6TPMLYkoau7cNJYA5GLuuUJjfmF+Zrad4yaupIWeGGzIinf8pD1L+CIAxjh8eowPvyL7Dw==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-content-length@4.0.3':
    resolution: {integrity: sha512-NE/Zph4BP5u16bzYq2csq9qD0T6UBLeg4AuNrwNJ7Gv9uLYaGEgelZUOdRndGdMGcUfSGvNlXGb2aA2hPCwJ6g==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-endpoint@4.1.7':
    resolution: {integrity: sha512-KDzM7Iajo6K7eIWNNtukykRT4eWwlHjCEsULZUaSfi/SRSBK8BPRqG5FsVfp58lUxcvre8GT8AIPIqndA0ERKw==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-retry@4.1.8':
    resolution: {integrity: sha512-e2OtQgFzzlSG0uCjcJmi02QuFSRTrpT11Eh2EcqqDFy7DYriteHZJkkf+4AsxsrGDugAtPFcWBz1aq06sSX5fQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-serde@4.0.6':
    resolution: {integrity: sha512-YECyl7uNII+jCr/9qEmCu8xYL79cU0fqjo0qxpcVIU18dAPHam/iYwcknAu4Jiyw1uN+sAx7/SMf/Kmef/Jjsg==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-stack@4.0.3':
    resolution: {integrity: sha512-baeV7t4jQfQtFxBADFmnhmqBmqR38dNU5cvEgHcMK/Kp3D3bEI0CouoX2Sr/rGuntR+Eg0IjXdxnGGTc6SbIkw==}
    engines: {node: '>=18.0.0'}

  '@smithy/node-config-provider@4.1.2':
    resolution: {integrity: sha512-SUvNup8iU1v7fmM8XPk+27m36udmGCfSz+VZP5Gb0aJ3Ne0X28K/25gnsrg3X1rWlhcnhzNUUysKW/Ied46ivQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/node-http-handler@4.0.5':
    resolution: {integrity: sha512-T7QglZC1vS7SPT44/1qSIAQEx5bFKb3LfO6zw/o4Xzt1eC5HNoH1TkS4lMYA9cWFbacUhx4hRl/blLun4EOCkg==}
    engines: {node: '>=18.0.0'}

  '@smithy/property-provider@4.0.3':
    resolution: {integrity: sha512-Wcn17QNdawJZcZZPBuMuzyBENVi1AXl4TdE0jvzo4vWX2x5df/oMlmr/9M5XAAC6+yae4kWZlOYIsNsgDrMU9A==}
    engines: {node: '>=18.0.0'}

  '@smithy/protocol-http@5.1.1':
    resolution: {integrity: sha512-Vsay2mzq05DwNi9jK01yCFtfvu9HimmgC7a4HTs7lhX12Sx8aWsH0mfz6q/02yspSp+lOB+Q2HJwi4IV2GKz7A==}
    engines: {node: '>=18.0.0'}

  '@smithy/querystring-builder@4.0.3':
    resolution: {integrity: sha512-UUzIWMVfPmDZcOutk2/r1vURZqavvQW0OHvgsyNV0cKupChvqg+/NKPRMaMEe+i8tP96IthMFeZOZWpV+E4RAw==}
    engines: {node: '>=18.0.0'}

  '@smithy/querystring-parser@4.0.3':
    resolution: {integrity: sha512-K5M4ZJQpFCblOJ5Oyw7diICpFg1qhhR47m2/5Ef1PhGE19RaIZf50tjYFrxa6usqcuXyTiFPGo4d1geZdH4YcQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/service-error-classification@4.0.4':
    resolution: {integrity: sha512-W5ScbQ1bTzgH91kNEE2CvOzM4gXlDOqdow4m8vMFSIXCel2scbHwjflpVNnC60Y3F1m5i7w2gQg9lSnR+JsJAA==}
    engines: {node: '>=18.0.0'}

  '@smithy/shared-ini-file-loader@4.0.3':
    resolution: {integrity: sha512-vHwlrqhZGIoLwaH8vvIjpHnloShqdJ7SUPNM2EQtEox+yEDFTVQ7E+DLZ+6OhnYEgFUwPByJyz6UZaOu2tny6A==}
    engines: {node: '>=18.0.0'}

  '@smithy/signature-v4@5.1.1':
    resolution: {integrity: sha512-zy8Repr5zvT0ja+Tf5wjV/Ba6vRrhdiDcp/ww6cvqYbSEudIkziDe3uppNRlFoCViyJXdPnLcwyZdDLA4CHzSg==}
    engines: {node: '>=18.0.0'}

  '@smithy/smithy-client@4.3.0':
    resolution: {integrity: sha512-DNsRA38pN6tYHUjebmwD9e4KcgqTLldYQb2gC6K+oxXYdCTxPn6wV9+FvOa6wrU2FQEnGJoi+3GULzOTKck/tg==}
    engines: {node: '>=18.0.0'}

  '@smithy/types@4.3.0':
    resolution: {integrity: sha512-+1iaIQHthDh9yaLhRzaoQxRk+l9xlk+JjMFxGRhNLz+m9vKOkjNeU8QuB4w3xvzHyVR/BVlp/4AXDHjoRIkfgQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/url-parser@4.0.3':
    resolution: {integrity: sha512-n5/DnosDu/tweOqUUNtUbu7eRIR4J/Wz9nL7V5kFYQQVb8VYdj7a4G5NJHCw6o21ul7CvZoJkOpdTnsQDLT0tQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-base64@4.0.0':
    resolution: {integrity: sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-body-length-browser@4.0.0':
    resolution: {integrity: sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-body-length-node@4.0.0':
    resolution: {integrity: sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@4.0.0':
    resolution: {integrity: sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-config-provider@4.0.0':
    resolution: {integrity: sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-defaults-mode-browser@4.0.15':
    resolution: {integrity: sha512-bJJ/B8owQbHAflatSq92f9OcV8858DJBQF1Y3GRjB8psLyUjbISywszYPFw16beREHO/C3I3taW4VGH+tOuwrQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-defaults-mode-node@4.0.15':
    resolution: {integrity: sha512-8CUrEW2Ni5q+NmYkj8wsgkfqoP7l4ZquptFbq92yQE66xevc4SxqP2zH6tMtN158kgBqBDsZ+qlrRwXWOjCR8A==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-endpoints@3.0.5':
    resolution: {integrity: sha512-PjDpqLk24/vAl340tmtCA++Q01GRRNH9cwL9qh46NspAX9S+IQVcK+GOzPt0GLJ6KYGyn8uOgo2kvJhiThclJw==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-hex-encoding@4.0.0':
    resolution: {integrity: sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-middleware@4.0.3':
    resolution: {integrity: sha512-iIsC6qZXxkD7V3BzTw3b1uK8RVC1M8WvwNxK1PKrH9FnxntCd30CSunXjL/8iJBE8Z0J14r2P69njwIpRG4FBQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-retry@4.0.4':
    resolution: {integrity: sha512-Aoqr9W2jDYGrI6OxljN8VmLDQIGO4VdMAUKMf9RGqLG8hn6or+K41NEy1Y5dtum9q8F7e0obYAuKl2mt/GnpZg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-stream@4.2.1':
    resolution: {integrity: sha512-W3IR0x5DY6iVtjj5p902oNhD+Bz7vs5S+p6tppbPa509rV9BdeXZjGuRSCtVEad9FA0Mba+tNUtUmtnSI1nwUw==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-uri-escape@4.0.0':
    resolution: {integrity: sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@4.0.0':
    resolution: {integrity: sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-waiter@4.0.4':
    resolution: {integrity: sha512-73aeIvHjtSB6fd9I08iFaQIGTICKpLrI3EtlWAkStVENGo1ARMq9qdoD4QwkY0RUp6A409xlgbD9NCCfCF5ieg==}
    engines: {node: '>=18.0.0'}

  '@sparticuz/chromium@133.0.0':
    resolution: {integrity: sha512-wioNxMtSxRI+Y6ymc/UFPX9lY7A1SDgBezjFITH6arwe5CONfWosNDGpgflUGYajxxGktb1k3kjJ83jWzbccBw==}
    engines: {node: '>= 16'}

  '@swc/counter@0.1.3':
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}

  '@swc/helpers@0.5.15':
    resolution: {integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==}

  '@tailwindcss/node@4.1.3':
    resolution: {integrity: sha512-H/6r6IPFJkCfBJZ2dKZiPJ7Ueb2wbL592+9bQEl2r73qbX6yGnmQVIfiUvDRB2YI0a3PWDrzUwkvQx1XW1bNkA==}

  '@tailwindcss/oxide-android-arm64@4.1.3':
    resolution: {integrity: sha512-cxklKjtNLwFl3mDYw4XpEfBY+G8ssSg9ADL4Wm6//5woi3XGqlxFsnV5Zb6v07dxw1NvEX2uoqsxO/zWQsgR+g==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.3':
    resolution: {integrity: sha512-mqkf2tLR5VCrjBvuRDwzKNShRu99gCAVMkVsaEOFvv6cCjlEKXRecPu9DEnxp6STk5z+Vlbh1M5zY3nQCXMXhw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.3':
    resolution: {integrity: sha512-7sGraGaWzXvCLyxrc7d+CCpUN3fYnkkcso3rCzwUmo/LteAl2ZGCDlGvDD8Y/1D3ngxT8KgDj1DSwOnNewKhmg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.3':
    resolution: {integrity: sha512-E2+PbcbzIReaAYZe997wb9rId246yDkCwAakllAWSGqe6VTg9hHle67hfH6ExjpV2LSK/siRzBUs5wVff3RW9w==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.3':
    resolution: {integrity: sha512-GvfbJ8wjSSjbLFFE3UYz4Eh8i4L6GiEYqCtA8j2Zd2oXriPuom/Ah/64pg/szWycQpzRnbDiJozoxFU2oJZyfg==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.3':
    resolution: {integrity: sha512-35UkuCWQTeG9BHcBQXndDOrpsnt3Pj9NVIB4CgNiKmpG8GnCNXeMczkUpOoqcOhO6Cc/mM2W7kaQ/MTEENDDXg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.3':
    resolution: {integrity: sha512-dm18aQiML5QCj9DQo7wMbt1Z2tl3Giht54uVR87a84X8qRtuXxUqnKQkRDK5B4bCOmcZ580lF9YcoMkbDYTXHQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.3':
    resolution: {integrity: sha512-LMdTmGe/NPtGOaOfV2HuO7w07jI3cflPrVq5CXl+2O93DCewADK0uW1ORNAcfu2YxDUS035eY2W38TxrsqngxA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-linux-x64-musl@4.1.3':
    resolution: {integrity: sha512-aalNWwIi54bbFEizwl1/XpmdDrOaCjRFQRgtbv9slWjmNPuJJTIKPHf5/XXDARc9CneW9FkSTqTbyvNecYAEGw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.3':
    resolution: {integrity: sha512-PEj7XR4OGTGoboTIAdXicKuWl4EQIjKHKuR+bFy9oYN7CFZo0eu74+70O4XuERX4yjqVZGAkCdglBODlgqcCXg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.3':
    resolution: {integrity: sha512-T8gfxECWDBENotpw3HR9SmNiHC9AOJdxs+woasRZ8Q/J4VHN0OMs7F+4yVNZ9EVN26Wv6mZbK0jv7eHYuLJLwA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.3':
    resolution: {integrity: sha512-t16lpHCU7LBxDe/8dCj9ntyNpXaSTAgxWm1u2XQP5NiIu4KGSyrDJJRlK9hJ4U9yJxx0UKCVI67MJWFNll5mOQ==}
    engines: {node: '>= 10'}

  '@tailwindcss/postcss@4.1.3':
    resolution: {integrity: sha512-6s5nJODm98F++QT49qn8xJKHQRamhYHfMi3X7/ltxiSQ9dyRsaFSfFkfaMsanWzf+TMYQtbk8mt5f6cCVXJwfg==}

  '@tootallnate/quickjs-emscripten@0.23.0':
    resolution: {integrity: sha512-C5Mc6rdnsaJDjO3UpGW/CQTHtCKaYlScZTly4JIu97Jxo/odCiH0ITnDXSJPTOrEKk/ycSZ0AOgTmkDtkOsvIA==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/diff-match-patch@1.0.36':
    resolution: {integrity: sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg==}

  '@types/estree-jsx@1.0.5':
    resolution: {integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==}

  '@types/estree@1.0.7':
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==}

  '@types/hast@3.0.4':
    resolution: {integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==}

  '@types/mdast@4.0.4':
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}

  '@types/ms@2.1.0':
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}

  '@types/node-fetch@2.6.12':
    resolution: {integrity: sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==}

  '@types/node-int64@0.4.32':
    resolution: {integrity: sha512-xf/JsSlnXQ+mzvc0IpXemcrO4BrCfpgNpMco+GLcXkFk01k/gW9lGJu+Vof0ZSvHK6DsHJDPSbjFPs36QkWXqw==}

  '@types/node@18.19.104':
    resolution: {integrity: sha512-mqjoYx1RjmN61vjnHWfiWzAlwvBKutoUdm+kYLPnjI5DCh8ZqofUhaTbT3WLl7bt3itR8DuCf8ShnxI0JvIC3g==}

  '@types/node@20.17.30':
    resolution: {integrity: sha512-7zf4YyHA+jvBNfVrk2Gtvs6x7E8V+YDW05bNfG2XkWDJfYRXrTiP/DsB2zSYTaHX0bGIujTBQdMVAhb+j7mwpg==}

  '@types/node@22.15.21':
    resolution: {integrity: sha512-EV/37Td6c+MgKAbkcLG6vqZ2zEYHD7bvSrzqqs2RIhbA6w3x+Dqz8MZM3sP6kGTeLrdoOgKZe+Xja7tUB2DNkQ==}

  '@types/parquetjs@0.10.6':
    resolution: {integrity: sha512-ZCsD6j97YD0mGU8/VnVs3NjORXa7zeHvqlpJpCqy4jU8a1O21dalL+MFn9QNbdEfy8rszR1N7NHeT7/LdtHf+A==}

  '@types/pg@8.15.2':
    resolution: {integrity: sha512-+BKxo5mM6+/A1soSHBI7ufUglqYXntChLDyTbvcAn1Lawi9J7J9Ok3jt6w7I0+T/UDJ4CyhHk66+GZbwmkYxSg==}

  '@types/progress-stream@2.0.5':
    resolution: {integrity: sha512-5YNriuEZkHlFHHepLIaxzq3atGeav1qCTGzB74HKWpo66qjfostF+rHc785YYYHeBytve8ZG3ejg42jEIfXNiQ==}

  '@types/react-dom@19.1.2':
    resolution: {integrity: sha512-XGJkWF41Qq305SKWEILa1O8vzhb3aOo3ogBlSmiqNko/WmRb6QIaweuZCXjKygVDXpzXb5wyxKTSOsmkuqj+Qw==}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react@19.1.2':
    resolution: {integrity: sha512-oxLPMytKchWGbnQM9O7D67uPa9paTNxO7jVoNMXgkkErULBPhPARCfkKL9ytcIJJRGjbsVwW4ugJzyFFvm/Tiw==}

  '@types/unist@2.0.11':
    resolution: {integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==}

  '@types/unist@3.0.3':
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}

  '@types/yauzl@2.10.3':
    resolution: {integrity: sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==}

  '@upstash/core-analytics@0.0.10':
    resolution: {integrity: sha512-7qJHGxpQgQr9/vmeS1PktEwvNAF7TI4iJDi8Pu2CFZ9YUGHZH4fOP5TfYlZ4aVxfopnELiE4BS4FBjyK7V1/xQ==}
    engines: {node: '>=16.0.0'}

  '@upstash/qstash@2.8.1':
    resolution: {integrity: sha512-E+JoLHMqygQksWATjS/9AAo7Mhi/k21Tu0zDwMm9GyNyNVjEJntQvmrH5NC7x6kXwjCDTEWFtIWz0JXu9/9pMQ==}

  '@upstash/ratelimit@2.0.5':
    resolution: {integrity: sha512-1FRv0cs3ZlBjCNOCpCmKYmt9BYGIJf0J0R3pucOPE88R21rL7jNjXG+I+rN/BVOvYJhI9niRAS/JaSNjiSICxA==}
    peerDependencies:
      '@upstash/redis': ^1.34.3

  '@upstash/redis@1.34.9':
    resolution: {integrity: sha512-7qzzF2FQP5VxR2YUNjemWs+hl/8VzJJ6fOkT7O7kt9Ct8olEVzb1g6/ik6B8Pb8W7ZmYv81SdlVV9F6O8bh/gw==}

  '@upstash/workflow@0.2.13':
    resolution: {integrity: sha512-TX2fOPnAWgW6ky7MoC9OCgVIu7rB1BRqfP+c8ILziR/I5vdpky5mrHvpSJq28Vqsq+rso0pyLW4Dmle1Hk56Rw==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  agent-base@7.1.3:
    resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
    engines: {node: '>= 14'}

  agentkeepalive@4.6.0:
    resolution: {integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==}
    engines: {node: '>= 8.0.0'}

  ai@4.3.16:
    resolution: {integrity: sha512-KUDwlThJ5tr2Vw0A1ZkbDKNME3wzWhuVfAOwIvFUzl1TPVDFAXDFTXio3p+jaKneB+dKNCvFFlolYmmgHttG1g==}
    engines: {node: '>=18'}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      zod: ^3.23.8
    peerDependenciesMeta:
      react:
        optional: true

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  aria-hidden@1.2.6:
    resolution: {integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==}
    engines: {node: '>=10'}

  ast-types@0.13.4:
    resolution: {integrity: sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w==}
    engines: {node: '>=4'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.9.0:
    resolution: {integrity: sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==}

  b4a@1.6.7:
    resolution: {integrity: sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg==}

  bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}

  bare-events@2.5.4:
    resolution: {integrity: sha512-+gFfDkR8pj4/TrWCGUGWmJIkBwuxPS5F+a5yWjOHQt2hHvNZd5YLzadjmDUtFmMM4y429bnKLa8bYBMHcYdnQA==}

  bare-fs@4.1.5:
    resolution: {integrity: sha512-1zccWBMypln0jEE05LzZt+V/8y8AQsQQqxtklqaIyg5nu6OAYFhZxPXinJTSG+kU5qyNmeLgcn9AW7eHiCHVLA==}
    engines: {bare: '>=1.16.0'}
    peerDependencies:
      bare-buffer: '*'
    peerDependenciesMeta:
      bare-buffer:
        optional: true

  bare-os@3.6.1:
    resolution: {integrity: sha512-uaIjxokhFidJP+bmmvKSgiMzj2sV5GPHaZVAIktcxcpCyBFFWO+YlikVAdhmUo2vYFvFhOXIAlldqV29L8126g==}
    engines: {bare: '>=1.14.0'}

  bare-path@3.0.0:
    resolution: {integrity: sha512-tyfW2cQcB5NN8Saijrhqn0Zh7AnFNsnczRcuWODH0eYAXBsJ5gVxAUuNr7tsHSC6IZ77cA0SitzT+s47kot8Mw==}

  bare-stream@2.6.5:
    resolution: {integrity: sha512-jSmxKJNJmHySi6hC42zlZnq00rga4jjxcgNZjY9N5WlOe/iOoGRtdwGsHzQv2RlH2KOYMwGUXhf2zXd32BA9RA==}
    peerDependencies:
      bare-buffer: '*'
      bare-events: '*'
    peerDependenciesMeta:
      bare-buffer:
        optional: true
      bare-events:
        optional: true

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  basic-ftp@5.0.5:
    resolution: {integrity: sha512-4Bcg1P8xhUuqcii/S0Z9wiHIrQVPMermM1any+MX5GeGD7faD3/msQUDGLol9wOcz4/jbg/WJnGqoJF6LiBdtg==}
    engines: {node: '>=10.0.0'}

  bindings@1.2.1:
    resolution: {integrity: sha512-u4cBQNepWxYA55FunZSM7wMi55yQaN0otnhhilNoWHq0MfOfJeQx0v0mRRpolGOExPjZcl6FtB0BB8Xkb88F0g==}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  brotli@1.3.3:
    resolution: {integrity: sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==}

  bson@1.1.6:
    resolution: {integrity: sha512-EvVNVeGo4tHxwi8L6bPj3y3itEvStdwvvlojVxxbyYfoaxJ6keLgrTuKdyfEAszFK+H3olzBuafE0yoh0D1gdg==}
    engines: {node: '>=0.6.19'}

  buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  busboy@1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  caniuse-lite@1.0.30001713:
    resolution: {integrity: sha512-wCIWIg+A4Xr7NfhTuHdX+/FKh3+Op3LBbSp2N5Pfx6T/LhdQy3GTyoTg48BReaW/MyMNZAkTadsBtai3ldWK0Q==}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  chalk@5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}

  character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}

  character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}

  character-reference-invalid@2.0.1:
    resolution: {integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==}

  chromium-bidi@5.1.0:
    resolution: {integrity: sha512-9MSRhWRVoRPDG0TgzkHrshFSJJNZzfY5UFqUMuksg7zL1yoZIZ3jLB0YAgHclbiAxPI86pBnwDX1tbzoiV8aFw==}
    peerDependencies:
      devtools-protocol: '*'

  class-variance-authority@0.7.1:
    resolution: {integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==}

  classnames@2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}

  client-only@0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}

  cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  data-uri-to-buffer@6.0.2:
    resolution: {integrity: sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==}
    engines: {node: '>= 14'}

  date-fns@4.1.0:
    resolution: {integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==}

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-named-character-reference@1.1.0:
    resolution: {integrity: sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w==}

  dedent@1.6.0:
    resolution: {integrity: sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  degenerator@5.0.1:
    resolution: {integrity: sha512-TllpMR/t0M5sqCXfj85i4XaAzxmS5tVA16dqvdkMwGmzI+dXLXnw3J+3Vdv7VKw+ThlTMboK6i9rnZ6Nntj5CQ==}
    engines: {node: '>= 14'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  detect-libc@2.0.3:
    resolution: {integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==}
    engines: {node: '>=8'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}

  devlop@1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}

  devtools-protocol@0.0.1452169:
    resolution: {integrity: sha512-FOFDVMGrAUNp0dDKsAU1TorWJUx2JOU1k9xdgBKKJF3IBh/Uhl2yswG5r3TEAOrCiGY2QRp1e6LVDQrCsTKO4g==}

  diff-match-patch@1.0.5:
    resolution: {integrity: sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==}

  dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}

  dotenv@16.5.0:
    resolution: {integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==}
    engines: {node: '>=12'}

  drizzle-kit@0.31.1:
    resolution: {integrity: sha512-PUjYKWtzOzPtdtQlTHQG3qfv4Y0XT8+Eas6UbxCmxTj7qgMf+39dDujf1BP1I+qqZtw9uzwTh8jYtkMuCq+B0Q==}
    hasBin: true

  drizzle-orm@0.43.1:
    resolution: {integrity: sha512-dUcDaZtE/zN4RV/xqGrVSMpnEczxd5cIaoDeor7Zst9wOe/HzC/7eAaulywWGYXdDEc9oBPMjayVEDg0ziTLJA==}
    peerDependencies:
      '@aws-sdk/client-rds-data': '>=3'
      '@cloudflare/workers-types': '>=4'
      '@electric-sql/pglite': '>=0.2.0'
      '@libsql/client': '>=0.10.0'
      '@libsql/client-wasm': '>=0.10.0'
      '@neondatabase/serverless': '>=0.10.0'
      '@op-engineering/op-sqlite': '>=2'
      '@opentelemetry/api': ^1.4.1
      '@planetscale/database': '>=1.13'
      '@prisma/client': '*'
      '@tidbcloud/serverless': '*'
      '@types/better-sqlite3': '*'
      '@types/pg': '*'
      '@types/sql.js': '*'
      '@vercel/postgres': '>=0.8.0'
      '@xata.io/client': '*'
      better-sqlite3: '>=7'
      bun-types: '*'
      expo-sqlite: '>=14.0.0'
      gel: '>=2'
      knex: '*'
      kysely: '*'
      mysql2: '>=2'
      pg: '>=8'
      postgres: '>=3'
      prisma: '*'
      sql.js: '>=1'
      sqlite3: '>=5'
    peerDependenciesMeta:
      '@aws-sdk/client-rds-data':
        optional: true
      '@cloudflare/workers-types':
        optional: true
      '@electric-sql/pglite':
        optional: true
      '@libsql/client':
        optional: true
      '@libsql/client-wasm':
        optional: true
      '@neondatabase/serverless':
        optional: true
      '@op-engineering/op-sqlite':
        optional: true
      '@opentelemetry/api':
        optional: true
      '@planetscale/database':
        optional: true
      '@prisma/client':
        optional: true
      '@tidbcloud/serverless':
        optional: true
      '@types/better-sqlite3':
        optional: true
      '@types/pg':
        optional: true
      '@types/sql.js':
        optional: true
      '@vercel/postgres':
        optional: true
      '@xata.io/client':
        optional: true
      better-sqlite3:
        optional: true
      bun-types:
        optional: true
      expo-sqlite:
        optional: true
      gel:
        optional: true
      knex:
        optional: true
      kysely:
        optional: true
      mysql2:
        optional: true
      pg:
        optional: true
      postgres:
        optional: true
      prisma:
        optional: true
      sql.js:
        optional: true
      sqlite3:
        optional: true

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  esbuild-register@3.6.0:
    resolution: {integrity: sha512-H2/S7Pm8a9CL1uhp9OvjwrBh5Pvx0H8qVOxNu8Wed9Y7qv56MPtq+GGM8RJpq6glYJn9Wspr8uw7l55uyinNeg==}
    peerDependencies:
      esbuild: '>=0.12 <1'

  esbuild@0.18.20:
    resolution: {integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==}
    engines: {node: '>=12'}
    hasBin: true

  esbuild@0.25.4:
    resolution: {integrity: sha512-8pgjLUcUjcgDg+2Q4NYXnPbo/vncAY4UmyaCm0jZevERqCHZIaWwdJHkf8XQtu4AxSKCdvrUbT0XUr1IdZzI8Q==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  escodegen@2.1.0:
    resolution: {integrity: sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==}
    engines: {node: '>=6.0'}
    hasBin: true

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-util-is-identifier-name@3.0.0:
    resolution: {integrity: sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  extract-zip@2.0.1:
    resolution: {integrity: sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==}
    engines: {node: '>= 10.17.0'}
    hasBin: true

  fast-fifo@1.3.2:
    resolution: {integrity: sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  fd-slicer@1.1.0:
    resolution: {integrity: sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data-encoder@1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}

  form-data@4.0.2:
    resolution: {integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==}
    engines: {node: '>= 6'}

  formdata-node@4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}

  framer-motion@12.9.1:
    resolution: {integrity: sha512-dZBp2TO0a39Cc24opshlLoM0/OdTZVKzcXWuhntfwy2Qgz3t9+N4sTyUqNANyHaRFiJUWbwwsXeDvQkEBPky+g==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-stream@5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}

  get-tsconfig@4.10.1:
    resolution: {integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==}

  get-uri@6.0.4:
    resolution: {integrity: sha512-E1b1lFFLvLgak2whF2xDBcOy6NLVGZBqqjJjsIhvopKfWWEi64pLVTWWehV8KlLerZkfNTA95sTe2OdJKm1OzQ==}
    engines: {node: '>= 14'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hast-util-to-jsx-runtime@2.3.6:
    resolution: {integrity: sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==}

  hast-util-whitespace@3.0.0:
    resolution: {integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==}

  html-url-attributes@3.0.1:
    resolution: {integrity: sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  int53@0.2.4:
    resolution: {integrity: sha512-a5jlKftS7HUOhkUyYD7j2sJ/ZnvWiNlZS1ldR+g1ifQ+/UuZXIE+YTc/lK1qGj/GwAU5F8Z0e1eVq2t1J5Ob2g==}

  ip-address@9.0.5:
    resolution: {integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==}
    engines: {node: '>= 12'}

  is-alphabetical@2.0.1:
    resolution: {integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==}

  is-alphanumerical@2.0.1:
    resolution: {integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-decimal@2.0.1:
    resolution: {integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-hexadecimal@2.0.1:
    resolution: {integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true

  jose@5.10.0:
    resolution: {integrity: sha512-s+3Al/p9g32Iq+oqXxkW//7jk2Vig6FF1CFqzVXoTUXt2qz89YWbL+OwS17NFYEvxC35n0FKeGO2LGYSxeM2Gg==}

  js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}

  jsbn@1.1.0:
    resolution: {integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==}

  json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  jsondiffpatch@0.6.0:
    resolution: {integrity: sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true

  lightningcss-darwin-arm64@1.29.2:
    resolution: {integrity: sha512-cK/eMabSViKn/PG8U/a7aCorpeKLMlK0bQeNHmdb7qUnBkNPnL+oV5DjJUo0kqWsJUapZsM4jCfYItbqBDvlcA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.29.2:
    resolution: {integrity: sha512-j5qYxamyQw4kDXX5hnnCKMf3mLlHvG44f24Qyi2965/Ycz829MYqjrVg2H8BidybHBp9kom4D7DR5VqCKDXS0w==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.29.2:
    resolution: {integrity: sha512-wDk7M2tM78Ii8ek9YjnY8MjV5f5JN2qNVO+/0BAGZRvXKtQrBC4/cn4ssQIpKIPP44YXw6gFdpUF+Ps+RGsCwg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.29.2:
    resolution: {integrity: sha512-IRUrOrAF2Z+KExdExe3Rz7NSTuuJ2HvCGlMKoquK5pjvo2JY4Rybr+NrKnq0U0hZnx5AnGsuFHjGnNT14w26sg==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.29.2:
    resolution: {integrity: sha512-KKCpOlmhdjvUTX/mBuaKemp0oeDIBBLFiU5Fnqxh1/DZ4JPZi4evEH7TKoSBFOSOV3J7iEmmBaw/8dpiUvRKlQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.29.2:
    resolution: {integrity: sha512-Q64eM1bPlOOUgxFmoPUefqzY1yV3ctFPE6d/Vt7WzLW4rKTv7MyYNky+FWxRpLkNASTnKQUaiMJ87zNODIrrKQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.29.2:
    resolution: {integrity: sha512-0v6idDCPG6epLXtBH/RPkHvYx74CVziHo6TMYga8O2EiQApnUPZsbR9nFNrg2cgBzk1AYqEd95TlrsL7nYABQg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.29.2:
    resolution: {integrity: sha512-rMpz2yawkgGT8RULc5S4WiZopVMOFWjiItBT7aSfDX4NQav6M44rhn5hjtkKzB+wMTRlLLqxkeYEtQ3dd9696w==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.29.2:
    resolution: {integrity: sha512-nL7zRW6evGQqYVu/bKGK+zShyz8OVzsCotFgc7judbt6wnB2KbiKKJwBE4SGoDBQ1O94RjW4asrCjQL4i8Fhbw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.29.2:
    resolution: {integrity: sha512-EdIUW3B2vLuHmv7urfzMI/h2fmlnOQBk1xlsDxkN1tCWKjNFjfLhGxYk8C8mzpSfr+A6jFFIi8fU6LbQGsRWjA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.29.2:
    resolution: {integrity: sha512-6b6gd/RUXKaw5keVdSEtqFVdzWnU5jMxTUjA2bVcMNPLwSQ08Sv/UodBVtETLCn7k4S1Ibxwh7k68IwLZPgKaA==}
    engines: {node: '>= 12.0.0'}

  longest-streak@3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}

  lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}

  lru-cache@7.18.3:
    resolution: {integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==}
    engines: {node: '>=12'}

  lucide-react@0.503.0:
    resolution: {integrity: sha512-HGGkdlPWQ0vTF8jJ5TdIqhQXZi6uh3LnNgfZ8MHiuxFfX3RZeA79r2MW2tHAZKlAVfoNE8esm3p+O6VkIvpj6w==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  lzo@0.4.11:
    resolution: {integrity: sha512-apQHNoW2Alg72FMqaC/7pn03I7umdgSVFt2KRkCXXils4Z9u3QBh1uOtl2O5WmZIDLd9g6Lu4lIdOLmiSTFVCQ==}

  map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}

  markdown-table@3.0.4:
    resolution: {integrity: sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mdast-util-find-and-replace@3.0.2:
    resolution: {integrity: sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==}

  mdast-util-from-markdown@2.0.2:
    resolution: {integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==}

  mdast-util-gfm-autolink-literal@2.0.1:
    resolution: {integrity: sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==}

  mdast-util-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==}

  mdast-util-gfm-strikethrough@2.0.0:
    resolution: {integrity: sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==}

  mdast-util-gfm-table@2.0.0:
    resolution: {integrity: sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==}

  mdast-util-gfm-task-list-item@2.0.0:
    resolution: {integrity: sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==}

  mdast-util-gfm@3.1.0:
    resolution: {integrity: sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==}

  mdast-util-mdx-expression@2.0.1:
    resolution: {integrity: sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==}

  mdast-util-mdx-jsx@3.2.0:
    resolution: {integrity: sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==}

  mdast-util-mdxjs-esm@2.0.1:
    resolution: {integrity: sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==}

  mdast-util-phrasing@4.1.0:
    resolution: {integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==}

  mdast-util-to-hast@13.2.0:
    resolution: {integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==}

  mdast-util-to-markdown@2.1.2:
    resolution: {integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==}

  mdast-util-to-string@4.0.0:
    resolution: {integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==}

  micromark-core-commonmark@2.0.3:
    resolution: {integrity: sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==}

  micromark-extension-gfm-autolink-literal@2.1.0:
    resolution: {integrity: sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==}

  micromark-extension-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==}

  micromark-extension-gfm-strikethrough@2.1.0:
    resolution: {integrity: sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==}

  micromark-extension-gfm-table@2.1.1:
    resolution: {integrity: sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==}

  micromark-extension-gfm-tagfilter@2.0.0:
    resolution: {integrity: sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==}

  micromark-extension-gfm-task-list-item@2.1.0:
    resolution: {integrity: sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==}

  micromark-extension-gfm@3.0.0:
    resolution: {integrity: sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==}

  micromark-factory-destination@2.0.1:
    resolution: {integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==}

  micromark-factory-label@2.0.1:
    resolution: {integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==}

  micromark-factory-space@2.0.1:
    resolution: {integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==}

  micromark-factory-title@2.0.1:
    resolution: {integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==}

  micromark-factory-whitespace@2.0.1:
    resolution: {integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==}

  micromark-util-character@2.1.1:
    resolution: {integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==}

  micromark-util-chunked@2.0.1:
    resolution: {integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==}

  micromark-util-classify-character@2.0.1:
    resolution: {integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==}

  micromark-util-combine-extensions@2.0.1:
    resolution: {integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==}

  micromark-util-decode-numeric-character-reference@2.0.2:
    resolution: {integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==}

  micromark-util-decode-string@2.0.1:
    resolution: {integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==}

  micromark-util-encode@2.0.1:
    resolution: {integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==}

  micromark-util-html-tag-name@2.0.1:
    resolution: {integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==}

  micromark-util-normalize-identifier@2.0.1:
    resolution: {integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==}

  micromark-util-resolve-all@2.0.1:
    resolution: {integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==}

  micromark-util-sanitize-uri@2.0.1:
    resolution: {integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==}

  micromark-util-subtokenize@2.1.0:
    resolution: {integrity: sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==}

  micromark-util-symbol@2.0.1:
    resolution: {integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==}

  micromark-util-types@2.0.2:
    resolution: {integrity: sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==}

  micromark@4.0.2:
    resolution: {integrity: sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  motion-dom@12.9.1:
    resolution: {integrity: sha512-xqXEwRLDYDTzOgXobSoWtytRtGlf7zdkRfFbrrdP7eojaGQZ5Go4OOKtgnx7uF8sAkfr1ZjMvbCJSCIT2h6fkQ==}

  motion-utils@12.8.3:
    resolution: {integrity: sha512-GYVauZEbca8/zOhEiYOY9/uJeedYQld6co/GJFKOy//0c/4lDqk0zB549sBYqqV2iMuX+uHrY1E5zd8A2L+1Lw==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.1.5:
    resolution: {integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==}
    engines: {node: ^18 || >=20}
    hasBin: true

  netmask@2.0.2:
    resolution: {integrity: sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==}
    engines: {node: '>= 0.4.0'}

  neverthrow@7.2.0:
    resolution: {integrity: sha512-iGBUfFB7yPczHHtA8dksKTJ9E8TESNTAx1UQWW6TzMF280vo9jdPYpLUXrMN1BCkPdHFdNG3fxOt2CUad8KhAw==}
    engines: {node: '>=18'}

  next-plausible@3.12.4:
    resolution: {integrity: sha512-cD3+ixJxf8yBYvsideTxqli3fvrB7R4BXcvsNJz8Sm2X1QN039WfiXjCyNWkub4h5++rRs6fHhchUMnOuJokcg==}
    peerDependencies:
      next: '^11.1.0 || ^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0 '
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  next@15.3.2:
    resolution: {integrity: sha512-CA3BatMyHkxZ48sgOCLdVHjFU36N7TF1HhqAHLFOkV6buwZnvMI84Cug8xD56B9mCuKrqXnLn94417GrZ/jjCQ==}
    engines: {node: ^18.18.0 || ^19.8.0 || >= 20.0.0}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      babel-plugin-react-compiler: '*'
      react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      babel-plugin-react-compiler:
        optional: true
      sass:
        optional: true

  no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}
    deprecated: Use your platform's native DOMException instead

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}

  object-stream@0.0.1:
    resolution: {integrity: sha512-+NPJnRvX9RDMRY9mOWOo/NDppBjbZhXirNNSu2IBnuNboClC9h1ZGHXgHBLDbJMHsxeJDq922aVmG5xs24a/cA==}
    engines: {node: '>=0.10'}

  obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  pac-proxy-agent@7.2.0:
    resolution: {integrity: sha512-TEB8ESquiLMc0lV8vcd5Ql/JAKAoyzHFXaStwjkzpOpC5Yv+pIzLfHvjTSdf3vpa2bMiUQrg9i6276yn8666aA==}
    engines: {node: '>= 14'}

  pac-resolver@7.0.1:
    resolution: {integrity: sha512-5NPgf87AT2STgwa2ntRMr45jTKrYBGkVU36yT0ig/n/GMAa3oPqhZfIQ2kMEimReg0+t9kZViDVZ83qfVUlckg==}
    engines: {node: '>= 14'}

  parquetjs@0.11.2:
    resolution: {integrity: sha512-Y6FOc3Oi2AxY4TzJPz7fhICCR8tQNL3p+2xGQoUAMbmlJBR7+JJmMrwuyMjIpDiM7G8Wj/8oqOH4UDUmu4I5ZA==}
    engines: {node: '>=7.6'}

  parse-entities@4.0.2:
    resolution: {integrity: sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==}

  pend@1.2.0:
    resolution: {integrity: sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==}

  pg-int8@1.0.1:
    resolution: {integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==}
    engines: {node: '>=4.0.0'}

  pg-numeric@1.0.2:
    resolution: {integrity: sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==}
    engines: {node: '>=4'}

  pg-protocol@1.10.0:
    resolution: {integrity: sha512-IpdytjudNuLv8nhlHs/UrVBhU0e78J0oIS/0AVdTbWxSOkFUVdsHC/NrorO6nXsQNDTT1kzDSOMJubBQviX18Q==}

  pg-types@4.0.2:
    resolution: {integrity: sha512-cRL3JpS3lKMGsKaWndugWQoLOCoP+Cic8oseVcbr0qhPzYD5DWXK+RZ9LY9wxRf7RQia4SCwQlXk0q6FCPrVng==}
    engines: {node: '>=10'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}

  postgres-array@3.0.4:
    resolution: {integrity: sha512-nAUSGfSDGOaOAEGwqsRY27GPOea7CNipJPOA7lPbdEpx5Kg3qzdP0AaWC5MlhTWV9s4hFX39nomVZ+C4tnGOJQ==}
    engines: {node: '>=12'}

  postgres-bytea@3.0.0:
    resolution: {integrity: sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==}
    engines: {node: '>= 6'}

  postgres-date@2.1.0:
    resolution: {integrity: sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==}
    engines: {node: '>=12'}

  postgres-interval@3.0.0:
    resolution: {integrity: sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==}
    engines: {node: '>=12'}

  postgres-range@1.1.4:
    resolution: {integrity: sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  progress-stream@2.0.0:
    resolution: {integrity: sha512-xJwOWR46jcXUq6EH9yYyqp+I52skPySOeHfkxOZ2IY1AiBi/sFJhbhAKHoV3OTw/omQ45KTio9215dRJ2Yxd3Q==}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  property-information@7.1.0:
    resolution: {integrity: sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==}

  proxy-agent@6.5.0:
    resolution: {integrity: sha512-TmatMXdr2KlRiA2CyDu8GqR8EjahTG3aY3nXjdzFyoZbmB8hrBsTyMezhULIXKnC0jpfjlmiZ3+EaCzoInSu/A==}
    engines: {node: '>= 14'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  pump@3.0.2:
    resolution: {integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==}

  puppeteer-core@24.10.0:
    resolution: {integrity: sha512-xX0QJRc8t19iAwRDsAOR38Q/Zx/W6WVzJCEhKCAwp2XMsaWqfNtQ+rBfQW9PlF+Op24d7c8Zlgq9YNmbnA7hdQ==}
    engines: {node: '>=18'}

  q@1.5.1:
    resolution: {integrity: sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==}
    engines: {node: '>=0.6.0', teleport: '>=0.2.0'}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  react-dom@19.1.0:
    resolution: {integrity: sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==}
    peerDependencies:
      react: ^19.1.0

  react-icons@5.5.0:
    resolution: {integrity: sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==}
    peerDependencies:
      react: '*'

  react-markdown@10.1.0:
    resolution: {integrity: sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==}
    peerDependencies:
      '@types/react': '>=18'
      react: '>=18'

  react-remove-scroll-bar@2.3.8:
    resolution: {integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.7.1:
    resolution: {integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-style-singleton@2.2.3:
    resolution: {integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react@19.1.0:
    resolution: {integrity: sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==}
    engines: {node: '>=0.10.0'}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  remark-gfm@4.0.1:
    resolution: {integrity: sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==}

  remark-parse@11.0.0:
    resolution: {integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==}

  remark-rehype@11.1.2:
    resolution: {integrity: sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==}

  remark-stringify@11.0.0:
    resolution: {integrity: sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  scheduler@0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}

  secure-json-parse@2.7.0:
    resolution: {integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==}

  semver@7.7.1:
    resolution: {integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==}
    engines: {node: '>=10'}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  server-only@0.0.1:
    resolution: {integrity: sha512-qepMx2JxAa5jjfzxG79yPPq+8BuFToHd1hm7kI+Z4zAq1ftQiP7HcxMhDDItrbtwVeLg/cY2JnKnrcFkmiswNA==}

  sharp@0.34.1:
    resolution: {integrity: sha512-1j0w61+eVxu7DawFJtnfYcvSv6qPFvfTaqzTQ2BLknVhHTwGS8sc63ZBF4rzkWMBVKybo4S5OBtDdZahh2A1xg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  snake-case@3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==}

  snakecase-keys@8.0.1:
    resolution: {integrity: sha512-Sj51kE1zC7zh6TDlNNz0/Jn1n5HiHdoQErxO8jLtnyrkJW/M5PrI7x05uDgY3BO7OUQYKCvmeMurW6BPUdwEOw==}
    engines: {node: '>=18'}

  snappyjs@0.6.1:
    resolution: {integrity: sha512-YIK6I2lsH072UE0aOFxxY1dPDCS43I5ktqHpeAsuLNYWkE5pGxRGWfDM4/vSUfNzXjC1Ivzt3qx31PCLmc9yqg==}

  socks-proxy-agent@8.0.5:
    resolution: {integrity: sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw==}
    engines: {node: '>= 14'}

  socks@2.8.5:
    resolution: {integrity: sha512-iF+tNDQla22geJdTyJB1wM/qrX9DMRwWrciEPwWLPRWAUEM8sQiyxgckLxWT1f7+9VabJS0jTGGr4QgBuvi6Ww==}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}

  sonner@2.0.3:
    resolution: {integrity: sha512-njQ4Hht92m0sMqqHVDL32V2Oun9W1+PHO9NDv9FHfJjT3JT22IG4Jpo3FPQy+mouRKCXFWO+r67v6MrHX2zeIA==}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}

  speedometer@1.0.0:
    resolution: {integrity: sha512-lgxErLl/7A5+vgIIXsh9MbeukOaCb2axgQ+bKCdIE+ibNT4XNYGNCR1qFEGq6F+YDASXK3Fh/c5FgtZchFolxw==}

  sprintf-js@1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==}

  std-env@3.9.0:
    resolution: {integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==}

  streamsearch@1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}

  streamx@2.22.1:
    resolution: {integrity: sha512-znKXEBxfatz2GBNK02kRnCXjV+AA4kjZIUxeWSr3UGirZMJfTE9uiwKHobnbgxWyL/JWro8tTq+vOqAK1/qbSA==}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  stringify-entities@4.0.4:
    resolution: {integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strnum@1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}

  style-to-js@1.1.16:
    resolution: {integrity: sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==}

  style-to-object@1.0.8:
    resolution: {integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==}

  styled-jsx@5.1.6:
    resolution: {integrity: sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true

  swr@2.3.3:
    resolution: {integrity: sha512-dshNvs3ExOqtZ6kJBaAsabhPdHyeY4P2cKwRCniDVifBMoG/SVI7tfLWqPXriVspf2Rg4tPzXJTnwaihIeFw2A==}
    peerDependencies:
      react: ^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  tailwind-merge@3.3.0:
    resolution: {integrity: sha512-fyW/pEfcQSiigd5SNn0nApUOxx0zB/dm6UDU/rEwc2c3sX2smWUNbapHv+QRqLGVp9GWX3THIa7MUGPo+YkDzQ==}

  tailwindcss@4.1.3:
    resolution: {integrity: sha512-2Q+rw9vy1WFXu5cIxlvsabCwhU2qUwodGq03ODhLJ0jW4ek5BUtoCsnLB0qG+m8AHgEsSJcJGDSDe06FXlP74g==}

  tailwindcss@4.1.4:
    resolution: {integrity: sha512-1ZIUqtPITFbv/DxRmDr5/agPqJwF69d24m9qmM1939TJehgY539CtzeZRjbLt5G6fSy/7YqqYsfvoTEw9xUI2A==}

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  tar-fs@3.0.9:
    resolution: {integrity: sha512-XF4w9Xp+ZQgifKakjZYmFdkLoSWd34VGKcsTCwlNWM7QG3ZbaxnTsaBwnjFZqHRf/rROxaR8rXnbtwdvaDI+lA==}

  tar-stream@3.1.7:
    resolution: {integrity: sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==}

  text-decoder@1.2.3:
    resolution: {integrity: sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA==}

  thrift@0.11.0:
    resolution: {integrity: sha512-UpsBhOC45a45TpeHOXE4wwYwL8uD2apbHTbtBvkwtUU4dNwCjC7DpQTjw2Q6eIdfNtw+dKthdwq94uLXTJPfFw==}
    engines: {node: '>= 4.1.0'}

  throttleit@2.1.0:
    resolution: {integrity: sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw==}
    engines: {node: '>=18'}

  through2@2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}

  together-ai@0.16.0:
    resolution: {integrity: sha512-k8vrcjNKlaklf9lFkIeNzH/M/xt2Ykt84/RruK+AGMu8G/+M2Kx4kHyjx6kgSgt8wpgi3JvvzZ6fg7Y3FORuFA==}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  trough@2.2.0:
    resolution: {integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsx@4.19.4:
    resolution: {integrity: sha512-gK5GVzDkJK1SI1zwHf32Mqxf2tSJkNx+eYcNly5+nHvWqXUJYUkWBQtKauoESz3ymezAI++ZwT855x5p5eop+Q==}
    engines: {node: '>=18.0.0'}
    hasBin: true

  tw-animate-css@1.3.1:
    resolution: {integrity: sha512-Z6tgVNW3em81PoF5+L5jRBuVvmhwN7bvyB76aQK0o3tWqe9Qp/yc7SUm+ZZK4beyj4XaCsD6e9gCS1avOfGhWA==}

  type-fest@4.41.0:
    resolution: {integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==}
    engines: {node: '>=16'}

  typed-query-selector@2.12.0:
    resolution: {integrity: sha512-SbklCd1F0EiZOyPiW192rrHZzZ5sBijB6xM+cpmrwDqObvdtunOHHIk9fCGsoK5JVIYXoyEp4iEdE3upFH3PAg==}

  typescript-event-target@1.1.1:
    resolution: {integrity: sha512-dFSOFBKV6uwaloBCCUhxlD3Pr/P1a/tJdcmPrTXCHlEFD3faj0mztjcGn6VBAhQ0/Bdy8K3VWrrqwbt/ffsYsg==}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  unified@11.0.5:
    resolution: {integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==}

  unist-util-is@6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}

  unist-util-position@5.0.0:
    resolution: {integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==}

  unist-util-stringify-position@4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}

  unist-util-visit-parents@6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}

  unist-util-visit@5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution: {integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  varint@5.0.2:
    resolution: {integrity: sha512-lKxKYG6H03yCZUpAGOPOsMcGxd1RHCu1iKvEHYDPmTyq2HueGhD73ssNBqqQWfvYs04G9iUFRvmAVLW20Jw6ow==}

  vfile-message@4.0.2:
    resolution: {integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==}

  vfile@6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}

  web-streams-polyfill@4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  ws@8.18.2:
    resolution: {integrity: sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yauzl@2.10.0:
    resolution: {integrity: sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==}

  zod-to-json-schema@3.24.5:
    resolution: {integrity: sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==}
    peerDependencies:
      zod: ^3.24.1

  zod@3.25.20:
    resolution: {integrity: sha512-z03fqpTMDF1G02VLKUMt6vyACE7rNWkh3gpXVHgPTw28NPtDFRGvcpTtPwn2kMKtQ0idtYJUTxchytmnqYswcw==}

  zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}

snapshots:

  '@ai-sdk/openai-compatible@0.2.14(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8(zod@3.25.20)
      zod: 3.25.20

  '@ai-sdk/openai@1.3.19(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.7(zod@3.25.20)
      zod: 3.25.20

  '@ai-sdk/provider-utils@2.2.7(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider': 1.1.3
      nanoid: 3.3.11
      secure-json-parse: 2.7.0
      zod: 3.25.20

  '@ai-sdk/provider-utils@2.2.8(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider': 1.1.3
      nanoid: 3.3.11
      secure-json-parse: 2.7.0
      zod: 3.25.20

  '@ai-sdk/provider@1.1.3':
    dependencies:
      json-schema: 0.4.0

  '@ai-sdk/react@1.2.12(react@19.1.0)(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider-utils': 2.2.8(zod@3.25.20)
      '@ai-sdk/ui-utils': 1.2.11(zod@3.25.20)
      react: 19.1.0
      swr: 2.3.3(react@19.1.0)
      throttleit: 2.1.0
    optionalDependencies:
      zod: 3.25.20

  '@ai-sdk/togetherai@0.2.14(zod@3.25.20)':
    dependencies:
      '@ai-sdk/openai-compatible': 0.2.14(zod@3.25.20)
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8(zod@3.25.20)
      zod: 3.25.20

  '@ai-sdk/ui-utils@1.2.11(zod@3.25.20)':
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8(zod@3.25.20)
      zod: 3.25.20
      zod-to-json-schema: 3.24.5(zod@3.25.20)

  '@alloc/quick-lru@5.2.0': {}

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.804.0
      tslib: 2.8.1

  '@aws-crypto/crc32c@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.804.0
      tslib: 2.8.1

  '@aws-crypto/sha1-browser@5.2.0':
    dependencies:
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-locate-window': 3.804.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-locate-window': 3.804.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.804.0
      tslib: 2.8.1

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.8.1

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-sdk/client-s3@3.817.0':
    dependencies:
      '@aws-crypto/sha1-browser': 5.2.0
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/credential-provider-node': 3.817.0
      '@aws-sdk/middleware-bucket-endpoint': 3.808.0
      '@aws-sdk/middleware-expect-continue': 3.804.0
      '@aws-sdk/middleware-flexible-checksums': 3.816.0
      '@aws-sdk/middleware-host-header': 3.804.0
      '@aws-sdk/middleware-location-constraint': 3.804.0
      '@aws-sdk/middleware-logger': 3.804.0
      '@aws-sdk/middleware-recursion-detection': 3.804.0
      '@aws-sdk/middleware-sdk-s3': 3.816.0
      '@aws-sdk/middleware-ssec': 3.804.0
      '@aws-sdk/middleware-user-agent': 3.816.0
      '@aws-sdk/region-config-resolver': 3.808.0
      '@aws-sdk/signature-v4-multi-region': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-endpoints': 3.808.0
      '@aws-sdk/util-user-agent-browser': 3.804.0
      '@aws-sdk/util-user-agent-node': 3.816.0
      '@aws-sdk/xml-builder': 3.804.0
      '@smithy/config-resolver': 4.1.3
      '@smithy/core': 3.4.0
      '@smithy/eventstream-serde-browser': 4.0.3
      '@smithy/eventstream-serde-config-resolver': 4.1.1
      '@smithy/eventstream-serde-node': 4.0.3
      '@smithy/fetch-http-handler': 5.0.3
      '@smithy/hash-blob-browser': 4.0.3
      '@smithy/hash-node': 4.0.3
      '@smithy/hash-stream-node': 4.0.3
      '@smithy/invalid-dependency': 4.0.3
      '@smithy/md5-js': 4.0.3
      '@smithy/middleware-content-length': 4.0.3
      '@smithy/middleware-endpoint': 4.1.7
      '@smithy/middleware-retry': 4.1.8
      '@smithy/middleware-serde': 4.0.6
      '@smithy/middleware-stack': 4.0.3
      '@smithy/node-config-provider': 4.1.2
      '@smithy/node-http-handler': 4.0.5
      '@smithy/protocol-http': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/url-parser': 4.0.3
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.15
      '@smithy/util-defaults-mode-node': 4.0.15
      '@smithy/util-endpoints': 3.0.5
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-retry': 4.0.4
      '@smithy/util-stream': 4.2.1
      '@smithy/util-utf8': 4.0.0
      '@smithy/util-waiter': 4.0.4
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso@3.817.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/middleware-host-header': 3.804.0
      '@aws-sdk/middleware-logger': 3.804.0
      '@aws-sdk/middleware-recursion-detection': 3.804.0
      '@aws-sdk/middleware-user-agent': 3.816.0
      '@aws-sdk/region-config-resolver': 3.808.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-endpoints': 3.808.0
      '@aws-sdk/util-user-agent-browser': 3.804.0
      '@aws-sdk/util-user-agent-node': 3.816.0
      '@smithy/config-resolver': 4.1.3
      '@smithy/core': 3.4.0
      '@smithy/fetch-http-handler': 5.0.3
      '@smithy/hash-node': 4.0.3
      '@smithy/invalid-dependency': 4.0.3
      '@smithy/middleware-content-length': 4.0.3
      '@smithy/middleware-endpoint': 4.1.7
      '@smithy/middleware-retry': 4.1.8
      '@smithy/middleware-serde': 4.0.6
      '@smithy/middleware-stack': 4.0.3
      '@smithy/node-config-provider': 4.1.2
      '@smithy/node-http-handler': 4.0.5
      '@smithy/protocol-http': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/url-parser': 4.0.3
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.15
      '@smithy/util-defaults-mode-node': 4.0.15
      '@smithy/util-endpoints': 3.0.5
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-retry': 4.0.4
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.816.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/core': 3.4.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/property-provider': 4.0.3
      '@smithy/protocol-http': 5.1.1
      '@smithy/signature-v4': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/util-middleware': 4.0.3
      fast-xml-parser: 4.4.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-env@3.816.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/property-provider': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/credential-provider-http@3.816.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/fetch-http-handler': 5.0.3
      '@smithy/node-http-handler': 4.0.5
      '@smithy/property-provider': 4.0.3
      '@smithy/protocol-http': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/util-stream': 4.2.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-ini@3.817.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/credential-provider-env': 3.816.0
      '@aws-sdk/credential-provider-http': 3.816.0
      '@aws-sdk/credential-provider-process': 3.816.0
      '@aws-sdk/credential-provider-sso': 3.817.0
      '@aws-sdk/credential-provider-web-identity': 3.817.0
      '@aws-sdk/nested-clients': 3.817.0
      '@aws-sdk/types': 3.804.0
      '@smithy/credential-provider-imds': 4.0.5
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-node@3.817.0':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.816.0
      '@aws-sdk/credential-provider-http': 3.816.0
      '@aws-sdk/credential-provider-ini': 3.817.0
      '@aws-sdk/credential-provider-process': 3.816.0
      '@aws-sdk/credential-provider-sso': 3.817.0
      '@aws-sdk/credential-provider-web-identity': 3.817.0
      '@aws-sdk/types': 3.804.0
      '@smithy/credential-provider-imds': 4.0.5
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-process@3.816.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/credential-provider-sso@3.817.0':
    dependencies:
      '@aws-sdk/client-sso': 3.817.0
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/token-providers': 3.817.0
      '@aws-sdk/types': 3.804.0
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.817.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/nested-clients': 3.817.0
      '@aws-sdk/types': 3.804.0
      '@smithy/property-provider': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/middleware-bucket-endpoint@3.808.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-arn-parser': 3.804.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      '@smithy/util-config-provider': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-expect-continue@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-flexible-checksums@3.816.0':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@aws-crypto/crc32c': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/is-array-buffer': 4.0.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-stream': 4.2.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-host-header@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-location-constraint@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-logger@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-recursion-detection@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-sdk-s3@3.816.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-arn-parser': 3.804.0
      '@smithy/core': 3.4.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/protocol-http': 5.1.1
      '@smithy/signature-v4': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-stream': 4.2.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@aws-sdk/middleware-ssec@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/middleware-user-agent@3.816.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-endpoints': 3.808.0
      '@smithy/core': 3.4.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/nested-clients@3.817.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/middleware-host-header': 3.804.0
      '@aws-sdk/middleware-logger': 3.804.0
      '@aws-sdk/middleware-recursion-detection': 3.804.0
      '@aws-sdk/middleware-user-agent': 3.816.0
      '@aws-sdk/region-config-resolver': 3.808.0
      '@aws-sdk/types': 3.804.0
      '@aws-sdk/util-endpoints': 3.808.0
      '@aws-sdk/util-user-agent-browser': 3.804.0
      '@aws-sdk/util-user-agent-node': 3.816.0
      '@smithy/config-resolver': 4.1.3
      '@smithy/core': 3.4.0
      '@smithy/fetch-http-handler': 5.0.3
      '@smithy/hash-node': 4.0.3
      '@smithy/invalid-dependency': 4.0.3
      '@smithy/middleware-content-length': 4.0.3
      '@smithy/middleware-endpoint': 4.1.7
      '@smithy/middleware-retry': 4.1.8
      '@smithy/middleware-serde': 4.0.6
      '@smithy/middleware-stack': 4.0.3
      '@smithy/node-config-provider': 4.1.2
      '@smithy/node-http-handler': 4.0.5
      '@smithy/protocol-http': 5.1.1
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/url-parser': 4.0.3
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.15
      '@smithy/util-defaults-mode-node': 4.0.15
      '@smithy/util-endpoints': 3.0.5
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-retry': 4.0.4
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/region-config-resolver@3.808.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/types': 4.3.0
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.3
      tslib: 2.8.1

  '@aws-sdk/signature-v4-multi-region@3.816.0':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/signature-v4': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/token-providers@3.817.0':
    dependencies:
      '@aws-sdk/core': 3.816.0
      '@aws-sdk/nested-clients': 3.817.0
      '@aws-sdk/types': 3.804.0
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/types@3.804.0':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/util-arn-parser@3.804.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-endpoints@3.808.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/types': 4.3.0
      '@smithy/util-endpoints': 3.0.5
      tslib: 2.8.1

  '@aws-sdk/util-locate-window@3.804.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-browser@3.804.0':
    dependencies:
      '@aws-sdk/types': 3.804.0
      '@smithy/types': 4.3.0
      bowser: 2.11.0
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-node@3.816.0':
    dependencies:
      '@aws-sdk/middleware-user-agent': 3.816.0
      '@aws-sdk/types': 3.804.0
      '@smithy/node-config-provider': 4.1.2
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@aws-sdk/xml-builder@3.804.0':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@clerk/backend@1.34.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@clerk/shared': 3.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@clerk/types': 4.59.3
      cookie: 1.0.2
      snakecase-keys: 8.0.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - react
      - react-dom

  '@clerk/clerk-react@5.31.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@clerk/shared': 3.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@clerk/types': 4.59.3
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      tslib: 2.8.1

  '@clerk/nextjs@6.20.2(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@clerk/backend': 1.34.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@clerk/clerk-react': 5.31.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@clerk/shared': 3.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@clerk/types': 4.59.3
      next: 15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      server-only: 0.0.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - svix

  '@clerk/shared@3.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@clerk/types': 4.59.3
      dequal: 2.0.3
      glob-to-regexp: 0.4.1
      js-cookie: 3.0.5
      std-env: 3.9.0
      swr: 2.3.3(react@19.1.0)
    optionalDependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@clerk/types@4.59.3':
    dependencies:
      csstype: 3.1.3

  '@drizzle-team/brocli@0.10.2': {}

  '@emnapi/runtime@1.4.0':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@esbuild-kit/core-utils@3.3.2':
    dependencies:
      esbuild: 0.18.20
      source-map-support: 0.5.21

  '@esbuild-kit/esm-loader@2.6.5':
    dependencies:
      '@esbuild-kit/core-utils': 3.3.2
      get-tsconfig: 4.10.1

  '@esbuild/aix-ppc64@0.25.4':
    optional: true

  '@esbuild/android-arm64@0.18.20':
    optional: true

  '@esbuild/android-arm64@0.25.4':
    optional: true

  '@esbuild/android-arm@0.18.20':
    optional: true

  '@esbuild/android-arm@0.25.4':
    optional: true

  '@esbuild/android-x64@0.18.20':
    optional: true

  '@esbuild/android-x64@0.25.4':
    optional: true

  '@esbuild/darwin-arm64@0.18.20':
    optional: true

  '@esbuild/darwin-arm64@0.25.4':
    optional: true

  '@esbuild/darwin-x64@0.18.20':
    optional: true

  '@esbuild/darwin-x64@0.25.4':
    optional: true

  '@esbuild/freebsd-arm64@0.18.20':
    optional: true

  '@esbuild/freebsd-arm64@0.25.4':
    optional: true

  '@esbuild/freebsd-x64@0.18.20':
    optional: true

  '@esbuild/freebsd-x64@0.25.4':
    optional: true

  '@esbuild/linux-arm64@0.18.20':
    optional: true

  '@esbuild/linux-arm64@0.25.4':
    optional: true

  '@esbuild/linux-arm@0.18.20':
    optional: true

  '@esbuild/linux-arm@0.25.4':
    optional: true

  '@esbuild/linux-ia32@0.18.20':
    optional: true

  '@esbuild/linux-ia32@0.25.4':
    optional: true

  '@esbuild/linux-loong64@0.18.20':
    optional: true

  '@esbuild/linux-loong64@0.25.4':
    optional: true

  '@esbuild/linux-mips64el@0.18.20':
    optional: true

  '@esbuild/linux-mips64el@0.25.4':
    optional: true

  '@esbuild/linux-ppc64@0.18.20':
    optional: true

  '@esbuild/linux-ppc64@0.25.4':
    optional: true

  '@esbuild/linux-riscv64@0.18.20':
    optional: true

  '@esbuild/linux-riscv64@0.25.4':
    optional: true

  '@esbuild/linux-s390x@0.18.20':
    optional: true

  '@esbuild/linux-s390x@0.25.4':
    optional: true

  '@esbuild/linux-x64@0.18.20':
    optional: true

  '@esbuild/linux-x64@0.25.4':
    optional: true

  '@esbuild/netbsd-arm64@0.25.4':
    optional: true

  '@esbuild/netbsd-x64@0.18.20':
    optional: true

  '@esbuild/netbsd-x64@0.25.4':
    optional: true

  '@esbuild/openbsd-arm64@0.25.4':
    optional: true

  '@esbuild/openbsd-x64@0.18.20':
    optional: true

  '@esbuild/openbsd-x64@0.25.4':
    optional: true

  '@esbuild/sunos-x64@0.18.20':
    optional: true

  '@esbuild/sunos-x64@0.25.4':
    optional: true

  '@esbuild/win32-arm64@0.18.20':
    optional: true

  '@esbuild/win32-arm64@0.25.4':
    optional: true

  '@esbuild/win32-ia32@0.18.20':
    optional: true

  '@esbuild/win32-ia32@0.25.4':
    optional: true

  '@esbuild/win32-x64@0.18.20':
    optional: true

  '@esbuild/win32-x64@0.25.4':
    optional: true

  '@floating-ui/core@1.7.1':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.7.1':
    dependencies:
      '@floating-ui/core': 1.7.1
      '@floating-ui/utils': 0.2.9

  '@floating-ui/react-dom@2.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/dom': 1.7.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@floating-ui/utils@0.2.9': {}

  '@img/sharp-darwin-arm64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-darwin-arm64': 1.1.0
    optional: true

  '@img/sharp-darwin-x64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-darwin-x64': 1.1.0
    optional: true

  '@img/sharp-libvips-darwin-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-darwin-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-arm@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-ppc64@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-s390x@1.1.0':
    optional: true

  '@img/sharp-libvips-linux-x64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    optional: true

  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    optional: true

  '@img/sharp-linux-arm64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm64': 1.1.0
    optional: true

  '@img/sharp-linux-arm@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linux-arm': 1.1.0
    optional: true

  '@img/sharp-linux-s390x@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linux-s390x': 1.1.0
    optional: true

  '@img/sharp-linux-x64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linux-x64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-arm64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
    optional: true

  '@img/sharp-linuxmusl-x64@0.34.1':
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
    optional: true

  '@img/sharp-wasm32@0.34.1':
    dependencies:
      '@emnapi/runtime': 1.4.0
    optional: true

  '@img/sharp-win32-ia32@0.34.1':
    optional: true

  '@img/sharp-win32-x64@0.34.1':
    optional: true

  '@mendable/firecrawl-js@1.25.5':
    dependencies:
      axios: 1.9.0
      typescript-event-target: 1.1.1
      zod: 3.25.20
      zod-to-json-schema: 3.24.5(zod@3.25.20)
    transitivePeerDependencies:
      - debug

  '@neondatabase/serverless@1.0.0':
    dependencies:
      '@types/node': 22.15.21
      '@types/pg': 8.15.2

  '@next/env@15.3.2': {}

  '@next/swc-darwin-arm64@15.3.2':
    optional: true

  '@next/swc-darwin-x64@15.3.2':
    optional: true

  '@next/swc-linux-arm64-gnu@15.3.2':
    optional: true

  '@next/swc-linux-arm64-musl@15.3.2':
    optional: true

  '@next/swc-linux-x64-gnu@15.3.2':
    optional: true

  '@next/swc-linux-x64-musl@15.3.2':
    optional: true

  '@next/swc-win32-arm64-msvc@15.3.2':
    optional: true

  '@next/swc-win32-x64-msvc@15.3.2':
    optional: true

  '@opentelemetry/api@1.9.0': {}

  '@puppeteer/browsers@2.10.5':
    dependencies:
      debug: 4.4.1
      extract-zip: 2.0.1
      progress: 2.0.3
      proxy-agent: 6.5.0
      semver: 7.7.2
      tar-fs: 3.0.9
      yargs: 17.7.2
    transitivePeerDependencies:
      - bare-buffer
      - supports-color

  '@radix-ui/number@1.1.1': {}

  '@radix-ui/primitive@1.1.2': {}

  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collapsible': 1.1.11(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-context@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.2)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.1(@types/react@19.1.2)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-direction@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-escape-keydown': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-id@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.2)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.1(@types/react@19.1.2)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-arrow': 1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-rect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-scroll-area@1.2.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-slot@1.2.3(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-visually-hidden': 1.2.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2(@types/react@19.1.2)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.2)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.2

  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.2(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2(@types/react@19.1.2)

  '@radix-ui/rect@1.1.1': {}

  '@smithy/abort-controller@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/chunked-blob-reader-native@4.0.0':
    dependencies:
      '@smithy/util-base64': 4.0.0
      tslib: 2.8.1

  '@smithy/chunked-blob-reader@5.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/config-resolver@4.1.3':
    dependencies:
      '@smithy/node-config-provider': 4.1.2
      '@smithy/types': 4.3.0
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.3
      tslib: 2.8.1

  '@smithy/core@3.4.0':
    dependencies:
      '@smithy/middleware-serde': 4.0.6
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-stream': 4.2.1
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/credential-provider-imds@4.0.5':
    dependencies:
      '@smithy/node-config-provider': 4.1.2
      '@smithy/property-provider': 4.0.3
      '@smithy/types': 4.3.0
      '@smithy/url-parser': 4.0.3
      tslib: 2.8.1

  '@smithy/eventstream-codec@4.0.3':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 4.3.0
      '@smithy/util-hex-encoding': 4.0.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-browser@4.0.3':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-config-resolver@4.1.1':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-node@4.0.3':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-universal@4.0.3':
    dependencies:
      '@smithy/eventstream-codec': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/fetch-http-handler@5.0.3':
    dependencies:
      '@smithy/protocol-http': 5.1.1
      '@smithy/querystring-builder': 4.0.3
      '@smithy/types': 4.3.0
      '@smithy/util-base64': 4.0.0
      tslib: 2.8.1

  '@smithy/hash-blob-browser@4.0.3':
    dependencies:
      '@smithy/chunked-blob-reader': 5.0.0
      '@smithy/chunked-blob-reader-native': 4.0.0
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/hash-node@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/hash-stream-node@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/invalid-dependency@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/is-array-buffer@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/md5-js@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/middleware-content-length@4.0.3':
    dependencies:
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/middleware-endpoint@4.1.7':
    dependencies:
      '@smithy/core': 3.4.0
      '@smithy/middleware-serde': 4.0.6
      '@smithy/node-config-provider': 4.1.2
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      '@smithy/url-parser': 4.0.3
      '@smithy/util-middleware': 4.0.3
      tslib: 2.8.1

  '@smithy/middleware-retry@4.1.8':
    dependencies:
      '@smithy/node-config-provider': 4.1.2
      '@smithy/protocol-http': 5.1.1
      '@smithy/service-error-classification': 4.0.4
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-retry': 4.0.4
      tslib: 2.8.1
      uuid: 9.0.1

  '@smithy/middleware-serde@4.0.6':
    dependencies:
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/middleware-stack@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/node-config-provider@4.1.2':
    dependencies:
      '@smithy/property-provider': 4.0.3
      '@smithy/shared-ini-file-loader': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/node-http-handler@4.0.5':
    dependencies:
      '@smithy/abort-controller': 4.0.3
      '@smithy/protocol-http': 5.1.1
      '@smithy/querystring-builder': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/property-provider@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/protocol-http@5.1.1':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/querystring-builder@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      '@smithy/util-uri-escape': 4.0.0
      tslib: 2.8.1

  '@smithy/querystring-parser@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/service-error-classification@4.0.4':
    dependencies:
      '@smithy/types': 4.3.0

  '@smithy/shared-ini-file-loader@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/signature-v4@5.1.1':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-middleware': 4.0.3
      '@smithy/util-uri-escape': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/smithy-client@4.3.0':
    dependencies:
      '@smithy/core': 3.4.0
      '@smithy/middleware-endpoint': 4.1.7
      '@smithy/middleware-stack': 4.0.3
      '@smithy/protocol-http': 5.1.1
      '@smithy/types': 4.3.0
      '@smithy/util-stream': 4.2.1
      tslib: 2.8.1

  '@smithy/types@4.3.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/url-parser@4.0.3':
    dependencies:
      '@smithy/querystring-parser': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/util-base64@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-body-length-browser@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-body-length-node@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.8.1

  '@smithy/util-buffer-from@4.0.0':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      tslib: 2.8.1

  '@smithy/util-config-provider@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-defaults-mode-browser@4.0.15':
    dependencies:
      '@smithy/property-provider': 4.0.3
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      bowser: 2.11.0
      tslib: 2.8.1

  '@smithy/util-defaults-mode-node@4.0.15':
    dependencies:
      '@smithy/config-resolver': 4.1.3
      '@smithy/credential-provider-imds': 4.0.5
      '@smithy/node-config-provider': 4.1.2
      '@smithy/property-provider': 4.0.3
      '@smithy/smithy-client': 4.3.0
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/util-endpoints@3.0.5':
    dependencies:
      '@smithy/node-config-provider': 4.1.2
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/util-hex-encoding@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-middleware@4.0.3':
    dependencies:
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/util-retry@4.0.4':
    dependencies:
      '@smithy/service-error-classification': 4.0.4
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@smithy/util-stream@4.2.1':
    dependencies:
      '@smithy/fetch-http-handler': 5.0.3
      '@smithy/node-http-handler': 4.0.5
      '@smithy/types': 4.3.0
      '@smithy/util-base64': 4.0.0
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-uri-escape@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.8.1

  '@smithy/util-utf8@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      tslib: 2.8.1

  '@smithy/util-waiter@4.0.4':
    dependencies:
      '@smithy/abort-controller': 4.0.3
      '@smithy/types': 4.3.0
      tslib: 2.8.1

  '@sparticuz/chromium@133.0.0':
    dependencies:
      follow-redirects: 1.15.9
      tar-fs: 3.0.9
    transitivePeerDependencies:
      - bare-buffer
      - debug

  '@swc/counter@0.1.3': {}

  '@swc/helpers@0.5.15':
    dependencies:
      tslib: 2.8.1

  '@tailwindcss/node@4.1.3':
    dependencies:
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.29.2
      tailwindcss: 4.1.3

  '@tailwindcss/oxide-android-arm64@4.1.3':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.3':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.3':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.3':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.3':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.3':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.3':
    optional: true

  '@tailwindcss/oxide@4.1.3':
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.3
      '@tailwindcss/oxide-darwin-arm64': 4.1.3
      '@tailwindcss/oxide-darwin-x64': 4.1.3
      '@tailwindcss/oxide-freebsd-x64': 4.1.3
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.3
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.3
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.3
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.3
      '@tailwindcss/oxide-linux-x64-musl': 4.1.3
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.3
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.3

  '@tailwindcss/postcss@4.1.3':
    dependencies:
      '@alloc/quick-lru': 5.2.0
      '@tailwindcss/node': 4.1.3
      '@tailwindcss/oxide': 4.1.3
      postcss: 8.5.3
      tailwindcss: 4.1.3

  '@tootallnate/quickjs-emscripten@0.23.0': {}

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/diff-match-patch@1.0.36': {}

  '@types/estree-jsx@1.0.5':
    dependencies:
      '@types/estree': 1.0.7

  '@types/estree@1.0.7': {}

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/ms@2.1.0': {}

  '@types/node-fetch@2.6.12':
    dependencies:
      '@types/node': 20.17.30
      form-data: 4.0.2

  '@types/node-int64@0.4.32':
    dependencies:
      '@types/node': 20.17.30

  '@types/node@18.19.104':
    dependencies:
      undici-types: 5.26.5

  '@types/node@20.17.30':
    dependencies:
      undici-types: 6.19.8

  '@types/node@22.15.21':
    dependencies:
      undici-types: 6.21.0

  '@types/parquetjs@0.10.6':
    dependencies:
      '@types/node-int64': 0.4.32

  '@types/pg@8.15.2':
    dependencies:
      '@types/node': 20.17.30
      pg-protocol: 1.10.0
      pg-types: 4.0.2

  '@types/progress-stream@2.0.5':
    dependencies:
      '@types/node': 20.17.30

  '@types/react-dom@19.1.2(@types/react@19.1.2)':
    dependencies:
      '@types/react': 19.1.2

  '@types/react@19.1.2':
    dependencies:
      csstype: 3.1.3

  '@types/unist@2.0.11': {}

  '@types/unist@3.0.3': {}

  '@types/yauzl@2.10.3':
    dependencies:
      '@types/node': 20.17.30
    optional: true

  '@ungap/structured-clone@1.3.0': {}

  '@upstash/core-analytics@0.0.10':
    dependencies:
      '@upstash/redis': 1.34.9

  '@upstash/qstash@2.8.1':
    dependencies:
      crypto-js: 4.2.0
      jose: 5.10.0
      neverthrow: 7.2.0

  '@upstash/ratelimit@2.0.5(@upstash/redis@1.34.9)':
    dependencies:
      '@upstash/core-analytics': 0.0.10
      '@upstash/redis': 1.34.9

  '@upstash/redis@1.34.9':
    dependencies:
      crypto-js: 4.2.0

  '@upstash/workflow@0.2.13(react@19.1.0)':
    dependencies:
      '@ai-sdk/openai': 1.3.19(zod@3.25.20)
      '@upstash/qstash': 2.8.1
      ai: 4.3.16(react@19.1.0)(zod@3.25.20)
      zod: 3.25.20
    transitivePeerDependencies:
      - react

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  agent-base@7.1.3: {}

  agentkeepalive@4.6.0:
    dependencies:
      humanize-ms: 1.2.1

  ai@4.3.16(react@19.1.0)(zod@3.25.20):
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8(zod@3.25.20)
      '@ai-sdk/react': 1.2.12(react@19.1.0)(zod@3.25.20)
      '@ai-sdk/ui-utils': 1.2.11(zod@3.25.20)
      '@opentelemetry/api': 1.9.0
      jsondiffpatch: 0.6.0
      zod: 3.25.20
    optionalDependencies:
      react: 19.1.0

  ansi-regex@5.0.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  ast-types@0.13.4:
    dependencies:
      tslib: 2.8.1

  asynckit@0.4.0: {}

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  b4a@1.6.7: {}

  bail@2.0.2: {}

  bare-events@2.5.4:
    optional: true

  bare-fs@4.1.5:
    dependencies:
      bare-events: 2.5.4
      bare-path: 3.0.0
      bare-stream: 2.6.5(bare-events@2.5.4)
    optional: true

  bare-os@3.6.1:
    optional: true

  bare-path@3.0.0:
    dependencies:
      bare-os: 3.6.1
    optional: true

  bare-stream@2.6.5(bare-events@2.5.4):
    dependencies:
      streamx: 2.22.1
    optionalDependencies:
      bare-events: 2.5.4
    optional: true

  base64-js@1.5.1: {}

  basic-ftp@5.0.5: {}

  bindings@1.2.1:
    optional: true

  bowser@2.11.0: {}

  brotli@1.3.3:
    dependencies:
      base64-js: 1.5.1

  bson@1.1.6: {}

  buffer-crc32@0.2.13: {}

  buffer-from@1.1.2: {}

  busboy@1.6.0:
    dependencies:
      streamsearch: 1.1.0

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  caniuse-lite@1.0.30001713: {}

  ccount@2.0.1: {}

  chalk@5.4.1: {}

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  character-entities@2.0.2: {}

  character-reference-invalid@2.0.1: {}

  chromium-bidi@5.1.0(devtools-protocol@0.0.1452169):
    dependencies:
      devtools-protocol: 0.0.1452169
      mitt: 3.0.1
      zod: 3.25.20

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  classnames@2.5.1: {}

  client-only@0.0.1: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    optional: true

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    optional: true

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  comma-separated-tokens@2.0.3: {}

  cookie@1.0.2: {}

  core-util-is@1.0.3: {}

  crypto-js@4.2.0: {}

  csstype@3.1.3: {}

  data-uri-to-buffer@6.0.2: {}

  date-fns@4.1.0: {}

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decode-named-character-reference@1.1.0:
    dependencies:
      character-entities: 2.0.2

  dedent@1.6.0: {}

  degenerator@5.0.1:
    dependencies:
      ast-types: 0.13.4
      escodegen: 2.1.0
      esprima: 4.0.1

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  detect-libc@2.0.3: {}

  detect-node-es@1.1.0: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  devtools-protocol@0.0.1452169: {}

  diff-match-patch@1.0.5: {}

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dotenv@16.5.0: {}

  drizzle-kit@0.31.1:
    dependencies:
      '@drizzle-team/brocli': 0.10.2
      '@esbuild-kit/esm-loader': 2.6.5
      esbuild: 0.25.4
      esbuild-register: 3.6.0(esbuild@0.25.4)
    transitivePeerDependencies:
      - supports-color

  drizzle-orm@0.43.1(@neondatabase/serverless@1.0.0)(@opentelemetry/api@1.9.0)(@types/pg@8.15.2):
    optionalDependencies:
      '@neondatabase/serverless': 1.0.0
      '@opentelemetry/api': 1.9.0
      '@types/pg': 8.15.2

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  emoji-regex@8.0.0: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild-register@3.6.0(esbuild@0.25.4):
    dependencies:
      debug: 4.4.0
      esbuild: 0.25.4
    transitivePeerDependencies:
      - supports-color

  esbuild@0.18.20:
    optionalDependencies:
      '@esbuild/android-arm': 0.18.20
      '@esbuild/android-arm64': 0.18.20
      '@esbuild/android-x64': 0.18.20
      '@esbuild/darwin-arm64': 0.18.20
      '@esbuild/darwin-x64': 0.18.20
      '@esbuild/freebsd-arm64': 0.18.20
      '@esbuild/freebsd-x64': 0.18.20
      '@esbuild/linux-arm': 0.18.20
      '@esbuild/linux-arm64': 0.18.20
      '@esbuild/linux-ia32': 0.18.20
      '@esbuild/linux-loong64': 0.18.20
      '@esbuild/linux-mips64el': 0.18.20
      '@esbuild/linux-ppc64': 0.18.20
      '@esbuild/linux-riscv64': 0.18.20
      '@esbuild/linux-s390x': 0.18.20
      '@esbuild/linux-x64': 0.18.20
      '@esbuild/netbsd-x64': 0.18.20
      '@esbuild/openbsd-x64': 0.18.20
      '@esbuild/sunos-x64': 0.18.20
      '@esbuild/win32-arm64': 0.18.20
      '@esbuild/win32-ia32': 0.18.20
      '@esbuild/win32-x64': 0.18.20

  esbuild@0.25.4:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.4
      '@esbuild/android-arm': 0.25.4
      '@esbuild/android-arm64': 0.25.4
      '@esbuild/android-x64': 0.25.4
      '@esbuild/darwin-arm64': 0.25.4
      '@esbuild/darwin-x64': 0.25.4
      '@esbuild/freebsd-arm64': 0.25.4
      '@esbuild/freebsd-x64': 0.25.4
      '@esbuild/linux-arm': 0.25.4
      '@esbuild/linux-arm64': 0.25.4
      '@esbuild/linux-ia32': 0.25.4
      '@esbuild/linux-loong64': 0.25.4
      '@esbuild/linux-mips64el': 0.25.4
      '@esbuild/linux-ppc64': 0.25.4
      '@esbuild/linux-riscv64': 0.25.4
      '@esbuild/linux-s390x': 0.25.4
      '@esbuild/linux-x64': 0.25.4
      '@esbuild/netbsd-arm64': 0.25.4
      '@esbuild/netbsd-x64': 0.25.4
      '@esbuild/openbsd-arm64': 0.25.4
      '@esbuild/openbsd-x64': 0.25.4
      '@esbuild/sunos-x64': 0.25.4
      '@esbuild/win32-arm64': 0.25.4
      '@esbuild/win32-ia32': 0.25.4
      '@esbuild/win32-x64': 0.25.4

  escalade@3.2.0: {}

  escape-string-regexp@5.0.0: {}

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  esprima@4.0.1: {}

  estraverse@5.3.0: {}

  estree-util-is-identifier-name@3.0.0: {}

  esutils@2.0.3: {}

  event-target-shim@5.0.1: {}

  extend@3.0.2: {}

  extract-zip@2.0.1:
    dependencies:
      debug: 4.4.1
      get-stream: 5.2.0
      yauzl: 2.10.0
    optionalDependencies:
      '@types/yauzl': 2.10.3
    transitivePeerDependencies:
      - supports-color

  fast-fifo@1.3.2: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.1.2

  fd-slicer@1.1.0:
    dependencies:
      pend: 1.2.0

  follow-redirects@1.15.9: {}

  form-data-encoder@1.7.2: {}

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  framer-motion@12.9.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      motion-dom: 12.9.1
      motion-utils: 12.8.3
      tslib: 2.8.1
    optionalDependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@5.2.0:
    dependencies:
      pump: 3.0.2

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  get-uri@6.0.4:
    dependencies:
      basic-ftp: 5.0.5
      data-uri-to-buffer: 6.0.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  glob-to-regexp@0.4.1: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-to-jsx-runtime@2.3.6:
    dependencies:
      '@types/estree': 1.0.7
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      hast-util-whitespace: 3.0.0
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.2.0
      mdast-util-mdxjs-esm: 2.0.1
      property-information: 7.1.0
      space-separated-tokens: 2.0.2
      style-to-js: 1.1.16
      unist-util-position: 5.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  hast-util-whitespace@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  html-url-attributes@3.0.1: {}

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  inherits@2.0.4: {}

  inline-style-parser@0.2.4: {}

  int53@0.2.4: {}

  ip-address@9.0.5:
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3

  is-alphabetical@2.0.1: {}

  is-alphanumerical@2.0.1:
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1

  is-arrayish@0.3.2:
    optional: true

  is-decimal@2.0.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-hexadecimal@2.0.1: {}

  is-plain-obj@4.1.0: {}

  isarray@1.0.0: {}

  jiti@2.4.2: {}

  jose@5.10.0: {}

  js-cookie@3.0.5: {}

  jsbn@1.1.0: {}

  json-schema@0.4.0: {}

  jsondiffpatch@0.6.0:
    dependencies:
      '@types/diff-match-patch': 1.0.36
      chalk: 5.4.1
      diff-match-patch: 1.0.5

  lightningcss-darwin-arm64@1.29.2:
    optional: true

  lightningcss-darwin-x64@1.29.2:
    optional: true

  lightningcss-freebsd-x64@1.29.2:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.29.2:
    optional: true

  lightningcss-linux-arm64-gnu@1.29.2:
    optional: true

  lightningcss-linux-arm64-musl@1.29.2:
    optional: true

  lightningcss-linux-x64-gnu@1.29.2:
    optional: true

  lightningcss-linux-x64-musl@1.29.2:
    optional: true

  lightningcss-win32-arm64-msvc@1.29.2:
    optional: true

  lightningcss-win32-x64-msvc@1.29.2:
    optional: true

  lightningcss@1.29.2:
    dependencies:
      detect-libc: 2.0.3
    optionalDependencies:
      lightningcss-darwin-arm64: 1.29.2
      lightningcss-darwin-x64: 1.29.2
      lightningcss-freebsd-x64: 1.29.2
      lightningcss-linux-arm-gnueabihf: 1.29.2
      lightningcss-linux-arm64-gnu: 1.29.2
      lightningcss-linux-arm64-musl: 1.29.2
      lightningcss-linux-x64-gnu: 1.29.2
      lightningcss-linux-x64-musl: 1.29.2
      lightningcss-win32-arm64-msvc: 1.29.2
      lightningcss-win32-x64-msvc: 1.29.2

  longest-streak@3.1.0: {}

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@7.18.3: {}

  lucide-react@0.503.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  lzo@0.4.11:
    dependencies:
      bindings: 1.2.1
    optional: true

  map-obj@4.3.0: {}

  markdown-table@3.0.4: {}

  math-intrinsics@1.1.0: {}

  mdast-util-find-and-replace@3.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      escape-string-regexp: 5.0.0
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  mdast-util-from-markdown@2.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.2
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-autolink-literal@2.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-find-and-replace: 3.0.2
      micromark-util-character: 2.1.1

  mdast-util-gfm-footnote@2.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      micromark-util-normalize-identifier: 2.0.1
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-strikethrough@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-table@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      markdown-table: 3.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-task-list-item@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm@3.1.0:
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-gfm-autolink-literal: 2.0.1
      mdast-util-gfm-footnote: 2.1.0
      mdast-util-gfm-strikethrough: 2.0.0
      mdast-util-gfm-table: 2.0.0
      mdast-util-gfm-task-list-item: 2.0.0
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-expression@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-jsx@3.2.0:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      parse-entities: 4.0.2
      stringify-entities: 4.0.4
      unist-util-stringify-position: 4.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdxjs-esm@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-phrasing@4.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-hast@13.2.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.3.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.1
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdast-util-to-markdown@2.1.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4

  micromark-core-commonmark@2.0.3:
    dependencies:
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-autolink-literal@2.1.0:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-footnote@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-strikethrough@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-table@2.1.1:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-tagfilter@2.0.0:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-extension-gfm-task-list-item@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm@3.0.0:
    dependencies:
      micromark-extension-gfm-autolink-literal: 2.1.0
      micromark-extension-gfm-footnote: 2.1.0
      micromark-extension-gfm-strikethrough: 2.1.0
      micromark-extension-gfm-table: 2.1.1
      micromark-extension-gfm-tagfilter: 2.0.0
      micromark-extension-gfm-task-list-item: 2.1.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-destination@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-label@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-space@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.2

  micromark-factory-title@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-whitespace@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-chunked@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-classify-character@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-combine-extensions@2.0.1:
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-decode-numeric-character-reference@2.0.2:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-decode-string@2.0.1:
    dependencies:
      decode-named-character-reference: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-html-tag-name@2.0.1: {}

  micromark-util-normalize-identifier@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-resolve-all@2.0.1:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-subtokenize@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.2: {}

  micromark@4.0.2:
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.4.0
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    transitivePeerDependencies:
      - supports-color

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mitt@3.0.1: {}

  motion-dom@12.9.1:
    dependencies:
      motion-utils: 12.8.3

  motion-utils@12.8.3: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  nanoid@5.1.5: {}

  netmask@2.0.2: {}

  neverthrow@7.2.0: {}

  next-plausible@3.12.4(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      next: 15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@next/env': 15.3.2
      '@swc/counter': 0.1.3
      '@swc/helpers': 0.5.15
      busboy: 1.6.0
      caniuse-lite: 1.0.30001713
      postcss: 8.4.31
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      styled-jsx: 5.1.6(react@19.1.0)
    optionalDependencies:
      '@next/swc-darwin-arm64': 15.3.2
      '@next/swc-darwin-x64': 15.3.2
      '@next/swc-linux-arm64-gnu': 15.3.2
      '@next/swc-linux-arm64-musl': 15.3.2
      '@next/swc-linux-x64-gnu': 15.3.2
      '@next/swc-linux-x64-musl': 15.3.2
      '@next/swc-win32-arm64-msvc': 15.3.2
      '@next/swc-win32-x64-msvc': 15.3.2
      '@opentelemetry/api': 1.9.0
      sharp: 0.34.1
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-domexception@1.0.0: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-int64@0.4.0: {}

  object-stream@0.0.1: {}

  obuf@1.1.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  pac-proxy-agent@7.2.0:
    dependencies:
      '@tootallnate/quickjs-emscripten': 0.23.0
      agent-base: 7.1.3
      debug: 4.4.1
      get-uri: 6.0.4
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      pac-resolver: 7.0.1
      socks-proxy-agent: 8.0.5
    transitivePeerDependencies:
      - supports-color

  pac-resolver@7.0.1:
    dependencies:
      degenerator: 5.0.1
      netmask: 2.0.2

  parquetjs@0.11.2:
    dependencies:
      brotli: 1.3.3
      bson: 1.1.6
      int53: 0.2.4
      object-stream: 0.0.1
      snappyjs: 0.6.1
      thrift: 0.11.0
      varint: 5.0.2
    optionalDependencies:
      lzo: 0.4.11
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  parse-entities@4.0.2:
    dependencies:
      '@types/unist': 2.0.11
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.1.0
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1

  pend@1.2.0: {}

  pg-int8@1.0.1: {}

  pg-numeric@1.0.2: {}

  pg-protocol@1.10.0: {}

  pg-types@4.0.2:
    dependencies:
      pg-int8: 1.0.1
      pg-numeric: 1.0.2
      postgres-array: 3.0.4
      postgres-bytea: 3.0.0
      postgres-date: 2.1.0
      postgres-interval: 3.0.0
      postgres-range: 1.1.4

  picocolors@1.1.1: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postgres-array@3.0.4: {}

  postgres-bytea@3.0.0:
    dependencies:
      obuf: 1.1.2

  postgres-date@2.1.0: {}

  postgres-interval@3.0.0: {}

  postgres-range@1.1.4: {}

  process-nextick-args@2.0.1: {}

  progress-stream@2.0.0:
    dependencies:
      speedometer: 1.0.0
      through2: 2.0.5

  progress@2.0.3: {}

  property-information@7.1.0: {}

  proxy-agent@6.5.0:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      lru-cache: 7.18.3
      pac-proxy-agent: 7.2.0
      proxy-from-env: 1.1.0
      socks-proxy-agent: 8.0.5
    transitivePeerDependencies:
      - supports-color

  proxy-from-env@1.1.0: {}

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  puppeteer-core@24.10.0:
    dependencies:
      '@puppeteer/browsers': 2.10.5
      chromium-bidi: 5.1.0(devtools-protocol@0.0.1452169)
      debug: 4.4.1
      devtools-protocol: 0.0.1452169
      typed-query-selector: 2.12.0
      ws: 8.18.2
    transitivePeerDependencies:
      - bare-buffer
      - bufferutil
      - supports-color
      - utf-8-validate

  q@1.5.1: {}

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-icons@5.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-markdown@10.1.0(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/react': 19.1.2
      devlop: 1.1.0
      hast-util-to-jsx-runtime: 2.3.6
      html-url-attributes: 3.0.1
      mdast-util-to-hast: 13.2.0
      react: 19.1.0
      remark-parse: 11.0.0
      remark-rehype: 11.1.2
      unified: 11.0.5
      unist-util-visit: 5.0.0
      vfile: 6.0.3
    transitivePeerDependencies:
      - supports-color

  react-remove-scroll-bar@2.3.8(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-style-singleton: 2.2.3(@types/react@19.1.2)(react@19.1.0)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.2

  react-remove-scroll@2.7.1(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.2)(react@19.1.0)
      react-style-singleton: 2.2.3(@types/react@19.1.2)(react@19.1.0)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.2)(react@19.1.0)
      use-sidecar: 1.1.3(@types/react@19.1.2)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.2

  react-style-singleton@2.2.3(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      get-nonce: 1.0.1
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.2

  react@19.1.0: {}

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  remark-gfm@4.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-gfm: 3.1.0
      micromark-extension-gfm: 3.0.0
      remark-parse: 11.0.0
      remark-stringify: 11.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-parse@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      micromark-util-types: 2.0.2
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-rehype@11.1.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      mdast-util-to-hast: 13.2.0
      unified: 11.0.5
      vfile: 6.0.3

  remark-stringify@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-to-markdown: 2.1.2
      unified: 11.0.5

  require-directory@2.1.1: {}

  resolve-pkg-maps@1.0.0: {}

  safe-buffer@5.1.2: {}

  scheduler@0.26.0: {}

  secure-json-parse@2.7.0: {}

  semver@7.7.1:
    optional: true

  semver@7.7.2: {}

  server-only@0.0.1: {}

  sharp@0.34.1:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.3
      semver: 7.7.1
    optionalDependencies:
      '@img/sharp-darwin-arm64': 0.34.1
      '@img/sharp-darwin-x64': 0.34.1
      '@img/sharp-libvips-darwin-arm64': 1.1.0
      '@img/sharp-libvips-darwin-x64': 1.1.0
      '@img/sharp-libvips-linux-arm': 1.1.0
      '@img/sharp-libvips-linux-arm64': 1.1.0
      '@img/sharp-libvips-linux-ppc64': 1.1.0
      '@img/sharp-libvips-linux-s390x': 1.1.0
      '@img/sharp-libvips-linux-x64': 1.1.0
      '@img/sharp-libvips-linuxmusl-arm64': 1.1.0
      '@img/sharp-libvips-linuxmusl-x64': 1.1.0
      '@img/sharp-linux-arm': 0.34.1
      '@img/sharp-linux-arm64': 0.34.1
      '@img/sharp-linux-s390x': 0.34.1
      '@img/sharp-linux-x64': 0.34.1
      '@img/sharp-linuxmusl-arm64': 0.34.1
      '@img/sharp-linuxmusl-x64': 0.34.1
      '@img/sharp-wasm32': 0.34.1
      '@img/sharp-win32-ia32': 0.34.1
      '@img/sharp-win32-x64': 0.34.1
    optional: true

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2
    optional: true

  smart-buffer@4.2.0: {}

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  snakecase-keys@8.0.1:
    dependencies:
      map-obj: 4.3.0
      snake-case: 3.0.4
      type-fest: 4.41.0

  snappyjs@0.6.1: {}

  socks-proxy-agent@8.0.5:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
      socks: 2.8.5
    transitivePeerDependencies:
      - supports-color

  socks@2.8.5:
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0

  sonner@2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  space-separated-tokens@2.0.2: {}

  speedometer@1.0.0: {}

  sprintf-js@1.1.3: {}

  std-env@3.9.0: {}

  streamsearch@1.1.0: {}

  streamx@2.22.1:
    dependencies:
      fast-fifo: 1.3.2
      text-decoder: 1.2.3
    optionalDependencies:
      bare-events: 2.5.4

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strnum@1.1.2: {}

  style-to-js@1.1.16:
    dependencies:
      style-to-object: 1.0.8

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  styled-jsx@5.1.6(react@19.1.0):
    dependencies:
      client-only: 0.0.1
      react: 19.1.0

  swr@2.3.3(react@19.1.0):
    dependencies:
      dequal: 2.0.3
      react: 19.1.0
      use-sync-external-store: 1.5.0(react@19.1.0)

  tailwind-merge@3.3.0: {}

  tailwindcss@4.1.3: {}

  tailwindcss@4.1.4: {}

  tapable@2.2.1: {}

  tar-fs@3.0.9:
    dependencies:
      pump: 3.0.2
      tar-stream: 3.1.7
    optionalDependencies:
      bare-fs: 4.1.5
      bare-path: 3.0.0
    transitivePeerDependencies:
      - bare-buffer

  tar-stream@3.1.7:
    dependencies:
      b4a: 1.6.7
      fast-fifo: 1.3.2
      streamx: 2.22.1

  text-decoder@1.2.3:
    dependencies:
      b4a: 1.6.7

  thrift@0.11.0:
    dependencies:
      node-int64: 0.4.0
      q: 1.5.1
      ws: 8.18.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  throttleit@2.1.0: {}

  through2@2.0.5:
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2

  together-ai@0.16.0:
    dependencies:
      '@types/node': 18.19.104
      '@types/node-fetch': 2.6.12
      '@types/parquetjs': 0.10.6
      '@types/progress-stream': 2.0.5
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      axios: 1.9.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
      parquetjs: 0.11.2
      progress-stream: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - encoding
      - utf-8-validate

  tr46@0.0.3: {}

  trim-lines@3.0.1: {}

  trough@2.2.0: {}

  tslib@2.8.1: {}

  tsx@4.19.4:
    dependencies:
      esbuild: 0.25.4
      get-tsconfig: 4.10.1
    optionalDependencies:
      fsevents: 2.3.3

  tw-animate-css@1.3.1: {}

  type-fest@4.41.0: {}

  typed-query-selector@2.12.0: {}

  typescript-event-target@1.1.1: {}

  typescript@5.8.3: {}

  undici-types@5.26.5: {}

  undici-types@6.19.8: {}

  undici-types@6.21.0: {}

  unified@11.0.5:
    dependencies:
      '@types/unist': 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  use-callback-ref@1.3.3(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.2

  use-sidecar@1.1.3(@types/react@19.1.2)(react@19.1.0):
    dependencies:
      detect-node-es: 1.1.0
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.2

  use-sync-external-store@1.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  util-deprecate@1.0.2: {}

  uuid@9.0.1: {}

  varint@5.0.2: {}

  vfile-message@4.0.2:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.2

  web-streams-polyfill@4.0.0-beta.3: {}

  webidl-conversions@3.0.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  ws@8.18.2: {}

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yauzl@2.10.0:
    dependencies:
      buffer-crc32: 0.2.13
      fd-slicer: 1.1.0

  zod-to-json-schema@3.24.5(zod@3.25.20):
    dependencies:
      zod: 3.25.20

  zod@3.25.20: {}

  zwitch@2.0.4: {}



================================================
FILE: postcss.config.mjs
================================================
const config = {
  plugins: ["@tailwindcss/postcss"],
};

export default config;



================================================
FILE: tsconfig.json
================================================
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}



================================================
FILE: .example.env
================================================
# API Keys
TOGETHER_API_KEY=your_together_api_key
FIRECRAWL_API_KEY=
HELICONE_API_KEY=
BRAVE_API_KEY=

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=your_upstash_redis_rest_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_rest_token

# Postgres Database to store messages and deepresearches
DATABASE_URL=postgresql://url

# Qstash Workflow Configuration
QSTASH_URL="http://127.0.0.1:8080"
QSTASH_TOKEN=<QSTASH_TOKEN>

# S3 AWS Credentials to upload the research cover image
S3_UPLOAD_KEY=
S3_UPLOAD_SECRET=
S3_UPLOAD_BUCKET=
S3_UPLOAD_REGION=

# CLERK for Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=


================================================
FILE: .prettierrc
================================================
{
  "singleQuote": false
}



================================================
FILE: src/middleware.ts
================================================
import { clerkMiddleware } from "@clerk/nextjs/server";

export default clerkMiddleware();

export const config = {
  matcher: [
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    "/(api|trpc)(.*)",
  ],
};



================================================
FILE: src/app/globals.css
================================================
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-figtree: var(--font-figtree);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-figtree;
  }
}

.markdown-content table {
  @apply w-full text-sm text-left border-collapse my-4 rounded-lg overflow-hidden;
}
.markdown-content thead {
  @apply bg-muted;
}
.markdown-content th {
  @apply px-4 py-2 font-semibold text-foreground border-b border-border;
}
.markdown-content td {
  @apply px-4 py-2 border-b border-border;
}
.markdown-content tr:last-child td {
  @apply border-b-0;
}
.markdown-content tr {
  @apply hover:bg-accent transition-colors;
}

/* Custom Scrollbar Styling */
/* For Webkit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 8px; /* width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent; /* transparent scrollbar track */
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* semi-transparent thumb */
  border-radius: 10px; /* rounded corners */
  border: 2px solid transparent; /* creates padding around the thumb */
  background-clip: content-box; /* ensures border doesn't overlap the thumb */
}

/* On hover */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3); /* slightly darker on hover */
}



================================================
FILE: src/app/LandingHomepage.tsx
================================================
"use client";
import { ChatInput } from "@/components/ChatInput";
import { Footer } from "@/components/Footer";
import { createResearchAndRedirect } from "@/db/action";
import { SignedOut, SignInButton, useUser } from "@clerk/nextjs";
import { useState } from "react";
import LoadingChat from "./chat/[chatId]/loading";
import { LandingHero } from "@/components/LandingHero";
import { LandingCard } from "@/components/LandingCard";

export const LandingHomepage = () => {
  const { isSignedIn, user, isLoaded } = useUser();
  const [isLoading, setIsLoading] = useState(false);

  if (isLoading) return <LoadingChat />;

  return (
    <div className="flex flex-col size-full items-center min-h-screen justify-center relative md:bg-[url('/bg.svg')] md:bg-no-repeat md:bg-center md:bg-[size:auto_100%]">
      <div className="px-4 pb-4 pt-8 flex flex-col items-center w-full max-w-3xl justify-center gap-4">
        <LandingHero />

        {!isLoaded ? (
          <div className="h-[66px] w-full" />
        ) : !isSignedIn ? (
          <SignedOut>
            <div className="flex flex-col gap-2">
              <SignInButton>
                <div className="flex justify-center items-center relative gap-1.5 px-5 py-2 rounded bg-[#072d77] border-[0.5px] border-[#072d77] cursor-pointer">
                  <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-white">
                    Generate a report
                  </p>
                </div>
              </SignInButton>
              <p className="text-xs text-center text-[#99a1af] pb-4">
                Fully{" "}
                <a
                  href="https://github.com/Nutlope/open-deep-research"
                  className="underline font-semibold hover:text-[#072d77] transition"
                >
                  open source
                </a>
              </p>
            </div>
            <div className="max-w-[760px] min-h-[258px] w-fit md:w-full relative overflow-hidden rounded-xl bg-[#f2f6ff] border border-gray-200 px-7 py-5 flex flex-col gap-4 mb-12">
              <p className="text-base text-center md:text-left text-[#364153] font-serif">
                How it works:
              </p>
              <div className="flex flex-col md:flex-row gap-3 items-center">
                <LandingCard
                  imageSrc="/cards/question.jpg"
                  title="Ask your question"
                  description="Type any topic or problem into the prompt box."
                />
                <LandingCard
                  imageSrc="/cards/search.jpg"
                  title="We Research & Refine"
                  description="Deep Research scans vetted sources and extracts the signal."
                />
                <LandingCard
                  imageSrc="/cards/report.jpg"
                  title="Get an Actionable Brief"
                  description="Receive a concise, referenced report you can save or share."
                />
              </div>
            </div>
          </SignedOut>
        ) : (
          <ChatInput
            disabled={!isLoaded || isLoading}
            append={(message) => {
              setIsLoading(true);
              createResearchAndRedirect({
                clerkUserId: isSignedIn ? user.id : undefined,
                initialUserMessage: message.content,
              });
            }}
          />
        )}

        <Footer />
      </div>
    </div>
  );
};



================================================
FILE: src/app/layout.tsx
================================================
import type { Metadata } from "next";
import { Figtree } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { Toaster } from "sonner";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app/AppSidebar";
import { Header } from "@/components/Header";
import PlausibleProvider from "next-plausible";
import { cn } from "@/lib/utils";
import { TogetherApiKeyProvider } from "@/components/app/ApiKeyControls";

const figtree = Figtree({
  variable: "--font-figtree",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Open Deep Research",
  description: "Let AI do research for you",
  openGraph: {
    images: ["https://www.opendeepresearch.dev/og.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <SidebarProvider>
        <TogetherApiKeyProvider>
          <AppSidebar />
          <html lang="en" className="h-full">
            <head>
              <PlausibleProvider domain="opendeepresearch.dev" />
            </head>
            <body
              className={cn(
                `${figtree.variable} flex min-h-full flex-col antialiased mt-[120px] md:mt-0`
              )}
            >
              <Header />
              <Toaster position="top-center" richColors />
              {children}
            </body>
          </html>
        </TogetherApiKeyProvider>
      </SidebarProvider>
    </ClerkProvider>
  );
}



================================================
FILE: src/app/page.tsx
================================================
import { LandingHomepage } from "./LandingHomepage";

export default function Home() {
  return <LandingHomepage />;
}



================================================
FILE: src/app/api/cancel/route.ts
================================================
import { auth } from "@clerk/nextjs/server";
import { db } from "@/db";
import { research } from "@/db/schema";
import { workflow } from "@/lib/clients";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import { cleanupSession } from "@/deepresearch/storage";

export async function POST(request: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { chatId } = await request.json();
  console.log("cancel workflow with run ID: ", chatId);

  const [researchEntry] = await db
    .select()
    .from(research)
    .where(eq(research.id, chatId))
    .limit(1);

  if (!researchEntry || researchEntry.clerkUserId !== userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  // delete this from the db
  await db.delete(research).where(eq(research.id, chatId));

  // also delete from redis
  await cleanupSession(chatId);

  try {
    await workflow.cancel({
      ids: chatId,
    });
  } catch (e) {
    console.error("failed to cancel workflow run", e);
  }
  return new Response("canceled workflow run!");
}



================================================
FILE: src/app/api/chats/route.ts
================================================
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { getChats } from "@/lib/getChats";

export async function GET() {
  const { userId } = await auth();

  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const chats = await getChats(userId);

  return NextResponse.json(chats);
}



================================================
FILE: src/app/api/pdf/route.ts
================================================
import { NextRequest, NextResponse } from "next/server";
import chrome from "@sparticuz/chromium";
import puppeteer from "puppeteer-core";
import { slugifyFilename } from "@/lib/utils";

export async function POST(req: NextRequest) {
  const body = await req.json();

  if (!body)
    return NextResponse.json({ error: "No body provided" }, { status: 400 });

  if (typeof body === "object" && !body.url)
    return NextResponse.json({ error: "No url provided" }, { status: 400 });

  const isProd = process.env.NODE_ENV === "production";

  let browser;

  if (isProd) {
    browser = await puppeteer.launch({
      args: chrome.args,
      defaultViewport: chrome.defaultViewport,
      executablePath: await chrome.executablePath(),
      headless: true, // Changed from "new" to true
    });
  } else {
    browser = await puppeteer.launch({
      headless: true, // Changed from "new" to true
      executablePath:
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    });
  }

  const page = await browser.newPage();

  await page.setViewport({ width: 600, height: 600 });

  // const url = getAbsoluteURL(`?hash=${hash}`, path)
  const url = body.url;

  console.log("url", url);

  await page.goto(url, { waitUntil: "networkidle0" }); // Wait until the network is idle

  const pdf = await page.pdf({
    format: "A4",
    printBackground: true,
  });

  await browser.close();

  const headers = new Headers();
  headers.set("Content-Type", "application/pdf");
  const fileName = slugifyFilename(body.fileName || "report");

  console.log("fileName", fileName);

  headers.set(
    "Content-Disposition",
    `attachment; filename=\"${fileName}.pdf\"; filename*=UTF-8''${fileName}.pdf`
  );
  headers.set("Access-Control-Allow-Credentials", "true");
  headers.set("Access-Control-Allow-Origin", "*");
  headers.set(
    "Access-Control-Allow-Methods",
    "GET,OPTIONS,PATCH,DELETE,POST,PUT"
  );
  headers.set(
    "Access-Control-Allow-Headers",
    "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"
  );

  // Return the PDF as a Blob to avoid ByteString errors and ensure correct filename
  return new NextResponse(new Blob([pdf]), { status: 200, headers });
}

export async function GET(req: NextRequest) {
  return new NextResponse("Method Not Allowed", { status: 405 });
}
export async function OPTIONS(req: NextRequest) {
  const headers = new Headers();
  headers.set("Access-Control-Allow-Credentials", "true");
  headers.set("Access-Control-Allow-Origin", "*");
  headers.set(
    "Access-Control-Allow-Methods",
    "GET,OPTIONS,PATCH,DELETE,POST,PUT"
  );
  headers.set(
    "Access-Control-Allow-Headers",
    "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"
  );
  return new NextResponse(null, { status: 200, headers });
}



================================================
FILE: src/app/api/research/route.ts
================================================
import { streamStorage } from "@/deepresearch/storage";
import { StreamEvent } from "@/deepresearch/schemas";
import { getResearch } from "@/db/action";

// Types for streaming data

interface ResearchStatusRow {
  type: "research_status";
  status: "pending" | "completed" | "processing" | "questions";
  timestamp: number;
  iteration: number;
}

export type ResearchEventStreamEvents = ResearchStatusRow | StreamEvent;

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const chatId = searchParams.get("chatId");

  if (!chatId) {
    return new Response("Missing chatId", { status: 400 });
  }

  try {
    // Get research data from database and events from Redis
    const research = await getResearch(chatId);
    const events = await streamStorage.getEvents(chatId);

    const steps: ResearchEventStreamEvents[] = [
      {
        type: "research_status",
        status: research?.status || "pending",
        timestamp:
          research?.researchStartedAt?.getTime() || new Date().getTime(),
        iteration: -1,
      },
      ...events,
    ];

    return new Response(JSON.stringify(steps), {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("Error fetching research data:", error);
    return new Response("Internal server error", { status: 500 });
  }
}



================================================
FILE: src/app/api/storeAnswers/route.ts
================================================
import { NextResponse } from "next/server";
import { skipQuestions, storeAnswers } from "@/db/action"; // Import the functions

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { chatId, answers, togetherApiKey } = body;

    if (!Array.isArray(answers)) {
      return NextResponse.json(
        { success: false, message: "Invalid answers format." },
        { status: 400 }
      );
    }

    if (answers.length === 0) {
      // Logic for skipping questions
      console.log("Skipping questions for chatId:", chatId);
      await skipQuestions({ chatId, togetherApiKey }); // Use skipQuestions
      return NextResponse.json({
        success: true,
        message: "Questions skipped successfully",
      });
    } else {
      // Logic for storing answers
      console.log("Storing answers for chatId:", {
        chatId,
        answers,
        togetherApiKey,
      });
      await storeAnswers({ chatId, answers, togetherApiKey }); // Use storeAnswers
      return NextResponse.json({
        success: true,
        message: "Answers stored successfully",
      });
    }
  } catch (error) {
    console.error("Error processing request:", error);
    return NextResponse.json(
      { success: false, message: "Failed to process request" },
      { status: 500 }
    );
  }
}



================================================
FILE: src/app/api/user/limits/route.ts
================================================
import { auth } from "@clerk/nextjs/server";
import { getRemainingResearch } from "../../../../lib/limits";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  const { userId } = await auth();

  // get the API key from the request body
  const { isBringingKey } = await request.json();

  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { remaining, reset } = await getRemainingResearch({
    clerkUserId: userId,
    isBringingKey: !!isBringingKey,
  });

  return NextResponse.json({
    remaining,
    reset: reset ? new Date(reset).toISOString() : null,
  });
}



================================================
FILE: src/app/api/validate-key/route.ts
================================================
import { togetheraiClientWithKey } from "@/deepresearch/apiClients";
import { generateText } from "ai";

export async function POST(request: Request) {
  const { apiKey } = await request.json();

  if (!apiKey) {
    return new Response(JSON.stringify({ message: "API key is required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    const customClient = togetheraiClientWithKey(apiKey);
    // Make a simple LLM call to validate the API key
    await generateText({
      model: customClient("Qwen/Qwen2.5-72B-Instruct-Turbo"),
      maxTokens: 100,
      messages: [
        {
          role: "user",
          content: "hello",
        },
      ],
    });

    return new Response(JSON.stringify({ message: "API key is valid" }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    console.error("API key validation failed:", error);
    return new Response(
      JSON.stringify({ message: "API key is invalid", error: error.message }),
      {
        status: 401,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}



================================================
FILE: src/app/api/workflows/[...any]/route.ts
================================================
/**
 * Workflow API Routes - Serves the refactored research workflows
 */

import { serveMany } from "@upstash/workflow/nextjs";
import { startResearchWorkflow } from "../../../../deepresearch/workflows/start-research-workflow";
import { gatherSearchQueriesWorkflow } from "../../../../deepresearch/workflows/gather-search-workflow";

// Export the workflow endpoints
export const { POST } = serveMany({
  "start-research": startResearchWorkflow,
  "gather-search-queries": gatherSearchQueriesWorkflow,
});



================================================
FILE: src/app/chat/[chatId]/loading.tsx
================================================
import React from "react";

const LoadingChat = () => {
  return (
    <div className="w-full flex items-center justify-center">
      <img
        src="/reportStep/loading.svg"
        alt="Loading Spinner"
        width={48}
        height={48}
        className="animate-spin"
      />
    </div>
  );
};

export default LoadingChat;



================================================
FILE: src/app/chat/[chatId]/page.tsx
================================================
import { db } from "@/db";
import { eq } from "drizzle-orm";
import { research } from "@/db/schema";
import { Metadata } from "next";
import { getResearch } from "@/db/action";
import { redirect } from "next/navigation";
import { generateObject, generateText } from "ai";
import { MODEL_CONFIG, PROMPTS } from "@/deepresearch/config";
import dedent from "dedent";
import { togetheraiClient } from "@/deepresearch/apiClients";
import z from "zod";
import { ChatPage } from "@/components/app/ChatPage";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ chatId: string }>;
}): Promise<Metadata> {
  const { chatId } = await params;

  if (!chatId) {
    return {
      title: "Chat Not Found | Open Deep Research",
      description: "This chat could not be found on Open Deep Research",
    };
  }

  const researchData = chatId ? await getResearch(chatId) : undefined;

  if (!researchData) {
    return redirect("/");
  }

  const topic = researchData.title || researchData.initialUserMessage;

  const title = `${topic} | Open Deep Research`;
  const description = `Discover the research on "${topic}" generated using ${
    researchData.sources && researchData.sources?.length > 0
      ? researchData.sources.length
      : "multiple"
  } sources on Open Deep Research`;

  return {
    title: title,
    description: description,
    openGraph: {
      title: title,
      description: description,
      images: researchData.coverUrl ? [researchData.coverUrl] : [],
    },
  };
}

export default async function Page(props: {
  params: Promise<{ chatId: string }>;
}) {
  const { chatId } = await props.params; // get the chat ID from the URL
  const researchData = await getResearch(chatId); // load the chat

  // if we get chat without questions, generate questions with AI LLM and save to DB
  if (!researchData || !researchData.initialUserMessage) {
    return redirect("/");
  }

  if (!researchData.questions) {
    const questionsText = await generateText({
      system: dedent(PROMPTS.clarificationPrompt),
      messages: [
        {
          role: "user",
          content: researchData.initialUserMessage,
        },
      ],
      model: togetheraiClient(MODEL_CONFIG.planningModel),
    });

    const result = await generateObject({
      system: dedent(PROMPTS.clarificationParsingPrompt),
      model: togetheraiClient(MODEL_CONFIG.jsonModel),
      messages: [
        {
          role: "user",
          content: questionsText.text,
        },
      ],
      schema: z.object({
        questions: z.array(z.string()),
      }),
      maxRetries: 3,
    });

    await db
      .update(research)
      .set({
        questions: result.object.questions,
      })
      .where(eq(research.id, researchData.id));

    researchData.questions = result.object.questions;
  }

  return <ChatPage chatId={chatId} researchData={researchData} />;
}



================================================
FILE: src/components/ChatInput.tsx
================================================
"use client";
import { useState, useRef, useEffect } from "react";
import cn from "classnames";
import { ArrowUpIcon } from "./icons";

export const ChatInput = ({
  append,
  disabled,
}: {
  disabled?: boolean;
  append: (message: {
    role: "user" | "assistant";
    content: string;
    createdAt: Date;
  }) => void;
}) => {
  const [input, setInput] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("chatInput") || "";
    }
    return "";
  });
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (textareaRef.current) {
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 400);
    }
  }, []);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
    if (typeof window !== "undefined") {
      localStorage.setItem("chatInput", input);
    }
  }, [input]);

  return (
    <div
      className="p-3 relative overflow-hidden rounded-lg max-w-[640px] mx-auto w-full flex"
      style={{
        border: "1px solid transparent",
        borderRadius: "8px",
        background: `
          linear-gradient(white, white) padding-box,
          radial-gradient(circle at center, #072D77, #D1D5DC) border-box
        `,
        backgroundOrigin: "border-box",
        backgroundRepeat: "no-repeat",
        boxShadow: "0px 1px 13px -6px rgba(0,0,0,0.2)",
      }}
    >
      <textarea
        ref={textareaRef}
        className="mb-12 resize-none w-full min-h-12 outline-none bg-transparent placeholder:text-zinc-400 max-h-[240px] overflow-y-auto"
        placeholder="Type your message (Enter to send, Shift+Enter for new line)"
        value={input}
        disabled={disabled}
        onChange={(event) => {
          setInput(event.currentTarget.value);
        }}
        onKeyDown={(event) => {
          if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();

            if (input === "") {
              return;
            }

            append({
              role: "user",
              content: input.trimEnd(),
              createdAt: new Date(),
            });

            setInput("");
            if (typeof window !== "undefined") {
              localStorage.removeItem("chatInput");
            }
          }
        }}
      />

      <div className="absolute bottom-2.5 right-2.5 flex flex-row gap-2">
        <button
          className={cn(
            "size-[26px] flex flex-row justify-center items-center bg-[#093999] text-white rounded"
          )}
          onClick={() => {
            if (input === "") {
              return;
            }

            append({
              role: "user",
              content: input.trimEnd(),
              createdAt: new Date(),
            });
            setInput("");
            if (typeof window !== "undefined") {
              localStorage.removeItem("chatInput");
            }
          }}
        >
          <ArrowUpIcon size={12} />
        </button>
      </div>
    </div>
  );
};



================================================
FILE: src/components/CustomMarkdown.tsx
================================================
import React from "react";
import remarkGfm from "remark-gfm";

import { Components } from "react-markdown";
import ReactMarkdown from "react-markdown";
import Link from "next/link";
import { FaviconImage } from "./FaviconImage";
import { CitationTooltip } from "./app/citations/CitationTooltip";

interface CustomMarkdownProps {
  children: string;
  sources?: Array<{ url: string; title: string }>;
}

const createMarkdownComponents = (
  sources?: Array<{ url: string; title: string }>
): Partial<Components> => ({
  p: ({ children }) => (
    <p className="text-base font-light text-left text-[#0f172b] leading-6 pb-4">
      {children}
    </p>
  ),
  hr: ({}) => <hr className="pb-4" />,
  pre: ({ children }) => <>{children}</>,
  img: ({ children, ...props }) => {
    return <img className="max-w-full rounded-lg" {...props} />;
  },
  ol: ({ children, ...props }) => {
    return (
      <ol className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ol>
    );
  },
  li: ({ children, ...props }) => {
    return (
      <li className="py-1" {...props}>
        {children}
      </li>
    );
  },
  ul: ({ children, ...props }) => {
    return (
      <ul className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ul>
    );
  },
  strong: ({ children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    );
  },
  a: ({ children, ...props }) => {
    if (children?.toString() === "INLINE_CITATION" && sources) {
      const normalizedHref = props.href?.replace(/\/+$/, "");
      const sourceIndex = sources.findIndex(
        (source) => source.url.replace(/\/+$/, "") === normalizedHref
      );
      if (sourceIndex !== -1) {
        return (
          <CitationTooltip index={sourceIndex} source={sources[sourceIndex]} />
        );
      }
      return (
        <a
          href={props.href}
          className="text-blue-500 hover:underline"
          {...props}
        >
          <FaviconImage url={props.href || ""} />
        </a>
      );
    }

    return (
      // @ts-expect-error - Link component expects href prop from markdown-parsed anchor tags
      <Link
        className="text-blue-500 hover:underline"
        target="_blank"
        rel="noreferrer"
        {...props}
      >
        {children}
      </Link>
    );
  },
  h1: ({ children, ...props }) => {
    const text =
      typeof children === "string"
        ? children
        : React.Children.toArray(children).join("");
    const anchor = text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    return (
      <h1
        id={anchor}
        className="text-[28px] md:text-[40px] font-medium text-left text-[#0f172b] mb-2 leading-[48px]"
        {...props}
      >
        {children}
      </h1>
    );
  },
  h2: ({ children, ...props }) => {
    const text =
      typeof children === "string"
        ? children
        : React.Children.toArray(children).join("");
    const anchor = text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    return (
      <h2
        id={anchor}
        className="text-2xl md:text-[28px] text-left font-medium text-[#0f172b] mb-2 "
        {...props}
      >
        {children}
      </h2>
    );
  },
  h3: ({ children, ...props }) => {
    const text =
      typeof children === "string"
        ? children
        : React.Children.toArray(children).join("");
    const anchor = text
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    return (
      <h3
        id={anchor}
        className="text-[18px] md:text-xl text-left text-[#0f172b] mb-2"
        {...props}
      >
        {children}
      </h3>
    );
  },
  h4: ({ children, ...props }) => {
    return (
      <h4 className="text-lg text-left text-[#0f172b] mb-2" {...props}>
        {children}
      </h4>
    );
  },
  h5: ({ children, ...props }) => {
    return (
      <h5 className="text-base text-left text-[#0f172b] mb-2" {...props}>
        {children}
      </h5>
    );
  },
  h6: ({ children, ...props }) => {
    return (
      <h6 className="text-sm text-left text-[#0f172b] mb-2" {...props}>
        {children}
      </h6>
    );
  },
  table: ({ children, ...props }) => {
    return (
      <div className="w-full overflow-auto">
        <table
          className="w-full text-sm text-left border-collapse my-4 rounded-lg overflow-hidden"
          {...props}
        >
          {children}
        </table>
      </div>
    );
  },
  thead: ({ children, ...props }) => {
    return (
      <thead className="bg-muted" {...props}>
        {children}
      </thead>
    );
  },
  th: ({ children, ...props }) => {
    return (
      <th
        className="px-4 py-2 font-semibold text-foreground border-b border-border"
        {...props}
      >
        {children}
      </th>
    );
  },
  td: ({ children, ...props }) => {
    return (
      <td className="px-4 py-2 border-b border-border" {...props}>
        {children}
      </td>
    );
  },
  tr: ({ children, ...props }) => {
    return (
      <tr className="hover:bg-accent transition-colors" {...props}>
        {children}
      </tr>
    );
  },
});

export const CustomMarkdown: React.FC<CustomMarkdownProps> = ({
  children,
  sources,
}) => {
  const components = createMarkdownComponents(sources);

  // TODO: Consider sanitizing HTML output for security if user input is rendered
  return (
    <ReactMarkdown components={components} remarkPlugins={[remarkGfm]}>
      {children}
    </ReactMarkdown>
  );
};



================================================
FILE: src/components/deploy-button.tsx
================================================
import Link from "next/link";
export const DeployButton = () => (
  <Link
    href={`https://vercel.com`}
    target="_blank"
    rel="noopener noreferrer"
    className="inline-flex items-center gap-2 ml-2 bg-black text-white text-sm px-3 py-1.5 rounded-md hover:bg-zinc-900 dark:bg-white dark:text-black dark:hover:bg-zinc-100"
  >
    <svg
      data-testid="geist-icon"
      height={14}
      strokeLinejoin="round"
      viewBox="0 0 16 16"
      width={14}
      style={{ color: "currentcolor" }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 1L16 15H0L8 1Z"
        fill="currentColor"
      />
    </svg>
    Deploy
  </Link>
);



================================================
FILE: src/components/FaviconImage.tsx
================================================
import { getDomainFromUrl } from "@/lib/utils";

export const FaviconImage: React.FC<{
  url: string;
  className?: string;
  children?: React.ReactNode;
}> = ({ url, className = "", children }) => (
  <img
    src={`https://www.google.com/s2/favicons?domain=${getDomainFromUrl(url)}`}
    alt="favicon"
    className="size-3.5 rounded-full mr-1"
    style={{ display: "inline-block", verticalAlign: "middle" }}
  />
);



================================================
FILE: src/components/Footer.tsx
================================================
import { FaGithub, FaTwitter } from "react-icons/fa";

export const Footer = () => {
  return (
    <div className="w-full flex justify-center items-center text-xs text-zinc-400 leading-5 px-4 py-2 bottom-4 fixed left-0 z-50">
      <div className="flex flex-row items-center gap-0">
        <span className="px-3">
          Powered By{" "}
          <a
            target="_blank"
            rel="noreferrer"
            href="https://www.together.ai/models/deepseek-v3"
            className="font-semibold hover:text-[#072d77] transition"
          >
            DeepSeek V3
          </a>{" "}
          on{" "}
          <a
            target="_blank"
            rel="noreferrer"
            href="https://togetherai.link"
            className="font-semibold hover:text-[#072d77] transition"
          >
            Together AI
          </a>
        </span>
        <span className="h-5 w-px bg-zinc-300 mx-2" />
        <a
          href="https://www.together.ai/blog/open-deep-research"
          target="_blank"
          rel="noreferrer"
          className="hover:text-zinc-600 transition-colors text-xs font-semibold px-3"
        >
          Blog on how it works
        </a>
        <span className="h-5 w-px bg-zinc-300 mx-2" />
        <div className="flex gap-2 items-center px-3">
          <a
            href="https://github.com/Nutlope/open-deep-research"
            target="_blank"
            rel="noreferrer"
            aria-label="GitHub"
            className="hover:text-zinc-600 transition-colors font-semibold flex items-center gap-2"
          >
            <FaGithub size={18} />
            Star on GitHub
          </a>
        </div>
      </div>
    </div>
  );
};



================================================
FILE: src/components/Header.tsx
================================================
"use client";
import Link from "next/link";
import { useSidebar } from "./ui/sidebar";
import { useUser } from "@clerk/nextjs";

export const Header = () => {
  const { toggleSidebar } = useSidebar();
  const { isSignedIn, user, isLoaded } = useUser();

  const isUserLoggedIn = isLoaded && isSignedIn;

  return (
    <div className="fixed md:hidden right-0 left-0 w-full top-0 bg-white/80 dark:bg-zinc-950/80 backdrop-blur-sm border-b border-zinc-200 dark:border-zinc-800 z-50">
      <div className="flex justify-center sm:justify-between items-center p-4 ">
        <div className="flex flex-row items-center gap-2 shrink-0 w-full">
          <div className="flex flex-row items-center gap-4 justify-center w-full">
            {isUserLoggedIn && (
              <button
                className="p-1 cursor-pointer absolute left-5"
                onClick={() => {
                  toggleSidebar();
                }}
              >
                <img src="/menu.svg" className="size-5" />
              </button>
            )}
            <Link className="flex flex-row items-center gap-2" href="/">
              <div className="flex flex-row items-center gap-2">
                <div className=" text-zinc-800 dark:text-zinc-100">
                  <img
                    src="/logo.svg"
                    alt="Open Deep Research"
                    className="size-6"
                  />
                </div>
                <div className="text-lg font-bold text-zinc-800 dark:text-zinc-100">
                  Open Deep Research
                </div>
              </div>
            </Link>
          </div>
        </div>
        <div className="flex flex-row items-center gap-2 shrink-0"></div>
      </div>
    </div>
  );
};



================================================
FILE: src/components/Heading.tsx
================================================
export const Heading = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => {
  return (
    <div className="flex flex-col gap-2 w-full pt-[60px]">
      <p
        title={title}
        className="w-full text-[32px] font-medium text-left text-[#101828] overflow-hidden"
      >
        {title}
      </p>
      <p className="w-full text-base font-light text-left text-[#6a7282]">
        {description}
      </p>
      <div className="w-full mt-2 mb-6 h-[1px] bg-[#e5e7eb]"></div>
    </div>
  );
};



================================================
FILE: src/components/icons.tsx
================================================
export const ArrowUpIcon = ({ size = 16 }: { size?: number }) => (
  <svg
    height={size}
    strokeLinejoin="round"
    viewBox="0 0 16 16"
    width={size}
    style={{ color: "currentcolor" }}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z"
      fill="currentColor"
    ></path>
  </svg>
);

export const ChevronDownIcon = ({ size = 16 }: { size?: number }) => (
  <svg
    height={size}
    strokeLinejoin="round"
    viewBox="0 0 16 16"
    width={size}
    style={{ color: "currentcolor" }}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.0607 6.74999L11.5303 7.28032L8.7071 10.1035C8.31657 10.4941 7.68341 10.4941 7.29288 10.1035L4.46966 7.28032L3.93933 6.74999L4.99999 5.68933L5.53032 6.21966L7.99999 8.68933L10.4697 6.21966L11 5.68933L12.0607 6.74999Z"
      fill="currentColor"
    />
  </svg>
);

export const ChevronUpIcon = ({ size = 16 }: { size?: number }) => (
  <svg
    height={size}
    strokeLinejoin="round"
    viewBox="0 0 16 16"
    width={size}
    style={{ color: "currentcolor" }}
    className="rotate-0"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.74999 3.93933L7.28032 4.46966L10.1035 7.29288C10.4941 7.68341 10.4941 8.31657 10.1035 8.7071L7.28032 11.5303L6.74999 12.0607L5.68933 11L6.21966 10.4697L8.68933 7.99999L6.21966 5.53032L5.68933 4.99999L6.74999 3.93933Z"
      fill="currentColor"
    ></path>
  </svg>
);

export const SpinnerIcon = ({ size = 16 }: { size?: number }) => (
  <svg
    height={size}
    strokeLinejoin="round"
    viewBox="0 0 16 16"
    width={size}
    style={{ color: "currentcolor" }}
  >
    <g clipPath="url(#clip0_2393_1490)">
      <path d="M8 0V4" stroke="currentColor" strokeWidth="1.5" />
      <path
        opacity="0.5"
        d="M8 16V12"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.9"
        d="M3.29773 1.52783L5.64887 4.7639"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.1"
        d="M12.7023 1.52783L10.3511 4.7639"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.4"
        d="M12.7023 14.472L10.3511 11.236"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.6"
        d="M3.29773 14.472L5.64887 11.236"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.2"
        d="M15.6085 5.52783L11.8043 6.7639"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.7"
        d="M0.391602 10.472L4.19583 9.23598"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.3"
        d="M15.6085 10.4722L11.8043 9.2361"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        opacity="0.8"
        d="M0.391602 5.52783L4.19583 6.7639"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </g>
    <defs>
      <clipPath id="clip0_2393_1490">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const VercelIcon = ({ size = 17 }) => {
  return (
    <svg
      height={size}
      strokeLinejoin="round"
      viewBox="0 0 16 16"
      width={size}
      style={{ color: "currentcolor" }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 1L16 15H0L8 1Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const StopIcon = ({ size = 16 }: { size?: number }) => {
  return (
    <svg
      height={size}
      viewBox="0 0 16 16"
      width={size}
      style={{ color: "currentcolor" }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 3H13V13H3V3Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CheckedSquare = ({ size = 16 }: { size?: number }) => {
  return (
    <svg
      height={size}
      strokeLinejoin="round"
      viewBox="0 0 16 16"
      width={size}
      style={{ color: "currentcolor" }}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 16H1C0.447715 16 0 15.5523 0 15V1C0 0.447715 0.447716 0 1 0L15 8.17435e-06C15.5523 8.47532e-06 16 0.447724 16 1.00001V15C16 15.5523 15.5523 16 15 16ZM11.7803 6.28033L12.3107 5.75L11.25 4.68934L10.7197 5.21967L6.5 9.43935L5.28033 8.21967L4.75001 7.68934L3.68934 8.74999L4.21967 9.28033L5.96967 11.0303C6.11032 11.171 6.30109 11.25 6.5 11.25C6.69891 11.25 6.88968 11.171 7.03033 11.0303L11.7803 6.28033Z"
        fill="currentColor"
      ></path>
    </svg>
  );
};

export const UncheckedSquare = ({ size = 16 }: { size?: number }) => {
  return (
    <svg
      height={size}
      strokeLinejoin="round"
      viewBox="0 0 16 16"
      width={size}
      style={{ color: "currentcolor" }}
    >
      <rect
        x="1"
        y="1"
        width="14"
        height="14"
        stroke="currentColor"
        strokeWidth="1.5"
        fill="none"
      />
    </svg>
  );
};



================================================
FILE: src/components/LandingCard.tsx
================================================
export const LandingCard = ({
  imageSrc,
  title,
  description,
}: {
  imageSrc: string;
  title: string;
  description: string;
}) => {
  return (
    <div className="w-[226px] relative overflow-hidden rounded-xl bg-white p-[7px] flex flex-col gap-4">
      <img
        src={imageSrc}
        className="w-full h-full max-w-[212px] max-h-[92px]"
      />
      <div className="flex flex-col gap-2 px-2">
        <p className="text-base font-medium text-left text-black">{title}</p>
        <p className="text-xs text-left text-[#4a5565] mb-2">{description}</p>
      </div>
    </div>
  );
};



================================================
FILE: src/components/LandingHero.tsx
================================================
import { cn } from "@/lib/utils";
import { useUser } from "@clerk/nextjs";

export const LandingHero = () => {
  const { isSignedIn, user, isLoaded } = useUser();

  const isAuthenticated = isSignedIn && !!user;

  if (!isLoaded) {
    return (
      <>
        <div className="w-[180px] h-9 rounded bg-gray-200 animate-pulse mb-8 mt-2" />
        <div className="flex flex-col gap-4 w-full animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-3/4 mx-auto" />
          <div className="h-5 bg-gray-200 rounded w-1/2 mx-auto" />
        </div>
        <div className="w-40 h-12 bg-gray-200 rounded-lg animate-pulse mb-4 mt-8" />
        <div className="h-[240px] bg-gray-200 w-full rounded-lg animate-pulse" />
      </>
    );
  }

  return (
    <>
      <a
        href="https://togetherai.link/"
        target="_blank"
        rel="noreferrer"
        className="w-[180px] relative items-center justify-center rounded bg-gray-50 border border-gray-200 flex flex-row gap-1 px-4 py-2"
      >
        <div className="text-xs text-[#6A7282] whitespace-nowrap">
          Powered by
        </div>
        <img
          src="/together.png"
          className="w-[77.3px] h-[12.94px] mt-0.5 object-fill"
          alt="Together.ai logo"
        />
      </a>

      <div
        className={cn(
          isAuthenticated ? "mb-8" : "mb-6",
          "flex flex-col gap-4 w-full"
        )}
      >
        <p className="text-[32px] md:text-[40px] font-medium text-center text-[#1e2939] font-serif">
          {!isAuthenticated ? (
            <>Reports with Open Deep Research</>
          ) : (
            <>Welcome Back, {user.firstName || user.fullName || "Researcher"}</>
          )}
        </p>
        {!isAuthenticated && (
          <p className="mx-auto max-w-[364px] text-base text-center text-[#6a7282]">
            Have AI do research for you, refine your ideas, and turn every
            question into a meaningful report
          </p>
        )}
      </div>
    </>
  );
};



================================================
FILE: src/components/star-button.tsx
================================================
import Link from "next/link";
import * as React from "react";
import type { SVGProps } from "react";

const Github = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 256 250"
    width="1em"
    height="1em"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    preserveAspectRatio="xMidYMid"
    {...props}
  >
    <path d="M128.001 0C57.317 0 0 57.307 0 128.001c0 56.554 36.676 104.535 87.535 121.46 6.397 1.185 8.746-2.777 8.746-6.158 0-3.052-.12-13.135-.174-23.83-35.61 7.742-43.124-15.103-43.124-15.103-5.823-14.795-14.213-18.73-14.213-18.73-11.613-7.944.876-7.78.876-7.78 12.853.902 19.621 13.19 19.621 13.19 11.417 19.568 29.945 13.911 37.249 10.64 1.149-8.272 4.466-13.92 8.127-17.116-28.431-3.236-58.318-14.212-58.318-63.258 0-13.975 5-25.394 13.188-34.358-1.329-3.224-5.71-16.242 1.24-33.874 0 0 10.749-3.44 35.21 13.121 10.21-2.836 21.16-4.258 32.038-4.307 10.878.049 21.837 1.47 32.066 4.307 24.431-16.56 35.165-13.12 35.165-13.12 6.967 17.63 2.584 30.65 1.255 33.873 8.207 8.964 13.173 20.383 13.173 34.358 0 49.163-29.944 59.988-58.447 63.157 4.591 3.972 8.682 11.762 8.682 23.704 0 17.126-.148 30.91-.148 35.126 0 3.407 2.304 7.398 8.792 6.14C219.37 232.5 256 184.537 256 128.002 256 57.307 198.691 0 128.001 0Zm-80.06 182.34c-.282.636-1.283.827-2.194.39-.929-.417-1.45-1.284-1.15-1.922.276-.655 1.279-.838 2.205-.399.93.418 1.46 1.293 1.139 1.931Zm6.296 5.618c-.61.566-1.804.303-2.614-.591-.837-.892-.994-2.086-.375-2.66.63-.566 1.787-.301 2.626.591.838.903 1 2.088.363 2.66Zm4.32 7.188c-.785.545-2.067.034-2.86-1.104-.784-1.138-.784-2.503.017-3.05.795-.547 2.058-.055 2.861 1.075.782 1.157.782 2.522-.019 3.08Zm7.304 8.325c-.701.774-2.196.566-3.29-.49-1.119-1.032-1.43-2.496-.726-3.27.71-.776 2.213-.558 3.315.49 1.11 1.03 1.45 2.505.701 3.27Zm9.442 2.81c-.31 1.003-1.75 1.459-3.199 1.033-1.448-.439-2.395-1.613-2.103-2.626.301-1.01 1.747-1.484 3.207-1.028 1.446.436 2.396 1.602 2.095 2.622Zm10.744 1.193c.036 1.055-1.193 1.93-2.715 1.95-1.53.034-2.769-.82-2.786-1.86 0-1.065 1.202-1.932 2.733-1.958 1.522-.03 2.768.818 2.768 1.868Zm10.555-.405c.182 1.03-.875 2.088-2.387 2.37-1.485.271-2.861-.365-3.05-1.386-.184-1.056.893-2.114 2.376-2.387 1.514-.263 2.868.356 3.061 1.403Z" />
  </svg>
);

export function StarButton() {
  return (
    <Link
      href="https://github.com/Nutlope/open-deep-research"
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center gap-2 text-sm text-zinc-600 dark:text-zinc-300 hover:text-zinc-700 dark:hover:text-zinc-300"
    >
      <Github className="size-4" />
      <span className="hidden sm:inline">Star on GitHub</span>
    </Link>
  );
}



================================================
FILE: src/components/app/ApiKeyControls.tsx
================================================
"use client";

import React, {
  useEffect,
  useState,
  useRef,
  useContext,
  createContext,
} from "react";

import { toast } from "sonner";

// Context for Together API Key
const TogetherApiKeyContext = createContext<
  | {
      apiKey: string | undefined;
      setApiKey: (key: string | undefined) => void;
    }
  | undefined
>(undefined);

export function TogetherApiKeyProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [apiKey, setApiKeyState] = useState<string | undefined>(undefined);

  useEffect(() => {
    setApiKeyState(sessionStorage.getItem("togetherApiKey") || undefined);
  }, []);

  // Sync to sessionStorage and notify listeners
  const setApiKey = (key: string | undefined) => {
    setApiKeyState(key);
    if (key) {
      sessionStorage.setItem("togetherApiKey", key);
    } else {
      sessionStorage.removeItem("togetherApiKey");
    }
  };

  return (
    <TogetherApiKeyContext.Provider value={{ apiKey, setApiKey }}>
      {children}
    </TogetherApiKeyContext.Provider>
  );
}

export function useTogetherApiKey() {
  const context = useContext(TogetherApiKeyContext);
  if (!context) {
    throw new Error(
      "useTogetherApiKey must be used within a TogetherApiKeyProvider"
    );
  }
  return context;
}

export function ApiKeyControls() {
  const { apiKey, setApiKey } = useTogetherApiKey();
  const [togetherApiKey, setTogetherApiKey] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setTogetherApiKey(apiKey || "");
  }, [apiKey]);

  const validateAndSaveApiKey = async (apiKey: string) => {
    setIsValidating(true);
    try {
      const response = await fetch("/api/validate-key", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ apiKey }),
      });

      if (response.ok) {
        setApiKey(apiKey);
        toast.success("API key validated and saved!");
        return true;
      } else {
        const errorData = await response.json();
        const errorMessage =
          errorData.message ||
          `API key validation failed with status: ${response.status}`;
        toast.error(errorMessage);

        if (errorMessage.startsWith("Invalid API key")) {
          setApiKey("");
          setTogetherApiKey("");
        }
        return false;
      }
    } finally {
      setIsValidating(false);
    }
  };

  const handleApiKeyChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTogetherApiKey(value);

    if (value.length === 0) {
      setApiKey("");
      return;
    }

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(async () => {
      await validateAndSaveApiKey(value);
    }, 500); // Debounce for 500ms
  };

  return (
    <div className="flex flex-col gap-3 w-full px-4 pb-2 border-t border-t-[#E5E7EB] pt-5">
      <p className="text-sm text-[#4a5565]">Add your Together API key</p>
      <div className="flex flex-col gap-1 rounded border border-[#d1d5dc] bg-white p-3 relative">
        <input
          type="password"
          placeholder="Together API key"
          className="text-sm text-[#4a5565] outline-none placeholder-[#d1d5dc] bg-transparent border-none focus:ring-0 p-0"
          value={togetherApiKey}
          onChange={handleApiKeyChange}
          autoComplete="off"
        />
        {isValidating && (
          <img
            src="/loading.svg"
            className="text-xs absolute right-3 top-1/2 -translate-y-1/2 animate-spin"
          />
        )}
      </div>

      <p className="text-xs font-light text-left">
        <span className="text-[#99a1af]">Don't have an API key? </span>
        <a
          href="https://togetherai.link/"
          className="text-[#6a7282] underline underline-offset-2"
        >
          Get one for free.
        </a>
      </p>
    </div>
  );
}



================================================
FILE: src/components/app/AppSidebar.tsx
================================================
"use client";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenuButton,
  useSidebar,
} from "@/components/ui/sidebar";
import { SignedIn, UserButton, useUser } from "@clerk/nextjs";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { getChats } from "@/lib/getChats";
import { ApiKeyControls } from "@/components/app/ApiKeyControls";

type Chat = Awaited<ReturnType<typeof getChats>>[number];

async function fetchChats(): Promise<Chat[]> {
  const res = await fetch("/api/chats");
  if (!res.ok) {
    // Handle error, maybe throw or return empty array
    console.error("Failed to fetch chats");
    return [];
  }
  return res.json();
}

export function AppSidebar() {
  const router = useRouter();
  const pathname = usePathname();
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { setOpenMobile, toggleSidebar } = useSidebar();
  const { isSignedIn, isLoaded } = useUser();

  const isUserLoggedIn = isLoaded && isSignedIn;

  useEffect(() => {
    if (!isUserLoggedIn) return;

    const fetchAndSetChats = async () => {
      const chatsData = await fetchChats();
      setChats(chatsData);
      setIsLoading(false);
    };

    fetchAndSetChats();
  }, [pathname, isUserLoggedIn]);

  if (!isUserLoggedIn) {
    return <></>;
  }

  return (
    <Sidebar className="print:hidden">
      <SidebarHeader className="pt-5 px-5">
        <div className="flex flex-row justify-between items-center pb-6">
          <Link className="flex flex-row items-center gap-2" href="/">
            <div className="flex flex-row items-center gap-2">
              <div className=" text-zinc-800 dark:text-zinc-100">
                <img
                  src="/logo.svg"
                  alt="Open Deep Research"
                  className="size-6"
                />
              </div>
              <div className="text-lg font-bold text-zinc-800 dark:text-zinc-100 tracking-tighter">
                Open Deep Research
              </div>
            </div>
          </Link>
          <button
            className="p-1 cursor-pointer md:hidden"
            onClick={() => {
              toggleSidebar();
            }}
          >
            <img src="/menu.svg" className="size-5" />
          </button>
        </div>
        <SidebarMenuButton asChild>
          <button
            onClick={() => {
              setOpenMobile(false);
              router.push("/");
            }}
            className="flex justify-center items-center w-full h-10 relative gap-1.5 px-4 py-1.5 rounded !bg-[#dce8ff] border border-[#072d77] cursor-pointer "
          >
            <svg
              width={11}
              height={12}
              viewBox="0 0 11 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="flex-grow-0 flex-shrink-0"
              preserveAspectRatio="none"
            >
              <path
                d="M5.5 1V11M10.5 6H0.5"
                stroke="#072D77"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-[#072d77]">
              New Report
            </p>
          </button>
        </SidebarMenuButton>
      </SidebarHeader>
      <SidebarContent>
        <div className="flex flex-col gap-3 px-5 mt-5">
          <div className="text-xs font-medium text-left text-[#99a1af]">
            Your reports
          </div>

          {isLoading ? (
            <>
              {[...Array(5)].map((_, index) => (
                <div key={index} className="p-2 h-8">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
              ))}
            </>
          ) : (
            chats.map((chat) => {
              const isActive = pathname === `/chat/${chat.id}`;
              return (
                <SidebarMenuButton isActive={isActive} asChild key={chat.id}>
                  <Link
                    onClick={() => setOpenMobile(false)}
                    href={`/chat/${chat.id}`}
                    className={`text-base text-left overflow-hidden ${
                      isActive
                        ? " text-[#1e2939] !font-medium"
                        : "text-[#4a5565]"
                    }`}
                  >
                    <span className="truncate">
                      {chat.title || chat.initialUserMessage}
                    </span>
                  </Link>
                </SidebarMenuButton>
              );
            })
          )}
          {chats.length === 0 && !isLoading && (
            <p className="text-base text-left text-[#4a5565]">No chats yet.</p>
          )}
        </div>
        <SidebarGroup />
      </SidebarContent>
      <SidebarFooter>
        <ApiKeyControls />
        <div className="flex flex-row items-center gap-2 px-5 pt-3 border-t border-t-[#E5E7EB]">
          <SignedIn>
            <UserButton
              appearance={{
                elements: {
                  userButtonBox: {
                    flexDirection: "row-reverse",
                  },
                },
              }}
              showName
            />
          </SignedIn>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}



================================================
FILE: src/components/app/ChatPage.tsx
================================================
"use client";
import { getResearch } from "@/db/action";
import { useRouter } from "next/navigation";

import { QuestionsPage } from "@/components/app/questions/QuestionsPage";
import { ReportLoadingPage } from "./ReportLoadingPage";
import { FinalReportPage } from "./FinalReportPage";

export const ChatPage = ({
  chatId,
  researchData,
}: {
  chatId: string;
  researchData: Awaited<ReturnType<typeof getResearch>>;
}) => {
  const router = useRouter();

  // if we get chat without questions, generate questions with AI LLM and save to DB
  if (!researchData || !researchData.initialUserMessage) {
    router.replace("/");
    return <></>;
  }

  if (!researchData.answers) {
    return (
      <QuestionsPage questions={researchData.questions || []} chatId={chatId} />
    );
  }

  if (!researchData?.report) {
    return (
      <ReportLoadingPage
        researchTopic={researchData.initialUserMessage}
        chatId={chatId}
        researchStartedAt={
          researchData.researchStartedAt || researchData.createdAt
        }
        onComplete={() => {
          router.refresh();
          // scroll to top of page
          window.scrollTo({ top: 0, behavior: "smooth" });
        }}
      />
    );
  }

  return <FinalReportPage researchData={researchData} />;
};



================================================
FILE: src/components/app/DownloadPdfButton.tsx
================================================
"use client";
import { toast } from "sonner";
import { useState } from "react";
import { slugifyFilename } from "@/lib/utils";

export const DownloadPdfButton = ({ fileName }: { fileName?: string }) => {
  const [loading, setLoading] = useState(false);

  const handleDownloadPdf = async () => {
    setLoading(true);

    try {
      const response = await fetch("/api/pdf", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url: window.location.href, fileName }),
      });

      if (!response.ok) {
        throw new Error("PDF generation failed");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      // Use slugified filename to match backend
      const safeFileName = slugifyFilename(fileName || "report");
      const a = document.createElement("a");
      a.href = url;
      a.download = `${safeFileName}.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      toast.success("PDF generated successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleDownloadPdf}
      className="cursor-pointer flex flex-col justify-center items-center overflow-hidden  gap-2.5 px-3 py-1.5 rounded bg-[#072d77] border border-[#072d77]"
      disabled={loading}
    >
      <div className="flex justify-start items-center self-stretch relative gap-1.5">
        {loading ? (
          <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-white">
            Generating PDF...
          </p>
        ) : (
          <>
            <img src="/download.svg" alt="" className="size-4" />
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-white">
              Download as PDF
            </p>
          </>
        )}
      </div>
    </button>
  );
};



================================================
FILE: src/components/app/FinalReportPage.tsx
================================================
"use client";
import { getResearch } from "@/db/action";
import { toast } from "sonner";
import React from "react";

import { DownloadPdfButton } from "./DownloadPdfButton";
import { ReportBody } from "./ReportBody";

export const FinalReportPage = ({
  researchData,
}: {
  researchData: Awaited<ReturnType<typeof getResearch>>;
}) => {
  if (!researchData || !researchData.report) {
    return <></>;
  }

  return (
    <div className="flex flex-col size-full pt-20 md:pt-5 mx-auto max-w-[886px] px-5">
      <div className="flex flex-row gap-2 xl:px-4 items-start justify-center md:justify-end print:hidden mb-5">
        <button
          onClick={() => {
            // copy to clipboard
            toast.success("Copied to clipboard!");
            navigator.clipboard.writeText(window.location.href);
          }}
          className="cursor-pointer flex flex-col justify-center items-center overflow-hidden gap-2.5 px-3 py-1.5 rounded border-[0.5px] border-[#cad5e2]"
          style={{ filter: "drop-shadow(0px 1px 5px rgba(0,0,0,0.15))" }}
        >
          <div className="flex justify-start items-center self-stretch relative gap-1.5">
            <img src="/share.svg" alt="" className="size-4" />
            <p className="flex-grow-0 flex-shrink-0 text-sm text-left text-[#62748e]">
              Share
            </p>
          </div>
        </button>
        <DownloadPdfButton fileName={researchData.researchTopic ?? undefined} />
      </div>

      <div className="print:block hidden text-lg text-zinc-400 leading-5 mx-auto text-center mb-5">
        <a href="/" className="flex flex-row items-center gap-2">
          <div className="flex flex-row items-center gap-2">
            <div className=" text-zinc-800 dark:text-zinc-100">
              <img
                src="/logo.svg"
                alt="Open Deep Research"
                className="size-6"
              />
            </div>
            <div className="text-lg font-bold text-zinc-800 dark:text-zinc-100">
              Open Deep Research
            </div>
          </div>
        </a>
      </div>

      <ReportBody researchData={researchData} />
    </div>
  );
};



================================================
FILE: src/components/app/ReportBody.tsx
================================================
"use client";
import React from "react";

import { CustomMarkdown } from "../CustomMarkdown";
import { WebResultCard } from "./reportLoading/WebResultCard";
import { CitationNumber } from "./citations/CitationNumber";
import { extractMarkdownHeadings } from "@/lib/utils";
import { TableOfContents } from "./TableOfContents";

export const ReportBody = ({
  researchData,
}: {
  researchData: {
    researchTopic: string | null;
    report: string | null;
    sources?: Array<{ url: string; title: string }> | null;
    coverUrl: string | null;
    citations?: Array<{
      url: string;
      title: string;
      citation: string;
    }> | null;
  };
}) => {
  if (!researchData || !researchData.report) {
    return <></>;
  }

  return (
    <div className="border border-[#E2E8F0] rounded-lg pb-4 mb-6 print:border-none">
      {researchData.coverUrl && (
        <div className="w-full h-[202px] md:h-[305px] relative overflow-hidden rounded-lg">
          <img
            src={researchData.coverUrl}
            className="w-full h-full object-cover rounded"
            alt=""
          />
        </div>
      )}
      <div className="flex flex-col-reverse xl:flex-row gap-6 px-5 pt-3">
        {/* Main Content */}
        <div className="max-w-[600px]">
          <CustomMarkdown sources={researchData.sources || []}>
            {researchData.report}
          </CustomMarkdown>
          {researchData.sources && researchData.sources?.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mt-4">
              <h3 className="text-lg font-medium text-[#101828] col-span-full mb-2">
                We used {researchData.sources.length} sources for this research:
              </h3>
              {researchData.sources.map((result, idx) => (
                <WebResultCard
                  key={result.url + "-" + idx}
                  result={result}
                  id={result.url}
                >
                  <CitationNumber num={idx + 1} />
                </WebResultCard>
              ))}
            </div>
          )}
        </div>

        {/* Table of Contents */}
        <TableOfContents markdown={researchData.report || ""} />
      </div>
    </div>
  );
};



================================================
FILE: src/components/app/ReportLoadingPage.tsx
================================================
"use client";

import { useEffect, useState } from "react";
import { Heading } from "../Heading";
import { ReportSteps, ReportStepType } from "./reportLoading/ReportSteps";
import TimelineProgress from "./reportLoading/TimelineProgress";
import { ResearchEventStreamEvents } from "@/app/api/research/route";
import { StreamingReportBody } from "./reportLoading/StreamingReportBody";

export const ReportLoadingPage = ({
  researchTopic,
  chatId,
  researchStartedAt,
  onComplete,
}: {
  researchStartedAt: Date;
  researchTopic: string;
  chatId: string;
  onComplete: () => void;
}) => {
  const [streamEvents, setStreamEvents] = useState<ResearchEventStreamEvents[]>(
    []
  );
  const [steps, setSteps] = useState<ReportStepType[]>([
    {
      id: "initial_planning",
      title: "Initial Planning",
      status: "loading",
    },
    {
      id: "iteration_1",
      title: "Iteration #1",
      status: "pending",
    },
    {
      id: "iteration_2",
      title: "Iteration #2",
      status: "pending",
    },
    {
      id: "iteration_3",
      title: "Iteration #3",
      status: "pending",
    },
    {
      id: "writing_report",
      title: "Writing Report",
      status: "pending",
    },
  ]);
  const [isStreaming, setIsStreaming] = useState(false);

  const onResearchEnd = () => {
    setIsStreaming(false);
    onComplete();
  };

  const fetchResearch = async (chatId: string) => {
    try {
      const response = await fetch(`/api/research/?chatId=${chatId}`);
      if (!response.ok) throw new Error("Failed to fetch research data");

      const events: ResearchEventStreamEvents[] = await response.json();

      const isCompleted =
        events.some((event) => event.type === "research_completed") ||
        events.some(
          (event) =>
            event.type === "research_status" && event.status === "completed"
        );

      if (isCompleted) {
        onResearchEnd();
      } else {
        if (!isStreaming) setIsStreaming(true);
      }

      // Update research data, maintaining chronological order
      setStreamEvents(
        events.sort((a: any, b: any) => a.timestamp - b.timestamp)
      );
      return { isCompleted: isCompleted };
    } catch (error) {
      console.error("Error fetching research:", error);
      setIsStreaming(false);
      return { isCompleted: true };
    }
  };

  useEffect(() => {
    let pollInterval: NodeJS.Timeout;
    let isPolling = true;

    const pollResearch = async () => {
      if (!isPolling) return;

      const { isCompleted } = await fetchResearch(chatId);

      if (!isCompleted && isPolling) {
        pollInterval = setTimeout(pollResearch, 4000);
      }
    };

    // Start polling
    pollResearch();

    // Cleanup function
    return () => {
      isPolling = false;
      if (pollInterval) clearTimeout(pollInterval);
    };
  }, []);

  useEffect(() => {
    if (streamEvents.length > 0) {
      const newSteps = [...steps];
      streamEvents.map((event) => {
        // parsing for steps
        if (event.type === "planning_completed") {
          newSteps[0].status = "completed";
          newSteps[1].status = "loading";
        }
        if (
          event.type === "evaluation_completed" ||
          event.type === "iteration_completed"
        ) {
          if (event.iteration === 1) {
            newSteps[1].status = "completed";
            newSteps[2].status = "loading";
          }
          if (event.iteration === 2) {
            newSteps[2].status = "completed";
            newSteps[3].status = "loading";
          }
          if (event.iteration === 3) {
            newSteps[3].status = "completed";
            newSteps[4].status = "loading";
          }
        }
        if (event.type === "report_generated") {
          newSteps[4].status = "completed";
        }
      });
      setSteps(newSteps);
    }
  }, [streamEvents]);

  // extract from streamEvents the coverUrl event to get url
  const coverUrl = streamEvents.find(
    (event) => event.type === "cover_generation_completed"
  )?.coverImage;

  const report = streamEvents
    .filter((event) => event.type === "report_generating")
    .sort((a, b) => b.timestamp - a.timestamp)[0]?.partialReport;

  if (coverUrl && report)
    return (
      <div className="flex flex-col size-full pt-20 md:pt-5 mx-auto max-w-[886px] relative px-5">
        <StreamingReportBody
          researchTopic={researchTopic}
          coverUrl={coverUrl}
          report={report}
        />
      </div>
    );

  return (
    <div className="px-5 py-5 h-full flex flex-col flex-1 mx-auto max-w-[700px] w-full">
      <Heading
        title={
          researchTopic.length > 60
            ? researchTopic.slice(0, 60) + "..."
            : researchTopic
        }
        description="It takes a few minutes to gather sources, analyze data, and create your report."
      />

      <div className="flex flex-col gap-2 md:flex-row">
        <ReportSteps
          steps={steps}
          chatId={chatId}
          researchStartedAt={researchStartedAt}
        />

        <TimelineProgress events={streamEvents} />
      </div>
    </div>
  );
};



================================================
FILE: src/components/app/TableOfContents.tsx
================================================
import React, { useEffect, useState } from "react";
import { cn, extractMarkdownHeadings } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

export const TableOfContents = ({ markdown }: { markdown: string }) => {
  const headings = extractMarkdownHeadings(markdown).filter(
    (h) => h.level === 2
  );

  const [currentHash, setCurrentHash] = useState<string>("");

  useEffect(() => {
    // Function to update currentHash based on scroll position
    const handleScroll = () => {
      const OFFSET = 80; // header offset
      const headingAnchors = headings.map((heading) => {
        const anchor = heading.text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/(^-|-$)/g, "");
        return {
          id: anchor,
          el: document.getElementById(anchor),
        };
      });
      // Filter out headings that don't exist in DOM
      const visibleHeadings = headingAnchors.filter((h) => h.el);
      // Find the heading closest to the top (with offset)
      let closest = visibleHeadings[0];
      for (const h of visibleHeadings) {
        const rect = h.el!.getBoundingClientRect();
        if (rect.top - OFFSET <= 0) {
          closest = h;
        } else {
          break;
        }
      }
      if (closest) {
        setCurrentHash(`#${closest.id}`);
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    // Initial call
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, [markdown]);

  if (headings.length === 0) return null;

  return (
    <div className="hidden xl:block w-full max-w-xs xl:max-w-[220px] mb-6 xl:mb-0 xl:mr-6 sticky top-6 max-h-[calc(100vh-48px)] overflow-auto border-l border-[#E2E8F0]">
      <ul className="space-y-1">
        {headings.map((heading, idx) => {
          const anchor = heading.text
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/(^-|-$)/g, "");
          const isActive = currentHash === `#${anchor}`;
          return (
            <motion.li
              key={idx}
              layout
              className={cn(
                "border-l-2 ml-[-1px] border-transparent relative z-10 bg-white"
              )}
              initial={false}
              animate={
                isActive
                  ? { backgroundColor: "#fff" }
                  : { backgroundColor: "#fff" }
              }
              transition={{ type: "spring", stiffness: 400, damping: 30 }}
            >
              {isActive && (
                <motion.div
                  layoutId="toc-active-border"
                  className="absolute left-[-1px] top-0 h-full w-0.5 bg-[#0f172b] rounded"
                  style={{ zIndex: 20 }}
                  transition={{ type: "spring", stiffness: 400, damping: 30 }}
                />
              )}
              <a
                href={`#${anchor}`}
                className={cn(
                  "block pl-3 transition-colors duration-300",
                  isActive
                    ? "text-sm font-medium text-left text-[#0f172b]"
                    : "text-sm font-light text-left text-[#62748e]"
                )}
              >
                {heading.text}
              </a>
            </motion.li>
          );
        })}
      </ul>
    </div>
  );
};



================================================
FILE: src/components/app/citations/CitationNumber.tsx
================================================
export const CitationNumber = ({ num }: { num: number }) => {
  return (
    <span className="mx-[1px] inline-block rounded bg-gray-200 px-1 align-text-bottom text-xs tabular-nums leading-normal dark:bg-slate-800 dark:text-slate-400 cursor-pointer">
      {num}
    </span>
  );
};



================================================
FILE: src/components/app/citations/CitationTooltip.tsx
================================================
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { WebResultCard } from "../reportLoading/WebResultCard";
import { CitationNumber } from "./CitationNumber";

interface CitationTooltipProps {
  index: number;
  source: { url: string; title: string };
}

export const CitationTooltip = ({ index, source }: CitationTooltipProps) => {
  return (
    <Popover>
      <PopoverTrigger>
        <CitationNumber num={index + 1} />
      </PopoverTrigger>
      <PopoverContent className="!p-0 !border-0 !bg-transparent">
        <WebResultCard result={source} id={source.url} />
      </PopoverContent>
    </Popover>
  );
};



================================================
FILE: src/components/app/questions/AnswerInput.tsx
================================================
export const AnswerInput = ({
  question,
  answer,
  setAnswer,
  onEnter,
}: {
  question: string;
  answer: string;
  setAnswer: (answer: string) => void;
  onEnter: () => void;
}) => {
  return (
    <div className="flex flex-col gap-3 w-full">
      <p className="w-full text-base text-left text-[#101828]">{question}</p>
      <input
        className="w-full h-[38px] relative overflow-hidden rounded bg-white border-[0.7px] border-[#d1d5dc] text-base font-light text-left text-[#6a7282] px-3 py-2.5"
        type="text"
        placeholder="Answer here..."
        value={answer}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            onEnter();
          }
        }}
        onChange={(e) => setAnswer(e.target.value)}
      ></input>
    </div>
  );
};



================================================
FILE: src/components/app/questions/QuestionsPage.tsx
================================================
"use client";

import { useState, useEffect } from "react";
import { Heading } from "../../Heading";
import { AnswerInput } from "./AnswerInput";
import { TooltipUsage } from "../tooltip/TooltipUsage";
import { useAuth } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useTogetherApiKey } from "../ApiKeyControls";

export const QuestionsPage = ({
  questions,
  chatId,
}: {
  questions: string[];
  chatId: string;
}) => {
  const { apiKey } = useTogetherApiKey();
  const [answers, setAnswers] = useState<string[]>(
    Array(questions.length).fill("")
  );
  const [remainingResearches, setRemainingResearches] = useState(0);
  const [resetTime, setResetTime] = useState<string | null>(null);
  const { userId } = useAuth();
  const router = useRouter();

  const onSaveAnswers = async (answersToSave: string[]) => {
    try {
      const response = await fetch("/api/storeAnswers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          chatId,
          answers: answersToSave,
          togetherApiKey: apiKey,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Your search will start shortly!");
        router.refresh();
      } else {
        toast.error(data.message || "Failed to process your request.");
      }
    } catch (error) {
      console.error("Error processing request:", error);
      toast.error("An unexpected error occurred.");
    }
  };

  useEffect(() => {
    const fetchLimits = async () => {
      try {
        const response = await fetch("/api/user/limits", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isBringingKey: !!apiKey,
          }),
        });
        const data = await response.json();
        setRemainingResearches(data.remaining);
        setResetTime(data.reset);
      } catch (error) {
        console.error("Failed to fetch limits:", error);
      }
    };

    if (userId) {
      fetchLimits();
    }

    const handleFocus = () => {
      if (userId) {
        fetchLimits();
      }
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      window.removeEventListener("focus", handleFocus);
    };
  }, [userId]);

  // Refetch limits when apiKey changes and userId is present
  useEffect(() => {
    const fetchLimits = async () => {
      try {
        const response = await fetch("/api/user/limits", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isBringingKey: !!apiKey,
          }),
        });
        const data = await response.json();
        setRemainingResearches(data.remaining);
        setResetTime(data.reset);
      } catch (error) {
        console.error("Failed to fetch limits:", error);
      }
    };
    if (userId) {
      fetchLimits();
    }
  }, [apiKey, userId]);

  return (
    <div className="my-5 px-5 h-full flex flex-col flex-1 max-w-[700px] mx-auto w-full">
      <Heading
        title="Answer some questions for a more accurate report"
        description="This is optional, you can skip this by clicking “Generate Report” or “Skip”."
      />
      <div className="flex flex-col gap-4">
        {questions?.map((question, index) => (
          <AnswerInput
            key={index}
            question={question}
            answer={answers[index]}
            setAnswer={(answer) => {
              setAnswers((prev) => {
                const newAnswers = [...prev];
                newAnswers[index] = answer;
                return newAnswers;
              });
            }}
            onEnter={() => {
              if (index === questions.length - 1 && userId) {
                onSaveAnswers(answers);
              }
            }}
          />
        ))}
      </div>

      <div className="w-full items-start flex flex-col gap-3 self-end justify-end flex-1 md:flex-auto  md:mt-[200px] md:flex-row-reverse pb-8">
        <button
          className="px-5 py-1.5 text-base font-medium !text-[#6a7282] cursor-pointer border border-[#6a7282]/50 rounded w-full md:w-[165px] items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={() => {
            onSaveAnswers([]); // Pass an empty array to skip questions
          }}
          disabled={remainingResearches <= 0}
        >
          Skip
        </button>

        <div className="flex flex-col gap-2 w-full md:w-fit">
          <button
            className="flex flex-col justify-between items-center w-full md:w-[165px] h-[38px] overflow-hidden px-5 py-1.5 rounded bg-[#072d77] border border-[#072d77] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => {
              if (userId) {
                onSaveAnswers(answers);
              }
            }}
            disabled={remainingResearches <= 0}
          >
            <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-1.5">
              <p className="flex-grow-0 flex-shrink-0 text-base font-medium text-left text-white">
                Generate Report
              </p>
            </div>
          </button>
          <TooltipUsage
            remainingResearches={remainingResearches}
            resetTime={resetTime}
          />
        </div>
      </div>
    </div>
  );
};



================================================
FILE: src/components/app/reportLoading/ReportSteps.tsx
================================================
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

export type ReportStepType = {
  id: string;
  title: string;
  status: "pending" | "completed" | "loading";
};

export const ReportSteps = ({
  chatId,
  researchStartedAt,
  steps,
}: {
  chatId: string;
  researchStartedAt: Date;
  steps: ReportStepType[];
}) => {
  const router = useRouter();
  const [isCanceling, setIsCanceling] = useState(false);
  const [elapsedSeconds, setElapsedSeconds] = useState<number>(0);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setElapsedSeconds(
        Math.floor((Date.now() - researchStartedAt.getTime()) / 1000)
      );
    }, 1000);

    return () => clearInterval(intervalId);
  }, [researchStartedAt]);

  const onCancel = async () => {
    setIsCanceling(true);
    await fetch("/api/cancel", {
      method: "POST",
      body: JSON.stringify({ chatId: chatId }),
    });
    router.replace("/");
    router.refresh();
    setIsCanceling(false);
  };

  return (
    <div className="flex flex-col overflow-hidden rounded-lg bg-white border-[0.7px] border-[#d1d5dc] md:min-w-[206px] h-fit md:sticky z-[20] md:top-5">
      <div className="flex-shrink-0 h-[68px] p-4 flex flex-col justify-center border-b-[0.7px] border-[#d1d5dc]">
        <p className="text-base font-medium text-left text-[#101828]">
          Generating Report
        </p>
        <p className="text-xs text-left text-[#6a7282]">
          <span className="text-sm font-light text-left text-[#6a7282]">
            Time elapsed:
          </span>
          <span className="text-sm text-left text-[#6a7282] ml-1">
            {elapsedSeconds < 120
              ? `${elapsedSeconds}s ago`
              : `${Math.floor(elapsedSeconds / 60)}m ago`}
          </span>
        </p>
      </div>
      <div className="flex flex-col px-2 py-3">
        {steps.map((step) => (
          <div
            key={step.title}
            className="flex items-center gap-2 px-2 py-2.5 rounded"
          >
            <img
              src={
                step.status === "completed"
                  ? "/reportStep/completed.svg"
                  : step.status === "loading"
                  ? "/reportStep/loading.svg"
                  : "/reportStep/pending.svg"
              }
              className={cn(
                "size-3",
                step.status === "loading" ? "animate-spin" : ""
              )}
              alt={`${step.status} icon`}
            />
            <p
              className={`text-sm text-left ${
                step.status === "pending" ? "text-[#d1d5dc]" : ""
              }`}
            >
              {step.title}
            </p>
          </div>
        ))}
      </div>

      <button
        disabled={isCanceling}
        onClick={onCancel}
        className="px-4 py-3 text-sm font-light text-left text-[#826a6a] cursor-pointer"
      >
        {isCanceling ? <>Cancelling search...</> : <>Cancel search</>}
      </button>
    </div>
  );
};



================================================
FILE: src/components/app/reportLoading/StreamingReportBody.tsx
================================================
import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ReportBody } from "../ReportBody";

function splitIntoSentences(text: string): string[] {
  // This regex splits on sentence-ending punctuation followed by a space or end of string
  return text.match(/[^.!?\n]+[.!?\n]+|[^.!?\n]+$/g) || [];
}

export const StreamingReportBody = ({
  report,
  coverUrl,
  researchTopic,
}: {
  report: string;
  coverUrl: string;
  researchTopic: string;
}) => {
  const [displayedSentences, setDisplayedSentences] = useState<string[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const sentences = report ? splitIntoSentences(report) : [];
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // When report changes, stream in new sentences
  useEffect(() => {
    if (!report) {
      setDisplayedSentences([]);
      setCurrentIndex(0);
      return;
    }
    if (currentIndex >= sentences.length) return;
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => {
        if (prev < sentences.length) {
          setDisplayedSentences(sentences.slice(0, prev + 1));
          return prev + 1;
        } else {
          if (intervalRef.current) clearInterval(intervalRef.current);
          return prev;
        }
      });
    }, 400); // Adjust speed here for sentences
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [report, sentences.length]);

  // If report shrinks (shouldn't happen, but for safety)
  useEffect(() => {
    if (currentIndex > sentences.length) {
      setCurrentIndex(sentences.length);
      setDisplayedSentences(sentences);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sentences.length]);

  // Compose the currently streamed report string
  const streamedReport = displayedSentences.join("");

  return (
    <div className="w-full">
      <ReportBody
        researchData={{
          researchTopic,
          coverUrl,
          report: streamedReport,
          sources: [],
          citations: [],
        }}
      />
    </div>
  );
};



================================================
FILE: src/components/app/reportLoading/TimelineEvent.tsx
================================================
import { ResearchEventStreamEvents } from "@/app/api/research/route";
import { motion } from "framer-motion";
import { ReactNode } from "react";
import { WebResultCard } from "@/components/app/reportLoading/WebResultCard";
import { cn } from "@/lib/utils";

export const TimelineEvent = ({
  isLast,
  type,
  title,
  description,
  queries,
  webResults,
}: {
  type: ResearchEventStreamEvents["type"];
  isLast: boolean;
  title: ReactNode;
  description?: string;
  queries?: string[];
  webResults?: {
    url: string;
    title: string;
  }[];
}) => {
  const getStatusIcon = ({
    type,
    isLast,
  }: {
    type: ResearchEventStreamEvents["type"];
    isLast: boolean;
  }) => {
    if (isLast) {
      return (
        <img src="/timeline/loading.svg" className="size-[10px] animate-spin" />
      );
    }

    switch (type) {
      case "search_completed":
        return (
          <img src="/timeline/search.svg" alt="" className="size-[12px]" />
        );
      default:
        return <img src="/timeline/completed.svg" className="size-[10px]" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        delay: isLast ? 0.2 : 0,
      }}
      className="relative flex gap-3 pb-6 last:pb-0"
    >
      {/* Status indicator */}
      <div className="flex flex-shrink-0 relative z-10 size-5 min-w-[20px] bg-[#F3F4F6] rounded-full border border-[#101828]flex items-center justify-center">
        {getStatusIcon({
          type: type,
          isLast: isLast,
        })}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0 pl-3">
        <div className="flex items-center gap-2 mb-2">
          <h3 className="text-sm text-[#101828] font-medium">{title}</h3>
        </div>

        {description && (
          <div
            className={cn(
              "relative max-h-40 overflow-hidden",
              description && description.length > 100
                ? "after:content-[''] after:absolute after:inset-x-0 after:bottom-0 after:h-16 after:bg-gradient-to-b after:from-transparent after:to-white"
                : ""
            )}
          >
            <p className="text-[#6a7282] text-sm font-light mb-2 leading-relaxed bg-gradient-to-r from-[#6a7282] to-[#4a5565] bg-clip-text">
              {description}
            </p>
          </div>
        )}

        {queries && (
          <div className="mb-2 flex fle-row flex-wrap gap-2">
            {queries.map((query, idx) => (
              <div
                key={idx}
                className="flex justify-center items-center relative gap-2.5 px-2.5 py-[5px] rounded bg-gray-200"
              >
                <p className=" text-xs text-left text-[#4a5565]">{query}</p>
              </div>
            ))}
          </div>
        )}

        {webResults && (
          <div className="flex flex-col md:grid grid-cols-2 gap-3 mb-2">
            {webResults.map((result, idx) => (
              <WebResultCard
                key={result.url + "-" + idx}
                result={result}
                id={result.url + "-" + idx}
              />
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};



================================================
FILE: src/components/app/reportLoading/TimelineEventLoader.tsx
================================================
import React from "react";
import { motion } from "framer-motion";

export const TimelineEventLoader = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative flex gap-3 pb-8"
    >
      <div className="flex-shrink-0 size-5 rounded-full border-[0.7px] bg-[#F3F4F6] border-[#D1D5DC] flex items-center justify-center relative z-10">
        <img src="/timeline/loading.svg" className="size-[10px] animate-spin" />
      </div>
      <div className="flex-1 min-w-0 pl-1">
        <div className="h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="h-3 bg-gray-100 rounded animate-pulse w-3/4" />
      </div>
    </motion.div>
  );
};



================================================
FILE: src/components/app/reportLoading/TimelineProgress.tsx
================================================
"use client";

import { Fragment, useEffect, useMemo, useRef } from "react";
import { AnimatePresence } from "framer-motion";
import { ResearchEventStreamEvents } from "@/app/api/research/route";
import { TimelineEvent } from "./TimelineEvent";
import { TimelineEventLoader } from "./TimelineEventLoader";
import { cleanMarkdownToText } from "@/lib/utils";

export default function TimelineProgress({
  events,
}: {
  events: ResearchEventStreamEvents[];
}) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new items are added
  const scrollToBottom = () => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  };

  // Auto-scroll when items change
  useEffect(() => {
    if (events.length > 2) {
      setTimeout(scrollToBottom, 100);
    }
  }, [events.length]);

  const filteredEvents: ResearchEventStreamEvents[] = useMemo(() => {
    // only keep the last report_generating event
    const reportEvents = events.filter((e) => e.type === "report_generating");
    const lastReportEvent = reportEvents[reportEvents.length - 1];

    return events.filter((e) => {
      if (e.type === "report_generating") {
        return e.timestamp === lastReportEvent.timestamp;
      }
      return true;
    });
  }, [events]);

  const mapOfUrlsToContentProcessing = useMemo(() => {
    const map = new Map<
      string,
      {
        url: string;
        title: string;
        content: string;
      }
    >();
    for (const event of filteredEvents) {
      if (event.type === "content_processing") {
        map.set(event.url, {
          url: event.url,
          title: event.title,
          content: event.content || "",
        });
      }
    }
    return map;
  }, [filteredEvents]);

  if (filteredEvents.length <= 1) {
    return null;
  }

  return (
    <div className="max-w-2xl mx-auto px-6 pt-8 md:pt-0 w-full">
      <div
        ref={scrollContainerRef}
        className="overflow-y-auto rounded-lg bg-white"
      >
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-[9px] top-[1px] bottom-0 w-[1px] bg-[#D1D5DC]" />

          <AnimatePresence>
            {filteredEvents

              .map((event, index) => {
                const isLast = index === filteredEvents.length - 1;

                switch (event.type) {
                  case "planning_started":
                    return (
                      <TimelineEvent
                        key={index}
                        type={event.type}
                        isLast={isLast}
                        title="Research Topic"
                        description={event.topic}
                      />
                    );

                  case "planning_completed":
                    return (
                      <Fragment key={index}>
                        <TimelineEvent
                          type={event.type}
                          isLast={false}
                          title="Research Plan"
                          description={cleanMarkdownToText(event.plan)}
                        />
                        <TimelineEvent
                          type={event.type}
                          isLast={isLast}
                          title="Generated Search Queries"
                          queries={event.queries}
                        />
                      </Fragment>
                    );

                  case "search_completed":
                    return (
                      <TimelineEvent
                        key={index}
                        type={event.type}
                        isLast={isLast}
                        title={
                          <>
                            <span className="text-sm font-light text-left text-[#6a7282]">
                              Searched for
                            </span>
                            <span className="ml-1 text-sm text-left text-[#4a5565]">
                              ‘{event.query}‘
                            </span>
                          </>
                        }
                        webResults={event.urls.map((url) => {
                          return {
                            url: url,
                            title: mapOfUrlsToContentProcessing.has(url)
                              ? mapOfUrlsToContentProcessing.get(url)?.title ||
                                ""
                              : "Loading...",
                          };
                        })}
                      />
                    );

                  case "content_summarized":
                    return (
                      <TimelineEvent
                        key={index}
                        type={event.type}
                        isLast={isLast}
                        title={
                          <>
                            <span className="text-sm font-light text-left text-[#6a7282]">
                              Summarized
                            </span>
                            <span className="ml-1 text-sm text-left text-[#4a5565]">
                              ‘{event.title}‘
                            </span>
                          </>
                        }
                      />
                    );

                  case "evaluation_completed":
                    return (
                      <Fragment key={index}>
                        <TimelineEvent
                          type={event.type}
                          isLast={false}
                          title="Evaluation Complete"
                          description={
                            cleanMarkdownToText(event.reasoning)?.slice(
                              0,
                              400
                            ) + "..." || ""
                          }
                        />
                        <TimelineEvent
                          key={index}
                          type={event.type}
                          isLast={isLast}
                          title="Additional Search Queries"
                          queries={event.additionalQueries}
                        />
                      </Fragment>
                    );

                  case "report_generating":
                    return (
                      <TimelineEvent
                        key={index}
                        type={event.type}
                        isLast={isLast}
                        title="Writing report..."
                        description={cleanMarkdownToText(event.partialReport)}
                      />
                    );

                  default:
                    return null;
                }
              })
              .filter(Boolean)}
          </AnimatePresence>

          <TimelineEventLoader />

          {/* Invisible element to scroll to */}
          <div ref={bottomRef} />
        </div>
      </div>
    </div>
  );
}



================================================
FILE: src/components/app/reportLoading/WebResultCard.tsx
================================================
import { FaviconImage } from "@/components/FaviconImage";
import { getDomainFromUrl } from "@/lib/utils";
import React from "react";

export const WebResultCard = ({
  result,
  id,
  children,
}: {
  result: { url: string; title: string };
  id: string;
  children?: React.ReactNode;
}) => {
  return (
    <a
      key={id}
      onClick={(e) => e.stopPropagation()}
      href={result.url}
      target="_blank"
      rel="noreferrer"
      className="flex justify-start items-center w-full overflow-hidden gap-3 px-4 py-3 rounded-lg bg-gray-50 border-[0.7px] border-gray-200"
    >
      <div className="flex flex-col justify-start items-start  relative gap-1 overflow-hidden">
        <div className="flex flex-row gap-2">
          {children}
          <p className="max-w-full truncate text-xs text-left text-[#4a5565]">
            {result.title}
          </p>
        </div>
        <div className="flex justify-start items-center  relative gap-1">
          <div className="flex justify-start items-center  relative gap-1.5">
            <div className="max-w-3.5 min-w-3.5 h-3.5 relative overflow-hidden rounded bg-gray-100">
              <FaviconImage
                url={result.url}
                className="w-2.5 h-2.5 absolute left-px top-px object-none"
              />
            </div>
            <p className=" text-xs font-light text-left text-[#99a1af]">
              {getDomainFromUrl(result.url)}
            </p>
          </div>
          <div className="p-2">
            <img src="/timeline/link.svg" alt="" className="size-3" />
          </div>
        </div>
      </div>
    </a>
  );
};



================================================
FILE: src/components/app/tooltip/TooltipUsage.tsx
================================================
"use client";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { intervalToDuration, formatDuration } from "date-fns";

export const TooltipUsage = ({
  remainingResearches,
  resetTime,
}: {
  remainingResearches: number;
  resetTime: string | null;
}) => {
  if (!resetTime) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger>
        <div className="relative flex flex-row items-center justify-center">
          <img src="/tooltip.svg" alt="" className="size-4 mr-[1px]" />
          <span className="text-xs text-[#6a7282]">
            Researches left: <strong>{remainingResearches}</strong>
          </span>
        </div>
      </PopoverTrigger>
      <PopoverContent className="!p-0 !border-0 !bg-transparent !w-fit">
        <div className="mb-2 w-[235px] overflow-hidden rounded bg-gray-50 border border-[#d1d5dc] z-10 p-3">
          <p className="text-sm text-left text-[#1e2939]">
            <span className="whitespace-nowrap">
              Credits will reset in{" "}
              <span className="text-sm font-bold text-left text-[#1e2939]">
                {formatDuration(
                  intervalToDuration({
                    start: new Date(),
                    end: new Date(resetTime),
                  }),
                  {
                    format: ["hours", "minutes"],
                    zero: true,
                    delimiter: ":",
                    locale: {
                      formatDistance: (token, count) => {
                        if (token === "xHours") return `${count}h`;
                        if (token === "xMinutes") return `${count}m`;
                        return "";
                      },
                    },
                  }
                )}
              </span>
              .
            </span>
            <br />
            <span className="">
              Enter your{" "}
              <a
                href="https://togetherai.link/"
                target="_blank"
                rel="noreferrer"
                className="underline"
              >
                Together API key
              </a>{" "}
              for infinite uses!
            </span>
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
};



================================================
FILE: src/components/ui/accordion.tsx
================================================
"use client"

import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { ChevronDownIcon } from "lucide-react"

import { cn } from "@/lib/utils"

function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" {...props} />
}

function AccordionItem({
  className,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn("border-b last:border-b-0", className)}
      {...props}
    />
  )
}

function AccordionTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        data-slot="accordion-trigger"
        className={cn(
          "focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",
          className
        )}
        {...props}
      >
        {children}
        <ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200" />
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  )
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn("pt-0 pb-4", className)}>{children}</div>
    </AccordionPrimitive.Content>
  )
}

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }



================================================
FILE: src/components/ui/badge.tsx
================================================
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
        destructive:
          "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span"

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }



================================================
FILE: src/components/ui/button.tsx
================================================
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost:
          "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };



================================================
FILE: src/components/ui/input.tsx
================================================
import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }



================================================
FILE: src/components/ui/popover.tsx
================================================
"use client"

import * as React from "react"
import * as PopoverPrimitive from "@radix-ui/react-popover"

import { cn } from "@/lib/utils"

function Popover({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Root>) {
  return <PopoverPrimitive.Root data-slot="popover" {...props} />
}

function PopoverTrigger({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {
  return <PopoverPrimitive.Trigger data-slot="popover-trigger" {...props} />
}

function PopoverContent({
  className,
  align = "center",
  sideOffset = 4,
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Content>) {
  return (
    <PopoverPrimitive.Portal>
      <PopoverPrimitive.Content
        data-slot="popover-content"
        align={align}
        sideOffset={sideOffset}
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",
          className
        )}
        {...props}
      />
    </PopoverPrimitive.Portal>
  )
}

function PopoverAnchor({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {
  return <PopoverPrimitive.Anchor data-slot="popover-anchor" {...props} />
}

export { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }



================================================
FILE: src/components/ui/scroll-area.tsx
================================================
"use client"

import * as React from "react"
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area"

import { cn } from "@/lib/utils"

function ScrollArea({
  className,
  children,
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {
  return (
    <ScrollAreaPrimitive.Root
      data-slot="scroll-area"
      className={cn("relative", className)}
      {...props}
    >
      <ScrollAreaPrimitive.Viewport
        data-slot="scroll-area-viewport"
        className="focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1"
      >
        {children}
      </ScrollAreaPrimitive.Viewport>
      <ScrollBar />
      <ScrollAreaPrimitive.Corner />
    </ScrollAreaPrimitive.Root>
  )
}

function ScrollBar({
  className,
  orientation = "vertical",
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {
  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar
      data-slot="scroll-area-scrollbar"
      orientation={orientation}
      className={cn(
        "flex touch-none p-px transition-colors select-none",
        orientation === "vertical" &&
          "h-full w-2.5 border-l border-l-transparent",
        orientation === "horizontal" &&
          "h-2.5 flex-col border-t border-t-transparent",
        className
      )}
      {...props}
    >
      <ScrollAreaPrimitive.ScrollAreaThumb
        data-slot="scroll-area-thumb"
        className="bg-border relative flex-1 rounded-full"
      />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  )
}

export { ScrollArea, ScrollBar }



================================================
FILE: src/components/ui/separator.tsx
================================================
"use client"

import * as React from "react"
import * as SeparatorPrimitive from "@radix-ui/react-separator"

import { cn } from "@/lib/utils"

function Separator({
  className,
  orientation = "horizontal",
  decorative = true,
  ...props
}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {
  return (
    <SeparatorPrimitive.Root
      data-slot="separator"
      decorative={decorative}
      orientation={orientation}
      className={cn(
        "bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",
        className
      )}
      {...props}
    />
  )
}

export { Separator }



================================================
FILE: src/components/ui/sheet.tsx
================================================
"use client"

import * as React from "react"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"

import { cn } from "@/lib/utils"

function Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {
  return <SheetPrimitive.Root data-slot="sheet" {...props} />
}

function SheetTrigger({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {
  return <SheetPrimitive.Trigger data-slot="sheet-trigger" {...props} />
}

function SheetClose({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Close>) {
  return <SheetPrimitive.Close data-slot="sheet-close" {...props} />
}

function SheetPortal({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Portal>) {
  return <SheetPrimitive.Portal data-slot="sheet-portal" {...props} />
}

function SheetOverlay({
  className,
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {
  return (
    <SheetPrimitive.Overlay
      data-slot="sheet-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  )
}

function SheetContent({
  className,
  children,
  side = "right",
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Content> & {
  side?: "top" | "right" | "bottom" | "left"
}) {
  return (
    <SheetPortal>
      <SheetOverlay />
      <SheetPrimitive.Content
        data-slot="sheet-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
          side === "right" &&
            "data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",
          side === "left" &&
            "data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",
          side === "top" &&
            "data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",
          side === "bottom" &&
            "data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",
          className
        )}
        {...props}
      >
        {children}
        <SheetPrimitive.Close className="ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none">
          <XIcon className="size-4" />
          <span className="sr-only">Close</span>
        </SheetPrimitive.Close>
      </SheetPrimitive.Content>
    </SheetPortal>
  )
}

function SheetHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sheet-header"
      className={cn("flex flex-col gap-1.5 p-4", className)}
      {...props}
    />
  )
}

function SheetFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sheet-footer"
      className={cn("mt-auto flex flex-col gap-2 p-4", className)}
      {...props}
    />
  )
}

function SheetTitle({
  className,
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Title>) {
  return (
    <SheetPrimitive.Title
      data-slot="sheet-title"
      className={cn("text-foreground font-semibold", className)}
      {...props}
    />
  )
}

function SheetDescription({
  className,
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Description>) {
  return (
    <SheetPrimitive.Description
      data-slot="sheet-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

export {
  Sheet,
  SheetTrigger,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetFooter,
  SheetTitle,
  SheetDescription,
}



================================================
FILE: src/components/ui/sidebar.tsx
================================================
"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, VariantProps } from "class-variance-authority"
import { PanelLeftIcon } from "lucide-react"

import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const SIDEBAR_COOKIE_NAME = "sidebar_state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7
const SIDEBAR_WIDTH = "16rem"
const SIDEBAR_WIDTH_MOBILE = "18rem"
const SIDEBAR_WIDTH_ICON = "3rem"
const SIDEBAR_KEYBOARD_SHORTCUT = "b"

type SidebarContextProps = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

const SidebarContext = React.createContext<SidebarContextProps | null>(null)

function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}

function SidebarProvider({
  defaultOpen = true,
  open: openProp,
  onOpenChange: setOpenProp,
  className,
  style,
  children,
  ...props
}: React.ComponentProps<"div"> & {
  defaultOpen?: boolean
  open?: boolean
  onOpenChange?: (open: boolean) => void
}) {
  const isMobile = useIsMobile()
  const [openMobile, setOpenMobile] = React.useState(false)

  // This is the internal state of the sidebar.
  // We use openProp and setOpenProp for control from outside the component.
  const [_open, _setOpen] = React.useState(defaultOpen)
  const open = openProp ?? _open
  const setOpen = React.useCallback(
    (value: boolean | ((value: boolean) => boolean)) => {
      const openState = typeof value === "function" ? value(open) : value
      if (setOpenProp) {
        setOpenProp(openState)
      } else {
        _setOpen(openState)
      }

      // This sets the cookie to keep the sidebar state.
      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
    },
    [setOpenProp, open]
  )

  // Helper to toggle the sidebar.
  const toggleSidebar = React.useCallback(() => {
    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)
  }, [isMobile, setOpen, setOpenMobile])

  // Adds a keyboard shortcut to toggle the sidebar.
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&
        (event.metaKey || event.ctrlKey)
      ) {
        event.preventDefault()
        toggleSidebar()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [toggleSidebar])

  // We add a state so that we can do data-state="expanded" or "collapsed".
  // This makes it easier to style the sidebar with Tailwind classes.
  const state = open ? "expanded" : "collapsed"

  const contextValue = React.useMemo<SidebarContextProps>(
    () => ({
      state,
      open,
      setOpen,
      isMobile,
      openMobile,
      setOpenMobile,
      toggleSidebar,
    }),
    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]
  )

  return (
    <SidebarContext.Provider value={contextValue}>
      <TooltipProvider delayDuration={0}>
        <div
          data-slot="sidebar-wrapper"
          style={
            {
              "--sidebar-width": SIDEBAR_WIDTH,
              "--sidebar-width-icon": SIDEBAR_WIDTH_ICON,
              ...style,
            } as React.CSSProperties
          }
          className={cn(
            "group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",
            className
          )}
          {...props}
        >
          {children}
        </div>
      </TooltipProvider>
    </SidebarContext.Provider>
  )
}

function Sidebar({
  side = "left",
  variant = "sidebar",
  collapsible = "offcanvas",
  className,
  children,
  ...props
}: React.ComponentProps<"div"> & {
  side?: "left" | "right"
  variant?: "sidebar" | "floating" | "inset"
  collapsible?: "offcanvas" | "icon" | "none"
}) {
  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()

  if (collapsible === "none") {
    return (
      <div
        data-slot="sidebar"
        className={cn(
          "bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }

  if (isMobile) {
    return (
      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>
        <SheetContent
          data-sidebar="sidebar"
          data-slot="sidebar"
          data-mobile="true"
          className="bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden"
          style={
            {
              "--sidebar-width": SIDEBAR_WIDTH_MOBILE,
            } as React.CSSProperties
          }
          side={side}
        >
          <SheetHeader className="sr-only">
            <SheetTitle>Sidebar</SheetTitle>
            <SheetDescription>Displays the mobile sidebar.</SheetDescription>
          </SheetHeader>
          <div className="flex h-full w-full flex-col">{children}</div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <div
      className="group peer text-sidebar-foreground hidden md:block"
      data-state={state}
      data-collapsible={state === "collapsed" ? collapsible : ""}
      data-variant={variant}
      data-side={side}
      data-slot="sidebar"
    >
      {/* This is what handles the sidebar gap on desktop */}
      <div
        data-slot="sidebar-gap"
        className={cn(
          "relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear",
          "group-data-[collapsible=offcanvas]:w-0",
          "group-data-[side=right]:rotate-180",
          variant === "floating" || variant === "inset"
            ? "group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]"
            : "group-data-[collapsible=icon]:w-(--sidebar-width-icon)"
        )}
      />
      <div
        data-slot="sidebar-container"
        className={cn(
          "fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",
          side === "left"
            ? "left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]"
            : "right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",
          // Adjust the padding for floating and inset variants.
          variant === "floating" || variant === "inset"
            ? "p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]"
            : "group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",
          className
        )}
        {...props}
      >
        <div
          data-sidebar="sidebar"
          data-slot="sidebar-inner"
          className="bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm"
        >
          {children}
        </div>
      </div>
    </div>
  )
}

function SidebarTrigger({
  className,
  onClick,
  ...props
}: React.ComponentProps<typeof Button>) {
  const { toggleSidebar } = useSidebar()

  return (
    <Button
      data-sidebar="trigger"
      data-slot="sidebar-trigger"
      variant="ghost"
      size="icon"
      className={cn("size-7", className)}
      onClick={(event) => {
        onClick?.(event)
        toggleSidebar()
      }}
      {...props}
    >
      <PanelLeftIcon />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  )
}

function SidebarRail({ className, ...props }: React.ComponentProps<"button">) {
  const { toggleSidebar } = useSidebar()

  return (
    <button
      data-sidebar="rail"
      data-slot="sidebar-rail"
      aria-label="Toggle Sidebar"
      tabIndex={-1}
      onClick={toggleSidebar}
      title="Toggle Sidebar"
      className={cn(
        "hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex",
        "in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize",
        "[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize",
        "hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full",
        "[[data-side=left][data-collapsible=offcanvas]_&]:-right-2",
        "[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",
        className
      )}
      {...props}
    />
  )
}

function SidebarInset({ className, ...props }: React.ComponentProps<"main">) {
  return (
    <main
      data-slot="sidebar-inset"
      className={cn(
        "bg-background relative flex w-full flex-1 flex-col",
        "md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",
        className
      )}
      {...props}
    />
  )
}

function SidebarInput({
  className,
  ...props
}: React.ComponentProps<typeof Input>) {
  return (
    <Input
      data-slot="sidebar-input"
      data-sidebar="input"
      className={cn("bg-background h-8 w-full shadow-none", className)}
      {...props}
    />
  )
}

function SidebarHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-header"
      data-sidebar="header"
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
}

function SidebarFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-footer"
      data-sidebar="footer"
      className={cn("flex flex-col gap-2 p-2", className)}
      {...props}
    />
  )
}

function SidebarSeparator({
  className,
  ...props
}: React.ComponentProps<typeof Separator>) {
  return (
    <Separator
      data-slot="sidebar-separator"
      data-sidebar="separator"
      className={cn("bg-sidebar-border mx-2 w-auto", className)}
      {...props}
    />
  )
}

function SidebarContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-content"
      data-sidebar="content"
      className={cn(
        "flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",
        className
      )}
      {...props}
    />
  )
}

function SidebarGroup({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-group"
      data-sidebar="group"
      className={cn("relative flex w-full min-w-0 flex-col p-2", className)}
      {...props}
    />
  )
}

function SidebarGroupLabel({
  className,
  asChild = false,
  ...props
}: React.ComponentProps<"div"> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "div"

  return (
    <Comp
      data-slot="sidebar-group-label"
      data-sidebar="group-label"
      className={cn(
        "text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        "group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",
        className
      )}
      {...props}
    />
  )
}

function SidebarGroupAction({
  className,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="sidebar-group-action"
      data-sidebar="group-action"
      className={cn(
        "text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
}

function SidebarGroupContent({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-group-content"
      data-sidebar="group-content"
      className={cn("w-full text-sm", className)}
      {...props}
    />
  )
}

function SidebarMenu({ className, ...props }: React.ComponentProps<"ul">) {
  return (
    <ul
      data-slot="sidebar-menu"
      data-sidebar="menu"
      className={cn("flex w-full min-w-0 flex-col gap-1", className)}
      {...props}
    />
  )
}

function SidebarMenuItem({ className, ...props }: React.ComponentProps<"li">) {
  return (
    <li
      data-slot="sidebar-menu-item"
      data-sidebar="menu-item"
      className={cn("group/menu-item relative", className)}
      {...props}
    />
  )
}

const sidebarMenuButtonVariants = cva(
  "peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
        outline:
          "bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]",
      },
      size: {
        default: "h-8 text-sm",
        sm: "h-7 text-xs",
        lg: "h-12 text-sm group-data-[collapsible=icon]:p-0!",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function SidebarMenuButton({
  asChild = false,
  isActive = false,
  variant = "default",
  size = "default",
  tooltip,
  className,
  ...props
}: React.ComponentProps<"button"> & {
  asChild?: boolean
  isActive?: boolean
  tooltip?: string | React.ComponentProps<typeof TooltipContent>
} & VariantProps<typeof sidebarMenuButtonVariants>) {
  const Comp = asChild ? Slot : "button"
  const { isMobile, state } = useSidebar()

  const button = (
    <Comp
      data-slot="sidebar-menu-button"
      data-sidebar="menu-button"
      data-size={size}
      data-active={isActive}
      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}
      {...props}
    />
  )

  if (!tooltip) {
    return button
  }

  if (typeof tooltip === "string") {
    tooltip = {
      children: tooltip,
    }
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>{button}</TooltipTrigger>
      <TooltipContent
        side="right"
        align="center"
        hidden={state !== "collapsed" || isMobile}
        {...tooltip}
      />
    </Tooltip>
  )
}

function SidebarMenuAction({
  className,
  asChild = false,
  showOnHover = false,
  ...props
}: React.ComponentProps<"button"> & {
  asChild?: boolean
  showOnHover?: boolean
}) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="sidebar-menu-action"
      data-sidebar="menu-action"
      className={cn(
        "text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0",
        // Increases the hit area of the button on mobile.
        "after:absolute after:-inset-2 md:after:hidden",
        "peer-data-[size=sm]/menu-button:top-1",
        "peer-data-[size=default]/menu-button:top-1.5",
        "peer-data-[size=lg]/menu-button:top-2.5",
        "group-data-[collapsible=icon]:hidden",
        showOnHover &&
          "peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",
        className
      )}
      {...props}
    />
  )
}

function SidebarMenuBadge({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="sidebar-menu-badge"
      data-sidebar="menu-badge"
      className={cn(
        "text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none",
        "peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground",
        "peer-data-[size=sm]/menu-button:top-1",
        "peer-data-[size=default]/menu-button:top-1.5",
        "peer-data-[size=lg]/menu-button:top-2.5",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
}

function SidebarMenuSkeleton({
  className,
  showIcon = false,
  ...props
}: React.ComponentProps<"div"> & {
  showIcon?: boolean
}) {
  // Random width between 50 to 90%.
  const width = React.useMemo(() => {
    return `${Math.floor(Math.random() * 40) + 50}%`
  }, [])

  return (
    <div
      data-slot="sidebar-menu-skeleton"
      data-sidebar="menu-skeleton"
      className={cn("flex h-8 items-center gap-2 rounded-md px-2", className)}
      {...props}
    >
      {showIcon && (
        <Skeleton
          className="size-4 rounded-md"
          data-sidebar="menu-skeleton-icon"
        />
      )}
      <Skeleton
        className="h-4 max-w-(--skeleton-width) flex-1"
        data-sidebar="menu-skeleton-text"
        style={
          {
            "--skeleton-width": width,
          } as React.CSSProperties
        }
      />
    </div>
  )
}

function SidebarMenuSub({ className, ...props }: React.ComponentProps<"ul">) {
  return (
    <ul
      data-slot="sidebar-menu-sub"
      data-sidebar="menu-sub"
      className={cn(
        "border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
}

function SidebarMenuSubItem({
  className,
  ...props
}: React.ComponentProps<"li">) {
  return (
    <li
      data-slot="sidebar-menu-sub-item"
      data-sidebar="menu-sub-item"
      className={cn("group/menu-sub-item relative", className)}
      {...props}
    />
  )
}

function SidebarMenuSubButton({
  asChild = false,
  size = "md",
  isActive = false,
  className,
  ...props
}: React.ComponentProps<"a"> & {
  asChild?: boolean
  size?: "sm" | "md"
  isActive?: boolean
}) {
  const Comp = asChild ? Slot : "a"

  return (
    <Comp
      data-slot="sidebar-menu-sub-button"
      data-sidebar="menu-sub-button"
      data-size={size}
      data-active={isActive}
      className={cn(
        "text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",
        "data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",
        size === "sm" && "text-xs",
        size === "md" && "text-sm",
        "group-data-[collapsible=icon]:hidden",
        className
      )}
      {...props}
    />
  )
}

export {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupAction,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarInset,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSkeleton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar,
}



================================================
FILE: src/components/ui/skeleton.tsx
================================================
import { cn } from "@/lib/utils"

function Skeleton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="skeleton"
      className={cn("bg-accent animate-pulse rounded-md", className)}
      {...props}
    />
  )
}

export { Skeleton }



================================================
FILE: src/components/ui/tooltip.tsx
================================================
"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  )
}

function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  )
}

function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />
}

function TooltipContent({
  className,
  sideOffset = 0,
  children,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>) {
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",
          className
        )}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  )
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }



================================================
FILE: src/db/action.ts
================================================
"use server";

import { eq } from "drizzle-orm";
import { db } from "./index";
import { research } from "./schema";
import { redirect } from "next/navigation";
import { startResearch } from "@/deepresearch/startResearch";

export const createResearch = async ({
  clerkUserId,
  initialUserMessage,
}: {
  clerkUserId?: string;
  initialUserMessage: string;
}) => {
  const [result] = await db
    .insert(research)
    .values({
      clerkUserId,
      initialUserMessage,
    })
    .returning();
  return result.id;
};

export const getResearches = async () => {
  const c = await db.select().from(research);
  return c;
};

export const getResearch = async (id: string) => {
  const result = await db
    .select()
    .from(research)
    .where(eq(research.id, id))
    .limit(1);
  return result.length > 0 ? result[0] : null;
};

export const deleteResearch = async (chatId: string) => {
  await db.delete(research).where(eq(research.id, chatId));
};

export async function createResearchAndRedirect({
  clerkUserId,
  initialUserMessage,
}: {
  clerkUserId?: string;
  initialUserMessage: string;
}) {
  const id = await createResearch({
    clerkUserId,
    initialUserMessage,
  });
  redirect(`/chat/${id}`);
}

export const skipQuestions = async ({
  chatId,
  togetherApiKey,
}: {
  chatId: string;
  togetherApiKey?: string;
}) => {
  await db
    .update(research)
    .set({
      answers: [],
    })
    .where(eq(research.id, chatId));
  await startResearch({ chatId, personalTogetherApiKey: togetherApiKey });
};

export const storeAnswers = async ({
  chatId,
  answers,
  togetherApiKey,
}: {
  chatId: string;
  answers: string[];
  togetherApiKey?: string;
}) => {
  await db
    .update(research)
    .set({
      answers: answers,
    })
    .where(eq(research.id, chatId));
  await startResearch({ chatId, personalTogetherApiKey: togetherApiKey });
};



================================================
FILE: src/db/index.ts
================================================
import { drizzle } from "drizzle-orm/neon-http";

export const db = drizzle(process.env.DATABASE_URL!);



================================================
FILE: src/db/schema.ts
================================================
import {
  jsonb,
  pgEnum,
  pgTable,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { customAlphabet } from "nanoid";

const nanoid = customAlphabet(
  "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
);

export const deepresearchStautsEnum = pgEnum("status", [
  "questions",
  "pending",
  "processing",
  "completed",
]);

export const research = pgTable("chats", {
  id: varchar()
    .primaryKey()
    .$defaultFn(() => nanoid()),
  clerkUserId: varchar(),
  // message prompt from the user in landing page
  initialUserMessage: varchar().notNull(),
  // generated questions based on the user prompt
  questions: jsonb().$type<string[]>(),
  // answers given from the user or empty array if skipped
  answers: jsonb().$type<string[]>(),
  // research topic
  researchTopic: varchar(),

  researchStartedAt: timestamp(),
  status: deepresearchStautsEnum().notNull().default("questions"),
  title: varchar(), // title of the research extracted from the report markdown
  report: varchar(), // markdown of the report
  completedAt: timestamp(), // timestamp when the research is completed
  coverUrl: varchar(), // url of the cover image generated with flux

  sources: jsonb().$type<
    {
      url: string;
      title: string;
    }[]
  >(), // urls of the sources used to generate the report

  createdAt: timestamp().defaultNow().notNull(),
});



================================================
FILE: src/deepresearch/apiClients.ts
================================================
import { createTogetherAI } from "@ai-sdk/togetherai";
import Together from "together-ai";
import FirecrawlApp from "@mendable/firecrawl-js";

const APP_NAME_HELICONE = "deepresearch";

export const togetheraiClient = createTogetherAI({
  apiKey: process.env.TOGETHER_API_KEY ?? "",
  baseURL: "https://together.helicone.ai/v1",
  headers: {
    "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
    "Helicone-Property-AppName": APP_NAME_HELICONE,
  },
});

// Dynamic TogetherAI client for client-side use
export function togetheraiClientWithKey(apiKey: string) {
  return createTogetherAI({
    apiKey: apiKey || process.env.TOGETHER_API_KEY || "",
    baseURL: "https://together.helicone.ai/v1",
    headers: {
      "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
      "Helicone-Property-AppName": APP_NAME_HELICONE,
    },
  });
}

export function togetheraiWithKey(apiKey: string) {
  const options: ConstructorParameters<typeof Together>[0] = {
    apiKey: apiKey || process.env.TOGETHER_API_KEY,
  };

  if (process.env.HELICONE_API_KEY) {
    options.baseURL = "https://together.helicone.ai/v1";
    options.defaultHeaders = {
      "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
      "Helicone-Property-Appname": APP_NAME_HELICONE,
    };
  }
  return new Together(options);
}

const options: ConstructorParameters<typeof Together>[0] = {
  apiKey: process.env.TOGETHER_API_KEY,
};

if (process.env.HELICONE_API_KEY) {
  options.baseURL = "https://together.helicone.ai/v1";
  options.defaultHeaders = {
    "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
    "Helicone-Property-Appname": APP_NAME_HELICONE,
  };
}

export const togetherai = new Together(options);

export const firecrawl = new FirecrawlApp({
  apiKey: process.env.FIRECRAWL_API_KEY,
});



================================================
FILE: src/deepresearch/config.ts
================================================
/**
 * Research Configuration Parameters
 *
 * These parameters control the Deep Research process, allowing customization
 * of research behavior, model selection, and output format.
 */

import dedent from "dedent";

// Model Selection
// Specialized models for different stages of the research pipeline
export const MODEL_CONFIG = {
  planningModel: "Qwen/Qwen2.5-72B-Instruct-Turbo", // Used for research planning and evaluation // 32k context window
  jsonModel: "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", // Used for structured data parsing
  summaryModel: "meta-llama/Llama-3.3-70B-Instruct-Turbo", // Used for web content summarization // 128k context window
  summaryModelLongPages: "meta-llama/Llama-4-Scout-17B-16E-Instruct", // Used for web content summarization of long pages
  answerModel: "deepseek-ai/DeepSeek-V3", // Used for final answer synthesis
};

// Resource Allocation
// Parameters controlling research depth and breadth
export const RESEARCH_CONFIG = {
  budget: 2, // Number of research refinement cycles to perform (in addition to the initial search operation)
  maxQueries: 2, // Maximum number of search queries per research cycle
  maxSources: 5, // Maximum number of sources to include in final synthesis
  maxTokens: 8192, // Maximum number of tokens in the generated report
};

// Add a constant for reply language
export const REPLY_LANGUAGE =
  "Always reply in English. Ignore the language the user provided questions or content always reply in English never reply in other languages.";

/**
 * Core prompt function that adds current date information to all prompts
 * This ensures all models have the correct temporal context for research
 */
export const getCurrentDateContext = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // JavaScript months are 0-indexed
  const day = now.getDate();
  const monthName = now.toLocaleString("default", { month: "long" });

  return `Current date is ${year}-${month.toString().padStart(2, "0")}-${day
    .toString()
    .padStart(2, "0")} (${monthName} ${day}, ${year}).
When searching for recent information, prioritize results from the current year (${year}) and month (${monthName} ${year}).
For queries about recent developments, include the current year (${year}) in your search terms.
When ranking search results, consider recency as a factor - newer information is generally more relevant for current topics.`;
};

// System Prompts
// Instructions for each stage of the research process
export const PROMPTS = {
  clarificationParsingPrompt:
    dedent(`You are an AI research assistant. You will be given a research topic and a list of clarifying questions. Your task is to parse the questions return them in an array of strings.

  ${REPLY_LANGUAGE}
  `),

  // Clarification: Helps to clarify research topics
  clarificationPrompt:
    dedent(`You are an AI research assistant. Your goal is to help users conduct deep research on topics by asking clarifying questions.

When a user provides a research topic, generate up to 3 concise bullet-point questions to clarify their needs. Focus on:

* Specific aspect or angle?
* Purpose or context?
* Any constraints (e.g. location, budget, timing)?

Keep your questions short, relevant, and directly related to the topic provided. Do not provide answers or additional commentary—just the questions.

${REPLY_LANGUAGE}
`),

  // Planning: Generates initial research queries
  planningPrompt: dedent(`${getCurrentDateContext()}
You are a strategic research planner with expertise in breaking down complex questions into logical search steps. When given a research topic or question, you'll analyze what specific information is needed and develop a sequential research plan.

    First, identify the core components of the question and any implicit information needs.

    Then provide a numbered list of 3-5 sequential search queries

    Your queries should be:
    - Specific and focused (avoid broad queries that return general information)
    - Written in natural language without Boolean operators (no AND/OR)
    - Designed to progress logically from foundational to specific information

    It's perfectly acceptable to start with exploratory queries to "test the waters" before diving deeper. Initial queries can help establish baseline information or verify assumptions before proceeding to more targeted searches.
    
${REPLY_LANGUAGE}`),

  planParsingPrompt: `${getCurrentDateContext()}
You are a research assistant, you will be provided with a plan of action to research a topic, identify the queries that we should run to search for the topic. Look carefully
    at the general plan provided and identify the key queries that we should run. For dependent queries (those requiring results from earlier searches), leave them for later execution and focus only on the self-contained queries that can be run immediately.
    `,

  // Content Processing: Identifies relevant information from search results
  rawContentSummarizerPrompt: dedent(`${getCurrentDateContext()}
You are a research extraction specialist. Extract only the most relevant information that directly answers or relates to the research topic.

FOCUS: Answer the research topic as directly as possible using only information from the provided content.

FORMAT:
- Start with the most direct answer or key finding
- Include only essential supporting data (numbers, dates, sources)
- Maximum 3-4 sentences
- If the content doesn't contain specific information about the research topic, state this clearly in 1-2 sentences

AVOID:
- Background context unless directly relevant
- Repetitive information
- Lengthy explanations
- General tourism/industry overview
- Speculation or external knowledge

Critical: If the content lacks specific information about the research topic, simply state: "The source does not provide specific information about [research topic]. The content covers [brief description of what it actually contains]."

Extract the core facts only.

${REPLY_LANGUAGE}`),

  // Completeness Evaluation: Determines if more research is needed
  evaluationPrompt:
    dedent(`You are a research query optimizer. Your task is to analyze search results against the original research goal and generate follow-up queries to fill in missing information.

    PROCESS:
    1. Identify ALL information explicitly requested in the original research goal
    2. Analyze what specific information has been successfully retrieved in the search results
    3. Identify ALL information gaps between what was requested and what was found
    4. For entity-specific gaps: Create targeted queries for each missing attribute of identified entities
    5. For general knowledge gaps: Create focused queries to find the missing conceptual information

    QUERY GENERATION RULES:
    - IF specific entities were identified AND specific attributes are missing:
    * Create direct queries for each entity-attribute pair (e.g., "LeBron James height")
    - IF general knowledge gaps exist:
    * Create focused queries to address each conceptual gap (e.g., "criteria for ranking basketball players")
    - Queries must be constructed to directly retrieve EXACTLY the missing information
    - Avoid tangential or merely interesting information not required by the original goal
    - Prioritize queries that will yield the most critical missing information first

    OUTPUT FORMAT:
    First, briefly state:
    1. What specific information was found
    2. What specific information is still missing
    3. What type of knowledge gaps exist (entity-specific or general knowledge)

    Then provide up to 5 targeted queries that directly address the identified gaps, ordered by importance. Please consider that you
    need to generate queries that tackle a single goal at a time (searching for A AND B will return bad results). Be specific!

    ${REPLY_LANGUAGE}
    `),

  // Evaluation Parsing: Extracts structured data from evaluation output
  evaluationParsingPrompt: `${getCurrentDateContext()}
    Extract follow-up search queries from the evaluation. If no follow-up queries are needed, return an empty list.`,

  // Source Filtering: Selects most relevant sources
  filterPrompt: `${getCurrentDateContext()}
    Evaluate each search result for relevance, accuracy, and information value
                       related to the research topic. At the end, you need to provide a list of
                       source numbers with the rank of relevance. Remove the irrelevant ones.`,

  // Source Filtering: Selects most relevant sources
  sourceParsingPrompt: `${getCurrentDateContext()}
    Extract the source list that should be included.`,

  // Answer Generation: Creates final research report
  answerPrompt: dedent(`${getCurrentDateContext()}
You are a senior research analyst tasked with creating a professional, publication-ready report.
Using **ONLY the provided sources**, produce a Markdown document (at least 5 pages) following these exact requirements:

---

# Structure Guidelines

## 1. **Abstract**

* Provide a concise (250–300 words) summary of the entire research
* State the main research question/objective
* Highlight key findings and their significance
* Summarize major conclusions and implications
* Write in a self-contained manner that can stand alone

## 2. **Introduction**

* Contextualize the research topic
* State the report's scope and objectives
* Preview key themes

## 3. **Analysis**

* Group findings into thematic categories
* Compare/contrast different sources' perspectives
* Highlight patterns, contradictions, and evidence quality
* MUST include **inline citations** in the format "[INLINE_CITATION](https://...)" after every key claim or data point
* Never make factual statements without providing the corresponding citation

## 4. **Conclusion**

* Synthesize overarching insights
* Discuss practical implications
* Identify knowledge gaps and research limitations
* Suggest areas for further investigation

---

# Composition Rules

* **Strict source adherence**: Every factual claim must cite a source using "[INLINE_CITATION](https://...)"
* **Analytical depth**: Prioritize insight generation over simple information reporting
* **Objective framing**: Present conflicting evidence neutrally, without bias
* **Information hierarchy**: Use "##" for main sections, "###" for subsections
* **Visual clarity**: Format tables using "|" delimiters and alignment markers
* **Citation integrity**: Ensure all claims are linked to an inline citation

---

# Prohibitions

* No bullet points or listicles in the final content
* No unsupported assertions
* No informal language
* No repetitive or filler content
* No summarizing sources without analytical commentary
* No external knowledge beyond the provided sources

---

# Formatting Requirements

[Research Topic]

## Abstract

[Paragraph 1...]

[Paragraph 2...]

## Introduction

[Opening paragraph with background...]

[Paragraph 2 expanding context...]

[Paragraph 3 outlining the structure...]

## [Primary Theme]

[Paragraph 1 of analysis with inline citations like this: According to a recent study [INLINE_CITATION](https://source1.com)...]

[Paragraph 2 comparing perspectives [INLINE_CITATION](https://source2.com)...]

[Paragraph 3 discussing patterns or contradictions [INLINE_CITATION](https://source3.com)...]

### [Subtheme]

[Detailed exploration in paragraph form...]

[Follow-up paragraph...]

[Third paragraph if necessary...]

### [Subtheme Where Table or Chart is Helpful]

*Table X: Comparative Metrics on [Topic] [INLINE_CITATION](https://source4.com)*

| Comparison Aspect | Source A [INLINE_CITATION](https://sourceA.com) | Source B [INLINE_CITATION](https://sourceB.com) |
|-------------------|--------------------------------|--------------------------------|
| Key Metric        | xx%                            | xx%                            |

[Paragraph analysis interpreting the table content...]

## Conclusion

[Synthesized findings and implications [INLINE_CITATION](https://source5.com)...]

[Discussion of limitations...]

[Recommendations for future work...]

[Final summary paragraph...]

---

**Before writing**, analyze how the sources relate.
Ensure inline citations use "[INLINE_CITATION](https://...)" formatting.
Use at least **3 full paragraphs per section**. Avoid short sections or outline-like writing.
Think like you're writing a **book chapter**, not an article — with deep reasoning, structured arguments, and fluent transitions.


${REPLY_LANGUAGE}
`),

  dataVisualizerPrompt: `You are an expert graphic designer and visual storyteller. I'm preparing a research report on a topic that will be specified by the user.

Please generate a detailed image-generation prompt that I can plug directly into Flux to produce a polished, professional cover photo for this research report.

Requirements:
- A clean, minimal layout
- Main visual element(s) should symbolize the core of the research
- No text in the image, just a nice clean professional visual

Output only the Flux-ready prompt—no explanations.`,

  planSummaryPrompt: `${getCurrentDateContext()}
You are a research assistant. Given a detailed research plan, summarize it in one short, plain sentence anyone can understand. Be brief and clear.`,
};



================================================
FILE: src/deepresearch/schemas.ts
================================================
import { z } from "zod";

// Schemas
export const researchPlanSchema = z.object({
  queries: z
    .string()
    .array()
    .describe("A list of search queries to thoroughly research the topic"),
});

export const searchResultSchema = z.object({
  title: z.string().describe("The title of the search result"),
  link: z.string().url().describe("The URL of the search result"),
  content: z.string().describe("The content of the web page"),
  summary: z.string().describe("The summary of the web page").optional(),
});

export type SearchResult = z.infer<typeof searchResultSchema>;

export const researchStateSchema = z.object({
  topic: z.string().describe("The topic of the research"),
  allQueries: z
    .string()
    .array()
    .describe("A list of all queries used in the research"),
  searchResults: searchResultSchema
    .array()
    .describe("A list of search results"),
  budget: z.number().describe("The budget for the research"),
  iteration: z.number().describe("The current iteration of the research"),
});

export type ResearchState = z.infer<typeof researchStateSchema>;

// Stream Event Schemas
// Base event schema
const baseEventSchema = z.object({
  type: z.string(),
  timestamp: z.number(),
  iteration: z.number().optional(),
});

// Planning events
export const planningStartedEventSchema = baseEventSchema.extend({
  type: z.literal("planning_started"),
  topic: z.string(),
});
export const planningCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("planning_completed"),
  plan: z.string().optional(),
  queries: z.string().array(),
});

// Search events
export const searchStartedEventSchema = baseEventSchema.extend({
  type: z.literal("search_started"),
  query: z.string(),
  iteration: z.number(),
});

export const searchCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("search_completed"),
  query: z.string(),
  urls: z.string().array(),
  resultCount: z.number(),
  iteration: z.number(),
});

// Content processing events
export const contentProcessingEventSchema = baseEventSchema.extend({
  type: z.literal("content_processing"),
  url: z.string(),
  title: z.string(),
  query: z.string(),
  content: z.string().optional(),
});

export const contentSummarizedEventSchema = baseEventSchema.extend({
  type: z.literal("content_summarized"),
  url: z.string(),
  title: z.string(),
  query: z.string(),
  summaryFirstHundredChars: z.string().optional(),
});

// Evaluation events
export const evaluationStartedEventSchema = baseEventSchema.extend({
  type: z.literal("evaluation_started"),
  totalResults: z.number(),
  iteration: z.number(),
});

export const evaluationCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("evaluation_completed"),
  needsMore: z.boolean(),
  reasoning: z.string().optional(), // TODO remove optional
  additionalQueries: z.string().array().optional(),
  iteration: z.number(),
});

export const coverGenerationStartedEventSchema = baseEventSchema.extend({
  type: z.literal("cover_generation_started"),
  prompt: z.string(),
});

export const coverGenerationCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("cover_generation_completed"),
  coverImage: z.string(),
});

// Report events
export const reportStartedEventSchema = baseEventSchema.extend({
  type: z.literal("report_started"),
});

export const reportGeneratingEventSchema = baseEventSchema.extend({
  type: z.literal("report_generating"),
  partialReport: z.string(),
});

export const reportGeneratedEventSchema = baseEventSchema.extend({
  type: z.literal("report_generated"),
  report: z.string().optional(),
});

// Status events
export const iterationCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("iteration_completed"),
  iteration: z.number(),
  totalResults: z.number(),
});

export const researchCompletedEventSchema = baseEventSchema.extend({
  type: z.literal("research_completed"),
  finalResultCount: z.number(),
  totalIterations: z.number(),
});

export const errorEventSchema = baseEventSchema.extend({
  type: z.literal("error"),
  message: z.string(),
  step: z.string(),
  iteration: z.number().optional(),
});

// Union type for all possible events
export const streamEventSchema = z.discriminatedUnion("type", [
  planningStartedEventSchema,
  planningCompletedEventSchema,
  searchStartedEventSchema,
  searchCompletedEventSchema,
  contentProcessingEventSchema,
  contentSummarizedEventSchema,
  evaluationStartedEventSchema,
  evaluationCompletedEventSchema,
  coverGenerationStartedEventSchema,
  coverGenerationCompletedEventSchema,
  reportStartedEventSchema,
  reportGeneratingEventSchema,
  reportGeneratedEventSchema,
  iterationCompletedEventSchema,
  researchCompletedEventSchema,
  errorEventSchema,
]);

export type StreamEvent = z.infer<typeof streamEventSchema>;

// Individual event types for type safety
export type PlanningStartedEvent = z.infer<typeof planningStartedEventSchema>;
export type PlanningCompletedEvent = z.infer<
  typeof planningCompletedEventSchema
>;
export type SearchStartedEvent = z.infer<typeof searchStartedEventSchema>;
export type SearchCompletedEvent = z.infer<typeof searchCompletedEventSchema>;
export type ContentProcessingEvent = z.infer<
  typeof contentProcessingEventSchema
>;
export type ContentSummarizedEvent = z.infer<
  typeof contentSummarizedEventSchema
>;
export type EvaluationStartedEvent = z.infer<
  typeof evaluationStartedEventSchema
>;
export type EvaluationCompletedEvent = z.infer<
  typeof evaluationCompletedEventSchema
>;

export type IterationCompletedEvent = z.infer<
  typeof iterationCompletedEventSchema
>;

export type CoverGenerationStartedEvent = z.infer<
  typeof coverGenerationStartedEventSchema
>;
export type CoverGenerationCompletedEvent = z.infer<
  typeof coverGenerationCompletedEventSchema
>;

export type ReportStartedEvent = z.infer<typeof reportStartedEventSchema>;
export type ReportGeneratingEvent = z.infer<typeof reportGeneratingEventSchema>;
export type ReportGeneratedEvent = z.infer<typeof reportGeneratedEventSchema>;

export type ResearchCompletedEvent = z.infer<
  typeof researchCompletedEventSchema
>;
export type ErrorEvent = z.infer<typeof errorEventSchema>;



================================================
FILE: src/deepresearch/startResearch.ts
================================================
"use server";
import { db } from "@/db";
import { getResearch } from "@/db/action";
import { research } from "@/db/schema";
import { StartResearchPayload } from "@/deepresearch/workflows/start-research-workflow";
import { qstash, workflow } from "@/lib/clients";
import { eq } from "drizzle-orm";
import { limitResearch } from "@/lib/limits";

export const startResearch = async ({
  chatId,
  personalTogetherApiKey,
}: {
  chatId: string;
  personalTogetherApiKey?: string;
}) => {
  console.log("startResearch", chatId);

  const researchData = await getResearch(chatId);

  if (!researchData || !researchData.clerkUserId) {
    throw new Error("Research with clerk user not found");
  }

  const { success } = await limitResearch({
    clerkUserId: researchData?.clerkUserId,
    isBringingKey: !!personalTogetherApiKey,
  });

  if (!success) {
    throw new Error("No remaining researches");
  }

  // Get the base URL for the workflow
  const baseUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
    ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
    : "http://localhost:3000";

  const workflowUrl = `${baseUrl}/api/workflows/nested-research/start-research`;

  const researchTopic = `${researchData?.initialUserMessage} ${
    researchData?.answers && researchData?.answers?.length > 0
      ? researchData?.questions
          ?.map((question, questionIdx) => {
            const answer = researchData?.answers?.[questionIdx];
            return answer ? `${questionIdx + 1}. ${question} ${answer}` : "";
          })
          .filter(Boolean)
          .join(" ")
      : ""
  }`.trim();

  await db
    .update(research)
    .set({
      researchTopic,
      researchStartedAt: new Date(),
    })
    .where(eq(research.id, chatId))
    .returning();

  const payload: StartResearchPayload = {
    topic: researchTopic,
    sessionId: chatId,
    togetherApiKey: personalTogetherApiKey,
  };

  // generate researchTopic by joining strings with:initialUserMessage + questions+answers the complete researchTopic to use in the research

  const { workflowRunId } = await workflow.trigger({
    url: workflowUrl,
    body: JSON.stringify(payload),
    retries: 3, // Optional retries for the initial request
  });

  // Schedule a cancel request to the cancel endpoint after 15 minutes
  await qstash.publishJSON({
    url: `${baseUrl}/api/cancel`,
    body: { id: workflowRunId },
    // delay of 15 minutes
    delay: 15 * 60 * 1000,
  });

  console.log(
    "Started research with ID:",
    chatId + " WfId:" + workflowRunId + " 🔎:" + researchTopic
  );

  if (!workflowRunId)
    throw new Error("No workflow run ID returned from Trigger");

  return {
    researchId: research.id,
    status: research.status,
    createdAt: research.createdAt,
  };
};



================================================
FILE: src/deepresearch/storage.ts
================================================
/**
 * Storage utilities for Deep Research Pipeline using Upstash Redis
 */

import { Redis } from "@upstash/redis";
import fs from "fs";
import { ResearchState, StreamEvent } from "./schemas";

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
  automaticDeserialization: false, // Disable automatic deserialization to handle it ourselves
});

// Key generation functions for easy management
const generateKeys = {
  state: (sessionId: string) => `research:${sessionId}:state`,
  stream: (sessionId: string) => `research:${sessionId}:stream`,
};

// Storage functions for research state
export const stateStorage = {
  async store(sessionId: string, state: ResearchState): Promise<void> {
    const key = generateKeys.state(sessionId);
    await redis.set(key, state, { ex: 86400 });
  },

  async get(sessionId: string): Promise<ResearchState | null> {
    const key = generateKeys.state(sessionId);
    const data = await redis.get(key);
    return data ? (JSON.parse(data as string) as ResearchState) : null;
  },

  async delete(sessionId: string): Promise<void> {
    const key = generateKeys.state(sessionId);
    await redis.del(key);
  },
};

// Storage functions for stream events

export const streamStorage = {
  async addEvent(sessionId: string, event: StreamEvent): Promise<void> {
    const key = generateKeys.stream(sessionId);
    const eventData = JSON.stringify(event);

    // Use XADD to add event to the stream
    await redis.xadd(key, "*", { event: eventData });

    // Set expiration on the stream
    await redis.expire(key, 86400); // 24 hours TTL
  },

  async getEvents(
    sessionId: string,
    count: number = 100
  ): Promise<StreamEvent[]> {
    const key = generateKeys.stream(sessionId);

    // Use XRANGE to get events from the stream (oldest to newest)
    const result = (await redis.xrange(
      key,
      "-",
      "+",
      count
    )) as unknown as Array<[string, string[]]>;

    if (!result || !result.length) return [];

    const streamResults = result
      .map(([id, fields]) => {
        try {
          // Find the event field in the array of key-value pairs
          const eventField = fields.find(
            (field: string, index: number) =>
              index % 2 === 0 && field === "event"
          );
          if (!eventField) return null;

          // Get the value that follows the "event" key
          const eventIndex = fields.indexOf(eventField);
          const eventData = fields[eventIndex + 1];

          return JSON.parse(eventData) as StreamEvent;
        } catch {
          return null;
        }
      })
      .filter(Boolean) as StreamEvent[];

    return streamResults.sort((a, b) => a.timestamp - b.timestamp);
  },
};

// Utility function to clean up all data for a session
export const cleanupSession = async (sessionId: string): Promise<void> => {
  const keys = [generateKeys.state(sessionId), generateKeys.stream(sessionId)];

  await Promise.all(keys.map((key) => redis.del(key)));
};



================================================
FILE: src/deepresearch/webSearch.ts
================================================
import z from "zod";
import { firecrawl } from "./apiClients";

import { SearchResult } from "./schemas";

type SearchResults = {
  results: SearchResult[];
};

export const searchOnWeb = async ({
  query,
}: {
  query: string;
}): Promise<SearchResults> => {
  // 1. Call Brave Search API for web results
  const res = await fetch(
    `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(
      query
    )}&count=5&result_filter=web`,
    {
      headers: {
        Accept: "application/json",
        "Accept-Encoding": "gzip",
        "X-Subscription-Token": process.env.BRAVE_API_KEY || "",
      } as HeadersInit,
    }
  );
  const responseJson = await res.json();
  const parsedResponseJson = z
    .object({
      web: z.object({
        results: z.array(
          z.object({
            url: z.string(),
            title: z.string(),
            meta_url: z.object({
              favicon: z.string(),
            }),
            extra_snippets: z.array(z.string()).default([]),
            thumbnail: z
              .object({
                original: z.string(),
              })
              .optional(),
          })
        ),
      }),
    })
    .parse(responseJson);

  const parsedResults = parsedResponseJson.web.results.map((r) => ({
    title: r.title,
    url: r.url,
    favicon: r.meta_url.favicon,
    extraSnippets: r.extra_snippets,
    thumbnail: r.thumbnail?.original,
  }));

  // 2. Validate and type results
  const searchResultSchema = z.object({
    title: z.string(),
    url: z.string(),
    favicon: z.string(),
    extraSnippets: z.array(z.string()).default([]),
    thumbnail: z.string().optional(),
  });
  type SearchResult = z.infer<typeof searchResultSchema>;
  const schema = z.array(searchResultSchema);
  const searchResults = schema.parse(parsedResults);

  // 4. Scrape each result with Firecrawl
  async function scrapeSearchResult(searchResult: SearchResult) {
    let scrapedText = "";
    let scrapeResponse:
      | Awaited<ReturnType<typeof firecrawl.scrapeUrl>>
      | undefined;
    try {
      scrapeResponse = await firecrawl.scrapeUrl(searchResult.url, {
        formats: ["markdown"],
        timeout: 15000,
        // 12 hours
        maxAge: 12 * 60 * 60 * 1000,
      });
      if (scrapeResponse.error) {
        throw scrapeResponse.error;
      }
      if (scrapeResponse.success) {
        const rawText = scrapeResponse.markdown ?? "";
        scrapedText = stripUrlsFromMarkdown(rawText).substring(0, 80_000);
      }
    } catch (e) {
      // ignore individual scrape errors
      console.warn("Error scraping", searchResult.url, " with error", e);
    }
    return {
      title: searchResult.title,
      link: searchResult.url,
      content: scrapedText,
    };
  }

  const resultsSettled = await Promise.allSettled(
    searchResults.map(scrapeSearchResult)
  );

  const results = resultsSettled
    .filter((r) => r.status === "fulfilled")
    .map((r) => (r as PromiseFulfilledResult<any>).value)
    .filter((r) => r.content !== "");

  if (results.length === 0) {
    return { results: [] };
  }
  return { results };
};

// 3. Markdown stripping helper
function stripUrlsFromMarkdown(markdown: string): string {
  let result = markdown;
  result = result.replace(
    /!\[([^\]]*)\]\((https?:\/\/[^\s)]+)(?:\s+"[^"]*")?\)/g,
    "$1"
  );
  result = result.replace(
    /\[([^\]]*)\]\((https?:\/\/[^\s)]+)(?:\s+"[^"]*")?\)/g,
    "$1"
  );
  result = result.replace(
    /^\[[^\]]+\]:\s*https?:\/\/[^\s]+(?:\s+"[^"]*")?$/gm,
    ""
  );
  result = result.replace(/<(https?:\/\/[^>]+)>/g, "");
  result = result.replace(/https?:\/\/[^\s]+/g, "");
  return result.trim();
}



================================================
FILE: src/deepresearch/workflows/gather-search-workflow.ts
================================================
/**
 * Gather Search Workflow - Handles iterative search and research gathering
 * Self-sufficient workflow with all necessary LLM and search logic
 */

import { createWorkflow } from "@upstash/workflow/nextjs";
import { stateStorage, streamStorage } from "../storage";
import { WorkflowContext } from "@upstash/workflow";
import { generateText, generateObject } from "ai";
import { togetheraiClientWithKey } from "../apiClients";
import { MODEL_CONFIG, PROMPTS, RESEARCH_CONFIG } from "../config";
import {
  researchPlanSchema,
  SearchResult,
  SearchStartedEvent,
  SearchCompletedEvent,
  ContentProcessingEvent,
  ContentSummarizedEvent,
  EvaluationStartedEvent,
  EvaluationCompletedEvent,
  IterationCompletedEvent,
  ErrorEvent,
} from "../schemas";
import { searchOnWeb } from "../webSearch";

// Helper function to summarize content
const summarizeContent = async ({
  result,
  query,
  togetherApiKey,
}: {
  result: SearchResult;
  query: string;
  togetherApiKey?: string;
}): Promise<string> => {
  console.log(`📝 Summarizing content from URL: ${result.link}`);

  // Use a higher threshold for very long content (around 128K characters)
  const isContentVeryLong = result.content.length > 100000;

  const model = isContentVeryLong
    ? togetheraiClientWithKey(togetherApiKey || "")(
        MODEL_CONFIG.summaryModelLongPages
      )
    : togetheraiClientWithKey(togetherApiKey || "")(MODEL_CONFIG.summaryModel);

  const response = await generateText({
    model,
    messages: [
      { role: "system", content: PROMPTS.rawContentSummarizerPrompt },
      {
        role: "user",
        content: `<Research Topic>${query}</Research Topic>\n\n<Raw Content>${result.content}</Raw Content>`,
      },
    ],
  });

  return response.text;
};

// Helper function to perform web search with summarization
const webSearch = async ({
  query,
  sessionId,
  iteration,
  togetherApiKey,
}: {
  query: string;
  sessionId: string;
  iteration: number;
  togetherApiKey?: string;
}): Promise<SearchResult[]> => {
  console.log(`�� Perform web search with query: ${query}`);

  // Emit search started event
  await streamStorage.addEvent(sessionId, {
    type: "search_started",
    query,
    iteration,
    timestamp: Date.now(),
  } satisfies SearchStartedEvent);

  // Truncate long queries to avoid issues
  if (query.length > 400) {
    query = query.substring(0, 400);
    console.log(`⚠️ Truncated query to 400 characters: ${query}`);
  }

  const searchResults = await searchOnWeb({ query });
  console.log(
    `📊 Web Search Responded with ${searchResults.results.length} results`
  );

  // Emit search completed event
  await streamStorage.addEvent(sessionId, {
    type: "search_completed",
    query,
    urls: searchResults.results.map((r) => r.link),
    resultCount: searchResults.results.length,
    iteration,
    timestamp: Date.now(),
  } satisfies SearchCompletedEvent);

  // Process and summarize raw content if available
  const summarizationTasks = [];
  const resultInfo = [];

  for (const result of searchResults.results) {
    if (!result.content) {
      continue;
    }

    // Emit content processing event
    await streamStorage.addEvent(sessionId, {
      type: "content_processing",
      url: result.link,
      title: result.title || "",
      content: result.content,
      query,
      timestamp: Date.now(),
    } satisfies ContentProcessingEvent);

    // Create a task for summarization
    const task = summarizeContent({ result, query, togetherApiKey });
    summarizationTasks.push(task);
    resultInfo.push(result);
  }

  // Wait for all summarization tasks to complete
  const summarizedContents = await Promise.all(summarizationTasks);

  // Combine results with summarized content
  const resultsWithSummary: SearchResult[] = [];
  for (let i = 0; i < resultInfo.length; i++) {
    const result = resultInfo[i];
    const summarizedContent = summarizedContents[i];

    // Emit content summarized event
    await streamStorage.addEvent(sessionId, {
      type: "content_summarized",
      url: result.link,
      title: result.title || "",
      query,
      timestamp: Date.now(),
      summaryFirstHundredChars: summarizedContent,
    } satisfies ContentSummarizedEvent);

    resultsWithSummary.push({
      title: result.title || "",
      link: result.link,
      content: result.content,
      summary: summarizedContent,
    });
  }

  return resultsWithSummary;
};

// Helper function to perform searches for multiple queries
const performSearch = async ({
  queries,
  sessionId,
  iteration,
  togetherApiKey,
}: {
  queries: string[];
  sessionId: string;
  iteration: number;
  togetherApiKey?: string;
}): Promise<SearchResult[]> => {
  const tasks = queries.map(async (query) => {
    return await webSearch({
      query,
      sessionId,
      iteration,
      togetherApiKey,
    });
  });

  const resultsList = await Promise.all(tasks);

  // Combine all results
  let combinedResults: SearchResult[] = [];
  for (const results of resultsList) {
    combinedResults = [...combinedResults, ...results];
  }

  // Simple deduplication by URL
  const seen = new Set<string>();
  const dedupedResults = combinedResults.filter((result) => {
    if (seen.has(result.link)) {
      return false;
    }
    seen.add(result.link);
    return true;
  });

  console.log(
    `Search complete, found ${dedupedResults.length} results after deduplication`
  );
  return dedupedResults;
};

// Helper function to evaluate research completeness
const evaluateResearchCompleteness = async ({
  topic,
  results,
  queries,
  togetherApiKey,
}: {
  topic: string;
  results: SearchResult[];
  queries: string[];
  togetherApiKey?: string;
}): Promise<{
  additionalQueries: string[];
  reasoning: string;
}> => {
  const formattedResults = results
    .map(
      (result) =>
        `- ${result.title}\n${
          result.summary || result.content.slice(0, 1000)
        }\n---\n`
    )
    .join("\n");

  const formattedQueries = queries.map((query) => `- ${query}`).join("\n");

  console.log(
    `📝 Evaluating research completeness for topic: ${topic} and ${results.length} results with queries: ${queries.length}`
  );

  const prompt = `
  <Research Topic>${topic}</Research Topic>
  <Search Queries Used>${formattedQueries}</Search Queries Used>
  <Current Search Results>${formattedResults}</Current Search Results>
  `;

  const evaluation = await generateText({
    model: togetheraiClientWithKey(togetherApiKey || "")(
      MODEL_CONFIG.planningModel
    ),
    messages: [
      { role: "system", content: PROMPTS.evaluationPrompt },
      {
        role: "user",
        content: prompt,
      },
    ],
  });
  console.log(`📝 Evaluation:\n\n ${evaluation.text}`);

  // Run evaluation summary and parsing in parallel
  const [evaluationSummary, parsedEvaluation] = await Promise.all([
    generateText({
      model: togetheraiClientWithKey(togetherApiKey || "")(
        MODEL_CONFIG.summaryModel
      ),
      messages: [
        { role: "system", content: PROMPTS.planSummaryPrompt },
        { role: "user", content: evaluation.text },
      ],
    }),
    generateObject({
      model: togetheraiClientWithKey(togetherApiKey || "")(
        MODEL_CONFIG.jsonModel
      ),
      messages: [
        { role: "system", content: PROMPTS.evaluationParsingPrompt },
        {
          role: "user",
          content: `Evaluation to be parsed: ${evaluation.text}`,
        },
      ],
      schema: researchPlanSchema,
    }),
  ]);

  const existingQueriesSet = new Set(queries);
  const newQueries = parsedEvaluation.object.queries.filter(
    (query) => !existingQueriesSet.has(query)
  );

  const additionalQueries = newQueries.slice(0, RESEARCH_CONFIG.maxQueries);

  return {
    additionalQueries,
    reasoning: evaluationSummary.text,
  };
};

// Types
export type GatherSearchPayload = {
  topic: string;
  queries: string[];
  existingResults: SearchResult[];
  budget: number;
  iteration: number;
  sessionId: string;
  togetherApiKey?: string;
};

// Nested workflow that handles iterative search and research gathering
export const gatherSearchQueriesWorkflow = createWorkflow<
  GatherSearchPayload,
  SearchResult[]
>(
  async (
    context: WorkflowContext<GatherSearchPayload>
  ): Promise<SearchResult[]> => {
    const {
      topic,
      queries,
      existingResults,
      budget,
      iteration,
      sessionId,
      togetherApiKey,
    } = context.requestPayload;

    // Step 1: Perform web searches for current queries using local search function
    const newSearchResults = await context.run(
      "perform-web-searches",
      async () => {
        console.log(
          `🔄 Iteration ${iteration} (budget: ${budget}) - searching ${queries.length} queries`
        );

        // Use local search function to perform searches
        const searchResults = await performSearch({
          queries,
          sessionId,
          iteration,
          togetherApiKey,
        });

        console.log(`📊 Found ${searchResults.length} new results`);
        return searchResults;
      }
    );

    // Step 2: Update state with new results
    const allResults = await context.run("update-research-state", async () => {
      // Update state in Redis

      const currentState = await stateStorage.get(sessionId);
      if (currentState) {
        currentState.searchResults.push(...newSearchResults);
        currentState.allQueries.push(...queries);
        currentState.iteration = iteration;
        await stateStorage.store(sessionId, currentState);
        return currentState.searchResults; // Return the updated search results
      }

      return existingResults;
    });

    // Step 3: Evaluate if more research is needed using local LLM function
    const evaluationResult = await context.run(
      "evaluate-research-completeness",
      async () => {
        const currentState = await stateStorage.get(sessionId);
        // we don't do evaluation if we don't have a state or if we're at the last iteration since we won't continue even if we might need more queries.
        if (!currentState || budget === 1) {
          return { needsMore: false, additionalQueries: [] };
        }

        // Emit evaluation started event
        await streamStorage.addEvent(sessionId, {
          type: "evaluation_started",
          totalResults: allResults.length,
          iteration,
          timestamp: Date.now(),
        } satisfies EvaluationStartedEvent);

        try {
          // Use local evaluation function to evaluate completeness
          const { additionalQueries, reasoning } =
            await evaluateResearchCompleteness({
              topic,
              results: allResults,
              queries: currentState.allQueries,
              togetherApiKey,
            });

          const needsMore = additionalQueries.length > 0;

          // Emit evaluation completed event
          await streamStorage.addEvent(sessionId, {
            type: "evaluation_completed",
            needsMore,
            additionalQueries,
            iteration,
            reasoning,
            timestamp: Date.now(),
          } satisfies EvaluationCompletedEvent);

          console.log(
            `🤔 Evaluation: ${needsMore ? "needs more research" : "complete"}`
          );

          return { needsMore, additionalQueries };
        } catch (error) {
          // Emit error event
          await streamStorage.addEvent(sessionId, {
            type: "error",
            message:
              error instanceof Error
                ? error.message
                : "Unknown error during evaluation",
            step: "evaluate-research-completeness",
            iteration,
            timestamp: Date.now(),
          } satisfies ErrorEvent);
          throw error;
        }
      }
    );

    // Step 4: Decide whether to continue iterating
    const shouldContinue =
      budget > 1 &&
      evaluationResult.needsMore &&
      evaluationResult.additionalQueries.length > 0;

    if (shouldContinue) {
      console.log(`🔄 Continuing research...`);

      // Recursively invoke this same workflow with updated parameters
      const nestedResponse = await context.invoke("nested-gather-search", {
        workflow: gatherSearchQueriesWorkflow,
        body: {
          topic,
          queries: evaluationResult.additionalQueries,
          existingResults: allResults,
          budget: budget - 1,
          iteration: iteration + 1,
          sessionId,
          togetherApiKey,
        },
      });

      if (nestedResponse.isCanceled || nestedResponse.isFailed) {
        console.error("Nested gather search workflow failed");
        return allResults;
      }

      return nestedResponse.body;
    } else {
      // Research is complete or budget exhausted
      const reason =
        budget <= 1
          ? "BUDGET EXHAUSTED"
          : !evaluationResult.needsMore
          ? "RESEARCH COMPLETE"
          : "NO ADDITIONAL QUERIES";

      // Emit iteration completed event
      await streamStorage.addEvent(sessionId, {
        type: "iteration_completed",
        iteration,
        totalResults: allResults.length,
        timestamp: Date.now(),
      } satisfies IterationCompletedEvent);

      console.log(
        `✅ Research finished (${reason}) - ${allResults.length} results`
      );

      return allResults;
    }
  }
);



================================================
FILE: src/deepresearch/workflows/start-research-workflow.ts
================================================
/**
 * Start Research Workflow - Orchestrates the complete research process
 * Self-sufficient workflow with all necessary LLM and search logic
 */

import { createWorkflow } from "@upstash/workflow/nextjs";
import { stateStorage, streamStorage } from "../storage";
import { gatherSearchQueriesWorkflow } from "./gather-search-workflow";
import { WorkflowContext } from "@upstash/workflow";
import { generateText, generateObject, streamText } from "ai";
import { MODEL_CONFIG, PROMPTS, RESEARCH_CONFIG } from "../config";
import {
  togetherai,
  togetheraiClient,
  togetheraiClientWithKey,
  togetheraiWithKey,
} from "../apiClients";
import {
  researchPlanSchema,
  ResearchState,
  PlanningStartedEvent,
  PlanningCompletedEvent,
  ReportGeneratedEvent,
  ReportGeneratingEvent,
  ResearchCompletedEvent,
  ErrorEvent,
  ReportStartedEvent,
  SearchResult,
} from "../schemas";
import { db } from "@/db";
import { research } from "@/db/schema";
import { eq } from "drizzle-orm";
import { awsS3Client } from "@/lib/clients";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getResearch } from "@/db/action";
import { extractMarkdownHeadings } from "@/lib/utils";

const MAX_BUDGET = 3;

// Types
export type StartResearchPayload = {
  topic: string;
  sessionId: string;
  togetherApiKey?: string;
};

// Helper function to generate research queries
const generateResearchQueries = async (
  topic: string,
  togetherApiKey?: string
): Promise<{
  queries: string[];
  plan: string;
  summarisedPlan: string;
}> => {
  const initialSearchEvaluation = await generateText({
    model: togetheraiClientWithKey(togetherApiKey || "")(
      MODEL_CONFIG.planningModel
    ),
    messages: [
      { role: "system", content: PROMPTS.planningPrompt },
      { role: "user", content: `Research Topic: ${topic}` },
    ],
  });

  // Run plan parsing and summary generation in parallel
  const [parsedPlan, planSummary] = await Promise.all([
    generateObject({
      model: togetheraiClientWithKey(togetherApiKey || "")(
        MODEL_CONFIG.jsonModel
      ),
      messages: [
        { role: "system", content: PROMPTS.planParsingPrompt },
        { role: "user", content: `Research Topic: ${topic}` },
      ],
      schema: researchPlanSchema,
    }),
    generateText({
      model: togetheraiClientWithKey(togetherApiKey || "")(
        MODEL_CONFIG.summaryModel
      ),
      messages: [
        { role: "system", content: PROMPTS.planSummaryPrompt },
        { role: "user", content: initialSearchEvaluation.text },
      ],
    }),
  ]);

  console.log(
    `📋 Research queries generated: \n - ${parsedPlan.object.queries.join(
      "\n - "
    )}`
  );

  const dedupedQueries = Array.from(new Set(parsedPlan.object.queries));
  const queries = dedupedQueries.slice(0, RESEARCH_CONFIG.maxQueries);

  return {
    queries,
    plan: initialSearchEvaluation.text,
    summarisedPlan: planSummary.text,
  };
};

// Helper function to generate final research answer with progressive updates
const generateResearchAnswer = async ({
  topic,
  results,
  sessionId,
  togetherApiKey,
}: {
  topic: string;
  results: SearchResult[];
  sessionId: string;
  togetherApiKey?: string;
}): Promise<string> => {
  const formattedSearchResults = results
    .map(
      (result) =>
        `- Link: ${result.link}\nTitle: ${result.title}\nSummary: ${result.summary}\n\n`
    )
    .join("\n");

  let fullReport = "";

  const { textStream } = await streamText({
    model: togetheraiClientWithKey(togetherApiKey || "")(
      MODEL_CONFIG.answerModel
    ),
    messages: [
      { role: "system", content: PROMPTS.answerPrompt },
      {
        role: "user",
        content: `Research Topic: ${topic}\n\nSearch Results:\n${formattedSearchResults}`,
      },
    ],
    maxTokens: RESEARCH_CONFIG.maxTokens,
  });

  let index = 0;
  for await (const textPart of textStream) {
    fullReport += textPart;
    // Emit progressive report updates
    index++;
    if (index % 250 === 0) {
      await streamStorage.addEvent(sessionId, {
        type: "report_generating",
        partialReport: fullReport,
        timestamp: Date.now(),
      } satisfies ReportGeneratingEvent);
    }
  }

  return fullReport.trim();
};

// Main workflow that orchestrates the entire research process
export const startResearchWorkflow = createWorkflow<
  StartResearchPayload,
  string
>(async (context: WorkflowContext<StartResearchPayload>) => {
  const { topic, sessionId, togetherApiKey } = context.requestPayload;

  // Step 1: Generate initial research plan using LLM
  const initialQueries = await context.run(
    "generate-initial-plan",
    async () => {
      console.log(
        `🔍 Starting research for: ${topic} and Session ID: ${sessionId}`
      );

      const researchData = await getResearch(sessionId);

      if (!researchData || !researchData.clerkUserId) {
        await streamStorage.addEvent(sessionId, {
          type: "error",
          message: "Research with clerk user not found",
          step: "generate-initial-plan",
          timestamp: Date.now(),
        } satisfies ErrorEvent);
        throw new Error("Research with clerk user not found");
      }

      // Emit planning started event
      await streamStorage.addEvent(sessionId, {
        type: "planning_started",
        topic: researchData?.initialUserMessage || topic,
        timestamp: Date.now(),
      } satisfies PlanningStartedEvent);

      try {
        // Generate queries using local LLM function
        const { queries, plan, summarisedPlan } = await generateResearchQueries(
          topic,
          togetherApiKey
        );

        // Emit queries generated event
        await streamStorage.addEvent(sessionId, {
          type: "planning_completed",
          queries,
          plan: summarisedPlan,
          iteration: 0,
          timestamp: Date.now(),
        } satisfies PlanningCompletedEvent);

        // Initialize state in Redis
        const initialState: ResearchState = {
          topic,
          allQueries: queries,
          searchResults: [],
          budget: MAX_BUDGET, // Allowed iterations
          iteration: 0,
        };
        await stateStorage.store(sessionId, initialState);

        console.log(`📋 Generated ${queries.length} initial queries`);
        return queries;
      } catch (error) {
        // Emit error event
        await streamStorage.addEvent(sessionId, {
          type: "error",
          message:
            error instanceof Error
              ? error.message
              : "Unknown error during planning",
          step: "generate-initial-plan",
          iteration: 0,
          timestamp: Date.now(),
        } satisfies ErrorEvent);
        throw error;
      }
    }
  );

  // Step 2: Invoke the iterative search workflow
  const gatherResponse = await context.invoke("invoke-gather-search", {
    workflow: gatherSearchQueriesWorkflow,
    body: {
      topic,
      queries: initialQueries,
      existingResults: [],
      budget: MAX_BUDGET,
      iteration: 1,
      sessionId,
    },
  });

  if (gatherResponse.isCanceled || gatherResponse.isFailed) {
    console.error("Gather search workflow failed or was canceled");
    return "Research failed during data gathering phase";
  }

  // Step 3: Generate a cover image for the research topic
  const coverImagePromise = context.run("generate-toc-image", async () => {
    console.log(`🎨 Generating cover image...`);

    try {
      // Generate the image prompt using the planning model
      const imageGenerationPrompt = await generateText({
        model: togetheraiClient(MODEL_CONFIG.summaryModel),
        messages: [
          { role: "system", content: PROMPTS.dataVisualizerPrompt },
          { role: "user", content: `Research Topic: ${topic}` },
        ],
      });

      if (!imageGenerationPrompt.text) {
        return undefined;
      }

      console.log(`📸 Image generation prompt: ${imageGenerationPrompt.text}`);

      await streamStorage.addEvent(sessionId, {
        type: "cover_generation_started",
        prompt: imageGenerationPrompt.text,
        timestamp: Date.now(),
      });

      const generatedImage = await togetheraiWithKey(
        togetherApiKey || ""
      ).images.create({
        prompt: imageGenerationPrompt.text,
        model: "black-forest-labs/FLUX.1-dev",
        width: 1024,
        height: 768,
        steps: 30,
      });

      const fluxImageUrl = generatedImage.data[0].url;

      if (!fluxImageUrl) return undefined;

      const fluxFetch = await fetch(fluxImageUrl);
      const fluxImage = await fluxFetch.blob();
      const imageBuffer = Buffer.from(await fluxImage.arrayBuffer());

      const coverImageKey = `research-cover-${generatedImage.id}.jpg`;

      await awsS3Client.send(
        new PutObjectCommand({
          Bucket: process.env.S3_UPLOAD_BUCKET || "",
          Key: coverImageKey,
          Body: imageBuffer,
          ContentType: "image/jpeg",
        })
      );

      const imageUrl = `https://${process.env.S3_UPLOAD_BUCKET}.s3.${
        process.env.S3_UPLOAD_REGION || "us-east-1"
      }.amazonaws.com/${coverImageKey}`;

      await streamStorage.addEvent(sessionId, {
        type: "cover_generation_completed",
        coverImage: imageUrl,
        timestamp: Date.now(),
      });

      return imageUrl;
    } catch (error) {
      console.error(`Failed to generate TOC image: ${error}`);
      throw error;
    }
  });

  // Step 4: Generate final comprehensive report using LLM
  const finalReportPromise = context.run("generate-final-report", async () => {
    console.log(`✨ Generating final report for ${sessionId}`);

    try {
      // Read final state from Redis
      const finalState = await stateStorage.get(sessionId);
      if (!finalState) {
        throw new Error("Could not read final research state");
      }

      await streamStorage.addEvent(sessionId, {
        type: "report_started",
        timestamp: Date.now(),
      } satisfies ReportStartedEvent);

      console.log(
        `📝 Generating report for ${finalState.searchResults.length} results`
      );

      const report = await generateResearchAnswer({
        topic,
        results: finalState.searchResults,
        sessionId,
        togetherApiKey,
      });

      // Emit report generated event
      await streamStorage.addEvent(sessionId, {
        type: "report_generated",
        report: report,
        timestamp: Date.now(),
      } satisfies ReportGeneratedEvent);

      return report;
    } catch (error) {
      // Emit error event
      await streamStorage.addEvent(sessionId, {
        type: "error",
        message:
          error instanceof Error
            ? error.message
            : "Unknown error during report generation",
        step: "generate-final-report",
        timestamp: Date.now(),
      } satisfies ErrorEvent);
      throw error;
    }
  });

  const [coverImage, finalReport] = await Promise.all([
    coverImagePromise,
    finalReportPromise,
  ]);

  // Step 5: Store the final report with cover image in the database and mark as completed the research
  await context.run("complete-research", async () => {
    try {
      // Read final state from Redis
      const finalState = await stateStorage.get(sessionId);
      if (!finalState) {
        throw new Error("Could not read final research state");
      }

      const headings = extractMarkdownHeadings(finalReport);

      const headingOne =
        headings && headings.find((heading) => heading.level === 1);

      await db
        .update(research)
        .set({
          report: finalReport,
          coverUrl: coverImage,
          status: "completed",
          title: headingOne?.text,
          completedAt: new Date(),
          sources: finalState.searchResults.map((result) => ({
            url: result.link,
            title: result.title,
          })),
        })
        .where(eq(research.id, sessionId))
        .returning();

      // Emit research completed event
      await streamStorage.addEvent(sessionId, {
        type: "research_completed",
        finalResultCount: finalState.searchResults.length,
        totalIterations: finalState.iteration,
        timestamp: Date.now(),
      } satisfies ResearchCompletedEvent);

      console.log(
        `🎉 Research completed: ${finalState.allQueries.length} queries, ${finalState.searchResults.length} results, ${finalState.iteration} iterations`
      );
    } catch (error) {
      // Emit error event
      await streamStorage.addEvent(sessionId, {
        type: "error",
        message:
          error instanceof Error
            ? error.message
            : "Unknown error during report generation",
        step: "complete-research",
        timestamp: Date.now(),
      } satisfies ErrorEvent);
      throw error;
    }
  });

  return finalReport;
});



================================================
FILE: src/hooks/use-mobile.ts
================================================
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}



================================================
FILE: src/lib/clients.ts
================================================
import { Client as ClientWorkflow } from "@upstash/workflow";
import { Client as ClientQstash } from "@upstash/qstash";
import { S3Client } from "@aws-sdk/client-s3";

export const workflow = new ClientWorkflow({
  token: process.env.QSTASH_TOKEN!,
});

export const qstash = new ClientQstash({
  token: process.env.QSTASH_TOKEN!,
});

export const awsS3Client = new S3Client({
  region: process.env.S3_UPLOAD_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.S3_UPLOAD_KEY || "",
    secretAccessKey: process.env.S3_UPLOAD_SECRET || "",
  },
});



================================================
FILE: src/lib/getChats.ts
================================================
import { db } from "@/db";
import { research } from "@/db/schema";
import { eq, desc } from "drizzle-orm";

export async function getChats(userId: string) {
  return db
    .select({
      id: research.id,
      title: research.title,
      initialUserMessage: research.initialUserMessage,
      researchTopic: research.researchTopic,
      completedAt: research.completedAt,
      createdAt: research.createdAt,
    })
    .from(research)
    .orderBy(desc(research.createdAt), desc(research.completedAt))
    .where(eq(research.clerkUserId, userId));
}



================================================
FILE: src/lib/limits.ts
================================================
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";
import { clerkClient } from "@clerk/nextjs/server";

const redis =
  !!process.env.UPSTASH_REDIS_REST_URL && !!process.env.UPSTASH_REDIS_REST_TOKEN
    ? new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      })
    : undefined;

const isLocal = process.env.NODE_ENV !== "production";

// 1 per day
const ratelimit =
  !isLocal && redis
    ? new Ratelimit({
        redis: redis,
        limiter: Ratelimit.fixedWindow(1, "1440 m"),
        analytics: true,
      })
    : undefined;

// 15 per day for people bringing their own API key
const byokRateLimit =
  !isLocal && redis
    ? new Ratelimit({
        redis: redis,
        limiter: Ratelimit.fixedWindow(15, "1440 m"),
        analytics: true,
      })
    : undefined;

const DEFAULT_LIMIT = 5;
const DEFAULT_RESET = null;
const BYOK_PREFIX = "byok-";

const fallbackResult = {
  success: true,
  remaining: DEFAULT_LIMIT,
  limit: DEFAULT_LIMIT,
  reset: DEFAULT_RESET,
};

export const limitResearch = async ({
  clerkUserId,
  isBringingKey,
}: {
  clerkUserId?: string;
  isBringingKey?: boolean;
}) => {
  // Unlimited for users with together.ai email
  if (clerkUserId) {
    const client = await clerkClient();
    try {
      const user = await client.users.getUser(clerkUserId);
      const email = user.emailAddresses?.[0]?.emailAddress;
      if (email && email.endsWith("@together.ai")) {
        return fallbackResult;
      }
    } catch (e) {
      // If Clerk fails, fallback to normal rate limiting
    }
  }
  if (!ratelimit || !byokRateLimit || !clerkUserId) {
    return fallbackResult;
  }

  const result = isBringingKey
    ? await byokRateLimit.limit(BYOK_PREFIX + clerkUserId)
    : await ratelimit.limit(clerkUserId);

  return {
    success: result.success,
    remaining: result.remaining,
    limit: result.limit,
    reset: result.reset,
  };
};

export const getRemainingResearch = async ({
  clerkUserId,
  isBringingKey,
}: {
  clerkUserId?: string;
  isBringingKey?: boolean;
}) => {
  // Unlimited for users with together.ai email
  if (clerkUserId) {
    try {
      const client = await clerkClient();
      const user = await client.users.getUser(clerkUserId);
      const email = user.emailAddresses?.[0]?.emailAddress;
      if (email && email.endsWith("@together.ai")) {
        return fallbackResult;
      }
    } catch (e) {
      // If Clerk fails, fallback to normal rate limiting
    }
  }
  if (!ratelimit || !byokRateLimit || !clerkUserId) {
    return fallbackResult;
  }

  try {
    const result = isBringingKey
      ? await byokRateLimit.getRemaining(BYOK_PREFIX + clerkUserId)
      : await ratelimit.getRemaining(clerkUserId);

    return result;
  } catch (e) {
    console.log(e);
    return fallbackResult;
  }
};



================================================
FILE: src/lib/utils.ts
================================================
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getDomainFromUrl(url: string): string {
  try {
    return new URL(url).hostname;
  } catch {
    return url;
  }
}

// Function to clean markdown to pure text
export function cleanMarkdownToText(markdownText: string | undefined): string {
  if (!markdownText) {
    return "";
  }

  let cleanText = markdownText;

  // Remove headers
  cleanText = cleanText.replace(/^#+\s/gm, "");

  // Remove bold and italics
  cleanText = cleanText.replace(/(\*\*|__)(.*?)\1/g, "$2");
  cleanText = cleanText.replace(/(\*|_)(.*?)\1/g, "$2");

  // Remove links, keeping only the link text
  cleanText = cleanText.replace(/\[(.*?)\]\(.*?\)/g, "$1");

  // Remove images, keeping only the alt text
  cleanText = cleanText.replace(/!\[(.*?)\]\(.*?\)/g, "$1");

  // Remove blockquotes
  cleanText = cleanText.replace(/^>\s/gm, "");

  // Remove list markers
  cleanText = cleanText.replace(/^(\s*)[-*+]\s/gm, "$1");
  cleanText = cleanText.replace(/^(\s*)\d+\.\s/gm, "$1");

  // Remove horizontal rules
  cleanText = cleanText.replace(/^-{3,}\s*$/gm, "");
  cleanText = cleanText.replace(/^\*{3,}\s*$/gm, "");
  cleanText = cleanText.replace(/^__{3,}\s*$/gm, "");

  // Remove code blocks
  cleanText = cleanText.replace(/```[\s\S]*?```/g, "");
  cleanText = cleanText.replace(/`([^`]+)`/g, "$1");

  // Remove extra whitespace and newlines
  cleanText = cleanText.replace(/\s+/g, " ").trim();

  return cleanText;
}

/**
 * Extracts all headings (h1, h2, h3) from markdown text.
 * Returns an array of objects: { level: number, text: string }
 */
export function extractMarkdownHeadings(
  markdownText: string
): Array<{ level: number; text: string }> {
  if (!markdownText) return [];
  const headingRegex = /^(#{1,3})\s+(.+)$/gm;
  const headings: Array<{ level: number; text: string }> = [];
  let match;
  while ((match = headingRegex.exec(markdownText)) !== null) {
    const level = match[1].length;
    if (level >= 1 && level <= 3) {
      headings.push({ level, text: match[2].trim() });
    }
  }
  return headings;
}

// Slugify function to create a short, valid filename
export function slugifyFilename(str: string, maxLength = 24): string {
  return (
    str
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "")
      .slice(0, maxLength) || "report"
  );
}


