/**
 * Flexible Tutor Chat Component
 * A flexible AI tutor interface similar to llamatutor that works without requiring file uploads
 */

import React, { useState, useRef, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Send,
  Loader2,
  BookOpen,
  Search,
  Code,
  Image as ImageIcon,
  FileText,
  Lightbulb,
  Brain,
  Calculator,
  Globe
} from "lucide-react";
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
  codeOutput?: string;
  visualizations?: any[];
}

interface FlexibleTutorChatProps {
  onNewConversation?: () => void;
}

const educationLevels = [
  { value: 'basic', label: 'Basic Level' },
  { value: 'intermediate', label: 'Intermediate Level' },
  { value: 'advanced', label: 'Advanced Level' },
  { value: 'expert', label: 'Expert Level' }
];

const suggestions = [
  { icon: Calculator, text: "Explain calculus derivatives", category: "Math" },
  { icon: Brain, text: "How does photosynthesis work?", category: "Science" },
  { icon: Globe, text: "World War II causes and effects", category: "History" },
  { icon: Code, text: "Python programming basics", category: "Programming" },
  { icon: BookOpen, text: "Shakespeare's writing style", category: "Literature" },
  { icon: Lightbulb, text: "How do solar panels work?", category: "Technology" }
];

export function FlexibleTutorChat({ onNewConversation }: FlexibleTutorChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [educationLevel, setEducationLevel] = useState('high-school');
  const [isLoading, setIsLoading] = useState(false);
  const [useCodeExecution, setUseCodeExecution] = useState(false);
  const [useWebSearch, setUseWebSearch] = useState(true);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (shouldAutoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Check if user has scrolled up manually
  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold
      setShouldAutoScroll(isAtBottom);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, shouldAutoScroll]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setShouldAutoScroll(true); // Enable auto-scroll for new conversation

    try {
      // Create assistant message placeholder
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        sources: [],
        codeOutput: undefined,
        visualizations: []
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Simulate streaming response
      await simulateStreamingResponse(assistantMessage.id, userMessage.content);

    } catch (error) {
      console.error('Error generating response:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === (Date.now() + 1).toString() 
          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.' }
          : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const simulateStreamingResponse = async (messageId: string, userQuery: string) => {
    try {
      // Check service availability
      const serviceStatus = integratedTutorService.getServiceStatus();

      if (!serviceStatus.gemini) {
        // Fallback to mock response if Gemini is not available
        const fallbackResponse = `I apologize, but the AI service is currently unavailable.

**To enable AI tutoring, please:**

1. **Set up your Gemini API key:**
   - Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Add it to your environment variables as \`NEXT_PUBLIC_GEMINI_API_KEY\` or \`GEMINI_API_KEY\`

2. **Optional - Enable web search:**
   - Get a Tavily API key from [Tavily](https://tavily.com)
   - Add it as \`NEXT_PUBLIC_TAVILY_API_KEY\` or \`TAVILY_API_KEY\`

Once configured, I'll be able to help you learn any topic with personalized explanations, code examples, and web-sourced information!`;

        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? { ...msg, content: fallbackResponse }
            : msg
        ));
        return;
      }

      // Use the integrated tutor service
      const response = await integratedTutorService.generateResponse(
        userQuery,
        {
          educationLevel,
          useCodeExecution,
          useWebSearch,
          useRAG: false
        },
        messages.filter(m => m.role === 'user' || m.role === 'assistant').map(m => ({
          role: m.role,
          content: m.content
        })),
        (chunk: string) => {
          // Stream chunks to the UI
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? { ...msg, content: msg.content + chunk }
              : msg
          ));
        }
      );

      // Update with final response data
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: response.content,
              sources: response.sources,
              codeOutput: response.codeOutput,
              visualizations: response.visualizations
            }
          : msg
      ));

    } catch (error) {
      console.error('Error generating AI response:', error);
      toast.error('Failed to generate response. Please try again.');

      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: 'I apologize, but I encountered an error while generating a response. Please try again or check your internet connection.'
            }
          : msg
      ));
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    textareaRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const startNewConversation = () => {
    setMessages([]);
    setInputValue('');
    onNewConversation?.();
  };

  return (
    <div className="flex flex-col h-full max-w-5xl mx-auto bg-white/50 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200/50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm rounded-t-lg">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg">
            <Brain className="w-7 h-7 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              AI Tutor Chat
            </h1>
            <p className="text-sm text-gray-600">Your personal learning assistant</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Select value={educationLevel} onValueChange={setEducationLevel}>
            <SelectTrigger className="w-44 bg-white/80 border-gray-200/50 shadow-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white/95 backdrop-blur-sm border-gray-200/50">
              {educationLevels.map(level => (
                <SelectItem key={level.value} value={level.value}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {messages.length > 0 && (
            <Button
              variant="outline"
              onClick={startNewConversation}
              className="bg-white/80 border-gray-200/50 hover:bg-white shadow-sm"
            >
              New Chat
            </Button>
          )}
        </div>
      </div>

      {/* Options */}
      <div className="flex items-center space-x-6 p-4 bg-gradient-to-r from-gray-50/80 to-blue-50/50 border-b border-gray-200/50">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="webSearch"
            checked={useWebSearch}
            onChange={(e) => setUseWebSearch(e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="webSearch" className="text-sm font-medium flex items-center text-gray-700">
            <Search className="w-4 h-4 mr-2 text-blue-500" />
            Web Search
          </label>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="codeExecution"
            checked={useCodeExecution}
            onChange={(e) => setUseCodeExecution(e.target.checked)}
            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <label htmlFor="codeExecution" className="text-sm font-medium flex items-center text-gray-700">
            <Code className="w-4 h-4 mr-2 text-indigo-500" />
            Code Execution
          </label>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        onScroll={handleScroll}
      >
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              What would you like to learn today?
            </h2>
            <p className="text-gray-600 mb-8">
              Ask me anything! I can help with any subject at your education level.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-w-4xl mx-auto">
              {suggestions.map((suggestion, index) => (
                <Card 
                  key={index}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleSuggestionClick(suggestion.text)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <suggestion.icon className="w-5 h-5 text-blue-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{suggestion.text}</p>
                        <Badge variant="secondary" className="text-xs mt-1">
                          {suggestion.category}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-3xl ${message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white border'} rounded-lg p-4`}>
                  {message.role === 'assistant' ? (
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown
                        components={{
                          code({ node, inline, className, children, ...props }) {
                            const match = /language-(\w+)/.exec(className || '');
                            return !inline && match ? (
                              <SyntaxHighlighter
                                style={tomorrow}
                                language={match[1]}
                                PreTag="div"
                                {...props}
                              >
                                {String(children).replace(/\n$/, '')}
                              </SyntaxHighlighter>
                            ) : (
                              <code className={className} {...props}>
                                {children}
                              </code>
                            );
                          }
                        }}
                      >
                        {message.content}
                      </ReactMarkdown>

                      {message.sources && message.sources.length > 0 && (
                        <div className="mt-4 pt-4 border-t">
                          <h4 className="font-medium text-gray-900 mb-2">Sources:</h4>
                          <div className="space-y-2">
                            {message.sources.map((source, index) => (
                              <div key={index} className="text-sm">
                                <a 
                                  href={source.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline font-medium"
                                >
                                  {source.title}
                                </a>
                                <p className="text-gray-600 text-xs mt-1">{source.snippet}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p>{message.content}</p>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input */}
      <div className="border-t border-gray-200/50 p-6 bg-white/80 backdrop-blur-sm rounded-b-lg">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <Textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me anything about any subject..."
            className="flex-1 min-h-[60px] max-h-32 resize-none bg-white/80 border-gray-200/50 focus:border-blue-400 focus:ring-blue-400/20 shadow-sm"
            disabled={isLoading}
          />
          <Button
            type="submit"
            disabled={!inputValue.trim() || isLoading}
            className="px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
