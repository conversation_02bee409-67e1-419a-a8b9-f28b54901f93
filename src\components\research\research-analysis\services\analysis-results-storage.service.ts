import { supabase } from '@/lib/supabase';
import { 
  LiteratureReview, 
  GapAnalysis, 
  ResearchHypothesis, 
  ResearchGap,
  ResearchTheme,
  ResearchOpportunity 
} from '../types';

/**
 * Service for managing analysis results in Supabase database
 * Handles literature reviews, gap analyses, hypotheses, and related data
 */
export class AnalysisResultsStorageService {

  /**
   * Save a literature review to the database
   */
  async saveLiteratureReview(review: LiteratureReview): Promise<{ data: any | null; error: any }> {
    try {


      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      // Check if review already exists
      const { data: existingReview, error: checkError } = await supabase
        .from('literature_reviews')
        .select('id')
        .eq('id', review.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        return { data: null, error: checkError };
      }

      if (existingReview) {
        return { data: existingReview, error: null };
      }

      // Prepare review data
      const reviewData = {
        id: review.id,
        user_id: user.id,
        title: review.title,
        topic: review.title.replace('Literature Review: ', ''),
        document_ids: review.documents,
        citation_style: review.citationStyle,
        status: review.status,
        word_count: review.wordCount,
        ai_model_used: 'google/gemini-2.5-flash', // Default model used
        generation_metadata: {
          generatedAt: review.generatedAt.toISOString(),
          exportOptions: review.exportOptions
        }
      };

      // Insert literature review
      const { data: savedReview, error: reviewError } = await supabase
        .from('literature_reviews')
        .insert(reviewData)
        .select()
        .single();

      if (reviewError) {
        console.error('Error saving literature review:', reviewError);
        return { data: null, error: reviewError };
      }

      // Save review sections
      if (review.sections && review.sections.length > 0) {
        const sectionsData = review.sections.map(section => ({
          id: section.id,
          review_id: review.id,
          title: section.title,
          content: section.content,
          section_type: section.type,
          section_order: section.order,
          source_document_ids: section.sourceDocuments,
          citations: section.citations
        }));

        const { error: sectionsError } = await supabase
          .from('literature_review_sections')
          .insert(sectionsData);

        if (sectionsError) {
          return { data: null, error: sectionsError };
        }
      }

      // Log activity
      await this.logActivity(user.id, 'generation', 'literature_review', review.id, `Generated literature review: ${review.title}`);

      return { data: savedReview, error: null };
    } catch (error) {
      console.error('Error in saveLiteratureReview:', error);
      return { data: null, error };
    }
  }

  /**
   * Get literature reviews for the current user
   */
  async getUserLiteratureReviews(limit: number = 20): Promise<{ data: LiteratureReview[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: [], error: null }; // Return empty array instead of error for better UX
      }

      const { data: reviews, error: reviewsError } = await supabase
        .from('literature_reviews')
        .select(`
          *,
          literature_review_sections (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (reviewsError) {
        return { data: [], error: reviewsError };
      }

      // Convert to LiteratureReview format
      const literatureReviews: LiteratureReview[] = reviews?.map(review => ({
        id: review.id,
        title: review.title,
        documents: review.document_ids || [],
        sections: review.literature_review_sections?.map((section: any) => ({
          id: section.id,
          title: section.title,
          content: section.content,
          type: section.section_type,
          order: section.section_order,
          sourceDocuments: section.source_document_ids || [],
          citations: section.citations || []
        })) || [],
        citationStyle: review.citation_style,
        status: review.status,
        generatedAt: new Date(review.created_at),
        wordCount: review.word_count || 0,
        exportOptions: review.generation_metadata?.exportOptions || ['PDF', 'Word']
      })) || [];

      return { data: literatureReviews, error: null };
    } catch (error) {
      console.error('Error in getUserLiteratureReviews:', error);
      return { data: null, error };
    }
  }

  /**
   * Save a gap analysis to the database
   */
  async saveGapAnalysis(analysis: GapAnalysis): Promise<{ data: any | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      // Check if gap analysis already exists
      const { data: existingAnalysis, error: checkError } = await supabase
        .from('research_gap_analyses')
        .select('id')
        .eq('id', analysis.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        return { data: null, error: checkError };
      }

      if (existingAnalysis) {
        return { data: existingAnalysis, error: null };
      }

      // Save gap analysis
      const analysisData = {
        id: analysis.id,
        user_id: user.id,
        title: `Gap Analysis - ${new Date().toLocaleDateString()}`,
        document_ids: analysis.documentIds,
        summary: analysis.summary,
        ai_model_used: 'google/gemini-2.5-flash',
        analysis_metadata: {
          generatedAt: analysis.generatedAt.toISOString(),
          gapCount: analysis.gaps.length,
          themeCount: analysis.themes.length,
          opportunityCount: analysis.opportunities.length
        }
      };

      const { data: savedAnalysis, error: analysisError } = await supabase
        .from('research_gap_analyses')
        .insert(analysisData)
        .select()
        .single();

      if (analysisError) {
        console.error('Error saving gap analysis:', analysisError);
        return { data: null, error: analysisError };
      }

      // Save gaps
      if (analysis.gaps && analysis.gaps.length > 0) {
        const gapsData = analysis.gaps.map(gap => ({
          id: gap.id,
          analysis_id: analysis.id,
          title: gap.title,
          description: gap.description,
          category: gap.category,
          priority: gap.priority,
          feasibility: gap.feasibility,
          impact: gap.impact,
          collaboration_potential: gap.collaborationPotential,
          related_document_ids: gap.relatedDocuments,
          suggested_methods: gap.suggestedMethods,
          required_resources: gap.requiredResources,
          timeline_estimate: gap.timelineEstimate
        }));

        const { error: gapsError } = await supabase
          .from('research_gaps')
          .insert(gapsData);

        if (gapsError) {
          return { data: null, error: gapsError };
        }
      }

      // Save themes
      if (analysis.themes.length > 0) {
        const themesData = analysis.themes.map(theme => ({
          id: theme.id,
          analysis_id: analysis.id,
          name: theme.name,
          description: theme.description,
          frequency: theme.frequency,
          related_document_ids: theme.relatedDocuments,
          key_terms: theme.keyTerms,
          evolution_data: theme.evolution
        }));

        const { error: themesError } = await supabase
          .from('research_themes')
          .insert(themesData);

        if (themesError) {
          console.error('Error saving research themes:', themesError);
        }
      }

      // Save opportunities
      if (analysis.opportunities.length > 0) {
        const opportunitiesData = analysis.opportunities.map(opp => ({
          id: opp.id,
          analysis_id: analysis.id,
          title: opp.title,
          description: opp.description,
          opportunity_type: opp.type,
          difficulty: opp.difficulty,
          potential_impact: opp.potentialImpact,
          required_expertise: opp.requiredExpertise,
          suggested_approach: opp.suggestedApproach
        }));

        const { error: opportunitiesError } = await supabase
          .from('research_opportunities')
          .insert(opportunitiesData);

        if (opportunitiesError) {
          console.error('Error saving research opportunities:', opportunitiesError);
        }
      }

      // Log activity
      await this.logActivity(user.id, 'analysis', 'gap_analysis', analysis.id, `Completed gap analysis with ${analysis.gaps.length} gaps identified`);

      return { data: savedAnalysis, error: null };
    } catch (error) {
      console.error('Error in saveGapAnalysis:', error);
      return { data: null, error };
    }
  }

  /**
   * Get gap analyses for the current user
   */
  async getUserGapAnalyses(limit: number = 20): Promise<{ data: GapAnalysis[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: [], error: null };
      }

      const { data: analyses, error: analysesError } = await supabase
        .from('research_gap_analyses')
        .select(`
          *,
          research_gaps (*),
          research_themes (*),
          research_opportunities (*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (analysesError) {
        console.error('Error getting gap analyses:', analysesError);
        return { data: [], error: null };
      }

      // Convert to GapAnalysis format
      const gapAnalyses: GapAnalysis[] = analyses?.map(analysis => ({
        id: analysis.id,
        documentIds: analysis.document_ids || [],
        gaps: analysis.research_gaps?.map((gap: any) => ({
          id: gap.id,
          title: gap.title,
          description: gap.description,
          category: gap.category,
          priority: gap.priority,
          feasibility: gap.feasibility,
          impact: gap.impact,
          collaborationPotential: gap.collaboration_potential,
          relatedDocuments: gap.related_document_ids || [],
          suggestedMethods: gap.suggested_methods || [],
          requiredResources: gap.required_resources || [],
          timelineEstimate: gap.timeline_estimate
        })) || [],
        themes: analysis.research_themes?.map((theme: any) => ({
          id: theme.id,
          name: theme.name,
          description: theme.description,
          frequency: theme.frequency,
          relatedDocuments: theme.related_document_ids || [],
          keyTerms: theme.key_terms || [],
          evolution: theme.evolution_data || []
        })) || [],
        opportunities: analysis.research_opportunities?.map((opp: any) => ({
          id: opp.id,
          title: opp.title,
          description: opp.description,
          type: opp.opportunity_type,
          difficulty: opp.difficulty,
          potentialImpact: opp.potential_impact,
          requiredExpertise: opp.required_expertise || [],
          suggestedApproach: opp.suggested_approach || []
        })) || [],
        generatedAt: new Date(analysis.created_at),
        summary: analysis.summary || 'Gap analysis completed successfully'
      })) || [];

      return { data: gapAnalyses, error: null };
    } catch (error) {
      console.error('Error in getUserGapAnalyses:', error);
      return { data: null, error };
    }
  }

  /**
   * Save research hypotheses to the database
   */
  async saveResearchHypotheses(
    hypotheses: ResearchHypothesis[],
    gapAnalysisId?: string
  ): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      const hypothesesData = hypotheses.map(hypothesis => ({
        id: hypothesis.id,
        user_id: user.id,
        gap_analysis_id: gapAnalysisId,
        statement: hypothesis.statement,
        hypothesis_type: hypothesis.type,
        variables: hypothesis.variables,
        testability: hypothesis.testability,
        novelty: hypothesis.novelty,
        significance: hypothesis.significance,
        suggested_methodology: hypothesis.suggestedMethodology,
        expected_outcomes: hypothesis.expectedOutcomes,
        limitations: hypothesis.limitations,
        required_resources: hypothesis.requiredResources,
        timeline: hypothesis.timeline,
        related_gap_ids: hypothesis.relatedGaps,
        ai_model_used: 'google/gemini-2.5-flash'
      }));

      const { data: savedHypotheses, error: hypothesesError } = await supabase
        .from('research_hypotheses')
        .insert(hypothesesData)
        .select();

      if (hypothesesError) {
        console.error('Error saving research hypotheses:', hypothesesError);
        return { data: null, error: hypothesesError };
      }

      // Log activity
      await this.logActivity(user.id, 'generation', 'hypothesis', '', `Generated ${hypotheses.length} research hypotheses`);

      return { data: savedHypotheses, error: null };
    } catch (error) {
      console.error('Error in saveResearchHypotheses:', error);
      return { data: null, error };
    }
  }

  /**
   * Get research hypotheses for the current user
   */
  async getUserResearchHypotheses(limit: number = 50): Promise<{ data: ResearchHypothesis[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      const { data: hypotheses, error: hypothesesError } = await supabase
        .from('research_hypotheses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (hypothesesError) {
        console.error('Error getting research hypotheses:', hypothesesError);
        return { data: null, error: hypothesesError };
      }

      // Convert to ResearchHypothesis format
      const researchHypotheses: ResearchHypothesis[] = hypotheses?.map(hypothesis => ({
        id: hypothesis.id,
        statement: hypothesis.statement,
        type: hypothesis.hypothesis_type,
        variables: hypothesis.variables || {},
        testability: hypothesis.testability,
        novelty: hypothesis.novelty,
        significance: hypothesis.significance,
        suggestedMethodology: hypothesis.suggested_methodology || [],
        expectedOutcomes: hypothesis.expected_outcomes || [],
        limitations: hypothesis.limitations || [],
        requiredResources: hypothesis.required_resources || [],
        timeline: hypothesis.timeline,
        relatedGaps: hypothesis.related_gap_ids || []
      })) || [];

      return { data: researchHypotheses, error: null };
    } catch (error) {
      console.error('Error in getUserResearchHypotheses:', error);
      return { data: null, error };
    }
  }

  /**
   * Get user activity history
   */
  async getUserActivity(limit: number = 50): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      const { data: activities, error: activitiesError } = await supabase
        .from('research_analysis_activity')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (activitiesError) {
        console.error('Error getting user activity:', activitiesError);
        return { data: null, error: activitiesError };
      }

      return { data: activities || [], error: null };
    } catch (error) {
      console.error('Error in getUserActivity:', error);
      return { data: null, error };
    }
  }

  /**
   * Delete analysis results
   */
  async deleteAnalysisResult(
    resultType: 'literature_review' | 'gap_analysis' | 'hypothesis',
    resultId: string
  ): Promise<{ success: boolean; error?: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: userError || new Error('User not authenticated') };
      }

      let tableName: string;
      switch (resultType) {
        case 'literature_review':
          tableName = 'literature_reviews';
          break;
        case 'gap_analysis':
          tableName = 'research_gap_analyses';
          break;
        case 'hypothesis':
          tableName = 'research_hypotheses';
          break;
        default:
          return { success: false, error: new Error('Invalid result type') };
      }

      const { error: deleteError } = await supabase
        .from(tableName)
        .delete()
        .eq('id', resultId)
        .eq('user_id', user.id);

      if (deleteError) {
        console.error('Error deleting analysis result:', deleteError);
        return { success: false, error: deleteError };
      }

      // Log activity
      await this.logActivity(user.id, 'delete', resultType, resultId, `Deleted ${resultType}: ${resultId}`);

      return { success: true };
    } catch (error) {
      console.error('Error in deleteAnalysisResult:', error);
      return { success: false, error };
    }
  }

  /**
   * Log user activity
   */
  private async logActivity(
    userId: string,
    activityType: string,
    entityType: string,
    entityId: string,
    description: string,
    metadata?: any
  ): Promise<void> {
    try {
      await supabase
        .from('research_analysis_activity')
        .insert({
          user_id: userId,
          activity_type: activityType,
          entity_type: entityType,
          entity_id: entityId,
          description,
          metadata: metadata || {}
        });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }
}

// Export singleton instance
export const analysisResultsStorageService = new AnalysisResultsStorageService();
