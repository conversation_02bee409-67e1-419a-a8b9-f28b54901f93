import { LucideIcon } from "lucide-react";

// Presentation metadata and configuration
export interface PresentationMetadata {
  title: string;
  topic: string;
  slideCount: number;
  style: 'professional' | 'casual' | 'academic' | 'creative';
  language: string;
  audience: string;
  description?: string;
}

// Individual slide structure
export interface Slide {
  id: string;
  title: string;
  content: any; // Plate.js value type
  layout: SlideLayout;
  order: number;
  notes?: string;
  imageUrl?: string;
  backgroundColor?: string;
}

// Slide layout types
export type SlideLayout = 
  | 'title-slide'
  | 'content-slide' 
  | 'two-column'
  | 'image-text'
  | 'bullet-points'
  | 'quote'
  | 'conclusion'
  | 'section-divider';

// Presentation structure
export interface Presentation {
  id: string;
  metadata: PresentationMetadata;
  slides: Slide[];
  outline: string[];
  theme: PresentationTheme;
  status: 'draft' | 'generating-outline' | 'generating-slides' | 'completed' | 'error';
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
}

// Theme system
export interface PresentationTheme {
  id: string;
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    accent: string;
    muted: string;
  };
  fonts: {
    heading: string;
    body: string;
    size: {
      title: string;
      heading: string;
      body: string;
    };
  };
  layout: {
    padding: string;
    spacing: string;
    borderRadius: string;
  };
  preview?: string; // Preview image URL
}

// Generation state and progress
export interface GenerationState {
  isGeneratingOutline: boolean;
  isGeneratingSlides: boolean;
  currentSlideIndex: number;
  totalSlides: number;
  progress: number;
  error?: string;
  stage: 'idle' | 'outline' | 'slides' | 'complete' | 'error';
}

// AI generation options
export interface PresentationGenerationOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  includeImages: boolean;
  includeSpeakerNotes: boolean;
}

// Export options
export interface ExportOptions {
  format: 'pdf' | 'pptx' | 'html' | 'markdown';
  includeNotes: boolean;
  quality: 'low' | 'medium' | 'high';
  theme: boolean;
}

// Slide template definition
export interface SlideTemplate {
  id: string;
  name: string;
  description: string;
  layout: SlideLayout;
  icon: LucideIcon;
  preview?: string;
  defaultContent: any; // Plate.js value
}

// User inputs for presentation creation
export interface UserInputs {
  metadata: PresentationMetadata;
  generationOptions: PresentationGenerationOptions;
}

// Outline item for review phase
export interface OutlineItem {
  id: string;
  title: string;
  description: string;
  order: number;
  slideLayout: SlideLayout;
  estimatedDuration?: number;
}

// AI model options for presentation generation
export interface AIModelOption {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  maxTokens: number;
  supportsImages: boolean;
  recommended?: boolean;
}

// Presentation analytics and metrics
export interface PresentationMetrics {
  totalSlides: number;
  estimatedDuration: number;
  wordCount: number;
  readabilityScore?: number;
  lastModified: Date;
}

// Error types for presentation generation
export interface PresentationError {
  type: 'generation' | 'export' | 'validation' | 'network';
  message: string;
  details?: any;
  timestamp: Date;
}

// Presentation history for saved presentations
export interface PresentationHistory {
  id: string;
  title: string;
  topic: string;
  slideCount: number;
  theme: string;
  createdAt: Date;
  lastModified: Date;
  status: Presentation['status'];
  preview?: string;
}
