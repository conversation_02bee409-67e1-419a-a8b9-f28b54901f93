<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tutor Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #007bff;
            font-weight: bold;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e9ecef;
            white-space: pre-wrap;
        }
        .education-levels {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .education-level {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .education-level:hover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .education-level.selected {
            background-color: #2196f3;
            color: white;
            border-color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 AI Tutor Test Page</h1>
            <p>Test the AI Tutor functionality with both OpenRouter and Google Gemini models</p>
        </div>

        <div class="test-section">
            <h3>📋 Service Configuration Status</h3>
            <div id="config-status">
                <p class="info">Checking API configuration...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Education Level Selection</h3>
            <p>Select your education level for testing:</p>
            <div class="education-levels">
                <div class="education-level" data-level="elementary">
                    📚 Elementary School<br>
                    <small>Ages 6-11</small>
                </div>
                <div class="education-level" data-level="middle-school">
                    🏫 Middle School<br>
                    <small>Ages 11-14</small>
                </div>
                <div class="education-level selected" data-level="high-school">
                    🎓 High School<br>
                    <small>Ages 14-18</small>
                </div>
                <div class="education-level" data-level="college">
                    🏛️ College<br>
                    <small>Ages 18-22</small>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🤖 AI Model Testing</h3>
            <p>Test different AI models with a sample tutoring question:</p>
            
            <div style="margin: 15px 0;">
                <label for="topic-input"><strong>Topic:</strong></label><br>
                <input type="text" id="topic-input" value="Quantum Physics" style="width: 200px; padding: 8px; margin: 5px 0;">
            </div>
            
            <div style="margin: 15px 0;">
                <label for="question-input"><strong>Question:</strong></label><br>
                <textarea id="question-input" rows="3" style="width: 100%; padding: 8px; margin: 5px 0;">What is quantum entanglement and how does it work?</textarea>
            </div>

            <div style="margin: 15px 0;">
                <button onclick="testGeminiModel()">Test Google Gemini 2.5 Flash</button>
                <button onclick="testOpenRouterModel()">Test OpenRouter Claude 3.5 Sonnet</button>
                <button onclick="testBothModels()">Test Both Models</button>
            </div>

            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ Model Configuration</h3>
            <div id="model-info">
                <p class="info">Loading model information...</p>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedEducationLevel = 'high-school';
        let tutorService = null;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkConfiguration();
            setupEducationLevelSelection();
            loadModelInfo();
        });

        function setupEducationLevelSelection() {
            const levels = document.querySelectorAll('.education-level');
            levels.forEach(level => {
                level.addEventListener('click', function() {
                    levels.forEach(l => l.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedEducationLevel = this.dataset.level;
                });
            });
        }

        function checkConfiguration() {
            const statusDiv = document.getElementById('config-status');
            
            // Check environment variables
            const openRouterKey = '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';
            const geminiKey = 'AIzaSyCT5njtIqo6yUfb22kLXI1Mrv2Z8NiLB0k';
            
            let status = '<h4>API Key Status:</h4>';
            
            if (openRouterKey && openRouterKey.length > 20) {
                status += '<p class="success">✅ OpenRouter API Key: Configured</p>';
            } else {
                status += '<p class="error">❌ OpenRouter API Key: Missing or Invalid</p>';
            }
            
            if (geminiKey && geminiKey.length > 20) {
                status += '<p class="success">✅ Google Gemini API Key: Configured</p>';
            } else {
                status += '<p class="error">❌ Google Gemini API Key: Missing or Invalid</p>';
            }
            
            statusDiv.innerHTML = status;
        }

        function loadModelInfo() {
            const modelInfoDiv = document.getElementById('model-info');
            
            const models = [
                {
                    name: "Google Gemini 2.5 Flash",
                    provider: "Google",
                    description: "Fast, efficient model perfect for interactive tutoring",
                    strengths: ["Speed", "Conversational", "Educational content"]
                },
                {
                    name: "Claude 3.5 Sonnet",
                    provider: "Anthropic (via OpenRouter)",
                    description: "Excellent for detailed explanations and complex topics",
                    strengths: ["Detailed explanations", "Complex reasoning", "Patient teaching"]
                }
            ];
            
            let html = '<h4>Available Models:</h4>';
            models.forEach(model => {
                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <strong>${model.name}</strong> (${model.provider})<br>
                        <small>${model.description}</small><br>
                        <em>Strengths: ${model.strengths.join(', ')}</em>
                    </div>
                `;
            });
            
            modelInfoDiv.innerHTML = html;
        }

        async function testGeminiModel() {
            await testModel('google/gemini-2.5-flash', 'Google Gemini 2.5 Flash');
        }

        async function testOpenRouterModel() {
            await testModel('anthropic/claude-3.5-sonnet', 'OpenRouter Claude 3.5 Sonnet');
        }

        async function testBothModels() {
            await testGeminiModel();
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between tests
            await testOpenRouterModel();
        }

        async function testModel(modelId, modelName) {
            const topic = document.getElementById('topic-input').value;
            const question = document.getElementById('question-input').value;
            const resultsDiv = document.getElementById('test-results');
            
            // Add loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'result';
            loadingDiv.innerHTML = `<p class="info">🔄 Testing ${modelName}...</p>`;
            resultsDiv.appendChild(loadingDiv);
            
            try {
                // Simulate API call (since we can't actually make the calls from this static page)
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                // Mock successful response
                const mockResponse = {
                    content: `Great question about ${topic}! This is a mock response from ${modelName}. 

In a real implementation, this would be a detailed explanation about ${question.toLowerCase()} tailored to the ${selectedEducationLevel.replace('-', ' ')} education level.

The AI would provide:
- Clear explanations appropriate for the education level
- Relevant examples and analogies
- Follow-up questions to check understanding
- Suggestions for further learning

Model used: ${modelId}
Education Level: ${selectedEducationLevel}
Topic: ${topic}`,
                    model: modelId,
                    confidence: 0.9,
                    tokens: 150
                };
                
                loadingDiv.innerHTML = `
                    <h4 class="success">✅ ${modelName} - Success</h4>
                    <p><strong>Topic:</strong> ${topic}</p>
                    <p><strong>Education Level:</strong> ${selectedEducationLevel.replace('-', ' ')}</p>
                    <p><strong>Model:</strong> ${mockResponse.model}</p>
                    <p><strong>Confidence:</strong> ${(mockResponse.confidence * 100).toFixed(1)}%</p>
                    <p><strong>Tokens:</strong> ${mockResponse.tokens}</p>
                    <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #28a745;">
                        <strong>Response:</strong><br>
                        ${mockResponse.content}
                    </div>
                `;
                
            } catch (error) {
                loadingDiv.innerHTML = `
                    <h4 class="error">❌ ${modelName} - Error</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
