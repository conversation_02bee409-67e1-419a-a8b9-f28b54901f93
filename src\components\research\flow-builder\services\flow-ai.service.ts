/**
 * Flow AI Service
 * AI-powered diagram generation using Google Gemini and OpenRouter APIs
 */

import { GoogleGenAI } from '@google/genai';
import { nanoid } from 'nanoid';
import {
  DiagramGenerationRequest,
  DiagramGenerationResponse,
  GeneratedDiagram,
  DiagramType,
  FlowBuilderError,
  DiagramGenerationError
} from '../types';
import {
  GENERATION_CONFIG,
  ERROR_MESSAGES,
  FLOW_AI_MODELS
} from '../constants';
import { FLOW_GENERATION_PROMPTS } from '../prompts';

/**
 * Flow AI Service Class
 * Handles AI-powered diagram generation with multiple providers
 */
class FlowAIService {
  private geminiAI: GoogleGenAI | null = null;
  private openRouterApiKey: string | null = null;

  constructor() {
    this.initializeServices();
  }

  /**
   * Initialize AI services
   */
  private initializeServices() {
    // Initialize Google Gemini
    const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (geminiApiKey && geminiApiKey.length > 20) {
      this.geminiAI = new GoogleGenAI({ apiKey: geminiApiKey });
    }

    // Initialize OpenRouter
    const openRouterKey = import.meta.env.VITE_OPENROUTER_API_KEY;
    if (openRouterKey && openRouterKey.length > 20) {
      this.openRouterApiKey = openRouterKey;
    }
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.geminiAI || this.openRouterApiKey);
  }

  /**
   * Generate a diagram using AI
   */
  async generateDiagram(request: DiagramGenerationRequest): Promise<DiagramGenerationResponse> {
    console.log('🚀 FlowAI: Starting diagram generation', {
      model: request.model,
      inputType: request.input.type,
      descriptionLength: request.input.description.length
    });

    if (!this.isConfigured()) {
      console.error('❌ FlowAI: Service not configured');
      throw new DiagramGenerationError(ERROR_MESSAGES.API_KEY_MISSING);
    }

    try {
      const model = FLOW_AI_MODELS.find(m => m.id === request.model);
      if (!model) {
        console.error('❌ FlowAI: Invalid model', request.model);
        throw new DiagramGenerationError(`Invalid model: ${request.model}`);
      }

      console.log('✅ FlowAI: Using model', {
        id: model.id,
        name: model.name,
        provider: model.provider
      });

      let mermaidCode: string;

      if (model.provider === 'google') {
        mermaidCode = await this.generateWithGemini(request);
      } else {
        mermaidCode = await this.generateWithOpenRouter(request);
      }

      console.log('📝 FlowAI: Raw generated code', {
        length: mermaidCode.length,
        preview: mermaidCode.substring(0, 200) + '...'
      });

      // Clean and validate the generated code
      const cleanedCode = this.cleanMermaidCode(mermaidCode);

      console.log('🧹 FlowAI: Cleaned code', {
        originalLength: mermaidCode.length,
        cleanedLength: cleanedCode.length,
        preview: cleanedCode.substring(0, 200) + '...'
      });

      if (!cleanedCode) {
        console.error('❌ FlowAI: Generated code is empty after cleaning');
        throw new DiagramGenerationError('Generated diagram code is empty or invalid');
      }

      // Create the diagram object
      const diagram: GeneratedDiagram = {
        id: nanoid(),
        metadata: {
          title: this.extractTitleFromDescription(request.input.description),
          description: request.input.description,
          type: request.input.type || this.detectDiagramType(cleanedCode),
          direction: request.input.direction,
          keywords: this.extractKeywords(request.input.description),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        mermaidCode: cleanedCode,
        status: 'ready',
        generatedAt: new Date(),
        model: request.model
      };

      console.log('✅ FlowAI: Successfully generated diagram', {
        diagramId: diagram.id,
        title: diagram.metadata.title,
        type: diagram.metadata.type,
        codeLines: diagram.mermaidCode.split('\n').length
      });

      return {
        success: true,
        diagram,
        suggestions: this.generateSuggestions(request.input.description, diagram.metadata.type)
      };

    } catch (error: any) {
      console.error('❌ FlowAI: Diagram generation error:', error);

      return {
        success: false,
        error: error.message || ERROR_MESSAGES.GENERATION_FAILED
      };
    }
  }

  /**
   * Generate diagram using Google Gemini
   */
  private async generateWithGemini(request: DiagramGenerationRequest): Promise<string> {
    console.log('🤖 Gemini: Starting generation');

    if (!this.geminiAI) {
      console.error('❌ Gemini: Not configured');
      throw new DiagramGenerationError('Google Gemini not configured');
    }

    const prompt = this.buildPrompt(request);

    console.log('📝 Gemini: Built prompt', {
      promptLength: prompt.length,
      preview: prompt.substring(0, 300) + '...'
    });

    try {
      const config = {
        generationConfig: {
          maxOutputTokens: request.maxTokens || GENERATION_CONFIG.DEFAULT_MAX_TOKENS,
          temperature: request.temperature || GENERATION_CONFIG.DEFAULT_TEMPERATURE
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: prompt }]
      }];

      const response = await this.geminiAI.models.generateContentStream({
        model: 'gemini-2.5-pro',
        config,
        contents
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
        }
      }

      console.log('✅ Gemini: Generation complete', {
        responseLength: fullText.length,
        preview: fullText.substring(0, 200) + '...'
      });

      return fullText;

    } catch (error: any) {
      console.error('❌ Gemini: Generation error:', error);
      throw new DiagramGenerationError(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Generate diagram using OpenRouter
   */
  private async generateWithOpenRouter(request: DiagramGenerationRequest): Promise<string> {
    if (!this.openRouterApiKey) {
      throw new DiagramGenerationError('OpenRouter not configured');
    }

    const prompt = this.buildPrompt(request);

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.openRouterApiKey}`
        },
        body: JSON.stringify({
          model: request.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: request.maxTokens || GENERATION_CONFIG.DEFAULT_MAX_TOKENS,
          temperature: request.temperature || GENERATION_CONFIG.DEFAULT_TEMPERATURE
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from OpenRouter API');
      }

      return data.choices[0].message.content;

    } catch (error: any) {
      console.error('OpenRouter generation error:', error);
      throw new DiagramGenerationError(`OpenRouter API error: ${error.message}`);
    }
  }

  /**
   * Build the AI prompt for diagram generation
   */
  private buildPrompt(request: DiagramGenerationRequest): string {
    const { input } = request;
    const diagramType = input.type || 'flowchart';
    
    let basePrompt = FLOW_GENERATION_PROMPTS.BASE_PROMPT;
    
    // Add type-specific instructions
    if (FLOW_GENERATION_PROMPTS.TYPE_SPECIFIC[diagramType]) {
      basePrompt += '\n\n' + FLOW_GENERATION_PROMPTS.TYPE_SPECIFIC[diagramType];
    }

    // Add examples if available
    if (FLOW_GENERATION_PROMPTS.EXAMPLES[diagramType]) {
      basePrompt += '\n\n' + FLOW_GENERATION_PROMPTS.EXAMPLES[diagramType];
    }

    // Build the specific request with enhanced error prevention
    let prompt = basePrompt + '\n\n' + FLOW_GENERATION_PROMPTS.GENERATION_REQUEST;

    // Add critical error prevention instructions
    prompt += `

CRITICAL ERROR PREVENTION:
- NEVER use special characters like /, \\, <, >, &, % in node labels
- ALWAYS use proper spacing around arrows (A --> B, not A-->B)
- ENSURE all quotes are properly closed
- VALIDATE syntax before outputting
- For ${diagramType} diagrams, follow the exact syntax patterns shown in examples
- Test the code mentally to ensure it will render without errors

VALIDATION CHECKLIST:
✓ Proper diagram type declaration
✓ Valid node/element syntax
✓ Correct arrow/connection format
✓ No special characters in labels
✓ Proper spacing and formatting
✓ Complete and renderable code`;

    prompt = prompt
      .replace('{DIAGRAM_TYPE}', diagramType)
      .replace('{DESCRIPTION}', input.description)
      .replace('{DIRECTION}', input.direction || 'TD')
      .replace('{ADDITIONAL_REQUIREMENTS}', input.additionalRequirements || 'None')
      .replace('{CONTEXT}', input.context || 'General research context');

    return prompt;
  }

  /**
   * Clean and validate Mermaid code
   */
  private cleanMermaidCode(rawCode: string): string {
    console.log('🧹 Cleaning Mermaid code', { originalLength: rawCode.length });

    if (!rawCode) {
      console.warn('⚠️ Empty raw code provided');
      return '';
    }

    // Remove markdown code blocks if present
    let cleaned = rawCode.replace(/```mermaid\n?/g, '').replace(/```\n?/g, '');

    // Remove extra whitespace and normalize line endings
    cleaned = cleaned.trim().replace(/\r\n/g, '\n');

    // Fix common syntax issues
    cleaned = this.fixMermaidSyntax(cleaned);

    // Ensure the code starts with a valid diagram type
    const lines = cleaned.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      console.warn('⚠️ No valid lines after cleaning');
      return '';
    }

    // Basic validation - ensure first line contains a diagram type
    const firstLine = lines[0].toLowerCase().trim();
    const validStarters = ['flowchart', 'graph', 'sequencediagram', 'gantt', 'pie', 'mindmap', 'timeline', 'journey', 'erdiagram', 'statediagram', 'classdiagram'];

    if (!validStarters.some(starter => firstLine.startsWith(starter))) {
      console.log('🔧 Adding flowchart declaration to code');
      // If no valid starter, assume it's a flowchart
      cleaned = 'flowchart TD\n' + cleaned;
    }

    console.log('✅ Code cleaning complete', {
      cleanedLength: cleaned.length,
      linesCount: cleaned.split('\n').length
    });

    return cleaned;
  }

  /**
   * Fix common Mermaid syntax issues
   */
  private fixMermaidSyntax(code: string): string {
    console.log('🔧 Fixing Mermaid syntax issues');

    let fixed = code;

    // Fix node labels with invalid characters
    // Replace problematic characters in node labels
    fixed = fixed.replace(/\[([^\]]*)\]/g, (match, label) => {
      // Remove or replace invalid characters
      const cleanLabel = label
        .replace(/[\/\\]/g, ' ') // Replace slashes with spaces
        .replace(/[<>]/g, '') // Remove angle brackets
        .replace(/[{}]/g, '') // Remove curly braces
        .replace(/[|]/g, ' ') // Replace pipes with spaces
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();

      return `[${cleanLabel}]`;
    });

    // Fix node IDs with invalid characters
    fixed = fixed.replace(/^(\s*)([A-Za-z0-9_]+)(\[.*?\])/gm, (match, indent, nodeId, label) => {
      // Ensure node IDs are valid (alphanumeric + underscore only)
      const cleanNodeId = nodeId.replace(/[^A-Za-z0-9_]/g, '');
      return `${indent}${cleanNodeId}${label}`;
    });

    // Fix arrow syntax issues
    fixed = fixed.replace(/-->/g, ' --> '); // Ensure spaces around arrows
    fixed = fixed.replace(/\s+-->\s+/g, ' --> '); // Normalize arrow spacing

    // Fix line endings and indentation
    const lines = fixed.split('\n').map(line => {
      // Remove excessive indentation
      return line.replace(/^\s{4,}/, '    '); // Max 4 spaces indentation
    });

    fixed = lines.join('\n');

    // Remove empty lines at start and end
    fixed = fixed.replace(/^\n+/, '').replace(/\n+$/, '');

    console.log('✅ Syntax fixing complete');

    return fixed;
  }

  /**
   * Detect diagram type from Mermaid code
   */
  private detectDiagramType(code: string): DiagramType {
    const firstLine = code.split('\n')[0]?.toLowerCase().trim() || '';
    
    if (firstLine.startsWith('flowchart') || firstLine.startsWith('graph')) return 'flowchart';
    if (firstLine.startsWith('sequencediagram')) return 'sequence';
    if (firstLine.startsWith('gantt')) return 'gantt';
    if (firstLine.startsWith('pie')) return 'pie';
    if (firstLine.startsWith('mindmap')) return 'mindmap';
    if (firstLine.startsWith('timeline')) return 'timeline';
    if (firstLine.startsWith('journey')) return 'user-journey';
    if (firstLine.startsWith('erdiagram')) return 'er';
    if (firstLine.startsWith('statediagram')) return 'state';
    if (firstLine.startsWith('classdiagram')) return 'class';
    
    return 'flowchart'; // Default fallback
  }

  /**
   * Extract title from description
   */
  private extractTitleFromDescription(description: string): string {
    // Take first sentence or first 50 characters
    const firstSentence = description.split('.')[0];
    if (firstSentence.length <= 50) {
      return firstSentence.trim();
    }
    
    return description.substring(0, 47).trim() + '...';
  }

  /**
   * Extract keywords from description
   */
  private extractKeywords(description: string): string[] {
    // Simple keyword extraction - can be enhanced with NLP
    const words = description.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Remove common words
    const commonWords = ['this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'];
    
    const keywords = words.filter(word => !commonWords.includes(word));
    
    // Return top 5 unique keywords
    return [...new Set(keywords)].slice(0, 5);
  }

  /**
   * Generate suggestions for improvement
   */
  private generateSuggestions(description: string, type: DiagramType): string[] {
    const suggestions: string[] = [];
    
    // Type-specific suggestions
    switch (type) {
      case 'flowchart':
        suggestions.push('Consider adding decision points for better flow control');
        suggestions.push('Add start and end nodes for clarity');
        break;
      case 'sequence':
        suggestions.push('Include activation boxes for active participants');
        suggestions.push('Add notes for complex interactions');
        break;
      case 'gantt':
        suggestions.push('Consider adding milestones for key deliverables');
        suggestions.push('Include dependencies between tasks');
        break;
    }
    
    // General suggestions
    if (description.length < 50) {
      suggestions.push('Provide more detailed description for better diagram generation');
    }
    
    return suggestions;
  }
}

// Export singleton instance
export const flowAIService = new FlowAIService();
