import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  File,
  X,
  Link,
  Globe,
  Plus,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Download,
  Eye,
  Trash2,
  FileUp
} from "lucide-react";

interface MultiDocumentUploaderProps {
  onUpload: (files: File[]) => void;
  maxFiles?: number;
  acceptedFormats?: string[];
  maxFileSize?: number;
}

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  preview?: string;
  error?: string;
}

export function MultiDocumentUploader({
  onUpload,
  maxFiles = 20,
  acceptedFormats = ['.pdf', '.doc', '.docx', '.txt'],
  maxFileSize = 50 * 1024 * 1024 // 50MB for research documents
}: MultiDocumentUploaderProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [doiInput, setDoiInput] = useState('');
  const [isProcessingDOI, setIsProcessingDOI] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFileSelection(files);
  }, []);

  // Handle file selection
  const handleFileSelection = useCallback((files: File[]) => {
    const validFiles = files.filter(file => {
      // Check file size
      if (file.size > maxFileSize) {
        toast.error(`File ${file.name} is too large. Maximum size is ${maxFileSize / 1024 / 1024}MB`);
        return false;
      }

      // Check file format
      const extension = '.' + file.name.split('.').pop()?.toLowerCase();
      const fileType = file.type.toLowerCase();
      
      // More flexible format checking
      const isValidFormat = acceptedFormats.includes(extension) || 
                           fileType === 'application/pdf' ||
                           fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                           fileType === 'application/msword' ||
                           fileType === 'text/plain';
      
      if (!isValidFormat) {
        toast.error(`File ${file.name} has unsupported format. Accepted formats: ${acceptedFormats.join(', ')}`);
        return false;
      }

      // Show format-specific warnings
      if (extension === '.pdf') {
        toast.success(`PDF file detected: ${file.name}. Text extraction will be performed automatically.`);
      } else if (extension === '.docx') {
        toast.info(`Word document detected: ${file.name}. Note: Full text extraction requires additional setup.`);
      } else if (extension === '.doc') {
        toast.warning(`Legacy Word document detected: ${file.name}. Consider converting to .docx or .txt for better compatibility.`);
      }

      return true;
    });

    // Check total files limit
    const totalFiles = uploadFiles.length + validFiles.length;
    if (totalFiles > maxFiles) {
      toast.error(`Too many files. Maximum allowed: ${maxFiles}`);
      return;
    }

    // Add files to upload queue
    const newUploadFiles: UploadFile[] = validFiles.map(file => ({
      file,
      id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      progress: 0,
      status: 'pending'
    }));

    setUploadFiles(prev => [...prev, ...newUploadFiles]);
  }, [uploadFiles, maxFiles, maxFileSize, acceptedFormats]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input changed:', e.target.files?.length);
    const files = Array.from(e.target.files || []);
    handleFileSelection(files);
    // Reset input
    e.target.value = '';
  }, [handleFileSelection]);

  // Remove file from queue
  const removeFile = useCallback((fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  // Clear all files
  const clearAllFiles = useCallback(() => {
    setUploadFiles([]);
  }, []);

  // Start upload process
  const startUpload = useCallback(async () => {
    if (uploadFiles.length === 0) return;

    const filesToUpload = uploadFiles.filter(f => f.status === 'pending').map(f => f.file);
    
    if (filesToUpload.length === 0) {
      toast.warning('No pending files to upload');
      return;
    }

    // Update status to uploading
    setUploadFiles(prev => prev.map(f => 
      f.status === 'pending' ? { ...f, status: 'uploading' as const } : f
    ));

    try {
      await onUpload(filesToUpload);
      
      // Update status to success
      setUploadFiles(prev => prev.map(f => 
        f.status === 'uploading' ? { ...f, status: 'success' as const, progress: 100 } : f
      ));
      
      toast.success(`Successfully uploaded ${filesToUpload.length} files!`);
    } catch (error) {
      // Update status to error
      setUploadFiles(prev => prev.map(f => 
        f.status === 'uploading' ? { 
          ...f, 
          status: 'error' as const, 
          error: 'Upload failed' 
        } : f
      ));
      
      toast.error('Upload failed. Please try again.');
    }
  }, [uploadFiles, onUpload]);

  // Fetch paper by DOI
  const fetchByDOI = useCallback(async () => {
    if (!doiInput.trim()) return;

    setIsProcessingDOI(true);
    
    try {
      // This is a placeholder - in a real implementation, you'd integrate with CrossRef or similar API
      toast.info('DOI fetching is not yet implemented in this demo');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setDoiInput('');
      toast.success('Paper fetched successfully!');
    } catch (error) {
      toast.error('Failed to fetch paper. Please check the DOI.');
    } finally {
      setIsProcessingDOI(false);
    }
  }, [doiInput]);

  // Get file icon
  const getFileIcon = useCallback((filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf': return '📄';
      case 'doc':
      case 'docx': return '📝';
      case 'txt': return '📄';
      default: return '📄';
    }
  }, []);

  // Get status color
  const getStatusColor = useCallback((status: UploadFile['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-600';
      case 'uploading': return 'bg-blue-100 text-blue-600';
      case 'success': return 'bg-green-100 text-green-600';
      case 'error': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  }, []);

  const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
  const uploadingFiles = uploadFiles.filter(f => f.status === 'uploading');
  const successFiles = uploadFiles.filter(f => f.status === 'success');
  const errorFiles = uploadFiles.filter(f => f.status === 'error');

  return (
    <div className="space-y-6">
      <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <FileUp className="h-6 w-6 text-blue-500" />
            Multi-Document Uploader
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload" className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">File Upload</TabsTrigger>
              <TabsTrigger value="doi" className="data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700">DOI Fetch</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upload" className="space-y-6 pt-4">
              {/* Drop Zone */}
              <div
                className={`border-2 border-dashed rounded-xl p-10 text-center transition-all duration-300 cursor-pointer ${
                  isDragging 
                    ? 'border-blue-500 bg-blue-50/70 shadow-md' 
                    : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50/50 hover:shadow-sm'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <div className="space-y-5">
                  <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-sm">
                    <Upload className="h-9 w-9 text-blue-600" />
                  </div>
                  
                  <div>
                    <p className="text-xl font-semibold text-gray-900">
                      Drag & drop your research papers here
                    </p>
                    <p className="text-gray-600 mt-2">
                      Or click to browse files from your computer
                    </p>
                    <p className="text-xs text-gray-500 mt-3">
                      Supported formats: PDF, Word (.docx), Text files • Max size: 50MB
                    </p>
                  </div>

                  <div className="flex items-center justify-center gap-4">
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,.txt,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain"
                      onChange={handleFileInputChange}
                      className="hidden"
                      id="file-upload"
                      ref={fileInputRef}
                    />
                    
                    <Button 
                      onClick={(e) => {
                        console.log('Browse button clicked');
                        e.stopPropagation();
                        fileInputRef.current?.click();
                      }}
                      className="cursor-pointer"
                      type="button"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Browse Files
                    </Button>
                  </div>

                  <div className="text-sm text-gray-500">
                    <p>Supported formats: PDF, Word (.doc/.docx), Text (.txt)</p>
                    <p>Maximum file size: {maxFileSize / 1024 / 1024}MB</p>
                    <p>Maximum files: {maxFiles}</p>
                    <p className="text-green-600 mt-1">
                      ✅ All document types now support automatic text extraction
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="doi" className="space-y-6">
              <div className="bg-blue-50 p-6 rounded-xl">
                <div className="flex items-start gap-3 mb-4">
                  <Globe className="h-6 w-6 text-blue-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-blue-900">Fetch Papers by DOI</h3>
                    <p className="text-blue-700 text-sm">
                      Enter DOI numbers to automatically fetch and analyze research papers
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <Input
                    placeholder="Enter DOI (e.g., 10.1038/nature12373)"
                    value={doiInput}
                    onChange={(e) => setDoiInput(e.target.value)}
                    className="flex-1"
                  />
                  <Button 
                    onClick={fetchByDOI}
                    disabled={!doiInput.trim() || isProcessingDOI}
                  >
                    {isProcessingDOI ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    Fetch
                  </Button>
                </div>
                
                {isProcessingDOI && (
                  <div className="mt-4">
                    <Progress value={50} className="w-full" />
                    <p className="text-sm text-blue-600 mt-2">Fetching paper metadata...</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* File Queue */}
      {uploadFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-blue-500" />
                Upload Queue ({uploadFiles.length} files)
              </span>
              <div className="flex items-center gap-2">
                {pendingFiles.length > 0 && (
                  <Button onClick={startUpload} size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload All ({pendingFiles.length})
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={clearAllFiles}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Status Summary */}
            <div className="flex items-center gap-4 mb-6 text-sm">
              {pendingFiles.length > 0 && (
                <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                  {pendingFiles.length} pending
                </Badge>
              )}
              {uploadingFiles.length > 0 && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  {uploadingFiles.length} uploading
                </Badge>
              )}
              {successFiles.length > 0 && (
                <Badge variant="secondary" className="bg-green-100 text-green-700">
                  {successFiles.length} completed
                </Badge>
              )}
              {errorFiles.length > 0 && (
                <Badge variant="secondary" className="bg-red-100 text-red-700">
                  {errorFiles.length} failed
                </Badge>
              )}
            </div>

            {/* File List */}
            <ScrollArea className="h-80">
              <div className="space-y-3">
                {uploadFiles.map((uploadFile) => (
                  <div
                    key={uploadFile.id}
                    className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="text-2xl">
                      {getFileIcon(uploadFile.file.name)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-gray-900 truncate">
                          {uploadFile.file.name}
                        </p>
                        <Badge 
                          variant="secondary" 
                          className={getStatusColor(uploadFile.status)}
                        >
                          {uploadFile.status === 'uploading' && <RefreshCw className="h-3 w-3 mr-1 animate-spin" />}
                          {uploadFile.status === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
                          {uploadFile.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                          {uploadFile.status}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB</span>
                        <span>{uploadFile.file.type || 'Unknown type'}</span>
                      </div>
                      
                      {uploadFile.status === 'uploading' && (
                        <Progress value={uploadFile.progress} className="w-full mt-2" />
                      )}
                      
                      {uploadFile.error && (
                        <p className="text-red-600 text-sm mt-1">{uploadFile.error}</p>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {uploadFile.status === 'pending' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeFile(uploadFile.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {uploadFile.status === 'success' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Preview file functionality
                            toast.info('File preview functionality would be implemented here');
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {uploadFile.status === 'error' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Retry upload
                            setUploadFiles(prev => prev.map(f => 
                              f.id === uploadFile.id ? { ...f, status: 'pending', error: undefined } : f
                            ));
                          }}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
