-- Create documents table for storing user documents
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL DEFAULT 'Untitled Document',
    content TEXT NOT NULL DEFAULT '',
    document_type TEXT NOT NULL DEFAULT 'research_paper', -- research_paper, article, report, etc.
    metadata JSONB DEFAULT '{}', -- Store additional metadata like keywords, authors, etc.
    status TEXT NOT NULL DEFAULT 'draft', -- draft, in_progress, completed, published
    tags TEXT[] DEFAULT '{}',
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    last_edited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_versions table for revision history
CREATE TABLE IF NOT EXISTS public.document_versions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES public.documents(id) ON DELETE CASCADE NOT NULL,
    version_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    changes_description TEXT,
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_user_id ON public.documents(user_id);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON public.documents(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_last_edited_at ON public.documents(last_edited_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_status ON public.documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_type ON public.documents(document_type);
CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON public.document_versions(document_id);

-- Enable Row Level Security
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_versions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for documents
CREATE POLICY "Users can only see their own documents" ON public.documents
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert their own documents" ON public.documents
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can only update their own documents" ON public.documents
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can only delete their own documents" ON public.documents
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for document versions
CREATE POLICY "Users can only see versions of their own documents" ON public.document_versions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.documents 
            WHERE documents.id = document_versions.document_id 
            AND documents.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can only insert versions of their own documents" ON public.document_versions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.documents 
            WHERE documents.id = document_versions.document_id 
            AND documents.user_id = auth.uid()
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers
CREATE TRIGGER handle_documents_updated_at
    BEFORE UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- Create function to automatically update word and character counts
CREATE OR REPLACE FUNCTION public.update_document_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- Remove HTML tags and count words/characters
    NEW.word_count = array_length(string_to_array(trim(regexp_replace(NEW.content, '<[^>]*>', '', 'g')), ' '), 1);
    NEW.character_count = length(regexp_replace(NEW.content, '<[^>]*>', '', 'g'));
    NEW.last_edited_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for document counts
CREATE TRIGGER update_document_counts_trigger
    BEFORE INSERT OR UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_document_counts();
