/**
 * Diagram Export Service
 * Handles exporting diagrams in various formats (PNG, SVG, PDF, Mermaid)
 */

import { GeneratedDiagram, DiagramExportOptions, DiagramExportError } from '../types';
import { EXPORT_FORMATS } from '../constants';

/**
 * Diagram Export Service Class
 * Provides methods to export diagrams in different formats
 */
class DiagramExportService {
  
  /**
   * Export diagram in the specified format
   */
  async exportDiagram(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<void> {
    try {
      switch (options.format) {
        case 'svg':
          await this.exportAsSVG(diagram, options);
          break;
        case 'png':
          await this.exportAsPNG(diagram, options);
          break;
        case 'pdf':
          await this.exportAsPDF(diagram, options);
          break;
        case 'mermaid':
          await this.exportAsMermaid(diagram, options);
          break;
        default:
          throw new DiagramExportError(`Unsupported export format: ${options.format}`);
      }
    } catch (error: any) {
      console.error('Export error:', error);
      throw new DiagramExportError(error.message || 'Failed to export diagram');
    }
  }

  /**
   * Enhanced SVG export with better element detection
   */
  private async exportAsSVG(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<void> {
    try {
      // Enhanced SVG element detection
      let svgElement = this.findDiagramSVG(diagram.id);

      if (!svgElement) {
        console.log('No rendered SVG found, rendering temporarily...');
        // If no rendered SVG found, render it temporarily
        const svgContent = await this.renderDiagramToSVG(diagram, options);
        this.downloadFile(svgContent, this.getFileName(diagram, options, 'svg'), 'image/svg+xml');
        return;
      }

      // Clone and enhance the SVG
      const clonedSVG = svgElement.cloneNode(true) as SVGElement;

      // Ensure proper SVG namespace
      if (!clonedSVG.getAttribute('xmlns')) {
        clonedSVG.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
      }

      // Apply export options with better dimension handling
      const bbox = this.getSVGBoundingBox(svgElement);
      const width = options.width || bbox.width || 800;
      const height = options.height || bbox.height || 600;

      clonedSVG.setAttribute('width', width.toString());
      clonedSVG.setAttribute('height', height.toString());

      // Ensure proper viewBox
      if (!clonedSVG.getAttribute('viewBox')) {
        clonedSVG.setAttribute('viewBox', `0 0 ${width} ${height}`);
      }

      // Handle background color properly
      if (options.backgroundColor && options.backgroundColor !== 'transparent') {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('width', '100%');
        rect.setAttribute('height', '100%');
        rect.setAttribute('fill', options.backgroundColor);
        clonedSVG.insertBefore(rect, clonedSVG.firstChild);
      }

      // Serialize the SVG
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(clonedSVG);

      // Add XML declaration and DOCTYPE
      const fullSVG = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
${svgString}`;

      this.downloadFile(fullSVG, this.getFileName(diagram, options, 'svg'), 'image/svg+xml');
    } catch (error: any) {
      console.error('SVG export error:', error);
      throw new DiagramExportError(`SVG export failed: ${error.message}`);
    }
  }

  /**
   * Enhanced PNG export with better SVG detection
   */
  private async exportAsPNG(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<void> {
    try {
      // Enhanced SVG element detection
      const svgElement = this.findDiagramSVG(diagram.id);

      if (!svgElement) {
        throw new DiagramExportError('No rendered diagram found for PNG export. Please ensure the diagram is visible on screen.');
      }

      // Create a canvas to convert SVG to PNG
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new DiagramExportError('Canvas context not available');
      }

      // Set canvas dimensions
      const width = options.width || 1200;
      const height = options.height || 800;
      canvas.width = width;
      canvas.height = height;

      // Set background color
      if (options.backgroundColor) {
        ctx.fillStyle = options.backgroundColor;
        ctx.fillRect(0, 0, width, height);
      }

      // Convert SVG to data URL
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Load SVG into an image
      const img = new Image();
      
      return new Promise((resolve, reject) => {
        img.onload = () => {
          try {
            // Draw the image on canvas
            ctx.drawImage(img, 0, 0, width, height);
            
            // Convert canvas to blob
            canvas.toBlob((blob) => {
              if (blob) {
                this.downloadBlob(blob, this.getFileName(diagram, options, 'png'));
                resolve();
              } else {
                reject(new Error('Failed to create PNG blob'));
              }
            }, 'image/png');
            
            // Clean up
            URL.revokeObjectURL(svgUrl);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load SVG for PNG conversion'));
        };
        
        img.src = svgUrl;
      });
    } catch (error: any) {
      throw new DiagramExportError(`PNG export failed: ${error.message}`);
    }
  }

  /**
   * Export as PDF
   */
  private async exportAsPDF(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<void> {
    try {
      // For PDF export, we'll use the browser's print functionality
      // First, create a new window with just the diagram
      const printWindow = window.open('', '_blank');
      
      if (!printWindow) {
        throw new DiagramExportError('Unable to open print window. Please check popup blockers.');
      }

      // Get the SVG content using enhanced detection
      const svgElement = this.findDiagramSVG(diagram.id);

      if (!svgElement) {
        throw new DiagramExportError('No rendered diagram found for PDF export. Please ensure the diagram is visible on screen.');
      }

      const svgContent = svgElement.outerHTML;

      // Create HTML content for printing
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${diagram.metadata.title}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: ${options.backgroundColor || 'white'};
            }
            .diagram-container {
              text-align: center;
              page-break-inside: avoid;
            }
            .diagram-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
            svg {
              max-width: 100%;
              height: auto;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="diagram-container">
            <div class="diagram-title">${diagram.metadata.title}</div>
            ${svgContent}
          </div>
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 500);
            };
          </script>
        </body>
        </html>
      `;

      printWindow.document.write(htmlContent);
      printWindow.document.close();
    } catch (error: any) {
      throw new DiagramExportError(`PDF export failed: ${error.message}`);
    }
  }

  /**
   * Export as Mermaid code
   */
  private async exportAsMermaid(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<void> {
    try {
      const content = `# ${diagram.metadata.title}

${diagram.metadata.description ? `## Description
${diagram.metadata.description}

` : ''}## Mermaid Code

\`\`\`mermaid
${diagram.mermaidCode}
\`\`\`

---
Generated on: ${diagram.generatedAt.toLocaleString()}
Model: ${diagram.model}
Type: ${diagram.metadata.type}
`;

      this.downloadFile(content, this.getFileName(diagram, options, 'md'), 'text/markdown');
    } catch (error: any) {
      throw new DiagramExportError(`Mermaid export failed: ${error.message}`);
    }
  }

  /**
   * Render diagram to SVG (for cases where no DOM element exists)
   */
  private async renderDiagramToSVG(diagram: GeneratedDiagram, options: DiagramExportOptions): Promise<string> {
    try {
      // Dynamic import of Mermaid
      const mermaidModule = await import('mermaid');
      const mermaid = mermaidModule.default;

      // Initialize Mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: options.theme || 'default',
        securityLevel: 'loose'
      });

      // Render the diagram
      const { svg } = await mermaid.render(`export-${Date.now()}`, diagram.mermaidCode);
      return svg;
    } catch (error: any) {
      throw new DiagramExportError(`Failed to render diagram: ${error.message}`);
    }
  }

  /**
   * Enhanced SVG element finder with multiple fallback strategies
   */
  private findDiagramSVG(diagramId: string): SVGElement | null {
    // Strategy 1: Look for specific diagram ID
    let svgElement = document.querySelector(`#mermaid-${diagramId}`) as SVGElement;
    if (svgElement) return svgElement;

    // Strategy 2: Look for any mermaid SVG
    svgElement = document.querySelector('svg[id*="mermaid"]') as SVGElement;
    if (svgElement) return svgElement;

    // Strategy 3: Look for SVG in diagram renderer container
    const rendererContainer = document.querySelector('[class*="DiagramRenderer"]');
    if (rendererContainer) {
      svgElement = rendererContainer.querySelector('svg') as SVGElement;
      if (svgElement) return svgElement;
    }

    // Strategy 4: Look for any SVG in the flow builder
    const flowBuilderContainer = document.querySelector('[class*="FlowBuilder"]');
    if (flowBuilderContainer) {
      svgElement = flowBuilderContainer.querySelector('svg') as SVGElement;
      if (svgElement) return svgElement;
    }

    // Strategy 5: Last resort - any SVG on the page
    svgElement = document.querySelector('svg') as SVGElement;
    return svgElement;
  }

  /**
   * Get SVG bounding box with fallbacks
   */
  private getSVGBoundingBox(svgElement: SVGElement): { width: number; height: number } {
    try {
      // Try getBBox first
      if (svgElement.getBBox) {
        const bbox = svgElement.getBBox();
        if (bbox.width > 0 && bbox.height > 0) {
          return { width: bbox.width, height: bbox.height };
        }
      }
    } catch (e) {
      // getBBox might fail, continue with fallbacks
    }

    // Try viewBox
    const viewBox = svgElement.getAttribute('viewBox');
    if (viewBox) {
      const [, , width, height] = viewBox.split(' ').map(Number);
      if (width > 0 && height > 0) {
        return { width, height };
      }
    }

    // Try width/height attributes
    const width = parseFloat(svgElement.getAttribute('width') || '0');
    const height = parseFloat(svgElement.getAttribute('height') || '0');
    if (width > 0 && height > 0) {
      return { width, height };
    }

    // Try computed style
    const computedStyle = window.getComputedStyle(svgElement);
    const computedWidth = parseFloat(computedStyle.width);
    const computedHeight = parseFloat(computedStyle.height);
    if (computedWidth > 0 && computedHeight > 0) {
      return { width: computedWidth, height: computedHeight };
    }

    // Default fallback
    return { width: 800, height: 600 };
  }

  /**
   * Generate filename for export
   */
  private getFileName(diagram: GeneratedDiagram, options: DiagramExportOptions, defaultExtension: string): string {
    if (options.filename) {
      return options.filename;
    }

    const baseName = diagram.metadata.title
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    
    const extension = EXPORT_FORMATS[options.format]?.extension || defaultExtension;
    return `${baseName}.${extension}`;
  }

  /**
   * Download file with content
   */
  private downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    this.downloadBlob(blob, filename);
  }

  /**
   * Download blob as file
   */
  private downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// Export singleton instance
export const diagramExportService = new DiagramExportService();
