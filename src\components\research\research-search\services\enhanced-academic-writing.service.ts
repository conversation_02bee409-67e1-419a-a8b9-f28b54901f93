/**
 * Enhanced Academic Writing Service
 * Generates high-quality academic content with proper citations and academic standards
 */

import {
  ResearchPoint,
  SearchSource,
  Citation,
  DeepResearchOptions,
  EnhancedSearchSource
} from '../types';

export interface AcademicWritingOptions {
  model: string;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicLevel: 'undergraduate' | 'graduate' | 'doctoral' | 'professional';
  minWordCount: number;
  maxWordCount: number;
  minCitations: number;
  maxCitations: number;
  writingStyle: 'formal' | 'technical' | 'review' | 'analytical';
  includeMethodology: boolean;
  includeLimitations: boolean;
  includeImplications: boolean;
  requireOriginalAnalysis: boolean;
}

export interface AcademicSection {
  title: string;
  content: string;
  wordCount: number;
  citationCount: number;
  qualityScore: number;
  readabilityScore: number;
  academicRigor: number;
  originalityScore: number;
  citations: Citation[];
  keyPoints: string[];
  methodology?: string;
  limitations?: string[];
  implications?: string[];
}

export class EnhancedAcademicWritingService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
  }

  /**
   * Generate enhanced academic content for a research point
   */
  async generateAcademicSection(
    point: ResearchPoint,
    sources: EnhancedSearchSource[],
    options: AcademicWritingOptions,
    context?: {
      overallTopic: string;
      previousSections: string[];
      researchObjectives: string[];
    }
  ): Promise<AcademicSection> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    // Prepare sources for citation
    const citableSources = this.prepareCitableSources(sources, options.minCitations);
    
    // Generate the academic content
    const content = await this.generateContent(point, citableSources, options, context);
    
    // Extract and validate citations
    const citations = this.extractCitations(content, citableSources);
    
    // Assess content quality
    const qualityMetrics = this.assessContentQuality(content, citations, options);
    
    // Extract key insights
    const keyPoints = this.extractKeyPoints(content);
    
    return {
      title: point.title,
      content,
      wordCount: content.split(' ').length,
      citationCount: citations.length,
      qualityScore: qualityMetrics.overallQuality,
      readabilityScore: qualityMetrics.readability,
      academicRigor: qualityMetrics.academicRigor,
      originalityScore: qualityMetrics.originality,
      citations,
      keyPoints,
      methodology: this.extractMethodology(content),
      limitations: this.extractLimitations(content),
      implications: this.extractImplications(content)
    };
  }

  /**
   * Generate comprehensive academic content using AI
   */
  private async generateContent(
    point: ResearchPoint,
    sources: EnhancedSearchSource[],
    options: AcademicWritingOptions,
    context?: any
  ): Promise<string> {
    const prompt = this.buildAcademicPrompt(point, sources, options, context);
    const systemPrompt = this.buildSystemPrompt(options);

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Enhanced Academic Writing Platform'
      },
      body: JSON.stringify({
        model: options.model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 16000,
        temperature: 0.3, // Lower temperature for more consistent academic writing
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`AI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  /**
   * Build comprehensive academic writing prompt
   */
  private buildAcademicPrompt(
    point: ResearchPoint,
    sources: EnhancedSearchSource[],
    options: AcademicWritingOptions,
    context?: any
  ): string {
    let prompt = `Write a comprehensive academic section on: "${point.title}"

RESEARCH CONTEXT:
${context?.overallTopic ? `- Overall Research Topic: ${context.overallTopic}` : ''}
${context?.researchObjectives ? `- Research Objectives: ${context.researchObjectives.join(', ')}` : ''}
- Section Description: ${point.description}
- Academic Level: ${options.academicLevel}
- Writing Style: ${options.writingStyle}

SUBPOINTS TO ADDRESS:
${point.subpoints?.map((sub, index) => 
  `${index + 1}. ${sub.title}: ${sub.description}`
).join('\n') || 'No specific subpoints defined'}

CRITICAL ACADEMIC REQUIREMENTS:
- Write ${options.minWordCount}-${options.maxWordCount} words of substantive academic content
- Include EXACTLY ${options.minCitations}-${options.maxCitations} in-text citations using ${options.citationStyle.toUpperCase()} format
- Maintain ${options.academicLevel}-level academic rigor and sophistication
- Use formal academic language and structure
- Provide critical analysis, not just description
- Synthesize information from multiple sources
- Include original insights and analysis
- Demonstrate deep understanding of the subject matter

CONTENT STRUCTURE REQUIREMENTS:
1. **Introduction** (10-15% of content)
   - Clear topic introduction with context
   - Thesis statement or main argument
   - Preview of key points to be discussed

2. **Main Body** (70-80% of content)
   - 3-4 major themes or arguments
   - Each theme supported by multiple citations
   - Critical analysis and evaluation of evidence
   - Comparison of different perspectives
   - Integration of theoretical frameworks

3. **Synthesis and Implications** (10-15% of content)
   - Synthesis of key findings
   - Implications for theory and practice
   - Connections to broader research context
   ${options.includeLimitations ? '- Discussion of limitations' : ''}
   ${options.includeImplications ? '- Future research directions' : ''}

CITATION REQUIREMENTS:
- Use ONLY the provided sources for citations
- Integrate citations naturally into the text
- Support all major claims with evidence
- Use a variety of citation types (direct quotes, paraphrases, references)
- Ensure proper ${options.citationStyle.toUpperCase()} formatting
- Include page numbers where available
- Balance citation distribution throughout the section

ACADEMIC QUALITY STANDARDS:
- Demonstrate critical thinking and analytical depth
- Use sophisticated vocabulary and academic register
- Maintain objectivity while presenting arguments
- Show awareness of scholarly debates and controversies
- Employ proper academic hedging and qualification
- Use precise, discipline-specific terminology
- Ensure logical flow and coherent argumentation
- Provide evidence-based conclusions

AVAILABLE SOURCES FOR CITATION:
`;

    // Add source information for citations
    sources.forEach((source, index) => {
      const authors = this.extractAuthors(source);
      const year = this.extractYear(source.publishedDate || '');
      const citationKey = `${authors[0] || 'Unknown'}, ${year}`;
      
      prompt += `\n[SOURCE ${index + 1}] - CITE AS: (${citationKey})
Title: ${source.title}
Authors: <AUTHORS>
Published: ${source.publishedDate || 'Date not specified'}
Type: ${source.isPeerReviewed ? 'Peer-reviewed' : source.isGovernment ? 'Government' : 'Web'} source
Quality Score: ${source.qualityScore.toFixed(2)}/1.0
URL: ${source.url}
Key Content: ${source.snippet.substring(0, 300)}...
---`;
    });

    prompt += `\n\nIMPORTANT CITATION INSTRUCTIONS:
1. Use ONLY the sources provided above for citations
2. NEVER use placeholder citations like [Citation 1] or [Source needed]
3. Format all citations in ${options.citationStyle.toUpperCase()} style
4. Include ${options.minCitations}-${options.maxCitations} citations distributed throughout the text
5. Support every major claim with appropriate evidence
6. Use direct quotes sparingly and appropriately
7. Integrate citations smoothly into the narrative flow

EXAMPLE CITATION FORMATS (${options.citationStyle.toUpperCase()}):
- In-text: "Recent studies demonstrate significant improvements (Smith, 2023)."
- Direct quote: According to Johnson (2024), "the evidence clearly indicates..." (p. 45).
- Multiple sources: Several researchers have confirmed this finding (Brown, 2023; Davis, 2024; Wilson, 2023).

Begin writing the academic section now, ensuring it meets all requirements for scholarly publication.`;

    return prompt;
  }

  /**
   * Build system prompt for academic writing
   */
  private buildSystemPrompt(options: AcademicWritingOptions): string {
    return `You are a distinguished academic writer and researcher with expertise in scholarly publication. Your credentials include:

ACADEMIC EXPERTISE:
- PhD-level knowledge across multiple disciplines
- Extensive experience in peer-reviewed academic writing
- Expert knowledge of ${options.citationStyle.toUpperCase()} citation style
- Advanced understanding of ${options.academicLevel}-level academic standards
- Proficiency in ${options.writingStyle} academic writing

WRITING STANDARDS:
- Scholarly tone and sophisticated academic language
- Critical analysis and evidence-based argumentation
- Proper academic structure and organization
- Accurate and consistent citation formatting
- Original insights and analytical depth
- Clear, precise, and engaging prose

QUALITY REQUIREMENTS:
- Every claim must be supported by credible evidence
- All citations must be accurate and properly formatted
- Content must demonstrate original thinking and analysis
- Writing must meet publication standards for academic journals
- Arguments must be logical, coherent, and well-developed
- Language must be appropriate for the specified academic level

Your task is to produce academic content that would be suitable for publication in top-tier academic journals. Focus on:
1. Analytical depth and critical thinking
2. Comprehensive coverage of the topic
3. Proper integration of sources and evidence
4. Original insights and contributions
5. Clear, sophisticated academic writing
6. Accurate and consistent citations
7. Logical structure and flow

Write content that demonstrates the highest standards of academic excellence and scholarly rigor.`;
  }

  /**
   * Prepare sources for citation with quality ranking
   */
  private prepareCitableSources(
    sources: EnhancedSearchSource[],
    minCitations: number
  ): EnhancedSearchSource[] {
    // Sort sources by quality score and relevance
    const sortedSources = sources
      .filter(source => source.title && source.url)
      .sort((a, b) => {
        // Prioritize academic sources
        if (a.isPeerReviewed && !b.isPeerReviewed) return -1;
        if (!a.isPeerReviewed && b.isPeerReviewed) return 1;
        
        // Then by quality score
        return b.qualityScore - a.qualityScore;
      });

    // Ensure we have enough sources for minimum citations
    const targetSources = Math.max(minCitations, Math.min(sortedSources.length, minCitations * 1.5));
    
    return sortedSources.slice(0, targetSources);
  }

  /**
   * Extract citations from generated content
   */
  private extractCitations(content: string, sources: EnhancedSearchSource[]): Citation[] {
    const citations: Citation[] = [];
    
    // Citation patterns for different styles
    const patterns = [
      /\(([A-Z][a-z]+(?:\s+et\s+al\.?)?),?\s*(\d{4})\)/g, // (Author, Year)
      /([A-Z][a-z]+)\s*\((\d{4})\)/g, // Author (Year)
      /\(([A-Z][a-z]+(?:\s+et\s+al\.?)?),?\s*(\d{4}),?\s*p\.?\s*(\d+)\)/g // (Author, Year, p. X)
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const citationText = match[0];
        const author = match[1];
        const year = match[2];
        const page = match[3] || undefined;
        
        // Find matching source
        const matchingSource = sources.find(source => {
          const sourceAuthors = this.extractAuthors(source);
          const sourceYear = this.extractYear(source.publishedDate || '');
          return sourceAuthors.some(a => a.includes(author)) && sourceYear === year;
        });

        if (matchingSource) {
          citations.push({
            id: `citation_${Date.now()}_${citations.length}`,
            text: citationText,
            sourceId: matchingSource.id,
            url: matchingSource.url,
            title: matchingSource.title,
            position: match.index,
            author,
            year,
            page
          });
        }
      }
    });

    return citations;
  }

  /**
   * Assess content quality with multiple metrics
   */
  private assessContentQuality(
    content: string,
    citations: Citation[],
    options: AcademicWritingOptions
  ): {
    overallQuality: number;
    readability: number;
    academicRigor: number;
    originality: number;
  } {
    const wordCount = content.split(' ').length;
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = wordCount / sentences.length;
    
    // Readability assessment (based on sentence complexity and vocabulary)
    let readability = 0.7; // Base score
    if (avgSentenceLength > 15 && avgSentenceLength < 25) readability += 0.1;
    if (content.includes('however') || content.includes('furthermore') || content.includes('moreover')) readability += 0.1;
    if (content.includes('therefore') || content.includes('consequently') || content.includes('thus')) readability += 0.1;
    
    // Academic rigor assessment
    let academicRigor = 0.6; // Base score
    const citationDensity = citations.length / (wordCount / 1000);
    if (citationDensity >= 15) academicRigor += 0.2;
    else if (citationDensity >= 10) academicRigor += 0.1;
    
    if (content.includes('research indicates') || content.includes('studies show')) academicRigor += 0.1;
    if (content.includes('analysis reveals') || content.includes('evidence suggests')) academicRigor += 0.1;
    
    // Originality assessment (based on analytical language and synthesis)
    let originality = 0.5; // Base score
    if (content.includes('this suggests') || content.includes('this indicates')) originality += 0.1;
    if (content.includes('in contrast') || content.includes('conversely')) originality += 0.1;
    if (content.includes('implications') || content.includes('significance')) originality += 0.1;
    if (content.includes('novel') || content.includes('innovative')) originality += 0.1;
    
    // Overall quality (weighted average)
    const overallQuality = (readability * 0.2 + academicRigor * 0.4 + originality * 0.4);
    
    return {
      overallQuality: Math.min(overallQuality, 1.0),
      readability: Math.min(readability, 1.0),
      academicRigor: Math.min(academicRigor, 1.0),
      originality: Math.min(originality, 1.0)
    };
  }

  /**
   * Extract key points from academic content
   */
  private extractKeyPoints(content: string): string[] {
    const keyPoints: string[] = [];

    // Look for sentences that indicate key findings or conclusions
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);

    const keyIndicators = [
      'key finding', 'main result', 'important discovery', 'significant',
      'demonstrates', 'reveals', 'indicates', 'suggests', 'shows that',
      'evidence shows', 'research indicates', 'studies reveal'
    ];

    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      if (keyIndicators.some(indicator => lowerSentence.includes(indicator))) {
        const cleanSentence = sentence.trim();
        if (cleanSentence.length > 30 && cleanSentence.length < 200) {
          keyPoints.push(cleanSentence);
        }
      }
    });

    return keyPoints.slice(0, 5); // Return top 5 key points
  }

  /**
   * Extract methodology information from content
   */
  private extractMethodology(content: string): string | undefined {
    const methodologyKeywords = [
      'methodology', 'method', 'approach', 'technique', 'procedure',
      'analysis', 'framework', 'design', 'protocol'
    ];

    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);

    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();
      if (methodologyKeywords.some(keyword => lowerSentence.includes(keyword))) {
        return sentence.trim();
      }
    }

    return undefined;
  }

  /**
   * Extract limitations from content
   */
  private extractLimitations(content: string): string[] {
    const limitations: string[] = [];

    const limitationKeywords = [
      'limitation', 'constraint', 'weakness', 'shortcoming',
      'caveat', 'restriction', 'drawback'
    ];

    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);

    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      if (limitationKeywords.some(keyword => lowerSentence.includes(keyword))) {
        const cleanSentence = sentence.trim();
        if (cleanSentence.length > 30) {
          limitations.push(cleanSentence);
        }
      }
    });

    return limitations.slice(0, 3); // Return top 3 limitations
  }

  /**
   * Extract implications from content
   */
  private extractImplications(content: string): string[] {
    const implications: string[] = [];

    const implicationKeywords = [
      'implication', 'consequence', 'significance', 'importance',
      'impact', 'effect', 'influence', 'application'
    ];

    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);

    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      if (implicationKeywords.some(keyword => lowerSentence.includes(keyword))) {
        const cleanSentence = sentence.trim();
        if (cleanSentence.length > 30) {
          implications.push(cleanSentence);
        }
      }
    });

    return implications.slice(0, 3); // Return top 3 implications
  }

  /**
   * Extract authors from source
   */
  private extractAuthors(source: EnhancedSearchSource): string[] {
    // Try to extract from authorCredentials if available
    if (source.authorCredentials && source.authorCredentials.length > 0) {
      return source.authorCredentials;
    }

    // Extract from title or content
    const content = `${source.title} ${source.snippet}`;
    const authors: string[] = [];

    const authorPatterns = [
      /(?:by|author[s]?:|written by)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+)+)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.(?:\s*[A-Z]\.)*(?:\s*,?\s*&?\s*[A-Z][a-z]+,?\s+[A-Z]\.)*(?:\s+et\s+al\.?)?/,
      /([A-Z][a-z]+\s+et\s+al\.?)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.?\s*(?:\([0-9]{4}\))?/,
      /^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\(/
    ];

    for (const pattern of authorPatterns) {
      const match = content.match(pattern);
      if (match) {
        const author = match[1].trim();
        if (!this.isWebsiteName(author)) {
          authors.push(this.formatAuthorName(author));
          break;
        }
      }
    }

    return authors.length > 0 ? authors : ['Unknown Author'];
  }

  /**
   * Extract year from date string
   */
  private extractYear(dateString: string): string {
    if (!dateString) return new Date().getFullYear().toString();

    const yearMatch = dateString.match(/\b(19|20)\d{2}\b/);
    return yearMatch ? yearMatch[0] : new Date().getFullYear().toString();
  }

  /**
   * Check if name is likely a website name
   */
  private isWebsiteName(name: string): boolean {
    const websiteNames = [
      'researchgate', 'sciencedirect', 'springer', 'nature', 'science',
      'pubmed', 'arxiv', 'jstor', 'wiley', 'elsevier', 'google'
    ];

    return websiteNames.some(site =>
      name.toLowerCase().includes(site) ||
      name.toLowerCase() === site
    );
  }

  /**
   * Format author name for citations
   */
  private formatAuthorName(name: string): string {
    if (name.toLowerCase().includes('et al')) {
      return name;
    }

    if (!name.includes(' ') || name.length < 4) {
      return name;
    }

    const parts = name.split(' ');
    if (parts.length >= 2) {
      const lastName = parts[parts.length - 1];
      const firstInitial = parts[0].charAt(0).toUpperCase();
      return `${lastName}, ${firstInitial}.`;
    }

    return name;
  }

  /**
   * Generate multiple academic sections with different perspectives
   */
  async generateMultiplePerspectives(
    point: ResearchPoint,
    sources: EnhancedSearchSource[],
    options: AcademicWritingOptions,
    perspectives: string[] = ['theoretical', 'empirical', 'practical']
  ): Promise<AcademicSection[]> {
    const sections: AcademicSection[] = [];

    for (const perspective of perspectives) {
      const perspectiveOptions = {
        ...options,
        writingStyle: perspective as any,
        minWordCount: Math.floor(options.minWordCount / perspectives.length),
        maxWordCount: Math.floor(options.maxWordCount / perspectives.length),
        minCitations: Math.floor(options.minCitations / perspectives.length),
        maxCitations: Math.floor(options.maxCitations / perspectives.length)
      };

      const section = await this.generateAcademicSection(
        {
          ...point,
          title: `${point.title}: ${perspective.charAt(0).toUpperCase() + perspective.slice(1)} Perspective`,
          description: `${point.description} from a ${perspective} perspective`
        },
        sources,
        perspectiveOptions
      );

      sections.push(section);
    }

    return sections;
  }

  /**
   * Validate academic content quality
   */
  validateAcademicQuality(section: AcademicSection, requirements: AcademicWritingOptions): {
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check word count
    if (section.wordCount < requirements.minWordCount) {
      issues.push(`Word count (${section.wordCount}) below minimum (${requirements.minWordCount})`);
      recommendations.push('Expand content with additional analysis and examples');
    }

    // Check citation count
    if (section.citationCount < requirements.minCitations) {
      issues.push(`Citation count (${section.citationCount}) below minimum (${requirements.minCitations})`);
      recommendations.push('Add more citations to support claims and arguments');
    }

    // Check quality scores
    if (section.qualityScore < 0.7) {
      issues.push(`Overall quality score (${section.qualityScore.toFixed(2)}) below acceptable threshold`);
      recommendations.push('Improve academic rigor and analytical depth');
    }

    if (section.academicRigor < 0.6) {
      issues.push(`Academic rigor score (${section.academicRigor.toFixed(2)}) needs improvement`);
      recommendations.push('Use more sophisticated academic language and deeper analysis');
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }
}

// Export singleton instance
export const enhancedAcademicWritingService = new EnhancedAcademicWritingService();
