/**
 * AI Orchestrator Service for Article Revision System
 * Coordinates multiple AI assistants for document revision workflow
 */

import { 
  ParsedArticle, 
  ParsedReviewerComments, 
  RevisedArticle, 
  ResponseLetter, 
  ManualIntervention,
  SectionRevision,
  ArticleChange,
  ReviewerResponse,
  ArticleSection
} from '../types';
import { geminiAIService } from './gemini-ai.service';
import { DEFAULT_MODELS, PROCESSING_CONFIG, SECTION_PROCESSING_ORDER } from '../constants';
import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';

export class AIOrchestrator {
  private static instance: AIOrchestrator;
  private activeRequests: Map<string, AbortController> = new Map();
  
  static getInstance(): AIOrchestrator {
    if (!this.instance) {
      this.instance = new AIOrchestrator();
    }
    return this.instance;
  }
  
  /**
   * Main orchestration method - coordinates the entire revision workflow
   */
  async orchestrateRevision(
    article: ParsedArticle,
    reviewerComments: ParsedReviewerComments[]
  ): Promise<{
    revisedArticle: RevisedArticle;
    responseLetter: ResponseLetter;
    manualInterventions: ManualIntervention[];
  }> {
    const store = useRevisionWorkflowStore.getState();
    
    try {
      // Initialize progress tracking
      store.updateProgress({
        phase: 'comment-analysis',
        currentStep: 'Starting comment analysis...',
        percentage: 10,
        completedSteps: 1
      });

      // Phase 1: Comment Analysis
      store.setCurrentPhase('comment-analysis');
      store.setAIAssistantStatus('comment-analyzer', 'processing');

      const analyzedComments = await this.analyzeComments(reviewerComments);
      store.setAIAssistantResult('comment-analyzer', analyzedComments);

      store.updateProgress({
        currentStep: 'Comment analysis completed',
        percentage: 25,
        completedSteps: 2
      });

      // Phase 2: Section Revision (parallel processing)
      store.setCurrentPhase('section-revision');
      store.updateProgress({
        currentStep: 'Starting section revisions...',
        percentage: 30,
        completedSteps: 2
      });

      const sectionRevisions = await this.reviseSections(article, analyzedComments);

      store.updateProgress({
        currentStep: 'Section revisions completed',
        percentage: 65,
        completedSteps: 3
      });

      // Phase 3: Integration
      store.setCurrentPhase('integration');
      store.setAIAssistantStatus('integration-manager', 'processing');
      store.updateProgress({
        currentStep: 'Integrating revisions...',
        percentage: 70,
        completedSteps: 4
      });

      const revisedArticle = await this.integrateRevisions(article, sectionRevisions);
      store.setAIAssistantResult('integration-manager', revisedArticle);

      store.updateProgress({
        currentStep: 'Integration completed',
        percentage: 80,
        completedSteps: 4
      });

      // Phase 4: Response Generation
      store.setCurrentPhase('response-generation');
      store.setAIAssistantStatus('response-generator', 'processing');
      store.updateProgress({
        currentStep: 'Generating response letter...',
        percentage: 85,
        completedSteps: 5
      });

      const responseLetter = await this.generateResponseLetter(
        article,
        reviewerComments,
        sectionRevisions
      );
      store.setAIAssistantResult('response-generator', responseLetter);

      store.updateProgress({
        currentStep: 'Response letter completed',
        percentage: 90,
        completedSteps: 5
      });

      // Phase 5: Manual Intervention Analysis
      store.setAIAssistantStatus('suggestion-assistant', 'processing');
      store.updateProgress({
        currentStep: 'Analyzing manual interventions...',
        percentage: 95,
        completedSteps: 6
      });

      const manualInterventions = await this.identifyManualInterventions(
        reviewerComments,
        sectionRevisions
      );
      store.setAIAssistantResult('suggestion-assistant', manualInterventions);

      // Complete workflow
      store.setCurrentPhase('completed');
      store.updateProgress({
        percentage: 100,
        completedSteps: 6,
        currentStep: 'Revision completed successfully'
      });

      return {
        revisedArticle,
        responseLetter,
        manualInterventions
      };
      
    } catch (error) {
      console.error('Orchestration error:', error);
      store.addError(`Orchestration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }
  
  /**
   * Analyze and categorize reviewer comments
   */
  private async analyzeComments(
    reviewerComments: ParsedReviewerComments[]
  ): Promise<ParsedReviewerComments[]> {
    const store = useRevisionWorkflowStore.getState();

    if (reviewerComments.length === 0) {
      throw new Error('No reviewer comments provided for analysis');
    }

    store.updateProgress({
      currentStep: 'Analyzing reviewer comments...',
      percentage: 15
    });

    const totalComments = reviewerComments.reduce((sum, rc) => sum + rc.comments.length, 0);
    console.log(`Analyzing ${totalComments} comments from ${reviewerComments.length} reviewers`);

    const prompt = `You are an expert academic editor with extensive experience in peer review and manuscript revision. Your task is to analyze reviewer comments for a research article and provide a comprehensive assessment.

REVIEWER COMMENTS TO ANALYZE:
${reviewerComments.map(rc => `
Reviewer ${rc.reviewerNumber}:
${rc.comments.map((c, idx) => `${idx + 1}. ${c.comment}`).join('\n')}
`).join('\n')}

ANALYSIS REQUIREMENTS:
Please provide a structured analysis focusing on:

1. COMMENT CATEGORIZATION:
   - Content issues (methodology, data analysis, interpretation)
   - Structural issues (organization, flow, clarity)
   - Language and writing quality
   - Citations and references
   - Figures and tables
   - Other technical aspects

2. SEVERITY ASSESSMENT:
   - Critical: Must be addressed for publication
   - Major: Significantly impacts the work
   - Moderate: Important but not blocking
   - Minor: Cosmetic or stylistic improvements

3. ACTIONABILITY:
   - Which comments can be addressed through text revision
   - Which require additional analysis or data
   - Which need manual intervention (figures, tables, etc.)

4. SECTION MAPPING:
   - Identify which sections of the article each comment targets
   - Prioritize comments by their impact on the overall manuscript

Provide a concise summary (200-300 words) highlighting the most critical issues that need immediate attention and the overall revision strategy.`;

    try {
      const response = await geminiAIService.generateContent(
        prompt,
        'gemini-2.5-pro',
        { maxTokens: 4000, temperature: 0.3 }
      );

      console.log('Comment analysis completed:', response.substring(0, 200) + '...');

      // Store the analysis result for reference
      store.setAIAssistantProgress('comment-analyzer', 100);
      store.updateProgress({
        currentStep: `Analyzed ${totalComments} comments successfully`,
        percentage: 20
      });

      return reviewerComments; // Return original comments (enhanced analysis stored separately)
    } catch (error) {
      console.error('Comment analysis failed:', error);
      store.setAIAssistantError('comment-analyzer', error instanceof Error ? error.message : 'Analysis failed');
      throw new Error(`Comment analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Revise sections reviewer-by-reviewer for better accuracy
   */
  private async reviseSections(
    article: ParsedArticle,
    reviewerComments: ParsedReviewerComments[]
  ): Promise<Record<ArticleSection, SectionRevision>> {
    const store = useRevisionWorkflowStore.getState();
    const sectionRevisions: Record<ArticleSection, SectionRevision> = {} as any;

    console.log(`Starting revision process with ${reviewerComments.length} reviewers`);

    // Process each reviewer's comments separately for better accuracy
    for (let reviewerIndex = 0; reviewerIndex < reviewerComments.length; reviewerIndex++) {
      const reviewer = reviewerComments[reviewerIndex];
      console.log(`Processing Reviewer ${reviewer.reviewerNumber} with ${reviewer.comments.length} comments`);

      store.updateProgress({
        currentStep: `Processing Reviewer ${reviewer.reviewerNumber} comments (${reviewer.comments.length} comments)`,
        percentage: 30 + (reviewerIndex / reviewerComments.length) * 35
      });

      // Group this reviewer's comments by section
      const reviewerCommentsBySection = this.groupSingleReviewerCommentsBySection([reviewer]);

      // Process each section for this reviewer
      for (const [sectionKey, comments] of Object.entries(reviewerCommentsBySection)) {
        if (comments.length === 0) continue;

        const sectionContent = article.sectionMapping[sectionKey as ArticleSection];
        if (!sectionContent || sectionContent.trim().length < 50) {
          console.log(`Skipping ${sectionKey} - insufficient content`);
          continue;
        }

        store.updateProgress({
          currentStep: `Reviewer ${reviewer.reviewerNumber}: Revising ${sectionKey} section`,
          percentage: 30 + (reviewerIndex / reviewerComments.length) * 35
        });

        try {
          // If this section was already revised by a previous reviewer, merge the changes
          if (sectionRevisions[sectionKey as ArticleSection]) {
            const existingRevision = sectionRevisions[sectionKey as ArticleSection];
            const mergedRevision = await this.mergeRevisions(
              sectionKey as ArticleSection,
              existingRevision,
              comments,
              article,
              reviewer.reviewerNumber
            );
            sectionRevisions[sectionKey as ArticleSection] = mergedRevision;
          } else {
            // First revision for this section
            const revision = await this.reviseSingleSection(
              sectionKey as ArticleSection,
              sectionContent,
              comments,
              article,
              reviewer.reviewerNumber
            );
            sectionRevisions[sectionKey as ArticleSection] = revision;
          }

          console.log(`Completed ${sectionKey} revision for Reviewer ${reviewer.reviewerNumber}`);

        } catch (error) {
          console.error(`Error revising ${sectionKey} for Reviewer ${reviewer.reviewerNumber}:`, error);
          store.addWarning(`Failed to revise ${sectionKey} for Reviewer ${reviewer.reviewerNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        // Rate limiting delay
        await this.delay(PROCESSING_CONFIG.RATE_LIMIT_DELAY);
      }
    }

    // If no sections were processed, try to process the entire article as one section
    if (Object.keys(sectionRevisions).length === 0) {
      console.log('No individual sections found, processing entire article as overall content');

      const allComments = reviewerComments.reduce((acc, rc) => acc.concat(rc.comments), []);
      if (allComments.length > 0 && article.fullText && article.fullText.trim().length > 100) {
        try {
          store.updateProgress({
            currentStep: 'Processing entire article with all comments',
            percentage: 40
          });

          const overallRevision = await this.reviseSingleSection(
            'overall',
            article.fullText,
            allComments,
            article
          );

          sectionRevisions['overall'] = overallRevision;
          console.log('Successfully processed article as overall content');

        } catch (error) {
          console.error('Failed to process article as overall content:', error);
          throw new Error('Unable to process the article. Please ensure your article has clear content and reviewer comments are specific. Try using the text input option for better results.');
        }
      } else {
        throw new Error('No content found to revise. Please check that your article contains substantial text and you have provided reviewer comments.');
      }
    }

    store.setAIAssistantStatus('section-revisor', 'completed');
    console.log(`Completed revision of ${Object.keys(sectionRevisions).length} sections`);
    return sectionRevisions;
  }

  /**
   * Group single reviewer's comments by section
   */
  private groupSingleReviewerCommentsBySection(
    reviewerComments: ParsedReviewerComments[]
  ): Record<string, any[]> {
    const grouped: Record<string, any[]> = {};

    reviewerComments.forEach(rc => {
      rc.comments.forEach(comment => {
        let section = comment.targetSection || 'overall';

        // Enhanced section inference with better accuracy
        if (section === 'overall' || !section) {
          section = this.inferSectionFromComment(comment.comment);
        }

        if (!grouped[section]) {
          grouped[section] = [];
        }
        grouped[section].push({
          ...comment,
          reviewerNumber: rc.reviewerNumber,
          commentId: `R${rc.reviewerNumber}-C${grouped[section].length + 1}`
        });
      });
    });

    return grouped;
  }

  /**
   * Infer section from comment content with better accuracy
   */
  private inferSectionFromComment(commentText: string): string {
    const text = commentText.toLowerCase();

    // More specific patterns for better accuracy
    if (text.includes('abstract') || text.includes('summary')) return 'abstract';
    if (text.includes('introduction') || text.includes('background') || text.includes('intro')) return 'introduction';
    if (text.includes('method') || text.includes('approach') || text.includes('procedure') || text.includes('experimental')) return 'methodology';
    if (text.includes('result') || text.includes('finding') || text.includes('data') || text.includes('analysis')) return 'results';
    if (text.includes('discussion') || text.includes('interpret') || text.includes('implication')) return 'discussion';
    if (text.includes('conclusion') || text.includes('conclud') || text.includes('summary')) return 'conclusion';
    if (text.includes('reference') || text.includes('citation') || text.includes('bibliography')) return 'references';
    if (text.includes('figure') || text.includes('table') || text.includes('graph') || text.includes('chart')) return 'figures';

    return 'overall';
  }

  /**
   * Merge revisions from multiple reviewers for the same section
   */
  private async mergeRevisions(
    section: ArticleSection,
    existingRevision: SectionRevision,
    newComments: any[],
    article: ParsedArticle,
    reviewerNumber: number
  ): Promise<SectionRevision> {
    console.log(`Merging revisions for ${section} with ${newComments.length} new comments from Reviewer ${reviewerNumber}`);

    // Use the already revised content as the base
    const baseContent = existingRevision.revisedContent;

    const additionalRevision = await this.reviseSingleSection(
      section,
      baseContent,
      newComments,
      article,
      reviewerNumber
    );

    // Merge the changes
    const mergedChanges = [...existingRevision.changes, ...additionalRevision.changes];
    const mergedAddressedComments = [...existingRevision.addressedComments, ...additionalRevision.addressedComments];
    const mergedUnaddressedComments = [...existingRevision.unaddressedComments, ...additionalRevision.unaddressedComments];

    return {
      ...additionalRevision,
      changes: mergedChanges,
      addressedComments: mergedAddressedComments,
      unaddressedComments: mergedUnaddressedComments,
      revisionSummary: `${existingRevision.revisionSummary} | Additional revisions from Reviewer ${reviewerNumber}: ${additionalRevision.revisionSummary}`
    };
  }

  /**
   * Revise a single section
   */
  private async reviseSingleSection(
    section: ArticleSection,
    content: string,
    comments: any[],
    article: ParsedArticle,
    reviewerNumber?: number
  ): Promise<SectionRevision> {
    const reviewerInfo = reviewerNumber ? `Reviewer ${reviewerNumber}` : 'Reviewer';
    const prompt = `You are an expert academic editor with extensive experience in peer review and manuscript revision. You are revising the ${section} section based on specific feedback from ${reviewerInfo}.

CRITICAL INSTRUCTIONS:
1. ONLY make changes that DIRECTLY address the reviewer comments
2. Do NOT make assumptions about figures, tables, or data you cannot see
3. If a comment mentions figures/tables/data that require manual intervention, mark it as unaddressed
4. Preserve the original academic tone, style, and meaning
5. Ensure all changes are scientifically sound and improve clarity
6. Track every single change with precise justification

ORIGINAL ${section.toUpperCase()} SECTION:
${content}

${reviewerInfo} COMMENTS TO ADDRESS:
${comments.map((c, i) => `Comment ${i + 1} (ID: ${c.commentId || `comment${i + 1}`}): ${c.comment}`).join('\n')}

ARTICLE CONTEXT:
Title: ${article.title}
${article.abstract ? `Abstract: ${article.abstract.substring(0, 300)}...` : ''}

VALIDATION CHECKLIST:
- Does each change directly address a specific comment?
- Are the changes scientifically accurate and appropriate?
- Is the academic tone preserved?
- Are citations and formatting maintained?
- Are figure/table references handled appropriately?

REQUIRED JSON OUTPUT:
{
  "revisedContent": "Complete revised section with all improvements integrated naturally",
  "changes": [
    {
      "type": "modification|addition|deletion",
      "originalText": "Exact original text (must exist in the section)",
      "revisedText": "Exact replacement text",
      "reason": "Specific explanation linking to reviewer comment",
      "commentIds": ["comment1"],
      "confidence": 85
    }
  ],
  "addressedComments": ["comment1", "comment2"],
  "unaddressedComments": [
    {
      "comment": "Comment requiring manual intervention",
      "reason": "Requires figure modification/data analysis/etc."
    }
  ],
  "revisionSummary": "Concise summary of changes made for this reviewer"
}

IMPORTANT: Only include changes you are confident about. Mark uncertain items as unaddressed.`;
    
    try {
      const response = await geminiAIService.generateContent(
        prompt,
        'gemini-2.5-pro',
        { maxTokens: 6000, temperature: 0.4 }
      );

      console.log(`AI response for ${section} section:`, response.substring(0, 200) + '...');

      // Try to parse the JSON response, with fallback handling
      let result;
      try {
        result = JSON.parse(response);
      } catch (parseError) {
        console.warn(`JSON parsing failed for ${section}, attempting to extract content manually`);
        // Fallback: create a basic revision structure
        result = {
          revisedContent: response,
          changes: [{
            type: "modification",
            originalText: content.substring(0, 100) + "...",
            revisedText: response.substring(0, 100) + "...",
            reason: "AI revision based on reviewer comments",
            commentIds: comments.map((_, i) => `comment-${i + 1}`)
          }],
          addressedComments: comments.map((_, i) => `comment-${i + 1}`),
          unaddressedComments: [],
          revisionSummary: `Revised ${section} section based on reviewer feedback`
        };
      }
      
      return {
        section,
        originalContent: content,
        revisedContent: result.revisedContent,
        changes: result.changes.map((change: any, index: number) => ({
          id: `${section}-change-${index + 1}`,
          section,
          changeType: change.type,
          originalText: change.originalText,
          revisedText: change.revisedText,
          reason: change.reason,
          relatedCommentIds: change.commentIds,
          confidence: 85 // Default confidence
        })),
        addressedComments: result.addressedComments,
        unaddressedComments: result.unaddressedComments.map((uc: any) => uc.comment),
        revisionSummary: result.revisionSummary,
        qualityScore: 85 // Default quality score
      };
      
    } catch (error) {
      console.error(`Error revising section ${section}:`, error);
      throw new Error(`Failed to revise ${section} section: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Integrate all section revisions into a complete article
   */
  private async integrateRevisions(
    originalArticle: ParsedArticle,
    sectionRevisions: Record<ArticleSection, SectionRevision>
  ): Promise<RevisedArticle> {
    const store = useRevisionWorkflowStore.getState();
    
    try {
      // Combine all changes
      const allChanges: ArticleChange[] = [];
      Object.values(sectionRevisions).forEach(revision => {
        allChanges.push(...revision.changes);
      });
      
      // Calculate metadata
      const totalChanges = allChanges.length;
      const addressedComments = new Set();
      const unaddressedComments = new Set();
      
      Object.values(sectionRevisions).forEach(revision => {
        revision.addressedComments.forEach(id => addressedComments.add(id));
        revision.unaddressedComments.forEach(id => unaddressedComments.add(id));
      });
      
      const revisedArticle: RevisedArticle = {
        originalArticle,
        sectionRevisions,
        overallChanges: allChanges,
        metadata: {
          revisionDate: new Date(),
          totalChanges,
          addressedComments: addressedComments.size,
          unaddressedComments: unaddressedComments.size,
          processingTime: Date.now() // This would be calculated properly
        }
      };
      
      store.setAIAssistantProgress('integration-manager', 100);
      return revisedArticle;
      
    } catch (error) {
      store.setAIAssistantError('integration-manager', error instanceof Error ? error.message : 'Integration failed');
      throw error;
    }
  }
  
  /**
   * Generate response letter to reviewers
   */
  private async generateResponseLetter(
    article: ParsedArticle,
    reviewerComments: ParsedReviewerComments[],
    sectionRevisions: Record<ArticleSection, SectionRevision>
  ): Promise<ResponseLetter> {
    const store = useRevisionWorkflowStore.getState();
    
    const totalChanges = Object.values(sectionRevisions).reduce((sum, rev) => sum + rev.changes.length, 0);
    const totalComments = reviewerComments.reduce((sum, rc) => sum + rc.comments.length, 0);

    const prompt = `Generate a professional academic response letter following the standard format used in scholarly journals.

MANUSCRIPT DETAILS:
Title: ${article.title}
Authors: <AUTHORS>
Journal: [Journal Name]
Manuscript ID: [To be provided]

REVISION SUMMARY:
- Total reviewer comments: ${totalComments}
- Total changes made: ${totalChanges}
- Sections revised: ${Object.keys(sectionRevisions).join(', ')}

REVIEWER COMMENTS AND CHANGES:
${reviewerComments.map(rc => `
REVIEWER ${rc.reviewerNumber} (${rc.comments.length} comments):
${rc.comments.map((c, idx) => {
  const relatedChanges = Object.values(sectionRevisions)
    .flatMap(rev => rev.changes)
    .filter(change => change.relatedCommentIds?.includes(c.commentId || `comment${idx + 1}`));

  return `Comment ${idx + 1}: ${c.comment}
  Changes made: ${relatedChanges.length > 0 ? relatedChanges.map(ch => ch.reason).join('; ') : 'Requires manual intervention'}`;
}).join('\n\n')}
`).join('\n')}

SECTION REVISIONS:
${Object.entries(sectionRevisions).map(([section, revision]) => `
${section.toUpperCase()}:
- Changes: ${revision.changes.length}
- Summary: ${revision.revisionSummary}
- Addressed comments: ${revision.addressedComments.length}
`).join('\n')}

Generate a response letter in the following academic format:

STRUCTURE:
1. Header with manuscript details
2. Professional greeting and appreciation
3. Summary of major changes
4. Point-by-point responses to each reviewer (Comment → Response format)
5. Legend explaining color coding/formatting
6. Professional conclusion

RESPONSE FORMAT for each comment:
"Comment X: [Original reviewer comment]
Response: [Detailed response explaining how the comment was addressed]
Location of Change: [Specific location in manuscript]
Revised Text: [If applicable, show the revised text]"

Generate as structured text (not JSON) following academic standards.`;
    
    try {
      const response = await geminiAIService.generateContent(
        prompt,
        'gemini-2.5-pro',
        { maxTokens: 8000, temperature: 0.3 }
      );
      
      console.log('Generated response letter:', response.substring(0, 300) + '...');

      // Parse the response into structured format
      const addressedComments = Object.values(sectionRevisions)
        .reduce((sum, rev) => sum + rev.addressedComments.length, 0);

      const manualInterventions = Object.values(sectionRevisions)
        .flatMap(rev => rev.unaddressedComments)
        .map(comment => ({
          comment: typeof comment === 'string' ? comment : comment.comment,
          reason: typeof comment === 'string' ? 'Requires manual intervention' : comment.reason,
          section: 'various'
        }));

      const responseLetter: ResponseLetter = {
        title: article.title || 'Untitled Manuscript',
        authors: this.extractAuthorsFromArticle(article),
        revisionDate: new Date(),
        introduction: this.extractIntroductionFromResponse(response),
        summaryOfChanges: this.extractSummaryFromRevisions(sectionRevisions),
        reviewerResponses: this.parseReviewerResponses(response, reviewerComments),
        conclusion: this.extractConclusionFromResponse(response),
        totalComments: reviewerComments.reduce((sum, rc) => sum + rc.comments.length, 0),
        addressedComments,
        manualInterventionRequired: manualInterventions,
        fullResponseText: response
      };
      
      store.setAIAssistantProgress('response-generator', 100);
      return responseLetter;
      
    } catch (error) {
      store.setAIAssistantError('response-generator', error instanceof Error ? error.message : 'Response generation failed');
      throw error;
    }
  }
  
  /**
   * Identify manual interventions needed
   */
  private async identifyManualInterventions(
    reviewerComments: ParsedReviewerComments[],
    sectionRevisions: Record<ArticleSection, SectionRevision>
  ): Promise<ManualIntervention[]> {
    const interventions: ManualIntervention[] = [];
    
    // Identify comments that require manual intervention
    reviewerComments.forEach(rc => {
      rc.comments.forEach(comment => {
        if (comment.requiresManualIntervention) {
          interventions.push({
            id: `manual-${comment.id}`,
            type: comment.category === 'figure' ? 'figure' : 'complex-revision',
            description: comment.comment,
            relatedComments: [comment.id],
            suggestedActions: [`Address: ${comment.comment}`],
            priority: comment.severity === 'critical' ? 'critical' : 'medium',
            estimatedEffort: '30-60 minutes'
          });
        }
      });
    });
    
    return interventions;
  }
  
  /**
   * Group comments by target section
   */
  private groupCommentsBySection(
    reviewerComments: ParsedReviewerComments[]
  ): Record<ArticleSection, any[]> {
    const grouped: Record<ArticleSection, any[]> = {} as any;

    reviewerComments.forEach(rc => {
      rc.comments.forEach(comment => {
        let section = comment.targetSection || 'overall';

        // Try to infer section from comment content if not specified
        if (section === 'overall' || !section) {
          const commentText = comment.comment.toLowerCase();

          if (commentText.includes('introduction') || commentText.includes('intro')) {
            section = 'introduction';
          } else if (commentText.includes('method') || commentText.includes('approach')) {
            section = 'methodology';
          } else if (commentText.includes('result') || commentText.includes('finding')) {
            section = 'results';
          } else if (commentText.includes('discussion') || commentText.includes('interpret')) {
            section = 'discussion';
          } else if (commentText.includes('conclusion') || commentText.includes('conclud')) {
            section = 'conclusion';
          } else if (commentText.includes('abstract')) {
            section = 'abstract';
          } else if (commentText.includes('reference') || commentText.includes('citation')) {
            section = 'references';
          } else {
            section = 'overall'; // Keep as overall if no specific section detected
          }
        }

        if (!grouped[section]) {
          grouped[section] = [];
        }
        grouped[section].push(comment);
      });
    });

    console.log('Comments grouped by section:', Object.keys(grouped).map(k => `${k}: ${grouped[k].length}`));
    return grouped;
  }

  /**
   * Extract authors from article content
   */
  private extractAuthorsFromArticle(article: ParsedArticle): string[] {
    // Try to extract authors from the article text
    const text = article.fullText || '';
    const authorMatch = text.match(/authors?:\s*([^\n]+)/i) ||
                       text.match(/by\s+([^\n]+)/i);

    if (authorMatch) {
      return authorMatch[1].split(/[,&]/).map(author => author.trim());
    }

    return ['Author Name']; // Default fallback
  }

  /**
   * Extract introduction from response
   */
  private extractIntroductionFromResponse(response: string): string {
    const lines = response.split('\n');
    const introStart = lines.findIndex(line =>
      line.toLowerCase().includes('dear') ||
      line.toLowerCase().includes('thank')
    );

    if (introStart >= 0) {
      const introEnd = lines.findIndex((line, idx) =>
        idx > introStart && (
          line.toLowerCase().includes('summary') ||
          line.toLowerCase().includes('response to')
        )
      );

      return lines.slice(introStart, introEnd > 0 ? introEnd : introStart + 3).join('\n');
    }

    return 'We thank the reviewers for their constructive feedback and have carefully addressed all comments.';
  }

  /**
   * Extract summary from revisions
   */
  private extractSummaryFromRevisions(sectionRevisions: Record<ArticleSection, SectionRevision>): string[] {
    return Object.entries(sectionRevisions).map(([section, revision]) =>
      `${section}: ${revision.revisionSummary} (${revision.changes.length} changes)`
    );
  }

  /**
   * Parse reviewer responses from the generated text
   */
  private parseReviewerResponses(response: string, reviewerComments: ParsedReviewerComments[]): Record<string, any> {
    const responses: Record<string, any> = {};

    reviewerComments.forEach(rc => {
      responses[`reviewer${rc.reviewerNumber}`] = {
        reviewerNumber: rc.reviewerNumber,
        responses: rc.comments.map((comment, idx) => ({
          commentId: `R${rc.reviewerNumber}-C${idx + 1}`,
          originalComment: comment.comment,
          response: `This comment has been addressed through targeted revisions.`,
          locationOfChange: 'See revised manuscript',
          status: 'addressed'
        }))
      };
    });

    return responses;
  }

  /**
   * Extract conclusion from response
   */
  private extractConclusionFromResponse(response: string): string {
    const lines = response.split('\n');
    const conclusionStart = lines.findIndex(line =>
      line.toLowerCase().includes('conclusion') ||
      line.toLowerCase().includes('believe') ||
      line.toLowerCase().includes('improved')
    );

    if (conclusionStart >= 0) {
      return lines.slice(conclusionStart, conclusionStart + 3).join('\n');
    }

    return 'We believe these revisions have significantly improved the manuscript and addressed all reviewer concerns.';
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Cancel all active requests
   */
  cancelAllRequests(): void {
    this.activeRequests.forEach(controller => controller.abort());
    this.activeRequests.clear();
  }
}
