/**
 * RAG (Retrieval-Augmented Generation) Service
 * Handles vector embeddings, document chunking, and similarity search
 * for document-based Q&A in the Research Comprehension Platform
 */

import { ResearchDocument, DocumentChunk, RAGQuery, RAGResult, RAGResponse, ChunkMetadata } from '../types';
import { documentStorageService } from './document-storage.service';
import { supabase } from '@/lib/supabase';

interface EmbeddingResponse {
  embedding: number[];
  error?: string;
}

interface ChunkingOptions {
  chunkSize: number;
  overlap: number;
  preserveSentences: boolean;
}

class RAGService {
  private readonly DEFAULT_CHUNK_SIZE = 1000;
  private readonly DEFAULT_OVERLAP = 200;
  private readonly DEFAULT_THRESHOLD = 0.7;
  private readonly GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

  /**
   * Check if RAG service is configured
   */
  isConfigured(): boolean {
    return Boolean(this.GEMINI_API_KEY);
  }

  /**
   * Process document for RAG by creating chunks and embeddings
   */
  async processDocumentForRAG(document: ResearchDocument): Promise<DocumentChunk[]> {
    if (!this.isConfigured()) {
      throw new Error('Gemini API key not configured');
    }

    try {
      console.log(`Processing document "${document.title}" for RAG...`);

      // Step 1: Create chunks from document content
      const chunks = await this.createDocumentChunks(document, {
        chunkSize: this.DEFAULT_CHUNK_SIZE,
        overlap: this.DEFAULT_OVERLAP,
        preserveSentences: true
      });

      console.log(`Created ${chunks.length} chunks for document`);

      // Step 2: Generate embeddings for each chunk
      const chunksWithEmbeddings: DocumentChunk[] = [];
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        
        try {
          const embedding = await this.generateEmbedding(chunk.content);
          
          if (embedding) {
            chunksWithEmbeddings.push({
              ...chunk,
              embedding
            });
          } else {
            console.warn(`Failed to generate embedding for chunk ${i}`);
            // Add chunk without embedding for fallback search
            chunksWithEmbeddings.push(chunk);
          }

          // Add delay to avoid rate limiting
          if (i < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error(`Error processing chunk ${i}:`, error);
          // Add chunk without embedding
          chunksWithEmbeddings.push(chunk);
        }
      }

      // Step 3: Store chunks in database
      await this.storeDocumentChunks(chunksWithEmbeddings);

      console.log(`Successfully processed ${chunksWithEmbeddings.length} chunks for RAG`);
      return chunksWithEmbeddings;

    } catch (error) {
      console.error('Error processing document for RAG:', error);
      throw new Error(`Failed to process document for RAG: ${error.message}`);
    }
  }

  /**
   * Perform RAG query to find relevant document chunks
   */
  async performRAGQuery(query: RAGQuery): Promise<RAGResponse> {
    if (!this.isConfigured()) {
      throw new Error('Gemini API key not configured');
    }

    const startTime = Date.now();

    try {
      console.log(`Performing RAG query: "${query.query}"`);

      // Step 1: Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query.query);
      
      if (!queryEmbedding) {
        throw new Error('Failed to generate query embedding');
      }

      // Step 2: Retrieve relevant chunks
      const relevantChunks = await this.findSimilarChunks(
        queryEmbedding,
        query.documentId,
        query.maxResults || 5,
        query.threshold || this.DEFAULT_THRESHOLD
      );

      // Step 3: Create RAG results
      const results: RAGResult[] = relevantChunks.map(chunk => ({
        chunk,
        similarity: chunk.similarity || 0,
        relevantText: this.extractRelevantText(chunk.content, query.query),
        context: this.generateContext(chunk),
        citation: this.generateCitation(chunk)
      }));

      // Step 4: Generate AI answer based on retrieved chunks
      const generatedAnswer = await this.generateAnswerFromChunks(query.query, results);

      const searchTime = Date.now() - startTime;

      const response: RAGResponse = {
        query: query.query,
        results,
        totalResults: results.length,
        searchTime,
        generatedAnswer,
        confidence: this.calculateConfidence(results),
        sources: [...new Set(results.map(r => r.chunk.documentId))]
      };

      console.log(`RAG query completed in ${searchTime}ms with ${results.length} results`);
      return response;

    } catch (error) {
      console.error('Error performing RAG query:', error);
      
      // Return fallback response
      return {
        query: query.query,
        results: [],
        totalResults: 0,
        searchTime: Date.now() - startTime,
        generatedAnswer: `I apologize, but I encountered an error while searching for information about "${query.query}". Please try rephrasing your question or check if the document has been properly processed.`,
        confidence: 0,
        sources: []
      };
    }
  }

  /**
   * Create document chunks from content
   */
  private async createDocumentChunks(
    document: ResearchDocument,
    options: ChunkingOptions
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    
    // Process each section separately to maintain context
    for (const section of document.sections) {
      const sectionChunks = this.chunkText(
        section.content,
        options,
        section.id,
        section.title
      );
      
      sectionChunks.forEach((chunk, index) => {
        chunks.push({
          id: crypto.randomUUID(),
          documentId: document.id,
          sectionId: section.id,
          content: chunk.content,
          metadata: {
            sectionTitle: section.title,
            chunkType: this.detectChunkType(chunk.content),
            wordCount: chunk.content.split(/\s+/).length,
            keyTerms: this.extractKeyTerms(chunk.content),
            importance: this.calculateImportance(chunk.content, section.type),
            pageNumber: section.startPage
          },
          chunkIndex: chunk.index,
          startPosition: chunk.startPosition,
          endPosition: chunk.endPosition
        });
      });
    }

    return chunks;
  }

  /**
   * Split text into chunks with overlap
   */
  private chunkText(
    text: string,
    options: ChunkingOptions,
    sectionId: string,
    sectionTitle: string
  ): Array<{
    content: string;
    index: number;
    startPosition: number;
    endPosition: number;
  }> {
    const chunks = [];
    const words = text.split(/\s+/);
    let currentPosition = 0;
    let chunkIndex = 0;

    while (currentPosition < words.length) {
      const chunkWords = words.slice(currentPosition, currentPosition + options.chunkSize);
      let chunkText = chunkWords.join(' ');

      // If preserving sentences, try to end at sentence boundary
      if (options.preserveSentences && currentPosition + options.chunkSize < words.length) {
        const lastSentenceEnd = chunkText.lastIndexOf('.');
        if (lastSentenceEnd > chunkText.length * 0.7) {
          chunkText = chunkText.substring(0, lastSentenceEnd + 1);
        }
      }

      chunks.push({
        content: chunkText.trim(),
        index: chunkIndex++,
        startPosition: currentPosition,
        endPosition: currentPosition + chunkWords.length
      });

      // Move position with overlap
      currentPosition += options.chunkSize - options.overlap;
    }

    return chunks;
  }

  /**
   * Generate embedding using Gemini API
   */
  private async generateEmbedding(text: string): Promise<number[] | null> {
    if (!this.GEMINI_API_KEY) {
      console.warn('Gemini API key not available for embeddings');
      return null;
    }

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/embedding-001:embedContent?key=${this.GEMINI_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'models/embedding-001',
            content: {
              parts: [{ text: text.substring(0, 2048) }] // Limit text length
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Embedding API error: ${response.status}`);
      }

      const data = await response.json();
      return data.embedding?.values || null;

    } catch (error) {
      console.error('Error generating embedding:', error);
      return null;
    }
  }

  /**
   * Find similar chunks using cosine similarity
   */
  private async findSimilarChunks(
    queryEmbedding: number[],
    documentId?: string,
    maxResults: number = 5,
    threshold: number = 0.7
  ): Promise<Array<DocumentChunk & { similarity: number }>> {
    try {
      // In a production environment, you would use a vector database like Pinecone or Weaviate
      // For now, we'll implement a simple in-memory similarity search
      
      const chunks = await this.getStoredChunks(documentId);
      const similarities: Array<DocumentChunk & { similarity: number }> = [];

      for (const chunk of chunks) {
        if (chunk.embedding) {
          const similarity = this.cosineSimilarity(queryEmbedding, chunk.embedding);
          
          if (similarity >= threshold) {
            similarities.push({
              ...chunk,
              similarity
            });
          }
        }
      }

      // Sort by similarity and return top results
      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, maxResults);

    } catch (error) {
      console.error('Error finding similar chunks:', error);
      return [];
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * Generate answer from retrieved chunks using Gemini
   */
  private async generateAnswerFromChunks(query: string, results: RAGResult[]): Promise<string> {
    if (!this.GEMINI_API_KEY || results.length === 0) {
      return "I couldn't find relevant information to answer your question.";
    }

    try {
      const context = results
        .map((result, index) => `[${index + 1}] ${result.chunk.content}`)
        .join('\n\n');

      const prompt = `Based on the following context from research documents, please answer the question. Provide a comprehensive answer and cite the relevant sources using [1], [2], etc.

Context:
${context}

Question: ${query}

Answer:`;

      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key=${this.GEMINI_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }],
            generationConfig: {
              temperature: 0.3,
              maxOutputTokens: 1000
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      return data.candidates?.[0]?.content?.parts?.[0]?.text || 
             "I couldn't generate an answer based on the available information.";

    } catch (error) {
      console.error('Error generating answer:', error);
      return "I encountered an error while generating an answer. Please try again.";
    }
  }

  // Helper methods
  private detectChunkType(content: string): ChunkMetadata['chunkType'] {
    if (content.match(/^#+\s/)) return 'heading';
    if (content.match(/^\s*[-*+]\s/m)) return 'list';
    if (content.match(/\|.*\|/)) return 'table';
    if (content.match(/figure|fig\.|table|tab\./i)) return 'figure-caption';
    return 'paragraph';
  }

  private extractKeyTerms(content: string): string[] {
    // Simple keyword extraction
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const frequency: { [key: string]: number } = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .filter(([, count]) => count > 1)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  private calculateImportance(content: string, sectionType: string): number {
    let importance = 3; // Base importance
    
    if (sectionType === 'abstract') importance += 2;
    if (sectionType === 'conclusion') importance += 1;
    if (content.includes('important') || content.includes('significant')) importance += 1;
    if (content.length > 500) importance += 1;
    
    return Math.min(importance, 5);
  }

  private extractRelevantText(content: string, query: string): string {
    const queryWords = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[.!?]+/);
    
    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();
      if (queryWords.some(word => lowerSentence.includes(word))) {
        return sentence.trim();
      }
    }
    
    return content.substring(0, 200) + '...';
  }

  private generateContext(chunk: DocumentChunk): string {
    return `From section "${chunk.metadata.sectionTitle}" (${chunk.metadata.chunkType})`;
  }

  private generateCitation(chunk: DocumentChunk): string {
    return `Section: ${chunk.metadata.sectionTitle}${chunk.metadata.pageNumber ? `, Page ${chunk.metadata.pageNumber}` : ''}`;
  }

  private calculateConfidence(results: RAGResult[]): number {
    if (results.length === 0) return 0;
    
    const avgSimilarity = results.reduce((sum, r) => sum + r.similarity, 0) / results.length;
    return Math.min(avgSimilarity * 1.2, 1); // Boost confidence slightly
  }

  // Database operations for document chunks
  private async storeDocumentChunks(chunks: DocumentChunk[]): Promise<void> {
    if (!documentStorageService.isConfigured()) {
      console.warn('Supabase not configured, skipping chunk storage');
      return;
    }

    try {
      // Store chunks in batches to avoid overwhelming the database
      const batchSize = 10;
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batch = chunks.slice(i, i + batchSize);

        const chunksData = batch.map(chunk => ({
          id: chunk.id,
          document_id: chunk.documentId,
          section_id: chunk.sectionId,
          content: chunk.content,
          embedding: chunk.embedding ? JSON.stringify(chunk.embedding) : null,
          metadata: chunk.metadata,
          chunk_index: chunk.chunkIndex,
          start_position: chunk.startPosition,
          end_position: chunk.endPosition
        }));

        const { error } = await supabase
          .from('document_chunks')
          .insert(chunksData);

        if (error) {
          console.error('Error storing chunk batch:', error);
        }

        // Add delay between batches
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`Successfully stored ${chunks.length} chunks in database`);
    } catch (error) {
      console.error('Error storing document chunks:', error);
    }
  }

  private async getStoredChunks(documentId?: string): Promise<DocumentChunk[]> {
    if (!documentStorageService.isConfigured()) {
      console.warn('Supabase not configured, returning empty chunks');
      return [];
    }

    try {
      let query = supabase
        .from('document_chunks')
        .select('*');

      if (documentId) {
        query = query.eq('document_id', documentId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error retrieving chunks:', error);
        return [];
      }

      return (data || []).map(chunk => ({
        id: chunk.id,
        documentId: chunk.document_id,
        sectionId: chunk.section_id,
        content: chunk.content,
        embedding: chunk.embedding ? JSON.parse(chunk.embedding) : undefined,
        metadata: chunk.metadata,
        chunkIndex: chunk.chunk_index,
        startPosition: chunk.start_position,
        endPosition: chunk.end_position
      }));
    } catch (error) {
      console.error('Error retrieving stored chunks:', error);
      return [];
    }
  }
}

export const ragService = new RAGService();
