/**
 * Context Management Components Index
 * Exports all context management and mode switching components
 */

export { default as ModeSwitch } from './ModeSwitch';
export type { AIMode } from './ModeSwitch';

// Re-export services
export { 
  educationalContextDetector,
  type EducationalContext,
  type IntentClassification 
} from '../../services/educational-context-detector.service';

export { 
  educationalBoundaryEnforcer,
  type BoundaryCheckResult,
  type SessionContext 
} from '../../services/educational-boundary-enforcer.service';
