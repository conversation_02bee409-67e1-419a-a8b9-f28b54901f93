# AI Tutor Module

A comprehensive AI-powered tutoring system integrated into the research platform. Provides personalized learning experiences with adaptive explanations, educational sources, and interactive conversations.

## Features

### 🎓 Personalized Learning
- **Education Level Adaptation**: Automatically adjusts explanations for Elementary through Graduate levels
- **Learning Style Support**: Visual, Auditory, Kinesthetic, and Reading/Writing approaches
- **Adaptive Pace**: Slow, Normal, or Fast learning speeds
- **Custom AI Models**: Multiple AI providers (Gemini, Claude, GPT-4) optimized for education

### 💬 Interactive Tutoring
- **Real-time Chat Interface**: Streaming AI responses with conversation history
- **Context-Aware Conversations**: Maintains learning context across sessions
- **Quick Actions**: Pre-built prompts for common requests (examples, details, real-world applications)
- **Follow-up Suggestions**: AI-generated next steps and related topics

### 📚 Educational Sources
- **Automatic Source Fetching**: Retrieves relevant educational materials using Tavily search
- **Source Quality Filtering**: Prioritizes academic and educational content
- **Relevance Scoring**: Ranks sources by educational value and topic relevance
- **Multiple Source Types**: Academic papers, educational websites, videos, books

### 📊 Session Management
- **Session History**: Save and resume learning sessions
- **Progress Tracking**: Monitor concepts learned and time spent
- **Export Capabilities**: Download sessions as PDF, Word, HTML, or text
- **Metadata Tracking**: Session duration, message count, difficulty level

### 🎯 Topic Suggestions
- **Curated Learning Topics**: Pre-built suggestions across multiple subjects
- **Category Organization**: Mathematics, Science, Technology, Languages, History, Arts
- **Difficulty Indicators**: Beginner, Intermediate, Advanced levels
- **Time Estimates**: Expected learning duration for each topic

## Architecture

### Components
```
src/components/research/ai-tutor/
├── AITutor.tsx                 # Main component with navigation
├── components/
│   ├── TutorHero.tsx          # Landing page with topic input
│   ├── TutorChat.tsx          # Interactive chat interface
│   ├── TutorSources.tsx       # Educational sources sidebar
│   ├── TutorHistory.tsx       # Session history management
│   └── TutorSettings.tsx      # User preferences and configuration
├── services/
│   ├── tutor-ai.service.ts    # AI model interactions
│   ├── tutor-sources.service.ts # Educational source fetching
│   └── tutor-export.service.ts # Session export functionality
├── stores/
│   └── tutor.store.ts         # Zustand state management
├── types.ts                   # TypeScript interfaces
└── constants.ts               # Configuration and defaults
```

### State Management
Uses Zustand for efficient state management with persistence:
- Current session state
- User settings and preferences
- Session history (last 50 sessions)
- UI state (loading, errors, current view)

### AI Integration
Integrates with existing AI infrastructure:
- **OpenRouter API**: Primary AI service with multiple model support
- **Model Selection**: Gemini 2.5 Flash (default), Claude 3.5 Sonnet, GPT-4o
- **Streaming Support**: Real-time response generation
- **Context Management**: Maintains conversation history for coherent learning

### Search Integration
Leverages existing search services:
- **Tavily Search**: Academic and educational source discovery
- **Query Enhancement**: Automatically improves search terms for educational content
- **Source Filtering**: Removes inappropriate or low-quality sources
- **Education Level Matching**: Finds age-appropriate materials

## Usage

### Starting a Session
1. Navigate to "AI Tutor" in the research dashboard
2. Enter a learning topic (e.g., "Quantum Physics", "Python Programming")
3. Select your education level
4. Click "Start Learning" to begin

### During a Session
- Ask questions in natural language
- Use quick action buttons for common requests
- View educational sources in the sidebar
- Click sources to open in new tabs

### Managing Sessions
- View session history to resume previous topics
- Export important conversations for later review
- Adjust settings for personalized learning experience

## Configuration

### Environment Variables
The AI Tutor uses existing API keys:
```env
VITE_OPENROUTER_API_KEY=your_openrouter_key
VITE_TAVILY_API_KEY=your_tavily_key
VITE_GEMINI_API_KEY=your_gemini_key (optional)
VITE_OPENAI_API_KEY=your_openai_key (optional)
```

### Default Settings
- **AI Model**: Gemini 2.5 Flash
- **Education Level**: High School
- **Learning Style**: Visual
- **Pace**: Normal
- **Sources**: Enabled (max 5)
- **Examples**: Enabled

### Customization
Users can modify settings through the Settings panel:
- Change preferred AI model
- Adjust education level
- Select learning style preference
- Configure content options (examples, quizzes, sources)
- Set notification preferences

## Integration

### Navigation
Integrated into the main research dashboard:
- Added to Sidebar menu with GraduationCap icon
- Included in ResearchDashboard routing
- Follows existing UI/UX patterns

### Data Persistence
- Session data stored in browser localStorage
- Settings persisted across browser sessions
- Integration ready for Supabase database storage

### Export Integration
- Consistent with other module export patterns
- Multiple format support (PDF, Word, HTML, Text)
- Includes conversation history, sources, and metadata

## Future Enhancements

### Planned Features
- **Supabase Integration**: Cloud storage for sessions and progress
- **Advanced Analytics**: Learning progress visualization
- **Collaborative Learning**: Share sessions with others
- **Quiz Generation**: Automatic comprehension testing
- **Voice Integration**: Audio explanations and voice input
- **Mobile Optimization**: Responsive design improvements

### Technical Improvements
- **Streaming UI**: Real-time message updates during AI generation
- **Advanced Source Filtering**: Better educational content detection
- **Performance Optimization**: Lazy loading and caching
- **Accessibility**: Screen reader support and keyboard navigation

## Dependencies

### Core Dependencies
- React 18+ with TypeScript
- Zustand for state management
- Lucide React for icons
- Tailwind CSS for styling

### UI Components
- shadcn/ui component library
- React Markdown for message rendering
- Sonner for toast notifications

### Services
- OpenRouter API for AI models
- Tavily API for educational search
- Existing platform authentication

## Development

### Adding New Features
1. Follow existing architectural patterns
2. Add types to `types.ts`
3. Update constants in `constants.ts`
4. Implement services in `services/`
5. Create UI components in `components/`
6. Update store in `stores/tutor.store.ts`

### Testing
- Test with different education levels
- Verify source quality and relevance
- Check export functionality
- Validate AI model switching
- Test session persistence

### Debugging
- Check browser console for API errors
- Verify API keys in environment variables
- Monitor network requests for service issues
- Use React DevTools for state inspection

## Support

For issues or questions:
1. Check the browser console for errors
2. Verify API key configuration
3. Test with different topics and education levels
4. Review session history for patterns
5. Check network connectivity for external services
