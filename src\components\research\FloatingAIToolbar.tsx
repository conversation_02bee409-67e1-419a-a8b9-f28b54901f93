import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Sparkles, 
  CheckCircle, 
  PenTool, 
  AlignLeft,
  Edit,
  X,
  Loader2
} from 'lucide-react';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import { toast } from 'sonner';

interface FloatingAIToolbarProps {
  selectedText: string;
  position: { x: number; y: number };
  onApplyResult: (result: string, mode: 'replace' | 'insert') => void;
  onClose: () => void;
  isLoading?: boolean;
}

export function FloatingAIToolbar({ 
  selectedText, 
  position, 
  onApplyResult, 
  onClose,
  isLoading = false 
}: FloatingAIToolbarProps) {
  const [processingTool, setProcessingTool] = React.useState<string | null>(null);

  const quickTools = [
    {
      id: 'clarity',
      name: 'Improve',
      icon: <Sparkles className="h-3 w-3" />,
      color: 'blue',
      description: 'Improve clarity and flow'
    },
    {
      id: 'grammar',
      name: 'Grammar',
      icon: <CheckCircle className="h-3 w-3" />,
      color: 'green',
      description: 'Fix grammar and style'
    },
    {
      id: 'academic',
      name: 'Academic',
      icon: <PenTool className="h-3 w-3" />,
      color: 'purple',
      description: 'Convert to academic tone'
    },
    {
      id: 'expand',
      name: 'Expand',
      icon: <AlignLeft className="h-3 w-3" />,
      color: 'indigo',
      description: 'Add more detail'
    }
  ];

  const handleQuickAction = async (toolId: string) => {
    if (!selectedText.trim() || isLoading) return;
    
    setProcessingTool(toolId);
    
    try {
      const result = await enhancedAIService.quickEnhance(
        selectedText, 
        toolId as 'clarity' | 'grammar' | 'academic' | 'expand'
      );
      
      onApplyResult(result, 'replace');
      onClose();
      
    } catch (error: any) {
      console.error('Quick action failed:', error);
      toast.error(`Failed to ${toolId} text: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  const getColorClasses = (color: string) => {
    const classes = {
      blue: 'bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-200',
      green: 'bg-green-100 hover:bg-green-200 text-green-700 border-green-200',
      purple: 'bg-purple-100 hover:bg-purple-200 text-purple-700 border-purple-200',
      indigo: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border-indigo-200'
    };
    return classes[color as keyof typeof classes] || classes.blue;
  };

  return (
    <Card 
      className="fixed z-50 p-2 shadow-lg border bg-white/95 backdrop-blur-sm"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateY(-100%)',
        maxWidth: '280px'
      }}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-1">
          <Edit className="h-3 w-3 text-gray-500" />
          <span className="text-xs font-medium text-gray-600">Quick AI Actions</span>
          <Badge variant="secondary" className="text-xs">
            {selectedText.length} chars
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-5 w-5 p-0 hover:bg-gray-100"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
      
      <div className="grid grid-cols-2 gap-1">
        {quickTools.map((tool) => (
          <Button
            key={tool.id}
            variant="outline"
            size="sm"
            className={`h-auto p-2 flex flex-col items-center gap-1 text-xs transition-all duration-200 ${getColorClasses(tool.color)}`}
            onClick={() => handleQuickAction(tool.id)}
            disabled={isLoading || processingTool !== null}
            title={tool.description}
          >
            {processingTool === tool.id ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              tool.icon
            )}
            <span className="font-medium">{tool.name}</span>
          </Button>
        ))}
      </div>
      
      <div className="mt-2 text-xs text-gray-500 text-center">
        AI will enhance your selected text
      </div>
    </Card>
  );
}
