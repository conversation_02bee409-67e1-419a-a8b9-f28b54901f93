/**
 * Saved Articles Dialog Component
 * Shows user's saved articles with options to view, export, or delete
 */

import React, { useState, useEffect } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Download, 
  Trash2, 
  Eye, 
  Calendar, 
  BarChart3,
  Quote,
  Clock,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

import { CompleteArticle } from '../services/complete-article-generation.service';
import { articleExportService } from '../services/article-export.service';

interface SavedArticlesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onViewArticle: (article: CompleteArticle) => void;
  userId?: string;
}

export function SavedArticlesDialog({
  isOpen,
  onClose,
  onViewArticle,
  userId
}: SavedArticlesDialogProps) {
  const [savedArticles, setSavedArticles] = useState<CompleteArticle[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [deletingArticleId, setDeletingArticleId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadSavedArticles();
    }
  }, [isOpen, userId]);

  const loadSavedArticles = async () => {
    setIsLoading(true);
    try {
      const articles = await articleExportService.loadSavedArticles(userId);
      setSavedArticles(articles);
    } catch (error) {
      console.error('Error loading saved articles:', error);
      toast.error('Failed to load saved articles');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteArticle = async (articleId: string) => {
    setDeletingArticleId(articleId);
    try {
      await articleExportService.deleteArticle(articleId);
      setSavedArticles(prev => prev.filter(article => article.id !== articleId));
      toast.success('Article deleted successfully');
    } catch (error) {
      console.error('Error deleting article:', error);
      toast.error('Failed to delete article');
    } finally {
      setDeletingArticleId(null);
    }
  };

  const handleExportArticle = async (article: CompleteArticle, format: 'pdf' | 'docx' | 'txt') => {
    try {
      const blob = await articleExportService.exportArticle(article, {
        format: format,
        includeMetadata: true,
        citationStyle: 'apa'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${article.title.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success(`Article exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error(`Error exporting article as ${format}:`, error);
      toast.error(`Failed to export article as ${format.toUpperCase()}`);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getReadingTime = (wordCount: number) => {
    return Math.ceil(wordCount / 200); // 200 words per minute
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Saved Articles
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600">Loading saved articles...</p>
              </div>
            </div>
          ) : savedArticles.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Saved Articles</h3>
                <p className="text-gray-600">
                  Generate and save your first research article to see it here.
                </p>
              </div>
            </div>
          ) : (
            <ScrollArea className="h-[60vh]">
              <div className="space-y-4 pr-4">
                {savedArticles.map((article) => (
                  <Card key={article.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            {article.title}
                          </CardTitle>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>{formatDate(article.generatedAt)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <BarChart3 className="w-4 h-4" />
                              <span>{article.wordCount.toLocaleString()} words</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Quote className="w-4 h-4" />
                              <span>{article.citationCount} citations</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-4 h-4" />
                              <span>{getReadingTime(article.wordCount)} min read</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Badge variant="outline" className="text-xs">
                            {article.metadata.researchType.replace('_', ' ')}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {article.metadata.academicLevel}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      {/* Abstract Preview */}
                      {article.abstract && (
                        <div className="mb-4">
                          <p className="text-sm text-gray-700 line-clamp-3 italic">
                            {article.abstract}
                          </p>
                        </div>
                      )}

                      {/* Keywords */}
                      {article.metadata.keywords.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {article.metadata.keywords.slice(0, 5).map((keyword, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {keyword}
                              </Badge>
                            ))}
                            {article.metadata.keywords.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{article.metadata.keywords.length - 5} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={() => onViewArticle(article)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExportArticle(article, 'pdf')}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          PDF
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExportArticle(article, 'docx')}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          DOCX
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleExportArticle(article, 'txt')}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          TXT
                        </Button>
                        
                        <div className="flex-1" />
                        
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteArticle(article.id)}
                          disabled={deletingArticleId === article.id}
                        >
                          {deletingArticleId === article.id ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-600">
            {savedArticles.length > 0 && (
              <span>{savedArticles.length} saved article{savedArticles.length !== 1 ? 's' : ''}</span>
            )}
          </div>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
