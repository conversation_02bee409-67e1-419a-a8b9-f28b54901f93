import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Settings,
  Brain,
  Zap,
  DollarSign,
  Gauge,
  Info,
  Check
} from "lucide-react";

import { AIModel, AIGenerationOptions } from '../types';

interface AIModelSelectorProps {
  selectedModel: string;
  options: AIGenerationOptions;
  onSettingsChange: (settings: { selectedModel: string; options: AIGenerationOptions }) => void;
}

const availableModels: AIModel[] = [
  {
    id: 'google/gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'Google',
    description: 'Fast and efficient model, great for general research analysis',
    strengths: ['Speed', 'Cost-effective', 'Good reasoning'],
    bestFor: ['Document analysis', 'Literature reviews', 'Quick insights'],
    maxTokens: 8192,
    pricing: 'low'
  },
  {
    id: 'google/gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    description: 'Balanced model with strong analytical capabilities',
    strengths: ['Balanced performance', 'Good at complex analysis', 'Reliable'],
    bestFor: ['Research gap analysis', 'Hypothesis generation', 'Methodology analysis'],
    maxTokens: 32768,
    pricing: 'medium'
  },
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for detailed analysis and academic writing',
    strengths: ['Academic writing', 'Critical thinking', 'Detailed analysis'],
    bestFor: ['Literature reviews', 'Research planning', 'Academic writing'],
    maxTokens: 200000,
    pricing: 'high'
  },
  {
    id: 'openai/gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    description: 'Powerful model with strong reasoning and analysis capabilities',
    strengths: ['Strong reasoning', 'Creative insights', 'Comprehensive analysis'],
    bestFor: ['Complex analysis', 'Hypothesis generation', 'Research planning'],
    maxTokens: 128000,
    pricing: 'high'
  },
  {
    id: 'meta-llama/llama-3.1-405b',
    name: 'Llama 3.1 405B',
    provider: 'Meta',
    description: 'Large open-source model with excellent analytical capabilities',
    strengths: ['Open source', 'Large context', 'Strong analysis'],
    bestFor: ['Complex research tasks', 'Large document analysis', 'Detailed insights'],
    maxTokens: 131072,
    pricing: 'medium'
  },
  {
    id: 'mistralai/mistral-large',
    name: 'Mistral Large',
    provider: 'Mistral AI',
    description: 'Efficient European model with strong analytical performance',
    strengths: ['Efficiency', 'Privacy-focused', 'Good reasoning'],
    bestFor: ['Document analysis', 'Research synthesis', 'European compliance'],
    maxTokens: 32768,
    pricing: 'medium'
  }
];

export function AIModelSelector({ selectedModel, options, onSettingsChange }: AIModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempSettings, setTempSettings] = useState({
    selectedModel,
    options
  });

  const currentModel = availableModels.find(m => m.id === selectedModel);

  const handleModelChange = (modelId: string) => {
    const model = availableModels.find(m => m.id === modelId);
    if (model) {
      setTempSettings(prev => ({
        ...prev,
        selectedModel: modelId,
        options: {
          ...prev.options,
          maxTokens: Math.min(prev.options.maxTokens || 4000, model.maxTokens)
        }
      }));
    }
  };

  const handleOptionChange = (key: keyof AIGenerationOptions, value: number) => {
    setTempSettings(prev => ({
      ...prev,
      options: {
        ...prev.options,
        [key]: value
      }
    }));
  };

  const applySettings = () => {
    onSettingsChange(tempSettings);
    setIsOpen(false);
  };

  const resetSettings = () => {
    setTempSettings({
      selectedModel: 'google/gemini-2.5-flash',
      options: {
        temperature: 0.3,
        maxTokens: 4000,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0
      }
    });
  };

  const getPricingColor = (pricing: string) => {
    switch (pricing) {
      case 'free': return 'text-green-600 bg-green-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Brain className="h-4 w-4" />
          {currentModel?.name || 'Select Model'}
          <Settings className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="h-5 w-5 text-blue-500" />
              AI Model Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Model Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Select AI Model</Label>
              <Select value={tempSettings.selectedModel} onValueChange={handleModelChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a model" />
                </SelectTrigger>
                <SelectContent className="max-h-80">
                  {availableModels.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <div className="font-medium">{model.name}</div>
                          <div className="text-xs text-gray-500">{model.provider}</div>
                        </div>
                        <Badge className={`ml-2 ${getPricingColor(model.pricing)}`}>
                          {model.pricing}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Model Info */}
              {currentModel && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div className="text-sm">
                      <p className="text-blue-900 font-medium mb-1">{currentModel.description}</p>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {currentModel.strengths.map((strength, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {strength}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-blue-700 text-xs">
                        Best for: {currentModel.bestFor.join(', ')}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Advanced Options */}
            <div className="space-y-4">
              <Label className="text-sm font-medium">Advanced Options</Label>
              
              {/* Temperature */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Temperature</Label>
                  <span className="text-sm text-gray-500">{tempSettings.options.temperature}</span>
                </div>
                <Slider
                  value={[tempSettings.options.temperature || 0.3]}
                  onValueChange={([value]) => handleOptionChange('temperature', value)}
                  max={1}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Lower values for more focused, higher for more creative responses
                </p>
              </div>

              {/* Max Tokens */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Max Tokens</Label>
                  <span className="text-sm text-gray-500">{tempSettings.options.maxTokens}</span>
                </div>
                <Slider
                  value={[tempSettings.options.maxTokens || 4000]}
                  onValueChange={([value]) => handleOptionChange('maxTokens', value)}
                  max={currentModel?.maxTokens || 8192}
                  min={500}
                  step={100}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Maximum length of generated response
                </p>
              </div>

              {/* Top P */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Top P</Label>
                  <span className="text-sm text-gray-500">{tempSettings.options.topP}</span>
                </div>
                <Slider
                  value={[tempSettings.options.topP || 0.9]}
                  onValueChange={([value]) => handleOptionChange('topP', value)}
                  max={1}
                  min={0.1}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Controls diversity of token selection
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2 pt-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={resetSettings}
                className="flex-1"
              >
                Reset
              </Button>
              <Button
                onClick={applySettings}
                size="sm"
                className="flex-1"
              >
                <Check className="h-4 w-4 mr-2" />
                Apply
              </Button>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
