/**
 * Enhanced Citation System Test
 * Tests the improved citation and reference generation system
 */

import { enhancedCitationSearchService } from './services/enhanced-citation-search.service';
import { realReferenceExtractorService } from './services/real-reference-extractor.service';
import { enhancedPaperAIService } from './services/enhanced-paper-ai.service';
import { tavilySearchService } from '../research-search/services/tavily-search.service';

interface TestCase {
  title: string;
  researchField: string;
  keywords: string[];
  sectionId: string;
  sectionName: string;
  userContent?: string;
}

export class EnhancedCitationTester {
  private testCases: TestCase[] = [
    {
      title: "Machine Learning Applications in Climate Change Prediction",
      researchField: "Computer Science",
      keywords: ["machine learning", "climate change", "prediction", "neural networks"],
      sectionId: "introduction",
      sectionName: "Introduction",
      userContent: "This research focuses on using deep learning algorithms to predict climate patterns and extreme weather events."
    },
    {
      title: "Sustainable Urban Planning Through Smart City Technologies",
      researchField: "Urban Planning",
      keywords: ["smart cities", "sustainability", "urban planning", "IoT"],
      sectionId: "methodology",
      sectionName: "Methodology",
      userContent: "We implemented IoT sensors across the city to collect real-time data on traffic, energy consumption, and air quality."
    },
    {
      title: "Blockchain Technology in Healthcare Data Management",
      researchField: "Healthcare Technology",
      keywords: ["blockchain", "healthcare", "data management", "security"],
      sectionId: "introduction",
      sectionName: "Introduction",
      userContent: "Healthcare data security and interoperability remain major challenges in modern medical systems."
    }
  ];

  /**
   * Run comprehensive tests on the enhanced citation system
   */
  async runTests(): Promise<void> {
    console.log('🧪 Starting Enhanced Citation System Tests...\n');

    for (let i = 0; i < this.testCases.length; i++) {
      const testCase = this.testCases[i];
      console.log(`\n📋 Test Case ${i + 1}: ${testCase.title}`);
      console.log(`Field: ${testCase.researchField}`);
      console.log(`Section: ${testCase.sectionName}`);
      console.log('─'.repeat(80));

      try {
        await this.testSingleCase(testCase);
      } catch (error) {
        console.error(`❌ Test case ${i + 1} failed:`, error);
      }

      console.log('\n' + '═'.repeat(80));
    }

    console.log('\n✅ Enhanced Citation System Tests Completed');
  }

  /**
   * Test a single case
   */
  private async testSingleCase(testCase: TestCase): Promise<void> {
    const context = {
      sectionId: testCase.sectionId,
      sectionName: testCase.sectionName,
      title: testCase.title,
      researchField: testCase.researchField,
      keywords: testCase.keywords,
      userContent: testCase.userContent,
      relatedSections: []
    };

    // Test 1: Search Query Generation
    console.log('\n🔍 Testing Search Query Generation...');
    const searchQueries = (enhancedCitationSearchService as any).generateSearchQueries(context);
    console.log(`Generated ${searchQueries.length} queries:`);
    searchQueries.forEach((query: string, index: number) => {
      console.log(`  ${index + 1}. "${query}"`);
    });

    // Test 2: Tavily Search
    console.log('\n🌐 Testing Tavily Search...');
    const searchResults = await tavilySearchService.searchAcademic(searchQueries[0], {
      maxResults: 5,
      searchDepth: 'advanced'
    });
    console.log(`Found ${searchResults.results.length} search results`);
    
    // Test 3: Reference Extraction
    console.log('\n📚 Testing Reference Extraction...');
    const researchContext = {
      title: testCase.title,
      field: testCase.researchField,
      keywords: testCase.keywords
    };
    
    const extractionResult = await realReferenceExtractorService.extractRealReferences(
      searchResults.results,
      "google/gemini-2.5-flash",
      researchContext
    );

    console.log(`Extraction Results:`);
    console.log(`  ✅ Valid references: ${extractionResult.realReferences.length}`);
    console.log(`  ❌ Rejected sources: ${extractionResult.rejectedSources.length}`);
    console.log(`  📊 Average confidence: ${extractionResult.extractionReport.averageConfidence.toFixed(2)}`);

    // Display valid references
    if (extractionResult.realReferences.length > 0) {
      console.log('\n📖 Valid References Found:');
      extractionResult.realReferences.slice(0, 3).forEach((ref, index) => {
        console.log(`\n  ${index + 1}. ${ref.title.substring(0, 60)}...`);
        console.log(`     Authors: <AUTHORS>
        console.log(`     Year: ${ref.year}`);
        console.log(`     Source: ${ref.source}`);
        console.log(`     Confidence: ${ref.confidence.toFixed(2)}`);
        console.log(`     Topic Relevance: ${ref.topicRelevance?.toFixed(2) || 'N/A'}`);
        console.log(`     URL: ${ref.url.substring(0, 60)}...`);
      });
    }

    // Display rejection reasons
    if (extractionResult.rejectedSources.length > 0) {
      console.log('\n🚫 Rejection Reasons (sample):');
      const rejectionCounts = extractionResult.rejectedSources.reduce((acc, rejected) => {
        acc[rejected.reason] = (acc[rejected.reason] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      Object.entries(rejectionCounts).forEach(([reason, count]) => {
        console.log(`  • ${reason}: ${count} sources`);
      });
    }

    // Test 4: Citation Search Service
    console.log('\n🎯 Testing Enhanced Citation Search...');
    const citationSources = await enhancedCitationSearchService.searchRealCitationsForSection(context, {
      maxSources: 5,
      searchDepth: 'comprehensive'
    });

    console.log(`Citation Search Results:`);
    console.log(`  📚 Found ${citationSources.length} citation sources`);
    
    if (citationSources.length > 0) {
      console.log('\n📝 Citation Sources:');
      citationSources.slice(0, 2).forEach((citation, index) => {
        console.log(`\n  ${index + 1}. In-text: ${citation.inTextCitation}`);
        console.log(`     Title: ${citation.title.substring(0, 50)}...`);
        console.log(`     Authors: <AUTHORS>
        console.log(`     Formatted: ${citation.formattedCitation.substring(0, 100)}...`);
        console.log(`     Relevance: ${citation.relevanceScore.toFixed(2)}`);
      });
    }

    // Test 5: Content Generation (if citations found)
    if (citationSources.length > 0) {
      console.log('\n✍️ Testing Content Generation with Citations...');
      try {
        const generationResult = await enhancedPaperAIService.generateSectionWithRealCitations(
          testCase.sectionId,
          testCase.sectionName,
          context,
          {
            model: "google/gemini-2.5-flash-preview-05-20",
            maxTokens: 1000,
            temperature: 0.7,
            maxCitations: 3,
            useRealCitations: true
          }
        );

        console.log(`Generated Content:`);
        console.log(`  📝 Word count: ${generationResult.wordCount}`);
        console.log(`  📚 Citations used: ${generationResult.citationCount}`);
        console.log(`  📄 Content preview: ${generationResult.content.substring(0, 200)}...`);
        
        // Check for citation integration
        const citationMatches = generationResult.content.match(/\([A-Z][a-z]+(?:\s+(?:&|et\s+al\.?))?,?\s+\d{4}\)/g);
        console.log(`  🔗 In-text citations found: ${citationMatches?.length || 0}`);
        
        if (citationMatches) {
          console.log(`  📋 Citations: ${citationMatches.slice(0, 3).join(', ')}`);
        }

      } catch (error) {
        console.error(`❌ Content generation failed:`, error);
      }
    }

    // Test Summary
    console.log('\n📊 Test Summary:');
    console.log(`  🔍 Search queries: ${searchQueries.length}`);
    console.log(`  🌐 Tavily results: ${searchResults.results.length}`);
    console.log(`  ✅ Valid references: ${extractionResult.realReferences.length}`);
    console.log(`  🎯 Citation sources: ${citationSources.length}`);
    
    const qualityScore = this.calculateQualityScore(extractionResult, citationSources);
    console.log(`  🏆 Quality score: ${qualityScore.toFixed(1)}/10`);
  }

  /**
   * Calculate overall quality score for the test
   */
  private calculateQualityScore(extractionResult: any, citationSources: any[]): number {
    let score = 0;
    
    // Reference extraction quality (40% of score)
    const extractionQuality = extractionResult.realReferences.length > 0 ? 
      Math.min(extractionResult.realReferences.length / 3, 1) * 4 : 0;
    score += extractionQuality;
    
    // Average confidence (30% of score)
    const confidenceScore = extractionResult.extractionReport.averageConfidence * 3;
    score += confidenceScore;
    
    // Citation source quality (30% of score)
    const citationQuality = citationSources.length > 0 ? 
      Math.min(citationSources.length / 3, 1) * 3 : 0;
    score += citationQuality;
    
    return score;
  }
}

// Export test runner function
export async function runEnhancedCitationTests(): Promise<void> {
  const tester = new EnhancedCitationTester();
  await tester.runTests();
}

// Add to window for manual testing
if (typeof window !== 'undefined') {
  (window as any).testEnhancedCitations = runEnhancedCitationTests;
}
