/**
 * Code Display Panel
 * Enhanced code display with syntax highlighting, explanations, and interactive features
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Code, 
  Copy, 
  Download, 
  Play, 
  Pause, 
  BookOpen, 
  Lightbulb,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff
} from "lucide-react";
import { toast } from 'sonner';

interface CodeDisplayPanelProps {
  code: string;
  language: string;
  title: string;
  description: string;
  educationLevel: string;
  onExecute?: () => void;
  onExplain?: (section: string) => void;
  className?: string;
}

interface CodeSection {
  id: string;
  title: string;
  startLine: number;
  endLine: number;
  explanation: string;
  concepts: string[];
}

export const CodeDisplayPanel: React.FC<CodeDisplayPanelProps> = ({
  code,
  language,
  title,
  description,
  educationLevel,
  onExecute,
  onExplain,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('code');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [highlightedLines, setHighlightedLines] = useState<Set<number>>(new Set());
  const [codeSections, setCodeSections] = useState<CodeSection[]>([]);
  const [showLineNumbers, setShowLineNumbers] = useState(true);

  // Parse code into sections for explanation
  useEffect(() => {
    const sections = parseCodeSections(code, language);
    setCodeSections(sections);
  }, [code, language]);

  const parseCodeSections = (code: string, language: string): CodeSection[] => {
    const lines = code.split('\n');
    const sections: CodeSection[] = [];

    if (language === 'javascript' || language === 'typescript') {
      // Parse React component sections
      let currentSection: Partial<CodeSection> | null = null;
      
      lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        
        // Detect function declarations
        if (trimmedLine.includes('function') || trimmedLine.includes('const') && trimmedLine.includes('=>')) {
          if (currentSection) {
            currentSection.endLine = index - 1;
            sections.push(currentSection as CodeSection);
          }
          
          currentSection = {
            id: `section-${sections.length}`,
            title: extractFunctionName(trimmedLine) || `Code Section ${sections.length + 1}`,
            startLine: index,
            explanation: generateExplanation(trimmedLine, educationLevel),
            concepts: extractConcepts(trimmedLine)
          };
        }
        
        // Detect hooks and important patterns
        if (trimmedLine.includes('useState') || trimmedLine.includes('useEffect')) {
          if (!currentSection) {
            currentSection = {
              id: `section-${sections.length}`,
              title: 'React Hooks',
              startLine: index,
              explanation: generateHookExplanation(trimmedLine, educationLevel),
              concepts: ['React Hooks', 'State Management']
            };
          }
        }
      });
      
      // Close the last section
      if (currentSection) {
        currentSection.endLine = lines.length - 1;
        sections.push(currentSection as CodeSection);
      }
    }

    return sections;
  };

  const extractFunctionName = (line: string): string | null => {
    const functionMatch = line.match(/function\s+(\w+)/);
    if (functionMatch) return functionMatch[1];
    
    const arrowMatch = line.match(/const\s+(\w+)\s*=/);
    if (arrowMatch) return arrowMatch[1];
    
    return null;
  };

  const generateExplanation = (line: string, level: string): string => {
    if (line.includes('useState')) {
      return level === 'elementary' 
        ? 'This creates a variable that can change and update the display'
        : 'useState is a React Hook that lets you add state to functional components';
    }
    
    if (line.includes('useEffect')) {
      return level === 'elementary'
        ? 'This runs code when something changes on the page'
        : 'useEffect lets you perform side effects in functional components';
    }
    
    if (line.includes('function') || line.includes('=>')) {
      return level === 'elementary'
        ? 'This is a set of instructions that the computer follows'
        : 'This defines a function that encapsulates reusable logic';
    }
    
    return 'This section contains important code logic';
  };

  const generateHookExplanation = (line: string, level: string): string => {
    if (line.includes('useState')) {
      return level === 'elementary'
        ? 'useState helps us remember and change information'
        : 'useState manages component state and triggers re-renders when state changes';
    }
    
    if (line.includes('useEffect')) {
      return level === 'elementary'
        ? 'useEffect runs code when the page loads or changes'
        : 'useEffect handles side effects like API calls, subscriptions, or DOM manipulation';
    }
    
    return 'React Hooks provide powerful functionality to components';
  };

  const extractConcepts = (line: string): string[] => {
    const concepts: string[] = [];
    
    if (line.includes('useState')) concepts.push('State Management');
    if (line.includes('useEffect')) concepts.push('Side Effects');
    if (line.includes('motion.')) concepts.push('Animation');
    if (line.includes('className')) concepts.push('Styling');
    if (line.includes('onClick')) concepts.push('Event Handling');
    
    return concepts;
  };

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const highlightSection = (section: CodeSection) => {
    const lines = new Set<number>();
    for (let i = section.startLine; i <= section.endLine; i++) {
      lines.add(i);
    }
    setHighlightedLines(lines);
    
    // Auto-scroll to section (simplified)
    setTimeout(() => setHighlightedLines(new Set()), 3000);
  };

  const copyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('Code copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  const downloadCode = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/\s+/g, '_')}.${language === 'typescript' ? 'tsx' : 'js'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderCodeWithHighlighting = () => {
    const lines = code.split('\n');
    
    return (
      <div className="relative">
        <pre className="text-sm overflow-x-auto">
          <code className="block">
            {lines.map((line, index) => (
              <div
                key={index}
                className={`flex ${highlightedLines.has(index) ? 'bg-yellow-100' : ''}`}
              >
                {showLineNumbers && (
                  <span className="text-gray-400 text-xs w-8 flex-shrink-0 text-right mr-4 select-none">
                    {index + 1}
                  </span>
                )}
                <span className="flex-1">
                  {highlightSyntax(line, language)}
                </span>
              </div>
            ))}
          </code>
        </pre>
      </div>
    );
  };

  const highlightSyntax = (line: string, language: string) => {
    // Basic syntax highlighting (in a real implementation, use a library like Prism.js)
    if (language === 'javascript' || language === 'typescript') {
      return (
        <span
          dangerouslySetInnerHTML={{
            __html: line
              .replace(/\b(const|let|var|function|return|if|else|for|while|import|export|default)\b/g, '<span class="text-purple-600 font-semibold">$1</span>')
              .replace(/\b(useState|useEffect|React)\b/g, '<span class="text-blue-600 font-semibold">$1</span>')
              .replace(/"([^"]*)"/g, '<span class="text-green-600">"$1"</span>')
              .replace(/'([^']*)'/g, '<span class="text-green-600">\'$1\'</span>')
              .replace(/\/\/.*$/g, '<span class="text-gray-500 italic">$&</span>')
              .replace(/\/\*[\s\S]*?\*\//g, '<span class="text-gray-500 italic">$&</span>')
          }}
        />
      );
    }
    
    return <span>{line}</span>;
  };

  return (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Code className="w-5 h-5 text-blue-500" />
              <span>{title}</span>
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{language}</Badge>
            <Badge variant="outline">{educationLevel}</Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="px-6 pb-0">
            <div className="flex items-center justify-between">
              <TabsList className="grid w-fit grid-cols-3">
                <TabsTrigger value="code">Code</TabsTrigger>
                <TabsTrigger value="explanation">Explanation</TabsTrigger>
                <TabsTrigger value="concepts">Concepts</TabsTrigger>
              </TabsList>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLineNumbers(!showLineNumbers)}
                >
                  {showLineNumbers ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
                <Button variant="ghost" size="sm" onClick={copyCode}>
                  <Copy className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={downloadCode}>
                  <Download className="w-4 h-4" />
                </Button>
                {onExecute && (
                  <Button variant="ghost" size="sm" onClick={onExecute}>
                    <Play className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          <TabsContent value="code" className="px-6 pb-6 mt-4">
            <div className="bg-gray-900 rounded-lg p-4 overflow-auto max-h-96">
              <div className="text-gray-100">
                {renderCodeWithHighlighting()}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="explanation" className="px-6 pb-6 mt-4">
            <div className="space-y-4">
              {codeSections.length > 0 ? (
                codeSections.map((section) => (
                  <Card key={section.id} className="border">
                    <CardHeader 
                      className="pb-2 cursor-pointer"
                      onClick={() => toggleSection(section.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {expandedSections.has(section.id) ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                          <h4 className="font-medium">{section.title}</h4>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            highlightSection(section);
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    
                    <AnimatePresence>
                      {expandedSections.has(section.id) && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                        >
                          <CardContent className="pt-0">
                            <p className="text-sm text-gray-700 mb-3">
                              {section.explanation}
                            </p>
                            <div className="flex flex-wrap gap-2">
                              {section.concepts.map((concept, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {concept}
                                </Badge>
                              ))}
                            </div>
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                ))
              ) : (
                <Alert>
                  <Lightbulb className="h-4 w-4" />
                  <AlertDescription>
                    Code analysis is being processed. Explanations will appear here once ready.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="concepts" className="px-6 pb-6 mt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Key concepts extracted from code */}
                <Card className="p-4">
                  <h4 className="font-medium mb-2 flex items-center">
                    <BookOpen className="w-4 h-4 mr-2 text-blue-500" />
                    Programming Concepts
                  </h4>
                  <div className="space-y-2 text-sm">
                    {code.includes('useState') && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">State</Badge>
                        <span className="text-gray-600">Managing changing data</span>
                      </div>
                    )}
                    {code.includes('useEffect') && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">Effects</Badge>
                        <span className="text-gray-600">Running code at specific times</span>
                      </div>
                    )}
                    {code.includes('motion.') && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">Animation</Badge>
                        <span className="text-gray-600">Making things move smoothly</span>
                      </div>
                    )}
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2 flex items-center">
                    <Lightbulb className="w-4 h-4 mr-2 text-yellow-500" />
                    Learning Tips
                  </h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p>• Try modifying the values to see what changes</p>
                    <p>• Look for patterns in how the code is structured</p>
                    <p>• Each function has a specific purpose</p>
                    <p>• Comments explain what the code does</p>
                  </div>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CodeDisplayPanel;
