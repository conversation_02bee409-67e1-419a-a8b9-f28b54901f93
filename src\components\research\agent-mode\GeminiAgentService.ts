/**
 * Google Gemini 2.5 Pro Agent Service for Targeted Document Editing
 * Provides context-aware, targeted editing capabilities using Google Gemini API
 */

export interface DocumentSection {
  id: string;
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'other';
  content: string;
  htmlContent: string;
  startIndex: number;
  endIndex: number;
  level?: number; // For headings
}

export interface EditInstruction {
  sectionId: string;
  originalContent: string;
  editedContent: string;
  changeType: 'modify' | 'add' | 'remove';
  reasoning: string;
}

export interface TargetedEditResult {
  success: boolean;
  edits: EditInstruction[];
  summary: string;
  reasoning: string;
  confidence: number;
  sectionsAnalyzed: number;
  sectionsModified: number;
  error?: string;
  warnings?: string[];
}

export class GeminiAgentService {
  private apiKey: string;
  private model = 'gemini-2.5-flash';
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    console.log('🤖 GeminiAgentService initialized with model:', this.model);
    console.log('🔑 API Key available:', this.hasValidApiKey());
    console.log('🔑 API Key length:', this.apiKey.length);
  }

  /**
   * Check if API key is configured
   */
  hasValidApiKey(): boolean {
    return !!this.apiKey && this.apiKey.length > 20 && !this.apiKey.includes('your_');
  }

  /**
   * Test the API connection with a simple request
   */
  async testApiConnection(): Promise<boolean> {
    try {
      console.log('🧪 Testing Gemini API connection...');
      const response = await this.callGeminiAPI('Say "Hello" in one word.');
      console.log('✅ API test successful:', response);
      return true;
    } catch (error) {
      console.error('❌ API test failed:', error);
      return false;
    }
  }

  /**
   * Parse document content into logical sections
   */
  analyzeDocumentStructure(htmlContent: string): DocumentSection[] {
    const sections: DocumentSection[] = [];
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    let sectionId = 0;

    // Find all significant elements, including divs that might contain content
    const elements = doc.body.querySelectorAll('h1, h2, h3, h4, h5, h6, p, ul, ol, table, blockquote, div');

    elements.forEach((element) => {
      const textContent = element.textContent?.trim() || '';

      // More lenient content filtering - include shorter content too
      if (textContent.length < 5) return; // Skip very short content

      // Skip elements that are just containers for other elements
      if (element.tagName === 'DIV' && element.children.length > 0) {
        const hasSignificantText = textContent.length > 20;
        const hasOnlyInlineChildren = Array.from(element.children).every(child =>
          ['SPAN', 'A', 'STRONG', 'EM', 'B', 'I'].includes(child.tagName)
        );

        if (!hasSignificantText || !hasOnlyInlineChildren) {
          return; // Skip this div as it's likely a container
        }
      }

      let type: DocumentSection['type'] = 'other';
      let level: number | undefined;

      if (element.tagName.match(/^H[1-6]$/)) {
        type = 'heading';
        level = parseInt(element.tagName.charAt(1));
      } else if (element.tagName === 'P' || (element.tagName === 'DIV' && textContent.length > 20)) {
        type = 'paragraph';
      } else if (element.tagName === 'UL' || element.tagName === 'OL') {
        type = 'list';
      } else if (element.tagName === 'TABLE') {
        type = 'table';
      }

      sections.push({
        id: `section_${sectionId++}`,
        type,
        content: textContent,
        htmlContent: element.outerHTML,
        startIndex: 0, // Will be calculated if needed
        endIndex: 0,   // Will be calculated if needed
        level
      });
    });

    console.log(`📊 Analyzed document structure: ${sections.length} sections found`);
    console.log('📋 Section types:', sections.map(s => `${s.type}(${s.content.length})`).join(', '));

    return sections;
  }

  /**
   * Generate targeted edits using Google Gemini 2.5 Pro
   */
  async executeTargetedEdit(
    userRequest: string,
    documentContent: string,
    options: {
      editMode?: 'conservative' | 'moderate' | 'aggressive';
      maxSections?: number;
      preserveFormatting?: boolean;
    } = {}
  ): Promise<TargetedEditResult> {
    if (!this.hasValidApiKey()) {
      throw new Error('Google Gemini API key not configured. Please set VITE_GEMINI_API_KEY in your environment.');
    }

    try {
      console.log('🎯 Starting targeted edit with Gemini 2.5 Pro:', {
        userRequest,
        documentLength: documentContent.length,
        options
      });

      // Step 1: Analyze document structure
      const sections = this.analyzeDocumentStructure(documentContent);
      
      // Step 2: Identify relevant sections for the user's request
      const relevantSections = await this.identifyRelevantSections(userRequest, sections);
      
      // Step 3: Generate targeted edits for relevant sections
      const edits = await this.generateEditsForSections(userRequest, relevantSections, options);
      
      // Step 4: Validate and prepare result
      const result: TargetedEditResult = {
        success: true,
        edits,
        summary: `Applied ${edits.length} targeted edit(s) based on: "${userRequest}"`,
        reasoning: `Analyzed ${sections.length} sections, identified ${relevantSections.length} relevant sections, and made ${edits.length} targeted improvements.`,
        confidence: this.calculateConfidence(edits),
        sectionsAnalyzed: sections.length,
        sectionsModified: edits.length,
        warnings: edits.length === 0 ? ['No changes were needed or could be made based on your request.'] : undefined
      };

      console.log('✅ Targeted edit completed:', result);
      return result;

    } catch (error: any) {
      console.error('❌ Targeted edit failed:', error);
      return {
        success: false,
        edits: [],
        summary: 'Failed to complete targeted editing',
        reasoning: error.message || 'Unknown error occurred',
        confidence: 0,
        sectionsAnalyzed: 0,
        sectionsModified: 0,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  /**
   * Identify sections relevant to the user's request
   */
  private async identifyRelevantSections(
    userRequest: string,
    sections: DocumentSection[]
  ): Promise<DocumentSection[]> {
    console.log('🔍 Identifying relevant sections for request:', userRequest);
    console.log('📊 Available sections:', sections.map(s => `${s.type}: ${s.content.substring(0, 50)}...`));

    // For now, let's use a more reliable heuristic approach
    // The AI-based approach was too unreliable
    const relevantSections = this.identifyRelevantSectionsHeuristic(userRequest, sections);

    console.log(`✅ Selected ${relevantSections.length} relevant sections:`,
      relevantSections.map(s => `${s.type}: ${s.content.substring(0, 30)}...`)
    );

    return relevantSections;
  }

  /**
   * Generate specific edits for relevant sections
   */
  private async generateEditsForSections(
    userRequest: string,
    sections: DocumentSection[],
    options: any
  ): Promise<EditInstruction[]> {
    const edits: EditInstruction[] = [];
    const maxSections = Math.min(sections.length, options.maxSections || 5);

    console.log(`🔧 Generating edits for ${maxSections} sections out of ${sections.length} total`);

    for (let i = 0; i < maxSections; i++) {
      const section = sections[i];
      try {
        console.log(`📝 Processing section ${i + 1}/${maxSections}: ${section.type} (${section.content.length} chars)`);

        const editPrompt = this.createEditPrompt(userRequest, section, options);
        console.log(`🚀 Sending prompt to Gemini for section ${section.id}`);

        const response = await this.callGeminiAPI(editPrompt);
        console.log(`📥 Received response for section ${section.id}, length: ${response.length}`);

        const edit = this.parseEditResponse(response, section);
        if (edit) {
          console.log(`✅ Created edit for section ${section.id}`);
          edits.push(edit);
        } else {
          console.log(`❌ No meaningful edit created for section ${section.id}`);
        }

        // Add delay to respect rate limits
        if (i < maxSections - 1) {
          console.log('⏳ Waiting 1 second before next request...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error: any) {
        console.error(`❌ Failed to edit section ${section.id}:`, error);
        // Continue with other sections even if one fails
      }
    }

    console.log(`🎯 Generated ${edits.length} edits out of ${maxSections} sections processed`);
    return edits;
  }

  /**
   * Create targeted edit prompt for a specific section
   */
  private createEditPrompt(
    userRequest: string,
    section: DocumentSection,
    options: any
  ): string {
    const editModeInstructions = {
      conservative: 'Make minimal, careful improvements while preserving the original meaning and style.',
      moderate: 'Make meaningful improvements while maintaining the core structure and intent.',
      aggressive: 'Make substantial improvements, restructuring as needed for better clarity and impact.'
    };

    // Create more specific prompts based on common requests
    let specificInstructions = '';
    const request = userRequest.toLowerCase();

    if (request.includes('remove') && (request.includes('reference') || request.includes('citation'))) {
      specificInstructions = `
SPECIFIC TASK: Remove all references and citations from this section.
- Remove in-text citations like (Author, Year), (Smith et al., 2020), [1], [2], etc.
- Remove reference lists or bibliography entries completely
- Remove any mention of "References", "Bibliography", "Works Cited" headings
- Remove numbered reference lists like "1. Author, A. (2020)..."
- Remove any text that looks like academic citations
- Keep all other content intact and preserve the meaning
- Do not add any new content or explanations
- If the entire section is just references, return an empty string or a simple placeholder`;
    } else if (request.includes('grammar') || request.includes('spelling')) {
      specificInstructions = `
SPECIFIC TASK: Fix grammar, spelling, and punctuation errors.
- Correct any grammatical mistakes
- Fix spelling errors
- Improve punctuation
- Maintain the original meaning and structure`;
    } else if (request.includes('clarity') || request.includes('clear')) {
      specificInstructions = `
SPECIFIC TASK: Improve clarity and readability.
- Simplify complex sentences
- Improve word choice
- Enhance flow and transitions
- Make the text easier to understand`;
    } else {
      specificInstructions = `
SPECIFIC TASK: ${userRequest}
- Focus specifically on what the user requested
- Make only the changes needed to fulfill the request
- Preserve everything else exactly as it is`;
    }

    // Truncate content if it's too long to avoid MAX_TOKENS
    let contentToEdit = section.content;
    const maxContentLength = 2000; // Conservative limit

    if (contentToEdit.length > maxContentLength) {
      console.warn(`⚠️ Content too long (${contentToEdit.length} chars), truncating to ${maxContentLength} chars`);
      contentToEdit = contentToEdit.substring(0, maxContentLength) + '...';
    }

    // Create a simpler, more direct prompt to avoid safety filter issues
    const simplePrompt = `Edit this text based on the request: "${userRequest}"

Original text:
${contentToEdit}

${specificInstructions}

Return only the edited text with no explanations:`;

    console.log('📝 Prompt length:', simplePrompt.length);
    return simplePrompt;
  }

  /**
   * Call Google Gemini API
   */
  private async callGeminiAPI(prompt: string): Promise<string> {
    const url = `${this.baseUrl}/${this.model}:generateContent?key=${this.apiKey}`;

    console.log('🚀 Calling Gemini API with prompt length:', prompt.length);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 32,
            topP: 0.9,
            maxOutputTokens: 4096,
            candidateCount: 1
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_ONLY_HIGH"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_ONLY_HIGH"
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Gemini API error:', response.status, errorText);
        throw new Error(`Gemini API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('📥 Gemini API response:', data);

      // Check for safety filter blocks
      if (data.candidates?.[0]?.finishReason === 'SAFETY') {
        console.error('❌ Content blocked by safety filters:', data.candidates[0]);
        throw new Error('Content was blocked by Gemini safety filters. Try rephrasing your request.');
      }

      // Check for other finish reasons
      if (data.candidates?.[0]?.finishReason && data.candidates[0].finishReason !== 'STOP') {
        const finishReason = data.candidates[0].finishReason;
        console.error('❌ Unexpected finish reason:', finishReason);

        if (finishReason === 'MAX_TOKENS') {
          throw new Error('Content is too long for Gemini API. Try with shorter sections or use OpenRouter models.');
        } else {
          throw new Error(`Gemini API finished with reason: ${finishReason}`);
        }
      }

      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        console.error('❌ No content in Gemini response:', data);
        console.error('Full response structure:', JSON.stringify(data, null, 2));

        // Check if there are any candidates at all
        if (!data.candidates || data.candidates.length === 0) {
          throw new Error('No candidates returned from Gemini API. The request may have been blocked.');
        }

        throw new Error('No content returned from Gemini API. Check the console for full response details.');
      }

      console.log('✅ Gemini API returned content length:', content.length);
      return content.trim();

    } catch (error: any) {
      console.error('❌ Gemini API call failed:', error);
      throw error;
    }
  }

  /**
   * Parse relevant sections from AI response
   */
  private parseRelevantSections(response: string): number[] {
    if (response.trim().toUpperCase() === 'NONE') {
      return [];
    }
    
    const matches = response.match(/\d+/g);
    if (!matches) return [];
    
    return matches.map(num => parseInt(num) - 1).filter(num => num >= 0);
  }

  /**
   * Parse edit response from AI
   */
  private parseEditResponse(response: string, originalSection: DocumentSection): EditInstruction | null {
    const editedContent = response.trim();

    console.log('🔍 Parsing edit response for section:', originalSection.id);
    console.log('📝 Original content length:', originalSection.content.length);
    console.log('📝 Edited content length:', editedContent.length);
    console.log('📝 Original (first 100 chars):', originalSection.content.substring(0, 100));
    console.log('📝 Edited (first 100 chars):', editedContent.substring(0, 100));

    // More lenient comparison - check if there are meaningful differences
    if (!editedContent) {
      console.log('❌ No edited content returned');
      return null;
    }

    // Normalize whitespace for comparison
    const normalizedOriginal = originalSection.content.replace(/\s+/g, ' ').trim();
    const normalizedEdited = editedContent.replace(/\s+/g, ' ').trim();

    if (normalizedEdited === normalizedOriginal) {
      console.log('❌ No meaningful changes detected');
      return null;
    }

    // Check if the change is substantial enough (at least 5% difference or 10 characters)
    const lengthDiff = Math.abs(editedContent.length - originalSection.content.length);
    const percentDiff = lengthDiff / originalSection.content.length;

    if (lengthDiff < 10 && percentDiff < 0.05) {
      // For small changes, check if there are actual word differences
      const originalWords = normalizedOriginal.split(' ');
      const editedWords = normalizedEdited.split(' ');
      const wordDifferences = originalWords.filter((word, index) => editedWords[index] !== word).length;

      if (wordDifferences < 2) {
        console.log('❌ Changes too minimal to be meaningful');
        return null;
      }
    }

    console.log('✅ Meaningful changes detected, creating edit instruction');

    return {
      sectionId: originalSection.id,
      originalContent: originalSection.content,
      editedContent,
      changeType: 'modify',
      reasoning: 'Content improved based on user request'
    };
  }

  /**
   * Fallback heuristic approach for identifying relevant sections
   */
  private identifyRelevantSectionsHeuristic(
    userRequest: string,
    sections: DocumentSection[]
  ): DocumentSection[] {
    const request = userRequest.toLowerCase();
    console.log('🎯 Using heuristic approach for request:', request);

    // Keywords that might indicate specific sections
    const sectionKeywords = {
      introduction: ['introduction', 'intro', 'background'],
      methodology: ['methodology', 'method', 'approach', 'procedure'],
      results: ['results', 'findings', 'data', 'analysis'],
      discussion: ['discussion', 'interpretation', 'implications'],
      conclusion: ['conclusion', 'summary', 'final']
    };

    // Content-based keywords with more comprehensive matching
    const contentKeywords = {
      citations: ['citation', 'reference', 'source', 'bibliography', 'cite', 'ref', 'author', 'year', 'et al'],
      grammar: ['grammar', 'spelling', 'language', 'writing', 'punctuation', 'syntax'],
      clarity: ['clarity', 'clear', 'readable', 'understand', 'confusing', 'unclear'],
      style: ['style', 'tone', 'academic', 'formal', 'improve', 'enhance']
    };

    let relevantSections: DocumentSection[] = [];

    // Check for specific section mentions first
    for (const [sectionType, keywords] of Object.entries(sectionKeywords)) {
      if (keywords.some(keyword => request.includes(keyword))) {
        const matchingSections = sections.filter(section =>
          section.content.toLowerCase().includes(sectionType) ||
          (section.type === 'heading' && keywords.some(keyword =>
            section.content.toLowerCase().includes(keyword)
          ))
        );
        if (matchingSections.length > 0) {
          console.log(`📍 Found ${matchingSections.length} sections matching "${sectionType}"`);
          return matchingSections;
        }
      }
    }

    // Special handling for reference/citation removal
    if (request.includes('remove') && (request.includes('reference') || request.includes('citation'))) {
      console.log('🎯 Looking for sections with references/citations');
      relevantSections = sections.filter(section => {
        const content = section.content.toLowerCase();
        return content.includes('reference') ||
               content.includes('citation') ||
               content.includes('bibliography') ||
               content.includes('et al') ||
               content.includes('(') && content.includes(')') || // Likely citations
               /\[\d+\]/.test(content) || // Numbered references
               /\(\w+,?\s*\d{4}\)/.test(content); // Author-year citations
      });

      if (relevantSections.length > 0) {
        console.log(`📍 Found ${relevantSections.length} sections with references/citations`);
        return relevantSections;
      }
    }

    // For content-based improvements, find relevant sections
    for (const [category, keywords] of Object.entries(contentKeywords)) {
      if (keywords.some(keyword => request.includes(keyword))) {
        if (category === 'citations') {
          // Already handled above
          continue;
        }

        relevantSections = sections.filter(section =>
          section.type === 'paragraph' && section.content.length > 50
        ).slice(0, 5); // Get more sections for general improvements

        if (relevantSections.length > 0) {
          console.log(`📍 Found ${relevantSections.length} sections for ${category} improvements`);
          return relevantSections;
        }
      }
    }

    // Default: return all substantial text sections
    relevantSections = sections.filter(section =>
      (section.type === 'paragraph' || section.type === 'heading') &&
      section.content.length > 30
    ).slice(0, 8); // Increased to catch more content

    console.log(`📍 Using default selection: ${relevantSections.length} sections`);
    return relevantSections;
  }

  /**
   * Calculate confidence score based on edits
   */
  private calculateConfidence(edits: EditInstruction[]): number {
    if (edits.length === 0) return 0.5;
    
    // Base confidence on number of successful edits
    const baseConfidence = Math.min(0.9, 0.6 + (edits.length * 0.1));
    
    // Adjust based on edit quality (length difference as proxy)
    const avgLengthDiff = edits.reduce((sum, edit) => {
      const diff = Math.abs(edit.editedContent.length - edit.originalContent.length);
      return sum + (diff / edit.originalContent.length);
    }, 0) / edits.length;
    
    const qualityAdjustment = Math.min(0.2, avgLengthDiff * 0.5);
    
    return Math.min(0.95, baseConfidence + qualityAdjustment);
  }
}

export const geminiAgentService = new GeminiAgentService();
