/**
 * Universal Introduction Generator Service
 * Generates comprehensive introductions for ANY research topic with source tracking
 */

import { tavilySearchService } from '../../research-search/services/tavily-search.service';
import { realReferenceExtractorService } from './real-reference-extractor.service';
import { paperAIService } from './paper-ai.service';
import { citationEnforcerService } from './citation-enforcer.service';

export interface ResearchContext {
  title: string;
  researchField: string;
  keywords: string[];
  methodology?: string;
  results?: string;
  existingIntroduction?: string;
  userSections?: any[];
}

export interface IntroductionOutline {
  sections: OutlineSection[];
  researchGaps: string[];
  keyThemes: string[];
  searchKeywords: string[];
}

export interface OutlineSection {
  id: string;
  title: string;
  description: string;
  requiredSources: number;
  searchTerms: string[];
}

export interface SourcedContent {
  content: string;
  sources: ContentSource[];
  wordCount: number;
  citationCount: number;
}

export interface ContentSource {
  id: string;
  title: string;
  authors: string[];
  year: number;
  url: string;
  relevantText: string;
  usedInSentences: string[];
  confidence: number;
}

export interface IntroductionResult {
  outline: IntroductionOutline;
  sourcedContent: SourcedContent;
  bibliography: ContentSource[];
  qualityMetrics: QualityMetrics;
  searchLog: SearchStep[];
}

export interface QualityMetrics {
  totalSources: number;
  academicSources: number;
  averageRelevance: number;
  coverageScore: number;
  citationDensity: number;
}

export interface SearchStep {
  step: number;
  query: string;
  resultsFound: number;
  relevantSources: number;
  timestamp: Date;
}

export class UniversalIntroductionGeneratorService {
  private searchLog: SearchStep[] = [];
  private minRequiredSources = 15;
  private maxSearchAttempts = 5;

  /**
   * Generate comprehensive introduction for any research topic
   */
  async generateUniversalIntroduction(
    context: ResearchContext,
    options: {
      model?: string;
      minSources?: number;
      maxLength?: number;
      includeSourceTracking?: boolean;
    } = {}
  ): Promise<IntroductionResult> {
    const {
      model = "google/gemini-2.5-flash-preview-05-20",
      minSources = 15,
      maxLength = 1500,
      includeSourceTracking = true
    } = options;

    this.minRequiredSources = minSources;
    this.searchLog = [];

    console.log(`🚀 Starting universal introduction generation for: "${context.title}"`);
    console.log(`📊 Target: ${minSources} sources, ${maxLength} words, Field: ${context.researchField}`);

    try {
      // Step 1: Analyze user inputs and create outline
      console.log('\n📋 Step 1: Analyzing inputs and creating outline...');
      const outline = await this.createIntroductionOutline(context, model);
      console.log(`✅ Created outline with ${outline.sections.length} sections`);

      // Step 2: Search for sources based on outline
      console.log('\n🔍 Step 2: Searching for academic sources...');
      const bibliography = await this.searchComprehensiveSources(context, outline);
      console.log(`✅ Found ${bibliography.length} relevant sources`);

      // Step 3: Generate introduction with source tracking
      console.log('\n✍️ Step 3: Writing introduction with source tracking...');
      const sourcedContent = await this.writeSourcedIntroduction(
        context, 
        outline, 
        bibliography, 
        model, 
        maxLength,
        includeSourceTracking
      );
      console.log(`✅ Generated ${sourcedContent.wordCount} words with ${sourcedContent.citationCount} citations`);

      // Step 4: Calculate quality metrics
      const qualityMetrics = this.calculateQualityMetrics(bibliography, sourcedContent);

      const result: IntroductionResult = {
        outline,
        sourcedContent,
        bibliography,
        qualityMetrics,
        searchLog: this.searchLog
      };

      console.log('\n📊 Introduction Generation Summary:');
      console.log(`- Sources Found: ${qualityMetrics.totalSources}`);
      console.log(`- Academic Sources: ${qualityMetrics.academicSources}`);
      console.log(`- Average Relevance: ${qualityMetrics.averageRelevance.toFixed(2)}`);
      console.log(`- Citation Density: ${qualityMetrics.citationDensity.toFixed(2)} citations/100 words`);

      return result;

    } catch (error) {
      console.error('❌ Universal introduction generation failed:', error);
      throw error;
    }
  }

  /**
   * Create introduction outline based on user inputs
   */
  private async createIntroductionOutline(
    context: ResearchContext,
    model: string
  ): Promise<IntroductionOutline> {
    const { title, researchField, methodology, results, existingIntroduction } = context;

    const prompt = `You are an expert academic writer. Analyze the following research context and create a comprehensive introduction outline.

RESEARCH CONTEXT:
Title: ${title}
Field: ${researchField}
${methodology ? `Methodology: ${methodology.substring(0, 500)}...` : ''}
${results ? `Results: ${results.substring(0, 500)}...` : ''}
${existingIntroduction ? `Existing Introduction: ${existingIntroduction.substring(0, 300)}...` : ''}

TASK: Create a detailed introduction outline that covers:
1. Background and context of the research area
2. Current state of knowledge and recent developments
3. Research gaps and limitations in existing work
4. Research objectives and significance
5. Brief overview of approach and contributions

For each section, specify:
- What topics to cover
- How many sources are needed (aim for 15+ total)
- Specific search terms for finding relevant literature

Return ONLY valid JSON in this format:
{
  "sections": [
    {
      "id": "background",
      "title": "Background and Context",
      "description": "Overview of the research domain and fundamental concepts",
      "requiredSources": 4,
      "searchTerms": ["term1", "term2", "term3"]
    }
  ],
  "researchGaps": ["gap1", "gap2"],
  "keyThemes": ["theme1", "theme2"],
  "searchKeywords": ["keyword1", "keyword2"]
}`;

    try {
      const response = await paperAIService.generatePaperSection(prompt, {
        model,
        maxTokens: 1024,
        temperature: 0.3
      });

      // Clean and parse response
      let cleanedResponse = response.trim();
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      }

      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanedResponse = jsonMatch[0];
      }

      const outline = JSON.parse(cleanedResponse);
      
      // Validate and enhance outline
      if (!outline.sections || outline.sections.length === 0) {
        throw new Error('Invalid outline structure');
      }

      // Ensure we have enough sources planned
      const totalSources = outline.sections.reduce((sum: number, section: any) => sum + (section.requiredSources || 3), 0);
      if (totalSources < this.minRequiredSources) {
        // Distribute additional sources across sections
        const additionalSources = this.minRequiredSources - totalSources;
        const sourcesPerSection = Math.ceil(additionalSources / outline.sections.length);
        outline.sections.forEach((section: any) => {
          section.requiredSources = (section.requiredSources || 3) + sourcesPerSection;
        });
      }

      return outline;

    } catch (error) {
      console.error('Failed to create outline, using fallback:', error);
      
      // Fallback outline for any research topic
      return this.createFallbackOutline(context);
    }
  }

  /**
   * Create fallback outline for any research topic
   */
  private createFallbackOutline(context: ResearchContext): IntroductionOutline {
    const { title, researchField, keywords } = context;

    return {
      sections: [
        {
          id: "background",
          title: "Background and Context",
          description: `Overview of ${researchField} and fundamental concepts related to the research`,
          requiredSources: 5,
          searchTerms: [researchField, ...keywords.slice(0, 2)]
        },
        {
          id: "current_state",
          title: "Current State of Knowledge",
          description: "Recent developments and current understanding in the field",
          requiredSources: 4,
          searchTerms: [...keywords, "recent developments", "current research"]
        },
        {
          id: "research_gaps",
          title: "Research Gaps and Limitations",
          description: "Identification of gaps in existing research and limitations",
          requiredSources: 3,
          searchTerms: [...keywords, "limitations", "research gaps", "challenges"]
        },
        {
          id: "objectives",
          title: "Research Objectives and Significance",
          description: "Clear statement of research objectives and their significance",
          requiredSources: 3,
          searchTerms: [...keywords, "objectives", "significance", "importance"]
        }
      ],
      researchGaps: ["Limited understanding", "Methodological challenges", "Need for comprehensive analysis"],
      keyThemes: keywords,
      searchKeywords: [...keywords, researchField, "research", "study", "analysis"]
    };
  }

  /**
   * Search for comprehensive sources across all research domains
   */
  private async searchComprehensiveSources(
    context: ResearchContext,
    outline: IntroductionOutline
  ): Promise<ContentSource[]> {
    const allSources: ContentSource[] = [];
    let searchStep = 1;

    // Search for each outline section
    for (const section of outline.sections) {
      console.log(`🔍 Searching for ${section.title} (${section.requiredSources} sources needed)...`);

      const sectionSources = await this.searchSectionSources(
        context,
        section,
        searchStep
      );

      allSources.push(...sectionSources);
      searchStep += section.searchTerms.length;
    }

    // If we don't have enough sources, search with broader terms
    if (allSources.length < this.minRequiredSources) {
      console.log(`⚠️ Only found ${allSources.length} sources, searching for more...`);

      const additionalSources = await this.searchAdditionalSources(
        context,
        outline,
        this.minRequiredSources - allSources.length,
        searchStep
      );

      allSources.push(...additionalSources);
    }

    // Remove duplicates and sort by relevance
    const uniqueSources = this.deduplicateSources(allSources);
    const sortedSources = uniqueSources.sort((a, b) => b.confidence - a.confidence);

    console.log(`✅ Final source collection: ${sortedSources.length} unique sources`);
    return sortedSources.slice(0, 25); // Limit to top 25 sources
  }

  /**
   * Search sources for a specific outline section
   */
  private async searchSectionSources(
    context: ResearchContext,
    section: OutlineSection,
    startStep: number
  ): Promise<ContentSource[]> {
    const sources: ContentSource[] = [];

    for (let i = 0; i < section.searchTerms.length && sources.length < section.requiredSources; i++) {
      const searchTerm = section.searchTerms[i];
      const query = this.buildUniversalSearchQuery(context, searchTerm);

      try {
        const searchResult = await tavilySearchService.searchAcademicFlexible(query, {
          maxResults: Math.ceil(section.requiredSources * 1.5),
          searchDepth: 'advanced'
        });

        this.searchLog.push({
          step: startStep + i,
          query,
          resultsFound: searchResult.results?.length || 0,
          relevantSources: 0,
          timestamp: new Date()
        });

        if (searchResult.results && searchResult.results.length > 0) {
          // Extract references from search results
          const extractionResult = await realReferenceExtractorService.extractRealReferences(
            searchResult.results,
            "google/gemini-2.5-flash",
            {
              title: context.title,
              field: context.researchField,
              keywords: context.keywords
            }
          );

          // Convert to ContentSource format
          const contentSources = extractionResult.realReferences.map(ref => ({
            id: ref.id,
            title: ref.title,
            authors: ref.authors,
            year: ref.year,
            url: ref.url,
            relevantText: ref.abstract || '',
            usedInSentences: [],
            confidence: ref.confidence
          }));

          sources.push(...contentSources);

          // Update search log
          this.searchLog[this.searchLog.length - 1].relevantSources = contentSources.length;
        }

      } catch (error) {
        console.error(`Search failed for "${query}":`, error);
      }
    }

    return sources.slice(0, section.requiredSources);
  }

  /**
   * Build universal search query for any research domain
   */
  private buildUniversalSearchQuery(context: ResearchContext, searchTerm: string): string {
    const { title, researchField, keywords } = context;

    // Extract key terms from title
    const titleWords = title.split(/\s+/).filter(word =>
      word.length > 3 &&
      !['the', 'and', 'for', 'with', 'using', 'based', 'analysis', 'study'].includes(word.toLowerCase())
    );

    // Create focused query
    const mainTerms = [searchTerm, ...keywords.slice(0, 2)].filter(Boolean);
    const titleTerm = titleWords.length > 0 ? titleWords[0] : '';

    // Build query based on research field
    if (titleTerm && mainTerms.length > 0) {
      return `"${titleTerm}" ${mainTerms.join(' ')} ${researchField}`;
    } else if (mainTerms.length >= 2) {
      return `"${mainTerms[0]}" "${mainTerms[1]}" ${researchField}`;
    } else {
      return `${searchTerm} ${researchField} research`;
    }
  }

  /**
   * Search for additional sources if minimum not met
   */
  private async searchAdditionalSources(
    context: ResearchContext,
    outline: IntroductionOutline,
    needed: number,
    startStep: number
  ): Promise<ContentSource[]> {
    const sources: ContentSource[] = [];
    const broadSearchTerms = [
      ...outline.searchKeywords,
      `${context.researchField} research`,
      `${context.researchField} methodology`,
      `${context.researchField} applications`,
      ...context.keywords.map(kw => `${kw} ${context.researchField}`)
    ];

    for (let i = 0; i < broadSearchTerms.length && sources.length < needed; i++) {
      const query = broadSearchTerms[i];

      try {
        const searchResult = await tavilySearchService.searchAcademicFlexible(query, {
          maxResults: Math.ceil(needed * 1.2),
          searchDepth: 'basic'
        });

        this.searchLog.push({
          step: startStep + i,
          query,
          resultsFound: searchResult.results?.length || 0,
          relevantSources: 0,
          timestamp: new Date()
        });

        if (searchResult.results && searchResult.results.length > 0) {
          const extractionResult = await realReferenceExtractorService.extractRealReferences(
            searchResult.results,
            "google/gemini-2.5-flash",
            {
              title: context.title,
              field: context.researchField,
              keywords: context.keywords
            }
          );

          const contentSources = extractionResult.realReferences.map(ref => ({
            id: ref.id,
            title: ref.title,
            authors: ref.authors,
            year: ref.year,
            url: ref.url,
            relevantText: ref.abstract || '',
            usedInSentences: [],
            confidence: ref.confidence
          }));

          sources.push(...contentSources);
          this.searchLog[this.searchLog.length - 1].relevantSources = contentSources.length;
        }

      } catch (error) {
        console.error(`Additional search failed for "${query}":`, error);
      }
    }

    return sources.slice(0, needed);
  }

  /**
   * Remove duplicate sources based on title similarity
   */
  private deduplicateSources(sources: ContentSource[]): ContentSource[] {
    const unique: ContentSource[] = [];
    const seenTitles = new Set<string>();

    for (const source of sources) {
      const normalizedTitle = source.title.toLowerCase().replace(/[^a-z0-9]/g, '');
      if (!seenTitles.has(normalizedTitle)) {
        seenTitles.add(normalizedTitle);
        unique.push(source);
      }
    }

    return unique;
  }

  /**
   * Write introduction with source tracking using multi-pass approach
   */
  private async writeSourcedIntroduction(
    context: ResearchContext,
    outline: IntroductionOutline,
    bibliography: ContentSource[],
    model: string,
    maxLength: number,
    includeSourceTracking: boolean
  ): Promise<SourcedContent> {
    // Try the comprehensive approach first
    const comprehensiveResult = await this.writeComprehensiveIntroduction(
      context, outline, bibliography, model, maxLength, includeSourceTracking
    );

    // Check if we used enough sources
    const sourcesUsed = comprehensiveResult.sources.length;
    const sourcesAvailable = Math.min(bibliography.length, 20);
    const usageRatio = sourcesUsed / sourcesAvailable;

    console.log(`📊 First pass results: ${sourcesUsed}/${sourcesAvailable} sources used (${(usageRatio * 100).toFixed(1)}%)`);

    // If usage is good (>70%), return the result
    if (usageRatio >= 0.7) {
      return comprehensiveResult;
    }

    // Otherwise, try section-by-section approach
    console.log('🔄 Attempting section-by-section generation for better source coverage...');
    return await this.writeSectionBySection(
      context, outline, bibliography, model, maxLength, includeSourceTracking
    );
  }

  /**
   * Write comprehensive introduction in one pass
   */
  private async writeComprehensiveIntroduction(
    context: ResearchContext,
    outline: IntroductionOutline,
    bibliography: ContentSource[],
    model: string,
    maxLength: number,
    includeSourceTracking: boolean
  ): Promise<SourcedContent> {
    const { title, researchField, methodology, results, existingIntroduction } = context;

    // Use more sources and prepare detailed information
    const sourcesToUse = Math.min(bibliography.length, 20);
    const sourceInfo = bibliography.slice(0, sourcesToUse).map((source, index) => {
      const authorString = source.authors.length > 2
        ? `${source.authors[0]} et al.`
        : source.authors.join(' & ');
      return `[${index + 1}] ${authorString} (${source.year}). ${source.title}.
Abstract/Content: ${source.relevantText.substring(0, 300)}...
Citation Format: (${source.authors[0].split(',')[0]}, ${source.year})`;
    }).join('\n\n');

    // Calculate required citations per section
    const totalSections = outline.sections.length;
    const citationsPerSection = Math.ceil(sourcesToUse / totalSections);
    const minCitationsRequired = Math.max(15, sourcesToUse);

    const prompt = `You are an expert academic writer. Write a comprehensive introduction that uses ALL ${sourcesToUse} provided sources with proper citations.

RESEARCH CONTEXT:
Title: ${title}
Field: ${researchField}
${methodology ? `Methodology Overview: ${methodology.substring(0, 400)}...` : ''}
${results ? `Key Results: ${results.substring(0, 400)}...` : ''}
${existingIntroduction ? `Existing Introduction to Rewrite: ${existingIntroduction.substring(0, 400)}...` : ''}

OUTLINE STRUCTURE (use ${citationsPerSection} citations per section):
${outline.sections.map((section, index) =>
  `${index + 1}. ${section.title}: ${section.description}
   Required citations: ${citationsPerSection} sources from the list below`
).join('\n')}

AVAILABLE SOURCES (YOU MUST USE ALL ${sourcesToUse} SOURCES):
${sourceInfo}

CRITICAL REQUIREMENTS:
1. MANDATORY: Use ALL ${sourcesToUse} sources provided above - each source must appear at least once
2. Target: ${minCitationsRequired}+ total citations throughout the introduction
3. Distribution: ${citationsPerSection} citations per major section
4. Format: Use exact citation format provided for each source
5. Length: ${maxLength} words comprehensive introduction
6. Style: Formal academic writing with clear logical flow
7. Relevance: Connect each citation to the specific research topic
8. NO FAKE CITATIONS: Use ONLY the numbered sources provided above

CITATION STRATEGY:
- Section 1 (${outline.sections[0]?.title}): Use sources [1-${citationsPerSection}]
- Section 2 (${outline.sections[1]?.title}): Use sources [${citationsPerSection + 1}-${citationsPerSection * 2}]
- Section 3 (${outline.sections[2]?.title}): Use sources [${citationsPerSection * 2 + 1}-${citationsPerSection * 3}]
- Section 4 (${outline.sections[3]?.title}): Use remaining sources
- Distribute any extra sources throughout for additional support

${includeSourceTracking ? 'IMPORTANT: After each sentence with citations, add [SOURCE_TRACK: source_numbers] to track which sources were used.' : ''}

Write a comprehensive introduction that demonstrates extensive literature review by using ALL ${sourcesToUse} sources:`;

    try {
      const response = await paperAIService.generatePaperSection(prompt, {
        model,
        maxTokens: Math.ceil(maxLength * 1.5),
        temperature: 0.7
      });

      // Process response and extract source tracking
      const { content, sources } = this.processSourcedContent(response, bibliography, includeSourceTracking);

      const wordCount = content.split(/\s+/).length;
      const citationCount = (content.match(/\([^)]+,\s*\d{4}\)/g) || []).length;

      // Validate citation usage
      const sourcesUsed = sources.length;
      const sourcesAvailable = Math.min(bibliography.length, 20);

      console.log(`📊 Citation Analysis:`);
      console.log(`- Sources available: ${sourcesAvailable}`);
      console.log(`- Sources used: ${sourcesUsed}`);
      console.log(`- Total citations: ${citationCount}`);
      console.log(`- Citation density: ${(citationCount / wordCount * 100).toFixed(1)} citations per 100 words`);

      // Enforce minimum citation usage
      const minCitations = Math.max(15, Math.floor(sourcesAvailable * 0.8));
      if (citationCount < minCitations || sourcesUsed < sourcesAvailable * 0.7) {
        console.log(`⚠️ Citation enforcement needed: ${citationCount}/${minCitations} citations, ${sourcesUsed}/${sourcesAvailable} sources`);

        // Convert sources to enforcer format
        const enforcerSources = bibliography.map(source => ({
          id: source.id,
          title: source.title,
          authors: source.authors,
          year: source.year,
          citation: `(${source.authors[0].split(',')[0]}, ${source.year})`,
          relevantText: source.relevantText
        }));

        const usedEnforcerSources = sources.map(source => ({
          id: source.id,
          title: source.title,
          authors: source.authors,
          year: source.year,
          citation: `(${source.authors[0].split(',')[0]}, ${source.year})`,
          relevantText: source.relevantText
        }));

        const enforcementResult = await citationEnforcerService.enforceMinimumCitations(
          content,
          enforcerSources,
          usedEnforcerSources,
          minCitations,
          model
        );

        console.log(`✅ Citation enforcement completed:`);
        enforcementResult.enforcementLog.forEach(log => console.log(`   ${log}`));

        // Re-process the enhanced content
        const enhancedResult = this.processSourcedContent(enforcementResult.content, bibliography, includeSourceTracking);

        return {
          content: enhancedResult.content,
          sources: enhancedResult.sources,
          wordCount: enhancedResult.content.split(/\s+/).length,
          citationCount: enforcementResult.totalCitations
        };
      }

      return {
        content,
        sources,
        wordCount,
        citationCount
      };

    } catch (error) {
      console.error('Failed to generate sourced introduction:', error);
      throw error;
    }
  }

  /**
   * Write introduction section by section to ensure all sources are used
   */
  private async writeSectionBySection(
    context: ResearchContext,
    outline: IntroductionOutline,
    bibliography: ContentSource[],
    model: string,
    maxLength: number,
    includeSourceTracking: boolean
  ): Promise<SourcedContent> {
    const { title, researchField } = context;
    const sections: string[] = [];
    const allUsedSources: ContentSource[] = [];
    let totalCitations = 0;

    // Distribute sources across sections
    const sourcesPerSection = Math.ceil(bibliography.length / outline.sections.length);

    for (let i = 0; i < outline.sections.length; i++) {
      const section = outline.sections[i];
      const startIndex = i * sourcesPerSection;
      const endIndex = Math.min(startIndex + sourcesPerSection, bibliography.length);
      const sectionSources = bibliography.slice(startIndex, endIndex);

      console.log(`✍️ Writing section ${i + 1}: ${section.title} (${sectionSources.length} sources)`);

      const sectionContent = await this.writeSingleSection(
        context,
        section,
        sectionSources,
        model,
        Math.floor(maxLength / outline.sections.length),
        includeSourceTracking
      );

      sections.push(sectionContent.content);
      allUsedSources.push(...sectionContent.sources);
      totalCitations += sectionContent.citationCount;
    }

    // Combine all sections
    const fullContent = sections.join('\n\n');
    const wordCount = fullContent.split(/\s+/).length;

    console.log(`📊 Section-by-section results:`);
    console.log(`- Total sources used: ${allUsedSources.length}/${bibliography.length}`);
    console.log(`- Total citations: ${totalCitations}`);
    console.log(`- Word count: ${wordCount}`);

    return {
      content: fullContent,
      sources: allUsedSources,
      wordCount,
      citationCount: totalCitations
    };
  }

  /**
   * Write a single section with specific sources
   */
  private async writeSingleSection(
    context: ResearchContext,
    section: OutlineSection,
    sources: ContentSource[],
    model: string,
    targetLength: number,
    includeSourceTracking: boolean
  ): Promise<SourcedContent> {
    const sourceInfo = sources.map((source, index) => {
      const authorString = source.authors.length > 2
        ? `${source.authors[0]} et al.`
        : source.authors.join(' & ');
      return `[${index + 1}] ${authorString} (${source.year}). ${source.title}.
Citation: (${source.authors[0].split(',')[0]}, ${source.year})
Content: ${source.relevantText.substring(0, 250)}...`;
    }).join('\n\n');

    const prompt = `Write a focused section for an academic introduction. You MUST use ALL ${sources.length} sources provided.

SECTION: ${section.title}
DESCRIPTION: ${section.description}
RESEARCH TOPIC: ${context.title}
FIELD: ${context.researchField}

SOURCES TO USE (USE ALL ${sources.length} SOURCES):
${sourceInfo}

REQUIREMENTS:
1. MANDATORY: Use ALL ${sources.length} sources in this section
2. Length: ${targetLength} words
3. Citations: Include ${sources.length} citations using the exact format provided
4. Style: Academic writing appropriate for ${context.researchField}
5. Focus: Stay relevant to "${context.title}"
6. Integration: Weave citations naturally into the narrative

${includeSourceTracking ? 'Add [SOURCE_TRACK: source_numbers] after each cited sentence.' : ''}

Write the section ensuring ALL sources are cited:`;

    try {
      const response = await paperAIService.generatePaperSection(prompt, {
        model,
        maxTokens: Math.ceil(targetLength * 1.5),
        temperature: 0.6
      });

      const { content, sources: usedSources } = this.processSourcedContent(response, sources, includeSourceTracking);
      const wordCount = content.split(/\s+/).length;
      const citationCount = (content.match(/\([^)]+,\s*\d{4}\)/g) || []).length;

      return {
        content,
        sources: usedSources,
        wordCount,
        citationCount
      };

    } catch (error) {
      console.error(`Failed to write section ${section.title}:`, error);
      throw error;
    }
  }

  /**
   * Enhance citation usage if not enough sources were used
   */
  private async enhanceCitationUsage(
    originalContent: string,
    bibliography: ContentSource[],
    usedSources: ContentSource[],
    model: string
  ): Promise<string | null> {
    const unusedSources = bibliography.filter(source =>
      !usedSources.find(used => used.id === source.id)
    );

    if (unusedSources.length === 0) return null;

    const unusedSourceInfo = unusedSources.slice(0, 10).map((source, index) => {
      const authorString = source.authors.length > 2
        ? `${source.authors[0]} et al.`
        : source.authors.join(' & ');
      return `[${index + 1}] ${authorString} (${source.year}). ${source.title}.
Citation: (${source.authors[0].split(',')[0]}, ${source.year})
Relevant content: ${source.relevantText.substring(0, 200)}...`;
    }).join('\n\n');

    const enhancementPrompt = `You are an academic editor. Enhance this introduction by integrating the additional sources provided.

CURRENT INTRODUCTION:
${originalContent}

ADDITIONAL SOURCES TO INTEGRATE:
${unusedSourceInfo}

TASK:
1. Add the unused sources naturally into the existing text
2. Maintain the current structure and flow
3. Add 2-3 sentences that incorporate these sources
4. Use proper citation format: (Author, Year)
5. Ensure citations support relevant claims
6. Do not change the overall length significantly
7. Maintain academic writing style

Enhanced introduction with additional citations:`;

    try {
      const enhancedResponse = await paperAIService.generatePaperSection(enhancementPrompt, {
        model,
        maxTokens: 2000,
        temperature: 0.5
      });

      return enhancedResponse;
    } catch (error) {
      console.error('Citation enhancement failed:', error);
      return null;
    }
  }

  /**
   * Process content and extract source tracking information
   */
  private processSourcedContent(
    rawContent: string,
    bibliography: ContentSource[],
    includeSourceTracking: boolean
  ): { content: string; sources: ContentSource[] } {
    let content = rawContent;
    const usedSources: ContentSource[] = [];

    if (includeSourceTracking) {
      // Extract source tracking markers
      const trackingPattern = /\[SOURCE_TRACK:\s*([^\]]+)\]/g;
      let match;

      while ((match = trackingPattern.exec(content)) !== null) {
        const sourceNumbers = match[1].split(',').map(n => parseInt(n.trim()) - 1);
        const sentence = this.extractSentenceBeforeMarker(content, match.index);

        sourceNumbers.forEach(sourceIndex => {
          if (sourceIndex >= 0 && sourceIndex < bibliography.length) {
            const source = bibliography[sourceIndex];
            if (!usedSources.find(s => s.id === source.id)) {
              usedSources.push({
                ...source,
                usedInSentences: [sentence]
              });
            } else {
              const existingSource = usedSources.find(s => s.id === source.id);
              if (existingSource && !existingSource.usedInSentences.includes(sentence)) {
                existingSource.usedInSentences.push(sentence);
              }
            }
          }
        });
      }

      // Remove tracking markers from final content
      content = content.replace(trackingPattern, '').trim();
    } else {
      // Enhanced citation extraction and matching
      const citationPatterns = [
        /\(([^)]+),\s*(\d{4})\)/g,           // (Author, Year)
        /\(([^)]+)\s+et\s+al\.,\s*(\d{4})\)/g, // (Author et al., Year)
        /\(([^)]+)\s+&\s+([^)]+),\s*(\d{4})\)/g // (Author & Author, Year)
      ];

      for (const pattern of citationPatterns) {
        let citationMatch;
        while ((citationMatch = pattern.exec(content)) !== null) {
          const authorName = citationMatch[1];
          const year = parseInt(citationMatch[citationMatch.length - 1]); // Last group is always year

          // Enhanced matching logic
          const matchingSource = bibliography.find(source => {
            if (source.year !== year) return false;

            // Check various author matching patterns
            return source.authors.some(author => {
              const authorLastName = author.split(',')[0].toLowerCase().trim();
              const citationAuthor = authorName.toLowerCase().trim();

              return (
                authorLastName.includes(citationAuthor) ||
                citationAuthor.includes(authorLastName) ||
                this.fuzzyAuthorMatch(authorLastName, citationAuthor)
              );
            });
          });

          if (matchingSource && !usedSources.find(s => s.id === matchingSource.id)) {
            usedSources.push(matchingSource);
          }
        }
      }

      // Additional pattern matching for numbered references like [1], [2], etc.
      const numberedPattern = /\[(\d+)\]/g;
      let numberedMatch;
      while ((numberedMatch = numberedPattern.exec(content)) !== null) {
        const sourceIndex = parseInt(numberedMatch[1]) - 1;
        if (sourceIndex >= 0 && sourceIndex < bibliography.length) {
          const source = bibliography[sourceIndex];
          if (!usedSources.find(s => s.id === source.id)) {
            usedSources.push(source);
          }
        }
      }
    }

    return { content, sources: usedSources };
  }

  /**
   * Fuzzy matching for author names to handle variations
   */
  private fuzzyAuthorMatch(authorName1: string, authorName2: string): boolean {
    // Remove common prefixes and suffixes
    const clean1 = authorName1.replace(/^(dr|prof|mr|ms|mrs)\.?\s*/i, '').replace(/\s+(jr|sr|ii|iii)\.?$/i, '');
    const clean2 = authorName2.replace(/^(dr|prof|mr|ms|mrs)\.?\s*/i, '').replace(/\s+(jr|sr|ii|iii)\.?$/i, '');

    // Check if one is contained in the other (minimum 3 characters)
    if (clean1.length >= 3 && clean2.length >= 3) {
      return clean1.includes(clean2) || clean2.includes(clean1);
    }

    // Check for similar starting characters
    if (clean1.length >= 4 && clean2.length >= 4) {
      return clean1.substring(0, 4) === clean2.substring(0, 4);
    }

    return false;
  }

  /**
   * Extract sentence before a source tracking marker
   */
  private extractSentenceBeforeMarker(content: string, markerIndex: number): string {
    const beforeMarker = content.substring(0, markerIndex);
    const sentences = beforeMarker.split(/[.!?]+/);
    return sentences[sentences.length - 1]?.trim() || '';
  }

  /**
   * Calculate quality metrics for the introduction
   */
  private calculateQualityMetrics(
    bibliography: ContentSource[],
    sourcedContent: SourcedContent
  ): QualityMetrics {
    const totalSources = bibliography.length;
    const academicSources = bibliography.filter(source =>
      source.url.includes('.edu') ||
      source.url.includes('.gov') ||
      source.url.includes('scholar.google') ||
      source.url.includes('arxiv.org') ||
      source.url.includes('researchgate.net')
    ).length;

    const averageRelevance = bibliography.reduce((sum, source) => sum + source.confidence, 0) / totalSources;
    const coverageScore = Math.min(totalSources / this.minRequiredSources, 1.0);
    const citationDensity = (sourcedContent.citationCount / sourcedContent.wordCount) * 100;

    return {
      totalSources,
      academicSources,
      averageRelevance,
      coverageScore,
      citationDensity
    };
  }
}

// Export singleton instance
export const universalIntroductionGeneratorService = new UniversalIntroductionGeneratorService();
