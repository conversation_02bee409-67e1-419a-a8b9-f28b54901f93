/**
 * AgenticEditingService - Main service that integrates the agentic system with the existing editor
 */

import { EventEmitter } from 'events';
import { AgentOrchestrator } from './AgentOrchestrator';
import { SearchTool, AnalysisTool, EditTool, ReviewTool, ValidationTool, WriteTool } from './tools';
import {
  AgentExecutionResult,
  AgentExecutionOptions,
  AgentProgressUpdate,
  AgentEvent,
  ChangeTrackingIntegration,
  EditResult
} from './types';

export interface AgenticEditingOptions extends AgentExecutionOptions {
  enableProgressUpdates?: boolean;
  enableChangeTracking?: boolean;
  previewChanges?: boolean;
}

export interface AgenticEditingResult extends AgentExecutionResult {
  changeIds?: string[];
  previewAvailable?: boolean;
}

export class AgenticEditingService extends EventEmitter {
  private orchestrator: AgentOrchestrator;
  private changeTrackingIntegration?: ChangeTrackingIntegration;
  private isInitialized = false;

  constructor() {
    super();
    this.orchestrator = new AgentOrchestrator();
    this.setupOrchestrator();
  }

  /**
   * Initialize the service with all tools
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🚀 Initializing Agentic Editing Service...');

    // Register all tools
    this.orchestrator.registerTool(new SearchTool());
    this.orchestrator.registerTool(new AnalysisTool());
    this.orchestrator.registerTool(new EditTool());
    this.orchestrator.registerTool(new ReviewTool());
    this.orchestrator.registerTool(new ValidationTool());
    this.orchestrator.registerTool(new WriteTool());

    this.isInitialized = true;
    console.log('✅ Agentic Editing Service initialized with 6 tools');
  }

  /**
   * Set up change tracking integration
   */
  setChangeTrackingIntegration(integration: ChangeTrackingIntegration): void {
    this.changeTrackingIntegration = integration;
    console.log('🔗 Change tracking integration configured');
  }

  /**
   * Execute targeted editing with the agentic system
   */
  async executeTargetedEdit(
    userRequest: string,
    documentContent: string,
    options: AgenticEditingOptions = {}
  ): Promise<AgenticEditingResult> {
    if (!this.isInitialized) {
      this.initialize();
    }

    console.log('🎯 Starting agentic targeted editing:', {
      userRequest,
      documentLength: documentContent.length,
      options
    });

    try {
      // Execute the workflow
      const result = await this.orchestrator.executeTargetedEdit(
        userRequest,
        documentContent,
        options
      );

      // Integrate with change tracking if enabled
      let changeIds: string[] = [];
      if (options.enableChangeTracking && this.changeTrackingIntegration && result.changes.length > 0) {
        changeIds = this.integrateWithChangeTracking(result.changes, userRequest, options);
      }

      // Prepare final result
      const agenticResult: AgenticEditingResult = {
        ...result,
        changeIds: changeIds.length > 0 ? changeIds : undefined,
        previewAvailable: options.previewChanges && result.changes.length > 0
      };

      console.log('✅ Agentic editing completed:', {
        success: result.success,
        changesCount: result.changes.length,
        changeIds: changeIds.length,
        executionTime: result.executionTime
      });

      return agenticResult;

    } catch (error: any) {
      console.error('❌ Agentic editing failed:', error);
      
      return {
        success: false,
        changes: [],
        summary: 'Agentic editing failed',
        reasoning: error.message,
        confidence: 0,
        executionTime: 0,
        toolsUsed: [],
        error: error.message
      };
    }
  }

  /**
   * Preview changes without applying them
   */
  async previewTargetedEdit(
    userRequest: string,
    documentContent: string,
    options: AgenticEditingOptions = {}
  ): Promise<AgenticEditingResult> {
    const previewOptions = {
      ...options,
      enableChangeTracking: true, // Enable for preview
      previewChanges: true,
      requirePreview: true
    };

    console.log('🔍 Executing preview for targeted edit...');
    const result = await this.executeTargetedEdit(userRequest, documentContent, previewOptions);

    // Show preview if change tracking integration is available
    if (this.changeTrackingIntegration && result.changes.length > 0) {
      console.log('📋 Showing preview in change tracking system...');
      this.changeTrackingIntegration.previewChanges(result.changes);
    }

    return result;
  }

  /**
   * Apply previously previewed changes
   */
  applyPreviewedChanges(changeIds: string[]): void {
    if (!this.changeTrackingIntegration) {
      throw new Error('Change tracking integration not configured');
    }

    console.log('✅ Applying previewed changes via change tracking integration...');
    this.changeTrackingIntegration.applyChanges(changeIds);
    console.log('✅ Applied previewed changes:', changeIds);
  }

  /**
   * Reject previously previewed changes
   */
  rejectPreviewedChanges(changeIds: string[]): void {
    if (!this.changeTrackingIntegration) {
      throw new Error('Change tracking integration not configured');
    }

    console.log('❌ Rejecting previewed changes via change tracking integration...');
    this.changeTrackingIntegration.rejectChanges(changeIds);
    console.log('❌ Rejected previewed changes:', changeIds);
  }

  /**
   * Get the current workflow state
   */
  getWorkflowState(workflowId: string) {
    return this.orchestrator.getWorkflowState(workflowId);
  }

  /**
   * Set up orchestrator event handling
   */
  private setupOrchestrator(): void {
    // Forward orchestrator events
    this.orchestrator.on('agent_event', (event: AgentEvent) => {
      this.emit('agent_event', event);
    });

    // Handle progress updates
    this.orchestrator.on('step_progress', (event: AgentEvent) => {
      const update = event.data as AgentProgressUpdate;
      this.emit('progress_update', update);
    });

    // Handle workflow completion
    this.orchestrator.on('workflow_completed', (event: AgentEvent) => {
      this.emit('workflow_completed', event.data);
    });

    // Handle workflow failure
    this.orchestrator.on('workflow_failed', (event: AgentEvent) => {
      this.emit('workflow_failed', event.data);
    });
  }

  /**
   * Integrate with change tracking system
   */
  private integrateWithChangeTracking(
    edits: EditResult[],
    userRequest: string,
    options: AgenticEditingOptions
  ): string[] {
    if (!this.changeTrackingIntegration) {
      console.warn('⚠️ Change tracking integration not available');
      return [];
    }

    try {
      const changeIds = this.changeTrackingIntegration.recordChanges(
        edits,
        `Agentic Edit: ${userRequest}`
      );

      console.log('📝 Recorded changes in tracking system:', {
        editsCount: edits.length,
        changeIds: changeIds.length
      });

      return changeIds;

    } catch (error: any) {
      console.error('❌ Failed to integrate with change tracking:', error);
      return [];
    }
  }

  /**
   * Get available tools information
   */
  getAvailableTools(): Array<{ id: string; name: string; description: string }> {
    return [
      {
        id: 'search-tool',
        name: 'Content Search',
        description: 'Locates specific content sections within the document'
      },
      {
        id: 'analysis-tool',
        name: 'Request Analysis',
        description: 'Analyzes user requests to understand intent and determine editing approach'
      },
      {
        id: 'edit-tool',
        name: 'Content Editor',
        description: 'Performs precise, targeted modifications to specific text sections'
      },
      {
        id: 'review-tool',
        name: 'Change Reviewer',
        description: 'Verifies that changes were applied correctly and match user intent'
      },
      {
        id: 'validation-tool',
        name: 'Change Validator',
        description: 'Ensures no unintended changes were made outside the target area'
      },
      {
        id: 'write-tool',
        name: 'Content Writer',
        description: 'Handles cases where complete section rewrites are necessary'
      }
    ];
  }

  /**
   * Get service status
   */
  getStatus(): {
    initialized: boolean;
    toolsCount: number;
    hasChangeTracking: boolean;
  } {
    return {
      initialized: this.isInitialized,
      toolsCount: this.getAvailableTools().length,
      hasChangeTracking: !!this.changeTrackingIntegration
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.removeAllListeners();
    this.orchestrator.removeAllListeners();
    console.log('🧹 Agentic Editing Service cleaned up');
  }
}

// Create singleton instance
export const agenticEditingService = new AgenticEditingService();
