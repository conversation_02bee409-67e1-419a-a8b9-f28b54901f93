import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search } from "lucide-react";

import { ResearchDocument, ExtractedData, AIGenerationOptions } from '../types';

interface DataExtractorProps {
  documents: ResearchDocument[];
  onExtract: (data: ExtractedData[]) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function DataExtractor({ documents }: DataExtractorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Search className="h-6 w-6 text-gray-500" />
          Data Extractor
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Data extraction feature coming soon!</p>
          <Badge variant="secondary">
            {documents.length} documents selected
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
