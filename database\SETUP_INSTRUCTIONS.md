# 🚀 Flow Builder Database Setup Instructions

## 📋 Prerequisites

1. **Supabase Project**: Ensure you have a Supabase project set up
2. **Environment Variables**: Verify your `.env` files have the correct Supabase credentials
3. **Database Access**: Admin access to your Supabase SQL editor

## 🗄️ Database Setup Steps

### Step 1: Run the Setup Script

1. **Open Supabase Dashboard**
   - Go to your Supabase project dashboard
   - Navigate to **SQL Editor** in the left sidebar

2. **Execute Setup Script**
   - Copy the entire content from `database/setup_flow_builder.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute the script

3. **Verify Success**
   - You should see: `Flow Builder database setup completed successfully!`
   - Check the **Table Editor** to confirm all tables were created

### Step 2: Verify Tables Created

The following tables should now exist in your database:

#### Core Tables:
- ✅ `user_diagrams` - Stores all generated diagrams
- ✅ `diagram_exports` - Export history and file tracking
- ✅ `diagram_activity` - User activity logging
- ✅ `diagram_templates` - Pre-built research templates
- ✅ `diagram_collections` - User-created folders
- ✅ `diagram_collection_items` - Collection relationships

#### Security Features:
- ✅ **Row Level Security (RLS)** enabled on all tables
- ✅ **Policies** created for secure data access
- ✅ **Indexes** for optimal performance
- ✅ **Triggers** for automatic updates

### Step 3: Verify Environment Variables

Ensure your `.env.local` file contains:

```bash
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Step 4: Test the Integration

1. **Start the Development Server**
   ```bash
   npm run dev
   ```

2. **Navigate to Flow Builder**
   - Go to the research section
   - Click on "Flow Builder"
   - You should see the beautiful floating AI assistant

3. **Test History Functionality**
   - Generate a test diagram
   - Check if it appears in the History tab
   - Verify the diagram is saved in Supabase

## 🔧 Troubleshooting

### Common Issues:

#### 1. **RLS Policies Not Working**
```sql
-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE '%diagram%';
```

#### 2. **Missing Functions**
```sql
-- Verify functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('increment_diagram_view_count', 'update_collection_diagram_count');
```

#### 3. **Template Data Missing**
```sql
-- Check if templates were inserted
SELECT COUNT(*) FROM public.diagram_templates;
-- Should return 6 templates
```

#### 4. **Authentication Issues**
- Verify user is logged in to your app
- Check Supabase auth configuration
- Ensure RLS policies allow the current user

### Manual Fixes:

#### Re-run Template Insertion:
```sql
-- If templates are missing, run this:
INSERT INTO public.diagram_templates (name, description, category, diagram_type, template_prompt, tags) VALUES
('Research Methodology', 'Complete research methodology flowchart', 'methodology', 'flowchart', 'Create a comprehensive research methodology flowchart with all phases', ARRAY['research', 'methodology']),
('Data Analysis Pipeline', 'Data analysis workflow', 'analysis', 'flowchart', 'Design a data analysis pipeline from raw data to insights', ARRAY['data', 'analysis']),
('Literature Review', 'Systematic literature review process', 'review', 'flowchart', 'Create a systematic literature review following PRISMA guidelines', ARRAY['literature', 'review'])
ON CONFLICT (name) DO NOTHING;
```

#### Reset User Data (if needed):
```sql
-- CAUTION: This will delete all user diagram data
DELETE FROM public.user_diagrams WHERE user_id = auth.uid();
DELETE FROM public.diagram_collections WHERE user_id = auth.uid();
```

## 🎯 Testing Checklist

### ✅ Basic Functionality:
- [ ] Can generate diagrams
- [ ] Diagrams appear in history
- [ ] Search works in history
- [ ] Can favorite/unfavorite diagrams
- [ ] Templates load correctly
- [ ] Error fixing works

### ✅ Database Operations:
- [ ] Diagrams save to `user_diagrams` table
- [ ] Activity logs to `diagram_activity` table
- [ ] View counts increment properly
- [ ] RLS policies work (users only see their data)
- [ ] Templates are publicly readable

### ✅ UI/UX:
- [ ] Floating bubble appears bottom-right
- [ ] Bubble expands/collapses smoothly
- [ ] History tab loads user diagrams
- [ ] Templates tab shows research templates
- [ ] Create tab has input form

## 🚀 Success Indicators

When everything is working correctly, you should see:

1. **Beautiful Floating Bubble**: Bottom-right corner with gradient and animations
2. **History Management**: Previous diagrams saved and searchable
3. **Template Library**: 6 research templates available
4. **Error Recovery**: "Fix with AI" button when diagrams fail
5. **Activity Tracking**: User actions logged in database
6. **Secure Access**: Users only see their own data

## 📞 Support

If you encounter issues:

1. **Check Browser Console**: Look for JavaScript errors
2. **Check Supabase Logs**: Monitor database queries and errors
3. **Verify Network**: Ensure connection to Supabase
4. **Test Authentication**: Confirm user is properly logged in

## 🎉 Congratulations!

Once setup is complete, you'll have a world-class research diagram tool with:
- ✨ Beautiful, modern interface
- 🗄️ Comprehensive history management
- 🤖 AI-powered error recovery
- 📚 Professional template library
- 🔒 Secure, scalable architecture

Your Flow Builder is now ready for professional research workflows! 🚀
