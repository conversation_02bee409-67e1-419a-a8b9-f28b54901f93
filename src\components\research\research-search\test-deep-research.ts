/**
 * Test Deep Research Functionality
 * Simple test to verify deep research service works correctly
 */

import { deepResearchService } from './services/deep-research.service';

export async function testDeepResearchService() {
  console.log('Testing Deep Research Service...');
  
  try {
    // Test service configuration
    const isConfigured = deepResearchService.isConfigured();
    console.log('Service configured:', isConfigured);
    
    if (!isConfigured) {
      console.warn('Deep research service not properly configured');
      return { success: false, error: 'Service not configured' };
    }

    // Test outline generation (NO SEARCH - Pure AI)
    console.log('Testing outline generation...');
    const outline = await deepResearchService.generateOutline(
      'What are the problems related to global warming and what achievements have been made in addressing them?',
      'google/gemini-2.0-flash-001',
      {
        academicFocus: true,
        minWordCountPerSection: 800,
        maxWordCountPerSection: 1500,
        citationStyle: 'apa'
      }
    );

    console.log('Outline generated successfully:', {
      title: outline.title,
      pointsCount: outline.points.length,
      totalPoints: outline.totalPoints
    });

    // Log outline points
    outline.points.forEach((point, index) => {
      console.log(`Point ${point.pointNumber}: ${point.title}`);
      console.log(`  Description: ${point.description}`);
      console.log(`  Subpoints: ${point.subpoints.length}`);
      point.subpoints.forEach((sub, subIndex) => {
        console.log(`    ${sub.subpointNumber}. ${sub.title}: ${sub.description}`);
      });
    });
    
    return { 
      success: true, 
      outline,
      message: 'Deep research outline generated successfully'
    };
    
  } catch (error: any) {
    console.error('Deep research test failed:', error);
    return { 
      success: false, 
      error: error.message,
      details: error
    };
  }
}

export async function testFullDeepResearch() {
  console.log('Testing Full Deep Research Workflow...');
  
  try {
    // Generate outline first
    const outline = await deepResearchService.generateOutline(
      'Machine learning applications in healthcare',
      'google/gemini-2.0-flash-001',
      {
        academicFocus: true,
        minWordCountPerSection: 500, // Smaller for testing
        maxWordCountPerSection: 800,
        citationStyle: 'apa'
      }
    );
    
    console.log('Outline generated, starting execution...');
    
    // Create a test session
    const session = {
      id: `test_session_${Date.now()}`,
      title: outline.title,
      originalQuery: 'Machine learning applications in healthcare',
      outline,
      currentPoint: 0,
      totalPoints: outline.totalPoints,
      status: 'outline_approved' as const,
      aiAssistants: [],
      completedPoints: [],
      allReferences: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'test_user',
      model: 'google/gemini-2.0-flash-001'
    };
    
    // Execute deep research (limit to first 2 points for testing)
    const limitedOutline = {
      ...outline,
      points: outline.points.slice(0, 2), // Only first 2 points
      totalPoints: 2
    };

    const limitedSession = {
      ...session,
      outline: limitedOutline,
      totalPoints: 2
    };
    
    const options = {
      model: 'google/gemini-2.0-flash-001',
      maxResultsPerSection: 5, // Smaller for testing
      searchDepth: 'advanced' as const,
      includeImages: false,
      academicFocus: true,
      minWordCountPerSection: 500,
      maxWordCountPerSection: 800,
      citationStyle: 'apa' as const,
      includeAbstract: true,
      includeConclusion: true,
      autoApproveOutline: false
    };
    
    const completedSession = await deepResearchService.executeDeepResearch(
      limitedSession,
      options,
      (progress) => {
        console.log(`Progress: ${progress.assistantName} working on ${progress.pointTitle} - ${progress.status} (${progress.progress}%)`);
        if (progress.currentSubpoint) {
          console.log(`  Current subpoint: ${progress.currentSubpoint}`);
        }
      }
    );

    console.log('Deep research completed successfully:', {
      status: completedSession.status,
      completedPoints: completedSession.completedPoints.length,
      aiAssistants: completedSession.aiAssistants.length,
      references: completedSession.allReferences.length,
      finalReportLength: completedSession.finalReport?.length || 0
    });
    
    return {
      success: true,
      session: completedSession,
      message: 'Full deep research workflow completed successfully'
    };
    
  } catch (error: any) {
    console.error('Full deep research test failed:', error);
    return {
      success: false,
      error: error.message,
      details: error
    };
  }
}

// Export test functions for use in console
if (typeof window !== 'undefined') {
  (window as any).testDeepResearch = {
    testService: testDeepResearchService,
    testFull: testFullDeepResearch
  };
}
