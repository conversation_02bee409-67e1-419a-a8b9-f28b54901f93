/**
 * Enhanced AI Model Selector Component
 * Beautiful and informative model selector for diagram generation
 */

import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Brain, Star, Zap, CheckCircle, Info } from 'lucide-react';
import { FLOW_AI_MODELS } from '../constants';

interface AIModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  className?: string;
}

export function AIModelSelector({
  selectedModel,
  onModelChange,
  className = ""
}: AIModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const currentModel = FLOW_AI_MODELS.find(m => m.id === selectedModel);

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'google':
        return '🤖';
      case 'openrouter':
        return '🔄';
      default:
        return '⚡';
    }
  };

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'google':
        return 'from-blue-500 to-blue-600';
      case 'openrouter':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className={`flex items-center gap-4 ${className}`}>
      {/* Enhanced Model Info Display */}
      <div className="flex items-center gap-3">
        <div className={`w-8 h-8 bg-gradient-to-br ${getProviderColor(currentModel?.provider || 'default')} rounded-lg flex items-center justify-center shadow-sm`}>
          <Brain className="h-4 w-4 text-white" />
        </div>
        <div className="hidden lg:block">
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold text-gray-700">AI Model</span>
            {currentModel?.recommended && (
              <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800 border-yellow-200">
                <Star className="h-3 w-3 mr-1 fill-current" />
                Recommended
              </Badge>
            )}
          </div>
          {currentModel && (
            <p className="text-xs text-gray-500 max-w-48 truncate">
              {currentModel.description}
            </p>
          )}
        </div>
      </div>

      {/* Enhanced Model Selector */}
      <Select
        value={selectedModel}
        onValueChange={onModelChange}
        onOpenChange={setIsOpen}
      >
        <SelectTrigger className="w-52 h-11 bg-white/80 backdrop-blur-sm border-gray-300/50 hover:border-blue-400 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md">
          <SelectValue placeholder="Choose AI model">
            {currentModel && (
              <div className="flex items-center gap-3">
                <span className="text-lg">{getProviderIcon(currentModel.provider)}</span>
                <div className="flex flex-col items-start">
                  <span className="font-semibold text-gray-900">{currentModel.name}</span>
                  <span className="text-xs text-gray-500 capitalize">{currentModel.provider}</span>
                </div>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>

        <SelectContent className="w-80 rounded-2xl border-gray-200/50 shadow-xl backdrop-blur-xl bg-white/95">
          <div className="p-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 px-2">
              Available AI Models
            </div>
            {FLOW_AI_MODELS.map((model) => (
              <SelectItem
                key={model.id}
                value={model.id}
                className="rounded-xl p-3 mb-1 hover:bg-blue-50 focus:bg-blue-50 transition-all duration-200"
              >
                <div className="flex items-start gap-3 w-full">
                  <div className={`w-10 h-10 bg-gradient-to-br ${getProviderColor(model.provider)} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm`}>
                    <span className="text-lg">{getProviderIcon(model.provider)}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-gray-900">{model.name}</span>
                      {model.recommended && (
                        <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800 border-yellow-200">
                          <Star className="h-3 w-3 mr-1 fill-current" />
                          Best
                        </Badge>
                      )}
                      {selectedModel === model.id && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    <p className="text-xs text-gray-600 mb-2 leading-relaxed">
                      {model.description}
                    </p>
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                      <span className="capitalize font-medium">{model.provider}</span>
                      <span>•</span>
                      <span>{model.maxTokens.toLocaleString()} tokens</span>
                      {model.supportsStreaming && (
                        <>
                          <span>•</span>
                          <span className="flex items-center gap-1">
                            <Zap className="h-3 w-3" />
                            Streaming
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </SelectItem>
            ))}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
}
