import { LucideIcon } from "lucide-react";

// Core career exploration types
export interface CareerMetadata {
  name: string;
  field: string;
  interests: string[];
  experience: string;
  goals: string;
}

export interface CareerPath {
  id: string;
  jobTitle: string;
  jobDescription: string;
  timeline: string;
  salary: string;
  difficulty: 'Low' | 'Medium' | 'High';
  workRequired?: string;
  aboutTheRole?: string;
  whyItsGoodFit?: string[];
  roadmap?: CareerRoadmapStep[];
  skills?: string[];
  certifications?: string[];
  connectPosition?: 'top' | 'bottom';
}

export interface CareerRoadmapStep {
  timeframe: string;
  description: string;
  tasks: string[];
  resources?: string[];
}

export interface CareerAnalysis {
  careerPaths: CareerPath[];
  overallAnalysis: string;
  recommendations: string[];
  nextSteps: string[];
  generatedAt: Date;
}

export interface ResumeData {
  rawText: string;
  extractedInfo: {
    skills: string[];
    experience: string[];
    education: string[];
    certifications: string[];
  };
  fileName: string;
  uploadedAt: Date;
}

// UI and state types
export interface CareerExplorationState {
  currentStep: 'input' | 'processing' | 'visualization' | 'export';
  resumeData: ResumeData | null;
  additionalContext: string;
  selectedModel: string;
  careerAnalysis: CareerAnalysis | null;
  isGenerating: boolean;
  error: string | null;
  visualizationNodes: CareerVisualizationNode[];
  visualizationEdges: CareerVisualizationEdge[];
}

export interface CareerVisualizationNode {
  id: string;
  type: 'center' | 'career';
  position: { x: number; y: number };
  data: {
    label?: string;
    career?: CareerPath;
  };
  style?: Record<string, any>;
}

export interface CareerVisualizationEdge {
  id: string;
  source: string;
  target: string;
  animated?: boolean;
  style?: Record<string, any>;
}

// AI generation types
export interface AIModelOption {
  id: string;
  name: string;
  provider: 'openrouter' | 'gemini';
  capabilities: string[];
  maxTokens: number;
  description?: string;
  isRecommended?: boolean;
}

// Gemini-specific types
export interface GeminiModelConfig {
  model: string;
  maxOutputTokens: number;
  temperature: number;
  topP?: number;
  topK?: number;
}

export interface GeminiCareerRequest {
  resumeText: string;
  additionalContext: string;
  model: string;
  config?: GeminiModelConfig;
}

export interface GeminiCareerResponse {
  careers: CareerPath[];
  analysis: string;
  confidence: number;
  processingTime: number;
  model: string;
}

export interface CareerGenerationRequest {
  resumeText: string;
  additionalContext: string;
  model: string;
  preferences?: {
    industries?: string[];
    workStyle?: string;
    salaryRange?: string;
    location?: string;
  };
}

export interface CareerGenerationResponse {
  careers: CareerPath[];
  analysis: string;
  confidence: number;
  processingTime: number;
}

// Export types
export interface CareerExportOptions {
  format: 'pdf' | 'docx' | 'json';
  includeRoadmaps: boolean;
  includeAnalysis: boolean;
  includeVisualization: boolean;
}

export interface CareerExportData {
  metadata: CareerMetadata;
  analysis: CareerAnalysis;
  exportedAt: Date;
  format: string;
}

// Database types
export interface CareerExploration {
  id: string;
  user_id: string;
  title: string;
  metadata: CareerMetadata;
  resume_data: ResumeData;
  career_analysis: CareerAnalysis;
  ai_model: string;
  status: 'draft' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

// Component props
export interface CareerExplorerProps {
  className?: string;
}

export interface ResumeUploadFormProps {
  onResumeUpload: (file: File) => void;
  onContextChange: (context: string) => void;
  onModelChange: (model: string) => void;
  onGenerate: () => void;
  isLoading: boolean;
  error: string | null;
  resumeData: ResumeData | null;
  additionalContext: string;
  selectedModel: string;
}

export interface CareerVisualizationProps {
  careerPaths: CareerPath[];
  onCareerSelect: (career: CareerPath) => void;
  className?: string;
}



export interface CareerDetailModalProps {
  career: CareerPath | null;
  isOpen: boolean;
  onClose: () => void;
  onExport: (career: CareerPath, format: string) => void;
}

export interface CareerHistoryPanelProps {
  onLoadExploration: (exploration: CareerExploration) => void;
  onCreateNew: () => void;
}

// Utility types
export type DifficultyLevel = 'Low' | 'Medium' | 'High';
export type CareerField = 'Technology' | 'Healthcare' | 'Finance' | 'Education' | 'Marketing' | 'Design' | 'Other';
export type ExportFormat = 'pdf' | 'docx' | 'json';
