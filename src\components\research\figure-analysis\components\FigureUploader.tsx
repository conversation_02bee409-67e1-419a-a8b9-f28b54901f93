/**
 * Figure Uploader Component
 * Advanced drag-and-drop file upload with preview and validation
 */

import React, { useState, useCallback, useRef } from 'react';
import { Upload, X, FileImage, AlertCircle, CheckCircle, Eye, Trash2 } from 'lucide-react';
import { FigureFile, FigureMetadata } from '../types';
import { SUPPORTED_FILE_TYPES, ACCEPTED_EXTENSIONS, ERROR_MESSAGES, UI_CONFIG } from '../constants';

interface FigureUploaderProps {
  onFilesUploaded: (files: FigureFile[]) => void;
  onFileRemoved: (fileId: string) => void;
  uploadedFiles: FigureFile[];
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
}

export const FigureUploader: React.FC<FigureUploaderProps> = ({
  onFilesUploaded,
  onFileRemoved,
  uploadedFiles,
  maxFiles = UI_CONFIG.MAX_UPLOAD_FILES,
  disabled = false,
  className = '',
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * Validate file type and size
   */
  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file type
    if (!SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES]) {
      return { valid: false, error: `${ERROR_MESSAGES.UNSUPPORTED_FORMAT}: ${file.type}` };
    }

    // Check file size
    const maxSize = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES].maxSize;
    if (file.size > maxSize) {
      return { valid: false, error: `${ERROR_MESSAGES.FILE_TOO_LARGE}: ${(file.size / 1024 / 1024).toFixed(1)}MB` };
    }

    return { valid: true };
  };

  /**
   * Extract metadata from file
   */
  const extractMetadata = async (file: File): Promise<FigureMetadata> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.width,
            height: img.height,
            fileFormat: file.type.split('/')[1],
            hasText: false, // Will be determined by AI analysis
          });
        };
        img.onerror = () => {
          resolve({
            width: 0,
            height: 0,
            fileFormat: file.type.split('/')[1],
            hasText: false,
          });
        };
        img.src = URL.createObjectURL(file);
      } else {
        // For non-image files (PDF, SVG)
        resolve({
          width: 0,
          height: 0,
          fileFormat: file.type.split('/')[1] || file.name.split('.').pop() || 'unknown',
          hasText: true,
        });
      }
    });
  };

  /**
   * Convert file to base64
   */
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  /**
   * Process uploaded files
   */
  const processFiles = async (files: FileList | File[]) => {
    setIsProcessing(true);
    setErrors([]);

    const fileArray = Array.from(files);
    const newErrors: string[] = [];
    const validFiles: FigureFile[] = [];

    // Check total file limit
    if (uploadedFiles.length + fileArray.length > maxFiles) {
      newErrors.push(`Maximum ${maxFiles} files allowed`);
      setErrors(newErrors);
      setIsProcessing(false);
      return;
    }

    for (const file of fileArray) {
      try {
        // Validate file
        const validation = validateFile(file);
        if (!validation.valid) {
          newErrors.push(`${file.name}: ${validation.error}`);
          continue;
        }

        // Extract metadata
        const metadata = await extractMetadata(file);

        // Convert to base64
        const base64Data = await fileToBase64(file);

        // Create figure file object
        const figureFile: FigureFile = {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: file.name,
          type: file.type,
          size: file.size,
          data: base64Data,
          url: URL.createObjectURL(file),
          uploadedAt: new Date(),
          metadata,
        };

        validFiles.push(figureFile);
      } catch (error) {
        newErrors.push(`${file.name}: ${ERROR_MESSAGES.UPLOAD_FAILED}`);
      }
    }

    if (newErrors.length > 0) {
      setErrors(newErrors);
    }

    if (validFiles.length > 0) {
      onFilesUploaded(validFiles);
    }

    setIsProcessing(false);
  };

  /**
   * Handle drag events
   */
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFiles(files);
    }
  }, [disabled]);

  /**
   * Handle file input change
   */
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow re-uploading the same file
    e.target.value = '';
  };

  /**
   * Open file dialog
   */
  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Get file type icon
   */
  const getFileTypeIcon = (type: string) => {
    if (type.startsWith('image/')) {
      return <FileImage className="w-8 h-8 text-blue-500" />;
    }
    return <FileImage className="w-8 h-8 text-gray-500" />;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isProcessing ? 'pointer-events-none' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={ACCEPTED_EXTENSIONS.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        <div className="space-y-4">
          <div className="flex justify-center">
            <Upload className={`w-12 h-12 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
          </div>

          <div>
            <p className="text-lg font-medium text-gray-900">
              {isProcessing ? 'Processing files...' : 'Upload figures for analysis'}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Drag and drop files here, or click to browse
            </p>
          </div>

          <div className="text-xs text-gray-400">
            <p>Supported formats: PNG, JPG, PDF, SVG</p>
            <p>Maximum file size: 20MB | Maximum files: {maxFiles}</p>
            <p>Current files: {uploadedFiles.length}/{maxFiles}</p>
          </div>
        </div>

        {isProcessing && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-sm text-gray-600">Processing...</span>
            </div>
          </div>
        )}
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-red-800">Upload Errors:</p>
              {errors.map((error, index) => (
                <p key={index} className="text-sm text-red-700">{error}</p>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Uploaded Files Preview */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Uploaded Figures ({uploadedFiles.length})
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getFileTypeIcon(file.type)}
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onFileRemoved(file.id);
                    }}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                    title="Remove file"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Image Preview */}
                {file.type.startsWith('image/') && (
                  <div className="mb-3">
                    <img
                      src={file.url}
                      alt={file.name}
                      className="w-full h-32 object-cover rounded border"
                    />
                  </div>
                )}

                {/* File Metadata */}
                {file.metadata && (
                  <div className="text-xs text-gray-500 space-y-1">
                    {file.metadata.width > 0 && (
                      <p>Dimensions: {file.metadata.width} × {file.metadata.height}</p>
                    )}
                    <p>Format: {file.metadata.fileFormat.toUpperCase()}</p>
                    <p>Uploaded: {file.uploadedAt.toLocaleTimeString()}</p>
                  </div>
                )}

                {/* Status Indicator */}
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">Ready for analysis</span>
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(file.url, '_blank');
                    }}
                    className="text-blue-500 hover:text-blue-700 transition-colors"
                    title="View full size"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
