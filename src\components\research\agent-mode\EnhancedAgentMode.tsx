/**
 * Enhanced Agent Mode Component with Google Gemini 2.5 Pro Integration
 * Provides targeted, context-aware document editing with real-time progress tracking
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Wand2,
  Brain,
  CheckCircle,
  AlertCircle,
  Loader2,
  Lightbulb,
  Search,
  BarChart3,
  Edit,
  Eye,
  Shield,
  FileText,
  Play,
  Pause,
  RotateCcw,
  ThumbsUp,
  ThumbsDown,
  Clock,
  Zap,
  Target,
  Layers
} from 'lucide-react';
import { geminiAgentService, TargetedEditResult, EditInstruction } from './GeminiAgentService';
import { openRouterAgentService, OPENROUTER_MODELS, OpenRouterModel } from './OpenRouterAgentService';
import { GeminiChangesPreview } from './GeminiChangesPreview';
import { toast } from 'sonner';

interface EnhancedAgentModeProps {
  documentContent: string;
  onAgentRequest: (prompt: string, options: any) => Promise<any>;
  isLoading?: boolean;
  lastResult?: any;
  onPreviewChanges?: (changes: any[]) => void;
  onApplyChanges?: (changeIds: string[]) => void;
  onRejectChanges?: (changeIds: string[]) => void;
}

interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  message: string;
  details?: string;
  executionTime?: number;
}

export function EnhancedAgentMode({
  documentContent,
  onAgentRequest,
  isLoading = false,
  lastResult,
  onPreviewChanges,
  onApplyChanges,
  onRejectChanges
}: EnhancedAgentModeProps) {
  const [prompt, setPrompt] = useState('');
  const [editMode, setEditMode] = useState<'conservative' | 'moderate' | 'aggressive'>('moderate');
  const [showExamples, setShowExamples] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([]);
  const [currentResult, setCurrentResult] = useState<TargetedEditResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  // Advanced options
  const [maxSections, setMaxSections] = useState(5);
  const [preserveFormatting, setPreserveFormatting] = useState(true);
  const [debugMode, setDebugMode] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<'gemini' | 'openrouter'>('gemini');
  const [selectedModel, setSelectedModel] = useState<string>('anthropic/claude-3.5-sonnet');

  // Processing step icons mapping
  const stepIcons = {
    'analyze': Search,
    'identify': Target,
    'edit': Edit,
    'validate': Shield,
    'apply': CheckCircle
  };

  // Example prompts for targeted editing (improved for better success)
  const examplePrompts = [
    "Fix grammar and spelling errors in the methodology section",
    "Improve the clarity of the introduction",
    "Add more academic citations to support key claims",
    "Make the conclusion more concise and impactful",
    "Enhance the writing style throughout the document",
    "Improve transitions between paragraphs",
    "Strengthen the academic tone in all sections",
    "Fix formatting and punctuation issues"
  ];

  const editModeDescriptions = {
    conservative: "Minimal changes, preserve original style and structure",
    moderate: "Balanced improvements with careful structural preservation",
    aggressive: "Comprehensive improvements, restructure as needed for clarity"
  };

  // Initialize processing steps when starting
  const initializeProcessingSteps = () => {
    const steps: ProcessingStep[] = [
      { id: 'analyze', name: 'Document Analysis', status: 'pending', progress: 0, message: 'Analyzing document structure...' },
      { id: 'identify', name: 'Section Identification', status: 'pending', progress: 0, message: 'Identifying relevant sections...' },
      { id: 'edit', name: 'Targeted Editing', status: 'pending', progress: 0, message: 'Generating targeted improvements...' },
      { id: 'validate', name: 'Quality Validation', status: 'pending', progress: 0, message: 'Validating changes...' },
      { id: 'apply', name: 'Change Integration', status: 'pending', progress: 0, message: 'Preparing changes for review...' }
    ];
    setProcessingSteps(steps);
  };

  // Update processing step
  const updateProcessingStep = (stepId: string, updates: Partial<ProcessingStep>) => {
    setProcessingSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const handleSubmit = async () => {
    if (!prompt.trim() || !documentContent.trim()) {
      toast.error('Please provide both a request and document content');
      return;
    }

    if (selectedProvider === 'gemini' && !geminiAgentService.hasValidApiKey()) {
      toast.error('Google Gemini API key not configured. Please set VITE_GEMINI_API_KEY in your environment.');
      return;
    }

    if (selectedProvider === 'openrouter' && !openRouterAgentService.hasValidApiKey()) {
      toast.error('OpenRouter API key not configured. Please set VITE_OPENROUTER_API_KEY in your environment.');
      return;
    }

    try {
      setIsProcessing(true);
      setCurrentResult(null);
      initializeProcessingSteps();

      // Step 1: Document Analysis
      updateProcessingStep('analyze', { status: 'running', progress: 20, message: 'Analyzing document structure...' });
      await new Promise(resolve => setTimeout(resolve, 500)); // Visual feedback

      // Step 2: Section Identification
      updateProcessingStep('analyze', { status: 'completed', progress: 100 });
      updateProcessingStep('identify', { status: 'running', progress: 40, message: 'Identifying sections relevant to your request...' });
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 3: Execute targeted editing with selected provider
      updateProcessingStep('identify', { status: 'completed', progress: 100 });

      let result: TargetedEditResult;

      if (selectedProvider === 'openrouter') {
        updateProcessingStep('edit', { status: 'running', progress: 60, message: `Generating targeted improvements with ${OPENROUTER_MODELS.find(m => m.id === selectedModel)?.name || selectedModel}...` });

        result = await openRouterAgentService.executeTargetedEdit(prompt.trim(), documentContent, {
          editMode,
          maxSections,
          preserveFormatting,
          model: selectedModel
        });
      } else {
        updateProcessingStep('edit', { status: 'running', progress: 60, message: 'Generating targeted improvements with Gemini 2.5 Pro...' });

        result = await geminiAgentService.executeTargetedEdit(prompt.trim(), documentContent, {
          editMode,
          maxSections,
          preserveFormatting
        });
      }

      // Step 4: Validation
      updateProcessingStep('edit', { status: 'completed', progress: 100 });
      updateProcessingStep('validate', { status: 'running', progress: 80, message: 'Validating changes...' });
      await new Promise(resolve => setTimeout(resolve, 300));

      // Step 5: Prepare for application
      updateProcessingStep('validate', { status: 'completed', progress: 100 });
      updateProcessingStep('apply', { status: 'running', progress: 90, message: 'Preparing changes for review...' });
      await new Promise(resolve => setTimeout(resolve, 200));

      updateProcessingStep('apply', { status: 'completed', progress: 100, message: 'Changes ready for review!' });

      setCurrentResult(result);

      if (result.success && result.edits.length > 0) {
        toast.success(`✅ Found ${result.edits.length} improvement(s)!`, {
          description: "Click 'Review Changes' to see what can be improved."
        });
      } else if (result.success && result.edits.length === 0) {
        toast.info('✨ Your document looks great!', {
          description: "No improvements were needed based on your request."
        });
      } else {
        toast.error('❌ Failed to analyze your document', {
          description: result.error || 'Please try again or switch to a different AI model.'
        });
      }

    } catch (error: any) {
      console.error('Enhanced agent mode request failed:', error);
      updateProcessingStep('edit', { status: 'failed', message: error.message || 'Processing failed' });
      toast.error(error.message || 'Failed to process your request');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExampleClick = (example: string) => {
    setPrompt(example);
    setShowExamples(false);
  };

  const handlePreviewChanges = () => {
    if (currentResult?.edits && currentResult.edits.length > 0) {
      console.log('🔍 Opening preview dialog for changes:', currentResult.edits);
      setShowPreviewDialog(true);
    } else {
      toast.warning('No changes available to preview');
    }
  };

  const handleApplyChanges = (editsToApply?: EditInstruction[]) => {
    // Use current result edits if no specific edits provided
    const edits = editsToApply || currentResult?.edits || [];

    console.log('✅ Applying changes:', edits);

    // Validate edits array
    if (!edits || !Array.isArray(edits) || edits.length === 0) {
      toast.error('No valid changes to apply');
      return;
    }

    // Try multiple approaches to apply changes
    const success = applyEditsToDocument(edits);

    if (success) {
      toast.success(`Applied ${edits.length} improvement(s) to your document!`);
      setCurrentResult(null);
      setShowPreviewDialog(false);

      // Trigger a custom event to notify the editor
      window.dispatchEvent(new CustomEvent('documentUpdated', {
        detail: {
          type: 'ai-improvements',
          count: edits.length
        }
      }));
    } else {
      // Fallback: try using the change tracking system
      console.log('🔄 Trying fallback approach with change tracking...');
      applyChangesViaChangeTracking(edits);
    }
  };

  const handleRejectChanges = () => {
    console.log('❌ Rejecting all changes');

    toast.info('All changes rejected');
    setCurrentResult(null);
    setShowPreviewDialog(false);
  };

  // Helper function to apply edits to the document
  const applyEditsToDocument = (edits: EditInstruction[]) => {
    console.log('📝 Applying edits to document:', edits);

    try {
      // Validate input
      if (!edits || !Array.isArray(edits) || edits.length === 0) {
        console.error('❌ Invalid edits array:', edits);
        return false;
      }

      // Get the current document content
      let updatedContent = documentContent;
      let appliedCount = 0;

      // Sort edits by original content length (longest first) to avoid replacement conflicts
      const sortedEdits = [...edits].sort((a, b) => b.originalContent.length - a.originalContent.length);

      // Apply each edit by replacing the original content with the edited content
      sortedEdits.forEach((edit, index) => {
        console.log(`Applying edit ${index + 1}/${sortedEdits.length}:`, {
          sectionId: edit.sectionId,
          originalLength: edit.originalContent.length,
          editedLength: edit.editedContent.length,
          original: edit.originalContent.substring(0, 100) + '...',
          edited: edit.editedContent.substring(0, 100) + '...'
        });

        // Try multiple replacement strategies
        let replaced = false;

        // Strategy 1: Direct replacement
        if (updatedContent.includes(edit.originalContent)) {
          updatedContent = updatedContent.replace(edit.originalContent, edit.editedContent);
          replaced = true;
          console.log(`✅ Applied edit ${index + 1} using direct replacement`);
        }
        // Strategy 2: Normalize whitespace and try again
        else {
          const normalizedOriginal = edit.originalContent.replace(/\s+/g, ' ').trim();
          const normalizedContent = updatedContent.replace(/\s+/g, ' ');

          if (normalizedContent.includes(normalizedOriginal)) {
            // Find the original text with its actual whitespace
            const regex = new RegExp(normalizedOriginal.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
            updatedContent = updatedContent.replace(regex, edit.editedContent);
            replaced = true;
            console.log(`✅ Applied edit ${index + 1} using normalized replacement`);
          }
        }
        // Strategy 3: Try partial matching for HTML content
        if (!replaced) {
          // Remove HTML tags and try matching just the text content
          const stripHtml = (str: string) => str.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
          const originalText = stripHtml(edit.originalContent);
          const contentText = stripHtml(updatedContent);

          if (originalText.length > 10 && contentText.includes(originalText)) {
            // Find the HTML section that contains this text
            const lines = updatedContent.split('\n');
            for (let i = 0; i < lines.length; i++) {
              if (stripHtml(lines[i]).includes(originalText)) {
                lines[i] = edit.editedContent;
                updatedContent = lines.join('\n');
                replaced = true;
                console.log(`✅ Applied edit ${index + 1} using HTML text matching`);
                break;
              }
            }
          }
        }

        if (replaced) {
          appliedCount++;
        } else {
          console.warn(`⚠️ Could not apply edit ${index + 1} - content not found in document`);
        }
      });

      console.log(`📊 Applied ${appliedCount}/${edits.length} edits successfully`);

      // Only update if we actually made changes
      if (appliedCount > 0) {
        console.log('📤 Sending updated content to parent component');
        console.log('📝 Updated content length:', updatedContent.length);
        console.log('📝 Original content length:', documentContent.length);

        // Multiple strategies to update the editor content
        console.log('🔄 Attempting to update editor with', updatedContent.length, 'characters');

        // Strategy 1: Custom event for the editor
        const event = new CustomEvent('geminiContentUpdate', {
          detail: { content: updatedContent }
        });
        console.log('🚀 Dispatching geminiContentUpdate event');
        window.dispatchEvent(event);

        // Strategy 2: Try TinyMCE if available
        if (typeof window !== 'undefined' && (window as any).tinymce) {
          const editor = (window as any).tinymce.activeEditor || (window as any).tinymce.get(0);
          if (editor) {
            console.log('🔄 Updating TinyMCE editor...');
            editor.setContent(updatedContent);
            editor.save(); // Save to textarea
          }
        }

        // Strategy 3: Find and update various editor elements
        const possibleEditors = [
          document.querySelector('[contenteditable="true"]'),
          document.querySelector('#editor-content'),
          document.querySelector('.editor-content'),
          (document.querySelector('.tox-edit-area iframe') as HTMLIFrameElement)?.contentDocument?.body,
          document.querySelector('textarea[name="content"]'),
          document.querySelector('#content'),
          document.querySelector('.ql-editor'), // Quill editor
          document.querySelector('.CodeMirror'), // CodeMirror
        ].filter(Boolean);

        possibleEditors.forEach((element, index) => {
          if (element) {
            console.log(`🔄 Updating editor element ${index + 1}...`);
            if ((element as HTMLElement).innerHTML !== undefined) {
              (element as HTMLElement).innerHTML = updatedContent;
            } else if ((element as HTMLTextAreaElement).value !== undefined) {
              (element as HTMLTextAreaElement).value = updatedContent;
            }
          }
        });

        // Strategy 4: Custom update function if available
        if (typeof window !== 'undefined' && (window as any).updateEditorContent) {
          console.log('🔄 Trying direct editor update...');
          (window as any).updateEditorContent(updatedContent);
        }

        // Strategy 5: Dispatch additional events
        window.dispatchEvent(new CustomEvent('documentUpdated', {
          detail: {
            content: updatedContent,
            type: 'ai-improvements',
            count: appliedCount
          }
        }));

        // Strategy 6: Try to trigger React state updates
        const reactFiberKey = Object.keys(document.body).find(key => key.startsWith('__reactFiber'));
        if (reactFiberKey) {
          console.log('🔄 Attempting React state update...');
          // Trigger a custom event that React components can listen to
          window.dispatchEvent(new CustomEvent('forceEditorUpdate', {
            detail: { content: updatedContent }
          }));
        }

        toast.success(`Applied ${appliedCount} change(s) to the document`);
        return true;
      } else {
        console.warn('⚠️ No changes could be applied - content may have been modified');
        return false;
      }

    } catch (error) {
      console.error('❌ Failed to apply edits:', error);
      toast.error('Failed to apply changes to document');
      return false;
    }
  };

  // Fallback method using change tracking system
  const applyChangesViaChangeTracking = (edits: EditInstruction[]) => {
    console.log('🔄 Applying changes via change tracking system...');

    try {
      // Validate input
      if (!edits || !Array.isArray(edits) || edits.length === 0) {
        console.error('❌ Invalid edits array for change tracking:', edits);
        toast.error('No valid changes to apply via change tracking');
        return;
      }

      // Convert edits to change tracking format
      const changes = edits.map((edit, index) => ({
        id: `gemini_edit_${index}`,
        type: edit.changeType,
        originalText: edit.originalContent,
        newText: edit.editedContent,
        reasoning: edit.reasoning,
        sectionId: edit.sectionId
      }));

      // Use the existing change tracking system
      if (onApplyChanges) {
        const changeIds = changes.map(c => c.id);
        onApplyChanges(changeIds);
        toast.success(`Applied ${edits.length} change(s) via change tracking`);
        setCurrentResult(null);
        setShowPreviewDialog(false);
      } else {
        console.warn('⚠️ No onApplyChanges handler available');
        toast.error('Unable to apply changes - no change handler available');
      }

    } catch (error) {
      console.error('❌ Failed to apply changes via change tracking:', error);
      toast.error('Failed to apply changes via change tracking');
    }
  };

  // Clear results when starting new request
  const handleClearResults = () => {
    setCurrentResult(null);
    setProcessingSteps([]);
  };

  // Test API connection
  const handleTestApi = async () => {
    try {
      if (selectedProvider === 'gemini') {
        toast.info('Testing Gemini API connection...');
        const success = await geminiAgentService.testApiConnection();
        if (success) {
          toast.success('Gemini API connection successful!');
        } else {
          toast.error('Gemini API connection failed - check console for details');
        }
      } else {
        toast.info('Testing OpenRouter API connection...');
        const success = await openRouterAgentService.testApiConnection();
        if (success) {
          toast.success('OpenRouter API connection successful!');
        } else {
          toast.error('OpenRouter API connection failed - check console for details');
        }
      }
    } catch (error) {
      toast.error('API test failed - check console for details');
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header */}
      <div className="flex-shrink-0 mb-4">
        <div className="flex items-center gap-2 mb-2">
          <Brain className="h-5 w-5 text-purple-600" />
          <h3 className="font-semibold">AI Writing Assistant</h3>
          <Badge variant="secondary" className="text-xs ml-auto">
            {selectedProvider === 'gemini' ? 'Gemini 2.5 Flash' : OPENROUTER_MODELS.find(m => m.id === selectedModel)?.name || 'AI Model'}
          </Badge>
        </div>
        <p className="text-sm text-gray-600">
          Improve your writing with targeted AI suggestions and edits
        </p>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto space-y-4 pr-2">

        {/* Main Input Card */}
        <Card className="border-purple-200 shadow-sm">
          <CardContent className="p-4">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                What would you like me to do?
              </label>
              <Textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Tell me what you want to improve...

Examples:
• Remove all references and citations
• Fix grammar and spelling errors
• Improve clarity and readability
• Make the writing more concise
• Enhance the academic tone"
                className="min-h-[80px] resize-none text-sm"
                disabled={isProcessing}
              />
            </div>

            {/* Example Prompts */}
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowExamples(!showExamples)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                {showExamples ? 'Hide' : 'Show'} example prompts
              </Button>
              
              {showExamples && (
                <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                  {examplePrompts.map((example, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      className="h-auto p-2 text-xs text-left justify-start w-full"
                      onClick={() => handleExampleClick(example)}
                    >
                      "{example}"
                    </Button>
                  ))}
                </div>
              )}
            </div>

            {/* Edit Mode Selection - Compact */}
            <div>
              <label className="text-sm font-medium mb-2 block">Edit Mode</label>
              <div className="grid grid-cols-3 gap-1">
                {Object.entries(editModeDescriptions).map(([mode]) => (
                  <Button
                    key={mode}
                    variant={editMode === mode ? "default" : "outline"}
                    size="sm"
                    className="h-8 text-xs px-2"
                    onClick={() => setEditMode(mode as any)}
                    disabled={isProcessing}
                  >
                    <span className="capitalize">{mode}</span>
                  </Button>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {editModeDescriptions[editMode]}
              </p>
            </div>

            {/* Advanced Options */}
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                <Zap className="h-3 w-3 mr-1" />
                {showAdvancedOptions ? 'Hide' : 'Show'} advanced options
              </Button>
              
              {showAdvancedOptions && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg space-y-3">
                  {/* AI Provider Selection */}
                  <div className="space-y-3">
                    <div>
                      <label className="text-xs font-medium mb-2 block">AI Provider</label>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant={selectedProvider === 'gemini' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedProvider('gemini')}
                          disabled={isProcessing}
                          className="text-xs h-8"
                        >
                          Gemini 2.5 Flash
                        </Button>
                        <Button
                          variant={selectedProvider === 'openrouter' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedProvider('openrouter')}
                          disabled={isProcessing}
                          className="text-xs h-8"
                        >
                          OpenRouter Models
                        </Button>
                      </div>
                    </div>

                    {/* OpenRouter Model Selection */}
                    {selectedProvider === 'openrouter' && (
                      <div>
                        <label className="text-xs font-medium mb-1 block">Model</label>
                        <select
                          value={selectedModel}
                          onChange={(e) => setSelectedModel(e.target.value)}
                          disabled={isProcessing}
                          className="w-full px-2 py-1 text-xs border rounded"
                        >
                          {OPENROUTER_MODELS.map((model) => (
                            <option key={model.id} value={model.id}>
                              {model.name} - ${model.costPer1kTokens}/1k tokens
                            </option>
                          ))}
                        </select>
                        <div className="text-xs text-gray-500 mt-1">
                          {OPENROUTER_MODELS.find(m => m.id === selectedModel)?.description}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-xs font-medium mb-1 block">Max Sections</label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={maxSections}
                          onChange={(e) => setMaxSections(parseInt(e.target.value) || 5)}
                          className="w-full px-2 py-1 text-xs border rounded"
                          disabled={isProcessing}
                        />
                      </div>
                      <div className="flex flex-col gap-1 pt-4">
                        <label className="flex items-center gap-2 text-xs">
                          <input
                            type="checkbox"
                            checked={preserveFormatting}
                            onChange={(e) => setPreserveFormatting(e.target.checked)}
                            disabled={isProcessing}
                          />
                          Preserve Formatting
                        </label>
                        <label className="flex items-center gap-2 text-xs">
                          <input
                            type="checkbox"
                            checked={debugMode}
                            onChange={(e) => setDebugMode(e.target.checked)}
                            disabled={isProcessing}
                          />
                          Debug Mode
                        </label>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTestApi}
                      disabled={isProcessing}
                      className="text-xs h-7 w-full"
                    >
                      Test {selectedProvider === 'gemini' ? 'Gemini' : 'OpenRouter'} API
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Main Action Button - Always Visible */}
            <div className="pt-2 border-t">
              <Button
                onClick={handleSubmit}
                disabled={!prompt.trim() || !documentContent.trim() || isProcessing}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing & Improving...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Improve My Writing
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Real-time Processing Progress */}
      {isProcessing && processingSteps.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              <Loader2 className="h-4 w-4 animate-spin" />
              Gemini 2.5 Pro Processing Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {processingSteps.map((step, index) => {
              const IconComponent = stepIcons[step.id as keyof typeof stepIcons] || Zap;

              return (
                <div key={step.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4" />
                      <span className="text-sm font-medium">{step.name}</span>
                      <Badge
                        variant={
                          step.status === 'completed' ? 'default' :
                          step.status === 'running' ? 'secondary' :
                          step.status === 'failed' ? 'destructive' : 'outline'
                        }
                        className="text-xs"
                      >
                        {step.status}
                      </Badge>
                    </div>
                    {step.status === 'completed' && step.executionTime && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        {step.executionTime}ms
                      </div>
                    )}
                  </div>

                  <Progress value={step.progress} className="w-full h-2" />

                  <div className="text-xs text-gray-600">
                    {step.message}
                  </div>

                  {step.details && (
                    <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border-l-2 border-blue-300">
                      <strong>Details:</strong> {step.details}
                    </div>
                  )}

                  {index < processingSteps.length - 1 && <Separator />}
                </div>
              );
            })}
          </CardContent>
        </Card>
      )}

      {/* Results and Preview */}
      {currentResult && !isProcessing && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              {currentResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              AI Suggestions
              {currentResult.edits.length > 0 && (
                <Badge variant="secondary" className="ml-auto">
                  {currentResult.edits.length} improvements found
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm">
                <strong>Summary:</strong> {currentResult.summary}
              </div>

              {currentResult.reasoning && (
                <div className="text-sm">
                  <strong>AI Analysis:</strong> {currentResult.reasoning}
                </div>
              )}

              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>Confidence: {(currentResult.confidence * 100).toFixed(0)}%</span>
                <span>Sections Analyzed: {currentResult.sectionsAnalyzed}</span>
                <span>Sections Modified: {currentResult.sectionsModified}</span>
              </div>

              {currentResult.edits.length > 0 && (
                <div className="space-y-4">
                  <Separator />

                  {/* Changes Summary */}
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h4 className="font-medium text-green-900 mb-3 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      {currentResult.edits.length} Improvement(s) Ready
                    </h4>
                    <div className="space-y-2 text-sm text-green-800">
                      {currentResult.edits.slice(0, 3).map((edit, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <span className="text-green-600 font-medium">{index + 1}.</span>
                          <span className="flex-1">
                            {edit.originalContent.length > edit.editedContent.length ?
                              `Removed content: "${edit.originalContent.substring(0, 60)}..."` :
                              `Improved text: "${edit.originalContent.substring(0, 60)}..."`
                            }
                          </span>
                        </div>
                      ))}
                      {currentResult.edits.length > 3 && (
                        <div className="text-green-600 font-medium">
                          + {currentResult.edits.length - 3} more improvements
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-1 gap-3">
                    <Button
                      onClick={handlePreviewChanges}
                      variant="outline"
                      size="lg"
                      className="w-full flex items-center justify-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      Review Changes
                    </Button>

                    <Button
                      onClick={() => handleApplyChanges()}
                      variant="default"
                      size="lg"
                      className="w-full flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700"
                    >
                      <ThumbsUp className="h-4 w-4" />
                      Apply All Changes
                    </Button>
                  </div>
                </div>
              )}

              {currentResult.error && (
                <div className="space-y-2">
                  <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200">
                    <strong>❌ Error:</strong> {currentResult.error}
                  </div>

                  {/* Debug Information */}
                  <details className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                    <summary className="cursor-pointer font-medium">🔍 Debug Information</summary>
                    <div className="mt-2 space-y-1">
                      <div><strong>Sections Analyzed:</strong> {currentResult.sectionsAnalyzed}</div>
                      <div><strong>Sections Modified:</strong> {currentResult.sectionsModified}</div>
                      <div><strong>Confidence:</strong> {(currentResult.confidence * 100).toFixed(0)}%</div>
                      <div><strong>Edits Generated:</strong> {currentResult.edits.length}</div>
                      <div><strong>Reasoning:</strong> {currentResult.reasoning || 'No reasoning provided'}</div>
                    </div>
                  </details>
                </div>
              )}

              {/* Show debug info even when successful but no changes */}
              {currentResult.success && currentResult.edits.length === 0 && (
                <div className="space-y-2">
                  <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded border border-amber-200">
                    <strong>⚠️ No Changes Generated</strong>
                    <p className="mt-1">Gemini 2.5 Pro analyzed your document but didn't generate any changes. This might mean:</p>
                    <ul className="mt-1 ml-4 list-disc text-xs">
                      <li>The content already meets the requirements</li>
                      <li>The request was too vague or unclear</li>
                      <li>No relevant sections were found to modify</li>
                      <li>Try being more specific about what you want to improve</li>
                    </ul>
                  </div>

                  <details className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                    <summary className="cursor-pointer font-medium">🔍 Analysis Details</summary>
                    <div className="mt-2 space-y-1">
                      <div><strong>Sections Analyzed:</strong> {currentResult.sectionsAnalyzed}</div>
                      <div><strong>Confidence:</strong> {(currentResult.confidence * 100).toFixed(0)}%</div>
                      <div><strong>Analysis:</strong> {currentResult.reasoning || 'No analysis provided'}</div>
                    </div>
                  </details>

                  <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border border-blue-200">
                    <strong>💡 Troubleshooting Tips:</strong>
                    <ul className="mt-1 ml-4 list-disc">
                      <li>Enable "Debug Mode" above and check browser console for detailed logs</li>
                      <li>Try a more specific request (e.g., "remove all citations from the methodology section")</li>
                      <li>Make sure your document has the content you want to modify</li>
                      <li>Check that your Google Gemini API key is working</li>
                    </ul>
                  </div>
                </div>
              )}

              {currentResult.warnings && currentResult.warnings.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-yellow-700">Warnings:</h4>
                  <div className="space-y-1">
                    {currentResult.warnings.map((warning, index) => (
                      <div key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded border-l-2 border-yellow-300">
                        {warning}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}



      {/* Preview Dialog */}
      {showPreviewDialog && currentResult && currentResult.edits.length > 0 && (
        <GeminiChangesPreview
          edits={currentResult.edits}
          onApplyChanges={handleApplyChanges}
          onRejectChanges={handleRejectChanges}
          onClose={() => setShowPreviewDialog(false)}
        />
      )}
      </div>
    </div>
  );
}
