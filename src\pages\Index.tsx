
import { useAuth } from "@/contexts/AuthContext";
import { ResearchDashboard } from "@/components/research/ResearchDashboard";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { GraduationCap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";

const Index = () => {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Add debugging
    console.log("Index - user:", user, "loading:", loading);
    
    // Check if user is authenticated
    const checkAuth = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        console.log("Current session check:", data?.session);
        
        // If no valid session and not in loading state, redirect to login
        if (!data?.session && !loading) {
          console.log("No valid session, redirecting to login");
          navigate("/login");
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        navigate("/login");
      }
    };
    
    // If not authenticated and not loading, redirect to login
    if (!loading && !user) {
      console.log("Redirecting to login - no user and not loading");
      checkAuth();
    }
  }, [user, loading, navigate]);

  // Handle potential loading state issues
  useEffect(() => {
    let timer: number;
    
    // If loading is stuck for too long
    if (loading) {
      console.log("Loading state detected, setting timeout...");
      timer = window.setTimeout(() => {
        console.log("Loading state lasted too long, forcing resolution");
        
        // Check if we can get a session directly from Supabase
        const checkDirectAuth = async () => {
          try {
            const { data } = await supabase.auth.getSession();
            
            if (data?.session) {
              console.log("Found valid session despite loading state");
              // Just reload the page to reset loading state
              window.location.reload();
            } else {
              console.log("No session found after timeout, redirecting to login");
              window.location.href = "/login";
            }
          } catch (error) {
            console.error("Error checking auth during timeout:", error);
            window.location.href = "/login";
          }
        };
        
        checkDirectAuth();
      }, 5000); // 5 seconds timeout for loading state
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [loading]);

  const handleForceSignOut = async () => {
    try {
      // Direct call to supabase to ensure sign out happens regardless of AuthContext state
      await supabase.auth.signOut();
      console.log("Forced sign out successful");
      
      // Clear local storage to remove any persisted auth data
      localStorage.removeItem('supabase.auth.token');
      
      // Force redirect to login page with a page reload to clear all state
      window.location.href = "/login";
    } catch (error) {
      console.error("Force sign out error:", error);
      // Even if error occurs, try to redirect
      window.location.href = "/login";
    }
  };

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20 flex items-center justify-center">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-3 rounded-xl">
              <GraduationCap className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Verbira
            </h1>
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 mb-4">Loading your research dashboard...</p>
          <Button onClick={handleForceSignOut} variant="outline" size="sm">
            Force Sign Out & Go to Login
          </Button>
        </div>
      </div>
    );
  }

  // If user is authenticated, show the research dashboard
  if (user) {
    console.log("User authenticated, showing dashboard");
    return <ResearchDashboard />;
  }

  // This shouldn't happen due to the useEffect redirect, but just in case
  console.log("Fallback case - no user and not loading");
  return null;
};

export default Index;
