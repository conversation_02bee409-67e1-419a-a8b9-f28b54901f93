import React, { useState } from 'react';
import { X, Plus, <PERSON>O<PERSON>, Tag } from "lucide-react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PaperMetadata } from './types';

interface PaperMetadataFormProps {
  metadata: PaperMetadata;
  updateMetadata: (metadata: Partial<PaperMetadata>) => void;
}

export function PaperMetadataForm({ metadata, updateMetadata }: PaperMetadataFormProps) {
  const [keywordInput, setKeywordInput] = useState("");
  const [authorInput, setAuthorInput] = useState("");
  
  const addKeyword = () => {
    if (keywordInput.trim()) {
      const newKeywords = [...metadata.keywords, keywordInput.trim()];
      updateMetadata({ keywords: newKeywords });
      setKeywordInput("");
    }
  };
  
  const removeKeyword = (index: number) => {
    const newKeywords = [...metadata.keywords];
    newKeywords.splice(index, 1);
    updateMetadata({ keywords: newKeywords });
  };
  
  const addAuthor = () => {
    if (authorInput.trim()) {
      const newAuthors = [...metadata.authors, authorInput.trim()];
      updateMetadata({ authors: newAuthors });
      setAuthorInput("");
    }
  };
  
  const removeAuthor = (index: number) => {
    const newAuthors = [...metadata.authors];
    newAuthors.splice(index, 1);
    updateMetadata({ authors: newAuthors });
  };

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm mb-8">
      <CardHeader>
        <CardTitle className="text-2xl flex items-center gap-3">
          <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
          Paper Metadata
        </CardTitle>
        <CardDescription className="text-base">
          Add essential information about your research paper
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Paper Title - Only Required Field */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-bold text-gray-800 flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              Paper Title <span className="text-red-500 ml-1 text-lg">*</span>
            </label>
            <Badge className="bg-red-100 text-red-800 border-red-200">
              Required Field
            </Badge>
          </div>
          <Input
            placeholder="Enter your research paper title..."
            value={metadata.title}
            onChange={(e) => updateMetadata({ title: e.target.value })}
            className="border-2 border-blue-200 focus:border-blue-400 focus:ring-blue-400 rounded-xl bg-white/70 backdrop-blur-sm h-12 text-lg font-medium"
            required
          />
          <p className="text-xs text-blue-600">
            This is the only required field to generate your research paper
          </p>
        </div>
        
        {/* Research Field */}
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-purple-600" />
            Research Field <span className="text-gray-400 text-xs">(optional)</span>
          </label>
          <Input
            placeholder="e.g., Machine Learning, Cognitive Psychology, Materials Science..."
            value={metadata.researchField}
            onChange={(e) => updateMetadata({ researchField: e.target.value })}
            className="border-2 border-gray-200 focus:border-purple-400 focus:ring-purple-400 rounded-xl bg-white/70 backdrop-blur-sm h-12"
          />
        </div>
        
        {/* Keywords */}
        <div className="space-y-4">
          <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            <Tag className="h-4 w-4 text-green-600" />
            Keywords (4-6 recommended)
          </label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter a keyword..."
              value={keywordInput}
              onChange={(e) => setKeywordInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && addKeyword()}
              className="border-2 border-gray-200 focus:border-green-400 focus:ring-green-400 rounded-xl bg-white/70 backdrop-blur-sm flex-1"
            />
            <Button 
              onClick={addKeyword} 
              variant="outline"
              className="border-2 hover:border-green-400 hover:bg-green-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
          
          {metadata.keywords.length > 0 && (
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
              {metadata.keywords.map((keyword, index) => (
                <Badge 
                  key={index} 
                  variant="secondary"
                  className="bg-green-100 text-green-800 hover:bg-green-200 px-3 py-1.5 flex items-center gap-1"
                >
                  {keyword}
                  <button 
                    onClick={() => removeKeyword(index)}
                    className="ml-1 hover:bg-green-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
        
        {/* Authors */}
        <div className="space-y-4">
          <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            <Tag className="h-4 w-4 text-yellow-600" />
            Authors
          </label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter an author name..."
              value={authorInput}
              onChange={(e) => setAuthorInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && addAuthor()}
              className="border-2 border-gray-200 focus:border-yellow-400 focus:ring-yellow-400 rounded-xl bg-white/70 backdrop-blur-sm flex-1"
            />
            <Button 
              onClick={addAuthor} 
              variant="outline"
              className="border-2 hover:border-yellow-400 hover:bg-yellow-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
          
          {metadata.authors.length > 0 && (
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
              {metadata.authors.map((author, index) => (
                <Badge 
                  key={index} 
                  variant="secondary"
                  className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 px-3 py-1.5 flex items-center gap-1"
                >
                  {author}
                  <button 
                    onClick={() => removeAuthor(index)}
                    className="ml-1 hover:bg-yellow-200 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
