import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  File, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  ExternalLink
} from "lucide-react";

export function FileTypeInfo() {
  const supportedFormats = [
    {
      extension: '.txt',
      name: 'Plain Text',
      icon: '📄',
      status: 'full-support',
      description: 'Complete text extraction and analysis',
      recommendation: 'Best format for immediate analysis'
    },
    {
      extension: '.pdf',
      name: 'PDF Documents',
      icon: '📕',
      status: 'full-support',
      description: 'Complete text extraction with page count and metadata',
      recommendation: 'Upload directly - fully supported with automatic text extraction'
    },
    {
      extension: '.docx',
      name: 'Word Documents (Modern)',
      icon: '📘',
      status: 'limited-support',
      description: 'Basic support with placeholder content',
      recommendation: 'Convert to .txt for full analysis or implement mammoth.js'
    },
    {
      extension: '.doc',
      name: 'Word Documents (Legacy)',
      icon: '📙',
      status: 'limited-support',
      description: 'Minimal support due to complex binary format',
      recommendation: 'Convert to .docx or .txt format'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'full-support': return 'bg-green-100 text-green-700 border-green-200';
      case 'limited-support': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'no-support': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'full-support': return <CheckCircle className="h-4 w-4" />;
      case 'limited-support': return <AlertTriangle className="h-4 w-4" />;
      case 'no-support': return <File className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <FileText className="h-5 w-5 text-blue-500" />
          File Format Support
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Current Implementation Status:</strong> This is a demonstration version with basic file handling. 
            For production use, additional libraries are needed for full PDF and Word document text extraction.
          </AlertDescription>
        </Alert>

        {/* Supported Formats Grid */}
        <div className="grid gap-4">
          {supportedFormats.map((format) => (
            <div 
              key={format.extension}
              className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg"
            >
              <div className="text-2xl">{format.icon}</div>
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="font-semibold text-gray-900">
                    {format.name} ({format.extension})
                  </h3>
                  <Badge 
                    variant="outline" 
                    className={`${getStatusColor(format.status)} flex items-center gap-1`}
                  >
                    {getStatusIcon(format.status)}
                    {format.status.replace('-', ' ')}
                  </Badge>
                </div>
                
                <p className="text-gray-600 text-sm mb-2">
                  {format.description}
                </p>
                
                <p className="text-blue-600 text-sm font-medium">
                  💡 {format.recommendation}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Implementation Notes */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900">Implementation Notes</h3>
          
          <div className="space-y-3 text-sm">
            <div className="p-3 bg-green-50 rounded border-l-4 border-green-200">
              <strong className="text-green-900">✅ PDF Support:</strong>
              <p className="text-green-700 mt-1">
                Already implemented with pdf-parse library. Upload PDF files directly!
              </p>
            </div>
            
            <div className="p-3 bg-green-50 rounded border-l-4 border-green-200">
              <strong className="text-green-900">✅ Word Document Support:</strong>
              <p className="text-green-700 mt-1">
                Already implemented with mammoth.js library. Upload .docx files directly!
              </p>
            </div>
            
            <div className="p-3 bg-green-50 rounded border-l-4 border-green-200">
              <strong className="text-green-900">Current Working Solution:</strong>
              <p className="text-green-700 mt-1">
                Convert your documents to .txt format before uploading for immediate full analysis.
              </p>
            </div>
          </div>
        </div>

        {/* Quick Tips */}
        <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
          <h4 className="font-semibold text-amber-900 mb-2">Quick Tips for Best Results</h4>
          <ul className="text-amber-800 text-sm space-y-1">
            <li>• Use .txt files for immediate, comprehensive analysis</li>
            <li>• Keep file sizes under 50MB for optimal performance</li>
            <li>• Ensure documents are in English for best AI analysis</li>
            <li>• Use descriptive filenames to help with organization</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
