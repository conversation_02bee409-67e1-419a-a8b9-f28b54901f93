/**
 * Google Search History Service
 * Handles storage and retrieval of Google search sessions and messages
 */

import { supabase } from '@/lib/supabase';
import {
  GoogleSearchSession,
  GoogleSearchMessage,
  GoogleSearchSource,
  GoogleCitation,
  GoogleReference
} from '../types';

export class GoogleSearchHistoryService {
  /**
   * Create a new Google search session
   */
  async createSession(userId: string, title: string = 'New Google Search'): Promise<GoogleSearchSession> {
    try {
      const session: Omit<GoogleSearchSession, 'id'> = {
        title,
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        userId,
        totalQueries: 0,
        isActive: true
      };

      const { data, error } = await supabase
        .from('google_search_sessions')
        .insert({
          title: session.title,
          user_id: session.userId,
          total_queries: session.totalQueries,
          is_active: session.isActive
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating Google search session:', error);
        throw error;
      }

      return {
        id: data.id,
        title: data.title,
        messages: [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        userId: data.user_id,
        totalQueries: data.total_queries,
        isActive: data.is_active
      };
    } catch (error) {
      console.error('Failed to create Google search session:', error);
      throw error;
    }
  }

  /**
   * Get all sessions for a user
   */
  async getUserSessions(userId: string): Promise<GoogleSearchSession[]> {
    try {
      const { data, error } = await supabase
        .from('google_search_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching Google search sessions:', error);
        throw error;
      }

      return data.map(session => ({
        id: session.id,
        title: session.title,
        messages: [], // Messages loaded separately
        createdAt: new Date(session.created_at),
        updatedAt: new Date(session.updated_at),
        userId: session.user_id,
        totalQueries: session.total_queries,
        isActive: session.is_active
      }));
    } catch (error) {
      console.error('Failed to fetch Google search sessions:', error);
      return [];
    }
  }

  /**
   * Get a specific session with messages
   */
  async getSession(sessionId: string): Promise<GoogleSearchSession | null> {
    try {
      const { data: sessionData, error: sessionError } = await supabase
        .from('google_search_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError || !sessionData) {
        console.error('Error fetching Google search session:', sessionError);
        return null;
      }

      const messages = await this.getSessionMessages(sessionId);

      return {
        id: sessionData.id,
        title: sessionData.title,
        messages,
        createdAt: new Date(sessionData.created_at),
        updatedAt: new Date(sessionData.updated_at),
        userId: sessionData.user_id,
        totalQueries: sessionData.total_queries,
        isActive: sessionData.is_active
      };
    } catch (error) {
      console.error('Failed to fetch Google search session:', error);
      return null;
    }
  }

  /**
   * Get messages for a session
   */
  async getSessionMessages(sessionId: string): Promise<GoogleSearchMessage[]> {
    try {
      const { data, error } = await supabase
        .from('google_search_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching Google search messages:', error);
        return [];
      }

      return data.map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: new Date(msg.created_at),
        searchQuery: msg.search_query,
        sources: msg.sources ? JSON.parse(msg.sources) : undefined,
        citations: msg.citations ? JSON.parse(msg.citations) : undefined,
        references: msg.reference_list ? JSON.parse(msg.reference_list) : undefined,
        isGoogleSearch: true
      }));
    } catch (error) {
      console.error('Failed to fetch Google search messages:', error);
      return [];
    }
  }

  /**
   * Add a message to a session
   */
  async addMessage(sessionId: string, message: GoogleSearchMessage): Promise<void> {
    try {
      const { error } = await supabase
        .from('google_search_messages')
        .insert({
          id: message.id,
          session_id: sessionId,
          type: message.type,
          content: message.content,
          search_query: message.searchQuery,
          sources: message.sources ? JSON.stringify(message.sources) : null,
          citations: message.citations ? JSON.stringify(message.citations) : null,
          reference_list: message.references ? JSON.stringify(message.references) : null
        });

      if (error) {
        console.error('Error adding Google search message:', error);
        throw error;
      }

      // Update session's updated_at timestamp
      await this.updateSessionTimestamp(sessionId);
    } catch (error) {
      console.error('Failed to add Google search message:', error);
      throw error;
    }
  }

  /**
   * Update session timestamp
   */
  async updateSessionTimestamp(sessionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('google_search_sessions')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', sessionId);

      if (error) {
        console.error('Error updating Google search session timestamp:', error);
      }
    } catch (error) {
      console.error('Failed to update Google search session timestamp:', error);
    }
  }

  /**
   * Update session title
   */
  async updateSessionTitle(sessionId: string, title: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('google_search_sessions')
        .update({ 
          title,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) {
        console.error('Error updating Google search session title:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to update Google search session title:', error);
      throw error;
    }
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      // Delete messages first
      await supabase
        .from('google_search_messages')
        .delete()
        .eq('session_id', sessionId);

      // Delete session
      const { error } = await supabase
        .from('google_search_sessions')
        .delete()
        .eq('id', sessionId);

      if (error) {
        console.error('Error deleting Google search session:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete Google search session:', error);
      throw error;
    }
  }

  /**
   * Search messages across sessions
   */
  async searchMessages(userId: string, query: string): Promise<GoogleSearchMessage[]> {
    try {
      // First get user's sessions
      const { data: sessions, error: sessionsError } = await supabase
        .from('google_search_sessions')
        .select('id')
        .eq('user_id', userId);

      if (sessionsError || !sessions || sessions.length === 0) {
        return [];
      }

      const sessionIds = sessions.map(s => s.id);

      // Then search messages in those sessions
      const { data, error } = await supabase
        .from('google_search_messages')
        .select('*')
        .in('session_id', sessionIds)
        .ilike('content', `%${query}%`)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error searching Google search messages:', error);
        return [];
      }

      return data.map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: new Date(msg.created_at),
        searchQuery: msg.search_query,
        sources: msg.sources ? JSON.parse(msg.sources) : undefined,
        citations: msg.citations ? JSON.parse(msg.citations) : undefined,
        references: msg.reference_list ? JSON.parse(msg.reference_list) : undefined,
        isGoogleSearch: true
      }));
    } catch (error) {
      console.error('Failed to search Google search messages:', error);
      return [];
    }
  }

  /**
   * Get recent searches for a user
   */
  async getRecentSearches(userId: string, limit: number = 10): Promise<string[]> {
    try {
      // First get user's sessions
      const { data: sessions, error: sessionsError } = await supabase
        .from('google_search_sessions')
        .select('id')
        .eq('user_id', userId);

      if (sessionsError || !sessions || sessions.length === 0) {
        return [];
      }

      const sessionIds = sessions.map(s => s.id);

      // Then get recent search queries from those sessions
      const { data, error } = await supabase
        .from('google_search_messages')
        .select('search_query')
        .in('session_id', sessionIds)
        .eq('type', 'user')
        .not('search_query', 'is', null)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recent Google searches:', error);
        return [];
      }

      // Remove duplicates and return unique search queries
      const uniqueQueries = [...new Set(data.map(item => item.search_query))];
      return uniqueQueries.filter(query => query && query.trim().length > 0);
    } catch (error) {
      console.error('Failed to fetch recent Google searches:', error);
      return [];
    }
  }
}

export const googleSearchHistoryService = new GoogleSearchHistoryService();
