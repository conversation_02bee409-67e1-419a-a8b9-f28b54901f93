# Google Search Research Module with Deep Research

A comprehensive Google Search integration for academic research using the Gemini API with Google Search capabilities, now featuring advanced Deep Research for generating comprehensive multi-section reports.

## Features

### Standard Search
- **Google Search Integration**: Uses Gemini API with Google Search tool for real-time web search
- **Academic Focus**: Prioritizes academic sources, research papers, and scholarly content
- **Citation Management**: Automatic generation of APA, MLA, Chicago, and Harvard citations
- **Reference Formatting**: Proper academic reference formatting with author extraction
- **Search History**: Persistent storage of search sessions and results
- **Real-time Progress**: Visual progress indicators during search operations
- **Source Validation**: Intelligent source type detection and scoring

### Deep Research (NEW)
- **Comprehensive Reports**: Generate 3000-6000 word research reports with 5-7 sections
- **Structured Outlines**: AI-generated research outlines with editable subtopics
- **Batch Research**: Efficient multi-topic research using Gemini's 1M token context window
- **Sequential Writing**: Context-aware section generation with narrative coherence
- **Cost Estimation**: Transparent API usage and cost prediction before research begins
- **Rate Limiting**: Intelligent batching to respect API limits and optimize performance
- **Export Options**: PDF, Word, and Markdown export capabilities for professional reports
- **Progress Tracking**: Multi-phase progress tracking (Outline → Research → Writing → Assembly)

## Architecture

### Components

#### Standard Components
- **GoogleSearchInterface**: Main interface component with mode toggle (Standard/Deep Research)
- **GoogleSearchInput**: Enhanced search input with suggestions
- **GoogleSearchMessage**: Message display with citations and sources
- **GoogleSearchProgress**: Real-time progress tracking
- **GoogleSearchHistory**: Session management and history
- **GoogleSearchResults**: Structured results display

#### Deep Research Components
- **DeepResearchOutline**: Interactive outline editor with subtopic management
- **DeepResearchProgress**: Multi-phase progress tracking with detailed status
- **DeepResearchResults**: Comprehensive report display with section management
- **DeepResearchCostEstimate**: Cost estimation and optimization suggestions

### Services

- **GoogleSearchService**: Core search functionality using Gemini API (enhanced with Deep Research methods)
  - `generateResearchOutline()`: Creates structured research outlines
  - `batchResearchSubtopics()`: Performs batch research on multiple topics
  - `generateResearchSection()`: Writes individual sections with context
- **DeepResearchCostService**: Cost estimation and rate limiting management
- **GoogleAcademicService**: Academic formatting and citation generation
- **GoogleSearchHistoryService**: Database operations for sessions and messages

### Types

Complete TypeScript definitions for all Google Search functionality including:
- Search sessions and messages
- Source types and citations
- Progress tracking and error handling
- User preferences and options
- Deep Research sessions, outlines, and sections
- Cost estimation and rate limiting interfaces

## Deep Research Workflow

The Deep Research feature follows a structured 4-phase approach:

1. **Query Input**: User enters research topic in Deep Research mode
2. **Cost Estimation**: System shows estimated cost, time, and token usage with optimization suggestions
3. **Outline Generation**: AI creates structured research outline with 5-7 subtopics (user can edit)
4. **Batch Research**: Simultaneous research on all subtopics using Gemini's 1M token context
5. **Sequential Writing**: Context-aware section generation with narrative flow
6. **Final Assembly**: Complete report with citations, references, and export options

### Hybrid Batch-Sequential Approach

The system uses an optimized approach that leverages Gemini's large context window:
- **Batch Research Phase**: Gather comprehensive data for all subtopics simultaneously
- **Sequential Writing Phase**: Generate sections one by one with full context awareness
- **Benefits**: Faster than pure sequential, more coherent than pure batch processing

## Setup

### 1. Environment Variables

Ensure you have the Google Gemini API key in your `.env` file:

```env
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. Database Schema

Run the database schema to create required tables:

```sql
-- Run the schema from database/google-search-schema.sql
```

### 3. Dependencies

The module uses existing dependencies:
- `@google/genai` - Google Gemini AI SDK
- `@supabase/supabase-js` - Database operations
- React and UI components

## Usage

### Basic Integration

```tsx
import { GoogleSearchInterface } from '@/components/research/google-search';

function App() {
  return (
    <GoogleSearchInterface
      userId="user-123"
      className="h-screen"
    />
  );
}
```

The interface automatically includes both Standard and Deep Research modes with a toggle switch.

### Deep Research Configuration

```tsx
import { GoogleSearchInterface } from '@/components/research/google-search';

function ResearchApp() {
  const deepResearchOptions = {
    maxSubtopics: 6,
    wordsPerSection: 700,
    researchDepth: 'advanced',
    includeExecutiveSummary: true,
    citationStyle: 'apa',
    targetAudience: 'professional',
    allowOutlineEditing: true
  };

  return (
    <GoogleSearchInterface
      userId="user-123"
      className="h-screen"
      // Deep Research options are configurable within the interface
    />
  );
}
```

### Using the Hook

```tsx
import { useGoogleSearch } from '@/components/research/google-search';

function CustomComponent() {
  const {
    currentSession,
    messages,
    isLoading,
    performSearch,
    preferences,
    updatePreferences
  } = useGoogleSearch('user-123');

  const handleSearch = async () => {
    await performSearch('artificial intelligence research', {
      maxResults: 10,
      citationStyle: 'apa',
      academicFocus: true
    });
  };

  return (
    <div>
      {/* Your custom UI */}
    </div>
  );
}
```

### Direct Service Usage

#### Standard Search
```tsx
import { googleSearchService } from '@/components/research/google-search';

async function searchExample() {
  const result = await googleSearchService.searchAcademic(
    'machine learning applications',
    {
      maxResults: 10,
      searchDepth: 'advanced',
      includeAcademicSources: true,
      citationStyle: 'apa'
    }
  );

  console.log('Search results:', result);
}
```

#### Deep Research Service Usage
```tsx
import {
  googleSearchService,
  deepResearchCostService
} from '@/components/research/google-search';

async function deepResearchExample() {
  const query = 'Impact of artificial intelligence on healthcare';
  const options = {
    maxSubtopics: 6,
    wordsPerSection: 700,
    researchDepth: 'advanced',
    citationStyle: 'apa',
    targetAudience: 'professional'
  };

  // 1. Estimate cost
  const estimate = deepResearchCostService.estimateCost(query, options);
  console.log('Estimated cost:', estimate.estimatedCost);
  console.log('Estimated time:', estimate.estimatedTime);

  // 2. Generate outline
  const outline = await googleSearchService.generateResearchOutline(query, options);
  console.log('Research outline:', outline);

  // 3. Batch research
  const researchData = await googleSearchService.batchResearchSubtopics(
    outline.subtopics,
    { maxResults: 8, searchDepth: 'advanced' }
  );

  // 4. Generate sections
  for (const subtopic of outline.subtopics) {
    const section = await googleSearchService.generateResearchSection(
      subtopic,
      researchData,
      [], // previous sections
      options
    );
    console.log('Generated section:', section);
  }
}
```

## Configuration

### Search Options

```typescript
interface GoogleSearchOptions {
  maxResults: number;                    // Number of results to return
  searchDepth: 'basic' | 'advanced' | 'comprehensive';
  includeAcademicSources: boolean;       // Prioritize academic sources
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicFocus: boolean;                // Academic-focused search
  includeRecentOnly?: boolean;           // Recent sources only
  yearRange?: {                          // Date range filter
    start: number;
    end: number;
  };
}
```

### User Preferences

```typescript
interface GoogleUserPreferences {
  defaultSearchDepth: 'basic' | 'advanced' | 'comprehensive';
  maxResults: number;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicFocus: boolean;
  includeRecentOnly: boolean;
}
```

### Deep Research Options

```typescript
interface DeepResearchOptions {
  maxSubtopics: number;                    // 3-8 subtopics per report
  wordsPerSection: number;                 // 500-1000 words per section
  researchDepth: 'basic' | 'advanced' | 'comprehensive';
  includeExecutiveSummary: boolean;        // Include summary section
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  targetAudience: 'academic' | 'professional' | 'general';
  allowOutlineEditing: boolean;            // Allow user to edit outline
}
```

### Cost Estimation Interface

```typescript
interface CostEstimate {
  totalTokens: number;                     // Total tokens required
  estimatedCost: number;                   // Cost in USD
  apiCalls: number;                        // Number of API calls
  estimatedTime: number;                   // Time in seconds
  breakdown: {
    outlineGeneration: number;
    batchResearch: number;
    sectionGeneration: number;
  };
}
```

## Testing

### Run Tests

```typescript
import { runGoogleSearchTests } from './test-google-search';

// Run all tests
const results = await runGoogleSearchTests();

// Run individual tests
import { 
  testGoogleSearchAPI,
  testGoogleSearch,
  testAcademicFormatting,
  testSearchHistory 
} from './test-google-search';
```

### Browser Testing

Open browser console and run:

```javascript
// Test API connectivity
await window.testGoogleSearch.testAPI();

// Test search functionality
await window.testGoogleSearch.testSearch();

// Run all tests
await window.testGoogleSearch.runAll();
```

## API Reference

### GoogleSearchService

- `searchAcademic(query, options)` - Perform academic search
- `isConfigured()` - Check API configuration
- `testConnection()` - Test API connectivity

### GoogleAcademicService

- `generateReferences(sources, style)` - Generate academic references
- `generateInTextCitations(sources, style)` - Create in-text citations
- `formatAcademicResponse(content, sources, style)` - Format complete response

### GoogleSearchHistoryService

- `createSession(userId, title)` - Create new search session
- `getSession(sessionId)` - Retrieve session with messages
- `addMessage(sessionId, message)` - Add message to session
- `deleteSession(sessionId)` - Delete session and messages

## Error Handling

The module includes comprehensive error handling:

- API connectivity issues
- Invalid search queries
- Database operation failures
- Network timeouts
- Rate limiting

## Performance

- Optimized search queries with result limiting
- Efficient database operations with proper indexing
- Lazy loading of search history
- Debounced search input
- Progress tracking for long operations

## Security

- Row Level Security (RLS) for database operations
- User-specific data isolation
- Input validation and sanitization
- API key protection
- CORS handling

## Contributing

1. Follow existing code patterns
2. Add tests for new functionality
3. Update documentation
4. Ensure TypeScript compliance
5. Test with real API calls

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify `VITE_GEMINI_API_KEY` is set correctly
   - Check API key permissions and quotas

2. **Database Errors**
   - Ensure schema is properly applied
   - Check RLS policies are enabled
   - Verify user authentication

3. **Search Not Working**
   - Test API connectivity first
   - Check network connection
   - Verify search query format

4. **Citations Not Formatting**
   - Check source data structure
   - Verify citation style parameter
   - Test with mock data first

5. **Fake or Invalid URLs**
   - Run GoogleSearchValidation component to test
   - Check Google Search grounding configuration
   - Verify academic source detection

6. **Poor Academic Quality**
   - Ensure academic focus is enabled
   - Validate sources using built-in validation tools
   - Check source scoring and ranking

## Google Search Grounding & Validation

### ✅ **Enhanced Google Search Integration**

The system now uses **proper Google Search grounding** to ensure:

#### **Real URL Validation**
- ✅ All URLs are validated for format and authenticity
- ✅ Fake/placeholder URLs are automatically filtered out
- ✅ Only working, accessible links are included
- ❌ No more `example.com` or `placeholder.com` links

#### **Academic Source Prioritization**
- 🎓 **University domains** (.edu) get highest priority
- 🏛️ **Government research** (.gov) sources prioritized
- 📚 **Academic databases** (PubMed, arXiv, JSTOR) emphasized
- 📖 **Peer-reviewed journals** (Nature, Science) prioritized
- 🔍 **Google Scholar** results included when available

#### **Academic Writing Standards**
- 📝 **Formal academic tone** with scholarly language
- 📊 **Evidence-based arguments** with specific data
- 🔗 **Proper citations** in APA/MLA/Chicago/Harvard formats
- 📈 **Statistical evidence** and research findings
- 🎯 **Graduate-level writing** standards

### **Validation Tools**

#### **GoogleSearchValidation Component**
```tsx
import { GoogleSearchValidation } from '@/components/research/google-search/components/GoogleSearchValidation';

function ValidationPage() {
  return <GoogleSearchValidation />;
}
```

#### **Integration Test**
```typescript
import { testAcademicSearchIntegration } from '@/components/research/google-search/utils/validation-utils';

const success = await testAcademicSearchIntegration();
console.log('Integration test:', success ? 'PASSED' : 'FAILED');
```

#### **Test Runner (Browser Console)**
```typescript
import { runGoogleSearchTests } from '@/components/research/google-search/test-runner';

// Run all tests
runGoogleSearchTests();

// Quick health check
quickHealthCheck();

// Run specific test
runIndividualTest('api');
runIndividualTest('url');
runIndividualTest('academic');
```

#### **Source Quality Scoring**
```typescript
// Academic sources get higher scores
.edu domains: +0.4 points
.gov domains: +0.35 points
Peer-reviewed indicators: +0.2 points
Recent publications: +0.1 points
Author information: +0.1 points
```
