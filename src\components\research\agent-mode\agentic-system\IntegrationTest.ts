/**
 * Integration test for preview, apply, and reject functionality
 */

import { agenticEditingService } from './AgenticEditingService';
import { changeTrackingAdapter } from './ChangeTrackingAdapter';

export async function testPreviewApplyReject(): Promise<{
  success: boolean;
  message: string;
  details: any;
}> {
  console.log('🧪 Testing preview, apply, and reject functionality...');

  try {
    // Initialize the system
    agenticEditingService.initialize();
    agenticEditingService.setChangeTrackingIntegration(changeTrackingAdapter);

    const testDocument = `
# Test Document

This is a test document with some content that needs improvement. The grammar here is not perfect and could be enhanced for better readability.

## Section 1
This section contains information that might need clarification.

## Section 2  
Another section with content that could be improved.
    `.trim();

    console.log('📝 Test document prepared');

    // Step 1: Execute targeted edit with change tracking
    console.log('🎯 Step 1: Executing targeted edit...');
    const result = await agenticEditingService.executeTargetedEdit(
      'Fix any grammatical errors in this document',
      testDocument,
      {
        editMode: 'conservative',
        enableChangeTracking: true,
        requirePreview: true,
        maxChanges: 3
      }
    );

    console.log('📊 Edit result:', {
      success: result.success,
      changesCount: result.changes.length,
      hasChangeIds: !!result.changeIds,
      changeIds: result.changeIds
    });

    if (!result.success || !result.changeIds || result.changeIds.length === 0) {
      return {
        success: false,
        message: 'Failed to generate changes with change IDs',
        details: result
      };
    }

    // Step 2: Test preview functionality
    console.log('🔍 Step 2: Testing preview functionality...');
    changeTrackingAdapter.previewChanges(result.changes);
    
    // Check if changes were recorded in change tracking
    const trackingState = changeTrackingAdapter.getChangeTrackingState();
    console.log('📋 Change tracking state:', {
      isEnabled: trackingState.isTrackingEnabled,
      changesCount: trackingState.changes.length,
      viewMode: trackingState.viewMode
    });

    // Step 3: Test apply functionality
    console.log('✅ Step 3: Testing apply functionality...');
    const changeIdsToApply = result.changeIds.slice(0, 1); // Apply first change
    agenticEditingService.applyPreviewedChanges(changeIdsToApply);

    // Step 4: Test reject functionality  
    console.log('❌ Step 4: Testing reject functionality...');
    const changeIdsToReject = result.changeIds.slice(1); // Reject remaining changes
    if (changeIdsToReject.length > 0) {
      agenticEditingService.rejectPreviewedChanges(changeIdsToReject);
    }

    // Step 5: Verify final state
    console.log('🔍 Step 5: Verifying final state...');
    const finalState = changeTrackingAdapter.getChangeTrackingState();
    const appliedChanges = finalState.changes.filter(c => c.status === 'accepted');
    const rejectedChanges = finalState.changes.filter(c => c.status === 'rejected');

    console.log('📊 Final state:', {
      totalChanges: finalState.changes.length,
      appliedChanges: appliedChanges.length,
      rejectedChanges: rejectedChanges.length
    });

    return {
      success: true,
      message: `Integration test passed! Applied ${appliedChanges.length} changes, rejected ${rejectedChanges.length} changes`,
      details: {
        originalChanges: result.changes.length,
        appliedChanges: appliedChanges.length,
        rejectedChanges: rejectedChanges.length,
        finalState: finalState
      }
    };

  } catch (error: any) {
    console.error('❌ Integration test failed:', error);
    return {
      success: false,
      message: `Integration test failed: ${error.message}`,
      details: { error: error.message, stack: error.stack }
    };
  }
}

export async function testBasicFunctionality(): Promise<{
  success: boolean;
  message: string;
}> {
  console.log('🔧 Testing basic functionality...');

  try {
    // Test service initialization
    agenticEditingService.initialize();
    const status = agenticEditingService.getStatus();
    
    if (!status.initialized) {
      throw new Error('Service failed to initialize');
    }

    // Test change tracking adapter
    const trackingStats = changeTrackingAdapter.getChangeStatistics();
    console.log('📊 Change tracking stats:', trackingStats);

    // Test basic integration
    agenticEditingService.setChangeTrackingIntegration(changeTrackingAdapter);

    return {
      success: true,
      message: 'Basic functionality test passed'
    };

  } catch (error: any) {
    console.error('❌ Basic functionality test failed:', error);
    return {
      success: false,
      message: `Basic functionality test failed: ${error.message}`
    };
  }
}

// Export test runner
export async function runIntegrationTests(): Promise<{
  overall: boolean;
  results: {
    basic: { success: boolean; message: string };
    integration: { success: boolean; message: string; details: any };
  };
}> {
  console.log('🚀 Running integration tests for preview/apply/reject functionality...');

  const results = {
    basic: await testBasicFunctionality(),
    integration: await testPreviewApplyReject()
  };

  const overall = results.basic.success && results.integration.success;

  console.log('\n📊 Integration Test Results:');
  console.log(`Basic Functionality: ${results.basic.success ? '✅' : '❌'} - ${results.basic.message}`);
  console.log(`Preview/Apply/Reject: ${results.integration.success ? '✅' : '❌'} - ${results.integration.message}`);
  console.log(`Overall: ${overall ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return { overall, results };
}

// Export for easy testing
export default {
  testBasicFunctionality,
  testPreviewApplyReject,
  runIntegrationTests
};
