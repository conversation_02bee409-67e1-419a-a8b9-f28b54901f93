/**
 * EditTool - Performs precise, targeted modifications to specific text sections
 */

import { BaseAgentTool } from '../BaseAgentTool';
import { Tool<PERSON>ontext, ToolResult, EditResult, EditToolResult, AnalysisResult } from '../types';
import { enhancedAIService } from '../../../paper-generator/enhanced-ai.service';

export class EditTool extends BaseAgentTool {
  constructor() {
    super(
      'edit-tool',
      'Content Editor',
      'Performs precise, targeted modifications to specific text sections'
    );
  }

  protected async executeInternal(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const { userRequest, documentContent, previousResults, metadata } = context;
    
    console.log(`✏️ [EditTool] Performing targeted edits for: "${userRequest}"`);

    try {
      // Get analysis results from previous step
      const analysisResult = this.extractAnalysisResult(previousResults);
      if (!analysisResult) {
        throw new Error('Analysis result not found from previous steps');
      }

      console.log(`📋 [EditTool] Edit strategy: ${analysisResult.editType} at ${analysisResult.scope} level`);

      // Perform edits based on analysis
      const edits = await this.performTargetedEdits(
        analysisResult,
        userRequest,
        documentContent,
        metadata?.options
      );

      // Calculate overall confidence
      const confidence = this.calculateEditConfidence(edits, analysisResult);

      const reasoning = this.generateEditReasoning(edits, analysisResult);

      return this.createSuccessResult(
        startTime,
        {
          edits,
          strategy: `${analysisResult.editType}_${analysisResult.scope}`
        } as EditToolResult['data'],
        reasoning,
        confidence
      );

    } catch (error: any) {
      return this.createErrorResult(
        startTime,
        `Edit failed: ${error.message}`,
        ['Try a simpler edit request', 'Check if the target content exists', 'Ensure the document has sufficient content']
      );
    }
  }

  /**
   * Extract analysis result from previous tool executions
   */
  private extractAnalysisResult(previousResults?: ToolResult[]): AnalysisResult | null {
    if (!previousResults) return null;
    
    const analysisResult = previousResults.find(r => r.toolId === 'analysis-tool' && r.success);
    return analysisResult?.data || null;
  }

  /**
   * Perform targeted edits based on analysis
   */
  private async performTargetedEdits(
    analysis: AnalysisResult,
    userRequest: string,
    documentContent: string,
    options: any
  ): Promise<EditResult[]> {
    const edits: EditResult[] = [];
    const { targetSections, editType, requirements } = analysis;

    console.log(`🎯 [EditTool] Processing ${targetSections.length} target sections`);

    for (const section of targetSections) {
      try {
        const edit = await this.createEditForSection(
          section,
          editType,
          userRequest,
          requirements,
          options
        );

        if (edit) {
          edits.push(edit);
          console.log(`✅ [EditTool] Created edit for section: ${section.text.substring(0, 50)}...`);
        }
      } catch (error: any) {
        console.warn(`⚠️ [EditTool] Failed to edit section: ${error.message}`);
      }
    }

    return edits;
  }

  /**
   * Create an edit for a specific section
   */
  private async createEditForSection(
    section: any,
    editType: string,
    userRequest: string,
    requirements: string[],
    options: any
  ): Promise<EditResult | null> {
    const editMode = options?.editMode || 'moderate';
    
    // Build AI prompt for the specific edit
    const prompt = this.buildEditPrompt(
      section.text,
      editType,
      userRequest,
      requirements,
      editMode
    );

    try {
      // Generate improved content using AI
      const improvedText = await enhancedAIService.generateText(prompt);

      if (!improvedText || improvedText.trim() === section.text.trim()) {
        console.log(`⏭️ [EditTool] No changes needed for section, creating minimal edit`);

        // Create a minimal edit as fallback
        const minimalEdit = this.createMinimalEdit(section.text, editType, userRequest);
        if (minimalEdit) {
          return {
            originalText: section.text,
            newText: minimalEdit,
            startPosition: section.startPosition,
            endPosition: section.endPosition,
            editType: editType as any,
            reasoning: `Applied minimal ${editType} edit based on request`,
            confidence: 0.6
          };
        }

        return null;
      }

      // Calculate confidence for this edit
      const confidence = this.calculateSectionEditConfidence(
        section.text,
        improvedText,
        editType,
        requirements
      );

      return {
        originalText: section.text,
        newText: improvedText.trim(),
        startPosition: section.startPosition,
        endPosition: section.endPosition,
        editType: editType as any,
        reasoning: `Applied ${editType} edit to improve: ${requirements.slice(0, 2).join(', ')}`,
        confidence
      };

    } catch (error: any) {
      console.error(`❌ [EditTool] AI generation failed for section: ${error.message}`);

      // Try to create a fallback edit
      const fallbackEdit = this.createFallbackEdit(section.text, editType, userRequest);
      if (fallbackEdit) {
        return {
          originalText: section.text,
          newText: fallbackEdit,
          startPosition: section.startPosition,
          endPosition: section.endPosition,
          editType: editType as any,
          reasoning: `Applied fallback ${editType} edit due to AI service error`,
          confidence: 0.4
        };
      }

      return null;
    }
  }

  /**
   * Build AI prompt for specific edit
   */
  private buildEditPrompt(
    originalText: string,
    editType: string,
    userRequest: string,
    requirements: string[],
    editMode: string
  ): string {
    const editModeInstructions = {
      conservative: 'Make minimal, careful improvements while preserving the original meaning and style.',
      moderate: 'Make meaningful improvements while maintaining the core structure and intent.',
      aggressive: 'Make substantial improvements, restructuring as needed for better clarity and impact.'
    };

    const editTypeInstructions = {
      replace: 'Replace the problematic parts with improved versions.',
      insert: 'Add new content while preserving the existing text.',
      enhance: 'Improve the existing content without major structural changes.',
      rewrite: 'Completely rewrite the content for better quality.'
    };

    return `You are an expert editor making TARGETED improvements to text. You must ONLY make changes that directly address the user's specific request.

CRITICAL RULES:
- ONLY modify content that directly relates to the user's request
- DO NOT make changes outside the scope of the request
- PRESERVE all content that doesn't need to be changed
- MAINTAIN the original structure and formatting
- FOCUS exclusively on the requested improvement

EDIT MODE: ${editModeInstructions[editMode as keyof typeof editModeInstructions]}
EDIT TYPE: ${editTypeInstructions[editType as keyof typeof editTypeInstructions]}

USER REQUEST: "${userRequest}"

REQUIREMENTS:
${requirements.map(req => `- ${req}`).join('\n')}

ORIGINAL TEXT:
"${originalText}"

INSTRUCTIONS:
1. Focus ONLY on the user's specific request
2. Make ONLY the necessary changes to address the request
3. Preserve the original meaning and context
4. Maintain consistency with the surrounding content
5. Do NOT make improvements beyond what was requested
6. Return only the improved text, no explanations or meta-text

IMPROVED TEXT:`;
  }

  /**
   * Calculate confidence for a section edit
   */
  private calculateSectionEditConfidence(
    originalText: string,
    improvedText: string,
    editType: string,
    requirements: string[]
  ): number {
    let confidence = 0.7; // Base confidence

    // Length change analysis
    const lengthRatio = improvedText.length / originalText.length;
    if (editType === 'insert' && lengthRatio > 1.1) confidence += 0.1;
    if (editType === 'enhance' && lengthRatio > 0.9 && lengthRatio < 1.3) confidence += 0.1;
    if (editType === 'replace' && lengthRatio > 0.8 && lengthRatio < 1.2) confidence += 0.1;

    // Content preservation check
    const preservationScore = this.calculateContentPreservation(originalText, improvedText);
    confidence += preservationScore * 0.2;

    // Requirements fulfillment (simplified check)
    const fulfillmentScore = this.estimateRequirementsFulfillment(
      originalText,
      improvedText,
      requirements
    );
    confidence += fulfillmentScore * 0.1;

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Calculate how well content is preserved
   */
  private calculateContentPreservation(originalText: string, improvedText: string): number {
    const originalWords = originalText.toLowerCase().split(/\s+/);
    const improvedWords = improvedText.toLowerCase().split(/\s+/);
    
    const commonWords = originalWords.filter(word => improvedWords.includes(word));
    const preservationRatio = commonWords.length / Math.max(originalWords.length, 1);
    
    return Math.min(preservationRatio, 1);
  }

  /**
   * Estimate how well requirements are fulfilled
   */
  private estimateRequirementsFulfillment(
    originalText: string,
    improvedText: string,
    requirements: string[]
  ): number {
    let fulfillmentScore = 0;
    const totalRequirements = requirements.length;

    if (totalRequirements === 0) return 0.5;

    requirements.forEach(requirement => {
      const req = requirement.toLowerCase();
      
      // Simple keyword-based fulfillment check
      if (req.includes('clarity') || req.includes('clear')) {
        // Check if text became clearer (simplified heuristic)
        if (improvedText.length > originalText.length * 0.9) {
          fulfillmentScore += 1;
        }
      } else if (req.includes('academic') || req.includes('formal')) {
        // Check for academic language indicators
        const academicWords = ['therefore', 'furthermore', 'however', 'moreover', 'consequently'];
        const hasAcademicWords = academicWords.some(word => 
          improvedText.toLowerCase().includes(word) && !originalText.toLowerCase().includes(word)
        );
        if (hasAcademicWords) fulfillmentScore += 1;
      } else if (req.includes('citation') || req.includes('reference')) {
        // Check for citation patterns
        const citationPattern = /\([^)]*\d{4}[^)]*\)|et al\.|ibid\./i;
        if (citationPattern.test(improvedText) && !citationPattern.test(originalText)) {
          fulfillmentScore += 1;
        }
      } else if (req.includes('concise')) {
        // Check if text became more concise
        if (improvedText.length < originalText.length * 0.9) {
          fulfillmentScore += 1;
        }
      } else {
        // Default: assume partial fulfillment
        fulfillmentScore += 0.5;
      }
    });

    return fulfillmentScore / totalRequirements;
  }

  /**
   * Calculate overall edit confidence
   */
  private calculateEditConfidence(edits: EditResult[], analysis: AnalysisResult): number {
    if (edits.length === 0) return 0;

    const avgEditConfidence = edits.reduce((sum, edit) => sum + edit.confidence, 0) / edits.length;
    const coverageScore = Math.min(edits.length / analysis.targetSections.length, 1);
    const complexityPenalty = analysis.complexity === 'complex' ? 0.9 : 1.0;

    return avgEditConfidence * coverageScore * complexityPenalty;
  }

  /**
   * Create minimal edit when AI service fails
   */
  private createMinimalEdit(originalText: string, editType: string, userRequest: string): string | null {
    const request = userRequest.toLowerCase();

    // Simple text-based improvements
    if (request.includes('grammar') || request.includes('fix')) {
      // Basic grammar fixes
      return originalText
        .replace(/\s+/g, ' ') // Fix multiple spaces
        .replace(/\s+\./g, '.') // Fix space before period
        .replace(/([.!?])\s*([a-z])/g, '$1 $2') // Fix spacing after punctuation
        .trim();
    }

    if (request.includes('remove') && request.includes('reference')) {
      // Remove reference-like patterns
      return originalText
        .replace(/\[[^\]]*\]/g, '') // Remove [citations]
        .replace(/\([^)]*\d{4}[^)]*\)/g, '') // Remove (Author, 2023) style citations
        .replace(/\s+/g, ' ')
        .trim();
    }

    if (request.includes('improve') || request.includes('enhance')) {
      // Basic improvements
      return originalText
        .replace(/\b(very|really|quite)\s+/gi, '') // Remove weak intensifiers
        .replace(/\s+/g, ' ')
        .trim();
    }

    return null;
  }

  /**
   * Create fallback edit when all else fails
   */
  private createFallbackEdit(originalText: string, editType: string, userRequest: string): string | null {
    // Last resort: make a minimal change to show the system is working
    if (editType === 'enhance') {
      return originalText.trim() + ' '; // Add space at end
    }

    if (editType === 'replace') {
      return originalText.replace(/\s+/g, ' ').trim(); // Normalize whitespace
    }

    return originalText; // Return original if nothing else works
  }

  /**
   * Generate reasoning for the edits
   */
  private generateEditReasoning(edits: EditResult[], analysis: AnalysisResult): string {
    if (edits.length === 0) {
      return `No edits were generated. The content may already meet the requirements or the request may need clarification.`;
    }

    const editTypes = [...new Set(edits.map(e => e.editType))];
    const avgConfidence = edits.reduce((sum, edit) => sum + edit.confidence, 0) / edits.length;

    return `Generated ${edits.length} targeted edit(s) using ${editTypes.join(', ')} approach(es). ` +
           `Average confidence: ${(avgConfidence * 100).toFixed(0)}%. ` +
           `Focused on: ${analysis.requirements.slice(0, 2).join(', ')}.`;
  }
}
