-- Research Analysis Platform Database Schema
-- This schema supports comprehensive research document analysis, storage, and history tracking

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table for storing uploaded research documents and their metadata
CREATE TABLE IF NOT EXISTS public.research_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    authors TEXT[] DEFAULT '{}',
    abstract TEXT,
    publication_year INTEGER,
    journal TEXT,
    doi TEXT,
    keywords TEXT[] DEFAULT '{}',
    
    -- File information
    filename TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT, -- Path in Supabase storage
    file_type TEXT NOT NULL, -- pdf, docx, txt, etc.
    
    -- Processing status
    status TEXT NOT NULL DEFAULT 'processing' CHECK (status IN ('processing', 'ready', 'error')),
    processing_progress INTEGER DEFAULT 0,
    
    -- Extracted content and analysis
    raw_content TEXT,
    summary TEXT,
    key_findings TEXT[] DEFAULT '{}',
    methodology TEXT[] DEFAULT '{}',
    limitations TEXT[] DEFAULT '{}',
    future_work TEXT[] DEFAULT '{}',
    
    -- Organization and metadata
    tags TEXT[] DEFAULT '{}',
    categories TEXT[] DEFAULT '{}',
    favorite BOOLEAN DEFAULT FALSE,
    notes TEXT,
    
    -- AI analysis metadata
    ai_model_used TEXT,
    analysis_confidence DECIMAL(3,2), -- 0.00 to 1.00
    extraction_metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing document sections (introduction, methodology, results, etc.)
CREATE TABLE IF NOT EXISTS public.research_document_sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES public.research_documents(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    section_type TEXT NOT NULL CHECK (section_type IN (
        'title', 'abstract', 'introduction', 'literature_review', 
        'methodology', 'results', 'discussion', 'conclusion', 
        'references', 'appendix', 'other'
    )),
    section_order INTEGER NOT NULL DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    start_page INTEGER,
    end_page INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing literature reviews generated from documents
CREATE TABLE IF NOT EXISTS public.literature_reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    topic TEXT,
    document_ids UUID[] DEFAULT '{}', -- Array of research_documents.id
    citation_style TEXT DEFAULT 'APA' CHECK (citation_style IN ('APA', 'MLA', 'Chicago', 'Harvard', 'IEEE', 'Vancouver')),
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'generating', 'completed', 'error')),
    word_count INTEGER DEFAULT 0,
    ai_model_used TEXT,
    generation_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing literature review sections
CREATE TABLE IF NOT EXISTS public.literature_review_sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    review_id UUID REFERENCES public.literature_reviews(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    section_type TEXT NOT NULL CHECK (section_type IN (
        'introduction', 'chronological', 'thematic', 'methodological', 'gaps', 'conclusion'
    )),
    section_order INTEGER NOT NULL DEFAULT 0,
    source_document_ids UUID[] DEFAULT '{}',
    citations JSONB DEFAULT '[]', -- Array of citation objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing research gap analyses
CREATE TABLE IF NOT EXISTS public.research_gap_analyses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    document_ids UUID[] DEFAULT '{}', -- Array of research_documents.id
    summary TEXT,
    ai_model_used TEXT,
    analysis_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing individual research gaps identified
CREATE TABLE IF NOT EXISTS public.research_gaps (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    analysis_id UUID REFERENCES public.research_gap_analyses(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('methodological', 'theoretical', 'empirical', 'interdisciplinary')),
    priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    feasibility INTEGER CHECK (feasibility >= 1 AND feasibility <= 10),
    impact INTEGER CHECK (impact >= 1 AND impact <= 10),
    collaboration_potential INTEGER CHECK (collaboration_potential >= 1 AND collaboration_potential <= 10),
    related_document_ids UUID[] DEFAULT '{}',
    suggested_methods TEXT[] DEFAULT '{}',
    required_resources TEXT[] DEFAULT '{}',
    timeline_estimate TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing research themes identified in gap analysis
CREATE TABLE IF NOT EXISTS public.research_themes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    analysis_id UUID REFERENCES public.research_gap_analyses(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    frequency INTEGER DEFAULT 0,
    related_document_ids UUID[] DEFAULT '{}',
    key_terms TEXT[] DEFAULT '{}',
    evolution_data JSONB DEFAULT '[]', -- Array of evolution objects
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing research opportunities
CREATE TABLE IF NOT EXISTS public.research_opportunities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    analysis_id UUID REFERENCES public.research_gap_analyses(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    opportunity_type TEXT NOT NULL CHECK (opportunity_type IN ('extension', 'replication', 'combination', 'application', 'innovation')),
    difficulty TEXT NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced', 'expert')),
    potential_impact TEXT NOT NULL CHECK (potential_impact IN ('low', 'medium', 'high', 'transformative')),
    required_expertise TEXT[] DEFAULT '{}',
    suggested_approach TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing research hypotheses
CREATE TABLE IF NOT EXISTS public.research_hypotheses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    gap_analysis_id UUID REFERENCES public.research_gap_analyses(id) ON DELETE SET NULL,
    statement TEXT NOT NULL,
    hypothesis_type TEXT NOT NULL CHECK (hypothesis_type IN ('directional', 'non-directional', 'null')),
    variables JSONB DEFAULT '{}', -- Object containing independent, dependent, control, etc. variables
    testability INTEGER CHECK (testability >= 1 AND testability <= 10),
    novelty INTEGER CHECK (novelty >= 1 AND novelty <= 10),
    significance INTEGER CHECK (significance >= 1 AND significance <= 10),
    suggested_methodology TEXT[] DEFAULT '{}',
    expected_outcomes TEXT[] DEFAULT '{}',
    limitations TEXT[] DEFAULT '{}',
    required_resources TEXT[] DEFAULT '{}',
    timeline TEXT,
    related_gap_ids UUID[] DEFAULT '{}',
    ai_model_used TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing user activity and history
CREATE TABLE IF NOT EXISTS public.research_analysis_activity (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    activity_type TEXT NOT NULL CHECK (activity_type IN ('upload', 'analysis', 'generation', 'export', 'view', 'edit', 'delete')),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('document', 'literature_review', 'gap_analysis', 'hypothesis')),
    entity_id UUID NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_research_documents_user_id ON public.research_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_research_documents_status ON public.research_documents(status);
CREATE INDEX IF NOT EXISTS idx_research_documents_created_at ON public.research_documents(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_research_documents_keywords ON public.research_documents USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_research_documents_tags ON public.research_documents USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_document_sections_document_id ON public.research_document_sections(document_id);
CREATE INDEX IF NOT EXISTS idx_document_sections_type ON public.research_document_sections(section_type);

CREATE INDEX IF NOT EXISTS idx_literature_reviews_user_id ON public.literature_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_literature_reviews_status ON public.literature_reviews(status);
CREATE INDEX IF NOT EXISTS idx_literature_reviews_created_at ON public.literature_reviews(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_gap_analyses_user_id ON public.research_gap_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_gap_analyses_created_at ON public.research_gap_analyses(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_hypotheses_user_id ON public.research_hypotheses(user_id);
CREATE INDEX IF NOT EXISTS idx_hypotheses_created_at ON public.research_hypotheses(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_activity_user_id ON public.research_analysis_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_created_at ON public.research_analysis_activity(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_type ON public.research_analysis_activity(activity_type);

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE public.research_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_document_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.literature_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.literature_review_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_gap_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_gaps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_opportunities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_hypotheses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.research_analysis_activity ENABLE ROW LEVEL SECURITY;

-- RLS Policies for research_documents
CREATE POLICY "Users can view their own research documents" ON public.research_documents
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own research documents" ON public.research_documents
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own research documents" ON public.research_documents
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own research documents" ON public.research_documents
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for research_document_sections
CREATE POLICY "Users can view sections of their own documents" ON public.research_document_sections
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.research_documents
            WHERE id = document_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sections for their own documents" ON public.research_document_sections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.research_documents
            WHERE id = document_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sections of their own documents" ON public.research_document_sections
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.research_documents
            WHERE id = document_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sections of their own documents" ON public.research_document_sections
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.research_documents
            WHERE id = document_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for literature_reviews
CREATE POLICY "Users can view their own literature reviews" ON public.literature_reviews
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own literature reviews" ON public.literature_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own literature reviews" ON public.literature_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own literature reviews" ON public.literature_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for literature_review_sections
CREATE POLICY "Users can view sections of their own literature reviews" ON public.literature_review_sections
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.literature_reviews
            WHERE id = review_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sections for their own literature reviews" ON public.literature_review_sections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.literature_reviews
            WHERE id = review_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sections of their own literature reviews" ON public.literature_review_sections
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.literature_reviews
            WHERE id = review_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sections of their own literature reviews" ON public.literature_review_sections
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.literature_reviews
            WHERE id = review_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for research_gap_analyses
CREATE POLICY "Users can view their own gap analyses" ON public.research_gap_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own gap analyses" ON public.research_gap_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own gap analyses" ON public.research_gap_analyses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own gap analyses" ON public.research_gap_analyses
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for research_gaps
CREATE POLICY "Users can view gaps from their own analyses" ON public.research_gaps
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert gaps for their own analyses" ON public.research_gaps
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update gaps from their own analyses" ON public.research_gaps
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete gaps from their own analyses" ON public.research_gaps
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for research_themes (similar pattern)
CREATE POLICY "Users can view themes from their own analyses" ON public.research_themes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert themes for their own analyses" ON public.research_themes
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update themes from their own analyses" ON public.research_themes
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete themes from their own analyses" ON public.research_themes
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for research_opportunities (similar pattern)
CREATE POLICY "Users can view opportunities from their own analyses" ON public.research_opportunities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert opportunities for their own analyses" ON public.research_opportunities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update opportunities from their own analyses" ON public.research_opportunities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete opportunities from their own analyses" ON public.research_opportunities
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.research_gap_analyses
            WHERE id = analysis_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for research_hypotheses
CREATE POLICY "Users can view their own hypotheses" ON public.research_hypotheses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own hypotheses" ON public.research_hypotheses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own hypotheses" ON public.research_hypotheses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own hypotheses" ON public.research_hypotheses
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for research_analysis_activity
CREATE POLICY "Users can view their own activity" ON public.research_analysis_activity
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity" ON public.research_analysis_activity
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to tables with updated_at columns
CREATE TRIGGER update_research_documents_updated_at
    BEFORE UPDATE ON public.research_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_literature_reviews_updated_at
    BEFORE UPDATE ON public.literature_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gap_analyses_updated_at
    BEFORE UPDATE ON public.research_gap_analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hypotheses_updated_at
    BEFORE UPDATE ON public.research_hypotheses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create storage bucket for research files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'research-files',
    'research-files',
    false,
    52428800, -- 50MB limit
    ARRAY['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain']
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for research files
CREATE POLICY "Users can upload their own research files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'research-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own research files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'research-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own research files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'research-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own research files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'research-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
