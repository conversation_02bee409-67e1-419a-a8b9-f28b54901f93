/**
 * AI Tutor - Main Component
 * Provides personalized AI tutoring experiences with flexible learning modes
 */

import React, { useState, useEffect } from 'react';
import { EnhancedTutorChat } from './components/EnhancedTutorChat';
import { DocumentUploader } from './components/TutorHero';
import { ResearchSettings } from './components/ResearchSettings';
import { EnhancedQuizGenerator } from './components/EnhancedQuizGenerator';
import { QuizInterface } from './components/QuizInterface';
import { InteractiveGames } from './components/InteractiveGames';
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  GraduationCap,
  MessageSquare,
  BookOpen,
  Settings,
  Brain,
  FileText,
  Search,
  Trophy,
  Target,
  Gamepad2
} from "lucide-react";
import { Quiz, ResearchDocument } from './types';
import { documentService } from './services/document.service';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

export function AITutor() {
  const [activeTab, setActiveTab] = useState('chat');
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [showQuizInterface, setShowQuizInterface] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<ResearchDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<ResearchDocument | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState({
    preferredModel: 'gemini-2.5-flash',
    educationLevel: 'intermediate', // Changed from 'high-school' to research-focused
    useCodeExecution: false,
    useWebSearch: true,
    theme: 'light'
  });

  // Load user documents on component mount
  useEffect(() => {
    loadUserDocuments();
  }, []);

  const loadUserDocuments = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const documents = await documentService.getUserDocuments(user.id);
        setUploadedDocuments(documents);
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewConversation = () => {
    // Reset any conversation state if needed
    console.log('Starting new conversation');
  };

  const handleDocumentUploaded = async (document: ResearchDocument) => {
    try {
      console.log('Document uploaded:', document.title);
      setUploadedDocuments(prev => [...prev, document]);
      setSelectedDocument(document);
      toast.success(`Document "${document.title}" uploaded successfully!`);
    } catch (error) {
      console.error('Failed to handle document upload:', error);
      toast.error('Failed to process uploaded document');
    }
  };

  const handleDocumentSelected = async (document: ResearchDocument) => {
    try {
      console.log('Document selected:', document.title);
      setSelectedDocument(document);
    } catch (error) {
      console.error('Failed to select document:', error);
      toast.error('Failed to select document');
    }
  };

  const handleUpdateSettings = (newSettings: any) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    setCurrentQuiz(quiz);
    setShowQuizInterface(true);
  };

  const handleQuizComplete = (results: any) => {
    console.log('Quiz completed with results:', results);
    setShowQuizInterface(false);
    setCurrentQuiz(null);
    // Could save results to database here
  };

  const handleQuizExit = () => {
    setShowQuizInterface(false);
    setCurrentQuiz(null);
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg">
                <Brain className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  AI Tutor
                </h1>
                <p className="text-sm text-gray-600">Your Personal Learning Assistant</p>
              </div>
            </div>

            {/* Navigation Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-auto">
              <TabsList className="bg-gray-100/80 backdrop-blur-sm border border-gray-200/50 shadow-sm">
                <TabsTrigger
                  value="chat"
                  className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <MessageSquare className="w-4 h-4" />
                  <span>Chat</span>
                </TabsTrigger>
                <TabsTrigger
                  value="quiz"
                  className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Trophy className="w-4 h-4" />
                  <span>Quiz</span>
                </TabsTrigger>
                <TabsTrigger
                  value="games"
                  className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Gamepad2 className="w-4 h-4" />
                  <span>Games</span>
                </TabsTrigger>
                <TabsTrigger
                  value="documents"
                  className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <FileText className="w-4 h-4" />
                  <span>Documents</span>
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="flex items-center space-x-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Settings className="w-4 h-4" />
                  <span>Settings</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="h-[calc(100vh-4rem)]">
        {showQuizInterface && currentQuiz ? (
          <QuizInterface
            quiz={currentQuiz}
            onComplete={handleQuizComplete}
            onExit={handleQuizExit}
          />
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsContent value="chat" className="h-full m-0 p-4">
              <EnhancedTutorChat
                uploadedDocuments={uploadedDocuments}
                onNewConversation={handleNewConversation}
              />
            </TabsContent>

            <TabsContent value="quiz" className="h-full m-0 p-4 overflow-y-auto">
              <EnhancedQuizGenerator
                onQuizGenerated={handleQuizGenerated}
                uploadedDocuments={uploadedDocuments}
              />
            </TabsContent>

            <TabsContent value="games" className="h-full m-0 p-4 overflow-y-auto">
              <InteractiveGames
                selectedDocument={selectedDocument}
                uploadedDocuments={uploadedDocuments}
              />
            </TabsContent>

            <TabsContent value="documents" className="h-full m-0 p-4">
              <div className="max-w-7xl mx-auto">
                <DocumentUploader
                  onDocumentUploaded={handleDocumentUploaded}
                  onDocumentSelected={handleDocumentSelected}
                  documents={uploadedDocuments}
                  isLoading={isLoading}
                />
              </div>
            </TabsContent>

            <TabsContent value="settings" className="h-full m-0 p-4">
              <div className="max-w-7xl mx-auto">
                <ResearchSettings
                  settings={settings}
                  onUpdateSettings={handleUpdateSettings}
                  onBack={() => setActiveTab('chat')}
                />
              </div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
