# AI Presentation Generator

A comprehensive AI-powered presentation generator module integrated into the research platform. This module follows the established architectural patterns and provides a complete workflow for creating professional presentations.

## Features

### ✨ Core Functionality
- **AI-Powered Generation**: Two-phase generation (outline → slides) using configurable AI models
- **Multiple Slide Layouts**: Support for 7+ slide types (title, content, bullet points, two-column, image-text, quote, conclusion)
- **Theme System**: 5 built-in themes (Modern, Minimal, Corporate, Creative, Dark) with customization options
- **Export Options**: Multiple formats (PDF, PowerPoint, HTML, Markdown) with quality settings
- **Real-time Progress**: Live generation progress with detailed feedback
- **Interactive Editing**: Grid and list view modes with drag-and-drop reordering

### 🎨 User Experience
- **Step-by-Step Workflow**: Guided process from input → outline → generation → editing → export
- **Responsive Design**: Works seamlessly across desktop and mobile devices
- **Modern UI**: Consistent with existing platform design using shadcn/ui components
- **Progress Tracking**: Visual indicators and progress bars throughout the generation process

### 🔧 Technical Features
- **TypeScript**: Fully typed with comprehensive interfaces
- **Zustand State Management**: Centralized state with devtools support
- **Modular Architecture**: Clean separation of concerns with services, stores, and components
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Performance Optimized**: Efficient rendering and state updates

## Architecture

### File Structure
```
src/components/research/presentation-generator/
├── PresentationGenerator.tsx          # Main component
├── types.ts                          # TypeScript interfaces
├── constants.ts                      # Themes, templates, and constants
├── index.ts                         # Module exports
├── components/                      # UI components
│   ├── PresentationMetadataForm.tsx # Input form
│   ├── OutlineReview.tsx           # Outline review and editing
│   ├── GenerationPanel.tsx         # Generation progress
│   ├── SlideEditor.tsx             # Slide management and editing
│   ├── ExportDialog.tsx            # Export options
│   └── index.ts                    # Component exports
├── services/                       # Business logic
│   ├── presentation-ai.service.ts  # AI generation service
│   └── index.ts                    # Service exports
├── stores/                         # State management
│   └── presentation.store.ts       # Zustand store
└── utils/                          # Utility functions (future)
```

### Key Components

#### 1. PresentationGenerator (Main Component)
- Orchestrates the entire presentation creation workflow
- Manages step navigation and state transitions
- Provides consistent UI layout and progress tracking

#### 2. PresentationMetadataForm
- Collects user input (title, topic, style, language, etc.)
- Form validation using react-hook-form and zod
- Style selection with visual cards

#### 3. OutlineReview
- Displays AI-generated presentation outline
- Allows editing, reordering, and customization of slides
- Shows estimated duration and layout types

#### 4. GenerationPanel
- Real-time generation progress with visual feedback
- Step-by-step generation status
- Error handling and retry options

#### 5. SlideEditor
- Grid and list view modes for slide management
- Slide reordering, duplication, and deletion
- Theme selection and presentation overview
- Placeholder for rich text editor (future enhancement)

#### 6. ExportDialog
- Multiple export format options
- Quality and inclusion settings
- Progress tracking for export process

### State Management

The presentation generator uses Zustand for state management with the following key features:

- **Centralized State**: All presentation data, generation state, and UI state
- **Async Actions**: Support for AI service integration with proper error handling
- **Devtools Integration**: Redux DevTools support for debugging
- **Type Safety**: Fully typed store with TypeScript interfaces

### AI Service Integration

The `presentationAIService` provides:

- **Outline Generation**: Creates structured presentation outlines based on user input
- **Slide Content Generation**: Generates content for individual slides with proper formatting
- **Progress Tracking**: Real-time progress updates during generation
- **Error Handling**: Comprehensive error handling with fallback options

## Integration

### Navigation Integration
The presentation generator is integrated into the main research platform:

1. **Sidebar Menu**: Added "AI Presentation Generator" option with Presentation icon
2. **Routing**: Integrated into ResearchDashboard with "presentation-generator" route
3. **ActiveView Type**: Extended to include presentation generator

### Dependencies
- React 18+ with TypeScript
- Zustand for state management
- shadcn/ui component library
- react-hook-form with zod validation
- Lucide React icons
- Sonner for toast notifications

## Usage

### Basic Workflow
1. **Setup**: Enter presentation title, topic, audience, and preferences
2. **Outline**: Review and customize AI-generated outline
3. **Generation**: Watch real-time slide generation with progress tracking
4. **Editing**: Manage slides with grid/list views, reordering, and basic editing
5. **Export**: Choose format and export options for final presentation

### Customization
- **Themes**: Select from 5 built-in themes or customize colors and fonts
- **Slide Layouts**: Choose from 7+ layout types for different content needs
- **Export Options**: Multiple formats with quality and inclusion settings

## Future Enhancements

### Phase 2 Features
1. **Rich Text Editor**: Full Plate.js integration for slide content editing
2. **Image Integration**: AI-powered image generation and insertion
3. **Collaboration**: Real-time collaborative editing
4. **Templates**: Pre-built presentation templates for different use cases
5. **Presentation Mode**: Full-screen presentation view with navigation
6. **Advanced Themes**: More theme options and custom theme creation
7. **Speaker Notes**: Enhanced speaker notes with AI suggestions
8. **Analytics**: Presentation performance and engagement metrics

### Technical Improvements
1. **Caching**: Implement caching for generated content
2. **Offline Support**: Basic offline functionality
3. **Performance**: Optimize for large presentations
4. **Accessibility**: Enhanced accessibility features
5. **Mobile**: Improved mobile editing experience

## Development

### Adding New Features
1. Follow the established architectural patterns
2. Add types to `types.ts`
3. Update constants in `constants.ts`
4. Implement services in `services/`
5. Update store actions in `stores/presentation.store.ts`
6. Create UI components in `components/`

### Testing
- Unit tests for services and utilities
- Component tests for UI components
- Integration tests for complete workflows
- E2E tests for critical user paths

## Contributing

When contributing to the presentation generator:

1. Follow TypeScript best practices
2. Maintain consistency with existing UI patterns
3. Add proper error handling
4. Update documentation for new features
5. Test thoroughly across different scenarios

## Support

For issues or questions related to the presentation generator:

1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure proper integration with the main platform
4. Review the store state using Redux DevTools
