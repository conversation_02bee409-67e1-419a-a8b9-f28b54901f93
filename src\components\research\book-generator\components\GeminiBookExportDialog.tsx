import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  FileText, 
  Edit, 
  BookOpen,
  Clock,
  Users,
  Loader2,
  CheckCircle
} from "lucide-react";
import { BookMetadata, GeminiGeneratedChapter, Citation } from '../types';
import { documentExportService } from '../../paper-generator/services/document-export.service';
import { editorService } from '../../paper-generator/services/editor.service';
import { toast } from 'sonner';

interface GeminiBookExportDialogProps {
  bookMetadata: BookMetadata;
  generatedChapters: GeminiGeneratedChapter[];
  allCitations: Citation[];
  onEditInEditor: () => void;
}

export const GeminiBookExportDialog: React.FC<GeminiBookExportDialogProps> = ({
  bookMetadata,
  generatedChapters,
  allCitations,
  onEditInEditor
}) => {
  const [isExporting, setIsExporting] = useState<string | null>(null);
  const [exportError, setExportError] = useState<string | null>(null);

  // Generate complete book content with proper formatting
  const generateCompleteBookContent = (): string => {
    let content = '';

    // Title page
    content += `# ${bookMetadata.title}\n\n`;
    if (bookMetadata.subtitle) {
      content += `## ${bookMetadata.subtitle}\n\n`;
    }
    
    // Authors
    if (bookMetadata.authors && bookMetadata.authors.length > 0) {
      content += `**Authors: <AUTHORS>
    }

    // Book description
    if (bookMetadata.description) {
      content += `## About This Book\n\n${bookMetadata.description}\n\n`;
    }

    // Table of Contents
    content += `## Table of Contents\n\n`;
    generatedChapters.forEach((chapter, index) => {
      content += `${index + 1}. [Chapter ${chapter.order}: ${chapter.title}](#chapter-${chapter.order})\n`;
    });
    content += '\n---\n\n';

    // Chapters
    generatedChapters.forEach((chapter) => {
      content += `# Chapter ${chapter.order}: ${chapter.title} {#chapter-${chapter.order}}\n\n`;
      
      // Clean and format chapter content
      let chapterContent = chapter.content
        .replace(/^#{1,6}\s*/gm, '') // Remove existing headers
        .replace(/\n{3,}/g, '\n\n') // Normalize line breaks
        .trim();

      content += `${chapterContent}\n\n`;
      
      // Add chapter summary if available
      if (chapter.summary) {
        content += `### Chapter Summary\n\n${chapter.summary}\n\n`;
      }
      
      content += '---\n\n';
    });

    // References section
    if (allCitations.length > 0) {
      content += `# References\n\n`;
      const uniqueCitations = Array.from(new Set(allCitations.map(c => c.inTextFormat || c.text)));
      uniqueCitations.forEach((citation, index) => {
        content += `${index + 1}. ${citation}\n`;
      });
      content += '\n';
    }

    return content;
  };

  // Export to Word
  const handleExportToWord = async () => {
    setExportError(null);
    setIsExporting('docx');

    try {
      const content = generateCompleteBookContent();
      const fileName = `${bookMetadata.title.replace(/[^a-zA-Z0-9]/g, '_')}_book.docx`;
      
      // Use enhanced book export
      await documentExportService.exportEnhancedBookToDocx(
        content,
        {
          title: bookMetadata.title,
          subtitle: bookMetadata.subtitle,
          authors: bookMetadata.authors,
          description: bookMetadata.description,
          genre: bookMetadata.genre,
          targetAudience: bookMetadata.targetAudience
        },
        {
          includeTableOfContents: true,
          includePreface: false,
          chapterNumbering: 'numeric',
          pageNumbering: true,
          fontSize: 'medium',
          lineSpacing: 'double'
        },
        fileName
      );

      toast.success('Book exported to Word successfully!');
    } catch (error: any) {
      console.error('Word export error:', error);
      setExportError(`Failed to export to Word: ${error.message || 'Please try again'}`);
      toast.error('Failed to export to Word format');
    } finally {
      setIsExporting(null);
    }
  };

  // Export to PDF
  const handleExportToPDF = async () => {
    setExportError(null);
    setIsExporting('pdf');

    try {
      const content = generateCompleteBookContent();
      const fileName = `${bookMetadata.title.replace(/[^a-zA-Z0-9]/g, '_')}_book.pdf`;
      
      // Use enhanced book export
      await documentExportService.exportEnhancedBookToPdf(
        content,
        {
          title: bookMetadata.title,
          subtitle: bookMetadata.subtitle,
          authors: bookMetadata.authors,
          description: bookMetadata.description,
          genre: bookMetadata.genre,
          targetAudience: bookMetadata.targetAudience
        },
        {
          includeTableOfContents: true,
          includePreface: false,
          chapterNumbering: 'numeric',
          pageNumbering: true,
          fontSize: 'medium',
          lineSpacing: 'double'
        },
        fileName
      );

      toast.info('PDF export window opened. Use your browser\'s print dialog to save as PDF.');
    } catch (error: any) {
      console.error('PDF export error:', error);
      setExportError(`Failed to export to PDF: ${error.message || 'Please try again'}`);
      toast.error('Failed to export to PDF format');
    } finally {
      setIsExporting(null);
    }
  };

  // Edit in main editor
  const handleEditInEditor = () => {
    const content = generateCompleteBookContent();
    
    // Convert markdown to HTML for the editor
    const htmlContent = convertMarkdownToHTML(content);

    editorService.sendToMainEditor({
      title: bookMetadata.title,
      content: htmlContent
    });

    onEditInEditor();
    toast.success("Book loaded in editor for further editing.");
  };

  // Convert markdown to HTML for editor
  const convertMarkdownToHTML = (markdown: string): string => {
    let html = markdown
      // Headers
      .replace(/^# (.+)$/gm, '<h1>$1</h1>')
      .replace(/^## (.+)$/gm, '<h2>$1</h2>')
      .replace(/^### (.+)$/gm, '<h3>$1</h3>')
      // Bold & Italic
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      // Lists
      .replace(/^\- (.+)$/gm, '<ul><li>$1</li></ul>')
      .replace(/^\d+\. (.+)$/gm, '<ol><li>$1</li></ol>')
      // Fix nested lists
      .replace(/<\/ul>\n<ul>/g, '')
      .replace(/<\/ol>\n<ol>/g, '')
      // Paragraphs
      .replace(/^(?!<[oh][lu]>|<li>|<h[1-6]>)(.+)$/gm, '<p>$1</p>')
      // Fix empty lines
      .replace(/\n/g, '<br>')
      // Clean up
      .replace(/<p><\/p>/g, '')
      .replace(/<p><br><\/p>/g, '<br>');

    return html;
  };

  const totalWords = generatedChapters.reduce((sum, chapter) => sum + chapter.wordCount, 0);
  const estimatedReadingTime = Math.ceil(totalWords / 200);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          size="lg"
          className="px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 shadow-lg transform hover:scale-105 transition-all duration-200"
        >
          <Download className="h-5 w-5 mr-2" />
          Export Complete Book
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Export Generated Book
          </DialogTitle>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <Badge variant="secondary" className="flex items-center gap-1">
              <BookOpen className="h-3 w-3" />
              {generatedChapters.length} chapters
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {totalWords.toLocaleString()} words
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              ~{estimatedReadingTime} min read
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Book Summary */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-2">{bookMetadata.title}</h3>
              {bookMetadata.subtitle && (
                <p className="text-gray-600 mb-2">{bookMetadata.subtitle}</p>
              )}
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Genre: {bookMetadata.genre}</span>
                <span>•</span>
                <span>Audience: {bookMetadata.targetAudience}</span>
              </div>
            </CardContent>
          </Card>

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Word Export */}
            <Card className="border-blue-200 hover:border-blue-300 transition-colors">
              <CardContent className="p-0">
                <Button
                  onClick={handleExportToWord}
                  disabled={isExporting === 'docx'}
                  variant="outline"
                  className="w-full h-auto p-6 justify-start border-0 hover:bg-blue-50"
                >
                  <div className="flex items-center gap-4 w-full">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      {isExporting === 'docx' ? (
                        <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
                      ) : (
                        <FileText className="h-6 w-6 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-lg">Export to Word</div>
                      <div className="text-sm text-gray-600 mt-1">
                        Download as .docx with professional formatting
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">Professional format</Badge>
                        <Badge variant="outline" className="text-xs">Editable</Badge>
                      </div>
                    </div>
                  </div>
                </Button>
              </CardContent>
            </Card>

            {/* PDF Export */}
            <Card className="border-red-200 hover:border-red-300 transition-colors">
              <CardContent className="p-0">
                <Button
                  onClick={handleExportToPDF}
                  disabled={isExporting === 'pdf'}
                  variant="outline"
                  className="w-full h-auto p-6 justify-start border-0 hover:bg-red-50"
                >
                  <div className="flex items-center gap-4 w-full">
                    <div className="p-3 bg-red-100 rounded-lg">
                      {isExporting === 'pdf' ? (
                        <Loader2 className="h-6 w-6 text-red-600 animate-spin" />
                      ) : (
                        <Download className="h-6 w-6 text-red-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-lg">Export to PDF</div>
                      <div className="text-sm text-gray-600 mt-1">
                        Generate PDF with print-ready formatting
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">Print ready</Badge>
                        <Badge variant="outline" className="text-xs">Shareable</Badge>
                      </div>
                    </div>
                  </div>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Edit in Editor Option */}
          <Card className="border-gray-200">
            <CardContent className="p-0">
              <Button
                onClick={handleEditInEditor}
                variant="outline"
                className="w-full h-auto p-6 justify-start border-2 hover:border-gray-300 hover:bg-gray-50"
              >
                <div className="flex items-center gap-4 w-full">
                  <div className="p-3 bg-gray-100 rounded-lg">
                    <Edit className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-semibold text-lg">Edit in Main Editor</div>
                    <div className="text-sm text-gray-600 mt-1">
                      Open in the advanced text editor for further refinement and customization
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">Advanced editing</Badge>
                      <Badge variant="outline" className="text-xs">AI assistance</Badge>
                    </div>
                  </div>
                </div>
              </Button>
            </CardContent>
          </Card>

          {/* Error Display */}
          {exportError && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-red-700">
                  <div className="text-sm">{exportError}</div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
