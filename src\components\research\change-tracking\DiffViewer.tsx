import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Check, 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Eye, 
  EyeOff,
  RotateCcw,
  CheckCheck
} from 'lucide-react';
import { changeTrackingService } from './ChangeTrackingService';
import { ChangeTrackingState, ChangeNavigationState } from './types';
import { cn } from '@/lib/utils';

interface DiffViewerProps {
  onAcceptChange: (changeId: string) => void;
  onRejectChange: (changeId: string) => void;
  onAcceptAllChanges: () => void;
  onRejectAllChanges: () => void;
  onNavigateChange: (direction: 'next' | 'previous') => void;
  className?: string;
}

export function DiffViewer({
  onAcceptChange,
  onRejectChange,
  onAcceptAllChanges,
  onRejectAllChanges,
  onNavigateChange,
  className
}: DiffViewerProps) {
  const [state, setState] = useState<ChangeTrackingState>(changeTrackingService.getState());
  const [navigationState, setNavigationState] = useState<ChangeNavigationState>(
    changeTrackingService.getNavigationState()
  );
  const [showDiffDetails, setShowDiffDetails] = useState(true);

  useEffect(() => {
    const unsubscribe = changeTrackingService.subscribe((newState) => {
      setState(newState);
      setNavigationState(changeTrackingService.getNavigationState());
    });

    return unsubscribe;
  }, []);

  const currentChange = changeTrackingService.getCurrentChange();
  const diffData = changeTrackingService.generateDiffData();
  const pendingChangesCount = changeTrackingService.getPendingChangesCount();

  if (!state.isTrackingEnabled || pendingChangesCount === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center">
          <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Changes to Review
          </h3>
          <p className="text-gray-600">
            AI modifications will appear here for review when made.
          </p>
        </CardContent>
      </Card>
    );
  }

  const renderDiffContent = () => {
    if (!diffData) return null;

    return (
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {diffData.map((part) => (
          <div
            key={part.id}
            className={cn(
              "p-2 rounded text-sm font-mono whitespace-pre-wrap",
              part.added && "bg-green-100 text-green-800 border-l-4 border-green-500",
              part.removed && "bg-red-100 text-red-800 border-l-4 border-red-500 line-through",
              !part.added && !part.removed && "bg-gray-50 text-gray-700"
            )}
          >
            {part.value}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg">Change Review</CardTitle>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {pendingChangesCount} pending
            </Badge>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDiffDetails(!showDiffDetails)}
            className="flex items-center gap-2"
          >
            {showDiffDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showDiffDetails ? 'Hide' : 'Show'} Details
          </Button>
        </div>

        {/* Navigation Controls */}
        {pendingChangesCount > 1 && (
          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateChange('previous')}
                disabled={!navigationState.hasPrevious}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              
              <span className="text-sm text-gray-600 px-2">
                {navigationState.currentIndex + 1} of {navigationState.totalChanges}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateChange('next')}
                disabled={!navigationState.hasNext}
                className="flex items-center gap-1"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Bulk Actions */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onRejectAllChanges}
                className="flex items-center gap-1 text-red-600 hover:text-red-700"
              >
                <RotateCcw className="h-4 w-4" />
                Reject All
              </Button>
              
              <Button
                variant="default"
                size="sm"
                onClick={onAcceptAllChanges}
                className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
              >
                <CheckCheck className="h-4 w-4" />
                Accept All
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Current Change Details */}
        {currentChange && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {currentChange.aiActionType}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {currentChange.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                {currentChange.prompt && (
                  <p className="text-sm text-gray-600 italic">
                    "{currentChange.prompt}"
                  </p>
                )}
              </div>

              {/* Individual Change Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRejectChange(currentChange.id)}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                  Reject
                </Button>
                
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => onAcceptChange(currentChange.id)}
                  className="flex items-center gap-1 bg-green-600 hover:bg-green-700"
                >
                  <Check className="h-4 w-4" />
                  Accept
                </Button>
              </div>
            </div>

            <Separator />
          </div>
        )}

        {/* Diff Content */}
        {showDiffDetails && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Changes Preview</h4>
            {renderDiffContent()}
          </div>
        )}

        {/* Legend */}
        <div className="flex items-center gap-4 text-xs text-gray-600 pt-2 border-t">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-100 border-l-2 border-green-500 rounded-sm"></div>
            <span>Added</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-100 border-l-2 border-red-500 rounded-sm"></div>
            <span>Removed</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
