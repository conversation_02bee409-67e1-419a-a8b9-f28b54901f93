/**
 * Deep Research Cost Estimation and Rate Limiting Service
 * Manages API usage, cost estimation, and intelligent batching
 */

import { DeepResearchOptions, ResearchOutline, SubTopic } from '../types';

export interface CostEstimate {
  totalTokens: number;
  estimatedCost: number;
  apiCalls: number;
  estimatedTime: number; // in seconds
  breakdown: {
    outlineGeneration: number;
    batchResearch: number;
    sectionGeneration: number;
  };
}

export interface RateLimitInfo {
  requestsPerMinute: number;
  tokensPerMinute: number;
  currentUsage: {
    requests: number;
    tokens: number;
    resetTime: Date;
  };
  canProceed: boolean;
  waitTime?: number; // seconds to wait
}

export interface BatchingStrategy {
  subtopicBatches: SubTopic[][];
  totalBatches: number;
  estimatedTimePerBatch: number;
  recommendedDelay: number; // seconds between batches
}

export class DeepResearchCostService {
  // Gemini API pricing (approximate)
  private readonly GEMINI_COST_PER_1K_TOKENS = 0.00075; // $0.00075 per 1K tokens
  private readonly GEMINI_RATE_LIMIT_RPM = 60; // requests per minute
  private readonly GEMINI_RATE_LIMIT_TPM = 1000000; // tokens per minute (1M)
  
  // Token estimation constants
  private readonly TOKENS_PER_WORD = 1.3; // approximate
  private readonly OUTLINE_BASE_TOKENS = 1000;
  private readonly RESEARCH_BASE_TOKENS = 2000;
  private readonly SECTION_BASE_TOKENS = 1500;
  
  // Rate limiting tracking
  private requestHistory: Date[] = [];
  private tokenHistory: { timestamp: Date; tokens: number }[] = [];

  /**
   * Estimate the cost of a deep research session
   */
  estimateCost(query: string, options: DeepResearchOptions, outline?: ResearchOutline): CostEstimate {
    const subtopicCount = outline?.subtopics.length || options.maxSubtopics;
    const wordsPerSection = options.wordsPerSection;
    
    // Estimate tokens for each phase
    const outlineTokens = this.estimateOutlineTokens(query, options);
    const researchTokens = this.estimateResearchTokens(subtopicCount, options);
    const sectionTokens = this.estimateSectionTokens(subtopicCount, wordsPerSection);
    
    const totalTokens = outlineTokens + researchTokens + sectionTokens;
    const estimatedCost = (totalTokens / 1000) * this.GEMINI_COST_PER_1K_TOKENS;
    
    // Estimate API calls
    const apiCalls = 1 + 1 + subtopicCount; // outline + batch research + sections
    
    // Estimate time (based on token processing speed)
    const estimatedTime = Math.ceil(totalTokens / 10000) * 30; // ~30 seconds per 10K tokens
    
    return {
      totalTokens,
      estimatedCost: Math.round(estimatedCost * 10000) / 10000, // round to 4 decimal places
      apiCalls,
      estimatedTime,
      breakdown: {
        outlineGeneration: outlineTokens,
        batchResearch: researchTokens,
        sectionGeneration: sectionTokens
      }
    };
  }

  /**
   * Check current rate limit status
   */
  checkRateLimit(): RateLimitInfo {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60000);
    
    // Clean old entries
    this.requestHistory = this.requestHistory.filter(timestamp => timestamp > oneMinuteAgo);
    this.tokenHistory = this.tokenHistory.filter(entry => entry.timestamp > oneMinuteAgo);
    
    const currentRequests = this.requestHistory.length;
    const currentTokens = this.tokenHistory.reduce((sum, entry) => sum + entry.tokens, 0);
    
    const canProceedRequests = currentRequests < this.GEMINI_RATE_LIMIT_RPM;
    const canProceedTokens = currentTokens < this.GEMINI_RATE_LIMIT_TPM;
    const canProceed = canProceedRequests && canProceedTokens;
    
    let waitTime: number | undefined;
    if (!canProceed) {
      if (!canProceedRequests) {
        const oldestRequest = this.requestHistory[0];
        waitTime = Math.ceil((oldestRequest.getTime() + 60000 - now.getTime()) / 1000);
      } else {
        const oldestToken = this.tokenHistory[0];
        waitTime = Math.ceil((oldestToken.timestamp.getTime() + 60000 - now.getTime()) / 1000);
      }
    }
    
    return {
      requestsPerMinute: this.GEMINI_RATE_LIMIT_RPM,
      tokensPerMinute: this.GEMINI_RATE_LIMIT_TPM,
      currentUsage: {
        requests: currentRequests,
        tokens: currentTokens,
        resetTime: new Date(now.getTime() + 60000)
      },
      canProceed,
      waitTime
    };
  }

  /**
   * Record API usage for rate limiting
   */
  recordApiUsage(tokens: number): void {
    const now = new Date();
    this.requestHistory.push(now);
    this.tokenHistory.push({ timestamp: now, tokens });
  }

  /**
   * Create intelligent batching strategy
   */
  createBatchingStrategy(subtopics: SubTopic[], options: DeepResearchOptions): BatchingStrategy {
    const maxSubtopicsPerBatch = this.calculateOptimalBatchSize(options);
    const batches: SubTopic[][] = [];
    
    // Group subtopics by priority first
    const highPriority = subtopics.filter(st => st.priority === 'high');
    const mediumPriority = subtopics.filter(st => st.priority === 'medium');
    const lowPriority = subtopics.filter(st => st.priority === 'low');
    
    // Create batches, prioritizing high-priority items
    const allSubtopics = [...highPriority, ...mediumPriority, ...lowPriority];
    
    for (let i = 0; i < allSubtopics.length; i += maxSubtopicsPerBatch) {
      batches.push(allSubtopics.slice(i, i + maxSubtopicsPerBatch));
    }
    
    const estimatedTimePerBatch = this.estimateBatchTime(maxSubtopicsPerBatch, options);
    const recommendedDelay = this.calculateRecommendedDelay(batches.length);
    
    return {
      subtopicBatches: batches,
      totalBatches: batches.length,
      estimatedTimePerBatch,
      recommendedDelay
    };
  }

  /**
   * Validate if research can proceed within limits
   */
  validateResearchLimits(estimate: CostEstimate, userLimits?: {
    maxCost?: number;
    maxTime?: number;
    maxTokens?: number;
  }): { canProceed: boolean; violations: string[] } {
    const violations: string[] = [];
    
    if (userLimits?.maxCost && estimate.estimatedCost > userLimits.maxCost) {
      violations.push(`Estimated cost ($${estimate.estimatedCost}) exceeds limit ($${userLimits.maxCost})`);
    }
    
    if (userLimits?.maxTime && estimate.estimatedTime > userLimits.maxTime) {
      violations.push(`Estimated time (${estimate.estimatedTime}s) exceeds limit (${userLimits.maxTime}s)`);
    }
    
    if (userLimits?.maxTokens && estimate.totalTokens > userLimits.maxTokens) {
      violations.push(`Estimated tokens (${estimate.totalTokens}) exceeds limit (${userLimits.maxTokens})`);
    }
    
    // Check against API limits
    if (estimate.totalTokens > this.GEMINI_RATE_LIMIT_TPM) {
      violations.push(`Research requires ${estimate.totalTokens} tokens, exceeding API limit of ${this.GEMINI_RATE_LIMIT_TPM} tokens per minute`);
    }
    
    return {
      canProceed: violations.length === 0,
      violations
    };
  }

  /**
   * Get cost optimization suggestions
   */
  getOptimizationSuggestions(estimate: CostEstimate, options: DeepResearchOptions): string[] {
    const suggestions: string[] = [];
    
    if (estimate.estimatedCost > 0.10) {
      suggestions.push('Consider reducing the number of subtopics to lower costs');
    }
    
    if (options.wordsPerSection > 800) {
      suggestions.push('Reducing words per section from ' + options.wordsPerSection + ' to 700 could save ~20% on costs');
    }
    
    if (options.researchDepth === 'comprehensive') {
      suggestions.push('Using "advanced" instead of "comprehensive" research depth could reduce costs by ~30%');
    }
    
    if (estimate.estimatedTime > 300) { // 5 minutes
      suggestions.push('Consider breaking this into multiple smaller research sessions');
    }
    
    return suggestions;
  }

  // Private helper methods
  private estimateOutlineTokens(query: string, options: DeepResearchOptions): number {
    const queryTokens = query.length * 0.25; // rough estimate
    const complexityMultiplier = options.researchDepth === 'comprehensive' ? 1.5 : 
                                 options.researchDepth === 'advanced' ? 1.2 : 1.0;
    
    return Math.ceil((this.OUTLINE_BASE_TOKENS + queryTokens) * complexityMultiplier);
  }

  private estimateResearchTokens(subtopicCount: number, options: DeepResearchOptions): number {
    const baseTokensPerSubtopic = this.RESEARCH_BASE_TOKENS;
    const depthMultiplier = options.researchDepth === 'comprehensive' ? 2.0 : 
                           options.researchDepth === 'advanced' ? 1.5 : 1.0;
    
    return Math.ceil(subtopicCount * baseTokensPerSubtopic * depthMultiplier);
  }

  private estimateSectionTokens(subtopicCount: number, wordsPerSection: number): number {
    const tokensPerSection = this.SECTION_BASE_TOKENS + (wordsPerSection * this.TOKENS_PER_WORD);
    return Math.ceil(subtopicCount * tokensPerSection);
  }

  private calculateOptimalBatchSize(options: DeepResearchOptions): number {
    // Base batch size on research depth and API limits
    const baseSize = options.researchDepth === 'comprehensive' ? 3 : 
                     options.researchDepth === 'advanced' ? 5 : 8;
    
    // Ensure we don't exceed token limits per batch
    const maxTokensPerBatch = this.GEMINI_RATE_LIMIT_TPM * 0.8; // 80% of limit for safety
    const estimatedTokensPerSubtopic = this.estimateResearchTokens(1, options);
    const maxByTokens = Math.floor(maxTokensPerBatch / estimatedTokensPerSubtopic);
    
    return Math.min(baseSize, maxByTokens, 8); // Cap at 8 for manageability
  }

  private estimateBatchTime(batchSize: number, options: DeepResearchOptions): number {
    const tokensPerBatch = this.estimateResearchTokens(batchSize, options);
    return Math.ceil(tokensPerBatch / 10000) * 30; // ~30 seconds per 10K tokens
  }

  private calculateRecommendedDelay(totalBatches: number): number {
    if (totalBatches <= 1) return 0;
    
    // Recommend delays to stay within rate limits
    const baseDelay = 10; // 10 seconds base delay
    const scalingFactor = Math.min(totalBatches * 2, 30); // Max 30 seconds
    
    return baseDelay + scalingFactor;
  }
}

export const deepResearchCostService = new DeepResearchCostService();
