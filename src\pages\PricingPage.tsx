import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Check,
  X,
  Star,
  Zap,
  Crown,
  Rocket,
  GraduationCap,
  Users,
  Building,
  ArrowRight,
  FileText,
  Book,
  FileCheck,
  BarChart3,
  Clock,
  Shield,
  Headphones
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const PricingPage = () => {
  const navigate = useNavigate();
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: "Free",
      description: "Perfect for getting started with basic research assistance",
      price: { monthly: 0, annual: 0 },
      badge: null,
      icon: <GraduationCap className="h-6 w-6" />,
      features: [
        "3 AI-generated papers per month",
        "Basic article review (5 papers/month)",
        "Standard AI models",
        "PDF export",
        "Community support",
        "Basic templates"
      ],
      limitations: [
        "No book generation",
        "Limited research analysis",
        "No priority support",
        "Verbira watermark"
      ],
      cta: "Get Started Free",
      popular: false,
      gradient: "from-gray-600 to-gray-700"
    },
    {
      name: "Researcher",
      description: "Ideal for individual researchers and graduate students",
      price: { monthly: 29, annual: 290 },
      badge: "Most Popular",
      icon: <Star className="h-6 w-6" />,
      features: [
        "Unlimited AI-generated papers",
        "Advanced article review with sentence-level analysis",
        "AI book generation (2 books/month)",
        "Research analysis with gap identification",
        "Premium AI models (GPT-4, Claude)",
        "All export formats (PDF, DOCX, LaTeX)",
        "Priority email support",
        "Advanced templates library",
        "Citation management",
        "Collaboration with 2 team members"
      ],
      limitations: [],
      cta: "Start Free Trial",
      popular: true,
      gradient: "from-blue-600 to-purple-600"
    },
    {
      name: "Professional",
      description: "For research teams and academic institutions",
      price: { monthly: 79, annual: 790 },
      badge: "Best Value",
      icon: <Crown className="h-6 w-6" />,
      features: [
        "Everything in Researcher",
        "Unlimited book generation",
        "Batch processing for multiple papers",
        "Advanced research analytics dashboard",
        "Custom AI model fine-tuning",
        "White-label options",
        "API access",
        "SSO integration",
        "Team collaboration (up to 10 members)",
        "Advanced security features",
        "Phone & chat support",
        "Custom templates creation"
      ],
      limitations: [],
      cta: "Start Free Trial",
      popular: false,
      gradient: "from-purple-600 to-pink-600"
    },
    {
      name: "Enterprise",
      description: "For large organizations with custom requirements",
      price: { monthly: "Custom", annual: "Custom" },
      badge: null,
      icon: <Building className="h-6 w-6" />,
      features: [
        "Everything in Professional",
        "Unlimited team members",
        "Dedicated account manager",
        "Custom integrations",
        "On-premise deployment options",
        "Advanced compliance (GDPR, HIPAA)",
        "Custom AI model training",
        "24/7 dedicated support",
        "SLA guarantees",
        "Custom contract terms",
        "Training & onboarding",
        "Advanced analytics & reporting"
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false,
      gradient: "from-indigo-600 to-purple-600"
    }
  ];

  const faqs = [
    {
      question: "What AI models do you use?",
      answer: "We use state-of-the-art AI models including GPT-4, Claude 3.5, and Google Gemini. Different models excel at different tasks, and our platform automatically selects the best model for each specific use case."
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period, and you won't be charged again."
    },
    {
      question: "Is my research data secure?",
      answer: "Absolutely. We use enterprise-grade encryption, don't store your content after processing, and comply with major data protection regulations including GDPR and CCPA."
    },
    {
      question: "Do you offer academic discounts?",
      answer: "Yes! We offer up to 50% discounts for students and educational institutions. Contact our sales team with your academic email for verification."
    },
    {
      question: "What file formats do you support?",
      answer: "We support input formats like PDF, DOCX, TXT, and various image formats. For export, we offer PDF, DOCX, LaTeX, and plain text formats."
    },
    {
      question: "How accurate are the AI-generated citations?",
      answer: "Our AI generates realistic citations following academic standards (APA, MLA, etc.). However, we recommend verifying citations for published work, as some may be generated examples."
    }
  ];

  const getPrice = (plan: any) => {
    if (typeof plan.price.monthly === 'string') return plan.price.monthly;
    return isAnnual ? plan.price.annual : plan.price.monthly;
  };

  const getSavings = (plan: any) => {
    if (typeof plan.price.monthly === 'string') return null;
    const monthlyCost = plan.price.monthly * 12;
    const annualCost = plan.price.annual;
    const savings = monthlyCost - annualCost;
    const percentage = Math.round((savings / monthlyCost) * 100);
    return { amount: savings, percentage };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                <GraduationCap className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Verbira
                </h1>
                <p className="text-sm text-gray-600">AI Research Assistant</p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</a>
              <a href="#pricing" className="text-blue-600 font-medium">Pricing</a>
              <a href="/docs" className="text-gray-600 hover:text-blue-600 transition-colors">Docs</a>
              <Button 
                variant="outline" 
                onClick={() => navigate('/login')}
                className="border-2 hover:bg-blue-50"
              >
                Sign In
              </Button>
              <Button 
                onClick={() => navigate('/app')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Get Started
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent leading-tight">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Choose the perfect plan for your research needs. Start free, upgrade anytime.
          </p>

          {/* Annual/Monthly Toggle */}
          <div className="flex items-center justify-center gap-4 mb-12">
            <span className={`text-lg ${!isAnnual ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <Switch
              checked={isAnnual}
              onCheckedChange={setIsAnnual}
              className="data-[state=checked]:bg-green-500"
            />
            <span className={`text-lg ${isAnnual ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Annual
            </span>
            {isAnnual && (
              <Badge className="bg-green-100 text-green-700 hover:bg-green-100 ml-2">
                Save up to 17%
              </Badge>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-20">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative border-2 transition-all duration-300 hover:shadow-xl ${
                plan.popular 
                  ? 'border-blue-500 shadow-lg scale-105' 
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              {plan.badge && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1">
                    {plan.badge}
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className={`w-12 h-12 mx-auto rounded-xl bg-gradient-to-r ${plan.gradient} flex items-center justify-center text-white mb-4`}>
                  {plan.icon}
                </div>
                <CardTitle className="text-2xl mb-2">{plan.name}</CardTitle>
                <CardDescription className="text-gray-600 mb-4">
                  {plan.description}
                </CardDescription>
                
                <div className="mb-4">
                  <div className="flex items-baseline justify-center mb-2">
                    <span className="text-4xl font-bold text-gray-900">
                      ${typeof getPrice(plan) === 'string' ? '' : getPrice(plan)}
                    </span>
                    {typeof getPrice(plan) === 'string' ? (
                      <span className="text-4xl font-bold text-gray-900">{getPrice(plan)}</span>
                    ) : (
                      <span className="text-gray-500 ml-1">
                        /{isAnnual ? 'year' : 'month'}
                      </span>
                    )}
                  </div>
                  
                  {isAnnual && getSavings(plan) && (
                    <div className="text-sm text-green-600 font-medium">
                      Save ${getSavings(plan)?.amount} ({getSavings(plan)?.percentage}% off)
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <Button 
                  className={`w-full mb-6 ${
                    plan.popular 
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700' 
                      : 'bg-gray-900 hover:bg-gray-800'
                  }`}
                  onClick={() => plan.name === 'Enterprise' ? navigate('/contact') : navigate('/app')}
                >
                  {plan.cta}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>

                <div className="space-y-3">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                  
                  {plan.limitations.map((limitation, idx) => (
                    <div key={idx} className="flex items-start gap-3">
                      <X className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-400">{limitation}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enterprise CTA */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 md:p-12 text-center mb-20">
          <h3 className="text-3xl font-bold text-white mb-4">
            Need Something Custom?
          </h3>
          <p className="text-xl text-gray-300 mb-6 max-w-2xl mx-auto">
            Large organization? Custom requirements? Let's build a solution that fits your exact needs.
          </p>
          <Button 
            size="lg"
            onClick={() => navigate('/contact')}
            className="bg-white text-gray-900 hover:bg-gray-100"
          >
            <Building className="mr-2 h-5 w-5" />
            Contact Enterprise Sales
          </Button>
        </div>
      </section>

      {/* Feature Comparison */}
      <section className="bg-white/50 backdrop-blur-sm py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Compare Features
            </h2>
            <p className="text-xl text-gray-600">
              See what's included in each plan
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <FileText className="h-6 w-6 text-blue-600" />
                <h3 className="text-xl font-semibold">AI Paper Generation</h3>
              </div>
              <ul className="space-y-2 text-gray-600">
                <li>• Research paper writing</li>
                <li>• Academic formatting</li>
                <li>• Citation generation</li>
                <li>• Multiple export formats</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <FileCheck className="h-6 w-6 text-green-600" />
                <h3 className="text-xl font-semibold">Article Review</h3>
              </div>
              <ul className="space-y-2 text-gray-600">
                <li>• Sentence-level analysis</li>
                <li>• Improvement suggestions</li>
                <li>• Academic quality scoring</li>
                <li>• Plagiarism insights</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <BarChart3 className="h-6 w-6 text-purple-600" />
                <h3 className="text-xl font-semibold">Research Analysis</h3>
              </div>
              <ul className="space-y-2 text-gray-600">
                <li>• Literature review generation</li>
                <li>• Research gap identification</li>
                <li>• Multi-paper analysis</li>
                <li>• Trend identification</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600">
            Got questions? We've got answers.
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          {faqs.map((faq, index) => (
            <Card key={index} className="border-0 shadow-lg">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">Still have questions?</p>
          <Button 
            variant="outline" 
            onClick={() => navigate('/contact')}
            className="border-2 hover:bg-blue-50"
          >
            <Headphones className="mr-2 h-4 w-4" />
            Contact Support
          </Button>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 py-20">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Accelerate Your Research?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Start your free trial today. No credit card required.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              onClick={() => navigate('/app')}
              className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4"
            >
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => navigate('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <span className="text-xl font-bold">Verbira</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Empowering researchers worldwide with advanced AI tools.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/docs" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="/api" className="hover:text-white transition-colors">API</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/about" className="hover:text-white transition-colors">About</a></li>
                <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/help" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="/terms" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Verbira. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PricingPage;
