import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Database, 
  BarChart3, 
  Upload, 
  Brain,
  Circle,
  Square,
  Grid,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp
} from "lucide-react";

/**
 * Test component to verify all icons and basic functionality work
 */
export const TestComponent: React.FC = () => {
  return (
    <Card className="max-w-2xl mx-auto m-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Visualization Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center p-4 border rounded">
            <Upload className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <p className="text-sm">Upload</p>
          </div>
          
          <div className="text-center p-4 border rounded">
            <Brain className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <p className="text-sm">Analysis</p>
          </div>
          
          <div className="text-center p-4 border rounded">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p className="text-sm">Charts</p>
          </div>
          
          <div className="text-center p-4 border rounded">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <p className="text-sm">Insights</p>
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="font-medium">Chart Type Icons:</h3>
          <div className="flex gap-4 flex-wrap">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span className="text-sm">Histogram</span>
            </div>
            <div className="flex items-center gap-2">
              <Circle className="h-4 w-4" />
              <span className="text-sm">Scatter</span>
            </div>
            <div className="flex items-center gap-2">
              <Grid className="h-4 w-4" />
              <span className="text-sm">Heatmap</span>
            </div>
            <div className="flex items-center gap-2">
              <Square className="h-4 w-4" />
              <span className="text-sm">Box Plot</span>
            </div>
            <div className="flex items-center gap-2">
              <LineChart className="h-4 w-4" />
              <span className="text-sm">Line Chart</span>
            </div>
            <div className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              <span className="text-sm">Pie Chart</span>
            </div>
          </div>
        </div>
        
        <Button className="w-full">
          Test Button - All Icons Working!
        </Button>
      </CardContent>
    </Card>
  );
};
