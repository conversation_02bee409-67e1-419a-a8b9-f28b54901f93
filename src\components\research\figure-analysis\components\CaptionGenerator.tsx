/**
 * Caption Generator Component
 * AI-powered academic caption generation with multiple styles and contexts
 */

import React, { useState, useEffect } from 'react';
import { 
  Copy, RefreshCw, Download, Settings, 
  BookOpen, Globe, Users, Sparkles, Check,
  ChevronDown, ChevronUp, Edit3, Save
} from 'lucide-react';
import { CaptionSuggestion, CitationStyle, FigureType } from '../types';
import { CITATION_STYLES, RESEARCH_FIELDS } from '../constants';

interface CaptionGeneratorProps {
  captions: CaptionSuggestion[];
  onRegenerateCaption: (context: CaptionContext) => void;
  onSaveCaption: (caption: string) => void;
  isGenerating?: boolean;
  className?: string;
}

interface CaptionContext {
  citationStyle: CitationStyle;
  researchField: string;
  purpose: 'descriptive' | 'analytical' | 'comparative' | 'explanatory';
  audience: 'general' | 'academic' | 'expert';
  language: string;
  userContext?: string;
}

export const CaptionGenerator: React.FC<CaptionGeneratorProps> = ({
  captions,
  onRegenerateCaption,
  onSaveCaption,
  isGenerating = false,
  className = '',
}) => {
  const [selectedCaption, setSelectedCaption] = useState<CaptionSuggestion | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [editingCaption, setEditingCaption] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  
  const [context, setContext] = useState<CaptionContext>({
    citationStyle: 'APA',
    researchField: 'Other',
    purpose: 'descriptive',
    audience: 'academic',
    language: 'en',
    userContext: '',
  });

  // Select first caption by default
  useEffect(() => {
    if (captions.length > 0 && !selectedCaption) {
      setSelectedCaption(captions[0]);
      setEditingCaption(captions[0].text);
    }
  }, [captions, selectedCaption]);

  /**
   * Handle copy to clipboard
   */
  const handleCopy = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  /**
   * Handle caption regeneration
   */
  const handleRegenerate = () => {
    onRegenerateCaption(context);
  };

  /**
   * Handle save edited caption
   */
  const handleSaveEdit = () => {
    if (editingCaption.trim()) {
      onSaveCaption(editingCaption.trim());
      setIsEditing(false);
    }
  };

  /**
   * Get confidence color
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600 bg-green-100';
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  /**
   * Get purpose icon
   */
  const getPurposeIcon = (purpose: string) => {
    switch (purpose) {
      case 'descriptive': return <BookOpen className="w-4 h-4" />;
      case 'analytical': return <Sparkles className="w-4 h-4" />;
      case 'comparative': return <Users className="w-4 h-4" />;
      case 'explanatory': return <Globe className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">AI Caption Generator</h3>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="Caption settings"
            >
              <Settings className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleRegenerate}
              disabled={isGenerating}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${isGenerating ? 'animate-spin' : ''}`} />
              <span className="text-sm font-medium">
                {isGenerating ? 'Generating...' : 'Regenerate'}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-blue-50 px-4 py-4 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Citation Style */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Citation Style
              </label>
              <select
                value={context.citationStyle}
                onChange={(e) => setContext(prev => ({ ...prev, citationStyle: e.target.value as CitationStyle }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {Object.entries(CITATION_STYLES).map(([key, style]) => (
                  <option key={key} value={key}>{style.name}</option>
                ))}
              </select>
            </div>

            {/* Research Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Research Field
              </label>
              <select
                value={context.researchField}
                onChange={(e) => setContext(prev => ({ ...prev, researchField: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {RESEARCH_FIELDS.map((field) => (
                  <option key={field} value={field}>{field}</option>
                ))}
              </select>
            </div>

            {/* Purpose */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Caption Purpose
              </label>
              <select
                value={context.purpose}
                onChange={(e) => setContext(prev => ({ ...prev, purpose: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="descriptive">Descriptive</option>
                <option value="analytical">Analytical</option>
                <option value="comparative">Comparative</option>
                <option value="explanatory">Explanatory</option>
              </select>
            </div>

            {/* Audience */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Audience
              </label>
              <select
                value={context.audience}
                onChange={(e) => setContext(prev => ({ ...prev, audience: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="general">General Public</option>
                <option value="academic">Academic</option>
                <option value="expert">Expert/Specialist</option>
              </select>
            </div>

            {/* User Context */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Context (Optional)
              </label>
              <input
                type="text"
                value={context.userContext}
                onChange={(e) => setContext(prev => ({ ...prev, userContext: e.target.value }))}
                placeholder="e.g., This figure shows the relationship between..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Caption Suggestions */}
      <div className="p-4">
        {captions.length === 0 ? (
          <div className="text-center py-8">
            <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No captions generated yet</p>
            <p className="text-sm text-gray-500 mt-1">Click "Regenerate" to create AI-powered captions</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Selected Caption Editor */}
            {selectedCaption && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">Selected Caption</h4>
                  <div className="flex items-center space-x-2">
                    {!isEditing ? (
                      <button
                        onClick={() => setIsEditing(true)}
                        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded transition-colors"
                        title="Edit caption"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                    ) : (
                      <button
                        onClick={handleSaveEdit}
                        className="p-2 text-green-600 hover:text-green-700 hover:bg-white rounded transition-colors"
                        title="Save changes"
                      >
                        <Save className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleCopy(isEditing ? editingCaption : selectedCaption.text, -1)}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded transition-colors"
                      title="Copy to clipboard"
                    >
                      {copiedIndex === -1 ? <Check className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {isEditing ? (
                  <textarea
                    value={editingCaption}
                    onChange={(e) => setEditingCaption(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    rows={3}
                    placeholder="Edit your caption..."
                  />
                ) : (
                  <p className="text-gray-800 leading-relaxed">{selectedCaption.text}</p>
                )}

                <div className="flex items-center justify-between mt-3 pt-3 border-t border-blue-200">
                  <div className="flex items-center space-x-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      getConfidenceColor(selectedCaption.confidence)
                    }`}>
                      {selectedCaption.confidence}% confidence
                    </span>
                    
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      {getPurposeIcon(selectedCaption.context.purpose)}
                      <span>{selectedCaption.context.purpose}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => onSaveCaption(isEditing ? editingCaption : selectedCaption.text)}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Use This Caption
                  </button>
                </div>
              </div>
            )}

            {/* All Caption Options */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">
                All Suggestions ({captions.length})
              </h4>
              
              <div className="space-y-3">
                {captions.map((caption, index) => (
                  <div
                    key={caption.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedCaption?.id === caption.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      setSelectedCaption(caption);
                      setEditingCaption(caption.text);
                      setIsEditing(false);
                    }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700">
                          Option {index + 1}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          getConfidenceColor(caption.confidence)
                        }`}>
                          {caption.confidence}%
                        </span>
                      </div>
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopy(caption.text, index);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Copy to clipboard"
                      >
                        {copiedIndex === index ? <Check className="w-4 h-4 text-green-600" /> : <Copy className="w-4 h-4" />}
                      </button>
                    </div>

                    <p className="text-gray-800 text-sm leading-relaxed mb-3">
                      {caption.text}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-3">
                        <span>Style: {caption.style}</span>
                        <span>Field: {caption.context.researchField}</span>
                        <div className="flex items-center space-x-1">
                          {getPurposeIcon(caption.context.purpose)}
                          <span>{caption.context.purpose}</span>
                        </div>
                      </div>
                      
                      <span>Audience: {caption.context.audience}</span>
                    </div>

                    {/* Variations */}
                    {caption.variations && caption.variations.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <details className="group">
                          <summary className="flex items-center space-x-2 cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                            <ChevronDown className="w-4 h-4 group-open:hidden" />
                            <ChevronUp className="w-4 h-4 hidden group-open:block" />
                            <span>View {caption.variations.length} variations</span>
                          </summary>
                          
                          <div className="mt-2 space-y-2">
                            {caption.variations.map((variation, vIndex) => (
                              <div key={vIndex} className="pl-6 text-sm text-gray-700 border-l-2 border-gray-200">
                                {variation}
                              </div>
                            ))}
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
