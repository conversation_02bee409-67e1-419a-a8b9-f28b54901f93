/**
 * Beautiful Loading States for Flow Builder
 * Modern loading animations and progress indicators
 */

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Brain,
  GitBranch,
  Sparkles,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Wand2
} from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text,
  className = ""
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="relative">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-500`} />
        <div className="absolute inset-0 rounded-full bg-blue-400 opacity-30 animate-ping"></div>
      </div>
      {text && <span className="text-sm font-medium text-gray-700">{text}</span>}
    </div>
  );
};

interface DiagramGenerationLoadingProps {
  stage: 'analyzing' | 'generating' | 'validating' | 'rendering';
  progress?: number;
  model?: string;
  description?: string;
}

export const DiagramGenerationLoading: React.FC<DiagramGenerationLoadingProps> = ({
  stage,
  progress = 0,
  model,
  description
}) => {
  const stageInfo = {
    analyzing: {
      icon: Brain,
      title: 'Analyzing Requirements',
      subtitle: 'Understanding your diagram needs',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-indigo-50'
    },
    generating: {
      icon: Wand2,
      title: 'Generating Diagram',
      subtitle: 'Creating Mermaid code with AI',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-pink-50'
    },
    validating: {
      icon: CheckCircle,
      title: 'Validating Code',
      subtitle: 'Ensuring diagram syntax is correct',
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-emerald-50'
    },
    rendering: {
      icon: GitBranch,
      title: 'Rendering Preview',
      subtitle: 'Creating visual representation',
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'from-indigo-50 to-blue-50'
    }
  };

  const { icon: Icon, title, subtitle, color, bgColor } = stageInfo[stage];

  return (
    <Card className="overflow-hidden border-0 shadow-lg">
      <CardContent className={`p-6 bg-gradient-to-br ${bgColor} relative`}>
        {/* Background Animation */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12 animate-pulse delay-1000"></div>
        </div>

        <div className="relative space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <div className={`w-16 h-16 bg-gradient-to-br ${color} rounded-2xl flex items-center justify-center shadow-lg`}>
              <Icon className="h-8 w-8 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-gray-900 mb-1">{title}</h3>
              <p className="text-gray-600">{subtitle}</p>
              {model && (
                <Badge variant="secondary" className="mt-2 bg-white/50 text-gray-700">
                  Using {model}
                </Badge>
              )}
            </div>
          </div>

          {/* Progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium text-gray-700">Progress</span>
              <span className="text-gray-600">{Math.round(progress)}%</span>
            </div>
            <div className="relative">
              <Progress value={progress} className="h-3 bg-white/50" />
              <div 
                className={`absolute top-0 left-0 h-3 rounded-full bg-gradient-to-r ${color} transition-all duration-1000 shadow-sm`}
                style={{ width: `${progress}%` }}
              >
                <div className="absolute inset-0 bg-white/20 animate-pulse rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Description */}
          {description && (
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50">
              <div className="flex items-start gap-3">
                <Sparkles className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-800 mb-1">Your Request:</p>
                  <p className="text-sm text-gray-600 leading-relaxed">{description}</p>
                </div>
              </div>
            </div>
          )}

          {/* Status Indicator */}
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>AI is working on your diagram</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>This may take a few moments</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface ErrorStateProps {
  title: string;
  message: string;
  onRetry?: () => void;
  onFixWithAI?: () => void;
  details?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  onRetry,
  onFixWithAI,
  details
}) => {
  return (
    <Card className="border-red-200 bg-gradient-to-br from-red-50 to-orange-50">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <AlertTriangle className="h-6 w-6 text-white" />
          </div>
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="font-semibold text-red-900 text-lg mb-1">{title}</h3>
              <p className="text-red-700">{message}</p>
            </div>

            <div className="flex flex-wrap gap-3">
              {onFixWithAI && (
                <button
                  onClick={onFixWithAI}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-all duration-200 hover:scale-105 shadow-sm"
                >
                  <Wand2 className="h-4 w-4" />
                  Fix with AI
                </button>
              )}
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 hover:scale-105 shadow-sm"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </button>
              )}
            </div>

            {details && (
              <details className="bg-red-100/50 border border-red-200/50 rounded-xl p-4">
                <summary className="text-sm font-medium text-red-800 cursor-pointer hover:text-red-900">
                  View Error Details
                </summary>
                <pre className="text-xs text-red-700 mt-3 whitespace-pre-wrap font-mono bg-red-50 p-3 rounded-lg border border-red-200/50 overflow-x-auto">
                  {details}
                </pre>
              </details>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface SuccessStateProps {
  title: string;
  message: string;
  onContinue?: () => void;
  stats?: {
    label: string;
    value: string;
  }[];
}

export const SuccessState: React.FC<SuccessStateProps> = ({
  title,
  message,
  onContinue,
  stats
}) => {
  return (
    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <CheckCircle className="h-6 w-6 text-white" />
          </div>
          <div className="flex-1 space-y-4">
            <div>
              <h3 className="font-semibold text-green-900 text-lg mb-1">{title}</h3>
              <p className="text-green-700">{message}</p>
            </div>

            {stats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white/60 backdrop-blur-sm rounded-lg p-3 text-center">
                    <div className="text-lg font-bold text-green-800">{stat.value}</div>
                    <div className="text-xs text-green-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            )}

            {onContinue && (
              <button
                onClick={onContinue}
                className="inline-flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-all duration-200 hover:scale-105 shadow-sm"
              >
                <CheckCircle className="h-4 w-4" />
                Continue
              </button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Pulsing dots animation component
interface PulsingDotsProps {
  count?: number;
  color?: 'blue' | 'purple' | 'green' | 'red';
  size?: 'sm' | 'md' | 'lg';
}

export const PulsingDots: React.FC<PulsingDotsProps> = ({
  count = 3,
  color = 'blue',
  size = 'md'
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    purple: 'bg-purple-500',
    green: 'bg-green-500',
    red: 'bg-red-500'
  };

  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  return (
    <div className="flex items-center gap-1">
      {Array.from({ length: count }, (_, i) => (
        <div
          key={i}
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-pulse`}
          style={{ animationDelay: `${i * 0.2}s` }}
        />
      ))}
    </div>
  );
};
