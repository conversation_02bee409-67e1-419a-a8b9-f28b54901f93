/**
 * Agent Orchestrator - Coordinates tool execution for targeted editing
 */

import { nanoid } from 'nanoid';
import { EventEmitter } from 'events';
import {
  AgentTool,
  Tool<PERSON>ontext,
  ToolResult,
  AgentWorkflowState,
  WorkflowStep,
  WorkflowStepResult,
  AgentExecutionResult,
  AgentExecutionOptions,
  AgentProgressUpdate,
  AgentEvent,
  WorkflowConfig,
  EditResult
} from './types';
import { errorHand<PERSON>, AgentError } from './ErrorHandler';

export class AgentOrchestrator extends EventEmitter {
  private tools: Map<string, AgentTool> = new Map();
  private activeWorkflows: Map<string, AgentWorkflowState> = new Map();

  constructor() {
    super();
  }

  /**
   * Register a tool with the orchestrator
   */
  registerTool(tool: AgentTool): void {
    this.tools.set(tool.id, tool);
    console.log(`🔧 Registered tool: ${tool.id} - ${tool.name}`);
  }

  /**
   * Execute a targeted editing request using the agentic workflow
   */
  async executeTargetedEdit(
    userRequest: string,
    documentContent: string,
    options: AgentExecutionOptions = {
      editMode: 'moderate',
      confidenceThreshold: 0.7,
      preserveFormatting: true,
      requirePreview: true,
      enableValidation: true
    }
  ): Promise<AgentExecutionResult> {
    const workflowId = nanoid();
    const startTime = new Date();

    console.log(`🚀 Starting targeted editing workflow:`, {
      workflowId,
      userRequest,
      documentLength: documentContent.length,
      options
    });

    // Create workflow state
    const workflow: AgentWorkflowState = {
      id: workflowId,
      userRequest,
      documentContent,
      currentStep: this.createStep('search', 'Search', 'Finding relevant content sections', 'search-tool'),
      stepHistory: [],
      startTime
    };

    this.activeWorkflows.set(workflowId, workflow);
    this.emitEvent('workflow_started', workflowId, { userRequest, options });

    try {
      // Define the workflow steps
      const workflowConfig = this.createWorkflowConfig(options);
      
      // Execute workflow steps
      const context: ToolContext = {
        documentContent,
        userRequest,
        metadata: { options }
      };

      let previousResults: ToolResult[] = [];

      for (const stepConfig of workflowConfig.steps) {
        const tool = this.tools.get(stepConfig.toolId);
        if (!tool) {
          throw new Error(`Tool not found: ${stepConfig.toolId}`);
        }

        // Check if step should be executed
        if (stepConfig.condition && !stepConfig.condition(context, previousResults)) {
          console.log(`⏭️ Skipping step: ${stepConfig.toolId}`);
          continue;
        }

        // Update workflow state
        const step = this.createStep(
          stepConfig.toolId,
          tool.name,
          tool.description,
          stepConfig.toolId
        );
        
        workflow.currentStep = step;
        this.updateWorkflowProgress(workflowId, step, 0, `Starting ${tool.name}...`);

        // Execute the tool
        step.status = 'running';
        this.updateWorkflowProgress(workflowId, step, 25, `Executing ${tool.name}...`);

        const updatedContext: ToolContext = {
          ...context,
          previousResults
        };

        let result: ToolResult;

        try {
          result = await tool.execute(updatedContext);
        } catch (error: any) {
          // Handle tool execution error with recovery
          console.error(`🚨 Tool execution failed: ${tool.id}`, error);

          const recoveryResult = await errorHandler.handleError(error, {
            toolId: tool.id,
            step,
            context: updatedContext
          });

          if (recoveryResult.success && recoveryResult.fallbackResult) {
            result = recoveryResult.fallbackResult as ToolResult;
            console.log(`🔄 Using fallback result for ${tool.id}`);
          } else {
            // Create error result
            result = {
              toolId: tool.id,
              success: false,
              data: null,
              reasoning: `Tool execution failed: ${error.message}`,
              confidence: 0,
              executionTime: 0,
              error: error.message,
              warnings: recoveryResult.alternativeApproach ? [recoveryResult.alternativeApproach] : undefined
            };
          }
        }

        // Record step result
        const stepResult: WorkflowStepResult = {
          step,
          result,
          timestamp: new Date()
        };

        workflow.stepHistory.push(stepResult);
        previousResults.push(result);

        if (result.success) {
          step.status = 'completed';
          step.progress = 100;
          this.updateWorkflowProgress(
            workflowId,
            step,
            100,
            `${tool.name} completed successfully`,
            result.reasoning
          );
          this.emitEvent('step_completed', workflowId, { step, result });
        } else {
          step.status = 'failed';
          this.emitEvent('step_failed', workflowId, { step, result });

          if (stepConfig.required) {
            // Try graceful degradation before failing completely
            const partialResults = previousResults.filter(r => r.success);
            if (partialResults.length > 0) {
              console.log(`🔄 Attempting graceful degradation with ${partialResults.length} partial results`);
              const degradedResult = errorHandler.createGracefulDegradation(
                workflow.userRequest,
                partialResults,
                [{
                  type: 'tool_failure',
                  message: result.error || 'Tool execution failed',
                  toolId: tool.id,
                  recoverable: true,
                  severity: 'medium'
                } as AgentError]
              );

              workflow.finalResult = degradedResult;
              workflow.endTime = new Date();
              this.emitEvent('workflow_completed', workflowId, degradedResult);
              return degradedResult;
            }

            throw new Error(`Required step failed: ${result.error}`);
          }
        }
      }

      // Compile final result
      const finalResult = this.compileFinalResult(workflow, previousResults, options);
      workflow.finalResult = finalResult;
      workflow.endTime = new Date();

      this.emitEvent('workflow_completed', workflowId, finalResult);
      
      console.log(`✅ Workflow completed:`, {
        workflowId,
        success: finalResult.success,
        changesCount: finalResult.changes.length,
        executionTime: finalResult.executionTime
      });

      return finalResult;

    } catch (error: any) {
      console.error(`❌ Workflow failed:`, { workflowId, error: error.message });
      
      const errorResult: AgentExecutionResult = {
        success: false,
        changes: [],
        summary: 'Workflow execution failed',
        reasoning: error.message,
        confidence: 0,
        executionTime: Date.now() - startTime.getTime(),
        toolsUsed: workflow.stepHistory.map(s => s.step.toolId),
        error: error.message
      };

      workflow.finalResult = errorResult;
      workflow.error = error.message;
      workflow.endTime = new Date();

      this.emitEvent('workflow_failed', workflowId, errorResult);
      return errorResult;
    } finally {
      // Cleanup
      setTimeout(() => {
        this.activeWorkflows.delete(workflowId);
      }, 60000); // Keep for 1 minute for debugging
    }
  }

  /**
   * Get the current state of a workflow
   */
  getWorkflowState(workflowId: string): AgentWorkflowState | null {
    return this.activeWorkflows.get(workflowId) || null;
  }

  /**
   * Create workflow configuration based on options
   */
  private createWorkflowConfig(options: AgentExecutionOptions): WorkflowConfig {
    return {
      steps: [
        {
          toolId: 'search-tool',
          required: true,
          timeout: 10000
        },
        {
          toolId: 'analysis-tool',
          required: true,
          timeout: 15000
        },
        {
          toolId: 'edit-tool',
          required: true,
          condition: (context, results) => {
            const analysisResult = results.find(r => r.toolId === 'analysis-tool');
            return analysisResult?.success && analysisResult.data?.targetSections?.length > 0;
          },
          timeout: 20000
        },
        {
          toolId: 'review-tool',
          required: false,
          condition: () => options.requirePreview !== false,
          timeout: 10000
        },
        {
          toolId: 'validation-tool',
          required: false,
          condition: () => options.enableValidation !== false,
          timeout: 10000
        }
      ],
      options
    };
  }

  /**
   * Create a workflow step
   */
  private createStep(id: string, name: string, description: string, toolId: string): WorkflowStep {
    return {
      id,
      name,
      description,
      toolId,
      status: 'pending',
      progress: 0
    };
  }

  /**
   * Update workflow progress and emit events
   */
  private updateWorkflowProgress(
    workflowId: string,
    step: WorkflowStep,
    progress: number,
    message: string,
    reasoning?: string
  ): void {
    step.progress = progress;
    
    const update: AgentProgressUpdate = {
      workflowId,
      currentStep: step,
      progress,
      message,
      reasoning
    };

    this.emitEvent('step_progress', workflowId, update);
  }

  /**
   * Emit an agent event
   */
  private emitEvent(type: string, workflowId: string, data: any): void {
    const event: AgentEvent = {
      type: type as any,
      workflowId,
      timestamp: new Date(),
      data
    };

    this.emit('agent_event', event);
    this.emit(type, event);
  }

  /**
   * Compile the final result from all step results
   */
  private compileFinalResult(
    workflow: AgentWorkflowState,
    results: ToolResult[],
    options: AgentExecutionOptions
  ): AgentExecutionResult {
    const editResults = results.filter(r => r.toolId === 'edit-tool' && r.success);
    const reviewResults = results.filter(r => r.toolId === 'review-tool' && r.success);
    const validationResults = results.filter(r => r.toolId === 'validation-tool' && r.success);

    let changes: EditResult[] = [];
    let warnings: string[] = [];

    // Extract changes from edit results
    editResults.forEach(result => {
      if (result.data?.edits) {
        changes.push(...result.data.edits);
      }
    });

    // Apply review filtering if available
    if (reviewResults.length > 0) {
      const reviewResult = reviewResults[0];
      if (reviewResult.data?.approvedChanges) {
        changes = reviewResult.data.approvedChanges;
      }
      if (reviewResult.data?.issues) {
        warnings.push(...reviewResult.data.issues);
      }
    }

    // Apply validation filtering if available
    if (validationResults.length > 0) {
      const validationResult = validationResults[0];
      if (validationResult.data?.issues) {
        warnings.push(...validationResult.data.issues);
      }
    }

    const executionTime = workflow.endTime 
      ? workflow.endTime.getTime() - workflow.startTime.getTime()
      : Date.now() - workflow.startTime.getTime();

    const avgConfidence = changes.length > 0
      ? changes.reduce((sum, change) => sum + change.confidence, 0) / changes.length
      : 0;

    return {
      success: changes.length > 0,
      changes,
      summary: `Applied ${changes.length} targeted edit(s) based on: "${workflow.userRequest}"`,
      reasoning: this.generateReasoningSummary(results),
      confidence: avgConfidence,
      executionTime,
      toolsUsed: workflow.stepHistory.map(s => s.step.toolId),
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Generate a reasoning summary from all tool results
   */
  private generateReasoningSummary(results: ToolResult[]): string {
    const reasonings = results
      .filter(r => r.success && r.reasoning)
      .map(r => `${r.toolId}: ${r.reasoning}`);

    return reasonings.join(' → ');
  }
}
