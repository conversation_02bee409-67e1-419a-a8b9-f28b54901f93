/**
 * Types and interfaces for the Agentic Targeted Editing System
 */

export interface AgentTool {
  id: string;
  name: string;
  description: string;
  execute: (context: ToolContext) => Promise<ToolResult>;
}

export interface ToolContext {
  documentContent: string;
  userRequest: string;
  targetText?: string;
  targetPosition?: { start: number; end: number };
  previousResults?: ToolResult[];
  metadata?: Record<string, any>;
}

export interface ToolResult {
  toolId: string;
  success: boolean;
  data: any;
  reasoning: string;
  confidence: number;
  executionTime: number;
  error?: string;
  warnings?: string[];
}

export interface SearchResult {
  text: string;
  startPosition: number;
  endPosition: number;
  relevanceScore: number;
  context: string;
  sectionType?: string;
}

export interface AnalysisResult {
  intent: string;
  targetSections: SearchResult[];
  editType: 'replace' | 'insert' | 'enhance' | 'rewrite';
  scope: 'word' | 'sentence' | 'paragraph' | 'section' | 'document';
  complexity: 'simple' | 'moderate' | 'complex';
  requirements: string[];
}

export interface EditResult {
  originalText: string;
  newText: string;
  startPosition: number;
  endPosition: number;
  editType: 'replace' | 'insert' | 'enhance' | 'rewrite';
  reasoning: string;
  confidence: number;
}

export interface ReviewResult {
  isValid: boolean;
  meetsRequirements: boolean;
  qualityScore: number;
  issues: string[];
  suggestions: string[];
  approvedChanges: EditResult[];
}

export interface ValidationResult {
  hasUnintendedChanges: boolean;
  preservesStructure: boolean;
  maintainsStyle: boolean;
  issues: string[];
  score: number;
}

export interface AgentWorkflowState {
  id: string;
  userRequest: string;
  documentContent: string;
  currentStep: WorkflowStep;
  stepHistory: WorkflowStepResult[];
  finalResult?: AgentExecutionResult;
  error?: string;
  startTime: Date;
  endTime?: Date;
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  toolId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress: number;
}

export interface WorkflowStepResult {
  step: WorkflowStep;
  result: ToolResult;
  timestamp: Date;
}

export interface AgentExecutionResult {
  success: boolean;
  changes: EditResult[];
  summary: string;
  reasoning: string;
  confidence: number;
  executionTime: number;
  toolsUsed: string[];
  warnings?: string[];
  error?: string;
}

export interface AgentExecutionOptions {
  editMode: 'conservative' | 'moderate' | 'aggressive';
  maxChanges?: number;
  confidenceThreshold?: number;
  preserveFormatting?: boolean;
  requirePreview?: boolean;
  enableValidation?: boolean;
}

export interface AgentProgressUpdate {
  workflowId: string;
  currentStep: WorkflowStep;
  progress: number;
  message: string;
  reasoning?: string;
  intermediateResult?: any;
}

// Tool-specific result types
export interface SearchToolResult extends ToolResult {
  data: {
    matches: SearchResult[];
    totalMatches: number;
    searchStrategy: string;
  };
}

export interface AnalysisToolResult extends ToolResult {
  data: AnalysisResult;
}

export interface EditToolResult extends ToolResult {
  data: {
    edits: EditResult[];
    strategy: string;
  };
}

export interface ReviewToolResult extends ToolResult {
  data: ReviewResult;
}

export interface WriteToolResult extends ToolResult {
  data: {
    newContent: string;
    sections: Array<{
      title: string;
      content: string;
      startPosition: number;
      endPosition: number;
    }>;
  };
}

export interface ValidationToolResult extends ToolResult {
  data: ValidationResult;
}

// Workflow configuration
export interface WorkflowConfig {
  steps: WorkflowStepConfig[];
  options: AgentExecutionOptions;
}

export interface WorkflowStepConfig {
  toolId: string;
  required: boolean;
  condition?: (context: ToolContext, previousResults: ToolResult[]) => boolean;
  retryCount?: number;
  timeout?: number;
}

// Event types for real-time updates
export type AgentEventType = 
  | 'workflow_started'
  | 'step_started'
  | 'step_progress'
  | 'step_completed'
  | 'step_failed'
  | 'workflow_completed'
  | 'workflow_failed';

export interface AgentEvent {
  type: AgentEventType;
  workflowId: string;
  timestamp: Date;
  data: any;
}

// Integration with existing change tracking
export interface ChangeTrackingIntegration {
  recordChanges: (edits: EditResult[], prompt: string) => string[];
  previewChanges: (edits: EditResult[]) => void;
  applyChanges: (changeIds: string[]) => void;
  rejectChanges: (changeIds: string[]) => void;
}
