/**
 * AgentModePanel - UI component for Agent Mode functionality
 * Provides interface for contextual document editing through natural language prompts
 */

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Wand2, 
  Brain, 
  Target, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Lightbulb,
  FileText,
  Edit3
} from 'lucide-react';
import { 
  AgentModePanelProps, 
  AgentModeRequest, 
  DocumentAnalysisProgress,
  DEFAULT_AGENT_MODE_OPTIONS 
} from './types';

export function AgentModePanel({
  documentContent,
  isVisible,
  onAgentModeRequest,
  onClose,
  isLoading,
  lastResult,
  availableMode<PERSON>,
  selectedModel,
  onModelChange
}: AgentModePanelProps) {
  const [prompt, setPrompt] = useState('');
  const [editMode, setEditMode] = useState<'conservative' | 'moderate' | 'aggressive'>('moderate');
  const [progress, setProgress] = useState<DocumentAnalysisProgress | null>(null);
  const [showExamples, setShowExamples] = useState(false);

  // Example prompts for user guidance
  const examplePrompts = [
    "Improve the introduction for better clarity and engagement",
    "Rewrite the methodology section to be more detailed and precise",
    "Make the conclusion more compelling and impactful",
    "Enhance the abstract to better highlight key findings",
    "Revise the discussion section for improved flow and analysis",
    "Expand the results section with more detailed explanations"
  ];

  const editModeDescriptions = {
    conservative: "Minimal changes, preserve original style",
    moderate: "Balanced improvements with structural preservation", 
    aggressive: "Substantial improvements, restructure as needed"
  };

  const handleSubmit = async () => {
    if (!prompt.trim() || !documentContent.trim()) return;

    const request: AgentModeRequest = {
      prompt: prompt.trim(),
      documentContent,
      model: selectedModel,
      options: {
        ...DEFAULT_AGENT_MODE_OPTIONS,
        editMode,
        maxSections: 3,
        confidenceThreshold: 0.6
      }
    };

    try {
      await onAgentModeRequest(request);
    } catch (error) {
      console.error('Agent mode request failed:', error);
    }
  };

  const handleExampleClick = (example: string) => {
    setPrompt(example);
    setShowExamples(false);
  };

  const getProgressIcon = () => {
    if (!progress) return <Brain className="h-4 w-4" />;
    
    switch (progress.stage) {
      case 'parsing-structure':
        return <FileText className="h-4 w-4" />;
      case 'identifying-sections':
      case 'matching-prompt':
        return <Target className="h-4 w-4" />;
      case 'generating-edits':
        return <Edit3 className="h-4 w-4" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5 text-purple-600" />
            Agent Mode
            <Badge variant="secondary" className="ml-auto">Beta</Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Use natural language to edit specific sections of your document
          </p>
        </CardHeader>
      </Card>

      {/* Prompt Input */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                What would you like me to do?
              </label>
              <Textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="e.g., Improve the introduction for better clarity and engagement"
                className="min-h-[100px] resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Example Prompts */}
            <div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowExamples(!showExamples)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                {showExamples ? 'Hide' : 'Show'} example prompts
              </Button>
              
              {showExamples && (
                <div className="mt-2 space-y-1">
                  {examplePrompts.map((example, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      className="h-auto p-2 text-xs text-left justify-start w-full"
                      onClick={() => handleExampleClick(example)}
                    >
                      "{example}"
                    </Button>
                  ))}
                </div>
              )}
            </div>

            {/* Edit Mode Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">
                Edit Mode
              </label>
              <div className="grid grid-cols-3 gap-2">
                {Object.entries(editModeDescriptions).map(([mode, description]) => (
                  <Button
                    key={mode}
                    variant={editMode === mode ? "default" : "outline"}
                    size="sm"
                    className="h-auto p-2 text-xs"
                    onClick={() => setEditMode(mode as any)}
                    disabled={isLoading}
                  >
                    <div className="text-center">
                      <div className="font-medium capitalize">{mode}</div>
                      <div className="text-xs opacity-70 mt-1">{description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={!prompt.trim() || !documentContent.trim() || isLoading}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Analyze & Edit
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Indicator */}
      {isLoading && progress && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {getProgressIcon()}
                <span className="text-sm font-medium">
                  {progress.message}
                </span>
              </div>
              <Progress value={progress.progress} className="w-full" />
              <p className="text-xs text-gray-500">
                Stage: {progress.stage.replace('-', ' ')}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {lastResult && !isLoading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-base">
              {lastResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm">{lastResult.summary}</p>
              
              {lastResult.success && lastResult.targetSections.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Sections Modified:</h4>
                  <div className="space-y-1">
                    {lastResult.targetSections.map((target, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span>{target.section.title}</span>
                        <Badge variant="secondary" className="text-xs">
                          {Math.round(target.confidence * 100)}% match
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {lastResult.edits.length > 0 && (
                <div className="text-xs text-gray-500">
                  Generated {lastResult.edits.length} edit(s) in {lastResult.processingTime}ms
                  {lastResult.tokensUsed && ` • ${lastResult.tokensUsed} tokens used`}
                </div>
              )}

              {lastResult.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {lastResult.error}
                </div>
              )}

              {lastResult.warnings && lastResult.warnings.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-yellow-700">Suggestions:</h4>
                  <div className="space-y-1">
                    {lastResult.warnings.map((warning, index) => (
                      <div key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded border-l-2 border-yellow-300">
                        {warning}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-4">
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-2">💡 Tips for better results:</h4>
            <ul className="space-y-1 text-xs">
              <li>• Be specific about which section you want to edit</li>
              <li>• Describe the type of improvement you want</li>
              <li>• Use keywords like "introduction", "methodology", "conclusion"</li>
              <li>• Try different edit modes for varying levels of change</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
