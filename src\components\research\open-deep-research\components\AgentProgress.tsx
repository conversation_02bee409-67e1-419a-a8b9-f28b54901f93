/**
 * Agent Progress Component
 * Displays progress and insights during agent mode research
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  ChevronDown, 
  Brain, 
  Search, 
  BarChart3, 
  FileText,
  CheckCircle,
  Clock
} from 'lucide-react';

import { AgentProgressProps } from '../types';
import { AGENT_STEP_LABELS } from '../constants';

export const AgentProgress: React.FC<AgentProgressProps> = ({ status }) => {
  const getStepIcon = (step: string) => {
    switch (step) {
      case 'processing':
        return <Brain className="h-5 w-5 text-blue-600" />;
      case 'searching':
        return <Search className="h-5 w-5 text-green-600" />;
      case 'analyzing':
        return <BarChart3 className="h-5 w-5 text-orange-600" />;
      case 'generating':
        return <FileText className="h-5 w-5 text-purple-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStepColor = (step: string) => {
    switch (step) {
      case 'processing':
        return 'bg-blue-50 border-blue-200';
      case 'searching':
        return 'bg-green-50 border-green-200';
      case 'analyzing':
        return 'bg-orange-50 border-orange-200';
      case 'generating':
        return 'bg-purple-50 border-purple-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const steps = [
    { key: 'processing', label: 'Planning Research', description: 'Analyzing topic and optimizing search strategy' },
    { key: 'searching', label: 'Searching Web', description: 'Finding relevant sources across the web' },
    { key: 'analyzing', label: 'Analyzing Results', description: 'Ranking and selecting best sources' },
    { key: 'generating', label: 'Writing Report', description: 'Generating comprehensive analysis' },
  ];

  const currentStepIndex = steps.findIndex(step => step.key === status.agentStep);

  return (
    <Card className={`${getStepColor(status.agentStep)} transition-colors duration-300`}>
      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
          <h3 className="text-lg font-semibold text-gray-800">
            AI Agent Research in Progress
          </h3>
        </div>

        {/* Current Step Display */}
        <div className="flex items-center gap-3 mb-4">
          {getStepIcon(status.agentStep)}
          <div>
            <div className="font-medium text-gray-800">
              {AGENT_STEP_LABELS[status.agentStep]}
            </div>
            <div className="text-sm text-gray-600">
              {steps.find(step => step.key === status.agentStep)?.description}
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-3 mb-4">
          {steps.map((step, index) => {
            const isCompleted = index < currentStepIndex;
            const isCurrent = step.key === status.agentStep;
            const isPending = index > currentStepIndex;

            return (
              <div
                key={step.key}
                className={`flex items-center gap-2 p-2 rounded-lg transition-colors ${
                  isCompleted
                    ? 'bg-green-100 text-green-800'
                    : isCurrent
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-500'
                }`}
              >
                {isCompleted ? (
                  <CheckCircle className="h-4 w-4" />
                ) : isCurrent ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Clock className="h-4 w-4" />
                )}
                <span className="text-sm font-medium">{step.label}</span>
              </div>
            );
          })}
        </div>

        {/* Search Queries */}
        {status.searchQueries.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Search Queries:</h4>
            <div className="flex flex-wrap gap-2">
              {status.searchQueries.map((query, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {query}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Agent Insights */}
        {status.agentInsights.length > 0 && (
          <Collapsible>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 transition-colors">
              <span>View Research Details ({status.agentInsights.length})</span>
              <ChevronDown className="h-4 w-4" />
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3">
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {status.agentInsights.map((insight, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-2 text-sm text-gray-600 p-2 bg-white rounded border"
                  >
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                    <span>{insight}</span>
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Fetch Status (during report generation) */}
        {status.agentStep === 'generating' && status.fetchStatus.total > 0 && (
          <div className="mt-4 p-3 bg-white rounded border">
            <div className="text-sm font-medium text-gray-700 mb-2">
              Content Extraction Progress:
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                {status.fetchStatus.successful} fetched
              </span>
              <span className="flex items-center gap-1">
                <Clock className="h-4 w-4 text-orange-600" />
                {status.fetchStatus.fallback} fallback
              </span>
              <span className="text-gray-500">
                of {status.fetchStatus.total} total
              </span>
            </div>
            
            {/* Progress bar */}
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${((status.fetchStatus.successful + status.fetchStatus.fallback) / status.fetchStatus.total) * 100}%`
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
