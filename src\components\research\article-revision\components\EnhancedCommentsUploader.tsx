/**
 * Enhanced Comments Uploader Component
 * Supports both file upload and text input for reviewer comments
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Upload, 
  Users, 
  X, 
  File,
  Plus,
  Type,
  FileText,
  AlertCircle,
  Check
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';

import { ParsedReviewerComments } from '../types';
import { FILE_CONSTRAINTS, ERROR_MESSAGES } from '../constants';
import { DocumentParserService } from '../services/document-parser.service';
import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';

interface EnhancedCommentsUploaderProps {
  isProcessing: boolean;
}

export function EnhancedCommentsUploader({ isProcessing }: EnhancedCommentsUploaderProps) {
  const [activeTab, setActiveTab] = useState<'files' | 'text'>('files');
  const [textInputs, setTextInputs] = useState<Record<number, string>>({});
  const [processingText, setProcessingText] = useState<number | null>(null);
  const [errors, setErrors] = useState<string[]>([]);

  const {
    reviewerComments,
    addReviewerComments,
    removeReviewerComments,
    addError
  } = useRevisionWorkflowStore();

  // File upload handler
  const onDropComments = async (acceptedFiles: File[], rejectedFiles: any[]) => {
    setErrors([]);
    
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        const errorMsg = error.code === 'file-too-large' 
          ? ERROR_MESSAGES.FILE_TOO_LARGE
          : error.code === 'file-invalid-type'
          ? ERROR_MESSAGES.UNSUPPORTED_FORMAT
          : error.message;
        setErrors(prev => [...prev, `${file.name}: ${errorMsg}`]);
      });
    });

    // Process accepted files
    for (const file of acceptedFiles) {
      const reviewerNumber = reviewerComments.length + 1;
      if (reviewerNumber <= FILE_CONSTRAINTS.MAX_REVIEWER_FILES) {
        await processCommentsFile(file, reviewerNumber);
      } else {
        setErrors(prev => [...prev, `Maximum ${FILE_CONSTRAINTS.MAX_REVIEWER_FILES} reviewer files allowed`]);
      }
    }
  };

  const processCommentsFile = async (file: File, reviewerNumber: number) => {
    try {
      const parsedComments = await DocumentParserService.parseReviewerComments(file, reviewerNumber);
      
      if (parsedComments.comments.length === 0) {
        throw new Error(`No comments found in ${file.name}. Please check the file content or use the text input option.`);
      }

      addReviewerComments(parsedComments);
      toast.success(`Reviewer ${reviewerNumber} comments processed (${parsedComments.comments.length} comments found)`);
      
    } catch (error) {
      console.error('Error processing comments:', error);
      const errorMsg = error instanceof Error ? error.message : ERROR_MESSAGES.PARSING_FAILED;
      setErrors(prev => [...prev, errorMsg]);
      addError(errorMsg);
      toast.error(errorMsg);
    }
  };

  // Text input handler
  const handleTextInput = (reviewerNumber: number, text: string) => {
    setTextInputs(prev => ({
      ...prev,
      [reviewerNumber]: text
    }));
  };

  const processTextComments = async (reviewerNumber: number) => {
    const text = textInputs[reviewerNumber];
    if (!text || text.trim().length < 10) {
      toast.error('Please enter at least 10 characters of comments');
      return;
    }

    setProcessingText(reviewerNumber);
    
    try {
      const parsedComments = DocumentParserService.parseReviewerCommentsFromText(
        text, 
        reviewerNumber,
        `Reviewer ${reviewerNumber} Comments`
      );
      
      addReviewerComments(parsedComments);
      toast.success(`Reviewer ${reviewerNumber} comments added (${parsedComments.comments.length} comments found)`);
      
      // Clear the text input
      setTextInputs(prev => ({
        ...prev,
        [reviewerNumber]: ''
      }));
      
    } catch (error) {
      console.error('Error processing text comments:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to process comments';
      toast.error(errorMsg);
    } finally {
      setProcessingText(null);
    }
  };

  const getNextReviewerNumber = () => {
    const existingNumbers = reviewerComments.map(rc => rc.reviewerNumber);
    for (let i = 1; i <= FILE_CONSTRAINTS.MAX_REVIEWER_FILES; i++) {
      if (!existingNumbers.includes(i)) {
        return i;
      }
    }
    return null;
  };

  const addTextInputField = () => {
    const nextNumber = getNextReviewerNumber();
    if (nextNumber) {
      setTextInputs(prev => ({
        ...prev,
        [nextNumber]: ''
      }));
    }
  };

  const removeTextInputField = (reviewerNumber: number) => {
    setTextInputs(prev => {
      const newInputs = { ...prev };
      delete newInputs[reviewerNumber];
      return newInputs;
    });
  };

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onDropComments,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
      'text/plain': ['.txt']
    },
    maxSize: FILE_CONSTRAINTS.MAX_FILE_SIZE,
    multiple: true,
    disabled: isProcessing || reviewerComments.length >= FILE_CONSTRAINTS.MAX_REVIEWER_FILES
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Reviewer Comments
          <Badge variant="secondary">{reviewerComments.length}/{FILE_CONSTRAINTS.MAX_REVIEWER_FILES}</Badge>
        </CardTitle>
        <CardDescription>
          Upload reviewer comment files or paste comments directly. Supports up to {FILE_CONSTRAINTS.MAX_REVIEWER_FILES} reviewers.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Error Display */}
        {errors.length > 0 && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Uploaded Comments Display */}
        {reviewerComments.length > 0 && (
          <div className="space-y-3 mb-4">
            {reviewerComments.map((rc) => (
              <div key={rc.reviewerNumber} className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <File className="h-6 w-6 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-800">
                      Reviewer {rc.reviewerNumber}: {rc.fileName}
                    </p>
                    <p className="text-sm text-blue-600">
                      {rc.comments.length} comments found
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeReviewerComments(rc.reviewerNumber)}
                  disabled={isProcessing}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Upload Options */}
        {reviewerComments.length < FILE_CONSTRAINTS.MAX_REVIEWER_FILES && (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'files' | 'text')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="files" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Upload Files
              </TabsTrigger>
              <TabsTrigger value="text" className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                Paste Text
              </TabsTrigger>
            </TabsList>

            {/* File Upload Tab */}
            <TabsContent value="files" className="space-y-4">
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-blue-400 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <input {...getInputProps()} />
                <Upload className="h-10 w-10 mx-auto mb-3 text-gray-400" />
                <p className="font-medium mb-2">
                  {isDragActive ? 'Drop reviewer files here' : 'Upload reviewer comment files'}
                </p>
                <p className="text-sm text-gray-500 mb-3">
                  You can upload multiple files at once (one per reviewer)
                </p>
                <p className="text-xs text-gray-400">
                  Supported formats: PDF, DOC, DOCX, TXT (max {formatFileSize(FILE_CONSTRAINTS.MAX_FILE_SIZE)} each)
                </p>
              </div>
            </TabsContent>

            {/* Text Input Tab */}
            <TabsContent value="text" className="space-y-4">
              {/* Existing text inputs */}
              {Object.entries(textInputs).map(([reviewerNumber, text]) => (
                <div key={reviewerNumber} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      Reviewer {reviewerNumber} Comments
                    </label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTextInputField(parseInt(reviewerNumber))}
                      disabled={isProcessing}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <Textarea
                    placeholder={`Paste reviewer ${reviewerNumber} comments here...

Examples:
1. The introduction needs more background information.
2. The methodology section lacks detail about data collection.
3. Figure 1 is unclear and should be improved.

Or paste the complete review text and we'll extract individual comments automatically.`}
                    value={text}
                    onChange={(e) => handleTextInput(parseInt(reviewerNumber), e.target.value)}
                    className="min-h-[120px]"
                    disabled={isProcessing}
                  />
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {text.length} characters
                    </span>
                    <Button
                      onClick={() => processTextComments(parseInt(reviewerNumber))}
                      disabled={isProcessing || text.trim().length < 10 || processingText === parseInt(reviewerNumber)}
                      size="sm"
                    >
                      {processingText === parseInt(reviewerNumber) ? (
                        <>Processing...</>
                      ) : (
                        <>
                          <Check className="h-4 w-4 mr-2" />
                          Add Comments
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ))}

              {/* Add new text input button */}
              {Object.keys(textInputs).length < FILE_CONSTRAINTS.MAX_REVIEWER_FILES - reviewerComments.length && (
                <Button
                  variant="outline"
                  onClick={addTextInputField}
                  disabled={isProcessing}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Reviewer
                </Button>
              )}

              {Object.keys(textInputs).length === 0 && (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500 mb-4">
                    No text inputs added yet. Click the button below to start adding reviewer comments.
                  </p>
                  <Button onClick={addTextInputField} disabled={isProcessing}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Reviewer
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}

        {/* Max reviewers reached message */}
        {reviewerComments.length >= FILE_CONSTRAINTS.MAX_REVIEWER_FILES && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Maximum number of reviewers ({FILE_CONSTRAINTS.MAX_REVIEWER_FILES}) reached. Remove a reviewer to add more comments.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
