# Library Compatibility Fix - Plotly Issue Resolution

## 🚨 **Problem Identified**

The Gemini code execution environment doesn't have `plotly` installed, causing errors like:
```
ModuleNotFoundError: No module named 'plotly'
```

This is a common issue with AI code execution environments that have limited package availability.

## ✅ **Solutions Implemented**

### 1. **Updated AI Prompts to Use Standard Libraries**

**Before:**
```typescript
"Generate Python code with pandas/plotly."
"Use pandas for data manipulation and plotly for visualizations."
```

**After:**
```typescript
"IMPORTANT: Use ONLY standard libraries available in Python execution environment:
- pandas (for data analysis)
- matplotlib (for visualizations) 
- numpy (for calculations)
- scipy (for statistics)

DO NOT use plotly, seaborn, or other external libraries that may not be installed."
```

### 2. **Provided Matplotlib Code Templates**

**Enhanced Query Prompt:**
```python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

try:
    # Your analysis code here
    # Use matplotlib for plots: plt.figure(), plt.plot(), plt.show()
    print("Analysis completed successfully")
except Exception as e:
    print(f"Error: {e}")
```

**Regular Query Prompt:**
```python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

try:
    # Analysis code
    plt.figure(figsize=(10, 6))
    plt.plot(x_data, y_data)  # or plt.bar(), plt.hist(), etc.
    plt.title("Chart Title")
    plt.xlabel("X Label")
    plt.ylabel("Y Label")
    plt.show()
    print("Analysis completed")
except Exception as e:
    print(f"Error: {e}")
```

### 3. **Enhanced Error Detection and Handling**

**Code Execution Result Processing:**
```typescript
if (part.codeExecutionResult) {
  const output = part.codeExecutionResult.output || '';
  result += output;
  
  // Check for common library import errors
  if (output.includes('No module named') || output.includes('ModuleNotFoundError')) {
    console.warn('Library import error detected:', output);
  }
}
```

**User-Friendly Error Messages:**
```typescript
// Check for library import errors in the result
let processedText = fullText;
if (result && (result.includes('No module named') || result.includes('ModuleNotFoundError'))) {
  processedText += '\n\n⚠️ **Note**: Some visualization libraries are not available in the execution environment. The analysis has been completed with available libraries (pandas, matplotlib, numpy, scipy).';
}
```

### 4. **Improved User Interface Feedback**

**Welcome Message Update:**
```typescript
🔍 **Analyze your data** with advanced statistical methods
📊 **Create visualizations** using Python (matplotlib, pandas, numpy)
🧮 **Execute code** in real-time to answer your questions
💡 **Provide insights** with evidence-based analysis

**Available Libraries:** pandas, matplotlib, numpy, scipy (standard Python libraries)
**Note:** Some external libraries (plotly, seaborn) may not be available in the execution environment.
```

**Toast Notifications:**
```typescript
// Check for library import errors
const hasLibraryError = response.result && 
  (response.result.includes('No module named') || response.result.includes('ModuleNotFoundError'));

if (hasLibraryError) {
  toast.success('Query processed with standard libraries (some visualization libraries unavailable)');
} else {
  toast.success('Query processed successfully with code execution!');
}
```

### 5. **Fallback Analysis Method**

Created a fallback analysis function that provides meaningful insights even when visualization libraries fail:

```typescript
private static createFallbackAnalysis(file: UploadedFile, question: string): string {
  // Analyzes data structure and provides insights without requiring external libraries
  return `
## Data Analysis Summary
**Dataset**: ${file.name}
- **Rows**: ${file.data.length}
- **Columns**: ${file.headers.length}

**Column Types**:
- **Numeric columns**: ${numericColumns.join(', ')}
- **Categorical columns**: ${categoricalColumns.join(', ')}

**Recommended Analysis Steps**:
1. **Descriptive Statistics**: Calculate mean, median, standard deviation
2. **Distribution Analysis**: Examine variable distributions
3. **Correlation Analysis**: Identify relationships between variables
4. **Categorical Analysis**: Analyze frequency distributions
  `;
}
```

## 📊 **Library Compatibility Matrix**

| Library | Availability | Usage | Alternative |
|---------|-------------|--------|-------------|
| ✅ pandas | Available | Data manipulation | - |
| ✅ matplotlib | Available | Visualizations | - |
| ✅ numpy | Available | Numerical computing | - |
| ✅ scipy | Available | Statistical analysis | - |
| ❌ plotly | Not available | Interactive plots | matplotlib |
| ❌ seaborn | Not available | Statistical plots | matplotlib |
| ❌ bokeh | Not available | Interactive plots | matplotlib |

## 🔧 **Matplotlib vs Plotly Conversion**

### Histogram
**Plotly:**
```python
import plotly.express as px
fig = px.histogram(df, x='column')
fig.show()
```

**Matplotlib:**
```python
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 6))
plt.hist(df['column'], bins=30)
plt.title('Distribution of Column')
plt.xlabel('Column')
plt.ylabel('Frequency')
plt.show()
```

### Scatter Plot
**Plotly:**
```python
fig = px.scatter(df, x='col1', y='col2')
fig.show()
```

**Matplotlib:**
```python
plt.figure(figsize=(10, 6))
plt.scatter(df['col1'], df['col2'])
plt.title('Col1 vs Col2')
plt.xlabel('Col1')
plt.ylabel('Col2')
plt.show()
```

### Bar Chart
**Plotly:**
```python
fig = px.bar(df, x='category', y='value')
fig.show()
```

**Matplotlib:**
```python
plt.figure(figsize=(10, 6))
plt.bar(df['category'], df['value'])
plt.title('Category vs Value')
plt.xlabel('Category')
plt.ylabel('Value')
plt.xticks(rotation=45)
plt.show()
```

## 🎯 **Expected Results**

### Before Fix:
- ❌ `ModuleNotFoundError: No module named 'plotly'`
- ❌ Code execution failures
- ❌ No visualizations generated
- ❌ Poor user experience

### After Fix:
- ✅ Code executes successfully with matplotlib
- ✅ Visualizations generated using standard libraries
- ✅ Clear user feedback about library limitations
- ✅ Graceful fallback when libraries are missing
- ✅ Better error handling and user guidance

## 🚀 **Usage Guidelines**

### For Users:
1. **Expect matplotlib visualizations** instead of plotly interactive charts
2. **Standard libraries available**: pandas, matplotlib, numpy, scipy
3. **Clear error messages** when libraries are unavailable
4. **Fallback analysis** provided when visualization fails

### For Developers:
1. **Always specify available libraries** in AI prompts
2. **Include error handling** in generated code
3. **Provide fallback options** for missing libraries
4. **Test with limited library environments**

This fix ensures the data visualization platform works reliably regardless of the AI execution environment's library limitations, providing a consistent user experience with appropriate fallbacks and clear communication about any limitations.
