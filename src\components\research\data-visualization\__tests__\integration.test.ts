/**
 * Integration Tests for Data Visualization Platform
 * 
 * These tests verify the core functionality of the data visualization
 * and analysis platform, including file upload, analysis pipeline,
 * and visualization generation.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FileProcessingService } from '../services/file-processing.service';
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';

// Mock the Google Gemini API
vi.mock('@google/genai', () => ({
  GoogleGenAI: vi.fn().mockImplementation(() => ({
    models: {
      generateContentStream: vi.fn().mockResolvedValue({
        [Symbol.asyncIterator]: async function* () {
          yield {
            candidates: [{
              content: {
                parts: [{
                  text: 'Mock analysis result',
                  executableCode: { code: 'print("Hello World")' },
                  codeExecutionResult: { output: 'Hello World' }
                }]
              }
            }]
          };
        }
      })
    }
  }))
}));

describe('Data Visualization Platform Integration', () => {
  beforeEach(() => {
    // Reset store state before each test
    useDataVisualizationStore.getState().reset();
  });

  describe('File Processing Service', () => {
    it('should validate file types correctly', () => {
      const csvFile = new File(['name,age\nJohn,25'], 'test.csv', { type: 'text/csv' });
      const invalidFile = new File(['content'], 'test.txt', { type: 'text/plain' });

      const csvValidation = FileProcessingService.validateFile(csvFile);
      const invalidValidation = FileProcessingService.validateFile(invalidFile);

      expect(csvValidation.isValid).toBe(true);
      expect(csvValidation.errors).toHaveLength(0);

      expect(invalidValidation.isValid).toBe(false);
      expect(invalidValidation.errors).toContain('Unsupported file format. Please upload CSV or Excel files.');
    });

    it('should detect data types correctly', () => {
      const data = [
        ['John', 25, 'true', '2023-01-01'],
        ['Jane', 30, 'false', '2023-01-02'],
        ['Bob', 35, 'true', '2023-01-03']
      ];
      const headers = ['name', 'age', 'active', 'date'];

      const dataTypes = FileProcessingService.detectDataTypes(data, headers);

      expect(dataTypes.name).toBe('categorical');
      expect(dataTypes.age).toBe('integer');
      expect(dataTypes.active).toBe('boolean');
      expect(dataTypes.date).toBe('date');
    });

    it('should calculate basic statistics for numeric columns', () => {
      const data = [
        [10], [20], [30], [40], [50]
      ];
      const headers = ['value'];
      const dataTypes = { value: 'integer' };

      const stats = FileProcessingService.calculateBasicStats(data, headers, dataTypes);

      expect(stats.value).toEqual({
        count: 5,
        mean: 30,
        median: 30,
        min: 10,
        max: 50,
        std: expect.any(Number),
        missing: 0
      });
    });
  });

  describe('Gemini Analysis Service', () => {
    it('should execute analysis with proper configuration', async () => {
      const mockFile = {
        id: 'test-file',
        name: 'test.csv',
        data: [['John', 25], ['Jane', 30]],
        headers: ['name', 'age'],
        size: 1000,
        type: 'text/csv',
        uploadedAt: new Date(),
        status: 'ready' as const
      };

      const result = await GeminiAnalysisService.analyzeData(mockFile);

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('fileId', mockFile.id);
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('insights');
      expect(result).toHaveProperty('visualizations');
      expect(result.summary.rowCount).toBe(2);
      expect(result.summary.columnCount).toBe(2);
    });

    it('should process natural language queries', async () => {
      const mockFile = {
        id: 'test-file',
        name: 'test.csv',
        data: [['John', 25], ['Jane', 30]],
        headers: ['name', 'age'],
        size: 1000,
        type: 'text/csv',
        uploadedAt: new Date(),
        status: 'ready' as const
      };

      const response = await GeminiAnalysisService.processQuery(
        mockFile,
        'What is the average age?'
      );

      expect(response).toHaveProperty('text');
      expect(response.text).toBe('Mock analysis result');
    });
  });

  describe('Data Visualization Store', () => {
    it('should manage file state correctly', () => {
      const store = useDataVisualizationStore.getState();
      const mockFile = {
        id: 'test-file',
        name: 'test.csv',
        data: [],
        headers: [],
        size: 1000,
        type: 'text/csv',
        uploadedAt: new Date(),
        status: 'ready' as const
      };

      store.addFile(mockFile);
      expect(store.uploadedFiles).toHaveLength(1);
      expect(store.uploadedFiles[0]).toEqual(mockFile);

      store.setCurrentFile(mockFile);
      expect(store.currentFile).toEqual(mockFile);

      store.removeFile(mockFile.id);
      expect(store.uploadedFiles).toHaveLength(0);
      expect(store.currentFile).toBeNull();
    });

    it('should manage analysis results correctly', () => {
      const store = useDataVisualizationStore.getState();
      const mockAnalysis = {
        id: 'analysis-1',
        fileId: 'file-1',
        summary: {
          rowCount: 100,
          columnCount: 5,
          dataTypes: {},
          missingValues: {},
          basicStats: {}
        },
        insights: {
          dataQuality: 'Good',
          keyFindings: ['Finding 1'],
          patterns: ['Pattern 1'],
          correlations: ['Correlation 1'],
          recommendations: ['Recommendation 1']
        },
        visualizations: [],
        generatedAt: new Date()
      };

      store.setAnalysisResult(mockAnalysis);
      expect(store.analysisResults[mockAnalysis.fileId]).toEqual(mockAnalysis);

      store.setCurrentAnalysis(mockAnalysis);
      expect(store.currentAnalysis).toEqual(mockAnalysis);
    });

    it('should manage UI state correctly', () => {
      const store = useDataVisualizationStore.getState();

      store.setActiveTab('analysis');
      expect(store.activeTab).toBe('analysis');

      store.setUploading(true);
      expect(store.isUploading).toBe(true);

      store.setAnalyzing(true);
      expect(store.isAnalyzing).toBe(true);

      store.setQuerying(true);
      expect(store.isQuerying).toBe(true);
    });

    it('should handle errors correctly', () => {
      const store = useDataVisualizationStore.getState();

      store.addError('Test error 1');
      store.addError('Test error 2');
      expect(store.errors).toHaveLength(2);

      store.removeError(0);
      expect(store.errors).toHaveLength(1);
      expect(store.errors[0]).toBe('Test error 2');

      store.clearErrors();
      expect(store.errors).toHaveLength(0);
    });
  });

  describe('End-to-End Workflow', () => {
    it('should complete full analysis workflow', async () => {
      const store = useDataVisualizationStore.getState();
      
      // Step 1: Upload file
      const csvContent = 'name,age,salary\nJohn,25,50000\nJane,30,60000\nBob,35,70000';
      const file = new File([csvContent], 'employees.csv', { type: 'text/csv' });
      
      const processedFile = await FileProcessingService.processFile(file);
      store.addFile(processedFile);
      store.setCurrentFile(processedFile);

      expect(store.currentFile).toBeTruthy();
      expect(store.currentFile?.data).toHaveLength(3);
      expect(store.currentFile?.headers).toEqual(['name', 'age', 'salary']);

      // Step 2: Run analysis
      const analysisResult = await GeminiAnalysisService.analyzeData(processedFile);
      store.setAnalysisResult(analysisResult);
      store.setCurrentAnalysis(analysisResult);

      expect(store.currentAnalysis).toBeTruthy();
      expect(store.currentAnalysis?.summary.rowCount).toBe(3);
      expect(store.currentAnalysis?.summary.columnCount).toBe(3);

      // Step 3: Verify visualizations were created
      expect(store.currentAnalysis?.visualizations).toBeDefined();
      expect(Array.isArray(store.currentAnalysis?.visualizations)).toBe(true);
    });
  });
});

// Mock data for testing
export const mockCsvData = `name,age,department,salary,start_date
John Doe,28,Engineering,75000,2022-01-15
Jane Smith,32,Marketing,65000,2021-03-20
Bob Johnson,45,Sales,80000,2020-07-10
Alice Brown,29,Engineering,72000,2022-05-01
Charlie Wilson,38,Marketing,68000,2019-11-30`;

export const mockExcelData = [
  ['Product', 'Category', 'Price', 'Stock', 'Rating'],
  ['Laptop', 'Electronics', 999.99, 50, 4.5],
  ['Phone', 'Electronics', 699.99, 100, 4.2],
  ['Desk', 'Furniture', 299.99, 25, 4.0],
  ['Chair', 'Furniture', 199.99, 30, 4.3],
  ['Monitor', 'Electronics', 399.99, 40, 4.4]
];

export const mockAnalysisResult = {
  id: 'analysis-test',
  fileId: 'file-test',
  summary: {
    rowCount: 5,
    columnCount: 5,
    dataTypes: {
      'Product': 'categorical',
      'Category': 'categorical', 
      'Price': 'number',
      'Stock': 'integer',
      'Rating': 'number'
    },
    missingValues: {
      'Product': 0,
      'Category': 0,
      'Price': 0,
      'Stock': 0,
      'Rating': 0
    },
    basicStats: {
      'Price': { mean: 519.99, median: 399.99, min: 199.99, max: 999.99 },
      'Stock': { mean: 49, median: 40, min: 25, max: 100 },
      'Rating': { mean: 4.28, median: 4.3, min: 4.0, max: 4.5 }
    }
  },
  insights: {
    dataQuality: 'Excellent - no missing values detected',
    keyFindings: [
      'Electronics category has highest average price',
      'Stock levels vary significantly across products',
      'All products have ratings above 4.0'
    ],
    patterns: [
      'Price correlates with product category',
      'Electronics items have higher stock levels'
    ],
    correlations: [
      'Weak negative correlation between price and stock'
    ],
    recommendations: [
      'Consider restocking furniture items',
      'Monitor electronics inventory turnover'
    ]
  },
  visualizations: [
    {
      id: 'price-histogram',
      type: 'histogram' as const,
      title: 'Price Distribution',
      description: 'Distribution of product prices',
      data: [{ x: [999.99, 699.99, 299.99, 199.99, 399.99], type: 'histogram' }],
      layout: { title: 'Price Distribution', xaxis: { title: 'Price' } }
    }
  ],
  generatedAt: new Date()
};
