/**
 * Test Tavily API functionality
 */

import { tavilySearchService } from './services/tavily-search.service';

export async function testTavilyAPI() {
  console.log('Testing Tavily API...');
  
  try {
    // Test service configuration
    const isConfigured = tavilySearchService.isConfigured();
    console.log('Tavily configured:', isConfigured);
    
    if (!isConfigured) {
      console.error('Tavily API key not configured');
      return { success: false, error: 'API key not configured' };
    }

    // Test simple search
    console.log('Testing simple search...');
    const result = await tavilySearchService.searchAcademic('climate change research', {
      maxResults: 3,
      searchDepth: 'basic',
      includeImages: false
    });
    
    console.log('Search successful:', {
      query: result.query,
      resultsCount: result.results.length,
      hasAnswer: !!result.answer
    });
    
    if (result.results.length > 0) {
      console.log('First result:', {
        title: result.results[0].title,
        url: result.results[0].url,
        score: result.results[0].score
      });
    }
    
    return { 
      success: true, 
      result,
      message: 'Tavily API working correctly'
    };
    
  } catch (error: any) {
    console.error('Tavily API test failed:', error);
    return { 
      success: false, 
      error: error.message,
      details: error
    };
  }
}

// Export test function for use in console
if (typeof window !== 'undefined') {
  (window as any).testTavily = testTavilyAPI;
}
