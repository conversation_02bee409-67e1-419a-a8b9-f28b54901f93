import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Key,
  AlertCircle,
  Info
} from "lucide-react";
import geminiBookService from '../services/gemini-book.service';

interface TestResult {
  status: 'idle' | 'testing' | 'success' | 'error';
  message: string;
  details?: any;
}

export const GeminiApiTest: React.FC = () => {
  const [testResult, setTestResult] = useState<TestResult>({
    status: 'idle',
    message: 'Click "Test API" to verify Google Gemini integration'
  });

  const testGeminiApi = async () => {
    setTestResult({
      status: 'testing',
      message: 'Testing Google Gemini API connection...'
    });

    try {
      // Check if API key is available
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('VITE_GEMINI_API_KEY not found in environment variables');
      }

      console.log('API Key found:', apiKey.substring(0, 10) + '...');

      // Check if service is configured
      if (!geminiBookService.isConfigured()) {
        throw new Error('Gemini service is not properly configured');
      }

      // Test with a simple outline generation
      const testMetadata = {
        title: "Test Book",
        subtitle: "",
        genre: "Non-Fiction",
        targetAudience: "General Public",
        keywords: ["test"],
        authors: ["Test Author"],
        description: "A simple test book for API verification",
        estimatedLength: "short" as const,
        tone: "professional" as const,
        chapterCount: 3
      };

      const testChapters = [
        {
          id: "chapter-1",
          title: "Introduction",
          outline: {
            id: "outline-1",
            title: "Introduction",
            description: "Introduction chapter",
            subSections: [],
            estimatedWordCount: 1000,
            order: 1
          },
          items: []
        }
      ];

      console.log('Attempting to generate test outline...');
      const outline = await geminiBookService.generateBookOutline(testMetadata, testChapters);

      if (outline && outline.title) {
        setTestResult({
          status: 'success',
          message: 'Google Gemini API connection successful!',
          details: {
            title: outline.title,
            chaptersGenerated: outline.chapters.length,
            totalWords: outline.totalEstimatedWords,
            readingTime: outline.estimatedReadingTime
          }
        });
      } else {
        throw new Error('Invalid response from Gemini API');
      }

    } catch (error) {
      console.error('Gemini API test failed:', error);
      
      let errorMessage = 'Unknown error occurred';
      let details = {};

      if (error instanceof Error) {
        errorMessage = error.message;
        
        // Provide specific guidance based on error type
        if (error.message.includes('API key')) {
          details = {
            issue: 'API Key Problem',
            solution: 'Check your VITE_GEMINI_API_KEY in the .env file',
            apiKeyFound: !!import.meta.env.VITE_GEMINI_API_KEY,
            apiKeyLength: import.meta.env.VITE_GEMINI_API_KEY?.length || 0
          };
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          details = {
            issue: 'API Quota/Rate Limit',
            solution: 'Wait a few minutes and try again, or check your Gemini API quota'
          };
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          details = {
            issue: 'Network Error',
            solution: 'Check your internet connection and try again'
          };
        } else if (error.message.includes('model')) {
          details = {
            issue: 'Model Access Error',
            solution: 'Verify that your API key has access to Gemini 2.5 Pro model'
          };
        } else {
          details = {
            issue: 'General API Error',
            fullError: error.message,
            stack: error.stack?.substring(0, 500)
          };
        }
      }

      setTestResult({
        status: 'error',
        message: errorMessage,
        details
      });
    }
  };

  const getStatusIcon = () => {
    switch (testResult.status) {
      case 'testing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (testResult.status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'testing':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5 text-purple-600" />
            Google Gemini API Test
          </CardTitle>
          <p className="text-gray-600">
            Test your Google Gemini API integration for book generation
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Test Button */}
          <Button 
            onClick={testGeminiApi} 
            disabled={testResult.status === 'testing'}
            className="w-full"
          >
            {testResult.status === 'testing' ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing API...
              </>
            ) : (
              'Test Google Gemini API'
            )}
          </Button>

          {/* Test Result */}
          <Card className={`${getStatusColor()}`}>
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                {getStatusIcon()}
                <div className="flex-1">
                  <p className="font-medium">{testResult.message}</p>
                  
                  {testResult.details && (
                    <div className="mt-3 space-y-2">
                      {testResult.status === 'success' && (
                        <div className="space-y-1 text-sm">
                          <div><strong>Generated Title:</strong> {testResult.details.title}</div>
                          <div><strong>Chapters:</strong> {testResult.details.chaptersGenerated}</div>
                          <div><strong>Estimated Words:</strong> {testResult.details.totalWords?.toLocaleString()}</div>
                          <div><strong>Reading Time:</strong> {testResult.details.readingTime}</div>
                        </div>
                      )}
                      
                      {testResult.status === 'error' && (
                        <div className="space-y-2 text-sm">
                          <div><strong>Issue:</strong> {testResult.details.issue}</div>
                          <div><strong>Solution:</strong> {testResult.details.solution}</div>
                          {testResult.details.apiKeyFound !== undefined && (
                            <div>
                              <strong>API Key Status:</strong> {testResult.details.apiKeyFound ? 
                                `Found (${testResult.details.apiKeyLength} characters)` : 
                                'Not found'
                              }
                            </div>
                          )}
                          {testResult.details.fullError && (
                            <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono">
                              {testResult.details.fullError}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Environment Info */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p><strong>Current Configuration:</strong></p>
                <div className="text-sm space-y-1">
                  <div>API Key: {import.meta.env.VITE_GEMINI_API_KEY ? 
                    `${import.meta.env.VITE_GEMINI_API_KEY.substring(0, 10)}...` : 
                    'Not configured'
                  }</div>
                  <div>Model: gemini-2.5-pro</div>
                  <div>Rate Limit: 10 seconds between requests</div>
                </div>
                {!import.meta.env.VITE_GEMINI_API_KEY && (
                  <div className="mt-2 p-2 bg-yellow-100 rounded text-sm">
                    <strong>Setup Required:</strong> Add your Gemini API key to the .env file:
                    <code className="block mt-1 bg-gray-100 p-1 rounded">
                      VITE_GEMINI_API_KEY=your_api_key_here
                    </code>
                    <p className="mt-1">
                      Get your API key from: <a 
                        href="https://makersuite.google.com/app/apikey" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Google AI Studio
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};
