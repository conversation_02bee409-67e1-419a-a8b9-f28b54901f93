/**
 * OpenRouter Article Service
 * Provides alternative AI processing for article analysis using OpenRouter API
 */

import { 
  ArticleFinderRequest, 
  ArticleFinderResponse, 
  ArticleAnalysis, 
  JournalRecommendation,
  InputType,
  AnalysisDepth
} from '../types';
import { ANALYSIS_PROMPTS } from '../constants';

export class OpenRouterArticleService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';
  private isConfigured: boolean = false;

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    this.isConfigured = Boolean(this.apiKey && this.apiKey.length > 20 && !this.apiKey.includes('your_'));
    
    if (this.isConfigured) {
      console.log('OpenRouter Article Service initialized successfully');
    } else {
      console.warn('OpenRouter API key not found or invalid');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isServiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    if (!this.isConfigured) {
      return { success: false, error: 'OpenRouter API key not configured' };
    }

    try {
      const testPrompt = "Respond with 'API connection successful' if you can read this.";
      await this.callOpenRouterAPI(testPrompt, 'anthropic/claude-3.5-sonnet');
      return { success: true };
    } catch (error) {
      console.error('OpenRouter API connection test failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Analyze article content using OpenRouter models
   */
  async analyzeArticle(request: ArticleFinderRequest): Promise<ArticleFinderResponse> {
    if (!this.isConfigured) {
      throw new Error('OpenRouter API key not configured');
    }

    const startTime = Date.now();
    
    try {
      const prompt = this.buildAnalysisPrompt(
        request.content, 
        request.inputType, 
        request.analysisDepth,
        request.additionalContext
      );
      
      const response = await this.callOpenRouterAPI(prompt, request.model);
      
      // Parse the JSON response
      const analysisData = this.parseAnalysisResponse(response);
      
      // Create analysis object
      const analysis: ArticleAnalysis = {
        researchDomain: analysisData.researchDomain,
        methodology: analysisData.methodology,
        keyTopics: analysisData.keyTopics || [],
        researchThemes: analysisData.researchThemes || [],
        academicField: analysisData.academicField,
        noveltyScore: analysisData.noveltyScore || 0.7,
        contributionLevel: analysisData.contributionLevel || 'Medium',
        recommendedJournals: this.processJournalRecommendations(analysisData.journals || []),
        analysisConfidence: analysisData.confidence || 0.8,
        generatedAt: new Date()
      };

      const processingTime = Date.now() - startTime;

      return {
        analysis,
        recommendations: analysis.recommendedJournals,
        confidence: analysis.analysisConfidence,
        processingTime
      };

    } catch (error) {
      console.error('OpenRouter article analysis failed:', error);
      throw new Error(`Failed to analyze article: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate journal recommendations using specific OpenRouter model
   */
  async generateJournalRecommendations(
    articleContent: string,
    researchField: string,
    model: string = 'anthropic/claude-3.5-sonnet'
  ): Promise<JournalRecommendation[]> {
    if (!this.isConfigured) {
      throw new Error('OpenRouter API key not configured');
    }

    try {
      const prompt = this.buildJournalRecommendationPrompt(articleContent, researchField);
      const response = await this.callOpenRouterAPI(prompt, model);
      
      const data = this.parseJournalResponse(response);
      return this.processJournalRecommendations(data.journals || []);

    } catch (error) {
      console.error('Journal recommendation generation failed:', error);
      throw new Error(`Failed to generate recommendations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouterAPI(prompt: string, model: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Article Finder Platform'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 8192,
        stream: false
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenRouter request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content || '';

    if (!content) {
      throw new Error('No content received from OpenRouter service');
    }

    return content;
  }

  /**
   * Build analysis prompt
   */
  private buildAnalysisPrompt(
    content: string, 
    inputType: InputType, 
    depth: AnalysisDepth,
    additionalContext?: string
  ): string {
    const basePrompt = ANALYSIS_PROMPTS[depth];
    const contextInfo = additionalContext ? `\n\nAdditional Context: ${additionalContext}` : '';
    
    return `${basePrompt}

Input Type: ${inputType}
Content to analyze:
${content}${contextInfo}

Please provide your response in the following JSON format:
{
  "researchDomain": "string",
  "methodology": "string", 
  "keyTopics": ["topic1", "topic2", ...],
  "researchThemes": ["theme1", "theme2", ...],
  "academicField": "string",
  "noveltyScore": 0.0-1.0,
  "contributionLevel": "High|Medium|Low",
  "confidence": 0.0-1.0,
  "journals": [
    {
      "name": "Journal Name",
      "publisher": "Publisher",
      "description": "Brief description",
      "scope": ["scope1", "scope2"],
      "impactFactor": 0.0,
      "acceptanceRate": 0.0,
      "averageReviewTime": 0,
      "publicationFees": 0,
      "isOpenAccess": boolean,
      "matchScore": 0.0-1.0,
      "matchReasons": ["reason1", "reason2"]
    }
  ]
}`;
  }

  /**
   * Build journal recommendation prompt
   */
  private buildJournalRecommendationPrompt(content: string, researchField: string): string {
    return `Based on this research content, recommend the best academic journals for publication.

Research Field: ${researchField}
Content: ${content}

Provide detailed journal recommendations in JSON format:
{
  "journals": [
    {
      "name": "Journal Name",
      "publisher": "Publisher",
      "description": "Description",
      "scope": ["scope1", "scope2"],
      "impactFactor": 0.0,
      "acceptanceRate": 0.0,
      "averageReviewTime": 0,
      "publicationFees": 0,
      "isOpenAccess": boolean,
      "matchScore": 0.0-1.0,
      "matchReasons": ["reason1", "reason2"]
    }
  ]
}`;
  }

  /**
   * Parse analysis response
   */
  private parseAnalysisResponse(response: string): any {
    try {
      // Clean the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Failed to parse analysis response:', error);
      throw new Error('Invalid response format from AI service');
    }
  }

  /**
   * Parse journal response
   */
  private parseJournalResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Failed to parse journal response:', error);
      return { journals: [] };
    }
  }

  /**
   * Process and enhance journal recommendations
   */
  private processJournalRecommendations(journals: any[]): JournalRecommendation[] {
    return journals.map((journal, index) => ({
      id: `openrouter-journal-${Date.now()}-${index}`,
      name: journal.name || 'Unknown Journal',
      publisher: journal.publisher || 'Unknown Publisher',
      issn: journal.issn || '',
      website: journal.website || '',
      description: journal.description || '',
      scope: journal.scope || [],
      targetAudience: journal.targetAudience || 'Academic researchers',
      editorialBoard: journal.editorialBoard || [],
      recentTopics: journal.recentTopics || [],
      metrics: {
        impactFactor: journal.impactFactor || 0,
        citeScore: journal.citeScore || 0,
        hIndex: journal.hIndex || 0,
        acceptanceRate: journal.acceptanceRate || 0,
        averageReviewTime: journal.averageReviewTime || 90,
        averagePublicationTime: journal.averagePublicationTime || 120,
        publicationFees: journal.publicationFees || 0,
        isOpenAccess: journal.isOpenAccess || false,
        quartile: journal.quartile || 'Q3'
      },
      matchScore: journal.matchScore || 0.5,
      matchReasons: journal.matchReasons || [],
      submissionGuidelines: journal.submissionGuidelines || 'Check journal website for guidelines',
      specialIssues: journal.specialIssues || [],
      geographicFocus: journal.geographicFocus || 'International',
      languageRequirements: journal.languageRequirements || ['English']
    }));
  }
}

export const openRouterArticleService = new OpenRouterArticleService();

// Add to window for testing
if (typeof window !== 'undefined') {
  (window as any).testOpenRouterArticle = async () => {
    console.log('Testing OpenRouter Article Service...');
    try {
      const testRequest = {
        content: 'Deep learning applications in medical image analysis',
        inputType: 'title' as InputType,
        analysisDepth: 'comprehensive' as AnalysisDepth,
        model: 'anthropic/claude-3.5-sonnet'
      };
      
      const result = await openRouterArticleService.analyzeArticle(testRequest);
      console.log('✅ OpenRouter Article Service test successful:', result);
      return result;
    } catch (error) {
      console.error('❌ OpenRouter Article Service test failed:', error);
      throw error;
    }
  };
}
