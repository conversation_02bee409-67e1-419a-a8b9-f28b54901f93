import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { debugAuthConfig } from "../utils/auth-debug";

const AuthCallbackPage = () => {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log("Starting auth callback handling...");
        console.log("Full callback URL:", window.location.href);

        // Debug authentication configuration
        const authConfig = debugAuthConfig();
        console.log("Auth config:", authConfig);
        
        // Examine all URL parameters for debugging
        const url = new URL(window.location.href);
        const allParams: Record<string, string> = {};
        url.searchParams.forEach((value, key) => {
          allParams[key] = key.includes('token') || key.includes('code') ? 'REDACTED' : value;
        });
        console.log("URL parameters:", allParams);
        
        // Check for any error parameters in URL that might indicate OAuth problems
        const errorCode = url.searchParams.get('error');
        const errorDescription = url.searchParams.get('error_description');
        
        if (errorCode) {
          console.error(`OAuth error: ${errorCode} - ${errorDescription}`);
          toast.error(`Authentication error: ${errorDescription || errorCode}`);
          navigate("/login");
          return;
        }
        
        // Try to get the auth code from URL if available (debug only)
        const code = url.searchParams.get('code');
        if (code) {
          console.log("OAuth code found in URL - length:", code.length);
        } else {
          console.log("No OAuth code found in URL - this might be an issue");
          toast.error("Authentication failed: No authorization code received");
          navigate("/login");
          return;
        }
        
        // Process the OAuth callback with robust error handling
        console.log("Processing OAuth callback...");
        
        try {
          console.log("Exchanging authorization code for session...");
          
          // Explicitly exchange the code first (most reliable method)
          await supabase.auth.exchangeCodeForSession(code);
          console.log("Code exchange completed successfully");
          
          // A short delay to allow Supabase client to update its internal state
          await new Promise(resolve => setTimeout(resolve, 300));
          
          // Get the session after exchange
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
          
          if (sessionError || !sessionData.session) {
            console.error("Unable to get session after code exchange:", sessionError);
            throw new Error("Failed to establish session after code exchange");
          }
          
          console.log("Authentication successful, user:", sessionData.session.user.email);
          
          // Check for return URL in storage, default to app
          const returnUrl = localStorage.getItem('authReturnUrl') || '/app';
          localStorage.removeItem('authReturnUrl'); // Clean up

          // Ensure we're redirecting to the correct domain
          const targetUrl = import.meta.env.PROD ?
            `https://verbira.com${returnUrl}` :
            returnUrl;

          console.log("Redirecting to:", targetUrl);

          // Use window.location for cross-domain redirects in production
          if (import.meta.env.PROD && !window.location.origin.includes('verbira.com')) {
            console.log("Cross-domain redirect to production");
            window.location.href = targetUrl;
          } else {
            navigate(returnUrl, { replace: true });
          }
          
        } catch (exchangeError: any) {
          console.error("Error during code exchange:", exchangeError);
          
          // Provide specific guidance based on error type
          if (exchangeError.message?.includes("Unable to exchange external code")) {
            toast.error("Authentication failed: The login code has expired", {
              description: "This can happen if the code was already used or the session timed out",
              action: {
                label: "Diagnostics",
                onClick: () => navigate("/oauth-diagnostics")
              }
            });
          } else if (exchangeError.message?.includes("bad_oauth_state")) {
            toast.error("Authentication failed: Session mismatch", {
              description: "This can happen if you attempted to login multiple times. Please clear your browser data and try again.",
              action: {
                label: "Diagnostics",
                onClick: () => navigate("/oauth-diagnostics")
              }
            });
          } else {
            toast.error("Authentication failed", {
              description: exchangeError.message || "An unknown error occurred",
              action: {
                label: "Diagnostics",
                onClick: () => navigate("/oauth-diagnostics")
              }
            });
          }
          
          // Redirect back to login after a delay
          setTimeout(() => navigate("/login"), 1000);
          return;
        }
        
      } catch (error: any) {
        console.error("Unexpected error in auth callback:", error);
        toast.error("Authentication failed", {
          description: error.message || "An unknown error occurred"
        });
        navigate("/login");
      } finally {
        setIsProcessing(false);
      }
    };
    
    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4">
      <div className="text-center">
        {isProcessing ? (
          <>
            <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">Completing Sign In</h1>
            <p className="text-gray-600">Please wait while we authenticate your account...</p>
          </>
        ) : (
          <h1 className="text-2xl font-bold">Redirecting...</h1>
        )}
      </div>
    </div>
  );
};

export default AuthCallbackPage;
