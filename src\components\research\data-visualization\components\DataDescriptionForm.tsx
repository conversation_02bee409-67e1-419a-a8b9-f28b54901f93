import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  Brain,
  Lightbulb,
  Target,
  Users,
  Calendar,
  Globe,
  Sparkles,
  CheckCircle,
  Info,
  ArrowRight
} from "lucide-react";
import { UploadedFile } from '../types';

interface DataDescriptionFormProps {
  file: UploadedFile;
  description: string;
  onDescriptionChange: (description: string) => void;
  className?: string;
}

export const DataDescriptionForm: React.FC<DataDescriptionFormProps> = ({
  file,
  description,
  onDescriptionChange,
  className = ""
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const templates = [
    {
      id: 'sales',
      icon: Target,
      title: 'Sales & Revenue Data',
      description: 'Customer transactions, revenue, products',
      template: `This dataset contains sales and revenue information including:
- Customer demographics and purchase behavior
- Product performance and pricing data
- Time-based sales trends and seasonal patterns
- Geographic distribution of sales

Key metrics to analyze: revenue trends, customer segments, product performance, seasonal variations.`
    },
    {
      id: 'hr',
      icon: Users,
      title: 'HR & Employee Data',
      description: 'Employee records, performance, demographics',
      template: `This dataset contains human resources and employee information including:
- Employee demographics and job roles
- Performance metrics and ratings
- Salary and compensation data
- Department and organizational structure

Key metrics to analyze: employee performance, salary distributions, departmental comparisons, tenure patterns.`
    },
    {
      id: 'financial',
      icon: Calendar,
      title: 'Financial Data',
      description: 'Financial metrics, budgets, expenses',
      template: `This dataset contains financial information including:
- Revenue and expense tracking
- Budget allocations and spending patterns
- Financial performance indicators
- Time-series financial data

Key metrics to analyze: financial trends, budget variance, expense categories, profitability patterns.`
    },
    {
      id: 'survey',
      icon: Globe,
      title: 'Survey & Research Data',
      description: 'Survey responses, research findings',
      template: `This dataset contains survey and research data including:
- Survey responses and participant demographics
- Rating scales and categorical responses
- Research variables and measurements
- Response patterns and correlations

Key metrics to analyze: response distributions, demographic patterns, correlation analysis, satisfaction trends.`
    }
  ];

  const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template.id);
    onDescriptionChange(template.template);
  };

  const clearDescription = () => {
    setSelectedTemplate(null);
    onDescriptionChange('');
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-purple-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl">
              <FileText className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Describe Your Data
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Help AI understand your data context for better analysis and insights
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Alert className="border-purple-200 bg-purple-50">
            <Brain className="h-4 w-4 text-purple-600" />
            <AlertDescription className="text-purple-800">
              <strong>Why describe your data?</strong> Providing context helps our AI generate more accurate insights, 
              relevant visualizations, and meaningful analysis tailored to your specific use case.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Quick Templates */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            Quick Start Templates
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose a template that matches your data type, or write a custom description below
          </p>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.map((template) => (
              <Card 
                key={template.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-md ${
                  selectedTemplate === template.id 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleTemplateSelect(template)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${
                      selectedTemplate === template.id 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      <template.icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{template.title}</h3>
                      <p className="text-sm text-gray-600">{template.description}</p>
                      {selectedTemplate === template.id && (
                        <div className="flex items-center gap-1 mt-2 text-blue-600">
                          <CheckCircle className="h-4 w-4" />
                          <span className="text-sm font-medium">Selected</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Description */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-orange-500" />
              Data Description
            </CardTitle>
            {description && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearDescription}
                className="text-gray-600"
              >
                Clear
              </Button>
            )}
          </div>
          <p className="text-sm text-gray-600">
            Describe what your data represents, its source, and what insights you're looking for
          </p>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Textarea
            value={description}
            onChange={(e) => onDescriptionChange(e.target.value)}
            placeholder="Describe your data here... For example:

This dataset contains customer transaction data from our e-commerce platform over the past year. It includes customer demographics, purchase amounts, product categories, and timestamps. I'm interested in understanding customer behavior patterns, seasonal trends, and identifying high-value customer segments for targeted marketing campaigns."
            className="min-h-[200px] resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500"
          />
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {description.length} characters
              {description.length > 500 && (
                <span className="text-green-600 ml-2">
                  ✓ Good detail level
                </span>
              )}
            </div>
            
            {description && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3 w-3 mr-1" />
                Description provided
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* File Context */}
      <Card className="border-0 shadow-lg bg-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Info className="h-5 w-5 text-blue-600" />
            Your Data Context
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{file.data.length}</div>
              <div className="text-sm text-gray-600">Data Rows</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{file.headers.length}</div>
              <div className="text-sm text-gray-600">Columns</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(file.size / 1024).toFixed(1)}KB
              </div>
              <div className="text-sm text-gray-600">File Size</div>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Column Headers:</h4>
            <div className="flex flex-wrap gap-2">
              {file.headers.slice(0, 10).map((header, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {header}
                </Badge>
              ))}
              {file.headers.length > 10 && (
                <Badge variant="outline" className="text-xs">
                  +{file.headers.length - 10} more
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      {description && (
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full">
                <ArrowRight className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Ready for Analysis!</h3>
                <p className="text-sm text-gray-600">
                  Your data description will help AI provide more accurate insights and relevant visualizations.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
