# 🚀 Production Deployment Guide for Vercel

## 📋 Pre-Deployment Checklist

### ✅ Files Created/Updated:
- [x] `vercel.json` - Vercel deployment configuration
- [x] `api/server.js` - Updated for serverless functions
- [x] `api/package.json` - Optimized for Vercel
- [x] `.env.production` - Production environment template
- [x] `.env.example` - Updated with all required variables
- [x] `vite.config.ts` - Optimized for production builds
- [x] `package.json` - Added production scripts

## 🔧 Environment Variables Setup

### Required Environment Variables for Vercel:

```bash
# AI API Keys (Choose one)
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here  # For serverless functions

# Supabase Configuration
VITE_SUPABASE_URL=https://swsnqpavwcnqiihsidss.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Tavily API for research search
VITE_TAVILY_API_KEY=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

# Production Settings
NODE_ENV=production
VITE_NODE_ENV=production
```

## 🌐 Supabase Configuration Updates

### 1. Update Site URL in Supabase Dashboard:
- Go to: Authentication → Settings → Site URL
- Change from: `http://localhost:8081`
- Change to: `https://verbira.com`

### 2. Update Redirect URLs:
- Go to: Authentication → Settings → Redirect URLs
- Add: `https://verbira.com/auth/callback`
- Keep: `http://localhost:8081/auth/callback` (for local development)

### 3. Update CORS Settings:
- Go to: API → Settings → CORS
- Add: `https://verbira.com`

## 🔑 Google OAuth Configuration

### Update Google Cloud Console:
1. Go to: Google Cloud Console → APIs & Services → Credentials
2. Edit your OAuth 2.0 Client ID
3. Add to Authorized redirect URIs:
   - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
   - `https://verbira.com/auth/callback`

## 📦 Deployment Steps

### Step 1: Test Local Production Build
```bash
npm run build:prod
npm run preview
```

### Step 2: Deploy to Vercel
```bash
# Option 1: Using Vercel CLI
npm install -g vercel
vercel

# Option 2: Connect GitHub repository to Vercel dashboard
# Go to vercel.com → Import Project → Connect GitHub
```

### Step 3: Configure Environment Variables in Vercel
1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables
2. Add all the environment variables listed above
3. Make sure to set them for Production, Preview, and Development

### Step 4: Update Domain References
After deployment, update:
1. Supabase Site URL to your Vercel domain
2. Google OAuth redirect URIs
3. Any hardcoded localhost references

## 🧪 Testing Checklist

After deployment, test:
- [ ] Homepage loads correctly
- [ ] User registration/login works
- [ ] Google OAuth login works
- [ ] AI paper generation works
- [ ] Research search functionality works
- [ ] File uploads work
- [ ] All API endpoints respond correctly

## 🔍 Troubleshooting

### Common Issues:

1. **Environment Variables Not Loading**
   - Check Vercel dashboard environment variables
   - Ensure VITE_ prefix for frontend variables
   - Redeploy after adding variables

2. **API Endpoints Not Working**
   - Check `/api/server.js` is deployed as serverless function
   - Verify OPENROUTER_API_KEY is set (without VITE_ prefix)

3. **Authentication Issues**
   - Update Supabase Site URL to production domain
   - Update Google OAuth redirect URIs
   - Clear browser cache and cookies

4. **CORS Errors**
   - Update Supabase CORS settings
   - Check vercel.json headers configuration

## 📝 Post-Deployment Tasks

1. **Monitor Performance**
   - Check Vercel Analytics
   - Monitor Supabase usage
   - Check API response times

2. **Set Up Custom Domain** (Optional)
   - Add custom domain in Vercel
   - Update all OAuth and Supabase settings
   - Update SSL certificates

3. **Enable Analytics** (Optional)
   - Vercel Analytics
   - Google Analytics
   - Supabase Analytics

## 🔄 Continuous Deployment

The app is now configured for automatic deployment:
- Push to main branch → Automatic deployment
- Pull requests → Preview deployments
- Environment variables → Managed in Vercel dashboard

## 📞 Support

If you encounter issues:
1. Check Vercel deployment logs
2. Check browser console for errors
3. Verify all environment variables are set
4. Test API endpoints individually
