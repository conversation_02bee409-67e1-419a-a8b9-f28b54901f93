/**
 * Knowledge Base Service
 * Handles Supabase database operations for saving and retrieving research reports
 */

import { KnowledgeBaseReport, Report, ResearchHistoryItem } from '../types';
import { KNOWLEDGE_BASE_KEY } from '../constants';
import { supabaseService } from './supabase.service';

class KnowledgeBaseService {
  /**
   * Add report to knowledge base using Supabase
   */
  async addReport(query: string, report: Report): Promise<boolean> {
    try {
      // Check if Supabase is configured
      if (!supabaseService.isConfigured()) {
        console.warn('Supabase not configured, falling back to local storage');
        return this.addReportToLocalStorage(query, report);
      }

      // Create a new session
      const session = await supabaseService.createSession({
        title: report.title,
        query: query,
        report_prompt: query,
        selected_model: 'google/gemini-2.0-flash-001', // Default model
        is_agent_mode: false,
        status: 'completed',
      });

      if (!session) {
        throw new Error('Failed to create session');
      }

      // Save the report
      const savedReport = await supabaseService.saveReport(session.id, report);

      if (!savedReport) {
        throw new Error('Failed to save report');
      }

      // Save sources
      if (report.sources && report.sources.length > 0) {
        const selectedIds = report.usedSources?.map(index => report.sources[index].id) || [];
        await supabaseService.saveSources(session.id, report.sources, selectedIds);
      }

      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to add report to knowledge base:', error);
      // Fallback to local storage
      return this.addReportToLocalStorage(query, report);
    }
  }

  /**
   * Fallback method to add report to local storage
   */
  private addReportToLocalStorage(query: string, report: Report): boolean {
    try {
      const reports = this.getReportsFromLocalStorage();
      const newReport: KnowledgeBaseReport = {
        id: this.generateId(),
        timestamp: Date.now(),
        query,
        report,
      };

      reports.push(newReport);
      this.saveReportsToLocalStorage(reports);
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to add report to local storage:', error);
      return false;
    }
  }

  /**
   * Get all reports from knowledge base
   */
  async getReports(): Promise<KnowledgeBaseReport[]> {
    try {
      // Check if Supabase is configured
      if (!supabaseService.isConfigured()) {
        console.warn('Supabase not configured, falling back to local storage');
        return this.getReportsFromLocalStorage();
      }

      // Get history from Supabase
      const historyItems = await supabaseService.getResearchHistory(20, 0);

      // Convert to knowledge base format
      return historyItems
        .filter(item => item.report) // Only include items with reports
        .map(item => this.convertHistoryItemToKnowledgeBaseReport(item))
        .sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to get knowledge base reports:', error);
      // Fallback to local storage
      return this.getReportsFromLocalStorage();
    }
  }

  /**
   * Get reports from local storage (fallback)
   */
  private getReportsFromLocalStorage(): KnowledgeBaseReport[] {
    try {
      const reportsJson = localStorage.getItem(KNOWLEDGE_BASE_KEY);
      if (!reportsJson) return [];

      const reports = JSON.parse(reportsJson) as KnowledgeBaseReport[];
      return reports.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to get reports from local storage:', error);
      return [];
    }
  }

  /**
   * Get report by ID
   */
  async getReportById(id: string): Promise<KnowledgeBaseReport | null> {
    try {
      // For now, get all reports and filter
      const reports = await this.getReports();
      return reports.find(report => report.id === id) || null;
    } catch (error) {
      console.error('Failed to get report by ID:', error);
      return null;
    }
  }

  /**
   * Search reports in knowledge base
   */
  async searchReports(query: string): Promise<KnowledgeBaseReport[]> {
    try {
      // Check if Supabase is configured
      if (!supabaseService.isConfigured()) {
        console.warn('Supabase not configured, falling back to local storage');
        return this.searchReportsInLocalStorage(query);
      }

      // Search in Supabase
      const historyItems = await supabaseService.searchHistory({
        query: query,
      }, 20);

      // Convert to knowledge base format
      return historyItems
        .filter(item => item.report) // Only include items with reports
        .map(item => this.convertHistoryItemToKnowledgeBaseReport(item));
    } catch (error) {
      console.error('Failed to search knowledge base:', error);
      // Fallback to local storage
      return this.searchReportsInLocalStorage(query);
    }
  }

  /**
   * Search reports in local storage (fallback)
   */
  private searchReportsInLocalStorage(query: string): KnowledgeBaseReport[] {
    try {
      const reports = this.getReportsFromLocalStorage();

      if (!query.trim()) {
        return reports;
      }

      const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

      return reports.filter(report => {
        const searchText = `${report.query} ${report.report.title} ${report.report.summary}`.toLowerCase();
        return searchTerms.every(term => searchText.includes(term));
      });
    } catch (error) {
      console.error('Failed to search local storage:', error);
      return [];
    }
  }

  /**
   * Get reports by date range
   */
  getReportsByDateRange(startDate: Date, endDate: Date): KnowledgeBaseReport[] {
    try {
      const reports = this.getReports();
      const startTime = startDate.getTime();
      const endTime = endDate.getTime();
      
      return reports.filter(report => 
        report.timestamp >= startTime && report.timestamp <= endTime
      );
    } catch (error) {
      console.error('Failed to get reports by date range:', error);
      return [];
    }
  }

  /**
   * Get recent reports
   */
  getRecentReports(limit: number = 10): KnowledgeBaseReport[] {
    try {
      const reports = this.getReports();
      return reports.slice(0, limit);
    } catch (error) {
      console.error('Failed to get recent reports:', error);
      return [];
    }
  }

  /**
   * Update report in knowledge base
   */
  updateReport(reportId: string, updates: Partial<Omit<KnowledgeBaseReport, 'id' | 'timestamp'>>): boolean {
    try {
      const reports = this.getReports();
      const reportIndex = reports.findIndex(report => report.id === reportId);
      
      if (reportIndex === -1) {
        return false;
      }

      reports[reportIndex] = {
        ...reports[reportIndex],
        ...updates,
      };

      this.saveReports(reports);
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to update report:', error);
      return false;
    }
  }



  /**
   * Convert history item to knowledge base report format
   */
  private convertHistoryItemToKnowledgeBaseReport(item: ResearchHistoryItem): KnowledgeBaseReport {
    return {
      id: item.session.id,
      timestamp: new Date(item.session.created_at).getTime(),
      query: item.session.query,
      report: {
        title: item.report?.title || item.session.title,
        summary: item.report?.summary || 'No summary available',
        sections: item.report?.sections || [],
        sources: item.report?.sources || [],
        usedSources: item.report?.used_sources || [],
      }
    };
  }

  /**
   * Delete report from knowledge base
   */
  async deleteReport(reportId: string): Promise<boolean> {
    try {
      // For now, we don't delete from Supabase (to preserve history)
      // Just remove from local storage if it exists there
      const localReports = this.getReportsFromLocalStorage();
      const filteredReports = localReports.filter(report => report.id !== reportId);
      this.saveReportsToLocalStorage(filteredReports);
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to delete report from knowledge base:', error);
      return false;
    }
  }

  /**
   * Clear all reports from knowledge base
   */
  async clearAll(): Promise<boolean> {
    try {
      // Only clear local storage (preserve Supabase data)
      localStorage.removeItem(KNOWLEDGE_BASE_KEY);
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to clear knowledge base:', error);
      return false;
    }
  }

  /**
   * Export knowledge base as JSON
   */
  async exportAsJson(): Promise<string> {
    try {
      const reports = await this.getReports();
      return JSON.stringify(reports, null, 2);
    } catch (error) {
      console.error('Failed to export knowledge base:', error);
      throw new Error('Failed to export knowledge base');
    }
  }

  /**
   * Import knowledge base from JSON
   */
  async importFromJson(jsonData: string): Promise<boolean> {
    try {
      const reports = JSON.parse(jsonData) as KnowledgeBaseReport[];

      // Validate structure
      if (!Array.isArray(reports)) {
        throw new Error('Invalid data format');
      }

      for (const report of reports) {
        if (!this.validateReport(report)) {
          throw new Error('Invalid report structure');
        }
      }

      // Save to local storage (for now)
      this.saveReportsToLocalStorage(reports);
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Failed to import knowledge base:', error);
      return false;
    }
  }

  /**
   * Get knowledge base statistics
   */
  async getStats(): Promise<{
    totalReports: number;
    totalQueries: number;
    oldestReport: Date | null;
    newestReport: Date | null;
    averageReportLength: number;
  }> {
    try {
      const reports = await this.getReports();

      if (reports.length === 0) {
        return {
          totalReports: 0,
          totalQueries: 0,
          oldestReport: null,
          newestReport: null,
          averageReportLength: 0,
        };
      }

      const timestamps = reports.map(r => r.timestamp);
      const reportLengths = reports.map(r =>
        r.report.summary.length + r.report.sections.reduce((acc, s) => acc + s.content.length, 0)
      );

      return {
        totalReports: reports.length,
        totalQueries: new Set(reports.map(r => r.query.toLowerCase())).size,
        oldestReport: new Date(Math.min(...timestamps)),
        newestReport: new Date(Math.max(...timestamps)),
        averageReportLength: Math.round(reportLengths.reduce((a, b) => a + b, 0) / reportLengths.length),
      };
    } catch (error) {
      console.error('Failed to get knowledge base stats:', error);
      return {
        totalReports: 0,
        totalQueries: 0,
        oldestReport: null,
        newestReport: null,
        averageReportLength: 0,
      };
    }
  }

  /**
   * Private helper methods
   */
  private generateId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private saveReportsToLocalStorage(reports: KnowledgeBaseReport[]): void {
    localStorage.setItem(KNOWLEDGE_BASE_KEY, JSON.stringify(reports));
  }

  private notifyChange(): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new Event('knowledge_base_change'));
    }
  }

  private validateReport(report: any): report is KnowledgeBaseReport {
    return (
      typeof report === 'object' &&
      report !== null &&
      typeof report.id === 'string' &&
      typeof report.timestamp === 'number' &&
      typeof report.query === 'string' &&
      typeof report.report === 'object' &&
      typeof report.report.title === 'string' &&
      typeof report.report.summary === 'string' &&
      Array.isArray(report.report.sections) &&
      Array.isArray(report.report.sources)
    );
  }
}

export const knowledgeBaseService = new KnowledgeBaseService();
