import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Book,
  FileCheck,
  BarChart3,
  Sparkles,
  Bot,
  Users,
  Zap,
  Shield,
  ArrowRight,
  CheckCircle,
  Star,
  Play,
  GraduationCap,
  Globe,
  Clock,
  Award
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const HomePage = () => {
  const navigate = useNavigate();
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const features = [
    {
      icon: <FileText className="h-8 w-8" />,
      title: "AI Paper Generator",
      description: "Generate complete research papers with proper citations, methodology, and academic structure.",
      gradient: "from-blue-600 to-indigo-600"
    },
    {
      icon: <Book className="h-8 w-8" />,
      title: "AI Book Generator", 
      description: "Create comprehensive books with multiple chapters, structured content, and professional formatting.",
      gradient: "from-purple-600 to-pink-600"
    },
    {
      icon: <FileCheck className="h-8 w-8" />,
      title: "AI Article Reviewer",
      description: "Upload papers for in-depth AI review with sentence-level analysis and improvement suggestions.",
      gradient: "from-green-600 to-emerald-600"
    },
    {
      icon: <BarChart3 className="h-8 w-8" />,
      title: "Research Analysis",
      description: "Analyze multiple papers, identify research gaps, and generate comprehensive literature reviews.",
      gradient: "from-orange-600 to-red-600"
    }
  ];

  const benefits = [
    {
      icon: <Zap className="h-5 w-5" />,
      title: "10x Faster Research",
      description: "Complete research papers in hours, not weeks"
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: "Academic Quality",
      description: "Professional formatting and proper citations"
    },
    {
      icon: <Bot className="h-5 w-5" />,
      title: "AI-Powered",
      description: "Advanced AI models for intelligent content generation"
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: "Researcher-Friendly",
      description: "Designed specifically for academic and professional use"
    }
  ];

  const testimonials = [
    {
      name: "Dr. Sarah Chen",
      role: "Research Scientist",
      content: "Verbira has revolutionized my research workflow. The AI-generated papers maintain academic rigor while saving me countless hours.",
      rating: 5
    },
    {
      name: "Prof. Michael Rodriguez",
      role: "University Professor",
      content: "The article reviewer feature provides incredibly detailed feedback that helps my students improve their writing significantly.",
      rating: 5
    },
    {
      name: "Lisa Thompson",
      role: "Graduate Student",
      content: "From literature review to final draft, Verbira guided me through my entire thesis. Absolutely indispensable!",
      rating: 5
    }
  ];

  const stats = [
    { number: "50,000+", label: "Papers Generated" },
    { number: "15,000+", label: "Researchers Helped" },
    { number: "98%", label: "Satisfaction Rate" },
    { number: "24/7", label: "AI Availability" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                <GraduationCap className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Verbira
                </h1>
                <p className="text-sm text-gray-600">AI Research Assistant</p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => {
                  const featuresSection = document.getElementById('features');
                  featuresSection?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="text-gray-600 hover:text-blue-600 transition-colors cursor-pointer"
              >
                Features
              </button>
              <button
                onClick={() => navigate('/pricing')}
                className="text-gray-600 hover:text-blue-600 transition-colors cursor-pointer"
              >
                Pricing
              </button>
              <button
                onClick={() => navigate('/about')}
                className="text-gray-600 hover:text-blue-600 transition-colors cursor-pointer"
              >
                About
              </button>
              <button
                onClick={() => navigate('/contact')}
                className="text-gray-600 hover:text-blue-600 transition-colors cursor-pointer"
              >
                Contact
              </button>
              <Button
                variant="outline"
                onClick={() => {
                  console.log("Navigating to login page");
                  navigate('/login');
                }}
                className="border-2 hover:bg-blue-50"
              >
                Sign In
              </Button>
              <Button
                onClick={() => {
                  console.log("Navigating to signup page");
                  navigate('/signup');
                }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Get Started
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="text-center max-w-4xl mx-auto">
          <Badge className="mb-6 bg-blue-100 text-blue-700 hover:bg-blue-100 px-4 py-2">
            <Sparkles className="h-4 w-4 mr-2" />
            Powered by Advanced AI Models
          </Badge>
          
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent leading-tight">
            Revolutionary AI Research Assistant
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto">
            Generate research papers, books, and conduct literature reviews with our advanced AI platform. 
            Transform your research workflow and focus on what matters most - discovery and innovation.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button 
              size="lg" 
              onClick={() => {
                console.log("Navigating to signup page");
                window.location.href = "/signup";
              }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-4"
            >
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              onClick={() => setIsVideoPlaying(true)}
              className="border-2 border-gray-300 hover:border-blue-400 text-lg px-8 py-4"
            >
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Powerful AI Features for Researchers
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Everything you need to accelerate your research and writing process with cutting-edge AI technology.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardHeader className="text-center pb-4">
                <div className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-gray-600 leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:border-blue-300 transition-colors">
              <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                {benefit.icon}
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* How It Works */}
      <section className="bg-white/50 backdrop-blur-sm py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              How Verbira Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Simple, powerful, and designed for researchers at every level.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6">
                1
              </div>
              <h3 className="text-xl font-semibold mb-4">Input Your Research</h3>
              <p className="text-gray-600">
                Upload papers, add research data, or simply describe your topic. Our AI understands your research context.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6">
                2
              </div>
              <h3 className="text-xl font-semibold mb-4">AI Analysis & Generation</h3>
              <p className="text-gray-600">
                Advanced AI models analyze your content and generate professional-quality research papers, reviews, or books.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-r from-pink-600 to-red-600 w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6">
                3
              </div>
              <h3 className="text-xl font-semibold mb-4">Edit & Export</h3>
              <p className="text-gray-600">
                Refine your content with our advanced editor and export in multiple formats including PDF, DOCX, and more.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="container mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Trusted by Researchers Worldwide
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            See what researchers, professors, and students are saying about Verbira.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 py-20">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Transform Your Research?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of researchers who are already using Verbira to accelerate their work and achieve better results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              onClick={() => {
                console.log("Navigating to signup page");
                window.location.href = "/signup";
              }}
              className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4"
            >
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <span className="text-xl font-bold">Verbira</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Empowering researchers worldwide with advanced AI tools for academic writing and research analysis.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#features" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/docs" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="/api" className="hover:text-white transition-colors">API</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/about" className="hover:text-white transition-colors">About</a></li>
                <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="/careers" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/help" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="/terms" className="hover:text-white transition-colors">Terms of Service</a></li>
                <li><a href="/security" className="hover:text-white transition-colors">Security</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Verbira. All rights reserved. Built for researchers, by researchers.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
