/**
 * Export Service for Article Revision System
 * Handles exporting revised articles and response letters
 */

import { 
  RevisedArticle, 
  ResponseLetter, 
  ManualIntervention, 
  ExportConfiguration,
  ArticleChange 
} from '../types';
import { EXPORT_TEMPLATES } from '../constants';

export class ExportService {
  /**
   * Export revised article as Word document with track changes
   */
  static async exportRevisedArticle(
    revisedArticle: RevisedArticle,
    config: ExportConfiguration
  ): Promise<Blob> {
    try {
      // Generate Word document content with track changes
      const wordContent = this.generateWordContent(revisedArticle, config);
      
      // In a real implementation, this would use a library like docx or mammoth
      // to generate proper Word documents with track changes
      const blob = new Blob([wordContent], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      
      return blob;
    } catch (error) {
      console.error('Error exporting revised article:', error);
      throw new Error('Failed to export revised article');
    }
  }
  
  /**
   * Export response letter as Word document
   */
  static async exportResponseLetter(
    responseLetter: ResponseLetter,
    config: ExportConfiguration
  ): Promise<Blob> {
    try {
      const letterContent = this.generateResponseLetterContent(responseLetter);
      
      const blob = new Blob([letterContent], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      
      return blob;
    } catch (error) {
      console.error('Error exporting response letter:', error);
      throw new Error('Failed to export response letter');
    }
  }
  
  /**
   * Export manual intervention suggestions as text file
   */
  static async exportManualSuggestions(
    manualInterventions: ManualIntervention[]
  ): Promise<Blob> {
    try {
      const content = this.generateManualSuggestionsContent(manualInterventions);
      
      const blob = new Blob([content], {
        type: 'text/plain;charset=utf-8'
      });
      
      return blob;
    } catch (error) {
      console.error('Error exporting manual suggestions:', error);
      throw new Error('Failed to export manual suggestions');
    }
  }
  
  /**
   * Export all files as a ZIP archive
   */
  static async exportAllFiles(
    revisedArticle: RevisedArticle,
    responseLetter: ResponseLetter | null,
    manualInterventions: ManualIntervention[],
    config: ExportConfiguration
  ): Promise<{ files: { name: string; blob: Blob }[] }> {
    try {
      const files: { name: string; blob: Blob }[] = [];
      
      // Export revised article
      const articleBlob = await this.exportRevisedArticle(revisedArticle, config);
      files.push({
        name: `${this.sanitizeFileName(revisedArticle.originalArticle.title || 'revised-article')}.docx`,
        blob: articleBlob
      });
      
      // Export response letter if available
      if (responseLetter && config.includeResponseLetter) {
        const letterBlob = await this.exportResponseLetter(responseLetter, config);
        files.push({
          name: 'response-letter.docx',
          blob: letterBlob
        });
      }
      
      // Export manual suggestions if any
      if (manualInterventions.length > 0 && config.includeManualSuggestions) {
        const suggestionsBlob = await this.exportManualSuggestions(manualInterventions);
        files.push({
          name: 'manual-interventions.txt',
          blob: suggestionsBlob
        });
      }
      
      // Export changes summary
      const summaryBlob = this.exportChangesSummary(revisedArticle);
      files.push({
        name: 'changes-summary.txt',
        blob: summaryBlob
      });
      
      return { files };
    } catch (error) {
      console.error('Error exporting all files:', error);
      throw new Error('Failed to export files');
    }
  }
  
  /**
   * Generate Word document content with track changes
   */
  private static generateWordContent(
    revisedArticle: RevisedArticle,
    config: ExportConfiguration
  ): string {
    let content = '';
    
    // Add title
    if (revisedArticle.originalArticle.title) {
      content += `${revisedArticle.originalArticle.title}\n\n`;
    }
    
    // Add each section with changes highlighted
    Object.entries(revisedArticle.sectionRevisions).forEach(([section, revision]) => {
      content += `${section.toUpperCase()}\n\n`;
      
      if (config.highlightChanges && revision.changes.length > 0) {
        // Add content with changes marked
        content += this.markChangesInText(revision.revisedContent, revision.changes);
      } else {
        content += revision.revisedContent;
      }
      
      content += '\n\n';
      
      // Add comments if requested
      if (config.includeComments && revision.changes.length > 0) {
        content += '--- CHANGES IN THIS SECTION ---\n';
        revision.changes.forEach((change, index) => {
          content += `${index + 1}. ${change.changeType.toUpperCase()}: ${change.reason}\n`;
          if (change.originalText) {
            content += `   Original: "${change.originalText}"\n`;
          }
          content += `   Revised: "${change.revisedText}"\n\n`;
        });
        content += '\n';
      }
    });
    
    return content;
  }
  
  /**
   * Mark changes in text for highlighting
   */
  private static markChangesInText(text: string, changes: ArticleChange[]): string {
    let markedText = text;
    
    // Sort changes by position (if available) to avoid conflicts
    const sortedChanges = [...changes].sort((a, b) => {
      if (a.wordPosition && b.wordPosition) {
        return b.wordPosition - a.wordPosition; // Reverse order to avoid position shifts
      }
      return 0;
    });
    
    sortedChanges.forEach(change => {
      if (change.changeType === 'addition') {
        markedText = markedText.replace(
          change.revisedText,
          `**[ADDED: ${change.revisedText}]**`
        );
      } else if (change.changeType === 'modification') {
        markedText = markedText.replace(
          change.revisedText,
          `**[MODIFIED: ${change.revisedText}]**`
        );
      }
      // Deletions would be marked differently in a real Word document
    });
    
    return markedText;
  }
  
  /**
   * Generate response letter content
   */
  private static generateResponseLetterContent(responseLetter: ResponseLetter): string {
    let content = '';
    
    // Header
    content += EXPORT_TEMPLATES.RESPONSE_LETTER.HEADER
      .replace('{manuscriptNumber}', responseLetter.manuscriptNumber || 'TBD')
      .replace('{title}', responseLetter.title)
      .replace('{authors}', responseLetter.authors.join(', '))
      .replace('{journalName}', 'Journal Name'); // Would be configurable
    
    content += '\n\n';
    
    // Summary of changes
    content += EXPORT_TEMPLATES.RESPONSE_LETTER.SUMMARY_SECTION
      .replace('{summaryPoints}', responseLetter.summaryOfChanges.map(change => `• ${change}`).join('\n'));
    
    content += '\n\n';
    
    // Response section header
    content += EXPORT_TEMPLATES.RESPONSE_LETTER.RESPONSE_SECTION;
    content += '\n\n';
    
    // Reviewer responses
    Object.entries(responseLetter.reviewerResponses).forEach(([reviewerNum, responses]) => {
      content += `Reviewer ${reviewerNum} Comments:\n\n`;
      
      responses.forEach((response, index) => {
        content += `Comment ${index + 1}: ${response.originalComment}\n\n`;
        content += `Answer: ${response.response}\n`;
        
        if (response.changesMade.length > 0) {
          content += `Changes made: ${response.changesMade.join(', ')}\n`;
        }
        
        if (response.changeLocations.length > 0) {
          content += `Location of changes: ${response.changeLocations.join(', ')}\n`;
        }
        
        content += '\n';
      });
      
      content += '\n';
    });
    
    // Footer
    content += EXPORT_TEMPLATES.RESPONSE_LETTER.FOOTER
      .replace('{journalName}', 'Journal Name')
      .replace('{authors}', responseLetter.authors.join(', '));
    
    return content;
  }
  
  /**
   * Generate manual suggestions content
   */
  private static generateManualSuggestionsContent(
    manualInterventions: ManualIntervention[]
  ): string {
    let content = 'MANUAL INTERVENTIONS REQUIRED\n';
    content += '=====================================\n\n';
    content += `Generated on: ${new Date().toLocaleString()}\n\n`;
    
    if (manualInterventions.length === 0) {
      content += 'No manual interventions required. All reviewer comments have been automatically addressed.\n';
      return content;
    }
    
    content += `Total items requiring manual attention: ${manualInterventions.length}\n\n`;
    
    // Group by priority
    const groupedByPriority = manualInterventions.reduce((groups, intervention) => {
      if (!groups[intervention.priority]) {
        groups[intervention.priority] = [];
      }
      groups[intervention.priority].push(intervention);
      return groups;
    }, {} as Record<string, ManualIntervention[]>);
    
    ['critical', 'high', 'medium', 'low'].forEach(priority => {
      const items = groupedByPriority[priority];
      if (items && items.length > 0) {
        content += `${priority.toUpperCase()} PRIORITY (${items.length} items)\n`;
        content += '-'.repeat(50) + '\n\n';
        
        items.forEach((intervention, index) => {
          content += `${index + 1}. ${intervention.description}\n`;
          content += `   Type: ${intervention.type}\n`;
          content += `   Estimated effort: ${intervention.estimatedEffort}\n`;
          content += `   Suggested actions:\n`;
          intervention.suggestedActions.forEach(action => {
            content += `   • ${action}\n`;
          });
          content += '\n';
        });
        
        content += '\n';
      }
    });
    
    return content;
  }
  
  /**
   * Export changes summary
   */
  private static exportChangesSummary(revisedArticle: RevisedArticle): Blob {
    let content = 'ARTICLE REVISION SUMMARY\n';
    content += '========================\n\n';
    content += `Article: ${revisedArticle.originalArticle.title}\n`;
    content += `Revision Date: ${revisedArticle.metadata.revisionDate.toLocaleString()}\n`;
    content += `Total Changes: ${revisedArticle.metadata.totalChanges}\n`;
    content += `Comments Addressed: ${revisedArticle.metadata.addressedComments}\n`;
    content += `Unaddressed Comments: ${revisedArticle.metadata.unaddressedComments}\n\n`;
    
    // Changes by section
    content += 'CHANGES BY SECTION\n';
    content += '------------------\n\n';
    
    Object.entries(revisedArticle.sectionRevisions).forEach(([section, revision]) => {
      if (revision.changes.length > 0) {
        content += `${section.toUpperCase()} (${revision.changes.length} changes)\n`;
        content += `Summary: ${revision.revisionSummary}\n\n`;
        
        revision.changes.forEach((change, index) => {
          content += `  ${index + 1}. ${change.changeType}: ${change.reason}\n`;
        });
        
        content += '\n';
      }
    });
    
    return new Blob([content], { type: 'text/plain;charset=utf-8' });
  }
  
  /**
   * Sanitize filename for safe file system usage
   */
  private static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-z0-9]/gi, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .toLowerCase();
  }
  
  /**
   * Download a blob as a file
   */
  static downloadBlob(blob: Blob, fileName: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  /**
   * Download multiple files
   */
  static downloadFiles(files: { name: string; blob: Blob }[]): void {
    files.forEach(file => {
      this.downloadBlob(file.blob, file.name);
    });
  }
}
