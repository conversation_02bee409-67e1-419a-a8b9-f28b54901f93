/**
 * Test file for Gemini Flowchart Service
 * This is a simple test to verify the service initializes correctly
 */

import { GeminiFlowchartService } from './gemini-flowchart.service';

describe('GeminiFlowchartService', () => {
  let service: GeminiFlowchartService;

  beforeEach(() => {
    service = new GeminiFlowchartService();
  });

  test('should initialize service', () => {
    expect(service).toBeDefined();
  });

  test('should have available models', () => {
    const models = service.getAvailableModels();
    expect(models).toContain('gemini-2.5-flash');
    expect(models).toContain('gemini-2.5-pro');
  });

  test('should have default model', () => {
    const defaultModel = service.getDefaultModel();
    expect(defaultModel).toBe('gemini-2.5-pro');
  });

  test('should check service configuration', () => {
    const isConfigured = service.isServiceConfigured();
    // This will depend on whether VITE_GEMINI_API_KEY is set
    expect(typeof isConfigured).toBe('boolean');
  });

  test('should handle missing API key gracefully', () => {
    // Test that service doesn't crash when API key is missing
    expect(() => new GeminiFlowchartService()).not.toThrow();
  });
});
