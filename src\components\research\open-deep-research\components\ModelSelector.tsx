/**
 * Model Selector Component
 * Dropdown for selecting AI models across different platforms
 */

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ModelVariant } from '../types';
import { AI_MODELS, DEFAULT_MODEL } from '../constants';

interface ModelSelectorProps {
  value: ModelVariant;
  onValueChange: (value: ModelVariant) => void;
  disabled?: boolean;
  className?: string;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onValueChange,
  disabled = false,
  className = '',
}) => {
  // Flatten models from all platforms
  const allModels = Object.entries(AI_MODELS).flatMap(([platformKey, platform]) => {
    if (!platform.enabled) return [];
    
    return Object.entries(platform.models).map(([modelKey, model]) => ({
      value: model.value,
      label: model.label,
      platform: platformKey,
      enabled: model.enabled,
    }));
  });

  // Group models by platform for display
  const modelsByPlatform = Object.entries(AI_MODELS).reduce((acc, [platformKey, platform]) => {
    if (!platform.enabled) return acc;
    
    const platformModels = Object.entries(platform.models).map(([modelKey, model]) => ({
      value: model.value,
      label: model.label,
      enabled: model.enabled,
    }));
    
    if (platformModels.length > 0) {
      acc[platformKey] = {
        name: getPlatformDisplayName(platformKey),
        models: platformModels,
      };
    }
    
    return acc;
  }, {} as Record<string, { name: string; models: Array<{ value: ModelVariant; label: string; enabled: boolean }> }>);

  const selectedModel = allModels.find(model => model.value === value);

  return (
    <Select 
      value={value} 
      onValueChange={onValueChange} 
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select AI Model">
          {selectedModel && (
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={`text-xs ${getModelProviderColor(selectedModel.value)}`}
              >
                {selectedModel.value.split('/')[0]}
              </Badge>
              <span className={selectedModel.enabled ? '' : 'text-gray-400'}>
                {selectedModel.label}
              </span>
              {!selectedModel.enabled && (
                <span className="text-xs text-gray-400">(Disabled)</span>
              )}
            </div>
          )}
        </SelectValue>
      </SelectTrigger>
      
      <SelectContent>
        {Object.entries(modelsByPlatform).map(([platformKey, platform]) => (
          <div key={platformKey}>
            {/* Platform Header */}
            <div className="px-2 py-1.5 text-sm font-semibold text-gray-700 bg-gray-50">
              {platform.name}
            </div>
            
            {/* Platform Models */}
            {platform.models.map((model) => (
              <SelectItem
                key={model.value}
                value={model.value}
                disabled={!model.enabled}
                className={`pl-4 ${!model.enabled ? 'text-gray-400' : ''}`}
              >
                <div className="flex items-center justify-between w-full">
                  <span>{model.label}</span>
                  {!model.enabled && (
                    <Badge variant="outline" className="text-xs text-gray-400 ml-2">
                      Disabled
                    </Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </div>
        ))}
        
        {/* No models available message */}
        {Object.keys(modelsByPlatform).length === 0 && (
          <div className="px-2 py-4 text-sm text-gray-500 text-center">
            No AI models available. Please check your configuration.
          </div>
        )}
      </SelectContent>
    </Select>
  );
};

// Helper functions
function getPlatformDisplayName(platformKey: string): string {
  const displayNames: Record<string, string> = {
    openrouter: 'OpenRouter',
  };

  return displayNames[platformKey] || platformKey.charAt(0).toUpperCase() + platformKey.slice(1);
}

function getPlatformColor(platformKey: string): string {
  const colors: Record<string, string> = {
    openrouter: 'bg-indigo-100 text-indigo-800',
  };

  return colors[platformKey] || 'bg-gray-100 text-gray-800';
}

function getModelProviderColor(modelValue: string): string {
  if (modelValue.includes('anthropic')) {
    return 'bg-purple-100 text-purple-800';
  } else if (modelValue.includes('openai')) {
    return 'bg-green-100 text-green-800';
  } else if (modelValue.includes('google')) {
    return 'bg-blue-100 text-blue-800';
  } else if (modelValue.includes('meta-llama')) {
    return 'bg-orange-100 text-orange-800';
  } else if (modelValue.includes('deepseek')) {
    return 'bg-red-100 text-red-800';
  } else if (modelValue.includes('qwen')) {
    return 'bg-yellow-100 text-yellow-800';
  }
  return 'bg-gray-100 text-gray-800';
}
