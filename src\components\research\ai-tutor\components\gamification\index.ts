/**
 * Gamification Components Index
 * Exports all gamification and progress tracking components
 */

export { default as ProgressTracker } from './ProgressTracker';
export { default as AchievementSystem } from './AchievementSystem';

// Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'learning' | 'engagement' | 'streak' | 'mastery' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockedAt?: Date;
  progress: number;
  maxProgress: number;
  xpReward: number;
  requirements: string[];
}

export interface ActivityItem {
  id: string;
  type: 'topic_completed' | 'streak_milestone' | 'level_up' | 'achievement_unlocked';
  title: string;
  timestamp: Date;
  xpGained?: number;
}
