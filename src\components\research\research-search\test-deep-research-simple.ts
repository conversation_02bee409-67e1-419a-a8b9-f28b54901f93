/**
 * Simple Deep Research Test
 * Test the deep research functionality with fallback mechanisms
 */

import { deepResearchService } from './services/deep-research.service';

export async function testDeepResearchSimple() {
  console.log('Testing Deep Research (Simple)...');
  
  try {
    // Test service configuration
    const isConfigured = deepResearchService.isConfigured();
    console.log('Deep Research configured:', isConfigured);
    
    if (!isConfigured) {
      console.warn('Deep research service not properly configured');
      return { success: false, error: 'Service not configured' };
    }

    // Test outline generation
    console.log('Testing outline generation...');
    const outline = await deepResearchService.generateOutline(
      'What are the main benefits and challenges of renewable energy?',
      'google/gemini-2.0-flash-001',
      {
        academicFocus: true,
        minWordCountPerSection: 500,
        maxWordCountPerSection: 800,
        citationStyle: 'apa'
      }
    );
    
    console.log('Outline generated successfully:', {
      title: outline.title,
      pointsCount: outline.points.length,
      totalPoints: outline.totalPoints
    });
    
    // Log outline points
    outline.points.forEach((point, index) => {
      console.log(`Point ${point.pointNumber}: ${point.title}`);
      console.log(`  Description: ${point.description}`);
      console.log(`  Subpoints: ${point.subpoints.length}`);
    });
    
    return { 
      success: true, 
      outline,
      message: 'Deep research outline generated successfully'
    };
    
  } catch (error: any) {
    console.error('Deep research test failed:', error);
    return { 
      success: false, 
      error: error.message,
      details: error
    };
  }
}

export async function testDeepResearchExecution() {
  console.log('Testing Deep Research Execution...');
  
  try {
    // Generate outline first
    const outline = await deepResearchService.generateOutline(
      'Benefits of artificial intelligence in healthcare',
      'google/gemini-2.0-flash-001',
      {
        academicFocus: true,
        minWordCountPerSection: 300, // Smaller for testing
        maxWordCountPerSection: 500,
        citationStyle: 'apa'
      }
    );
    
    console.log('Outline generated, starting execution...');
    
    // Create a test session with only first 2 points
    const limitedOutline = {
      ...outline,
      points: outline.points.slice(0, 2), // Only first 2 points
      totalPoints: 2
    };
    
    const session = {
      id: `test_session_${Date.now()}`,
      title: limitedOutline.title,
      originalQuery: 'Benefits of artificial intelligence in healthcare',
      outline: limitedOutline,
      currentPoint: 0,
      totalPoints: 2,
      status: 'outline_approved' as const,
      aiAssistants: [],
      completedPoints: [],
      allReferences: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'test_user',
      model: 'google/gemini-2.0-flash-001'
    };
    
    const options = {
      model: 'google/gemini-2.0-flash-001',
      maxResultsPerSection: 3, // Small for testing
      searchDepth: 'basic' as const,
      includeImages: false,
      academicFocus: true,
      minWordCountPerSection: 300,
      maxWordCountPerSection: 500,
      citationStyle: 'apa' as const,
      includeAbstract: true,
      includeConclusion: true,
      autoApproveOutline: false
    };
    
    const completedSession = await deepResearchService.executeDeepResearch(
      session,
      options,
      (progress) => {
        console.log(`Progress: ${progress.assistantName} working on ${progress.pointTitle} - ${progress.status} (${progress.progress}%)`);
        if (progress.currentSubpoint) {
          console.log(`  Current subpoint: ${progress.currentSubpoint}`);
        }
      }
    );
    
    console.log('Deep research completed successfully:', {
      status: completedSession.status,
      completedPoints: completedSession.completedPoints.length,
      aiAssistants: completedSession.aiAssistants.length,
      references: completedSession.allReferences.length,
      finalReportLength: completedSession.finalReport?.length || 0
    });
    
    return {
      success: true,
      session: completedSession,
      message: 'Deep research execution completed successfully'
    };
    
  } catch (error: any) {
    console.error('Deep research execution test failed:', error);
    return {
      success: false,
      error: error.message,
      details: error
    };
  }
}

// Export test functions for use in console
if (typeof window !== 'undefined') {
  (window as any).testDeepResearchSimple = {
    testOutline: testDeepResearchSimple,
    testExecution: testDeepResearchExecution
  };
}
