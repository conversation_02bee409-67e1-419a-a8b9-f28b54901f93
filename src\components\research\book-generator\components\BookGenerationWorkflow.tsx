import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { 
  BookOpen, 
  CheckCircle, 
  Clock, 
  Edit, 
  RefreshCw, 
  ArrowRight, 
  ArrowLeft,
  FileText,
  Loader2,
  AlertCircle,
  ThumbsUp,
  MessageSquare
} from "lucide-react";
import {
  BookMetadata,
  UserChapter,
  GeneratedChapter,
  GeneratedOutline,
  BookContext,
  Citation
} from '../types';
import type { BookGenerationWorkflow } from '../types';
import { useBookContextStore } from '../stores/book-context.store';
import bookAIService from '../services/book-ai.service';
import { enhancedOutlineService } from '../services/enhanced-outline.service';
import { longBookService, LongBookStrategy, BookProgressState } from '../services/long-book.service';
import { errorDetectionService, ErrorDetectionResult } from '../services/error-detection.service';
import { getOptimalModel, AI_MODELS } from '../constants';
import { extractCitationsFromText } from '../../paper-generator/citation-extraction.enhanced';
import { ChapterReviewCard } from './ChapterReviewCard';
import { OutlineEditor } from './OutlineEditor';
import { AIModelSelector } from './AIModelSelector';
import { toast } from 'sonner';

interface BookGenerationWorkflowProps {
  bookMetadata: BookMetadata;
  userChapters: UserChapter[];
  selectedModel: string; // Keep for backward compatibility
  onComplete: (chapters: GeneratedChapter[], citations: Citation[]) => void;
  onBack: () => void;
  autoGenerate?: boolean;
}

export const BookGenerationWorkflow: React.FC<BookGenerationWorkflowProps> = ({
  bookMetadata,
  userChapters,
  selectedModel,
  onComplete,
  onBack,
  autoGenerate = false
}) => {
  // Workflow state
  const [workflow, setWorkflow] = useState<BookGenerationWorkflow>({
    currentStep: 'outline-generation',
    currentChapterIndex: 0,
    totalChapters: userChapters.length,
    outlineGenerated: false,
    chaptersCompleted: 0
  });

  // Generated content
  const [generatedOutlines, setGeneratedOutlines] = useState<GeneratedOutline[]>([]);
  const [generatedChapters, setGeneratedChapters] = useState<GeneratedChapter[]>([]);
  const [allCitations, setAllCitations] = useState<Citation[]>([]);

  // UI state
  const [isGenerating, setIsGenerating] = useState(false);
  const [userFeedback, setUserFeedback] = useState('');
  const [currentChapterContent, setCurrentChapterContent] = useState('');

  // Long book support
  const [longBookStrategy, setLongBookStrategy] = useState<LongBookStrategy | null>(null);
  const [bookProgress, setBookProgress] = useState<BookProgressState | null>(null);
  const [generationStartTime, setGenerationStartTime] = useState<Date | null>(null);

  // Model selection state - use models from metadata if available
  const [outlineModel, setOutlineModel] = useState(bookMetadata.outlineModel || selectedModel || "google/gemini-2.0-flash-001");
  const [contentModel, setContentModel] = useState(bookMetadata.contentModel || selectedModel || "moonshotai/kimi-k2:free");

  // Auto-generation state to prevent multiple starts
  const [autoGenerationStarted, setAutoGenerationStarted] = useState(false);

  // Ref to store the latest chapters state for reliable access in callbacks
  const latestChaptersRef = useRef(generatedChapters);

  // Keep the ref updated with the latest chapters state
  useEffect(() => {
    console.log('🔄 Updating latestChaptersRef with:', generatedChapters.map(ch => ({
      id: ch.id,
      title: ch.title,
      hasContent: !!ch.content,
      contentLength: ch.content?.length || 0
    })));
    latestChaptersRef.current = generatedChapters;
  }, [generatedChapters]);

  // Context store
  const {
    initializeBookContext,
    getContextForChapter,
    addChapterContext,
    updateGenerationProgress,
    setChapterOutline,
    getChapterOutline
  } = useBookContextStore();

  // Validate and repair context for chapter generation
  const validateAndRepairContext = async (
    context: BookContext,
    chapterIndex: number,
    chapterId: string
  ): Promise<BookContext> => {
    try {
      // Check if we have the expected number of previous chapters
      const expectedPreviousChapters = chapterIndex;
      const actualPreviousChapters = context.previousChapters.length;

      console.log(`Context validation: Expected ${expectedPreviousChapters} previous chapters, got ${actualPreviousChapters}`);

      if (actualPreviousChapters < expectedPreviousChapters) {
        console.warn(`Missing context for chapter ${chapterId}. Attempting to repair...`);

        // Try to rebuild context from generated chapters
        const previousGeneratedChapters = generatedChapters.filter((ch, idx) =>
          idx < chapterIndex && ch.content && (ch.status === 'content-ready' || ch.status === 'completed')
        );

        console.log(`Found ${previousGeneratedChapters.length} generated chapters to add to context`);

        // Add missing chapters to context
        for (const ch of previousGeneratedChapters) {
          if (!ch.summary) {
            console.log(`Generating missing summary for ${ch.title}`);
            try {
              const { summary, keyPoints } = await bookAIService.generateChapterSummary(
                ch.title,
                ch.content || '',
                { model: contentModel }
              );

              // Update the chapter with summary
              setGeneratedChapters(prev => prev.map((chapter) =>
                chapter.id === ch.id ? { ...chapter, summary, keyPoints } : chapter
              ));

              // Add to context
              addChapterContext({
                chapterId: ch.id,
                summary,
                keyPoints: keyPoints || [],
                wordCount: ch.wordCount || (ch.content?.split(' ').length || 0),
                generatedAt: new Date()
              });
            } catch (summaryError) {
              console.error(`Failed to generate summary for ${ch.title}:`, summaryError);
              // Add basic context without summary
              addChapterContext({
                chapterId: ch.id,
                summary: `Chapter ${ch.title} - Summary generation failed`,
                keyPoints: [],
                wordCount: ch.wordCount || 0,
                generatedAt: new Date()
              });
            }
          } else {
            // Add existing summary to context
            addChapterContext({
              chapterId: ch.id,
              summary: ch.summary,
              keyPoints: [], // GeneratedChapter doesn't have keyPoints, will be extracted from summary
              wordCount: ch.wordCount || 0,
              generatedAt: new Date()
            });
          }
        }

        // Get updated context after repairs
        const repairedContext = getContextForChapter(chapterId);
        console.log(`Context repaired: Now has ${repairedContext.previousChapters.length} previous chapters`);
        return repairedContext;
      }

      return context;
    } catch (error) {
      console.error('Error validating/repairing context:', error);
      return context; // Return original context if repair fails
    }
  };

  // Initialize context and long book strategy when component mounts
  useEffect(() => {
    const chapterOutlines = userChapters.map(ch =>
      `${ch.outline.title}: ${ch.outline.description}`
    );
    initializeBookContext(bookMetadata, chapterOutlines);

    // Initialize long book strategy
    const strategy = longBookService.getOptimalStrategy(bookMetadata, userChapters);
    setLongBookStrategy(strategy);

    // Log strategy for debugging
    console.log('Long book strategy:', strategy);
    const estimatedWords = userChapters.reduce((sum, ch) => sum + (ch.outline.estimatedWordCount || 3000), 0);
    const estimatedPages = Math.ceil(estimatedWords / 300);
    console.log(`Book estimated at ${estimatedPages} pages with ${userChapters.length} chapters`);
  }, [bookMetadata, userChapters]);

  // Initialize generated chapters
  useEffect(() => {
    const initialChapters = userChapters.map((ch, index) => ({
      id: ch.id,
      title: ch.outline.title,
      description: ch.outline.description,
      status: 'pending' as const,
      icon: BookOpen,
      order: index + 1,
      userApproved: false
    }));
    setGeneratedChapters(initialChapters);
  }, [userChapters]);

  // Auto-generation mode: automatically start the process
  useEffect(() => {
    // Add more specific guards to prevent multiple triggers
    if (autoGenerate &&
        userChapters.length > 0 &&
        workflow.currentStep === 'outline-generation' &&
        !isGenerating &&
        !autoGenerationStarted &&
        generatedOutlines.length === 0) { // Only start if no outlines exist yet

      const startAutoGeneration = async () => {
        try {
          setAutoGenerationStarted(true);
          console.log('🚀 Starting auto-generation mode...');
          toast.info('Auto-generating outlines...', { duration: 3000 });

          // Auto-generate outlines first
          await generateAllOutlines();

          // Wait a moment for state to update, then check if outlines were generated successfully
          setTimeout(async () => {
            console.log('🚀 Checking outline generation status...');

            // Verify outlines were generated
            if (generatedOutlines.length === 0) {
              console.error('No outlines were generated, cannot proceed with auto-generation');
              toast.error('Outline generation failed. Cannot proceed with auto-generation.');
              setAutoGenerationStarted(false);
              return;
            }

            console.log(`✅ Found ${generatedOutlines.length} outlines, proceeding with auto-generation...`);
            console.log('🚀 Auto-approving outlines and starting generation...');

            try {
              await approveOutlinesAndStartGeneration();
            } catch (error) {
              console.error('Error during auto-approval and generation start:', error);
              toast.error('Failed to start chapter generation. Please try manual mode.');
              setAutoGenerationStarted(false);
            }
          }, 2000); // Reduced delay

        } catch (error) {
          handleAutoGenerationError(error, 'Auto-generation startup');
        }
      };

      startAutoGeneration();
    }
  }, [autoGenerate, userChapters.length, workflow.currentStep, isGenerating, autoGenerationStarted, generatedOutlines.length]);

  // Auto-approval mechanism for chapter review step
  useEffect(() => {
    if (autoGenerate &&
        workflow.currentStep === 'chapter-review' &&
        !isGenerating) {

      const chapterIndex = workflow.currentChapterIndex;
      // Use ref to get the most current chapter state
      const currentChapters = latestChaptersRef.current;
      const currentChapter = currentChapters[chapterIndex];

      // Only auto-approve if chapter has content and we haven't already set up auto-approval
      if (currentChapter?.content && currentChapter.status === 'content-ready') {
        const autoApprovalKey = `auto-approved-${chapterIndex}`;

        if (!window[autoApprovalKey]) {
          console.log(`🚀 Setting up auto-approval via useEffect for chapter ${chapterIndex + 1}`);
          window[autoApprovalKey] = true;

          // Show countdown toast
          toast.info(`Chapter ${chapterIndex + 1} ready! Auto-continuing in 3 seconds...`, {
            duration: 3000,
            action: {
              label: 'Continue Now',
              onClick: () => {
                console.log(`🚀 Manual approval via useEffect for chapter ${chapterIndex + 1}`);
                delete window[autoApprovalKey];
                approveChapterAndContinue(chapterIndex);
              }
            }
          });

          // Set timeout for auto-approval with shorter delay
          setTimeout(() => {
            if (window[autoApprovalKey]) {
              console.log(`🚀 Auto-approving chapter ${chapterIndex + 1} via useEffect after 3 seconds...`);
              delete window[autoApprovalKey];
              approveChapterAndContinue(chapterIndex);
            }
          }, 3000);
        }
      }
    }
  }, [autoGenerate, workflow.currentStep, workflow.currentChapterIndex, isGenerating]);

  // Cleanup auto-approval flags and timeouts on unmount
  useEffect(() => {
    return () => {
      // Clean up any pending auto-approval flags and timeouts
      Object.keys(window).forEach(key => {
        if (key.startsWith('auto-approved-') || key.startsWith('timeout-auto-approved-')) {
          if (key.startsWith('timeout-')) {
            clearTimeout(window[key]);
          }
          delete window[key];
        }
      });
    };
  }, []);

  // Simplified auto-generation recovery mechanism
  useEffect(() => {
    if (autoGenerate && autoGenerationStarted && !isGenerating) {
      // Simple recovery: if we have outlines but are stuck in outline-review, proceed
      if (workflow.currentStep === 'outline-review' && generatedOutlines.length > 0) {
        console.log('🔄 Auto-generation recovery: Found outlines in review step, proceeding...');
        setTimeout(async () => {
          if (workflow.currentStep === 'outline-review' && !isGenerating) {
            console.log('🔄 Auto-recovery: Proceeding with outline approval...');
            try {
              await approveOutlinesAndStartGeneration();
            } catch (error) {
              console.error('Auto-recovery failed:', error);
              toast.error('Auto-generation recovery failed. Please continue manually.');
              setAutoGenerationStarted(false);
            }
          }
        }, 1000); // Reduced delay
      }
    }
  }, [autoGenerate, autoGenerationStarted, isGenerating, workflow.currentStep, generatedOutlines.length]);

  // Enhanced error recovery helper
  const handleAutoGenerationError = (error: any, context: string, retryCount: number = 0) => {
    console.error(`Auto-generation error in ${context}:`, error);

    // Categorize error types
    const errorType = getErrorType(error);
    const maxRetries = 3;

    console.log(`Error type: ${errorType}, Retry count: ${retryCount}/${maxRetries}`);

    // Determine if this is a recoverable error and we haven't exceeded retry limit
    const isRecoverable = errorType !== 'critical' && retryCount < maxRetries;

    if (isRecoverable && autoGenerate) {
      console.log(`🔄 Attempting auto-recovery (attempt ${retryCount + 1}/${maxRetries})...`);

      const retryDelay = Math.min(3000 * Math.pow(2, retryCount), 15000); // Exponential backoff

      toast.warning(
        `${context} failed (${getErrorMessage(errorType)}). Retrying in ${retryDelay/1000}s... (${retryCount + 1}/${maxRetries})`,
        { duration: retryDelay }
      );

      // Reset generation state and try again after a delay
      setTimeout(() => {
        if (workflow.currentStep === 'outline-generation') {
          console.log('🔄 Retrying outline generation...');
          generateAllOutlines().catch(retryError =>
            handleAutoGenerationError(retryError, context, retryCount + 1)
          );
        } else if (workflow.currentStep === 'chapter-generation') {
          console.log('🔄 Retrying chapter generation...');
          generateCurrentChapter().catch(retryError =>
            handleAutoGenerationError(retryError, context, retryCount + 1)
          );
        }
      }, retryDelay);
    } else {
      // Non-recoverable error or max retries exceeded
      const errorMessage = retryCount >= maxRetries
        ? `${context} failed after ${maxRetries} attempts. Please try manual mode or check your settings.`
        : `${context} failed: ${getErrorMessage(errorType)}. Please try manual mode.`;

      toast.error(errorMessage, { duration: 10000 });

      if (autoGenerate) {
        setAutoGenerationStarted(false);
        // Reset to a safe state
        setWorkflow(prev => ({
          ...prev,
          currentStep: prev.outlineGenerated ? 'outline-review' : 'outline-generation'
        }));
      }
    }
  };

  // Error type classification
  const getErrorType = (error: any): 'network' | 'api' | 'parsing' | 'validation' | 'critical' => {
    const message = error.message?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    if (message.includes('api') || message.includes('rate limit') || message.includes('quota')) {
      return 'api';
    }
    if (message.includes('json') || message.includes('parse') || message.includes('syntax')) {
      return 'parsing';
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    }
    return 'critical';
  };

  // User-friendly error messages
  const getErrorMessage = (errorType: string): string => {
    switch (errorType) {
      case 'network': return 'Network connection issue';
      case 'api': return 'AI service temporarily unavailable';
      case 'parsing': return 'Response format issue';
      case 'validation': return 'Content validation failed';
      default: return 'Unexpected error occurred';
    }
  };

  // Step 1: Generate all chapter outlines with enhanced validation
  const generateAllOutlines = async () => {
    setIsGenerating(true);
    try {
      toast.info("Analyzing topics and generating high-quality outlines...", { duration: 8000 });

      // Use enhanced outline service for better quality and validation
      let outlines = await enhancedOutlineService.generateValidatedOutlines(
        bookMetadata,
        userChapters,
        { model: outlineModel }
      );

      // Validate that we got the correct number of outlines
      if (outlines.length !== userChapters.length) {
        console.warn(`Expected ${userChapters.length} outlines, got ${outlines.length}. Filling missing outlines.`);

        // Fill missing outlines with fallback
        for (let i = outlines.length; i < userChapters.length; i++) {
          outlines.push({
            id: `outline-${userChapters[i].id}`,
            chapterId: userChapters[i].id,
            title: userChapters[i].outline.title || `Chapter ${i + 1}`,
            description: userChapters[i].outline.description || `Content for Chapter ${i + 1}`,
            sections: [],
            estimatedWordCount: 5000,
            keyPoints: [],
            status: 'ready' as const
          });
        }
      }

      setGeneratedOutlines(outlines);

      // Store outlines in context store
      outlines.forEach(outline => {
        setChapterOutline(outline.chapterId, outline);
      });

      setWorkflow(prev => ({
        ...prev,
        currentStep: 'outline-review',
        outlineGenerated: true
      }));

      // Update chapter statuses
      setGeneratedChapters(prev => prev.map((ch, index) => ({
        ...ch,
        status: 'outline-ready',
        outline: outlines[index]
      })));

      // Enhanced error detection for outlines
      const outlineErrors: { index: number; errors: ErrorDetectionResult }[] = [];

      for (let i = 0; i < outlines.length; i++) {
        const outline = outlines[i];
        const userChapter = userChapters[i];

        if (outline.status !== 'needs-revision') {
          const errorResult = errorDetectionService.detectFakeOutline(outline, userChapter, bookMetadata);

          if (errorResult.hasErrors && errorResult.severity === 'critical') {
            outlineErrors.push({ index: i, errors: errorResult });
            console.warn(`Outline ${i + 1} failed quality check:`, errorResult);
          }
        }
      }

      // Handle detected errors
      if (outlineErrors.length > 0) {
        const criticalErrors = outlineErrors.filter(e => e.errors.severity === 'critical');
        const highErrors = outlineErrors.filter(e => e.errors.severity === 'high');

        if (criticalErrors.length > 0) {
          toast.error(`${criticalErrors.length} outline(s) detected as fake/generic. Please provide more specific chapter details and regenerate.`, { duration: 15000 });
        } else if (highErrors.length > 0) {
          toast.warning(`${highErrors.length} outline(s) need improvement. Consider adding more topic-specific details.`, { duration: 10000 });
        }
      }

      // Check for any outlines that need revision
      const needsRevision = outlines.filter(outline => outline.status === 'needs-revision');
      if (needsRevision.length > 0) {
        toast.warning(`${needsRevision.length} outline(s) need more specific topic details. Please review and regenerate.`, { duration: 10000 });
      } else if (outlineErrors.length === 0) {
        toast.success("High-quality, topic-specific outlines generated successfully!");
      }
    } catch (error: any) {
      console.error('Enhanced outline generation error:', error);

      // Always try fallback generation to ensure we have outlines for auto-generation
      try {
        console.log('Attempting fallback outline generation...');
        const errorType = getErrorType(error);
        toast.info(
          `Primary outline generation failed (${getErrorMessage(errorType)}). Trying fallback method...`,
          { duration: 5000 }
        );

        const fallbackOutlines = await bookAIService.generateAllChapterOutlines(
          bookMetadata,
          userChapters,
          { model: outlineModel }
        );

        console.log(`Fallback generated ${fallbackOutlines.length} outlines`);

        // Ensure we have the correct number of outlines
        if (fallbackOutlines.length !== userChapters.length) {
          console.warn(`Fallback expected ${userChapters.length} outlines, got ${fallbackOutlines.length}. Creating missing outlines.`);

          // Fill missing outlines
          for (let i = fallbackOutlines.length; i < userChapters.length; i++) {
            fallbackOutlines.push({
              id: `outline-${userChapters[i].id}`,
              chapterId: userChapters[i].id,
              title: userChapters[i].outline.title || `Chapter ${i + 1}`,
              description: userChapters[i].outline.description || `Content for Chapter ${i + 1}`,
              sections: [
                {
                  id: 'section-1',
                  title: 'Introduction',
                  description: 'Chapter introduction',
                  level: 1,
                  order: 1,
                  estimatedWordCount: 500,
                  keyPoints: []
                },
                {
                  id: 'section-2',
                  title: 'Main Content',
                  description: 'Main chapter content',
                  level: 1,
                  order: 2,
                  estimatedWordCount: 2000,
                  keyPoints: []
                },
                {
                  id: 'section-3',
                  title: 'Conclusion',
                  description: 'Chapter conclusion',
                  level: 1,
                  order: 3,
                  estimatedWordCount: 500,
                  keyPoints: []
                }
              ],
              estimatedWordCount: 3000,
              keyPoints: [],
              status: 'ready' as const
            });
          }
        }

        setGeneratedOutlines(fallbackOutlines);

        // Store outlines in context store
        fallbackOutlines.forEach(outline => {
          setChapterOutline(outline.chapterId, outline);
        });

        setWorkflow(prev => ({
          ...prev,
          currentStep: 'outline-review',
          outlineGenerated: true
        }));

        // Update chapter statuses
        setGeneratedChapters(prev => prev.map((ch, index) => ({
          ...ch,
          status: 'outline-ready',
          outline: fallbackOutlines[index]
        })));

        if (error.message.includes('Topic quality is too low')) {
          toast.warning("Generated basic outlines due to low topic quality. For better results, add more specific chapter details.", { duration: 8000 });
        } else {
          toast.warning("Generated fallback outlines. For better quality, please add more specific chapter details.", { duration: 5000 });
        }

      } catch (fallbackError) {
        console.error('Fallback outline generation also failed:', fallbackError);

        // Create minimal outlines as last resort to prevent auto-generation from failing
        console.log('Creating minimal outlines as last resort...');
        const minimalOutlines = userChapters.map((chapter, index) => ({
          id: `outline-${chapter.id}`,
          chapterId: chapter.id,
          title: chapter.outline.title || `Chapter ${index + 1}`,
          description: chapter.outline.description || `This chapter covers ${chapter.outline.title || 'the specified topic'}.`,
          sections: [
            {
              id: 'section-1',
              title: 'Introduction',
              description: 'Introduction to the chapter topic',
              level: 1,
              order: 1,
              estimatedWordCount: 1000,
              keyPoints: ['Overview', 'Key concepts']
            },
            {
              id: 'section-2',
              title: 'Main Discussion',
              description: 'Detailed exploration of the topic',
              level: 1,
              order: 2,
              estimatedWordCount: 2000,
              keyPoints: ['Detailed analysis', 'Examples']
            }
          ],
          estimatedWordCount: chapter.outline.estimatedWordCount || 3000,
          keyPoints: chapter.outline.keyPoints || ['Key concepts', 'Important points'],
          status: 'ready' as const
        }));

        setGeneratedOutlines(minimalOutlines);

        // Store outlines in context store
        minimalOutlines.forEach(outline => {
          setChapterOutline(outline.chapterId, outline);
        });

        setWorkflow(prev => ({
          ...prev,
          currentStep: 'outline-review',
          outlineGenerated: true
        }));

        // Update chapter statuses
        setGeneratedChapters(prev => prev.map((ch, index) => ({
          ...ch,
          status: 'outline-ready',
          outline: minimalOutlines[index]
        })));

        toast.warning("Created minimal outlines to continue generation. Quality may be reduced.", { duration: 8000 });
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Step 2: Regenerate specific outline
  const regenerateOutline = async (chapterIndex: number) => {
    setIsGenerating(true);
    try {
      const userChapter = userChapters[chapterIndex];
      const previousChapters = generatedOutlines
        .slice(0, chapterIndex)
        .map(outline => `${outline.title}: ${outline.description}`);

      toast.info(`Regenerating outline for ${userChapter.outline.title}...`);

      const newOutline = await bookAIService.generateChapterOutline(
        bookMetadata,
        userChapter,
        previousChapters,
        { model: outlineModel }
      );

      // Update the specific outline
      setGeneratedOutlines(prev => prev.map((outline, index) =>
        index === chapterIndex ? newOutline : outline
      ));

      // Update chapter status
      setGeneratedChapters(prev => prev.map((ch, index) =>
        index === chapterIndex ? { ...ch, outline: newOutline, status: 'outline-ready' } : ch
      ));

      toast.success("Outline regenerated successfully!");
    } catch (error: any) {
      console.error('Outline regeneration error:', error);
      toast.error("Failed to regenerate outline: " + (error.message || "Please try again"));
    } finally {
      setIsGenerating(false);
    }
  };

  // Save edited outline
  const saveOutline = (chapterIndex: number, updatedOutline: GeneratedOutline) => {
    setGeneratedOutlines(prev => prev.map((outline, index) =>
      index === chapterIndex ? updatedOutline : outline
    ));

    // Update context store
    setChapterOutline(updatedOutline.chapterId, updatedOutline);

    // Update chapter status
    setGeneratedChapters(prev => prev.map((ch, index) =>
      index === chapterIndex ? { ...ch, outline: updatedOutline } : ch
    ));

    toast.success("Outline saved successfully!");
  };

  // Step 3: Approve outlines and start chapter generation
  const approveOutlinesAndStartGeneration = async () => {
    console.log('🚀 Starting outline approval and chapter generation...');

    // Validate that we have outlines
    if (generatedOutlines.length === 0) {
      console.error('No outlines available for approval');
      toast.error('No outlines available. Please generate outlines first.');
      return;
    }

    if (generatedOutlines.length !== userChapters.length) {
      console.error(`Outline count mismatch: ${generatedOutlines.length} outlines vs ${userChapters.length} chapters`);
      toast.error('Outline count mismatch. Please regenerate outlines.');
      return;
    }

    console.log(`✅ Approving ${generatedOutlines.length} outlines and starting generation...`);

    // Initialize generation start time for long book progress tracking
    setGenerationStartTime(new Date());

    setWorkflow(prev => ({
      ...prev,
      currentStep: 'chapter-generation',
      currentChapterIndex: 0
    }));

    // Mark all outlines as approved
    setGeneratedOutlines(prev => prev.map(outline => ({
      ...outline,
      status: 'approved'
    })));

    // Show long book information if applicable
    if (longBookStrategy && userChapters.length > 10) {
      const estimatedWords = userChapters.reduce((sum, ch) => sum + (ch.outline.estimatedWordCount || 3000), 0);
      const estimatedPages = Math.ceil(estimatedWords / 300);
      toast.info(`Starting long book generation: ${estimatedPages} pages, ${userChapters.length} chapters. Processing with optimized context management.`, { duration: 8000 });
    }

    // Wait a moment for state to update before starting chapter generation
    setTimeout(() => {
      console.log('🚀 Starting first chapter generation...');
      generateCurrentChapter();
    }, 500);
  };

  // Step 4: Generate current chapter content
  const generateCurrentChapter = async (forceChapterIndex?: number) => {
    const chapterIndex = forceChapterIndex !== undefined ? forceChapterIndex : workflow.currentChapterIndex;
    console.log(`=== GENERATING CHAPTER ${chapterIndex + 1} ===`);
    console.log(`Chapter index: ${chapterIndex}`);
    console.log(`Force index provided: ${forceChapterIndex}`);
    console.log(`Workflow current index: ${workflow.currentChapterIndex}`);
    console.log(`Total chapters: ${userChapters.length}`);
    console.log(`Generated outlines count: ${generatedOutlines.length}`);
    console.log(`Generated chapters count: ${generatedChapters.length}`);
    console.log(`Auto-generate mode: ${autoGenerate}`);
    console.log(`Is generating: ${isGenerating}`);

    if (chapterIndex >= userChapters.length) {
      console.error(`Invalid chapter index: ${chapterIndex}, max: ${userChapters.length-1}`);
      toast.error("Invalid chapter index");
      return;
    }

    const userChapter = userChapters[chapterIndex];
    const outline = generatedOutlines[chapterIndex];

    console.log(`User chapter:`, userChapter);
    console.log(`Outline:`, outline);
    console.log(`Generated outlines array length:`, generatedOutlines.length);
    console.log(`User chapters array length:`, userChapters.length);
    console.log(`Current chapter index:`, chapterIndex);

    if (!outline) {
      console.error(`No outline found for chapter index ${chapterIndex}`);
      console.error(`Available outlines:`, generatedOutlines.map((o, i) => `${i}: ${o?.title || 'undefined'}`));
      console.error(`Workflow state:`, workflow);

      // Try to get outline from context store as fallback
      const contextOutline = getChapterOutline(userChapter.id);
      if (contextOutline) {
        console.log('Found outline in context store, using as fallback');
        // Update the generated outlines array
        const updatedOutlines = [...generatedOutlines];
        updatedOutlines[chapterIndex] = contextOutline;
        setGeneratedOutlines(updatedOutlines);

        // Continue with the found outline
        console.log('Continuing with context outline:', contextOutline);
      } else {
        toast.error("No outline found for this chapter. Please regenerate outlines first.");
        return;
      }
    }
    
    setIsGenerating(true);
    
    try {
      toast.info(`Generating Chapter ${chapterIndex + 1}: ${outline.title}...`, {
        duration: 10000
      });
      
      // Update chapter status
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'content-generating' } : ch
      ));
      
      updateGenerationProgress(userChapter.id, 10);
      
      // Update progress for long books
      if (longBookStrategy && generationStartTime) {
        const progress = longBookService.calculateProgress(
          userChapters.length,
          chapterIndex,
          generationStartTime,
          longBookStrategy
        );
        setBookProgress(progress);
      }

      // Get optimized context for long books
      console.log(`Getting context for chapter ID: ${userChapter.id}`);
      let context;

      if (longBookStrategy && generatedChapters.length > 0) {
        console.log('Using long book context optimization');
        context = await longBookService.optimizeContextForLongBook(
          bookMetadata,
          generatedChapters.filter(ch => ch.content), // Only chapters with content
          chapterIndex,
          longBookStrategy
        );
      } else {
        context = getContextForChapter(userChapter.id);
        // Validate and repair context if needed
        context = await validateAndRepairContext(context, chapterIndex, userChapter.id);
      }

      // Log the context to help with debugging
      console.log(`Previous chapters context: ${context.previousChapters.length} chapters`);
      
      // Verify we have context from previous chapters if this isn't the first chapter
      if (chapterIndex > 0 && context.previousChapters.length === 0) {
        console.warn("Missing context from previous chapters - attempting to rebuild");
        
        // Try to rebuild context from generated chapters
        const previousGeneratedChapters = generatedChapters
          .filter((ch, idx) => idx < chapterIndex && ch.content && ch.title);
        
        if (previousGeneratedChapters.length > 0) {
          console.log(`Rebuilding context from ${previousGeneratedChapters.length} previous chapters`);
          
          // Add each previous chapter to context
          for (const ch of previousGeneratedChapters) {
            if (!ch.summary) {
              console.log(`Generating missing summary for ${ch.title}`);
              const { summary, keyPoints } = await bookAIService.generateChapterSummary(
                ch.title,
                ch.content || '',
                { model: contentModel }
              );
              
              // Add to context
              addChapterContext({
                chapterId: ch.id,
                summary,
                keyPoints: keyPoints || [],
                wordCount: ch.wordCount || (ch.content?.split(' ').length || 0),
                generatedAt: new Date()
              });
            } else {
              // Add existing summary to context
              addChapterContext({
                chapterId: ch.id,
                summary: ch.summary,
                keyPoints: [],
                wordCount: ch.wordCount || 0,
                generatedAt: new Date()
              });
            }
          }
          
          // Get updated context
          context = getContextForChapter(userChapter.id);
          console.log(`Rebuilt context now has ${context.previousChapters.length} chapters`);
        }
      }
      
      // Compile user content
      const userContent = userChapter.items
        .filter(item => item.content.trim())
        .map(item => `${item.type === 'text' ? 'Text' : 'Figure'}: ${item.content}`)
        .join('\n\n');
      
      updateGenerationProgress(userChapter.id, 30);
      
      // Create enhanced prompt with outline
      const enhancedPrompt = `Write Chapter ${chapterIndex + 1}: "${outline.title}" for the book "${bookMetadata.title}".

Chapter Outline:
${outline.sections.map(section => 
  `${section.order}. ${section.title} (${section.estimatedWordCount} words)
     Description: ${section.description}
     Key Points: ${section.keyPoints.join(', ')}`
).join('\n')}

${context && context.previousChapters.length > 0 ? `
Previous Chapters Context:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
   Summary: ${ch.summary}
   Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}` : ''}

${userContent ? `
User Provided Content:
${userContent}` : ''}

Requirements:
- Follow the detailed outline structure exactly
- Write substantial, book-quality content (${outline.estimatedWordCount} words minimum)
- Use hierarchical headings (## for main sections, ### for subsections)
- Maintain consistency with previous chapters and overall book narrative
- Include relevant examples, case studies, or illustrations where appropriate
- Use a ${bookMetadata.tone} tone throughout
- Include proper citations where academic references would be appropriate
- Target audience: ${bookMetadata.targetAudience}

Write only the main chapter content without the chapter title heading.`;

      updateGenerationProgress(userChapter.id, 50);
      
      // Use selected content model for generation
      console.log(`Using model for content generation: ${contentModel}`);

      console.log(`Calling AI service to generate chapter content...`);
      const content = await bookAIService.generateChapter(enhancedPrompt, {
        model: contentModel,
        context
      });

      console.log(`Chapter generation completed. Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 100)}...`);
      
      updateGenerationProgress(userChapter.id, 70);
      
      // Generate chapter summary for context
      const { summary, keyPoints } = await bookAIService.generateChapterSummary(
        outline.title,
        content,
        { model: contentModel }
      );
      
      updateGenerationProgress(userChapter.id, 85);
      
      // Error detection for generated content
      const generatedChapter = {
        id: userChapter.id,
        title: outline.title,
        content,
        summary,
        wordCount: content.split(' ').length,
        status: 'content-ready' as const,
        order: chapterIndex + 1,
        description: outline.description
      };

      const contentErrors = errorDetectionService.detectGenericContent(
        generatedChapter,
        outline,
        bookMetadata
      );

      if (contentErrors.hasErrors && contentErrors.severity === 'critical') {
        console.error(`Chapter ${chapterIndex + 1} failed quality check:`, contentErrors);
        toast.error(`Chapter ${chapterIndex + 1} quality issues detected: ${contentErrors.issues.join(', ')}. Consider regenerating.`, { duration: 10000 });
      } else if (contentErrors.hasErrors && contentErrors.severity === 'high') {
        console.warn(`Chapter ${chapterIndex + 1} has quality concerns:`, contentErrors);
        toast.warning(`Chapter ${chapterIndex + 1} could be improved: ${contentErrors.suggestions.slice(0, 2).join(', ')}.`, { duration: 8000 });
      }

      // Context mismatch detection for non-first chapters
      if (chapterIndex > 0) {
        const previousChapters = generatedChapters.slice(0, chapterIndex).filter(ch => ch.content);
        const contextErrors = errorDetectionService.detectContextMismatch(
          generatedChapter,
          previousChapters,
          bookMetadata
        );

        if (contextErrors.hasErrors && contextErrors.severity === 'medium') {
          console.warn(`Chapter ${chapterIndex + 1} context issues:`, contextErrors);
          toast.warning(`Chapter ${chapterIndex + 1} may have continuity issues with previous chapters.`, { duration: 6000 });
        }
      }

      // Extract citations
      const extractedInfo = extractCitationsFromText(content, userChapter.id);
      if (extractedInfo.citations.length > 0) {
        setAllCitations(prev => [...prev, ...extractedInfo.citations]);
      }

      const wordCount = content.split(' ').length;
      
      // Update chapter with generated content
      console.log(`Updating chapter ${chapterIndex} with content length: ${content.length}`);

      // Use functional update to ensure we have the latest state
      setGeneratedChapters(prev => {
        console.log(`Previous chapters state:`, prev.map((ch, i) => `${i}: ${ch.title} (${ch.status})`));

        const updated = prev.map((ch, index) => {
          if (index === chapterIndex) {
            const updatedChapter = {
              ...ch,
              status: 'content-ready' as const,
              content,
              summary,
              wordCount,
              citations: extractedInfo.matches,
              hasContent: true // Explicitly set this flag
            };
            console.log(`Updating chapter ${index}:`, {
              title: updatedChapter.title,
              status: updatedChapter.status,
              hasContent: !!updatedChapter.content,
              contentLength: updatedChapter.content?.length || 0
            });
            return updatedChapter;
          }
          return ch;
        });

        console.log(`Updated chapters state:`, updated.map((ch, i) => `${i}: ${ch.title} (${ch.status}) - content: ${!!ch.content}`));

        // Immediately update the ref to ensure synchronization
        latestChaptersRef.current = updated;

        return updated;
      });
      
      // Memory management for long books
      if (longBookStrategy && generatedChapters.length > 10) {
        console.log('Applying memory management for long book');
        const managedChapters = await longBookService.manageMemoryForLongBook(
          generatedChapters.filter(ch => ch.content)
        );
        setGeneratedChapters(managedChapters);
      }

      // Add to context - this is critical for passing context to the next chapter
      console.log(`Adding chapter context for: ${userChapter.id}, with summary length: ${summary.length}`);
      const chapterContext = {
        chapterId: userChapter.id,
        summary,
        keyPoints,
        wordCount,
        generatedAt: new Date()
      };

      try {
        // Add chapter context for the next chapter to use
        addChapterContext(chapterContext);

        // Verify context was added correctly
        const contextInfo = getContextForChapter(userChapter.id);
        console.log(`Context after adding chapter: ${contextInfo.previousChapters.length} previous chapters available`);
        console.log(`Previous chapters in context:`, contextInfo.previousChapters.map(ch => ch.chapterId));
      } catch (error) {
        console.error('Error adding chapter context:', error);
        // Continue anyway - we'll try to recover during chapter approval
      }

      // Generate summary for long books
      if (longBookStrategy && longBookStrategy.summaryFrequency > 0 &&
          (chapterIndex + 1) % longBookStrategy.summaryFrequency === 0) {
        console.log(`Generating contextual summary after chapter ${chapterIndex + 1}`);
        toast.info("Generating contextual summary for better continuity...", { duration: 3000 });
        try {
          const completedChapters = generatedChapters.slice(0, chapterIndex + 1).filter(ch => ch.content);
          const bookSummary = await longBookService.generateBookSummary(bookMetadata, completedChapters);
          console.log(`Generated book summary:`, bookSummary.substring(0, 200) + '...');
        } catch (error) {
          console.error('Failed to generate book summary:', error);
        }
      }
      
      console.log(`Setting current chapter content with length: ${content.length}`);
      setCurrentChapterContent(content);
      console.log(`Moving to chapter-review step for chapter ${chapterIndex + 1}`);

      // Ensure state synchronization before moving to review step
      setTimeout(() => {
        // Use ref to get the most current state
        const currentChapters = latestChaptersRef.current;
        const updatedChapter = currentChapters[chapterIndex];

        console.log(`=== VERIFICATION AFTER UPDATE ===`);
        console.log(`Chapter ${chapterIndex + 1} after update:`, {
          hasContent: !!updatedChapter?.content,
          contentLength: updatedChapter?.content?.length || 0,
          status: updatedChapter?.status
        });

        // Always move to review step since we've updated the state
        console.log(`Moving to chapter-review step`);
        setWorkflow(prev => ({ ...prev, currentStep: 'chapter-review' }));
      }, 50); // Reduced delay since we're now updating ref immediately

      updateGenerationProgress(userChapter.id, 100);

      // Show how many chapters are left
      const remainingChapters = userChapters.length - (chapterIndex + 1);
      if (remainingChapters > 0) {
        toast.success(
          `Chapter ${chapterIndex + 1} generated successfully! ${remainingChapters} chapter${remainingChapters !== 1 ? 's' : ''} remaining.`,
          { duration: 5000 }
        );
      } else {
        toast.success(`Chapter ${chapterIndex + 1} generated successfully! This is the final chapter.`, { duration: 5000 });
      }

      // Auto-approve and continue after 5 seconds (always enabled)
      console.log('🚀 Setting up auto-approval for chapter after 5 seconds...');
      console.log('🚀 Current workflow state:', workflow);
      console.log('🚀 Chapter index:', chapterIndex);

      // Prevent multiple auto-approvals for the same chapter
      const autoApprovalKey = `auto-approved-${chapterIndex}`;
      if (!window[autoApprovalKey]) {
        window[autoApprovalKey] = true;
        console.log(`🚀 Auto-approval flag set for chapter ${chapterIndex + 1}`);

        // Show countdown toast
        toast.info(`Chapter ${chapterIndex + 1} generated! Auto-continuing in 5 seconds...`, {
          duration: 5000,
          action: {
            label: 'Continue Now',
            onClick: () => {
              console.log(`🚀 Manual approval clicked for chapter ${chapterIndex + 1}`);
              console.log(`🚀 Current workflow index: ${workflow.currentChapterIndex}, target index: ${chapterIndex}`);
              console.log(`🚀 Manual approval triggered for chapter ${chapterIndex + 1}`);
              // Clear the auto-approval to prevent double execution
              delete window[autoApprovalKey];
              approveChapterAndContinue(chapterIndex);
            }
          }
        });

        // Use a more robust timeout approach that doesn't rely on stale state
        const timeoutId = setTimeout(() => {
          console.log(`🚀 Auto-approval timeout triggered for chapter ${chapterIndex + 1}`);
          console.log(`🚀 Auto-approval flag exists: ${!!window[autoApprovalKey]}`);

          // Check if the flag still exists (meaning we haven't manually approved yet)
          if (window[autoApprovalKey]) {
            console.log(`🚀 Auto-approving chapter ${chapterIndex + 1} after 5 seconds...`);
            delete window[autoApprovalKey]; // Clear flag before approval

            // Call approval directly with the captured chapterIndex
            try {
              approveChapterAndContinue(chapterIndex);
              console.log(`🚀 Auto-approval completed for chapter ${chapterIndex + 1}`);
            } catch (error) {
              console.error(`🚀 Auto-approval failed for chapter ${chapterIndex + 1}:`, error);
            }
          } else {
            console.log(`🚀 Skipping auto-approval for chapter ${chapterIndex + 1} - already processed manually`);
          }
        }, 5000); // 5 second delay to show the generated chapter

        // Store timeout ID for potential cleanup
        window[`timeout-${autoApprovalKey}`] = timeoutId;
        console.log(`🚀 Auto-approval timeout set with ID: ${timeoutId}`);
      } else {
        console.log(`🚀 Auto-approval already triggered for chapter ${chapterIndex + 1}, skipping...`);
      }
      
    } catch (error: any) {
      console.error('Chapter generation error:', error);

      setGeneratedChapters(prev => prev.map((ch, index) =>
        index === chapterIndex ? { ...ch, status: 'error' } : ch
      ));

      // Use enhanced error handling for auto-generation mode
      if (autoGenerate) {
        handleAutoGenerationError(error, `Chapter ${chapterIndex + 1} generation`);
      } else {
        toast.error("Failed to generate chapter: " + (error.message || "Please try again"));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Step 5: Approve chapter and move to next
  const approveChapterAndContinue = async (targetChapterIndex?: number, isRetry: boolean = false) => {
    console.log('Starting chapter approval and continuation process');
    const chapterIndex = targetChapterIndex !== undefined ? targetChapterIndex : workflow.currentChapterIndex;

    console.log(`🔍 Approval Debug Info:`);
    console.log(`🔍 Target chapter index: ${targetChapterIndex}`);
    console.log(`🔍 Workflow current index: ${workflow.currentChapterIndex}`);
    console.log(`🔍 Using chapter index: ${chapterIndex}`);
    console.log(`🔍 Generated chapters count: ${generatedChapters.length}`);
    console.log(`🔍 User chapters count: ${userChapters.length}`);

    // Prevent multiple approvals for the same chapter
    const approvalKey = `approving-${chapterIndex}`;
    if (window[approvalKey]) {
      console.log(`Chapter ${chapterIndex + 1} is already being approved, skipping...`);
      return;
    }
    window[approvalKey] = true;

    // Get the most current chapter state from the ref
    const currentChapters = latestChaptersRef.current;
    const currentChapter = currentChapters[chapterIndex];
    const userChapter = userChapters[chapterIndex];

    console.log(`🔍 Current chapter from ref:`, currentChapter);
    console.log(`🔍 Chapter has content: ${!!currentChapter?.content}`);
    console.log(`🔍 Content length: ${currentChapter?.content?.length || 0}`);
    console.log(`🔍 Chapter status: ${currentChapter?.status}`);
    console.log(`🔍 All chapters from ref:`, currentChapters.map((ch, i) => ({
      index: i,
      id: ch.id,
      title: ch.title,
      hasContent: !!ch.content,
      contentLength: ch.content?.length || 0,
      status: ch.status
    })));

    if (!currentChapter || !currentChapter.content) {
      console.error('Cannot approve chapter: missing chapter or content');
      console.error(`🔍 Chapter exists: ${!!currentChapter}`);
      console.error(`🔍 Content exists: ${!!currentChapter?.content}`);
      console.error(`🔍 All generated chapters from ref:`, currentChapters.map((ch, i) => ({
        index: i,
        id: ch.id,
        title: ch.title,
        hasContent: !!ch.content,
        contentLength: ch.content?.length || 0,
        status: ch.status
      })));

      // Only try to find alternative chapters if this is not already a retry
      if (!isRetry) {
        // Try to find a chapter with content at the current workflow index
        const workflowChapter = currentChapters[workflow.currentChapterIndex];
        if (workflowChapter && workflowChapter.content && chapterIndex !== workflow.currentChapterIndex) {
          console.log(`🔍 Found chapter with content at workflow index ${workflow.currentChapterIndex}, using that instead`);
          delete window[approvalKey]; // Clean up current flag
          approveChapterAndContinue(workflow.currentChapterIndex, true);
          return;
        }

        // Try to find any chapter with content
        const chapterWithContent = currentChapters.find((ch, i) => ch.content && ch.status === 'content-ready');
        if (chapterWithContent) {
          const foundIndex = currentChapters.indexOf(chapterWithContent);
          console.log(`🔍 Found chapter with content at index ${foundIndex}, using that instead`);
          delete window[approvalKey]; // Clean up current flag
          approveChapterAndContinue(foundIndex, true);
          return;
        }
      }

      toast.error("Cannot approve chapter: no content available");
      delete window[approvalKey]; // Clean up flag
      return;
    }

    // Check if chapter is already approved
    if (currentChapter.status === 'completed' && currentChapter.userApproved) {
      console.log(`Chapter ${chapterIndex + 1} is already approved, moving to next...`);
      delete window[approvalKey]; // Clean up flag

      const nextIndex = chapterIndex + 1;
      if (nextIndex < userChapters.length) {
        setWorkflow(prev => ({
          ...prev,
          currentChapterIndex: nextIndex,
          currentStep: 'chapter-generation' as const,
          chaptersCompleted: prev.chaptersCompleted + 1
        }));
        generateCurrentChapter(nextIndex);
      }
      return;
    }
    
    console.log(`Ensuring context for chapter ${chapterIndex + 1} (${currentChapter.id}) is captured`);
    
    try {
      // Always regenerate the summary for consistency
      console.log('Generating/refreshing chapter summary');
      toast.info('Finalizing chapter context...', { id: 'chapter-summary' });
      
      const { summary, keyPoints } = await bookAIService.generateChapterSummary(
        currentChapter.title,
        currentChapter.content,
        { model: contentModel }
      );
      
      console.log(`Generated summary for ${currentChapter.title}:`, summary.substring(0, 50) + '...');
      
      // Update chapter with summary
      setGeneratedChapters(prev => prev.map((ch, idx) => 
        idx === chapterIndex ? { ...ch, summary, keyPoints: keyPoints || [] } : ch
      ));
      
      // Forcefully add to context store
      const contextToAdd = {
        chapterId: userChapter.id,  // Use consistent ID format
        summary,
        keyPoints: keyPoints || [],
        wordCount: currentChapter.wordCount || currentChapter.content.split(' ').length,
        generatedAt: new Date()
      };
      
      console.log(`Adding context for ${userChapter.id} with summary length: ${summary.length}`);
      addChapterContext(contextToAdd);
      
      // Verify the context was added correctly
      const availableContext = getContextForChapter(userChapter.id);
      console.log(`Available context after addition:`, 
        availableContext.previousChapters.map(ch => ch.chapterId)
      );
      
      toast.success('Chapter context prepared successfully', { id: 'chapter-summary' });
    } catch (error) {
      console.error('Error ensuring chapter context:', error);
      toast.error('Had trouble preparing chapter context, but will try to continue');
      // Continue anyway - we don't want to block the process
    }
    
    // Mark current chapter as approved
    setGeneratedChapters(prev => prev.map((ch, index) => 
      index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
    ));
    
    const nextIndex = chapterIndex + 1;
    
    if (nextIndex < userChapters.length) {
      // Log context state before moving to next chapter
      const nextChapterId = userChapters[nextIndex].id;
      const contextInfo = getContextForChapter(nextChapterId);
      console.log(`Context available for next chapter ${nextIndex + 1} (${nextChapterId}):`, 
        contextInfo.previousChapters.map(ch => `${ch.chapterId}: ${ch.summary.substring(0, 30)}...`)
      );
      
      // Mark current chapter as approved
      setGeneratedChapters(prev => prev.map((ch, index) => 
        index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
      ));
      
      // Show progress message
      toast.info(`Preparing to generate Chapter ${nextIndex + 1}...`, {
        id: 'next-chapter',
        duration: 3000
      });
      
      // First clear current content and reset UI state
      setCurrentChapterContent('');
      setUserFeedback('');
      
      // Then update workflow state in a separate operation
      console.log(`=== MOVING TO NEXT CHAPTER ===`);
      console.log(`Current chapter index: ${chapterIndex}`);
      console.log(`Next chapter index: ${nextIndex}`);
      console.log(`Total chapters: ${userChapters.length}`);

      // Update workflow state and immediately generate next chapter
      setWorkflow(prev => {
        const newWorkflow = {
          ...prev,
          currentChapterIndex: nextIndex,
          currentStep: 'chapter-generation' as const,
          chaptersCompleted: prev.chaptersCompleted + 1
        };
        console.log(`Updated workflow:`, newWorkflow);
        return newWorkflow;
      });

      // Generate next chapter immediately with explicit index
      console.log(`=== IMMEDIATELY GENERATING NEXT CHAPTER ===`);
      console.log(`Now generating chapter ${nextIndex + 1} (${nextChapterId})`);
      console.log(`Generated outlines available:`, generatedOutlines.length);
      console.log(`Generated chapters available:`, generatedChapters.length);

      toast.info(`Generating Chapter ${nextIndex + 1}: ${userChapters[nextIndex].outline.title}`, {
        id: 'next-chapter',
        duration: 5000
      });

      try {
        // Double-check that context is still available before generating
        const verifyContext = getContextForChapter(nextChapterId);
        console.log(`VERIFY: Next chapter context has ${verifyContext.previousChapters.length} previous chapters`);

        if (verifyContext.previousChapters.length < nextIndex) {
          console.warn(`Missing context for next chapter - detected ${verifyContext.previousChapters.length} when should have ${nextIndex}`);

          // Final attempt to repair context before generation
          const previousChaptersToAdd = generatedChapters.filter((ch, idx) =>
            idx < nextIndex && ch.content && ch.summary
          );

          for (const ch of previousChaptersToAdd) {
            console.log(`Adding emergency context for ${ch.id} with summary: ${ch.summary?.substring(0, 50)}...`);
            addChapterContext({
              chapterId: ch.id,
              summary: ch.summary || `Summary of ${ch.title} (emergency fallback)`,
              keyPoints: [],
              wordCount: ch.wordCount || 0,
              generatedAt: new Date()
            });
          }
        }

        console.log(`=== CALLING generateCurrentChapter(${nextIndex}) ===`);
        await generateCurrentChapter(nextIndex);
      } catch (error) {
        console.error('Error during chapter generation:', error);
        toast.error(`Failed to generate Chapter ${nextIndex + 1}. Please try regenerating.`);
      } finally {
        // Clean up approval flag
        delete window[approvalKey];
      }
    } else {
      // All chapters completed
      // Mark current chapter as approved first
      setGeneratedChapters(prev => prev.map((ch, index) =>
        index === chapterIndex ? { ...ch, status: 'completed', userApproved: true } : ch
      ));

      console.log('All chapters have been completed!');

      // Move to completed state
      setWorkflow(prev => ({
        ...prev,
        currentStep: 'completed',
        chaptersCompleted: prev.chaptersCompleted + 1
      }));

      // Show completion message
      toast.success('All chapters have been completed! 🎉', {
        duration: 8000
      });

      // Call completion handler - use the latest chapters from ref to ensure we have the most current data
      const latestChapters = latestChaptersRef.current;
      console.log('🎯 Workflow completion - using latest chapters from ref:', latestChapters.map(ch => ({
        id: ch.id,
        title: ch.title,
        status: ch.status,
        hasContent: !!ch.content,
        contentLength: ch.content?.length || 0,
        wordCount: ch.wordCount,
        contentPreview: ch.content ? ch.content.substring(0, 100) + '...' : 'NO CONTENT'
      })));
      console.log('🎯 Workflow completion - comparing state vs ref:');
      console.log('🎯 State chapters:', generatedChapters.map(ch => ({ id: ch.id, hasContent: !!ch.content, contentLength: ch.content?.length || 0 })));
      console.log('🎯 Ref chapters:', latestChapters.map(ch => ({ id: ch.id, hasContent: !!ch.content, contentLength: ch.content?.length || 0 })));
      console.log('🎯 Workflow completion - passing citations:', allCitations.length);

      onComplete(latestChapters, allCitations);

      // Clean up approval flag
      delete window[approvalKey];
    }
  };

  // Regenerate current chapter with feedback
  const regenerateChapterWithFeedback = async (feedback?: string) => {
    // Add feedback to the generation process
    if (feedback) {
      toast.info("Regenerating chapter with your feedback...");
    } else {
      toast.info("Regenerating chapter...");
    }
    await generateCurrentChapter();
    setUserFeedback('');
  };

  // Handle chapter content editing
  const handleChapterEdit = (content: string) => {
    const chapterIndex = workflow.currentChapterIndex;

    // Update chapter with edited content
    setGeneratedChapters(prev => prev.map((ch, index) =>
      index === chapterIndex ? {
        ...ch,
        content,
        wordCount: content.split(' ').length
      } : ch
    ));

    setCurrentChapterContent(content);
    toast.success("Chapter content updated successfully!");
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'outline-generation':
      case 'outline-review':
        return <FileText className="h-5 w-5" />;
      case 'chapter-generation':
      case 'chapter-review':
        return <BookOpen className="h-5 w-5" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStepTitle = () => {
    switch (workflow.currentStep) {
      case 'outline-generation':
        return 'Generating Chapter Outlines';
      case 'outline-review':
        return 'Review Chapter Outlines';
      case 'chapter-generation':
        return `Generating Chapter ${workflow.currentChapterIndex + 1}`;
      case 'chapter-review':
        return `Review Chapter ${workflow.currentChapterIndex + 1}`;
      case 'completed':
        return 'Book Generation Complete';
      default:
        return 'Book Generation';
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Progress Header */}
      <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-green-500 rounded-full"></div>
            {getStepIcon(workflow.currentStep)}
            {getStepTitle()}
          </CardTitle>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{Math.round((workflow.chaptersCompleted / workflow.totalChapters) * 100)}%</span>
            </div>
            <Progress value={(workflow.chaptersCompleted / workflow.totalChapters) * 100} className="h-2" />
            <div className="text-xs text-gray-600">
              Chapter {workflow.currentChapterIndex + 1} of {workflow.totalChapters}
              {workflow.currentStep !== 'completed' && workflow.currentStep !== 'outline-generation' && workflow.currentStep !== 'outline-review' && (
                <span> - {generatedOutlines[workflow.currentChapterIndex]?.title || userChapters[workflow.currentChapterIndex]?.outline.title}</span>
              )}
              {longBookStrategy && workflow.totalChapters > 10 && (
                <div className="mt-1 text-xs text-blue-600">
                  Long book mode: Processing in chunks of {longBookStrategy.chunkSize} chapters
                  {bookProgress && bookProgress.estimatedTimeRemaining > 0 && (
                    <span> • Est. {Math.ceil(bookProgress.estimatedTimeRemaining / 60000)} min remaining</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Step Content */}
      {workflow.currentStep === 'outline-generation' && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>Generate Chapter Outlines</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              First, we'll generate comprehensive outlines for all {userChapters.length} chapters. 
              This ensures proper structure and flow throughout your book.
            </p>
            <div className="flex gap-3">
              <Button onClick={generateAllOutlines} disabled={isGenerating} size="lg">
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Outlines...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate All Outlines
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={onBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Chapters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Outline Review Step */}
      {workflow.currentStep === 'outline-review' && (
        <div className="space-y-6">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Review Generated Outlines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Review the generated outlines below. You can regenerate any outline or proceed to chapter generation.
              </p>

              {/* Model Selection for Chapter Generation */}
              <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-2">AI Model Configuration</h4>
                <p className="text-sm text-blue-700 mb-4">
                  Your selected models for this book generation. You can change them if needed.
                </p>

                <div className="grid md:grid-cols-2 gap-4">
                  {/* Outline Model */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-blue-700">Outline Model:</label>
                    <select
                      value={outlineModel}
                      onChange={(e) => setOutlineModel(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      {AI_MODELS.map(model => (
                        <option key={model.id} value={model.id}>
                          {model.name}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-blue-600">Used for creating chapter outlines</p>
                  </div>

                  {/* Content Model */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-green-700">Content Model:</label>
                    <select
                      value={contentModel}
                      onChange={(e) => setContentModel(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      {AI_MODELS.map(model => (
                        <option key={model.id} value={model.id}>
                          {model.name}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-green-600">Used for generating chapter content</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button onClick={approveOutlinesAndStartGeneration} size="lg">
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Approve & Start Generation
                </Button>
                <Button variant="outline" onClick={onBack}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Chapters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Outline Editors */}
          <div className="grid gap-6">
            {generatedOutlines.map((outline, index) => (
              <OutlineEditor
                key={outline.id}
                outline={outline}
                chapterIndex={index}
                onSave={(updatedOutline) => saveOutline(index, updatedOutline)}
                onRegenerate={() => regenerateOutline(index)}
                isRegenerating={isGenerating}
              />
            ))}
          </div>
        </div>
      )}

      {/* Chapter Generation Step */}
      {workflow.currentStep === 'chapter-generation' && (
        <Card className="shadow-2xl border-0 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/20 backdrop-blur-sm overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10">
            <CardTitle className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Loader2 className="h-6 w-6 text-white animate-spin" />
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-gray-800">
                  Generating Chapter {workflow.currentChapterIndex + 1}
                </span>
                <span className="text-sm text-gray-600 font-medium">
                  {userChapters[workflow.currentChapterIndex]?.outline.title}
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              {/* Current Chapter Info */}
              <div className="text-center bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{workflow.currentChapterIndex + 1}</div>
                    <div className="text-sm text-blue-700 font-medium">Current Chapter</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{workflow.totalChapters}</div>
                    <div className="text-sm text-purple-700 font-medium">Total Chapters</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{workflow.chaptersCompleted}</div>
                    <div className="text-sm text-green-700 font-medium">Completed</div>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 max-w-2xl mx-auto">
                  Creating comprehensive, detailed content with examples, case studies, and practical applications.
                  Each chapter targets 4,000-6,000+ words for maximum value.
                </p>

                {/* Progress Bar */}
                <div className="space-y-3">
                  <div className="flex justify-between text-sm font-medium">
                    <span className="text-gray-700">Overall Progress</span>
                    <span className="text-blue-600">{Math.round((workflow.chaptersCompleted / workflow.totalChapters) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-4 rounded-full shadow-lg transition-all duration-500 ease-out"
                      style={{ width: `${(workflow.chaptersCompleted / workflow.totalChapters) * 100}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 text-center">
                    {workflow.totalChapters - workflow.chaptersCompleted} chapters remaining
                  </div>
                </div>
              </div>

              {/* Generation Status */}
              <div className="flex items-center justify-center gap-2 text-blue-600">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="font-medium">AI is writing your chapter with context from previous chapters...</span>
              </div>

              <div className="text-center text-sm text-gray-500">
                This may take several minutes for high-quality, comprehensive content.
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chapter Review Step */}
      {workflow.currentStep === 'chapter-review' && (
        <div className="space-y-6">
          {(() => {
            const currentChapter = generatedChapters[workflow.currentChapterIndex];
            const currentOutline = generatedOutlines[workflow.currentChapterIndex];

            console.log(`=== CHAPTER REVIEW RENDER ===`);
            console.log(`Current chapter index: ${workflow.currentChapterIndex}`);
            console.log(`Current chapter:`, currentChapter);
            console.log(`Current outline:`, currentOutline);
            console.log(`Chapter has content:`, !!currentChapter?.content);
            console.log(`Content length:`, currentChapter?.content?.length || 0);

            // Validate state before rendering
            if (workflow.currentChapterIndex >= generatedChapters.length) {
              console.error(`Chapter index ${workflow.currentChapterIndex} is out of bounds for ${generatedChapters.length} chapters`);
              return (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-semibold text-red-800">State Synchronization Error</h4>
                  <p className="text-sm text-red-700">
                    Chapter index is out of bounds. Please refresh the page or restart the generation process.
                  </p>
                  <Button
                    onClick={() => window.location.reload()}
                    className="mt-2 bg-red-600 hover:bg-red-700"
                  >
                    Refresh Page
                  </Button>
                </div>
              );
            }

            // If chapter doesn't exist, show loading state
            if (!currentChapter) {
              console.warn(`Chapter at index ${workflow.currentChapterIndex} is undefined`);
              return (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center animate-spin">
                      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-blue-800">Loading Chapter Data</h4>
                      <p className="text-sm text-blue-700">
                        Chapter data is being prepared. Please wait...
                      </p>
                    </div>
                  </div>
                </div>
              );
            }

            return (
              <div className="space-y-4">
                {autoGenerate && (
                  <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-800">Auto-Generation Mode</h4>
                        <p className="text-sm text-green-700">
                          Chapter generated successfully! Automatically proceeding to next chapter in 5 seconds...
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <ChapterReviewCard
                  chapter={currentChapter}
                  chapterIndex={workflow.currentChapterIndex}
                  outline={currentOutline}
                  onApprove={() => approveChapterAndContinue(workflow.currentChapterIndex)}
                  onRegenerate={regenerateChapterWithFeedback}
                  onEdit={handleChapterEdit}
                  isRegenerating={isGenerating}
                />
              </div>
            );
          })()}
        </div>
      )}

      {/* Completion Step */}
      {workflow.currentStep === 'completed' && (
        <>
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-green-600">
                <CheckCircle className="h-6 w-6" />
                Book Generation Complete!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Congratulations! Your book "{bookMetadata.title}" has been successfully generated with {workflow.totalChapters} chapters.
              </p>
              <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{workflow.totalChapters}</div>
                  <div className="text-sm text-gray-600">Chapters</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {generatedChapters.reduce((sum, ch) => sum + (ch.wordCount || 0), 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Words</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{allCitations.length}</div>
                  <div className="text-sm text-gray-600">Citations</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Display all generated chapters */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Chapters Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generatedChapters.map((chapter, index) => (
                  <div key={chapter.id} className="border rounded-md p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-800">
                          {index + 1}
                        </span>
                        {chapter.title}
                      </h3>
                      <Badge variant={chapter.status === 'completed' ? 'default' : 'secondary'}>
                        {chapter.status === 'completed' ? 'Completed' : chapter.status}
                      </Badge>
                    </div>
                    <p className="text-gray-600">{chapter.description}</p>
                    {chapter.outline && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-700">Sections:</p>
                        <ul className="text-sm text-gray-600 mt-1 pl-5 list-disc">
                          {chapter.outline.sections.slice(0, 5).map(section => (
                            <li key={section.id}>{section.title}</li>
                          ))}
                          {chapter.outline.sections.length > 5 && (
                            <li className="text-gray-500">+ {chapter.outline.sections.length - 5} more sections</li>
                          )}
                        </ul>
                      </div>
                    )}
                    {chapter.wordCount && (
                      <div className="mt-2 text-sm text-gray-500">{chapter.wordCount.toLocaleString()} words</div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};
