import React, { useEffect, useRef } from 'react';
import embed from 'vega-embed';
import { VegaLiteSpec, UploadedFile } from '../../types';
import { prepareDataForVega, getVegaTheme } from '../../utils/vega-utils';
// Note: Using a simple theme detection for now
// import { useTheme } from 'next-themes';

interface VegaLiteRendererProps {
  spec: VegaLiteSpec;
  file: UploadedFile;
  className?: string;
}

export const VegaLiteRenderer: React.FC<VegaLiteRendererProps> = ({ 
  spec, 
  file, 
  className = '' 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  // Simple theme detection - you can enhance this based on your theme system
  const isDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

  useEffect(() => {
    if (!containerRef.current || !spec || !file.data) return;

    const renderChart = async () => {
      try {
        // Prepare data for Vega-Lite
        const data = prepareDataForVega(file);
        
        // Create the complete spec with data
        const completeSpec = {
          ...spec,
          data: { values: data },
          config: getVegaTheme(isDark)
        };

        // Clear previous chart
        containerRef.current!.innerHTML = '';

        // Embed the chart
        const result = await embed(containerRef.current!, completeSpec, {
          actions: {
            export: true,
            source: false,
            compiled: false,
            editor: false
          },
          theme: isDark ? 'dark' : 'light'
        });

        // Resize the chart
        result.view.resize();
        await result.view.runAsync();
      } catch (error) {
        console.error('Error rendering Vega-Lite chart:', error);
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="flex items-center justify-center h-64 bg-red-50 border border-red-200 rounded-lg">
              <div class="text-center">
                <p class="text-red-600 font-medium">Error rendering chart</p>
                <p class="text-red-500 text-sm mt-1">${error instanceof Error ? error.message : 'Unknown error'}</p>
              </div>
            </div>
          `;
        }
      }
    };

    renderChart();
  }, [spec, file, isDark]);

  return (
    <div className={`vega-lite-container ${className}`}>
      <div ref={containerRef} className="w-full" />
    </div>
  );
};
