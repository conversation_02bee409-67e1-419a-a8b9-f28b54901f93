/**
 * Document Uploader Component
 * Modern interface for uploading and managing research documents
 * for the Research Comprehension Platform
 */

import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Upload,
  FileText,
  File,
  CheckCircle,
  AlertCircle,
  X,
  Download,
  Eye,
  Trash2,
  Search,
  Filter,
  BookOpen,
  Brain,
  Sparkles,
  Clock,
  Users,
  BarChart3
} from "lucide-react";
import { ResearchDocument } from '../types';
import { documentService } from '../services/document.service';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface DocumentUploaderProps {
  onDocumentUploaded: (document: ResearchDocument) => void;
  onDocumentSelected: (document: ResearchDocument) => void;
  documents: ResearchDocument[];
  isLoading: boolean;
}

interface UploadProgress {
  stage: 'uploading' | 'processing' | 'storing' | 'complete';
  progress: number;
  message: string;
}

export function DocumentUploader({
  onDocumentUploaded,
  onDocumentSelected,
  documents,
  isLoading
}: DocumentUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // File upload handlers
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!documentService.isFileSupported(file)) {
      setError(`Unsupported file type. Please upload PDF, DOC, DOCX, or TXT files.`);
      return;
    }

    setSelectedFile(file);
    setError(null);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setError(null);

      // Get current user from Supabase auth
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error('Please log in to upload documents');
      }

      const document = await documentService.uploadDocument(
        selectedFile,
        user.id,
        {
          onProgress: setUploadProgress
        }
      );

      setSelectedFile(null);
      setUploadProgress(null);
      onDocumentUploaded(document);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      setError(error.message);
      setUploadProgress(null);
    }
  };

  const handleCancelUpload = () => {
    setSelectedFile(null);
    setUploadProgress(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getFileIcon = (fileType?: string) => {
    if (fileType?.includes('pdf')) return <FileText className="h-5 w-5 text-red-500" />;
    if (fileType?.includes('word')) return <File className="h-5 w-5 text-blue-500" />;
    return <FileText className="h-5 w-5 text-gray-500" />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center"
      >
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
            <BookOpen className="h-8 w-8 text-white" />
          </div>
        </div>
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
          Research Comprehension Platform
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Upload research documents and transform them into interactive learning experiences with AI-powered quizzes, games, and personalized study sessions.
        </p>
      </motion.div>

      {/* Upload Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
          <CardContent className="p-8">
            {!selectedFile && !uploadProgress ? (
              <div
                className={`text-center cursor-pointer transition-colors ${
                  isDragOver ? 'bg-blue-50' : ''
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <div className="flex flex-col items-center space-y-4">
                  <div className="p-4 bg-blue-100 rounded-full">
                    <Upload className="h-12 w-12 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Upload Research Document</h3>
                    <p className="text-gray-600 mb-4">
                      Drag and drop your research paper here, or click to browse
                    </p>
                    <div className="flex flex-wrap justify-center gap-2 text-sm text-gray-500">
                      <Badge variant="outline">PDF</Badge>
                      <Badge variant="outline">DOC</Badge>
                      <Badge variant="outline">DOCX</Badge>
                      <Badge variant="outline">TXT</Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-2">Maximum file size: 50MB</p>
                  </div>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>
            ) : selectedFile && !uploadProgress ? (
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center space-x-3">
                  {getFileIcon(selectedFile.type)}
                  <div className="text-left">
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</p>
                  </div>
                </div>
                <div className="flex justify-center space-x-3">
                  <Button onClick={handleUpload} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Document
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={handleCancelUpload}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </div>
            ) : uploadProgress ? (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2">Processing Document</h3>
                  <p className="text-gray-600">{uploadProgress.message}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="capitalize">{uploadProgress.stage}</span>
                    <span>{uploadProgress.progress}%</span>
                  </div>
                  <Progress value={uploadProgress.progress} className="w-full" />
                </div>
              </div>
            ) : null}

            {error && (
              <Alert className="mt-4 border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-700">
                  {error}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Document Library */}
      {documents.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Your Research Library</span>
                  <Badge variant="secondary">{documents.length}</Badge>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search documents..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredDocuments.map((document) => (
                  <motion.div
                    key={document.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => onDocumentSelected(document)}
                  >
                    <div className="flex items-start space-x-3">
                      {getFileIcon(document.fileType)}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">
                          {document.title}
                        </h3>
                        {document.authors.length > 0 && (
                          <p className="text-sm text-gray-600 truncate">
                            by {document.authors.join(', ')}
                          </p>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-500">
                            {formatDate(document.uploadedAt)}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatFileSize(document.fileSize)}
                          </span>
                        </div>
                        {document.abstract && (
                          <p className="text-xs text-gray-600 mt-2 line-clamp-2">
                            {document.abstract}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                      <div className="flex space-x-1">
                        <Button size="sm" variant="ghost" className="h-8 px-2">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 px-2">
                          <Brain className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 px-2">
                          <BarChart3 className="h-3 w-3" />
                        </Button>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {document.sections.length} sections
                      </Badge>
                    </div>
                  </motion.div>
                ))}
              </div>

              {filteredDocuments.length === 0 && searchQuery && (
                <div className="text-center py-8">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No documents found matching "{searchQuery}"</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Getting Started Guide */}
      {documents.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-8">
              <div className="text-center">
                <div className="flex items-center justify-center mb-4">
                  <Sparkles className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Get Started with Research Comprehension</h3>
                <div className="grid md:grid-cols-3 gap-6 text-left">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                      <h4 className="font-medium">Upload Document</h4>
                    </div>
                    <p className="text-sm text-gray-600">
                      Upload your research paper in PDF, DOC, or TXT format
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                      <h4 className="font-medium">AI Processing</h4>
                    </div>
                    <p className="text-sm text-gray-600">
                      Our AI analyzes the content and creates interactive learning materials
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                      <h4 className="font-medium">Learn & Practice</h4>
                    </div>
                    <p className="text-sm text-gray-600">
                      Take quizzes, play games, and have AI-powered discussions
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
