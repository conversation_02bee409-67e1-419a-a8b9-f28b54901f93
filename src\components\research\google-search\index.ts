/**
 * Google Search Module with Deep Research
 * Academic research using Google Search via Gemini API with comprehensive deep research capabilities
 */

// Main component
export { GoogleSearchInterface as default, GoogleSearchInterface } from './components/GoogleSearchInterface';

// Standard Components
export { GoogleSearchMessage } from './components/GoogleSearchMessage';
export { GoogleSearchInput } from './components/GoogleSearchInput';
export { GoogleSearchHistory } from './components/GoogleSearchHistory';
export { GoogleSearchProgress } from './components/GoogleSearchProgress';
export { GoogleSearchResults } from './components/GoogleSearchResults';

// Deep Research Components
export { DeepResearchOutline } from './components/DeepResearchOutline';
export { DeepResearchProgress } from './components/DeepResearchProgress';
export { DeepResearchResults } from './components/DeepResearchResults';
export { DeepResearchCostEstimate } from './components/DeepResearchCostEstimate';

// Validation Components
export { GoogleSearchValidation } from './components/GoogleSearchValidation';
export { GoogleSearchDiagnostic } from './components/GoogleSearchDiagnostic';

// Test Page
export { GoogleSearchTestPage } from './test-page';

// Validation Utilities
export {
  runAllValidationTests,
  testAcademicSearchIntegration,
  validateUrls,
  validateAcademicSources,
  validateAcademicScoring,
  validateApiConfiguration,
  validateCostEstimation
} from './utils/validation-utils';

// Test Runner
export {
  runGoogleSearchTests,
  runIntegrationTest,
  runIndividualTest,
  quickHealthCheck,
  showTestHelp
} from './test-runner';

// Diagnostic Tools
export { runGoogleSearchDiagnostic } from './diagnostics/google-search-diagnostic';

// Services
export { googleSearchService } from './services/google-search.service';
export { googleAcademicService } from './services/google-academic.service';
export { googleSearchHistoryService } from './services/google-search-history.service';
export { deepResearchCostService } from './services/deep-research-cost.service';

// Types
export * from './types';

// Types
export type {
  GoogleSearchMessage as GoogleSearchMessageType,
  GoogleSearchSource,
  GoogleSearchResult,
  GoogleSearchSession,
  GoogleSearchOptions,
  GoogleSearchProgress as GoogleSearchProgressType,
  GoogleSearchError,
  GoogleCitation,
  GoogleReference
} from './types';

// Hooks
export { useGoogleSearch } from './hooks/useGoogleSearch';
