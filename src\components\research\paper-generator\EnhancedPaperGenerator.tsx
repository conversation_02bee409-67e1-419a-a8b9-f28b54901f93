import React, { useState, useEffect } from 'react';
import { toast } from "sonner";
import { <PERSON><PERSON>, Spark<PERSON>, GripVertical, FileText, BookOpen, FlaskConical, BarChart3 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { UserInputs, UserSection, ContentItem, GeneratedSection, PaperMetadata, PaperGeneration, PaperSection } from './types';
import { SECTION_TYPES, SECTION_PROMPTS, AI_MODELS } from './constants';
import { AIModelSelector } from './AIModelSelector';
import { PaperMetadataForm } from './PaperMetadataForm';
import { SectionCard } from './SectionCard';
import { GenerationPanel } from './GenerationPanel';
import { SavedPapersPanel } from './SavedPapersPanel';
import { StepByStepInput } from './StepByStepInput';
import paperAIService from './paper-ai.service';
import { enhancedAIService } from './enhanced-ai.service';
import { editorService } from './editor.service';
import { paperGenerationService } from './paper-generation.service';
import { MainEditor } from '../MainEditor';
import { EnhancedMainEditor } from '../EnhancedMainEditor';
import { documentService } from '@/services/documentService';
import { useAuth } from '@/contexts/AuthContext';
// Using the enhanced citation extraction service for better reference matching
import { extractCitationsFromText, matchCitationsWithReferences } from './citation-extraction.enhanced';
import { Citation } from './types';
import { separateSectionReferences } from './section-utils';
import { enhancedPaperAIService } from './services/enhanced-paper-ai.service';
import { SectionCitationContext, CitationSource } from './services/enhanced-citation-search.service';
import { referenceCollectionService } from './services/reference-collection.service';
// Import test functions for development
import './test-enhanced-citations';

export function EnhancedPaperGenerator() {
  const { user } = useAuth();

  // State for user inputs with metadata
  const [userInputs, setUserInputs] = useState<UserInputs>({
    metadata: {
      title: "",
      researchField: "",
      keywords: [],
      authors: []
    },
    userSections: []
  });

  // Track which sections have been generated
  const [generatedSections, setGeneratedSections] = useState<GeneratedSection[]>(
    SECTION_TYPES
      .filter(type => type.id !== 'keywords') // Keywords aren't a generated section
      .sort((a, b) => a.order - b.order)
      .map(type => ({
        id: type.id,
        name: type.name,
        description: type.description,
        status: 'pending',
        icon: type.icon
      }))
  );

  // UI state
  const [currentStep, setCurrentStep] = useState<'input' | 'generation' | 'editor' | 'saved'>('input');
  const [inputStep, setInputStep] = useState<'title' | 'methodology' | 'results' | 'additional'>('title');
  const [isGenerating, setIsGenerating] = useState(false);
  const [analyzingItems, setAnalyzingItems] = useState<Set<string>>(new Set());
  const [selectedModel, setSelectedModel] = useState(enhancedAIService.getDefaultModel());
  const [editorContent, setEditorContent] = useState<{ title: string; content: string; documentId?: string } | null>(null);

  // Section analysis and feedback state
  const [sectionAnalysis, setSectionAnalysis] = useState<Record<string, {
    feedback: string;
    suggestions: string[];
    isAnalyzing: boolean;
  }>>({});
  const [isFixing, setIsFixing] = useState<Set<string>>(new Set());

  // Citation tracking
  const [allCitations, setAllCitations] = useState<Citation[]>([]);
  const [sectionCitations, setSectionCitations] = useState<Record<string, string[]>>({});

  // Enhanced citation tracking with real sources
  const [enhancedCitations, setEnhancedCitations] = useState<Record<string, CitationSource[]>>({});
  const [useEnhancedCitations, setUseEnhancedCitations] = useState(true);
  const [useAIValidation, setUseAIValidation] = useState(true);
  const [useRealCitations, setUseRealCitations] = useState(true);

  // Supabase integration state
  const [currentPaperGeneration, setCurrentPaperGeneration] = useState<PaperGeneration | null>(null);
  const [paperSections, setPaperSections] = useState<PaperSection[]>([]);
  const [showSavedPapers, setShowSavedPapers] = useState(false);

  // Helper functions to check if we can proceed with generation
  const hasRequiredSections = () => {
    const requiredSectionIds = SECTION_TYPES
      .filter(type => type.required)
      .map(type => type.id);
      
    return requiredSectionIds.every(id => 
      userInputs.userSections.some(section => 
        section.name.toLowerCase() === SECTION_TYPES.find(t => t.id === id)?.name.toLowerCase()
      )
    );
  };

  const sectionsHaveContent = () => {
    return userInputs.userSections.every(section => section.items.length > 0);
  };

  const hasTitle = () => {
    return userInputs.metadata.title.trim() !== "";
  };

  // Only title is required to proceed
  const canProceed = hasTitle();

  // Supabase integration functions
  const createPaperGenerationSession = async (): Promise<PaperGeneration | null> => {
    if (!user) return null;

    try {
      const paperGeneration = await paperGenerationService.createPaperGeneration({
        user_id: user.id,
        title: userInputs.metadata.title,
        research_field: userInputs.metadata.researchField,
        keywords: userInputs.metadata.keywords,
        authors: userInputs.metadata.authors,
        ai_model: selectedModel,
        status: 'generating',
        metadata: {
          userSections: userInputs.userSections.map(section => ({
            id: section.id,
            name: section.name,
            itemCount: section.items.length
          }))
        }
      });

      if (paperGeneration) {
        setCurrentPaperGeneration(paperGeneration);
        toast.success('Paper generation session created');
      }

      return paperGeneration;
    } catch (error) {
      console.error('Error creating paper generation session:', error);
      toast.error('Failed to create paper generation session');
      return null;
    }
  };

  const saveSectionGeneration = async (
    sectionId: string,
    sectionName: string,
    prompt: string,
    content?: string,
    status: 'pending' | 'generating' | 'completed' | 'error' = 'pending',
    errorMessage?: string
  ): Promise<PaperSection | null> => {
    if (!currentPaperGeneration) return null;

    try {
      const sectionData = await paperGenerationService.createPaperSection({
        paper_generation_id: currentPaperGeneration.id,
        section_id: sectionId,
        section_name: sectionName,
        prompt_text: prompt,
        generated_content: content,
        status,
        generation_order: SECTION_TYPES.find(t => t.id === sectionId)?.order || 0,
        ai_model: selectedModel,
        generation_metadata: {
          temperature: 0.7,
          maxTokens: 2048,
          timestamp: new Date().toISOString()
        },
        error_message: errorMessage
      });

      if (sectionData) {
        setPaperSections(prev => {
          const existing = prev.find(s => s.section_id === sectionId);
          if (existing) {
            return prev.map(s => s.section_id === sectionId ? sectionData : s);
          }
          return [...prev, sectionData];
        });
      }

      return sectionData;
    } catch (error) {
      console.error('Error saving section generation:', error);
      return null;
    }
  };

  const loadPaperGeneration = async (paperGeneration: PaperGeneration) => {
    try {
      toast.info('Loading paper generation...');

      // Load the paper generation details
      const details = await paperGenerationService.getPaperGenerationWithDetails(paperGeneration.id);

      if (!details.generation) {
        toast.error('Failed to load paper generation');
        return;
      }

      // Set the current paper generation
      setCurrentPaperGeneration(details.generation);
      setPaperSections(details.sections);

      // Reconstruct user sections from metadata if available
      const userSections: UserSection[] = details.generation.metadata?.userSections || [];

      // Update user inputs with the loaded data
      setUserInputs({
        metadata: {
          title: details.generation.title,
          researchField: details.generation.research_field || '',
          keywords: details.generation.keywords,
          authors: details.generation.authors
        },
        userSections: userSections
      });

      // Update selected model
      setSelectedModel(details.generation.ai_model);

      // Update generated sections with the loaded content
      const updatedSections = SECTION_TYPES
        .filter(type => type.id !== 'keywords')
        .sort((a, b) => a.order - b.order)
        .map(type => {
          const savedSection = details.sections.find(s => s.section_id === type.id);
          if (savedSection) {
            return {
              id: type.id,
              name: type.name,
              description: type.description,
              status: savedSection.status === 'completed' ? 'completed' :
                      savedSection.status === 'error' ? 'error' :
                      savedSection.status === 'generating' ? 'generating' : 'pending',
              icon: type.icon,
              content: savedSection.generated_content || undefined
            };
          }
          return {
            id: type.id,
            name: type.name,
            description: type.description,
            status: 'pending' as const,
            icon: type.icon
          };
        });

      setGeneratedSections(updatedSections);

      // Load citations if available
      if (details.citations.length > 0) {
        const citations: Citation[] = details.citations.map(c => ({
          id: c.citation_id,
          inTextFormat: c.in_text_format,
          authors: c.authors,
          year: c.year,
          title: '',
          source: '',
          sectionIds: c.section_ids,
          referenceText: c.reference_text
        }));
        setAllCitations(citations);

        // Rebuild section citations mapping
        const sectionCitationsMap: Record<string, string[]> = {};
        details.citations.forEach(citation => {
          citation.section_ids.forEach(sectionId => {
            if (!sectionCitationsMap[sectionId]) {
              sectionCitationsMap[sectionId] = [];
            }
            sectionCitationsMap[sectionId].push(citation.citation_id);
          });
        });
        setSectionCitations(sectionCitationsMap);
      }

      // Determine which step to show
      const hasCompletedSections = details.sections.some(s => s.status === 'completed');
      const hasUserSections = userSections.length > 0;

      if (hasCompletedSections) {
        setCurrentStep('generation');
      } else if (hasUserSections || details.generation.title) {
        setCurrentStep('input');
      } else {
        setCurrentStep('input');
      }

      toast.success('Paper generation loaded successfully');
    } catch (error) {
      console.error('Error loading paper generation:', error);
      toast.error('Failed to load paper generation');
    }
  };

  // Metadata management
  const updateMetadata = (metadata: Partial<PaperMetadata>) => {
    setUserInputs(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        ...metadata
      }
    }));
  };

  // Section management
  const addUserSection = (sectionType: string) => {
    const sectionTemplate = SECTION_TYPES.find(s => s.id === sectionType);
    if (!sectionTemplate) return;

    // Check if section already exists to prevent duplicates
    if (userInputs.userSections.some(s => s.name === sectionTemplate.name)) {
      toast.error(`${sectionTemplate.name} section already exists.`);
      return;
    }

    const newSection: UserSection = {
      id: Date.now().toString(),
      name: sectionTemplate.name,
      items: []
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: [...prev.userSections, newSection]
    }));
  };

  const removeUserSection = (sectionId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.filter(section => section.id !== sectionId)
    }));
  };

  // Content item management
  const addContentItem = (sectionId: string, type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: Date.now().toString(),
      type,
      content: '',
      order: 0,
      ...(type === 'figure' && { title: '', caption: '' })
    };
    
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: [...section.items, { ...newItem, order: section.items.length }] }
          : section
      )
    }));
  };

  const updateContentItem = (sectionId: string, itemId: string, updates: Partial<ContentItem>) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section =>
        section.id === sectionId
          ? {
              ...section,
              items: section.items.map(item =>
                item.id === itemId ? { ...item, ...updates } : item
              )
            }
          : section
      )
    }));
  };

  // Custom function to add a figure with all data at once
  const addFigureWithData = (sectionId: string, figureData: {
    content: string;
    title: string;
    caption: string;
    analysis: string;
    originalFile: File;
  }) => {
    const newItem: ContentItem = {
      id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
      type: 'figure',
      content: figureData.content,
      title: figureData.title,
      caption: figureData.caption,
      analysis: figureData.analysis,
      originalFile: figureData.originalFile,
      order: 0
    };

    console.log('addFigureWithData called:', {
      sectionId,
      newItemId: newItem.id,
      hasContent: !!newItem.content,
      title: newItem.title
    });

    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section =>
        section.id === sectionId
          ? { ...section, items: [...section.items, { ...newItem, order: section.items.length }] }
          : section
      )
    }));
  };

  const removeContentItem = (sectionId: string, itemId: string) => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => 
        section.id === sectionId 
          ? { ...section, items: section.items.filter(item => item.id !== itemId) }
          : section
      )
    }));
  };

  const moveContentItem = (sectionId: string, itemId: string, direction: 'up' | 'down') => {
    setUserInputs(prev => ({
      ...prev,
      userSections: prev.userSections.map(section => {
        if (section.id !== sectionId) return section;
        
        const items = [...section.items];
        const index = items.findIndex(item => item.id === itemId);
        
        if (direction === 'up' && index > 0) {
          [items[index], items[index - 1]] = [items[index - 1], items[index]];
        } else if (direction === 'down' && index < items.length - 1) {
          [items[index], items[index + 1]] = [items[index + 1], items[index]];
        }
        
        return { ...section, items };
      })
    }));
  };

  // Helper to get section content as a string
  const getSectionContent = (sectionName: string): string => {
    const section = userInputs.userSections.find(
      s => s.name.toLowerCase() === sectionName.toLowerCase()
    );

    if (!section || !section.items.length) {
      return `[AI will generate ${sectionName} content]`;
    }

    // Remove redundant headings from user content (e.g., "Introduction", "Methodology", etc.)
    const headingPattern = new RegExp(`^\\s*(${SECTION_TYPES.map(s => s.name).join('|')})\\s*\\n+`, 'i');
    return section.items
      .map(item => {
        if (item.type === 'text') {
          // Remove section heading if present at the start
          return item.content.replace(headingPattern, '').trim();
        } else {
          return `Figure: ${item.title || 'Untitled'}\n${item.caption || 'No caption'}`;
        }
      })
      .join('\n\n');
  };

  // Helper to get related section content for citation context with enhanced context passing
  const getRelatedSectionContent = (sectionId: string): string[] => {
    const relatedContent: string[] = [];

    // Add content from related sections based on section type
    switch (sectionId) {
      case 'introduction':
        // Introduction can reference methodology and results if available
        const methodContent = getSectionContent('Methodology');
        const resultsContent = getSectionContent('Results');

        if (methodContent !== `[AI will generate Methodology content]`) {
          // Extract key methodological concepts for introduction context
          const methodConcepts = extractKeyConceptsFromContent(methodContent);
          relatedContent.push(`Methodology Context: This research uses ${methodConcepts.join(', ')}. ${methodContent.substring(0, 300)}...`);
        }

        if (resultsContent !== `[AI will generate Results content]`) {
          // Extract key findings for introduction context
          const resultConcepts = extractKeyConceptsFromContent(resultsContent);
          relatedContent.push(`Results Context: Key findings include ${resultConcepts.join(', ')}. ${resultsContent.substring(0, 300)}...`);
        }
        break;

      case 'methodology':
        // Methodology: ENHANCE user methodology with citations and online search
        // Include generated introduction for research context
        const generatedIntro = getGeneratedSectionContent('introduction');
        if (generatedIntro && generatedIntro.trim() !== '') {
          const generatedConcepts = extractKeyConceptsFromContent(generatedIntro);
          relatedContent.push(`Generated Introduction Context: Research background shows ${generatedConcepts.join(', ')}. ${generatedIntro.substring(0, 400)}...`);
        }

        // Also include user introduction if provided
        const introContent = getSectionContent('Introduction');
        if (introContent !== `[AI will generate Introduction content]`) {
          const introConcepts = extractKeyConceptsFromContent(introContent);
          relatedContent.push(`User Introduction Context: ${introConcepts.join(', ')}. ${introContent.substring(0, 300)}...`);
        }
        break;

      case 'results':
        // Results: ENHANCE user results with analysis and citations
        // Prioritize generated/enhanced methodology for context
        const generatedMethod = getGeneratedSectionContent('methodology');
        if (generatedMethod && generatedMethod.trim() !== '') {
          const generatedConcepts = extractKeyConceptsFromContent(generatedMethod);
          relatedContent.push(`Enhanced Methodology Context: Methods include ${generatedConcepts.join(', ')}. ${generatedMethod.substring(0, 400)}...`);
        }

        // Include user methodology if no generated version available
        const methodologyContent = getSectionContent('Methodology');
        if (methodologyContent !== `[AI will generate Methodology content]` && (!generatedMethod || generatedMethod.trim() === '')) {
          const methodConcepts = extractKeyConceptsFromContent(methodologyContent);
          relatedContent.push(`User Methodology Context: Using ${methodConcepts.join(', ')}. ${methodologyContent.substring(0, 300)}...`);
        }
        break;

      case 'discussion':
        // Discussion: Connect introduction + enhanced methodology + enhanced results
        // Prioritize ALL generated content for comprehensive context
        const generatedIntroForDiscussion = getGeneratedSectionContent('introduction');
        if (generatedIntroForDiscussion && generatedIntroForDiscussion.trim() !== '') {
          const introConcepts = extractKeyConceptsFromContent(generatedIntroForDiscussion);
          relatedContent.push(`Generated Introduction Context: Research background shows ${introConcepts.join(', ')}. ${generatedIntroForDiscussion.substring(0, 400)}...`);
        }

        const generatedMethodForDiscussion = getGeneratedSectionContent('methodology');
        if (generatedMethodForDiscussion && generatedMethodForDiscussion.trim() !== '') {
          const methodConcepts = extractKeyConceptsFromContent(generatedMethodForDiscussion);
          relatedContent.push(`Enhanced Methodology Context: Methods include ${methodConcepts.join(', ')}. ${generatedMethodForDiscussion.substring(0, 400)}...`);
        }

        const generatedResults = getGeneratedSectionContent('results');
        if (generatedResults && generatedResults.trim() !== '') {
          const resultConcepts = extractKeyConceptsFromContent(generatedResults);
          relatedContent.push(`Enhanced Results Context: Key findings show ${resultConcepts.join(', ')}. ${generatedResults.substring(0, 400)}...`);
        }

        // Include user content as fallback if generated content not available
        const methodologyForDiscussion = getSectionContent('Methodology');
        const resultForDiscussion = getSectionContent('Results');

        if (methodologyForDiscussion !== `[AI will generate Methodology content]` && (!generatedMethodForDiscussion || generatedMethodForDiscussion.trim() === '')) {
          const methodConcepts = extractKeyConceptsFromContent(methodologyForDiscussion);
          relatedContent.push(`User Methodology Context: Methods used include ${methodConcepts.join(', ')}. ${methodologyForDiscussion.substring(0, 300)}...`);
        }

        if (resultForDiscussion !== `[AI will generate Results content]` && (!generatedResults || generatedResults.trim() === '')) {
          const resultConcepts = extractKeyConceptsFromContent(resultForDiscussion);
          relatedContent.push(`User Results Context: Key findings are ${resultConcepts.join(', ')}. ${resultForDiscussion.substring(0, 300)}...`);
        }
        break;

      case 'conclusion':
        // Conclusion can reference all previous sections with enhanced context
        ['Introduction', 'Methodology', 'Results', 'Discussion'].forEach(sectionName => {
          const content = getSectionContent(sectionName);
          if (content !== `[AI will generate ${sectionName} content]`) {
            const concepts = extractKeyConceptsFromContent(content);
            relatedContent.push(`${sectionName} Context: ${concepts.join(', ')}. ${content.substring(0, 250)}...`);
          }

          // Also include generated content
          const generatedContent = getGeneratedSectionContent(sectionName.toLowerCase());
          if (generatedContent && generatedContent.trim() !== '') {
            const generatedConcepts = extractKeyConceptsFromContent(generatedContent);
            relatedContent.push(`Generated ${sectionName} Context: ${generatedConcepts.join(', ')}. ${generatedContent.substring(0, 250)}...`);
          }
        });
        break;

      case 'abstract':
        // Abstract should reference all sections for comprehensive summary
        ['Introduction', 'Methodology', 'Results', 'Discussion', 'Conclusion'].forEach(sectionName => {
          const content = getSectionContent(sectionName);
          const generatedContent = getGeneratedSectionContent(sectionName.toLowerCase());

          if (content !== `[AI will generate ${sectionName} content]`) {
            const concepts = extractKeyConceptsFromContent(content);
            relatedContent.push(`${sectionName}: ${concepts.join(', ')}. ${content.substring(0, 200)}...`);
          } else if (generatedContent && generatedContent.trim() !== '') {
            const concepts = extractKeyConceptsFromContent(generatedContent);
            relatedContent.push(`${sectionName}: ${concepts.join(', ')}. ${generatedContent.substring(0, 200)}...`);
          }
        });
        break;
    }

    return relatedContent;
  };

  // Helper to extract key concepts from content for better context passing
  const extractKeyConceptsFromContent = (content: string): string[] => {
    if (!content || content.trim() === '') return [];

    const concepts: string[] = [];
    const lowerContent = content.toLowerCase();

    // Extract technical terms (uppercase acronyms, hyphenated terms, etc.)
    const technicalPatterns = [
      /\b[A-Z]{2,6}\b/g, // Acronyms like PSInSAR, SAR, DEM
      /\b\w+-\w+(?:-\w+)?\b/g, // Hyphenated terms
      /\b\w+(?:ometry|ography|ology|metry|scopy|graphy)\b/gi, // Technical suffixes
    ];

    for (const pattern of technicalPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        concepts.push(...matches.filter(term =>
          term.length > 2 &&
          term.length < 20 &&
          !term.match(/^\d+$/)
        ));
      }
    }

    // Extract important nouns and phrases
    const importantWords = content.match(/\b(?:method|technique|approach|analysis|model|system|process|algorithm|data|result|finding|conclusion|measurement|observation|experiment|study|research)\w*\b/gi);
    if (importantWords) {
      concepts.push(...importantWords);
    }

    return [...new Set(concepts)].slice(0, 5); // Return unique concepts, max 5
  };

  // Section analysis functions
  const analyzeSectionContent = async (sectionId: string, sectionName: string) => {
    const section = userInputs.userSections.find(s => s.id === sectionId);
    if (!section || section.items.length === 0) return;

    setSectionAnalysis(prev => ({
      ...prev,
      [sectionId]: { ...prev[sectionId], isAnalyzing: true }
    }));

    try {
      const content = getSectionContent(sectionName);
      const analysisPrompt = `Analyze the following ${sectionName} section content for a research paper and provide:
1. Feedback on what's missing or could be improved
2. Specific suggestions for enhancement
3. Assessment of completeness and quality

Content to analyze:
${content}

Provide your response in this format:
FEEDBACK: [Your assessment of the current content]
SUGGESTIONS: [List 3-5 specific suggestions for improvement]`;

      const analysis = await enhancedAIService.generateText(analysisPrompt, selectedModel, {
        maxTokens: 1024,
        temperature: 0.7
      });

      // Parse the analysis response
      const feedbackMatch = analysis.match(/FEEDBACK:\s*(.*?)(?=SUGGESTIONS:|$)/s);
      const suggestionsMatch = analysis.match(/SUGGESTIONS:\s*(.*?)$/s);

      const feedback = feedbackMatch ? feedbackMatch[1].trim() : analysis;
      const suggestionsText = suggestionsMatch ? suggestionsMatch[1].trim() : '';
      const suggestions = suggestionsText.split('\n').filter(s => s.trim()).map(s => s.replace(/^[-•]\s*/, ''));

      setSectionAnalysis(prev => ({
        ...prev,
        [sectionId]: {
          feedback,
          suggestions,
          isAnalyzing: false
        }
      }));

      toast.success(`Analysis completed for ${sectionName} section`);
    } catch (error) {
      console.error('Error analyzing section:', error);
      setSectionAnalysis(prev => ({
        ...prev,
        [sectionId]: { ...prev[sectionId], isAnalyzing: false }
      }));
      toast.error('Failed to analyze section content');
    }
  };

  const fixSectionContent = async (sectionId: string, sectionName: string) => {
    const section = userInputs.userSections.find(s => s.id === sectionId);
    const analysis = sectionAnalysis[sectionId];
    if (!section || !analysis) return;

    setIsFixing(prev => new Set(prev).add(sectionId));

    try {
      const currentContent = getSectionContent(sectionName);
      const fixPrompt = `Rewrite and improve the following ${sectionName} section content based on the feedback and suggestions provided:

Current Content:
${currentContent}

Feedback: ${analysis.feedback}

Suggestions:
${analysis.suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}

Please provide an improved version that addresses the feedback and incorporates the suggestions. Write in academic style suitable for a research paper.`;

      const improvedContent = await enhancedAIService.generateText(fixPrompt, selectedModel, {
        maxTokens: 2048,
        temperature: 0.7
      });

      // Update the first text item in the section with the improved content
      const textItem = section.items.find(item => item.type === 'text');
      if (textItem) {
        updateContentItem(sectionId, textItem.id, { content: improvedContent });
        toast.success(`${sectionName} section has been improved based on AI suggestions`);
      }
    } catch (error) {
      console.error('Error fixing section:', error);
      toast.error('Failed to improve section content');
    } finally {
      setIsFixing(prev => {
        const newSet = new Set(prev);
        newSet.delete(sectionId);
        return newSet;
      });
    }
  };

  // Enhanced generation logic with real citations
  const generateSection = async (sectionId: string) => {
    // Mark section as generating
    setGeneratedSections(prev => prev.map(s =>
      s.id === sectionId ? { ...s, status: 'generating' } : s
    ));

    try {
      const { title, researchField, keywords } = userInputs.metadata;
      const sectionType = SECTION_TYPES.find(t => t.id === sectionId);
      const sectionName = sectionType?.name || sectionId;

      // Use enhanced citation system if enabled
      if (useEnhancedCitations && sectionId !== 'references') {
        const shouldUseTavily = ['introduction', 'methodology'].includes(sectionId);
        console.log(`Generating ${sectionName} with enhanced system (Tavily search: ${shouldUseTavily})...`);

        // Build citation context
        const citationContext: SectionCitationContext = {
          sectionId,
          sectionName,
          title,
          researchField: researchField || '',
          keywords: keywords || [],
          userContent: getSectionContent(sectionName),
          relatedSections: getRelatedSectionContent(sectionId)
        };

        // Generate with enhanced system (selective Tavily usage) - INCREASED LIMITS
        const citationLimits = {
          'introduction': 25,  // Increased for better coverage
          'methodology': 15,   // Increased for better coverage
          'results': 8,        // Increased for better coverage
          'discussion': 12,    // Increased for better coverage
          'conclusion': 0,
          'abstract': 0
        };

        const maxCitations = citationLimits[sectionId] || 5;

        // Use AI validation if enabled and section has citations
        const result = useAIValidation && maxCitations > 0
          ? await enhancedPaperAIService.generateSectionWithValidatedReferences(
              sectionId,
              sectionName,
              citationContext,
              {
                model: selectedModel,
                maxTokens: 2048,
                temperature: 0.7,
                maxCitations: maxCitations,
                useRealCitations: useRealCitations
              }
            )
          : await enhancedPaperAIService.generateSectionWithRealCitations(
              sectionId,
              sectionName,
              citationContext,
              {
                model: selectedModel,
                maxTokens: 2048,
                temperature: 0.7,
                maxCitations: maxCitations,
                useRealCitations: useRealCitations
              }
            );

        // Store enhanced citations
        setEnhancedCitations(prev => ({
          ...prev,
          [sectionId]: result.citations
        }));

        // Convert enhanced citations to traditional citation format for compatibility
        const traditionalCitations = result.citations.map(citation => ({
          id: citation.id,
          text: citation.inTextCitation,
          authors: citation.authors,
          year: citation.year,
          title: citation.title,
          journal: citation.journal || '',
          url: citation.url,
          referenceText: citation.formattedCitation,
          sectionId: sectionId
        }));

        // Add to allCitations for traditional reference generation
        setAllCitations(prev => {
          const filtered = prev.filter(c => c.sectionId !== sectionId); // Remove old citations from this section
          return [...filtered, ...traditionalCitations];
        });

        // Update section citations mapping
        setSectionCitations(prev => ({
          ...prev,
          [sectionId]: result.citations.map(c => c.inTextCitation)
        }));

        // Update section with generated content
        setGeneratedSections(prev => prev.map(s =>
          s.id === sectionId ? {
            ...s,
            status: 'completed',
            content: result.content
          } : s
        ));

        // Save to Supabase
        await saveSectionGeneration(sectionId, sectionName, 'Enhanced generation with real citations', result.content, 'completed');

        console.log(`Generated ${sectionName}: ${result.wordCount} words, ${result.citationCount} real citations`);
        console.log(`Added ${traditionalCitations.length} citations to allCitations for reference generation`);
        return;
      }

      // Fallback to original generation logic
      let prompt = '';
      let generatedContent = '';

      // Always generate a section, even if user content is missing
      switch (sectionId) {
        case 'introduction':
          prompt = SECTION_PROMPTS.introduction(title, researchField || '', keywords || []);
          break;

        case 'methodology':
          // ENHANCE user methodology with citations and online search
          prompt = SECTION_PROMPTS.methodology(title, researchField || '', getSectionContent('Methodology') || '');
          break;
        case 'results':
          // ENHANCE user results with analysis and citations
          // Use generated methodology if available, otherwise user methodology
          const methodologyForResults = getGeneratedSectionContent('methodology') || getSectionContent('Methodology') || '';
          prompt = SECTION_PROMPTS.results(title, methodologyForResults, getSectionContent('Results') || '');
          break;
        case 'discussion':
          // Generate discussion based on ALL generated content
          const resultsForDiscussion = getGeneratedSectionContent('results') || getSectionContent('Results') || '';
          const methodologyForDiscussion = getGeneratedSectionContent('methodology') || getSectionContent('Methodology') || '';
          prompt = SECTION_PROMPTS.discussion(title, resultsForDiscussion, methodologyForDiscussion);
          break;
        case 'conclusion':
          // Generate conclusion based on generated discussion and results
          const resultsForConclusion = getGeneratedSectionContent('results') || getSectionContent('Results') || '';
          const discussionForConclusion = getGeneratedSectionContent('discussion') || '';
          prompt = SECTION_PROMPTS.conclusion(title, resultsForConclusion, discussionForConclusion);
          break;
        case 'abstract':
          prompt = SECTION_PROMPTS.abstract(
            title,
            getGeneratedSectionContent('introduction') || '',
            getGeneratedSectionContent('methodology') || '',
            getGeneratedSectionContent('results') || '',
            getGeneratedSectionContent('conclusion') || ''
          );
          break;
        case 'references':
          // Generate references using the enhanced collection service
          if (useEnhancedCitations && Object.keys(enhancedCitations).length > 0) {
            console.log('Generating references using reference collection service...');

            try {
              // Use AI validation for reference collection if enabled
              const collectionResult = useAIValidation
                ? await referenceCollectionService.collectAndValidateReferencesWithAI(enhancedCitations, selectedModel)
                : await referenceCollectionService.collectReferencesFromSections(enhancedCitations);

              console.log(`Reference collection completed (AI validation: ${useAIValidation}):`, collectionResult.statistics);

              // Update the section with the formatted references
              setGeneratedSections(prev => prev.map(s =>
                s.id === 'references' ? {
                  ...s,
                  status: 'completed',
                  content: collectionResult.formattedReferencesText
                } : s
              ));

              // Log detailed statistics
              console.log(`Generated ${collectionResult.statistics.totalReferences} references from ${Object.keys(enhancedCitations).length} sections`);
              console.log('Section breakdown:', collectionResult.statistics.sectionBreakdown);

              return;
            } catch (error) {
              console.error('Reference collection failed:', error);
              // Fallback to simple collection
            }
          }

          // Fallback to original citation extraction
          console.log('Generating references section from extracted citations');
          console.log(`Found ${allCitations.length} total citations`);

          // Force citation extraction from all completed sections before generating references
          await extractCitationsFromAllSections();

          // Use the generateReferencesFromCitations helper function
          const referencesContent = generateReferencesFromCitations();

          // Update the section directly without calling the AI service
          setGeneratedSections(prev => prev.map(s =>
            s.id === 'references' ? {
              ...s,
              status: 'completed',
              content: referencesContent
            } : s
          ));

          console.log('Traditional references generation completed');
          // Return early to skip the AI call
          return;
        default:
          prompt = `Generate a ${sectionId} section for the research paper titled "${title}".`;
      }

      // Save the prompt to Supabase before generating
      await saveSectionGeneration(sectionId, sectionName, prompt, undefined, 'generating');

      generatedContent = await enhancedAIService.generateText(prompt, selectedModel, {
        maxTokens: 2048,
        temperature: 0.7
      });

      // Clean and update the section with generated content
      const cleanedContent = cleanAcademicContent(generatedContent);
      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'completed', content: cleanedContent } : s
      ));

      // Save the generated content to Supabase
      await saveSectionGeneration(sectionId, sectionName, prompt, cleanedContent, 'completed');
      
      // For non-reference sections, extract citations and their references
      if (sectionId !== 'references' && generatedContent) {
        // Separate main content from any references section that might be included
        const { mainContent, extractedReferences } = separateSectionReferences(generatedContent);

        // Update the section content to only show main content (without references)
        setGeneratedSections(prev => prev.map(s =>
          s.id === sectionId ? { ...s, content: mainContent } : s
        ));

        console.log(`Separated content for ${sectionId}: ${extractedReferences.length} references found`);

        // Extract citations from the main content only
        const extractedInfo = extractCitationsFromText(mainContent, sectionId);

        if (extractedInfo.citations.length > 0) {
          console.log(`Found ${extractedInfo.citations.length} citations in ${sectionId} section`);

          // Update section citations map
          setSectionCitations(prev => ({
            ...prev,
            [sectionId]: extractedInfo.citations.map(c => c.id)
          }));

          // Create citations with reference data from extracted references
          const citationsWithRefs = extractedInfo.citations.map(citation => {
            // Try to find a matching reference in the extracted references
            let matchingRef = '';

            if (extractedReferences.length > 0) {
              const authorLastName = citation.authors[0].split(' ')[0].toLowerCase();
              const yearStr = citation.year.toString();

              matchingRef = extractedReferences.find(ref =>
                ref.toLowerCase().includes(authorLastName) && ref.includes(yearStr)
              ) || '';
            }

            return {
              ...citation,
              referenceText: matchingRef
            };
          });

          // Merge with existing citations
          setAllCitations(prev => {
            // Check for existing citations with same ID or in-text format
            const newCitations = citationsWithRefs.filter(newCitation =>
              !prev.some(existing =>
                existing.id === newCitation.id ||
                existing.inTextFormat === newCitation.inTextFormat
              )
            );

            // Update existing citations to include this section ID if they're found in this section
            const updatedExisting = prev.map(citation => {
              const matchingNew = citationsWithRefs.find(c =>
                c.id === citation.id ||
                c.inTextFormat === citation.inTextFormat
              );

              if (matchingNew) {
                // Add this section to the citation's sectionIds if not already there
                // And update the referenceText if we found one and it doesn't already have one
                if (!citation.sectionIds.includes(sectionId)) {
                  return {
                    ...citation,
                    sectionIds: [...citation.sectionIds, sectionId],
                    referenceText: citation.referenceText || matchingNew.referenceText
                  };
                }
              }

              return citation;
            });

            // Return updated citations plus any new ones
            return [...updatedExisting, ...newCitations];
          });
        }
        
        // After extracting citations from a content section, try to match with references if we have any
        // This helps build up the reference text as we go
        setAllCitations(prev => {
          // Only try to match if we have citations
          if (prev.length === 0) return prev;
          
          // Get all existing references content, even from incomplete sections
          let referencesContent = '';
          const refsSection = generatedSections.find(s => s.id === 'references');
          if (refsSection?.content) {
            referencesContent = refsSection.content;
          } else {
            // Don't use default references as a fallback
            referencesContent = "";
          }
          
          // Match citations with references
          const updatedCitations = matchCitationsWithReferences(prev, referencesContent);
          
          // Log how many citations have references
          const withRefs = updatedCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
          console.log(`After matching: ${withRefs}/${updatedCitations.length} citations have reference text`);
          
          return updatedCitations;
        });
      }
      
      // For references section, match with existing citations
      if (sectionId === 'references' && generatedContent) {
        // Update citations with reference details
        const updatedCitations = matchCitationsWithReferences(allCitations, generatedContent);
        const withRefs = updatedCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
        
        console.log(`Matched references: ${withRefs}/${updatedCitations.length} citations have reference text`);
        
        setAllCitations(updatedCitations);          // We no longer try to match with default references
          // Instead, we'll rely only on the references naturally generated by the AI model
        
        // Generate a fresh references section from the updated citations
        const freshReferences = generateReferencesFromCitations();
        
        // Always update the references section to ensure we're showing the most current data
        console.log('Updating references section with matched citations');
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: freshReferences
          } : s
        ));
      }
    } catch (error) {
      console.error(`Error generating ${sectionId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      setGeneratedSections(prev => prev.map(s =>
        s.id === sectionId ? { ...s, status: 'error' } : s
      ));

      // Save the error to Supabase
      await saveSectionGeneration(sectionId, sectionName, prompt || '', undefined, 'error', errorMessage);

      toast.error(`Failed to generate ${sectionId} section. Please try again.`);
    }
  };
  
  // Helper function to get the content of generated sections
  const getGeneratedSectionContent = (sectionId: string): string => {
    const section = generatedSections.find(s => s.id === sectionId && s.status === 'completed');
    // Add logging to debug references issues
    console.log(`Getting content for ${sectionId}: ${section ? 'Content found' : 'No content'} (${section?.content?.substring(0, 20)}...)`);
    return section?.content || '';
  };

  // Generate all sections in order
  const generateAllSections = async () => {
    if (!canProceed) {
      toast.error("Please provide a title for your research paper.");
      return;
    }

    setIsGenerating(true);
    setCurrentStep('generation');

    // Create a paper generation session in Supabase if user is logged in
    if (user && !currentPaperGeneration) {
      await createPaperGenerationSession();
    }

    // Sort sections by logical order, except references which will be generated last
    const orderedSections = [...generatedSections]
      .filter(section => section.id !== 'references') // Remove references first
      .sort((a, b) => {
        const aType = SECTION_TYPES.find(t => t.id === a.id);
        const bType = SECTION_TYPES.find(t => t.id === b.id);
        return (aType?.order || 0) - (bType?.order || 0);
      });
    
    // Find the references section
    const referencesSection = generatedSections.find(s => s.id === 'references');

    try {
      // Generate all main sections first
      const generatedContentSections = [];
      for (const section of orderedSections) {
        try {
          await generateSection(section.id);
          generatedContentSections.push(section.id);
          // Add a small delay to ensure the UI updates properly
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (sectionError) {
          console.error(`Error generating ${section.id} section:`, sectionError);
          toast.error(`Failed to generate ${section.name} section.`);
        }
      }
      
      console.log(`Successfully generated ${generatedContentSections.length} content sections: ${generatedContentSections.join(', ')}`);
      
      // Always generate a references section, even if no content sections have been generated
      if (referencesSection) {
        toast.info("Compiling references from all in-text citations...");
        
        try {
          // Force a longer delay before generating references to ensure state is updated and content is available
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // We'll no longer try to match with default references
          if (allCitations.length > 0) {
            console.log(`Processing ${allCitations.length} citations for references`);
            
            // Log how many citations already have references
            const withRefs = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
            console.log(`${withRefs}/${allCitations.length} citations have reference text`);
            
            // Short delay to ensure state is updated
            await new Promise(resolve => setTimeout(resolve, 300));
          }
          
          // Generate references directly from the extracted citations
          const referencesContent = generateReferencesFromCitations();
          
          // Update the section state
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: referencesContent
            } : s
          ));
          
          // Log the first 100 chars of references content
          console.log(`Generated references section (${referencesContent.length} chars): ${referencesContent.substring(0, 100)}...`);
          
          const citationsWithRefs = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
          toast.success(`References compiled successfully! Found ${citationsWithRefs} citations with reference data.`);
        } catch (refsError) {
          console.error("Error processing references:", refsError);
          toast.error("Failed to compile references. No default references will be shown.");
          
          // Add an error message for references if generation failed
          setGeneratedSections(prev => prev.map(s => 
            s.id === 'references' ? { 
              ...s, 
              status: 'completed', 
              content: "No references could be extracted from this paper. Please ensure your sections include proper citations that can be matched with references."
            } : s
          ));
        }
      } else {
        console.error("References section not found in generatedSections");
      }
      
      toast.success("Your research paper has been generated successfully!");
    } catch (error) {
      console.error("Error generating paper:", error);
      toast.error("There was an error generating your paper. Please try again.");
      
      // Add an error message for references section
      if (referencesSection) {
        setGeneratedSections(prev => prev.map(s => 
          s.id === 'references' ? { 
            ...s, 
            status: 'completed', 
            content: "Cannot generate references because there was an error generating the paper content. Please try again."
          } : s
        ));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Export the paper - this is a fallback function, main export is handled by ExportDialog
  const handleExport = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to export.");
      return;
    }

    // This function serves as a validation check and fallback
    // The actual export functionality is handled by the ExportDialog component
    toast.info("Use the export buttons below to choose your preferred format.");
  };

  // Helper function to clean and format academic content
  const cleanAcademicContent = (content: string): string => {
    return content
      // Convert markdown headers to HTML headers instead of bold with asterisks
      .replace(/^#{6}\s+(.+)$/gm, '<h6>$1</h6>')
      .replace(/^#{5}\s+(.+)$/gm, '<h5>$1</h5>')
      .replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>')
      .replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>')
      .replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>')
      .replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>')
      // Convert markdown bold to HTML strong tags
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      // Convert markdown italic to HTML em tags
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      // Remove any remaining stray asterisks that aren't part of formatting
      .replace(/(?<!\*)\*(?!\*)/g, '')
      // Clean up any remaining markdown-style formatting
      .replace(/^\s*\*\s+/gm, '• ') // Convert bullet points
      .replace(/^\s*-\s+/gm, '• ') // Convert dashes to bullet points
      // Remove any standalone asterisks at the beginning or end of lines
      .replace(/^\*+\s*/gm, '')
      .replace(/\s*\*+$/gm, '')
      // Fix citation formatting - ensure proper capitalization
      .replace(/\([A-Z][A-Z\s,&]+,\s*\d{4}\)/g, (match) => {
        // Convert all-caps citations to proper case
        return match.replace(/[A-Z]{2,}/g, (caps) => {
          return caps.charAt(0) + caps.slice(1).toLowerCase();
        });
      })
      // Make common subheadings bold if they're not already
      .replace(/^(Abstract|Introduction|Methodology|Methods|Results|Discussion|Conclusion|References|Background|Objectives?|Aims?|Literature Review|Data Analysis|Statistical Analysis|Findings|Implications|Limitations|Future Work)$/gm, '**$1**')
      // Clean up extra whitespace
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  };

  // Generate references section from collected citations
  const generateReferencesFromCitations = () => {
    console.log(`Generating references from ${allCitations.length} citations`);

    // First check if we have any citations
    if (allCitations.length === 0) {
      // If using enhanced citations, check if we have any enhanced citations to fall back on
      if (useEnhancedCitations && Object.keys(enhancedCitations).length > 0) {
        console.log("No traditional citations found, but enhanced citations available");
        const allEnhanced: CitationSource[] = [];
        Object.values(enhancedCitations).forEach(sectionCitations => {
          allEnhanced.push(...sectionCitations);
        });

        if (allEnhanced.length > 0) {
          const uniqueEnhanced = allEnhanced.filter((citation, index, array) =>
            array.findIndex(c => c.citationKey === citation.citationKey) === index
          );

          uniqueEnhanced.sort((a, b) => {
            const authorA = a.authors[0]?.split(',')[0] || '';
            const authorB = b.authors[0]?.split(',')[0] || '';
            return authorA.localeCompare(authorB);
          });

          const referencesText = uniqueEnhanced.map(citation =>
            `<p>${citation.formattedCitation}</p>`
          ).join('\n') +
          `\n\n<p><em>Reference Statistics: ${uniqueEnhanced.length} total references | Enhanced citations with verified sources</em></p>`;

          console.log(`Generated references from ${uniqueEnhanced.length} enhanced citations`);
          return referencesText;
        }
      }

      toast.warning("No citations found in the paper.");
      console.log("No citations found, returning empty references");
      return "<p>No citations were found in this paper. Please regenerate sections to include proper academic citations.</p>";
    }

    // Collect all references from all sections that were extracted during generation
    const allSectionRefs = new Set<string>();

    // Extract references from each completed section (excluding the references section itself)
    generatedSections.forEach(section => {
      if (section.status === 'completed' && section.content && section.id !== 'references') {
        // Get the original content that might contain references
        const originalContent = section.content;

        // Use the separateSectionReferences utility to extract references properly
        const { extractedReferences } = separateSectionReferences(originalContent);

        console.log(`Section ${section.id}: Found ${extractedReferences.length} references`);

        // Add each extracted reference to our set
        extractedReferences.forEach(ref => {
          if (ref && ref.trim().length > 30) {
            allSectionRefs.add(ref.trim());
          }
        });

        // If no references were found with the utility, try a more aggressive approach
        if (extractedReferences.length === 0) {
          const lines = originalContent.split('\n');
          let inReferencesSection = false;

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Check if we're entering a references section
            if (line.match(/^references\s*:?$/i) || line.match(/^bibliography\s*:?$/i)) {
              inReferencesSection = true;
              continue;
            }

            // If we're in references section, look for reference-like lines
            if (inReferencesSection && line.length > 30) {
              // Look for lines that look like academic references
              if (line.match(/[A-Z][a-z]+.*\(\d{4}\)/) &&
                  (line.includes('.') || line.includes(',')) &&
                  !line.toLowerCase().startsWith('citation') &&
                  !line.toLowerCase().startsWith('total')) {
                allSectionRefs.add(line.trim());
              }
            }
          }
        }
      }
    });

    console.log(`Found ${allSectionRefs.size} references in section content`);

    // Use citations that already have reference text from the extraction process
    allCitations.forEach(citation => {
      if (citation.referenceText && citation.referenceText.trim() !== '') {
        allSectionRefs.add(citation.referenceText.trim());
      }
    });

    // Match citations with extracted references more intelligently
    allCitations.forEach(citation => {
      if (!citation.referenceText || citation.referenceText.trim() === '') {
        // Try to match with existing references first
        const authorLastName = citation.authors[0].split(' ')[0].toLowerCase();
        const yearStr = citation.year.toString();

        let matchingRef = '';
        for (const ref of allSectionRefs) {
          if (ref.toLowerCase().includes(authorLastName) && ref.includes(yearStr)) {
            matchingRef = ref;
            break;
          }
        }

        if (matchingRef) {
          citation.referenceText = matchingRef;
        }
      }
    });

    // Only generate placeholder references if we have citations but no references at all
    if (allSectionRefs.size === 0 && allCitations.length > 0) {
      console.log("No references found in sections, this indicates the AI didn't generate proper references");
      toast.warning("No references were found in the generated content. Please regenerate the paper to get proper references.");
      return "No references were found in the generated content. Please regenerate the paper sections to get proper academic references.";
    }

    // Create our final references array
    const referencesArray = Array.from(allSectionRefs);

    if (referencesArray.length === 0) {
      toast.error("No references could be generated for the citations in this paper.");
      return "No references could be found or generated for the citations in this paper.";
    }

    // Sort references alphabetically by first author's last name
    const sortedReferences = referencesArray.sort((a, b) => {
      const getFirstAuthorLastName = (ref: string): string => {
        const authorPart = ref.split('(')[0].trim();
        return authorPart.split(',')[0].trim().toLowerCase();
      };
      return getFirstAuthorLastName(a).localeCompare(getFirstAuthorLastName(b));
    });

    // Generate statistics for references and citations
    const totalCitations = allCitations.length;
    const citationsWithReferences = allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length;
    const uniqueAuthorsCount = new Set(allCitations.flatMap(c => c.authors.map(a => a.toLowerCase()))).size;

    // Format the references section in proper academic style with proper spacing
    const referencesText = sortedReferences.map(ref => `<p>${ref}</p>`).join('\n') +
      `\n\n<p><em>Citation Statistics: ${totalCitations} total citations | ${citationsWithReferences} with references | ${uniqueAuthorsCount} unique authors</em></p>`;

    console.log(`Generated references text with ${sortedReferences.length} entries`);

    return referencesText;
  };

  // Edit in main editor
  const handleEditInMainEditor = () => {
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    if (completedSections.length === 0) {
      toast.error("No completed sections to edit.");
      return;
    }
    
    // Only proceed with regenerating references if we have content sections completed
    const contentSections = completedSections.filter(s =>
      ['introduction', 'methodology', 'results', 'discussion', 'conclusion'].includes(s.id)
    );
    
    console.log(`Found ${contentSections.length} completed content sections before editing`);
    
    // Generate references from collected citations
    const referencesContent = generateReferencesFromCitations();
    toast.success(`Compiled ${allCitations.length} citations into references section.`);
    
    // Create or update the references section
    const refsSection = generatedSections.find(s => s.id === 'references');
    
    if (refsSection) {
      // Update existing references section
      setGeneratedSections(prev => prev.map(s => 
        s.id === 'references' ? { 
          ...s, 
          status: 'completed', 
          content: referencesContent
        } : s
      ));
    } else {
      // Create a new references section if it doesn't exist
      setGeneratedSections(prev => [
        ...prev, 
        {
          id: 'references',
          name: 'References',
          description: 'Citations and references used throughout the paper',
          status: 'completed',
          content: referencesContent,
          icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
        }
      ]);
    }
    
    // Small delay to ensure state is updated before sending to editor
    setTimeout(() => sendToEditor(), 500);
  };
  
  // Helper function to send content to editor with properly formatted sections
  const sendToEditor = async () => {
    
    // Get the latest completed sections, including the newly generated references
    const completedSections = generatedSections.filter(s => s.status === 'completed');
    
    // Create a formatted document from all the sections except references
    const mainSections = completedSections
      .filter(s => s.id !== 'references') // Exclude references initially
      .sort((a, b) => {
        // Order should be: Abstract, Introduction, Methodology, Results, Discussion, Conclusion
        const sectionOrder: Record<string, number> = {
          'abstract': 1,
          'introduction': 2,
          'methodology': 3,
          'results': 4,
          'discussion': 5,
          'conclusion': 6
        };
        return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
      })
      .map(section => `<h2>${section.name}</h2>\n\n${section.content}`)
      .join('\n\n');
    
    // Get references section separately to ensure it's always at the end
    let referencesSection = completedSections.find(s => s.id === 'references');
    
    // Generate fresh references from the citations every time
    let referencesContent = generateReferencesFromCitations();
    
    // Log the citations and references status
    console.log(`Using ${allCitations.length} citations to generate references section`);
    console.log(`Citations with reference text: ${allCitations.filter(c => c.referenceText && c.referenceText.trim() !== '').length}`);
    
    // Ensure we have valid references content
    if (!referencesContent || referencesContent.trim() === '') {
      console.log('No references content generated, creating minimal references section');
      referencesContent = "<p>No references were found in the generated paper content.</p>";
    }

    // Update the references section with the generated content
    if (referencesSection) {
      setGeneratedSections(prev => prev.map(s =>
        s.id === 'references' ? { ...s, content: referencesContent, status: 'completed' as const } : s
      ));
    } else {
      // Create a new references section if none exists
      const newReferencesSection: GeneratedSection = {
        id: 'references',
        name: 'References',
        description: 'Citations and references used throughout the paper',
        status: 'completed',
        content: referencesContent,
        icon: SECTION_TYPES.find(t => t.id === 'references')?.icon
      };

      setGeneratedSections(prev => [...prev, newReferencesSection]);
      referencesSection = newReferencesSection;
    }
    
    // Clean up references content
    // Remove any "References:" header that might be at the beginning
    referencesContent = referencesContent.replace(/^References:[ \t]*[\r\n]+/, '');
    
    // Combine main content with references section always at the end
    // Always include references section, with bold heading
    const rawContent = mainSections + `\n\n<h2>References</h2>\n\n${referencesContent}`;
    const formattedContent = cleanAcademicContent(rawContent);

    try {
      // Create a new document in Supabase if user is logged in
      if (user) {
        const newDocument = await documentService.createDocument({
          title: userInputs.metadata.title || 'Generated Research Paper',
          content: formattedContent,
          document_type: 'paper',
          status: 'completed',
          metadata: {
            generatedBy: 'AI Paper Generator',
            researchField: userInputs.metadata.researchField,
            keywords: userInputs.metadata.keywords,
            authors: userInputs.metadata.authors,
            sections: completedSections.map(s => ({ id: s.id, name: s.name })),
            citationCount: allCitations.length
          },
        });
        
        if (newDocument) {
          toast.success("Paper saved and loaded in editor for further editing.");
          
          // Store the document ID for the enhanced editor
          setEditorContent({
            title: newDocument.title,
            content: newDocument.content || '',
            documentId: newDocument.id
          });
        } else {
          throw new Error('Failed to create document');
        }
      } else {
        // Fallback to localStorage if not logged in
        editorService.sendToMainEditor({
          title: userInputs.metadata.title || 'Generated Research Paper',
          content: formattedContent
        });
        
        setEditorContent({
          title: userInputs.metadata.title || 'Generated Research Paper',
          content: formattedContent
        });
        
        toast.success("Paper loaded in editor. Please log in to save your work.");
      }
      
      // Update UI state to show the editor
      setCurrentStep('editor');
    } catch (error) {
      console.error('Error loading editor:', error);
      toast.error('Failed to save paper. Content loaded in editor without saving.');
      toast.error("An error occurred while loading the editor.");
    }
  };

  // Render the appropriate step based on currentStep
  if (currentStep === 'editor' && editorContent) {
    return (
      <div className="min-h-screen">
        <EnhancedMainEditor 
          initialTitle={editorContent.title} 
          initialContent={editorContent.content}
          initialDocumentId={editorContent.documentId}
        />
      </div>
    );
  }

  // Render saved papers step
  if (currentStep === 'saved') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-between mb-6">
              <Button
                onClick={() => setCurrentStep('input')}
                variant="outline"
                className="flex items-center gap-2"
              >
                ← Back to Generator
              </Button>
              <div className="flex items-center justify-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20"></div>
                  <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                    <Bot className="h-10 w-10 text-white" />
                  </div>
                </div>
              </div>
              <div className="w-32"></div> {/* Spacer for centering */}
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Your Saved Papers
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Access your previously generated research papers and continue working on them.
            </p>
          </div>

          <SavedPapersPanel
            onLoadPaper={loadPaperGeneration}
            onCreateNew={() => {
              // Reset state for new paper
              setCurrentPaperGeneration(null);
              setPaperSections([]);
              setUserInputs({
                metadata: {
                  title: "",
                  researchField: "",
                  keywords: [],
                  authors: []
                },
                userSections: []
              });
              setGeneratedSections(
                SECTION_TYPES
                  .filter(type => type.id !== 'keywords')
                  .sort((a, b) => a.order - b.order)
                  .map(type => ({
                    id: type.id,
                    name: type.name,
                    description: type.description,
                    status: 'pending',
                    icon: type.icon
                  }))
              );
              setAllCitations([]);
              setSectionCitations({});
              setCurrentStep('input');
            }}
          />
        </div>
      </div>
    );
  }

  // Render input step
  if (currentStep === 'input') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex-1"></div>
            <div className="flex items-center justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                  <Bot className="h-10 w-10 text-white" />
                </div>
              </div>
            </div>
            <div className="flex-1 flex justify-end gap-2">
              {user && (
                <Button
                  onClick={() => setCurrentStep('saved')}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Saved Papers
                </Button>
              )}
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            AI Research Paper Generator
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Create your research paper step-by-step with AI assistance for writing, analysis, and enhancement.
          </p>
        </div>

        <StepByStepInput
          userInputs={userInputs}
          updateMetadata={updateMetadata}
          addUserSection={addUserSection}
          removeUserSection={removeUserSection}
          addContentItem={addContentItem}
          updateContentItem={updateContentItem}
          removeContentItem={removeContentItem}
          moveContentItem={moveContentItem}
          analyzingItems={analyzingItems}
          setAnalyzingItems={setAnalyzingItems}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          inputStep={inputStep}
          setInputStep={setInputStep}
          onProceedToGeneration={generateAllSections}
          sectionAnalysis={sectionAnalysis}
          analyzeSectionContent={analyzeSectionContent}
          fixSectionContent={fixSectionContent}
          isFixing={isFixing}
          addFigureWithData={addFigureWithData}
          useEnhancedCitations={useEnhancedCitations}
          setUseEnhancedCitations={setUseEnhancedCitations}
          useAIValidation={useAIValidation}
          setUseAIValidation={setUseAIValidation}
          useRealCitations={useRealCitations}
          setUseRealCitations={setUseRealCitations}
        />
      </div>
    );
  }

  // Render generation step if not in editor mode
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6 flex flex-col">
      <div className="max-w-7xl mx-auto flex-1 flex flex-col">
        {/* Header with navigation */}
        <div className="flex items-center justify-between mb-6 flex-shrink-0">
          <Button
            onClick={() => setCurrentStep('input')}
            variant="outline"
            className="flex items-center gap-2"
          >
            ← Back to Input
          </Button>
          <h1 className="text-2xl font-bold text-gray-800">
            {userInputs.metadata.title || 'Generated Research Paper'}
          </h1>
          {user && (
            <Button
              onClick={() => setCurrentStep('saved')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              Saved Papers
            </Button>
          )}
        </div>

        <div className="text-center mb-8 flex-shrink-0">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-20 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            {isGenerating ? "Generating Your Research Paper" : "Research Paper Generated"}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {isGenerating
              ? "Our AI is carefully crafting each section of your research paper step by step"
              : "All sections of your research paper have been generated successfully"}
          </p>
        </div>

        <div className="flex-1 overflow-hidden">
          <GenerationPanel
            generatedSections={generatedSections}
            isGenerating={isGenerating}
            onExport={handleExport}
            onEditInEditor={handleEditInMainEditor}
            paperMetadata={userInputs.metadata}
            allCitations={allCitations}
            sectionCitations={sectionCitations}
            paperSections={paperSections}
            onRegenerateSection={generateSection}
            enhancedCitations={enhancedCitations}
            useEnhancedDisplay={useEnhancedCitations}
            onNewPaper={() => {
              // Reset state for new paper
              setCurrentPaperGeneration(null);
              setPaperSections([]);
              setUserInputs({
                metadata: {
                  title: "",
                  researchField: "",
                  keywords: [],
                  authors: []
                },
                userSections: []
              });
              setGeneratedSections(
                SECTION_TYPES
                  .filter(type => type.id !== 'keywords')
                  .sort((a, b) => a.order - b.order)
                  .map(type => ({
                    id: type.id,
                    name: type.name,
                    description: type.description,
                    status: 'pending',
                    icon: type.icon
                  }))
              );
              setAllCitations([]);
              setSectionCitations({});
              setEnhancedCitations({});
              setUseAIValidation(true); // Reset to default
              setUseRealCitations(true); // Reset to default
              setCurrentStep('input');

              // Reset any references section
              setGeneratedSections(prev => prev.map(s =>
                s.id === 'references' ? { ...s, status: 'not_started', content: '' } : s
              ));

              toast.success('Ready to create a new paper!');
            }}
          />
        </div>
      </div>
    </div>
  );
}
