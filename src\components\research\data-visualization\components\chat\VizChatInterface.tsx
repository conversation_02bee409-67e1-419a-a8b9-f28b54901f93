import React, { useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  MessageSquare, 
  Trash2, 
  Download,
  RefreshCw,
  AlertTriangle,
  Sparkles
} from 'lucide-react';
import { toast } from 'sonner';

import { VizChatMessage } from './ChatMessage';
import { VizChatInput } from './VizChatInput';
import { useDataVisualizationStore } from '../../stores/data-visualization.store';
import { vegaLiteService } from '../../services/vega-lite.service';
import { getValidVegaSpec } from '../../utils/vega-utils';
import { ChatMessage as ChatMessageType } from '../../types';

export const VizChatInterface: React.FC = () => {
  const {
    currentFile,
    chatMessages,
    isChatGenerating,
    addChatMessage,
    updateChatMessage,
    clearChatMessages,
    setChatGenerating,
    addError
  } = useDataVisualizationStore();

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  const handleSendMessage = async (userMessage: string) => {
    if (!currentFile) {
      toast.error('Please upload a file first');
      return;
    }

    // Add user message
    const userChatMessage: ChatMessageType = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: userMessage,
      timestamp: new Date()
    };
    addChatMessage(userChatMessage);

    // Generate AI response
    setChatGenerating(true);
    
    try {
      const response = await vegaLiteService.generateVisualization({
        messages: chatMessages,
        file: currentFile,
        userPrompt: userMessage
      });

      if (response.success) {
        const vegaSpec = getValidVegaSpec(response.content, currentFile);
        
        const assistantMessage: ChatMessageType = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
          vegaSpec: vegaSpec || undefined,
          error: !vegaSpec ? 'Could not generate a valid visualization from the response' : undefined
        };
        
        addChatMessage(assistantMessage);
        
        if (vegaSpec) {
          toast.success('Visualization generated successfully!');
        } else {
          toast.warning('Generated response but could not create visualization');
        }
      } else {
        const errorMessage: ChatMessageType = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: 'I apologize, but I encountered an error while generating your visualization.',
          timestamp: new Date(),
          error: response.error
        };
        
        addChatMessage(errorMessage);
        addError(response.error || 'Failed to generate visualization');
        toast.error('Failed to generate visualization');
      }
    } catch (error) {
      const errorMessage: ChatMessageType = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an unexpected error.',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      
      addChatMessage(errorMessage);
      addError(error instanceof Error ? error.message : 'Unknown error');
      toast.error('An unexpected error occurred');
    } finally {
      setChatGenerating(false);
    }
  };

  const handleFeedback = (messageId: string, feedback: 'like' | 'dislike') => {
    // TODO: Implement feedback tracking
    toast.success(`Thank you for your ${feedback}!`);
  };

  const handleDeleteMessage = (messageId: string) => {
    // TODO: Implement message deletion
    toast.success('Message deleted');
  };

  const handleCopySpec = (spec: any) => {
    navigator.clipboard.writeText(JSON.stringify(spec, null, 2));
  };

  const handleClearChat = () => {
    clearChatMessages();
    toast.success('Chat cleared');
  };

  const handleGenerateRecommendation = async () => {
    if (!currentFile) {
      toast.error('Please upload a file first');
      return;
    }

    setChatGenerating(true);
    
    try {
      const response = await vegaLiteService.generateChartRecommendation(currentFile);
      
      if (response.success) {
        const vegaSpec = getValidVegaSpec(response.content, currentFile);
        
        const recommendationMessage: ChatMessageType = {
          id: `recommendation-${Date.now()}`,
          role: 'assistant',
          content: response.content,
          timestamp: new Date(),
          vegaSpec: vegaSpec || undefined
        };
        
        addChatMessage(recommendationMessage);
        toast.success('Chart recommendation generated!');
      } else {
        toast.error('Failed to generate recommendation');
      }
    } catch (error) {
      toast.error('An error occurred while generating recommendation');
    } finally {
      setChatGenerating(false);
    }
  };

  if (!currentFile) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center space-y-4">
            <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto" />
            <div>
              <h3 className="text-lg font-medium">No Data File Selected</h3>
              <p className="text-muted-foreground">
                Please upload and select a data file to start creating visualizations.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              <CardTitle className="text-lg">Chat Visualization</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateRecommendation}
                disabled={isChatGenerating}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Suggest Chart
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearChat}
                disabled={chatMessages.length === 0}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Working with: <span className="font-medium">{currentFile.name}</span>
          </p>
        </CardHeader>
      </Card>

      {/* Messages */}
      <Card className="flex-1 flex flex-col min-h-0">
        <CardContent className="flex-1 p-0">
          <div className="h-full overflow-y-auto">
            {chatMessages.length === 0 ? (
              <div className="flex items-center justify-center h-full p-8">
                <div className="text-center space-y-4">
                  <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto" />
                  <div>
                    <h3 className="text-lg font-medium">Start a Conversation</h3>
                    <p className="text-muted-foreground">
                      Ask me to create any visualization from your data!
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-0">
                {chatMessages.map((message, index) => (
                  <div key={message.id}>
                    <VizChatMessage
                      message={message}
                      file={currentFile}
                      onFeedback={handleFeedback}
                      onDelete={handleDeleteMessage}
                      onCopySpec={handleCopySpec}
                    />
                    {index < chatMessages.length - 1 && <Separator />}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Input */}
      <Card>
        <CardContent className="p-4">
          <VizChatInput
            onSendMessage={handleSendMessage}
            isGenerating={isChatGenerating}
            disabled={!currentFile}
          />
        </CardContent>
      </Card>
    </div>
  );
};
