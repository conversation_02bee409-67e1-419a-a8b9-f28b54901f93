import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Network } from "lucide-react";

import { ResearchDocument, CitationNetwork } from '../types';

interface CitationNetworkViewerProps {
  documents: ResearchDocument[];
  onGenerate: (network: CitationNetwork) => void;
}

export function CitationNetworkViewer({ documents }: CitationNetworkViewerProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Network className="h-6 w-6 text-pink-500" />
          Citation Network Viewer
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Citation network visualization coming soon!</p>
          <Badge variant="secondary">
            {documents.length} documents selected
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
