import { BookMetadata } from '../types';

export interface ChapterEstimation {
  recommendedChapters: number;
  estimatedWordsPerChapter: number;
  totalEstimatedWords: number;
  estimatedPages: number;
  reasoning: string;
  suggestedChapterTitles: string[];
}

export interface BookComplexity {
  level: 'simple' | 'moderate' | 'complex' | 'advanced';
  score: number;
  factors: string[];
}

/**
 * Service for intelligent chapter estimation and book structure planning
 */
export class SmartChapterService {
  
  /**
   * Estimate optimal number of chapters based on book metadata
   */
  estimateChapters(metadata: BookMetadata): ChapterEstimation {
    const complexity = this.analyzeBookComplexity(metadata);
    const targetLength = this.getTargetWordCount(metadata.estimatedLength);
    
    // Base chapter estimation on complexity and length
    let recommendedChapters = this.calculateOptimalChapters(targetLength, complexity);
    
    // Adjust based on genre
    recommendedChapters = this.adjustForGenre(recommendedChapters, metadata.genre);
    
    // Adjust based on audience
    recommendedChapters = this.adjustForAudience(recommendedChapters, metadata.targetAudience);
    
    const estimatedWordsPerChapter = Math.round(targetLength / recommendedChapters);
    const estimatedPages = Math.ceil(targetLength / 300); // ~300 words per page
    
    const reasoning = this.generateReasoning(
      metadata, 
      complexity, 
      recommendedChapters, 
      targetLength
    );
    
    const suggestedChapterTitles = this.generateChapterTitles(
      metadata, 
      recommendedChapters, 
      complexity
    );
    
    return {
      recommendedChapters,
      estimatedWordsPerChapter,
      totalEstimatedWords: targetLength,
      estimatedPages,
      reasoning,
      suggestedChapterTitles
    };
  }

  /**
   * Analyze book complexity based on metadata
   */
  private analyzeBookComplexity(metadata: BookMetadata): BookComplexity {
    let score = 0;
    const factors: string[] = [];

    // Genre complexity
    const complexGenres = ['Academic', 'Technology', 'Science', 'Philosophy', 'Medical'];
    const moderateGenres = ['Business', 'History', 'Biography', 'Self-Help'];
    
    if (complexGenres.includes(metadata.genre)) {
      score += 3;
      factors.push('Complex technical subject matter');
    } else if (moderateGenres.includes(metadata.genre)) {
      score += 2;
      factors.push('Moderate subject complexity');
    } else {
      score += 1;
      factors.push('Accessible subject matter');
    }

    // Audience complexity
    const expertAudiences = ['Researchers', 'Professionals', 'Graduate Students'];
    const intermediateAudiences = ['Students', 'Practitioners', 'Enthusiasts'];
    
    if (expertAudiences.includes(metadata.targetAudience)) {
      score += 3;
      factors.push('Expert-level audience');
    } else if (intermediateAudiences.includes(metadata.targetAudience)) {
      score += 2;
      factors.push('Intermediate audience');
    } else {
      score += 1;
      factors.push('General audience');
    }

    // Keyword complexity
    const technicalKeywords = metadata.keywords.filter(keyword => 
      /\b(algorithm|methodology|framework|implementation|analysis|optimization|architecture|protocol|paradigm)\b/i.test(keyword)
    );
    
    if (technicalKeywords.length >= 3) {
      score += 2;
      factors.push('Technical terminology present');
    } else if (technicalKeywords.length >= 1) {
      score += 1;
      factors.push('Some technical concepts');
    }

    // Description complexity
    const descriptionLength = metadata.description.length;
    const technicalTermsInDesc = (metadata.description.match(/\b(methodology|framework|implementation|analysis|system|process|technique|approach)\b/gi) || []).length;
    
    if (descriptionLength > 500 && technicalTermsInDesc >= 3) {
      score += 2;
      factors.push('Detailed technical description');
    } else if (descriptionLength > 200) {
      score += 1;
      factors.push('Well-detailed description');
    }

    // Determine complexity level
    let level: BookComplexity['level'];
    if (score >= 8) {
      level = 'advanced';
    } else if (score >= 6) {
      level = 'complex';
    } else if (score >= 4) {
      level = 'moderate';
    } else {
      level = 'simple';
    }

    return { level, score, factors };
  }

  /**
   * Get target word count based on estimated length
   */
  private getTargetWordCount(estimatedLength: string): number {
    switch (estimatedLength) {
      case 'short': return 25000; // ~80 pages, 10 chapters
      case 'medium': return 60000; // ~200 pages, 15 chapters
      case 'long': return 100000; // ~330 pages, 20 chapters
      case 'very-long': return 150000; // ~500 pages, 25 chapters
      default: return 60000;
    }
  }

  /**
   * Get default chapter count based on book length
   */
  getDefaultChapterCount(estimatedLength: string): number {
    switch (estimatedLength) {
      case 'short': return 10;
      case 'medium': return 15;
      case 'long': return 20;
      case 'very-long': return 25;
      default: return 15;
    }
  }

  /**
   * Calculate optimal chapters based on word count and complexity
   */
  private calculateOptimalChapters(wordCount: number, complexity: BookComplexity): number {
    // Base calculation: aim for 4000-6000 words per chapter for longer content
    let baseChapters = Math.round(wordCount / 5000);

    // Adjust for complexity
    switch (complexity.level) {
      case 'advanced':
        // More chapters for better organization of complex content
        baseChapters = Math.round(wordCount / 4500);
        break;
      case 'complex':
        baseChapters = Math.round(wordCount / 4800);
        break;
      case 'moderate':
        baseChapters = Math.round(wordCount / 5200);
        break;
      case 'simple':
        // Fewer, longer chapters for simpler content
        baseChapters = Math.round(wordCount / 5800);
        break;
    }

    // Ensure reasonable bounds (minimum 8, maximum 30 for longer books)
    return Math.max(8, Math.min(30, baseChapters));
  }

  /**
   * Adjust chapter count based on genre
   */
  private adjustForGenre(chapters: number, genre: string): number {
    const adjustments: Record<string, number> = {
      'Academic': 1.2, // More chapters for better organization
      'Technology': 1.15,
      'Science': 1.1,
      'Fiction': 0.9, // Fewer, longer chapters for narrative flow
      'Biography': 0.95,
      'Self-Help': 1.1, // More actionable chapters
      'Business': 1.05
    };

    const multiplier = adjustments[genre] || 1;
    return Math.round(chapters * multiplier);
  }

  /**
   * Adjust chapter count based on target audience
   */
  private adjustForAudience(chapters: number, audience: string): number {
    const adjustments: Record<string, number> = {
      'Beginners': 1.1, // More chapters for step-by-step learning
      'Students': 1.05,
      'General Public': 1.0,
      'Professionals': 0.95,
      'Researchers': 0.9, // Fewer, more comprehensive chapters
      'Experts': 0.9
    };

    const multiplier = adjustments[audience] || 1;
    return Math.round(chapters * multiplier);
  }

  /**
   * Generate reasoning for chapter recommendation
   */
  private generateReasoning(
    metadata: BookMetadata, 
    complexity: BookComplexity, 
    chapters: number, 
    wordCount: number
  ): string {
    const wordsPerChapter = Math.round(wordCount / chapters);
    const pages = Math.ceil(wordCount / 300);
    
    return `Based on your ${metadata.genre.toLowerCase()} book for ${metadata.targetAudience.toLowerCase()}, I recommend ${chapters} chapters (~${wordsPerChapter} words each, ${pages} total pages). This structure provides optimal organization for ${complexity.level} content while maintaining reader engagement. Key factors: ${complexity.factors.slice(0, 2).join(', ')}.`;
  }

  /**
   * Generate suggested chapter titles based on book topic
   */
  private generateChapterTitles(
    metadata: BookMetadata, 
    chapterCount: number, 
    complexity: BookComplexity
  ): string[] {
    const titles: string[] = [];
    const topic = metadata.title.toLowerCase();
    const keywords = metadata.keywords;
    
    // Generate titles based on genre and complexity
    if (metadata.genre === 'Technology' || metadata.genre === 'Academic') {
      titles.push(
        `Introduction to ${metadata.title}`,
        'Fundamental Concepts and Principles',
        'Core Methodologies and Approaches',
        'Advanced Techniques and Implementation',
        'Practical Applications and Case Studies',
        'Best Practices and Optimization',
        'Troubleshooting and Problem Solving',
        'Future Trends and Developments',
        'Conclusion and Next Steps'
      );
    } else if (metadata.genre === 'Business') {
      titles.push(
        `Understanding ${metadata.title}`,
        'Strategic Foundation and Planning',
        'Implementation Strategies',
        'Operational Excellence',
        'Performance Measurement and Analytics',
        'Risk Management and Mitigation',
        'Innovation and Growth',
        'Leadership and Team Management',
        'Future Outlook and Recommendations'
      );
    } else if (metadata.genre === 'Self-Help') {
      titles.push(
        `Getting Started with ${metadata.title}`,
        'Building Your Foundation',
        'Developing Key Skills',
        'Overcoming Common Challenges',
        'Advanced Strategies and Techniques',
        'Creating Sustainable Habits',
        'Measuring Progress and Success',
        'Maintaining Long-term Growth',
        'Your Journey Forward'
      );
    } else {
      // Generic structure
      titles.push(
        `Introduction to ${metadata.title}`,
        'Background and Context',
        'Core Concepts and Ideas',
        'Detailed Analysis and Exploration',
        'Practical Applications',
        'Advanced Topics and Considerations',
        'Real-world Examples and Case Studies',
        'Implications and Impact',
        'Conclusions and Future Directions'
      );
    }

    // Customize with keywords if available
    if (keywords.length > 0) {
      const keywordTitles = keywords.slice(0, Math.min(3, chapterCount - 3)).map(keyword => 
        `Deep Dive: ${keyword.charAt(0).toUpperCase() + keyword.slice(1)}`
      );
      titles.splice(3, keywordTitles.length, ...keywordTitles);
    }

    // Return the appropriate number of titles
    return titles.slice(0, chapterCount);
  }

  /**
   * Validate user's manual chapter input
   */
  validateChapterStructure(chapters: any[], metadata: BookMetadata): {
    isValid: boolean;
    warnings: string[];
    suggestions: string[];
  } {
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    // Check chapter count
    const estimation = this.estimateChapters(metadata);
    const difference = Math.abs(chapters.length - estimation.recommendedChapters);
    
    if (difference > 3) {
      warnings.push(`You have ${chapters.length} chapters, but ${estimation.recommendedChapters} is recommended for optimal structure.`);
      suggestions.push(`Consider ${chapters.length > estimation.recommendedChapters ? 'combining some chapters' : 'splitting complex chapters'} for better reader experience.`);
    }

    // Check for very short or very long chapters
    const avgWordsPerChapter = estimation.totalEstimatedWords / chapters.length;
    if (avgWordsPerChapter < 2000) {
      warnings.push('Chapters may be too short for comprehensive coverage.');
      suggestions.push('Consider combining related chapters or adding more detailed content.');
    } else if (avgWordsPerChapter > 6000) {
      warnings.push('Chapters may be too long for reader engagement.');
      suggestions.push('Consider breaking down complex chapters into smaller, focused sections.');
    }

    return {
      isValid: warnings.length === 0,
      warnings,
      suggestions
    };
  }
}

export const smartChapterService = new SmartChapterService();
