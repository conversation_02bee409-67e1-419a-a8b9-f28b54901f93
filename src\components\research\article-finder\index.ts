/**
 * Article Finder Module Index
 * Main export file for the Article Finder module
 */

// Main component
export { ArticleFinder } from './ArticleFinder';

// Types
export type {
  ArticleMetadata,
  JournalRecommendation,
  JournalMetrics,
  ArticleAnalysis,
  ArticleContent,
  ArticleFinderState,
  JournalRanking,
  JournalCategory,
  AIModelOption,
  ArticleFinderRequest,
  ArticleFinderResponse,
  ExportOptions,
  ExportData,
  ArticleSubmission,
  ArticleFinderProps,
  ArticleInputFormProps,
  JournalRecommendationCardProps,
  JournalRankingSystemProps,
  ArticleAnalysisDisplayProps,
  ExportDialogProps,
  HistoryPanelProps,
  RankingCriteria,
  JournalField,
  ExportFormat,
  InputType,
  AnalysisDepth
} from './types';

// Components
export {
  ArticleInputForm,
  JournalRecommendationCard,
  JournalRankingSystem,
  ArticleAnalysisDisplay,
  ExportDialog,
  HistoryPanel
} from './components';

// Services
export { articleFinderAIService } from './services/article-finder-ai.service';
export { journalSearchService } from './services/journal-search.service';
export { openRouterArticleService } from './services/openrouter-article.service';

// Store
export { useArticleFinderStore } from './stores/article-finder.store';

// Constants
export {
  AI_MODELS,
  JOURNAL_CATEGORIES,
  RANKING_CRITERIA,
  INPUT_TYPES,
  ANALYSIS_DEPTHS,
  EXPORT_FORMATS,
  DEFAULT_SEARCH_OPTIONS,
  JOURNAL_METRICS,
  UI_CONSTANTS,
  JOURNAL_COLORS,
  FIELD_CATEGORIES
} from './constants';
