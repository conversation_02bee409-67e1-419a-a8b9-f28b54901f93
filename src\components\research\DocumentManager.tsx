import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  FileText,
  Plus,
  Search,
  Clock,
  Star,
  MoreHorizontal,
  Trash2,
  Edit,
  Copy,
  Download,
  Import,
  FolderOpen,
  BookOpen,
  Brain,
  FileCheck,
  Sparkles,
  Upload
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { documentService } from '@/services/documentService';
import { useAuth } from '@/contexts/AuthContext';
import { Database } from '@/lib/database.types';

type Document = Database['public']['Tables']['user_documents']['Row'];

interface DocumentManagerProps {
  onOpenDocument: (document: Document) => void;
  onNewDocument: () => void;
  onImportDocument: () => void;
  selectedDocumentId?: string;
}

const DOCUMENT_TYPES = [
  { id: 'paper', label: 'Research Paper', icon: FileText, color: 'bg-blue-500' },
  { id: 'book', label: 'Book', icon: BookOpen, color: 'bg-green-500' },
  { id: 'article_review', label: 'Article Review', icon: FileCheck, color: 'bg-purple-500' },
  { id: 'research_analysis', label: 'Research Analysis', icon: Brain, color: 'bg-orange-500' },
];

export function DocumentManager({ 
  onOpenDocument, 
  onNewDocument, 
  onImportDocument,
  selectedDocumentId 
}: DocumentManagerProps) {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewDocDialog, setShowNewDocDialog] = useState(false);
  const [newDocTitle, setNewDocTitle] = useState('');
  const [newDocType, setNewDocType] = useState('paper');

  // Load documents
  useEffect(() => {
    if (user) {
      loadDocuments();
    }
  }, [user]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const docs = await documentService.getUserDocuments(20);
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.document_type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateDocument = async () => {
    if (!newDocTitle.trim()) {
      toast.error('Please enter a document title');
      return;
    }

    try {
      const newDoc = await documentService.createDocument({
        title: newDocTitle,
        content: '',
        document_type: newDocType as Document['document_type'],
        status: 'draft',
        metadata: {
          createdVia: 'document_manager'
        }
      });

      if (newDoc) {
        setDocuments(prev => [newDoc, ...prev]);
        onOpenDocument(newDoc);
        setShowNewDocDialog(false);
        setNewDocTitle('');
        toast.success('Document created successfully!');
      }
    } catch (error) {
      console.error('Error creating document:', error);
      toast.error('Failed to create document');
    }
  };

  const handleDeleteDocument = async (docId: string) => {
    try {
      const success = await documentService.deleteDocument(docId);
      if (success) {
        setDocuments(prev => prev.filter(doc => doc.id !== docId));
        toast.success('Document deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    }
  };

  const handleDuplicateDocument = async (docId: string) => {
    try {
      const duplicated = await documentService.duplicateDocument(docId);
      if (duplicated) {
        setDocuments(prev => [duplicated, ...prev]);
        toast.success('Document duplicated successfully');
      }
    } catch (error) {
      console.error('Error duplicating document:', error);
      toast.error('Failed to duplicate document');
    }
  };

  const getDocumentTypeInfo = (type: string) => {
    return DOCUMENT_TYPES.find(t => t.id === type) || DOCUMENT_TYPES[0];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <div className="p-6 text-center">
        <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-600">Please log in to access your documents</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b bg-white">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Documents</h2>
          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={onImportDocument}
              variant="outline"
            >
              <Import className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Dialog open={showNewDocDialog} onOpenChange={setShowNewDocDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Document</DialogTitle>
                  <DialogDescription>
                    Start a new document for your research and writing.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Document Title</label>
                    <Input
                      value={newDocTitle}
                      onChange={(e) => setNewDocTitle(e.target.value)}
                      placeholder="Enter document title..."
                      onKeyDown={(e) => e.key === 'Enter' && handleCreateDocument()}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Document Type</label>
                    <div className="grid grid-cols-2 gap-2">
                      {DOCUMENT_TYPES.map((type) => (
                        <Button
                          key={type.id}
                          variant={newDocType === type.id ? "default" : "outline"}
                          onClick={() => setNewDocType(type.id)}
                          className="justify-start h-auto p-3"
                        >
                          <type.icon className="h-4 w-4 mr-2" />
                          <span className="text-xs">{type.label}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowNewDocDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateDocument}>
                    Create Document
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Document List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {loading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-8">
              {searchQuery ? (
                <>
                  <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">No documents found matching "{searchQuery}"</p>
                </>
              ) : (
                <>
                  <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-2">No documents yet</p>
                  <p className="text-sm text-gray-500">Create your first document to get started!</p>
                </>
              )}
            </div>
          ) : (
            filteredDocuments.map((doc) => {
              const typeInfo = getDocumentTypeInfo(doc.document_type);
              const isSelected = selectedDocumentId === doc.id;
              
              return (
                <Card 
                  key={doc.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => onOpenDocument(doc)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <div className={`p-1 rounded ${typeInfo.color} text-white`}>
                            <typeInfo.icon className="h-3 w-3" />
                          </div>
                          <h3 className="font-medium text-gray-900 truncate">
                            {doc.title}
                          </h3>
                          <Badge 
                            variant="secondary" 
                            className={getStatusColor(doc.status)}
                          >
                            {doc.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <typeInfo.icon className="h-3 w-3" />
                            {typeInfo.label}
                          </span>
                          <span>{doc.word_count || 0} words</span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(doc.updated_at || doc.created_at)}
                          </span>
                        </div>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            onOpenDocument(doc);
                          }}>
                            <Edit className="h-4 w-4 mr-2" />
                            Open
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleDuplicateDocument(doc.id);
                          }}>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm('Are you sure you want to delete this document?')) {
                                handleDeleteDocument(doc.id);
                              }
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </ScrollArea>

      {/* Quick Actions Footer */}
      <div className="p-4 border-t bg-gray-50">
        <div className="grid grid-cols-2 gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onNewDocument('paper')}
            className="justify-start"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            AI Paper
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onNewDocument('article')}
            className="justify-start"
          >
            <Brain className="h-4 w-4 mr-2" />
            AI Article
          </Button>
        </div>
      </div>
    </div>
  );
}


