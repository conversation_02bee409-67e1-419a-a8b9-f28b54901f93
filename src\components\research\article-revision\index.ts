/**
 * Article Revision System - Main Export
 */

// Main component
export { ArticleRevisionSystem } from './ArticleRevisionSystem';

// Types
export * from './types';

// Components
export { ArticleRevisionUploader } from './components/ArticleRevisionUploader';
export { RevisionWorkflow } from './components/RevisionWorkflow';
export { RevisionResults } from './components/RevisionResults';

// Services
export { DocumentParserService } from './services/document-parser.service';
export { AIOrchestrator } from './services/ai-orchestrator.service';
export { ExportService } from './services/export.service';

// Store
export { useRevisionWorkflowStore } from './stores/revision-workflow.store';

// Constants
export * from './constants';
