import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from '@/components/ui/badge';
import { Lightbulb } from 'lucide-react';
import { AIModelOption } from '../types';

interface AIModelSelectorProps {
  selectedModel: string;
  setSelectedModel: (modelId: string) => void;
  availableModels: AIModelOption[];
  isDisabled?: boolean;
}

export function AIModelSelector({
  selectedModel,
  setSelectedModel,
  availableModels,
  isDisabled = false
}: AIModelSelectorProps) {
  const [expanded, setExpanded] = useState(false);
  
  // Group models by provider for organization
  const groupedModels = availableModels.reduce((acc, model) => {
    const provider = model.provider;
    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(model);
    return acc;
  }, {} as Record<string, AIModelOption[]>);
  
  // Provider display names
  const providerNames: Record<string, string> = {
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google',
    openrouter: 'OpenRouter'
  };
  
  // Models to display (either all or just the default selection)
  const modelsToDisplay = expanded
    ? availableModels
    : availableModels.filter(m => 
        m.id === selectedModel || 
        m.id === 'google/gemini-2.5-flash' ||
        m.provider === 'google');
  
  return (
    <Card className="shadow-md border-gray-200">
      <CardHeader className="pb-2 bg-gradient-to-r from-gray-50 to-gray-100 border-b">
        <CardTitle className="text-base font-medium flex items-center">
          <div className="bg-blue-100 p-1 rounded-md mr-2">
            <Lightbulb className="h-4 w-4 text-blue-600" />
          </div>
          AI Model Selection
        </CardTitle>
        <CardDescription>
          Choose which AI model will review your paper
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-4">
        <RadioGroup 
          value={selectedModel} 
          onValueChange={setSelectedModel}
          className="space-y-4"
          disabled={isDisabled}
        >
          {modelsToDisplay.map((model) => {
            const isSelected = model.id === selectedModel;
            
            // Define provider colors and icons
            const providerColor = model.provider === 'google' 
              ? 'bg-blue-50 border-blue-200' 
              : model.provider === 'anthropic' 
                ? 'bg-purple-50 border-purple-200' 
                : model.provider === 'openai' 
                  ? 'bg-green-50 border-green-200'
                  : 'bg-gray-50 border-gray-200';
            
            return (
              <div 
                key={model.id} 
                className={`flex items-start space-x-3 p-3 rounded-lg transition-all ${
                  isSelected 
                    ? `${providerColor} shadow-sm border` 
                    : 'hover:bg-gray-50 border border-transparent'
                }`}
              >
                <RadioGroupItem 
                  value={model.id} 
                  id={model.id} 
                  disabled={isDisabled || !model.isAvailable}
                  className="mt-1"
                />
                <div className="flex-1 grid gap-1">
                  <Label 
                    htmlFor={model.id} 
                    className="flex items-center gap-2 font-medium text-gray-900"
                  >
                    {model.name}
                    {model.id === 'google/gemini-2.5-flash' && (
                      <Badge 
                        variant="outline" 
                        className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                      >
                        Default
                      </Badge>
                    )}
                  </Label>
                  {model.description && (
                    <div className="text-sm text-gray-600 leading-relaxed">
                      {model.description}
                      {model.contextSize && (
                        <div className="flex items-center mt-1.5 text-xs text-gray-500">
                          <Badge 
                            variant="outline" 
                            className="font-normal bg-gray-50 border-gray-200"
                          >
                            {Math.round(model.contextSize / 1000)}k tokens context
                          </Badge>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </RadioGroup>
        
        {availableModels.length > modelsToDisplay.length && (
          <button 
            onClick={() => setExpanded(!expanded)}
            className="text-xs text-blue-600 mt-3 hover:underline"
            disabled={isDisabled}
          >
            {expanded ? 'Show fewer models' : 'Show all models'}
          </button>
        )}
        
        <div className="mt-4 pt-4 border-t text-xs text-gray-500">
          <p>
            Using OpenRouter API to access models from different providers.
            Model availability and performance may vary.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
