/**
 * Research Comprehension Store
 * Zustand store for managing research document learning state
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase } from '@/lib/supabase';
import {
  ResearchComprehensionStoreState,
  TutorSession,
  TutorMessage,
  EducationLevel,
  ResearchComprehensionSettings,
  ResearchDocument,
  LearningProgress
} from '../types';
import { documentService } from '../services/document.service';
import { ragService } from '../services/rag.service';
import { quizGeneratorService } from '../services/quiz-generator.service';
import { educationalGamesService } from '../services/educational-games.service';

import { toast } from 'sonner';

export const useTutorStore = create<ResearchComprehensionStoreState>()(
  persist(
    (set, get) => ({
      // Current session and documents
      currentDocument: null,
      uploadedDocuments: [],
      currentSession: null,
      
      // UI state
      currentView: 'library' as const,
      isLoading: false,
      isProcessingDocument: false,
      error: null,
      
      // Settings
      settings: {
        preferredModel: 'gemini-2.5-flash', // Changed default to flash
        educationLevel: 'intermediate', // Changed to research-focused level
        autoGenerateQuizzes: true,
        autoGenerateGames: true,
        chunkSize: 1000,
        quizDifficulty: 'intermediate',
        gameDifficulty: 3,
        showHints: true,
        allowRetries: true,
        maxSearchResults: 10,
        searchTimeout: 30,
        ragThreshold: 0.7,
        trackDetailedAnalytics: true,
        shareProgressWithInstructors: false,
        theme: 'light',
        showProgressIndicators: true,
        enableNotifications: true,
        autoSaveInterval: 5
      },
      
      // Learning state
      learningProgress: [],
      currentQuiz: null,
      currentGame: null,
      
      // RAG state
      ragResults: [],
      isSearching: false,

      // Actions
      uploadDocument: async (file: File): Promise<ResearchDocument> => {
        set({ isProcessingDocument: true, error: null });

        try {
          if (!supabase) {
            throw new Error('Supabase client not initialized');
          }

          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          const document = await documentService.uploadDocument(file, user.id, {
            onProgress: (progress) => {
              // Progress updates are handled by the service
            }
          });

          // Add to uploaded documents
          const currentDocuments = get().uploadedDocuments;
          set({ 
            uploadedDocuments: [...currentDocuments, document],
            isProcessingDocument: false 
          });

          // Process document for RAG
          try {
            await ragService.processDocumentForRAG(document);
          } catch (ragError) {
            console.warn('RAG processing failed:', ragError);
            // Continue without RAG functionality
          }

          return document;
        } catch (error: any) {
          set({ error: error.message, isProcessingDocument: false });
          throw error;
        }
      },

      selectDocument: (documentId: string) => {
        const document = get().uploadedDocuments.find(d => d.id === documentId);
        if (document) {
          set({ currentDocument: document });
        }
      },

      startStudySession: async (documentId: string) => {
        const document = get().uploadedDocuments.find(d => d.id === documentId);
        if (!document) {
          throw new Error('Document not found');
        }

        set({ 
          currentDocument: document,
          currentView: 'study',
          isLoading: true 
        });

        try {
          // Initialize study session
          if (!supabase) {
            throw new Error('Supabase client not initialized');
          }

          const { data: { user } } = await supabase.auth.getUser();
          if (user) {
            // Create or update learning progress
            const progress = await documentService.getLearningProgress(user.id, documentId);
            if (progress) {
              const currentProgress = get().learningProgress;
              const updatedProgress = currentProgress.filter(p => p.documentId !== documentId);
              set({ learningProgress: [...updatedProgress, progress] });
            }
          }

          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      endStudySession: () => {
        set({ 
          currentSession: null,
          currentView: 'library' 
        });
      },

      generateQuiz: async (documentId: string, options: any): Promise<any> => {
        const document = get().uploadedDocuments.find(d => d.id === documentId);
        if (!document) {
          throw new Error('Document not found');
        }

        set({ isLoading: true, error: null });

        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          const quiz = await quizGeneratorService.generateQuiz(document, user.id, options);
          set({ currentQuiz: quiz, isLoading: false });
          return quiz;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      generateGame: async (documentId: string, gameType: any): Promise<any> => {
        const document = get().uploadedDocuments.find(d => d.id === documentId);
        if (!document) {
          throw new Error('Document not found');
        }

        set({ isLoading: true, error: null });

        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('User not authenticated');
          }

          const game = await educationalGamesService.generateGame(document, gameType, user.id);
          set({ currentGame: game, isLoading: false });
          return game;
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          throw error;
        }
      },

      performRAGQuery: async (query: any): Promise<any> => {
        set({ isSearching: true, error: null });

        try {
          const response = await ragService.performRAGQuery(query);
          set({ ragResults: response.results, isSearching: false });
          return response;
        } catch (error: any) {
          set({ error: error.message, isSearching: false });
          throw error;
        }
      },

      searchOnline: async (query: any): Promise<any[]> => {
        set({ isSearching: true, error: null });

        try {
          // TODO: Implement Tavily search integration
          const results: any[] = [];
          set({ isSearching: false });
          return results;
        } catch (error: any) {
          set({ error: error.message, isSearching: false });
          throw error;
        }
      },

      updateProgress: (progress: Partial<LearningProgress>) => {
        const currentProgress = get().learningProgress;
        const existingIndex = currentProgress.findIndex(p => p.documentId === progress.documentId);
        
        if (existingIndex >= 0) {
          const updatedProgress = [...currentProgress];
          updatedProgress[existingIndex] = { ...updatedProgress[existingIndex], ...progress };
          set({ learningProgress: updatedProgress });
        }
      },

      updateSettings: (newSettings: Partial<ResearchComprehensionSettings>) => {
        const currentSettings = get().settings;
        set({ settings: { ...currentSettings, ...newSettings } });
      },

      setCurrentView: (view: ResearchComprehensionStoreState['currentView']) => {
        set({ currentView: view });
      },

      clearError: () => {
        set({ error: null });
      },

      // Legacy actions for backward compatibility
      startSession: async (topic: string, educationLevel: EducationLevel) => {
        // For backward compatibility, redirect to document library
        console.log('Legacy startSession called, redirecting to document library');
        set({ currentView: 'library', error: null });
      }
    }),
    {
      name: 'research-comprehension-store',
      partialize: (state) => ({
        settings: state.settings,
        uploadedDocuments: state.uploadedDocuments.slice(0, 20), // Keep only last 20 documents
        learningProgress: state.learningProgress
      })
    }
  )
);
