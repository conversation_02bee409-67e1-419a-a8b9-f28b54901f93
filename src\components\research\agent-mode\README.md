# Agent Mode - Contextual Document Editing

Agent Mode is an advanced AI-powered feature that enables contextual document editing through natural language prompts. It analyzes your document structure, identifies relevant sections, and makes targeted improvements while preserving the overall document integrity.

## Features

### 🎯 Contextual Section Identification
- Automatically identifies document sections (introduction, methodology, results, etc.)
- Maps natural language prompts to specific document parts
- Supports academic papers, articles, reports, and general documents

### 🧠 Intelligent Editing
- Makes surgical edits to specific sections rather than rewriting entire documents
- Preserves document structure and formatting
- Provides confidence scores for section matching

### 📊 Change Tracking Integration
- Full integration with existing change tracking system
- Batch change recording for multiple simultaneous edits
- Granular accept/reject functionality for individual changes

### 🛡️ Comprehensive Error Handling
- Input validation for prompts and documents
- Helpful suggestions for improving prompt quality
- Fallback options for ambiguous requests

## Usage

### Basic Usage

1. **Open Agent Mode**: Click the "Agent" tab in the AI sidebar
2. **Enter Your Prompt**: Describe what you want to edit (e.g., "Improve the introduction for better clarity")
3. **Select Edit Mode**: Choose between Conservative, Moderate, or Aggressive editing
4. **Analyze & Edit**: Click the button to process your request
5. **Review Changes**: Use the change tracking system to accept or reject edits

### Example Prompts

**Good Prompts:**
- "Improve the introduction for better clarity and engagement"
- "Enhance the methodology section with more detailed explanations"
- "Make the conclusion more compelling and impactful"
- "Revise the discussion section for improved flow and analysis"
- "Clarify the results presentation for better understanding"

**Avoid These Prompts:**
- "Fix it" (too vague)
- "Rewrite everything" (too broad)
- "Delete all content" (destructive)

### Edit Modes

- **Conservative**: Minimal changes, preserve original style
- **Moderate**: Balanced improvements with structural preservation
- **Aggressive**: Substantial improvements, restructure as needed

## Architecture

### Core Components

#### `AgentModeService`
Main service class that orchestrates the entire Agent Mode workflow:
- Document analysis and structure identification
- Prompt analysis and intent recognition
- Section matching and confidence scoring
- AI-powered edit generation
- Error handling and validation

#### `DocumentAnalyzer`
Utility class for analyzing document structure:
- Parses HTML content to identify headings, paragraphs, and sections
- Classifies section types (introduction, methodology, results, etc.)
- Determines document type (academic, article, report, general)
- Provides position mapping for precise editing

#### `AgentModePanel`
React component providing the user interface:
- Prompt input with validation and suggestions
- Edit mode selection
- Progress tracking during processing
- Results display with section identification
- Error handling and user feedback

#### `utils.ts`
Helper functions for validation and error handling:
- Prompt validation and quality analysis
- Document content validation
- Error message generation with suggestions
- Processing time formatting

### Data Flow

1. **Input Validation**: Validate prompt and document content
2. **Document Analysis**: Parse structure and identify sections
3. **Prompt Analysis**: Understand user intent and target sections
4. **Section Matching**: Find relevant sections with confidence scores
5. **Edit Generation**: Use AI to generate targeted improvements
6. **Change Application**: Apply edits with change tracking integration
7. **User Review**: Present results for acceptance or rejection

## Integration

### With EnhancedMainEditor

Agent Mode is fully integrated with the main editor:

```typescript
// Add Agent Mode state
const [agentModeLoading, setAgentModeLoading] = useState(false);
const [lastAgentModeResult, setLastAgentModeResult] = useState<AgentModeResult | null>(null);

// Handle Agent Mode requests
const handleAgentModeRequest = async (request: AgentModeRequest) => {
  // Process request and apply changes
};

// Pass to sidebar
<EditorAISidebar
  onAgentModeRequest={handleAgentModeRequest}
  agentModeLoading={agentModeLoading}
  lastAgentModeResult={lastAgentModeResult}
  // ... other props
/>
```

### With Change Tracking

Agent Mode leverages the existing change tracking system:

```typescript
// Record batch changes
const changeIds = changeTracking.recordBatchChanges(edits.map(edit => ({
  originalText: edit.originalText,
  newText: edit.newText,
  startPosition: edit.startPosition,
  endPosition: edit.endPosition,
  aiActionType: 'replace',
  prompt: edit.prompt
})));
```

## API Reference

### Types

```typescript
interface AgentModeRequest {
  prompt: string;
  documentContent: string;
  model: string;
  options: AgentModeOptions;
}

interface AgentModeResult {
  success: boolean;
  targetSections: TargetSection[];
  edits: EditInstruction[];
  summary: string;
  processingTime: number;
  tokensUsed?: number;
  error?: string;
  warnings?: string[];
}

interface TargetSection {
  section: Section;
  confidence: number;
  reason: string;
  matchedKeywords: string[];
  relevanceScore: number;
}
```

### Methods

```typescript
// Execute Agent Mode request
const result = await agentModeService.executeAgentModeRequest(request, onProgress);

// Analyze document structure
const structure = documentAnalyzer.analyzeDocument(htmlContent);

// Validate prompt
const validation = validatePrompt(prompt);

// Analyze prompt quality
const quality = analyzePromptQuality(prompt);
```

## Testing

Run the test suite to verify functionality:

```typescript
import { runAgentModeTests } from './test-agent-mode';

// Run all tests
await runAgentModeTests();

// Or run individual test categories
testDocumentAnalysis();
testPromptValidation();
testDocumentValidation();
```

## Error Handling

Agent Mode includes comprehensive error handling:

- **Input Validation**: Checks prompt and document validity
- **Section Identification**: Handles cases where no sections match
- **AI Service Errors**: Graceful handling of API failures
- **Edit Application**: Fallback for edit application failures

Each error includes helpful suggestions for resolution.

## Performance Considerations

- Document analysis is cached to avoid repeated processing
- Token usage is estimated and tracked
- Large documents are handled with progressive enhancement
- Batch operations minimize API calls

## Future Enhancements

- Real-time section highlighting during analysis
- Support for more document formats (Markdown, LaTeX)
- Advanced section relationship analysis
- Integration with document templates
- Collaborative editing support

## Troubleshooting

### Common Issues

1. **No sections found**: Make your prompt more specific about which part to edit
2. **Low quality score**: Add section names and specific improvement types
3. **Edit application failed**: Try refreshing and attempting again
4. **API errors**: Check internet connection and try again

### Debug Mode

Enable debug logging in the browser console:

```javascript
// Access test utilities
window.testAgentMode.runTests();
```

## Contributing

When contributing to Agent Mode:

1. Follow the existing architecture patterns
2. Add comprehensive error handling
3. Include unit tests for new functionality
4. Update documentation for API changes
5. Test with various document types and prompts
