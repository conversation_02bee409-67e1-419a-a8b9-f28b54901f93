/**
 * Test script for Gemini Career Integration
 * This file can be used to test the Gemini integration manually
 */

import { geminiCareerService } from './services/gemini-career.service';
import { CareerGenerationRequest } from './types';

// Sample resume text for testing
const sampleResumeText = `
<PERSON>
Software Engineer

EXPERIENCE:
- 5 years of experience in full-stack web development
- Proficient in JavaScript, React, Node.js, and Python
- Experience with cloud platforms (AWS, Azure)
- Led a team of 3 developers on multiple projects
- Built scalable web applications serving 100k+ users

EDUCATION:
- Bachelor's Degree in Computer Science
- Certified AWS Solutions Architect

SKILLS:
- Programming: JavaScript, Python, Java, TypeScript
- Frameworks: React, Node.js, Express, Django
- Databases: PostgreSQL, MongoDB, Redis
- Cloud: AWS, Docker, Kubernetes
- Tools: Git, <PERSON>, <PERSON><PERSON>
`;

const sampleContext = `
I'm interested in transitioning to a more senior role with leadership responsibilities. 
I enjoy working with cutting-edge technologies and want to make a positive impact on products that reach millions of users.
I prefer remote work opportunities and am open to roles in AI/ML, cloud architecture, or product management.
`;

/**
 * Test the Gemini career service
 */
export async function testGeminiIntegration() {
  console.log('🚀 Testing Gemini Career Integration...');
  
  try {
    // Test connection first
    console.log('📡 Testing API connection...');
    const connectionTest = await geminiCareerService.testConnection();
    
    if (!connectionTest.success) {
      console.error('❌ Connection test failed:', connectionTest.error);
      return false;
    }
    
    console.log('✅ Connection test successful');
    
    // Test career path generation
    console.log('🎯 Testing career path generation...');
    
    const request: CareerGenerationRequest = {
      resumeText: sampleResumeText,
      additionalContext: sampleContext,
      model: 'gemini-2.5-flash'
    };
    
    const startTime = Date.now();
    const response = await geminiCareerService.generateCareerPaths(request);
    const endTime = Date.now();
    
    console.log('✅ Career paths generated successfully!');
    console.log(`⏱️  Processing time: ${endTime - startTime}ms`);
    console.log(`📊 Generated ${response.careers.length} career paths`);
    console.log(`🎯 Confidence: ${response.confidence}`);
    
    // Log the first career path as an example
    if (response.careers.length > 0) {
      const firstCareer = response.careers[0];
      console.log('\n📋 Sample Career Path:');
      console.log(`   Title: ${firstCareer.jobTitle}`);
      console.log(`   Description: ${firstCareer.jobDescription}`);
      console.log(`   Timeline: ${firstCareer.timeline}`);
      console.log(`   Salary: ${firstCareer.salary}`);
      console.log(`   Difficulty: ${firstCareer.difficulty}`);
    }
    
    // Test detailed analysis for the first career
    if (response.careers.length > 0) {
      console.log('\n🔍 Testing detailed analysis...');
      
      const detailedCareer = await geminiCareerService.generateDetailedAnalysis(
        response.careers[0],
        {
          rawText: sampleResumeText,
          extractedInfo: {
            skills: ['JavaScript', 'React', 'Node.js', 'Python'],
            experience: ['5 years full-stack development', 'Team leadership'],
            education: ['Computer Science Degree'],
            certifications: ['AWS Solutions Architect']
          },
          fileName: 'test-resume.pdf',
          uploadedAt: new Date()
        },
        sampleContext,
        'gemini-2.5-flash'
      );
      
      console.log('✅ Detailed analysis generated successfully!');
      console.log(`   Work Required: ${detailedCareer.workRequired}`);
      console.log(`   Why It's a Good Fit: ${detailedCareer.whyItsGoodFit?.length || 0} reasons`);
      console.log(`   Roadmap Steps: ${detailedCareer.roadmap?.length || 0} steps`);
    }
    
    console.log('\n🎉 All tests passed! Gemini integration is working correctly.');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

/**
 * Test with different models
 */
export async function testDifferentModels() {
  console.log('🔄 Testing different Gemini models...');
  
  const models = ['gemini-2.5-flash', 'gemini-2.5-pro'];
  const request: CareerGenerationRequest = {
    resumeText: sampleResumeText,
    additionalContext: sampleContext,
    model: 'gemini-2.5-flash' // Will be overridden
  };
  
  for (const model of models) {
    try {
      console.log(`\n🧪 Testing ${model}...`);
      const testRequest = { ...request, model };
      
      const startTime = Date.now();
      const response = await geminiCareerService.generateCareerPaths(testRequest);
      const endTime = Date.now();
      
      console.log(`✅ ${model} - Success!`);
      console.log(`   Careers: ${response.careers.length}`);
      console.log(`   Time: ${endTime - startTime}ms`);
      console.log(`   Confidence: ${response.confidence}`);
      
    } catch (error) {
      console.error(`❌ ${model} - Failed:`, error);
    }
  }
}

// Export for manual testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testGeminiIntegration = testGeminiIntegration;
  (window as any).testDifferentModels = testDifferentModels;
  console.log('🔧 Test functions available in console:');
  console.log('   - testGeminiIntegration()');
  console.log('   - testDifferentModels()');
}
