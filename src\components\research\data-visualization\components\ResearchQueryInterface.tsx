import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  MessageSquare,
  Send,
  Brain,
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  Target,
  Lightbulb,
  Clock,
  CheckCircle,
  AlertTriangle,
  Sparkles,
  FlaskConical,
  Microscope,
  BookOpen
} from "lucide-react";

import { UploadedFile, DataAnalysisResult, QueryRequest, QueryResponse } from '../types';
import { EnhancedQueryInterface } from './EnhancedQueryInterface';

interface ResearchQueryInterfaceProps {
  file: UploadedFile;
  analysis?: DataAnalysisResult | null;
  researchContext: {
    researchQuestion?: string;
    studyType?: string;
    researchField?: string;
    hypotheses?: string[];
  };
  className?: string;
}

export const ResearchQueryInterface: React.FC<ResearchQueryInterfaceProps> = ({
  file,
  analysis,
  researchContext,
  className = ""
}) => {
  const [queries, setQueries] = useState<QueryRequest[]>([]);
  const [responses, setResponses] = useState<QueryResponse[]>([]);
  const [currentQuery, setCurrentQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Research-specific suggested questions
  const suggestedQuestions = [
    {
      category: 'Statistical Analysis',
      icon: FlaskConical,
      questions: [
        'What statistical tests are most appropriate for this data?',
        'Are there any significant correlations between variables?',
        'What is the statistical power of this study?',
        'Are there any outliers that might affect the results?'
      ]
    },
    {
      category: 'Research Methodology',
      icon: Microscope,
      questions: [
        'What are the limitations of this study design?',
        'How representative is this sample?',
        'What confounding variables should be considered?',
        'What would be the next steps for future research?'
      ]
    },
    {
      category: 'Data Interpretation',
      icon: Brain,
      questions: [
        'What are the key findings from this analysis?',
        'How do these results compare to existing literature?',
        'What are the practical implications of these findings?',
        'What recommendations can be made based on this data?'
      ]
    },
    {
      category: 'Publication Support',
      icon: BookOpen,
      questions: [
        'How should I present these results in a research paper?',
        'What figures would best illustrate these findings?',
        'What should be included in the discussion section?',
        'How can I address potential reviewer concerns?'
      ]
    }
  ];

  const handleQuerySubmit = async (query: string) => {
    if (!query.trim()) return;

    setIsProcessing(true);
    
    const newQuery: QueryRequest = {
      id: `query-${Date.now()}`,
      fileId: file.id,
      question: query,
      timestamp: new Date(),
      status: 'processing'
    };

    setQueries(prev => [...prev, newQuery]);
    setCurrentQuery('');

    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create a research-focused response
      const response: QueryResponse = {
        id: `response-${Date.now()}`,
        requestId: newQuery.id,
        answer: generateResearchResponse(query, file, analysis, researchContext),
        executionTime: 2000,
      };

      setResponses(prev => [...prev, response]);
      
      // Update query status
      setQueries(prev => prev.map(q => 
        q.id === newQuery.id ? { ...q, status: 'completed' } : q
      ));

      toast.success('Research query completed!');

    } catch (error) {
      console.error('Query failed:', error);
      
      setQueries(prev => prev.map(q => 
        q.id === newQuery.id ? { ...q, status: 'error' } : q
      ));

      toast.error('Query failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const generateResearchResponse = (
    query: string, 
    file: UploadedFile, 
    analysis: DataAnalysisResult | null | undefined,
    context: any
  ): string => {
    // Simple response generation based on query content
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('statistical test') || lowerQuery.includes('appropriate test')) {
      return `Based on your dataset with ${file.data.length} observations and ${file.headers.length} variables, I recommend starting with descriptive statistics and correlation analysis. For inferential statistics, consider t-tests for comparing means, chi-square tests for categorical associations, or regression analysis for predictive modeling, depending on your research questions and variable types.`;
    }
    
    if (lowerQuery.includes('correlation') || lowerQuery.includes('relationship')) {
      return `The correlation analysis reveals several interesting relationships in your data. ${analysis?.insights.correlations.join(' ') || 'Key correlations have been identified between your variables.'} These relationships suggest potential areas for further investigation and hypothesis testing.`;
    }
    
    if (lowerQuery.includes('limitation') || lowerQuery.includes('weakness')) {
      return `Key limitations of this study include: 1) Sample size considerations (n=${file.data.length}), 2) Cross-sectional design limiting causal inference, 3) Potential selection bias in data collection, 4) Missing data patterns that may affect results. Consider addressing these in your methodology and discussion sections.`;
    }
    
    if (lowerQuery.includes('finding') || lowerQuery.includes('result')) {
      return `The key findings from your analysis include: ${analysis?.insights.keyFindings.join('; ') || 'Significant patterns and relationships have been identified in your data.'} These results provide valuable insights for your research field and suggest several avenues for future investigation.`;
    }
    
    if (lowerQuery.includes('figure') || lowerQuery.includes('visualization')) {
      return `For publication, I recommend creating: 1) A descriptive statistics table, 2) Correlation matrix heatmap, 3) Distribution plots for key variables, 4) Scatter plots for significant relationships. Each figure should include proper statistical annotations and follow journal formatting guidelines.`;
    }
    
    // Default research-focused response
    return `Thank you for your research question about "${query}". Based on your dataset (${file.data.length} observations, ${file.headers.length} variables) and the analysis performed, this is an important consideration for your research. I recommend reviewing the statistical analysis results and considering how this relates to your research objectives and methodology.`;
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-purple-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl">
              <MessageSquare className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Research Query Assistant
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Ask research-specific questions about your data analysis and methodology
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{queries.length}</div>
              <div className="text-sm text-gray-600">Questions Asked</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {responses.length}
              </div>
              <div className="text-sm text-gray-600">Responses Generated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {researchContext.researchField || 'General'}
              </div>
              <div className="text-sm text-gray-600">Research Field</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Research Context */}
      {researchContext.researchQuestion && (
        <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-500 rounded-lg">
                <Target className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Research Context</h3>
                <p className="text-gray-700">
                  <strong>Research Question:</strong> {researchContext.researchQuestion}
                </p>
                {researchContext.studyType && (
                  <p className="text-gray-700 mt-1">
                    <strong>Study Type:</strong> {researchContext.studyType}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggested Questions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {suggestedQuestions.map((category, index) => (
          <Card key={index} className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <category.icon className="h-5 w-5 text-blue-600" />
                {category.category}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {category.questions.map((question, qIndex) => (
                  <Button
                    key={qIndex}
                    variant="ghost"
                    className="w-full text-left justify-start h-auto p-3 text-sm"
                    onClick={() => handleQuerySubmit(question)}
                    disabled={isProcessing}
                  >
                    {question}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Query Input */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            Ask Your Research Question
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3">
            <Input
              value={currentQuery}
              onChange={(e) => setCurrentQuery(e.target.value)}
              placeholder="Ask a research question about your data..."
              onKeyPress={(e) => e.key === 'Enter' && handleQuerySubmit(currentQuery)}
              disabled={isProcessing}
              className="flex-1"
            />
            <Button 
              onClick={() => handleQuerySubmit(currentQuery)}
              disabled={isProcessing || !currentQuery.trim()}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isProcessing ? (
                <Clock className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Query History */}
      {queries.length > 0 && (
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-green-600" />
              Research Query History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {queries.map((query) => {
                  const response = responses.find(r => r.requestId === query.id);
                  return (
                    <div key={query.id} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-start gap-3 mb-3">
                        <div className="p-2 bg-purple-500 rounded-lg">
                          <MessageSquare className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{query.question}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge 
                              variant={query.status === 'completed' ? 'default' : 'secondary'}
                              className={query.status === 'completed' ? 'bg-green-500' : ''}
                            >
                              {query.status}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {query.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      {response && (
                        <div className="ml-11 p-3 bg-white rounded-lg border">
                          <div className="flex items-start gap-2 mb-2">
                            <Brain className="h-4 w-4 text-blue-600 mt-0.5" />
                            <span className="text-sm font-medium text-gray-900">Research Assistant</span>
                          </div>
                          <p className="text-sm text-gray-700">{response.answer}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Fallback to Enhanced Query Interface */}
      <div className="mt-8">
        <Separator className="mb-6" />
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Data Queries</h3>
        <EnhancedQueryInterface 
          file={file} 
          dataDescription={researchContext.researchQuestion || ''}
        />
      </div>
    </div>
  );
};
