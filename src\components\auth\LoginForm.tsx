
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { GraduationCap, BookOpen, Users, Zap } from "lucide-react";

interface LoginFormProps {
  onLogin: () => void;
}

export function LoginForm({ onLogin }: LoginFormProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // For demo purposes, just log in
    onLogin();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <GraduationCap className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">PaperGenius</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            The most powerful AI-powered research writing platform for academics, students, and researchers
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
          {/* Features */}
          <div className="space-y-8">
            <div className="flex items-start space-x-4">
              <BookOpen className="h-8 w-8 text-blue-600 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">AI Research Assistant</h3>
                <p className="text-gray-600">Chat with AI about your research, upload PDFs, and get intelligent insights</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <Users className="h-8 w-8 text-purple-600 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Real-time Collaboration</h3>
                <p className="text-gray-600">Work together with your team in real-time with comments and suggestions</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <Zap className="h-8 w-8 text-green-600 mt-1" />
              <div>
                <h3 className="text-xl font-semibold mb-2">Smart Citations</h3>
                <p className="text-gray-600">Automatic citation formatting in 1,700+ academic styles</p>
              </div>
            </div>
          </div>

          {/* Login Form */}
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Welcome Back</CardTitle>
              <CardDescription>Sign in to continue your research</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">
                  Sign In
                </Button>
                <div className="text-center">
                  <Button variant="link" className="text-sm">
                    Don't have an account? Sign up
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
