/**
 * <PERSON><PERSON> History Component
 * Displays and manages previous tutoring sessions
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  History, 
  MessageSquare, 
  Clock, 
  Trash2, 
  Play, 
  Search,
  Plus,
  BookOpen,
  Calendar,
  Filter
} from "lucide-react";
import { TutorHistoryProps, TutorSession } from '../types';

export function TutorHistory({ 
  sessions, 
  onSessionSelect, 
  onSessionDelete, 
  onCreateNew 
}: TutorHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState<string>('all');

  // Filter sessions based on search term and education level
  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         session.topic.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = filterLevel === 'all' || session.educationLevel === filterLevel;
    return matchesSearch && matchesLevel;
  });

  // Group sessions by date
  const groupedSessions = filteredSessions.reduce((groups, session) => {
    const date = new Date(session.createdAt).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(session);
    return groups;
  }, {} as Record<string, TutorSession[]>);

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getEducationLevels = () => {
    const levels = [...new Set(sessions.map(s => s.educationLevel))];
    return levels.sort();
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
            <History className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Session History</h1>
            <p className="text-gray-600">
              {sessions.length} total sessions
            </p>
          </div>
        </div>
        
        <Button onClick={onCreateNew} className="bg-blue-500 hover:bg-blue-600">
          <Plus className="w-4 h-4 mr-2" />
          New Session
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search sessions by topic or title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Education Level Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filterLevel}
                onChange={(e) => setFilterLevel(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Levels</option>
                {getEducationLevels().map(level => (
                  <option key={level} value={level}>
                    {level.replace('-', ' ')}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sessions List */}
      {Object.keys(groupedSessions).length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {sessions.length === 0 ? 'No sessions yet' : 'No sessions found'}
            </h3>
            <p className="text-gray-500 mb-6">
              {sessions.length === 0 
                ? 'Start your first tutoring session to begin learning!'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
            <Button onClick={onCreateNew} className="bg-blue-500 hover:bg-blue-600">
              <Plus className="w-4 h-4 mr-2" />
              Start Learning
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedSessions)
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
            .map(([date, dateSessions]) => (
              <div key={date}>
                {/* Date Header */}
                <div className="flex items-center space-x-2 mb-3">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <h2 className="text-lg font-semibold text-gray-900">
                    {new Date(date).toLocaleDateString([], { 
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </h2>
                </div>

                {/* Sessions for this date */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dateSessions
                    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                    .map((session) => (
                      <Card 
                        key={session.id} 
                        className="hover:shadow-md transition-shadow cursor-pointer group"
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-base line-clamp-2 group-hover:text-blue-600 transition-colors">
                                {session.title}
                              </CardTitle>
                              <p className="text-sm text-gray-500 mt-1">
                                {session.topic}
                              </p>
                            </div>
                            <Badge 
                              variant={session.isActive ? "default" : "secondary"}
                              className="ml-2 shrink-0"
                            >
                              {session.isActive ? 'Active' : 'Completed'}
                            </Badge>
                          </div>
                        </CardHeader>

                        <CardContent className="pt-0">
                          {/* Session Stats */}
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                            <div className="flex items-center space-x-1">
                              <MessageSquare className="w-3 h-3" />
                              <span>{session.messages.length}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{formatTime(session.createdAt)}</span>
                            </div>
                            {session.metadata?.duration && (
                              <span>{formatDuration(session.metadata.duration)}</span>
                            )}
                          </div>

                          {/* Education Level */}
                          <div className="mb-4">
                            <Badge variant="outline" className="text-xs">
                              {session.educationLevel.replace('-', ' ')}
                            </Badge>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              onClick={() => onSessionSelect(session)}
                              className="flex-1 bg-blue-500 hover:bg-blue-600"
                            >
                              <Play className="w-3 h-3 mr-1" />
                              {session.isActive ? 'Continue' : 'Resume'}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                onSessionDelete(session.id);
                              }}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
}
