import React, { useEffect, useState } from 'react';
import { 
  <PERSON><PERSON>les, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  FileText,
  Zap,
  Brain
} from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

import { GenerationState } from '../types';

interface GenerationPanelProps {
  generationState: GenerationState;
  onComplete: () => void;
}

export function GenerationPanel({ generationState, onComplete }: GenerationPanelProps) {
  const [currentSlideTitle, setCurrentSlideTitle] = useState<string>('');
  const [generationSteps, setGenerationSteps] = useState<string[]>([]);

  // Simulate generation steps for demo purposes
  useEffect(() => {
    if (generationState.isGeneratingSlides) {
      const steps = [
        'Analyzing presentation structure...',
        'Generating slide content...',
        'Optimizing layout and design...',
        'Adding speaker notes...',
        'Finalizing presentation...'
      ];
      
      let stepIndex = 0;
      const interval = setInterval(() => {
        if (stepIndex < steps.length) {
          setGenerationSteps(prev => [...prev, steps[stepIndex]]);
          stepIndex++;
        } else {
          clearInterval(interval);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [generationState.isGeneratingSlides]);

  // Auto-complete when generation is done
  useEffect(() => {
    if (generationState.progress === 100 && generationState.stage === 'complete') {
      const timer = setTimeout(() => {
        onComplete();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [generationState.progress, generationState.stage, onComplete]);

  const getStatusIcon = () => {
    switch (generationState.stage) {
      case 'slides':
        return <Sparkles className="w-8 h-8 text-blue-500 animate-spin" />;
      case 'complete':
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-8 h-8 text-red-500" />;
      default:
        return <Clock className="w-8 h-8 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    switch (generationState.stage) {
      case 'slides':
        return 'Generating your presentation slides...';
      case 'complete':
        return 'Presentation generated successfully!';
      case 'error':
        return 'An error occurred during generation';
      default:
        return 'Preparing to generate slides...';
    }
  };

  const getProgressColor = () => {
    switch (generationState.stage) {
      case 'complete':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          {getStatusIcon()}
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          {getStatusMessage()}
        </h2>
        <p className="text-gray-600 text-lg">
          {generationState.stage === 'slides' && 
            'Please wait while our AI creates your presentation slides with engaging content and professional layouts.'
          }
          {generationState.stage === 'complete' && 
            'Your presentation is ready! You can now review and edit your slides.'
          }
          {generationState.stage === 'error' && 
            'Something went wrong. Please try again or contact support if the issue persists.'
          }
        </p>
      </div>

      {/* Progress Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Generation Progress
            </span>
            <Badge variant={generationState.stage === 'complete' ? 'default' : 'secondary'}>
              {Math.round(generationState.progress)}%
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress 
              value={generationState.progress} 
              className="w-full h-3"
            />
            <div className="flex justify-between text-sm text-gray-600">
              <span>
                {generationState.isGeneratingSlides && generationState.currentSlideIndex > 0 && (
                  `Slide ${generationState.currentSlideIndex} of ${generationState.totalSlides}`
                )}
              </span>
              <span>{Math.round(generationState.progress)}% Complete</span>
            </div>
          </div>

          {/* Current Slide Info */}
          {generationState.isGeneratingSlides && generationState.currentSlideIndex > 0 && (
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-blue-600" />
                <div>
                  <h4 className="font-medium text-blue-900">
                    Currently Generating
                  </h4>
                  <p className="text-blue-700 text-sm">
                    Slide {generationState.currentSlideIndex}: {currentSlideTitle || 'Processing...'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Generation Steps */}
          {generationSteps.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900 flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Generation Steps
              </h4>
              <div className="space-y-2">
                {generationSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{step}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Error Message */}
          {generationState.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900 mb-1">Generation Error</h4>
                  <p className="text-red-700 text-sm">{generationState.error}</p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      {generationState.totalSlides > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Presentation Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {generationState.totalSlides}
                </div>
                <div className="text-sm text-gray-600">Total Slides</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {generationState.currentSlideIndex}
                </div>
                <div className="text-sm text-gray-600">Slides Generated</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {Math.max(0, generationState.totalSlides - generationState.currentSlideIndex)}
                </div>
                <div className="text-sm text-gray-600">Remaining</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        {generationState.stage === 'complete' && (
          <Button onClick={onComplete} size="lg">
            <CheckCircle className="w-5 h-5 mr-2" />
            Continue to Editor
          </Button>
        )}
        
        {generationState.stage === 'error' && (
          <Button variant="outline" onClick={() => window.location.reload()}>
            <AlertCircle className="w-5 h-5 mr-2" />
            Try Again
          </Button>
        )}
      </div>

      {/* Tips */}
      {generationState.isGeneratingSlides && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Sparkles className="w-4 h-4" />
              Pro Tips
            </h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• Your slides will include professional layouts and engaging content</li>
              <li>• Speaker notes are automatically generated for each slide</li>
              <li>• You can edit and customize everything after generation</li>
              <li>• Multiple export formats will be available (PDF, PowerPoint, HTML)</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
