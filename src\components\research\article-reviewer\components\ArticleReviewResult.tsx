import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowUp, ArrowDown, FileText, Plus, Download, Copy, MessageCircle,
  Book, FileCode, Clipboard, CheckCircle, Lightbulb, AlertCircle, MessageSquare,
  FileSearch, Highlighter, ArrowRight, Info
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ArticleReviewResults, ArticleSection, DetailedFeedbackItem, SectionReview, ReviewMetrics } from "../types";

interface ArticleReviewResultProps {
  articleFile: File | null;
  reviewResults: ArticleReviewResults;
}

export function ArticleReviewResult({ articleFile, reviewResults }: ArticleReviewResultProps) {
  // Active tab state
  const [activeTab, setActiveTab] = useState<string>("summary");
  
  // Handle download of results
  const handleDownload = () => {
    // Convert results to JSON
    const resultsJson = JSON.stringify(reviewResults, null, 2);
    
    // Create a blob and download
    const blob = new Blob([resultsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${articleFile?.name || 'article'}_review_results.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  // Handle copy of results summary
  const handleCopy = () => {
    const summary = `
Article Review Summary
---------------------
Overall Score: ${reviewResults.overall.overallScore}/100

Key Strengths:
${reviewResults.overall.majorStrengths.map(s => `- ${s}`).join('\n')}

Areas for Improvement:
${reviewResults.overall.majorWeaknesses.map(w => `- ${w}`).join('\n')}

Recommendations:
${reviewResults.overall.recommendations.map(r => `- ${r}`).join('\n')}
`;
    
    navigator.clipboard.writeText(summary);
    // You would typically show a toast notification here
  };
  
  // Define the sections in the order we want to display them
  const sectionOrder: ArticleSection[] = [
    'introduction',
    'methodology',
    'results',
    'discussion',
    'conclusion', 
    'abstract',
    'title',
    'keywords'
  ];
  
  // Filter out sections that don't have reviews
  const availableSections = sectionOrder.filter(
    section => reviewResults[section] !== undefined
  );
  
  // Map section names to display names
  const sectionDisplayNames: Record<string, string> = {
    title: 'Title',
    abstract: 'Abstract',
    keywords: 'Keywords',
    introduction: 'Introduction',
    methodology: 'Methodology',
    results: 'Results',
    discussion: 'Discussion',
    conclusion: 'Conclusion',
    references: 'References',
    overall: 'Overall Analysis'
  };
  
  // Helper function to determine score color
  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 75) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (score >= 60) return 'bg-amber-100 text-amber-800 border-amber-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Review Results</CardTitle>
            <CardDescription>
              AI-powered analysis of your research article
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleCopy}
            >
              <Copy className="h-4 w-4 mr-1" />
              Copy Summary
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleDownload}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="summary">
              <FileText className="h-4 w-4 mr-2" />
              Summary
            </TabsTrigger>
            <TabsTrigger value="detailed">
              <FileSearch className="h-4 w-4 mr-2" />
              Section Analysis
            </TabsTrigger>
            <TabsTrigger value="deep-analysis">
              <Highlighter className="h-4 w-4 mr-2" />
              In-Depth Review
            </TabsTrigger>
            <TabsTrigger value="recommendations">
              <Lightbulb className="h-4 w-4 mr-2" />
              Recommendations
            </TabsTrigger>
          </TabsList>
          
          {/* Summary Tab */}
          <TabsContent value="summary" className="mt-0">
            <div className="space-y-6">
              {/* Overall summary section */}
              <div className="prose max-w-none">
                <h3 className="text-lg font-medium mb-3">Review Summary</h3>
                <p className="text-gray-700">
                  {reviewResults.overall.summary}
                </p>
              </div>
              
              {/* Major strengths */}
              <div>
                <h4 className="text-base font-medium mb-2 flex items-center">
                  <ArrowUp className="h-4 w-4 text-green-600 mr-2" />
                  Key Strengths
                </h4>
                <ul className="space-y-2">
                  {reviewResults.overall.majorStrengths.map((strength, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2 mt-1 shrink-0" />
                      <span>{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* Major weaknesses */}
              <div>
                <h4 className="text-base font-medium mb-2 flex items-center">
                  <ArrowDown className="h-4 w-4 text-red-600 mr-2" />
                  Areas for Improvement
                </h4>
                <ul className="space-y-2">
                  {reviewResults.overall.majorWeaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start">
                      <span className="h-4 w-4 rounded-full bg-red-600 mr-2 mt-1 shrink-0" />
                      <span>{weakness}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* Recommendations */}
              <div>
                <h4 className="text-base font-medium mb-2 flex items-center">
                  <MessageCircle className="h-4 w-4 text-blue-600 mr-2" />
                  Recommendations
                </h4>
                <ul className="space-y-2">
                  {reviewResults.overall.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start">
                      <Lightbulb className="h-4 w-4 text-amber-500 mr-2 mt-1 shrink-0" />
                      <span>{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </TabsContent>
          
          {/* Detailed Analysis Tab */}
          <TabsContent value="detailed" className="mt-0">
            <Accordion type="multiple" className="w-full">
              {availableSections.map((section) => {
                const sectionReview = reviewResults[section] as SectionReview;
                if (!sectionReview || section === 'overall') return null;
                
                // Get detailed feedback for this section if available
                const detailedFeedback = sectionReview.detailedFeedback || [];
                
                return (
                  <AccordionItem value={section} key={section}>
                    <AccordionTrigger className="hover:bg-gray-50 px-4 py-2 rounded-md">
                      <div className="flex items-center">
                        <span className="mr-2">{sectionDisplayNames[section]}</span>
                        <Badge 
                          variant="outline"
                          className={`ml-2 ${getScoreColor(sectionReview.score)}`}
                        >
                          Score: {sectionReview.score}
                        </Badge>
                        {detailedFeedback.length > 0 && (
                          <Badge variant="secondary" className="ml-2">
                            {detailedFeedback.length} detailed comments
                          </Badge>
                        )}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pt-2 pb-4">
                      <div className="space-y-6">
                        {/* Overview analysis */}
                        <div className="prose max-w-none bg-white p-4 rounded-lg shadow-sm border">
                          <h4 className="text-base font-medium flex items-center mb-2">
                            <FileSearch className="h-4 w-4 mr-2 text-blue-600" />
                            Overview Analysis
                          </h4>
                          <p className="text-gray-700">
                            {sectionReview.analysis}
                          </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Strengths */}
                          <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg shadow-sm border border-green-100">
                            <h5 className="font-medium text-green-800 mb-3 flex items-center">
                              <ArrowUp className="h-4 w-4 mr-2" />
                              Strengths
                            </h5>
                            <ul className="space-y-2">
                              {sectionReview.strengths.map((strength, idx) => (
                                <li key={idx} className="flex items-start text-sm text-green-700">
                                  <CheckCircle className="h-3 w-3 mr-2 mt-1 shrink-0" />
                                  <span>{strength}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          {/* Weaknesses */}
                          <div className="bg-gradient-to-br from-red-50 to-orange-50 p-4 rounded-lg shadow-sm border border-red-100">
                            <h5 className="font-medium text-red-800 mb-3 flex items-center">
                              <ArrowDown className="h-4 w-4 mr-2" />
                              Issues Identified
                            </h5>
                            <ul className="space-y-2">
                              {sectionReview.weaknesses.map((weakness, idx) => (
                                <li key={idx} className="flex items-start text-sm text-red-700">
                                  <AlertCircle className="h-3 w-3 mr-2 mt-1 shrink-0" />
                                  <span>{weakness}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        
                        {/* Detailed sentence-level feedback */}
                        {detailedFeedback.length > 0 && (
                          <div className="mt-6 border-t pt-4">
                            <h4 className="text-base font-medium flex items-center mb-3">
                              <Highlighter className="h-4 w-4 mr-2 text-purple-600" />
                              Detailed Text Analysis
                            </h4>
                            
                            <div className="space-y-4">
                              {detailedFeedback.map((item, idx) => {
                                // Determine badge color based on severity and type
                                let badgeColor = "bg-gray-100 text-gray-800 border-gray-200";
                                if (item.severity === 'major') {
                                  badgeColor = "bg-red-100 text-red-800 border-red-200";
                                } else if (item.severity === 'moderate') {
                                  badgeColor = "bg-amber-100 text-amber-800 border-amber-200";
                                } else {
                                  badgeColor = "bg-blue-100 text-blue-800 border-blue-200";
                                }
                                
                                return (
                                  <Card key={idx} className="overflow-hidden">
                                    <CardContent className="p-0">
                                      <div className="p-4 border-b bg-gray-50">
                                        <div className="flex flex-wrap items-center gap-2 mb-2">
                                          <Badge variant="outline" className={badgeColor}>
                                            {item.severity.charAt(0).toUpperCase() + item.severity.slice(1)}
                                          </Badge>
                                          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
                                            {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                                          </Badge>
                                          {item.lineNumber && (
                                            <Badge variant="outline" className="bg-gray-100 text-gray-800">
                                              Line {item.lineNumber}
                                            </Badge>
                                          )}
                                        </div>
                                        <div className="text-sm font-medium text-gray-700">
                                          {item.issue}
                                        </div>
                                      </div>
                                      
                                      {/* Original Text */}
                                      <div className="p-4 bg-white border-b">
                                        <div className="text-xs text-gray-500 mb-1">Original Text:</div>
                                        <div className="bg-red-50 border-l-2 border-red-400 p-2 text-sm whitespace-pre-wrap font-mono rounded">
                                          {item.originalText}
                                        </div>
                                      </div>
                                      
                                      {/* Suggestion */}
                                      <div className="p-4 bg-white">
                                        <div className="flex items-center text-xs text-gray-500 mb-1">
                                          <ArrowRight className="h-3 w-3 mr-1" /> Suggestion:
                                        </div>
                                        <div className="bg-green-50 border-l-2 border-green-400 p-2 text-sm whitespace-pre-wrap font-mono rounded">
                                          {item.suggestion}
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </TabsContent>
          
          {/* Deep Analysis Tab */}
          <TabsContent value="deep-analysis" className="mt-0">
            <div className="space-y-6">
              {/* Instructions */}
              <div className="bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 p-6 rounded-lg border border-blue-200 mb-6 shadow-sm">
                <h3 className="text-base font-medium flex items-center text-blue-800 mb-2">
                  <Highlighter className="h-5 w-5 mr-2 text-blue-600" />
                  In-Depth Sentence Analysis
                </h3>
                <p className="text-sm text-blue-700 leading-relaxed">
                  This view provides detailed sentence-level analysis of your paper, highlighting specific issues and suggesting improvements.
                  Each item shows the original problematic text, explains the issue, and offers an actionable suggestion for improvement.
                </p>
                <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2">
                  <div className="flex items-center gap-1 text-xs text-blue-700">
                    <div className="w-3 h-3 rounded-full bg-red-500 opacity-70"></div>
                    <span>Major issues</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-blue-700">
                    <div className="w-3 h-3 rounded-full bg-amber-500 opacity-70"></div>
                    <span>Moderate issues</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-blue-700">
                    <div className="w-3 h-3 rounded-full bg-blue-500 opacity-70"></div>
                    <span>Minor issues</span>
                  </div>
                </div>
              </div>
              
              {/* Filter controls */}
              <div className="flex flex-wrap gap-2 mb-6 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div className="flex items-center mr-3">
                  <FileSearch className="h-4 w-4 text-gray-500 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Filter by:</span>
                </div>
                <Badge variant="outline" className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white cursor-pointer hover:opacity-90 border-none">
                  All Issues
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  Grammar
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  Clarity
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <ArrowRight className="h-3 w-3" />
                  Structure
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  Content
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <Book className="h-3 w-3" />
                  Citations
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Logic
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-gray-100 flex items-center gap-1">
                  <Clipboard className="h-3 w-3" />
                  Style
                </Badge>
              </div>
              
              {/* Severity filter */}
              <div className="flex flex-wrap gap-2 mb-6">
                <div className="flex items-center space-x-2 mr-3">
                  <span className="text-sm font-medium text-gray-700">Severity:</span>
                </div>
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 cursor-pointer hover:bg-red-200">
                  Major
                </Badge>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 cursor-pointer hover:bg-amber-200">
                  Moderate
                </Badge>
                <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 cursor-pointer hover:bg-blue-200">
                  Minor
                </Badge>
              </div>
              
              {/* Group feedback by section */}
              {availableSections.map((section) => {
                const sectionReview = reviewResults[section] as SectionReview;
                if (!sectionReview || section === 'overall' || !sectionReview.detailedFeedback || sectionReview.detailedFeedback.length === 0) {
                  return null;
                }
                
                return (
                  <div key={section} className="mb-8">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium flex items-center">
                        <span className="w-2 h-6 bg-blue-600 rounded-sm mr-2" />
                        {sectionDisplayNames[section]}
                      </h3>
                      <Badge variant="outline">
                        {sectionReview.detailedFeedback.length} issues
                      </Badge>
                    </div>
                    
                    <div className="space-y-4">
                      {sectionReview.detailedFeedback.map((item, idx) => {
                        // Determine badge color based on severity and type
                        let severityColor = "";
                        let typeIcon = null;
                        
                        // Set colors and icons based on severity
                        switch(item.severity) {
                          case 'major':
                            severityColor = "bg-gradient-to-r from-red-500 to-red-600 text-white";
                            break;
                          case 'moderate':
                            severityColor = "bg-gradient-to-r from-amber-500 to-amber-600 text-white";
                            break;
                          case 'minor':
                            severityColor = "bg-gradient-to-r from-blue-500 to-blue-600 text-white";
                            break;
                        }
                        
                        // Set icon based on type
                        switch(item.type) {
                          case 'grammar':
                            typeIcon = <MessageSquare className="h-3 w-3 mr-1" />;
                            break;
                          case 'clarity':
                            typeIcon = <FileSearch className="h-3 w-3 mr-1" />;
                            break;
                          case 'structure':
                            typeIcon = <ArrowRight className="h-3 w-3 mr-1" />;
                            break;
                          case 'content':
                            typeIcon = <FileText className="h-3 w-3 mr-1" />;
                            break;
                          case 'citation':
                            typeIcon = <Book className="h-3 w-3 mr-1" />;
                            break;
                          default:
                            typeIcon = <Info className="h-3 w-3 mr-1" />;
                        }
                        
                        return (
                          <Card key={idx} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow border border-gray-200">
                            <CardHeader className={`py-3 px-4 ${severityColor}`}>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  <CardTitle className="text-sm font-medium">
                                    {item.issue}
                                  </CardTitle>
                                </div>
                                <div className="flex items-center space-x-2">
                                  {item.lineNumber && (
                                    <Badge variant="outline" className="bg-white/20 text-white border-white/30 text-xs">
                                      Line {item.lineNumber}
                                    </Badge>
                                  )}
                                  <Badge variant="outline" className="bg-white/20 text-white border-white/30 text-xs flex items-center">
                                    {typeIcon}
                                    {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                                  </Badge>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="p-0">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-0 divide-x divide-gray-200">
                                {/* Original Text */}
                                <div className="p-4 bg-gray-50">
                                  <div className="flex items-center text-xs text-gray-500 mb-2 font-medium">
                                    <FileText className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                    Original Text:
                                  </div>
                                  <div className="bg-white border border-red-200 p-4 rounded-md text-sm whitespace-pre-wrap font-mono text-gray-700 shadow-sm relative">
                                    <div className="absolute -left-1 top-0 bottom-0 w-1 bg-red-400 rounded-full"></div>
                                    {item.originalText}
                                  </div>
                                </div>
                                {/* Suggestion */}
                                <div className="p-4 bg-white">
                                  <div className="flex items-center text-xs text-gray-500 mb-2 font-medium">
                                    <Lightbulb className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                    Suggested Improvement:
                                  </div>
                                  <div className="bg-green-50 border border-green-200 p-4 rounded-md text-sm whitespace-pre-wrap font-mono text-gray-700 shadow-sm relative">
                                    <div className="absolute -left-1 top-0 bottom-0 w-1 bg-green-400 rounded-full"></div>
                                    {item.suggestion}
                                  </div>
                                </div>
                              </div>
                              <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-white border-t text-xs flex justify-between items-center">
                                <div className="flex items-center text-gray-500">
                                  <Info className="h-3 w-3 mr-1" />
                                  <span>This feedback is part of the {section} section analysis</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button variant="ghost" size="sm" className="h-7 text-xs">
                                    <Copy className="h-3 w-3 mr-1" />
                                    Copy Suggestion
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
              
              {/* No issues found state */}
              {availableSections.every(section => {
                const sectionReview = reviewResults[section] as SectionReview;
                return !sectionReview || section === 'overall' || !sectionReview.detailedFeedback || sectionReview.detailedFeedback.length === 0;
              }) && (
                <div className="text-center p-12">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No Detailed Issues Found</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    No sentence-level issues were identified. This could mean your paper is well-written, or that the AI model needs to perform a deeper analysis.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
          
          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="mt-0">
            <div className="space-y-6">
              {availableSections.map((section) => {
                const sectionReview = reviewResults[section] as SectionReview;
                if (!sectionReview || section === 'overall' || !sectionReview.suggestions || sectionReview.suggestions.length === 0) {
                  return null;
                }
                
                return (
                  <Card key={section} className="overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50">
                      <CardTitle className="text-base flex items-center">
                        <span className="w-1 h-5 bg-blue-600 rounded-sm mr-2" />
                        {sectionDisplayNames[section]} Recommendations
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <ul className="space-y-3">
                        {sectionReview.suggestions.map((suggestion, idx) => (
                          <li key={idx} className="flex items-start bg-white p-3 rounded-md border border-gray-100 hover:border-blue-200 transition-colors">
                            <Lightbulb className="h-5 w-5 text-amber-500 mr-2 mt-0.5 shrink-0" />
                            <span>{suggestion}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                );
              })}
              
              {/* Overall recommendations */}
              <Card className="overflow-hidden shadow-sm hover:shadow-md transition-shadow border-blue-200">
                <CardHeader className="pb-2 bg-gradient-to-r from-blue-100 to-indigo-100">
                  <CardTitle className="text-base flex items-center text-blue-800">
                    <Book className="h-5 w-5 mr-2" />
                    General Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4 bg-gradient-to-br from-blue-50 to-white">
                  <ul className="space-y-3">
                    {(reviewResults.overall as ReviewMetrics).recommendations.map((recommendation, idx) => (
                      <li key={idx} className="flex items-start bg-white p-3 rounded-md border border-blue-100 hover:border-blue-300 transition-colors">
                        <MessageCircle className="h-5 w-5 text-blue-600 mr-2 mt-0.5 shrink-0" />
                        <span className="text-blue-900">{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
              
              {/* Action buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" className="border-blue-200 hover:border-blue-400 hover:bg-blue-50">
                  <Clipboard className="h-4 w-4 mr-2" />
                  Copy All Recommendations
                </Button>
                <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                  <FileCode className="h-4 w-4 mr-2" />
                  Export as Report
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
