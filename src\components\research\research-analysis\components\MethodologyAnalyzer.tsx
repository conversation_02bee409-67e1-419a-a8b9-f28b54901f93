import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "lucide-react";

import { ResearchDocument, MethodologyAnalysis, AIGenerationOptions } from '../types';

interface MethodologyAnalyzerProps {
  documents: ResearchDocument[];
  onAnalyze: (analysis: MethodologyAnalysis) => void;
  aiSettings: {
    selectedModel: string;
    options: AIGenerationOptions;
  };
}

export function MethodologyAnalyzer({ documents }: MethodologyAnalyzerProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Brain className="h-6 w-6 text-indigo-500" />
          Methodology Analyzer
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Methodology analysis feature coming soon!</p>
          <Badge variant="secondary">
            {documents.length} documents selected
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
