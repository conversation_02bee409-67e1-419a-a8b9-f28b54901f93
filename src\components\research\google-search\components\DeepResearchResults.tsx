/**
 * Deep Research Results Component
 * Displays the comprehensive research report
 */

import React, { useState } from 'react';
import { 
  FileText, 
  Download, 
  Copy, 
  ExternalLink, 
  BookOpen, 
  Quote,
  Eye,
  EyeOff,
  Share2,
  RefreshCw
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';

import { 
  DeepResearchSession, 
  ResearchSection, 
  GoogleReference, 
  GoogleCitation 
} from '../types';

interface DeepResearchResultsProps {
  session: DeepResearchSession;
  onExport?: (format: 'pdf' | 'docx' | 'markdown') => void;
  onRegenerateSection?: (sectionId: string) => void;
  onNewResearch?: () => void;
  className?: string;
}

export function DeepResearchResults({
  session,
  onExport,
  onRegenerateSection,
  onNewResearch,
  className = ''
}: DeepResearchResultsProps) {
  const [showCitations, setShowCitations] = useState(true);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success('Content copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  const generateExecutiveSummary = () => {
    const totalWords = session.sections.reduce((sum, section) => sum + section.wordCount, 0);
    const keyPoints = session.sections.map(section => 
      section.content.split('.')[0] + '.'
    ).join(' ');
    
    return {
      totalSections: session.sections.length,
      totalWords,
      keyPoints: keyPoints.substring(0, 300) + '...'
    };
  };

  const getAllReferences = (): GoogleReference[] => {
    const allRefs: GoogleReference[] = [];
    const seenUrls = new Set<string>();
    
    session.sections.forEach(section => {
      section.references.forEach(ref => {
        if (!seenUrls.has(ref.url)) {
          seenUrls.add(ref.url);
          allRefs.push(ref);
        }
      });
    });
    
    return allRefs.sort((a, b) => a.title.localeCompare(b.title));
  };

  const summary = generateExecutiveSummary();
  const allReferences = getAllReferences();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">
              {session.outline?.mainTopic || 'Research Report'}
            </h2>
            <p className="text-gray-600">
              Generated on {new Date(session.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowCitations(!showCitations)}
          >
            {showCitations ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showCitations ? 'Hide' : 'Show'} Citations
          </Button>
          <Button variant="outline" onClick={() => onExport?.('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button onClick={onNewResearch}>
            <RefreshCw className="h-4 w-4 mr-2" />
            New Research
          </Button>
        </div>
      </div>

      {/* Executive Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Executive Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {summary.totalSections}
              </div>
              <div className="text-sm text-gray-600">Sections</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {summary.totalWords.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Words</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {allReferences.length}
              </div>
              <div className="text-sm text-gray-600">Sources</div>
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700 leading-relaxed">
              {session.outline?.description}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Research Sections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Research Sections</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const allSectionIds = new Set(session.sections.map(s => s.id));
                setExpandedSections(
                  expandedSections.size === session.sections.length ? new Set() : allSectionIds
                );
              }}
            >
              {expandedSections.size === session.sections.length ? 'Collapse All' : 'Expand All'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-6">
              {session.sections
                .sort((a, b) => a.order - b.order)
                .map((section, index) => (
                  <ResearchSectionCard
                    key={section.id}
                    section={section}
                    index={index}
                    isExpanded={expandedSections.has(section.id)}
                    showCitations={showCitations}
                    onToggle={() => toggleSection(section.id)}
                    onCopy={() => copyToClipboard(section.content)}
                    onRegenerate={() => onRegenerateSection?.(section.id)}
                  />
                ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* References */}
      {showCitations && allReferences.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Quote className="h-5 w-5" />
              <span>References</span>
              <Badge variant="secondary">{allReferences.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {allReferences.map((reference, index) => (
                  <div key={reference.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600 flex-shrink-0 mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900 font-medium">
                        {reference.title}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        {reference.apaFormat}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(reference.url, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" onClick={() => onExport?.('pdf')}>
              <Download className="h-4 w-4 mr-2" />
              PDF Report
            </Button>
            <Button variant="outline" onClick={() => onExport?.('docx')}>
              <Download className="h-4 w-4 mr-2" />
              Word Document
            </Button>
            <Button variant="outline" onClick={() => onExport?.('markdown')}>
              <Download className="h-4 w-4 mr-2" />
              Markdown
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                const fullReport = session.sections
                  .sort((a, b) => a.order - b.order)
                  .map(s => `# ${s.title}\n\n${s.content}`)
                  .join('\n\n');
                copyToClipboard(fullReport);
              }}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy All
            </Button>
            <Button variant="outline">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface ResearchSectionCardProps {
  section: ResearchSection;
  index: number;
  isExpanded: boolean;
  showCitations: boolean;
  onToggle: () => void;
  onCopy: () => void;
  onRegenerate: () => void;
}

function ResearchSectionCard({
  section,
  index,
  isExpanded,
  showCitations,
  onToggle,
  onCopy,
  onRegenerate
}: ResearchSectionCardProps) {
  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
              {index + 1}
            </div>
            <div>
              <h4 className="font-medium text-gray-900">{section.title}</h4>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {section.wordCount} words
                </Badge>
                {showCitations && section.citations.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {section.citations.length} citations
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={onCopy}>
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onRegenerate}>
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onToggle}>
              {isExpanded ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Section Content */}
          <div className="prose prose-sm max-w-none">
            <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {section.content}
            </div>
          </div>

          {/* Citations */}
          {showCitations && section.citations.length > 0 && (
            <div>
              <Separator className="my-4" />
              <h5 className="text-sm font-medium text-gray-700 mb-3">
                Citations Used in This Section
              </h5>
              <div className="space-y-2">
                {section.citations.map((citation, citIndex) => (
                  <div key={citation.id} className="flex items-start space-x-2 text-xs">
                    <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 flex-shrink-0 mt-0.5">
                      {citIndex + 1}
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-700">{citation.title}</p>
                      <a
                        href={citation.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {citation.url}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
