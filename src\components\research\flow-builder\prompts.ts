/**
 * Flow Builder AI Prompts
 * Specialized prompts for generating different types of diagrams
 */

export const FLOW_GENERATION_PROMPTS = {
  BASE_PROMPT: `You are an expert diagram generator specializing in creating Mermaid.js diagrams for research and methodology visualization. Your task is to generate clear, professional, and accurate diagrams based on user descriptions.

CRITICAL MERMAID SYNTAX RULES:
1. Generate ONLY valid Mermaid.js syntax
2. Do NOT include markdown code blocks (no \`\`\`mermaid)
3. Start directly with the diagram type declaration
4. Node labels MUST NOT contain: / \\ < > { } |
5. Use only alphanumeric characters, spaces, hyphens, and underscores in labels
6. Node IDs must be alphanumeric with underscores only (A, B, C, Node1, Step_1)
7. Always use spaces around arrows: A --> B (not A-->B)
8. Keep labels concise and descriptive

VALID NODE SYNTAX EXAMPLES:
✅ A[Research Question]
✅ B[Data Collection]
✅ C[Statistical Analysis]
✅ D{Decision Point}
✅ E((Start))

INVALID NODE SYNTAX (DO NOT USE):
❌ A[SAR Data Stack (N+1 Images)]  // Contains parentheses and +
❌ B[Input/Output Processing]      // Contains forward slash
❌ C[Data<Processing>]             // Contains angle brackets
❌ D[Step|Process]                 // Contains pipe character

MERMAID SYNTAX GUIDELINES:
- Flowcharts: Use flowchart TD/LR/BT/RL
- Sequence: Use sequenceDiagram
- Gantt: Use gantt
- Pie: Use pie title "Title"
- Mind maps: Use mindmap
- Timelines: Use timeline
- User journeys: Use journey
- ER diagrams: Use erDiagram
- State diagrams: Use stateDiagram-v2
- Class diagrams: Use classDiagram`,

  TYPE_SPECIFIC: {
    flowchart: `FLOWCHART SPECIFIC GUIDELINES:
- Use appropriate node shapes: [] for processes, {} for decisions, () for start/end
- Include clear decision paths with Yes/No labels
- Use meaningful node IDs (A, B, C, etc.)
- Connect nodes with --> arrows
- Add descriptive labels for decision branches
- Consider using subgraphs for complex processes
- Use different node shapes: [] rectangles, () rounded, {} diamonds, (()) circles`,

    sequence: `SEQUENCE DIAGRAM GUIDELINES:
- Define participants clearly at the top
- Use participant A->>B: Message format
- Include activation boxes with activate/deactivate
- Add notes where helpful: Note over A: Description
- Use different arrow types: ->> solid, -->> dashed, -x broken
- Include loops and alternatives where appropriate
- Show return messages with dashed arrows`,

    gantt: `GANTT CHART GUIDELINES:
- Start with gantt and title
- Define sections for different phases
- Use task format: Task Name :status, id, start-date, duration
- Include milestones with :milestone
- Use appropriate statuses: done, active, crit
- Show dependencies between tasks
- Include realistic timeframes`,

    pie: `PIE CHART GUIDELINES:
- Start with pie title "Chart Title"
- Use format: "Label" : value
- Ensure values add up to meaningful totals (preferably 100)
- Use descriptive labels without special characters
- Limit to 5-8 segments for readability
- Order segments by size when possible
- Values must be numbers only (no percentages or units)
- Example: "Research Phase" : 35`,

    mindmap: `MIND MAP GUIDELINES:
- Start with mindmap and root concept
- Use indentation to show hierarchy
- Keep labels concise but descriptive
- Balance branches for visual appeal
- Use meaningful groupings
- Limit depth to 3-4 levels for clarity`,

    timeline: `TIMELINE GUIDELINES:
- Start with timeline and title
- Use chronological order
- Include specific dates or periods
- Add meaningful events and milestones
- Use consistent time intervals
- Include context for each event`,

    'user-journey': `USER JOURNEY GUIDELINES:
- Start with journey and title
- Define sections for different phases
- Include user actions and emotions
- Use realistic scenarios
- Show pain points and opportunities
- Include touchpoints and interactions`,

    er: `ENTITY RELATIONSHIP GUIDELINES:
- Start with erDiagram
- Define entities with attributes
- Show relationships with proper cardinality
- Use meaningful entity and attribute names
- Include primary and foreign keys
- Show relationship types clearly`,

    state: `STATE DIAGRAM GUIDELINES:
- Start with stateDiagram-v2
- Define states clearly
- Show transitions with triggers
- Include initial and final states
- Use meaningful state names
- Show conditions for transitions`,

    class: `CLASS DIAGRAM GUIDELINES:
- Start with classDiagram
- Define classes with attributes and methods
- Show inheritance and associations
- Use proper visibility indicators (+, -, #)
- Include meaningful class names
- Show relationships and cardinalities`
  },

  EXAMPLES: {
    flowchart: `EXAMPLE FLOWCHART:
flowchart TD
    A[Start Research] --> B{Define Question?}
    B -->|Yes| C[Literature Review]
    B -->|No| D[Refine Question]
    D --> B
    C --> E[Design Study]
    E --> F[Collect Data]
    F --> G[Analyze Results]
    G --> H[Draw Conclusions]
    H --> I[Publish Findings]`,

    sequence: `EXAMPLE SEQUENCE DIAGRAM:
sequenceDiagram
    participant R as Researcher
    participant D as Database
    participant A as Analysis Tool

    R->>D: Query data
    activate D
    D-->>R: Return dataset
    deactivate D

    R->>A: Load data
    activate A
    A->>A: Process data
    A-->>R: Analysis results
    deactivate A`,

    gantt: `EXAMPLE GANTT CHART:
gantt
    title Research Project Timeline
    dateFormat  YYYY-MM-DD
    section Planning
    Literature Review    :done, lit, 2024-01-01, 30d
    Research Design      :done, design, after lit, 20d
    section Execution
    Data Collection      :active, collect, after design, 45d
    Data Analysis        :analysis, after collect, 30d
    section Reporting
    Write Paper          :writing, after analysis, 40d
    Peer Review          :review, after writing, 30d`,

    pie: `EXAMPLE PIE CHART:
pie title Research Time Distribution
    "Literature Review" : 25
    "Data Collection" : 35
    "Analysis" : 20
    "Writing" : 15
    "Review" : 5`,

    mindmap: `EXAMPLE MIND MAP:
mindmap
  root((Research Project))
    Planning
      Literature Review
      Hypothesis
      Design
    Execution
      Data Collection
      Analysis
      Validation
    Reporting
      Writing
      Review
      Publication`,

    timeline: `EXAMPLE TIMELINE:
timeline
    title Research Project Timeline
    2024-Q1 : Literature Review
           : Research Design
    2024-Q2 : Data Collection
           : Pilot Study
    2024-Q3 : Data Analysis
           : Results Review
    2024-Q4 : Paper Writing
           : Submission`,

    'user-journey': `EXAMPLE USER JOURNEY:
journey
    title Research Participant Journey
    section Recruitment
      Contact: 3: Participant
      Screening: 4: Participant
      Consent: 5: Participant
    section Study
      Baseline: 4: Participant
      Intervention: 3: Participant
      Follow-up: 4: Participant
    section Completion
      Final Assessment: 5: Participant
      Feedback: 4: Participant`,

    er: `EXAMPLE ER DIAGRAM:
erDiagram
    RESEARCHER ||--o{ STUDY : conducts
    STUDY ||--o{ PARTICIPANT : includes
    PARTICIPANT ||--o{ DATA_POINT : generates
    STUDY {
        string study_id
        string title
        date start_date
        date end_date
    }
    PARTICIPANT {
        string participant_id
        int age
        string gender
        date enrollment_date
    }`,

    state: `EXAMPLE STATE DIAGRAM:
stateDiagram-v2
    [*] --> Planning
    Planning --> DataCollection : Design Complete
    DataCollection --> Analysis : Data Ready
    Analysis --> Review : Results Ready
    Review --> Publication : Approved
    Review --> Analysis : Revisions Needed
    Publication --> [*]`,

    class: `EXAMPLE CLASS DIAGRAM:
classDiagram
    class ResearchStudy {
        +String studyId
        +String title
        +Date startDate
        +addParticipant()
        +collectData()
    }
    class Participant {
        +String participantId
        +int age
        +String gender
        +enroll()
        +withdraw()
    }
    ResearchStudy "1" --> "*" Participant : includes`
  },

  GENERATION_REQUEST: `Now generate a {DIAGRAM_TYPE} diagram based on the following requirements:

DESCRIPTION: {DESCRIPTION}
DIAGRAM TYPE: {DIAGRAM_TYPE}
DIRECTION: {DIRECTION}
ADDITIONAL REQUIREMENTS: {ADDITIONAL_REQUIREMENTS}
CONTEXT: {CONTEXT}

CRITICAL REMINDERS:
- Use ONLY alphanumeric characters, spaces, hyphens, and underscores in node labels
- NO special characters: / \\ < > { } | ( ) + * % $ # @ ! ^ &
- Keep node labels simple and clear
- Use proper spacing around arrows: A --> B
- Start with diagram type declaration (e.g., flowchart TD)

Generate a clear, professional Mermaid diagram that accurately represents the described process or concept. Ensure the diagram is well-structured, uses appropriate node types and connections, and follows Mermaid.js syntax exactly. Do not include any explanatory text or markdown formatting - provide only the raw Mermaid code.

EXAMPLE OF CORRECT SYNTAX:
flowchart TD
    A[Start Process] --> B[Collect Data]
    B --> C{Quality Check}
    C -->|Pass| D[Analyze Results]
    C -->|Fail| B
    D --> E[Generate Report]`
};

// Research-specific prompt templates
export const RESEARCH_PROMPTS = {
  METHODOLOGY: `Create a research methodology flowchart that shows the systematic approach to conducting research. Include key phases like problem identification, literature review, hypothesis formation, research design, data collection, analysis, and reporting. Use decision points where appropriate and show iterative processes.`,

  DATA_ANALYSIS: `Generate a data analysis workflow diagram that demonstrates the process from raw data to insights. Include steps like data cleaning, exploratory analysis, statistical testing, visualization, and interpretation. Show quality control checkpoints and validation steps.`,

  EXPERIMENTAL_DESIGN: `Create an experimental design flowchart that outlines the process of designing and conducting a scientific experiment. Include hypothesis formation, variable identification, control group setup, data collection protocols, and analysis procedures.`,

  LITERATURE_REVIEW: `Design a systematic literature review process diagram showing the steps from research question formulation through database searching, screening, quality assessment, data extraction, synthesis, and reporting.`,

  SURVEY_RESEARCH: `Generate a survey research methodology diagram that includes survey design, sampling strategy, data collection methods, response validation, statistical analysis, and results interpretation.`,

  QUALITATIVE_RESEARCH: `Create a qualitative research process flowchart showing participant recruitment, data collection methods (interviews, focus groups, observations), transcription, coding, theme development, and analysis.`
};

// Common diagram patterns for research
export const RESEARCH_PATTERNS = {
  DECISION_TREE: `Use diamond shapes {} for decision points, rectangles [] for processes, and circles () for start/end points. Include clear Yes/No paths and ensure all paths lead to conclusions.`,

  PROCESS_FLOW: `Use rectangles [] for sequential steps, diamonds {} for decision points, and parallelograms for input/output. Show the logical flow with arrows and include feedback loops where appropriate.`,

  HIERARCHICAL: `Use a top-down structure with clear parent-child relationships. Group related concepts and use consistent spacing and alignment for visual clarity.`,

  TEMPORAL: `Show chronological progression with clear time indicators. Use consistent intervals and include milestones or key events. Consider using timeline or Gantt chart formats for time-based processes.`,

  COMPARATIVE: `Use parallel structures to show comparisons or alternatives. Clearly label different approaches or options and show how they converge or diverge.`
};
