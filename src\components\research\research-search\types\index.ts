/**
 * Types for Research Search functionality
 */

export interface SearchMessage {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'deep_research_outline' | 'deep_research_progress' | 'deep_research_section' | 'deep_research_final';
  content: string;
  timestamp: Date;
  searchQuery?: string;
  sources?: SearchSource[];
  citations?: Citation[];
  // Deep research specific fields
  deepResearchSession?: DeepResearchSession;
  deepResearchOutline?: DeepResearchOutline;
  deepResearchProgress?: DeepResearchProgress;
  completedSection?: CompletedSection;
  isDeepResearch?: boolean;
}

export interface SearchSource {
  id: string;
  title: string;
  url: string;
  snippet: string;
  domain: string;
  publishedDate?: string;
  score: number;
  type: 'academic' | 'web' | 'news' | 'book';
}

export interface Citation {
  id: string;
  text: string;
  sourceId: string;
  url: string;
  title: string;
  position: number;
}

export interface SearchSession {
  id: string;
  title: string;
  messages: SearchMessage[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  model: string;
  totalQueries: number;
  isActive: boolean;
}

export interface TavilySearchResult {
  query: string;
  follow_up_questions?: string[];
  answer: string;
  images: string[];
  results: TavilyResult[];
  response_time: number;
}

export interface TavilyResult {
  title: string;
  url: string;
  content: string;
  raw_content?: string;
  score: number;
  published_date?: string;
}

export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  maxTokens: number;
  cost: 'low' | 'medium' | 'high';
  strengths: string[];
  bestFor: string[];
  supportsImages?: boolean;
}

export interface SearchOptions {
  model: string;
  maxResults: number;
  searchDepth: 'basic' | 'advanced';
  includeImages: boolean;
  includeAnswer: boolean;
  domains?: string[];
  excludeDomains?: string[];
  publishedAfter?: string;
  publishedBefore?: string;
}

export interface SearchProgress {
  stage: 'searching' | 'analyzing' | 'generating' | 'complete';
  message: string;
  progress: number;
}

export interface SearchError {
  type: 'api_error' | 'network_error' | 'validation_error' | 'rate_limit';
  message: string;
  details?: any;
}

export interface SearchStats {
  totalSearches: number;
  totalSessions: number;
  averageResponseTime: number;
  mostUsedModel: string;
  topDomains: string[];
}

export interface UserPreferences {
  defaultModel: string;
  defaultSearchDepth: 'basic' | 'advanced';
  maxResults: number;
  autoSave: boolean;
  showSources: boolean;
  showCitations: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// Deep Research Types
export interface DeepResearchOutline {
  id: string;
  title: string;
  description: string;
  researchType: ResearchType;
  estimatedWordCount: number;
  estimatedTimeHours: number;
  points: ResearchPoint[];
  totalPoints: number;
  createdAt: Date;
  abstract?: string;
  keywords?: string[];
  targetAudience?: string;
}

export interface ResearchType {
  id: string;
  name: string;
  description: string;
  minSections: number;
  maxSections: number;
  minWordsPerSection: number;
  maxWordsPerSection: number;
  totalWordTarget: number;
  requiredSections: string[];
  optionalSections: string[];
  citationDensity: 'light' | 'moderate' | 'heavy';
  academicLevel: 'undergraduate' | 'graduate' | 'doctoral' | 'professional';
}

export interface ResearchPoint {
  id: string;
  pointNumber: number;
  title: string;
  description: string;
  subpoints: ResearchSubpoint[];
  status: 'pending' | 'researching' | 'completed' | 'error';
  searchQueries?: string[];
  content?: string;
  sources?: SearchSource[];
  citations?: Citation[];
  wordCount?: number;
  completedAt?: Date;
}

export interface ResearchSubpoint {
  id: string;
  subpointNumber: number;
  title: string;
  description: string;
  searchQueries?: string[];
}

export interface DeepResearchSession {
  id: string;
  title: string;
  originalQuery: string;
  outline: DeepResearchOutline;
  researchType: ResearchType;
  currentPoint: number;
  totalPoints: number;
  status: 'outline_pending' | 'outline_approved' | 'researching' | 'completed' | 'error';
  aiAssistants: AIAssistant[];
  completedPoints: CompletedPoint[];
  finalReport?: string;
  formattedReport?: string; // Clean formatted version without markdown
  allReferences: ResearchReference[];
  consolidatedReferences?: string; // Formatted reference list
  researchMetadata: ResearchMetadata;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  model: string;
}

export interface ResearchMetadata {
  totalWordCount: number;
  totalCitations: number;
  academicSources: number;
  governmentSources: number;
  averageSourceQuality: number;
  researchDuration: number; // in minutes
  sectionsCompleted: number;
  keyTopics: string[];
  confidenceScore: number; // 0-100
}

export interface AIAssistant {
  id: string;
  assistantNumber: number;
  name: string;
  specialization: string;
  assignedPointId: string;
  status: 'waiting' | 'active' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  progress: number; // 0-100
  currentTask: string;
}

export interface DeepResearchProgress {
  pointNumber: number;
  pointId: string;
  pointTitle: string;
  assistantId: string;
  assistantName: string;
  status: 'pending' | 'searching' | 'analyzing' | 'writing' | 'completed' | 'error';
  message: string;
  progress: number; // 0-100
  startTime?: Date;
  endTime?: Date;
  currentSubpoint?: string;
  searchResults?: TavilySearchResult[];
  error?: string;
}

export interface CompletedPoint {
  pointId: string;
  pointNumber: number;
  title: string;
  content: string;
  wordCount: number;
  sources: SearchSource[];
  citations: Citation[];
  completedAt: Date;
  searchQueries: string[];
  keyFindings: string[];
  assistantId: string;
  subpointResults: SubpointResult[];
}

export interface SubpointResult {
  subpointId: string;
  title: string;
  searchQueries: string[];
  sources: SearchSource[];
  keyInsights: string[];
}

export interface ResearchReference {
  id: string;
  title: string;
  authors: string[];
  url: string;
  domain: string;
  publishedDate?: string;
  type: 'academic' | 'web' | 'news' | 'book' | 'report';
  citationCount: number;
  isAcademic: boolean;
  abstract?: string;
  doi?: string;
}

export interface DeepResearchOptions {
  model: string;
  researchType: ResearchType;
  maxResultsPerSection: number;
  searchDepth: 'basic' | 'advanced' | 'comprehensive';
  includeImages: boolean;
  academicFocus: boolean;
  governmentSourcesOnly?: boolean;
  peerReviewedOnly?: boolean;
  minWordCountPerSection: number;
  maxWordCountPerSection: number;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  includeAbstract: boolean;
  includeConclusion: boolean;
  includeExecutiveSummary: boolean;
  autoApproveOutline: boolean;
  sourceQualityThreshold: number; // 0-1
  maxCitationsPerSection: number;
  requireInTextCitations: boolean;
  generateConsolidatedReferences: boolean;
}

export interface OutlineEditRequest {
  action: 'add_point' | 'remove_point' | 'edit_point' | 'reorder_points';
  pointId?: string;
  newPoint?: Partial<ResearchPoint>;
  newOrder?: number[];
  editedContent?: Partial<ResearchPoint>;
}

// Research Planning Types
export interface ResearchPlan {
  id: string;
  query: string;
  researchType: ResearchType;
  suggestedOutline: DeepResearchOutline;
  alternativeOutlines?: DeepResearchOutline[];
  estimatedCost: number;
  estimatedTime: number;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  requiredSources: number;
  createdAt: Date;
}

export interface ResearchTypeTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  examples: string[];
  config: ResearchType;
}

// Enhanced Source Types
export interface EnhancedSearchSource extends SearchSource {
  qualityScore: number; // 0-1
  isGovernment: boolean;
  isPeerReviewed: boolean;
  citationCount?: number;
  impactFactor?: number;
  authorCredentials?: string[];
  institutionAffiliation?: string;
  methodology?: string;
  dataQuality: 'high' | 'medium' | 'low';
  recency: 'current' | 'recent' | 'dated';
}

// Reference Management Types
export interface ConsolidatedReference {
  id: string;
  citationKey: string; // e.g., "Smith2023"
  formattedCitation: string;
  inTextCitations: InTextCitation[];
  source: EnhancedSearchSource;
  usageCount: number;
  sections: string[]; // Which sections use this reference
}

export interface InTextCitation {
  id: string;
  text: string;
  position: number;
  sectionId: string;
  context: string; // Surrounding text
  citationType: 'direct_quote' | 'paraphrase' | 'reference' | 'data';
}

// =====================================================
// ENHANCED ACADEMIC RESEARCH TYPES
// =====================================================

export interface EnhancedSearchSource extends SearchSource {
  qualityScore: number;
  isGovernment: boolean;
  isPeerReviewed: boolean;
  citationCount: number;
  impactFactor?: number;
  authorCredentials: string[];
  institutionAffiliation?: string;
  methodology?: string;
  dataQuality: 'high' | 'medium' | 'low';
  recency: 'current' | 'recent' | 'dated';
}

export interface EnhancedCitation extends Citation {
  author: string;
  year: string;
  page?: string;
  citationType: 'direct_quote' | 'paraphrase' | 'reference' | 'data';
  confidence: number;
  context: string;
}

export interface AcademicWritingOptions {
  model: string;
  citationStyle: 'apa' | 'mla' | 'chicago' | 'harvard';
  academicLevel: 'undergraduate' | 'graduate' | 'doctoral' | 'professional';
  minWordCount: number;
  maxWordCount: number;
  minCitations: number;
  maxCitations: number;
  writingStyle: 'formal' | 'technical' | 'review' | 'analytical';
  includeMethodology: boolean;
  includeLimitations: boolean;
  includeImplications: boolean;
  requireOriginalAnalysis: boolean;
}

export interface AcademicSection {
  title: string;
  content: string;
  wordCount: number;
  citationCount: number;
  qualityScore: number;
  readabilityScore: number;
  academicRigor: number;
  originalityScore: number;
  citations: EnhancedCitation[];
  keyPoints: string[];
  methodology?: string;
  limitations?: string[];
  implications?: string[];
}

export interface EnhancedResearchMetadata extends ResearchMetadata {
  searchResultsCollected: number;
  citationsExtracted: number;
  referencesFormatted: number;
  qualityScore: number;
  academicSourcesPercentage: number;
  governmentSourcesPercentage: number;
  averageCitationsPerSection: number;
  totalSearchQueries: number;
  averageSourceQuality: number;
}

export interface ResearchOutlineData {
  id: string;
  session_id: string;
  user_id: string;
  title: string;
  description: string;
  research_type: string;
  total_points: number;
  estimated_word_count: number;
  estimated_time_hours: number;
  status: string;
  outline_data: any;
  keywords: string[];
  target_audience: string;
  abstract: string;
  created_at: string;
  updated_at: string;
}

export interface ResearchPointData {
  id: string;
  outline_id: string;
  point_number: number;
  title: string;
  description: string;
  status: string;
  search_queries: string[];
  content: string;
  word_count: number;
  citation_count: number;
  quality_score: number;
  ai_assistant_id: string;
  ai_assistant_name: string;
  subpoints: any;
  key_findings: string[];
  completed_at: string;
  created_at: string;
  updated_at: string;
}

export interface ResearchSourceData {
  id: string;
  session_id: string;
  point_id: string;
  user_id: string;
  title: string;
  url: string;
  domain: string;
  snippet: string;
  content: string;
  published_date: string;
  authors: string[];
  doi: string;
  journal: string;
  volume: string;
  issue: string;
  pages: string;
  source_type: string;
  quality_score: number;
  relevance_score: number;
  is_peer_reviewed: boolean;
  is_government: boolean;
  is_academic: boolean;
  citation_count: number;
  impact_factor: number;
  institution_affiliation: string;
  methodology: string;
  data_quality: string;
  recency: string;
  search_query: string;
  tavily_score: number;
  created_at: string;
  updated_at: string;
}

export interface ResearchCitationData {
  id: string;
  session_id: string;
  point_id: string;
  source_id: string;
  user_id: string;
  citation_text: string;
  citation_type: string;
  position_in_text: number;
  context_text: string;
  formatted_citation: string;
  citation_key: string;
  in_text_format: string;
  section_id: string;
  page_number: string;
  confidence_score: number;
  created_at: string;
  updated_at: string;
}

export interface ResearchReferenceData {
  id: string;
  session_id: string;
  user_id: string;
  citation_key: string;
  formatted_apa: string;
  formatted_mla: string;
  formatted_chicago: string;
  formatted_harvard: string;
  source_data: any;
  usage_count: number;
  sections_used: string[];
  quality_assessment: any;
  created_at: string;
  updated_at: string;
}

export interface ResearchProgressData {
  id: string;
  session_id: string;
  point_id: string;
  user_id: string;
  stage: string;
  progress_percentage: number;
  current_task: string;
  ai_assistant_id: string;
  ai_assistant_name: string;
  status: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  error_message: string;
  metadata: any;
  created_at: string;
  updated_at: string;
}

export interface ResearchReportData {
  id: string;
  session_id: string;
  outline_id: string;
  user_id: string;
  title: string;
  content: string;
  formatted_content: string;
  word_count: number;
  citation_count: number;
  reference_count: number;
  quality_metrics: any;
  export_formats: any;
  version: number;
  is_final: boolean;
  generated_at: string;
  created_at: string;
  updated_at: string;
}
