/**
 * Google Search Service
 * Handles academic research searches using Google Search via Gemini API
 */

import { GoogleGenAI } from '@google/genai';
import {
  GoogleSearchResult,
  GoogleSearchSource,
  GoogleSearchOptions,
  GoogleCitation,
  GoogleReference,
  DeepResearchSession,
  ResearchOutline,
  SubTopic,
  ResearchData,
  ResearchSection,
  DeepResearchOptions,
  DeepResearchProgress
} from '../types';

export class GoogleSearchService {
  private apiKey: string;
  private ai: GoogleGenAI;

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    
    console.log('Google Gemini API Key loaded:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT FOUND');

    if (!this.apiKey) {
      console.warn('Google Gemini API key not found in environment variables');
      throw new Error('Google Gemini API key not configured');
    }

    this.ai = new GoogleGenAI({
      apiKey: this.apiKey,
    });
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const result = await this.searchAcademic('artificial intelligence', { maxResults: 1 });
      return true;
    } catch (error) {
      console.error('Google Search API connection test failed:', error);
      return false;
    }
  }

  /**
   * Perform academic search using Google Search via Gemini with proper grounding
   */
  async searchAcademic(
    query: string,
    options: Partial<GoogleSearchOptions> = {}
  ): Promise<GoogleSearchResult> {
    if (!this.isConfigured()) {
      throw new Error('Google Gemini API key not configured');
    }

    const searchOptions = {
      maxResults: options.maxResults || 10,
      searchDepth: options.searchDepth || 'advanced',
      includeAcademicSources: options.includeAcademicSources !== false,
      citationStyle: options.citationStyle || 'apa',
      academicFocus: options.academicFocus !== false,
      ...options
    };

    try {
      const startTime = Date.now();

      // Configure Gemini with Google Search tool for proper grounding
      const tools = [
        {
          googleSearch: {}
        }
      ];

      const config = {
        thinkingConfig: {
          thinkingBudget: -1,
        },
        tools,
        responseMimeType: 'text/plain',
      };

      const model = 'gemini-2.5-flash';

      // Create enhanced academic prompt with grounding instructions
      const academicPrompt = this.createGroundedAcademicPrompt(query, searchOptions);

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: academicPrompt,
            },
          ],
        },
      ];

      // Generate response with Google Search grounding
      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      const responseTime = Date.now() - startTime;

      // Enhanced parsing with source validation
      const structuredResult = await this.parseGroundedSearchResponse(
        fullResponse,
        query,
        searchOptions,
        responseTime
      );

      // Validate and filter sources
      const validatedResult = await this.validateAndFilterSources(structuredResult);

      return validatedResult;

    } catch (error) {
      console.error('Google Search API error:', error);
      throw new GoogleSearchServiceError({
        type: 'api_error',
        message: `Google Search failed: ${error.message}`,
        details: error
      });
    }
  }

  /**
   * Create enhanced academic prompt with proper Google Search grounding
   */
  private createGroundedAcademicPrompt(query: string, options: GoogleSearchOptions): string {
    const academicFocus = options.academicFocus ?
      'CRITICAL: Focus EXCLUSIVELY on academic sources, research papers, scholarly articles, peer-reviewed publications, university websites, and educational institutions.' :
      'Include both academic and high-quality authoritative web sources.';

    const citationStyle = options.citationStyle.toUpperCase();

    // Enhanced academic search terms
    const academicSearchTerms = [
      `"${query}" site:edu`,
      `"${query}" site:org academic`,
      `"${query}" research paper`,
      `"${query}" scholarly article`,
      `"${query}" peer reviewed`,
      `"${query}" journal article`,
      `"${query}" university study`,
      `"${query}" academic publication`
    ];

    return `
You are an expert academic research assistant with access to Google Search. Your task is to conduct comprehensive academic research on: "${query}"

CRITICAL SEARCH INSTRUCTIONS:
1. Use Google Search to find REAL, WORKING sources
2. ${academicFocus}
3. Search using these academic-focused terms: ${academicSearchTerms.join(', ')}
4. Prioritize sources from: .edu domains, academic journals, research institutions, government research agencies
5. VERIFY that all URLs you provide are actual, working links from your search results
6. Do NOT generate or fabricate any URLs - only use URLs found through Google Search

ACADEMIC WRITING REQUIREMENTS:
- Write in formal academic tone with proper scholarly language
- Use evidence-based arguments with specific data and statistics
- Include in-text citations in ${citationStyle} format
- Provide comprehensive analysis, not just summaries
- Use academic vocabulary and sentence structures
- Present balanced perspectives when appropriate
- Include methodology discussions when relevant

SEARCH AND CITATION REQUIREMENTS:
- Conduct multiple Google searches using different academic keywords
- Find at least ${options.maxResults} high-quality academic sources
- Extract exact URLs from search results
- Verify source credibility and academic standing
- Include publication dates, authors, and institutional affiliations
- Cross-reference information across multiple sources

RESPONSE STRUCTURE:
1. **Executive Summary** (150-200 words): Key findings and implications
2. **Comprehensive Analysis** (800-1200 words): Detailed academic discussion with in-text citations
3. **Methodology and Sources**: Description of search strategy and source evaluation
4. **Verified Academic Sources**: List with complete bibliographic information
   Format: Author(s). (Year). Title. Journal/Institution. DOI/URL
5. **Statistical Data and Evidence**: Key numbers, percentages, and research findings
6. **Future Research Directions**: 3-5 specific research questions for further investigation

QUALITY ASSURANCE:
- Every claim must be supported by a credible academic source
- All URLs must be real and accessible
- Citations must follow proper ${citationStyle} format
- Language must meet academic publication standards
- Sources must be from the last 10 years unless historical context is needed

Search depth: ${options.searchDepth}
Target academic sources: ${options.maxResults}

Begin your research now using Google Search to find real, credible academic sources.
    `.trim();
  }

  /**
   * Parse grounded Google Search response with enhanced validation
   */
  private async parseGroundedSearchResponse(
    response: string,
    query: string,
    options: GoogleSearchOptions,
    responseTime: number
  ): Promise<GoogleSearchResult> {
    console.log('Raw Grounded Google Search Response:', response); // Debug log

    // Extract executive summary
    const summaryMatch = response.match(/\*\*Executive Summary\*\*:?\s*(.*?)(?=\*\*(?:Comprehensive Analysis|Methodology)\*\*|$)/s);
    const summary = summaryMatch ? summaryMatch[1].trim() : '';

    // Extract comprehensive analysis
    const analysisMatch = response.match(/\*\*Comprehensive Analysis\*\*:?\s*(.*?)(?=\*\*(?:Methodology|Verified Academic Sources|Statistical Data)\*\*|$)/s);
    const analysis = analysisMatch ? analysisMatch[1].trim() : '';

    // Combine summary and analysis for main answer
    const mainAnswer = summary && analysis ? `${summary}\n\n${analysis}` : (analysis || summary || response);

    // Extract verified academic sources
    const sourcesMatch = response.match(/\*\*Verified Academic Sources\*\*:?\s*(.*?)(?=\*\*(?:Statistical Data|Future Research)\*\*|$)/s);
    const sourcesText = sourcesMatch ? sourcesMatch[1] : '';

    // Extract statistical data
    const statsMatch = response.match(/\*\*Statistical Data and Evidence\*\*:?\s*(.*?)(?=\*\*(?:Future Research|$)\*\*|$)/s);
    const statsText = statsMatch ? statsMatch[1] : '';

    // Extract future research directions
    const futureResearchMatch = response.match(/\*\*Future Research Directions\*\*:?\s*(.*?)$/s);
    const futureResearchText = futureResearchMatch ? futureResearchMatch[1] : '';

    // Parse academic sources with enhanced validation
    const sources = await this.parseAcademicSources(sourcesText, response);

    // Parse follow-up questions from future research
    const followUpQuestions = this.parseFollowUpQuestions(futureResearchText);

    // Add statistical data to the answer if available
    const enhancedAnswer = statsText ?
      `${mainAnswer}\n\n**Key Statistical Evidence:**\n${statsText}` :
      mainAnswer;

    console.log('Parsed academic sources:', sources); // Debug log

    return {
      query,
      answer: enhancedAnswer.replace(/^\*\*(?:Executive Summary|Main Answer)\*\*:?\s*/i, '').trim(),
      sources,
      followUpQuestions,
      responseTime,
      totalResults: sources.length
    };
  }

  /**
   * Validate and filter sources for authenticity and academic quality
   */
  private async validateAndFilterSources(result: GoogleSearchResult): Promise<GoogleSearchResult> {
    const validatedSources = [];

    for (const source of result.sources) {
      // Validate URL format
      if (!this.isValidUrl(source.url)) {
        console.warn('Invalid URL detected:', source.url);
        continue;
      }

      // Check for academic domains and credible sources
      if (this.isAcademicSource(source.url, source.domain)) {
        // Additional validation for academic sources
        const validatedSource = {
          ...source,
          type: this.determineSourceType(source.domain) as any,
          score: this.calculateAcademicScore(source)
        };
        validatedSources.push(validatedSource);
      } else if (this.isCredibleSource(source.domain)) {
        // Include credible non-academic sources with lower priority
        const validatedSource = {
          ...source,
          type: 'web' as any,
          score: Math.max(0.3, source.score * 0.7) // Reduce score for non-academic
        };
        validatedSources.push(validatedSource);
      }
    }

    // Sort by academic score (highest first)
    validatedSources.sort((a, b) => b.score - a.score);

    return {
      ...result,
      sources: validatedSources,
      totalResults: validatedSources.length
    };
  }

  /**
   * Extract URLs from the full response as fallback
   */
  private extractUrlsFromFullResponse(response: string): GoogleSearchSource[] {
    const sources: GoogleSearchSource[] = [];
    const urlRegex = /https?:\/\/[^\s\)\]\,\;]+/g;
    const urls = [...new Set(response.match(urlRegex) || [])];

    urls.forEach((url, index) => {
      try {
        const cleanUrl = url.replace(/[.,;)\]]+$/, '');
        const domain = new URL(cleanUrl).hostname;

        // Extract context around URL
        const urlIndex = response.indexOf(url);
        const beforeUrl = response.substring(Math.max(0, urlIndex - 100), urlIndex);
        const afterUrl = response.substring(urlIndex + url.length, urlIndex + url.length + 100);

        // Try to extract title from context
        const contextText = beforeUrl + ' ' + afterUrl;
        const sentences = contextText.split(/[.!?]/).filter(s => s.trim().length > 10);
        const title = sentences.length > 0 ?
          sentences[0].trim().substring(0, 100) :
          `Source from ${domain}`;

        sources.push({
          id: `fallback-source-${index + 1}`,
          title: title.replace(/^\W+/, '').replace(/\W+$/, ''),
          url: cleanUrl,
          snippet: afterUrl.substring(0, 150).trim() || `Information from ${domain}`,
          domain,
          score: Math.max(0.1, 1 - (index * 0.1)),
          type: this.determineSourceType(domain),
          publishedDate: new Date().toISOString().split('T')[0]
        });
      } catch (error) {
        console.warn('Error parsing fallback URL:', url, error);
      }
    });

    return sources;
  }

  /**
   * Parse sources from the response text
   */
  private parseSources(sourcesText: string, referencesText: string): GoogleSearchSource[] {
    const sources: GoogleSearchSource[] = [];

    // Combine sources and references text for parsing
    const combinedText = sourcesText + '\n' + referencesText;

    // Enhanced URL extraction with better context parsing
    const urlRegex = /https?:\/\/[^\s\)\]\,\;]+/g;
    const urls = [...new Set(combinedText.match(urlRegex) || [])]; // Remove duplicates

    // Try to parse structured source entries first
    const structuredSources = this.parseStructuredSources(combinedText);
    if (structuredSources.length > 0) {
      return structuredSources;
    }

    // Fallback to URL-based parsing
    urls.forEach((url, index) => {
      try {
        // Clean URL (remove trailing punctuation)
        const cleanUrl = url.replace(/[.,;)\]]+$/, '');

        // Extract title from surrounding text with better patterns
        const urlIndex = combinedText.indexOf(url);
        const beforeUrl = combinedText.substring(Math.max(0, urlIndex - 300), urlIndex);
        const afterUrl = combinedText.substring(urlIndex + url.length, urlIndex + url.length + 300);

        // Multiple title extraction patterns
        let title = '';

        // Pattern 1: "Title: Something" or "- Title"
        const titlePattern1 = beforeUrl.match(/(?:Title:\s*|[-•]\s*)([^.\n\r]+)(?=\s*https?:\/\/|$)/i);
        if (titlePattern1) {
          title = titlePattern1[1].trim();
        }

        // Pattern 2: Line before URL
        if (!title) {
          const lines = beforeUrl.split('\n');
          const lastLine = lines[lines.length - 1]?.trim();
          if (lastLine && lastLine.length > 10 && lastLine.length < 200) {
            title = lastLine.replace(/^\d+\.\s*/, '').replace(/^[-•]\s*/, '');
          }
        }

        // Pattern 3: Extract from URL path
        if (!title) {
          const urlObj = new URL(cleanUrl);
          const pathParts = urlObj.pathname.split('/').filter(p => p.length > 0);
          if (pathParts.length > 0) {
            title = pathParts[pathParts.length - 1]
              .replace(/[-_]/g, ' ')
              .replace(/\.(html|php|aspx?)$/i, '')
              .replace(/\b\w/g, l => l.toUpperCase());
          }
        }

        // Fallback title
        if (!title) {
          title = `Source ${index + 1}`;
        }

        // Extract domain
        const domain = new URL(cleanUrl).hostname;

        // Determine source type based on domain
        const type = this.determineSourceType(domain);

        // Extract snippet from after URL or surrounding context
        let snippet = '';
        const descPattern = afterUrl.match(/Description:\s*([^.\n\r]+)/i);
        if (descPattern) {
          snippet = descPattern[1].trim();
        } else {
          // Use text after URL as snippet
          snippet = afterUrl.substring(0, 200).trim();
        }

        if (!snippet) {
          snippet = `Information from ${domain}`;
        }

        sources.push({
          id: `google-source-${index + 1}`,
          title: title.substring(0, 200), // Limit title length
          url: cleanUrl,
          snippet: snippet.substring(0, 300) + (snippet.length > 300 ? '...' : ''),
          domain,
          score: Math.max(0.1, 1 - (index * 0.1)), // Decreasing score with minimum
          type,
          publishedDate: new Date().toISOString().split('T')[0]
        });
      } catch (error) {
        console.warn('Error parsing URL:', url, error);
      }
    });

    return sources;
  }

  /**
   * Parse structured source entries from text
   */
  private parseStructuredSources(text: string): GoogleSearchSource[] {
    const sources: GoogleSearchSource[] = [];

    // Look for structured source patterns
    const sourceBlocks = text.split(/(?=Title:|Source \d+:|^\d+\.)/gm);

    sourceBlocks.forEach((block, index) => {
      const titleMatch = block.match(/Title:\s*(.+?)(?=\n|URL:|$)/i);
      const urlMatch = block.match(/URL:\s*(https?:\/\/[^\s\n]+)/i);
      const descMatch = block.match(/Description:\s*(.+?)(?=\n|$)/i);

      if (titleMatch && urlMatch) {
        try {
          const url = urlMatch[1].replace(/[.,;)\]]+$/, '');
          const domain = new URL(url).hostname;

          sources.push({
            id: `structured-source-${index + 1}`,
            title: titleMatch[1].trim(),
            url,
            snippet: descMatch ? descMatch[1].trim() : `Information from ${domain}`,
            domain,
            score: Math.max(0.1, 1 - (index * 0.1)),
            type: this.determineSourceType(domain),
            publishedDate: new Date().toISOString().split('T')[0]
          });
        } catch (error) {
          console.warn('Error parsing structured source:', error);
        }
      }
    });

    return sources;
  }

  /**
   * Parse academic sources with enhanced validation
   */
  private async parseAcademicSources(sourcesText: string, fullResponse: string): Promise<GoogleSearchSource[]> {
    const sources: GoogleSearchSource[] = [];

    // Enhanced URL extraction with academic focus
    const urlRegex = /https?:\/\/[^\s\)\]\,\;\"\'\`]+/g;
    const urls = [...new Set((sourcesText + '\n' + fullResponse).match(urlRegex) || [])];

    // Parse structured academic citations
    const citationBlocks = sourcesText.split(/(?=\d+\.|Author\(s\)|[A-Z][a-z]+,\s*[A-Z]\.)/);

    for (let i = 0; i < citationBlocks.length; i++) {
      const block = citationBlocks[i].trim();
      if (block.length < 20) continue; // Skip short blocks

      // Extract components from academic citation
      const urlMatch = block.match(/https?:\/\/[^\s\)\]\,\;]+/);
      const url = urlMatch ? urlMatch[0].replace(/[.,;)\]]+$/, '') : null;

      if (!url || !this.isValidUrl(url)) continue;

      try {
        const domain = new URL(url).hostname;

        // Extract title (look for patterns like "Title." or "Title:" or quoted titles)
        let title = '';
        const titlePatterns = [
          /(?:Title:|")(.*?)(?:"|\.|\n)/i,
          /^([^.]+)\./,
          /([A-Z][^.]{10,100})\./
        ];

        for (const pattern of titlePatterns) {
          const match = block.match(pattern);
          if (match && match[1] && match[1].length > 10) {
            title = match[1].trim();
            break;
          }
        }

        if (!title) {
          // Fallback: use first substantial line
          const lines = block.split('\n').filter(line => line.trim().length > 15);
          title = lines[0] ? lines[0].trim().substring(0, 100) : `Academic Source ${i + 1}`;
        }

        // Extract authors
        const authorMatch = block.match(/(?:Author\(s\):|By:?)\s*([^.\n]+)/i) ||
                           block.match(/^([A-Z][a-z]+,\s*[A-Z]\..*?)(?:\(|\.|$)/);
        const authors = authorMatch ? [authorMatch[1].trim()] : [];

        // Extract year
        const yearMatch = block.match(/\((\d{4})\)/) || block.match(/(\d{4})/);
        const year = yearMatch ? parseInt(yearMatch[1]) : new Date().getFullYear();

        // Extract journal/publication
        const journalMatch = block.match(/(?:Journal|Publication):\s*([^.\n]+)/i) ||
                            block.match(/In\s+([^.\n]+)/i);
        const journal = journalMatch ? journalMatch[1].trim() : undefined;

        // Create snippet from the block
        const snippet = block.substring(0, 200).replace(/https?:\/\/[^\s]+/g, '').trim();

        sources.push({
          id: `academic-source-${i + 1}`,
          title: title.replace(/^["']|["']$/g, ''), // Remove quotes
          url,
          snippet: snippet || `Academic research from ${domain}`,
          domain,
          authors,
          journal,
          year,
          score: this.calculateAcademicScore({ domain, title, authors, journal } as any),
          type: this.determineSourceType(domain),
          publishedDate: `${year}-01-01`
        });

      } catch (error) {
        console.warn('Error parsing academic source:', url, error);
      }
    }

    // If no structured sources found, extract from URLs with academic validation
    if (sources.length === 0) {
      console.log('No structured academic sources found, extracting from URLs');
      const fallbackSources = this.extractAcademicUrlsFromResponse(fullResponse);
      sources.push(...fallbackSources);
    }

    return sources;
  }

  /**
   * Extract academic URLs from response with validation
   */
  private extractAcademicUrlsFromResponse(response: string): GoogleSearchSource[] {
    const sources: GoogleSearchSource[] = [];
    const urlRegex = /https?:\/\/[^\s\)\]\,\;\"\'\`]+/g;
    const urls = [...new Set(response.match(urlRegex) || [])];

    urls.forEach((url, index) => {
      try {
        const cleanUrl = url.replace(/[.,;)\]]+$/, '');
        if (!this.isValidUrl(cleanUrl)) return;

        const domain = new URL(cleanUrl).hostname;

        // Only include academic or highly credible sources
        if (!this.isAcademicSource(cleanUrl, domain) && !this.isCredibleSource(domain)) {
          return;
        }

        // Extract context around URL for title
        const urlIndex = response.indexOf(url);
        const beforeUrl = response.substring(Math.max(0, urlIndex - 200), urlIndex);
        const afterUrl = response.substring(urlIndex + url.length, urlIndex + url.length + 200);

        // Better title extraction from context
        let title = '';
        const contextLines = (beforeUrl + ' ' + afterUrl).split(/[.\n]/).filter(line =>
          line.trim().length > 15 && !line.includes('http') && line.length < 150
        );

        if (contextLines.length > 0) {
          title = contextLines[0].trim().replace(/^[-•*]\s*/, '');
        }

        if (!title || title.length < 10) {
          title = `Academic Research from ${domain}`;
        }

        sources.push({
          id: `extracted-academic-${index + 1}`,
          title: title.substring(0, 150),
          url: cleanUrl,
          snippet: afterUrl.substring(0, 200).trim() || `Research from ${domain}`,
          domain,
          score: this.calculateAcademicScore({ domain, title } as any),
          type: this.determineSourceType(domain),
          publishedDate: new Date().toISOString().split('T')[0]
        });

      } catch (error) {
        console.warn('Error parsing extracted URL:', url, error);
      }
    });

    return sources;
  }

  /**
   * Determine source type based on domain with enhanced academic detection
   */
  private determineSourceType(domain: string): 'academic' | 'web' | 'news' | 'book' | 'journal' {
    const academicDomains = [
      'scholar.google.com', 'pubmed.ncbi.nlm.nih.gov', 'arxiv.org', 'researchgate.net',
      'ieee.org', 'acm.org', 'jstor.org', 'sciencedirect.com', 'tandfonline.com',
      'sagepub.com', 'cambridge.org', 'oxford.org', 'mit.edu', 'harvard.edu',
      'stanford.edu', 'berkeley.edu', 'nih.gov', 'nsf.gov'
    ];

    const journalDomains = [
      'nature.com', 'science.org', 'cell.com', 'springer.com', 'wiley.com',
      'elsevier.com', 'plos.org', 'bmj.com', 'nejm.org', 'thelancet.com'
    ];

    const newsDomains = [
      'bbc.com', 'cnn.com', 'reuters.com', 'nytimes.com', 'guardian.com',
      'washingtonpost.com', 'wsj.com', 'economist.com'
    ];

    // Check for .edu domains (universities)
    if (domain.endsWith('.edu')) return 'academic';

    // Check for .gov domains (government research)
    if (domain.endsWith('.gov')) return 'academic';

    // Check specific academic domains
    if (academicDomains.some(d => domain.includes(d))) return 'academic';

    // Check journal domains
    if (journalDomains.some(d => domain.includes(d))) return 'journal';

    // Check news domains
    if (newsDomains.some(d => domain.includes(d))) return 'news';

    // Check for books
    if (domain.includes('books.google.com')) return 'book';

    return 'web';
  }

  /**
   * Parse follow-up questions from text
   */
  private parseFollowUpQuestions(followUpText: string): string[] {
    const questions: string[] = [];
    
    // Split by lines and look for question patterns
    const lines = followUpText.split('\n');
    
    lines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed && (trimmed.includes('?') || trimmed.match(/^\d+\./))) {
        const question = trimmed
          .replace(/^\d+\.\s*/, '')
          .replace(/^-\s*/, '')
          .trim();
        
        if (question.length > 10) {
          questions.push(question);
        }
      }
    });

    return questions.slice(0, 5); // Limit to 5 questions
  }

  /**
   * Validate URL format and accessibility
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // Basic URL validation
      if (!urlObj.protocol.startsWith('http')) return false;
      if (!urlObj.hostname || urlObj.hostname.length < 3) return false;

      // Exclude obviously fake or placeholder URLs
      const invalidPatterns = [
        'example.com',
        'placeholder.com',
        'fake-url',
        'dummy-link',
        'test.com',
        'localhost',
        '127.0.0.1',
        'your-domain.com'
      ];

      const hostname = urlObj.hostname.toLowerCase();
      if (invalidPatterns.some(pattern => hostname.includes(pattern))) {
        return false;
      }

      // Check for suspicious patterns
      if (url.includes('...') || url.includes('{{') || url.includes('}}')) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if source is from academic domain
   */
  private isAcademicSource(url: string, domain: string): boolean {
    const academicIndicators = [
      '.edu',
      '.gov',
      'scholar.google',
      'pubmed.ncbi.nlm.nih.gov',
      'arxiv.org',
      'researchgate.net',
      'ieee.org',
      'acm.org',
      'jstor.org',
      'sciencedirect.com',
      'nature.com',
      'science.org',
      'springer.com',
      'wiley.com',
      'cambridge.org',
      'oxford.org',
      'nih.gov',
      'nsf.gov'
    ];

    return academicIndicators.some(indicator =>
      domain.includes(indicator) || url.includes(indicator)
    );
  }

  /**
   * Check if source is from credible domain
   */
  private isCredibleSource(domain: string): boolean {
    const credibleDomains = [
      'bbc.com',
      'reuters.com',
      'nytimes.com',
      'washingtonpost.com',
      'economist.com',
      'wsj.com',
      'guardian.com',
      'who.int',
      'cdc.gov',
      'fda.gov',
      'worldbank.org',
      'un.org',
      'oecd.org'
    ];

    return credibleDomains.some(credible => domain.includes(credible));
  }

  /**
   * Calculate academic score with enhanced criteria
   */
  private calculateAcademicScore(source: Partial<GoogleSearchSource>): number {
    let score = 0.3; // Base score

    const domain = source.domain || '';
    const title = (source.title || '').toLowerCase();
    const snippet = (source.snippet || '').toLowerCase();

    // Domain-based scoring (highest priority)
    if (domain.endsWith('.edu')) score += 0.4;
    if (domain.endsWith('.gov')) score += 0.35;
    if (domain.includes('scholar.google')) score += 0.4;
    if (domain.includes('pubmed') || domain.includes('ncbi')) score += 0.4;
    if (domain.includes('arxiv')) score += 0.35;
    if (domain.includes('nature.com') || domain.includes('science.org')) score += 0.4;
    if (domain.includes('ieee.org') || domain.includes('acm.org')) score += 0.35;
    if (domain.includes('jstor') || domain.includes('sciencedirect')) score += 0.3;

    // Academic content indicators
    const academicTerms = [
      'research', 'study', 'analysis', 'investigation', 'peer-reviewed',
      'journal', 'publication', 'academic', 'scholarly', 'university',
      'methodology', 'findings', 'results', 'conclusion', 'abstract'
    ];

    academicTerms.forEach(term => {
      if (title.includes(term)) score += 0.05;
      if (snippet.includes(term)) score += 0.03;
    });

    // Author indicators
    if (source.authors && source.authors.length > 0) score += 0.1;
    if (source.journal) score += 0.15;

    // Recency bonus for academic sources
    if (source.year) {
      const currentYear = new Date().getFullYear();
      const age = currentYear - source.year;
      if (age <= 2) score += 0.1;
      else if (age <= 5) score += 0.05;
      else if (age > 20) score -= 0.1; // Penalty for very old sources
    }

    return Math.min(1.0, Math.max(0.1, score));
  }

  /**
   * Transform sources to citations
   */
  transformToCitations(sources: GoogleSearchSource[], citationStyle: string = 'apa'): GoogleCitation[] {
    return sources.map((source, index) => ({
      id: `citation-${source.id}`,
      text: source.snippet,
      sourceId: source.id,
      url: source.url,
      title: source.title,
      position: index + 1,
      inTextFormat: this.generateInTextCitation(source, citationStyle)
    }));
  }

  // ===== DEEP RESEARCH METHODS =====

  /**
   * Generate a comprehensive research outline for deep research
   */
  async generateResearchOutline(
    query: string,
    options: Partial<DeepResearchOptions> = {}
  ): Promise<ResearchOutline> {
    if (!this.isConfigured()) {
      throw new Error('Google Gemini API key not configured');
    }

    const researchOptions = {
      maxSubtopics: options.maxSubtopics || 6,
      researchDepth: options.researchDepth || 'advanced',
      targetAudience: options.targetAudience || 'professional',
      wordsPerSection: options.wordsPerSection || 700,
      ...options
    };

    try {
      const outlinePrompt = this.createOutlinePrompt(query, researchOptions);

      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        responseMimeType: 'application/json',
      };

      const contents = [{
        role: 'user',
        parts: [{ text: outlinePrompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: 'gemini-2.5-flash',
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      // Parse the JSON response
      const outlineData = JSON.parse(fullResponse);

      return {
        id: `outline-${Date.now()}`,
        mainTopic: outlineData.mainTopic,
        description: outlineData.description,
        subtopics: outlineData.subtopics.map((subtopic: any, index: number) => ({
          id: `subtopic-${index + 1}`,
          title: subtopic.title,
          description: subtopic.description,
          keyQuestions: subtopic.keyQuestions || [],
          estimatedWords: researchOptions.wordsPerSection,
          priority: subtopic.priority || 'medium',
          searchTerms: subtopic.searchTerms || [subtopic.title],
          order: index + 1
        })),
        researchQuestions: outlineData.researchQuestions || [],
        estimatedLength: outlineData.subtopics.length * researchOptions.wordsPerSection,
        targetAudience: researchOptions.targetAudience,
        researchDepth: researchOptions.researchDepth
      };

    } catch (error) {
      console.error('Outline generation failed:', error);
      throw new GoogleSearchServiceError({
        type: 'api_error',
        message: `Outline generation failed: ${error.message}`,
        details: error
      });
    }
  }

  /**
   * Batch research multiple subtopics simultaneously
   */
  async batchResearchSubtopics(
    subtopics: SubTopic[],
    options: Partial<GoogleSearchOptions> = {}
  ): Promise<ResearchData[]> {
    if (!this.isConfigured()) {
      throw new Error('Google Gemini API key not configured');
    }

    const searchOptions = {
      maxResults: options.maxResults || 8,
      searchDepth: options.searchDepth || 'advanced',
      includeAcademicSources: options.includeAcademicSources !== false,
      citationStyle: options.citationStyle || 'apa',
      academicFocus: options.academicFocus !== false,
      ...options
    };

    try {
      const startTime = Date.now();

      // Create comprehensive batch research prompt
      const batchPrompt = this.createBatchResearchPrompt(subtopics, searchOptions);

      const tools = [{ googleSearch: {} }];
      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        tools,
        responseMimeType: 'text/plain',
      };

      const contents = [{
        role: 'user',
        parts: [{ text: batchPrompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: 'gemini-2.5-flash',
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      const responseTime = Date.now() - startTime;

      // Parse the batch research response
      return this.parseBatchResearchResponse(fullResponse, subtopics, responseTime);

    } catch (error) {
      console.error('Batch research failed:', error);
      throw new GoogleSearchServiceError({
        type: 'api_error',
        message: `Batch research failed: ${error.message}`,
        details: error
      });
    }
  }

  /**
   * Generate a research section using comprehensive research data
   */
  async generateResearchSection(
    subtopic: SubTopic,
    allResearchData: ResearchData[],
    previousSections: ResearchSection[],
    options: Partial<DeepResearchOptions> = {}
  ): Promise<ResearchSection> {
    if (!this.isConfigured()) {
      throw new Error('Google Gemini API key not configured');
    }

    const sectionOptions = {
      wordsPerSection: options.wordsPerSection || 700,
      citationStyle: options.citationStyle || 'apa',
      targetAudience: options.targetAudience || 'professional',
      ...options
    };

    try {
      // Find research data for this subtopic
      const subtopicResearch = allResearchData.find(rd => rd.subtopicId === subtopic.id);
      if (!subtopicResearch) {
        throw new Error(`No research data found for subtopic: ${subtopic.title}`);
      }

      // Create section generation prompt with context
      const sectionPrompt = this.createSectionPrompt(
        subtopic,
        subtopicResearch,
        previousSections,
        sectionOptions
      );

      const config = {
        thinkingConfig: { thinkingBudget: -1 },
        responseMimeType: 'text/plain',
      };

      const contents = [{
        role: 'user',
        parts: [{ text: sectionPrompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model: 'gemini-2.5-flash',
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      // Parse and structure the section
      return this.parseResearchSection(fullResponse, subtopic, subtopicResearch);

    } catch (error) {
      console.error('Section generation failed:', error);
      throw new GoogleSearchServiceError({
        type: 'api_error',
        message: `Section generation failed: ${error.message}`,
        details: error
      });
    }
  }

  /**
   * Generate in-text citation format
   */
  private generateInTextCitation(source: GoogleSearchSource, style: string): string {
    const year = source.year || new Date().getFullYear();
    const domain = source.domain.replace('www.', '');

    switch (style.toLowerCase()) {
      case 'apa':
        return `(${domain}, ${year})`;
      case 'mla':
        return `(${domain})`;
      case 'chicago':
        return `(${domain} ${year})`;
      default:
        return `(${domain}, ${year})`;
    }
  }
  // ===== DEEP RESEARCH HELPER METHODS =====

  /**
   * Create enhanced outline generation prompt for academic research
   */
  private createOutlinePrompt(query: string, options: Partial<DeepResearchOptions>): string {
    return `
You are an expert academic research consultant. Generate a comprehensive scholarly research outline for: "${query}"

ACADEMIC RESEARCH REQUIREMENTS:
- Create ${options.maxSubtopics} well-structured academic subtopics
- Target audience: ${options.targetAudience} (academic/scholarly focus)
- Research depth: ${options.researchDepth}
- Each section: approximately ${options.wordsPerSection} words
- Focus on evidence-based, peer-reviewed research

OUTLINE CRITERIA:
1. **Academic Rigor**: Each subtopic must be researchable through academic sources
2. **Logical Progression**: Subtopics should build upon each other logically
3. **Research Depth**: Questions should require scholarly investigation
4. **Evidence-Based**: Focus on topics with available peer-reviewed research
5. **Current Relevance**: Include contemporary research perspectives

SEARCH STRATEGY:
For each subtopic, include academic search terms that will find:
- Peer-reviewed journal articles
- University research publications
- Government research reports
- Academic conference proceedings
- Scholarly books and monographs

Please respond with a JSON object in this exact format:
{
  "mainTopic": "Comprehensive academic title for the research",
  "description": "Detailed description of research scope, objectives, and academic significance",
  "subtopics": [
    {
      "title": "Academic subtopic title",
      "description": "Detailed description focusing on scholarly aspects and research dimensions",
      "keyQuestions": [
        "Research question requiring academic investigation",
        "Question about methodology or empirical findings",
        "Question about theoretical frameworks or implications"
      ],
      "priority": "high|medium|low",
      "searchTerms": [
        "academic search term site:edu",
        "scholarly keyword research",
        "peer-reviewed term study"
      ]
    }
  ],
  "researchQuestions": [
    "Primary research question addressing core academic inquiry",
    "Secondary question about methodology or theoretical framework",
    "Question about implications for future research"
  ]
}

ACADEMIC FOCUS AREAS:
- Theoretical foundations and frameworks
- Empirical research and methodologies
- Current scholarly debates and perspectives
- Evidence-based findings and conclusions
- Implications for practice and future research
- Cross-disciplinary connections and insights

Ensure the outline meets academic publication standards and is suitable for scholarly research in the field.
    `.trim();
  }

  /**
   * Create enhanced batch research prompt with academic grounding
   */
  private createBatchResearchPrompt(subtopics: SubTopic[], options: GoogleSearchOptions): string {
    const subtopicList = subtopics.map((st, index) =>
      `${index + 1}. ${st.title}: ${st.description}\n   Key questions: ${st.keyQuestions.join(', ')}\n   Search terms: ${st.searchTerms?.join(', ') || st.title}`
    ).join('\n\n');

    return `
You are an expert academic researcher with access to Google Search. Conduct comprehensive research on these related subtopics for a scholarly report:

${subtopicList}

CRITICAL RESEARCH INSTRUCTIONS:
1. Use Google Search to find REAL academic sources for each subtopic
2. Focus EXCLUSIVELY on academic sources: .edu domains, peer-reviewed journals, research institutions, government research agencies
3. For each subtopic, search using academic-focused terms like:
   - "[subtopic] site:edu"
   - "[subtopic] research paper"
   - "[subtopic] scholarly article"
   - "[subtopic] peer reviewed"
   - "[subtopic] academic study"

ACADEMIC QUALITY REQUIREMENTS:
- Only include sources from credible academic institutions
- Verify all URLs are real and working from your search results
- Extract specific data, statistics, and research findings
- Include publication dates and author information when available
- Cross-reference findings across multiple sources

RESPONSE FORMAT for each subtopic:

**SUBTOPIC [X]: [Title]**

**Academic Research Summary:** [200-300 word comprehensive summary with specific data and findings]

**Key Research Findings:**
- [Specific finding with data/statistics]
- [Research methodology or approach mentioned]
- [Quantitative results or percentages]
- [Expert opinions or conclusions]

**Verified Academic Sources:**
1. Author(s): [Full author names]
   Title: [Complete article/paper title]
   Institution/Journal: [University/Journal name]
   Year: [Publication year]
   URL: [Real, working URL from search]
   Key Data: [Specific statistics or findings from this source]

2. [Continue for 3-5 sources per subtopic]

**Statistical Evidence:**
- [Specific numbers, percentages, or quantitative data found]
- [Sample sizes, study populations, or research scope]

QUALITY ASSURANCE:
- Every URL must be real and accessible from Google Search
- Every claim must be backed by academic evidence
- Include specific numbers and data points
- Use formal academic language and terminology
- Cross-validate information across sources

Search depth: ${options.searchDepth}
Target: 3-5 verified academic sources per subtopic
Begin comprehensive academic research now.
    `.trim();
  }

  /**
   * Create enhanced section generation prompt with academic writing standards
   */
  private createSectionPrompt(
    subtopic: SubTopic,
    researchData: ResearchData,
    previousSections: ResearchSection[],
    options: Partial<DeepResearchOptions>
  ): string {
    const previousContext = previousSections.length > 0
      ? `\n\nPrevious sections for context and flow:\n${previousSections.map(s => `- ${s.title}: ${s.content.substring(0, 300)}...`).join('\n')}`
      : '';

    const academicSources = researchData.sources.filter(s =>
      s.url.includes('.edu') || s.url.includes('.gov') ||
      s.url.includes('scholar.google') || s.url.includes('pubmed') ||
      s.url.includes('nature.com') || s.url.includes('science.org')
    );

    return `
You are writing an academic section for a scholarly research report. Write a comprehensive ${options.wordsPerSection}-word section on: "${subtopic.title}"

SECTION REQUIREMENTS:
- Topic: ${subtopic.title}
- Description: ${subtopic.description}
- Target Length: ${options.wordsPerSection} words (±50 words)
- Citation Style: ${options.citationStyle?.toUpperCase()}
- Audience: ${options.targetAudience}

KEY RESEARCH QUESTIONS TO ADDRESS:
${subtopic.keyQuestions.map(q => `- ${q}`).join('\n')}

ACADEMIC RESEARCH DATA:
Research Summary: ${researchData.summary}

Key Academic Findings:
${researchData.keyFindings.map(f => `- ${f}`).join('\n')}

VERIFIED ACADEMIC SOURCES (use these for citations):
${academicSources.map((s, i) => `${i + 1}. ${s.title}\n   Institution/Journal: ${s.domain}\n   URL: ${s.url}\n   Key Data: ${s.snippet.substring(0, 150)}...`).join('\n\n')}

${previousContext}

ACADEMIC WRITING STANDARDS:
1. **Formal Academic Tone**: Use scholarly language, avoid colloquialisms
2. **Evidence-Based Arguments**: Every claim must be supported by academic sources
3. **Critical Analysis**: Don't just summarize - analyze, synthesize, and evaluate
4. **Proper Citations**: Include 4-6 in-text citations using ${options.citationStyle?.toUpperCase()} format
5. **Logical Structure**: Clear topic sentences, supporting evidence, analysis
6. **Specific Data**: Include statistics, percentages, sample sizes when available
7. **Balanced Perspective**: Present multiple viewpoints when appropriate
8. **Academic Vocabulary**: Use discipline-specific terminology appropriately

SECTION STRUCTURE:
1. **Opening**: Clear topic sentence establishing the section's focus
2. **Literature Review**: What current research shows (with citations)
3. **Analysis**: Critical examination of findings and methodologies
4. **Evidence**: Specific data, statistics, and research results
5. **Synthesis**: How findings relate to broader research questions
6. **Transition**: Connect to overall research narrative

CITATION REQUIREMENTS:
- Use ONLY the verified academic sources provided above
- Include 4-6 in-text citations distributed throughout the section
- Cite specific data points, research findings, and expert opinions
- Follow ${options.citationStyle?.toUpperCase()} format precisely
- Ensure every major claim has academic support

QUALITY STANDARDS:
- Write at graduate-level academic standard
- Use complex sentence structures and academic transitions
- Include specific quantitative data when available
- Maintain objective, scholarly perspective
- Ensure logical flow and coherent argumentation

Format your response as:
**Section Content:** [The complete academic section with proper in-text citations]
**Word Count:** [Exact word count]
**Academic Sources Cited:** [List each source used with full citation]
**Key Data Points:** [Specific statistics or findings highlighted]
    `.trim();
  }

  /**
   * Parse batch research response into structured data
   */
  private parseBatchResearchResponse(
    response: string,
    subtopics: SubTopic[],
    responseTime: number
  ): ResearchData[] {
    const researchDataList: ResearchData[] = [];

    // Split response by subtopic sections
    const subtopicSections = response.split(/\*\*SUBTOPIC \d+:/);

    subtopics.forEach((subtopic, index) => {
      const sectionIndex = index + 1;
      const sectionText = subtopicSections[sectionIndex] || '';

      // Extract research summary
      const summaryMatch = sectionText.match(/\*\*Research Summary:\*\*\s*(.*?)(?=\*\*Key Findings:|$)/s);
      const summary = summaryMatch ? summaryMatch[1].trim() : '';

      // Extract key findings
      const findingsMatch = sectionText.match(/\*\*Key Findings:\*\*\s*(.*?)(?=\*\*Sources:|$)/s);
      const findingsText = findingsMatch ? findingsMatch[1] : '';
      const keyFindings = findingsText
        .split('\n')
        .filter(line => line.trim().startsWith('-'))
        .map(line => line.replace(/^-\s*/, '').trim())
        .filter(finding => finding.length > 0);

      // Extract sources
      const sourcesMatch = sectionText.match(/\*\*Sources:\*\*\s*(.*?)(?=\*\*SUBTOPIC|$)/s);
      const sourcesText = sourcesMatch ? sourcesMatch[1] : '';
      const sources = this.parseSources(sourcesText, '');

      researchDataList.push({
        id: `research-${subtopic.id}`,
        subtopicId: subtopic.id,
        query: subtopic.title,
        sources,
        summary,
        keyFindings,
        searchTime: responseTime,
        totalResults: sources.length
      });
    });

    return researchDataList;
  }

  /**
   * Parse research section response
   */
  private parseResearchSection(
    response: string,
    subtopic: SubTopic,
    researchData: ResearchData
  ): ResearchSection {
    // Extract main content
    const contentMatch = response.match(/\*\*Section Content:\*\*\s*(.*?)(?=\*\*Word Count:|$)/s);
    const content = contentMatch ? contentMatch[1].trim() : response;

    // Extract word count
    const wordCountMatch = response.match(/\*\*Word Count:\*\*\s*(\d+)/);
    const wordCount = wordCountMatch ? parseInt(wordCountMatch[1]) : content.split(/\s+/).length;

    // Extract citations used
    const citationsMatch = response.match(/\*\*Citations Used:\*\*\s*(.*?)$/s);
    const citationsText = citationsMatch ? citationsMatch[1] : '';

    // Create citations from the research data sources
    const citations = this.extractCitationsFromContent(content, researchData.sources);
    const references = this.createReferencesFromSources(researchData.sources);

    return {
      id: `section-${subtopic.id}`,
      subtopicId: subtopic.id,
      title: subtopic.title,
      content,
      wordCount,
      citations,
      references,
      status: 'completed',
      generatedAt: new Date(),
      order: subtopic.order
    };
  }

  /**
   * Extract citations from content based on available sources
   */
  private extractCitationsFromContent(content: string, sources: GoogleSearchSource[]): GoogleCitation[] {
    const citations: GoogleCitation[] = [];

    // Look for citation patterns in the content
    const citationPattern = /\([^)]+\)/g;
    const citationMatches = content.match(citationPattern) || [];

    citationMatches.forEach((match, index) => {
      if (index < sources.length) {
        const source = sources[index];
        citations.push({
          id: `citation-${source.id}`,
          text: source.snippet,
          sourceId: source.id,
          url: source.url,
          title: source.title,
          position: index + 1,
          inTextFormat: match
        });
      }
    });

    return citations;
  }

  /**
   * Create references from sources
   */
  private createReferencesFromSources(sources: GoogleSearchSource[]): GoogleReference[] {
    return sources.map((source, index) => ({
      id: `ref-${source.id}`,
      title: source.title,
      authors: source.authors || [source.domain],
      year: source.year || new Date().getFullYear(),
      url: source.url,
      journal: source.journal,
      apaFormat: this.generateAPAReference(source)
    }));
  }

  /**
   * Generate APA format reference
   */
  private generateAPAReference(source: GoogleSearchSource): string {
    const year = source.year || new Date().getFullYear();
    const authors = source.authors?.join(', ') || source.domain;
    const title = source.title;
    const url = source.url;

    return `${authors} (${year}). ${title}. Retrieved from ${url}`;
  }
}

// Custom error class
class GoogleSearchServiceError extends Error {
  public type: string;
  public details?: any;

  constructor({ type, message, details }: { type: string; message: string; details?: any }) {
    super(message);
    this.name = 'GoogleSearchServiceError';
    this.type = type;
    this.details = details;
  }
}

export const googleSearchService = new GoogleSearchService();
