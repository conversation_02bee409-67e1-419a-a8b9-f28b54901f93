/**
 * History Panel Component
 * Displays and manages previous article submissions and analyses
 */

import React, { useState, useEffect } from 'react';
import { HistoryPanelProps, ArticleSubmission } from '../types';

import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  History, 
  Search, 
  Calendar, 
  FileText, 
  Trash2, 
  Download,
  RefreshCw,
  Filter,
  Clock,
  Target,
  BookOpen,
  AlertCircle
} from "lucide-react";

export const HistoryPanel: React.FC<HistoryPanelProps> = ({
  isOpen,
  onClose,
  onLoadSubmission
}) => {
  const [submissions, setSubmissions] = useState<ArticleSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'draft' | 'error'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'title'>('date');

  // Load submissions on mount
  useEffect(() => {
    if (isOpen) {
      loadSubmissions();
    }
  }, [isOpen]);

  /**
   * Load submissions from storage/database
   */
  const loadSubmissions = async () => {
    setIsLoading(true);
    try {
      // For now, use mock data - in real implementation, this would fetch from Supabase
      const mockSubmissions: ArticleSubmission[] = [
        {
          id: '1',
          user_id: 'user1',
          title: 'Machine Learning in Healthcare Diagnostics',
          metadata: {
            title: 'Machine Learning in Healthcare Diagnostics',
            keywords: ['machine learning', 'healthcare', 'diagnostics', 'AI'],
            researchField: 'Computer Science',
            methodology: 'Experimental study with deep learning models',
            novelty: '0.8',
            contributionLevel: 'High'
          },
          content: {
            type: 'abstract',
            content: 'This study explores the application of machine learning algorithms in healthcare diagnostics...',
            wordCount: 250,
            extractedKeywords: ['machine learning', 'healthcare', 'diagnostics'],
            detectedLanguage: 'English'
          },
          analysis: {
            researchDomain: 'Computer Science - Healthcare Applications',
            methodology: 'Experimental study with deep learning models',
            keyTopics: ['machine learning', 'healthcare', 'diagnostics', 'AI'],
            researchThemes: ['artificial intelligence', 'medical technology'],
            academicField: 'Computer Science',
            noveltyScore: 0.8,
            contributionLevel: 'High',
            recommendedJournals: [],
            analysisConfidence: 0.9,
            generatedAt: new Date('2024-01-15')
          },
          recommendations: [],
          ai_model: 'gemini-2.5-flash',
          status: 'completed',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:35:00Z'
        },
        {
          id: '2',
          user_id: 'user1',
          title: 'Sustainable Energy Solutions for Urban Development',
          metadata: {
            title: 'Sustainable Energy Solutions for Urban Development',
            keywords: ['sustainable energy', 'urban development', 'renewable'],
            researchField: 'Environmental Science',
            methodology: 'Comparative analysis of energy systems',
            novelty: '0.7',
            contributionLevel: 'Medium'
          },
          content: {
            type: 'title',
            content: 'Sustainable Energy Solutions for Urban Development',
            wordCount: 7,
            extractedKeywords: ['sustainable', 'energy', 'urban', 'development'],
            detectedLanguage: 'English'
          },
          analysis: {
            researchDomain: 'Environmental Science',
            methodology: 'Comparative analysis of energy systems',
            keyTopics: ['sustainable energy', 'urban development', 'renewable'],
            researchThemes: ['sustainability', 'urban planning'],
            academicField: 'Environmental Science',
            noveltyScore: 0.7,
            contributionLevel: 'Medium',
            recommendedJournals: [],
            analysisConfidence: 0.75,
            generatedAt: new Date('2024-01-10')
          },
          recommendations: [],
          ai_model: 'gemini-2.5-pro',
          status: 'completed',
          created_at: '2024-01-10T14:20:00Z',
          updated_at: '2024-01-10T14:25:00Z'
        }
      ];

      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmissions(mockSubmissions);
    } catch (error) {
      console.error('Failed to load submissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Filter and sort submissions
   */
  const getFilteredSubmissions = (): ArticleSubmission[] => {
    let filtered = submissions;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(submission =>
        submission.title.toLowerCase().includes(query) ||
        submission.metadata.researchField.toLowerCase().includes(query) ||
        submission.metadata.keywords.some(keyword => 
          keyword.toLowerCase().includes(query)
        )
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(submission => submission.status === filterStatus);
    }

    // Sort submissions
    filtered.sort((a, b) => {
      if (sortBy === 'date') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      } else {
        return a.title.localeCompare(b.title);
      }
    });

    return filtered;
  };

  /**
   * Handle submission selection
   */
  const handleLoadSubmission = (submission: ArticleSubmission) => {
    onLoadSubmission(submission);
    onClose();
  };

  /**
   * Delete submission
   */
  const handleDeleteSubmission = async (submissionId: string) => {
    try {
      // In real implementation, this would delete from database
      setSubmissions(prev => prev.filter(s => s.id !== submissionId));
    } catch (error) {
      console.error('Failed to delete submission:', error);
    }
  };

  /**
   * Get status color
   */
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  /**
   * Format date
   */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const filteredSubmissions = getFilteredSubmissions();

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full sm:max-w-2xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <History className="w-5 h-5 mr-2" />
            Analysis History
          </SheetTitle>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Search and Filters */}
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by title, field, or keywords..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">By Date</SelectItem>
                  <SelectItem value="title">By Title</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm" onClick={loadSubmissions}>
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Submissions List */}
          {!isLoading && (
            <div className="space-y-4">
              {filteredSubmissions.length === 0 ? (
                <div className="text-center py-12">
                  {submissions.length === 0 ? (
                    <>
                      <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        No Analysis History
                      </h3>
                      <p className="text-gray-600">
                        Your previous article analyses will appear here.
                      </p>
                    </>
                  ) : (
                    <>
                      <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        No Results Found
                      </h3>
                      <p className="text-gray-600">
                        Try adjusting your search or filter criteria.
                      </p>
                    </>
                  )}
                </div>
              ) : (
                filteredSubmissions.map((submission) => (
                  <Card key={submission.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
                            {submission.title}
                          </CardTitle>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={getStatusColor(submission.status)}>
                              {submission.status}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {submission.metadata.researchField}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {submission.content.type}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="text-right text-sm text-gray-500">
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {formatDate(submission.created_at)}
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      {/* Content Preview */}
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {submission.content.content.substring(0, 150)}
                        {submission.content.content.length > 150 && '...'}
                      </p>

                      {/* Keywords */}
                      <div className="flex flex-wrap gap-1 mb-4">
                        {submission.metadata.keywords.slice(0, 4).map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                        {submission.metadata.keywords.length > 4 && (
                          <Badge variant="secondary" className="text-xs">
                            +{submission.metadata.keywords.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Analysis Info */}
                      {submission.status === 'completed' && (
                        <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                          <div>
                            <span className="text-gray-500">Confidence:</span>
                            <span className="ml-1 font-medium">
                              {Math.round(submission.analysis.analysisConfidence * 100)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Novelty:</span>
                            <span className="ml-1 font-medium">
                              {Math.round(submission.analysis.noveltyScore * 100)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Model:</span>
                            <span className="ml-1 font-medium text-xs">
                              {submission.ai_model.split('-')[0]}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex justify-between items-center">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleLoadSubmission(submission)}
                            disabled={submission.status === 'error'}
                          >
                            <Target className="w-4 h-4 mr-1" />
                            Load
                          </Button>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSubmission(submission.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      {/* Error State */}
                      {submission.status === 'error' && (
                        <Alert className="mt-3 border-red-200 bg-red-50">
                          <AlertCircle className="h-4 w-4 text-red-600" />
                          <AlertDescription className="text-red-800">
                            Analysis failed. Please try submitting again.
                          </AlertDescription>
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}

          {/* Summary */}
          {!isLoading && submissions.length > 0 && (
            <div className="text-center text-sm text-gray-500 pt-4 border-t">
              Showing {filteredSubmissions.length} of {submissions.length} analyses
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};
