import {
  Graduation<PERSON>ap,
  BookOpen,
  Calculator,
  Atom,
  Globe,
  Palette,
  Music,
  Heart,
  Briefcase,
  Code,
  Brain,
  Microscope,
  Languages,
  Trophy,
  Lightbulb,
  Users,
  Zap,
  Target,
  Star,
  Coffee
} from "lucide-react";
import { EducationLevelOption, TopicSuggestion, TutorAIModel, EducationLevel } from "./types";

// Education levels configuration
export const EDUCATION_LEVELS: EducationLevelOption[] = [
  {
    id: 'basic',
    name: 'Basic',
    description: 'Fundamental concepts with clear explanations and examples',
    icon: BookOpen,
    ageRange: 'Beginner level',
    complexity: 3
  },
  {
    id: 'intermediate',
    name: 'Intermediate',
    description: 'Moderate complexity with detailed analysis and applications',
    icon: GraduationCap,
    ageRange: 'Intermediate level',
    complexity: 5
  },
  {
    id: 'advanced',
    name: 'Advanced',
    description: 'Complex concepts with in-depth research and critical thinking',
    icon: Brain,
    ageRange: 'Advanced level',
    complexity: 8
  },
  {
    id: 'expert',
    name: 'Expert',
    description: 'Cutting-edge research with specialized knowledge and methodology',
    icon: Star,
    ageRange: 'Expert level',
    complexity: 9
  }
];

// Topic suggestions organized by category
export const TOPIC_SUGGESTIONS: TopicSuggestion[] = [
  // Mathematics
  {
    id: 'algebra-basics',
    name: 'Algebra Basics',
    category: 'Mathematics',
    icon: '🔢',
    description: 'Learn fundamental algebraic concepts and equations',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: ['basic arithmetic'],
    relatedTopics: ['geometry', 'calculus']
  },
  {
    id: 'calculus-intro',
    name: 'Introduction to Calculus',
    category: 'Mathematics',
    icon: '📈',
    description: 'Understand derivatives and integrals',
    difficulty: 'intermediate',
    estimatedTime: '60 minutes',
    prerequisites: ['algebra', 'trigonometry'],
    relatedTopics: ['physics', 'engineering']
  },
  {
    id: 'statistics',
    name: 'Statistics & Probability',
    category: 'Mathematics',
    icon: '📊',
    description: 'Learn data analysis and probability theory',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['data science', 'research methods']
  },

  // Science
  {
    id: 'chemistry-basics',
    name: 'Chemistry Fundamentals',
    category: 'Science',
    icon: '⚗️',
    description: 'Explore atoms, molecules, and chemical reactions',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['physics', 'biology']
  },
  {
    id: 'physics-mechanics',
    name: 'Physics: Mechanics',
    category: 'Science',
    icon: '⚡',
    description: 'Understand motion, forces, and energy',
    difficulty: 'intermediate',
    estimatedTime: '55 minutes',
    prerequisites: ['algebra', 'trigonometry'],
    relatedTopics: ['engineering', 'astronomy']
  },
  {
    id: 'biology-cells',
    name: 'Cell Biology',
    category: 'Science',
    icon: '🧬',
    description: 'Learn about cellular structure and function',
    difficulty: 'beginner',
    estimatedTime: '35 minutes',
    prerequisites: [],
    relatedTopics: ['genetics', 'biochemistry']
  },

  // Technology
  {
    id: 'programming-python',
    name: 'Python Programming',
    category: 'Technology',
    icon: '🐍',
    description: 'Learn Python programming from scratch',
    difficulty: 'beginner',
    estimatedTime: '60 minutes',
    prerequisites: [],
    relatedTopics: ['data science', 'web development']
  },
  {
    id: 'ai-machine-learning',
    name: 'Machine Learning Basics',
    category: 'Technology',
    icon: '🤖',
    description: 'Introduction to AI and machine learning concepts',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['programming', 'statistics'],
    relatedTopics: ['data science', 'neural networks']
  },
  {
    id: 'web-development',
    name: 'Web Development',
    category: 'Technology',
    icon: '🌐',
    description: 'Build websites with HTML, CSS, and JavaScript',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: [],
    relatedTopics: ['programming', 'design']
  },

  // Languages
  {
    id: 'spanish-basics',
    name: 'Spanish Basics',
    category: 'Languages',
    icon: '🇪🇸',
    description: 'Learn fundamental Spanish vocabulary and grammar',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: [],
    relatedTopics: ['culture', 'travel']
  },
  {
    id: 'english-grammar',
    name: 'English Grammar',
    category: 'Languages',
    icon: '📝',
    description: 'Master English grammar rules and usage',
    difficulty: 'intermediate',
    estimatedTime: '35 minutes',
    prerequisites: ['basic english'],
    relatedTopics: ['writing', 'literature']
  },

  // History & Social Studies
  {
    id: 'world-war-2',
    name: 'World War II',
    category: 'History',
    icon: '🌍',
    description: 'Understand the causes, events, and impact of WWII',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['basic history'],
    relatedTopics: ['politics', 'geography']
  },
  {
    id: 'economics-basics',
    name: 'Economics Fundamentals',
    category: 'Social Studies',
    icon: '💰',
    description: 'Learn supply and demand, markets, and economic principles',
    difficulty: 'intermediate',
    estimatedTime: '45 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['business', 'politics']
  },

  // Arts & Literature
  {
    id: 'art-history',
    name: 'Art History',
    category: 'Arts',
    icon: '🎨',
    description: 'Explore major art movements and famous artists',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: [],
    relatedTopics: ['culture', 'history']
  },
  {
    id: 'creative-writing',
    name: 'Creative Writing',
    category: 'Literature',
    icon: '✍️',
    description: 'Develop storytelling and creative writing skills',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: ['basic writing'],
    relatedTopics: ['literature', 'english']
  }
];

// AI models optimized for tutoring - includes all models from open-deep-research
export const TUTOR_AI_MODELS: TutorAIModel[] = [
  // Google Models (Direct Gemini API)
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash (Recommended)",
    provider: "Google",
    apiProvider: "gemini",
    description: "Fast, efficient model perfect for interactive tutoring",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Speed', 'Conversational', 'Educational content'],
    bestFor: ['Quick explanations', 'Interactive learning', 'Q&A sessions'],
    educationLevels: ['basic', 'intermediate']
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "Google",
    apiProvider: "gemini",
    description: "Advanced reasoning for complex educational content",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Advanced reasoning', 'Multimodal', 'Research synthesis'],
    bestFor: ['Complex problems', 'Research projects', 'Advanced concepts'],
    educationLevels: ['intermediate', 'advanced', 'expert']
  },

  // OpenRouter Models
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    apiProvider: "openrouter",
    description: "Excellent for detailed explanations and complex topics",
    maxTokens: 200000,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Detailed explanations', 'Complex reasoning', 'Patient teaching'],
    bestFor: ['Advanced topics', 'Step-by-step learning', 'Research guidance'],
    educationLevels: ['intermediate', 'advanced', 'expert']
  },
  {
    id: "anthropic/claude-3-haiku",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    apiProvider: "openrouter",
    description: "Fast and efficient for quick tutoring sessions",
    maxTokens: 200000,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Speed', 'Efficiency', 'Clear explanations'],
    bestFor: ['Quick questions', 'Basic concepts', 'Homework help'],
    educationLevels: ['basic', 'intermediate']
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    apiProvider: "openrouter",
    description: "Versatile model with strong educational capabilities",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'high',
    strengths: ['Versatility', 'Code explanation', 'Creative teaching'],
    bestFor: ['Programming', 'Creative subjects', 'Personalized learning'],
    educationLevels: ['intermediate', 'advanced', 'expert']
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    apiProvider: "openrouter",
    description: "Efficient and cost-effective for educational tasks",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Cost-effective', 'Good reasoning', 'Educational content'],
    bestFor: ['General tutoring', 'Homework help', 'Study assistance'],
    educationLevels: ['basic', 'intermediate']
  },
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    provider: "Google",
    apiProvider: "openrouter",
    description: "Latest Gemini model via OpenRouter",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Latest features', 'Fast responses', 'Multimodal'],
    bestFor: ['Modern tutoring', 'Interactive learning', 'Visual explanations'],
    educationLevels: ['basic', 'intermediate', 'advanced']
  },
  {
    id: "google/gemini-pro",
    name: "Gemini Pro",
    provider: "Google",
    apiProvider: "openrouter",
    description: "Professional-grade Gemini via OpenRouter",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Professional quality', 'Complex reasoning', 'Research support'],
    bestFor: ['Advanced topics', 'Research projects', 'Professional learning'],
    educationLevels: ['advanced', 'expert']
  },
  {
    id: "meta-llama/llama-3.1-405b-instruct",
    name: "Llama 3.1 405B",
    provider: "Meta",
    apiProvider: "openrouter",
    description: "Powerful open-source model for complex educational tasks",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'high',
    strengths: ['Open source', 'Large context', 'Complex reasoning'],
    bestFor: ['Research', 'Advanced mathematics', 'Complex problem solving'],
    educationLevels: ['advanced', 'expert']
  },
  {
    id: "meta-llama/llama-3.1-70b-instruct",
    name: "Llama 3.1 70B",
    provider: "Meta",
    apiProvider: "openrouter",
    description: "Balanced open-source model for educational content",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Open source', 'Good reasoning', 'Educational content'],
    bestFor: ['General tutoring', 'STEM subjects', 'Academic writing'],
    educationLevels: ['intermediate', 'advanced']
  },
  {
    id: "deepseek/deepseek-chat",
    name: "DeepSeek Chat",
    provider: "DeepSeek",
    apiProvider: "openrouter",
    description: "Specialized model for coding and technical subjects",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Coding expertise', 'Technical subjects', 'Problem solving'],
    bestFor: ['Programming', 'Computer science', 'Technical tutorials'],
    educationLevels: ['intermediate', 'advanced', 'expert']
  },
  {
    id: "qwen/qwen-2.5-72b-instruct",
    name: "Qwen 2.5 72B",
    provider: "Alibaba",
    apiProvider: "openrouter",
    description: "Multilingual model with strong reasoning capabilities",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Multilingual', 'Strong reasoning', 'Cultural awareness'],
    bestFor: ['Language learning', 'International studies', 'Cultural topics'],
    educationLevels: ['basic', 'intermediate', 'advanced']
  }
];

// Default tutoring settings (legacy)
export const DEFAULT_TUTOR_SETTINGS = {
  preferredModel: "google/gemini-2.5-flash",
  educationLevel: 'intermediate' as EducationLevel,
  learningStyle: 'visual' as const,
  pace: 'normal' as const,
  includeExamples: true,
  includeQuizzes: false,
  sourcesEnabled: true,
  maxSources: 5,
  language: 'en',
  notifications: {
    sessionReminders: true,
    progressUpdates: true
  }
};

// Default Research Comprehension settings
export const DEFAULT_RESEARCH_COMPREHENSION_SETTINGS = {
  preferredModel: 'gemini-2.5-flash',
  educationLevel: 'intermediate' as EducationLevel,
  autoGenerateQuizzes: true,
  autoGenerateGames: true,
  chunkSize: 1000,
  quizDifficulty: 'intermediate' as const,
  gameDifficulty: 3,
  showHints: true,
  allowRetries: true,
  maxSearchResults: 10,
  searchTimeout: 30,
  ragThreshold: 0.7,
  trackDetailedAnalytics: true,
  shareProgressWithInstructors: false,
  theme: 'light' as const,
  showProgressIndicators: true,
  enableNotifications: true,
  autoSaveInterval: 5
};

// Topic categories for organization
export const TOPIC_CATEGORIES = [
  { id: 'mathematics', name: 'Mathematics', icon: Calculator, color: 'blue' },
  { id: 'science', name: 'Science', icon: Atom, color: 'green' },
  { id: 'technology', name: 'Technology', icon: Code, color: 'purple' },
  { id: 'languages', name: 'Languages', icon: Languages, color: 'orange' },
  { id: 'history', name: 'History', icon: Globe, color: 'red' },
  { id: 'arts', name: 'Arts', icon: Palette, color: 'pink' },
  { id: 'literature', name: 'Literature', icon: BookOpen, color: 'indigo' },
  { id: 'social-studies', name: 'Social Studies', icon: Users, color: 'yellow' }
];

// Learning styles
export const LEARNING_STYLES = [
  { id: 'visual', name: 'Visual', description: 'Learn through images, diagrams, and visual aids' },
  { id: 'auditory', name: 'Auditory', description: 'Learn through listening and verbal explanations' },
  { id: 'kinesthetic', name: 'Kinesthetic', description: 'Learn through hands-on activities and practice' },
  { id: 'reading', name: 'Reading/Writing', description: 'Learn through text and written materials' }
];

// System prompts for different education levels
export const EDUCATION_LEVEL_PROMPTS = {
  basic: "You are a friendly, patient tutor for learners at the basic level. Use clear, simple language with concrete examples. Break down complex concepts into fundamental building blocks. Focus on establishing strong foundational understanding.",
  intermediate: "You are a knowledgeable tutor for intermediate learners. Provide detailed explanations with practical applications. Use moderate complexity and encourage analytical thinking. Connect concepts to real-world scenarios.",
  advanced: "You are an expert tutor for advanced learners. Provide comprehensive explanations with academic depth. Use sophisticated analysis and encourage critical thinking. Support research-level understanding and methodology.",
  expert: "You are a specialized tutor for expert-level learners. Provide cutting-edge, research-level explanations with scholarly depth. Support original thinking, advanced methodologies, and innovative approaches. Focus on the latest developments and complex theoretical frameworks."
};
