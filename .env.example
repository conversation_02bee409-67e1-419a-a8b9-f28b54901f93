# AI API Keys (Only one of these is required)
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_GEMINI_API_KEY=your_gemini_api_key_here
VITE_AI_API_KEY=your_preferred_ai_api_key_here

# For Vercel serverless functions (without VITE_ prefix)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Tavily API for research search
VITE_TAVILY_API_KEY=your_tavily_api_key_here

# Development Settings
VITE_PORT=8081

# Production Settings (Vercel will set these automatically)
# VERCEL_URL=your-app.vercel.app
# NODE_ENV=production
