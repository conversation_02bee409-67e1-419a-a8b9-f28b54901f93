// Data Visualization and Analysis Module Exports

// Main Platform Component
export { DataVisualizationPlatform } from './DataVisualizationPlatform';
export { ResearchDataPlatform } from './components/ResearchDataPlatform';

// Core Components
export { FileUploader } from './components/FileUploader';
export { AnalysisPipeline } from './components/AnalysisPipeline';
export { VisualizationGallery } from './components/VisualizationGallery';
export { QueryInterface } from './components/QueryInterface';
export { PlotlyChart } from './components/PlotlyChart';
export { FallbackChart } from './components/FallbackChart';
export { TestComponent } from './components/TestComponent';
export { GeminiApiTest } from './components/GeminiApiTest';
export { EnhancedDataVisualizationPlatform } from './components/EnhancedDataVisualizationPlatform';
export { EnhancedFileUploader } from './components/EnhancedFileUploader';
export { DataDescriptionForm } from './components/DataDescriptionForm';
export { DataPreview } from './components/DataPreview';
export { EnhancedAnalysisPipeline } from './components/EnhancedAnalysisPipeline';
export { EnhancedVisualizationGallery } from './components/EnhancedVisualizationGallery';
export { EnhancedQueryInterface } from './components/EnhancedQueryInterface';

// Chat Components
export { VizChatInterface } from './components/chat/VizChatInterface';
export { VizChatMessage } from './components/chat/ChatMessage';
export { VizChatInput } from './components/chat/VizChatInput';
export { VegaLiteRenderer } from './components/chat/VegaLiteRenderer';

// Error Handling & Loading
export { DataVisualizationErrorBoundary } from './components/ErrorBoundary';
export { LoadingSpinner, EmptyState } from './components/LoadingStates';

// Services
export { FileProcessingService } from './services/file-processing.service';
export { GeminiAnalysisService } from './services/gemini-analysis.service';
export { FallbackAnalysisService } from './services/fallback-analysis.service';
export { ResearchAnalysisService } from './services/research-analysis.service';
export { ResearchReportService } from './services/research-report.service';
export { VegaLiteService, vegaLiteService } from './services/vega-lite.service';

// Utilities
export {
  matchQuote,
  getValidVegaSpec,
  inferDataFields,
  prepareDataForVega,
  getVegaTheme
} from './utils/vega-utils';

// Store
export { useDataVisualizationStore } from './stores/data-visualization.store';

// Types
export type {
  UploadedFile,
  DataAnalysisResult,
  VisualizationConfig,
  QueryRequest,
  QueryResponse,
  GeminiCodeExecutionRequest,
  GeminiCodeExecutionResponse,
  DataVisualizationState,
  FileValidationResult,
  AnalysisPipelineStep,
  SupportedFileType,
  // Chat and Vega-Lite types
  ChatSession,
  VegaLiteSpec,
  DataField,
  // Research-specific types
  ResearchAnalysisResult,
  ResearchReport,
  ResearchFigure,
  ResearchTable,
  StatisticalTestRecommendation,
  ResearchHypothesis,
  ResearchFinding,
  PowerAnalysisResult,
  DemographicAnalysis,
  StatisticalAnnotation
} from './types';

// Constants from constants.ts
export {
  DATA_VIZ_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ANALYSIS_PROMPTS,
  PLOTLY_THEMES,
  CHART_CONFIGS,
  DATA_TYPE_DETECTION,
  LOADING_STATES
} from './constants';

// Constants from types.ts
export {
  SUPPORTED_FILE_TYPES,
  FILE_SIZE_LIMITS,
  VISUALIZATION_TYPES
} from './types';

