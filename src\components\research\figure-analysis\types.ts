/**
 * Figure Analysis Types
 * Comprehensive type definitions for AI-powered figure analysis
 */

// Core Figure Types
export interface FigureFile {
  id: string;
  name: string;
  type: string;
  size: number;
  data: string; // base64 encoded
  url: string;
  uploadedAt: Date;
  metadata?: FigureMetadata;
}

export interface FigureMetadata {
  width: number;
  height: number;
  dpi?: number;
  colorSpace?: string;
  hasText?: boolean;
  fileFormat: string;
  exifData?: Record<string, any>;
}

// Analysis Result Types
export interface FigureAnalysisResult {
  figureId: string;
  timestamp: Date;
  qualityAssessment: QualityAssessment;
  captionSuggestions: CaptionSuggestion[];
  advancedAnalysis: AdvancedAnalysis;
  recommendations: Recommendation[];
  confidence: number;
  processingTime: number;
}

export interface QualityAssessment {
  overall: QualityScore;
  resolution: ResolutionAnalysis;
  clarity: ClarityAnalysis;
  readability: ReadabilityAnalysis;
  accessibility: AccessibilityAnalysis;
  publicationCompliance: PublicationCompliance;
}

export interface QualityScore {
  score: number; // 0-100
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  issues: QualityIssue[];
  improvements: string[];
}

export interface QualityIssue {
  type: 'resolution' | 'clarity' | 'readability' | 'accessibility' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestion: string;
}

export interface ResolutionAnalysis {
  dpi: number;
  isHighRes: boolean;
  printQuality: 'poor' | 'acceptable' | 'good' | 'excellent';
  webQuality: 'poor' | 'acceptable' | 'good' | 'excellent';
  recommendations: string[];
}

export interface ClarityAnalysis {
  sharpness: number; // 0-100
  contrast: number; // 0-100
  brightness: number; // 0-100
  colorBalance: number; // 0-100
  noiseLevel: number; // 0-100
  overallClarity: 'poor' | 'fair' | 'good' | 'excellent';
}

export interface ReadabilityAnalysis {
  textDetected: boolean;
  textClarity: number; // 0-100
  fontSize: 'too-small' | 'acceptable' | 'optimal' | 'too-large';
  fontContrast: number; // 0-100
  labelVisibility: 'poor' | 'fair' | 'good' | 'excellent';
  axisReadability?: number; // for charts/graphs
}

export interface AccessibilityAnalysis {
  colorBlindFriendly: boolean;
  contrastRatio: number;
  altTextSuggestion: string;
  screenReaderCompatible: boolean;
  wcagCompliance: 'AA' | 'AAA' | 'non-compliant';
  improvements: string[];
}

export interface PublicationCompliance {
  journalStandards: JournalStandard[];
  commonIssues: string[];
  formatRecommendations: string[];
  citationRequirements: string[];
}

export interface JournalStandard {
  name: string;
  compliant: boolean;
  requirements: string[];
  violations: string[];
}

// Caption Generation Types
export interface CaptionSuggestion {
  id: string;
  text: string;
  style: CitationStyle;
  confidence: number;
  context: CaptionContext;
  variations: string[];
}

export interface CaptionContext {
  figureType: FigureType;
  researchField: string;
  purpose: 'descriptive' | 'analytical' | 'comparative' | 'explanatory';
  audience: 'general' | 'academic' | 'expert';
  language: string;
}

export type CitationStyle = 'APA' | 'MLA' | 'Chicago' | 'IEEE' | 'Harvard' | 'Vancouver' | 'Nature';

export type FigureType = 
  | 'chart' | 'graph' | 'plot' | 'diagram' | 'flowchart' | 'map' 
  | 'photograph' | 'illustration' | 'screenshot' | 'table' | 'other';

// Advanced Analysis Types
export interface AdvancedAnalysis {
  authenticityCheck: AuthenticityAnalysis;
  crossReference: CrossReferenceAnalysis;
  statisticalValidation: StatisticalValidation;
  methodologyExtraction: MethodologyAnalysis;
  biasDetection: BiasAnalysis;
  impactPrediction: ImpactPrediction;
  dataExtraction?: DataExtractionResult;
}

export interface AuthenticityAnalysis {
  manipulationLikelihood: number; // 0-100
  suspiciousAreas: SuspiciousArea[];
  technicalIndicators: TechnicalIndicator[];
  confidence: number;
  recommendation: string;
}

export interface SuspiciousArea {
  coordinates: { x: number; y: number; width: number; height: number };
  type: 'cloning' | 'splicing' | 'enhancement' | 'removal';
  confidence: number;
  description: string;
}

export interface TechnicalIndicator {
  type: string;
  value: number;
  threshold: number;
  suspicious: boolean;
  description: string;
}

export interface CrossReferenceAnalysis {
  similarFigures: SimilarFigure[];
  potentialDuplicates: PotentialDuplicate[];
  relatedWork: RelatedWork[];
  noveltyScore: number; // 0-100
}

export interface SimilarFigure {
  source: string;
  similarity: number; // 0-100
  type: 'identical' | 'very-similar' | 'similar' | 'related';
  url?: string;
  citation?: string;
}

export interface PotentialDuplicate {
  source: string;
  confidence: number;
  differences: string[];
  recommendation: string;
}

export interface RelatedWork {
  title: string;
  authors: string[];
  year: number;
  relevance: number;
  figureDescription: string;
}

export interface StatisticalValidation {
  hasStatistics: boolean;
  statisticalClaims: StatisticalClaim[];
  validationResults: ValidationResult[];
  overallValidity: 'valid' | 'questionable' | 'invalid';
  recommendations: string[];
}

export interface StatisticalClaim {
  claim: string;
  type: 'correlation' | 'significance' | 'trend' | 'comparison';
  confidence: number;
  supportingEvidence: string[];
}

export interface ValidationResult {
  claim: string;
  valid: boolean;
  issues: string[];
  suggestions: string[];
}

export interface MethodologyAnalysis {
  detectedMethods: DetectedMethod[];
  dataVisualizationType: string;
  appropriateness: number; // 0-100
  alternatives: AlternativeMethod[];
  bestPractices: string[];
}

export interface DetectedMethod {
  name: string;
  confidence: number;
  description: string;
  strengths: string[];
  limitations: string[];
}

export interface AlternativeMethod {
  name: string;
  advantages: string[];
  suitability: number; // 0-100
  implementation: string;
}

export interface BiasAnalysis {
  detectedBiases: DetectedBias[];
  overallBiasScore: number; // 0-100, higher = more biased
  recommendations: string[];
  neutralityScore: number; // 0-100
}

export interface DetectedBias {
  type: 'selection' | 'presentation' | 'confirmation' | 'cherry-picking' | 'scale-manipulation';
  severity: 'low' | 'medium' | 'high';
  description: string;
  evidence: string[];
  mitigation: string[];
}

export interface ImpactPrediction {
  citationPotential: number; // 0-100
  visualAppeal: number; // 0-100
  clarity: number; // 0-100
  novelty: number; // 0-100
  overallImpact: 'low' | 'medium' | 'high' | 'very-high';
  factors: ImpactFactor[];
}

export interface ImpactFactor {
  name: string;
  score: number;
  weight: number;
  description: string;
}

export interface DataExtractionResult {
  extractedData: ExtractedDataPoint[];
  dataType: 'numerical' | 'categorical' | 'temporal' | 'mixed';
  confidence: number;
  structure: DataStructure;
}

export interface ExtractedDataPoint {
  label: string;
  value: number | string;
  coordinates?: { x: number; y: number };
  confidence: number;
}

export interface DataStructure {
  axes: AxisInfo[];
  series: SeriesInfo[];
  legends: LegendInfo[];
  annotations: AnnotationInfo[];
}

export interface AxisInfo {
  type: 'x' | 'y' | 'z';
  label: string;
  scale: 'linear' | 'logarithmic' | 'categorical';
  range: [number, number] | string[];
}

export interface SeriesInfo {
  name: string;
  type: 'line' | 'bar' | 'scatter' | 'area' | 'other';
  dataPoints: number;
  color?: string;
}

export interface LegendInfo {
  items: LegendItem[];
  position: string;
}

export interface LegendItem {
  label: string;
  color?: string;
  symbol?: string;
}

export interface AnnotationInfo {
  text: string;
  position: { x: number; y: number };
  type: 'label' | 'callout' | 'note';
}

// Recommendation Types
export interface Recommendation {
  id: string;
  type: 'quality' | 'accessibility' | 'methodology' | 'presentation' | 'compliance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  actionItems: string[];
  estimatedImpact: number; // 0-100
  difficulty: 'easy' | 'medium' | 'hard';
}

// UI State Types
export interface FigureAnalysisState {
  uploadedFigures: FigureFile[];
  selectedFigure: FigureFile | null;
  analysisResults: Map<string, FigureAnalysisResult>;
  isAnalyzing: boolean;
  analysisProgress: AnalysisProgress;
  settings: AnalysisSettings;
  exportOptions: ExportOptions;
}

export interface AnalysisProgress {
  currentStep: string;
  progress: number; // 0-100
  estimatedTimeRemaining: number; // seconds
  steps: AnalysisStep[];
}

export interface AnalysisStep {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  message?: string;
}

export interface AnalysisSettings {
  selectedModels: GeminiModel[];
  analysisDepth: 'quick' | 'standard' | 'comprehensive';
  enableAdvancedFeatures: boolean;
  citationStyle: CitationStyle;
  researchField: string;
  language: string;
  qualityThresholds: QualityThresholds;
}

export interface QualityThresholds {
  minimumDPI: number;
  minimumClarity: number;
  minimumContrast: number;
  accessibilityLevel: 'AA' | 'AAA';
}

export type GeminiModel = 'gemini-2.5-flash' | 'gemini-2.5-pro' | 'gemini-2.0-flash';

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'json' | 'csv';
  includeImages: boolean;
  includeRecommendations: boolean;
  includeRawData: boolean;
  template: 'academic' | 'technical' | 'summary';
}

// API Request/Response Types
export interface AnalysisRequest {
  figureId: string;
  analysisType: AnalysisType[];
  settings: AnalysisSettings;
  context?: AnalysisContext;
}

export type AnalysisType = 
  | 'quality' | 'caption' | 'authenticity' | 'cross-reference' 
  | 'statistical' | 'methodology' | 'bias' | 'impact' | 'data-extraction';

export interface AnalysisContext {
  researchQuestion?: string;
  studyType?: string;
  targetJournal?: string;
  researchField?: string;
  userInstructions?: string;
}

export interface AnalysisResponse {
  success: boolean;
  result?: FigureAnalysisResult;
  error?: string;
  warnings?: string[];
  usage?: {
    tokensUsed: number;
    cost: number;
    processingTime: number;
  };
}
