import { supabase } from '@/lib/supabase';
import { ResearchDocument, DocumentSection } from '../types';
import { researchFileStorageService } from './research-file-storage.service';

/**
 * Service for managing research documents in Supabase database
 * Handles CRUD operations for research documents and their sections
 */
export class ResearchDocumentStorageService {

  /**
   * Save a research document to the database
   */
  async saveResearchDocument(
    document: ResearchDocument,
    file?: File
  ): Promise<{ data: any | null; error: any }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      let filePath: string | null = null;

      // Upload file if provided
      if (file) {
        const { filePath: uploadedPath, error: uploadError } = await researchFileStorageService.uploadResearchFile(
          file,
          user.id,
          document.id
        );

        if (uploadError) {
          console.error('Error uploading file:', uploadError);
          // Continue without file upload - save document metadata only
          console.log('Continuing without file upload, saving document metadata only');
          filePath = null;
        } else {
          filePath = uploadedPath;
        }
      }

      // Prepare document data for database
      const documentData = {
        id: document.id,
        user_id: user.id,
        title: document.title,
        authors: document.authors,
        abstract: document.abstract,
        publication_year: document.publicationYear,
        journal: document.journal,
        doi: document.doi,
        keywords: document.keywords,
        filename: document.filename,
        file_size: document.fileSize,
        file_path: filePath,
        file_type: file?.type || document.filename.split('.').pop() || 'unknown',
        status: document.status,
        processing_progress: document.processingProgress,
        raw_content: document.rawContent,
        summary: document.summary,
        key_findings: document.keyFindings,
        methodology: document.methodology,
        limitations: document.limitations,
        future_work: document.futureWork,
        tags: document.tags,
        categories: document.categories,
        favorite: document.favorite,
        notes: document.notes,
        ai_model_used: null, // Will be set during analysis
        analysis_confidence: document.metadata?.confidence,
        extraction_metadata: {
          extractedAt: document.metadata?.extractedAt?.toISOString() || new Date().toISOString(),
          sources: document.metadata?.sources || [],
          language: document.metadata?.language || 'english',
          citationCount: document.metadata?.citationCount || 0
        },
        uploaded_at: document.uploadedAt?.toISOString() || new Date().toISOString(),
        last_modified: document.lastModified?.toISOString() || new Date().toISOString()
      };

      // Insert document
      const { data: savedDocument, error: documentError } = await supabase
        .from('research_documents')
        .insert(documentData)
        .select()
        .single();

      if (documentError) {
        console.error('Error saving document:', documentError);
        // Clean up uploaded file if document save failed
        if (filePath) {
          await researchFileStorageService.deleteFile(filePath);
        }
        return { data: null, error: documentError };
      }

      // Save document sections
      if (document.sections && document.sections.length > 0) {
        const sectionsData = document.sections.map(section => ({
          id: section.id,
          document_id: document.id,
          title: section.title,
          content: section.content,
          section_type: section.type,
          section_order: section.order,
          word_count: section.wordCount,
          start_page: section.startPage,
          end_page: section.endPage
        }));

        const { error: sectionsError } = await supabase
          .from('research_document_sections')
          .insert(sectionsData);

        if (sectionsError) {
          console.error('Error saving document sections:', sectionsError);
          // Note: We don't rollback the document here as sections can be added later
        }
      }

      // Log activity
      await this.logActivity(user.id, 'upload', 'document', document.id, `Uploaded document: ${document.title}`);

      return { data: savedDocument, error: null };
    } catch (error) {
      console.error('Error in saveResearchDocument:', error);
      return { data: null, error };
    }
  }

  /**
   * Update a research document
   */
  async updateResearchDocument(
    documentId: string,
    updates: Partial<ResearchDocument>
  ): Promise<{ data: any | null; error: any }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      // Prepare update data
      const updateData: any = {};
      
      if (updates.title !== undefined) updateData.title = updates.title;
      if (updates.authors !== undefined) updateData.authors = updates.authors;
      if (updates.abstract !== undefined) updateData.abstract = updates.abstract;
      if (updates.publicationYear !== undefined) updateData.publication_year = updates.publicationYear;
      if (updates.journal !== undefined) updateData.journal = updates.journal;
      if (updates.doi !== undefined) updateData.doi = updates.doi;
      if (updates.keywords !== undefined) updateData.keywords = updates.keywords;
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.processingProgress !== undefined) updateData.processing_progress = updates.processingProgress;
      if (updates.summary !== undefined) updateData.summary = updates.summary;
      if (updates.keyFindings !== undefined) updateData.key_findings = updates.keyFindings;
      if (updates.methodology !== undefined) updateData.methodology = updates.methodology;
      if (updates.limitations !== undefined) updateData.limitations = updates.limitations;
      if (updates.futureWork !== undefined) updateData.future_work = updates.futureWork;
      if (updates.tags !== undefined) updateData.tags = updates.tags;
      if (updates.categories !== undefined) updateData.categories = updates.categories;
      if (updates.favorite !== undefined) updateData.favorite = updates.favorite;
      if (updates.notes !== undefined) updateData.notes = updates.notes;

      // Always update the last_modified timestamp
      updateData.last_modified = new Date().toISOString();

      // Update document
      const { data: updatedDocument, error: updateError } = await supabase
        .from('research_documents')
        .update(updateData)
        .eq('id', documentId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating document:', updateError);
        return { data: null, error: updateError };
      }

      // Log activity
      await this.logActivity(user.id, 'edit', 'document', documentId, `Updated document: ${updates.title || 'Unknown'}`);

      return { data: updatedDocument, error: null };
    } catch (error) {
      console.error('Error in updateResearchDocument:', error);
      return { data: null, error };
    }
  }

  /**
   * Get a research document by ID
   */
  async getResearchDocument(documentId: string): Promise<{ data: ResearchDocument | null; error: any }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { data: null, error: userError || new Error('User not authenticated') };
      }

      // Get document
      const { data: document, error: documentError } = await supabase
        .from('research_documents')
        .select('*')
        .eq('id', documentId)
        .eq('user_id', user.id)
        .single();

      if (documentError) {
        console.error('Error getting document:', documentError);
        return { data: null, error: documentError };
      }

      // Get document sections
      const { data: sections, error: sectionsError } = await supabase
        .from('research_document_sections')
        .select('*')
        .eq('document_id', documentId)
        .order('section_order');

      if (sectionsError) {
        console.error('Error getting document sections:', sectionsError);
        // Continue without sections rather than failing completely
      }

      // Convert database format to ResearchDocument type
      const researchDocument: ResearchDocument = {
        id: document.id,
        title: document.title,
        authors: document.authors || [],
        abstract: document.abstract || '',
        publicationYear: document.publication_year || new Date().getFullYear(),
        journal: document.journal,
        doi: document.doi,
        keywords: document.keywords || [],
        filename: document.filename,
        fileSize: document.file_size,
        uploadedAt: new Date(document.uploaded_at),
        lastModified: new Date(document.last_modified),
        status: document.status,
        processingProgress: document.processing_progress,
        sections: sections?.map(section => ({
          id: section.id,
          title: section.title,
          content: section.content,
          type: section.section_type,
          order: section.section_order,
          wordCount: section.word_count || 0,
          startPage: section.start_page,
          endPage: section.end_page
        })) || [],
        rawContent: document.raw_content || '',
        metadata: {
          extractedAt: document.extraction_metadata?.extractedAt ? new Date(document.extraction_metadata.extractedAt) : new Date(),
          confidence: document.analysis_confidence || 0,
          sources: document.extraction_metadata?.sources || [],
          language: document.extraction_metadata?.language || 'english',
          citationCount: document.extraction_metadata?.citationCount
        },
        summary: document.summary,
        keyFindings: document.key_findings || [],
        methodology: document.methodology || [],
        limitations: document.limitations || [],
        futureWork: document.future_work || [],
        tags: document.tags || [],
        categories: document.categories || [],
        favorite: document.favorite || false,
        notes: document.notes || ''
      };

      // Log activity
      await this.logActivity(user.id, 'view', 'document', documentId, `Viewed document: ${document.title}`);

      return { data: researchDocument, error: null };
    } catch (error) {
      console.error('Error in getResearchDocument:', error);
      return { data: null, error };
    }
  }

  /**
   * Get all research documents for the current user
   */
  async getUserResearchDocuments(
    limit: number = 50,
    offset: number = 0,
    filters?: {
      status?: string;
      tags?: string[];
      search?: string;
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Promise<{ data: ResearchDocument[] | null; error: any; count?: number }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        console.log('❌ User not authenticated for research documents:', userError);
        return { data: [], error: null, count: 0 };
      }

      console.log('✅ User authenticated for documents query:', user.id, user.email);

      // Build query
      let query = supabase
        .from('research_documents')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      if (filters?.search) {
        query = query.or(`title.ilike.%${filters.search}%,abstract.ilike.%${filters.search}%`);
      }

      if (filters?.dateFrom) {
        query = query.gte('created_at', filters.dateFrom.toISOString());
      }

      if (filters?.dateTo) {
        query = query.lte('created_at', filters.dateTo.toISOString());
      }

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data: documents, error: documentsError, count } = await query;

      if (documentsError) {
        console.error('❌ Error getting user documents:', documentsError);
        return { data: [], error: documentsError, count: 0 };
      }

      console.log('📄 Raw documents from database:', documents?.length || 0, documents);

      // Convert to ResearchDocument format (simplified without sections for list view)
      const researchDocuments: ResearchDocument[] = documents?.map(doc => ({
        id: doc.id,
        title: doc.title,
        authors: doc.authors || [],
        abstract: doc.abstract || '',
        publicationYear: doc.publication_year || new Date().getFullYear(),
        journal: doc.journal,
        doi: doc.doi,
        keywords: doc.keywords || [],
        filename: doc.filename,
        fileSize: doc.file_size,
        uploadedAt: new Date(doc.uploaded_at),
        lastModified: new Date(doc.last_modified),
        status: doc.status,
        processingProgress: doc.processing_progress,
        sections: [], // Sections loaded separately for performance
        rawContent: '',
        metadata: {
          extractedAt: doc.extraction_metadata?.extractedAt ? new Date(doc.extraction_metadata.extractedAt) : new Date(),
          confidence: doc.analysis_confidence || 0,
          sources: doc.extraction_metadata?.sources || [],
          language: doc.extraction_metadata?.language || 'english',
          citationCount: doc.extraction_metadata?.citationCount
        },
        summary: doc.summary,
        keyFindings: doc.key_findings || [],
        methodology: doc.methodology || [],
        limitations: doc.limitations || [],
        futureWork: doc.future_work || [],
        tags: doc.tags || [],
        categories: doc.categories || [],
        favorite: doc.favorite || false,
        notes: doc.notes || ''
      })) || [];

      return { data: researchDocuments, error: null, count: count || 0 };
    } catch (error) {
      console.error('Error in getUserResearchDocuments:', error);
      return { data: null, error };
    }
  }

  /**
   * Delete a research document
   */
  async deleteResearchDocument(documentId: string): Promise<{ success: boolean; error?: any }> {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        return { success: false, error: userError || new Error('User not authenticated') };
      }

      // Get document to find file path
      const { data: document, error: getError } = await supabase
        .from('research_documents')
        .select('file_path, title')
        .eq('id', documentId)
        .eq('user_id', user.id)
        .single();

      if (getError) {
        console.error('Error getting document for deletion:', getError);
        return { success: false, error: getError };
      }

      // Delete file from storage if exists
      if (document.file_path) {
        await researchFileStorageService.deleteFile(document.file_path);
      }

      // Delete document (sections will be deleted automatically due to CASCADE)
      const { error: deleteError } = await supabase
        .from('research_documents')
        .delete()
        .eq('id', documentId)
        .eq('user_id', user.id);

      if (deleteError) {
        console.error('Error deleting document:', deleteError);
        return { success: false, error: deleteError };
      }

      // Log activity
      await this.logActivity(user.id, 'delete', 'document', documentId, `Deleted document: ${document.title}`);

      return { success: true };
    } catch (error) {
      console.error('Error in deleteResearchDocument:', error);
      return { success: false, error };
    }
  }

  /**
   * Log user activity
   */
  private async logActivity(
    userId: string,
    activityType: string,
    entityType: string,
    entityId: string,
    description: string,
    metadata?: any
  ): Promise<void> {
    try {
      await supabase
        .from('research_analysis_activity')
        .insert({
          user_id: userId,
          activity_type: activityType,
          entity_type: entityType,
          entity_id: entityId,
          description,
          metadata: metadata || {}
        });
    } catch (error) {
      console.error('Error logging activity:', error);
      // Don't throw error for logging failures
    }
  }
}

// Export singleton instance
export const researchDocumentStorageService = new ResearchDocumentStorageService();
