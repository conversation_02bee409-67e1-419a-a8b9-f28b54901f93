/**
 * Base class for all Agent Tools in the Agentic Targeted Editing System
 */

import { nanoid } from 'nanoid';
import { AgentTool, ToolContext, ToolResult } from './types';

export abstract class BaseAgentTool implements AgentTool {
  public readonly id: string;
  public readonly name: string;
  public readonly description: string;

  constructor(id: string, name: string, description: string) {
    this.id = id;
    this.name = name;
    this.description = description;
  }

  /**
   * Execute the tool with the given context
   */
  async execute(context: ToolContext): Promise<ToolResult> {
    const startTime = Date.now();
    const executionId = nanoid();

    try {
      console.log(`🔧 [${this.id}] Starting execution:`, {
        executionId,
        userRequest: context.userRequest,
        hasTargetText: !!context.targetText,
        documentLength: context.documentContent.length
      });

      // Validate input context
      const validationResult = await this.validateContext(context);
      if (!validationResult.isValid) {
        return this.createErrorResult(
          startTime,
          `Invalid context: ${validationResult.error}`,
          validationResult.warnings
        );
      }

      // Execute the tool-specific logic
      const result = await this.executeInternal(context);
      
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ [${this.id}] Execution completed:`, {
        executionId,
        success: result.success,
        executionTime,
        confidence: result.confidence
      });

      return {
        ...result,
        executionTime
      };

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      
      console.error(`❌ [${this.id}] Execution failed:`, {
        executionId,
        error: error.message,
        executionTime
      });

      return this.createErrorResult(
        startTime,
        error.message || 'Unknown error occurred',
        ['Tool execution failed unexpectedly']
      );
    }
  }

  /**
   * Tool-specific execution logic - to be implemented by subclasses
   */
  protected abstract executeInternal(context: ToolContext): Promise<ToolResult>;

  /**
   * Validate the input context - can be overridden by subclasses
   */
  protected async validateContext(context: ToolContext): Promise<{
    isValid: boolean;
    error?: string;
    warnings?: string[];
  }> {
    const warnings: string[] = [];

    if (!context.documentContent || context.documentContent.trim().length === 0) {
      return {
        isValid: false,
        error: 'Document content is required'
      };
    }

    if (!context.userRequest || context.userRequest.trim().length === 0) {
      return {
        isValid: false,
        error: 'User request is required'
      };
    }

    if (context.documentContent.length > 100000) {
      warnings.push('Document is very large, processing may take longer');
    }

    if (context.userRequest.length > 1000) {
      warnings.push('User request is very long, consider being more concise');
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Create a standardized error result
   */
  protected createErrorResult(
    startTime: number,
    error: string,
    warnings?: string[]
  ): ToolResult {
    return {
      toolId: this.id,
      success: false,
      data: null,
      reasoning: `Tool execution failed: ${error}`,
      confidence: 0,
      executionTime: Date.now() - startTime,
      error,
      warnings
    };
  }

  /**
   * Create a standardized success result
   */
  protected createSuccessResult(
    startTime: number,
    data: any,
    reasoning: string,
    confidence: number,
    warnings?: string[]
  ): ToolResult {
    return {
      toolId: this.id,
      success: true,
      data,
      reasoning,
      confidence: Math.max(0, Math.min(1, confidence)),
      executionTime: Date.now() - startTime,
      warnings
    };
  }

  /**
   * Extract text from HTML content
   */
  protected extractTextFromHtml(html: string): string {
    // Simple HTML tag removal - in production, use a proper HTML parser
    return html
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Find text position in HTML content
   */
  protected findTextPosition(html: string, searchText: string): {
    start: number;
    end: number;
  } | null {
    const plainText = this.extractTextFromHtml(html);
    const index = plainText.toLowerCase().indexOf(searchText.toLowerCase());
    
    if (index === -1) {
      return null;
    }

    return {
      start: index,
      end: index + searchText.length
    };
  }

  /**
   * Calculate confidence score based on various factors
   */
  protected calculateConfidence(factors: {
    textMatch?: number;
    contextRelevance?: number;
    requestClarity?: number;
    resultQuality?: number;
  }): number {
    const weights = {
      textMatch: 0.3,
      contextRelevance: 0.25,
      requestClarity: 0.2,
      resultQuality: 0.25
    };

    let totalScore = 0;
    let totalWeight = 0;

    Object.entries(factors).forEach(([key, value]) => {
      if (value !== undefined && weights[key as keyof typeof weights]) {
        totalScore += value * weights[key as keyof typeof weights];
        totalWeight += weights[key as keyof typeof weights];
      }
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0.5;
  }

  /**
   * Analyze text complexity
   */
  protected analyzeTextComplexity(text: string): {
    wordCount: number;
    sentenceCount: number;
    avgWordsPerSentence: number;
    complexity: 'simple' | 'moderate' | 'complex';
  } {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    const wordCount = words.length;
    const sentenceCount = sentences.length;
    const avgWordsPerSentence = sentenceCount > 0 ? wordCount / sentenceCount : 0;

    let complexity: 'simple' | 'moderate' | 'complex' = 'simple';
    if (avgWordsPerSentence > 20 || wordCount > 500) {
      complexity = 'complex';
    } else if (avgWordsPerSentence > 12 || wordCount > 100) {
      complexity = 'moderate';
    }

    return {
      wordCount,
      sentenceCount,
      avgWordsPerSentence,
      complexity
    };
  }
}
