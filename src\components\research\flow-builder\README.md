# Flow Chart Builder Module

An AI-powered flowchart and diagram generation platform for research methodology visualization.

## Overview

The Flow Chart Builder allows users to generate professional diagrams using natural language descriptions. It leverages AI models (Google Gemini, Claude, GPT-4) to create Mermaid.js diagrams for various research and methodology visualization needs.

## Features

### ✨ AI-Powered Generation
- **Natural Language Input**: Describe your process in plain English
- **Multiple AI Models**: Support for Google Gemini, Claude 3.5 Sonnet, GPT-4 Turbo
- **Smart Type Detection**: Automatically detects the best diagram type
- **Context-Aware**: Understands research and methodology contexts

### 📊 Diagram Types Supported
- **Flowcharts**: Process flows and decision trees
- **Sequence Diagrams**: Interactions between entities over time
- **Gantt Charts**: Project timelines and schedules
- **Mind Maps**: Hierarchical concept mapping
- **Timelines**: Chronological event sequences
- **User Journeys**: User experience workflows
- **Entity Relationship**: Database and data relationships
- **State Diagrams**: System states and transitions
- **Class Diagrams**: Object-oriented system structure

### 🎨 Interactive Features
- **Real-time Rendering**: Instant diagram visualization
- **Code Editing**: Direct Mermaid code editing with validation
- **Zoom Controls**: Pan, zoom, and fullscreen viewing
- **Template Library**: Pre-built research methodology templates

### 📤 Export Options
- **PNG**: High-quality raster images
- **SVG**: Scalable vector graphics
- **PDF**: Print-ready documents
- **Mermaid Code**: Raw syntax for sharing

## Architecture

### Component Structure
```
flow-builder/
├── FlowBuilder.tsx              # Main component
├── components/
│   ├── DiagramInputForm.tsx     # Input form with templates
│   ├── DiagramRenderer.tsx      # Mermaid diagram renderer
│   ├── DiagramEditor.tsx        # Code editor (placeholder)
│   ├── DiagramExportDialog.tsx  # Export options (placeholder)
│   ├── DiagramHistoryPanel.tsx  # History management (placeholder)
│   ├── FlowBuilderHeader.tsx    # Header component (placeholder)
│   ├── FlowBuilderErrorBoundary.tsx # Error handling (placeholder)
│   └── AIModelSelector.tsx      # Model selection (placeholder)
├── services/
│   ├── flow-ai.service.ts       # AI generation service
│   └── diagram-export.service.ts # Export functionality
├── stores/
│   └── flow-builder.store.ts    # Zustand state management
├── types.ts                     # TypeScript interfaces
├── constants.ts                 # Configuration constants
├── prompts.ts                   # AI generation prompts
└── index.ts                     # Module exports
```

### State Management
Uses Zustand for efficient state management with persistence:
- **Diagram History**: Automatic saving and loading
- **Generation Queue**: Sequential diagram generation
- **Editing State**: Code editing with validation
- **Export Options**: User preferences persistence

### AI Integration
- **Google Gemini**: Primary AI provider for diagram generation
- **OpenRouter**: Support for Claude and GPT-4 models
- **Prompt Engineering**: Specialized prompts for different diagram types
- **Error Handling**: Robust error handling and retry logic

## Usage

### Basic Usage
1. Navigate to "Flow Chart Builder" in the sidebar
2. Describe your process or methodology
3. Select diagram type (optional - AI will auto-detect)
4. Click "Generate Diagram"
5. Review and edit the generated diagram
6. Export in your preferred format

### Template Usage
1. Switch to the "Templates" tab
2. Browse pre-built research methodology templates
3. Select a template that matches your needs
4. Customize the description if needed
5. Apply the template to generate the diagram

### Advanced Features
- **Code Editing**: Switch to editor tab to modify Mermaid code directly
- **Regeneration**: Provide feedback to improve diagram generation
- **History**: Access previously generated diagrams
- **Export**: Multiple format options with customizable settings

## Configuration

### Environment Variables
```env
VITE_GEMINI_API_KEY=your_gemini_api_key
VITE_OPENROUTER_API_KEY=your_openrouter_api_key
```

### AI Models
- **Google Gemini 2.0 Flash**: Fast and efficient (recommended)
- **Claude 3.5 Sonnet**: Excellent for complex methodologies
- **GPT-4 Turbo**: High-quality generation

## Templates

### Research Methodology Templates
- **Research Methodology**: Standard research process flowchart
- **Data Analysis Process**: Data analysis workflow
- **Experimental Design**: Scientific experiment workflow
- **Literature Review Process**: Systematic literature review

### Customization
Templates support variable replacement for personalization:
```mermaid
flowchart TD
    A[{{start_step}}] --> B[{{process_step}}]
```

## Technical Details

### Dependencies
- **Mermaid.js**: Diagram rendering engine
- **Zustand**: State management
- **React Hook Form**: Form handling
- **Zod**: Schema validation

### Browser Support
- Modern browsers with ES2020 support
- SVG rendering capabilities required
- Canvas API for PNG export

### Performance
- Dynamic imports for code splitting
- Lazy loading of Mermaid library
- Optimized rendering with caching

## Future Enhancements

### Planned Features
- **Collaborative Editing**: Real-time collaboration
- **Custom Templates**: User-created template library
- **Advanced Export**: PowerPoint and LaTeX export
- **Integration**: Direct integration with paper generator
- **AI Improvements**: Better context understanding

### Component Completion
Several components are currently placeholders and need implementation:
- DiagramEditor: Full code editor with syntax highlighting
- DiagramExportDialog: Advanced export options
- DiagramHistoryPanel: Comprehensive history management
- FlowBuilderHeader: Enhanced header with actions
- AIModelSelector: Model selection and configuration

## Contributing

When extending the Flow Builder:
1. Follow existing architectural patterns
2. Use TypeScript for type safety
3. Add proper error handling
4. Include comprehensive tests
5. Update documentation

## License

Part of the Verbira research platform.
