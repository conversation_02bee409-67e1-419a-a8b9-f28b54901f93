import { createClient } from '@supabase/supabase-js'
import type { Database } from './database.types'

/**
 * Supabase client configuration
 * 
 * IMPORTANT AUTHENTICATION NOTES:
 * 1. We use PKCE flow for OAuth - this is more secure than implicit flow
 * 2. DO NOT manually clear supabase.auth tokens during OAuth flows - let Supabase handle this
 * 3. If encountering "Unable to exchange external code" errors, this usually means:
 *    - The code was already used or expired
 *    - There's a browser caching issue
 *    - Multiple login attempts were made
 * 4. If encountering "bad_oauth_state" errors:
 *    - This happens when the state parameter doesn't match
 *    - Could be due to browser cache, multiple login attempts, or localStorage being cleared
 *    - Try clearing all browser data or using private/incognito mode
 * 
 * For more info, see: https://supabase.com/docs/guides/auth/social-login
 */

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Get the correct base URL for redirects based on environment
const getBaseUrl = () => {
  // In production, use the actual domain
  if (import.meta.env.PROD) {
    return 'https://verbira.com'
  }
  // In development, use localhost with the correct port
  return window.location.origin
}

// Log the Supabase URL for debugging (without exposing the anon key)
console.log('Supabase URL configured as:', supabaseUrl)
console.log('Base URL for redirects:', getBaseUrl())

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Use PKCE flow for more secure OAuth (Proof Key for Code Exchange)
    flowType: 'pkce',
    // IMPORTANT: Use a consistent storage key to prevent duplicate auth states
    storageKey: 'supabase.auth.token',
    // Add debug logging in development
    debug: true // Always enable debug to help troubleshoot
  }
})

// Helper functions for authentication
export const auth = {
  // Sign in with Google
  signInWithGoogle: async () => {
    // DO NOT generate a custom state parameter - let Supabase handle it
    // Clear any old state parameters
    localStorage.removeItem('oauth_state')

    const baseUrl = getBaseUrl()
    console.log('Using redirect URL for Google OAuth:', `${baseUrl}/auth/callback`)

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${baseUrl}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
          // Let Supabase handle its own state parameter for PKCE
        }
      }
    })
    return { data, error }
  },

  // Sign in with email/password
  signInWithEmail: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign up with email/password
  signUpWithEmail: async (email: string, password: string, metadata?: any) => {
    const baseUrl = getBaseUrl()
    console.log('Using redirect URL for email signup:', `${baseUrl}/auth/callback`)

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
        emailRedirectTo: `${baseUrl}/auth/callback`
      }
    })
    return { data, error }
  },

  // Sign out - let Supabase handle session cleanup
  signOut: async () => {
    // DO NOT manually clear Supabase auth tokens - let Supabase handle it
    // Only clear custom items we might have added
    localStorage.removeItem('oauth_state')
    
    // Let Supabase handle the signout process
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current session
  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession()
    return { session, error }
  },

  // Get current user
  getUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  }
}

// Helper functions for database operations
export const db = {
  // Get user profile
  getUserProfile: async (userId: string) => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  // Update user profile
  updateUserProfile: async (userId: string, updates: any) => {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  // Get user documents
  getUserDocuments: async (userId: string) => {
    const { data, error } = await supabase
      .from('user_documents')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
    return { data, error }
  },

  // Create user document
  createUserDocument: async (document: any) => {
    const { data, error } = await supabase
      .from('user_documents')
      .insert(document)
      .select()
      .single()
    return { data, error }
  },

  // Update user document
  updateUserDocument: async (documentId: string, updates: any) => {
    const { data, error } = await supabase
      .from('user_documents')
      .update(updates)
      .eq('id', documentId)
      .select()
      .single()
    return { data, error }
  },

  // Delete user document
  deleteUserDocument: async (documentId: string) => {
    const { error } = await supabase
      .from('user_documents')
      .delete()
      .eq('id', documentId)
    return { error }
  },

  // Get user usage for current month
  getUserUsage: async (userId: string, monthYear?: string) => {
    const currentMonth = monthYear || new Date().toISOString().slice(0, 7)
    const { data, error } = await supabase
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', currentMonth)
      .single()
    return { data, error }
  },

  // Update user usage
  updateUserUsage: async (userId: string, updates: any, monthYear?: string) => {
    const currentMonth = monthYear || new Date().toISOString().slice(0, 7)
    const { data, error } = await supabase
      .from('user_usage')
      .upsert({
        user_id: userId,
        month_year: currentMonth,
        ...updates
      })
      .select()
      .single()
    return { data, error }
  }
}
