import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ChevronDown,
  ChevronRight,
  Plus,
  X,
  GripVertical,
  BookOpen,
  FileText,
  ImageIcon,
  Edit3,
  Trash2,
  Lightbulb,
  Target,
  Brain,
  Sparkles,
  AlertCircle,
  CheckCircle,
  HelpCircle
} from "lucide-react";
import { UserChapter, ChapterOutline, SubSection, ContentItem } from '../types';
import { ContentItemRenderer } from './ContentItemRenderer';
import { WORD_COUNT_ESTIMATES } from '../constants';

interface ChapterCardProps {
  chapter: UserChapter;
  chapterIndex: number;
  onChapterUpdate: (chapterId: string, updates: Partial<UserChapter>) => void;
  onChapterRemove: (chapterId: string) => void;
  onMoveChapter: (chapterId: string, direction: 'up' | 'down') => void;
  analyzingItems: Set<string>;
  setAnalyzingItems: (items: Set<string>) => void;
  selectedModel: string;
}

export const ChapterCard: React.FC<ChapterCardProps> = ({
  chapter,
  chapterIndex,
  onChapterUpdate,
  onChapterRemove,
  onMoveChapter,
  analyzingItems,
  setAnalyzingItems,
  selectedModel
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditingOutline, setIsEditingOutline] = useState(false);
  const [newSubSectionTitle, setNewSubSectionTitle] = useState('');
  const [showTopicGuidance, setShowTopicGuidance] = useState(false);
  const [topicQuality, setTopicQuality] = useState<'poor' | 'good' | 'excellent'>('poor');

  const updateChapterField = (field: keyof UserChapter, value: any) => {
    onChapterUpdate(chapter.id, { [field]: value });
  };

  const updateOutlineField = (field: keyof ChapterOutline, value: any) => {
    updateChapterField('outline', { ...chapter.outline, [field]: value });
  };

  const addSubSection = () => {
    if (newSubSectionTitle.trim()) {
      const newSubSection: SubSection = {
        id: `subsection-${Date.now()}`,
        title: newSubSectionTitle.trim(),
        description: '',
        order: chapter.outline.subSections.length + 1,
        level: 1
      };
      
      updateOutlineField('subSections', [...chapter.outline.subSections, newSubSection]);
      setNewSubSectionTitle('');
    }
  };

  const updateSubSection = (subSectionId: string, updates: Partial<SubSection>) => {
    const updatedSubSections = chapter.outline.subSections.map(sub =>
      sub.id === subSectionId ? { ...sub, ...updates } : sub
    );
    updateOutlineField('subSections', updatedSubSections);
  };

  const removeSubSection = (subSectionId: string) => {
    const updatedSubSections = chapter.outline.subSections.filter(sub => sub.id !== subSectionId);
    updateOutlineField('subSections', updatedSubSections);
  };

  const addContentItem = (type: 'text' | 'figure') => {
    const newItem: ContentItem = {
      id: `item-${Date.now()}`,
      type,
      content: '',
      order: chapter.items.length + 1,
      title: type === 'figure' ? 'Figure Title' : undefined,
      caption: type === 'figure' ? 'Figure caption...' : undefined
    };
    
    updateChapterField('items', [...chapter.items, newItem]);
  };

  const updateContentItem = (itemId: string, updates: Partial<ContentItem>) => {
    const updatedItems = chapter.items.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    );
    updateChapterField('items', updatedItems);
  };

  const removeContentItem = (itemId: string) => {
    const updatedItems = chapter.items.filter(item => item.id !== itemId);
    updateChapterField('items', updatedItems);
  };

  const moveContentItem = (itemId: string, direction: 'up' | 'down') => {
    const items = [...chapter.items];
    const index = items.findIndex(item => item.id === itemId);
    
    if (index === -1) return;
    
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= items.length) return;
    
    [items[index], items[newIndex]] = [items[newIndex], items[index]];
    
    // Update order values
    items.forEach((item, idx) => {
      item.order = idx + 1;
    });
    
    updateChapterField('items', items);
  };

  // Analyze topic quality based on title and description
  const analyzeTopicQuality = () => {
    const title = chapter.outline.title.toLowerCase();
    const description = chapter.outline.description.toLowerCase();
    const combinedText = `${title} ${description}`;

    // Check for specific terms, length, and detail level
    const hasSpecificTerms = /\b(advanced|specific|detailed|comprehensive|practical|implementation|analysis|framework|methodology|technique|strategy|approach|system|process|model|theory|principle)\b/.test(combinedText);
    const hasGoodLength = description.length > 100;
    const hasKeywords = /\b(how|what|why|when|where|implement|create|build|develop|design|optimize|manage|analyze|evaluate)\b/.test(combinedText);

    if (hasSpecificTerms && hasGoodLength && hasKeywords) {
      return 'excellent';
    } else if ((hasSpecificTerms && hasGoodLength) || (hasKeywords && hasGoodLength)) {
      return 'good';
    } else {
      return 'poor';
    }
  };

  const currentTopicQuality = analyzeTopicQuality();
  const estimatedWordCount = WORD_COUNT_ESTIMATES.chapter;

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 bg-green-50 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-orange-600 bg-orange-50 border-orange-200';
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case 'excellent': return <CheckCircle className="h-4 w-4" />;
      case 'good': return <Target className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <Card className="shadow-lg border-2 border-gray-200 hover:border-blue-300 transition-all duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
            <GripVertical className="h-5 w-5 text-gray-400" />
            <BookOpen className="h-6 w-6 text-blue-600" />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-xl">
                  Chapter {chapterIndex + 1}: {chapter.outline.title || 'Untitled Chapter'}
                </CardTitle>
                <Badge className={`text-xs ${getQualityColor(currentTopicQuality)}`}>
                  {getQualityIcon(currentTopicQuality)}
                  <span className="ml-1">
                    {currentTopicQuality === 'excellent' ? 'Excellent Topic' :
                     currentTopicQuality === 'good' ? 'Good Topic' : 'Needs Detail'}
                  </span>
                </Badge>
              </div>
              <p className="text-sm text-gray-500">
                {chapter.outline.subSections.length} sections • {chapter.items.length} content items
              </p>
              {currentTopicQuality === 'poor' && (
                <p className="text-xs text-orange-600 mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Add more specific topic details for better AI outlines
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {estimatedWordCount.min}-{estimatedWordCount.max} words
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveChapter(chapter.id, 'up')}
              disabled={chapterIndex === 0}
            >
              ↑
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMoveChapter(chapter.id, 'down')}
            >
              ↓
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onChapterRemove(chapter.id)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Chapter Basic Info */}
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Chapter Title</label>
                <Input
                  value={chapter.outline.title}
                  onChange={(e) => updateOutlineField('title', e.target.value)}
                  placeholder="Enter chapter title..."
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Estimated Word Count</label>
                <Input
                  type="number"
                  value={chapter.outline.estimatedWordCount}
                  onChange={(e) => updateOutlineField('estimatedWordCount', parseInt(e.target.value) || 0)}
                  placeholder="3000"
                  min="500"
                  max="10000"
                />
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block flex items-center gap-2">
                Chapter Description
                <HelpCircle
                  className="h-4 w-4 text-gray-400 cursor-pointer"
                  onClick={() => setShowTopicGuidance(!showTopicGuidance)}
                  title="Click for topic guidance"
                />
              </label>
              <Textarea
                value={chapter.outline.description}
                onChange={(e) => updateOutlineField('description', e.target.value)}
                placeholder="Describe specific topics, concepts, methodologies, or frameworks this chapter will cover. Be detailed about what readers will learn and any practical applications. Example: 'This chapter covers advanced neural network architectures including CNNs and RNNs, with hands-on implementation using TensorFlow and practical examples in image recognition...'"
                rows={4}
              />
              <div className="text-xs text-gray-500 flex items-center justify-between mt-1">
                <span>Characters: {chapter.outline.description.length}</span>
                <span className={chapter.outline.description.length >= 150 ? "text-green-600" : "text-orange-500"}>
                  {chapter.outline.description.length >= 150 ? "✓ Good detail level" : "Add more specific details"}
                </span>
              </div>
            </div>
          </div>

          {/* Topic Guidance */}
          {showTopicGuidance && (
            <Alert className="border-blue-200 bg-blue-50">
              <Brain className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-3">
                  <p className="font-medium text-blue-800">💡 How to write excellent chapter topics:</p>
                  <div className="grid md:grid-cols-2 gap-3 text-sm text-blue-700">
                    <div>
                      <p className="font-medium mb-1">✅ Include:</p>
                      <ul className="space-y-1 text-xs">
                        <li>• Specific methodologies or frameworks</li>
                        <li>• Technical terms and concepts</li>
                        <li>• Practical applications or use cases</li>
                        <li>• Learning objectives and outcomes</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">❌ Avoid:</p>
                      <ul className="space-y-1 text-xs">
                        <li>• Generic descriptions like "introduction"</li>
                        <li>• Vague terms without specifics</li>
                        <li>• Single-sentence descriptions</li>
                        <li>• Missing technical context</li>
                      </ul>
                    </div>
                  </div>
                  <div className="p-2 bg-white rounded border-l-4 border-blue-400">
                    <p className="text-xs font-medium text-blue-800">Example Good Description:</p>
                    <p className="text-xs text-blue-700 mt-1">
                      "Advanced data preprocessing techniques including feature engineering, dimensionality reduction using PCA and t-SNE,
                      handling missing data with multiple imputation methods, and outlier detection algorithms. Includes practical
                      implementation with pandas and scikit-learn, plus real-world case studies from e-commerce and healthcare domains."
                    </p>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Sub-sections */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-lg">Chapter Outline</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingOutline(!isEditingOutline)}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                {isEditingOutline ? 'Done' : 'Edit Outline'}
              </Button>
            </div>

            {isEditingOutline && (
              <div className="flex gap-2">
                <Input
                  value={newSubSectionTitle}
                  onChange={(e) => setNewSubSectionTitle(e.target.value)}
                  placeholder="Add new section..."
                  onKeyPress={(e) => e.key === 'Enter' && addSubSection()}
                />
                <Button onClick={addSubSection} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}

            <div className="space-y-3">
              {chapter.outline.subSections.map((subSection, index) => (
                <div key={subSection.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-500">
                          {chapterIndex + 1}.{index + 1}
                        </span>
                        {isEditingOutline ? (
                          <Input
                            value={subSection.title}
                            onChange={(e) => updateSubSection(subSection.id, { title: e.target.value })}
                            className="font-medium"
                          />
                        ) : (
                          <span className="font-medium">{subSection.title}</span>
                        )}
                      </div>
                      {isEditingOutline ? (
                        <Textarea
                          value={subSection.description}
                          onChange={(e) => updateSubSection(subSection.id, { description: e.target.value })}
                          placeholder="Describe this section..."
                          rows={2}
                        />
                      ) : (
                        subSection.description && (
                          <p className="text-sm text-gray-600">{subSection.description}</p>
                        )
                      )}
                    </div>
                    {isEditingOutline && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSubSection(subSection.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Content Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-lg">Chapter Content</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addContentItem('text')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Add Text
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addContentItem('figure')}
                >
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Add Figure
                </Button>
              </div>
            </div>

            {chapter.items.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No content items yet. Add text or figures to provide context for this chapter.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {chapter.items.map((item, index) => (
                  <ContentItemRenderer
                    key={item.id}
                    sectionId={chapter.id}
                    sectionName={chapter.outline.title}
                    item={item}
                    index={index}
                    totalItems={chapter.items.length}
                    updateContentItem={updateContentItem}
                    removeContentItem={removeContentItem}
                    moveContentItem={moveContentItem}
                    analyzingItems={analyzingItems}
                    setAnalyzingItems={setAnalyzingItems}
                    selectedModel={selectedModel}
                    textPlaceholders={{ [chapter.outline.title]: "Describe the main concepts and insights for this chapter..." }}
                    figurePlaceholders={{ [chapter.outline.title]: "Explain how this visual supports the chapter's concepts..." }}
                    defaultTextPrompt="Provide detailed content for this chapter section..."
                    defaultFigurePrompt="Describe how this figure illustrates the chapter's key concepts..."
                  />
                ))}
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};
