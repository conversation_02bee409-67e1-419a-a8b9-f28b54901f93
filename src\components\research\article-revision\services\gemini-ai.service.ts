/**
 * Gemini AI Service for Article Revision System
 * Uses Google Gemini API for AI-powered article revision
 */

import { GoogleGenAI } from '@google/genai';

export interface GeminiGenerationOptions {
  maxTokens?: number;
  temperature?: number;
  thinkingBudget?: number;
}

export class GeminiAIService {
  private static instance: GeminiAIService;
  private ai: GoogleGenAI;
  private apiKey: string;

  private constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.apiKey) {
      throw new Error('VITE_GEMINI_API_KEY is not configured');
    }
    this.ai = new GoogleGenAI({ apiKey: this.apiKey });
  }

  static getInstance(): GeminiAIService {
    if (!this.instance) {
      this.instance = new GeminiAIService();
    }
    return this.instance;
  }

  /**
   * Generate content using Gemini API
   */
  async generateContent(
    prompt: string,
    model: string = 'gemini-2.5-pro',
    options: GeminiGenerationOptions = {}
  ): Promise<string> {
    try {
      console.log(`🤖 Gemini: Generating content with model ${model}`);
      
      const config = {
        thinkingConfig: {
          thinkingBudget: options.thinkingBudget ?? -1,
        },
        responseMimeType: 'text/plain',
        generationConfig: {
          maxOutputTokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
        }
      };

      const contents = [
        {
          role: 'user' as const,
          parts: [
            {
              text: prompt,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
        }
      }

      if (!fullText.trim()) {
        throw new Error('Empty response from Gemini API');
      }

      console.log(`✅ Gemini: Generated ${fullText.length} characters`);
      return fullText.trim();

    } catch (error) {
      console.error('❌ Gemini API error:', error);
      throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate content with streaming for real-time updates
   */
  async generateContentStream(
    prompt: string,
    model: string = 'gemini-2.5-pro',
    options: GeminiGenerationOptions = {},
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    try {
      console.log(`🤖 Gemini: Starting streaming generation with model ${model}`);
      
      const config = {
        thinkingConfig: {
          thinkingBudget: options.thinkingBudget ?? -1,
        },
        responseMimeType: 'text/plain',
        generationConfig: {
          maxOutputTokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
        }
      };

      const contents = [
        {
          role: 'user' as const,
          parts: [
            {
              text: prompt,
            },
          ],
        },
      ];

      const response = await this.ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
          if (onChunk) {
            onChunk(chunk.text);
          }
        }
      }

      if (!fullText.trim()) {
        throw new Error('Empty response from Gemini API');
      }

      console.log(`✅ Gemini: Streaming completed, generated ${fullText.length} characters`);
      return fullText.trim();

    } catch (error) {
      console.error('❌ Gemini streaming error:', error);
      throw new Error(`Gemini streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if API key is valid
   */
  hasValidApiKey(): boolean {
    return !!this.apiKey && 
           !this.apiKey.includes('your_') && 
           this.apiKey.length > 20;
  }

  /**
   * Get available models
   */
  getAvailableModels(): string[] {
    return [
      'gemini-2.5-pro',
      'gemini-2.5-flash',
      'gemini-2.0-flash-exp',
      'gemini-1.5-pro',
      'gemini-1.5-flash'
    ];
  }
}

// Export singleton instance
export const geminiAIService = GeminiAIService.getInstance();
