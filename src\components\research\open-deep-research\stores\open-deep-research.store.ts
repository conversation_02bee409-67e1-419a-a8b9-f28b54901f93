/**
 * Open Deep Research Store
 * Zustand store for managing Open Deep Research state
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  OpenDeepResearchState, 
  SearchResult, 
  Report, 
  Status, 
  ModelVariant 
} from '../types';
import { DEFAULT_MODEL, SEARCH_CONFIG } from '../constants';

interface OpenDeepResearchActions {
  // Search actions
  setQuery: (query: string) => void;
  setTimeFilter: (filter: string) => void;
  setResults: (results: SearchResult[]) => void;
  addResult: (result: SearchResult) => void;
  removeResult: (resultId: string) => void;
  setSelectedResults: (selectedResults: string[]) => void;
  toggleResultSelection: (resultId: string) => void;
  
  // Report actions
  setReportPrompt: (prompt: string) => void;
  setReport: (report: Report | null) => void;
  
  // UI actions
  setError: (error: string | null) => void;
  setNewUrl: (url: string) => void;
  setIsSourcesOpen: (open: boolean) => void;
  setSelectedModel: (model: ModelVariant) => void;
  setIsAgentMode: (enabled: boolean) => void;
  setSidebarOpen: (open: boolean) => void;
  setActiveTab: (tab: 'search' | 'report' | 'history') => void;
  
  // Status actions
  setStatus: (status: Partial<Status>) => void;
  updateStatus: (updates: Partial<Status>) => void;
  setLoading: (loading: boolean) => void;
  setGeneratingReport: (generating: boolean) => void;
  setAgentStep: (step: Status['agentStep']) => void;
  addAgentInsight: (insight: string) => void;
  clearAgentInsights: () => void;
  setSearchQueries: (queries: string[]) => void;
  addSearchQuery: (query: string) => void;
  
  // Utility actions
  reset: () => void;
  clearResults: () => void;
  clearReport: () => void;
}

const initialState: OpenDeepResearchState = {
  // Search state
  query: '',
  timeFilter: 'all',
  results: [],
  selectedResults: [],
  
  // Report state
  reportPrompt: '',
  report: null,
  
  // UI state
  error: null,
  newUrl: '',
  isSourcesOpen: false,
  selectedModel: DEFAULT_MODEL,
  isAgentMode: false,
  sidebarOpen: false,
  activeTab: 'search',
  
  // Status
  status: {
    loading: false,
    generatingReport: false,
    agentStep: 'idle',
    fetchStatus: {
      total: 0,
      successful: 0,
      fallback: 0,
      sourceStatuses: {},
    },
    agentInsights: [],
    searchQueries: [],
  },
};

export const useOpenDeepResearchStore = create<OpenDeepResearchState & OpenDeepResearchActions>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Search actions
        setQuery: (query) => set({ query }),
        setTimeFilter: (timeFilter) => set({ timeFilter }),
        setResults: (results) => set({ results }),
        addResult: (result) => set((state) => ({ 
          results: [result, ...state.results] 
        })),
        removeResult: (resultId) => set((state) => ({
          results: state.results.filter(r => r.id !== resultId),
          selectedResults: state.selectedResults.filter(id => id !== resultId),
        })),
        setSelectedResults: (selectedResults) => set({ selectedResults }),
        toggleResultSelection: (resultId) => set((state) => {
          const isSelected = state.selectedResults.includes(resultId);
          if (isSelected) {
            return {
              selectedResults: state.selectedResults.filter(id => id !== resultId),
            };
          } else if (state.selectedResults.length < SEARCH_CONFIG.maxSelectableResults) {
            return {
              selectedResults: [...state.selectedResults, resultId],
            };
          }
          return state;
        }),

        // Report actions
        setReportPrompt: (reportPrompt) => set({ reportPrompt }),
        setReport: (report) => set({ report }),

        // UI actions
        setError: (error) => set({ error }),
        setNewUrl: (newUrl) => set({ newUrl }),
        setIsSourcesOpen: (isSourcesOpen) => set({ isSourcesOpen }),
        setSelectedModel: (selectedModel) => set({ selectedModel }),
        setIsAgentMode: (isAgentMode) => set({ isAgentMode }),
        setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
        setActiveTab: (activeTab) => set({ activeTab }),

        // Status actions
        setStatus: (status) => set((state) => ({
          status: { ...state.status, ...status }
        })),
        updateStatus: (updates) => set((state) => ({
          status: { ...state.status, ...updates }
        })),
        setLoading: (loading) => set((state) => ({
          status: { ...state.status, loading }
        })),
        setGeneratingReport: (generatingReport) => set((state) => ({
          status: { ...state.status, generatingReport }
        })),
        setAgentStep: (agentStep) => set((state) => ({
          status: { ...state.status, agentStep }
        })),
        addAgentInsight: (insight) => set((state) => ({
          status: {
            ...state.status,
            agentInsights: [...state.status.agentInsights, insight]
          }
        })),
        clearAgentInsights: () => set((state) => ({
          status: { ...state.status, agentInsights: [] }
        })),
        setSearchQueries: (searchQueries) => set((state) => ({
          status: { ...state.status, searchQueries }
        })),
        addSearchQuery: (query) => set((state) => ({
          status: {
            ...state.status,
            searchQueries: [...state.status.searchQueries, query]
          }
        })),

        // Utility actions
        reset: () => set(initialState),
        clearResults: () => set({ 
          results: [], 
          selectedResults: [],
          error: null 
        }),
        clearReport: () => set({ 
          report: null, 
          reportPrompt: '',
          activeTab: 'search' 
        }),
      }),
      {
        name: 'open-deep-research-store',
        partialize: (state) => ({
          // Only persist user preferences and UI state
          selectedModel: state.selectedModel,
          isAgentMode: state.isAgentMode,
          timeFilter: state.timeFilter,
        }),
      }
    ),
    {
      name: 'open-deep-research-store',
    }
  )
);

// Selector hooks for better performance
export const useSearchState = () => useOpenDeepResearchStore((state) => ({
  query: state.query,
  timeFilter: state.timeFilter,
  results: state.results,
  selectedResults: state.selectedResults,
  newUrl: state.newUrl,
}));

export const useReportState = () => useOpenDeepResearchStore((state) => ({
  reportPrompt: state.reportPrompt,
  report: state.report,
  activeTab: state.activeTab,
}));

export const useUIState = () => useOpenDeepResearchStore((state) => ({
  error: state.error,
  selectedModel: state.selectedModel,
  isAgentMode: state.isAgentMode,
  sidebarOpen: state.sidebarOpen,
  isSourcesOpen: state.isSourcesOpen,
}));

export const useStatusState = () => useOpenDeepResearchStore((state) => state.status);
