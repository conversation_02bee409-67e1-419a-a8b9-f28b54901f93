// Vega-Lite utility functions adapted from VizGPT
import { VegaLiteSpec, DataField, UploadedFile } from '../types';

/**
 * Extract JSON content between curly braces from a string
 */
export function matchQuote(str: string, left: string, right: string): string | null {
  let stack = 0;
  let start = -1;
  let end = -1;
  
  for (let i = 0; i < str.length; i++) {
    if (str[i] === left) {
      if (stack === 0) {
        start = i;
      }
      stack++;
    }
    if (str[i] === right) {
      stack--;
      if (stack === 0) {
        end = i;
        break;
      }
    }
  }
  
  if (start !== -1 && end !== -1) {
    return str.substring(start, end + 1);
  }
  return null;
}

/**
 * Traverse JSON object and apply callback to each property
 */
type JsonValue = string | number | boolean | null | JsonArray | JsonObject;
type JsonObject = { [key: string]: JsonValue };
type JsonArray = Array<JsonValue>;
type Callback = (key: string, value: JsonValue, node: JsonObject) => void;

function traverseJson(
  json: JsonValue, 
  callback: Callback, 
  prefix: string, 
  parent: JsonObject | JsonArray | null = null
): void {
  if (typeof json === 'object' && json !== null) {
    if (Array.isArray(json)) {
      (json as JsonArray).forEach((value, index) => {
        const newPrefix = `${prefix}[${index}]`;
        traverseJson(value, callback, newPrefix, json);
      });
    } else {
      Object.entries(json as JsonObject).forEach(([key, value]) => {
        const newPrefix = prefix ? `${prefix}.${key}` : key;
        traverseJson(value, callback, newPrefix, json as JsonObject);
      });
    }
  } else {
    if (parent !== null && !Array.isArray(parent)) {
      callback(prefix, json, parent);
    }
  }
}

/**
 * Extract and validate Vega-Lite specification from AI response
 */
export function getValidVegaSpec(content: string, file: UploadedFile): VegaLiteSpec | null {
  const raw = matchQuote(content, '{', '}');
  if (!raw) return null;

  try {
    const spec = JSON.parse(raw);
    const fields = inferDataFields(file);
    
    // Process field references in the spec
    traverseJson(spec, (k, v, fieldDef) => {
      if (fieldDef.field) {
        const fn = (fieldDef.aggregate || fieldDef.timeUnit || (fieldDef.bin && 'bin')) as string;
        const field = fields.find(f => f.name === v);
        
        if (!field) return;
        
        if (fn) {
          fieldDef.title = `${fn.toUpperCase()}(${field.name})`;
        } else {
          fieldDef.title = field.name;
        }
        fieldDef.field = field.fid;
      }
    }, '');
    
    return spec as VegaLiteSpec;
  } catch (e) {
    console.error('Failed to parse Vega-Lite spec:', e);
    return null;
  }
}

/**
 * Infer data field types from uploaded file
 */
export function inferDataFields(file: UploadedFile): DataField[] {
  if (!file.data || file.data.length === 0) return [];
  
  return file.headers.map((header, index) => {
    const values = file.data.slice(1).map(row => row[index]).filter(v => v != null);
    const semanticType = inferSemanticType(values);
    
    return {
      fid: header,
      name: header,
      semanticType
    };
  });
}

/**
 * Infer semantic type of data values
 */
function inferSemanticType(values: any[]): 'quantitative' | 'nominal' | 'ordinal' | 'temporal' {
  if (values.length === 0) return 'nominal';
  
  // Check if numeric
  if (isNumericArray(values)) {
    return 'quantitative';
  }
  
  // Check if temporal
  if (isDateTimeArray(values.map(v => String(v)))) {
    return 'temporal';
  }
  
  return 'nominal';
}

/**
 * Check if array contains numeric values
 */
function isNumericArray(data: any[]): boolean {
  return data.every((item) => {
    if (typeof item === 'number') return true;
    const number = parseFloat(item);
    return !isNaN(number) && isFinite(number);
  });
}

/**
 * Check if array contains date/time values
 */
function isDateTimeArray(data: string[]): boolean {
  const COMMON_TIME_FORMAT: RegExp[] = [
    /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
    /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
    /^\d{2}\/\d{2}\/\d{4}$/, // DD/MM/YYYY
    /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
    /^\d{4}\.\d{2}\.\d{2}$/, // YYYY.MM.DD
    /^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/, // YYYY-MM-DD HH:MM:SS
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/, // YYYY-MM-DDTHH:MM:SS (ISO-8601)
  ];
  
  return data.every(d => {
    return COMMON_TIME_FORMAT.some(regex => regex.test(d));
  });
}

/**
 * Convert uploaded file data to format suitable for Vega-Lite
 */
export function prepareDataForVega(file: UploadedFile): any[] {
  if (!file.data || file.data.length === 0) return [];
  
  const headers = file.headers;
  const dataRows = file.data.slice(1); // Skip header row
  
  return dataRows.map(row => {
    const obj: any = {};
    headers.forEach((header, index) => {
      obj[header] = row[index];
    });
    return obj;
  });
}

/**
 * Generate default Vega-Lite theme configuration
 */
export function getVegaTheme(isDark: boolean = false) {
  return {
    background: isDark ? '#1f2937' : '#ffffff',
    title: {
      color: isDark ? '#f9fafb' : '#111827',
    },
    axis: {
      domainColor: isDark ? '#6b7280' : '#d1d5db',
      gridColor: isDark ? '#374151' : '#f3f4f6',
      tickColor: isDark ? '#6b7280' : '#d1d5db',
      labelColor: isDark ? '#d1d5db' : '#374151',
      titleColor: isDark ? '#f9fafb' : '#111827',
    },
    legend: {
      labelColor: isDark ? '#d1d5db' : '#374151',
      titleColor: isDark ? '#f9fafb' : '#111827',
    },
    range: {
      category: isDark 
        ? ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#f97316', '#84cc16']
        : ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#0891b2', '#ea580c', '#65a30d']
    }
  };
}
