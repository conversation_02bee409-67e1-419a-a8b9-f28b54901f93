/**
 * Google Search Validation Utilities
 * Simple validation functions that don't require testing frameworks
 */

import { googleSearchService } from '../services/google-search.service';
import { deepResearchCostService } from '../services/deep-research-cost.service';

export interface ValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

/**
 * Validate URL format and authenticity
 */
export const validateUrls = (): ValidationResult => {
  try {
    const validUrls = [
      'https://scholar.google.com/scholar?q=artificial+intelligence',
      'https://pubmed.ncbi.nlm.nih.gov/12345678/',
      'https://arxiv.org/abs/2301.12345',
      'https://www.nature.com/articles/s41586-023-12345-6',
      'https://mit.edu/research/ai'
    ];

    const invalidUrls = [
      'https://example.com/fake-article',
      'https://placeholder.com/research',
      'https://fake-url.com/study',
      'https://dummy-link.org/paper',
      'https://test.com/article'
    ];

    let validCount = 0;
    let invalidCount = 0;

    // Test valid URLs
    validUrls.forEach(url => {
      if (googleSearchService['isValidUrl'](url)) {
        validCount++;
      }
    });

    // Test invalid URLs (should be rejected)
    invalidUrls.forEach(url => {
      if (!googleSearchService['isValidUrl'](url)) {
        invalidCount++;
      }
    });

    const totalTests = validUrls.length + invalidUrls.length;
    const passedTests = validCount + invalidCount;
    const successRate = (passedTests / totalTests) * 100;

    return {
      test: 'URL Validation',
      status: successRate >= 90 ? 'pass' : successRate >= 70 ? 'warning' : 'fail',
      message: `${passedTests}/${totalTests} URL validations correct (${successRate.toFixed(1)}%)`,
      details: { validCount, invalidCount, successRate }
    };

  } catch (error) {
    return {
      test: 'URL Validation',
      status: 'fail',
      message: `URL validation failed: ${error.message}`,
      details: { error: error.message }
    };
  }
};

/**
 * Validate academic source detection
 */
export const validateAcademicSources = (): ValidationResult => {
  try {
    const academicSources = [
      { url: 'https://mit.edu/research/ai', domain: 'mit.edu' },
      { url: 'https://stanford.edu/study', domain: 'stanford.edu' },
      { url: 'https://nih.gov/research', domain: 'nih.gov' },
      { url: 'https://scholar.google.com/paper', domain: 'scholar.google.com' },
      { url: 'https://nature.com/articles/123', domain: 'nature.com' }
    ];

    const nonAcademicSources = [
      { url: 'https://facebook.com/post', domain: 'facebook.com' },
      { url: 'https://twitter.com/tweet', domain: 'twitter.com' },
      { url: 'https://reddit.com/post', domain: 'reddit.com' },
      { url: 'https://techblog.com/article', domain: 'techblog.com' }
    ];

    let academicCorrect = 0;
    let nonAcademicCorrect = 0;

    // Test academic sources (should be detected as academic)
    academicSources.forEach(source => {
      if (googleSearchService['isAcademicSource'](source.url, source.domain)) {
        academicCorrect++;
      }
    });

    // Test non-academic sources (should NOT be detected as academic)
    nonAcademicSources.forEach(source => {
      if (!googleSearchService['isAcademicSource'](source.url, source.domain)) {
        nonAcademicCorrect++;
      }
    });

    const totalTests = academicSources.length + nonAcademicSources.length;
    const passedTests = academicCorrect + nonAcademicCorrect;
    const successRate = (passedTests / totalTests) * 100;

    return {
      test: 'Academic Source Detection',
      status: successRate >= 90 ? 'pass' : successRate >= 70 ? 'warning' : 'fail',
      message: `${passedTests}/${totalTests} academic detections correct (${successRate.toFixed(1)}%)`,
      details: { academicCorrect, nonAcademicCorrect, successRate }
    };

  } catch (error) {
    return {
      test: 'Academic Source Detection',
      status: 'fail',
      message: `Academic source detection failed: ${error.message}`,
      details: { error: error.message }
    };
  }
};

/**
 * Validate academic scoring system
 */
export const validateAcademicScoring = (): ValidationResult => {
  try {
    const academicSource = {
      domain: 'mit.edu',
      title: 'Research Study on Machine Learning Applications',
      snippet: 'This peer-reviewed research investigates machine learning methodologies',
      authors: ['Dr. John Smith', 'Dr. Jane Doe'],
      journal: 'Journal of AI Research',
      year: 2023
    };

    const webSource = {
      domain: 'techblog.com',
      title: 'AI is Cool',
      snippet: 'Some thoughts about artificial intelligence',
      year: 2023
    };

    const academicScore = googleSearchService['calculateAcademicScore'](academicSource);
    const webScore = googleSearchService['calculateAcademicScore'](webSource);

    const scoreDifference = academicScore - webScore;
    const isAcademicHigher = academicScore > webScore;
    const isAcademicGoodScore = academicScore > 0.6;
    const isWebLowerScore = webScore < 0.5;

    const allTestsPassed = isAcademicHigher && isAcademicGoodScore && isWebLowerScore;

    return {
      test: 'Academic Scoring',
      status: allTestsPassed ? 'pass' : 'warning',
      message: `Academic score: ${academicScore.toFixed(2)}, Web score: ${webScore.toFixed(2)}`,
      details: { 
        academicScore, 
        webScore, 
        scoreDifference: scoreDifference.toFixed(2),
        isAcademicHigher,
        isAcademicGoodScore,
        isWebLowerScore
      }
    };

  } catch (error) {
    return {
      test: 'Academic Scoring',
      status: 'fail',
      message: `Academic scoring failed: ${error.message}`,
      details: { error: error.message }
    };
  }
};

/**
 * Validate API configuration
 */
export const validateApiConfiguration = (): ValidationResult => {
  try {
    const isConfigured = googleSearchService['isConfigured']();
    
    return {
      test: 'API Configuration',
      status: isConfigured ? 'pass' : 'fail',
      message: isConfigured ? 'Gemini API key is configured' : 'Gemini API key is missing - set VITE_GEMINI_API_KEY',
      details: { isConfigured }
    };

  } catch (error) {
    return {
      test: 'API Configuration',
      status: 'fail',
      message: `API configuration check failed: ${error.message}`,
      details: { error: error.message }
    };
  }
};

/**
 * Validate cost estimation
 */
export const validateCostEstimation = (): ValidationResult => {
  try {
    const query = 'sustainable energy technologies';
    const options = {
      maxSubtopics: 6,
      wordsPerSection: 700,
      researchDepth: 'advanced' as const,
      includeExecutiveSummary: true,
      citationStyle: 'apa' as const,
      targetAudience: 'academic' as const,
      allowOutlineEditing: true
    };

    const estimate = deepResearchCostService.estimateCost(query, options);

    const hasValidTokens = estimate.totalTokens > 1000;
    const hasValidCost = estimate.estimatedCost > 0.001;
    const hasValidTime = estimate.estimatedTime > 30;
    const hasValidApiCalls = estimate.apiCalls > 3;
    const hasBreakdown = estimate.breakdown && 
                        estimate.breakdown.outlineGeneration > 0 &&
                        estimate.breakdown.batchResearch > 0 &&
                        estimate.breakdown.sectionGeneration > 0;

    const allValid = hasValidTokens && hasValidCost && hasValidTime && hasValidApiCalls && hasBreakdown;

    return {
      test: 'Cost Estimation',
      status: allValid ? 'pass' : 'warning',
      message: `Estimated ${estimate.totalTokens.toLocaleString()} tokens, $${estimate.estimatedCost.toFixed(4)}, ${estimate.estimatedTime}s`,
      details: { 
        estimate,
        hasValidTokens,
        hasValidCost,
        hasValidTime,
        hasValidApiCalls,
        hasBreakdown
      }
    };

  } catch (error) {
    return {
      test: 'Cost Estimation',
      status: 'fail',
      message: `Cost estimation failed: ${error.message}`,
      details: { error: error.message }
    };
  }
};

/**
 * Run all validation tests
 */
export const runAllValidationTests = (): ValidationResult[] => {
  console.log('🚀 Running Google Search Validation Tests...\n');

  const results: ValidationResult[] = [
    validateApiConfiguration(),
    validateUrls(),
    validateAcademicSources(),
    validateAcademicScoring(),
    validateCostEstimation()
  ];

  // Log results
  results.forEach(result => {
    const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
    console.log(`${icon} ${result.test}: ${result.message}`);
  });

  const passedTests = results.filter(r => r.status === 'pass').length;
  const totalTests = results.length;
  const successRate = (passedTests / totalTests) * 100;

  console.log(`\n📊 Overall Results: ${passedTests}/${totalTests} tests passed (${successRate.toFixed(1)}%)`);

  if (successRate >= 80) {
    console.log('🎉 Google Search grounding is working correctly!');
  } else {
    console.log('⚠️ Google Search grounding needs attention. Check failed tests above.');
  }

  return results;
};

/**
 * Simple integration test that can be called directly
 */
export const testAcademicSearchIntegration = async (): Promise<boolean> => {
  console.log('🧪 Testing Academic Search Integration...');
  
  try {
    // Check API configuration first
    const apiResult = validateApiConfiguration();
    if (apiResult.status === 'fail') {
      console.log('❌ API not configured. Set VITE_GEMINI_API_KEY environment variable.');
      return false;
    }

    // Test basic academic search
    const result = await googleSearchService.searchAcademic(
      'machine learning applications in healthcare',
      {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAcademicSources: true,
        academicFocus: true,
        citationStyle: 'apa'
      }
    );

    console.log('✅ Academic search completed');
    console.log(`📊 Found ${result.sources.length} sources`);
    
    // Validate sources
    const academicSources = result.sources.filter(s => 
      s.domain.includes('.edu') || 
      s.domain.includes('.gov') || 
      s.domain.includes('scholar.google') ||
      s.domain.includes('pubmed') ||
      s.domain.includes('nature.com') ||
      s.domain.includes('science.org')
    );

    console.log(`🎓 Academic sources: ${academicSources.length}/${result.sources.length}`);
    
    // Check for real URLs
    const validUrls = result.sources.filter(s => {
      try {
        new URL(s.url);
        return !s.url.includes('example.com') && !s.url.includes('placeholder');
      } catch {
        return false;
      }
    });

    console.log(`🔗 Valid URLs: ${validUrls.length}/${result.sources.length}`);
    
    if (academicSources.length > 0 && validUrls.length > 0) {
      console.log('✅ Academic search integration working correctly');
      return true;
    } else {
      console.log('❌ Academic search integration needs improvement');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Academic search integration test failed:', error);
    return false;
  }
};
