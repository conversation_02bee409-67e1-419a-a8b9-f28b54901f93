/**
 * Gemini Vision Service
 * Advanced multimodal AI analysis using Google Gemini 2.5 Flash and Pro
 */

import { GoogleGenAI } from '@google/genai';
import {
  FigureFile,
  AnalysisRequest,
  AnalysisResponse,
  FigureAnalysisResult,
  QualityAssessment,
  CaptionSuggestion,
  AdvancedAnalysis,
  GeminiModel,
  AnalysisType,
} from '../types';
import { GEMINI_CONFIG, ANALYSIS_PROMPTS, CITATION_STYLES } from '../constants';

export class GeminiVisionService {
  private static ai: GoogleGenAI | null = null;
  private static lastRequestTime: number = 0;
  private static requestCount: number = 0;

  /**
   * Initialize Gemini AI client
   */
  private static getAI(): GoogleGenAI {
    if (!this.ai) {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('Gemini API key is not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
      }
      this.ai = new GoogleGenAI({ apiKey });
    }
    return this.ai;
  }

  /**
   * Rate limiting check
   */
  private static async checkRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    // Ensure minimum time between requests (1 second)
    if (timeSinceLastRequest < 1000) {
      await new Promise(resolve => setTimeout(resolve, 1000 - timeSinceLastRequest));
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Convert file to base64 for Gemini API
   */
  private static async fileToBase64(file: FigureFile): Promise<string> {
    // If already base64, return as is
    if (file.data.startsWith('data:')) {
      return file.data.split(',')[1];
    }
    return file.data;
  }

  /**
   * Execute Gemini API request with multimodal content
   */
  private static async executeGeminiRequest(
    prompt: string,
    figure: FigureFile,
    model: GeminiModel = 'gemini-2.5-flash',
    options: {
      temperature?: number;
      maxTokens?: number;
      useCodeExecution?: boolean;
      useGoogleSearch?: boolean;
    } = {}
  ): Promise<string> {
    await this.checkRateLimit();

    const ai = this.getAI();
    const base64Data = await this.fileToBase64(figure);

    // Configure tools based on options
    const tools: any[] = [];
    if (options.useCodeExecution) {
      tools.push({ codeExecution: {} });
    }
    if (options.useGoogleSearch) {
      tools.push({ googleSearch: {} });
    }

    const config = {
      thinkingConfig: {
        thinkingBudget: -1,
      },
      generationConfig: {
        temperature: options.temperature ?? GEMINI_CONFIG.TEMPERATURE.ANALYSIS,
        maxOutputTokens: options.maxTokens ?? GEMINI_CONFIG.MAX_TOKENS.FLASH,
      },
      ...(tools.length > 0 && { tools }),
      responseMimeType: 'text/plain',
    };

    const contents = [{
      role: 'user' as const,
      parts: [
        { text: prompt },
        {
          inlineData: {
            mimeType: figure.type,
            data: base64Data,
          },
        },
      ],
    }];

    try {
      const response = await ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
        // Handle code execution results
        if (chunk.candidates?.[0]?.content?.parts) {
          for (const part of chunk.candidates[0].content.parts) {
            if (part.codeExecutionResult) {
              fullResponse += `\n[Code Execution Result]: ${part.codeExecutionResult.output}`;
            }
          }
        }
      }

      return fullResponse;
    } catch (error: any) {
      console.error('Gemini API request failed:', error);
      throw new Error(`Gemini analysis failed: ${error.message}`);
    }
  }

  /**
   * Perform quality assessment analysis
   */
  static async analyzeQuality(figure: FigureFile): Promise<QualityAssessment> {
    const prompt = `${ANALYSIS_PROMPTS.QUALITY_ASSESSMENT}

Please analyze this figure and provide a detailed quality assessment in the following JSON format:
{
  "overall": {
    "score": number (0-100),
    "grade": "A" | "B" | "C" | "D" | "F",
    "issues": [{"type": string, "severity": string, "description": string, "suggestion": string}],
    "improvements": [string]
  },
  "resolution": {
    "dpi": number,
    "isHighRes": boolean,
    "printQuality": string,
    "webQuality": string,
    "recommendations": [string]
  },
  "clarity": {
    "sharpness": number (0-100),
    "contrast": number (0-100),
    "brightness": number (0-100),
    "colorBalance": number (0-100),
    "noiseLevel": number (0-100),
    "overallClarity": string
  },
  "readability": {
    "textDetected": boolean,
    "textClarity": number (0-100),
    "fontSize": string,
    "fontContrast": number (0-100),
    "labelVisibility": string,
    "axisReadability": number
  },
  "accessibility": {
    "colorBlindFriendly": boolean,
    "contrastRatio": number,
    "altTextSuggestion": string,
    "screenReaderCompatible": boolean,
    "wcagCompliance": string,
    "improvements": [string]
  },
  "publicationCompliance": {
    "journalStandards": [{"name": string, "compliant": boolean, "requirements": [string], "violations": [string]}],
    "commonIssues": [string],
    "formatRecommendations": [string],
    "citationRequirements": [string]
  }
}`;

    try {
      const response = await this.executeGeminiRequest(prompt, figure, 'gemini-2.5-pro');
      return this.parseJSONResponse<QualityAssessment>(response);
    } catch (error) {
      console.error('Quality analysis failed:', error);
      throw error;
    }
  }

  /**
   * Generate academic captions
   */
  static async generateCaptions(
    figure: FigureFile,
    context: {
      citationStyle: string;
      researchField: string;
      purpose?: string;
      userContext?: string;
    }
  ): Promise<CaptionSuggestion[]> {
    const styleInfo = CITATION_STYLES[context.citationStyle as keyof typeof CITATION_STYLES];
    
    const prompt = `${ANALYSIS_PROMPTS.CAPTION_GENERATION
      .replace('{citationStyle}', `${context.citationStyle} (${styleInfo?.format})`)
      .replace('{researchField}', context.researchField)
      .replace('{context}', context.userContext || 'General academic context')}

Additional context: ${context.purpose || 'Descriptive caption for academic publication'}

Please provide 3-5 caption suggestions in the following JSON format:
{
  "captions": [
    {
      "id": string,
      "text": string,
      "style": string,
      "confidence": number (0-100),
      "context": {
        "figureType": string,
        "researchField": string,
        "purpose": string,
        "audience": string,
        "language": "en"
      },
      "variations": [string]
    }
  ]
}`;

    try {
      const response = await this.executeGeminiRequest(
        prompt, 
        figure, 
        'gemini-2.5-flash',
        { temperature: GEMINI_CONFIG.TEMPERATURE.CREATIVE }
      );
      const result = this.parseJSONResponse<{ captions: CaptionSuggestion[] }>(response);
      return result.captions;
    } catch (error) {
      console.error('Caption generation failed:', error);
      throw error;
    }
  }

  /**
   * Perform advanced analysis (authenticity, cross-reference, etc.)
   */
  static async performAdvancedAnalysis(
    figure: FigureFile,
    analysisTypes: AnalysisType[]
  ): Promise<AdvancedAnalysis> {
    const analysisPrompts = analysisTypes.map(type => {
      switch (type) {
        case 'authenticity':
          return ANALYSIS_PROMPTS.AUTHENTICITY_CHECK;
        case 'cross-reference':
          return ANALYSIS_PROMPTS.CROSS_REFERENCE;
        case 'statistical':
          return ANALYSIS_PROMPTS.STATISTICAL_VALIDATION;
        case 'methodology':
          return ANALYSIS_PROMPTS.METHODOLOGY_EXTRACTION;
        case 'bias':
          return ANALYSIS_PROMPTS.BIAS_DETECTION;
        case 'impact':
          return ANALYSIS_PROMPTS.IMPACT_PREDICTION;
        case 'data-extraction':
          return ANALYSIS_PROMPTS.DATA_EXTRACTION;
        default:
          return '';
      }
    }).filter(Boolean);

    const combinedPrompt = `Perform comprehensive advanced analysis on this figure. Include the following analyses:

${analysisPrompts.join('\n\n---\n\n')}

Please provide results in the following JSON format:
{
  "authenticityCheck": {
    "manipulationLikelihood": number (0-100),
    "suspiciousAreas": [{"coordinates": object, "type": string, "confidence": number, "description": string}],
    "technicalIndicators": [{"type": string, "value": number, "threshold": number, "suspicious": boolean, "description": string}],
    "confidence": number (0-100),
    "recommendation": string
  },
  "crossReference": {
    "similarFigures": [{"source": string, "similarity": number, "type": string, "url": string, "citation": string}],
    "potentialDuplicates": [{"source": string, "confidence": number, "differences": [string], "recommendation": string}],
    "relatedWork": [{"title": string, "authors": [string], "year": number, "relevance": number, "figureDescription": string}],
    "noveltyScore": number (0-100)
  },
  "statisticalValidation": {
    "hasStatistics": boolean,
    "statisticalClaims": [{"claim": string, "type": string, "confidence": number, "supportingEvidence": [string]}],
    "validationResults": [{"claim": string, "valid": boolean, "issues": [string], "suggestions": [string]}],
    "overallValidity": string,
    "recommendations": [string]
  },
  "methodologyExtraction": {
    "detectedMethods": [{"name": string, "confidence": number, "description": string, "strengths": [string], "limitations": [string]}],
    "dataVisualizationType": string,
    "appropriateness": number (0-100),
    "alternatives": [{"name": string, "advantages": [string], "suitability": number, "implementation": string}],
    "bestPractices": [string]
  },
  "biasDetection": {
    "detectedBiases": [{"type": string, "severity": string, "description": string, "evidence": [string], "mitigation": [string]}],
    "overallBiasScore": number (0-100),
    "recommendations": [string],
    "neutralityScore": number (0-100)
  },
  "impactPrediction": {
    "citationPotential": number (0-100),
    "visualAppeal": number (0-100),
    "clarity": number (0-100),
    "novelty": number (0-100),
    "overallImpact": string,
    "factors": [{"name": string, "score": number, "weight": number, "description": string}]
  },
  "dataExtraction": {
    "extractedData": [{"label": string, "value": any, "coordinates": object, "confidence": number}],
    "dataType": string,
    "confidence": number (0-100),
    "structure": {
      "axes": [{"type": string, "label": string, "scale": string, "range": any}],
      "series": [{"name": string, "type": string, "dataPoints": number, "color": string}],
      "legends": [{"items": [{"label": string, "color": string, "symbol": string}], "position": string}],
      "annotations": [{"text": string, "position": object, "type": string}]
    }
  }
}`;

    try {
      const response = await this.executeGeminiRequest(
        combinedPrompt,
        figure,
        'gemini-2.5-pro',
        { 
          useCodeExecution: analysisTypes.includes('statistical') || analysisTypes.includes('data-extraction'),
          useGoogleSearch: analysisTypes.includes('cross-reference'),
          maxTokens: GEMINI_CONFIG.MAX_TOKENS.PRO
        }
      );
      return this.parseJSONResponse<AdvancedAnalysis>(response);
    } catch (error) {
      console.error('Advanced analysis failed:', error);
      throw error;
    }
  }

  /**
   * Comprehensive figure analysis
   */
  static async analyzeFigure(request: AnalysisRequest, figure: FigureFile): Promise<AnalysisResponse> {
    const startTime = Date.now();

    try {
      const result: Partial<FigureAnalysisResult> = {
        figureId: request.figureId,
        timestamp: new Date(),
        recommendations: [],
        confidence: 0,
        processingTime: 0,
      };

      // Perform quality assessment
      if (request.analysisType.includes('quality')) {
        result.qualityAssessment = await this.analyzeQuality(figure);
      }

      // Generate captions
      if (request.analysisType.includes('caption')) {
        result.captionSuggestions = await this.generateCaptions(figure, {
          citationStyle: request.settings.citationStyle,
          researchField: request.settings.researchField,
          userContext: request.context?.userInstructions,
        });
      }

      // Perform advanced analysis
      const advancedTypes = request.analysisType.filter(type => 
        ['authenticity', 'cross-reference', 'statistical', 'methodology', 'bias', 'impact', 'data-extraction'].includes(type)
      );
      
      if (advancedTypes.length > 0) {
        result.advancedAnalysis = await this.performAdvancedAnalysis(figure, advancedTypes);
      }

      // Calculate overall confidence and processing time
      result.confidence = this.calculateOverallConfidence(result);
      result.processingTime = Date.now() - startTime;

      return {
        success: true,
        result: result as FigureAnalysisResult,
        usage: {
          tokensUsed: 0, // TODO: Implement token counting
          cost: 0, // TODO: Implement cost calculation
          processingTime: result.processingTime,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        usage: {
          tokensUsed: 0,
          cost: 0,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Parse JSON response with error handling
   */
  private static parseJSONResponse<T>(response: string): T {
    try {
      // Extract JSON from response if it's wrapped in markdown or other text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Failed to parse JSON response:', response);
      throw new Error('Invalid JSON response from AI model');
    }
  }

  /**
   * Calculate overall confidence score
   */
  private static calculateOverallConfidence(result: Partial<FigureAnalysisResult>): number {
    const scores: number[] = [];

    if (result.qualityAssessment) {
      scores.push(result.qualityAssessment.overall.score);
    }

    if (result.captionSuggestions) {
      const avgCaptionConfidence = result.captionSuggestions.reduce(
        (sum, caption) => sum + caption.confidence, 0
      ) / result.captionSuggestions.length;
      scores.push(avgCaptionConfidence);
    }

    if (result.advancedAnalysis) {
      if (result.advancedAnalysis.authenticityCheck) {
        scores.push(result.advancedAnalysis.authenticityCheck.confidence);
      }
      if (result.advancedAnalysis.crossReference) {
        scores.push(result.advancedAnalysis.crossReference.noveltyScore);
      }
    }

    return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
  }

  /**
   * Test API connectivity
   */
  static async testConnection(): Promise<boolean> {
    try {
      const ai = this.getAI();
      const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: [{ role: 'user', parts: [{ text: 'Hello, this is a test.' }] }],
      });
      return !!response.response.text();
    } catch (error) {
      console.error('Gemini API connection test failed:', error);
      return false;
    }
  }
}
