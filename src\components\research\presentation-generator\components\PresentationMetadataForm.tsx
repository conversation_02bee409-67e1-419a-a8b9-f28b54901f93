import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Sparkles, 
  Users, 
  Globe, 
  Presentation,
  FileText,
  Target
} from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";

import { PresentationMetadata } from '../types';
import { PRESENTATION_STYLES, LANGUAGE_OPTIONS, SLIDE_COUNT_OPTIONS, AI_MODELS } from '../constants';
import { usePresentationStore } from '../stores/presentation.store';

// Validation schema
const metadataSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  topic: z.string().min(10, 'Topic must be at least 10 characters').max(500, 'Topic must be less than 500 characters'),
  slideCount: z.number().min(3, 'Minimum 3 slides').max(30, 'Maximum 30 slides'),
  style: z.enum(['professional', 'casual', 'academic', 'creative']),
  language: z.string().min(1, 'Language is required'),
  audience: z.string().min(1, 'Audience is required').max(100, 'Audience must be less than 100 characters'),
  description: z.string().optional()
});

type MetadataFormData = z.infer<typeof metadataSchema>;

interface PresentationMetadataFormProps {
  onSubmit: (metadata: PresentationMetadata) => void;
  isLoading?: boolean;
  initialData?: Partial<PresentationMetadata>;
}

export function PresentationMetadataForm({ 
  onSubmit, 
  isLoading = false,
  initialData 
}: PresentationMetadataFormProps) {
  const [selectedStyle, setSelectedStyle] = useState(initialData?.style || 'professional');

  const form = useForm<MetadataFormData>({
    resolver: zodResolver(metadataSchema),
    defaultValues: {
      title: initialData?.title || '',
      topic: initialData?.topic || '',
      slideCount: initialData?.slideCount || 10,
      style: initialData?.style || 'professional',
      language: initialData?.language || 'en',
      audience: initialData?.audience || '',
      description: initialData?.description || ''
    }
  });

  const handleSubmit = (data: MetadataFormData) => {
    onSubmit(data);
  };

  const renderStyleCard = (style: typeof PRESENTATION_STYLES[0]) => {
    const isSelected = selectedStyle === style.id;
    
    return (
      <Card 
        key={style.id}
        className={`cursor-pointer transition-all duration-200 ${
          isSelected 
            ? 'ring-2 ring-blue-500 bg-blue-50' 
            : 'hover:shadow-md hover:bg-gray-50'
        }`}
        onClick={() => {
          setSelectedStyle(style.id);
          form.setValue('style', style.id as any);
        }}
      >
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              isSelected ? 'bg-blue-500' : 'bg-gray-300'
            }`} />
            <div>
              <h4 className="font-medium text-gray-900">{style.name}</h4>
              <p className="text-sm text-gray-600">{style.description}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Presentation className="w-8 h-8 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Create Your Presentation
        </h2>
        <p className="text-gray-600">
          Tell us about your presentation and we'll generate a complete slide deck for you.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Presentation Title</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Enter your presentation title"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="audience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        Target Audience
                      </FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., Business executives, Students, General public"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="topic"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Topic & Content</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe your presentation topic in detail. Include key points you want to cover, main objectives, and any specific information you'd like to include."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Any additional context, requirements, or specific instructions for the AI..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Presentation Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Presentation Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="slideCount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Slides</FormLabel>
                      <Select 
                        value={field.value.toString()} 
                        onValueChange={(value) => field.onChange(parseInt(value))}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {SLIDE_COUNT_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              <div>
                                <div className="font-medium">{option.label}</div>
                                <div className="text-sm text-gray-500">{option.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="language"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Globe className="w-4 h-4" />
                        Language
                      </FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {LANGUAGE_OPTIONS.map((lang) => (
                            <SelectItem key={lang.code} value={lang.code}>
                              {lang.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* AI Model Selection */}
              <div>
                <Label className="text-base font-medium mb-4 flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  AI Model
                </Label>
                <ModelSelectionComponent />
              </div>

              {/* Style Selection */}
              <div>
                <Label className="text-base font-medium mb-4 block">Presentation Style</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {PRESENTATION_STYLES.map(renderStyleCard)}
                </div>
                <FormField
                  control={form.control}
                  name="style"
                  render={() => (
                    <FormItem className="hidden">
                      <FormControl>
                        <input type="hidden" value={selectedStyle} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-center">
            <Button 
              type="submit" 
              size="lg"
              disabled={isLoading}
              className="px-8 py-3"
            >
              {isLoading ? (
                <>
                  <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                  Generating Outline...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Presentation
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

// Model Selection Component
function ModelSelectionComponent() {
  const { generationOptions, setGenerationOptions } = usePresentationStore();

  return (
    <div className="space-y-3">
      <Select
        value={generationOptions.model}
        onValueChange={(value) => setGenerationOptions({ model: value })}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select AI model" />
        </SelectTrigger>
        <SelectContent>
          {AI_MODELS.map((model) => (
            <SelectItem key={model.id} value={model.id}>
              <div className="flex items-center justify-between w-full">
                <div>
                  <div className="font-medium">{model.name}</div>
                  <div className="text-sm text-gray-500">{model.provider}</div>
                </div>
                {model.recommended && (
                  <Badge variant="secondary" className="ml-2">Recommended</Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Model Info */}
      {generationOptions.model && (
        <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
          {(() => {
            const selectedModel = AI_MODELS.find(m => m.id === generationOptions.model);
            if (!selectedModel) return null;

            return (
              <div>
                <div className="font-medium mb-1">{selectedModel.name}</div>
                <div className="text-xs space-y-1">
                  <div>Provider: {selectedModel.provider}</div>
                  <div>Max Tokens: {selectedModel.maxTokens.toLocaleString()}</div>
                  <div>Capabilities: {selectedModel.capabilities.join(', ')}</div>
                  {selectedModel.supportsImages && (
                    <div className="text-green-600">✓ Supports images</div>
                  )}
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
