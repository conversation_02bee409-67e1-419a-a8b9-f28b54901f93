/**
 * Tavily Search Service
 * Handles academic research searches using Tavily API
 */

import { 
  TavilySearchResult, 
  TavilyResult, 
  SearchSource, 
  SearchOptions, 
  SearchError 
} from '../types';

export class TavilySearchService {
  private apiKey: string;
  private baseUrl: string = 'https://api.tavily.com';

  constructor() {
    // Use the Tavily API key from environment variables
    this.apiKey = import.meta.env.VITE_TAVILY_API_KEY || '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';

    console.log('Tavily API Key loaded:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT FOUND');

    if (!this.apiKey) {
      console.warn('Tavily API key not found in environment variables');
    }
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return Boolean(this.apiKey && this.apiKey.length > 0);
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const result = await this.searchAcademic('artificial intelligence', { maxResults: 1 });
      return true; // If we get here without error, API is working
    } catch (error) {
      console.error('Tavily API connection test failed:', error);
      return false;
    }
  }

  /**
   * Perform flexible academic search with multiple strategies
   */
  async searchAcademicFlexible(
    query: string,
    options: Partial<SearchOptions> = {}
  ): Promise<TavilySearchResult> {
    console.log(`🔍 Starting flexible academic search for: "${query}"`);

    // Strategy 1: Exact query with minimal enhancement
    try {
      const exactResult = await this.searchAcademic(query, {
        ...options,
        maxResults: Math.ceil((options.maxResults || 10) / 2)
      });

      if (exactResult.results && exactResult.results.length > 0) {
        console.log(`✅ Strategy 1 (exact query) found ${exactResult.results.length} results`);

        // Strategy 2: Enhanced query for additional results
        try {
          const enhancedQuery = this.createAlternativeQuery(query);
          const enhancedResult = await this.searchAcademic(enhancedQuery, {
            ...options,
            maxResults: Math.floor((options.maxResults || 10) / 2)
          });

          // Combine and deduplicate results
          const combinedResults = this.combineAndDeduplicateResults(exactResult, enhancedResult);
          console.log(`✅ Combined search found ${combinedResults.results.length} total results`);
          return combinedResults;

        } catch (enhancedError) {
          console.warn('Enhanced search failed, using exact results:', enhancedError);
          return exactResult;
        }
      }
    } catch (exactError) {
      console.warn('Exact search failed, trying enhanced search:', exactError);
    }

    // Fallback: Enhanced search only
    const enhancedQuery = this.createAlternativeQuery(query);
    console.log(`🔄 Fallback search with enhanced query: "${enhancedQuery}"`);
    return await this.searchAcademic(enhancedQuery, options);
  }

  /**
   * Perform enhanced academic research search with source prioritization
   */
  async searchAcademic(
    query: string,
    options: Partial<SearchOptions> = {}
  ): Promise<TavilySearchResult> {
    if (!this.isConfigured()) {
      throw new Error('Tavily API key not configured');
    }

    const searchOptions = {
      maxResults: options.maxResults || 10,
      searchDepth: options.searchDepth || 'advanced',
      includeImages: options.includeImages || false,
      includeAnswer: options.includeAnswer || true,
      ...options
    };

    try {
      // Enhanced query processing for academic search
      const enhancedQuery = this.enhanceQueryForAcademicSearch(query);

      // Clean and validate the query
      const cleanQuery = enhancedQuery.trim().replace(/[^\w\s\-"]/g, '').substring(0, 400);
      if (!cleanQuery || cleanQuery.length < 3) {
        throw new Error('Search query is too short or invalid');
      }

      // Prioritize academic domains
      const academicDomains = this.getAcademicDomains();
      const governmentDomains = this.getGovernmentDomains();

      const requestBody = {
        api_key: this.apiKey,
        query: cleanQuery,
        search_depth: searchOptions.searchDepth === 'comprehensive' ? 'advanced' : searchOptions.searchDepth,
        include_answer: searchOptions.includeAnswer,
        include_images: searchOptions.includeImages,
        include_raw_content: false,
        max_results: Math.min(searchOptions.maxResults, 20), // Increased for better filtering
        // More flexible domain inclusion - don't restrict too much initially
        include_domains: options.domains && options.domains.length > 0
          ? options.domains
          : undefined, // Let Tavily search broadly first
        exclude_domains: this.getLowQualityDomains()
      };

      // Remove undefined values
      Object.keys(requestBody).forEach(key => {
        if (requestBody[key] === undefined) {
          delete requestBody[key];
        }
      });

      console.log('Enhanced Tavily search request:', { query: cleanQuery, requestBody });

      const response = await fetch(`${this.baseUrl}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) {
          errorMessage = response.statusText || errorMessage;
        }

        console.error('Tavily API error response:', {
          status: response.status,
          statusText: response.statusText,
          message: errorMessage
        });

        throw new Error(`Tavily API error: ${response.status} - ${errorMessage}`);
      }

      const data = await response.json();
      console.log('Tavily API response:', data);

      // Transform Tavily response to our format with enhanced academic prioritization
      const transformedResult = this.transformTavilyResponse(data);

      // Post-process results to prioritize academic sources
      transformedResult.results = this.prioritizeAcademicSources(transformedResult.results);

      return transformedResult;

    } catch (error: any) {
      console.error('Tavily search error:', error);

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to Tavily API');
      }

      if (error.message?.includes('Tavily API error')) {
        throw error; // Re-throw API errors as-is
      }

      throw new Error(`Failed to search: ${error.message}`);
    }
  }

  /**
   * Enhance query for better academic search results with universal field optimization
   */
  private enhanceQueryForAcademicSearch(query: string): string {
    const lowerQuery = query.toLowerCase();

    // Preserve exact quoted terms - these are usually the most important
    const quotedTerms = query.match(/"[^"]+"/g) || [];
    const hasQuotedTerms = quotedTerms.length > 0;

    // Check if query already has academic context
    const academicKeywords = ['research', 'study', 'analysis', 'academic', 'peer-reviewed', 'journal', 'methodology', 'paper'];
    const hasAcademicKeywords = academicKeywords.some(keyword => lowerQuery.includes(keyword));

    // Detect research field from query content
    const fieldDetection = this.detectResearchField(lowerQuery);

    // If query has quoted terms, preserve them and add minimal academic context
    if (hasQuotedTerms) {
      if (!hasAcademicKeywords) {
        return `${query} ${fieldDetection.academicSuffix}`;
      }
      return query; // Already has academic context
    }

    // For queries without quotes, add appropriate academic context
    if (!hasAcademicKeywords) {
      return `${query} ${fieldDetection.academicSuffix}`;
    }

    return query;
  }

  /**
   * Detect research field from query and return appropriate academic enhancement
   */
  private detectResearchField(lowerQuery: string): { field: string; academicSuffix: string } {
    // STEM Fields
    if (this.containsTerms(lowerQuery, ['psinsar', 'insar', 'sar', 'radar', 'interferometry', 'sentinel', 'landsat', 'deformation', 'subsidence'])) {
      return { field: 'remote_sensing', academicSuffix: 'remote sensing journal' };
    }
    if (this.containsTerms(lowerQuery, ['gis', 'spatial', 'geospatial', 'mapping', 'cartography', 'geodesy'])) {
      return { field: 'geospatial', academicSuffix: 'geospatial research' };
    }
    if (this.containsTerms(lowerQuery, ['engineering', 'mechanical', 'civil', 'electrical', 'structural', 'design', 'optimization'])) {
      return { field: 'engineering', academicSuffix: 'engineering journal' };
    }
    if (this.containsTerms(lowerQuery, ['computer', 'algorithm', 'machine learning', 'ai', 'artificial intelligence', 'neural', 'programming'])) {
      return { field: 'computer_science', academicSuffix: 'computer science research' };
    }
    if (this.containsTerms(lowerQuery, ['physics', 'quantum', 'particle', 'energy', 'wave', 'electromagnetic', 'thermodynamics'])) {
      return { field: 'physics', academicSuffix: 'physics journal' };
    }
    if (this.containsTerms(lowerQuery, ['chemistry', 'chemical', 'molecular', 'reaction', 'synthesis', 'catalyst', 'compound'])) {
      return { field: 'chemistry', academicSuffix: 'chemistry research' };
    }
    if (this.containsTerms(lowerQuery, ['mathematics', 'mathematical', 'equation', 'theorem', 'proof', 'calculus', 'algebra'])) {
      return { field: 'mathematics', academicSuffix: 'mathematics journal' };
    }

    // Life Sciences
    if (this.containsTerms(lowerQuery, ['medicine', 'medical', 'clinical', 'patient', 'treatment', 'diagnosis', 'therapy', 'health'])) {
      return { field: 'medicine', academicSuffix: 'medical journal' };
    }
    if (this.containsTerms(lowerQuery, ['biology', 'biological', 'gene', 'protein', 'cell', 'molecular', 'genetic', 'organism'])) {
      return { field: 'biology', academicSuffix: 'biology research' };
    }
    if (this.containsTerms(lowerQuery, ['psychology', 'psychological', 'behavior', 'cognitive', 'mental', 'brain', 'emotion'])) {
      return { field: 'psychology', academicSuffix: 'psychology journal' };
    }

    // Social Sciences
    if (this.containsTerms(lowerQuery, ['sociology', 'social', 'society', 'culture', 'community', 'demographic', 'anthropology'])) {
      return { field: 'social_science', academicSuffix: 'social science research' };
    }
    if (this.containsTerms(lowerQuery, ['economics', 'economic', 'finance', 'market', 'business', 'management', 'trade'])) {
      return { field: 'economics', academicSuffix: 'economics journal' };
    }
    if (this.containsTerms(lowerQuery, ['education', 'educational', 'learning', 'teaching', 'pedagogy', 'curriculum'])) {
      return { field: 'education', academicSuffix: 'education research' };
    }
    if (this.containsTerms(lowerQuery, ['political', 'politics', 'government', 'policy', 'governance', 'democracy'])) {
      return { field: 'political_science', academicSuffix: 'political science journal' };
    }

    // Humanities
    if (this.containsTerms(lowerQuery, ['literature', 'literary', 'language', 'linguistics', 'text', 'narrative', 'poetry'])) {
      return { field: 'literature', academicSuffix: 'literature research' };
    }
    if (this.containsTerms(lowerQuery, ['history', 'historical', 'ancient', 'medieval', 'modern', 'civilization'])) {
      return { field: 'history', academicSuffix: 'history journal' };
    }
    if (this.containsTerms(lowerQuery, ['philosophy', 'philosophical', 'ethics', 'moral', 'metaphysics', 'epistemology'])) {
      return { field: 'philosophy', academicSuffix: 'philosophy journal' };
    }

    // Environmental Sciences
    if (this.containsTerms(lowerQuery, ['environment', 'environmental', 'climate', 'ecology', 'conservation', 'sustainability'])) {
      return { field: 'environmental', academicSuffix: 'environmental research' };
    }

    // Default for unrecognized fields
    return { field: 'general', academicSuffix: 'academic research' };
  }

  /**
   * Check if query contains any of the specified terms
   */
  private containsTerms(query: string, terms: string[]): boolean {
    return terms.some(term => query.includes(term));
  }

  /**
   * Create alternative query for broader search
   */
  private createAlternativeQuery(originalQuery: string): string {
    const lowerQuery = originalQuery.toLowerCase();

    // Extract key technical terms
    const technicalTerms = originalQuery.match(/\b[A-Z]{2,6}\b/g) || []; // Acronyms
    const quotedTerms = originalQuery.match(/"[^"]+"/g) || [];

    // If we have specific technical terms, create variations
    if (technicalTerms.length > 0 || quotedTerms.length > 0) {
      const keyTerms = [...technicalTerms, ...quotedTerms.map(q => q.replace(/"/g, ''))];

      // Create alternative with synonyms and related terms
      let alternative = keyTerms.join(' ');

      // Add domain-specific alternatives
      if (lowerQuery.includes('psinsar') || lowerQuery.includes('insar')) {
        alternative += ' interferometry radar deformation monitoring';
      }
      if (lowerQuery.includes('sentinel') || lowerQuery.includes('sar')) {
        alternative += ' satellite remote sensing earth observation';
      }
      if (lowerQuery.includes('subsidence') || lowerQuery.includes('deformation')) {
        alternative += ' ground movement displacement monitoring';
      }

      return alternative + ' research study';
    }

    // For general queries, try different academic phrasings
    const words = originalQuery.split(/\s+/).filter(word => word.length > 3);
    if (words.length >= 2) {
      return `${words.slice(0, 3).join(' ')} academic research methodology`;
    }

    return originalQuery + ' scientific study';
  }

  /**
   * Combine and deduplicate search results
   */
  private combineAndDeduplicateResults(
    result1: TavilySearchResult,
    result2: TavilySearchResult
  ): TavilySearchResult {
    const combinedResults = [...(result1.results || []), ...(result2.results || [])];
    const seenUrls = new Set<string>();
    const uniqueResults = [];

    for (const result of combinedResults) {
      if (!seenUrls.has(result.url)) {
        seenUrls.add(result.url);
        uniqueResults.push(result);
      }
    }

    return {
      query: result1.query,
      results: uniqueResults,
      answer: result1.answer || result2.answer,
      response_time: Math.max(result1.response_time || 0, result2.response_time || 0)
    };
  }

  /**
   * Get prioritized academic domains with universal coverage for all research fields
   */
  private getAcademicDomains(): string[] {
    return [
      // General academic search engines (highest priority)
      'scholar.google.com',
      'arxiv.org',
      'researchgate.net',
      'jstor.org',
      'semanticscholar.org',

      // Major publishers (universal coverage)
      'springer.com',
      'sciencedirect.com',
      'wiley.com',
      'nature.com',
      'science.org',
      'frontiersin.org',
      'mdpi.com',
      'tandfonline.com',
      'sagepub.com',
      'cambridge.org',
      'oxford.org',

      // STEM fields
      'ieee.org',           // Engineering, Computer Science
      'acm.org',            // Computer Science
      'ams.org',            // Mathematics
      'aps.org',            // Physics
      'rsc.org',            // Chemistry
      'asme.org',           // Mechanical Engineering
      'asce.org',           // Civil Engineering

      // Life Sciences & Medicine
      'pubmed.ncbi.nlm.nih.gov',
      'plos.org',
      'bmj.com',
      'nejm.org',
      'thelancet.com',
      'cell.com',
      'biomedcentral.com',
      'nih.gov',

      // Social Sciences & Humanities
      'psycnet.apa.org',    // Psychology
      'jstor.org',          // Humanities
      'anthropology.org',   // Anthropology
      'sociologycompass.com', // Sociology
      'econlit.org',        // Economics
      'mla.org',            // Literature

      // Earth & Environmental Sciences
      'copernicus.org',
      'earthdata.nasa.gov',
      'esa.int',
      'usgs.gov',
      'noaa.gov',
      'ipcc.ch',

      // Business & Management
      'emerald.com',
      'business.org',
      'hbr.org',

      // Educational institutions (global)
      '.edu',              // US universities
      '.ac.uk',            // UK universities
      '.edu.au',           // Australian universities
      '.ac.in',            // Indian universities
      '.edu.cn',           // Chinese universities
      '.ac.jp',            // Japanese universities

      // Government and institutional sources
      '.gov',              // Government sources
      '.org',              // Non-profit organizations
      'who.int',           // World Health Organization
      'worldbank.org',     // World Bank
      'un.org',            // United Nations
      'oecd.org'           // OECD
    ];
  }

  /**
   * Get government and official domains
   */
  private getGovernmentDomains(): string[] {
    return [
      'nih.gov',
      'cdc.gov',
      'fda.gov',
      'nsf.gov',
      'nasa.gov',
      'epa.gov',
      'usda.gov',
      'who.int',
      'oecd.org',
      'worldbank.org',
      'un.org',
      'europa.eu'
    ];
  }

  /**
   * Get domains to exclude for better quality
   */
  private getLowQualityDomains(): string[] {
    return [
      'wikipedia.org', // While useful, prioritize primary sources
      'quora.com',
      'reddit.com',
      'yahoo.com',
      'ask.com',
      'answers.com'
    ];
  }

  /**
   * Prioritize academic sources in search results
   */
  private prioritizeAcademicSources(results: TavilyResult[]): TavilyResult[] {
    return results.sort((a, b) => {
      const aIsAcademic = this.isAcademicSource(a.url, a.title);
      const bIsAcademic = this.isAcademicSource(b.url, b.title);
      const aIsGovernment = this.isGovernmentSource(a.url, a.title);
      const bIsGovernment = this.isGovernmentSource(b.url, b.title);

      // Academic sources first
      if (aIsAcademic && !bIsAcademic) return -1;
      if (!aIsAcademic && bIsAcademic) return 1;

      // Then government sources
      if (aIsGovernment && !bIsGovernment) return -1;
      if (!aIsGovernment && bIsGovernment) return 1;

      // Finally by score
      return b.score - a.score;
    });
  }

  /**
   * Check if source is academic
   */
  private isAcademicSource(url: string, title: string): boolean {
    const academicDomains = this.getAcademicDomains();
    const academicKeywords = [
      'journal', 'research', 'study', 'analysis', 'peer-reviewed',
      'systematic review', 'meta-analysis', 'clinical trial'
    ];

    const domain = url.toLowerCase();
    const titleLower = title.toLowerCase();

    return academicDomains.some(acadDomain => domain.includes(acadDomain)) ||
           academicKeywords.some(keyword => titleLower.includes(keyword));
  }

  /**
   * Check if source is government/official
   */
  private isGovernmentSource(url: string, title: string): boolean {
    const governmentDomains = this.getGovernmentDomains();
    const governmentKeywords = [
      'government', 'federal', 'state', 'department', 'agency',
      'bureau', 'administration', 'commission', 'ministry'
    ];

    const domain = url.toLowerCase();
    const titleLower = title.toLowerCase();

    return governmentDomains.some(govDomain => domain.includes(govDomain)) ||
           governmentKeywords.some(keyword => titleLower.includes(keyword));
  }

  /**
   * Get search suggestions based on query
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    // For now, return academic-focused suggestions
    // In the future, this could use Tavily's suggestion API if available
    const suggestions = [
      `${query} recent research`,
      `${query} systematic review`,
      `${query} meta-analysis`,
      `${query} literature review`,
      `${query} academic papers`,
      `${query} peer reviewed studies`
    ];

    return suggestions.slice(0, 4);
  }

  /**
   * Transform Tavily response to our SearchSource format
   */
  private transformTavilyResponse(data: any): TavilySearchResult {
    const results: TavilyResult[] = (data.results || []).map((result: any) => ({
      title: result.title || '',
      url: result.url || '',
      content: result.content || '',
      raw_content: result.raw_content || '',
      score: result.score || 0,
      published_date: result.published_date || undefined
    }));

    return {
      query: data.query || '',
      follow_up_questions: data.follow_up_questions || [],
      answer: data.answer || '',
      images: data.images || [],
      results,
      response_time: data.response_time || 0
    };
  }

  /**
   * Convert SearchSource to TavilyResult format
   */
  transformToSearchSources(tavilyResults: TavilyResult[]): SearchSource[] {
    return tavilyResults.map((result, index) => ({
      id: `tavily-${index}`,
      title: result.title,
      url: result.url,
      snippet: result.content.substring(0, 300) + (result.content.length > 300 ? '...' : ''),
      domain: this.extractDomain(result.url),
      publishedDate: result.published_date,
      score: result.score,
      type: this.determineSourceType(result.url, result.title)
    }));
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return 'unknown';
    }
  }

  /**
   * Enhanced source type determination with academic prioritization
   */
  private determineSourceType(url: string, title: string): 'academic' | 'web' | 'news' | 'book' {
    const domain = this.extractDomain(url);
    const titleLower = title.toLowerCase();

    // Enhanced academic domain detection
    const academicDomains = this.getAcademicDomains();
    const governmentDomains = this.getGovernmentDomains();

    const newsDomains = [
      'bbc.com', 'cnn.com', 'reuters.com', 'ap.org', 'npr.org',
      'theguardian.com', 'nytimes.com', 'washingtonpost.com',
      'bloomberg.com', 'wsj.com', 'economist.com'
    ];

    // Check for academic sources first (highest priority)
    if (academicDomains.some(d => domain.includes(d)) ||
        governmentDomains.some(d => domain.includes(d))) {
      return 'academic';
    }

    // Check for academic keywords in title
    const academicKeywords = [
      'journal', 'research', 'study', 'analysis', 'peer-reviewed',
      'systematic review', 'meta-analysis', 'clinical trial', 'proceedings',
      'conference', 'university', 'academic', 'scholarly'
    ];

    if (academicKeywords.some(keyword => titleLower.includes(keyword))) {
      return 'academic';
    }

    // Check for news sources
    if (newsDomains.some(d => domain.includes(d))) {
      return 'news';
    }

    // Check for books
    if (titleLower.includes('book') || domain.includes('books.google') ||
        titleLower.includes('chapter') || titleLower.includes('textbook')) {
      return 'book';
    }

    return 'web';
  }

  /**
   * Calculate days from date string
   */
  private calculateDaysFromDate(dateString: string): number {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Validate search query
   */
  validateQuery(query: string): { isValid: boolean; error?: string } {
    if (!query || query.trim().length === 0) {
      return { isValid: false, error: 'Search query cannot be empty' };
    }
    
    if (query.length < 3) {
      return { isValid: false, error: 'Search query must be at least 3 characters long' };
    }
    
    if (query.length > 500) {
      return { isValid: false, error: 'Search query is too long (max 500 characters)' };
    }
    
    return { isValid: true };
  }
}

export const tavilySearchService = new TavilySearchService();

// Add to window for testing
if (typeof window !== 'undefined') {
  (window as any).testTavilyAPI = async () => {
    console.log('Testing Tavily API...');
    try {
      const result = await tavilySearchService.searchAcademic('artificial intelligence research', { maxResults: 2 });
      console.log('✅ Tavily API test successful:', result);
      return result;
    } catch (error) {
      console.error('❌ Tavily API test failed:', error);
      throw error;
    }
  };
}
