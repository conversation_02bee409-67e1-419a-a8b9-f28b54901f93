/**
 * Unified Source Selection Component
 * Reusable component for selecting knowledge sources across AI Tutor features
 */

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Globe, 
  FileText, 
  Sparkles, 
  CheckCircle, 
  AlertCircle,
  Database,
  Search,
  BookOpen
} from "lucide-react";
import { ResearchDocument } from '../types';

export type SourceType = 'ai' | 'web' | 'documents' | 'mixed';

interface SourceOption {
  value: SourceType;
  label: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  features: string[];
  requirements?: string;
}

interface SourceSelectorProps {
  selectedSource: SourceType;
  onSourceChange: (source: SourceType) => void;
  uploadedDocuments?: ResearchDocument[];
  showMixed?: boolean;
  title?: string;
  description?: string;
  className?: string;
  variant?: 'cards' | 'buttons' | 'compact';
}

const sourceOptions: SourceOption[] = [
  {
    value: 'ai',
    label: 'AI Knowledge',
    description: 'Use AI\'s built-in knowledge base',
    icon: Brain,
    color: 'bg-blue-500',
    features: ['Instant responses', 'Broad knowledge', 'No setup required'],
  },
  {
    value: 'web',
    label: 'Web Search',
    description: 'Search current information from the internet',
    icon: Globe,
    color: 'bg-green-500',
    features: ['Current information', 'Real-time data', 'Latest research'],
    requirements: 'Requires internet connection'
  },
  {
    value: 'documents',
    label: 'Your Documents',
    description: 'Use your uploaded documents as knowledge source',
    icon: FileText,
    color: 'bg-purple-500',
    features: ['Personalized content', 'Specific materials', 'Custom knowledge'],
    requirements: 'Requires uploaded documents'
  },
  {
    value: 'mixed',
    label: 'Mixed Sources',
    description: 'Combine multiple knowledge sources for comprehensive answers',
    icon: Sparkles,
    color: 'bg-gradient-to-r from-blue-500 to-purple-500',
    features: ['Comprehensive answers', 'Multiple perspectives', 'Enhanced accuracy'],
  }
];

export function SourceSelector({
  selectedSource,
  onSourceChange,
  uploadedDocuments = [],
  showMixed = true,
  title = "Knowledge Source",
  description = "Choose where the AI should get information from",
  className = "",
  variant = 'cards'
}: SourceSelectorProps) {
  
  const availableOptions = sourceOptions.filter(option => 
    showMixed || option.value !== 'mixed'
  );

  const isSourceAvailable = (source: SourceType): boolean => {
    if (source === 'documents') {
      return uploadedDocuments.length > 0;
    }
    return true;
  };

  const getSourceStatus = (source: SourceType): 'available' | 'unavailable' | 'selected' => {
    if (selectedSource === source) return 'selected';
    if (!isSourceAvailable(source)) return 'unavailable';
    return 'available';
  };

  if (variant === 'compact') {
    return (
      <div className={`space-y-2 ${className}`}>
        <label className="text-sm font-medium text-gray-700">{title}</label>
        <div className="flex flex-wrap gap-2">
          {availableOptions.map((source) => {
            const status = getSourceStatus(source.value);
            return (
              <Button
                key={source.value}
                variant={status === 'selected' ? "default" : "outline"}
                size="sm"
                onClick={() => status === 'available' ? onSourceChange(source.value) : null}
                disabled={status === 'unavailable'}
                className={`flex items-center space-x-2 ${
                  status === 'selected' 
                    ? 'bg-blue-600 text-white shadow-md' 
                    : status === 'available'
                    ? 'bg-white/80 border-gray-200/50 hover:bg-blue-50'
                    : 'opacity-50 cursor-not-allowed'
                }`}
              >
                <source.icon className="w-4 h-4" />
                <span>{source.label}</span>
                {status === 'selected' && <CheckCircle className="w-4 h-4" />}
              </Button>
            );
          })}
        </div>
        {selectedSource === 'documents' && uploadedDocuments.length === 0 && (
          <p className="text-xs text-amber-600">Upload documents to use them as a source</p>
        )}
      </div>
    );
  }

  if (variant === 'buttons') {
    return (
      <div className={`space-y-4 ${className}`}>
        <div>
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {availableOptions.map((source) => {
            const status = getSourceStatus(source.value);
            return (
              <Button
                key={source.value}
                variant={status === 'selected' ? "default" : "outline"}
                className={`p-4 h-auto justify-start ${
                  status === 'selected' 
                    ? 'bg-blue-600 text-white border-blue-600' 
                    : status === 'available'
                    ? 'hover:bg-blue-50 hover:border-blue-300'
                    : 'opacity-50 cursor-not-allowed'
                }`}
                onClick={() => status === 'available' ? onSourceChange(source.value) : null}
                disabled={status === 'unavailable'}
              >
                <div className="flex items-center space-x-3 w-full">
                  <div className={`p-2 rounded-lg ${
                    status === 'selected' ? 'bg-white/20' : source.color
                  }`}>
                    <source.icon className={`w-5 h-5 ${
                      status === 'selected' ? 'text-white' : 'text-white'
                    }`} />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium">{source.label}</div>
                    <div className={`text-sm ${
                      status === 'selected' ? 'text-blue-100' : 'text-gray-600'
                    }`}>
                      {source.description}
                    </div>
                  </div>
                  {status === 'selected' && (
                    <CheckCircle className="w-5 h-5 text-white" />
                  )}
                  {status === 'unavailable' && (
                    <AlertCircle className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </Button>
            );
          })}
        </div>
      </div>
    );
  }

  // Default cards variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Database className="w-5 h-5 text-blue-500" />
          <span>{title}</span>
        </CardTitle>
        <p className="text-sm text-gray-600">{description}</p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {availableOptions.map((source) => {
            const status = getSourceStatus(source.value);
            return (
              <div
                key={source.value}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  status === 'selected'
                    ? 'border-blue-500 bg-blue-50'
                    : status === 'available'
                    ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    : 'border-gray-200 opacity-50 cursor-not-allowed'
                }`}
                onClick={() => status === 'available' ? onSourceChange(source.value) : null}
              >
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${source.color} flex-shrink-0`}>
                    <source.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{source.label}</h3>
                      {status === 'selected' && (
                        <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0" />
                      )}
                      {status === 'unavailable' && (
                        <AlertCircle className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{source.description}</p>
                    
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-1">
                        {source.features.map((feature, index) => (
                          <Badge 
                            key={index} 
                            variant="secondary" 
                            className="text-xs bg-gray-100 text-gray-700"
                          >
                            {feature}
                          </Badge>
                        ))}
                      </div>
                      
                      {source.requirements && status === 'unavailable' && (
                        <p className="text-xs text-amber-600 flex items-center">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          {source.requirements}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {selectedSource === 'documents' && uploadedDocuments.length === 0 && (
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-800">Documents Required</span>
            </div>
            <p className="text-sm text-amber-700 mt-1">
              Please upload documents in the Documents tab to use them as a knowledge source.
            </p>
          </div>
        )}
        
        {selectedSource === 'web' && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Search className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Web Search Active</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              The AI will search the internet for current information about your topic.
            </p>
          </div>
        )}
        
        {selectedSource === 'mixed' && (
          <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">Mixed Sources Active</span>
            </div>
            <p className="text-sm text-purple-700 mt-1">
              The AI will combine information from multiple sources for comprehensive answers.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
