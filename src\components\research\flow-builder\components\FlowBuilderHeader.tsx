/**
 * Flow Builder Header Component
 * Header with title, stats, and quick actions
 */

import React from 'react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  GitBranch,
  History,
  RefreshCw,
  Settings,
  HelpCircle,
  Sparkles,
  BarChart3,
  Clock
} from 'lucide-react';

interface FlowBuilderHeaderProps {
  onShowHistory: () => void;
  onReset: () => void;
  historyCount: number;
  className?: string;
}

export function FlowBuilderHeader({
  onShowHistory,
  onReset,
  historyCount,
  className = ""
}: FlowBuilderHeaderProps) {
  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Title Section */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                <GitBranch className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Flow Chart Builder</h1>
                <p className="text-sm text-gray-600">AI-powered diagram generation for research methodologies</p>
              </div>
            </div>
            
            <Separator orientation="vertical" className="h-12" />
            
            {/* Stats */}
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">{historyCount}</div>
                <div className="text-xs text-gray-500">Diagrams Created</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">9</div>
                <div className="text-xs text-gray-500">Diagram Types</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-semibold text-purple-600">4</div>
                <div className="text-xs text-gray-500">AI Models</div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {/* Quick Stats Badges */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <Sparkles className="h-3 w-3 mr-1" />
                AI Powered
              </Badge>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <BarChart3 className="h-3 w-3 mr-1" />
                Research Ready
              </Badge>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Action Buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={onShowHistory}
              className="flex items-center gap-2"
            >
              <History className="h-4 w-4" />
              History
              {historyCount > 0 && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {historyCount}
                </Badge>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Reset
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <HelpCircle className="h-4 w-4" />
              Help
            </Button>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="mt-4 flex items-center gap-6 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span>Natural language input</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>Multiple export formats</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
            <span>Research templates</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
            <span>Real-time editing</span>
          </div>
        </div>
      </div>
    </div>
  );
}
