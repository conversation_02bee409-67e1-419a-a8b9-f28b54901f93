# Google OAuth Complete Fix Guide

## 🔍 **Current Issue Analysis**

The error `Unable to exchange external code: 4/0AVMBsJj0RLUattggUSxTiAfuxDBbx4Uupl7NAKxRW4raRFQnSDjjumi2o5P0Q4vJZ4MH3Q` indicates:

1. ✅ **Google OAuth redirect is working** - Google successfully redirects to your app
2. ❌ **Supabase can't exchange the code** - Google OAuth provider not configured in Supabase
3. ✅ **Your Google Cloud Console is configured correctly** - Has the right redirect URIs

## 🛠️ **STEP-BY-STEP FIX**

### Step 1: Configure Google OAuth in Supabase Dashboard

1. **Open Supabase Dashboard**:
   ```
   https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers
   ```

2. **Find Google Provider**:
   - Look for "Google" in the list of providers
   - Click on it to configure

3. **Enable Google Provider**:
   - Toggle the "Enable" switch to ON

4. **Add Google Credentials**:
   You need to get these from your Google Cloud Console:
   - **Client ID**: From your Google Cloud Console OAuth 2.0 client
   - **Client Secret**: From your Google Cloud Console OAuth 2.0 client

### Step 2: Update Site URL and Redirect URLs

1. **In the same Supabase Auth Settings**:
   - **Site URL**: Set to `http://localhost:8081`
   - **Redirect URLs**: Add these:
     - `http://localhost:8081/auth/callback`
     - `http://localhost:8081/**`

### Step 3: Get Google OAuth Credentials

1. **Go to Google Cloud Console**:
   ```
   https://console.cloud.google.com/apis/credentials
   ```

2. **Find your OAuth 2.0 Client**:
   - Look for the client you created for this project
   - Click on it to view details

3. **Copy the credentials**:
   - **Client ID**: Looks like `xxxxx.apps.googleusercontent.com`
   - **Client Secret**: A long random string

4. **Verify Redirect URIs in Google Cloud**:
   Make sure these are configured:
   - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
   - `http://localhost:8081/auth/callback`

### Step 4: Test Both Authentication Methods

#### Test Email/Password (Should work now):
1. Go to: `http://localhost:8081/login`
2. Use: `<EMAIL>` / `Password123!`
3. Should login successfully

#### Test Google OAuth (After configuration):
1. Go to: `http://localhost:8081/login`
2. Click "Continue with Google"
3. Should redirect to Google, then back to your app successfully

## 🔧 **If You Don't Have Google OAuth Credentials**

If you need to create new Google OAuth credentials:

1. **Go to Google Cloud Console**:
   ```
   https://console.cloud.google.com/apis/credentials
   ```

2. **Create OAuth 2.0 Client ID**:
   - Click "Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: "Web application"
   - Name: "Verbira App"

3. **Add Authorized Redirect URIs**:
   - `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
   - `http://localhost:8081/auth/callback`

4. **Save and copy the credentials**

## 🧪 **Testing Checklist**

- [ ] Email/Password login works with demo account
- [ ] Google OAuth provider enabled in Supabase
- [ ] Google credentials added to Supabase
- [ ] Site URL set to `http://localhost:8081`
- [ ] Redirect URLs configured
- [ ] Google OAuth login works without errors
- [ ] User can access dashboard after login
- [ ] Research search works without authentication errors

## 🚨 **Common Issues & Solutions**

### Issue: "Invalid redirect URI"
**Solution**: Make sure redirect URIs match exactly in both Google Cloud Console and Supabase

### Issue: "Client ID not found"
**Solution**: Double-check the Client ID is copied correctly to Supabase

### Issue: "Access blocked"
**Solution**: Make sure your Google Cloud project has the necessary APIs enabled

### Issue: Still getting "Unable to exchange external code"
**Solution**: 
1. Clear browser cache and cookies
2. Try incognito/private browsing mode
3. Verify all credentials are saved in Supabase

## 📝 **Quick Reference**

**Your Supabase Project**: `swsnqpavwcnqiihsidss`
**Dashboard URL**: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss/auth/providers
**App URL**: http://localhost:8081
**Demo Login**: <EMAIL> / Password123!

## 🎯 **Expected Result**

After completing these steps:
- ✅ Email/password authentication works
- ✅ Google OAuth authentication works
- ✅ No more "Unable to exchange external code" errors
- ✅ Users can login and access all app features
- ✅ App is ready for production deployment
