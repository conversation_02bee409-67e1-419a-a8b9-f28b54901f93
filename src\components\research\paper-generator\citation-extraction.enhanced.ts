/**
 * Service for extracting and managing citations from AI-generated paper sections
 */

import { Citation } from './types';

/**
 * Extract citations from text in (Author, Year) format
 */
export function extractCitationsFromText(text: string, sectionId: string): { 
  citations: Citation[];
  matches: string[]
} {
  // Regular expression to match citations in various formats:
  // (<PERSON>, 2023)
  // (<PERSON> et al., 2023)
  // (<PERSON> and <PERSON>, 2023)
  // (<PERSON> & <PERSON>, 2023)
  // (Smith, <PERSON>, & <PERSON>, 2023)
  // (Smith, 2023; Jones, 2022)
  const citationRegex = /\(([^)]+?,\s*\d{4}[^)]*)\)/g;
  
  const matches = [...text.matchAll(citationRegex)].map(match => match[0]);
  const uniqueMatches = Array.from(new Set(matches));
  
  let allCitations: Citation[] = [];
  
  // Create citation objects from matches
  uniqueMatches.forEach(match => {
    // Clean up the citation text
    const citationText = match.replace(/[()]/g, '').trim();
    
    // Handle multiple citations separated by semicolons (<PERSON>, 2023; <PERSON>, 2022)
    const multipleCitations = citationText.split(';').map(part => part.trim());
    
    multipleCitations.forEach(singleCitation => {
      // Parse author and year information
      const lastCommaIndex = singleCitation.lastIndexOf(',');
      if (lastCommaIndex === -1) return; // Skip invalid citations
      
      let authorPart = singleCitation.substring(0, lastCommaIndex).trim();
      const yearPart = singleCitation.substring(lastCommaIndex + 1).trim();
      
      // Extract year as a number
      const yearMatch = yearPart.match(/\d{4}/);
      if (!yearMatch) return; // Skip if no valid year found
      
      const year = parseInt(yearMatch[0]);
      
      // Process authors
      let authors: string[] = [];
      if (authorPart.includes(' et al.')) {
        authorPart = authorPart.replace(' et al.', '');
        authors = [authorPart];
      } else if (authorPart.includes(' and ')) {
        authors = authorPart.split(' and ').map(author => author.trim());
      } else if (authorPart.includes(' & ')) {
        authors = authorPart.split(' & ').map(author => author.trim());
      } else if (authorPart.includes(', ') && (authorPart.includes(' and ') || authorPart.includes(' & '))) {
        // Handle format like "Smith, Jones, & Lee"
        let parts = authorPart.split(', ');
        
        // Last part might contain "and" or "&"
        const lastPart = parts.pop() || '';
        if (lastPart.includes(' and ')) {
          const lastAuthors = lastPart.split(' and ').map(a => a.trim());
          parts.push(...lastAuthors);
        } else if (lastPart.includes(' & ')) {
          const lastAuthors = lastPart.split(' & ').map(a => a.trim());
          parts.push(...lastAuthors);
        } else {
          parts.push(lastPart);
        }
        
        authors = parts.filter(p => p.trim() !== '');
      } else {
        authors = [authorPart];
      }
      
      // Filter out any empty author names
      authors = authors.filter(author => author.trim() !== '');
      if (authors.length === 0) return; // Skip if no valid authors
      
      // Generate a unique ID based on authors and year
      const authorsKey = authors.join('-').toLowerCase().replace(/[^a-z0-9]/g, '');
      const id = `citation-${authorsKey}-${year}`;
      
      // Get the in-text format - for multiple citations, use just this citation's part
      const inTextFormat = multipleCitations.length > 1 ? 
        `(${singleCitation})` : match;
      
      allCitations.push({
        id,
        inTextFormat,
        authors,
        year,
        title: '', // These will be filled in later when matched with references
        source: '',
        sectionIds: [sectionId],
        referenceText: '' // Will be populated when matched with references
      });
    });
  });
  
  return {
    citations: allCitations,
    matches: uniqueMatches
  };
}

/**
 * Split a references section text into individual reference entries
 */
export function splitReferencesIntoEntries(referencesText: string): string[] {
  // Remove the 'References' header if present
  referencesText = referencesText.replace(/^\s*references\s*(:|\n)/i, '');
  
  // Try different splitting strategies
  
  // Strategy 1: Split by numbered references (e.g. "1. Smith, J. (2023)...")
  const numberedPattern = /(?:^|\n)\d+\.\s+/g;
  if (referencesText.match(numberedPattern)) {
    return referencesText.split(numberedPattern)
      .filter(entry => entry.trim().length > 0)
      .map(entry => entry.trim());
  }
  
  // Strategy 2: Split by new paragraph (double newline)
  const paragraphEntries = referencesText.split(/\n\s*\n/)
    .filter(entry => entry.trim().length > 0 && entry.match(/\(\d{4}\)/))
    .map(entry => entry.trim());
  
  if (paragraphEntries.length > 1) {
    return paragraphEntries;
  }
  
  // Strategy 3: Split by author name pattern at start of line
  const authorStartEntries = referencesText.split(/\n(?=[A-Z][a-z]+,\s+[A-Z]\.|\s+[A-Z][a-z]+\s+(?:et\s+al\.|\([0-9]{4}\)))/g)
    .filter(entry => entry.trim().length > 0 && entry.match(/\(\d{4}\)/))
    .map(entry => entry.trim());
  
  if (authorStartEntries.length > 1) {
    return authorStartEntries;
  }
  
  // Final fallback: just break by new lines if they look like separate references
  return referencesText
    .split('\n')
    .filter(line => 
      line.trim() !== '' && 
      line.trim().length > 30 &&
      line.match(/\(\d{4}\)/) &&
      !line.toLowerCase().startsWith('references')
    )
    .map(line => line.trim());
}

/**
 * Attempt to match extracted citations with full reference information from references text
 */
export function matchCitationsWithReferences(citations: Citation[], referencesText: string): Citation[] {
  if (!referencesText || citations.length === 0) {
    return citations;
  }
  
  console.log(`Matching ${citations.length} citations with references text (${referencesText.length} chars)`);
  
  // Split references into individual entries using multiple strategies
  const references = splitReferencesIntoEntries(referencesText);
  
  console.log(`Found ${references.length} individual references in text`);
  
  // Enhanced matching that prioritizes authors and years
  return citations.map(citation => {
    // Skip if the citation already has reference text
    if (citation.referenceText && citation.referenceText.trim() !== '') {
      return citation;
    }
    
    // Get all author last names and the year for more reliable matching
    const authorLastNames = citation.authors.map(author => {
      // Handle format like "Smith" or "Smith, J."
      const parts = author.split(/,|\s+/).filter(p => p.trim() !== '');
      return parts[0].toLowerCase(); // First part is usually the last name
    });
    const yearStr = citation.year.toString();
    
    // Try to find a matching reference with multiple strategies
    
    // Strategy 1: Exact match on first author last name and year
    let matchingRef = references.find(ref => {
      const refLower = ref.toLowerCase();
      const firstAuthorMatches = refLower.includes(authorLastNames[0]);
      const yearMatches = ref.includes(yearStr);
      return firstAuthorMatches && yearMatches;
    });
    
    // Strategy 2: Match on any author last name and year
    if (!matchingRef && authorLastNames.length > 1) {
      matchingRef = references.find(ref => {
        const refLower = ref.toLowerCase();
        const anyAuthorMatches = authorLastNames.some(name => refLower.includes(name));
        const yearMatches = ref.includes(yearStr);
        return anyAuthorMatches && yearMatches;
      });
    }
    
    // Strategy 3: Match just on year and check if ref contains text similar to in-text format
    if (!matchingRef) {
      const citationText = citation.inTextFormat.replace(/[()]/g, '').toLowerCase();
      matchingRef = references.find(ref => {
        const yearMatches = ref.includes(yearStr);
        if (!yearMatches) return false;
        
        // Check if significant words from citation text appear in the reference
        const significantWords = citationText.split(/\W+/).filter(word => word.length > 3);
        const textMatches = significantWords.some(word => ref.toLowerCase().includes(word));
        
        return yearMatches && textMatches;
      });
    }
    
    if (matchingRef) {
      // Found a matching reference
      console.log(`Matched citation ${citation.inTextFormat} to reference`);
      return {
        ...citation,
        referenceText: matchingRef
      };
    }
    
    console.log(`No reference match found for citation ${citation.inTextFormat}`);
    return citation;
  });
}
