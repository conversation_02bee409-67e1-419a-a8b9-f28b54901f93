import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { ScrollArea } from "@/components/ui/scroll-area";
import {
  BookOpen,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  FileText,
  Clock,
  Quote,
  CheckCircle
} from "lucide-react";
import { GeminiGeneratedChapter } from '../types';

interface GeminiChapterReaderProps {
  chapters: GeminiGeneratedChapter[];
  currentGeneratingIndex: number;
  totalChapters: number;
  isGenerating: boolean;
}

export const GeminiChapterReader: React.FC<GeminiChapterReaderProps> = ({
  chapters,
  currentGeneratingIndex,
  totalChapters,
  isGenerating
}) => {
  const [selectedChapterIndex, setSelectedChapterIndex] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);

  // Auto-select the latest chapter when new chapters are added
  React.useEffect(() => {
    if (chapters.length > 0) {
      const newIndex = chapters.length - 1;
      console.log(`=== CHAPTER READER: Auto-selecting chapter ${newIndex + 1} ===`);
      console.log(`Available chapters:`, chapters.map(ch => ({ id: ch.id, title: ch.title, order: ch.order })));
      setSelectedChapterIndex(newIndex);
    }
  }, [chapters.length]);

  const selectedChapter = chapters[selectedChapterIndex];
  const hasChapters = chapters.length > 0;

  if (!hasChapters) {
    return (
      <Card className="h-96">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-gray-400" />
            Chapter Reader
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No chapters generated yet</p>
              <p className="text-sm">Completed chapters will appear here for reading</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`transition-all duration-300 ${isExpanded ? 'col-span-2' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            Chapter Reader
            <Badge variant="secondary">
              {chapters.length} of {totalChapters} completed
            </Badge>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Chapter Navigation */}
        <div className="flex items-center gap-2 mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSelectedChapterIndex(Math.max(0, selectedChapterIndex - 1))}
            disabled={selectedChapterIndex === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 text-center">
            <select
              value={selectedChapterIndex}
              onChange={(e) => setSelectedChapterIndex(parseInt(e.target.value))}
              className="px-3 py-1 border rounded-md text-sm min-w-0 flex-1"
            >
              {chapters.map((chapter, index) => (
                <option key={chapter.id} value={index}>
                  Chapter {chapter.order}: {chapter.title.substring(0, 40)}{chapter.title.length > 40 ? '...' : ''}
                </option>
              ))}
            </select>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSelectedChapterIndex(Math.min(chapters.length - 1, selectedChapterIndex + 1))}
            disabled={selectedChapterIndex === chapters.length - 1}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Chapter Content */}
        {selectedChapter && (
          <div className="space-y-4">
            {/* Chapter Header */}
            <div className="border-b pb-3">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Chapter {selectedChapter.order}: {selectedChapter.title}</h3>
                <Badge variant="outline">
                  {selectedChapterIndex + 1} of {chapters.length}
                </Badge>
              </div>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  {selectedChapter.wordCount.toLocaleString()} words
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {Math.ceil(selectedChapter.wordCount / 200)} min read
                </div>
                {selectedChapter.citations.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Quote className="h-4 w-4" />
                    {selectedChapter.citations.length} citations
                  </div>
                )}
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Completed
                </div>
              </div>
            </div>

            {/* Chapter Content */}
            <div className={`${isExpanded ? 'h-96' : 'h-64'} w-full rounded-md border p-4 overflow-y-auto`}>
              <div className="prose prose-sm max-w-none">
                {selectedChapter.content.split('\n').map((paragraph, index) => {
                  if (paragraph.trim() === '') return null;
                  
                  // Handle markdown headers
                  if (paragraph.startsWith('##')) {
                    return (
                      <h3 key={index} className="text-lg font-semibold mt-6 mb-3 text-gray-800">
                        {paragraph.replace(/^##\s*/, '')}
                      </h3>
                    );
                  }
                  
                  if (paragraph.startsWith('#')) {
                    return (
                      <h2 key={index} className="text-xl font-bold mt-8 mb-4 text-gray-900">
                        {paragraph.replace(/^#\s*/, '')}
                      </h2>
                    );
                  }
                  
                  // Regular paragraphs
                  return (
                    <p key={index} className="mb-4 text-gray-700 leading-relaxed">
                      {paragraph}
                    </p>
                  );
                })}
              </div>
            </div>

            {/* Chapter Summary */}
            {selectedChapter.summary && (
              <div className="bg-blue-50 p-3 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Chapter Summary</h4>
                <p className="text-sm text-blue-700">{selectedChapter.summary}</p>
              </div>
            )}

            {/* Citations */}
            {selectedChapter.citations.length > 0 && (
              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="text-sm font-medium text-gray-800 mb-2">Citations</h4>
                <div className="space-y-1">
                  {selectedChapter.citations.map((citation, index) => (
                    <p key={index} className="text-xs text-gray-600">
                      {citation}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Generation Status */}
        {isGenerating && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-md">
            <p className="text-sm text-yellow-800">
              📝 Currently generating Chapter {currentGeneratingIndex + 1}...
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
