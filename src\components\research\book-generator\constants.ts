import {
  BookOpen,
  FileText,
  List,
  Users,
  Target,
  Lightbulb,
  PenTool,
  Search,
  Quote,
  Archive,
  Bookmark,
  Globe,
  Brain,
  Heart,
  Zap,
  Star,
  Award,
  Coffee,
  Compass
} from "lucide-react";
import { BookSectionType } from "./types";

// Book genres and categories
export const BOOK_GENRES = [
  { id: 'fiction', name: 'Fiction', icon: '📚' },
  { id: 'non-fiction', name: 'Non-Fiction', icon: '📖' },
  { id: 'academic', name: 'Academic', icon: '🎓' },
  { id: 'business', name: 'Business', icon: '💼' },
  { id: 'self-help', name: 'Self-Help', icon: '🌟' },
  { id: 'biography', name: 'Biography', icon: '👤' },
  { id: 'history', name: 'History', icon: '📜' },
  { id: 'science', name: 'Science', icon: '🔬' },
  { id: 'technology', name: 'Technology', icon: '💻' },
  { id: 'health', name: 'Health & Wellness', icon: '🏥' },
  { id: 'education', name: 'Education', icon: '📚' },
  { id: 'philosophy', name: 'Philosophy', icon: '🤔' },
  { id: 'psychology', name: 'Psychology', icon: '🧠' },
  { id: 'politics', name: 'Politics', icon: '🏛️' },
  { id: 'economics', name: 'Economics', icon: '📈' },
  { id: 'art', name: 'Art & Culture', icon: '🎨' },
  { id: 'travel', name: 'Travel', icon: '✈️' },
  { id: 'cooking', name: 'Cooking', icon: '👨‍🍳' },
  { id: 'sports', name: 'Sports', icon: '⚽' },
  { id: 'religion', name: 'Religion & Spirituality', icon: '🙏' }
];

// Target audiences
export const TARGET_AUDIENCES = [
  { id: 'general', name: 'General Public' },
  { id: 'researchers', name: 'Academic Researchers' },
  { id: 'undergrad', name: 'Students (Undergraduate)' },
  { id: 'graduate', name: 'Students (Graduate)' },
  { id: 'professionals', name: 'Professionals' },
  { id: 'experts', name: 'Industry Experts' },
  { id: 'beginners', name: 'Beginners' },
  { id: 'intermediate', name: 'Intermediate Learners' },
  { id: 'advanced', name: 'Advanced Practitioners' },
  { id: 'young-adults', name: 'Young Adults' },
  { id: 'adults', name: 'Adults' },
  { id: 'seniors', name: 'Seniors' }
];

// Book tones
export const BOOK_TONES = [
  { id: 'academic', name: 'Academic', description: 'Formal, research-based, scholarly' },
  { id: 'professional', name: 'Professional', description: 'Business-oriented, authoritative' },
  { id: 'casual', name: 'Casual', description: 'Conversational, accessible, friendly' },
  { id: 'narrative', name: 'Narrative', description: 'Story-driven, engaging, personal' }
];

// Book length estimates
export const BOOK_LENGTHS = [
  { id: 'short', name: 'Short (50-100 pages)', wordCount: '15,000-30,000 words', chapters: '5-8 chapters' },
  { id: 'medium', name: 'Medium (100-300 pages)', wordCount: '30,000-90,000 words', chapters: '8-15 chapters' },
  { id: 'long', name: 'Long (300-500 pages)', wordCount: '90,000-150,000 words', chapters: '15-25 chapters' },
  { id: 'very-long', name: 'Very Long (500+ pages)', wordCount: '150,000+ words', chapters: '25+ chapters' }
];

// Book section types (including chapters and special sections)
export const BOOK_SECTION_TYPES: BookSectionType[] = [
  // Front matter
  { 
    id: 'preface', 
    name: 'Preface', 
    icon: Quote, 
    color: 'bg-purple-500', 
    description: 'Author\'s introduction to the book',
    order: 1,
    required: false,
    isChapter: false
  },
  { 
    id: 'foreword', 
    name: 'Foreword', 
    icon: Users, 
    color: 'bg-indigo-500', 
    description: 'Introduction by someone other than the author',
    order: 2,
    required: false,
    isChapter: false
  },
  { 
    id: 'introduction', 
    name: 'Introduction', 
    icon: Compass, 
    color: 'bg-blue-500', 
    description: 'Overview and context for the book',
    order: 3,
    required: true,
    isChapter: false
  },
  
  // Main chapters (dynamic - will be generated based on user input)
  { 
    id: 'chapter', 
    name: 'Chapter', 
    icon: BookOpen, 
    color: 'bg-green-500', 
    description: 'Main content chapters',
    order: 10,
    required: true,
    isChapter: true
  },
  
  // Back matter
  { 
    id: 'conclusion', 
    name: 'Conclusion', 
    icon: Target, 
    color: 'bg-orange-500', 
    description: 'Final thoughts and summary',
    order: 90,
    required: true,
    isChapter: false
  },
  { 
    id: 'appendix', 
    name: 'Appendix', 
    icon: Archive, 
    color: 'bg-gray-500', 
    description: 'Additional supporting material',
    order: 91,
    required: false,
    isChapter: false
  },
  { 
    id: 'glossary', 
    name: 'Glossary', 
    icon: Search, 
    color: 'bg-teal-500', 
    description: 'Definitions of key terms',
    order: 92,
    required: false,
    isChapter: false
  },
  { 
    id: 'bibliography', 
    name: 'Bibliography', 
    icon: FileText, 
    color: 'bg-red-500', 
    description: 'Complete list of sources and references',
    order: 93,
    required: false,
    isChapter: false
  },
  { 
    id: 'index', 
    name: 'Index', 
    icon: List, 
    color: 'bg-yellow-500', 
    description: 'Alphabetical listing of topics and page numbers',
    order: 94,
    required: false,
    isChapter: false
  }
];

// Enhanced AI Models with task-specific optimization
export const AI_MODELS = [
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash (Default)",
    provider: "Google",
    capabilities: ["Very fast generation", "Long content", "Context retention", "High token limit"],
    maxTokens: 8192,
    bestFor: ["content-generation", "long-chapters", "outline-generation"],
    description: "Excellent for generating long-form content quickly with high quality"
  },
  {
    id: "deepseek/deepseek-r1-0528",
    name: "DeepSeek R1",
    provider: "DeepSeek",
    capabilities: ["Advanced reasoning", "Long-form content", "Technical writing", "Complex analysis"],
    maxTokens: 8192,
    bestFor: ["content-generation", "technical-content", "analysis"],
    description: "Advanced reasoning model excellent for detailed technical and analytical content"
  },
  {
    id: "moonshotai/kimi-k2:free",
    name: "Kimi K2 (Free)",
    provider: "Moonshot AI",
    capabilities: ["Long context", "Multilingual", "Fast generation", "Cost-effective"],
    maxTokens: 16384,
    bestFor: ["content-generation", "long-chapters", "multilingual-content"],
    description: "Free high-capacity model with excellent long context handling"
  },
  {
    id: "moonshotai/kimi-k2",
    name: "Kimi K2",
    provider: "Moonshot AI",
    capabilities: ["Very long context", "Multilingual", "High quality", "Detailed content"],
    maxTokens: 32768,
    bestFor: ["content-generation", "very-long-chapters", "complex-outlines"],
    description: "Premium model with exceptional long context for very detailed book generation"
  },
  {
    id: "mistralai/devstral-medium",
    name: "Devstral Medium",
    provider: "Mistral AI",
    capabilities: ["Code generation", "Technical writing", "Structured content", "Fast processing"],
    maxTokens: 8192,
    bestFor: ["technical-content", "programming-books", "structured-writing"],
    description: "Specialized for technical and programming-related content generation"
  },
  {
    id: "tngtech/deepseek-r1t2-chimera:free",
    name: "DeepSeek R1T2 Chimera (Free)",
    provider: "TNG Tech",
    capabilities: ["Advanced reasoning", "Free access", "Quality content", "Efficient generation"],
    maxTokens: 8192,
    bestFor: ["content-generation", "analysis", "cost-effective-generation"],
    description: "Free advanced reasoning model for high-quality content generation"
  },
  {
    id: "minimax/minimax-m1",
    name: "MiniMax M1",
    provider: "MiniMax",
    capabilities: ["Creative writing", "Narrative content", "Engaging style", "Character development"],
    maxTokens: 8192,
    bestFor: ["creative-writing", "narrative-content", "fiction-books"],
    description: "Excellent for creative and narrative-driven content with engaging writing style"
  },
  {
    id: "deepseek/deepseek-chat-v3-0324",
    name: "DeepSeek Chat V3",
    provider: "DeepSeek",
    capabilities: ["Conversational style", "Accessible writing", "Clear explanations", "Educational content"],
    maxTokens: 8192,
    bestFor: ["educational-content", "accessible-writing", "tutorial-books"],
    description: "Great for educational and tutorial content with clear, accessible explanations"
  },
  {
    id: "anthropic/claude-3-haiku",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    capabilities: ["Fast generation", "Topic analysis", "Structured output", "Efficient processing"],
    maxTokens: 4096,
    bestFor: ["topic-analysis", "outline-generation", "quick-tasks"],
    description: "Fast and efficient for topic analysis and outline generation"
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    capabilities: ["Creative writing", "Structured content", "Research synthesis", "Versatile generation"],
    maxTokens: 4096,
    bestFor: ["content-generation", "creative-writing", "research-synthesis"],
    description: "Versatile model great for creative and engaging content generation"
  }
];

// Task-specific model recommendations
export const TASK_SPECIFIC_MODELS = {
  "topic-analysis": "anthropic/claude-3-haiku",
  "outline-generation": "google/gemini-2.0-flash-001",
  "content-generation": "moonshotai/kimi-k2:free",
  "technical-content": "mistralai/devstral-medium",
  "creative-content": "minimax/minimax-m1",
  "long-chapters": "moonshotai/kimi-k2",
  "very-long-chapters": "moonshotai/kimi-k2",
  "summary-generation": "anthropic/claude-3-haiku",
  "enhancement": "deepseek/deepseek-r1-0528",
  "educational-content": "deepseek/deepseek-chat-v3-0324",
  "programming-books": "mistralai/devstral-medium",
  "fiction-books": "minimax/minimax-m1",
  "cost-effective": "tngtech/deepseek-r1t2-chimera:free"
};

// Model selection strategy based on book characteristics
export const getOptimalModel = (task: string, bookMetadata?: any, chapterLength?: number) => {
  // For very long chapters (>8000 words), prefer models with highest token limits
  if (task === "content-generation" && chapterLength && chapterLength > 8000) {
    return "moonshotai/kimi-k2";
  }

  // For long chapters (>5000 words), prefer high-capacity models
  if (task === "content-generation" && chapterLength && chapterLength > 5000) {
    return "moonshotai/kimi-k2:free";
  }

  // For technical/academic books, prefer specialized models
  if (bookMetadata?.genre === "Academic" || bookMetadata?.genre === "Technology") {
    if (task === "outline-generation") return "google/gemini-2.0-flash-001";
    if (task === "content-generation") return "mistralai/devstral-medium";
  }

  // For programming/code books, use specialized model
  if (bookMetadata?.genre === "Technology" && bookMetadata?.keywords?.some((k: string) =>
    ['programming', 'code', 'software', 'development', 'coding'].includes(k.toLowerCase()))) {
    if (task === "content-generation") return "mistralai/devstral-medium";
  }

  // For creative/fiction books, prefer creative models
  if (bookMetadata?.genre === "Fiction" || bookMetadata?.tone === "narrative") {
    if (task === "content-generation") return "minimax/minimax-m1";
  }

  // For educational content, use accessible model
  if (bookMetadata?.genre === "Education" || bookMetadata?.targetAudience?.includes("Students")) {
    if (task === "content-generation") return "deepseek/deepseek-chat-v3-0324";
  }

  // For very long books, prefer high-capacity models
  if (bookMetadata?.estimatedLength === "very-long" || bookMetadata?.chapterCount > 20) {
    if (task === "content-generation") return "moonshotai/kimi-k2";
    if (task === "outline-generation") return "google/gemini-2.0-flash-001";
  }

  // Default to task-specific recommendations
  return TASK_SPECIFIC_MODELS[task as keyof typeof TASK_SPECIFIC_MODELS] || "google/gemini-2.0-flash-001";
};

// Context management settings
export const CONTEXT_SETTINGS = {
  MAX_CONTEXT_TOKENS: 16000, // Increased for better context retention
  SUMMARY_MAX_WORDS: 600, // Increased for more detailed summaries
  MAX_PREVIOUS_CHAPTERS: 8, // Keep context from more chapters for better continuity
  CHAPTER_GENERATION_TOKENS: 32768, // Significantly increased for very long chapters (up to ~24k words)
  SUMMARY_GENERATION_TOKENS: 4096, // Increased for comprehensive summaries
  VERY_LONG_CHAPTER_TOKENS: 65536, // For extremely long chapters using high-capacity models
  ENHANCEMENT_TOKENS: 8192 // For AI enhancement tasks
};

// Word count estimates per section type
export const WORD_COUNT_ESTIMATES = {
  preface: { min: 500, max: 2000 },
  foreword: { min: 300, max: 1500 },
  introduction: { min: 1500, max: 4000 }, // Increased for more comprehensive introductions
  chapter: { min: 6000, max: 20000 }, // Significantly increased for very detailed chapters
  conclusion: { min: 1500, max: 4000 },
  appendix: { min: 1000, max: 3000 },
  glossary: { min: 500, max: 2000 },
  bibliography: { min: 200, max: 1000 },
  index: { min: 200, max: 500 }
};

// Chapter numbering styles
export const CHAPTER_NUMBERING_STYLES = [
  { id: 'numeric', name: 'Numeric (1, 2, 3...)', format: (n: number) => n.toString() },
  { id: 'roman', name: 'Roman (I, II, III...)', format: (n: number) => toRoman(n) },
  { id: 'none', name: 'No numbering', format: () => '' }
];

// Google Gemini Configuration
export const GEMINI_CONFIG = {
  MODEL: 'gemini-2.5-pro',
  RATE_LIMIT: {
    DELAY_BETWEEN_REQUESTS: 10000, // 10 seconds
    MAX_RETRIES: 3,
    BACKOFF_MULTIPLIER: 2,
    MAX_BACKOFF_DELAY: 60000 // 1 minute
  },
  CONTEXT: {
    MAX_SUMMARY_WORDS: 400,
    MAX_PREVIOUS_CHAPTERS: 5,
    MAX_CONTEXT_TOKENS: 32000
  },
  GENERATION: {
    OUTLINE_MAX_TOKENS: 8192,
    CHAPTER_MAX_TOKENS: 32768,
    SUMMARY_MAX_TOKENS: 2048,
    TEMPERATURE: 0.7
  }
};

// Gemini-specific prompts configuration
export const GEMINI_PROMPTS = {
  OUTLINE_SYSTEM: `You are an expert book author and editor with decades of experience in creating comprehensive, well-structured books across various genres. Your task is to create detailed book outlines that serve as blueprints for exceptional books.`,

  CHAPTER_SYSTEM: `You are a professional book author writing a comprehensive, detailed chapter. Your writing should be engaging, informative, and maintain narrative coherence with previous chapters. Focus on creating substantial, book-quality content that provides real value to readers.`,

  SUMMARY_SYSTEM: `You are an expert editor creating concise chapter summaries for maintaining narrative coherence in book writing. Focus on key plot points, character development, main arguments, and essential information needed for subsequent chapters.`
};

// Helper function for Roman numerals
function toRoman(num: number): string {
  const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
  const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
  let result = '';

  for (let i = 0; i < values.length; i++) {
    while (num >= values[i]) {
      result += symbols[i];
      num -= values[i];
    }
  }

  return result;
}
