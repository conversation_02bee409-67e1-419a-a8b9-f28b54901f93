# 🔧 Post-Deployment Configuration for verbira.com

## 📋 Configuration Checklist

### ✅ Completed
- [x] App deployed to Vercel
- [x] Environment variables configured
- [x] Custom domain added (verbira.com)

### 🔄 Next Steps (After Deployment)

## 1. Update Supabase Configuration

### Site URL Update
Current: `http://localhost:8081`
New: `https://verbira.com`

### Redirect URLs Update
Add: `https://verbira.com/auth/callback`
Keep: `http://localhost:8081/auth/callback` (for local development)

### CORS Settings Update
Add: `https://verbira.com`

## 2. Google OAuth Configuration

### Add Production Redirect URI
In Google Cloud Console → APIs & Services → Credentials:
- Edit your OAuth 2.0 Client ID
- Add: `https://verbira.com/auth/callback`
- Keep existing: `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`

## 3. DNS Configuration

### Vercel DNS Settings
When you add verbira.com as custom domain, Vercel will provide:
- A record or CNAME record to configure
- Update your domain registrar's DNS settings accordingly

### SSL Certificate
- Vercel automatically provisions SSL certificates
- Should be active within minutes of DNS propagation

## 4. Testing Protocol

### Basic Functionality Test
1. Visit `https://verbira.com`
2. Check homepage loads correctly
3. Test navigation between pages

### Authentication Test
1. Try user registration with email
2. Test Google OAuth login
3. Verify user session persistence

### Feature Test
1. AI Paper Generator
2. Research Search functionality
3. Article Reviewer
4. Book Generator
5. File upload capabilities

### API Test
1. Check `/api` endpoints respond
2. Verify AI model connections
3. Test Supabase database operations

## 5. Performance Monitoring

### Vercel Analytics
- Enable in Vercel dashboard
- Monitor page load times
- Track user interactions

### Error Monitoring
- Check Vercel function logs
- Monitor browser console errors
- Track failed API requests

## 6. Backup Plan

### If Issues Occur
1. Revert DNS to previous settings
2. Keep old app running until new one is stable
3. Document any issues for troubleshooting

### Rollback Procedure
1. Change DNS back to old app
2. Disable new Vercel deployment
3. Restore previous Supabase settings

## 📞 Support

After deployment, I can help with:
- Updating Supabase settings using tools
- Troubleshooting any configuration issues
- Testing all functionality
- Optimizing performance

**Ready to proceed with deployment?** Follow the DEPLOY_NOW.md guide!
