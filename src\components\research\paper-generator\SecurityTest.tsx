import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { paperGenerationService } from './paper-generation.service';
import { useAuth } from '@/contexts/AuthContext';
import { Shield, CheckCircle, XCircle, AlertTriangle } from "lucide-react";

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

export const SecurityTest: React.FC = () => {
  const { user } = useAuth();
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const runSecurityTests = async () => {
    if (!user) {
      toast.error('Please log in to run security tests');
      return;
    }

    setTesting(true);
    setResults([]);
    const testResults: TestResult[] = [];

    try {
      // Test 1: Create a paper generation
      testResults.push({
        test: 'Creating paper generation',
        status: 'pass',
        message: 'Starting test...'
      });
      setResults([...testResults]);

      const paperGeneration = await paperGenerationService.createPaperGeneration({
        user_id: user.id,
        title: 'Security Test Paper',
        research_field: 'Computer Science',
        keywords: ['security', 'testing'],
        authors: ['Test Author'],
        ai_model: 'test-model',
        status: 'draft'
      });

      if (paperGeneration) {
        testResults[0] = {
          test: 'Creating paper generation',
          status: 'pass',
          message: `Successfully created paper generation: ${paperGeneration.id}`
        };
      } else {
        testResults[0] = {
          test: 'Creating paper generation',
          status: 'fail',
          message: 'Failed to create paper generation'
        };
      }
      setResults([...testResults]);

      if (!paperGeneration) {
        setTesting(false);
        return;
      }

      // Test 2: Validate ownership
      testResults.push({
        test: 'Validating paper ownership',
        status: 'pass',
        message: 'Testing ownership validation...'
      });
      setResults([...testResults]);

      const isOwner = await paperGenerationService.validatePaperOwnership(paperGeneration.id);
      testResults[1] = {
        test: 'Validating paper ownership',
        status: isOwner ? 'pass' : 'fail',
        message: isOwner ? 'Ownership validation successful' : 'Ownership validation failed'
      };
      setResults([...testResults]);

      // Test 3: Create a section
      testResults.push({
        test: 'Creating paper section',
        status: 'pass',
        message: 'Creating section...'
      });
      setResults([...testResults]);

      const section = await paperGenerationService.createPaperSection({
        paper_generation_id: paperGeneration.id,
        section_id: 'introduction',
        section_name: 'Introduction',
        prompt_text: 'Generate an introduction for a security testing paper.',
        ai_model: 'test-model',
        status: 'completed',
        generated_content: 'This is a test introduction.',
        generation_metadata: { test: true }
      });

      if (section) {
        testResults[2] = {
          test: 'Creating paper section',
          status: 'pass',
          message: `Successfully created section: ${section.id}`
        };
      } else {
        testResults[2] = {
          test: 'Creating paper section',
          status: 'fail',
          message: 'Failed to create paper section'
        };
      }
      setResults([...testResults]);

      // Test 4: Get user's papers
      testResults.push({
        test: 'Retrieving user papers',
        status: 'pass',
        message: 'Fetching user papers...'
      });
      setResults([...testResults]);

      const userPapers = await paperGenerationService.getUserPaperGenerations(10);
      const foundTestPaper = userPapers.find(p => p.id === paperGeneration.id);

      testResults[3] = {
        test: 'Retrieving user papers',
        status: foundTestPaper ? 'pass' : 'fail',
        message: foundTestPaper 
          ? `Found test paper in user's papers (${userPapers.length} total papers)`
          : 'Test paper not found in user\'s papers'
      };
      setResults([...testResults]);

      // Test 5: Get paper count
      testResults.push({
        test: 'Getting paper count',
        status: 'pass',
        message: 'Getting paper count...'
      });
      setResults([...testResults]);

      const paperCount = await paperGenerationService.getUserPaperCount();
      testResults[4] = {
        test: 'Getting paper count',
        status: paperCount > 0 ? 'pass' : 'warning',
        message: `User has ${paperCount} papers`
      };
      setResults([...testResults]);

      // Test 6: Clean up - Delete test paper
      testResults.push({
        test: 'Cleaning up test data',
        status: 'pass',
        message: 'Deleting test paper...'
      });
      setResults([...testResults]);

      const deleted = await paperGenerationService.deletePaperGeneration(paperGeneration.id);
      testResults[5] = {
        test: 'Cleaning up test data',
        status: deleted ? 'pass' : 'warning',
        message: deleted ? 'Test paper deleted successfully' : 'Failed to delete test paper'
      };
      setResults([...testResults]);

      // Summary
      const passCount = testResults.filter(r => r.status === 'pass').length;
      const failCount = testResults.filter(r => r.status === 'fail').length;
      const warningCount = testResults.filter(r => r.status === 'warning').length;

      if (failCount === 0) {
        toast.success(`Security tests completed: ${passCount} passed, ${warningCount} warnings`);
      } else {
        toast.error(`Security tests failed: ${failCount} failures, ${passCount} passed`);
      }

    } catch (error) {
      console.error('Security test error:', error);
      testResults.push({
        test: 'Security test execution',
        status: 'fail',
        message: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      setResults([...testResults]);
      toast.error('Security tests encountered an error');
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'bg-green-100 text-green-800';
      case 'fail':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Shield className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">Authentication Required</h3>
          <p className="text-gray-500 text-center">
            Please log in to run security tests for the paper generator.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-blue-600" />
          Paper Generator Security Tests
        </CardTitle>
        <p className="text-gray-600">
          Run comprehensive security tests to verify database isolation and user access controls.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-600">
              Logged in as: <span className="font-medium">{user.email}</span>
            </p>
            <p className="text-sm text-gray-600">
              User ID: <span className="font-mono text-xs">{user.id}</span>
            </p>
          </div>
          <Button 
            onClick={runSecurityTests} 
            disabled={testing}
            className="flex items-center gap-2"
          >
            <Shield className="h-4 w-4" />
            {testing ? 'Running Tests...' : 'Run Security Tests'}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold text-lg">Test Results</h3>
            {results.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <span className="font-medium">{result.test}</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600">{result.message}</span>
                  <Badge className={getStatusColor(result.status)}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">What These Tests Verify:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• User can only create papers for themselves</li>
            <li>• Paper ownership validation works correctly</li>
            <li>• Sections can only be created for owned papers</li>
            <li>• Users can only see their own papers</li>
            <li>• Paper count tracking functions properly</li>
            <li>• Data cleanup works as expected</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
