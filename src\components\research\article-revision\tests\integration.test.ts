/**
 * Integration Tests for Article Revision System
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { useRevisionWorkflowStore } from '../stores/revision-workflow.store';
import { DocumentParserService } from '../services/document-parser.service';
import { ParsedArticle, ParsedReviewerComments } from '../types';

// Mock data for testing
const mockArticleFile = new File(['Mock article content'], 'test-article.pdf', {
  type: 'application/pdf'
});

const mockCommentsFile = new File(['Mock reviewer comments'], 'reviewer-1-comments.pdf', {
  type: 'application/pdf'
});

const mockParsedArticle: ParsedArticle = {
  title: 'Test Article Title',
  abstract: 'This is a test abstract for the article.',
  keywords: ['test', 'article', 'revision'],
  introduction: 'This is the introduction section.',
  methodology: 'This is the methodology section.',
  results: 'This is the results section.',
  discussion: 'This is the discussion section.',
  conclusion: 'This is the conclusion section.',
  references: 'References section.',
  fullText: 'Complete article text...',
  sectionMapping: {
    title: 'Test Article Title',
    abstract: 'This is a test abstract for the article.',
    keywords: 'test, article, revision',
    introduction: 'This is the introduction section.',
    methodology: 'This is the methodology section.',
    results: 'This is the results section.',
    discussion: 'This is the discussion section.',
    conclusion: 'This is the conclusion section.',
    references: 'References section.',
    overall: 'Complete article text...'
  },
  fileName: 'test-article.pdf',
  fileType: 'application/pdf',
  fileSize: 1024,
  wordCount: 500
};

const mockParsedComments: ParsedReviewerComments = {
  fileName: 'reviewer-1-comments.pdf',
  fileType: 'application/pdf',
  reviewerNumber: 1,
  comments: [
    {
      id: 'comment-1',
      reviewerNumber: 1,
      commentType: 'specific',
      severity: 'moderate',
      category: 'content',
      originalText: 'The introduction needs more background information.',
      comment: 'The introduction needs more background information.',
      targetSection: 'introduction',
      canAutoFix: true,
      requiresManualIntervention: false
    },
    {
      id: 'comment-2',
      reviewerNumber: 1,
      commentType: 'general',
      severity: 'major',
      category: 'structure',
      originalText: 'The paper structure could be improved.',
      comment: 'The paper structure could be improved.',
      canAutoFix: false,
      requiresManualIntervention: true
    }
  ],
  generalComments: ['Overall good work but needs improvements.'],
  overallAssessment: 'Accept with major revisions'
};

describe('Article Revision System Integration', () => {
  beforeEach(() => {
    // Reset the store before each test
    const store = useRevisionWorkflowStore.getState();
    store.resetWorkflow();
  });

  describe('Workflow Store', () => {
    it('should initialize with default state', () => {
      const store = useRevisionWorkflowStore.getState();
      
      expect(store.originalArticle).toBeNull();
      expect(store.reviewerComments).toHaveLength(0);
      expect(store.progress.phase).toBe('parsing');
      expect(store.progress.percentage).toBe(0);
      expect(store.canStartRevision()).toBe(false);
    });

    it('should update state when article is uploaded', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.setOriginalArticle(mockParsedArticle);
      
      expect(store.originalArticle).toEqual(mockParsedArticle);
      expect(store.progress.completedSteps).toBe(1);
      expect(store.progress.percentage).toBeGreaterThan(0);
    });

    it('should update state when reviewer comments are added', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.addReviewerComments(mockParsedComments);
      
      expect(store.reviewerComments).toHaveLength(1);
      expect(store.reviewerComments[0]).toEqual(mockParsedComments);
    });

    it('should allow starting revision when both article and comments are present', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.setOriginalArticle(mockParsedArticle);
      store.addReviewerComments(mockParsedComments);
      
      expect(store.canStartRevision()).toBe(true);
    });

    it('should manage AI assistant states correctly', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.initializeAIAssistants();
      
      const assistants = Object.values(store.aiAssistants);
      expect(assistants.length).toBeGreaterThan(0);
      
      // Test updating assistant status
      const assistantId = assistants[0].id;
      store.setAIAssistantStatus(assistantId, 'processing');
      
      expect(store.aiAssistants[assistantId].status).toBe('processing');
      expect(store.getActiveAssistants()).toHaveLength(1);
    });
  });

  describe('Document Parser Service', () => {
    it('should validate file constraints', () => {
      // Test file size validation
      const largeFile = new File(['x'.repeat(100 * 1024 * 1024)], 'large.pdf', {
        type: 'application/pdf'
      });
      
      expect(() => {
        DocumentParserService['validateFile'](largeFile, 'article');
      }).toThrow('File size exceeds the maximum limit');
    });

    it('should validate file formats', () => {
      const invalidFile = new File(['content'], 'test.xyz', {
        type: 'application/xyz'
      });
      
      expect(() => {
        DocumentParserService['validateFile'](invalidFile, 'article');
      }).toThrow('File format not supported');
    });

    it('should extract sections from text', () => {
      const sampleText = `
        Test Article Title
        
        Abstract
        This is the abstract section.
        
        Keywords: test, article, revision
        
        Introduction
        This is the introduction section.
        
        Methodology
        This is the methodology section.
        
        Results
        This is the results section.
        
        Discussion
        This is the discussion section.
        
        Conclusion
        This is the conclusion section.
        
        References
        Reference 1
        Reference 2
      `;
      
      const sections = DocumentParserService['extractSections'](sampleText);
      
      expect(sections.abstract).toContain('abstract section');
      expect(sections.introduction).toContain('introduction section');
      expect(sections.methodology).toContain('methodology section');
    });

    it('should extract keywords correctly', () => {
      const keywordsText = 'Keywords: machine learning, artificial intelligence, deep learning';
      const keywords = DocumentParserService['extractKeywords'](keywordsText);
      
      expect(keywords).toContain('machine learning');
      expect(keywords).toContain('artificial intelligence');
      expect(keywords).toContain('deep learning');
    });

    it('should categorize comments correctly', () => {
      const contentComment = 'The methodology section lacks sufficient detail about the data collection process.';
      const languageComment = 'There are several grammatical errors in the introduction.';
      
      expect(DocumentParserService['determineCategory'](contentComment)).toBe('content');
      expect(DocumentParserService['determineCategory'](languageComment)).toBe('language');
    });

    it('should assess comment severity correctly', () => {
      const criticalComment = 'This is a critical issue that must be addressed immediately.';
      const minorComment = 'This is a minor cosmetic issue that might be improved.';
      
      expect(DocumentParserService['determineSeverity'](criticalComment)).toBe('critical');
      expect(DocumentParserService['determineSeverity'](minorComment)).toBe('minor');
    });
  });

  describe('Workflow Integration', () => {
    it('should complete full workflow simulation', async () => {
      const store = useRevisionWorkflowStore.getState();
      
      // Step 1: Initialize workflow
      store.initializeWorkflow();
      expect(store.progress.phase).toBe('parsing');
      
      // Step 2: Upload article
      store.setOriginalArticle(mockParsedArticle);
      expect(store.originalArticle).toBeTruthy();
      
      // Step 3: Upload reviewer comments
      store.addReviewerComments(mockParsedComments);
      expect(store.reviewerComments).toHaveLength(1);
      
      // Step 4: Verify ready to start
      expect(store.canStartRevision()).toBe(true);
      
      // Step 5: Simulate processing phases
      store.setCurrentPhase('comment-analysis');
      expect(store.progress.phase).toBe('comment-analysis');
      
      store.setCurrentPhase('section-revision');
      expect(store.progress.phase).toBe('section-revision');
      
      store.setCurrentPhase('integration');
      expect(store.progress.phase).toBe('integration');
      
      store.setCurrentPhase('response-generation');
      expect(store.progress.phase).toBe('response-generation');
      
      store.setCurrentPhase('completed');
      expect(store.progress.phase).toBe('completed');
    });

    it('should handle errors gracefully', () => {
      const store = useRevisionWorkflowStore.getState();
      
      const errorMessage = 'Test error message';
      store.addError(errorMessage);
      
      expect(store.progress.errors).toContain(errorMessage);
      
      // Test error clearing
      store.clearErrors();
      expect(store.progress.errors).toHaveLength(0);
    });

    it('should calculate overall progress correctly', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.initializeAIAssistants();
      
      // Set some assistants to different progress levels
      const assistantIds = Object.keys(store.aiAssistants);
      store.setAIAssistantProgress(assistantIds[0], 100);
      store.setAIAssistantProgress(assistantIds[1], 50);
      store.setAIAssistantProgress(assistantIds[2], 0);
      
      const overallProgress = store.getOverallProgress();
      expect(overallProgress).toBeGreaterThan(0);
      expect(overallProgress).toBeLessThan(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing required data', () => {
      const store = useRevisionWorkflowStore.getState();
      
      // Try to start revision without article
      expect(store.canStartRevision()).toBe(false);
      
      // Add article but no comments
      store.setOriginalArticle(mockParsedArticle);
      expect(store.canStartRevision()).toBe(false);
      
      // Add comments - now should be ready
      store.addReviewerComments(mockParsedComments);
      expect(store.canStartRevision()).toBe(true);
    });

    it('should handle AI assistant failures', () => {
      const store = useRevisionWorkflowStore.getState();
      
      store.initializeAIAssistants();
      const assistantId = Object.keys(store.aiAssistants)[0];
      
      store.setAIAssistantError(assistantId, 'Test error');
      
      expect(store.aiAssistants[assistantId].status).toBe('error');
      expect(store.aiAssistants[assistantId].error).toBe('Test error');
      expect(store.getFailedAssistants()).toHaveLength(1);
    });
  });
});

// Export for potential use in other test files
export {
  mockParsedArticle,
  mockParsedComments,
  mockArticleFile,
  mockCommentsFile
};
