import { 
  AIModelOption, 
  JournalCategory, 
  RankingCriteria, 
  InputType, 
  AnalysisDepth, 
  ExportFormat,
  JournalField 
} from './types';

// AI Models configuration
export const AI_MODELS: { [key: string]: AIModelOption } = {
  'gemini-2.5-pro': {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    provider: 'gemini',
    model: 'gemini-2.5-pro',
    description: 'Advanced analysis with comprehensive journal matching',
    maxTokens: 8192,
    costPerToken: 0.001,
    enabled: true
  },
  'gemini-2.5-flash': {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    provider: 'gemini',
    model: 'gemini-2.5-flash',
    description: 'Fast analysis with good accuracy',
    maxTokens: 8192,
    costPerToken: 0.0005,
    enabled: true
  },
  'claude-3.5-sonnet': {
    id: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'openrouter',
    model: 'anthropic/claude-3.5-sonnet',
    description: 'Excellent for academic analysis and journal recommendations',
    maxTokens: 8192,
    costPerToken: 0.003,
    enabled: true
  },
  'gpt-4o': {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'openrouter',
    model: 'openai/gpt-4o',
    description: 'Strong analytical capabilities for journal matching',
    maxTokens: 8192,
    costPerToken: 0.005,
    enabled: true
  }
};

// Journal categories
export const JOURNAL_CATEGORIES: JournalCategory[] = [
  'High Impact',
  'Specialized',
  'Open Access',
  'Fast Track',
  'Emerging'
];

// Research field categories
export const FIELD_CATEGORIES: JournalField[] = [
  'STEM',
  'Medicine',
  'Social Sciences',
  'Humanities',
  'Business',
  'Arts',
  'Other'
];

// Input types
export const INPUT_TYPES: { [key in InputType]: { label: string; description: string } } = {
  title: {
    label: 'Title Only',
    description: 'Analyze based on article title'
  },
  abstract: {
    label: 'Abstract',
    description: 'Analyze based on abstract text'
  },
  full: {
    label: 'Full Article',
    description: 'Analyze complete article content'
  }
};

// Analysis depths
export const ANALYSIS_DEPTHS: { [key in AnalysisDepth]: { label: string; description: string } } = {
  basic: {
    label: 'Basic Analysis',
    description: 'Quick journal matching based on keywords and field'
  },
  comprehensive: {
    label: 'Comprehensive Analysis',
    description: 'Detailed analysis with impact metrics and timeline estimates'
  },
  detailed: {
    label: 'Detailed Analysis',
    description: 'In-depth analysis with competitive landscape and submission strategy'
  }
};

// Default ranking criteria
export const DEFAULT_RANKING_CRITERIA: RankingCriteria = {
  relevanceWeight: 0.3,
  impactWeight: 0.25,
  speedWeight: 0.2,
  costWeight: 0.15,
  accessibilityWeight: 0.1
};

// Export formats
export const EXPORT_FORMATS: { [key in ExportFormat]: { label: string; description: string; icon: string } } = {
  pdf: {
    label: 'PDF Report',
    description: 'Comprehensive journal recommendation report',
    icon: 'FileText'
  },
  docx: {
    label: 'Word Document',
    description: 'Editable journal analysis document',
    icon: 'FileEdit'
  },
  json: {
    label: 'JSON Data',
    description: 'Raw data for further analysis',
    icon: 'Database'
  },
  csv: {
    label: 'CSV Spreadsheet',
    description: 'Journal recommendations in spreadsheet format',
    icon: 'Table'
  }
};

// Default search options
export const DEFAULT_SEARCH_OPTIONS = {
  model: 'gemini-2.5-flash',
  analysisDepth: 'comprehensive' as AnalysisDepth,
  maxRecommendations: 10,
  includeMetrics: true,
  includeTimelines: true,
  includeCosts: true
};

// Journal metrics configuration
export const JOURNAL_METRICS = {
  impactFactorRanges: {
    excellent: { min: 5.0, label: 'Excellent (5.0+)' },
    good: { min: 2.0, max: 4.99, label: 'Good (2.0-4.99)' },
    moderate: { min: 1.0, max: 1.99, label: 'Moderate (1.0-1.99)' },
    emerging: { min: 0, max: 0.99, label: 'Emerging (0-0.99)' }
  },
  acceptanceRateRanges: {
    competitive: { max: 20, label: 'Highly Competitive (<20%)' },
    selective: { min: 20, max: 40, label: 'Selective (20-40%)' },
    moderate: { min: 40, max: 60, label: 'Moderate (40-60%)' },
    accessible: { min: 60, label: 'Accessible (60%+)' }
  },
  reviewTimeRanges: {
    fast: { max: 60, label: 'Fast (<2 months)' },
    standard: { min: 60, max: 120, label: 'Standard (2-4 months)' },
    slow: { min: 120, label: 'Slow (4+ months)' }
  }
};

// UI Constants
export const UI_CONSTANTS = {
  animationDuration: 300,
  debounceDelay: 500,
  maxContentLength: 100000,
  maxTitleLength: 500,
  maxAbstractLength: 5000,
  defaultTimeout: 45000,
  progressUpdateInterval: 1000
};

// Color scheme for journal visualization
export const JOURNAL_COLORS = {
  primary: '#2563EB',
  secondary: '#1E40AF',
  success: '#059669',
  warning: '#D97706',
  danger: '#DC2626',
  muted: '#6B7280',
  background: '#F8FAFC',
  cardBackground: '#FFFFFF',
  accent: '#7C3AED'
};

// Journal ranking criteria descriptions
export const RANKING_CRITERIA = {
  relevance: {
    label: 'Topic Relevance',
    description: 'How well the journal scope matches your research',
    weight: 0.3,
    color: '#2563EB'
  },
  impact: {
    label: 'Impact Factor',
    description: 'Journal prestige and citation metrics',
    weight: 0.25,
    color: '#059669'
  },
  speed: {
    label: 'Publication Speed',
    description: 'Review and publication timeline',
    weight: 0.2,
    color: '#D97706'
  },
  cost: {
    label: 'Publication Cost',
    description: 'Article processing charges and fees',
    weight: 0.15,
    color: '#DC2626'
  },
  accessibility: {
    label: 'Open Access',
    description: 'Accessibility and reach of publication',
    weight: 0.1,
    color: '#7C3AED'
  }
};

// Prompts for AI analysis
export const ANALYSIS_PROMPTS = {
  basic: `Analyze this research content and provide basic journal recommendations. Focus on:
- Research field identification
- Key topics and themes
- 5-7 suitable journal suggestions
- Brief match explanations`,
  
  comprehensive: `Perform comprehensive analysis of this research content. Provide:
- Detailed research domain classification
- Methodology and approach analysis
- Key topics, themes, and keywords
- 8-10 journal recommendations with detailed metrics
- Impact factor, acceptance rates, and timelines
- Match scores and reasoning`,
  
  detailed: `Conduct detailed academic analysis for journal selection. Include:
- Complete research classification and methodology assessment
- Competitive landscape analysis
- 10-12 journal recommendations with comprehensive metrics
- Submission strategy recommendations
- Timeline and cost analysis
- Alternative journal suggestions for different scenarios`
};
