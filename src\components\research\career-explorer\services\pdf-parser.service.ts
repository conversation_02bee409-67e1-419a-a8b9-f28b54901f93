import { ResumeData } from '../types';
import { careerAIService } from './career-ai.service';

class PDFParserService {
  /**
   * Parse uploaded file and extract text content
   */
  async parseFile(file: File): Promise<ResumeData> {
    try {
      let rawText = '';
      
      if (file.type === 'application/pdf') {
        rawText = await this.parsePDF(file);
      } else if (file.type === 'text/plain') {
        rawText = await this.parseTextFile(file);
      } else if (file.type.includes('word') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
        rawText = await this.parseWordDocument(file);
      } else {
        throw new Error('Unsupported file type. Please upload a PDF, Word document, or text file.');
      }

      if (!rawText || rawText.trim().length === 0) {
        throw new Error('No text content found in the uploaded file.');
      }

      // Extract structured information from the raw text
      const extractedInfo = careerAIService.parseResumeText(rawText);

      return {
        rawText: this.normalizeText(rawText),
        extractedInfo,
        fileName: file.name,
        uploadedAt: new Date()
      };
    } catch (error) {
      console.error('Error parsing file:', error);
      throw new Error(`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse PDF file using PDF.js or similar library
   */
  private async parsePDF(file: File): Promise<string> {
    try {
      // For now, we'll use a simple approach that works in the browser
      // In a production environment, you might want to use pdf-parse or PDF.js
      
      // Convert file to array buffer
      const arrayBuffer = await file.arrayBuffer();
      
      // For demonstration, we'll use a simple text extraction
      // In reality, you'd use a proper PDF parsing library
      const text = await this.extractTextFromPDF(arrayBuffer);
      
      return text;
    } catch (error) {
      throw new Error('Failed to parse PDF file. Please ensure the file is not corrupted.');
    }
  }

  /**
   * Extract text from PDF using a simple approach
   * Note: This is a simplified implementation. For production use, consider using pdf-parse or PDF.js
   */
  private async extractTextFromPDF(arrayBuffer: ArrayBuffer): Promise<string> {
    // This is a placeholder implementation
    // In a real application, you would use a proper PDF parsing library
    
    try {
      // Convert ArrayBuffer to Uint8Array
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // Simple text extraction (this is very basic and won't work for all PDFs)
      let text = '';
      for (let i = 0; i < uint8Array.length; i++) {
        const char = String.fromCharCode(uint8Array[i]);
        if (char.match(/[a-zA-Z0-9\s\.\,\!\?\-\(\)]/)) {
          text += char;
        }
      }
      
      // Clean up the extracted text
      text = text.replace(/\s+/g, ' ').trim();
      
      if (text.length < 50) {
        throw new Error('Unable to extract meaningful text from PDF. Please try a different file or convert to text format.');
      }
      
      return text;
    } catch (error) {
      throw new Error('PDF parsing failed. Please try uploading a text file or Word document instead.');
    }
  }

  /**
   * Parse plain text file
   */
  private async parseTextFile(file: File): Promise<string> {
    try {
      const text = await file.text();
      if (text.trim().length === 0) {
        throw new Error('The text file appears to be empty.');
      }
      return text;
    } catch (error) {
      throw new Error('Failed to read text file.');
    }
  }

  /**
   * Parse Word document
   * Note: This is a simplified implementation
   */
  private async parseWordDocument(file: File): Promise<string> {
    try {
      // For Word documents, we'll try to read as text
      // In a production environment, you might want to use mammoth.js or similar
      const text = await file.text();
      
      // Basic cleanup for Word document text
      const cleanText = text
        .replace(/[^\x20-\x7E\n\r\t]/g, ' ') // Remove non-printable characters
        .replace(/\s+/g, ' ')
        .trim();
      
      if (cleanText.length < 50) {
        throw new Error('Unable to extract meaningful text from Word document. Please try converting to PDF or text format.');
      }
      
      return cleanText;
    } catch (error) {
      throw new Error('Failed to parse Word document. Please try converting to PDF or text format.');
    }
  }

  /**
   * Normalize extracted text
   */
  private normalizeText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n+/g, '\n') // Replace multiple line breaks with single line break
      .trim();
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size exceeds 10MB limit. Please upload a smaller file.'
      };
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const allowedExtensions = ['.pdf', '.txt', '.doc', '.docx'];
    const hasValidType = allowedTypes.includes(file.type);
    const hasValidExtension = allowedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );

    if (!hasValidType && !hasValidExtension) {
      return {
        isValid: false,
        error: 'Unsupported file type. Please upload a PDF, Word document, or text file.'
      };
    }

    return { isValid: true };
  }

  /**
   * Get file type description
   */
  getFileTypeDescription(file: File): string {
    if (file.type === 'application/pdf' || file.name.endsWith('.pdf')) {
      return 'PDF Document';
    } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
      return 'Text File';
    } else if (file.type.includes('word') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
      return 'Word Document';
    } else {
      return 'Unknown File Type';
    }
  }
}

export const pdfParserService = new PDFParserService();
