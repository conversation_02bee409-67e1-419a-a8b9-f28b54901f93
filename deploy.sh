#!/bin/bash

# Deployment script for verbira.com
echo "🚀 Starting deployment to verbira.com..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "📦 Installing Vercel CLI..."
    npm install -g vercel
fi

# Build the project
echo "🔨 Building the project..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    # Deploy to Vercel
    echo "🚀 Deploying to Vercel..."
    vercel --prod
    
    echo "🎉 Deployment complete!"
    echo "📝 Next steps:"
    echo "1. Add verbira.com as custom domain in Vercel dashboard"
    echo "2. Update Supabase Site URL to https://verbira.com"
    echo "3. Update Google OAuth redirect URIs"
    echo "4. Test all functionality"
else
    echo "❌ Build failed! Please fix the errors and try again."
    exit 1
fi
