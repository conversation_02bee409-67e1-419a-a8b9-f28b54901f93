/**
 * Console Test for Google Search
 * Simple test you can run in browser console to check Google Search functionality
 */

import { GoogleGenAI } from '@google/genai';

// Simple console test function
export const testGoogleSearchNow = async () => {
  console.clear();
  console.log('🔍 Testing Google Search Integration...');
  console.log('=====================================\n');

  try {
    // Check API key
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      console.log('❌ VITE_GEMINI_API_KEY not found in environment variables');
      console.log('💡 Add your Gemini API key to .env file');
      return false;
    }

    console.log('✅ API key found');

    // Initialize Gemini
    const ai = new GoogleGenAI({ apiKey });

    // Test Google Search
    const tools = [{ googleSearch: {} }];
    const config = {
      thinkingConfig: { thinkingBudget: -1 },
      tools,
      responseMimeType: 'text/plain',
    };

    const model = 'gemini-2.5-pro';

    console.log('🔍 Testing Google Search with current information...');

    const contents = [
      {
        role: 'user',
        parts: [
          {
            text: `CRITICAL TEST: Use Google Search to find current information.

            Search for: "latest AI research 2024" OR "current technology news"

            REQUIREMENTS:
            1. MUST use Google Search tool - not training data
            2. Find REAL URLs from actual websites
            3. Include current dates from 2024
            4. If you successfully use Google Search, start with "GOOGLE SEARCH SUCCESS:"
            5. If you cannot use Google Search, start with "GOOGLE SEARCH FAILED:"

            Include at least 3 real URLs in your response.`,
          },
        ],
      },
    ];

    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let fullResponse = '';
    for await (const chunk of response) {
      if (chunk.text) {
        fullResponse += chunk.text;
      }
    }

    console.log('📄 Response received:');
    console.log('===================');
    console.log(fullResponse.substring(0, 500) + '...\n');

    // Analyze response
    const analysis = analyzeResponse(fullResponse);
    
    console.log('📊 Analysis Results:');
    console.log('===================');
    console.log(`Google Search Used: ${analysis.googleSearchUsed ? '✅ YES' : '❌ NO'}`);
    console.log(`Real URLs Found: ${analysis.realUrls.length}`);
    console.log(`Fake URLs Found: ${analysis.fakeUrls.length}`);
    console.log(`Current Year Mentioned: ${analysis.hasCurrentYear ? '✅ YES' : '❌ NO'}`);
    
    if (analysis.realUrls.length > 0) {
      console.log('\n🔗 Real URLs Found:');
      analysis.realUrls.forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
      });
    }

    if (analysis.fakeUrls.length > 0) {
      console.log('\n❌ Fake URLs Found:');
      analysis.fakeUrls.forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
      });
    }

    // Final verdict
    console.log('\n🎯 Final Verdict:');
    console.log('================');
    
    if (analysis.googleSearchUsed && analysis.realUrls.length > 0 && analysis.hasCurrentYear) {
      console.log('🎉 SUCCESS: Google Search is working correctly!');
      console.log('   ✅ Real search results found');
      console.log('   ✅ Current information included');
      console.log('   ✅ Authentic URLs provided');
      return true;
    } else if (analysis.realUrls.length > 0) {
      console.log('⚠️ PARTIAL: Google Search may be working but has issues');
      console.log(`   ${analysis.googleSearchUsed ? '✅' : '❌'} Google Search indicators`);
      console.log(`   ${analysis.realUrls.length > 0 ? '✅' : '❌'} Real URLs found`);
      console.log(`   ${analysis.hasCurrentYear ? '✅' : '❌'} Current year information`);
      return false;
    } else {
      console.log('❌ FAILED: Google Search is NOT working');
      console.log('   ❌ No real URLs found');
      console.log('   ❌ Likely using training data only');
      console.log('   💡 Check API configuration and Google Search tool access');
      return false;
    }

  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    console.log('\n💡 Troubleshooting:');
    console.log('   1. Check VITE_GEMINI_API_KEY in .env file');
    console.log('   2. Verify API key has Google Search access');
    console.log('   3. Check network connection');
    return false;
  }
};

// Analyze response for Google Search indicators
function analyzeResponse(response: string) {
  const urlRegex = /https?:\/\/[^\s\)\]\,\;\"\'\`]+/g;
  const allUrls = response.match(urlRegex) || [];
  
  const fakePatterns = [
    'example.com',
    'placeholder.com',
    'fake-url',
    'dummy-link',
    'test.com',
    'your-domain.com',
    'sample-site.com'
  ];

  const realUrls: string[] = [];
  const fakeUrls: string[] = [];

  allUrls.forEach(url => {
    const isFake = fakePatterns.some(pattern => url.includes(pattern));
    if (isFake) {
      fakeUrls.push(url);
    } else {
      try {
        new URL(url); // Validate URL format
        realUrls.push(url);
      } catch {
        fakeUrls.push(url);
      }
    }
  });

  const googleSearchUsed = 
    response.includes('GOOGLE SEARCH SUCCESS:') ||
    (realUrls.length > 0 && !response.includes('GOOGLE SEARCH FAILED:'));

  const currentYear = new Date().getFullYear();
  const hasCurrentYear = response.includes(currentYear.toString());

  return {
    googleSearchUsed,
    realUrls,
    fakeUrls,
    hasCurrentYear,
    totalUrls: allUrls.length,
    responseLength: response.length
  };
}

// Make available globally
if (typeof window !== 'undefined') {
  (window as any).testGoogleSearchNow = testGoogleSearchNow;
  console.log('🔧 Google Search test function available globally:');
  console.log('   Run: testGoogleSearchNow()');
}

// Quick test for immediate use
export const quickGoogleSearchCheck = async () => {
  console.log('⚡ Quick Google Search Check...');
  
  try {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      console.log('❌ No API key found');
      return false;
    }

    const ai = new GoogleGenAI({ apiKey });
    const tools = [{ googleSearch: {} }];
    const config = { tools, responseMimeType: 'text/plain' };

    const response = await ai.models.generateContentStream({
      model: 'gemini-2.5-pro',
      config,
      contents: [{
        role: 'user',
        parts: [{ text: 'Use Google Search to find one current news article. Include the URL.' }]
      }]
    });

    let result = '';
    for await (const chunk of response) {
      if (chunk.text) result += chunk.text;
    }

    const hasUrl = /https?:\/\/[^\s]+/.test(result);
    const hasCurrentInfo = result.includes('2024') || result.includes('recent');

    console.log(hasUrl && hasCurrentInfo ? '✅ Google Search working' : '❌ Google Search not working');
    return hasUrl && hasCurrentInfo;

  } catch (error) {
    console.log('❌ Quick check failed:', error.message);
    return false;
  }
};

// Export for use in components
export { testGoogleSearchNow as default };
