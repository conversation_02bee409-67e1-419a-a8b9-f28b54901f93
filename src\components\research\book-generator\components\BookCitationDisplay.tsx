import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Quote, 
  BookOpen, 
  FileText, 
  ExternalLink, 
  Copy,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { Citation, GeneratedChapter } from '../types';
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface BookCitationDisplayProps {
  allCitations: Citation[];
  chapterCitations: Record<string, string[]>;
  generatedChapters: GeneratedChapter[];
  generatedSections: GeneratedChapter[];
}

export const BookCitationDisplay: React.FC<BookCitationDisplayProps> = ({
  allCitations,
  chapterCitations,
  generatedChapters,
  generatedSections
}) => {
  const allContent = [...generatedSections, ...generatedChapters].sort((a, b) => a.order - b.order);
  
  // Group citations by chapter/section
  const citationsByContent = Object.entries(chapterCitations).map(([contentId, citationIds]) => {
    const content = allContent.find(c => c.id === contentId);
    const citations = allCitations.filter(citation => 
      citation.chapterIds.includes(contentId)
    );
    
    return {
      contentId,
      contentTitle: content?.title || 'Unknown Section',
      contentType: content?.id.startsWith('chapter') ? 'Chapter' : 'Section',
      citations,
      citationCount: citations.length
    };
  }).filter(item => item.citationCount > 0);

  // Get unique citations for bibliography
  const uniqueCitations = allCitations.reduce((acc, citation) => {
    const existing = acc.find(c => 
      c.authors.join(',') === citation.authors.join(',') && 
      c.year === citation.year &&
      c.title === citation.title
    );
    
    if (!existing) {
      acc.push(citation);
    } else {
      // Merge chapter references
      existing.chapterIds = [...new Set([...existing.chapterIds, ...citation.chapterIds])];
    }
    
    return acc;
  }, [] as Citation[]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard!");
  };

  const formatCitation = (citation: Citation): string => {
    const authors = citation.authors.length > 3 
      ? `${citation.authors[0]} et al.`
      : citation.authors.join(', ');
    
    return `${authors} (${citation.year}). ${citation.title}. ${citation.source}${citation.doi ? `. DOI: ${citation.doi}` : ''}`;
  };

  const generateBibliography = (): string => {
    return uniqueCitations
      .sort((a, b) => a.authors[0]?.localeCompare(b.authors[0]) || 0)
      .map(citation => formatCitation(citation))
      .join('\n\n');
  };

  return (
    <Card className="shadow-lg border-0 bg-white/70 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-xl">
          <div className="w-2 h-6 bg-gradient-to-b from-orange-500 to-red-500 rounded-full"></div>
          <Quote className="h-5 w-5" />
          Citations & References
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="by-chapter" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="by-chapter">By Chapter</TabsTrigger>
            <TabsTrigger value="bibliography">Bibliography</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
          </TabsList>

          {/* Citations by Chapter */}
          <TabsContent value="by-chapter" className="space-y-4">
            <ScrollArea className="h-[400px] pr-4">
              {citationsByContent.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <Quote className="h-12 w-12 text-gray-400 mb-3" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">No Citations Found</h3>
                  <p className="text-gray-500 max-w-md">
                    Citations will appear here as your book content is generated with academic references.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {citationsByContent.map((item) => (
                    <div key={item.contentId} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold flex items-center gap-2">
                          {item.contentType === 'Chapter' ? (
                            <BookOpen className="h-4 w-4 text-blue-600" />
                          ) : (
                            <FileText className="h-4 w-4 text-green-600" />
                          )}
                          {item.contentTitle}
                        </h4>
                        <Badge variant="secondary">
                          {item.citationCount} citation{item.citationCount !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        {item.citations.map((citation, index) => (
                          <div key={`${item.contentId}-${index}`} className="flex items-start justify-between p-2 bg-white rounded border">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-blue-600">
                                {citation.inTextFormat}
                              </div>
                              <div className="text-xs text-gray-600 mt-1">
                                {formatCitation(citation)}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(formatCitation(citation))}
                              className="ml-2"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Bibliography */}
          <TabsContent value="bibliography" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Complete Bibliography</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(generateBibliography())}
                disabled={uniqueCitations.length === 0}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy All
              </Button>
            </div>
            
            <ScrollArea className="h-[400px] pr-4">
              {uniqueCitations.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center">
                  <FileText className="h-12 w-12 text-gray-400 mb-3" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">No References Yet</h3>
                  <p className="text-gray-500 max-w-md">
                    Your bibliography will be automatically generated from citations found in the book content.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {uniqueCitations
                    .sort((a, b) => a.authors[0]?.localeCompare(b.authors[0]) || 0)
                    .map((citation, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg border">
                        <div className="text-sm text-gray-800 mb-2">
                          {formatCitation(citation)}
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex flex-wrap gap-1">
                            {citation.chapterIds.map(chapterId => {
                              const content = allContent.find(c => c.id === chapterId);
                              return content ? (
                                <Badge key={chapterId} variant="outline" className="text-xs">
                                  {content.title}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(formatCitation(citation))}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Statistics */}
          <TabsContent value="statistics" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{allCitations.length}</div>
                <div className="text-sm text-blue-800">Total Citations</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{uniqueCitations.length}</div>
                <div className="text-sm text-green-800">Unique References</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{citationsByContent.length}</div>
                <div className="text-sm text-purple-800">Sections with Citations</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {allCitations.length > 0 ? Math.round(allCitations.length / Math.max(citationsByContent.length, 1) * 10) / 10 : 0}
                </div>
                <div className="text-sm text-orange-800">Avg per Section</div>
              </div>
            </div>

            {/* Citation Quality Indicators */}
            <div className="space-y-3">
              <h4 className="font-semibold">Citation Quality</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm">Citations with DOI</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {allCitations.filter(c => c.doi).length} / {allCitations.length}
                    </span>
                    {allCitations.filter(c => c.doi).length / Math.max(allCitations.length, 1) > 0.5 ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm">Recent Citations (last 5 years)</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {allCitations.filter(c => c.year >= new Date().getFullYear() - 5).length} / {allCitations.length}
                    </span>
                    {allCitations.filter(c => c.year >= new Date().getFullYear() - 5).length / Math.max(allCitations.length, 1) > 0.3 ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
