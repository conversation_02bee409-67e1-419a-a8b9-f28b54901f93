/**
 * Mode Switch Component
 * Allows users to switch between different AI assistant modes
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  GraduationCap, 
  Search, 
  MessageSquare, 
  Brain,
  BookOpen,
  Lightbulb,
  Target,
  Zap,
  ArrowRight,
  Check,
  Info
} from "lucide-react";

export type AIMode = 'tutor' | 'research' | 'general';

interface ModeOption {
  id: AIMode;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  features: string[];
  bestFor: string[];
}

interface ModeSwitchProps {
  currentMode: AIMode;
  onModeChange: (mode: AIMode) => void;
  suggestedMode?: AIMode;
  className?: string;
}

const modeOptions: ModeOption[] = [
  {
    id: 'tutor',
    name: '<PERSON> Tutor',
    description: 'Personalized learning with explanations tailored to your education level',
    icon: GraduationCap,
    color: 'blue',
    features: [
      'Level-adapted explanations',
      'Interactive visualizations',
      'Practice problems',
      'Progress tracking',
      'Learning assessments'
    ],
    bestFor: [
      'Learning new concepts',
      'Getting explanations',
      'Practice and exercises',
      'Homework help'
    ]
  },
  {
    id: 'research',
    name: 'Research Assistant',
    description: 'Deep research with citations, sources, and comprehensive analysis',
    icon: Search,
    color: 'green',
    features: [
      'Academic citations',
      'Source verification',
      'Comprehensive analysis',
      'Literature review',
      'Data visualization'
    ],
    bestFor: [
      'Academic research',
      'Writing papers',
      'Finding sources',
      'Data analysis'
    ]
  },
  {
    id: 'general',
    name: 'General Assistant',
    description: 'Versatile AI for general questions, tasks, and conversations',
    icon: MessageSquare,
    color: 'purple',
    features: [
      'General knowledge',
      'Task assistance',
      'Creative writing',
      'Problem solving',
      'Casual conversation'
    ],
    bestFor: [
      'General questions',
      'Creative tasks',
      'Planning and organizing',
      'Casual conversation'
    ]
  }
];

export const ModeSwitch: React.FC<ModeSwitchProps> = ({
  currentMode,
  onModeChange,
  suggestedMode,
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [hoveredMode, setHoveredMode] = useState<AIMode | null>(null);

  const currentModeOption = modeOptions.find(mode => mode.id === currentMode);
  const suggestedModeOption = suggestedMode ? modeOptions.find(mode => mode.id === suggestedMode) : null;

  const getModeColor = (mode: AIMode, variant: 'bg' | 'text' | 'border' = 'bg') => {
    const modeOption = modeOptions.find(m => m.id === mode);
    if (!modeOption) return '';
    
    const colorMap = {
      blue: {
        bg: 'bg-blue-500',
        text: 'text-blue-600',
        border: 'border-blue-300'
      },
      green: {
        bg: 'bg-green-500',
        text: 'text-green-600',
        border: 'border-green-300'
      },
      purple: {
        bg: 'bg-purple-500',
        text: 'text-purple-600',
        border: 'border-purple-300'
      }
    };
    
    return colorMap[modeOption.color]?.[variant] || '';
  };

  return (
    <TooltipProvider>
      <div className={`space-y-4 ${className}`}>
        {/* Mode Suggestion */}
        <AnimatePresence>
          {suggestedMode && suggestedMode !== currentMode && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-3"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Lightbulb className="w-4 h-4 text-yellow-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-800">
                    Suggested: Switch to {suggestedModeOption?.name}
                  </p>
                  <p className="text-xs text-yellow-600">
                    This mode is better suited for your current query
                  </p>
                </div>
                <Button
                  size="sm"
                  onClick={() => onModeChange(suggestedMode)}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white"
                >
                  Switch
                  <ArrowRight className="w-3 h-3 ml-1" />
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Current Mode Display */}
        <Card className="shadow-lg border-0 bg-gradient-to-r from-gray-50 to-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 ${getModeColor(currentMode)} rounded-lg flex items-center justify-center shadow-md`}>
                  {currentModeOption && <currentModeOption.icon className="w-5 h-5 text-white" />}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Current Mode: {currentModeOption?.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {currentModeOption?.description}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDetails(!showDetails)}
                    >
                      <Info className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mode details</TooltipContent>
                </Tooltip>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDetails(!showDetails)}
                >
                  Switch Mode
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mode Selection */}
        <AnimatePresence>
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {modeOptions.map((mode) => (
                  <motion.div
                    key={mode.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    onHoverStart={() => setHoveredMode(mode.id)}
                    onHoverEnd={() => setHoveredMode(null)}
                    className={`relative cursor-pointer transition-all duration-300 ${
                      currentMode === mode.id ? 'ring-2 ring-offset-2' : ''
                    } ${getModeColor(mode.id, 'border')} ring-offset-white`}
                    onClick={() => {
                      onModeChange(mode.id);
                      setShowDetails(false);
                    }}
                  >
                    <Card className={`h-full border-2 ${
                      currentMode === mode.id 
                        ? `${getModeColor(mode.id, 'border')} bg-gradient-to-br from-white to-${mode.color}-50` 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          {/* Header */}
                          <div className="flex items-center justify-between">
                            <div className={`w-12 h-12 ${getModeColor(mode.id)} rounded-xl flex items-center justify-center shadow-lg`}>
                              <mode.icon className="w-6 h-6 text-white" />
                            </div>
                            
                            {currentMode === mode.id && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"
                              >
                                <Check className="w-4 h-4 text-white" />
                              </motion.div>
                            )}
                          </div>

                          {/* Title and Description */}
                          <div>
                            <h3 className={`font-semibold text-lg ${getModeColor(mode.id, 'text')}`}>
                              {mode.name}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {mode.description}
                            </p>
                          </div>

                          {/* Features */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-700">Key Features:</h4>
                            <ul className="space-y-1">
                              {mode.features.slice(0, 3).map((feature, index) => (
                                <li key={index} className="flex items-center space-x-2 text-xs text-gray-600">
                                  <div className={`w-1 h-1 ${getModeColor(mode.id)} rounded-full`} />
                                  <span>{feature}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Best For */}
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-700">Best For:</h4>
                            <div className="flex flex-wrap gap-1">
                              {mode.bestFor.slice(0, 2).map((use, index) => (
                                <Badge 
                                  key={index} 
                                  variant="outline" 
                                  className={`text-xs ${getModeColor(mode.id, 'text')} ${getModeColor(mode.id, 'border')}`}
                                >
                                  {use}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Action Button */}
                          <Button
                            className={`w-full ${
                              currentMode === mode.id 
                                ? 'bg-gray-400 cursor-default' 
                                : `${getModeColor(mode.id)} hover:opacity-90`
                            }`}
                            disabled={currentMode === mode.id}
                          >
                            {currentMode === mode.id ? 'Current Mode' : `Switch to ${mode.name}`}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Detailed Features for Hovered Mode */}
              <AnimatePresence>
                {hoveredMode && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="bg-white rounded-lg border border-gray-200 p-4 shadow-lg"
                  >
                    {(() => {
                      const mode = modeOptions.find(m => m.id === hoveredMode);
                      if (!mode) return null;
                      
                      return (
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <mode.icon className={`w-5 h-5 ${getModeColor(mode.id, 'text')}`} />
                            <h4 className="font-semibold text-gray-900">{mode.name} - Full Features</h4>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="text-sm font-medium text-gray-700 mb-2">All Features:</h5>
                              <ul className="space-y-1">
                                {mode.features.map((feature, index) => (
                                  <li key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                                    <Zap className={`w-3 h-3 ${getModeColor(mode.id, 'text')}`} />
                                    <span>{feature}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                            
                            <div>
                              <h5 className="text-sm font-medium text-gray-700 mb-2">Perfect For:</h5>
                              <ul className="space-y-1">
                                {mode.bestFor.map((use, index) => (
                                  <li key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                                    <Target className={`w-3 h-3 ${getModeColor(mode.id, 'text')}`} />
                                    <span>{use}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      );
                    })()}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </TooltipProvider>
  );
};

export default ModeSwitch;
