/**
 * Custom hook for managing research search functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { 
  SearchSession, 
  SearchMessage, 
  SearchProgress, 
  UserPreferences,
  TavilySearchResult 
} from '../types';

import { searchHistoryService } from '../services/search-history.service';
import { tavilySearchService } from '../services/tavily-search.service';
import { researchAIService } from '../services/research-ai.service';

interface UseResearchSearchReturn {
  // State
  currentSession: SearchSession | null;
  messages: SearchMessage[];
  isLoading: boolean;
  searchProgress: SearchProgress | null;
  error: string | null;
  preferences: UserPreferences | null;
  
  // Actions
  createNewSession: () => Promise<void>;
  loadSession: (sessionId: string) => Promise<void>;
  performSearch: (query: string, model: string) => Promise<void>;
  updatePreferences: (prefs: Partial<UserPreferences>) => Promise<void>;
  clearError: () => void;
  retryLastSearch: () => Promise<void>;
}

export function useResearchSearch(): UseResearchSearchReturn {
  const [currentSession, setCurrentSession] = useState<SearchSession | null>(null);
  const [messages, setMessages] = useState<SearchMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchProgress, setSearchProgress] = useState<SearchProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);

  // Initialize preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      const userPrefs = await searchHistoryService.getUserPreferences();
      setPreferences(userPrefs);
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const createNewSession = useCallback(async () => {
    try {
      const defaultModel = preferences?.defaultModel || 'google/gemini-2.0-flash-001';
      const session = await searchHistoryService.createSession(
        'New Research Session',
        defaultModel
      );
      setCurrentSession(session);
      setMessages([]);
      setError(null);
      toast.success('New research session created');
    } catch (error: any) {
      console.error('Error creating new session:', error);
      setError('Failed to create new search session');
      toast.error('Failed to create new session');
    }
  }, [preferences]);

  const loadSession = useCallback(async (sessionId: string) => {
    try {
      const session = await searchHistoryService.getSession(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages);
        await searchHistoryService.setActiveSession(sessionId);
        setError(null);
        toast.success('Session loaded successfully');
      }
    } catch (error: any) {
      console.error('Error loading session:', error);
      setError('Failed to load search session');
      toast.error('Failed to load session');
    }
  }, []);

  const performSearch = useCallback(async (query: string, model: string) => {
    if (!currentSession || isLoading) return;

    setIsLoading(true);
    setError(null);
    setSearchProgress({ stage: 'searching', message: 'Initializing search...', progress: 0 });

    // Add user message
    const userMessage: SearchMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: query,
      timestamp: new Date(),
      searchQuery: query
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      // Save user message to database
      await searchHistoryService.addMessage(currentSession.id, userMessage);

      // Update search progress
      setSearchProgress({ stage: 'searching', message: 'Searching academic sources...', progress: 25 });

      // Validate search query
      const validation = tavilySearchService.validateQuery(query);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Perform Tavily search
      const searchResults = await tavilySearchService.searchAcademic(query, {
        maxResults: preferences?.maxResults || 10,
        searchDepth: preferences?.defaultSearchDepth || 'advanced',
        includeAnswer: true,
        includeImages: false
      });

      setSearchProgress({ stage: 'analyzing', message: 'Analyzing search results...', progress: 50 });

      // Generate AI response
      setSearchProgress({ stage: 'generating', message: 'Generating comprehensive response...', progress: 75 });

      const { response, citations } = await researchAIService.generateResponse(
        query,
        searchResults,
        model,
        messages.slice(-5) // Last 5 messages for context
      );

      // Create assistant message
      const assistantMessage: SearchMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        sources: tavilySearchService.transformToSearchSources(searchResults.results),
        citations
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Save assistant message to database
      await searchHistoryService.addMessage(currentSession.id, assistantMessage);

      setSearchProgress({ stage: 'complete', message: 'Response generated successfully', progress: 100 });

      // Update session title if it's the first query
      if (messages.length === 0) {
        const title = query.length > 50 ? query.substring(0, 50) + '...' : query;
        await searchHistoryService.updateSessionTitle(currentSession.id, title);
        setCurrentSession(prev => prev ? { ...prev, title } : null);
      }

      toast.success('Search completed successfully');

    } catch (error: any) {
      console.error('Search error:', error);
      const errorMessage = error.message || 'Failed to perform search';
      setError(errorMessage);
      
      // Add error message
      const errorResponse: SearchMessage = {
        id: (Date.now() + 2).toString(),
        type: 'assistant',
        content: `I apologize, but I encountered an error while searching: ${errorMessage}. Please try again or rephrase your question.`,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, errorResponse]);
      toast.error('Search failed: ' + errorMessage);
    } finally {
      setIsLoading(false);
      setSearchProgress(null);
    }
  }, [currentSession, isLoading, messages, preferences]);

  const updatePreferences = useCallback(async (prefs: Partial<UserPreferences>) => {
    try {
      const updatedPrefs = { ...preferences, ...prefs } as UserPreferences;
      await searchHistoryService.updateUserPreferences(updatedPrefs);
      setPreferences(updatedPrefs);
      toast.success('Preferences updated successfully');
    } catch (error: any) {
      console.error('Error updating preferences:', error);
      toast.error('Failed to update preferences');
    }
  }, [preferences]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const retryLastSearch = useCallback(async () => {
    const lastUserMessage = messages.filter(m => m.type === 'user').pop();
    if (lastUserMessage?.searchQuery && currentSession) {
      const model = preferences?.defaultModel || 'google/gemini-2.0-flash-001';
      await performSearch(lastUserMessage.searchQuery, model);
    }
  }, [messages, currentSession, preferences, performSearch]);

  return {
    // State
    currentSession,
    messages,
    isLoading,
    searchProgress,
    error,
    preferences,
    
    // Actions
    createNewSession,
    loadSession,
    performSearch,
    updatePreferences,
    clearError,
    retryLastSearch
  };
}
