/**
 * Journal Ranking System Component
 * Displays and manages journal recommendations with customizable ranking criteria
 */

import React, { useState, useEffect } from 'react';
import { JournalRankingSystemProps, JournalRecommendation, JournalRanking } from '../types';
import { JournalRecommendationCard } from './JournalRecommendationCard';
import { RANKING_CRITERIA } from '../constants';
import { useArticleFinderStore } from '../stores/article-finder.store';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  Settings, 
  TrendingUp, 
  Filter,
  SortAsc,
  SortDesc,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Star,
  Award,
  Clock,
  DollarSign,
  Globe
} from "lucide-react";

export const JournalRankingSystem: React.FC<JournalRankingSystemProps> = ({
  journals,
  criteria,
  onCriteriaChange,
  onRankingUpdate
}) => {
  const { calculateJournalRankings, setRankingCriteria } = useArticleFinderStore();
  
  const [selectedJournals, setSelectedJournals] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'score' | 'impact' | 'speed' | 'cost'>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showRankingControls, setShowRankingControls] = useState(false);
  const [filterBy, setFilterBy] = useState<'all' | 'openaccess' | 'highimpact' | 'fasttrack'>('all');

  // Calculate rankings when criteria or journals change
  const [rankings, setRankings] = useState<JournalRanking[]>([]);

  useEffect(() => {
    const newRankings = calculateJournalRankings();
    setRankings(newRankings);
    onRankingUpdate(newRankings);
  }, [criteria, journals, calculateJournalRankings, onRankingUpdate]);

  /**
   * Handle criteria weight change
   */
  const handleWeightChange = (criterion: keyof typeof criteria, value: number[]) => {
    const newCriteria = {
      ...criteria,
      [criterion]: value[0] / 100
    };
    
    // Normalize weights to sum to 1
    const totalWeight = Object.values(newCriteria).reduce((sum, w) => sum + w, 0);
    if (totalWeight > 0) {
      Object.keys(newCriteria).forEach(key => {
        newCriteria[key as keyof typeof criteria] /= totalWeight;
      });
    }
    
    setRankingCriteria(newCriteria);
    onCriteriaChange(newCriteria);
  };

  /**
   * Reset criteria to defaults
   */
  const resetCriteria = () => {
    const defaultCriteria = {
      relevanceWeight: 0.3,
      impactWeight: 0.25,
      speedWeight: 0.2,
      costWeight: 0.15,
      accessibilityWeight: 0.1
    };
    setRankingCriteria(defaultCriteria);
    onCriteriaChange(defaultCriteria);
  };

  /**
   * Handle journal selection
   */
  const handleJournalSelect = (journal: JournalRecommendation) => {
    const newSelected = new Set(selectedJournals);
    if (newSelected.has(journal.id)) {
      newSelected.delete(journal.id);
    } else {
      newSelected.add(journal.id);
    }
    setSelectedJournals(newSelected);
  };

  /**
   * Filter journals based on criteria
   */
  const getFilteredJournals = (): JournalRecommendation[] => {
    let filtered = [...journals];

    switch (filterBy) {
      case 'openaccess':
        filtered = filtered.filter(j => j.metrics.isOpenAccess);
        break;
      case 'highimpact':
        filtered = filtered.filter(j => j.metrics.impactFactor >= 3);
        break;
      case 'fasttrack':
        filtered = filtered.filter(j => j.metrics.averageReviewTime < 90);
        break;
      default:
        break;
    }

    return filtered;
  };

  /**
   * Sort journals based on selected criteria
   */
  const getSortedJournals = (): { journal: JournalRecommendation; ranking: JournalRanking }[] => {
    const filtered = getFilteredJournals();
    const journalsWithRankings = filtered.map((journal, index) => ({
      journal,
      ranking: rankings[journals.findIndex(j => j.id === journal.id)] || {
        criteria,
        weightedScore: 0,
        breakdown: { relevance: 0, impact: 0, speed: 0, cost: 0, accessibility: 0 }
      }
    }));

    return journalsWithRankings.sort((a, b) => {
      let valueA: number, valueB: number;

      switch (sortBy) {
        case 'score':
          valueA = a.ranking.weightedScore;
          valueB = b.ranking.weightedScore;
          break;
        case 'impact':
          valueA = a.journal.metrics.impactFactor;
          valueB = b.journal.metrics.impactFactor;
          break;
        case 'speed':
          valueA = a.journal.metrics.averageReviewTime;
          valueB = b.journal.metrics.averageReviewTime;
          break;
        case 'cost':
          valueA = a.journal.metrics.publicationFees;
          valueB = b.journal.metrics.publicationFees;
          break;
        default:
          valueA = a.ranking.weightedScore;
          valueB = b.ranking.weightedScore;
      }

      if (sortBy === 'speed' || sortBy === 'cost') {
        // For speed and cost, lower is better
        return sortOrder === 'asc' ? valueA - valueB : valueB - valueA;
      } else {
        // For score and impact, higher is better
        return sortOrder === 'desc' ? valueB - valueA : valueA - valueB;
      }
    });
  };

  const sortedJournals = getSortedJournals();
  const filteredCount = getFilteredJournals().length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="w-6 h-6 text-blue-600 mr-2" />
            Journal Rankings
          </h2>
          <p className="text-gray-600 mt-1">
            {filteredCount} journal{filteredCount !== 1 ? 's' : ''} ranked by your criteria
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowRankingControls(!showRankingControls)}
          >
            <Settings className="w-4 h-4 mr-2" />
            Customize Ranking
          </Button>
        </div>
      </div>

      {/* Ranking Controls */}
      <Collapsible open={showRankingControls} onOpenChange={setShowRankingControls}>
        <CollapsibleContent>
          <Card className="bg-gray-50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                <span>Ranking Criteria</span>
                <Button variant="ghost" size="sm" onClick={resetCriteria}>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Reset
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {Object.entries(RANKING_CRITERIA).map(([key, config]) => (
                <div key={key}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-2" 
                        style={{ backgroundColor: config.color }}
                      />
                      <span className="font-medium text-gray-900">{config.label}</span>
                    </div>
                    <Badge variant="outline">
                      {Math.round(criteria[key as keyof typeof criteria] * 100)}%
                    </Badge>
                  </div>
                  <Slider
                    value={[criteria[key as keyof typeof criteria] * 100]}
                    onValueChange={(value) => handleWeightChange(key as keyof typeof criteria, value)}
                    max={100}
                    step={5}
                    className="mb-1"
                  />
                  <p className="text-xs text-gray-500">{config.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>

      {/* Filters and Sorting */}
      <div className="flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg border">
        {/* Filter */}
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <Select value={filterBy} onValueChange={setFilterBy}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Journals</SelectItem>
              <SelectItem value="openaccess">Open Access</SelectItem>
              <SelectItem value="highimpact">High Impact (IF ≥ 3)</SelectItem>
              <SelectItem value="fasttrack">Fast Review (&lt; 3 months)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Sort */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Sort by:</span>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="score">Overall Score</SelectItem>
              <SelectItem value="impact">Impact Factor</SelectItem>
              <SelectItem value="speed">Review Speed</SelectItem>
              <SelectItem value="cost">Publication Cost</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'desc' ? (
              <SortDesc className="w-4 h-4" />
            ) : (
              <SortAsc className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Selected Count */}
        {selectedJournals.size > 0 && (
          <Badge variant="secondary" className="ml-auto">
            {selectedJournals.size} selected
          </Badge>
        )}
      </div>

      {/* Journal Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sortedJournals.map(({ journal, ranking }, index) => (
          <div key={journal.id} className="relative">
            {/* Ranking Badge */}
            <div className="absolute -top-2 -left-2 z-10">
              <Badge 
                className={`
                  ${index === 0 ? 'bg-yellow-500 text-white' : 
                    index === 1 ? 'bg-gray-400 text-white' : 
                    index === 2 ? 'bg-amber-600 text-white' : 
                    'bg-blue-100 text-blue-800'}
                `}
              >
                #{index + 1}
              </Badge>
            </div>
            
            <JournalRecommendationCard
              journal={journal}
              ranking={ranking}
              onSelect={handleJournalSelect}
              isSelected={selectedJournals.has(journal.id)}
            />
          </div>
        ))}
      </div>

      {/* Empty State */}
      {sortedJournals.length === 0 && (
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No journals match your filters
          </h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your filter criteria to see more results.
          </p>
          <Button variant="outline" onClick={() => setFilterBy('all')}>
            Show All Journals
          </Button>
        </div>
      )}

      {/* Summary Stats */}
      {sortedJournals.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(sortedJournals[0]?.ranking.weightedScore * 100 || 0)}%
                </div>
                <div className="text-sm text-blue-700">Top Match Score</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {sortedJournals.filter(({ journal }) => journal.metrics.isOpenAccess).length}
                </div>
                <div className="text-sm text-green-700">Open Access</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {sortedJournals.filter(({ journal }) => journal.metrics.averageReviewTime < 90).length}
                </div>
                <div className="text-sm text-purple-700">Fast Review</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">
                  {sortedJournals.filter(({ journal }) => journal.metrics.impactFactor >= 3).length}
                </div>
                <div className="text-sm text-yellow-700">High Impact</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
