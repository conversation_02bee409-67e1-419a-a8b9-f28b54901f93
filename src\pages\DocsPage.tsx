import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  FileText,
  Zap,
  Code,
  Database,
  Users,
  Shield,
  GraduationCap,
  ArrowRight,
  ExternalLink,
  Download,
  Play,
  GitBranch
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const DocsPage = () => {
  const navigate = useNavigate();

  const quickStartSteps = [
    {
      step: "1",
      title: "Create Account",
      description: "Sign up for free and access your research dashboard",
      action: "Get Started",
      link: "/app"
    },
    {
      step: "2", 
      title: "Choose Your Tool",
      description: "Select from AI Paper Generator, Article Reviewer, or Research Analysis",
      action: "View Features",
      link: "#features"
    },
    {
      step: "3",
      title: "Input Your Research", 
      description: "Upload papers, add content, or describe your research topic",
      action: "See Examples",
      link: "#examples"
    },
    {
      step: "4",
      title: "Generate & Export",
      description: "Get AI-generated content and export in your preferred format",
      action: "Export Guide",
      link: "#export"
    }
  ];

  const features = [
    {
      icon: <FileText className="h-8 w-8" />,
      title: "AI Paper Generator",
      description: "Generate complete research papers with proper structure and citations",
      features: [
        "Automatic section generation (Introduction, Methodology, Results, etc.)",
        "Academic citation formatting (APA, MLA, Chicago)",
        "Multiple AI models (GPT-4, Claude, Gemini)",
        "Export to PDF, DOCX, LaTeX"
      ],
      codeExample: `// Example: Generate paper section
const paper = await verbira.generatePaper({
  title: "Machine Learning in Climate Research",
  field: "Environmental Science",
  sections: ["introduction", "methodology", "results"]
});`
    },
    {
      icon: <BookOpen className="h-8 w-8" />,
      title: "AI Book Generator", 
      description: "Create comprehensive books with multiple chapters and structured content",
      features: [
        "Chapter-by-chapter generation",
        "Consistent tone and style",
        "Table of contents and index generation",
        "Professional formatting"
      ],
      codeExample: `// Example: Generate book
const book = await verbira.generateBook({
  title: "Introduction to Data Science",
  chapters: 12,
  targetAudience: "undergraduate students"
});`
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: "Article Reviewer",
      description: "Upload papers for detailed AI review and improvement suggestions",
      features: [
        "Sentence-level analysis",
        "Academic quality scoring",
        "Improvement suggestions",
        "Plagiarism detection insights"
      ],
      codeExample: `// Example: Review article
const review = await verbira.reviewArticle({
  file: uploadedPaper,
  analysisType: "comprehensive",
  focusAreas: ["clarity", "structure", "citations"]
});`
    },
    {
      icon: <Database className="h-8 w-8" />,
      title: "Research Analysis",
      description: "Analyze multiple papers to identify gaps and generate literature reviews",
      features: [
        "Multi-paper analysis",
        "Research gap identification", 
        "Literature review generation",
        "Trend analysis and insights"
      ],
      codeExample: `// Example: Analyze research
const analysis = await verbira.analyzeResearch({
  papers: [paper1, paper2, paper3],
  analysisType: "gap-analysis",
  generateLiteratureReview: true
});`
    }
  ];

  const apiEndpoints = [
    {
      method: "POST",
      endpoint: "/api/v1/papers/generate",
      description: "Generate a research paper",
      params: ["title", "field", "sections", "model"]
    },
    {
      method: "POST", 
      endpoint: "/api/v1/articles/review",
      description: "Review an uploaded article",
      params: ["file", "analysisType", "focusAreas"]
    },
    {
      method: "POST",
      endpoint: "/api/v1/research/analyze", 
      description: "Analyze multiple research papers",
      params: ["papers", "analysisType", "outputFormat"]
    },
    {
      method: "GET",
      endpoint: "/api/v1/models",
      description: "List available AI models",
      params: []
    }
  ];

  const integrations = [
    {
      name: "Zotero",
      description: "Sync your reference library",
      status: "Available"
    },
    {
      name: "Mendeley", 
      description: "Import and export citations",
      status: "Available"
    },
    {
      name: "LaTeX",
      description: "Export formatted LaTeX documents", 
      status: "Available"
    },
    {
      name: "Google Docs",
      description: "Direct export to Google Docs",
      status: "Coming Soon"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/30 to-purple-50/20">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                <GraduationCap className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Verbira
                </h1>
                <p className="text-sm text-gray-600">AI Research Assistant</p>
              </div>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="/" className="text-gray-600 hover:text-blue-600 transition-colors">Home</a>
              <a href="/pricing" className="text-gray-600 hover:text-blue-600 transition-colors">Pricing</a>
              <a href="#docs" className="text-blue-600 font-medium">Docs</a>
              <Button 
                variant="outline" 
                onClick={() => navigate('/login')}
                className="border-2 hover:bg-blue-50"
              >
                Sign In
              </Button>
              <Button 
                onClick={() => navigate('/app')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Get Started
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-12">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg">Documentation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <a href="#quick-start" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    Quick Start
                  </a>
                  <a href="#features" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    Features Guide
                  </a>
                  <a href="#api" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    API Reference
                  </a>
                  <a href="#integrations" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    Integrations
                  </a>
                  <a href="#examples" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    Examples
                  </a>
                  <a href="#troubleshooting" className="block p-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                    Troubleshooting
                  </a>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg">Resources</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <a href="#" className="flex items-center gap-2 p-2 text-sm text-gray-600 hover:text-blue-600 rounded transition-colors">
                    <Download className="h-4 w-4" />
                    Download SDK
                  </a>
                  <a href="#" className="flex items-center gap-2 p-2 text-sm text-gray-600 hover:text-blue-600 rounded transition-colors">
                    <GitBranch className="h-4 w-4" />
                    GitHub Repository
                  </a>
                  <a href="#" className="flex items-center gap-2 p-2 text-sm text-gray-600 hover:text-blue-600 rounded transition-colors">
                    <ExternalLink className="h-4 w-4" />
                    API Playground
                  </a>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-12">
            {/* Hero */}
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Verbira Documentation
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Everything you need to get started with AI-powered research assistance. 
                From basic setup to advanced integrations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg"
                  onClick={() => navigate('/app')}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <Play className="mr-2 h-5 w-5" />
                  Try Now
                </Button>
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-2 hover:bg-blue-50"
                >
                  <Code className="mr-2 h-5 w-5" />
                  View API
                </Button>
              </div>
            </div>

            {/* Quick Start */}
            <section id="quick-start">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Quick Start Guide</h2>
              <p className="text-lg text-gray-600 mb-8">
                Get up and running with Verbira in just a few minutes.
              </p>
              
              <div className="grid md:grid-cols-2 gap-6">
                {quickStartSteps.map((step, index) => (
                  <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-4">
                        <div className="bg-gradient-to-r from-blue-600 to-purple-600 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold">
                          {step.step}
                        </div>
                        <CardTitle className="text-xl">{step.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-gray-600 mb-4">
                        {step.description}
                      </CardDescription>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => step.link.startsWith('#') ? document.querySelector(step.link)?.scrollIntoView() : navigate(step.link)}
                      >
                        {step.action}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Features Guide */}
            <section id="features">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Features Guide</h2>
              <p className="text-lg text-gray-600 mb-8">
                Detailed guide to all Verbira features and capabilities.
              </p>

              <div className="space-y-8">
                {features.map((feature, index) => (
                  <Card key={index} className="border-0 shadow-lg">
                    <CardHeader>
                      <div className="flex items-center gap-4 mb-4">
                        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl text-white">
                          {feature.icon}
                        </div>
                        <div>
                          <CardTitle className="text-2xl">{feature.title}</CardTitle>
                          <CardDescription className="text-lg">
                            {feature.description}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                          <ul className="space-y-2">
                            {feature.features.map((item, idx) => (
                              <li key={idx} className="flex items-start gap-2 text-gray-600">
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">Example Usage:</h4>
                          <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                            <pre className="text-sm text-green-400">
                              <code>{feature.codeExample}</code>
                            </pre>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* API Reference */}
            <section id="api">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">API Reference</h2>
              <p className="text-lg text-gray-600 mb-8">
                Integrate Verbira into your applications with our REST API.
              </p>

              <Card className="border-0 shadow-lg mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Authentication
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    All API requests require authentication using your API key in the header:
                  </p>
                  <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre className="text-sm text-green-400">
                      <code>{`Authorization: Bearer your_api_key_here
Content-Type: application/json`}</code>
                    </pre>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h3 className="text-2xl font-semibold text-gray-900">Endpoints</h3>
                {apiEndpoints.map((endpoint, index) => (
                  <Card key={index} className="border-0 shadow-md">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <Badge className={`${
                          endpoint.method === 'POST' ? 'bg-green-500' : 
                          endpoint.method === 'GET' ? 'bg-blue-500' : 'bg-orange-500'
                        } hover:bg-opacity-80`}>
                          {endpoint.method}
                        </Badge>
                        <code className="text-lg font-mono text-gray-800">
                          {endpoint.endpoint}
                        </code>
                      </div>
                      <p className="text-gray-600 mb-4">{endpoint.description}</p>
                      {endpoint.params.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Parameters:</h4>
                          <div className="flex flex-wrap gap-2">
                            {endpoint.params.map((param, idx) => (
                              <Badge key={idx} variant="outline">
                                {param}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Integrations */}
            <section id="integrations">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Integrations</h2>
              <p className="text-lg text-gray-600 mb-8">
                Connect Verbira with your favorite research tools and platforms.
              </p>

              <div className="grid md:grid-cols-2 gap-6">
                {integrations.map((integration, index) => (
                  <Card key={index} className="border-0 shadow-lg">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {integration.name}
                        </h3>
                        <Badge className={
                          integration.status === 'Available' 
                            ? 'bg-green-100 text-green-700 hover:bg-green-100'
                            : 'bg-orange-100 text-orange-700 hover:bg-orange-100'
                        }>
                          {integration.status}
                        </Badge>
                      </div>
                      <p className="text-gray-600">{integration.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* Examples */}
            <section id="examples">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Examples</h2>
              <p className="text-lg text-gray-600 mb-8">
                Real-world examples and use cases to help you get started.
              </p>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Coming Soon</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    We're preparing comprehensive examples and tutorials. 
                    Check back soon or contact support for specific use cases.
                  </p>
                </CardContent>
              </Card>
            </section>

            {/* Troubleshooting */}
            <section id="troubleshooting">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Troubleshooting</h2>
              <p className="text-lg text-gray-600 mb-8">
                Common issues and solutions.
              </p>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Need Help?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    If you can't find what you're looking for, we're here to help:
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button 
                      variant="outline"
                      onClick={() => navigate('/contact')}
                    >
                      Contact Support
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => window.open('mailto:<EMAIL>')}
                    >
                      Email Us
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* CTA */}
            <section className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-center">
              <h3 className="text-3xl font-bold text-white mb-4">
                Ready to Get Started?
              </h3>
              <p className="text-xl text-blue-100 mb-6 max-w-2xl mx-auto">
                Try Verbira today and experience the future of AI-powered research assistance.
              </p>
              <Button 
                size="lg"
                onClick={() => navigate('/app')}
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </section>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 mt-20">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl">
                  <GraduationCap className="h-6 w-6 text-white" />
                </div>
                <span className="text-xl font-bold">Verbira</span>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Empowering researchers worldwide with advanced AI tools.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/docs" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="/api" className="hover:text-white transition-colors">API</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/about" className="hover:text-white transition-colors">About</a></li>
                <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/help" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="/terms" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Verbira. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DocsPage;
