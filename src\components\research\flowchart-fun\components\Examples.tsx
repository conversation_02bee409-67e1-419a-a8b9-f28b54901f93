/**
 * Examples Component
 * Beautiful showcase of flowchart templates and examples
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Copy, Eye, Download, Sparkles } from 'lucide-react';
import { FlowchartTemplate } from '../types';
import { 
  flowchartExamples, 
  getExamplesByCategory, 
  searchExamples, 
  getAllCategories, 
  getAllTags 
} from '../examples/flowchartExamples';

interface ExamplesProps {
  onSelectExample: (template: FlowchartTemplate) => void;
  onPreviewExample?: (template: FlowchartTemplate) => void;
  className?: string;
}

const Examples: React.FC<ExamplesProps> = ({
  onSelectExample,
  onPreviewExample,
  className = '',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('all');

  const categories = getAllCategories();
  const tags = getAllTags();

  // Filter examples based on search and filters
  const filteredExamples = useMemo(() => {
    let examples = flowchartExamples;

    // Apply search filter
    if (searchQuery.trim()) {
      examples = searchExamples(searchQuery);
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      examples = examples.filter(example => example.category === selectedCategory);
    }

    // Apply tag filter
    if (selectedTag !== 'all') {
      examples = examples.filter(example => example.tags.includes(selectedTag));
    }

    return examples;
  }, [searchQuery, selectedCategory, selectedTag]);

  // Group examples by category for tabs
  const examplesByCategory = useMemo(() => {
    const grouped: Record<string, FlowchartTemplate[]> = {};
    categories.forEach(category => {
      grouped[category] = getExamplesByCategory(category);
    });
    return grouped;
  }, [categories]);

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const ExampleCard: React.FC<{ template: FlowchartTemplate }> = ({ template }) => (
    <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-1">{template.name}</CardTitle>
            <CardDescription className="text-sm">{template.description}</CardDescription>
          </div>
          <Badge variant="secondary" className="ml-2 shrink-0">
            {template.category}
          </Badge>
        </div>
        <div className="flex flex-wrap gap-1 mt-2">
          {template.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Preview text */}
          <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
            {template.preview}
          </div>
          
          {/* Code preview */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <pre className="text-xs text-gray-700 whitespace-pre-wrap line-clamp-4 overflow-hidden">
              {template.text.split('\n').slice(0, 4).join('\n')}
              {template.text.split('\n').length > 4 && '\n...'}
            </pre>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              size="sm"
              onClick={() => onSelectExample(template)}
              className="flex-1"
            >
              <Copy className="h-4 w-4 mr-1" />
              Use Template
            </Button>
            {onPreviewExample && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPreviewExample(template)}
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCopyText(template.text)}
              title="Copy text"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={`w-full max-w-7xl mx-auto ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <Sparkles className="h-6 w-6 text-blue-500" />
            <h2 className="text-2xl font-bold">Flowchart Examples</h2>
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Get started quickly with our collection of professional flowchart templates. 
            Choose from business processes, software development workflows, and more.
          </p>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search examples..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedTag} onValueChange={setSelectedTag}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All Tags" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tags</SelectItem>
                    {tags.map((tag) => (
                      <SelectItem key={tag} value={tag}>
                        {tag}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Examples */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-6">
            <TabsTrigger value="all">All</TabsTrigger>
            {categories.slice(0, 5).map((category) => (
              <TabsTrigger key={category} value={category} className="text-xs">
                {category.split(' ')[0]}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredExamples.map((template) => (
                <ExampleCard key={template.id} template={template} />
              ))}
            </div>
          </TabsContent>

          {categories.map((category) => (
            <TabsContent key={category} value={category} className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {examplesByCategory[category]?.map((template) => (
                  <ExampleCard key={template.id} template={template} />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Empty state */}
        {filteredExamples.length === 0 && (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No examples found</h3>
              <p className="text-sm">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="text-center text-sm text-muted-foreground">
          Showing {filteredExamples.length} of {flowchartExamples.length} examples
        </div>
      </div>
    </div>
  );
};

export default Examples;
