/**
 * Gemini Changes Preview Component
 * Shows a detailed diff view of changes before applying them
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Eye, 
  EyeOff, 
  ThumbsUp, 
  ThumbsDown, 
  ChevronDown, 
  ChevronUp,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { EditInstruction } from './GeminiAgentService';

interface GeminiChangesPreviewProps {
  edits: EditInstruction[];
  onApplyChanges: (edits: EditInstruction[]) => void;
  onRejectChanges: () => void;
  onClose: () => void;
}

export function GeminiChangesPreview({
  edits,
  onApplyChanges,
  onRejectChanges,
  onClose
}: GeminiChangesPreviewProps) {
  const [selectedEdits, setSelectedEdits] = useState<Set<string>>(new Set(edits.map(e => e.sectionId)));
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleEditSelection = (sectionId: string) => {
    const newSelected = new Set(selectedEdits);
    if (newSelected.has(sectionId)) {
      newSelected.delete(sectionId);
    } else {
      newSelected.add(sectionId);
    }
    setSelectedEdits(newSelected);
  };

  const toggleSectionExpansion = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleApplySelected = () => {
    const selectedEditsList = edits.filter(edit => selectedEdits.has(edit.sectionId));
    console.log('🎯 Applying selected edits from preview:', selectedEditsList);
    onApplyChanges(selectedEditsList);
  };

  const selectAll = () => {
    setSelectedEdits(new Set(edits.map(e => e.sectionId)));
  };

  const selectNone = () => {
    setSelectedEdits(new Set());
  };

  const getChangeType = (original: string, edited: string) => {
    if (edited.length < original.length * 0.5) {
      return { type: 'major-removal', label: 'Major Removal', color: 'red' };
    } else if (edited.length < original.length * 0.8) {
      return { type: 'removal', label: 'Content Removed', color: 'orange' };
    } else if (edited.length > original.length * 1.2) {
      return { type: 'addition', label: 'Content Added', color: 'green' };
    } else {
      return { type: 'modification', label: 'Modified', color: 'blue' };
    }
  };

  const highlightDifferences = (original: string, edited: string) => {
    // Simple word-based diff highlighting
    const originalWords = original.split(/(\s+)/);
    const editedWords = edited.split(/(\s+)/);
    
    const maxLength = Math.max(originalWords.length, editedWords.length);
    const result = { original: [], edited: [] };
    
    for (let i = 0; i < maxLength; i++) {
      const origWord = originalWords[i] || '';
      const editWord = editedWords[i] || '';
      
      if (origWord !== editWord) {
        result.original.push({ text: origWord, changed: true });
        result.edited.push({ text: editWord, changed: true });
      } else {
        result.original.push({ text: origWord, changed: false });
        result.edited.push({ text: editWord, changed: false });
      }
    }
    
    return result;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-hidden">
      <div className="w-full max-w-4xl h-[90vh] flex flex-col bg-white rounded-lg shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b flex-shrink-0">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold">Review Changes</h2>
            <Badge variant="secondary">{edits.length} changes</Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XCircle className="h-4 w-4" />
          </Button>
        </div>

        {/* Selection Controls */}
        <div className="p-4 border-b bg-gray-50 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                className="text-xs"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Select All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={selectNone}
                className="text-xs"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Select None
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {selectedEdits.size} of {edits.length} selected
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4 space-y-3">
            {edits.map((edit, index) => {
              const isSelected = selectedEdits.has(edit.sectionId);
              const isExpanded = expandedSections.has(edit.sectionId);
              const changeType = getChangeType(edit.originalContent, edit.editedContent);

              return (
                <div key={edit.sectionId} className={`border rounded-lg ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'} shadow-sm`}>
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleEditSelection(edit.sectionId)}
                          className="rounded w-4 h-4"
                        />
                        <span className="font-medium text-sm">Change {index + 1}</span>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            changeType.color === 'red' ? 'text-red-600 border-red-300' :
                            changeType.color === 'green' ? 'text-green-600 border-green-300' :
                            changeType.color === 'orange' ? 'text-orange-600 border-orange-300' :
                            'text-blue-600 border-blue-300'
                          }`}
                        >
                          {changeType.label}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleSectionExpansion(edit.sectionId)}
                        className="h-8 w-8 p-0"
                      >
                        {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </Button>
                    </div>

                    {/* Always show a preview */}
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <div className="font-medium text-red-600 mb-1">Before:</div>
                        <div className="bg-red-50 border border-red-200 p-2 rounded max-h-20 overflow-y-auto">
                          {edit.originalContent.substring(0, 150)}{edit.originalContent.length > 150 ? '...' : ''}
                        </div>
                      </div>
                      <div>
                        <div className="font-medium text-green-600 mb-1">After:</div>
                        <div className="bg-green-50 border border-green-200 p-2 rounded max-h-20 overflow-y-auto">
                          {edit.editedContent.substring(0, 150)}{edit.editedContent.length > 150 ? '...' : ''}
                        </div>
                      </div>
                    </div>

                    {isExpanded && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-4">
                          {/* Original Content */}
                          <div>
                            <div className="font-medium text-red-600 mb-2 flex items-center gap-1 text-sm">
                              <XCircle className="h-4 w-4" />
                              Full Original Content
                            </div>
                            <div className="bg-red-50 border border-red-200 rounded p-3 text-xs max-h-64 overflow-y-auto">
                              {edit.originalContent}
                            </div>
                          </div>

                          {/* Edited Content */}
                          <div>
                            <div className="font-medium text-green-600 mb-2 flex items-center gap-1 text-sm">
                              <CheckCircle className="h-4 w-4" />
                              Full Edited Content
                            </div>
                            <div className="bg-green-50 border border-green-200 rounded p-3 text-xs max-h-64 overflow-y-auto">
                              {edit.editedContent}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="border-t p-4 flex-shrink-0 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {selectedEdits.size > 0 ? (
                <span className="flex items-center gap-1 font-medium text-green-700">
                  <CheckCircle className="h-4 w-4" />
                  {selectedEdits.size} change(s) selected
                </span>
              ) : (
                <span className="flex items-center gap-1 text-amber-600">
                  <AlertTriangle className="h-4 w-4" />
                  Select changes to apply
                </span>
              )}
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onRejectChanges}
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                <ThumbsDown className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                onClick={handleApplySelected}
                disabled={selectedEdits.size === 0}
                className="bg-green-600 hover:bg-green-700 text-white px-6"
                size="default"
              >
                <ThumbsUp className="h-4 w-4 mr-2" />
                Apply {selectedEdits.size > 0 ? `${selectedEdits.size} Change(s)` : 'Changes'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
