import { GeneratedSection } from './types';
import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';

// Enhanced helper function to create HTML content for the paper with better formatting
const createPaperHTML = (title: string, authors: string[], generatedSections: GeneratedSection[]): string => {
  const authorsText = authors.length > 0
    ? authors.join(', ')
    : 'Anonymous';

  const sectionsHTML = generatedSections
    .filter(section => section.status === 'completed' && section.content)
    .sort((a, b) => {
      // Enhanced order including references section
      const sectionOrder: Record<string, number> = {
        'abstract': 1,
        'introduction': 2,
        'literature-review': 3,
        'methodology': 4,
        'results': 5,
        'discussion': 6,
        'conclusion': 7,
        'references': 8
      };
      return (sectionOrder[a.id] || 99) - (sectionOrder[b.id] || 99);
    })
    .map(section => {
      let content = section.content || '';

      // Enhanced content processing for different section types
      if (section.id === 'references' || section.name.toLowerCase().includes('reference')) {
        // Special handling for references to preserve formatting
        content = content
          .replace(/<p>/g, '')
          .replace(/<\/p>/g, '\n')
          .replace(/<em>/g, '<i>')
          .replace(/<\/em>/g, '</i>')
          .replace(/<strong>/g, '<b>')
          .replace(/<\/strong>/g, '</b>')
          .split('\n')
          .filter(line => line.trim())
          .map(line => `<p style="margin-bottom: 8px; text-indent: 0; padding-left: 1em; text-indent: -1em;">${line.trim()}</p>`)
          .join('\n');
      } else {
        // Process other sections with proper paragraph handling
        if (content.includes('<p>')) {
          // Content already has paragraph tags
          content = content
            .replace(/\n\n/g, '</p>\n<p>')
            .replace(/\n/g, '<br/>');
        } else {
          // Content without paragraph tags - wrap in paragraphs
          content = content
            .split('\n\n')
            .filter(para => para.trim())
            .map(para => `<p>${para.replace(/\n/g, '<br/>')}</p>`)
            .join('\n');
        }
      }

      return `
        <div class="section">
          <h2>${section.name}</h2>
          <div class="section-content">${content}</div>
        </div>
      `;
    })
    .join('');

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body {
          font-family: 'Times New Roman', Times, serif;
          line-height: 1.6;
          margin: 0;
          padding: 40px;
          max-width: 800px;
          margin: 0 auto;
          background: white;
          color: #333;
        }
        h1 {
          text-align: center;
          font-size: 28px;
          margin-bottom: 15px;
          color: #1a1a1a;
          font-weight: bold;
        }
        .authors {
          text-align: center;
          font-style: italic;
          margin-bottom: 40px;
          font-size: 16px;
          color: #4a4a4a;
        }
        h2 {
          font-size: 20px;
          margin-top: 30px;
          margin-bottom: 15px;
          color: #1a1a1a;
          border-bottom: 2px solid #e5e7eb;
          padding-bottom: 8px;
          font-weight: bold;
        }
        .section {
          margin-bottom: 20px;
          page-break-inside: avoid;
        }
        .section-content {
          text-align: justify;
          line-height: 1.6;
          margin-bottom: 15px;
        }
        .section-content p {
          margin-bottom: 12px;
          text-indent: 1.5em;
        }
        .section-content p:first-child {
          text-indent: 0;
        }
        /* Special styling for references section */
        .section h2:contains("References") + .section-content,
        .section h2:contains("Bibliography") + .section-content {
          text-indent: 0;
        }
        .section h2:contains("References") + .section-content p,
        .section h2:contains("Bibliography") + .section-content p {
          text-indent: 0;
          margin-bottom: 8px;
          padding-left: 1em;
          text-indent: -1em;
        }
        /* Handle line breaks in references */
        .section-content br {
          display: block;
          margin-bottom: 6px;
        }
        /* Print styles */
        @media print {
          body {
            padding: 20px;
            font-size: 12pt;
          }
          h1 {
            font-size: 18pt;
          }
          h2 {
            font-size: 14pt;
            page-break-after: avoid;
          }
          .section {
            page-break-inside: avoid;
          }
        }
        /* Ensure proper spacing for citations and references */
        i, em {
          font-style: italic;
        }
        b, strong {
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <h1>${title}</h1>
      <div class="authors">${authorsText}</div>
      ${sectionsHTML}
    </body>
    </html>
  `;
};

// Legacy export functions (preserved for backward compatibility)
const exportToWord = (title: string, authors: string[], generatedSections: GeneratedSection[]): void => {
  const htmlContent = createPaperHTML(title, authors, generatedSections);

  // Create a Blob with the HTML content
  const blob = new Blob([htmlContent], { type: 'application/msword' });

  // Create a download link and trigger the download
  const a = document.createElement('a');
  const url = URL.createObjectURL(blob);
  a.href = url;
  a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}.doc`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Legacy PDF export (preserved for backward compatibility)
const exportToPDF = (title: string, authors: string[], generatedSections: GeneratedSection[]): void => {
  const htmlContent = createPaperHTML(title, authors, generatedSections);

  // Create a new window and write the HTML content to it
  const printWindow = window.open('', '_blank', 'width=800,height=600');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Give the browser a moment to load the content before printing
    setTimeout(() => {
      printWindow.print();
      // Don't auto-close so user can save as PDF
    }, 500);
  } else {
    alert('Please allow popups for this website to export to PDF. You can also try using the browser\'s print function (Ctrl+P) and select "Save as PDF".');
  }
};

/**
 * Convert markdown to docx document
 *
 * @param title Document title
 * @param content Markdown content
 * @returns Document object
 */
const markdownToDocx = (title: string, content: string): Document => {
  // Parse content to identify headings, paragraphs, etc.
  const lines = content.split('\n');
  const documentChildren = [];

  // Add title
  documentChildren.push(
    new Paragraph({
      children: [
        new TextRun({
          text: title,
          bold: true,
          size: 36,
        }),
      ],
      spacing: { after: 300 },
    })
  );

  // Process content line by line
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (!line) {
      documentChildren.push(new Paragraph({}));
      continue;
    }

    // Handle headings
    if (line.startsWith('# ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          heading: HeadingLevel.HEADING_1,
          spacing: { before: 240, after: 120 }
        })
      );
    } else if (line.startsWith('## ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(3),
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 240, after: 120 }
        })
      );
    } else if (line.startsWith('### ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(4),
          heading: HeadingLevel.HEADING_3,
          spacing: { before: 240, after: 120 }
        })
      );
    }

    // Handle lists - simple implementation
    else if (line.startsWith('- ') || line.startsWith('* ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          bullet: { level: 0 }
        })
      );
    }

    // Handle numbered lists
    else if (/^\d+\.\s/.test(line)) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(line.indexOf('.') + 1).trim(),
          numbering: {
            reference: "1",
            level: 0
          }
        })
      );
    }

    // Handle blockquotes
    else if (line.startsWith('> ')) {
      documentChildren.push(
        new Paragraph({
          text: line.substring(2),
          indent: { left: 400 },
          style: 'Quote'
        })
      );
    }

    // Regular paragraph (check for inline formatting)
    else {
      let text = line;

      // Strip markdown formatting for now
      // For a production app, would need more sophisticated parsing
      text = text.replace(/\*\*(.*?)\*\*/g, '$1');
      text = text.replace(/\*(.*?)\*/g, '$1');

      documentChildren.push(
        new Paragraph({
          text: text
        })
      );
    }
  }

  return new Document({
    sections: [
      {
        properties: {},
        children: documentChildren,
      },
    ],
  });
};

/**
 * Generate and export a DOCX document from markdown content
 *
 * @param title Document title
 * @param content Markdown content
 * @param fileName Output file name
 */
const exportMarkdownToDocx = async (title: string, content: string, fileName: string): Promise<void> => {
  try {
    // Make sure filename has .docx extension
    if (!fileName.toLowerCase().endsWith('.docx')) {
      fileName = `${fileName}.docx`;
    }

    const doc = markdownToDocx(title, content);

    // Generate DOCX blob
    const blob = await Packer.toBlob(doc);
    saveAs(blob, fileName);

    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting to DOCX:", error);
    return Promise.reject(error);
  }
};

/**
 * Export content to PDF file
 *
 * @param title Document title
 * @param content Markdown content
 * @param fileName Output file name
 */
const exportMarkdownToPdf = async (title: string, content: string, fileName: string): Promise<void> => {
  // In a real implementation, you would use a PDF generation library or service
  // For example, using jsPDF or a server-side service

  try {
    // Make sure filename has .pdf extension
    if (!fileName.toLowerCase().endsWith('.pdf')) {
      fileName = `${fileName}.pdf`;
    }

    // Placeholder for PDF generation
    // In a real implementation, this would convert the document to PDF
    console.log('PDF export would be implemented with a PDF generation library');

    // Use the legacy PDF export for now as a fallback
    const generatedSections = [
      {
        id: 'content',
        name: 'Content',
        description: 'Document Content',
        status: 'completed' as const,
        icon: {} as any,
        content: content
      }
    ];

    exportToPDF(title, [], generatedSections);

    // Placeholder for success
    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting to PDF:", error);
    return Promise.reject(error);
  }
};

/**
 * Enhanced export HTML content to DOCX format with proper formatting
 */
const exportToDocx = async (title: string, htmlContent: string, fileName: string): Promise<void> => {
  try {
    // Make sure filename has .docx extension
    if (!fileName.toLowerCase().endsWith('.docx')) {
      fileName = `${fileName}.docx`;
    }

    console.log('DOCX Export - HTML Content:', htmlContent.substring(0, 500) + '...');

    // Parse HTML content to extract structured sections
    const parseHTMLContent = (html: string) => {
      const children: any[] = [];

      // Add title
      children.push(new Paragraph({
        text: title,
        heading: HeadingLevel.TITLE,
        alignment: 'center',
        spacing: { after: 400 }
      }));

      // Parse authors section
      const authorsMatch = html.match(/<div[^>]*class="authors"[^>]*>(.*?)<\/div>/i);
      if (authorsMatch) {
        const authorsText = authorsMatch[1].replace(/<[^>]*>/g, '').trim();
        if (authorsText) {
          children.push(new Paragraph({
            children: [new TextRun({
              text: authorsText,
              italics: true
            })],
            alignment: 'center',
            spacing: { after: 600 }
          }));
        }
      }

      // Parse sections using regex to find section divs
      const sectionRegex = /<div[^>]*class="section"[^>]*>(.*?)<\/div>/gs;
      const sectionMatches = Array.from(html.matchAll(sectionRegex));

      console.log('DOCX Export - Found sections:', sectionMatches.length);

      for (const match of sectionMatches) {
        const sectionContent = match[1];

        // Extract section heading
        const headingMatch = sectionContent.match(/<h2[^>]*>(.*?)<\/h2>/i);
        if (headingMatch) {
          const headingText = headingMatch[1].replace(/<[^>]*>/g, '').trim();
          console.log('DOCX Export - Adding heading:', headingText);

          children.push(new Paragraph({
            text: headingText,
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 }
          }));
        }

        // Extract section content
        let contentToProcess = '';

        // Try to find section-content div first
        const contentMatch = sectionContent.match(/<div[^>]*class="section-content"[^>]*>(.*?)<\/div>/s);
        if (contentMatch) {
          contentToProcess = contentMatch[1];
        } else {
          // Fallback: extract everything after the h2 tag
          contentToProcess = sectionContent.replace(/<h2[^>]*>.*?<\/h2>/i, '').trim();
        }

        if (contentToProcess) {
          console.log('DOCX Export - Processing content:', contentToProcess.substring(0, 100) + '...');

          // Clean up HTML entities and tags
          let cleanContent = contentToProcess
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&apos;/g, "'")
            // Clean up any markdown headers that might have been included
            .replace(/^#{1,6}\s+/gm, '') // Remove markdown headers
            .replace(/^\*\*(.+?)\*\*$/gm, '$1') // Remove bold markdown from standalone lines
            .replace(/^\*(.+?)\*$/gm, '$1'); // Remove italic markdown from standalone lines

          // Handle different content structures
          if (cleanContent.includes('<p>')) {
            // Content has paragraph tags - split by paragraphs
            const paragraphs = cleanContent.split(/<\/p>\s*<p[^>]*>|<\/p>|<p[^>]*>/);

            for (const para of paragraphs) {
              let cleanPara = para
                .replace(/<br\s*\/?>/gi, '\n')
                .replace(/<[^>]*>/g, '') // Remove all HTML tags
                .trim();

              if (cleanPara) {
                // Handle line breaks within paragraphs (for references)
                const lines = cleanPara.split('\n').filter(line => line.trim());

                for (const line of lines) {
                  if (line.trim()) {
                    children.push(new Paragraph({
                      children: [new TextRun(line.trim())],
                      spacing: { after: 200 },
                      alignment: 'both'
                    }));
                  }
                }
              }
            }
          } else {
            // Content without paragraph tags - split by line breaks
            const lines = cleanContent
              .replace(/<br\s*\/?>/gi, '\n')
              .replace(/<[^>]*>/g, '') // Remove all HTML tags
              .split('\n')
              .filter(line => line.trim());

            for (const line of lines) {
              if (line.trim()) {
                children.push(new Paragraph({
                  children: [new TextRun(line.trim())],
                  spacing: { after: 200 },
                  alignment: 'both'
                }));
              }
            }
          }
        }
      }

      console.log('DOCX Export - Total paragraphs created:', children.length);
      return children;
    };

    // Create DOCX document with enhanced formatting
    const doc = new Document({
      sections: [{
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,  // 1 inch
              bottom: 1440, // 1 inch
              left: 1440,   // 1 inch
            },
          },
        },
        children: parseHTMLContent(htmlContent),
      }],
    });

    const blob = await Packer.toBlob(doc);
    saveAs(blob, fileName);

    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting to DOCX:", error);
    return Promise.reject(error);
  }
};

/**
 * Enhanced book export to DOCX with proper book formatting
 */
const exportEnhancedBookToDocx = async (
  content: string,
  metadata: any,
  exportOptions: any,
  fileName: string
): Promise<void> => {
  try {
    if (!fileName.toLowerCase().endsWith('.docx')) {
      fileName = `${fileName}.docx`;
    }

    const doc = createEnhancedBookDocument(content, metadata, exportOptions);
    const blob = await Packer.toBlob(doc);
    saveAs(blob, fileName);

    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting enhanced book to DOCX:", error);
    return Promise.reject(error);
  }
};

/**
 * Enhanced book export to PDF with proper book formatting
 */
const exportEnhancedBookToPdf = async (
  content: string,
  metadata: any,
  exportOptions: any,
  fileName: string
): Promise<void> => {
  try {
    if (!fileName.toLowerCase().endsWith('.pdf')) {
      fileName = `${fileName}.pdf`;
    }

    // Create enhanced HTML for PDF generation
    const htmlContent = createEnhancedBookHTML(content, metadata, exportOptions);

    // Use the existing PDF export with enhanced HTML
    const generatedSections = [
      {
        id: 'book-content',
        name: 'Book Content',
        description: 'Complete Book',
        status: 'completed' as const,
        icon: {} as any,
        content: htmlContent
      }
    ];

    exportToPDF(metadata.title, metadata.authors || [], generatedSections);
    return Promise.resolve();
  } catch (error) {
    console.error("Error exporting enhanced book to PDF:", error);
    return Promise.reject(error);
  }
};

/**
 * Clean content from markdown artifacts and formatting issues
 */
const cleanContentForExport = (content: string): string => {
  return content
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
    .replace(/\*(.*?)\*/g, '$1') // Remove italic markdown
    .replace(/`(.*?)`/g, '$1') // Remove code markdown
    .replace(/_{2,}(.*?)_{2,}/g, '$1') // Remove underline markdown
    .replace(/~~(.*?)~~/g, '$1') // Remove strikethrough markdown
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
    .replace(/[-=]{3,}/g, '') // Remove horizontal rules
    .replace(/\|/g, ' ') // Remove table separators
    .replace(/^\s*[-*+]\s+/gm, '') // Remove bullet points
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered lists
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
};

/**
 * Enhanced content cleaning specifically for DOCX export
 */
const cleanContentForDocx = (content: string): string => {
  return content
    // Preserve line structure but clean formatting
    .replace(/\n{3,}/g, '\n\n') // Reduce multiple line breaks to double
    .replace(/\s*---\s*/g, '\n---\n') // Normalize horizontal rules
    .replace(/^\s+/gm, '') // Remove leading whitespace from lines
    .replace(/\s+$/gm, '') // Remove trailing whitespace from lines
    .trim();
};

/**
 * Create enhanced DOCX document for books
 */
const createEnhancedBookDocument = (content: string, metadata: any, exportOptions: any): Document => {
  // Split content into lines and filter out empty lines for processing
  const lines = content.split('\n');
  const documentChildren = [];

  // Enhanced font configuration for books
  const BOOK_FONT = {
    name: "Times New Roman",
    ascii: "Times New Roman",
    eastAsia: "Times New Roman",
    hAnsi: "Times New Roman"
  };

  // Font size mapping - reduced sizes for better readability
  const getFontSize = (size: string, isHeading: boolean = false): number => {
    const baseSizes = {
      small: isHeading ? 24 : 20,
      medium: isHeading ? 28 : 22,
      large: isHeading ? 32 : 24
    };
    return baseSizes[size as keyof typeof baseSizes] || baseSizes.medium;
  };

  // Line spacing mapping
  const getLineSpacing = (spacing: string): number => {
    const spacingMap = {
      single: 240,
      '1.5': 360,
      double: 480
    };
    return spacingMap[spacing as keyof typeof spacingMap] || spacingMap['1.5'];
  };

  let isInChapter = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip completely empty lines
    if (!line) {
      continue;
    }

    // Title page - main book title
    if (line.startsWith('# ') && !isInChapter && !line.toLowerCase().includes('chapter')) {
      const title = line.replace(/^# /, '').trim();
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: title,
              bold: true,
              size: 36,
              font: BOOK_FONT
            }),
          ],
          spacing: { after: 600, before: 300, line: getLineSpacing(exportOptions.lineSpacing || '1.5') },
          alignment: 'center'
        })
      );
    }
    // Chapter headings
    else if (line.startsWith('# Chapter') || (line.startsWith('# ') && line.toLowerCase().includes('chapter'))) {
      isInChapter = true;
      const chapterTitle = line.replace(/^# /, '').trim();
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: chapterTitle,
              bold: true,
              size: 28,
              font: BOOK_FONT
            }),
          ],
          spacing: { after: 400, before: 600, line: getLineSpacing(exportOptions.lineSpacing || '1.5') },
          pageBreakBefore: documentChildren.length > 0 // Only add page break if not the first element
        })
      );
    }
    // Section headings (##)
    else if (line.startsWith('## ')) {
      const sectionTitle = line.replace(/^## /, '').trim();
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: sectionTitle,
              bold: true,
              size: 24,
              font: BOOK_FONT
            }),
          ],
          spacing: { after: 300, before: 400, line: getLineSpacing(exportOptions.lineSpacing || '1.5') }
        })
      );
    }
    // Subsection headings (###)
    else if (line.startsWith('### ')) {
      const subsectionTitle = line.replace(/^### /, '').trim();
      documentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: subsectionTitle,
              bold: true,
              size: 22,
              font: BOOK_FONT
            }),
          ],
          spacing: { after: 200, before: 300, line: getLineSpacing(exportOptions.lineSpacing || '1.5') }
        })
      );
    }
    // Horizontal rules (skip them)
    else if (line.startsWith('---')) {
      continue;
    }
    // Regular paragraphs
    else {
      // Process the text to handle markdown formatting properly
      let processedText = line.trim();

      // Skip if empty after trimming
      if (!processedText) continue;

      // Create text runs for different formatting
      const textRuns = [];

      // For now, create a simple text run without bold formatting for regular paragraphs
      // This fixes the issue where all text was appearing bold
      textRuns.push(
        new TextRun({
          text: processedText
            .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown but keep text
            .replace(/\*(.*?)\*/g, '$1') // Remove italic markdown but keep text
            .replace(/`(.*?)`/g, '$1') // Remove code markdown but keep text
            .replace(/_{2,}(.*?)_{2,}/g, '$1') // Remove underline markdown but keep text
            .replace(/~~(.*?)~~/g, '$1') // Remove strikethrough markdown but keep text
            .replace(/\[(.*?)\]\(.*?\)/g, '$1'), // Remove links, keep text
          font: BOOK_FONT,
          size: getFontSize(exportOptions.fontSize || 'medium', false), // false = not a heading
          bold: false // Explicitly set to false for regular text
        })
      );

      documentChildren.push(
        new Paragraph({
          children: textRuns,
          spacing: {
            after: 240, // Increased spacing between paragraphs
            before: 120,
            line: getLineSpacing(exportOptions.lineSpacing || '1.5')
          },
          alignment: 'left' // Explicitly set alignment
        })
      );
    }
  }

  return new Document({
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,  // 1 inch
              bottom: 1440, // 1 inch
              left: 1440,   // 1 inch
            },
          },
        },
        children: documentChildren,
      },
    ],
  });
};

/**
 * Create enhanced HTML for book PDF export
 */
const createEnhancedBookHTML = (content: string, metadata: any, exportOptions: any): string => {
  const lines = content.split('\n');
  let htmlContent = '';
  let isInChapter = false;

  for (const line of lines) {
    const trimmed = line.trim();

    if (!trimmed) {
      htmlContent += '<br>';
      continue;
    }

    // Title page
    if (trimmed.startsWith('# ') && !isInChapter && !trimmed.includes('Chapter')) {
      const title = trimmed.replace(/^# /, '');
      htmlContent += `<h1 style="text-align: center; font-size: 36pt; margin-bottom: 24pt; page-break-after: always;">${title}</h1>`;
    }
    // Chapter headings
    else if (trimmed.startsWith('# Chapter') || (trimmed.startsWith('# ') && trimmed.toLowerCase().includes('chapter'))) {
      isInChapter = true;
      const chapterTitle = trimmed.replace(/^# /, '');
      htmlContent += `<h1 style="page-break-before: always; font-size: 28pt; margin-top: 48pt; margin-bottom: 24pt; text-align: left;">${chapterTitle}</h1>`;
    }
    // Section headings
    else if (trimmed.startsWith('## ')) {
      const sectionTitle = trimmed.replace(/^## /, '');
      htmlContent += `<h2 style="font-size: 22pt; margin-top: 24pt; margin-bottom: 16pt;">${sectionTitle}</h2>`;
    }
    // Subsection headings
    else if (trimmed.startsWith('### ')) {
      const subsectionTitle = trimmed.replace(/^### /, '');
      htmlContent += `<h3 style="font-size: 18pt; margin-top: 20pt; margin-bottom: 12pt;">${subsectionTitle}</h3>`;
    }
    // Horizontal rules (skip them)
    else if (trimmed.startsWith('---')) {
      continue;
    }
    // Regular paragraphs
    else {
      let text = trimmed;
      // Handle bold formatting
      text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

      htmlContent += `<p style="margin-bottom: 12pt; text-align: justify; line-height: 1.6;">${text}</p>`;
    }
  }

  return htmlContent;
};

// Export the service
export const documentExportService = {
  // Legacy exports (preserved for backward compatibility)
  exportToWord,
  exportToPDF,

  // Enhanced exports for HTML/markdown documents
  exportToDocx,
  exportMarkdownToDocx,
  exportMarkdownToPdf,

  // Enhanced book exports
  exportEnhancedBookToDocx,
  exportEnhancedBookToPdf
};
