# 🚀 Quick Deployment Guide for verbira.com

## ✅ Pre-Deployment Status
- [x] Build configuration optimized
- [x] API server configured for Vercel serverless functions
- [x] Environment variables template ready
- [x] Production build tested successfully

## 🔧 Current Environment Variables Needed

Based on your existing setup, you'll need these environment variables in Vercel:

```bash
# AI API Keys
VITE_OPENROUTER_API_KEY=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
OPENROUTER_API_KEY=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

# Supabase Configuration
VITE_SUPABASE_URL=https://swsnqpavwcnqiihsidss.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.cOSvLGmaJx1y9PutRST81mTiCNXnVTRO8kHWii9CFfg

# Tavily API for research search
VITE_TAVILY_API_KEY=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

# Production Settings
NODE_ENV=production
VITE_NODE_ENV=production
```

## 📦 Deployment Options

### Option 1: Vercel CLI (Recommended)
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy from project root
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? Your account
# - Link to existing project? No (create new)
# - Project name? verbira-genius or similar
# - Directory? ./ (current directory)
# - Override settings? No
```

### Option 2: GitHub Integration
1. Push your code to GitHub repository
2. Go to [vercel.com](https://vercel.com)
3. Click "Import Project"
4. Connect your GitHub repository
5. Configure environment variables
6. Deploy

## 🔧 Post-Deployment Configuration

### 1. Add Custom Domain in Vercel
1. Go to Vercel Dashboard → Your Project → Settings → Domains
2. Add `verbira.com` as custom domain
3. Configure DNS records as instructed by Vercel

### 2. Update Supabase Settings
Run these commands to update Supabase configuration:

```bash
# We'll use the Supabase tools to update these settings
```

### 3. Update Google OAuth
- Add `https://verbira.com/auth/callback` to Google Cloud Console
- Update OAuth redirect URIs

## 🧪 Testing Checklist
After deployment:
- [ ] Homepage loads at verbira.com
- [ ] User registration works
- [ ] Google OAuth login works
- [ ] AI paper generation works
- [ ] Research search works
- [ ] File uploads work

## 🚨 Important Notes
- Your existing app on verbira.com will be replaced
- Make sure to backup any important data from the current app
- DNS changes may take up to 24 hours to propagate
- Test thoroughly before announcing the new app

## 🔄 Rollback Plan
If something goes wrong:
1. Revert DNS settings in Vercel
2. Restore previous app deployment
3. Update Supabase settings back to previous URLs

Ready to deploy? Let's start with Option 1 (Vercel CLI) or Option 2 (GitHub)!
