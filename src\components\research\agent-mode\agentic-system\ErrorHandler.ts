/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON>ust error handling and recovery for the agentic system
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentExecutionResult, EditResult } from './types';

export interface ErrorRecoveryStrategy {
  id: string;
  name: string;
  description: string;
  canRecover: (error: AgentError) => boolean;
  recover: (error: AgentError, context: any) => Promise<RecoveryResult>;
}

export interface AgentError {
  type: 'tool_failure' | 'network_error' | 'validation_error' | 'timeout' | 'unknown';
  message: string;
  toolId?: string;
  originalError?: Error;
  context?: any;
  recoverable: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface RecoveryResult {
  success: boolean;
  fallbackResult?: ToolResult | AgentExecutionResult;
  message: string;
  retryRecommended?: boolean;
  alternativeApproach?: string;
}

export class ErrorHandler {
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];
  private errorHistory: AgentError[] = [];
  private maxHistorySize = 100;

  constructor() {
    this.initializeRecoveryStrategies();
  }

  /**
   * Handle an error with automatic recovery attempts
   */
  async handleError(
    error: Error | AgentError,
    context?: any
  ): Promise<RecoveryResult> {
    const agentError = this.normalizeError(error, context);
    this.recordError(agentError);

    console.error(`🚨 [ErrorHandler] Handling error:`, {
      type: agentError.type,
      message: agentError.message,
      toolId: agentError.toolId,
      severity: agentError.severity
    });

    // Try recovery strategies
    if (agentError.recoverable) {
      for (const strategy of this.recoveryStrategies) {
        if (strategy.canRecover(agentError)) {
          console.log(`🔄 [ErrorHandler] Attempting recovery with: ${strategy.name}`);
          
          try {
            const recoveryResult = await strategy.recover(agentError, context);
            
            if (recoveryResult.success) {
              console.log(`✅ [ErrorHandler] Recovery successful: ${recoveryResult.message}`);
              return recoveryResult;
            } else {
              console.warn(`⚠️ [ErrorHandler] Recovery failed: ${recoveryResult.message}`);
            }
          } catch (recoveryError: any) {
            console.error(`❌ [ErrorHandler] Recovery strategy failed:`, recoveryError);
          }
        }
      }
    }

    // No recovery possible
    return {
      success: false,
      message: this.generateUserFriendlyMessage(agentError),
      retryRecommended: this.shouldRecommendRetry(agentError),
      alternativeApproach: this.suggestAlternativeApproach(agentError)
    };
  }

  /**
   * Create a graceful degradation result
   */
  createGracefulDegradation(
    originalRequest: string,
    partialResults: ToolResult[],
    errors: AgentError[]
  ): AgentExecutionResult {
    const successfulResults = partialResults.filter(r => r.success);
    const changes: EditResult[] = [];

    // Extract any successful edits
    successfulResults.forEach(result => {
      if (result.data?.edits) {
        changes.push(...result.data.edits);
      }
    });

    const warnings = errors.map(e => e.message);
    const confidence = successfulResults.length > 0 ? 0.5 : 0;

    return {
      success: changes.length > 0,
      changes,
      summary: changes.length > 0 
        ? `Partial success: Applied ${changes.length} change(s) despite some errors`
        : 'Unable to complete request due to errors',
      reasoning: `Encountered ${errors.length} error(s) during execution. ${successfulResults.length} tool(s) completed successfully.`,
      confidence,
      executionTime: 0,
      toolsUsed: successfulResults.map(r => r.toolId),
      warnings,
      error: errors.length > 0 ? errors[0].message : undefined
    };
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: AgentError[];
    recoveryRate: number;
  } {
    const errorsByType: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};
    let recoveredErrors = 0;

    this.errorHistory.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
      
      if (error.recoverable) {
        recoveredErrors++;
      }
    });

    return {
      totalErrors: this.errorHistory.length,
      errorsByType,
      errorsBySeverity,
      recentErrors: this.errorHistory.slice(-10),
      recoveryRate: this.errorHistory.length > 0 ? recoveredErrors / this.errorHistory.length : 0
    };
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    console.log('🧹 Error history cleared');
  }

  /**
   * Initialize recovery strategies
   */
  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies = [
      {
        id: 'network_retry',
        name: 'Network Retry',
        description: 'Retry network requests with exponential backoff',
        canRecover: (error) => error.type === 'network_error',
        recover: async (error, context) => {
          // Implement retry logic
          return {
            success: false,
            message: 'Network retry not implemented yet',
            retryRecommended: true
          };
        }
      },
      {
        id: 'fallback_tool',
        name: 'Fallback Tool',
        description: 'Use alternative tools when primary tools fail',
        canRecover: (error) => error.type === 'tool_failure' && error.toolId !== 'search-tool',
        recover: async (error, context) => {
          // Implement fallback tool logic
          return {
            success: false,
            message: 'Fallback tool strategy not implemented yet',
            alternativeApproach: 'Try using a simpler request or different approach'
          };
        }
      },
      {
        id: 'partial_execution',
        name: 'Partial Execution',
        description: 'Continue with partial results when some tools fail',
        canRecover: (error) => error.severity !== 'critical',
        recover: async (error, context) => {
          return {
            success: true,
            message: 'Continuing with partial results',
            fallbackResult: this.createPartialResult(context)
          };
        }
      },
      {
        id: 'simplified_request',
        name: 'Simplified Request',
        description: 'Break down complex requests into simpler parts',
        canRecover: (error) => error.type === 'validation_error',
        recover: async (error, context) => {
          return {
            success: false,
            message: 'Request too complex, try breaking it down',
            alternativeApproach: 'Try making smaller, more specific requests'
          };
        }
      }
    ];

    console.log(`🛡️ Initialized ${this.recoveryStrategies.length} recovery strategies`);
  }

  /**
   * Normalize different error types to AgentError
   */
  private normalizeError(error: Error | AgentError, context?: any): AgentError {
    if ('type' in error && 'recoverable' in error) {
      return error as AgentError;
    }

    const originalError = error as Error;
    let type: AgentError['type'] = 'unknown';
    let severity: AgentError['severity'] = 'medium';
    let recoverable = true;

    // Classify error based on message
    const message = originalError.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      type = 'network_error';
      severity = 'medium';
      recoverable = true;
    } else if (message.includes('validation') || message.includes('invalid')) {
      type = 'validation_error';
      severity = 'low';
      recoverable = true;
    } else if (message.includes('tool') || message.includes('execution')) {
      type = 'tool_failure';
      severity = 'high';
      recoverable = true;
    } else if (message.includes('critical') || message.includes('fatal')) {
      type = 'unknown';
      severity = 'critical';
      recoverable = false;
    }

    return {
      type,
      message: originalError.message,
      originalError,
      context,
      recoverable,
      severity
    };
  }

  /**
   * Record error in history
   */
  private recordError(error: AgentError): void {
    this.errorHistory.push(error);
    
    // Maintain history size limit
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserFriendlyMessage(error: AgentError): string {
    const messages = {
      'tool_failure': 'One of the AI tools encountered an issue. Please try again or use a simpler request.',
      'network_error': 'Network connection issue. Please check your internet connection and try again.',
      'validation_error': 'The request could not be processed. Please check your input and try again.',
      'timeout': 'The operation took too long to complete. Please try a simpler request.',
      'unknown': 'An unexpected error occurred. Please try again or contact support.'
    };

    return messages[error.type] || messages['unknown'];
  }

  /**
   * Determine if retry should be recommended
   */
  private shouldRecommendRetry(error: AgentError): boolean {
    const retryableTypes = ['network_error', 'timeout'];
    return retryableTypes.includes(error.type) && error.severity !== 'critical';
  }

  /**
   * Suggest alternative approach
   */
  private suggestAlternativeApproach(error: AgentError): string {
    const suggestions = {
      'tool_failure': 'Try using the simple agent mode or break down your request into smaller parts',
      'network_error': 'Check your internet connection and try again in a few moments',
      'validation_error': 'Make your request more specific and ensure the document has relevant content',
      'timeout': 'Try a shorter document or a more focused request',
      'unknown': 'Try restarting the application or using a different approach'
    };

    return suggestions[error.type] || suggestions['unknown'];
  }

  /**
   * Create partial result for recovery
   */
  private createPartialResult(context: any): ToolResult {
    return {
      toolId: 'error-handler',
      success: true,
      data: { message: 'Partial execution completed' },
      reasoning: 'Continuing with available results despite some errors',
      confidence: 0.5,
      executionTime: 0,
      warnings: ['Some operations failed but partial results are available']
    };
  }
}

// Create singleton instance
export const errorHandler = new ErrorHandler();
