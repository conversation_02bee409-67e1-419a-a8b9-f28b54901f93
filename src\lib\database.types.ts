export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          university: string | null
          research_field: string | null
          academic_level: 'undergraduate' | 'graduate' | 'phd' | 'postdoc' | 'professor' | 'researcher' | 'other' | null
          subscription_tier: 'free' | 'pro' | 'enterprise'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          university?: string | null
          research_field?: string | null
          academic_level?: 'undergraduate' | 'graduate' | 'phd' | 'postdoc' | 'professor' | 'researcher' | 'other' | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          university?: string | null
          research_field?: string | null
          academic_level?: 'undergraduate' | 'graduate' | 'phd' | 'postdoc' | 'professor' | 'researcher' | 'other' | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string
          document_type: string
          metadata: Json
          status: 'draft' | 'in_progress' | 'completed' | 'published'
          tags: string[]
          word_count: number
          character_count: number
          last_edited_at: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title?: string
          content?: string
          document_type?: string
          metadata?: Json
          status?: 'draft' | 'in_progress' | 'completed' | 'published'
          tags?: string[]
          word_count?: number
          character_count?: number
          last_edited_at?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string
          document_type?: string
          metadata?: Json
          status?: 'draft' | 'in_progress' | 'completed' | 'published'
          tags?: string[]
          word_count?: number
          character_count?: number
          last_edited_at?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      document_versions: {
        Row: {
          id: string
          document_id: string
          version_number: number
          title: string
          content: string
          changes_description: string | null
          word_count: number
          created_at: string
          created_by: string | null
        }
        Insert: {
          id?: string
          document_id: string
          version_number: number
          title: string
          content: string
          changes_description?: string | null
          word_count?: number
          created_at?: string
          created_by?: string | null
        }
        Update: {
          id?: string
          document_id?: string
          version_number?: number
          title?: string
          content?: string
          changes_description?: string | null
          word_count?: number
          created_at?: string
          created_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_versions_document_id_fkey"
            columns: ["document_id"]
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_versions_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_documents: {
        Row: {
          id: string
          user_id: string
          title: string
          content: string | null
          document_type: 'paper' | 'book' | 'article_review' | 'research_analysis'
          status: 'draft' | 'generating' | 'completed' | 'published'
          metadata: Json
          word_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          content?: string | null
          document_type: 'paper' | 'book' | 'article_review' | 'research_analysis'
          status?: 'draft' | 'generating' | 'completed' | 'published'
          metadata?: Json
          word_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string | null
          document_type?: 'paper' | 'book' | 'article_review' | 'research_analysis'
          status?: 'draft' | 'generating' | 'completed' | 'published'
          metadata?: Json
          word_count?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_documents_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_books: {
        Row: {
          id: string
          user_id: string
          title: string
          subtitle: string | null
          description: string | null
          genre: string
          target_audience: string
          authors: string[]
          keywords: string[]
          estimated_length: string
          tone: string
          chapter_count: number
          total_word_count: number
          generation_status: 'draft' | 'generating' | 'completed' | 'error'
          outline_model: string | null
          content_model: string | null
          generation_started_at: string | null
          generation_completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          subtitle?: string | null
          description?: string | null
          genre: string
          target_audience: string
          authors?: string[]
          keywords?: string[]
          estimated_length?: string
          tone?: string
          chapter_count?: number
          total_word_count?: number
          generation_status?: 'draft' | 'generating' | 'completed' | 'error'
          outline_model?: string | null
          content_model?: string | null
          generation_started_at?: string | null
          generation_completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          subtitle?: string | null
          description?: string | null
          genre?: string
          target_audience?: string
          authors?: string[]
          keywords?: string[]
          estimated_length?: string
          tone?: string
          chapter_count?: number
          total_word_count?: number
          generation_status?: 'draft' | 'generating' | 'completed' | 'error'
          outline_model?: string | null
          content_model?: string | null
          generation_started_at?: string | null
          generation_completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_books_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      book_chapters: {
        Row: {
          id: string
          book_id: string
          chapter_number: number
          title: string
          description: string | null
          content: string | null
          word_count: number
          status: 'pending' | 'generating' | 'completed' | 'error'
          order_index: number
          outline: Json | null
          citations: Json
          generation_metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          chapter_number: number
          title: string
          description?: string | null
          content?: string | null
          word_count?: number
          status?: 'pending' | 'generating' | 'completed' | 'error'
          order_index: number
          outline?: Json | null
          citations?: Json
          generation_metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          chapter_number?: number
          title?: string
          description?: string | null
          content?: string | null
          word_count?: number
          status?: 'pending' | 'generating' | 'completed' | 'error'
          order_index?: number
          outline?: Json | null
          citations?: Json
          generation_metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "book_chapters_book_id_fkey"
            columns: ["book_id"]
            referencedRelation: "user_books"
            referencedColumns: ["id"]
          }
        ]
      }
      book_sections: {
        Row: {
          id: string
          book_id: string
          section_type: string
          title: string
          content: string | null
          word_count: number
          order_index: number
          is_generated: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          section_type: string
          title: string
          content?: string | null
          word_count?: number
          order_index: number
          is_generated?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          section_type?: string
          title?: string
          content?: string | null
          word_count?: number
          order_index?: number
          is_generated?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "book_sections_book_id_fkey"
            columns: ["book_id"]
            referencedRelation: "user_books"
            referencedColumns: ["id"]
          }
        ]
      }
      book_exports: {
        Row: {
          id: string
          book_id: string
          export_format: string
          file_name: string
          file_path: string | null
          file_size: number | null
          export_options: Json
          download_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          export_format: string
          file_name: string
          file_path?: string | null
          file_size?: number | null
          export_options?: Json
          download_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          export_format?: string
          file_name?: string
          file_path?: string | null
          file_size?: number | null
          export_options?: Json
          download_count?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "book_exports_book_id_fkey"
            columns: ["book_id"]
            referencedRelation: "user_books"
            referencedColumns: ["id"]
          }
        ]
      }
      book_activity: {
        Row: {
          id: string
          book_id: string
          user_id: string
          activity_type: string
          activity_details: Json
          created_at: string
        }
        Insert: {
          id?: string
          book_id: string
          user_id: string
          activity_type: string
          activity_details?: Json
          created_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          user_id?: string
          activity_type?: string
          activity_details?: Json
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "book_activity_book_id_fkey"
            columns: ["book_id"]
            referencedRelation: "user_books"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_activity_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
        Update: {
          id?: string
          user_id?: string
          title?: string
          content?: string | null
          document_type?: 'paper' | 'book' | 'article_review' | 'research_analysis'
          status?: 'draft' | 'generating' | 'completed' | 'published'
          metadata?: Json
          word_count?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      user_usage: {
        Row: {
          id: string
          user_id: string
          month_year: string
          papers_generated: number
          books_generated: number
          articles_reviewed: number
          ai_requests: number
          total_words_generated: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          month_year: string
          papers_generated?: number
          books_generated?: number
          articles_reviewed?: number
          ai_requests?: number
          total_words_generated?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          month_year?: string
          papers_generated?: number
          books_generated?: number
          articles_reviewed?: number
          ai_requests?: number
          total_words_generated?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      paper_generations: {
        Row: {
          id: string
          user_id: string
          title: string
          research_field: string | null
          keywords: string[]
          authors: string[]
          ai_model: string
          status: 'draft' | 'generating' | 'completed' | 'error'
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          research_field?: string | null
          keywords?: string[]
          authors?: string[]
          ai_model: string
          status?: 'draft' | 'generating' | 'completed' | 'error'
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          research_field?: string | null
          keywords?: string[]
          authors?: string[]
          ai_model?: string
          status?: 'draft' | 'generating' | 'completed' | 'error'
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "paper_generations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      paper_sections: {
        Row: {
          id: string
          paper_generation_id: string
          section_id: string
          section_name: string
          prompt_text: string
          generated_content: string | null
          status: 'pending' | 'generating' | 'completed' | 'error'
          generation_order: number
          ai_model: string
          generation_metadata: Json
          error_message: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          paper_generation_id: string
          section_id: string
          section_name: string
          prompt_text: string
          generated_content?: string | null
          status?: 'pending' | 'generating' | 'completed' | 'error'
          generation_order?: number
          ai_model: string
          generation_metadata?: Json
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          paper_generation_id?: string
          section_id?: string
          section_name?: string
          prompt_text?: string
          generated_content?: string | null
          status?: 'pending' | 'generating' | 'completed' | 'error'
          generation_order?: number
          ai_model?: string
          generation_metadata?: Json
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "paper_sections_paper_generation_id_fkey"
            columns: ["paper_generation_id"]
            isOneToOne: false
            referencedRelation: "paper_generations"
            referencedColumns: ["id"]
          }
        ]
      }
      paper_citations: {
        Row: {
          id: string
          paper_generation_id: string
          section_id: string | null
          citation_id: string
          in_text_format: string
          authors: string[]
          year: number
          reference_text: string | null
          section_ids: string[]
          created_at: string
        }
        Insert: {
          id?: string
          paper_generation_id: string
          section_id?: string | null
          citation_id: string
          in_text_format: string
          authors: string[]
          year: number
          reference_text?: string | null
          section_ids?: string[]
          created_at?: string
        }
        Update: {
          id?: string
          paper_generation_id?: string
          section_id?: string | null
          citation_id?: string
          in_text_format?: string
          authors?: string[]
          year?: number
          reference_text?: string | null
          section_ids?: string[]
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "paper_citations_paper_generation_id_fkey"
            columns: ["paper_generation_id"]
            isOneToOne: false
            referencedRelation: "paper_generations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "paper_citations_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "paper_sections"
            referencedColumns: ["id"]
          }
        ]
      }
      research_documents: {
        Row: {
          id: string
          user_id: string
          title: string
          authors: string[]
          abstract: string | null
          publication_year: number | null
          journal: string | null
          doi: string | null
          keywords: string[]
          filename: string
          file_size: number
          file_path: string | null
          file_type: string
          status: 'processing' | 'ready' | 'error'
          processing_progress: number | null
          raw_content: string | null
          summary: string | null
          key_findings: string[]
          methodology: string[]
          limitations: string[]
          future_work: string[]
          tags: string[]
          categories: string[]
          favorite: boolean
          notes: string | null
          ai_model_used: string | null
          analysis_confidence: number | null
          extraction_metadata: Json
          uploaded_at: string
          last_modified: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          authors?: string[]
          abstract?: string | null
          publication_year?: number | null
          journal?: string | null
          doi?: string | null
          keywords?: string[]
          filename: string
          file_size: number
          file_path?: string | null
          file_type: string
          status?: 'processing' | 'ready' | 'error'
          processing_progress?: number | null
          raw_content?: string | null
          summary?: string | null
          key_findings?: string[]
          methodology?: string[]
          limitations?: string[]
          future_work?: string[]
          tags?: string[]
          categories?: string[]
          favorite?: boolean
          notes?: string | null
          ai_model_used?: string | null
          analysis_confidence?: number | null
          extraction_metadata?: Json
          uploaded_at?: string
          last_modified?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          authors?: string[]
          abstract?: string | null
          publication_year?: number | null
          journal?: string | null
          doi?: string | null
          keywords?: string[]
          filename?: string
          file_size?: number
          file_path?: string | null
          file_type?: string
          status?: 'processing' | 'ready' | 'error'
          processing_progress?: number | null
          raw_content?: string | null
          summary?: string | null
          key_findings?: string[]
          methodology?: string[]
          limitations?: string[]
          future_work?: string[]
          tags?: string[]
          categories?: string[]
          favorite?: boolean
          notes?: string | null
          ai_model_used?: string | null
          analysis_confidence?: number | null
          extraction_metadata?: Json
          uploaded_at?: string
          last_modified?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "research_documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      literature_reviews: {
        Row: {
          id: string
          user_id: string
          title: string
          topic: string | null
          document_ids: string[]
          citation_style: 'APA' | 'MLA' | 'Chicago' | 'Harvard' | 'IEEE' | 'Vancouver'
          status: 'draft' | 'generating' | 'completed' | 'error'
          word_count: number | null
          ai_model_used: string | null
          generation_metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          topic?: string | null
          document_ids?: string[]
          citation_style?: 'APA' | 'MLA' | 'Chicago' | 'Harvard' | 'IEEE' | 'Vancouver'
          status?: 'draft' | 'generating' | 'completed' | 'error'
          word_count?: number | null
          ai_model_used?: string | null
          generation_metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          topic?: string | null
          document_ids?: string[]
          citation_style?: 'APA' | 'MLA' | 'Chicago' | 'Harvard' | 'IEEE' | 'Vancouver'
          status?: 'draft' | 'generating' | 'completed' | 'error'
          word_count?: number | null
          ai_model_used?: string | null
          generation_metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "literature_reviews_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      literature_review_sections: {
        Row: {
          id: string
          review_id: string
          title: string
          content: string
          section_type: 'introduction' | 'chronological' | 'thematic' | 'methodological' | 'gaps' | 'conclusion'
          section_order: number
          source_document_ids: string[]
          citations: Json
          created_at: string
        }
        Insert: {
          id?: string
          review_id: string
          title: string
          content: string
          section_type: 'introduction' | 'chronological' | 'thematic' | 'methodological' | 'gaps' | 'conclusion'
          section_order?: number
          source_document_ids?: string[]
          citations?: Json
          created_at?: string
        }
        Update: {
          id?: string
          review_id?: string
          title?: string
          content?: string
          section_type?: 'introduction' | 'chronological' | 'thematic' | 'methodological' | 'gaps' | 'conclusion'
          section_order?: number
          source_document_ids?: string[]
          citations?: Json
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "literature_review_sections_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: false
            referencedRelation: "literature_reviews"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
