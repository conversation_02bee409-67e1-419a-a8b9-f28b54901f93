/**
 * Figure Preview Component
 * Interactive preview with zoom, metadata, and analysis status
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  ZoomIn, ZoomOut, RotateCw, Download, Share2, 
  Info, Eye, EyeOff, Maximize2, Minimize2 
} from 'lucide-react';
import { FigureFile, FigureAnalysisResult } from '../types';
import { UI_CONFIG } from '../constants';

interface FigurePreviewProps {
  figure: FigureFile;
  analysisResult?: FigureAnalysisResult;
  onAnalyze?: () => void;
  className?: string;
}

export const FigurePreview: React.FC<FigurePreviewProps> = ({
  figure,
  analysisResult,
  onAnalyze,
  className = '',
}) => {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [showMetadata, setShowMetadata] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  /**
   * Handle zoom controls
   */
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 5));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.1));
  };

  const handleZoomReset = () => {
    setZoom(1);
    setRotation(0);
  };

  /**
   * Handle rotation
   */
  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  /**
   * Handle fullscreen toggle
   */
  const toggleFullscreen = () => {
    if (!isFullscreen && containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else if (document.exitFullscreen) {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };

  /**
   * Handle download
   */
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = figure.url;
    link.download = figure.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  /**
   * Handle share
   */
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Figure: ${figure.name}`,
          text: `Check out this figure analysis`,
          url: figure.url,
        });
      } catch (error) {
        console.log('Share cancelled');
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(figure.url);
      // TODO: Show toast notification
    }
  };

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Get quality grade color
   */
  const getQualityColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100';
      case 'B': return 'text-blue-600 bg-blue-100';
      case 'C': return 'text-yellow-600 bg-yellow-100';
      case 'D': return 'text-orange-600 bg-orange-100';
      case 'F': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  /**
   * Render file content based on type
   */
  const renderFileContent = () => {
    if (figure.type.startsWith('image/')) {
      return (
        <img
          ref={imageRef}
          src={figure.url}
          alt={figure.name}
          className={`max-w-full max-h-full object-contain transition-transform duration-300 ${
            imageError ? 'hidden' : ''
          }`}
          style={{
            transform: `scale(${zoom}) rotate(${rotation}deg)`,
            transformOrigin: 'center',
          }}
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageError(true)}
        />
      );
    } else if (figure.type === 'application/pdf') {
      return (
        <div className="flex items-center justify-center h-full bg-gray-100 rounded">
          <div className="text-center">
            <div className="text-6xl mb-4">📄</div>
            <p className="text-gray-600">PDF Preview</p>
            <p className="text-sm text-gray-500 mt-2">
              Click download to view full PDF
            </p>
          </div>
        </div>
      );
    } else if (figure.type === 'image/svg+xml') {
      return (
        <div 
          className="w-full h-full flex items-center justify-center"
          style={{
            transform: `scale(${zoom}) rotate(${rotation}deg)`,
            transformOrigin: 'center',
          }}
        >
          <object
            data={figure.url}
            type="image/svg+xml"
            className="max-w-full max-h-full"
          >
            <div className="text-center">
              <div className="text-6xl mb-4">🖼️</div>
              <p className="text-gray-600">SVG Preview</p>
            </div>
          </object>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded">
        <div className="text-center">
          <div className="text-6xl mb-4">📁</div>
          <p className="text-gray-600">Unsupported Preview</p>
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900 truncate">
              {figure.name}
            </h3>
            
            {/* Quality Badge */}
            {analysisResult?.qualityAssessment && (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                getQualityColor(analysisResult.qualityAssessment.overall.grade)
              }`}>
                Grade {analysisResult.qualityAssessment.overall.grade}
              </span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowMetadata(!showMetadata)}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="Toggle metadata"
            >
              <Info className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleShare}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="Share"
            >
              <Share2 className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleDownload}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="Download"
            >
              <Download className="w-4 h-4" />
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
              title="Fullscreen"
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Metadata Panel */}
      {showMetadata && (
        <div className="bg-blue-50 px-4 py-3 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Size:</span>
              <p className="text-gray-600">{formatFileSize(figure.size)}</p>
            </div>
            
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <p className="text-gray-600">{figure.type}</p>
            </div>
            
            {figure.metadata?.width && (
              <div>
                <span className="font-medium text-gray-700">Dimensions:</span>
                <p className="text-gray-600">
                  {figure.metadata.width} × {figure.metadata.height}
                </p>
              </div>
            )}
            
            <div>
              <span className="font-medium text-gray-700">Uploaded:</span>
              <p className="text-gray-600">
                {figure.uploadedAt.toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Analysis Status */}
          {analysisResult && (
            <div className="mt-3 pt-3 border-t border-blue-200">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-700">Analysis Status:</span>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-green-600">
                    ✓ Complete ({analysisResult.processingTime}ms)
                  </span>
                  <span className="text-sm text-gray-600">
                    Confidence: {analysisResult.confidence.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Preview Area */}
      <div 
        ref={containerRef}
        className={`relative bg-gray-50 ${isFullscreen ? 'h-screen' : 'h-96'} overflow-hidden`}
      >
        {/* Zoom Controls */}
        <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-md border border-gray-200">
          <div className="flex items-center">
            <button
              onClick={handleZoomOut}
              className="p-2 hover:bg-gray-100 transition-colors border-r border-gray-200"
              title="Zoom out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            
            <span className="px-3 py-2 text-sm font-medium min-w-[60px] text-center">
              {Math.round(zoom * 100)}%
            </span>
            
            <button
              onClick={handleZoomIn}
              className="p-2 hover:bg-gray-100 transition-colors border-l border-gray-200"
              title="Zoom in"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Additional Controls */}
        <div className="absolute top-4 right-4 z-10 bg-white rounded-lg shadow-md border border-gray-200">
          <div className="flex items-center">
            <button
              onClick={handleRotate}
              className="p-2 hover:bg-gray-100 transition-colors border-r border-gray-200"
              title="Rotate"
            >
              <RotateCw className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleZoomReset}
              className="p-2 hover:bg-gray-100 transition-colors text-sm font-medium"
              title="Reset view"
            >
              Reset
            </button>
          </div>
        </div>

        {/* File Content */}
        <div className="w-full h-full flex items-center justify-center p-4">
          {imageError ? (
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <p className="text-gray-600">Failed to load image</p>
              <button
                onClick={() => {
                  setImageError(false);
                  setImageLoaded(false);
                }}
                className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
              >
                Try again
              </button>
            </div>
          ) : (
            renderFileContent()
          )}
        </div>

        {/* Loading Overlay */}
        {!imageLoaded && !imageError && figure.type.startsWith('image/') && (
          <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-sm text-gray-600">Loading image...</span>
            </div>
          </div>
        )}
      </div>

      {/* Analysis Action */}
      {!analysisResult && onAnalyze && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
          <button
            onClick={onAnalyze}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Start AI Analysis
          </button>
        </div>
      )}
    </div>
  );
};
