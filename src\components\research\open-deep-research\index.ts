/**
 * Open Deep Research Module Index
 * Main export file for the Open Deep Research component
 */

// Main component
export { OpenDeepResearch } from './OpenDeepResearch';

// Components
export { SearchInterface } from './components/SearchInterface';
export { AgentProgress } from './components/AgentProgress';
export { ResultsList } from './components/ResultsList';
export { ReportViewer } from './components/ReportViewer';
export { KnowledgeBaseSidebar } from './components/KnowledgeBaseSidebar';
export { ModelSelector } from './components/ModelSelector';

// Services
export { searchService } from './services/search.service';
export { contentService } from './services/content.service';
export { reportService } from './services/report.service';
export { knowledgeBaseService } from './services/knowledge-base.service';

// Store
export { 
  useOpenDeepResearchStore,
  useSearchState,
  useReportState,
  useUIState,
  useStatusState
} from './stores/open-deep-research.store';

// Types
export type {
  SearchResult,
  Report,
  KnowledgeBaseReport,
  RankingResult,
  Article,
  ModelVariant,
  Status,
  OpenDeepResearchState,
  SearchConfig,
  PlatformModel,
  ExportOptions,
  SearchResponse,
  OptimizeResearchResponse,
  AnalyzeResultsResponse,
  ReportGenerationResponse,
  OpenDeepResearchProps,
  SearchInterfaceProps,
  ResultsListProps,
  ReportViewerProps,
  AgentProgressProps,
  KnowledgeBaseSidebarProps
} from './types';

// Constants
export {
  SEARCH_CONFIG,
  TIME_FILTERS,
  AI_MODELS,
  DEFAULT_MODEL,
  SUPPORTED_FILE_TYPES,
  RATE_LIMITS,
  KNOWLEDGE_BASE_KEY,
  EXPORT_FORMATS,
  API_ENDPOINTS,
  UI_CONSTANTS,
  AGENT_STEPS,
  AGENT_STEP_LABELS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION
} from './constants';
