# Supabase Authentication Configuration Fix

## Issue
Google OAuth is failing with error: "Unable to exchange external code"

This happens because the Supabase authentication settings don't match the current development server URL.

## Current Setup
- **Development Server**: `http://localhost:8081`
- **Google OAuth Redirect URL**: `http://localhost:8081/auth/callback`
- **Supabase Project ID**: `swsnqpavwcnqiihsidss`

## Required Configuration in Supabase Dashboard

### 1. Go to Supabase Dashboard
1. Visit: https://supabase.com/dashboard/project/swsnqpavwcnqiihsidss
2. Navigate to **Authentication** → **Settings**

### 2. Update Site URL
In the **General** tab:
- **Site URL**: `http://localhost:8081`

### 3. Update Redirect URLs
In the **Redirect URLs** section:
- Add: `http://localhost:8081/auth/callback`
- Add: `http://localhost:8081/**` (for wildcard support)

### 4. Verify Google OAuth Provider
In the **Providers** tab:
- Ensure Google provider is enabled
- Client ID should match your Google Cloud Console setup
- Client Secret should be configured
- **Redirect URL** should be: `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`

### 5. Google Cloud Console Setup
Make sure your Google Cloud Console OAuth application has these redirect URIs:
- `https://swsnqpavwcnqiihsidss.supabase.co/auth/v1/callback`
- `http://localhost:8081/auth/callback` (for development)

## Quick Test After Configuration
After updating the Supabase settings:
1. Restart the development server
2. Try Google OAuth login again
3. Should redirect to AI Paper Generator page after successful login

## Production URLs
For production deployment, you'll need to:
1. Update Site URL to your production domain
2. Add production redirect URLs
3. Update Google Cloud Console with production redirect URIs
