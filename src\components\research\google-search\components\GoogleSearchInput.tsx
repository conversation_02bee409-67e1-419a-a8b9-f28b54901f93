/**
 * Google Search Input Component
 * Input field for Google search queries with enhanced features
 */

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, Loader2, <PERSON><PERSON><PERSON>, <PERSON>, Send } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

interface GoogleSearchInputProps {
  onSubmit: (query: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function GoogleSearchInput({
  onSubmit,
  isLoading = false,
  placeholder = "Ask your research question...",
  disabled = false,
  className = ''
}: GoogleSearchInputProps) {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [query]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim() && !isLoading && !disabled) {
      onSubmit(query.trim());
      setQuery('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const exampleQueries = [
    "Latest research on artificial intelligence in healthcare 2024",
    "Climate change impact on marine ecosystems recent studies",
    "Renewable energy efficiency improvements peer reviewed research",
    "Quantum computing applications in cryptography academic papers",
    "Machine learning algorithms for drug discovery recent publications",
    "CRISPR gene editing latest clinical trials and results"
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Input Form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className={`
          relative rounded-lg border transition-all duration-200
          ${isFocused 
            ? 'border-blue-500 ring-2 ring-blue-500/20 shadow-lg' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}>
          <Textarea
            ref={textareaRef}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={disabled || isLoading}
            className="min-h-[60px] max-h-[200px] resize-none border-0 focus:ring-0 focus:border-0 bg-transparent pr-16"
            style={{ paddingRight: '4rem' }}
          />
          
          {/* Submit Button */}
          <div className="absolute bottom-3 right-3">
            <Button
              type="submit"
              size="sm"
              disabled={!query.trim() || isLoading || disabled}
              className="h-8 w-8 p-0 rounded-full bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Character count and hints */}
        <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
          <div className="flex items-center space-x-4">
            <span>{query.length} characters</span>
            {query.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                <Brain className="h-3 w-3 mr-1" />
                Academic Mode
              </Badge>
            )}
          </div>
          <div className="text-gray-400">
            Press Enter to search, Shift+Enter for new line
          </div>
        </div>
      </form>

      {/* Example Queries */}
      {query.length === 0 && !isLoading && (
        <div className="space-y-3">
          <div className="text-sm font-medium text-gray-700 flex items-center">
            <Sparkles className="h-4 w-4 mr-2 text-blue-500" />
            Try these example queries:
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {exampleQueries.map((example, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setQuery(example)}
                className="text-left justify-start h-auto py-2 px-3 text-sm text-gray-600 hover:text-gray-900 hover:bg-blue-50 border-gray-200"
                disabled={disabled}
              >
                <Search className="h-3 w-3 mr-2 flex-shrink-0 text-blue-500" />
                <span className="truncate">{example}</span>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-4 text-blue-600">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span className="text-sm font-medium">Searching with Google...</span>
        </div>
      )}
    </div>
  );
}

export default GoogleSearchInput;
