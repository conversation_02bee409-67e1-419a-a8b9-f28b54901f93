/**
 * Research Type Selector Component
 * Allows users to select the type of research they want to conduct
 */

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  BookOpen, 
  FileText, 
  Book, 
  Shield, 
  Clock, 
  Target, 
  Users,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { ResearchTypeTemplate, ResearchType } from '../types';
import { deepResearchService } from '../services/deep-research.service';

interface ResearchTypeSelectorProps {
  query: string;
  selectedModel: string;
  onTypeSelect: (researchType: ResearchType) => void;
  onCancel: () => void;
  className?: string;
}

const iconMap = {
  Search,
  BookOpen,
  FileText,
  Book,
  Shield
};

export function ResearchTypeSelector({
  query,
  selectedModel,
  onTypeSelect,
  onCancel,
  className
}: ResearchTypeSelectorProps) {
  const [availableTypes, setAvailableTypes] = useState<ResearchTypeTemplate[]>([]);
  const [suggestedTypes, setSuggestedTypes] = useState<ResearchTypeTemplate[]>([]);
  const [selectedType, setSelectedType] = useState<ResearchTypeTemplate | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showAllTypes, setShowAllTypes] = useState(false);

  useEffect(() => {
    loadResearchTypes();
  }, []);

  useEffect(() => {
    if (query && selectedModel) {
      analyzeSuggestedTypes();
    }
  }, [query, selectedModel]);

  const loadResearchTypes = () => {
    const types = deepResearchService.getResearchTypes();
    setAvailableTypes(types);
  };

  const analyzeSuggestedTypes = async () => {
    if (!query.trim()) return;

    setIsAnalyzing(true);
    try {
      const suggested = await deepResearchService.analyzeQueryForResearchType(query, selectedModel);
      setSuggestedTypes(suggested);
      
      // Auto-select the first suggested type
      if (suggested.length > 0) {
        setSelectedType(suggested[0]);
      }
    } catch (error) {
      console.error('Error analyzing query for research type:', error);
      // Fallback to showing all types
      setSuggestedTypes(availableTypes.slice(0, 3));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleTypeSelect = (type: ResearchTypeTemplate) => {
    setSelectedType(type);
  };

  const handleConfirm = () => {
    if (selectedType) {
      onTypeSelect(selectedType.config);
    }
  };

  const formatEstimatedTime = (hours: number): string => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${Math.round(hours)}h`;
    const days = Math.round(hours / 24);
    return `${days}d`;
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-900',
      green: 'border-green-200 bg-green-50 hover:bg-green-100 text-green-900',
      purple: 'border-purple-200 bg-purple-50 hover:bg-purple-100 text-purple-900',
      orange: 'border-orange-200 bg-orange-50 hover:bg-orange-100 text-orange-900',
      red: 'border-red-200 bg-red-50 hover:bg-red-100 text-red-900'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColorClasses = (color: string) => {
    const colorMap = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      orange: 'text-orange-600',
      red: 'text-red-600'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const typesToShow = showAllTypes ? availableTypes : (suggestedTypes.length > 0 ? suggestedTypes : availableTypes.slice(0, 3));

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">Choose Research Type</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Select the type of research that best fits your needs. We've analyzed your query and suggested the most appropriate options.
        </p>
        {query && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Your Query:</strong> "{query}"
            </p>
          </div>
        )}
      </div>

      {/* Analysis Status */}
      {isAnalyzing && (
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-3 text-blue-600">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="text-sm font-medium">Analyzing your query to suggest the best research type...</span>
          </div>
        </div>
      )}

      {/* Suggested Types */}
      {!isAnalyzing && suggestedTypes.length > 0 && !showAllTypes && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Target className="w-5 h-5 text-green-600" />
              Recommended for Your Query
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllTypes(true)}
              className="text-blue-600 hover:text-blue-700"
            >
              View All Types
            </Button>
          </div>
        </div>
      )}

      {/* Research Type Cards */}
      {!isAnalyzing && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {typesToShow.map((type) => {
            const IconComponent = iconMap[type.icon as keyof typeof iconMap] || Search;
            const isSelected = selectedType?.id === type.id;
            const isSuggested = suggestedTypes.some(s => s.id === type.id);

            return (
              <Card
                key={type.id}
                className={cn(
                  "cursor-pointer transition-all duration-200 relative",
                  isSelected 
                    ? `ring-2 ring-${type.color}-500 ${getColorClasses(type.color)}` 
                    : `hover:shadow-md border-gray-200 hover:border-${type.color}-300`
                )}
                onClick={() => handleTypeSelect(type)}
              >
                {isSuggested && !showAllTypes && (
                  <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Recommended
                  </div>
                )}
                
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "w-10 h-10 rounded-lg flex items-center justify-center",
                      `bg-${type.color}-100`
                    )}>
                      <IconComponent className={cn("w-5 h-5", getIconColorClasses(type.color))} />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{type.name}</CardTitle>
                      {isSelected && (
                        <CheckCircle className="w-5 h-5 text-green-600 absolute top-4 right-4" />
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-gray-600">{type.description}</p>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="flex items-center gap-1">
                      <FileText className="w-3 h-3 text-gray-500" />
                      <span>{type.config.totalWordTarget.toLocaleString()} words</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3 text-gray-500" />
                      <span>{formatEstimatedTime(type.config.totalWordTarget / 500)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="w-3 h-3 text-gray-500" />
                      <span>{type.config.minSections}-{type.config.maxSections} sections</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-3 h-3 text-gray-500" />
                      <span className="capitalize">{type.config.academicLevel}</span>
                    </div>
                  </div>

                  {/* Citation Density */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">Citation Density:</span>
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs",
                        type.config.citationDensity === 'heavy' && "border-red-300 text-red-700",
                        type.config.citationDensity === 'moderate' && "border-yellow-300 text-yellow-700",
                        type.config.citationDensity === 'light' && "border-green-300 text-green-700"
                      )}
                    >
                      {type.config.citationDensity}
                    </Badge>
                  </div>

                  {/* Examples */}
                  <div className="space-y-1">
                    <span className="text-xs font-medium text-gray-700">Examples:</span>
                    <div className="flex flex-wrap gap-1">
                      {type.examples.slice(0, 2).map((example, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {example}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Show All Types Toggle */}
      {!isAnalyzing && showAllTypes && suggestedTypes.length > 0 && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAllTypes(false)}
            className="text-blue-600 hover:text-blue-700"
          >
            Show Only Recommended Types
          </Button>
        </div>
      )}

      {/* Action Buttons */}
      {!isAnalyzing && (
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onCancel}
            className="px-6"
          >
            Cancel
          </Button>
          
          <div className="flex items-center gap-3">
            {selectedType && (
              <div className="text-sm text-gray-600">
                Selected: <span className="font-medium">{selectedType.name}</span>
              </div>
            )}
            <Button
              onClick={handleConfirm}
              disabled={!selectedType}
              className="px-6"
            >
              Continue with {selectedType?.name || 'Selected Type'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
