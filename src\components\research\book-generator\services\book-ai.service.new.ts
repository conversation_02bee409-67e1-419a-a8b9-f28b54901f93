import { AIGenerationOptions, AIAnalysisResult, BookContext, ChapterContext, GeneratedOutline, BookMetadata, UserChapter } from '../types';
import { CONTEXT_SETTINGS } from '../constants';
import { OUTLINE_GENERATION_PROMPTS } from '../prompts';

/**
 * Service for handling AI book generation with context management
 * Extends the paper AI service with book-specific capabilities
 */
export class BookAIService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables');
    }
  }
  
  /**
   * Helper method to extract and parse JSON from AI responses
   * Handles common issues with AI-generated JSON like markdown formatting
   */
  private extractAndParseJSON(content: string): any {
    // First check if the content is wrapped in markdown code blocks
    let jsonContent = content;
    const jsonMatch = content.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch && jsonMatch[1]) {
      jsonContent = jsonMatch[1].trim();
    }

    // Clean up the content before parsing
    jsonContent = jsonContent
      .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes
      .replace(/\n+/g, '\n') // Normalize newlines
      .trim();

    try {
      return JSON.parse(jsonContent);
    } catch (error) {
      // Try to fix common JSON formatting issues
      try {
        // Sometimes AI adds trailing commas in arrays/objects which is invalid JSON
        const fixedJson = jsonContent
          .replace(/,(\s*[\]}])/g, '$1'); // Remove trailing commas
        return JSON.parse(fixedJson);
      } catch (secondError) {
        console.error('Failed to parse AI response as JSON:', error);
        console.error('Content that failed to parse:', jsonContent);
        throw new Error(`Failed to parse JSON: ${(error as Error).message || 'Invalid format'}`);
      }
    }
  }
  
  /**
   * Generate a book chapter with context awareness
   */
  async generateChapter(
    chapterPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      const { 
        model = "anthropic/claude-3.5-sonnet", 
        maxTokens = CONTEXT_SETTINGS.CHAPTER_GENERATION_TOKENS,
        context
      } = options;
      
      // Build context-aware system prompt
      const systemPrompt = this.buildSystemPrompt(context);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: chapterPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7 // Slightly creative for book writing
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      return data.choices?.[0]?.message?.content || '[No content generated]';
    } catch (error: any) {
      console.error('AI Chapter Generation Error:', error);
      throw new Error('Failed to generate chapter. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate comprehensive outlines for all chapters
   */
  async generateAllChapterOutlines(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    try {
      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 4096
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(metadata, userChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book outline generator. Create comprehensive, detailed outlines that provide clear structure for book chapters. Always respond with valid JSON using the provided schema. Never include any text outside the JSON object. Do not include markdown formatting or code blocks."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      try {
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content);
        
        // Validate the parsed JSON structure
        if (!parsed.chapters || !Array.isArray(parsed.chapters)) {
          console.error('Invalid outline format, missing chapters array:', parsed);
          throw new Error('Invalid outline format returned by AI. Missing chapters array.');
        }

        const outlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => {
          // Validate chapter structure and provide defaults
          if (!chapter.title || !chapter.sections || !Array.isArray(chapter.sections)) {
            console.error('Invalid chapter structure:', chapter);
            throw new Error(`Invalid structure for chapter ${index + 1}. Missing title or sections.`);
          }

          return {
            id: `outline-${userChapters[index]?.id || index}`,
            chapterId: userChapters[index]?.id || `chapter-${index}`,
            title: chapter.title || `Chapter ${index + 1}`,
            description: chapter.description || '',
            sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
              id: `section-${section.order || sectionIndex + 1}`,
              title: section.title || `Section ${sectionIndex + 1}`,
              description: section.description || '',
              level: section.level || 1,
              order: section.order || sectionIndex + 1,
              estimatedWordCount: section.estimatedWordCount || 500,
              keyPoints: section.keyPoints || []
            })),
            estimatedWordCount: chapter.estimatedWordCount || 3000,
            keyPoints: chapter.keyPoints || [],
            status: 'ready' as const
          };
        });

        return outlines;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outlines. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outlines. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate outline for a single chapter
   */
  async generateChapterOutline(
    metadata: BookMetadata,
    userChapter: UserChapter,
    previousChapters: string[] = [],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline> {
    try {
      const {
        model = "anthropic/claude-3.5-sonnet",
        maxTokens = 2048
      } = options;

      const prompt = OUTLINE_GENERATION_PROMPTS.generateChapterOutline(metadata, userChapter, previousChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book outline generator. Create detailed, comprehensive outlines for book chapters. Always respond with valid JSON. Do not include markdown formatting or code blocks."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      // Parse the JSON response
      try {
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content);

        // Validate and build outline with defaults for missing fields
        const outline: GeneratedOutline = {
          id: `outline-${userChapter.id}`,
          chapterId: userChapter.id,
          title: parsed.title || userChapter.outline.title,
          description: parsed.description || userChapter.outline.description,
          sections: (parsed.sections || []).map((section: any, index: number) => ({
            id: `section-${section.order || index + 1}`,
            title: section.title || `Section ${index + 1}`,
            description: section.description || '',
            level: section.level || 1,
            order: section.order || index + 1,
            estimatedWordCount: section.estimatedWordCount || 500,
            keyPoints: section.keyPoints || []
          })),
          estimatedWordCount: parsed.estimatedWordCount || userChapter.outline.estimatedWordCount || 3000,
          keyPoints: parsed.keyPoints || [],
          status: 'ready' as const
        };

        return outline;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outline. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Chapter Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outline. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate a summary of a chapter for context building
   */
  async generateChapterSummary(
    chapterTitle: string, 
    chapterContent: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<AIAnalysisResult> {
    try {
      const {
        model = "anthropic/claude-3-sonnet",
        maxTokens = 1024
      } = options;
      
      const truncatedContent = chapterContent.slice(0, 6000); // Truncate to avoid token limits
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: "You are an expert book editor and summarizer. Create concise but comprehensive summaries and extract key points from book chapters. Always respond with valid JSON."
            },
            {
              role: "user",
              content: `Create a summary of Chapter "${chapterTitle}" and extract its key points for use as context in generating subsequent chapters.

The chapter content is:
${truncatedContent}

Respond in the following JSON format:
{
  "summary": "A concise summary of the chapter (200-300 words)",
  "keyPoints": ["Key point 1", "Key point 2", "Key point 3", ...]
}`
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.3 // Lower temperature for more accurate summary
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      try {
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content);
        
        return {
          summary: parsed.summary || `Summary of "${chapterTitle}"`,
          keyPoints: parsed.keyPoints || []
        };
      } catch (parseError: any) {
        console.error('Failed to parse summary JSON:', parseError);
        return {
          summary: `Summary of "${chapterTitle}" (parsing error)`,
          keyPoints: ['Chapter summary extraction failed']
        };
      }
    } catch (error: any) {
      console.error('AI Summary Generation Error:', error);
      return {
        summary: `Summary of "${chapterTitle}" (generation error)`,
        keyPoints: ['Chapter summary generation failed']
      };
    }
  }
  
  /**
   * Build a system prompt with book context for consistent generation
   */
  private buildSystemPrompt(context?: BookContext): string {
    const basePrompt = `You are an expert book author with deep knowledge across many subjects.
Write in a clear, engaging, and professional style appropriate for a published book.
Create substantial, high-quality content that incorporates creative elements, examples, and depth.
Maintain consistency with the book's voice, tone, and previous chapters.`;
    
    if (!context) {
      return basePrompt;
    }
    
    const contextPrompt = `
You have access to the following context from the book:

BOOK OUTLINE:
${context.bookOutline}

${context.previousChapters.length > 0 ? `PREVIOUS CHAPTERS:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
   Summary: ${ch.summary}
   Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}` : ''}

Use this context to ensure your writing maintains consistent tone, narrative, and references throughout the book.`;
    
    return basePrompt + contextPrompt;
  }
}

// Export as singleton
const bookAIService = new BookAIService();
export default bookAIService;
