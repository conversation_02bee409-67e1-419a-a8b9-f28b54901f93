/**
 * Test file for Gemini integration in Presentation Generator
 * Run this in the browser console to test the API connection
 */

import { presentationAIService } from './services/presentation-ai.service';
import { PresentationMetadata } from './types';

export async function testPresentationGeneration(): Promise<boolean> {
  try {
    console.log('🧪 Testing Presentation Generator...');

    // Test metadata
    const testMetadata: PresentationMetadata = {
      title: 'Test Presentation',
      topic: 'Introduction to Artificial Intelligence and its applications in modern technology',
      slideCount: 5,
      style: 'professional',
      language: 'en',
      audience: 'Technology professionals',
      description: 'A brief overview of AI concepts and practical applications'
    };

    console.log('✅ Test metadata prepared');

    // Test outline generation
    console.log('🔄 Testing outline generation...');
    const outline = await presentationAIService.generateOutline(testMetadata, 'gemini-2.5-flash');

    if (!outline || outline.length === 0) {
      console.error('❌ Outline generation failed - no outline returned');
      return false;
    }

    console.log('✅ Outline generated successfully:', outline.length, 'slides');
    console.log('📋 Outline preview:', outline.map(item => item.title));

    // Test slide content generation
    console.log('🔄 Testing slide content generation...');
    const firstSlide = await presentationAIService.generateSlideContent(
      outline[0],
      testMetadata,
      'gemini-2.5-flash'
    );

    if (!firstSlide || !firstSlide.content) {
      console.error('❌ Slide content generation failed');
      return false;
    }

    console.log('✅ Slide content generated successfully');
    console.log('📄 Slide preview:', firstSlide.title);

    console.log('🎉 All tests passed! Presentation generator is working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Presentation generation test failed:', error);

    if (error instanceof Error) {
      console.error('💡 Error details:', error.message);
    }

    return false;
  }
}

// Make function available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testPresentationGeneration = testPresentationGeneration;
  console.log('🔧 Test function available: testPresentationGeneration()');
}
