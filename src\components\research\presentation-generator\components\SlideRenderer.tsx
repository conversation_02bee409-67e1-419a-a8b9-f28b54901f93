import React from 'react';
import { Slide, PresentationTheme } from '../types';
import {
  ColumnsLayout,
  BulletsLayout,
  IconsLayout,
  CycleLayout,
  ArrowsLayout,
  TimelineLayout,
  PyramidLayout,
  StaircaseLayout
} from './AdvancedSlideLayouts';

interface SlideRendererProps {
  slide: Slide;
  theme: PresentationTheme;
  isPreview?: boolean;
  className?: string;
}

export function SlideRenderer({ slide, theme, isPreview = false, className = '' }: SlideRendererProps) {
  const slideStyle = {
    backgroundColor: theme.colors.background,
    color: theme.colors.text,
    fontFamily: theme.fonts.body,
    padding: isPreview ? '1rem' : theme.layout.padding,
    borderRadius: theme.layout.borderRadius,
    minHeight: isPreview ? '200px' : '600px',
    width: '100%',
    position: 'relative' as const,
    overflow: 'hidden' as const,
    boxShadow: isPreview ? '0 2px 8px rgba(0,0,0,0.1)' : 'none',
    border: `1px solid ${theme.colors.muted}`,
    background: `linear-gradient(135deg, ${theme.colors.background} 0%, ${theme.colors.muted}15 100%)`,
  };

  const renderContent = () => {
    if (!slide.content || slide.content.length === 0) {
      return (
        <div className="flex items-center justify-center h-full">
          <p style={{ color: theme.colors.muted }}>No content available</p>
        </div>
      );
    }

    return slide.content.map((item: any, index: number) => {
      switch (item.type) {
        case 'h1':
          return (
            <h1
              key={index}
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: isPreview ? '1.5rem' : theme.fonts.size.title,
                color: theme.colors.heading,
                marginBottom: theme.layout.spacing,
                fontWeight: 'bold',
                textAlign: 'center',
                background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                textShadow: `2px 2px 4px ${theme.colors.muted}`,
                letterSpacing: '0.02em'
              }}
            >
              {item.children?.[0]?.text || ''}
            </h1>
          );

        case 'h2':
          return (
            <h2
              key={index}
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: isPreview ? '1.2rem' : theme.fonts.size.heading,
                color: theme.colors.primary,
                marginBottom: theme.layout.spacing,
                fontWeight: '600',
                borderBottom: `3px solid ${theme.colors.accent}`,
                paddingBottom: '0.5rem',
                position: 'relative'
              }}
            >
              {item.children?.[0]?.text || ''}
              <div
                style={{
                  position: 'absolute',
                  bottom: '-3px',
                  left: '0',
                  width: '60px',
                  height: '3px',
                  background: `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                  borderRadius: '2px'
                }}
              />
            </h2>
          );

        case 'h3':
          return (
            <h3
              key={index}
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: '1.5rem',
                color: theme.colors.heading,
                marginBottom: '1rem',
                fontWeight: '600'
              }}
            >
              {item.children?.[0]?.text || ''}
            </h3>
          );

        case 'p':
          return (
            <p
              key={index}
              style={{
                fontFamily: theme.fonts.body,
                fontSize: isPreview ? '0.9rem' : theme.fonts.size.body,
                color: theme.colors.text,
                marginBottom: '1rem',
                lineHeight: '1.7',
                textAlign: 'justify',
                padding: '0.5rem 0'
              }}
            >
              {item.children?.[0]?.text || ''}
            </p>
          );

        case 'ul':
          return (
            <ul
              key={index}
              style={{
                fontFamily: theme.fonts.body,
                fontSize: isPreview ? '0.8rem' : theme.fonts.size.body,
                color: theme.colors.text,
                marginBottom: '1.5rem',
                paddingLeft: '0',
                listStyle: 'none'
              }}
            >
              {item.children?.map((listItem: any, liIndex: number) => (
                <li
                  key={liIndex}
                  style={{
                    marginBottom: '0.8rem',
                    padding: '0.5rem 1rem',
                    backgroundColor: `${theme.colors.muted}20`,
                    borderLeft: `4px solid ${theme.colors.accent}`,
                    borderRadius: '0 8px 8px 0',
                    position: 'relative',
                    transition: 'all 0.3s ease'
                  }}
                >
                  <div
                    style={{
                      position: 'absolute',
                      left: '-8px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '8px',
                      height: '8px',
                      backgroundColor: theme.colors.accent,
                      borderRadius: '50%'
                    }}
                  />
                  {listItem.children?.[0]?.text || ''}
                </li>
              ))}
            </ul>
          );

        case 'li':
          return (
            <li
              key={index}
              style={{
                fontFamily: theme.fonts.body,
                fontSize: theme.fonts.size.body,
                color: theme.colors.text,
                marginBottom: '0.5rem',
                listStyleType: 'disc'
              }}
            >
              {item.children?.[0]?.text || ''}
            </li>
          );

        case 'blockquote':
          return (
            <blockquote
              key={index}
              style={{
                fontFamily: theme.fonts.heading,
                fontSize: isPreview ? '1rem' : '1.5rem',
                color: theme.colors.primary,
                fontStyle: 'italic',
                fontWeight: '500',
                textAlign: 'center',
                margin: '2rem 0',
                padding: '2rem',
                background: `linear-gradient(135deg, ${theme.colors.muted}30, ${theme.colors.accent}20)`,
                borderRadius: '16px',
                border: `2px solid ${theme.colors.accent}40`,
                position: 'relative',
                boxShadow: `0 8px 32px ${theme.colors.primary}20`
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  top: '10px',
                  left: '20px',
                  fontSize: '3rem',
                  color: theme.colors.accent,
                  opacity: 0.3,
                  fontFamily: 'serif'
                }}
              >
                "
              </div>
              <div
                style={{
                  position: 'absolute',
                  bottom: '10px',
                  right: '20px',
                  fontSize: '3rem',
                  color: theme.colors.accent,
                  opacity: 0.3,
                  fontFamily: 'serif'
                }}
              >
                "
              </div>
              {item.children?.[0]?.text || ''}
            </blockquote>
          );

        default:
          return (
            <div key={index} style={{ marginBottom: '1rem' }}>
              {item.children?.[0]?.text || JSON.stringify(item)}
            </div>
          );
      }
    });
  };

  // Detect advanced layout from slide content
  const detectAdvancedLayout = () => {
    if (!slide.content || slide.content.length === 0) return null;

    const contentText = slide.content.map(item =>
      item.children?.[0]?.text || ''
    ).join(' ').toLowerCase();

    // Check for layout keywords in content
    if (contentText.includes('comparison') || contentText.includes('vs') || contentText.includes('versus')) {
      return 'columns';
    }
    if (contentText.includes('process') || contentText.includes('workflow') || contentText.includes('cycle')) {
      return 'cycle';
    }
    if (contentText.includes('timeline') || contentText.includes('chronological') || contentText.includes('history')) {
      return 'timeline';
    }
    if (contentText.includes('hierarchy') || contentText.includes('pyramid') || contentText.includes('levels')) {
      return 'pyramid';
    }
    if (contentText.includes('steps') || contentText.includes('progression') || contentText.includes('advancement')) {
      return 'staircase';
    }
    if (contentText.includes('flow') || contentText.includes('cause') || contentText.includes('effect')) {
      return 'arrows';
    }
    if (contentText.includes('concepts') || contentText.includes('features') || contentText.includes('benefits')) {
      return 'icons';
    }

    // Check for bullet points
    const hasBullets = slide.content.some(item =>
      item.type === 'ul' || item.type === 'li' ||
      (item.children?.[0]?.text && item.children[0].text.includes('•'))
    );
    if (hasBullets) return 'bullets';

    return null;
  };

  const parseContentForAdvancedLayout = () => {
    if (!slide.content) return [];

    const items = [];
    let currentItem = { title: '', description: '', icon: '🔹' };

    for (const item of slide.content) {
      const text = item.children?.[0]?.text || '';

      if (item.type === 'h2' || item.type === 'h3') {
        if (currentItem.title) {
          items.push({ ...currentItem });
          currentItem = { title: '', description: '', icon: '🔹' };
        }
        currentItem.title = text;
      } else if (item.type === 'p') {
        currentItem.description += (currentItem.description ? ' ' : '') + text;
      } else if (item.type === 'ul') {
        item.children?.forEach((li: any) => {
          const liText = li.children?.[0]?.text || '';
          if (liText) {
            items.push({
              title: liText.split('.')[0] || liText.substring(0, 30),
              description: liText,
              icon: '🔹'
            });
          }
        });
      } else if (item.type === 'li') {
        items.push({
          title: text.split('.')[0] || text.substring(0, 30),
          description: text,
          icon: '🔹'
        });
      }
    }

    if (currentItem.title || currentItem.description) {
      items.push(currentItem);
    }

    return items.length > 0 ? items : [
      { title: 'Point 1', description: 'First key point', icon: '🚀' },
      { title: 'Point 2', description: 'Second key point', icon: '⭐' },
      { title: 'Point 3', description: 'Third key point', icon: '🎯' }
    ];
  };

  const renderLayoutSpecificContent = () => {
    const advancedLayout = detectAdvancedLayout();
    const layoutItems = parseContentForAdvancedLayout();

    // Use advanced layouts when detected
    if (advancedLayout) {
      switch (advancedLayout) {
        case 'columns':
          return <ColumnsLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'bullets':
          return <BulletsLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'icons':
          return <IconsLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'cycle':
          return <CycleLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'arrows':
          return <ArrowsLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'timeline':
          return <TimelineLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'pyramid':
          return <PyramidLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
        case 'staircase':
          return <StaircaseLayout theme={theme} isPreview={isPreview} items={layoutItems} />;
      }
    }

    // Fallback to original layout logic
    switch (slide.layout) {
      case 'title-slide':
        return (
          <div
            className="flex flex-col items-center justify-center h-full text-center relative"
            style={{
              background: `radial-gradient(ellipse at center, ${theme.colors.primary}10 0%, ${theme.colors.background} 70%)`,
            }}
          >
            <div
              style={{
                position: 'absolute',
                top: '20%',
                left: '10%',
                width: '80%',
                height: '60%',
                background: `linear-gradient(45deg, ${theme.colors.accent}05, ${theme.colors.secondary}05)`,
                borderRadius: '50%',
                filter: 'blur(40px)',
                zIndex: 0
              }}
            />
            <div style={{ position: 'relative', zIndex: 1 }}>
              {renderContent()}
            </div>
          </div>
        );

      case 'two-column':
        return (
          <div className="grid grid-cols-2 gap-8 h-full">
            <div
              style={{
                padding: '1rem',
                background: `linear-gradient(135deg, ${theme.colors.background} 0%, ${theme.colors.muted}20 100%)`,
                borderRadius: '12px',
                border: `1px solid ${theme.colors.accent}30`
              }}
            >
              {renderContent()}
            </div>
            <div className="flex items-center justify-center">
              <div
                style={{
                  width: '100%',
                  height: isPreview ? '120px' : '300px',
                  background: `linear-gradient(45deg, ${theme.colors.primary}20, ${theme.colors.accent}20)`,
                  borderRadius: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: theme.colors.primary,
                  fontSize: isPreview ? '0.8rem' : '1.2rem',
                  fontWeight: '500',
                  border: `2px dashed ${theme.colors.accent}60`,
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <div
                  style={{
                    position: 'absolute',
                    top: '-50%',
                    right: '-50%',
                    width: '200%',
                    height: '200%',
                    background: `conic-gradient(from 0deg, ${theme.colors.primary}10, ${theme.colors.accent}10, ${theme.colors.secondary}10)`,
                    animation: 'rotate 20s linear infinite',
                    opacity: 0.3
                  }}
                />
                <span style={{ position: 'relative', zIndex: 1 }}>📊 Visual Content</span>
              </div>
            </div>
          </div>
        );

      case 'image-text':
        return (
          <div className="space-y-6">
            <div className="flex justify-center">
              <div
                style={{
                  width: isPreview ? '80%' : '60%',
                  height: isPreview ? '100px' : '250px',
                  background: `linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.accent}15)`,
                  borderRadius: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: theme.colors.primary,
                  fontSize: isPreview ? '0.8rem' : '1.1rem',
                  fontWeight: '600',
                  border: `3px solid ${theme.colors.accent}40`,
                  position: 'relative',
                  overflow: 'hidden',
                  boxShadow: `0 10px 30px ${theme.colors.primary}20`
                }}
              >
                <div
                  style={{
                    position: 'absolute',
                    inset: '10px',
                    background: `radial-gradient(circle at 30% 30%, ${theme.colors.secondary}20, transparent 50%)`,
                    borderRadius: '16px'
                  }}
                />
                <span style={{ position: 'relative', zIndex: 1 }}>🖼️ Featured Image</span>
              </div>
            </div>
            <div
              style={{
                padding: '1rem',
                background: `${theme.colors.muted}10`,
                borderRadius: '12px',
                border: `1px solid ${theme.colors.accent}20`
              }}
            >
              {renderContent()}
            </div>
          </div>
        );

      case 'quote':
        return (
          <div
            className="flex items-center justify-center h-full relative"
            style={{
              background: `radial-gradient(ellipse at center, ${theme.colors.accent}08 0%, transparent 70%)`
            }}
          >
            <div
              className="text-center max-w-4xl relative"
              style={{
                padding: '3rem',
                background: `linear-gradient(135deg, ${theme.colors.background}95, ${theme.colors.muted}30)`,
                borderRadius: '24px',
                border: `2px solid ${theme.colors.accent}30`,
                backdropFilter: 'blur(10px)',
                boxShadow: `0 20px 60px ${theme.colors.primary}15`
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  top: '-20px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '4px',
                  background: `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.accent})`,
                  borderRadius: '2px'
                }}
              />
              {renderContent()}
            </div>
          </div>
        );

      default:
        return (
          <div
            className="space-y-4 h-full"
            style={{
              padding: '1rem',
              background: `linear-gradient(135deg, ${theme.colors.background} 0%, ${theme.colors.muted}05 100%)`,
              borderRadius: '12px',
              border: `1px solid ${theme.colors.accent}20`
            }}
          >
            {renderContent()}
          </div>
        );
    }
  };

  return (
    <>
      {/* Add CSS animations */}
      <style>
        {`
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
          }
          .slide-renderer {
            animation: fadeInUp 0.6s ease-out;
          }
          .slide-renderer:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
          }
        `}
      </style>
      <div
        className={`slide-renderer ${className}`}
        style={slideStyle}
      >
      {/* Slide Number */}
      {!isPreview && (
        <div
          style={{
            position: 'absolute',
            bottom: '1rem',
            right: '1rem',
            fontSize: '0.875rem',
            color: theme.colors.primary,
            fontFamily: theme.fonts.body,
            fontWeight: '600',
            backgroundColor: `${theme.colors.accent}20`,
            padding: '0.5rem 1rem',
            borderRadius: '20px',
            border: `1px solid ${theme.colors.accent}40`,
            backdropFilter: 'blur(10px)'
          }}
        >
          {slide.order + 1}
        </div>
      )}

      {/* Main Content */}
      <div className="h-full">
        {renderLayoutSpecificContent()}
      </div>
    </div>
    </>
  );
}
