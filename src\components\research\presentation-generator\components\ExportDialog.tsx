import React, { useState } from 'react';
import { 
  Download, 
  FileText, 
  Presentation as PresentationIcon,
  Globe,
  Type,
  Settings,
  CheckCircle
} from 'lucide-react';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

import { Presentation, ExportOptions } from '../types';
import { EXPORT_FORMATS } from '../constants';
import { ExportService } from '../services/export.service';
import { usePresentationStore } from '../stores/presentation.store';

interface ExportDialogProps {
  presentation: Presentation | null;
  isOpen: boolean;
  onClose: () => void;
  isExporting: boolean;
}

export function ExportDialog({ presentation, isOpen, onClose, isExporting }: ExportDialogProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeNotes: true,
    quality: 'high',
    theme: true
  });

  const { selectedTheme } = usePresentationStore();

  const handleExport = async () => {
    if (!presentation) return;

    try {
      switch (exportOptions.format) {
        case 'pdf':
          await ExportService.exportToPDF(presentation, selectedTheme);
          break;
        case 'pptx':
          await ExportService.exportToPPTX(presentation, selectedTheme);
          break;
        case 'html':
          await ExportService.exportToHTML(presentation, selectedTheme);
          break;
        case 'markdown':
          await ExportService.exportToMarkdown(presentation);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      // Show success message
      alert(`Presentation exported as ${exportOptions.format.toUpperCase()} successfully!`);
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    }
  };

  const getFormatIcon = (formatId: string) => {
    switch (formatId) {
      case 'pdf':
        return FileText;
      case 'pptx':
        return PresentationIcon;
      case 'html':
        return Globe;
      case 'markdown':
        return Type;
      default:
        return FileText;
    }
  };

  const getFormatDescription = (formatId: string) => {
    switch (formatId) {
      case 'pdf':
        return 'Best for sharing and printing. Preserves formatting across devices.';
      case 'pptx':
        return 'Native PowerPoint format. Fully editable in Microsoft PowerPoint.';
      case 'html':
        return 'Interactive web presentation. Can be hosted online or viewed in browsers.';
      case 'markdown':
        return 'Plain text format. Great for version control and further editing.';
      default:
        return '';
    }
  };

  if (!presentation) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export Presentation
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Presentation Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{presentation.metadata.title}</h4>
                  <p className="text-sm text-gray-600">
                    {presentation.slides.length} slides • {presentation.theme.displayName} theme
                  </p>
                </div>
                <Badge variant="secondary">
                  {presentation.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Format Selection */}
          <div>
            <Label className="text-base font-medium mb-4 block">Export Format</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {EXPORT_FORMATS.map((format) => {
                const Icon = getFormatIcon(format.id);
                const isSelected = exportOptions.format === format.id;
                
                return (
                  <Card 
                    key={format.id}
                    className={`cursor-pointer transition-all ${
                      isSelected 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:shadow-md hover:bg-gray-50'
                    }`}
                    onClick={() => setExportOptions(prev => ({ ...prev, format: format.id as any }))}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Icon className={`w-5 h-5 mt-0.5 ${
                          isSelected ? 'text-blue-600' : 'text-gray-600'
                        }`} />
                        <div className="flex-1">
                          <h4 className={`font-medium ${
                            isSelected ? 'text-blue-900' : 'text-gray-900'
                          }`}>
                            {format.name}
                          </h4>
                          <p className={`text-sm ${
                            isSelected ? 'text-blue-700' : 'text-gray-600'
                          }`}>
                            {getFormatDescription(format.id)}
                          </p>
                        </div>
                        {isSelected && (
                          <CheckCircle className="w-5 h-5 text-blue-600" />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Settings className="w-4 h-4" />
                Export Options
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="include-notes">Include Speaker Notes</Label>
                  <p className="text-sm text-gray-600">
                    Add speaker notes to the exported presentation
                  </p>
                </div>
                <Switch
                  id="include-notes"
                  checked={exportOptions.includeNotes}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeNotes: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="include-theme">Apply Theme Styling</Label>
                  <p className="text-sm text-gray-600">
                    Include custom theme colors and fonts
                  </p>
                </div>
                <Switch
                  id="include-theme"
                  checked={exportOptions.theme}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, theme: checked }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label>Export Quality</Label>
                <Select
                  value={exportOptions.quality}
                  onValueChange={(value) => 
                    setExportOptions(prev => ({ ...prev, quality: value as any }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low (Faster, smaller file)</SelectItem>
                    <SelectItem value="medium">Medium (Balanced)</SelectItem>
                    <SelectItem value="high">High (Best quality, larger file)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <>
                  <Download className="w-4 h-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {exportOptions.format.toUpperCase()}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
