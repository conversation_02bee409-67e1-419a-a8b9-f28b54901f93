import { useState, useCallback, useRef, useEffect } from 'react';
import * as pdfjs from 'pdfjs-dist';
import { ArticleSection, ParsedArticle, ArticleReviewResults, ReviewProgress, AIModelOption, ReviewMetrics, SectionReview } from '../types';
import articleReviewAPI from '../services/article-review-api.service';

// Set up the PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

/**
 * Hook for handling article review functionality
 * 
 * This hook manages the state and logic for:
 * - File uploading and parsing (PDF/DOCX)
 * - Article content extraction
 * - Section identification
 * - AI model selection
 * - Review process management
 * - Results handling
 */
export const useArticleReview = () => {
  // File state
  const [articleFile, setArticleFile] = useState<File | null>(null);
  const [parsedContent, setParsedContent] = useState<ParsedArticle | null>(null);
  
  // Review state
  const [reviewResults, setReviewResults] = useState<ArticleReviewResults | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState<ReviewProgress>({
    percentage: 0,
    currentStep: '',
    totalSteps: 0,
    completedSteps: 0,
  });
  const [currentSection, setCurrentSection] = useState<ArticleSection | null>(null);
  
  // Cancel reference
  const cancelRef = useRef(false);
  
  // AI model selection
  const [selectedModel, setSelectedModel] = useState<string>('google/gemini-2.5-flash'); 
  
  // Available AI models
  const availableModels: AIModelOption[] = [
    {
      id: 'google/gemini-2.5-flash',
      name: 'Google Gemini 2.5 Flash',
      provider: 'google',
      description: 'Fast and efficient analysis for most academic papers',
      contextSize: 1000000,
      isAvailable: true
    },
    {
      id: 'anthropic/claude-sonnet-3.5',
      name: 'Claude Sonnet 3.5',
      provider: 'anthropic',
      description: 'Exceptional comprehension of research context and methodology',
      contextSize: 200000,
      isAvailable: true
    },
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      provider: 'openai',
      description: 'Well-rounded analysis with strong academic understanding',
      contextSize: 128000,
      isAvailable: true
    },
    {
      id: 'anthropic/claude-3-opus',
      name: 'Claude 3 Opus',
      provider: 'anthropic',
      description: 'Highest quality analysis, best for complex papers',
      contextSize: 200000,
      isAvailable: true
    },
  ];

  /**
   * Parse PDF file content
   * @param file PDF file to parse
   * @returns Parsed content
   */
  const parsePdfFile = useCallback(async (file: File): Promise<ParsedArticle> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      
      let fullText = '';
      
      // Extract text from all pages
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        
        fullText += pageText + ' ';
      }

      // Identify sections in the PDF
      const sections = identifyArticleSections(fullText);
      
      return {
        ...sections,
        fullText,
        fileName: file.name,
        fileType: 'pdf',
        fileSize: file.size,
      };
    } catch (error) {
      console.error('Error parsing PDF:', error);
      throw new Error('Failed to parse PDF file. Please try a different file.');
    }
  }, []);

  /**
   * Parse DOCX file content
   * @param file DOCX file to parse
   * @returns Parsed content
   */
  const parseDocxFile = useCallback(async (file: File): Promise<ParsedArticle> => {
    try {
      // Note: For full implementation, we'd use mammoth.js to parse DOCX
      // This is currently a limitation that should be addressed
      
      throw new Error('DOCX parsing is not yet implemented. Please upload a PDF file instead.');
      
    } catch (error) {
      console.error('Error parsing DOCX:', error);
      throw new Error('DOCX parsing is not yet implemented. Please upload a PDF file instead.');
    }
  }, []);

  /**
   * Identify and extract sections from article text
   * 
   * @param text Full article text
   * @returns Object with extracted sections
   */
  const identifyArticleSections = useCallback((text: string) => {
    // Regular expressions to identify common academic paper sections
    const sectionPatterns = {
      title: /^(.*?)(?=abstract|\nabstract|ABSTRACT)/is,
      abstract: /abstract(?:[\s\n]+)(.*?)(?=(\n|\s)*introduction|\n|\s*keywords|\n|\s*1\.)/is,
      keywords: /keywords(?:[\s\n:]+)(.*?)(?=(\n|\s)*introduction|\n|\s*1\.)/is,
      introduction: /(?:introduction|1\.\s*introduction)(?:[\s\n]+)(.*?)(?=(\n|\s)*(?:methods|methodology|materials and methods|experimental|2\.))/is,
      methodology: /(?:methods|methodology|materials and methods|experimental|2\.\s*(?:methods|methodology))(?:[\s\n]+)(.*?)(?=(\n|\s)*(?:results|findings|3\.))/is,
      results: /(?:results|findings|3\.\s*results)(?:[\s\n]+)(.*?)(?=(\n|\s)*(?:discussion|interpretation|4\.))/is,
      discussion: /(?:discussion|interpretation|4\.\s*discussion)(?:[\s\n]+)(.*?)(?=(\n|\s)*(?:conclusion|summary|5\.))/is,
      conclusion: /(?:conclusion|summary|5\.\s*conclusion)(?:[\s\n]+)(.*?)(?=(\n|\s)*(?:references|bibliography|acknowledgements))/is,
      references: /(?:references|bibliography)(?:[\s\n]+)(.*?)$/is,
    };
    
    // Extract sections using regex patterns
    const extractedSections: Record<string, string> = {};
    const sectionMapping: Partial<Record<ArticleSection, string>> = {};
    
    // Extract sections
    for (const [section, pattern] of Object.entries(sectionPatterns)) {
      const match = text.match(pattern);
      if (match && match[1]) {
        const cleanedText = match[1].trim();
        extractedSections[section] = cleanedText;
        sectionMapping[section as ArticleSection] = cleanedText;
      }
    }
    
    // Extract keywords as array if found
    let keywords: string[] = [];
    if (extractedSections.keywords) {
      keywords = extractedSections.keywords
        .split(/[,;]/)
        .map(k => k.trim())
        .filter(Boolean);
    }
    
    return {
      title: extractedSections.title || '',
      abstract: extractedSections.abstract || '',
      keywords: keywords,
      introduction: extractedSections.introduction || '',
      methodology: extractedSections.methodology || '',
      results: extractedSections.results || '',
      discussion: extractedSections.discussion || '',
      conclusion: extractedSections.conclusion || '',
      references: extractedSections.references || '',
      sectionMapping: sectionMapping as Record<ArticleSection, string>,
    };
  }, []);

  /**
   * Start the review process for the uploaded article
   */
  const startReview = useCallback(async () => {
    if (!articleFile || !parsedContent) {
      console.error('No article file or parsed content available');
      return;
    }
    
    try {
      setIsAnalyzing(true);
      cancelRef.current = false;
      setReviewResults(null);
      
      // Define the sections to analyze in order
      const sectionsToAnalyze: ArticleSection[] = [
        'introduction',
        'methodology', 
        'results',
        'discussion',
        'conclusion',
        'abstract',
        'title',
        'keywords',
        'overall'
      ];
      
      // Initialize progress tracking
      setProgress({
        percentage: 0,
        currentStep: 'Starting analysis',
        totalSteps: sectionsToAnalyze.length,
        completedSteps: 0,
      });
      
      // Create a container for results
      const results: Partial<ArticleReviewResults> = {};
      
      // Analyze each section
      for (let i = 0; i < sectionsToAnalyze.length; i++) {
        // Check if operation was canceled
        if (cancelRef.current) {
          setIsAnalyzing(false);
          return;
        }
        
        const section = sectionsToAnalyze[i];
        setCurrentSection(section);
        
        // Update progress
        setProgress({
          percentage: Math.round((i / sectionsToAnalyze.length) * 100),
          currentStep: `Analyzing ${section}`,
          totalSteps: sectionsToAnalyze.length,
          completedSteps: i,
          estimatedTimeRemaining: (sectionsToAnalyze.length - i) * 15, // Rough estimate: 15 seconds per section
        });
        
        // Get content for the section
        const sectionContent = parsedContent.sectionMapping[section] || '';
        
        // Skip empty sections
        if (!sectionContent && section !== 'overall') {
          continue;
        }
        
        // Analyze the section using selected AI model
        const sectionResult = await analyzeSection(section, sectionContent, parsedContent);
        
        // Type safety: cast result to the appropriate type based on section
        if (section === 'overall') {
          results[section] = sectionResult as ReviewMetrics;
        } else {
          results[section] = sectionResult as SectionReview;
        }
        
        // Update progress
        setProgress(prev => ({
          ...prev,
          completedSteps: prev.completedSteps + 1,
          percentage: Math.round(((i + 1) / sectionsToAnalyze.length) * 100),
        }));
      }
      
      // Compile overall metrics if not explicitly calculated
      if (!results.overall) {
        try {
          results.overall = await generateOverallMetrics(results);
        } catch (error) {
          console.error("Error generating overall metrics:", error);
          
          // Provide fallback metrics
          results.overall = {
            clarity: 0,
            structure: 0,
            methodology: 0,
            significance: 0,
            originality: 0,
            overallScore: 0,
            summary: "Error generating overall metrics: " + (error.message || "Unknown error"),
            majorStrengths: [],
            majorWeaknesses: ["Error during overall analysis"],
            recommendations: ["Try again or check API configuration"]
          };
        }
      }
      
      // Set final results
      setReviewResults(results as ArticleReviewResults);
      
    } catch (error) {
      console.error('Error during review process:', error);
      // Handle error appropriately
    } finally {
      setIsAnalyzing(false);
      setCurrentSection(null);
    }
  }, [articleFile, parsedContent]);

  /**
   * Cancel ongoing review process
   */
  const cancelReview = useCallback(() => {
    cancelRef.current = true;
  }, []);

  /**
   * Analyze a section of the article using the selected AI model
   * 
   * This function sends the content to an AI service for detailed analysis
   * and returns structured feedback for the section.
   * 
   * @param section The section to analyze
   * @param content The text content of the section
   * @param fullArticle The complete parsed article for context
   * @returns Analysis results for the section
   */
  const analyzeSection = useCallback(async (
    section: ArticleSection,
    content: string,
    fullArticle: ParsedArticle
  ) => {
    // Log analysis attempt with selected model
    console.log(`Analyzing ${section} using ${selectedModel}`);
    
    try {
      // Check if OpenRouter API key is configured
      const isApiConfigured = articleReviewAPI.isConfigured();
      
      if (!isApiConfigured) {
        throw new Error('OpenRouter API key not configured in .env file (VITE_OPENROUTER_API_KEY)');
      }
      
      // Check if content exists for this section
      if (!content && section !== 'overall') {
        return {
          content: "",
          analysis: `No content found for ${section} section.`,
          strengths: [],
          weaknesses: [`${section} section could not be identified in the document`],
          suggestions: ["Check document structure and formatting"],
          score: 0,
          detailedFeedback: []
        };
      }
      
      // For discussion section, check if content is substantial
      if (section === 'discussion' && content.trim().length < 100) {
        return {
          content: content || "No substantial discussion section found",
          analysis: `This section contains ${content?.length || 0} characters. The paper may combine discussion with results or conclusion.`,
          strengths: [],
          weaknesses: ["Section content is limited or missing"],
          suggestions: ["Review the structure of the paper to ensure proper section organization"],
          score: 0,
          detailedFeedback: []
        };
      }
      
      // Make the API call to analyze the section
      if (section === 'overall' && fullArticle.fullText) {
        // For overall section, analyze the full text or combine all sections
        return await articleReviewAPI.analyzeSection(section, fullArticle.fullText, selectedModel);
      } else if (content) {
        // For regular sections with content
        return await articleReviewAPI.analyzeSection(section, content, selectedModel);
      } else {
        // This should never happen due to prior checks, but just in case
        throw new Error(`No content available for ${section} section`);
      }
    } catch (error) {
      console.error(`Error analyzing ${section}:`, error);
      
      // Specific error message for API key issues
      const errorMessage = error.message || 'An unknown error occurred';
      const isApiKeyError = errorMessage.includes('API key') || 
                           errorMessage.includes('authorization') || 
                           errorMessage.includes('401');
      
      // Return appropriate error-state result
      if (isApiKeyError) {
        return section === 'overall' ? {
          clarity: 0,
          structure: 0,
          methodology: 0,
          significance: 0,
          originality: 0,
          overallScore: 0,
          summary: "OpenRouter API key error. Please check your API key in the .env file (VITE_OPENROUTER_API_KEY).",
          majorStrengths: [],
          majorWeaknesses: ["API configuration issue detected"],
          recommendations: ["Check that VITE_OPENROUTER_API_KEY is properly set in your .env file"]
        } : {
          content: content?.substring(0, 100) + (content?.length > 100 ? "..." : "") || "",
          analysis: "OpenRouter API key error. Please check your API key configuration.",
          strengths: [],
          weaknesses: ["API configuration issue detected"],
          suggestions: ["Check that VITE_OPENROUTER_API_KEY is properly set in your .env file"],
          score: 0,
          detailedFeedback: [
            {
              originalText: "API Connection Error",
              issue: "Could not connect to OpenRouter API",
              suggestion: "Verify your API key in the .env file",
              type: 'content',
              severity: 'major'
            }
          ]
        };
      } else {
        // Generic error handling
        return section === 'overall' ? {
          clarity: 0,
          structure: 0,
          methodology: 0,
          significance: 0,
          originality: 0,
          overallScore: 0,
          summary: `Error during analysis: ${errorMessage}`,
          majorStrengths: [],
          majorWeaknesses: ["Analysis encountered an error"],
          recommendations: ["Try again or contact support"]
        } : {
          content: content?.substring(0, 100) + (content?.length > 100 ? "..." : "") || "",
          analysis: `Error during analysis: ${errorMessage}`,
          strengths: [],
          weaknesses: ["Analysis encountered an error"],
          suggestions: ["Try again or contact support"],
          score: 0,
          detailedFeedback: []
        };
      }
    }
  }, [selectedModel]);

  /**
   * Generate overall metrics from individual section reviews
   */
  const generateOverallMetrics = useCallback(async (results: Partial<ArticleReviewResults>) => {
    try {
      // First check if the API is configured
      const isApiConfigured = articleReviewAPI.isConfigured();

      if (!isApiConfigured) {
        throw new Error('OpenRouter API key not configured');
      }

      // Count the number of sections that were successfully parsed
      const validSections = Object.entries(results)
        .filter(([key]) => key !== 'overall')
        .filter(([, value]) => {
          const sectionReview = value as SectionReview;
          return sectionReview?.content && sectionReview.content.length > 0;
        });
      
      // Calculate average scores from existing section analyses
      const sectionScores = validSections.map(([, value]) => {
        const sectionReview = value as SectionReview;
        return sectionReview?.score || 0;
      });
      
      const avgScore = sectionScores.length > 0
        ? sectionScores.reduce((sum, score) => sum + score, 0) / sectionScores.length
        : 0;
      
      // If we have actual scores and content, use them for the overall metrics
      if (parsedContent && avgScore > 0) {
        try {
          // Try to analyze the overall document with the API
          return await articleReviewAPI.analyzeSection('overall', parsedContent.fullText, selectedModel) as ReviewMetrics;
        } catch (error) {
          console.error('Error analyzing overall metrics:', error);
          
          // Fallback to calculated metrics
          return {
            clarity: Math.round(avgScore),
            structure: Math.round(avgScore),
            methodology: Math.round(avgScore),
            significance: Math.round(avgScore),
            originality: Math.round(avgScore),
            overallScore: Math.round(avgScore),
            summary: `Based on analysis of ${validSections.length} sections, the document received an average score of ${Math.round(avgScore)}/100.`,
            majorStrengths: validSections.slice(0, 3).map(([key, value]) => {
              const sectionReview = value as SectionReview;
              return sectionReview.strengths[0] || `${key} section is well-structured`;
            }),
            majorWeaknesses: validSections.slice(0, 3).map(([key, value]) => {
              const sectionReview = value as SectionReview;
              return sectionReview.weaknesses[0] || `${key} section could be improved`;
            }),
            recommendations: [
              "Review each section's specific feedback",
              "Address the highest priority issues first",
              "Consider restructuring based on section scores"
            ]
          };
        }
      }
      
      // If API is configured but we don't have scores yet (should not happen normally)
      return {
        clarity: 0,
        structure: 0,
        methodology: 0,
        significance: 0,
        originality: 0,
        overallScore: 0,
        summary: `Document contains ${validSections.length} identifiable sections. Analysis is being processed.`,
        majorStrengths: [
          "Document successfully uploaded and parsed",
          `${validSections.length} document sections identified`
        ],
        majorWeaknesses: [
          "Analysis in progress"
        ],
        recommendations: [
          "Wait for the analysis to complete",
          "Review individual section feedback when available"
        ]
      };
    } catch (error) {
      // Handle API configuration or connection errors
      return {
        clarity: 0,
        structure: 0,
        methodology: 0,
        significance: 0,
        originality: 0,
        overallScore: 0,
        summary: `Error: ${error.message || 'Unknown error during analysis'}`,
        majorStrengths: [],
        majorWeaknesses: [
          "API configuration or connection error",
          error.message || 'Unknown error'
        ],
        recommendations: [
          "Check that VITE_OPENROUTER_API_KEY is set in your .env file",
          "Verify your internet connection",
          "Try again or contact support"
        ]
      };
    }
  }, [parsedContent, selectedModel]);

  /**
   * Effect to parse uploaded file when it changes
   */
  const parseUploadedFile = useCallback(async (file: File | null) => {
    if (!file) {
      setParsedContent(null);
      return;
    }
    
    try {
      let parsedData: ParsedArticle;
      
      if (file.type === 'application/pdf') {
        parsedData = await parsePdfFile(file);
      } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                file.name.endsWith('.docx')) {
        parsedData = await parseDocxFile(file);
      } else {
        throw new Error('Unsupported file format. Please upload a PDF or DOCX file.');
      }
      
      setParsedContent(parsedData);
      return parsedData;
    } catch (error) {
      console.error('Error parsing file:', error);
      setParsedContent(null);
      throw error;
    }
  }, [parsePdfFile, parseDocxFile]);

  // When article file changes, parse it
  useEffect(() => {
    if (articleFile) {
      parseUploadedFile(articleFile).catch(console.error);
    }
  }, [articleFile, parseUploadedFile]);

  return {
    // File state
    articleFile,
    setArticleFile,
    parsedContent,
    parseUploadedFile,
    
    // Review state and controls
    reviewResults,
    isAnalyzing,
    progress,
    currentSection,
    startReview,
    cancelReview,
    
    // AI model selection
    selectedModel,
    setSelectedModel,
    availableModels,
  };
};

export default useArticleReview;
