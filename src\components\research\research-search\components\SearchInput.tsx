/**
 * Search Input Component
 * <PERSON>les user input for research search queries
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Lightbulb, X, Search, BookOpen, Zap, Sparkles, Brain, Rocket, GraduationCap, FileText, Microscope, Wand2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  onSubmit: (query: string) => void;
  onDeepResearch?: (query: string) => void;
  onPromptEnhance?: (query: string) => void;
  isLoading?: boolean;
  isDeepResearchLoading?: boolean;
  placeholder?: string;
  suggestions?: string[];
  onSuggestionClick?: (suggestion: string) => void;
  disabled?: boolean;
  showDeepResearch?: boolean;
  showPromptEnhance?: boolean;
  className?: string;
}

export function SearchInput({
  onSubmit,
  onDeepResearch,
  onPromptEnhance,
  isLoading = false,
  isDeepResearchLoading = false,
  placeholder = "Ask a research question...",
  suggestions = [],
  onSuggestionClick,
  disabled = false,
  showDeepResearch = true,
  showPromptEnhance = true,
  className
}: SearchInputProps) {
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [hasShownSuggestions, setHasShownSuggestions] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [query]);

  const handleSubmit = () => {
    if (!query.trim() || isLoading || isDeepResearchLoading || disabled) return;

    onSubmit(query.trim());
    setQuery('');
    setShowSuggestions(false);
  };

  const handleDeepResearch = () => {
    if (!query.trim() || isLoading || isDeepResearchLoading || disabled || !onDeepResearch) return;

    onDeepResearch(query.trim());
    setQuery('');
    setShowSuggestions(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    setHasShownSuggestions(true);
    onSuggestionClick?.(suggestion);
    textareaRef.current?.focus();
  };

  const handleFocus = () => {
    if (!hasShownSuggestions) {
      setShowSuggestions(true);
      setHasShownSuggestions(true);
    }
  };

  const clearQuery = () => {
    setQuery('');
    textareaRef.current?.focus();
  };

  // Sample research suggestions if none provided
  const defaultSuggestions = [
    "What are the latest developments in artificial intelligence?",
    "Climate change impact on biodiversity",
    "Machine learning applications in healthcare",
    "Renewable energy technologies comparison",
    "Social media effects on mental health",
    "Detailed analysis of global warming problems and achievements",
    "Comprehensive review of quantum computing limitations and breakthroughs",
    "In-depth study of renewable energy adoption challenges"
  ];

  const displaySuggestions = suggestions.length > 0 ? suggestions : defaultSuggestions;

  return (
    <div className={cn("space-y-8", className)}>
      {/* Research Options & Suggestions */}
      {showSuggestions && (
        <div className="space-y-4">
          {/* Research Methods */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4 border border-indigo-200 bg-gradient-to-br from-indigo-50 to-blue-50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <Search className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Quick Search</h4>
                  <p className="text-sm text-gray-600">Instant answers with citations</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 border border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Deep Research</h4>
                  <p className="text-sm text-gray-600">Comprehensive analysis</p>
                </div>
              </div>
            </Card>

            {showPromptEnhance && onPromptEnhance && (
              <Card
                className="p-4 border border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50 cursor-pointer hover:shadow-md transition-all duration-200"
                onClick={() => query.trim() && onPromptEnhance(query)}
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center">
                    <Wand2 className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Enhance Prompt</h4>
                    <p className="text-sm text-gray-600">Transform into detailed research spec</p>
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Quick Suggestions */}
          {displaySuggestions.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Popular Topics</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {displaySuggestions.slice(0, 4).map((suggestion, index) => (
                  <Card
                    key={index}
                    className="p-3 cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-indigo-300 bg-white hover:bg-indigo-50"
                    onClick={() => handleSuggestionSelect(suggestion)}
                  >
                    <p className="text-sm text-gray-700 hover:text-indigo-700 line-clamp-2">
                      {suggestion}
                    </p>
                  </Card>
                ))}
              </div>
            </div>
          )}

          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSuggestions(false)}
              className="text-gray-500 hover:text-gray-700 text-xs"
            >
              <X className="w-3 h-3 mr-1" />
              Hide
            </Button>
          </div>
        </div>
      )}

      {/* Premium Academic Input Area */}
      <div className="relative">
        <div className="relative">
          {/* Clean Academic Input Container */}
          <div className="flex items-center gap-4 p-4 border border-gray-300 rounded-xl bg-white shadow-sm focus-within:border-indigo-500 focus-within:shadow-md transition-all duration-200">
            <div className="flex-1 relative">
              <Textarea
                ref={textareaRef}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={handleFocus}
                placeholder={placeholder}
                disabled={disabled || isLoading || isDeepResearchLoading}
                className="min-h-[50px] max-h-32 resize-none border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent text-gray-900 placeholder:text-gray-500 text-base leading-relaxed"
                rows={2}
              />

              {query && !isLoading && !isDeepResearchLoading && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearQuery}
                  className="absolute right-1 top-1 h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
                >
                  <X className="w-3 h-3" />
                </Button>
              )}
            </div>

            {/* Clean Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                onClick={handleSubmit}
                disabled={!query.trim() || isLoading || isDeepResearchLoading || disabled}
                className="h-12 px-6 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Searching...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Search className="w-4 h-4" />
                    <span>Search</span>
                  </div>
                )}
              </Button>

              {showDeepResearch && onDeepResearch && (
                <Button
                  onClick={handleDeepResearch}
                  disabled={!query.trim() || isLoading || isDeepResearchLoading || disabled}
                  variant="outline"
                  className="h-12 px-6 border-purple-300 text-purple-700 hover:bg-purple-50 font-medium rounded-lg transition-colors relative group"
                >
                  {isDeepResearchLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Analyzing Query...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <GraduationCap className="w-4 h-4" />
                      <span>Academic Research</span>
                      <Badge variant="secondary" className="ml-1 text-xs bg-purple-100 text-purple-700">
                        Enhanced
                      </Badge>
                    </div>
                  )}

                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10">
                    Choose research type: Literature Review, Academic Book, Research Paper, etc.
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                  </div>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Simple Help Text */}
        <div className="flex items-center justify-between mt-4 text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Enter</kbd> to search</span>
            <span><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Shift+Enter</kbd> for new line</span>
          </div>
          <span>{query.length}/500</span>
        </div>
      </div>

      {/* Simple Actions */}
      {showSuggestions && (
        <div className="mt-4 text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSuggestions(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4 mr-1" />
            Hide suggestions
          </Button>
        </div>
      )}
    </div>
  );
}
