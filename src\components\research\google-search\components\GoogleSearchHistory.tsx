/**
 * Google Search History Component
 * Shows search history and session management
 */

import React, { useState, useEffect } from 'react';
import { History, Search, Trash2, X, Clock, MessageSquare } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

import { GoogleSearchSession } from '../types';
import { googleSearchHistoryService } from '../services/google-search-history.service';

interface GoogleSearchHistoryProps {
  userId: string;
  onSessionSelect: (session: GoogleSearchSession) => void;
  onClose: () => void;
  className?: string;
}

export function GoogleSearchHistory({
  userId,
  onSessionSelect,
  onClose,
  className = ''
}: GoogleSearchHistoryProps) {
  const [sessions, setSessions] = useState<GoogleSearchSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);

  useEffect(() => {
    loadSessions();
  }, [userId]);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      const userSessions = await googleSearchHistoryService.getUserSessions(userId);
      setSessions(userSessions);
    } catch (error) {
      console.error('Failed to load Google search sessions:', error);
      toast.error('Failed to load search history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSessionSelect = async (sessionId: string) => {
    try {
      setSelectedSessionId(sessionId);
      const session = await googleSearchHistoryService.getSession(sessionId);
      if (session) {
        onSessionSelect(session);
      } else {
        toast.error('Failed to load session');
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      toast.error('Failed to load session');
    } finally {
      setSelectedSessionId(null);
    }
  };

  const handleDeleteSession = async (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!confirm('Are you sure you want to delete this search session?')) {
      return;
    }

    try {
      await googleSearchHistoryService.deleteSession(sessionId);
      setSessions(prev => prev.filter(s => s.id !== sessionId));
      toast.success('Session deleted successfully');
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast.error('Failed to delete session');
    }
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const getSessionPreview = (session: GoogleSearchSession) => {
    const userMessages = session.messages?.filter(m => m.type === 'user') || [];
    if (userMessages.length > 0) {
      return userMessages[userMessages.length - 1].content.substring(0, 100) + '...';
    }
    return 'No messages yet';
  };

  return (
    <div className={`h-full flex flex-col bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-2">
          <History className="h-5 w-5 text-gray-600" />
          <h2 className="font-semibold text-gray-900">Search History</h2>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Sessions List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : sessions.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No search history</h3>
              <p className="text-gray-600">
                Your Google search sessions will appear here.
              </p>
            </div>
          ) : (
            sessions.map((session) => (
              <Card
                key={session.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedSessionId === session.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-medium text-gray-900 truncate">
                          {session.title}
                        </h3>
                        {session.isActive && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Active
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {getSessionPreview(session)}
                      </p>
                      
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatDate(session.updatedAt)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3 w-3" />
                          <span>{session.messages?.length || 0} messages</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Search className="h-3 w-3" />
                          <span>{session.totalQueries} queries</span>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => handleDeleteSession(session.id, e)}
                      className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          {sessions.length} search session{sessions.length !== 1 ? 's' : ''} total
        </div>
      </div>
    </div>
  );
}

export default GoogleSearchHistory;
