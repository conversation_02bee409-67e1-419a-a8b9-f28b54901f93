/**
 * Google Search Interface
 * Main component for Google Search research functionality
 */

import React, { useState, useEffect, useRef } from 'react';
import { Search, Loader2, AlertCircle, RefreshCw, History, BookOpen, Brain, GraduationCap, Sparkles, ExternalLink, Quote, FileText } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

import { GoogleSearchInput } from './GoogleSearchInput';
import { GoogleSearchMessage } from './GoogleSearchMessage';
import { GoogleSearchHistory } from './GoogleSearchHistory';
import { GoogleSearchProgress } from './GoogleSearchProgress';
import { GoogleSearchResults } from './GoogleSearchResults';
import { DeepResearchOutline } from './DeepResearchOutline';
import { DeepResearchProgress } from './DeepResearchProgress';
import { DeepResearchResults } from './DeepResearchResults';
import { DeepResearchCostEstimate } from './DeepResearchCostEstimate';

import {
  GoogleSearchSession,
  GoogleSearchMessage as GoogleSearchMessageType,
  GoogleSearchProgress as GoogleSearchProgressType,
  GoogleSearchOptions,
  GoogleUserPreferences,
  GoogleSearchSource,
  GoogleReference,
  DeepResearchSession,
  ResearchOutline,
  DeepResearchOptions,
  DeepResearchProgress as DeepResearchProgressType,
  ResearchSection
} from '../types';

import { googleSearchService } from '../services/google-search.service';
import { googleAcademicService } from '../services/google-academic.service';
import { googleSearchHistoryService } from '../services/google-search-history.service';
import { deepResearchCostService } from '../services/deep-research-cost.service';
import { supabase } from '@/lib/supabase';

interface GoogleSearchInterfaceProps {
  userId?: string;
  className?: string;
}

export function GoogleSearchInterface({ userId, className = '' }: GoogleSearchInterfaceProps) {
  // State management
  const [currentUserId, setCurrentUserId] = useState<string | null>(userId || null);
  const [currentSession, setCurrentSession] = useState<GoogleSearchSession | null>(null);
  const [messages, setMessages] = useState<GoogleSearchMessageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchProgress, setSearchProgress] = useState<GoogleSearchProgressType | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Deep Research state
  const [researchMode, setResearchMode] = useState<'standard' | 'deep'>('standard');
  const [deepResearchSession, setDeepResearchSession] = useState<DeepResearchSession | null>(null);
  const [deepResearchProgress, setDeepResearchProgress] = useState<DeepResearchProgressType | null>(null);
  const [currentOutline, setCurrentOutline] = useState<ResearchOutline | null>(null);
  const [showCostEstimate, setShowCostEstimate] = useState(false);
  const [pendingQuery, setPendingQuery] = useState<string>('');
  const [deepResearchOptions, setDeepResearchOptions] = useState<DeepResearchOptions>({
    maxSubtopics: 6,
    wordsPerSection: 700,
    researchDepth: 'advanced',
    includeExecutiveSummary: true,
    citationStyle: 'apa',
    targetAudience: 'professional',
    allowOutlineEditing: true
  });

  // User preferences
  const [preferences, setPreferences] = useState<GoogleUserPreferences>({
    defaultSearchDepth: 'advanced',
    maxResults: 10,
    citationStyle: 'apa',
    academicFocus: true,
    includeRecentOnly: false
  });

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Initialize component
  useEffect(() => {
    initializeUser();
  }, [userId]);

  useEffect(() => {
    if (currentUserId) {
      initializeSession();
      loadRecentSearches();
    }
  }, [currentUserId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  /**
   * Initialize user authentication
   */
  const initializeUser = async () => {
    try {
      if (userId) {
        setCurrentUserId(userId);
        return;
      }

      // Get current user from Supabase auth
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
      } else {
        // For demo purposes, use a demo user ID
        setCurrentUserId('demo-user');
      }
    } catch (error) {
      console.error('Failed to initialize user:', error);
      setCurrentUserId('demo-user');
    }
  };

  /**
   * Initialize a new search session
   */
  const initializeSession = async () => {
    if (!currentUserId) return;

    try {
      const session = await googleSearchHistoryService.createSession(
        currentUserId,
        'Google Search Session'
      );
      setCurrentSession(session);
      setMessages([]);
      setError(null);
    } catch (error) {
      console.error('Failed to initialize Google search session:', error);
      setError('Failed to initialize search session');
    }
  };

  /**
   * Load recent searches
   */
  const loadRecentSearches = async () => {
    if (!currentUserId) return;

    try {
      const recent = await googleSearchHistoryService.getRecentSearches(currentUserId, 10);
      setRecentSearches(recent);
    } catch (error) {
      console.error('Failed to load recent searches:', error);
    }
  };

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Handle search submission
   */
  const handleSearch = async (query: string) => {
    if (!currentSession || !query.trim()) return;

    setIsLoading(true);
    setError(null);
    setSearchProgress({ stage: 'searching', message: 'Searching with Google...', progress: 10 });

    try {
      // Add user message
      const userMessage: GoogleSearchMessageType = {
        id: `user-${Date.now()}`,
        type: 'user',
        content: query,
        timestamp: new Date(),
        searchQuery: query,
        isGoogleSearch: true
      };

      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);

      // Save user message
      try {
        await googleSearchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.warn('Database error, continuing with search:', dbError);
      }

      // Update progress
      setSearchProgress({ stage: 'searching', message: 'Performing Google Search...', progress: 30 });

      // Perform Google search
      const searchOptions: GoogleSearchOptions = {
        maxResults: preferences.maxResults,
        searchDepth: preferences.defaultSearchDepth,
        includeAcademicSources: preferences.academicFocus,
        citationStyle: preferences.citationStyle,
        academicFocus: preferences.academicFocus,
        includeRecentOnly: preferences.includeRecentOnly
      };

      const searchResult = await googleSearchService.searchAcademic(query, searchOptions);

      // Update progress
      setSearchProgress({ stage: 'analyzing', message: 'Analyzing results...', progress: 60 });

      // Format academic response
      const { formattedContent, references } = googleAcademicService.formatAcademicResponse(
        searchResult.answer,
        searchResult.sources,
        preferences.citationStyle
      );

      // Update progress
      setSearchProgress({ stage: 'generating_citations', message: 'Generating citations...', progress: 80 });

      // Generate citations
      const citations = googleAcademicService.generateInTextCitations(
        searchResult.sources,
        preferences.citationStyle
      );

      // Create assistant message
      const assistantMessage: GoogleSearchMessageType = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: formattedContent,
        timestamp: new Date(),
        searchQuery: query,
        sources: searchResult.sources,
        citations,
        references,
        isGoogleSearch: true
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      setMessages(finalMessages);

      // Save assistant message
      try {
        await googleSearchHistoryService.addMessage(currentSession.id, assistantMessage);
      } catch (dbError) {
        console.warn('Database error, continuing:', dbError);
      }

      // Update progress
      setSearchProgress({ stage: 'complete', message: 'Search completed!', progress: 100 });

      // Update recent searches
      await loadRecentSearches();

      // Clear progress after delay
      setTimeout(() => {
        setSearchProgress(null);
      }, 2000);

    } catch (error) {
      console.error('Google search failed:', error);
      setError(`Search failed: ${error.message}`);
      
      // Add error message
      const errorMessage: GoogleSearchMessageType = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `I apologize, but the Google search encountered an error: ${error.message}. Please try again or rephrase your query.`,
        timestamp: new Date(),
        isGoogleSearch: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setSearchProgress(null);
    }
  };

  /**
   * Handle recent search selection
   */
  const handleRecentSearchSelect = (query: string) => {
    handleSearch(query);
  };

  /**
   * Clear current session
   */
  const handleClearSession = () => {
    setMessages([]);
    setError(null);
    initializeSession();
  };

  /**
   * Test Google Search API
   */
  const handleTestAPI = async () => {
    try {
      setIsLoading(true);
      const isConnected = await googleSearchService.testConnection();
      if (isConnected) {
        toast.success('Google Search API is working correctly!');
      } else {
        toast.error('Google Search API connection failed');
      }
    } catch (error) {
      toast.error(`API test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // ===== DEEP RESEARCH METHODS =====

  /**
   * Start deep research process with cost estimation
   */
  const handleDeepResearch = async (query: string) => {
    if (!query.trim()) return;

    // Show cost estimate first
    setPendingQuery(query);
    setShowCostEstimate(true);
  };

  /**
   * Proceed with deep research after cost approval
   */
  const handleProceedWithResearch = async () => {
    if (!pendingQuery.trim()) return;

    setShowCostEstimate(false);
    setIsLoading(true);
    setError(null);
    setResearchMode('deep');

    try {
      // Record API usage for rate limiting
      deepResearchCostService.recordApiUsage(1000); // Estimate for outline generation

      // Create deep research session
      const session: DeepResearchSession = {
        id: `deep-research-${Date.now()}`,
        userId: currentUserId || 'demo-user',
        query: pendingQuery,
        outline: null,
        researchData: [],
        sections: [],
        status: 'outline',
        progress: {
          phase: 'outline',
          currentStep: 'Generating research outline...',
          completedSteps: 0,
          totalSteps: 4,
          percentage: 10
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setDeepResearchSession(session);
      setDeepResearchProgress(session.progress);

      // Generate outline
      const outline = await googleSearchService.generateResearchOutline(pendingQuery, deepResearchOptions);

      const updatedSession = {
        ...session,
        outline,
        progress: {
          ...session.progress,
          currentStep: 'Outline generated. Review and proceed to research.',
          completedSteps: 1,
          percentage: 25
        }
      };

      setDeepResearchSession(updatedSession);
      setCurrentOutline(outline);
      setDeepResearchProgress(updatedSession.progress);

    } catch (error) {
      console.error('Deep research failed:', error);
      setError(`Deep research failed: ${error.message}`);
    } finally {
      setIsLoading(false);
      setPendingQuery('');
    }
  };

  /**
   * Cancel cost estimation
   */
  const handleCancelCostEstimate = () => {
    setShowCostEstimate(false);
    setPendingQuery('');
  };

  /**
   * Proceed with research after outline approval
   */
  const handleProceedToResearch = async () => {
    if (!deepResearchSession || !currentOutline) return;

    setIsLoading(true);

    try {
      // Update progress to research phase
      const researchProgress = {
        phase: 'research' as const,
        currentStep: 'Conducting comprehensive research...',
        completedSteps: 1,
        totalSteps: 2 + currentOutline.subtopics.length,
        percentage: 30
      };

      setDeepResearchProgress(researchProgress);

      // Batch research all subtopics
      const researchData = await googleSearchService.batchResearchSubtopics(
        currentOutline.subtopics,
        {
          maxResults: 8,
          searchDepth: deepResearchOptions.researchDepth,
          citationStyle: deepResearchOptions.citationStyle,
          academicFocus: true
        }
      );

      // Update session with research data
      const updatedSession = {
        ...deepResearchSession,
        researchData,
        status: 'writing' as const,
        progress: {
          phase: 'writing' as const,
          currentStep: 'Research completed. Starting section generation...',
          completedSteps: 2,
          totalSteps: 2 + currentOutline.subtopics.length,
          percentage: 50
        }
      };

      setDeepResearchSession(updatedSession);
      setDeepResearchProgress(updatedSession.progress);

      // Generate sections sequentially
      await generateResearchSections(updatedSession, researchData);

    } catch (error) {
      console.error('Research phase failed:', error);
      setError(`Research failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Generate research sections sequentially
   */
  const generateResearchSections = async (
    session: DeepResearchSession,
    researchData: any[]
  ) => {
    const sections: ResearchSection[] = [];

    for (let i = 0; i < session.outline!.subtopics.length; i++) {
      const subtopic = session.outline!.subtopics[i];

      // Update progress
      const sectionProgress = {
        phase: 'writing' as const,
        currentStep: `Writing section: ${subtopic.title}`,
        currentSubtopic: subtopic.title,
        completedSteps: 2 + i,
        totalSteps: 2 + session.outline!.subtopics.length,
        percentage: 50 + ((i / session.outline!.subtopics.length) * 40)
      };

      setDeepResearchProgress(sectionProgress);

      try {
        const section = await googleSearchService.generateResearchSection(
          subtopic,
          researchData,
          sections,
          deepResearchOptions
        );

        sections.push(section);

        // Update session with new section
        const updatedSession = {
          ...session,
          sections: [...sections]
        };
        setDeepResearchSession(updatedSession);

      } catch (error) {
        console.error(`Failed to generate section ${subtopic.title}:`, error);
        // Continue with other sections
      }
    }

    // Final assembly
    const finalProgress = {
      phase: 'completed' as const,
      currentStep: 'Research report completed!',
      completedSteps: 2 + session.outline!.subtopics.length,
      totalSteps: 2 + session.outline!.subtopics.length,
      percentage: 100
    };

    setDeepResearchProgress(finalProgress);

    const finalSession = {
      ...session,
      sections,
      status: 'completed' as const,
      progress: finalProgress,
      updatedAt: new Date()
    };

    setDeepResearchSession(finalSession);
  };

  /**
   * Regenerate outline
   */
  const handleRegenerateOutline = async () => {
    if (!deepResearchSession) return;

    setIsLoading(true);
    try {
      const newOutline = await googleSearchService.generateResearchOutline(
        deepResearchSession.query,
        deepResearchOptions
      );
      setCurrentOutline(newOutline);

      const updatedSession = {
        ...deepResearchSession,
        outline: newOutline
      };
      setDeepResearchSession(updatedSession);

    } catch (error) {
      setError(`Failed to regenerate outline: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Start new research
   */
  const handleNewResearch = () => {
    setResearchMode('standard');
    setDeepResearchSession(null);
    setDeepResearchProgress(null);
    setCurrentOutline(null);
    setShowCostEstimate(false);
    setPendingQuery('');
    setError(null);
  };

  return (
    <div className={`flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Search className="h-6 w-6 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                {researchMode === 'deep' ? 'Deep Research' : 'Google Search Research'}
              </h1>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              <Brain className="h-3 w-3 mr-1" />
              {researchMode === 'deep' ? 'Deep Research Mode' : 'Academic Mode'}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            {/* Research Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <Button
                variant={researchMode === 'standard' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResearchMode('standard')}
                className="text-xs"
              >
                Standard
              </Button>
              <Button
                variant={researchMode === 'deep' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setResearchMode('deep')}
                className="text-xs"
              >
                Deep Research
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              className="text-gray-600"
            >
              <History className="h-4 w-4 mr-1" />
              History
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleTestAPI}
              disabled={isLoading}
              className="text-gray-600"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Test API
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={researchMode === 'deep' ? handleNewResearch : handleClearSession}
              className="text-gray-600"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              {researchMode === 'deep' ? 'New Research' : 'New Session'}
            </Button>
          </div>
        </div>

        {/* Search Progress */}
        {searchProgress && researchMode === 'standard' && (
          <div className="mt-4">
            <GoogleSearchProgress progress={searchProgress} />
          </div>
        )}

        {/* Deep Research Progress */}
        {deepResearchProgress && researchMode === 'deep' && (
          <div className="mt-4">
            <DeepResearchProgress
              progress={deepResearchProgress}
              subtopics={currentOutline?.subtopics || []}
              completedSections={deepResearchSession?.sections || []}
            />
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Content Area */}
        <div className="flex-1 flex flex-col">
          {researchMode === 'standard' ? (
            <>
              {/* Standard Search Messages */}
              <ScrollArea className="flex-1 px-6 py-4" ref={messagesContainerRef}>
                <div className="max-w-4xl mx-auto space-y-6">
                  {messages.length === 0 ? (
                    <div className="text-center py-12">
                      <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Welcome to Google Search Research
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Ask any research question and get comprehensive answers with proper academic citations.
                      </p>

                      {recentSearches.length > 0 && (
                        <div className="max-w-md mx-auto">
                          <h4 className="text-sm font-medium text-gray-700 mb-3">Recent Searches:</h4>
                          <div className="space-y-2">
                            {recentSearches.slice(0, 3).map((search, index) => (
                              <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                onClick={() => handleRecentSearchSelect(search)}
                                className="w-full text-left justify-start"
                              >
                                <Search className="h-3 w-3 mr-2" />
                                {search}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    messages.map((message) => (
                      <GoogleSearchMessage
                        key={message.id}
                        message={message}
                        onSourceClick={(url) => window.open(url, '_blank')}
                      />
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
            </>
          ) : (
            <>
              {/* Deep Research Content */}
              <ScrollArea className="flex-1 px-6 py-4">
                <div className="max-w-6xl mx-auto space-y-6">
                  {showCostEstimate ? (
                    <DeepResearchCostEstimate
                      query={pendingQuery}
                      options={deepResearchOptions}
                      outline={currentOutline}
                      onProceed={handleProceedWithResearch}
                      onCancel={handleCancelCostEstimate}
                      onOptimize={setDeepResearchOptions}
                    />
                  ) : !deepResearchSession ? (
                    <div className="text-center py-12">
                      <BookOpen className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Welcome to Deep Research
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Generate comprehensive, multi-section research reports with detailed analysis and citations.
                      </p>
                    </div>
                  ) : deepResearchSession.status === 'outline' && currentOutline ? (
                    <DeepResearchOutline
                      outline={currentOutline}
                      options={deepResearchOptions}
                      isEditable={true}
                      onOutlineChange={setCurrentOutline}
                      onOptionsChange={setDeepResearchOptions}
                      onProceedToResearch={handleProceedToResearch}
                      onRegenerateOutline={handleRegenerateOutline}
                      isGenerating={isLoading}
                    />
                  ) : deepResearchSession.status === 'completed' ? (
                    <DeepResearchResults
                      session={deepResearchSession}
                      onNewResearch={handleNewResearch}
                      onRegenerateSection={(sectionId) => {
                        // TODO: Implement section regeneration
                        console.log('Regenerate section:', sectionId);
                      }}
                    />
                  ) : (
                    <div className="text-center py-12">
                      <Loader2 className="h-12 w-12 text-blue-400 mx-auto mb-4 animate-spin" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {deepResearchProgress?.currentStep || 'Processing...'}
                      </h3>
                      <p className="text-gray-600">
                        Please wait while we conduct your deep research.
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </>
          )}

          {/* Error Display */}
          {error && (
            <div className="px-6 py-2">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
          )}

          {/* Input Area */}
          <div className="border-t border-gray-200 bg-white px-6 py-4">
            <div className="max-w-4xl mx-auto">
              <GoogleSearchInput
                onSubmit={researchMode === 'deep' ? handleDeepResearch : handleSearch}
                isLoading={isLoading}
                placeholder={
                  researchMode === 'deep'
                    ? "Enter your research topic for comprehensive analysis (e.g., 'Impact of artificial intelligence on healthcare')"
                    : "Ask your research question (e.g., 'What are the latest developments in artificial intelligence?')"
                }
                disabled={researchMode === 'standard' ? !currentSession : false}
              />
            </div>
          </div>
        </div>

        {/* History Sidebar */}
        {showHistory && (
          <div className="w-80 border-l border-gray-200 bg-white">
            <GoogleSearchHistory
              userId={currentUserId || 'demo-user'}
              onSessionSelect={(session) => {
                setCurrentSession(session);
                setMessages(session.messages);
                setShowHistory(false);
              }}
              onClose={() => setShowHistory(false)}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default GoogleSearchInterface;
