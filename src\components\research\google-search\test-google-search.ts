/**
 * Google Search API Test
 * Test file to validate Google Search functionality
 */

import { googleSearchService } from './services/google-search.service';
import { googleAcademicService } from './services/google-academic.service';
import { googleSearchHistoryService } from './services/google-search-history.service';

/**
 * Test Google Search API connectivity
 */
export async function testGoogleSearchAPI() {
  console.log('🔍 Testing Google Search API...');
  
  try {
    // Test API configuration
    const isConfigured = googleSearchService.isConfigured();
    console.log('✅ API Configuration:', isConfigured ? 'Valid' : 'Invalid');
    
    if (!isConfigured) {
      throw new Error('Google Search API not properly configured');
    }

    // Test connection
    const isConnected = await googleSearchService.testConnection();
    console.log('✅ API Connection:', isConnected ? 'Success' : 'Failed');
    
    if (!isConnected) {
      throw new Error('Google Search API connection failed');
    }

    return { success: true, configured: isConfigured, connected: isConnected };
  } catch (error) {
    console.error('❌ Google Search API test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test Google Search functionality
 */
export async function testGoogleSearch() {
  console.log('🔍 Testing Google Search functionality...');
  
  try {
    const query = 'artificial intelligence machine learning';
    const options = {
      maxResults: 5,
      searchDepth: 'basic' as const,
      includeAcademicSources: true,
      citationStyle: 'apa' as const,
      academicFocus: true
    };

    console.log(`🔍 Searching for: "${query}"`);
    
    const result = await googleSearchService.searchAcademic(query, options);
    
    console.log('✅ Search Results:', {
      query: result.query,
      answerLength: result.answer.length,
      sourcesCount: result.sources.length,
      responseTime: result.responseTime,
      totalResults: result.totalResults
    });

    // Validate result structure
    if (!result.query || !result.answer || !Array.isArray(result.sources)) {
      throw new Error('Invalid search result structure');
    }

    return { success: true, result };
  } catch (error) {
    console.error('❌ Google Search test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test Academic Formatting
 */
export async function testAcademicFormatting() {
  console.log('📚 Testing Academic Formatting...');
  
  try {
    // Mock search sources for testing
    const mockSources = [
      {
        id: 'test-1',
        title: 'Artificial Intelligence: A Modern Approach',
        url: 'https://example.com/ai-book',
        snippet: 'This book provides a comprehensive introduction to artificial intelligence.',
        domain: 'example.com',
        score: 0.95,
        type: 'academic' as const,
        authors: ['Stuart Russell', 'Peter Norvig'],
        year: 2020
      },
      {
        id: 'test-2',
        title: 'Machine Learning Fundamentals',
        url: 'https://journal.example.com/ml-paper',
        snippet: 'A comprehensive review of machine learning algorithms.',
        domain: 'journal.example.com',
        score: 0.88,
        type: 'journal' as const,
        journal: 'Journal of AI Research',
        year: 2023
      }
    ];

    // Test APA formatting
    const apaReferences = googleAcademicService.generateReferences(mockSources, 'apa');
    console.log('✅ APA References generated:', apaReferences.length);

    // Test in-text citations
    const citations = googleAcademicService.generateInTextCitations(mockSources, 'apa');
    console.log('✅ In-text citations generated:', citations.length);

    // Test academic response formatting
    const mockContent = 'Artificial intelligence has revolutionized many fields. Machine learning algorithms continue to advance rapidly.';
    const { formattedContent, references } = googleAcademicService.formatAcademicResponse(
      mockContent,
      mockSources,
      'apa'
    );

    console.log('✅ Academic formatting:', {
      originalLength: mockContent.length,
      formattedLength: formattedContent.length,
      referencesCount: references.length
    });

    return { success: true, apaReferences, citations, formattedContent, references };
  } catch (error) {
    console.error('❌ Academic formatting test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test Search History Service
 */
export async function testSearchHistory() {
  console.log('📝 Testing Search History Service...');
  
  try {
    const userId = 'test-user-' + Date.now();
    
    // Test session creation
    const session = await googleSearchHistoryService.createSession(userId, 'Test Session');
    console.log('✅ Session created:', session.id);

    // Test message addition
    const testMessage = {
      id: 'test-msg-' + Date.now(),
      type: 'user' as const,
      content: 'Test search query',
      timestamp: new Date(),
      searchQuery: 'test query',
      isGoogleSearch: true
    };

    await googleSearchHistoryService.addMessage(session.id, testMessage);
    console.log('✅ Message added to session');

    // Test session retrieval
    const retrievedSession = await googleSearchHistoryService.getSession(session.id);
    console.log('✅ Session retrieved:', {
      id: retrievedSession?.id,
      messagesCount: retrievedSession?.messages.length || 0
    });

    // Test session cleanup
    await googleSearchHistoryService.deleteSession(session.id);
    console.log('✅ Session cleaned up');

    return { success: true, sessionId: session.id };
  } catch (error) {
    console.error('❌ Search history test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Run comprehensive Google Search tests
 */
export async function runGoogleSearchTests() {
  console.log('🚀 Running Google Search Tests...\n');

  const results = {
    apiTest: await testGoogleSearchAPI(),
    searchTest: await testGoogleSearch(),
    formattingTest: await testAcademicFormatting(),
    historyTest: await testSearchHistory()
  };

  console.log('\n📊 Test Results Summary:');
  console.log('API Test:', results.apiTest.success ? '✅ PASS' : '❌ FAIL');
  console.log('Search Test:', results.searchTest.success ? '✅ PASS' : '❌ FAIL');
  console.log('Formatting Test:', results.formattingTest.success ? '✅ PASS' : '❌ FAIL');
  console.log('History Test:', results.historyTest.success ? '✅ PASS' : '❌ FAIL');

  const allPassed = Object.values(results).every(result => result.success);
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

  return { success: allPassed, results };
}

// Export for browser testing
if (typeof window !== 'undefined') {
  (window as any).testGoogleSearch = {
    runAll: runGoogleSearchTests,
    testAPI: testGoogleSearchAPI,
    testSearch: testGoogleSearch,
    testFormatting: testAcademicFormatting,
    testHistory: testSearchHistory
  };
}
