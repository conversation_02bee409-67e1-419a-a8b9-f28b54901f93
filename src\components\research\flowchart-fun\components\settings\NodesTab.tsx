/**
 * Nodes Settings Tab
 * Configure node appearance and behavior
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SettingsTabProps, Shape } from '../../types';

const shapeOptions: { value: Shape; label: string; description: string }[] = [
  { value: 'rectangle', label: 'Rectangle', description: 'Sharp corners, clean look' },
  { value: 'roundrectangle', label: 'Rounded Rectangle', description: 'Soft corners, modern feel' },
  { value: 'ellipse', label: 'Ellipse', description: 'Oval shape, organic appearance' },
];

const NodesTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const handleShapeChange = (shape: Shape) => {
    onThemeChange({ shape });
  };

  const handleNodeBackgroundChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onThemeChange({ nodeBackground: e.target.value });
  };

  const handleNodeForegroundChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onThemeChange({ nodeForeground: e.target.value });
  };

  const handleBorderColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onThemeChange({ borderColor: e.target.value });
  };

  const handlePaddingChange = (value: number[]) => {
    onThemeChange({ padding: value[0] });
  };

  const handleBorderWidthChange = (value: number[]) => {
    onThemeChange({ borderWidth: value[0] });
  };

  const handleTextMaxWidthChange = (value: number[]) => {
    onThemeChange({ textMaxWidth: value[0] });
  };

  const handleTextMarginYChange = (value: number[]) => {
    onThemeChange({ textMarginY: value[0] });
  };

  const handleFixedHeightToggle = (checked: boolean) => {
    onThemeChange({ useFixedHeight: checked });
  };

  const handleFixedHeightChange = (value: number[]) => {
    onThemeChange({ fixedHeight: value[0] });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Node Shape & Style</CardTitle>
          <CardDescription>
            Configure the basic appearance of flowchart nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Shape Selection */}
          <div className="space-y-3">
            <Label htmlFor="shape-select">Shape</Label>
            <Select value={theme.shape} onValueChange={handleShapeChange}>
              <SelectTrigger id="shape-select">
                <SelectValue placeholder="Select node shape" />
              </SelectTrigger>
              <SelectContent>
                {shapeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Colors */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="node-background">Background Color</Label>
              <div className="flex items-center gap-3">
                <div 
                  className="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                  style={{ backgroundColor: theme.nodeBackground }}
                  onClick={() => document.getElementById('node-bg-input')?.click()}
                />
                <Input
                  id="node-bg-input"
                  type="color"
                  value={theme.nodeBackground}
                  onChange={handleNodeBackgroundChange}
                  className="w-16 h-10 p-1 cursor-pointer"
                />
                <Input
                  type="text"
                  value={theme.nodeBackground}
                  onChange={handleNodeBackgroundChange}
                  className="flex-1 font-mono text-sm"
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="node-foreground">Text Color</Label>
              <div className="flex items-center gap-3">
                <div 
                  className="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                  style={{ backgroundColor: theme.nodeForeground }}
                  onClick={() => document.getElementById('node-fg-input')?.click()}
                />
                <Input
                  id="node-fg-input"
                  type="color"
                  value={theme.nodeForeground}
                  onChange={handleNodeForegroundChange}
                  className="w-16 h-10 p-1 cursor-pointer"
                />
                <Input
                  type="text"
                  value={theme.nodeForeground}
                  onChange={handleNodeForegroundChange}
                  className="flex-1 font-mono text-sm"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Border & Spacing</CardTitle>
          <CardDescription>
            Adjust borders, padding, and spacing within nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Border */}
          <div className="space-y-4">
            <div className="space-y-3">
              <Label htmlFor="border-color">Border Color</Label>
              <div className="flex items-center gap-3">
                <div 
                  className="w-10 h-10 rounded border-2 border-gray-300 cursor-pointer"
                  style={{ backgroundColor: theme.borderColor }}
                  onClick={() => document.getElementById('border-color-input')?.click()}
                />
                <Input
                  id="border-color-input"
                  type="color"
                  value={theme.borderColor}
                  onChange={handleBorderColorChange}
                  className="w-16 h-10 p-1 cursor-pointer"
                />
                <Input
                  type="text"
                  value={theme.borderColor}
                  onChange={handleBorderColorChange}
                  className="flex-1 font-mono text-sm"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="border-width">Border Width</Label>
                <Badge variant="outline">{theme.borderWidth}px</Badge>
              </div>
              <Slider
                id="border-width"
                min={0}
                max={10}
                step={1}
                value={[theme.borderWidth]}
                onValueChange={handleBorderWidthChange}
                className="w-full"
              />
            </div>
          </div>

          {/* Padding */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="padding">Padding</Label>
              <Badge variant="outline">{theme.padding}px</Badge>
            </div>
            <Slider
              id="padding"
              min={0}
              max={30}
              step={1}
              value={[theme.padding]}
              onValueChange={handlePaddingChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Space between the node border and text content
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Text Layout</CardTitle>
          <CardDescription>
            Control how text is displayed within nodes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="text-max-width">Maximum Text Width</Label>
              <Badge variant="outline">{theme.textMaxWidth}px</Badge>
            </div>
            <Slider
              id="text-max-width"
              min={50}
              max={400}
              step={5}
              value={[theme.textMaxWidth]}
              onValueChange={handleTextMaxWidthChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Maximum width before text wraps to the next line
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="text-margin-y">Vertical Text Margin</Label>
              <Badge variant="outline">{theme.textMarginY}px</Badge>
            </div>
            <Slider
              id="text-margin-y"
              min={-10}
              max={20}
              step={1}
              value={[theme.textMarginY]}
              onValueChange={handleTextMarginYChange}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Additional vertical spacing around text
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="fixed-height-toggle">Use Fixed Height</Label>
              <Switch
                id="fixed-height-toggle"
                checked={theme.useFixedHeight}
                onCheckedChange={handleFixedHeightToggle}
              />
            </div>
            {theme.useFixedHeight && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="fixed-height">Fixed Height</Label>
                  <Badge variant="outline">{theme.fixedHeight}px</Badge>
                </div>
                <Slider
                  id="fixed-height"
                  min={20}
                  max={200}
                  step={5}
                  value={[theme.fixedHeight]}
                  onValueChange={handleFixedHeightChange}
                  className="w-full"
                />
              </div>
            )}
            <p className="text-sm text-muted-foreground">
              {theme.useFixedHeight 
                ? "All nodes will have the same height"
                : "Node height adjusts to content"
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NodesTab;
