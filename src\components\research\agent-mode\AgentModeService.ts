/**
 * AgentModeService - Core service for contextual document editing
 * Handles document analysis, section identification, and AI-powered editing
 */

import { nanoid } from 'nanoid';
import { DocumentAnalyzer } from './DocumentAnalyzer';
import { enhancedAIService } from '../paper-generator/enhanced-ai.service';
import {
  AgentModeRequest,
  AgentModeResult,
  DocumentStructure,
  TargetSection,
  EditInstruction,
  PromptAnalysis,
  SectionType,
  EditType,
  DocumentAnalysisProgress,
  DEFAULT_AGENT_MODE_OPTIONS
} from './types';
import {
  validatePrompt,
  validateDocumentContent,
  createAgentModeError,
  analyzePromptQuality
} from './utils';

export class AgentModeService {
  private static instance: AgentModeService;
  private documentAnalyzer: DocumentAnalyzer;
  private progressCallback?: (progress: DocumentAnalysisProgress) => void;

  private constructor() {
    this.documentAnalyzer = DocumentAnalyzer.getInstance();
  }

  public static getInstance(): AgentModeService {
    if (!AgentModeService.instance) {
      AgentModeService.instance = new AgentModeService();
    }
    return AgentModeService.instance;
  }

  /**
   * Execute an agent mode request
   */
  public async executeAgentModeRequest(
    request: AgentModeRequest,
    onProgress?: (progress: DocumentAnalysisProgress) => void
  ): Promise<AgentModeResult> {
    this.progressCallback = onProgress;
    const startTime = Date.now();

    try {
      // Validate inputs
      const promptValidation = validatePrompt(request.prompt);
      if (!promptValidation.isValid) {
        return {
          success: false,
          targetSections: [],
          edits: [],
          summary: 'Invalid prompt',
          processingTime: Date.now() - startTime,
          error: promptValidation.error,
          warnings: promptValidation.suggestions
        };
      }

      const contentValidation = validateDocumentContent(request.documentContent);
      if (!contentValidation.isValid) {
        return {
          success: false,
          targetSections: [],
          edits: [],
          summary: 'Invalid document content',
          processingTime: Date.now() - startTime,
          error: contentValidation.error,
          warnings: contentValidation.suggestions
        };
      }

      // Analyze prompt quality and provide feedback
      const promptQuality = analyzePromptQuality(request.prompt);
      const warnings: string[] = [];
      if (promptQuality.score < 70) {
        warnings.push(`Prompt quality score: ${promptQuality.score}/100`);
        warnings.push(...promptQuality.suggestions);
      }
      // Step 1: Analyze document structure
      this.reportProgress('parsing-structure', 10, 'Analyzing document structure...');
      const documentStructure = this.documentAnalyzer.analyzeDocument(request.documentContent);

      // Step 2: Analyze the prompt
      this.reportProgress('identifying-sections', 25, 'Analyzing your request...');
      const promptAnalysis = await this.analyzePrompt(request.prompt);

      // Step 3: Identify target sections
      this.reportProgress('matching-prompt', 50, 'Identifying relevant sections...');
      const targetSections = this.identifyTargetSections(
        promptAnalysis,
        documentStructure,
        request.options
      );

      if (targetSections.length === 0) {
        const noSectionsError = createAgentModeError(
          'NO_SECTIONS_FOUND',
          'No relevant sections found for your request.',
          { prompt: request.prompt, documentStructure }
        );

        return {
          success: false,
          targetSections: [],
          edits: [],
          summary: 'No relevant sections found for your request.',
          processingTime: Date.now() - startTime,
          error: noSectionsError.message,
          warnings: noSectionsError.suggestions
        };
      }

      // Step 4: Generate edits for target sections
      this.reportProgress('generating-edits', 75, 'Generating improvements...');
      const edits = await this.generateEditsForSections(
        request.prompt,
        targetSections,
        request.model,
        request.options
      );

      // Step 5: Finalize results
      this.reportProgress('finalizing-results', 95, 'Finalizing changes...');
      const summary = this.generateSummary(targetSections, edits);

      this.reportProgress('complete', 100, 'Agent mode completed successfully!');

      return {
        success: true,
        targetSections,
        edits,
        summary,
        processingTime: Date.now() - startTime,
        tokensUsed: this.estimateTokensUsed(request.prompt, edits),
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error: any) {
      console.error('Agent mode execution failed:', error);

      this.reportProgress('error', 0, 'Agent mode failed');

      // Create appropriate error based on error type
      let agentError;
      if (error.message?.includes('API') || error.message?.includes('network')) {
        agentError = createAgentModeError('AI_SERVICE_ERROR', error.message, error);
      } else if (error.message?.includes('analysis') || error.message?.includes('structure')) {
        agentError = createAgentModeError('DOCUMENT_ANALYSIS_FAILED', error.message, error);
      } else {
        agentError = createAgentModeError('UNKNOWN_ERROR', error.message || 'An unexpected error occurred', error);
      }

      return {
        success: false,
        targetSections: [],
        edits: [],
        summary: 'Agent mode execution failed.',
        processingTime: Date.now() - startTime,
        error: agentError.message,
        warnings: agentError.suggestions
      };
    }
  }

  /**
   * Analyze the user's prompt to understand intent
   */
  private async analyzePrompt(prompt: string): Promise<PromptAnalysis> {
    const analysisPrompt = `
Analyze this user request for document editing and extract the following information in JSON format:

User Request: "${prompt}"

Return a JSON object with:
- intent: Brief description of what the user wants to do
- targetSectionTypes: Array of likely section types (introduction, methodology, results, discussion, conclusion, abstract, other)
- actionType: Type of edit (replace, enhance, clarify, expand, condense, restructure)
- keywords: Array of important keywords from the request
- confidence: Confidence score 0-1 for the analysis

Example response:
{
  "intent": "Improve clarity of the methodology section",
  "targetSectionTypes": ["methodology", "methods"],
  "actionType": "clarify",
  "keywords": ["methodology", "clarity", "improve"],
  "confidence": 0.9
}
`;

    try {
      const response = await enhancedAIService.generateText(analysisPrompt);
      const analysis = JSON.parse(response);
      
      return {
        intent: analysis.intent || 'General document improvement',
        targetSectionTypes: analysis.targetSectionTypes || ['other'],
        actionType: analysis.actionType || 'enhance',
        keywords: analysis.keywords || [],
        confidence: analysis.confidence || 0.5
      };
    } catch (error) {
      console.warn('Failed to analyze prompt, using fallback analysis:', error);
      
      // Fallback analysis based on keywords
      return this.fallbackPromptAnalysis(prompt);
    }
  }

  /**
   * Fallback prompt analysis using keyword matching
   */
  private fallbackPromptAnalysis(prompt: string): PromptAnalysis {
    const promptLower = prompt.toLowerCase();
    const keywords: string[] = [];
    const targetSectionTypes: SectionType[] = [];
    let actionType: EditType = 'enhance';

    // Extract action type
    if (promptLower.includes('rewrite') || promptLower.includes('replace')) {
      actionType = 'replace';
    } else if (promptLower.includes('expand') || promptLower.includes('elaborate')) {
      actionType = 'expand';
    } else if (promptLower.includes('clarify') || promptLower.includes('clear')) {
      actionType = 'clarify';
    } else if (promptLower.includes('shorten') || promptLower.includes('condense')) {
      actionType = 'condense';
    } else if (promptLower.includes('restructure') || promptLower.includes('reorganize')) {
      actionType = 'restructure';
    }

    // Extract section types
    if (promptLower.includes('introduction') || promptLower.includes('intro')) {
      targetSectionTypes.push('introduction');
      keywords.push('introduction');
    }
    if (promptLower.includes('methodology') || promptLower.includes('method')) {
      targetSectionTypes.push('methodology', 'methods');
      keywords.push('methodology');
    }
    if (promptLower.includes('result') || promptLower.includes('finding')) {
      targetSectionTypes.push('results');
      keywords.push('results');
    }
    if (promptLower.includes('discussion') || promptLower.includes('analysis')) {
      targetSectionTypes.push('discussion');
      keywords.push('discussion');
    }
    if (promptLower.includes('conclusion') || promptLower.includes('summary')) {
      targetSectionTypes.push('conclusion');
      keywords.push('conclusion');
    }
    if (promptLower.includes('abstract')) {
      targetSectionTypes.push('abstract');
      keywords.push('abstract');
    }

    // If no specific sections found, target all
    if (targetSectionTypes.length === 0) {
      targetSectionTypes.push('other');
    }

    return {
      intent: `${actionType} the ${targetSectionTypes.join(', ')} section(s)`,
      targetSectionTypes,
      actionType,
      keywords,
      confidence: 0.7
    };
  }

  /**
   * Identify target sections based on prompt analysis
   */
  private identifyTargetSections(
    promptAnalysis: PromptAnalysis,
    documentStructure: DocumentStructure,
    options = DEFAULT_AGENT_MODE_OPTIONS
  ): TargetSection[] {
    const targetSections: TargetSection[] = [];

    // First, try to match by section type
    const sectionsByType = this.documentAnalyzer.findSectionsByType(
      documentStructure,
      promptAnalysis.targetSectionTypes
    );

    sectionsByType.forEach(section => {
      const confidence = this.calculateSectionConfidence(section, promptAnalysis);
      
      if (confidence >= (options.confidenceThreshold || 0.6)) {
        targetSections.push({
          section,
          confidence,
          reason: `Matches section type: ${section.type}`,
          matchedKeywords: promptAnalysis.keywords.filter(keyword =>
            section.title.toLowerCase().includes(keyword) ||
            section.content.toLowerCase().includes(keyword)
          ),
          relevanceScore: confidence
        });
      }
    });

    // If no type matches, try keyword matching
    if (targetSections.length === 0 && promptAnalysis.keywords.length > 0) {
      const sectionsByKeywords = this.documentAnalyzer.findSectionsByKeywords(
        documentStructure,
        promptAnalysis.keywords
      );

      sectionsByKeywords.forEach(section => {
        const confidence = this.calculateSectionConfidence(section, promptAnalysis);
        
        if (confidence >= (options.confidenceThreshold || 0.6)) {
          targetSections.push({
            section,
            confidence,
            reason: `Contains relevant keywords`,
            matchedKeywords: promptAnalysis.keywords.filter(keyword =>
              section.title.toLowerCase().includes(keyword) ||
              section.content.toLowerCase().includes(keyword)
            ),
            relevanceScore: confidence
          });
        }
      });
    }

    // Sort by confidence and limit results
    return targetSections
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, options.maxSections || 3);
  }

  /**
   * Calculate confidence score for section matching
   */
  private calculateSectionConfidence(section: any, promptAnalysis: PromptAnalysis): number {
    let confidence = 0;

    // Type matching
    if (promptAnalysis.targetSectionTypes.includes(section.type)) {
      confidence += 0.4;
    }

    // Keyword matching in title
    const titleMatches = promptAnalysis.keywords.filter(keyword =>
      section.title.toLowerCase().includes(keyword)
    ).length;
    confidence += (titleMatches / Math.max(promptAnalysis.keywords.length, 1)) * 0.3;

    // Keyword matching in content (weighted less)
    const contentMatches = promptAnalysis.keywords.filter(keyword =>
      section.content.toLowerCase().includes(keyword)
    ).length;
    confidence += (contentMatches / Math.max(promptAnalysis.keywords.length, 1)) * 0.2;

    // Section size factor (prefer substantial sections)
    if (section.wordCount > 50) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate edits for target sections
   */
  private async generateEditsForSections(
    originalPrompt: string,
    targetSections: TargetSection[],
    model: string,
    options = DEFAULT_AGENT_MODE_OPTIONS
  ): Promise<EditInstruction[]> {
    const edits: EditInstruction[] = [];

    for (const targetSection of targetSections) {
      try {
        const editPrompt = this.buildEditPrompt(originalPrompt, targetSection, options);
        const improvedContent = await enhancedAIService.generateText(editPrompt, model);

        edits.push({
          id: nanoid(),
          sectionId: targetSection.section.id,
          originalText: targetSection.section.content,
          newText: improvedContent.trim(),
          startPosition: targetSection.section.startPosition,
          endPosition: targetSection.section.endPosition,
          editType: 'enhance', // Could be derived from prompt analysis
          confidence: targetSection.confidence,
          reasoning: `Applied "${originalPrompt}" to ${targetSection.section.title}`,
          prompt: originalPrompt
        });

      } catch (error) {
        console.error(`Failed to generate edit for section ${targetSection.section.title}:`, error);
      }
    }

    return edits;
  }

  /**
   * Build edit prompt for AI
   */
  private buildEditPrompt(originalPrompt: string, targetSection: TargetSection, options: any): string {
    const editModeInstructions = {
      conservative: 'Make minimal, careful improvements while preserving the original meaning and style.',
      moderate: 'Make meaningful improvements while maintaining the core structure and intent.',
      aggressive: 'Make substantial improvements, restructuring as needed for better clarity and impact.'
    };

    return `
You are an expert editor. ${editModeInstructions[options.editMode || 'moderate']}

User Request: "${originalPrompt}"

Section Title: "${targetSection.section.title}"
Section Content:
"${targetSection.section.content}"

Instructions:
- Apply the user's request to improve this specific section
- Return ONLY the improved content without explanations
- Maintain appropriate formatting and structure
- Preserve any important technical details or data
- Keep the same general length unless specifically asked to expand or condense
${options.preserveFormatting ? '- Preserve the original formatting style' : ''}

Improved content:`;
  }

  /**
   * Generate summary of changes
   */
  private generateSummary(targetSections: TargetSection[], edits: EditInstruction[]): string {
    const sectionNames = targetSections.map(ts => ts.section.title).join(', ');
    const editCount = edits.length;
    
    if (editCount === 0) {
      return 'No edits were generated.';
    }
    
    if (editCount === 1) {
      return `Generated 1 improvement for: ${sectionNames}`;
    }
    
    return `Generated ${editCount} improvements for: ${sectionNames}`;
  }

  /**
   * Estimate tokens used for billing/tracking
   */
  private estimateTokensUsed(prompt: string, edits: EditInstruction[]): number {
    const promptTokens = Math.ceil(prompt.length / 4);
    const editTokens = edits.reduce((total, edit) => 
      total + Math.ceil((edit.originalText.length + edit.newText.length) / 4), 0
    );
    return promptTokens + editTokens;
  }

  /**
   * Report progress to callback
   */
  private reportProgress(stage: any, progress: number, message: string): void {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message });
    }
  }
}
