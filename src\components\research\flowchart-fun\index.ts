/**
 * Flowchart Fun Module Index
 * Main export file for the Flowchart Fun module
 */

// Main component
export { FlowchartFun } from './FlowchartFun';

// Types
export type {
  FlowchartNode,
  FlowchartEdge,
  FlowchartGraph,
  FlowchartMetadata,
  ParsedLine,
  ParseResult,
  ParseError,
  ExportFormat,
  ExportOptions,
  ExportResult,
  FlowchartFunProps,
  TextEditorProps,
  GraphViewerProps,
  ExportDialogProps,
  FlowchartFunState,
  FlowchartFunActions,
  FlowchartFunStore,
  FlowchartHistory,
  TextParserService,
  GraphRenderService,
  ExportService,
  FlowchartConfig,
  FlowchartError,
  FlowchartTemplate,
  AIFlowchartRequest,
  AIFlowchartResponse
} from './types';

// Components
export {
  FlowchartTextEditor,
  FlowchartGraphViewer,
  FlowchartExportDialog
} from './components';

// Services
export { textParserService } from './services/text-parser.service';

// Store
export { useFlowchartFunStore } from './stores/flowchart-fun.store';

// Constants
export {
  DEFAULT_FLOWCHART_TEXT,
  EXPORT_FORMATS,
  EXPORT_FORMAT_LABELS,
  LAYOUT_ALGORITHMS,
  DEFAULT_NODE_STYLE,
  DEFAULT_EDGE_STYLE,
  THEMES,
  PARSER_CONFIG,
  DEFAULT_EXPORT_OPTIONS,
  TEMPLATE_CATEGORIES,
  DEFAULT_TEMPLATES,
  ERROR_MESSAGES,
  UI_CONFIG,
  STORAGE_KEYS,
  FLOWCHART_CONFIG
} from './constants';
