-- Setup script for Flow Builder with Supabase
-- Run this script in your Supabase SQL editor

-- First, ensure we have the required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create flow builder tables for storing user-generated diagrams
-- This schema supports the AI Flow Builder with history functionality

-- Table for storing diagram metadata and generation info
CREATE TABLE IF NOT EXISTS public.user_diagrams (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    diagram_type TEXT NOT NULL, -- flowchart, sequence, gantt, pie, mindmap, etc.
    direction TEXT DEFAULT 'TD', -- TD, LR, BT, RL
    mermaid_code TEXT NOT NULL,
    ai_model TEXT NOT NULL, -- Model used for generation
    generation_prompt TEXT NOT NULL, -- Original user prompt
    additional_requirements TEXT,
    context TEXT,
    status TEXT NOT NULL DEFAULT 'completed', -- completed, error, generating
    error_message TEXT,
    tags TEXT[] DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    export_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing diagram export history
CREATE TABLE IF NOT EXISTS public.diagram_exports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    diagram_id UUID REFERENCES public.user_diagrams(id) ON DELETE CASCADE NOT NULL,
    export_format TEXT NOT NULL, -- svg, png, pdf, mermaid
    file_name TEXT NOT NULL,
    file_path TEXT, -- Path in Supabase storage if applicable
    file_size INTEGER,
    export_options JSONB DEFAULT '{}', -- Store export configuration
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking user diagram activity
CREATE TABLE IF NOT EXISTS public.diagram_activity (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    diagram_id UUID REFERENCES public.user_diagrams(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    activity_type TEXT NOT NULL, -- created, viewed, edited, exported, downloaded, favorited
    activity_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing diagram templates (admin-created)
CREATE TABLE IF NOT EXISTS public.diagram_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL, -- methodology, analysis, review, experiment, etc.
    diagram_type TEXT NOT NULL,
    template_prompt TEXT NOT NULL,
    example_code TEXT,
    tags TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing user diagram collections/folders
CREATE TABLE IF NOT EXISTS public.diagram_collections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#3B82F6', -- Hex color for collection
    icon TEXT DEFAULT 'folder', -- Icon identifier
    diagram_count INTEGER DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction table for diagram-collection relationships
CREATE TABLE IF NOT EXISTS public.diagram_collection_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    collection_id UUID REFERENCES public.diagram_collections(id) ON DELETE CASCADE NOT NULL,
    diagram_id UUID REFERENCES public.user_diagrams(id) ON DELETE CASCADE NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(collection_id, diagram_id)
);

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_diagrams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagram_exports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagram_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagram_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagram_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagram_collection_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own diagrams" ON public.user_diagrams;
DROP POLICY IF EXISTS "Users can insert their own diagrams" ON public.user_diagrams;
DROP POLICY IF EXISTS "Users can update their own diagrams" ON public.user_diagrams;
DROP POLICY IF EXISTS "Users can delete their own diagrams" ON public.user_diagrams;

-- Create RLS policies for user_diagrams
CREATE POLICY "Users can view their own diagrams" ON public.user_diagrams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own diagrams" ON public.user_diagrams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own diagrams" ON public.user_diagrams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own diagrams" ON public.user_diagrams
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for diagram_exports
DROP POLICY IF EXISTS "Users can view their own diagram exports" ON public.diagram_exports;
DROP POLICY IF EXISTS "Users can insert exports for their own diagrams" ON public.diagram_exports;

CREATE POLICY "Users can view their own diagram exports" ON public.diagram_exports
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_diagrams 
            WHERE id = diagram_exports.diagram_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert exports for their own diagrams" ON public.diagram_exports
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_diagrams 
            WHERE id = diagram_exports.diagram_id AND user_id = auth.uid()
        )
    );

-- Create RLS policies for diagram_activity
DROP POLICY IF EXISTS "Users can view their own diagram activity" ON public.diagram_activity;
DROP POLICY IF EXISTS "Users can insert their own diagram activity" ON public.diagram_activity;

CREATE POLICY "Users can view their own diagram activity" ON public.diagram_activity
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own diagram activity" ON public.diagram_activity
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for diagram_templates (public read, admin write)
DROP POLICY IF EXISTS "Anyone can view active templates" ON public.diagram_templates;
CREATE POLICY "Anyone can view active templates" ON public.diagram_templates
    FOR SELECT USING (is_active = true);

-- Create RLS policies for diagram_collections
DROP POLICY IF EXISTS "Users can manage their own collections" ON public.diagram_collections;
CREATE POLICY "Users can manage their own collections" ON public.diagram_collections
    FOR ALL USING (auth.uid() = user_id);

-- Create RLS policies for diagram_collection_items
DROP POLICY IF EXISTS "Users can manage their own collection items" ON public.diagram_collection_items;
CREATE POLICY "Users can manage their own collection items" ON public.diagram_collection_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.diagram_collections 
            WHERE id = diagram_collection_items.collection_id AND user_id = auth.uid()
        )
    );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_diagrams_user_id ON public.user_diagrams(user_id);
CREATE INDEX IF NOT EXISTS idx_user_diagrams_created_at ON public.user_diagrams(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_diagrams_type ON public.user_diagrams(diagram_type);
CREATE INDEX IF NOT EXISTS idx_user_diagrams_status ON public.user_diagrams(status);
CREATE INDEX IF NOT EXISTS idx_diagram_exports_diagram_id ON public.diagram_exports(diagram_id);
CREATE INDEX IF NOT EXISTS idx_diagram_activity_user_id ON public.diagram_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_diagram_activity_diagram_id ON public.diagram_activity(diagram_id);
CREATE INDEX IF NOT EXISTS idx_diagram_templates_category ON public.diagram_templates(category);
CREATE INDEX IF NOT EXISTS idx_diagram_collections_user_id ON public.diagram_collections(user_id);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_user_diagrams_updated_at ON public.user_diagrams;
CREATE TRIGGER update_user_diagrams_updated_at BEFORE UPDATE ON public.user_diagrams
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_diagram_templates_updated_at ON public.diagram_templates;
CREATE TRIGGER update_diagram_templates_updated_at BEFORE UPDATE ON public.diagram_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_diagram_collections_updated_at ON public.diagram_collections;
CREATE TRIGGER update_diagram_collections_updated_at BEFORE UPDATE ON public.diagram_collections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment diagram view count
CREATE OR REPLACE FUNCTION increment_diagram_view_count(diagram_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.user_diagrams 
    SET view_count = view_count + 1 
    WHERE id = diagram_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update diagram count in collections
CREATE OR REPLACE FUNCTION update_collection_diagram_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.diagram_collections 
        SET diagram_count = diagram_count + 1 
        WHERE id = NEW.collection_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.diagram_collections 
        SET diagram_count = diagram_count - 1 
        WHERE id = OLD.collection_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_collection_count_on_item_change ON public.diagram_collection_items;
CREATE TRIGGER update_collection_count_on_item_change
    AFTER INSERT OR DELETE ON public.diagram_collection_items
    FOR EACH ROW EXECUTE FUNCTION update_collection_diagram_count();

-- Insert default diagram templates
INSERT INTO public.diagram_templates (name, description, category, diagram_type, template_prompt, example_code, tags) VALUES
('Research Methodology', 'Complete research methodology flowchart with all phases', 'methodology', 'flowchart', 'Create a comprehensive research methodology flowchart that includes problem identification, literature review, hypothesis formation, research design, data collection, analysis, and reporting phases. Include decision points and feedback loops.', 'flowchart TD
    A[Research Problem] --> B[Literature Review]
    B --> C{Hypothesis Clear?}
    C -->|Yes| D[Research Design]
    C -->|No| B
    D --> E[Data Collection]
    E --> F[Data Analysis]
    F --> G[Results Interpretation]
    G --> H[Report Writing]', ARRAY['research', 'methodology', 'academic']),

('Data Analysis Pipeline', 'Data analysis workflow from raw data to insights', 'analysis', 'flowchart', 'Design a data analysis pipeline that shows the process from raw data collection through cleaning, exploration, statistical analysis, visualization, and interpretation. Include quality control checkpoints.', 'flowchart TD
    A[Raw Data] --> B[Data Cleaning]
    B --> C{Quality Check}
    C -->|Pass| D[Exploratory Analysis]
    C -->|Fail| B
    D --> E[Statistical Analysis]
    E --> F[Visualization]
    F --> G[Interpretation]
    G --> H[Report]', ARRAY['data', 'analysis', 'statistics']),

('Literature Review Process', 'Systematic literature review methodology', 'review', 'flowchart', 'Create a systematic literature review process diagram following PRISMA guidelines. Include database searching, screening, quality assessment, data extraction, and synthesis phases.', 'flowchart TD
    A[Research Question] --> B[Search Strategy]
    B --> C[Database Search]
    C --> D[Title/Abstract Screening]
    D --> E[Full-text Review]
    E --> F[Quality Assessment]
    F --> G[Data Extraction]
    G --> H[Synthesis]
    H --> I[Report]', ARRAY['literature', 'review', 'systematic']),

('Survey Research Design', 'Complete survey research methodology', 'methodology', 'flowchart', 'Design a survey research methodology that includes survey design, sampling strategy, data collection methods, response validation, statistical analysis, and results interpretation.', 'flowchart TD
    A[Research Objectives] --> B[Survey Design]
    B --> C[Sampling Strategy]
    C --> D[Pilot Testing]
    D --> E[Data Collection]
    E --> F[Response Validation]
    F --> G[Statistical Analysis]
    G --> H[Results Interpretation]', ARRAY['survey', 'research', 'quantitative']),

('Experimental Design', 'Scientific experiment design and execution', 'experiment', 'flowchart', 'Create an experimental design flowchart that outlines hypothesis formation, variable identification, control group setup, randomization, data collection protocols, and analysis procedures.', 'flowchart TD
    A[Hypothesis] --> B[Variable Definition]
    B --> C[Experimental Design]
    C --> D[Randomization]
    D --> E[Control Groups]
    E --> F[Data Collection]
    F --> G[Statistical Analysis]
    G --> H[Conclusion]', ARRAY['experiment', 'design', 'scientific']),

('Qualitative Research Process', 'Qualitative research methodology workflow', 'methodology', 'flowchart', 'Generate a qualitative research process flowchart showing participant recruitment, data collection methods (interviews, focus groups, observations), transcription, coding, theme development, and analysis.', 'flowchart TD
    A[Research Question] --> B[Participant Recruitment]
    B --> C[Data Collection]
    C --> D[Transcription]
    D --> E[Initial Coding]
    E --> F[Theme Development]
    F --> G[Analysis]
    G --> H[Validation]', ARRAY['qualitative', 'research', 'interviews'])

ON CONFLICT (name) DO NOTHING;

-- Success message
SELECT 'Flow Builder database setup completed successfully!' as message;
