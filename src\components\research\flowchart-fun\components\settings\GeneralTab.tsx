/**
 * General Settings Tab
 * Configure global appearance settings
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Palette } from 'lucide-react';
import { SettingsTabProps } from '../../types';

const fontFamilyOptions = [
  'Helvetica Neue, Arial, sans-serif',
  'Inter, system-ui, sans-serif',
  'SF Pro Display, system-ui, sans-serif',
  'Roboto, sans-serif',
  'Open Sans, sans-serif',
  'Lato, sans-serif',
  'Montserrat, sans-serif',
  'Source Sans Pro, sans-serif',
  'Georgia, serif',
  'Times New Roman, serif',
  'Courier New, monospace',
  'Monaco, monospace',
];

const GeneralTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const handleFontFamilyChange = (fontFamily: string) => {
    onThemeChange({ fontFamily });
  };

  const handleBackgroundChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onThemeChange({ background: e.target.value });
  };

  const handleLineHeightChange = (value: number[]) => {
    onThemeChange({ lineHeight: value[0] });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Global Appearance
          </CardTitle>
          <CardDescription>
            Configure the overall look and feel of your flowcharts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Background Color */}
          <div className="space-y-3">
            <Label htmlFor="background-color">Background Color</Label>
            <div className="flex items-center gap-3">
              <div 
                className="w-12 h-12 rounded-lg border-2 border-gray-300 cursor-pointer"
                style={{ backgroundColor: theme.background }}
                onClick={() => document.getElementById('background-color-input')?.click()}
              />
              <Input
                id="background-color-input"
                type="color"
                value={theme.background}
                onChange={handleBackgroundChange}
                className="w-20 h-12 p-1 cursor-pointer"
              />
              <Input
                type="text"
                value={theme.background}
                onChange={handleBackgroundChange}
                className="flex-1 font-mono text-sm"
                placeholder="#ffffff"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              The background color of the entire flowchart canvas
            </p>
          </div>

          {/* Font Family */}
          <div className="space-y-3">
            <Label htmlFor="font-family">Font Family</Label>
            <div className="grid grid-cols-1 gap-2">
              {fontFamilyOptions.map((font) => (
                <Button
                  key={font}
                  variant={theme.fontFamily === font ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleFontFamilyChange(font)}
                  className="justify-start text-left h-auto py-2"
                  style={{ fontFamily: font }}
                >
                  <span className="truncate">{font}</span>
                </Button>
              ))}
            </div>
            <div className="mt-3">
              <Label htmlFor="custom-font">Custom Font Family</Label>
              <Input
                id="custom-font"
                type="text"
                value={theme.fontFamily}
                onChange={(e) => handleFontFamilyChange(e.target.value)}
                placeholder="Enter custom font family"
                className="mt-1"
              />
            </div>
          </div>

          {/* Line Height */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="line-height">Line Height</Label>
              <Badge variant="outline">{theme.lineHeight.toFixed(1)}</Badge>
            </div>
            <Slider
              id="line-height"
              min={1.0}
              max={2.0}
              step={0.1}
              value={[theme.lineHeight]}
              onValueChange={handleLineHeightChange}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Tight (1.0)</span>
              <span>Normal (1.2)</span>
              <span>Loose (2.0)</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Controls the spacing between lines of text in nodes
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Preview</CardTitle>
          <CardDescription>
            See how your general settings affect the appearance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            className="p-6 rounded-lg border-2 border-dashed border-gray-300"
            style={{ 
              backgroundColor: theme.background,
              fontFamily: theme.fontFamily,
              lineHeight: theme.lineHeight 
            }}
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Sample Text</h3>
              <p className="text-sm">
                This is how your text will appear in the flowchart nodes.
                <br />
                Multiple lines will use the line height setting.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GeneralTab;
