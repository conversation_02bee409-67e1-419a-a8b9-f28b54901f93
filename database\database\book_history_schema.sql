-- Create book history tables for storing user-generated books
-- This schema supports the AI Book Generator with history functionality

-- Table for storing book metadata and generation info
CREATE TABLE IF NOT EXISTS public.user_books (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    subtitle TEXT,
    description TEXT,
    genre TEXT NOT NULL,
    target_audience TEXT NOT NULL,
    authors TEXT[] DEFAULT '{}',
    keywords TEXT[] DEFAULT '{}',
    estimated_length TEXT NOT NULL DEFAULT 'medium', -- short, medium, long, very-long
    tone TEXT NOT NULL DEFAULT 'professional', -- academic, professional, casual, narrative
    chapter_count INTEGER DEFAULT 0,
    total_word_count INTEGER DEFAULT 0,
    generation_status TEXT NOT NULL DEFAULT 'draft', -- draft, generating, completed, error
    outline_model TEXT, -- AI model used for outline generation
    content_model TEXT, -- AI model used for content generation
    generation_started_at TIMESTAMP WITH TIME ZONE,
    generation_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing individual book chapters
CREATE TABLE IF NOT EXISTS public.book_chapters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    book_id UUID REFERENCES public.user_books(id) ON DELETE CASCADE NOT NULL,
    chapter_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    content TEXT, -- The generated chapter content
    word_count INTEGER DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'pending', -- pending, generating, completed, error
    order_index INTEGER NOT NULL,
    outline JSONB, -- Store chapter outline structure
    citations JSONB DEFAULT '[]', -- Store citations for this chapter
    generation_metadata JSONB DEFAULT '{}', -- Store AI generation metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(book_id, chapter_number)
);

-- Table for storing book sections (preface, acknowledgments, etc.)
CREATE TABLE IF NOT EXISTS public.book_sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    book_id UUID REFERENCES public.user_books(id) ON DELETE CASCADE NOT NULL,
    section_type TEXT NOT NULL, -- preface, acknowledgments, table_of_contents, bibliography, etc.
    title TEXT NOT NULL,
    content TEXT,
    word_count INTEGER DEFAULT 0,
    order_index INTEGER NOT NULL,
    is_generated BOOLEAN DEFAULT false, -- true if AI-generated, false if template
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(book_id, section_type)
);

-- Table for storing book export history and files
CREATE TABLE IF NOT EXISTS public.book_exports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    book_id UUID REFERENCES public.user_books(id) ON DELETE CASCADE NOT NULL,
    export_format TEXT NOT NULL, -- docx, pdf, html, epub
    file_name TEXT NOT NULL,
    file_path TEXT, -- Path in Supabase storage
    file_size INTEGER,
    export_options JSONB DEFAULT '{}', -- Store export configuration
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking user book access and activity
CREATE TABLE IF NOT EXISTS public.book_activity (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    book_id UUID REFERENCES public.user_books(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    activity_type TEXT NOT NULL, -- created, viewed, edited, exported, downloaded
    activity_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_books_user_id ON public.user_books(user_id);
CREATE INDEX IF NOT EXISTS idx_user_books_created_at ON public.user_books(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_books_status ON public.user_books(generation_status);
CREATE INDEX IF NOT EXISTS idx_user_books_genre ON public.user_books(genre);

CREATE INDEX IF NOT EXISTS idx_book_chapters_book_id ON public.book_chapters(book_id);
CREATE INDEX IF NOT EXISTS idx_book_chapters_order ON public.book_chapters(book_id, order_index);
CREATE INDEX IF NOT EXISTS idx_book_chapters_status ON public.book_chapters(status);

CREATE INDEX IF NOT EXISTS idx_book_sections_book_id ON public.book_sections(book_id);
CREATE INDEX IF NOT EXISTS idx_book_sections_order ON public.book_sections(book_id, order_index);

CREATE INDEX IF NOT EXISTS idx_book_exports_book_id ON public.book_exports(book_id);
CREATE INDEX IF NOT EXISTS idx_book_exports_format ON public.book_exports(export_format);
CREATE INDEX IF NOT EXISTS idx_book_exports_created_at ON public.book_exports(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_book_activity_book_id ON public.book_activity(book_id);
CREATE INDEX IF NOT EXISTS idx_book_activity_user_id ON public.book_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_book_activity_type ON public.book_activity(activity_type);
CREATE INDEX IF NOT EXISTS idx_book_activity_created_at ON public.book_activity(created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.user_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.book_chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.book_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.book_exports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.book_activity ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_books
CREATE POLICY "Users can only see their own books" ON public.user_books
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert their own books" ON public.user_books
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for book_chapters
CREATE POLICY "Users can only see chapters of their own books" ON public.book_chapters
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_chapters.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can only insert chapters for their own books" ON public.book_chapters
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_chapters.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

-- Create RLS policies for book_sections
CREATE POLICY "Users can only see sections of their own books" ON public.book_sections
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_sections.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can only insert sections for their own books" ON public.book_sections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_sections.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

-- Create RLS policies for book_exports
CREATE POLICY "Users can only see exports of their own books" ON public.book_exports
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_exports.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can only insert exports for their own books" ON public.book_exports
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_books 
            WHERE user_books.id = book_exports.book_id 
            AND user_books.user_id = auth.uid()
        )
    );

-- Create RLS policies for book_activity
CREATE POLICY "Users can only see activity for their own books" ON public.book_activity
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only insert activity for their own books" ON public.book_activity
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create functions for automatic updates
CREATE OR REPLACE FUNCTION public.update_book_word_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update total word count for the book when chapters are updated
    UPDATE public.user_books 
    SET total_word_count = (
        SELECT COALESCE(SUM(word_count), 0) 
        FROM public.book_chapters 
        WHERE book_id = NEW.book_id
    ),
    updated_at = NOW()
    WHERE id = NEW.book_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for book word count updates
CREATE TRIGGER update_book_word_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.book_chapters
    FOR EACH ROW
    EXECUTE FUNCTION public.update_book_word_count();

-- Create function to update chapter word count from content
CREATE OR REPLACE FUNCTION public.update_chapter_word_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate word count from content (remove HTML tags and count words)
    IF NEW.content IS NOT NULL THEN
        NEW.word_count = array_length(
            string_to_array(
                trim(regexp_replace(NEW.content, '<[^>]*>', '', 'g')), 
                ' '
            ), 
            1
        );
    END IF;
    
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for chapter word count
CREATE TRIGGER update_chapter_word_count_trigger
    BEFORE INSERT OR UPDATE ON public.book_chapters
    FOR EACH ROW
    EXECUTE FUNCTION public.update_chapter_word_count();

-- Create function to update section word count from content
CREATE OR REPLACE FUNCTION public.update_section_word_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate word count from content (remove HTML tags and count words)
    IF NEW.content IS NOT NULL THEN
        NEW.word_count = array_length(
            string_to_array(
                trim(regexp_replace(NEW.content, '<[^>]*>', '', 'g')), 
                ' '
            ), 
            1
        );
    END IF;
    
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for section word count
CREATE TRIGGER update_section_word_count_trigger
    BEFORE INSERT OR UPDATE ON public.book_sections
    FOR EACH ROW
    EXECUTE FUNCTION public.update_section_word_count();

-- Create function to log book activity
CREATE OR REPLACE FUNCTION public.log_book_activity(
    p_book_id UUID,
    p_activity_type TEXT,
    p_activity_details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO public.book_activity (book_id, user_id, activity_type, activity_details)
    VALUES (p_book_id, auth.uid(), p_activity_type, p_activity_details)
    RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ language 'plpgsql';
