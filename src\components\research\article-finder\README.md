# Article Finder Module

A comprehensive AI-powered journal discovery platform that helps researchers identify the best academic journals for their research submissions.

## Overview

The Article Finder module analyzes research content using advanced AI models and provides personalized journal recommendations based on multiple criteria including impact factor, publication timeline, costs, and topic relevance.

## Features

### Core Functionality
- **Multi-Input Support**: Analyze article titles, abstracts, or complete manuscripts
- **AI-Powered Analysis**: Uses Google Gemini 2.5 Pro/Flash and OpenRouter models
- **Journal Discovery**: Integrates with Tavily search API for comprehensive journal database access
- **Smart Ranking**: Customizable ranking system with weighted criteria
- **Export Options**: Multiple export formats (PDF, DOCX, JSON, CSV)

### Analysis Capabilities
- Research domain classification
- Methodology assessment
- Key topic extraction
- Novelty scoring
- Contribution level evaluation
- Academic field identification

### Journal Evaluation
- **Impact Metrics**: Impact factor, CiteScore, h-index
- **Publication Timeline**: Average review and publication duration
- **Cost Analysis**: Publication fees and open access policies
- **Quality Indicators**: Peer review process, editorial board reputation
- **Acceptance Rates**: Historical acceptance statistics
- **Target Audience**: Readership demographics and geographic focus

## Architecture

### Directory Structure
```
src/components/research/article-finder/
├── ArticleFinder.tsx              # Main component
├── index.ts                       # Module exports
├── types.ts                       # TypeScript definitions
├── constants.ts                   # Configuration constants
├── README.md                      # Documentation
├── components/                    # UI components
│   ├── ArticleInputForm.tsx       # Content input interface
│   ├── ArticleAnalysisDisplay.tsx # Analysis results display
│   ├── JournalRecommendationCard.tsx # Individual journal cards
│   ├── JournalRankingSystem.tsx   # Ranking and filtering system
│   ├── ExportDialog.tsx           # Export functionality
│   ├── HistoryPanel.tsx           # Previous analyses
│   └── index.ts                   # Component exports
├── services/                      # API services
│   ├── article-finder-ai.service.ts    # Google Gemini integration
│   ├── journal-search.service.ts       # Tavily search integration
│   ├── openrouter-article.service.ts   # OpenRouter API integration
│   └── index.ts                         # Service exports
└── stores/                        # State management
    ├── article-finder.store.ts    # Zustand store
    └── index.ts                   # Store exports
```

### Key Components

#### ArticleFinder (Main Component)
- Orchestrates the entire analysis workflow
- Manages step navigation (Input → Analysis → Results → Export)
- Handles service integration and error management
- Provides progress tracking and user feedback

#### ArticleInputForm
- Multi-type content input (title, abstract, full article)
- AI model selection and analysis depth configuration
- Content validation and keyword extraction
- Advanced options for customization

#### JournalRankingSystem
- Customizable ranking criteria with weighted scoring
- Advanced filtering (open access, high impact, fast track)
- Interactive sorting and comparison tools
- Real-time ranking updates

#### ArticleAnalysisDisplay
- Comprehensive analysis visualization
- Research domain and methodology insights
- Key topics and themes extraction
- Confidence scoring and metrics

### Services Integration

#### Google Gemini AI Service
- Primary AI analysis engine
- Supports Gemini 2.5 Pro and Flash models
- Rate limiting and error handling
- JSON response parsing and validation

#### Tavily Search Service
- Academic journal discovery
- Recent publication analysis
- Journal metrics extraction
- Source prioritization for academic content

#### OpenRouter Service
- Alternative AI processing
- Multiple model support (Claude, GPT-4, etc.)
- Fallback and comparison capabilities
- Cost-effective analysis options

## Configuration

### Environment Variables
```bash
# Required for Google Gemini integration
VITE_GEMINI_API_KEY=your_gemini_api_key

# Required for Tavily search integration
VITE_TAVILY_API_KEY=your_tavily_api_key

# Optional for OpenRouter integration
VITE_OPENROUTER_API_KEY=your_openrouter_api_key
```

### AI Models
- **Gemini 2.5 Pro**: Advanced analysis with comprehensive journal matching
- **Gemini 2.5 Flash**: Fast analysis with good accuracy (default)
- **Claude 3.5 Sonnet**: Excellent for academic analysis via OpenRouter
- **GPT-4o**: Strong analytical capabilities via OpenRouter

### Analysis Depths
- **Basic**: Quick journal matching based on keywords and field
- **Comprehensive**: Detailed analysis with impact metrics and timelines
- **Detailed**: In-depth analysis with competitive landscape and strategy

## Usage

### Basic Workflow
1. **Input**: Submit article title, abstract, or full content
2. **Analysis**: AI analyzes content and identifies research characteristics
3. **Discovery**: System searches for relevant journals using multiple sources
4. **Ranking**: Journals are ranked based on customizable criteria
5. **Export**: Results can be exported in multiple formats

### Ranking Criteria
- **Relevance** (30%): Topic alignment and journal scope matching
- **Impact** (25%): Impact factor and citation metrics
- **Speed** (20%): Review and publication timeline
- **Cost** (15%): Publication fees and financial considerations
- **Accessibility** (10%): Open access policies and reach

### Export Options
- **PDF Report**: Comprehensive analysis with journal recommendations
- **Word Document**: Editable format for further customization
- **JSON Data**: Raw data for programmatic processing
- **CSV Spreadsheet**: Tabular format for analysis and comparison

## Integration

The module is integrated into the main research dashboard:

1. **Navigation**: Added to sidebar as "Article Finder"
2. **Routing**: Accessible via `activeView === "article-finder"`
3. **State Management**: Independent Zustand store for module state
4. **Services**: Shared integration with existing Tavily service

## Testing

### Browser Console Tests
```javascript
// Test Google Gemini integration
window.testArticleFinderAI()

// Test OpenRouter integration
window.testOpenRouterArticle()

// Test Tavily search service
window.testTavily()
```

### Service Configuration Check
The module automatically checks service availability on mount and provides appropriate fallbacks for missing configurations.

## Performance Considerations

- **Rate Limiting**: Built-in delays between API requests
- **Caching**: Results cached in browser storage
- **Progressive Loading**: Incremental result display
- **Error Recovery**: Graceful degradation for service failures

## Future Enhancements

- **Journal Database Integration**: Direct integration with journal APIs
- **Citation Analysis**: Reference pattern matching
- **Collaboration Features**: Team-based journal selection
- **Machine Learning**: Improved ranking algorithms
- **Real-time Updates**: Live journal metrics updates

## Dependencies

- React 18+ with TypeScript
- Zustand for state management
- Lucide React for icons
- Shadcn/ui components
- Google Generative AI SDK
- Existing Tavily search service

## Support

For issues or questions:
1. Check browser console for error messages
2. Verify API key configuration
3. Test individual services using console commands
4. Review network requests for API failures
