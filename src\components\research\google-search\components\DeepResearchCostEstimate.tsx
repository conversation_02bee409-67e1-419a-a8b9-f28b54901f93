/**
 * Deep Research Cost Estimate Component
 * Shows cost estimation and optimization suggestions before starting research
 */

import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Clock, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Settings,
  Info
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';

import { 
  CostEstimate, 
  RateLimitInfo, 
  BatchingStrategy,
  deepResearchCostService 
} from '../services/deep-research-cost.service';
import { DeepResearchOptions, ResearchOutline } from '../types';

interface DeepResearchCostEstimateProps {
  query: string;
  options: DeepResearchOptions;
  outline?: ResearchOutline;
  onProceed: () => void;
  onCancel: () => void;
  onOptimize: (newOptions: DeepResearchOptions) => void;
  className?: string;
}

export function DeepResearchCostEstimate({
  query,
  options,
  outline,
  onProceed,
  onCancel,
  onOptimize,
  className = ''
}: DeepResearchCostEstimateProps) {
  const [estimate, setEstimate] = useState<CostEstimate | null>(null);
  const [rateLimit, setRateLimit] = useState<RateLimitInfo | null>(null);
  const [batchingStrategy, setBatchingStrategy] = useState<BatchingStrategy | null>(null);
  const [showOptimizations, setShowOptimizations] = useState(false);
  const [userLimits] = useState({
    maxCost: 1.00, // $1.00 default limit
    maxTime: 600,  // 10 minutes
    maxTokens: 500000 // 500K tokens
  });

  useEffect(() => {
    calculateEstimate();
  }, [query, options, outline]);

  const calculateEstimate = () => {
    const costEstimate = deepResearchCostService.estimateCost(query, options, outline);
    const rateLimitInfo = deepResearchCostService.checkRateLimit();
    
    setEstimate(costEstimate);
    setRateLimit(rateLimitInfo);

    if (outline?.subtopics) {
      const strategy = deepResearchCostService.createBatchingStrategy(outline.subtopics, options);
      setBatchingStrategy(strategy);
    }
  };

  const handleOptimize = (field: keyof DeepResearchOptions, value: any) => {
    const newOptions = { ...options, [field]: value };
    onOptimize(newOptions);
  };

  const validation = estimate ? 
    deepResearchCostService.validateResearchLimits(estimate, userLimits) : 
    { canProceed: false, violations: [] };

  const suggestions = estimate ? 
    deepResearchCostService.getOptimizationSuggestions(estimate, options) : 
    [];

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const formatCost = (cost: number) => {
    return cost < 0.01 ? '< $0.01' : `$${cost.toFixed(4)}`;
  };

  if (!estimate || !rateLimit) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Calculating cost estimate...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <DollarSign className="h-6 w-6 text-green-600" />
          <h2 className="text-2xl font-semibold text-gray-900">Cost Estimate</h2>
          {validation.canProceed ? (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Ready to Proceed
            </Badge>
          ) : (
            <Badge variant="secondary" className="bg-red-100 text-red-800">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Needs Optimization
            </Badge>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowOptimizations(!showOptimizations)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Optimize
          </Button>
        </div>
      </div>

      {/* Cost Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {formatCost(estimate.estimatedCost)}
                </p>
                <p className="text-sm text-gray-600">Estimated Cost</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-blue-600">
                  {formatTime(estimate.estimatedTime)}
                </p>
                <p className="text-sm text-gray-600">Estimated Time</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold text-purple-600">
                  {estimate.totalTokens.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Total Tokens</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rate Limit Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>API Usage Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Requests ({rateLimit.currentUsage.requests}/{rateLimit.requestsPerMinute})</span>
              <span>{Math.round((rateLimit.currentUsage.requests / rateLimit.requestsPerMinute) * 100)}%</span>
            </div>
            <Progress 
              value={(rateLimit.currentUsage.requests / rateLimit.requestsPerMinute) * 100} 
              className="h-2"
            />
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Tokens ({rateLimit.currentUsage.tokens.toLocaleString()}/{rateLimit.tokensPerMinute.toLocaleString()})</span>
              <span>{Math.round((rateLimit.currentUsage.tokens / rateLimit.tokensPerMinute) * 100)}%</span>
            </div>
            <Progress 
              value={(rateLimit.currentUsage.tokens / rateLimit.tokensPerMinute) * 100} 
              className="h-2"
            />
          </div>

          {!rateLimit.canProceed && rateLimit.waitTime && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Rate limit reached. Please wait {rateLimit.waitTime} seconds before proceeding.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Cost Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Cost Breakdown</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Outline Generation</span>
            <span className="text-sm font-medium">{estimate.breakdown.outlineGeneration.toLocaleString()} tokens</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Batch Research</span>
            <span className="text-sm font-medium">{estimate.breakdown.batchResearch.toLocaleString()} tokens</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Section Generation</span>
            <span className="text-sm font-medium">{estimate.breakdown.sectionGeneration.toLocaleString()} tokens</span>
          </div>
          <Separator />
          <div className="flex justify-between items-center font-medium">
            <span>Total</span>
            <span>{estimate.totalTokens.toLocaleString()} tokens</span>
          </div>
        </CardContent>
      </Card>

      {/* Batching Strategy */}
      {batchingStrategy && (
        <Card>
          <CardHeader>
            <CardTitle>Processing Strategy</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Batches:</span>
                <span className="ml-2 font-medium">{batchingStrategy.totalBatches}</span>
              </div>
              <div>
                <span className="text-gray-600">Time per Batch:</span>
                <span className="ml-2 font-medium">{formatTime(batchingStrategy.estimatedTimePerBatch)}</span>
              </div>
              <div>
                <span className="text-gray-600">Recommended Delay:</span>
                <span className="ml-2 font-medium">{formatTime(batchingStrategy.recommendedDelay)}</span>
              </div>
              <div>
                <span className="text-gray-600">API Calls:</span>
                <span className="ml-2 font-medium">{estimate.apiCalls}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation Issues */}
      {validation.violations.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Cannot proceed due to the following issues:</p>
              <ul className="list-disc list-inside space-y-1">
                {validation.violations.map((violation, index) => (
                  <li key={index} className="text-sm">{violation}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Optimization Suggestions */}
      {suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Info className="h-5 w-5" />
              <span>Optimization Suggestions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-700">{suggestion}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          onClick={onProceed}
          disabled={!validation.canProceed || !rateLimit.canProceed}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {!rateLimit.canProceed ? 'Rate Limited' : 
           !validation.canProceed ? 'Optimize First' : 
           'Proceed with Research'}
        </Button>
      </div>
    </div>
  );
}
