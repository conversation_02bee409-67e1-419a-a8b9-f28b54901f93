/**
 * Google Search Hook
 * Custom hook for managing Google search state and operations
 */

import { useState, useCallback, useEffect } from 'react';
import {
  GoogleSearchSession,
  GoogleSearchMessage,
  GoogleSearchProgress,
  GoogleSearchOptions,
  GoogleUserPreferences,
  GoogleSearchError
} from '../types';

import { googleSearchService } from '../services/google-search.service';
import { googleAcademicService } from '../services/google-academic.service';
import { googleSearchHistoryService } from '../services/google-search-history.service';

interface UseGoogleSearchReturn {
  // State
  currentSession: GoogleSearchSession | null;
  messages: GoogleSearchMessage[];
  isLoading: boolean;
  searchProgress: GoogleSearchProgress | null;
  error: string | null;
  preferences: GoogleUserPreferences;
  
  // Actions
  createNewSession: () => Promise<void>;
  loadSession: (sessionId: string) => Promise<void>;
  performSearch: (query: string, options?: Partial<GoogleSearchOptions>) => Promise<void>;
  updatePreferences: (prefs: Partial<GoogleUserPreferences>) => void;
  clearError: () => void;
  retryLastSearch: () => Promise<void>;
  clearSession: () => void;
}

export function useGoogleSearch(userId: string = 'demo-user'): UseGoogleSearchReturn {
  // State
  const [currentSession, setCurrentSession] = useState<GoogleSearchSession | null>(null);
  const [messages, setMessages] = useState<GoogleSearchMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchProgress, setSearchProgress] = useState<GoogleSearchProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastQuery, setLastQuery] = useState<string>('');
  const [lastOptions, setLastOptions] = useState<Partial<GoogleSearchOptions>>({});

  // User preferences
  const [preferences, setPreferences] = useState<GoogleUserPreferences>({
    defaultSearchDepth: 'advanced',
    maxResults: 10,
    citationStyle: 'apa',
    academicFocus: true,
    includeRecentOnly: false
  });

  // Initialize session on mount
  useEffect(() => {
    createNewSession();
  }, [userId]);

  /**
   * Create a new search session
   */
  const createNewSession = useCallback(async () => {
    try {
      const session = await googleSearchHistoryService.createSession(
        userId,
        'Google Search Session'
      );
      setCurrentSession(session);
      setMessages([]);
      setError(null);
    } catch (error) {
      console.error('Failed to create Google search session:', error);
      setError('Failed to create search session');
    }
  }, [userId]);

  /**
   * Load an existing session
   */
  const loadSession = useCallback(async (sessionId: string) => {
    try {
      const session = await googleSearchHistoryService.getSession(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages);
        setError(null);
      } else {
        setError('Session not found');
      }
    } catch (error) {
      console.error('Failed to load Google search session:', error);
      setError('Failed to load session');
    }
  }, []);

  /**
   * Perform a Google search
   */
  const performSearch = useCallback(async (
    query: string,
    options: Partial<GoogleSearchOptions> = {}
  ) => {
    if (!currentSession || !query.trim()) return;

    setIsLoading(true);
    setError(null);
    setLastQuery(query);
    setLastOptions(options);
    setSearchProgress({ stage: 'searching', message: 'Searching with Google...', progress: 10 });

    try {
      // Add user message
      const userMessage: GoogleSearchMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        content: query,
        timestamp: new Date(),
        searchQuery: query,
        isGoogleSearch: true
      };

      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);

      // Save user message
      try {
        await googleSearchHistoryService.addMessage(currentSession.id, userMessage);
      } catch (dbError) {
        console.warn('Database error, continuing with search:', dbError);
      }

      // Update progress
      setSearchProgress({ stage: 'searching', message: 'Performing Google Search...', progress: 30 });

      // Merge options with preferences
      const searchOptions: GoogleSearchOptions = {
        maxResults: preferences.maxResults,
        searchDepth: preferences.defaultSearchDepth,
        includeAcademicSources: preferences.academicFocus,
        citationStyle: preferences.citationStyle,
        academicFocus: preferences.academicFocus,
        includeRecentOnly: preferences.includeRecentOnly,
        ...options
      };

      // Perform Google search
      const searchResult = await googleSearchService.searchAcademic(query, searchOptions);

      // Update progress
      setSearchProgress({ stage: 'analyzing', message: 'Analyzing results...', progress: 60 });

      // Format academic response
      const { formattedContent, references } = googleAcademicService.formatAcademicResponse(
        searchResult.answer,
        searchResult.sources,
        searchOptions.citationStyle
      );

      // Update progress
      setSearchProgress({ stage: 'generating_citations', message: 'Generating citations...', progress: 80 });

      // Generate citations
      const citations = googleAcademicService.generateInTextCitations(
        searchResult.sources,
        searchOptions.citationStyle
      );

      // Create assistant message
      const assistantMessage: GoogleSearchMessage = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: formattedContent,
        timestamp: new Date(),
        searchQuery: query,
        sources: searchResult.sources,
        citations,
        references,
        isGoogleSearch: true
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      setMessages(finalMessages);

      // Save assistant message
      try {
        await googleSearchHistoryService.addMessage(currentSession.id, assistantMessage);
      } catch (dbError) {
        console.warn('Database error, continuing:', dbError);
      }

      // Update progress
      setSearchProgress({ stage: 'complete', message: 'Search completed!', progress: 100 });

      // Clear progress after delay
      setTimeout(() => {
        setSearchProgress(null);
      }, 2000);

    } catch (error) {
      console.error('Google search failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(`Search failed: ${errorMessage}`);
      
      // Add error message
      const errorMsg: GoogleSearchMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `I apologize, but the Google search encountered an error: ${errorMessage}. Please try again or rephrase your query.`,
        timestamp: new Date(),
        isGoogleSearch: true
      };

      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
      setSearchProgress(null);
    }
  }, [currentSession, messages, preferences]);

  /**
   * Update user preferences
   */
  const updatePreferences = useCallback((prefs: Partial<GoogleUserPreferences>) => {
    setPreferences(prev => ({ ...prev, ...prefs }));
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Retry the last search
   */
  const retryLastSearch = useCallback(async () => {
    if (lastQuery) {
      await performSearch(lastQuery, lastOptions);
    }
  }, [lastQuery, lastOptions, performSearch]);

  /**
   * Clear current session
   */
  const clearSession = useCallback(() => {
    setMessages([]);
    setError(null);
    createNewSession();
  }, [createNewSession]);

  return {
    // State
    currentSession,
    messages,
    isLoading,
    searchProgress,
    error,
    preferences,
    
    // Actions
    createNewSession,
    loadSession,
    performSearch,
    updatePreferences,
    clearError,
    retryLastSearch,
    clearSession
  };
}

export default useGoogleSearch;
