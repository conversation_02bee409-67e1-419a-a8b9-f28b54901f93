// Main component
export { PresentationGenerator } from './PresentationGenerator';

// Types
export type {
  Presentation,
  Slide,
  PresentationMetadata,
  PresentationTheme,
  GenerationState,
  PresentationGenerationOptions,
  OutlineItem,
  ExportOptions,
  SlideTemplate,
  AIModelOption,
  UserInputs,
  PresentationMetrics,
  PresentationError,
  PresentationHistory
} from './types';

// Constants
export {
  SLIDE_TEMPLATES,
  PRESENTATION_STYLES,
  LANGUAGE_OPTIONS,
  SLIDE_COUNT_OPTIONS,
  AI_MODELS,
  PRESENTATION_THEMES,
  DEFAULT_GENERATION_OPTIONS,
  EXPORT_FORMATS
} from './constants';

// Store
export { usePresentationStore } from './stores/presentation.store';

// Components
export { PresentationMetadataForm } from './components/PresentationMetadataForm';

// Services (will be added later)
// export { presentationAIService } from './services/presentation-ai.service';
// export { slideGenerationService } from './services/slide-generation.service';
// export { themeService } from './services/theme.service';
// export { exportService } from './services/export.service';
