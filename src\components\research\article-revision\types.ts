/**
 * Types for the AI Article Revision System
 */

// Available models for article revision
export interface AIModelOption {
  id: string;
  name: string;
  description?: string;
  provider: 'openrouter' | 'openai' | 'anthropic' | 'google';
  contextSize?: number;
  isAvailable?: boolean;
}

// Article section types
export type ArticleSection = 
  | 'title'
  | 'abstract' 
  | 'keywords'
  | 'introduction'
  | 'methodology'
  | 'results'
  | 'discussion'
  | 'conclusion'
  | 'references'
  | 'overall';

// The parsed article content by section
export interface ParsedArticle {
  title?: string;
  abstract?: string;
  keywords?: string[];
  introduction?: string;
  methodology?: string;
  results?: string;
  discussion?: string;
  conclusion?: string;
  references?: string;
  fullText: string;
  sectionMapping: Record<ArticleSection, string>;
  fileName: string;
  fileType: string;
  fileSize: number;
  wordCount: number;
}

// Reviewer comment types
export interface ReviewerComment {
  id: string;
  reviewerNumber: number; // 1-5
  commentType: 'general' | 'specific' | 'figure' | 'table' | 'reference';
  severity: 'minor' | 'moderate' | 'major' | 'critical';
  category: 'content' | 'structure' | 'language' | 'methodology' | 'citation' | 'figure' | 'other';
  originalText: string;
  comment: string;
  suggestion?: string;
  targetSection?: ArticleSection;
  lineNumber?: number;
  pageNumber?: number;
  canAutoFix: boolean;
  requiresManualIntervention: boolean;
}

// Parsed reviewer comments from uploaded files
export interface ParsedReviewerComments {
  fileName: string;
  fileType: string;
  reviewerNumber: number;
  comments: ReviewerComment[];
  generalComments: string[];
  overallAssessment?: string;
}

// Change tracking for revisions
export interface ArticleChange {
  id: string;
  section: ArticleSection;
  changeType: 'addition' | 'deletion' | 'modification' | 'restructure';
  originalText: string;
  revisedText: string;
  reason: string;
  relatedCommentIds: string[];
  confidence: number; // 0-100
  lineNumber?: number;
  wordPosition?: number;
}

// Section revision results
export interface SectionRevision {
  section: ArticleSection;
  originalContent: string;
  revisedContent: string;
  changes: ArticleChange[];
  addressedComments: string[];
  unaddressedComments: string[];
  revisionSummary: string;
  qualityScore: number; // 0-100
}

// Complete revised article
export interface RevisedArticle {
  originalArticle: ParsedArticle;
  sectionRevisions: Record<ArticleSection, SectionRevision>;
  overallChanges: ArticleChange[];
  metadata: {
    revisionDate: Date;
    totalChanges: number;
    addressedComments: number;
    unaddressedComments: number;
    processingTime: number;
  };
}

// Response to reviewer comments
export interface ReviewerResponse {
  commentId: string;
  reviewerNumber: number;
  originalComment: string;
  response: string;
  changesMade: string[];
  changeLocations: string[];
  status: 'addressed' | 'partially-addressed' | 'not-addressed' | 'requires-manual';
  reasoning: string;
}

// Complete response letter
export interface ResponseLetter {
  manuscriptNumber?: string;
  title: string;
  authors: string[];
  journalName?: string;
  submissionDate?: Date;
  revisionDate: Date;
  
  // Letter content
  introduction: string;
  summaryOfChanges: string[];
  reviewerResponses: Record<number, ReviewerResponse[]>; // Grouped by reviewer number
  conclusion: string;
  
  // Metadata
  totalComments: number;
  addressedComments: number;
  manualInterventionRequired: string[];

  // Full generated response text
  fullResponseText?: string;
}

// Manual intervention suggestions
export interface ManualIntervention {
  id: string;
  type: 'figure' | 'table' | 'complex-revision' | 'data-analysis' | 'methodology-change';
  description: string;
  relatedComments: string[];
  suggestedActions: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedEffort: string;
}

// Progress tracking for the revision process
export interface RevisionProgress {
  phase: 'parsing' | 'comment-analysis' | 'section-revision' | 'integration' | 'response-generation' | 'export' | 'completed';
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  percentage: number;
  estimatedTimeRemaining?: number;
  currentSection?: ArticleSection;
  errors: string[];
  warnings: string[];
}

// AI Assistant workflow state
export interface AIAssistantState {
  id: string;
  name: string;
  status: 'idle' | 'processing' | 'completed' | 'error';
  progress: number;
  currentTask?: string;
  result?: any;
  error?: string;
  startTime?: Date;
  endTime?: Date;
}

// Complete revision workflow state
export interface RevisionWorkflowState {
  // Input data
  originalArticle: ParsedArticle | null;
  reviewerComments: ParsedReviewerComments[];
  
  // Processing state
  progress: RevisionProgress;
  aiAssistants: Record<string, AIAssistantState>;
  
  // Results
  revisedArticle: RevisedArticle | null;
  responseLetter: ResponseLetter | null;
  manualInterventions: ManualIntervention[];
  
  // Export options
  exportFormats: ('word' | 'pdf')[];
  highlightChanges: boolean;
  includeComments: boolean;
}

// Export configuration
export interface ExportConfiguration {
  format: 'word' | 'pdf';
  includeTrackChanges: boolean;
  highlightChanges: boolean;
  includeComments: boolean;
  includeResponseLetter: boolean;
  includeManualSuggestions: boolean;
  fileName?: string;
}
