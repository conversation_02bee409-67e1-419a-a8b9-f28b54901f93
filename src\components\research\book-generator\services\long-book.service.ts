import { BookMetadata, UserChapter, GeneratedChapter, BookContext } from '../types';
import { getOptimalModel } from '../constants';

export interface LongBookStrategy {
  chunkSize: number;
  contextWindow: number;
  summaryFrequency: number;
  modelRotation: boolean;
}

export interface BookProgressState {
  totalChapters: number;
  completedChapters: number;
  currentChunk: number;
  totalChunks: number;
  estimatedTimeRemaining: number;
  contextSummaries: string[];
}

/**
 * Service for handling very long books (100+ pages) with optimized context management
 */
export class LongBookService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
  }

  /**
   * Determine optimal strategy for long book generation
   */
  getOptimalStrategy(metadata: BookMetadata, chapters: UserChapter[]): LongBookStrategy {
    const totalChapters = chapters.length;
    const estimatedWords = chapters.reduce((sum, ch) => sum + (ch.outline.estimatedWordCount || 3000), 0);
    const estimatedPages = Math.ceil(estimatedWords / 300); // ~300 words per page

    if (estimatedPages > 500 || totalChapters > 20) {
      // Very long book strategy
      return {
        chunkSize: 3,
        contextWindow: 3,
        summaryFrequency: 2, // Generate summary every 2 chapters
        modelRotation: true
      };
    } else if (estimatedPages > 200 || totalChapters > 10) {
      // Long book strategy
      return {
        chunkSize: 4,
        contextWindow: 4,
        summaryFrequency: 3,
        modelRotation: false
      };
    } else {
      // Standard book strategy
      return {
        chunkSize: 6,
        contextWindow: 5,
        summaryFrequency: 5,
        modelRotation: false
      };
    }
  }

  /**
   * Generate comprehensive book summary for context management
   */
  async generateBookSummary(
    metadata: BookMetadata,
    completedChapters: GeneratedChapter[]
  ): Promise<string> {
    try {
      if (!this.hasValidApiKey()) {
        return this.generateFallbackSummary(metadata, completedChapters);
      }

      const prompt = this.buildBookSummaryPrompt(metadata, completedChapters);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: getOptimalModel("summary-generation", metadata),
          messages: [
            {
              role: "system",
              content: "You are an expert book summarizer. Create concise, comprehensive summaries that capture key themes, developments, and narrative progression for context management in long book generation."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 1500,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.choices?.[0]?.message?.content || this.generateFallbackSummary(metadata, completedChapters);

    } catch (error) {
      console.error('Book summary generation failed:', error);
      return this.generateFallbackSummary(metadata, completedChapters);
    }
  }

  /**
   * Generate contextual chapter summaries for long books
   */
  async generateContextualSummaries(
    chapters: GeneratedChapter[],
    windowSize: number = 5
  ): Promise<string[]> {
    const summaries: string[] = [];
    
    for (let i = 0; i < chapters.length; i += windowSize) {
      const chunk = chapters.slice(i, Math.min(i + windowSize, chapters.length));
      const summary = await this.generateChunkSummary(chunk);
      summaries.push(summary);
    }
    
    return summaries;
  }

  /**
   * Optimize context for very long books by creating hierarchical summaries
   */
  async optimizeContextForLongBook(
    metadata: BookMetadata,
    allChapters: GeneratedChapter[],
    currentChapterIndex: number,
    strategy: LongBookStrategy
  ): Promise<BookContext> {
    try {
      // Get relevant chapters within context window
      const startIndex = Math.max(0, currentChapterIndex - strategy.contextWindow);
      const relevantChapters = allChapters.slice(startIndex, currentChapterIndex);

      // Generate hierarchical summaries
      const recentSummary = await this.generateChunkSummary(
        relevantChapters.slice(-3) // Last 3 chapters in detail
      );

      const overallSummary = relevantChapters.length > 3 
        ? await this.generateChunkSummary(relevantChapters.slice(0, -3))
        : '';

      // Create optimized context
      return {
        bookMetadata: metadata,
        bookOutline: this.generateBookOutline(metadata, allChapters),
        previousChapters: relevantChapters.map(ch => ({
          chapterId: ch.id,
          summary: ch.summary || `Chapter ${ch.order}: ${ch.title}`,
          keyPoints: [],
          wordCount: ch.wordCount || 0,
          generatedAt: new Date()
        })),
        totalWordCount: allChapters.reduce((sum, ch) => sum + (ch.wordCount || 0), 0),
        recentSummary,
        overallSummary
      };

    } catch (error) {
      console.error('Context optimization failed:', error);
      // Return basic context as fallback
      return {
        bookMetadata: metadata,
        bookOutline: '',
        previousChapters: [],
        totalWordCount: 0
      };
    }
  }

  /**
   * Calculate progress and time estimates for long books
   */
  calculateProgress(
    totalChapters: number,
    completedChapters: number,
    startTime: Date,
    strategy: LongBookStrategy
  ): BookProgressState {
    const progress = completedChapters / totalChapters;
    const elapsedTime = Date.now() - startTime.getTime();
    const estimatedTotalTime = progress > 0 ? elapsedTime / progress : 0;
    const estimatedTimeRemaining = Math.max(0, estimatedTotalTime - elapsedTime);

    const totalChunks = Math.ceil(totalChapters / strategy.chunkSize);
    const currentChunk = Math.ceil(completedChapters / strategy.chunkSize);

    return {
      totalChapters,
      completedChapters,
      currentChunk,
      totalChunks,
      estimatedTimeRemaining,
      contextSummaries: []
    };
  }

  /**
   * Handle memory management for very long books
   */
  async manageMemoryForLongBook(
    chapters: GeneratedChapter[],
    maxMemoryChapters: number = 10
  ): Promise<GeneratedChapter[]> {
    if (chapters.length <= maxMemoryChapters) {
      return chapters;
    }

    // Keep the most recent chapters and compress older ones
    const recentChapters = chapters.slice(-maxMemoryChapters);
    const olderChapters = chapters.slice(0, -maxMemoryChapters);

    // Compress older chapters by keeping only essential information
    const compressedOlderChapters = olderChapters.map(chapter => ({
      ...chapter,
      content: undefined, // Remove full content to save memory
      summary: chapter.summary || `Chapter ${chapter.order}: ${chapter.title}`,
      wordCount: chapter.wordCount || 0
    }));

    return [...compressedOlderChapters, ...recentChapters];
  }

  private buildBookSummaryPrompt(metadata: BookMetadata, chapters: GeneratedChapter[]): string {
    return `Create a comprehensive summary of this book for context management:

Book: "${metadata.title}"
Genre: ${metadata.genre}
Target Audience: ${metadata.targetAudience}

Completed Chapters (${chapters.length}):
${chapters.map((ch, i) => `
Chapter ${i + 1}: ${ch.title}
Summary: ${ch.summary || 'No summary available'}
Word Count: ${ch.wordCount || 0}
`).join('\n')}

Create a summary that includes:
1. Main themes and narrative progression
2. Key developments and turning points
3. Character/concept evolution (if applicable)
4. Important context for future chapters
5. Overall trajectory and direction

Keep it concise but comprehensive (max 400 words).`;
  }

  private async generateChunkSummary(chapters: GeneratedChapter[]): Promise<string> {
    if (chapters.length === 0) return '';
    
    try {
      const prompt = `Summarize these chapters for context management:

${chapters.map((ch, i) => `
Chapter: ${ch.title}
Content Summary: ${ch.summary || 'No summary available'}
`).join('\n')}

Create a concise summary focusing on key developments, themes, and context needed for subsequent chapters (max 200 words).`;

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: getOptimalModel("summary-generation"),
          messages: [
            {
              role: "system",
              content: "You are an expert at creating concise chapter summaries for context management."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 500,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.choices?.[0]?.message?.content || this.generateFallbackChunkSummary(chapters);

    } catch (error) {
      console.error('Chunk summary generation failed:', error);
      return this.generateFallbackChunkSummary(chapters);
    }
  }

  private generateFallbackSummary(metadata: BookMetadata, chapters: GeneratedChapter[]): string {
    return `Book "${metadata.title}" (${metadata.genre}) - ${chapters.length} chapters completed. 
    Key themes and developments from completed chapters provide foundation for continuing narrative. 
    Total word count: ${chapters.reduce((sum, ch) => sum + (ch.wordCount || 0), 0)} words.`;
  }

  private generateFallbackChunkSummary(chapters: GeneratedChapter[]): string {
    return `Summary of ${chapters.length} chapters: ${chapters.map(ch => ch.title).join(', ')}. 
    Key developments and themes continue the book's progression.`;
  }

  private generateBookOutline(metadata: BookMetadata, chapters: GeneratedChapter[]): string {
    return chapters.map((ch, i) => `${i + 1}. ${ch.title}: ${ch.description || 'Chapter content'}`).join('\n');
  }

  private hasValidApiKey(): boolean {
    return !!this.apiKey && !this.apiKey.includes('your_') && this.apiKey.length > 20;
  }
}

export const longBookService = new LongBookService();
