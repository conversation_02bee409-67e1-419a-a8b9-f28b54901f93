/**
 * Progress Tracker Component
 * Gamification and progress tracking for student motivation
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { 
  Trophy, 
  Star, 
  Target, 
  TrendingUp, 
  Award, 
  Zap, 
  Brain,
  BookOpen,
  CheckCircle,
  Clock,
  Flame,
  Calendar,
  BarChart3,
  Medal,
  Crown,
  Sparkles
} from "lucide-react";

interface ProgressTrackerProps {
  studentName?: string;
  currentLevel: number;
  totalXP: number;
  streakDays: number;
  completedTopics: number;
  totalTopics: number;
  achievements: Achievement[];
  recentActivity: ActivityItem[];
  onViewDetails?: () => void;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface ActivityItem {
  id: string;
  type: 'topic_completed' | 'streak_milestone' | 'level_up' | 'achievement_unlocked';
  title: string;
  timestamp: Date;
  xpGained?: number;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  studentName = "Student",
  currentLevel,
  totalXP,
  streakDays,
  completedTopics,
  totalTopics,
  achievements,
  recentActivity,
  onViewDetails
}) => {
  const [showAchievements, setShowAchievements] = useState(false);
  const [celebratingAchievement, setCelebratingAchievement] = useState<Achievement | null>(null);

  // Calculate level progress
  const xpForCurrentLevel = currentLevel * 1000;
  const xpForNextLevel = (currentLevel + 1) * 1000;
  const currentLevelProgress = ((totalXP - xpForCurrentLevel) / (xpForNextLevel - xpForCurrentLevel)) * 100;

  // Get level title
  const getLevelTitle = (level: number) => {
    if (level < 5) return "Curious Learner";
    if (level < 10) return "Knowledge Seeker";
    if (level < 20) return "Dedicated Student";
    if (level < 35) return "Learning Expert";
    if (level < 50) return "Master Scholar";
    return "Legendary Learner";
  };

  // Get achievement rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-600 bg-gray-100';
      case 'rare': return 'text-blue-600 bg-blue-100';
      case 'epic': return 'text-purple-600 bg-purple-100';
      case 'legendary': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Simulate achievement unlock
  useEffect(() => {
    const newAchievement = achievements.find(a => 
      a.unlockedAt && 
      Date.now() - a.unlockedAt.getTime() < 5000 // Within last 5 seconds
    );
    
    if (newAchievement && !celebratingAchievement) {
      setCelebratingAchievement(newAchievement);
      setTimeout(() => setCelebratingAchievement(null), 3000);
    }
  }, [achievements, celebratingAchievement]);

  return (
    <div className="space-y-6">
      {/* Achievement Celebration Modal */}
      <AnimatePresence>
        {celebratingAchievement && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              className="bg-white rounded-2xl p-8 max-w-md mx-4 text-center"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{ duration: 0.6, repeat: 2 }}
                className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
              >
                <Trophy className="w-10 h-10 text-white" />
              </motion.div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Achievement Unlocked!</h3>
              <h4 className="text-lg font-semibold text-blue-600 mb-2">{celebratingAchievement.title}</h4>
              <p className="text-gray-600 mb-4">{celebratingAchievement.description}</p>
              
              <Button
                onClick={() => setCelebratingAchievement(null)}
                className="bg-gradient-to-r from-blue-500 to-purple-500"
              >
                Awesome!
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Progress Card */}
      <Card className="shadow-xl border-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg"
              >
                <Crown className="w-8 h-8 text-white" />
              </motion.div>
              
              <div>
                <CardTitle className="text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Welcome back, {studentName}!
                </CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    Level {currentLevel}
                  </Badge>
                  <span className="text-sm text-gray-600">{getLevelTitle(currentLevel)}</span>
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={onViewDetails}
              className="bg-white/80 backdrop-blur-sm"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Level Progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Level Progress</span>
              <span className="text-sm text-gray-500">
                {Math.round(currentLevelProgress)}% to Level {currentLevel + 1}
              </span>
            </div>
            <div className="relative">
              <Progress value={currentLevelProgress} className="h-3" />
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center"
              >
                <Star className="w-3 h-3 text-white" />
              </motion.div>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>{totalXP.toLocaleString()} XP</span>
              <span>{xpForNextLevel.toLocaleString()} XP</span>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl"
            >
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Flame className="w-5 h-5 text-orange-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{streakDays}</div>
              <div className="text-xs text-gray-600">Day Streak</div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl"
            >
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{completedTopics}</div>
              <div className="text-xs text-gray-600">Topics Mastered</div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl"
            >
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Trophy className="w-5 h-5 text-purple-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {achievements.filter(a => a.unlockedAt).length}
              </div>
              <div className="text-xs text-gray-600">Achievements</div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              className="text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl"
            >
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Target className="w-5 h-5 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round((completedTopics / totalTopics) * 100)}%
              </div>
              <div className="text-xs text-gray-600">Overall Progress</div>
            </motion.div>
          </div>

          {/* Recent Achievements */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                <Sparkles className="w-4 h-4 text-yellow-500" />
                <span>Recent Achievements</span>
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAchievements(!showAchievements)}
              >
                {showAchievements ? 'Hide' : 'Show All'}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {achievements
                .filter(a => a.unlockedAt)
                .slice(0, showAchievements ? undefined : 4)
                .map((achievement) => (
                  <motion.div
                    key={achievement.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center space-x-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg"
                  >
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getRarityColor(achievement.rarity)}`}>
                      <achievement.icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-gray-900 truncate">{achievement.title}</h5>
                      <p className="text-xs text-gray-600 truncate">{achievement.description}</p>
                    </div>
                    <Badge variant="outline" className={`text-xs ${getRarityColor(achievement.rarity)}`}>
                      {achievement.rarity}
                    </Badge>
                  </motion.div>
                ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="space-y-3">
            <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
              <Clock className="w-4 h-4 text-blue-500" />
              <span>Recent Activity</span>
            </h4>

            <div className="space-y-2 max-h-40 overflow-y-auto">
              {recentActivity.slice(0, 5).map((activity) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center space-x-3 p-2 bg-white/40 backdrop-blur-sm rounded-lg"
                >
                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900 truncate">{activity.title}</p>
                    <p className="text-xs text-gray-500">
                      {activity.timestamp.toLocaleDateString()}
                      {activity.xpGained && (
                        <span className="ml-2 text-green-600">+{activity.xpGained} XP</span>
                      )}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProgressTracker;
