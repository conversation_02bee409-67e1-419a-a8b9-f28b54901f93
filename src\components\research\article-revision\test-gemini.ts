/**
 * Test file to verify Gemini AI integration
 * Run this to test if the Gemini API is working correctly
 */

import { geminiAIService } from './services/gemini-ai.service';

export async function testGeminiIntegration(): Promise<boolean> {
  try {
    console.log('🧪 Testing Gemini AI integration...');
    
    // Check if API key is configured
    if (!geminiAIService.hasValidApiKey()) {
      console.error('❌ Gemini API key is not configured properly');
      return false;
    }
    
    console.log('✅ API key is configured');
    
    // Test basic content generation
    const testPrompt = `You are an academic writing assistant. Please provide a brief (50 words) explanation of what peer review is in academic publishing.`;
    
    const response = await geminiAIService.generateContent(testPrompt, 'gemini-2.5-pro', {
      maxTokens: 100,
      temperature: 0.3
    });
    
    if (response && response.length > 10) {
      console.log('✅ Gemini API is working correctly');
      console.log('📝 Test response:', response.substring(0, 100) + '...');
      return true;
    } else {
      console.error('❌ Gemini API returned empty or invalid response');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Gemini API test failed:', error);
    return false;
  }
}

// Export for use in development
if (typeof window !== 'undefined') {
  (window as any).testGeminiIntegration = testGeminiIntegration;
}
