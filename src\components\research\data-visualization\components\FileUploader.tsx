import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertTriangle,
  Loader2,
  File,
  Database
} from "lucide-react";
import { FileProcessingService } from '../services/file-processing.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { SUPPORTED_FILE_TYPES, FILE_SIZE_LIMITS } from '../types';
import { DATA_VIZ_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../constants';

interface FileUploaderProps {
  onFileProcessed?: (fileId: string) => void;
  className?: string;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onFileProcessed,
  className = ""
}) => {
  const {
    addFile,
    updateFile,
    setCurrentFile,
    setUploading,
    addError,
    isUploading
  } = useDataVisualizationStore();

  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingFile, setProcessingFile] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          addError(`${file.name}: ${ERROR_MESSAGES.FILE_TOO_LARGE}`);
        } else if (error.code === 'file-invalid-type') {
          addError(`${file.name}: ${ERROR_MESSAGES.UNSUPPORTED_FORMAT}`);
        } else {
          addError(`${file.name}: ${error.message}`);
        }
      });
    });

    // Process accepted files
    for (const file of acceptedFiles) {
      await processFile(file);
    }
  }, [addError]);

  const processFile = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // Validate file first
      const validation = FileProcessingService.validateFile(file);
      
      if (!validation.isValid) {
        validation.errors.forEach(error => addError(error));
        return;
      }

      // Show warnings if any
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });

      // Create initial file entry
      const initialFile = {
        id: `temp-${Date.now()}`,
        name: file.name,
        size: file.size,
        type: file.type,
        data: [],
        headers: [],
        uploadedAt: new Date(),
        status: 'uploading' as const
      };

      addFile(initialFile);
      setProcessingFile(initialFile.id);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      // Process the file
      const processedFile = await FileProcessingService.processFile(file);
      
      // Update with processed data
      updateFile(initialFile.id, {
        ...processedFile,
        id: initialFile.id // Keep the same ID
      });

      setUploadProgress(100);
      setCurrentFile({ ...processedFile, id: initialFile.id });
      
      toast.success(SUCCESS_MESSAGES.FILE_UPLOADED);
      
      if (onFileProcessed) {
        onFileProcessed(initialFile.id);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      addError(errorMessage);
      toast.error(errorMessage);
      
      if (processingFile) {
        updateFile(processingFile, {
          status: 'error',
          error: errorMessage
        });
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setProcessingFile(null);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxSize: FILE_SIZE_LIMITS.MAX_FILE_SIZE,
    multiple: false
  });

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Upload Data File
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
              }
              ${isUploading ? 'pointer-events-none opacity-50' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <div className="flex flex-col items-center space-y-4">
              {isUploading ? (
                <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
              ) : (
                <Upload className="h-12 w-12 text-gray-400" />
              )}
              
              <div>
                <p className="text-lg font-medium text-gray-900">
                  {isDragActive
                    ? "Drop your file here"
                    : "Drag & drop a data file here, or click to select"
                  }
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Supports CSV, Excel (.xlsx, .xls) files up to {formatFileSize(FILE_SIZE_LIMITS.MAX_FILE_SIZE)}
                </p>
              </div>

              {!isUploading && (
                <Button variant="outline" size="sm">
                  <File className="h-4 w-4 mr-2" />
                  Choose File
                </Button>
              )}
            </div>
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing file...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Supported Formats Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Supported File Formats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries(SUPPORTED_FILE_TYPES).map(([mimeType, description]) => (
            <div key={mimeType} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{description}</span>
              </div>
              <Badge variant="secondary" className="text-xs">
                {mimeType.split('/').pop()?.toUpperCase()}
              </Badge>
            </div>
          ))}
          
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Files must contain at least {DATA_VIZ_CONFIG.MIN_ROWS_FOR_ANALYSIS} rows of data for analysis.
              First row should contain column headers.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};
