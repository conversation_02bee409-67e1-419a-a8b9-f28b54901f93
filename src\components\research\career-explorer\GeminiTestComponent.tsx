/**
 * Test Component for Gemini Integration
 * This component can be used to test the Gemini career service in the browser
 */

import React, { useState } from 'react';
import { geminiCareerService } from './services/gemini-career.service';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Sparkles, CheckCircle, AlertCircle, Loader2 } from "lucide-react";

export function GeminiTestComponent() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const sampleResumeText = `
John Doe
Software Engineer

EXPERIENCE:
- 5 years of experience in full-stack web development
- Proficient in JavaScript, React, Node.js, and Python
- Experience with cloud platforms (AWS, Azure)
- Led a team of 3 developers on multiple projects
- Built scalable web applications serving 100k+ users

EDUCATION:
- Bachelor's Degree in Computer Science
- Certified AWS Solutions Architect

SKILLS:
- Programming: JavaScript, Python, Java, TypeScript
- Frameworks: React, Node.js, Express, Django
- Databases: PostgreSQL, MongoDB, Redis
- Cloud: AWS, Docker, Kubernetes
- Tools: Git, Jenkins, Jira
`;

  const sampleContext = `
I'm interested in transitioning to a more senior role with leadership responsibilities. 
I enjoy working with cutting-edge technologies and want to make a positive impact on products that reach millions of users.
I prefer remote work opportunities and am open to roles in AI/ML, cloud architecture, or product management.
`;

  const testGeminiConnection = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // Test connection first
      const connectionTest = await geminiCareerService.testConnection();
      
      if (!connectionTest.success) {
        throw new Error(`Connection failed: ${connectionTest.error}`);
      }

      // Test career generation
      const response = await geminiCareerService.generateCareerPaths({
        resumeText: sampleResumeText,
        additionalContext: sampleContext,
        model: 'gemini-2.5-flash'
      });

      setResult(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const testGeminiPro = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await geminiCareerService.generateCareerPaths({
        resumeText: sampleResumeText,
        additionalContext: sampleContext,
        model: 'gemini-2.5-pro'
      });

      setResult(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-yellow-600" />
            Gemini Career Service Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={testGeminiConnection}
              disabled={isLoading}
              className="flex items-center"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              Test Gemini 2.5 Flash
            </Button>
            
            <Button 
              onClick={testGeminiPro}
              disabled={isLoading}
              variant="outline"
              className="flex items-center"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              Test Gemini 2.5 Pro
            </Button>
          </div>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-green-800">
                Successfully generated {result.careers?.length || 0} career paths in {result.processingTime}ms
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {result && result.careers && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Career Paths</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {result.careers.map((career: any, index: number) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{career.jobTitle}</h3>
                    <Badge variant={
                      career.difficulty === 'Low' ? 'default' :
                      career.difficulty === 'Medium' ? 'secondary' : 'destructive'
                    }>
                      {career.difficulty}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{career.jobDescription}</p>
                  <div className="flex gap-4 text-xs text-gray-500">
                    <span>Timeline: {career.timeline}</span>
                    <span>Salary: {career.salary}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Sample Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Resume Text (Sample)</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
                {sampleResumeText.trim()}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">Additional Context (Sample)</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded">
                {sampleContext.trim()}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
