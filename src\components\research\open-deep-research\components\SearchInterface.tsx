/**
 * Search Interface Component
 * Handles search input, controls, and mode switching
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Brain, 
  FileText, 
  Plus, 
  UploadIcon, 
  Loader2,
  Sparkles 
} from 'lucide-react';

import { SearchInterfaceProps } from '../types';
import { TIME_FILTERS, AI_MODELS, SUPPORTED_FILE_TYPES } from '../constants';
import { ModelSelector } from './ModelSelector';

export const SearchInterface: React.FC<SearchInterfaceProps> = ({
  query,
  timeFilter,
  selectedModel,
  isAgentMode,
  reportPrompt,
  newUrl,
  status,
  onQueryChange,
  onTimeFilterChange,
  onModelChange,
  onAgentModeChange,
  onReportPromptChange,
  onNewUrlChange,
  onSearch,
  onAgentSearch,
  onAddCustomUrl,
  onFileUpload,
  onGenerateReport,
}) => {
  const isLoading = status.loading || status.generatingReport || status.agentStep !== 'idle';

  return (
    <div className="space-y-6">
      {/* Agent Mode Toggle */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-3 p-3 bg-white rounded-lg border shadow-sm">
          <Checkbox
            id="agent-mode"
            checked={isAgentMode}
            onCheckedChange={onAgentModeChange}
            className="w-4 h-4"
          />
          <label
            htmlFor="agent-mode"
            className="text-sm font-medium leading-none cursor-pointer select-none"
          >
            <span className="flex items-center gap-2">
              <Brain className="w-4 h-4 text-blue-600" />
              Agent Mode (Automatic research and report generation)
            </span>
          </label>
        </div>
      </div>

      {/* Search Form */}
      <form onSubmit={isAgentMode ? onAgentSearch : onSearch} className="space-y-4">
        {!isAgentMode ? (
          <>
            {/* Manual Mode - Search Input */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Input
                  type="text"
                  value={query}
                  onChange={(e) => onQueryChange(e.target.value)}
                  placeholder="Enter your search query..."
                  className="pr-10"
                  disabled={isLoading}
                />
                <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                {/* Time Filter */}
                <Select value={timeFilter} onValueChange={onTimeFilterChange} disabled={isLoading}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="Time range" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_FILTERS.map((filter) => (
                      <SelectItem key={filter.value} value={filter.value}>
                        {filter.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Model Selector */}
                <ModelSelector
                  value={selectedModel}
                  onValueChange={onModelChange}
                  disabled={isLoading}
                  className="w-full sm:w-[200px]"
                />

                {/* Search Button */}
                <Button
                  type="submit"
                  disabled={isLoading || !query.trim()}
                  className="w-full sm:w-auto"
                >
                  {status.loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="w-4 h-4 mr-2" />
                      Search
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Custom URL and File Upload */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                type="url"
                value={newUrl}
                onChange={(e) => onNewUrlChange(e.target.value)}
                placeholder="Add custom URL..."
                className="flex-1"
                disabled={isLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    onAddCustomUrl(e);
                  }
                }}
              />
              
              <Button
                type="button"
                variant="outline"
                onClick={onAddCustomUrl}
                disabled={isLoading || !newUrl.trim()}
                className="hidden sm:inline-flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add URL
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={onAddCustomUrl}
                disabled={isLoading || !newUrl.trim()}
                className="sm:hidden"
                size="icon"
              >
                <Plus className="h-4 w-4" />
              </Button>

              {/* File Upload */}
              <div className="relative">
                <Input
                  type="file"
                  onChange={onFileUpload}
                  className="absolute inset-0 opacity-0 cursor-pointer"
                  accept={SUPPORTED_FILE_TYPES}
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  variant="outline"
                  disabled={isLoading}
                  className="hidden sm:inline-flex items-center gap-2 pointer-events-none"
                >
                  <UploadIcon className="h-4 w-4" />
                  Upload File
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  disabled={isLoading}
                  className="sm:hidden pointer-events-none"
                >
                  <UploadIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Agent Mode - Research Topic Input */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Input
                  value={query || reportPrompt}
                  onChange={(e) => {
                    onReportPromptChange(e.target.value);
                    onQueryChange('');
                  }}
                  placeholder="What would you like to research? (e.g., 'Tesla Q4 2024 financial performance and market impact')"
                  className="pr-10 text-lg"
                  disabled={isLoading}
                />
                <Brain className="absolute right-3 top-3 h-5 w-5 text-blue-400" />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2">
                {/* Model Selector */}
                <ModelSelector
                  value={selectedModel}
                  onValueChange={onModelChange}
                  disabled={isLoading}
                  className="w-full sm:w-[200px]"
                />

                {/* Agent Search Button */}
                <Button
                  type="submit"
                  disabled={status.agentStep !== 'idle' || !reportPrompt.trim()}
                  className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {status.agentStep !== 'idle' ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      {
                        {
                          processing: 'Planning Research...',
                          searching: 'Searching Web...',
                          analyzing: 'Analyzing Results...',
                          generating: 'Writing Report...',
                        }[status.agentStep]
                      }
                    </span>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Start Deep Research
                    </>
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </form>

      {/* Manual Mode Report Generation */}
      {!isAgentMode && (
        <div className="space-y-3">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Input
                value={reportPrompt}
                onChange={(e) => onReportPromptChange(e.target.value)}
                placeholder="What would you like to know about these sources? (e.g., 'Compare and analyze the key points')"
                className="pr-10"
                disabled={isLoading}
              />
              <FileText className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
            
            <Button
              onClick={onGenerateReport}
              disabled={
                !reportPrompt.trim() ||
                status.generatingReport ||
                !selectedModel
              }
              type="button"
              className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
            >
              {status.generatingReport ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Generating...
                </span>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </div>
          
          <div className="text-sm text-gray-600 text-center">
            {status.generatingReport && (
              <p>
                {status.fetchStatus.successful} fetched, {status.fetchStatus.fallback} failed 
                (of {status.fetchStatus.total})
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
