import { TavilySearchService } from './tavily-search.service';
import { ResearchAIService } from './research-ai.service';
import { ReferenceManagementService } from './reference-management.service';

export interface ResearchStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  result?: any;
  error?: string;
}

export interface ResearchFlowContext {
  query: string;
  searchResults?: any;
  rawContent?: string;
  organizedContent?: string;
  references?: any[];
  citations?: any[];
  currentStep: number;
  steps: ResearchStep[];
}

export interface ResearchFlowOptions {
  maxResults?: number;
  searchDepth?: 'basic' | 'advanced' | 'comprehensive';
  citationStyle?: 'apa' | 'mla' | 'chicago' | 'harvard';
}

/**
 * Research Flow Service - Manages multi-step research workflow
 * 
 * Step 1: Search & Gather Sources
 * Step 2: Write Content with Basic Citations
 * Step 3: Organize & Format References
 */
export class ResearchFlowService {
  private tavilyService: TavilySearchService;
  private aiService: ResearchAIService;
  private referenceService: ReferenceManagementService;

  constructor() {
    this.tavilyService = new TavilySearchService();
    this.aiService = new ResearchAIService();
    this.referenceService = new ReferenceManagementService();
  }

  /**
   * Initialize a new research flow
   */
  initializeFlow(query: string, options: ResearchFlowOptions = {}): ResearchFlowContext {
    const steps: ResearchStep[] = [
      {
        id: 'search',
        name: 'Search & Gather Sources',
        description: 'Searching for academic and authoritative sources',
        status: 'pending'
      },
      {
        id: 'write',
        name: 'Write Content with Citations',
        description: 'Creating academic content with basic citations',
        status: 'pending'
      },
      {
        id: 'organize',
        name: 'Organize References',
        description: 'Formatting and organizing references properly',
        status: 'pending'
      }
    ];

    return {
      query,
      currentStep: 0,
      steps,
      ...options
    };
  }

  /**
   * Execute the complete research flow
   */
  async executeFlow(
    context: ResearchFlowContext,
    onStepUpdate?: (context: ResearchFlowContext) => void
  ): Promise<ResearchFlowContext> {
    try {
      // Step 1: Search & Gather Sources
      context = await this.executeSearchStep(context);
      onStepUpdate?.(context);

      // Step 2: Write Content with Basic Citations
      context = await this.executeWriteStep(context);
      onStepUpdate?.(context);

      // Step 3: Organize & Format References
      context = await this.executeOrganizeStep(context);
      onStepUpdate?.(context);

      return context;
    } catch (error) {
      console.error('Research flow error:', error);
      this.markStepAsError(context, error as Error);
      throw error;
    }
  }

  /**
   * Execute individual step
   */
  async executeStep(
    context: ResearchFlowContext,
    stepId: string
  ): Promise<ResearchFlowContext> {
    switch (stepId) {
      case 'search':
        return this.executeSearchStep(context);
      case 'write':
        return this.executeWriteStep(context);
      case 'organize':
        return this.executeOrganizeStep(context);
      default:
        throw new Error(`Unknown step: ${stepId}`);
    }
  }

  /**
   * Step 1: Search & Gather Sources
   */
  private async executeSearchStep(context: ResearchFlowContext): Promise<ResearchFlowContext> {
    const step = context.steps.find(s => s.id === 'search');
    if (!step) throw new Error('Search step not found');

    try {
      step.status = 'in-progress';
      context.currentStep = 0;

      // Enhanced search with comprehensive source gathering
      const searchResults = await this.tavilyService.searchAcademic(context.query, {
        maxResults: context.maxResults || 15,
        searchDepth: context.searchDepth || 'advanced',
        includeAnswer: true
      });

      // Process and enrich search results
      const enrichedResults = await this.enrichSearchResults(searchResults);

      step.status = 'completed';
      step.result = enrichedResults;
      context.searchResults = enrichedResults;
      context.currentStep = 1;

      return context;
    } catch (error) {
      step.status = 'error';
      step.error = (error as Error).message;
      throw error;
    }
  }

  /**
   * Step 2: Write Content with Basic Citations
   */
  private async executeWriteStep(context: ResearchFlowContext): Promise<ResearchFlowContext> {
    const step = context.steps.find(s => s.id === 'write');
    if (!step) throw new Error('Write step not found');

    try {
      step.status = 'in-progress';
      context.currentStep = 1;

      if (!context.searchResults) {
        throw new Error('Search results not available');
      }

      // Generate content with basic citations using focused prompt
      const content = await this.aiService.generateContentWithBasicCitations(
        context.query,
        context.searchResults
      );

      step.status = 'completed';
      step.result = content;
      context.rawContent = content.text;
      context.citations = content.citations;
      context.currentStep = 2;

      return context;
    } catch (error) {
      step.status = 'error';
      step.error = (error as Error).message;
      throw error;
    }
  }

  /**
   * Step 3: Organize & Format References
   */
  private async executeOrganizeStep(context: ResearchFlowContext): Promise<ResearchFlowContext> {
    const step = context.steps.find(s => s.id === 'organize');
    if (!step) throw new Error('Organize step not found');

    try {
      step.status = 'in-progress';
      context.currentStep = 2;

      if (!context.rawContent || !context.citations) {
        throw new Error('Content and citations not available');
      }

      // Organize references using dedicated AI prompt
      const organizedResult = await this.aiService.organizeReferences(
        context.rawContent,
        context.citations,
        context.searchResults,
        context.citationStyle || 'apa'
      );

      step.status = 'completed';
      step.result = organizedResult;
      context.organizedContent = organizedResult.content;
      context.references = organizedResult.references;
      context.currentStep = 3;

      return context;
    } catch (error) {
      step.status = 'error';
      step.error = (error as Error).message;
      throw error;
    }
  }

  /**
   * Enrich search results with additional metadata
   */
  private async enrichSearchResults(searchResults: any): Promise<any> {
    // Add source type classification, quality scoring, etc.
    const enriched = {
      ...searchResults,
      results: searchResults.results.map((result: any) => ({
        ...result,
        sourceType: this.classifySourceType(result),
        qualityScore: this.calculateQualityScore(result),
        extractedMetadata: this.extractMetadata(result)
      }))
    };

    return enriched;
  }

  /**
   * Classify source type for better organization
   */
  private classifySourceType(source: any): string {
    const url = source.url.toLowerCase();
    const title = source.title.toLowerCase();

    if (this.isAcademicSource(url, title)) return 'academic';
    if (this.isGovernmentSource(url, title)) return 'government';
    if (this.isNewsSource(url, title)) return 'news';
    return 'web';
  }

  /**
   * Calculate quality score for source prioritization
   */
  private calculateQualityScore(source: any): number {
    let score = source.score || 0.5;

    // Boost academic sources
    if (this.isAcademicSource(source.url, source.title)) score += 0.3;
    
    // Boost government sources
    if (this.isGovernmentSource(source.url, source.title)) score += 0.2;
    
    // Boost recent sources
    if (source.published_date) {
      const publishedYear = new Date(source.published_date).getFullYear();
      const currentYear = new Date().getFullYear();
      if (currentYear - publishedYear <= 3) score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Extract metadata from source
   */
  private extractMetadata(source: any): any {
    return {
      author: this.extractAuthor(source),
      year: this.extractYear(source),
      journal: this.extractJournal(source),
      doi: this.extractDOI(source),
      volume: this.extractVolume(source),
      issue: this.extractIssue(source),
      pages: this.extractPages(source)
    };
  }

  // Helper methods for source classification and metadata extraction
  private isAcademicSource(url: string, title: string): boolean {
    const academicDomains = [
      'pubmed.ncbi.nlm.nih.gov', 'scholar.google.com', 'arxiv.org',
      'researchgate.net', 'jstor.org', 'springer.com', 'sciencedirect.com'
    ];
    return academicDomains.some(domain => url.includes(domain)) ||
           title.includes('journal') || title.includes('research');
  }

  private isGovernmentSource(url: string, title: string): boolean {
    return url.includes('.gov') || url.includes('who.int') || 
           title.includes('government') || title.includes('agency');
  }

  private isNewsSource(url: string, title: string): boolean {
    const newsDomains = ['bbc.com', 'cnn.com', 'reuters.com', 'npr.org'];
    return newsDomains.some(domain => url.includes(domain));
  }

  private markStepAsError(context: ResearchFlowContext, error: Error): void {
    const currentStep = context.steps[context.currentStep];
    if (currentStep) {
      currentStep.status = 'error';
      currentStep.error = error.message;
    }
  }

  // Metadata extraction methods
  private extractAuthor(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const authorPatterns = [
      /(?:by|author[s]?:|written by)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*[A-Z][a-z]+)+)/i,
      /([A-Z][a-z]+),?\s+[A-Z]\.(?:\s*[A-Z]\.)*(?:\s*,?\s*&?\s*[A-Z][a-z]+,?\s+[A-Z]\.)*(?:\s+et\s+al\.?)?/,
      /([A-Z][a-z]+\s+et\s+al\.?)/i
    ];

    for (const pattern of authorPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    return null;
  }

  private extractYear(source: any): string | null {
    if (source.published_date) {
      const year = new Date(source.published_date).getFullYear();
      if (year && year > 1900 && year <= new Date().getFullYear()) {
        return year.toString();
      }
    }

    const content = (source.content || '') + ' ' + (source.title || '');
    const yearMatch = content.match(/\b(20[0-2][0-9]|19[8-9][0-9])\b/g);
    if (yearMatch) {
      const years = yearMatch.map(y => parseInt(y))
        .filter(year => year > 1900 && year <= new Date().getFullYear())
        .sort((a, b) => b - a);
      return years.length > 0 ? years[0].toString() : null;
    }
    return null;
  }

  private extractJournal(source: any): string | null {
    const content = source.content || '';
    const title = source.title || '';
    const fullText = `${title} ${content}`;

    const journalPatterns = [
      /published in\s+([A-Z][a-zA-Z\s&]+(?:Journal|Review|Letters|Proceedings)[a-zA-Z\s]*)/i,
      /([A-Z][a-zA-Z\s&]+(?:Journal|Review|Letters|Proceedings)[a-zA-Z\s]*)/i,
      /(Nature|Science|Cell|PNAS|BMJ|NEJM|Lancet|JAMA)/i
    ];

    for (const pattern of journalPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1] || match[0];
      }
    }
    return null;
  }

  private extractDOI(source: any): string | null {
    const content = source.content || '';
    const url = source.url || '';
    const fullText = `${content} ${url}`;

    const doiPatterns = [
      /doi\.org\/([0-9]+\.[0-9]+\/[^\s]+)/i,
      /doi:\s*([0-9]+\.[0-9]+\/[^\s]+)/i,
      /DOI:\s*([0-9]+\.[0-9]+\/[^\s]+)/i
    ];

    for (const pattern of doiPatterns) {
      const match = fullText.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    return null;
  }

  private extractVolume(source: any): string | null {
    const content = source.content || '';
    const volumeMatch = content.match(/volume\s*(\d+)|vol\.?\s*(\d+)/i);
    return volumeMatch ? (volumeMatch[1] || volumeMatch[2]) : null;
  }

  private extractIssue(source: any): string | null {
    const content = source.content || '';
    const issueMatch = content.match(/issue\s*(\d+)|no\.?\s*(\d+)|\((\d+)\)/i);
    return issueMatch ? (issueMatch[1] || issueMatch[2] || issueMatch[3]) : null;
  }

  private extractPages(source: any): string | null {
    const content = source.content || '';
    const pageMatch = content.match(/pages?\s*(\d+[-–]\d+)|pp\.?\s*(\d+[-–]\d+)/i);
    return pageMatch ? (pageMatch[1] || pageMatch[2]) : null;
  }
}

// Export singleton instance
export const researchFlowService = new ResearchFlowService();
