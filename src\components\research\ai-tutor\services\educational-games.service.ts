/**
 * Educational Games Service
 * Creates interactive learning games from document content
 * for the Research Comprehension Platform
 */

import { 
  ResearchDocument, 
  EducationalGame, 
  GameResult,
  TerminologyMatchData,
  ConceptMapData,
  MethodologyPuzzleData,
  CitationChallengeData,
  SequenceOrderData
} from '../types';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface GameGenerationOptions {
  difficulty: number; // 1-5 scale
  gameCount?: number;
  focusSections?: string[];
}

class EducationalGamesService {
  private readonly GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

  /**
   * Check if service is configured
   */
  isConfigured(): boolean {
    return Boolean(this.GEMINI_API_KEY);
  }

  /**
   * Generate educational game from document
   */
  async generateGame(
    document: ResearchDocument,
    gameType: EducationalGame['type'],
    userId: string,
    options: GameGenerationOptions = { difficulty: 3 }
  ): Promise<EducationalGame> {
    if (!this.isConfigured()) {
      throw new Error('Gemini API key not configured');
    }

    try {
      console.log(`Generating ${gameType} game for document "${document.title}"`);

      const content = this.prepareContentForGame(document, options);
      let gameData: any;

      switch (gameType) {
        case 'terminology-match':
          gameData = await this.generateTerminologyMatch(content, options.difficulty);
          break;
        case 'concept-map':
          gameData = await this.generateConceptMap(content, options.difficulty);
          break;
        case 'methodology-puzzle':
          gameData = await this.generateMethodologyPuzzle(content, options.difficulty);
          break;
        case 'citation-challenge':
          gameData = await this.generateCitationChallenge(content, options.difficulty);
          break;
        case 'sequence-order':
          gameData = await this.generateSequenceOrder(content, options.difficulty);
          break;
        default:
          throw new Error(`Unsupported game type: ${gameType}`);
      }

      const game: EducationalGame = {
        id: crypto.randomUUID(),
        documentId: document.id,
        userId,
        type: gameType,
        title: this.generateGameTitle(gameType, document.title),
        description: this.generateGameDescription(gameType),
        gameData,
        difficulty: options.difficulty,
        estimatedTime: this.calculateEstimatedTime(gameType, options.difficulty),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await this.saveGame(game);

      console.log(`Generated ${gameType} game successfully`);
      toast.success(`${this.getGameTypeDisplayName(gameType)} game created!`);

      return game;

    } catch (error) {
      console.error('Game generation failed:', error);
      toast.error(`Failed to generate game: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate terminology matching game
   */
  private async generateTerminologyMatch(content: string, difficulty: number): Promise<TerminologyMatchData> {
    const prompt = `Extract key terms and their definitions from the following academic content. Create a terminology matching game.

Difficulty Level: ${difficulty}/5
Content: ${content.substring(0, 3000)}

Generate 8-12 terms with their definitions. Also provide 4-6 distractor definitions (incorrect but plausible).

Format as JSON:
{
  "terms": [
    {
      "term": "Term name",
      "definition": "Clear, concise definition",
      "category": "Optional category"
    }
  ],
  "distractors": ["Incorrect definition 1", "Incorrect definition 2"]
}`;

    const response = await this.callGeminiAPI(prompt);
    return this.parseTerminologyMatchResponse(response);
  }

  /**
   * Generate concept mapping game
   */
  private async generateConceptMap(content: string, difficulty: number): Promise<ConceptMapData> {
    const prompt = `Identify key concepts and their relationships from the following academic content. Create a concept map structure.

Difficulty Level: ${difficulty}/5
Content: ${content.substring(0, 3000)}

Extract 6-10 main concepts and their relationships. Include different types of relationships.

Format as JSON:
{
  "concepts": [
    {
      "id": "concept1",
      "name": "Concept Name",
      "description": "Brief description"
    }
  ],
  "relationships": [
    {
      "from": "concept1",
      "to": "concept2",
      "label": "relationship description",
      "type": "causes|leads-to|part-of|related-to|opposite-of"
    }
  ]
}`;

    const response = await this.callGeminiAPI(prompt);
    return this.parseConceptMapResponse(response);
  }

  /**
   * Generate methodology puzzle game
   */
  private async generateMethodologyPuzzle(content: string, difficulty: number): Promise<MethodologyPuzzleData> {
    const prompt = `Extract research methodology steps from the following academic content. Create a methodology sequencing puzzle.

Difficulty Level: ${difficulty}/5
Content: ${content.substring(0, 3000)}

Identify 6-10 methodology steps and their correct sequence. Categorize each step.

Format as JSON:
{
  "steps": [
    {
      "id": "step1",
      "description": "Step description",
      "order": 1,
      "category": "data-collection|analysis|interpretation|validation"
    }
  ],
  "correctSequence": ["step1", "step2", "step3"]
}`;

    const response = await this.callGeminiAPI(prompt);
    return this.parseMethodologyPuzzleResponse(response);
  }

  /**
   * Generate citation challenge game
   */
  private async generateCitationChallenge(content: string, difficulty: number): Promise<CitationChallengeData> {
    const prompt = `Create citation format challenges based on the following academic content.

Difficulty Level: ${difficulty}/5
Content: ${content.substring(0, 2000)}

Generate 6-8 citations in different formats (APA, MLA, Chicago, IEEE). Include both correct and incorrect examples.

Format as JSON:
{
  "citations": [
    {
      "id": "citation1",
      "text": "Citation text",
      "format": "APA|MLA|Chicago|IEEE",
      "isCorrect": true|false,
      "errors": ["Error description if incorrect"]
    }
  ]
}`;

    const response = await this.callGeminiAPI(prompt);
    return this.parseCitationChallengeResponse(response);
  }

  /**
   * Generate sequence ordering game
   */
  private async generateSequenceOrder(content: string, difficulty: number): Promise<SequenceOrderData> {
    const prompt = `Extract sequential processes or chronological events from the following academic content.

Difficulty Level: ${difficulty}/5
Content: ${content.substring(0, 3000)}

Identify 5-8 items that have a logical sequence or chronological order.

Format as JSON:
{
  "items": [
    {
      "id": "item1",
      "content": "Item description",
      "correctPosition": 1,
      "category": "Optional category"
    }
  ],
  "context": "Description of what should be ordered"
}`;

    const response = await this.callGeminiAPI(prompt);
    return this.parseSequenceOrderResponse(response);
  }

  /**
   * Call Gemini API
   */
  private async callGeminiAPI(prompt: string): Promise<string> {
    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key=${this.GEMINI_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 2000
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      return data.candidates?.[0]?.content?.parts?.[0]?.text || '';

    } catch (error) {
      console.error('Error calling Gemini API:', error);
      throw error;
    }
  }

  /**
   * Parse terminology match response
   */
  private parseTerminologyMatchResponse(response: string): TerminologyMatchData {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        terms: parsed.terms || this.createFallbackTerms(),
        distractors: parsed.distractors || ['Incorrect definition', 'Another wrong answer']
      };
    } catch (error) {
      console.error('Error parsing terminology match response:', error);
      return this.createFallbackTerminologyMatch();
    }
  }

  /**
   * Parse concept map response
   */
  private parseConceptMapResponse(response: string): ConceptMapData {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        concepts: parsed.concepts || this.createFallbackConcepts(),
        relationships: parsed.relationships || []
      };
    } catch (error) {
      console.error('Error parsing concept map response:', error);
      return this.createFallbackConceptMap();
    }
  }

  /**
   * Parse methodology puzzle response
   */
  private parseMethodologyPuzzleResponse(response: string): MethodologyPuzzleData {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        steps: parsed.steps || this.createFallbackSteps(),
        correctSequence: parsed.correctSequence || []
      };
    } catch (error) {
      console.error('Error parsing methodology puzzle response:', error);
      return this.createFallbackMethodologyPuzzle();
    }
  }

  /**
   * Parse citation challenge response
   */
  private parseCitationChallengeResponse(response: string): CitationChallengeData {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        citations: parsed.citations || this.createFallbackCitations()
      };
    } catch (error) {
      console.error('Error parsing citation challenge response:', error);
      return this.createFallbackCitationChallenge();
    }
  }

  /**
   * Parse sequence order response
   */
  private parseSequenceOrderResponse(response: string): SequenceOrderData {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) throw new Error('No JSON found');

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        items: parsed.items || this.createFallbackSequenceItems(),
        context: parsed.context || 'Order the following items correctly'
      };
    } catch (error) {
      console.error('Error parsing sequence order response:', error);
      return this.createFallbackSequenceOrder();
    }
  }

  // Fallback methods for when AI generation fails
  private createFallbackTerminologyMatch(): TerminologyMatchData {
    return {
      terms: [
        { term: 'Research', definition: 'Systematic investigation to establish facts', category: 'General' },
        { term: 'Methodology', definition: 'System of methods used in research', category: 'General' },
        { term: 'Analysis', definition: 'Detailed examination of elements', category: 'General' }
      ],
      distractors: ['Random incorrect definition', 'Another wrong answer']
    };
  }

  private createFallbackConceptMap(): ConceptMapData {
    return {
      concepts: [
        { id: 'concept1', name: 'Research', description: 'Systematic investigation' },
        { id: 'concept2', name: 'Data', description: 'Information collected' },
        { id: 'concept3', name: 'Analysis', description: 'Examination of data' }
      ],
      relationships: [
        { from: 'concept1', to: 'concept2', label: 'collects', type: 'leads-to' },
        { from: 'concept2', to: 'concept3', label: 'undergoes', type: 'leads-to' }
      ]
    };
  }

  private createFallbackMethodologyPuzzle(): MethodologyPuzzleData {
    return {
      steps: [
        { id: 'step1', description: 'Define research question', order: 1, category: 'data-collection' },
        { id: 'step2', description: 'Collect data', order: 2, category: 'data-collection' },
        { id: 'step3', description: 'Analyze results', order: 3, category: 'analysis' }
      ],
      correctSequence: ['step1', 'step2', 'step3']
    };
  }

  private createFallbackCitationChallenge(): CitationChallengeData {
    return {
      citations: [
        {
          id: 'citation1',
          text: 'Smith, J. (2023). Research Methods. Academic Press.',
          format: 'APA',
          isCorrect: true,
          errors: []
        }
      ]
    };
  }

  private createFallbackSequenceOrder(): SequenceOrderData {
    return {
      items: [
        { id: 'item1', content: 'First step', correctPosition: 1 },
        { id: 'item2', content: 'Second step', correctPosition: 2 },
        { id: 'item3', content: 'Third step', correctPosition: 3 }
      ],
      context: 'Order the research process steps'
    };
  }

  // Helper methods
  private createFallbackTerms() {
    return [
      { term: 'Research', definition: 'Systematic investigation', category: 'General' }
    ];
  }

  private createFallbackConcepts() {
    return [
      { id: 'concept1', name: 'Research', description: 'Systematic investigation' }
    ];
  }

  private createFallbackSteps() {
    return [
      { id: 'step1', description: 'Research step', order: 1, category: 'data-collection' }
    ];
  }

  private createFallbackCitations() {
    return [
      { id: 'citation1', text: 'Sample citation', format: 'APA', isCorrect: true, errors: [] }
    ];
  }

  private createFallbackSequenceItems() {
    return [
      { id: 'item1', content: 'First item', correctPosition: 1 }
    ];
  }

  private prepareContentForGame(document: ResearchDocument, options: GameGenerationOptions): string {
    let content = '';

    if (document.abstract) {
      content += `Abstract: ${document.abstract}\n\n`;
    }

    const sectionsToInclude = options.focusSections?.length 
      ? document.sections.filter(s => options.focusSections!.includes(s.id))
      : document.sections;

    for (const section of sectionsToInclude) {
      if (section.type !== 'references') {
        content += `${section.title}:\n${section.content}\n\n`;
      }
    }

    return content.substring(0, 6000);
  }

  private generateGameTitle(gameType: EducationalGame['type'], documentTitle: string): string {
    const typeNames = {
      'terminology-match': 'Terminology Match',
      'concept-map': 'Concept Mapping',
      'methodology-puzzle': 'Methodology Puzzle',
      'citation-challenge': 'Citation Challenge',
      'sequence-order': 'Sequence Order'
    };

    return `${typeNames[gameType]}: ${documentTitle}`;
  }

  private generateGameDescription(gameType: EducationalGame['type']): string {
    const descriptions = {
      'terminology-match': 'Match key terms with their correct definitions to test your understanding of important concepts.',
      'concept-map': 'Connect related concepts to build a comprehensive understanding of how ideas relate to each other.',
      'methodology-puzzle': 'Arrange research methodology steps in the correct order to understand the research process.',
      'citation-challenge': 'Identify correct and incorrect citation formats to improve your academic writing skills.',
      'sequence-order': 'Put events, processes, or steps in the correct chronological or logical order.'
    };

    return descriptions[gameType];
  }

  private calculateEstimatedTime(gameType: EducationalGame['type'], difficulty: number): number {
    const baseTimes = {
      'terminology-match': 5,
      'concept-map': 8,
      'methodology-puzzle': 6,
      'citation-challenge': 7,
      'sequence-order': 4
    };

    return baseTimes[gameType] + (difficulty - 1) * 2;
  }

  private getGameTypeDisplayName(gameType: EducationalGame['type']): string {
    const names = {
      'terminology-match': 'Terminology Match',
      'concept-map': 'Concept Map',
      'methodology-puzzle': 'Methodology Puzzle',
      'citation-challenge': 'Citation Challenge',
      'sequence-order': 'Sequence Order'
    };

    return names[gameType];
  }

  /**
   * Save game to database
   */
  private async saveGame(game: EducationalGame): Promise<void> {
    try {
      const { error } = await supabase
        .from('educational_games')
        .insert({
          id: game.id,
          document_id: game.documentId,
          user_id: game.userId,
          game_type: game.type,
          title: game.title,
          description: game.description,
          game_data: game.gameData,
          difficulty: game.difficulty,
          estimated_time: game.estimatedTime,
          created_at: game.createdAt.toISOString(),
          updated_at: game.updatedAt.toISOString()
        });

      if (error) {
        console.error('Error saving game:', error);
        throw new Error(`Failed to save game: ${error.message}`);
      }
    } catch (error) {
      console.error('Error in saveGame:', error);
      throw error;
    }
  }

  /**
   * Get games for a document
   */
  async getGamesForDocument(documentId: string, userId?: string): Promise<EducationalGame[]> {
    try {
      let query = supabase
        .from('educational_games')
        .select('*')
        .eq('document_id', documentId);

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching games:', error);
        return [];
      }

      return (data || []).map(game => ({
        id: game.id,
        documentId: game.document_id,
        userId: game.user_id,
        type: game.game_type,
        title: game.title,
        description: game.description,
        gameData: game.game_data,
        difficulty: game.difficulty,
        estimatedTime: game.estimated_time,
        createdAt: new Date(game.created_at),
        updatedAt: new Date(game.updated_at)
      }));

    } catch (error) {
      console.error('Error in getGamesForDocument:', error);
      return [];
    }
  }

  /**
   * Delete game
   */
  async deleteGame(gameId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('educational_games')
        .delete()
        .eq('id', gameId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting game:', error);
        return false;
      }

      toast.success('Game deleted successfully');
      return true;

    } catch (error) {
      console.error('Error in deleteGame:', error);
      toast.error('Failed to delete game');
      return false;
    }
  }
}

export const educationalGamesService = new EducationalGamesService();

/**
 * Game Results Service
 * Handles game playing, scoring, and results management
 */
class GameResultsService {
  /**
   * Save game result
   */
  async saveGameResult(result: GameResult): Promise<void> {
    try {
      const { error } = await supabase
        .from('game_results')
        .insert({
          id: result.id,
          game_id: result.gameId,
          user_id: result.userId,
          score: result.score,
          time_spent: result.timeSpent,
          moves: result.moves,
          hints_used: result.hintsUsed,
          completed_at: result.completedAt.toISOString(),
          game_state: result.gameState,
          feedback: result.feedback
        });

      if (error) {
        console.error('Error saving game result:', error);
        throw new Error(`Failed to save game result: ${error.message}`);
      }

      toast.success(`Game completed! Score: ${result.score}%`);
    } catch (error) {
      console.error('Error in saveGameResult:', error);
      throw error;
    }
  }

  /**
   * Get game results for a user
   */
  async getUserGameResults(userId: string, gameId?: string): Promise<GameResult[]> {
    try {
      let query = supabase
        .from('game_results')
        .select('*')
        .eq('user_id', userId);

      if (gameId) {
        query = query.eq('game_id', gameId);
      }

      const { data, error } = await query.order('completed_at', { ascending: false });

      if (error) {
        console.error('Error fetching game results:', error);
        return [];
      }

      return (data || []).map(result => ({
        id: result.id,
        gameId: result.game_id,
        userId: result.user_id,
        score: result.score,
        timeSpent: result.time_spent,
        moves: result.moves,
        hintsUsed: result.hints_used,
        completedAt: new Date(result.completed_at),
        gameState: result.game_state,
        feedback: result.feedback
      }));

    } catch (error) {
      console.error('Error in getUserGameResults:', error);
      return [];
    }
  }

  /**
   * Calculate game score based on performance
   */
  calculateGameScore(
    gameType: EducationalGame['type'],
    correctAnswers: number,
    totalAnswers: number,
    timeSpent: number,
    hintsUsed: number,
    moves: number
  ): number {
    let baseScore = (correctAnswers / totalAnswers) * 100;

    // Apply time bonus/penalty
    const expectedTime = totalAnswers * 30; // 30 seconds per answer
    const timeFactor = Math.max(0.5, Math.min(1.5, expectedTime / timeSpent));

    // Apply hints penalty
    const hintsPenalty = hintsUsed * 5; // 5% penalty per hint

    // Apply moves penalty for games that track efficiency
    let movesPenalty = 0;
    if (['concept-map', 'sequence-order'].includes(gameType)) {
      const expectedMoves = totalAnswers;
      movesPenalty = Math.max(0, (moves - expectedMoves) * 2);
    }

    const finalScore = Math.max(0, Math.min(100,
      baseScore * timeFactor - hintsPenalty - movesPenalty
    ));

    return Math.round(finalScore);
  }

  /**
   * Generate game feedback
   */
  generateGameFeedback(
    gameType: EducationalGame['type'],
    score: number,
    timeSpent: number,
    hintsUsed: number
  ): string {
    let feedback = '';

    if (score >= 90) {
      feedback = 'Excellent work! You have mastered this content.';
    } else if (score >= 80) {
      feedback = 'Great job! You have a solid understanding.';
    } else if (score >= 70) {
      feedback = 'Good effort! Review the areas you missed.';
    } else if (score >= 60) {
      feedback = 'Keep practicing! You\'re making progress.';
    } else {
      feedback = 'This content is challenging. Consider reviewing the material again.';
    }

    if (hintsUsed > 0) {
      feedback += ` You used ${hintsUsed} hint${hintsUsed > 1 ? 's' : ''}.`;
    }

    const minutes = Math.floor(timeSpent / 60);
    const seconds = timeSpent % 60;
    feedback += ` Completed in ${minutes}:${seconds.toString().padStart(2, '0')}.`;

    return feedback;
  }
}

export const gameResultsService = new GameResultsService();
