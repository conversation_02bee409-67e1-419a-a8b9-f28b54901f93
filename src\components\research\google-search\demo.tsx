/**
 * Google Search Demo Component
 * Demonstrates Google Search functionality with test features
 */

import React, { useState } from 'react';
import { Search, TestTube, CheckCircle, XCircle, Loader2, Play, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

import { GoogleSearchInterface } from './components/GoogleSearchInterface';
import { 
  runGoogleSearchTests,
  testGoogleSearchAPI,
  testGoogleSearch,
  testAcademicFormatting,
  testSearchHistory
} from './test-google-search';

export function GoogleSearchDemo() {
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [activeTest, setActiveTest] = useState<string | null>(null);

  const runIndividualTest = async (testName: string, testFunction: () => Promise<any>) => {
    setActiveTest(testName);
    try {
      const result = await testFunction();
      setTestResults(prev => ({
        ...prev,
        [testName]: result
      }));
      
      if (result.success) {
        toast.success(`${testName} test passed!`);
      } else {
        toast.error(`${testName} test failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`${testName} test error: ${error.message}`);
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, error: error.message }
      }));
    } finally {
      setActiveTest(null);
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults(null);
    
    try {
      const results = await runGoogleSearchTests();
      setTestResults(results.results);
      
      if (results.success) {
        toast.success('All tests passed!');
      } else {
        toast.error('Some tests failed. Check the results below.');
      }
    } catch (error) {
      toast.error(`Test suite error: ${error.message}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const getTestStatus = (testName: string) => {
    if (activeTest === testName) return 'running';
    if (!testResults || !testResults[testName]) return 'pending';
    return testResults[testName].success ? 'passed' : 'failed';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />;
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <TestTube className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'passed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const testCases = [
    {
      name: 'apiTest',
      label: 'API Configuration',
      description: 'Test Google Search API connectivity and configuration',
      function: testGoogleSearchAPI
    },
    {
      name: 'searchTest',
      label: 'Search Functionality',
      description: 'Test actual Google Search with Gemini API',
      function: testGoogleSearch
    },
    {
      name: 'formattingTest',
      label: 'Academic Formatting',
      description: 'Test citation and reference generation',
      function: testAcademicFormatting
    },
    {
      name: 'historyTest',
      label: 'Search History',
      description: 'Test database operations for sessions and messages',
      function: testSearchHistory
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-3 rounded-xl">
              <Search className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Google Search Demo
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Test and explore the Google Search integration with academic research capabilities,
            citation management, and comprehensive search functionality.
          </p>
        </div>

        <Tabs defaultValue="demo" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="demo" className="flex items-center space-x-2">
              <Search className="h-4 w-4" />
              <span>Live Demo</span>
            </TabsTrigger>
            <TabsTrigger value="tests" className="flex items-center space-x-2">
              <TestTube className="h-4 w-4" />
              <span>API Tests</span>
            </TabsTrigger>
          </TabsList>

          {/* Live Demo Tab */}
          <TabsContent value="demo">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Search className="h-5 w-5 text-blue-600" />
                  <span>Google Search Interface</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="h-[600px]">
                  <GoogleSearchInterface userId="demo-user" />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tests Tab */}
          <TabsContent value="tests">
            <div className="space-y-6">
              {/* Test Controls */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <TestTube className="h-5 w-5 text-purple-600" />
                      <span>API Test Suite</span>
                    </div>
                    <Button
                      onClick={runAllTests}
                      disabled={isRunningTests}
                      className="bg-gradient-to-r from-blue-600 to-purple-600"
                    >
                      {isRunningTests ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Play className="h-4 w-4 mr-2" />
                      )}
                      Run All Tests
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {testCases.map((test) => {
                      const status = getTestStatus(test.name);
                      return (
                        <div
                          key={test.name}
                          className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                        >
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(status)}
                            <div>
                              <h3 className="font-medium text-gray-900">{test.label}</h3>
                              <p className="text-sm text-gray-600">{test.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge variant="secondary" className={getStatusColor(status)}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </Badge>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => runIndividualTest(test.name, test.function)}
                              disabled={activeTest === test.name}
                            >
                              {activeTest === test.name ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Test Results */}
              {testResults && (
                <Card>
                  <CardHeader>
                    <CardTitle>Test Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(testResults).map(([testName, result]: [string, any]) => (
                        <Alert
                          key={testName}
                          variant={result.success ? "default" : "destructive"}
                        >
                          <div className="flex items-center space-x-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600" />
                            )}
                            <span className="font-medium">
                              {testCases.find(t => t.name === testName)?.label || testName}
                            </span>
                          </div>
                          <AlertDescription className="mt-2">
                            {result.success ? (
                              <span className="text-green-700">Test passed successfully!</span>
                            ) : (
                              <span className="text-red-700">
                                Test failed: {result.error}
                              </span>
                            )}
                            {result.success && result.result && (
                              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                                {JSON.stringify(result.result, null, 2)}
                              </pre>
                            )}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default GoogleSearchDemo;
