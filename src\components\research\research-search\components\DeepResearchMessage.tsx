/**
 * Deep Research Message Component
 * Handles display of deep research specific messages
 */

import React, { useState } from 'react';
import {
  BookO<PERSON>,
  CheckCircle,
  Clock,
  FileText,
  Target,
  TrendingUp,
  Download,
  Copy,
  ExternalLink,
  Edit,
  Check
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

import { SearchMessage } from '../types';
import { OutlineApproval } from './OutlineApproval';
import { DeepResearchProgress } from './DeepResearchProgress';
import { TextWithCitations } from './InlineCitation';
import { editorService } from '../../paper-generator/editor.service';
import { academicFormattingService } from '../services/academic-formatting.service';
import { documentExportService } from '../../paper-generator/document-export.service';
import { toast } from 'sonner';

interface DeepResearchMessageProps {
  message: SearchMessage;
  onApproveOutline?: (outline: any) => void;
  onEditOutline?: (editRequest: any) => void;
  onRegenerateOutline?: () => void;
  onViewPoint?: (pointId: string) => void;
  onViewReference?: (reference: any) => void;
  onCopyContent?: (content: string) => void;
  onDownloadReport?: (session: any) => void;
  className?: string;
}

export function DeepResearchMessage({
  message,
  onApproveOutline,
  onEditOutline,
  onRegenerateOutline,
  onViewPoint,
  onViewReference,
  onCopyContent,
  onDownloadReport,
  className
}: DeepResearchMessageProps) {
  const [copied, setCopied] = useState(false);

  // Add academic styles to the document head
  React.useEffect(() => {
    const styleId = 'academic-formatting-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = academicFormattingService.getAcademicStyles();
      document.head.appendChild(style);
    }
  }, []);

  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      toast.success('Research report copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error('Failed to copy research report');
    }
  };

  const handleSendToEditor = async () => {
    try {
      const title = message.deepResearchSession?.title || 'Deep Research Report';

      // Get the complete content including consolidated references
      let fullContent = message.content;

      // Add consolidated references if available
      if (message.deepResearchSession?.consolidatedReferences) {
        fullContent += '\n\n## References\n\n' + message.deepResearchSession.consolidatedReferences;
      }

      // Format content for editor (clean text without HTML but keep markdown structure)
      let formattedContent = fullContent;

      // Clean up any HTML tags but preserve markdown structure
      formattedContent = formattedContent
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace HTML entities
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Clean up excessive line breaks
        .trim();

      console.log('Sending to editor:', {
        title,
        contentLength: formattedContent.length,
        preview: formattedContent.substring(0, 200) + '...'
      });

      const success = await editorService.sendToMainEditor({
        title,
        content: formattedContent
      });

      if (success) {
        toast.success('Research report sent to editor successfully! Check the main editor tab.');

        // Try to open editor in new tab/window as fallback
        try {
          const editorUrl = '/editor'; // Adjust this path as needed
          window.open(editorUrl, '_blank');
        } catch (navError) {
          console.warn('Could not open editor tab:', navError);
        }
      } else {
        toast.error('Failed to send report to editor');
      }
    } catch (error) {
      console.error('Error sending to editor:', error);
      toast.error('Failed to send report to editor');
    }
  };

  const handleDownloadWord = async () => {
    try {
      const title = message.deepResearchSession?.title || 'Deep_Research_Report';
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.docx`;

      // Get the complete content including consolidated references
      let fullContent = message.content;
      if (message.deepResearchSession?.consolidatedReferences) {
        fullContent += '\n\n' + message.deepResearchSession.consolidatedReferences;
      }

      // Use enhanced formatting for Word export (clean text without markdown/HTML)
      const contentForExport = academicFormattingService.formatForWordExport(fullContent);

      console.log('Exporting to Word:', { title, contentLength: contentForExport.length });

      await documentExportService.exportMarkdownToDocx(
        message.deepResearchSession?.title || 'Deep Research Report',
        contentForExport,
        fileName
      );

      toast.success('Research report downloaded successfully!');
    } catch (error) {
      console.error('Error downloading Word document:', error);
      toast.error('Failed to download research report');
    }
  };

  const handleDownload = () => {
    if (onDownloadReport && message.deepResearchSession) {
      onDownloadReport(message.deepResearchSession);
    }
  };

  // Render based on message type
  switch (message.type) {
    case 'deep_research_outline':
      return (
        <div className={cn("space-y-4", className)}>
          <Card className="border-blue-200 bg-blue-50/30">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-blue-600" />
                <CardTitle className="text-lg text-blue-900">Research Outline Generated</CardTitle>
              </div>
              <p className="text-sm text-blue-700">
                Review the proposed research structure and approve to begin deep research.
              </p>
            </CardHeader>
          </Card>

          {message.deepResearchOutline && (
            <OutlineApproval
              outline={message.deepResearchOutline}
              onApprove={onApproveOutline || (() => {})}
              onEdit={onEditOutline || (() => {})}
              onRegenerate={onRegenerateOutline || (() => {})}
            />
          )}
        </div>
      );

    case 'deep_research_progress':
      return (
        <div className={cn("space-y-4", className)}>
          <Card className="border-orange-200 bg-orange-50/30">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-orange-600" />
                  <CardTitle className="text-lg text-orange-900">Enhanced Academic Research in Progress</CardTitle>
                </div>
                {message.deepResearchProgress && (
                  <div className="text-sm font-medium text-orange-700">
                    {message.deepResearchProgress.progress}% Complete
                  </div>
                )}
              </div>
              {message.deepResearchProgress ? (
                <div className="mt-3">
                  <div className="text-sm text-orange-700 mb-2">
                    <strong>{message.deepResearchProgress.pointTitle}</strong>
                  </div>
                  <div className="text-xs text-orange-600 mb-2">
                    {message.deepResearchProgress.message}
                  </div>
                  <div className="w-full bg-orange-200 rounded-full h-2">
                    <div
                      className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${message.deepResearchProgress.progress}%` }}
                    />
                  </div>
                </div>
              ) : (
                <p className="text-sm text-orange-700 mt-2">
                  Conducting comprehensive research step by step.
                </p>
              )}
            </CardHeader>
          </Card>

          {message.deepResearchSession && (
            <DeepResearchProgress
              session={message.deepResearchSession}
              currentProgress={message.deepResearchProgress}
              onViewPoint={onViewPoint}
              onViewReference={onViewReference}
            />
          )}
        </div>
      );

    case 'deep_research_section':
      return (
        <div className={cn("space-y-4", className)}>
          <Card className="border-green-200 bg-green-50/30">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <CardTitle className="text-lg text-green-900">Section Completed</CardTitle>
                </div>
                <Button variant="outline" size="sm" onClick={handleCopy}>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy
                </Button>
              </div>
              {message.completedSection && (
                <div className="flex items-center gap-4 text-sm text-green-700">
                  <span>{message.completedSection.title}</span>
                  <Badge variant="outline" className="bg-white">
                    {message.completedSection.wordCount} words
                  </Badge>
                  <Badge variant="outline" className="bg-white">
                    {message.completedSection.sources.length} sources
                  </Badge>
                </div>
              )}
            </CardHeader>
          </Card>

          <Card>
            <CardContent className="p-6">
              <ScrollArea className="h-96">
                <div 
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: message.content }}
                />
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Section Sources */}
          {message.completedSection && message.completedSection.sources.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Sources ({message.completedSection.sources.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {message.completedSection.sources.slice(0, 5).map((source) => (
                    <div key={source.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex-1">
                        <div className="text-sm font-medium">{source.title}</div>
                        <div className="text-xs text-gray-600">{source.domain}</div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(source.url, '_blank')}
                      >
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                  {message.completedSection.sources.length > 5 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{message.completedSection.sources.length - 5} more sources
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      );

    case 'deep_research_final':
      return (
        <div className={cn("space-y-4", className)}>
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-purple-600" />
                  <CardTitle className="text-lg text-purple-900">Deep Research Complete</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    className="text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  >
                    {copied ? <Check className="w-4 h-4 mr-2" /> : <Copy className="w-4 h-4 mr-2" />}
                    {copied ? 'Copied' : 'Copy Report'}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSendToEditor}
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Send to Editor
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDownloadWord}
                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Word
                  </Button>
                </div>
              </div>
              {message.deepResearchSession && (
                <div className="flex items-center gap-4 text-sm text-purple-700">
                  <span>Comprehensive research report completed</span>
                  <Badge variant="outline" className="bg-white">
                    {message.deepResearchSession.completedPoints.length} points
                  </Badge>
                  <Badge variant="outline" className="bg-white">
                    {message.deepResearchSession.allReferences.length} references
                  </Badge>
                  <Badge variant="outline" className="bg-white">
                    10 AI assistants
                  </Badge>
                </div>
              )}
            </CardHeader>
          </Card>

          {/* Final Report Content */}
          <Card className="border border-gray-200 shadow-sm">
            <CardContent className="p-8">
              <div className="max-w-none">
                {message.deepResearchSession?.formattedReport ? (
                  <div
                    className="academic-content prose prose-lg max-w-none text-gray-800"
                    dangerouslySetInnerHTML={{
                      __html: message.deepResearchSession.formattedReport
                    }}
                  />
                ) : (
                  <TextWithCitations
                    content={message.content}
                    citations={message.citations}
                    className="prose prose-lg max-w-none text-gray-800"
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Consolidated References Section */}
          {message.deepResearchSession?.consolidatedReferences && (
            <Card className="border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-blue-600" />
                  References
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="prose prose-sm max-w-none text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: academicFormattingService.formatForAcademicOutput(message.deepResearchSession.consolidatedReferences)
                  }}
                />
              </CardContent>
            </Card>
          )}

          {/* Enhanced Research Summary */}
          {message.deepResearchSession && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  Research Summary & Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Primary Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {message.deepResearchSession.researchMetadata?.sectionsCompleted || message.deepResearchSession.completedPoints.length}
                      </div>
                      <div className="text-sm text-gray-600">Sections Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {message.deepResearchSession.researchMetadata?.totalCitations || message.deepResearchSession.allReferences.length}
                      </div>
                      <div className="text-sm text-gray-600">Total Citations</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {message.deepResearchSession.researchMetadata?.academicSources || message.deepResearchSession.allReferences.filter(ref => ref.isAcademic).length}
                      </div>
                      <div className="text-sm text-gray-600">Academic Sources</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {(message.deepResearchSession.researchMetadata?.totalWordCount || message.deepResearchSession.completedPoints.reduce((total, point) => total + point.wordCount, 0)).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Total Words</div>
                    </div>
                  </div>

                  {/* Quality Metrics */}
                  {message.deepResearchSession.researchMetadata && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
                      <div className="text-center">
                        <div className="text-lg font-bold text-emerald-600">
                          {message.deepResearchSession.researchMetadata.governmentSources}
                        </div>
                        <div className="text-sm text-gray-600">Government Sources</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-indigo-600">
                          {(message.deepResearchSession.researchMetadata.averageSourceQuality * 100).toFixed(0)}%
                        </div>
                        <div className="text-sm text-gray-600">Avg. Source Quality</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-teal-600">
                          {message.deepResearchSession.researchMetadata.researchDuration}m
                        </div>
                        <div className="text-sm text-gray-600">Research Duration</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-rose-600">
                          {message.deepResearchSession.researchMetadata.confidenceScore.toFixed(1)}
                        </div>
                        <div className="text-sm text-gray-600">Confidence Score</div>
                      </div>
                    </div>
                  )}

                  {/* Research Type Info */}
                  {message.deepResearchSession.researchType && (
                    <div className="pt-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">Research Type</h4>
                          <p className="text-sm text-gray-600">{message.deepResearchSession.researchType.name}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Target: {message.deepResearchSession.researchType.totalWordTarget.toLocaleString()} words</div>
                          <div className="text-sm text-gray-600">Level: {message.deepResearchSession.researchType.academicLevel}</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      );

    default:
      return (
        <Card className={className}>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">
              {message.content}
            </div>
          </CardContent>
        </Card>
      );
  }
}
