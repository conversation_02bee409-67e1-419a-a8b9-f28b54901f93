import { AIGenerationOptions, AIAnalysisResult, BookContext, ChapterContext, GeneratedOutline, BookMetadata, UserChapter } from '../types';
import { CONTEXT_SETTINGS, getOptimalModel } from '../constants';
import { OUTLINE_GENERATION_PROMPTS } from '../prompts';

/**
 * Service for handling AI book generation with context management
 * Extends the paper AI service with book-specific capabilities
 */
export class BookAIService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || '';
    
    const isValidKey = this.apiKey && 
                       !this.apiKey.includes('your_') && 
                       this.apiKey.length > 20;
                       
    if (!isValidKey) {
      console.warn('No valid OpenRouter API key found in environment variables - book generation features will not work');
    }
  }
  
  /**
   * Helper method to extract and parse JSON from AI responses
   * Handles malformed JSON with aggressive repair strategies
   */
  private extractAndParseJSON(content: string, expectedFormat?: 'chapters' | 'summary', contextData?: any): any {
    console.log("Attempting to parse AI response:", content.substring(0, 100) + "...");

    // Extract JSON content from various formats
    let jsonContent = this.extractJSONContent(content);

    // Pre-process for the most common error pattern: mixed quotes in property names
    jsonContent = this.fixMixedQuotePatterns(jsonContent);

    // Apply aggressive malformed JSON repair
    jsonContent = this.repairMalformedJSON(jsonContent);

    console.log("Repaired JSON (first 200 chars):", jsonContent.substring(0, 200));

    // Try parsing the repaired JSON
    try {
      return JSON.parse(jsonContent);
    } catch (error) {
      console.log("Primary JSON parse failed, attempting advanced recovery strategies...");

      // Advanced recovery strategies with context awareness
      return this.attemptJSONRecovery(jsonContent, error as Error, expectedFormat, contextData);
    }
  }

  /**
   * Fix the most common mixed quote patterns that break JSON parsing
   */
  private fixMixedQuotePatterns(content: string): string {
    console.log("Applying mixed quote pattern fixes...");

    let fixed = content;

    // Fix the specific pattern: "property': value
    fixed = fixed.replace(/"([^"']*)':\s*/g, '"$1": ');

    // Fix the reverse pattern: 'property": value
    fixed = fixed.replace(/'([^"']*)":\s*/g, '"$1": ');

    // Fix both quotes wrong: 'property': value
    fixed = fixed.replace(/'([^"']*)':\s*/g, '"$1": ');

    // Fix property names that lost their opening quote: summary': value
    fixed = fixed.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)':\s*/g, '$1"$2": ');

    console.log("Mixed quote pattern fixes applied");
    return fixed;
  }

  /**
   * Extract JSON content from various AI response formats
   */
  private extractJSONContent(content: string): string {
    let jsonContent = content.trim();

    // First try to parse directly
    try {
      JSON.parse(jsonContent);
      return jsonContent; // If it parses, return as-is
    } catch (e) {
      // Continue with extraction
    }

    // Match content inside code blocks with or without json specifier
    const codeBlockRegexes = [
      /```(?:json)?\s*([\s\S]*?)\s*```/,     // Standard markdown code blocks
      /`{3,}(?:json)?\s*([\s\S]*?)\s*`{3,}/, // Handle cases with more than 3 backticks
      /\{[\s\S]*\}/                          // Just try to find a JSON object directly
    ];

    for (const regex of codeBlockRegexes) {
      const match = content.match(regex);
      if (match && match[1]) {
        jsonContent = match[1].trim();
        console.log("Extracted content from code block");
        break;
      }
    }

    // If we couldn't extract from a code block, look for first { and last }
    if (jsonContent === content.trim()) {
      console.log("No code block match - looking for JSON directly");
      const firstBrace = content.indexOf('{');
      const lastBrace = content.lastIndexOf('}');
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = content.substring(firstBrace, lastBrace + 1);
        console.log("Extracted content using brace indices");
      }
    }

    return jsonContent;
  }

  /**
   * Aggressively repair malformed JSON from AI responses
   */
  private repairMalformedJSON(jsonContent: string): string {
    console.log("Applying aggressive JSON repair...");

    // Step 1: Remove all control characters that break JSON parsing
    let repaired = jsonContent.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

    // Step 2: Handle LaTeX and mathematical expressions that break JSON
    // Replace problematic LaTeX expressions with plain text descriptions
    repaired = repaired.replace(/\$\\Delta\\phi[^$]*\$/g, '"phase difference equation"');
    repaired = repaired.replace(/\$\\frac\{[^}]*\}\{[^}]*\}[^$]*\$/g, '"mathematical formula"');
    repaired = repaired.replace(/\$[^$]*\$/g, '"equation"');

    // Handle backslash sequences that aren't valid JSON escapes
    repaired = repaired.replace(/\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/g, '\\\\');

    // Step 3: Fix the most common malformed pattern: escaped quotes in property names
    // "chapters\": → "chapters":
    repaired = repaired.replace(/\\":/g, '":');

    // Step 4: Fix escaped quotes in property values
    // : "value\" → : "value"
    repaired = repaired.replace(/:\s*"([^"]*)\\"(\s*[,}\]])/g, ': "$1"$2');

    // Step 5: Fix escaped quotes at the start of property names
    // \"property": → "property":
    repaired = repaired.replace(/\\"([^"]*)":/g, '"$1":');

    // Step 6: Fix escaped quotes in array elements
    // ["item\", → ["item",
    repaired = repaired.replace(/\\"(\s*[,\]])/g, '"$1');

    // Step 7: Fix mixed quote patterns in property names (critical fix)
    // "summary': → "summary":
    repaired = repaired.replace(/"([^"']*)':\s*/g, '"$1": ');
    // 'summary": → "summary":
    repaired = repaired.replace(/'([^"']*)":\s*/g, '"$1": ');
    // 'summary': → "summary":
    repaired = repaired.replace(/'([^"']*)':\s*/g, '"$1": ');

    // Step 8: Handle unescaped quotes within strings by replacing with single quotes
    // This is a more aggressive approach for content with embedded quotes
    repaired = repaired.replace(/"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}\]])/g, '"$1\'$2\'$3"');

    // Step 9: Remove any remaining unnecessary escaping
    repaired = repaired.replace(/\\"/g, '"');

    // Step 10: Fix double escapes
    repaired = repaired.replace(/\\\\/g, '\\');

    // Step 11: Replace smart quotes and normalize
    repaired = repaired
      .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes
      .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
      .replace(/\n+/g, '\n')           // Normalize newlines
      .trim();

    console.log("JSON repair completed");
    return repaired;
  }

  /**
   * Attempt various JSON recovery strategies when parsing fails
   */
  private attemptJSONRecovery(jsonContent: string, originalError: Error, expectedFormat?: 'chapters' | 'summary', contextData?: any): any {
    console.log("Attempting JSON recovery strategies...");

    // Strategy 1: Enhanced LaTeX and special character handling
    try {
      let fixedJson = jsonContent;

      // Handle LaTeX expressions that commonly break JSON
      fixedJson = fixedJson.replace(/\$\\Delta\\phi[^$]*\$/g, '"phase difference equation"');
      fixedJson = fixedJson.replace(/\$\\frac\{[^}]*\}\{[^}]*\}[^$]*\$/g, '"mathematical formula"');
      fixedJson = fixedJson.replace(/\$[^$]*\$/g, '"equation"');

      // Handle backslash sequences that aren't valid JSON escapes
      fixedJson = fixedJson.replace(/\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/g, '\\\\');

      // CRITICAL: Fix mixed quote patterns in property names (the main issue)
      // "summary': → "summary":
      fixedJson = fixedJson.replace(/"([^"']*)':\s*/g, '"$1": ');
      // 'summary": → "summary":
      fixedJson = fixedJson.replace(/'([^"']*)":\s*/g, '"$1": ');
      // 'summary': → "summary":
      fixedJson = fixedJson.replace(/'([^"']*)':\s*/g, '"$1": ');

      // Fix trailing commas in arrays/objects
      fixedJson = fixedJson.replace(/,(\s*[\]}])/g, '$1');

      // Fix missing quotes around property names
      fixedJson = fixedJson.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');

      // Fix single quotes used instead of double quotes for values
      fixedJson = fixedJson.replace(/'([^']*)'/g, '"$1"');

      console.log("Attempting parse with enhanced formatting fixes");
      return JSON.parse(fixedJson);
    } catch (basicError) {
      console.log("Enhanced formatting fixes failed, trying bracket completion...");
    }

    // Strategy 2: Fix incomplete JSON by adding missing closing brackets
    try {
      let repairedJson = jsonContent;
      const openBraces = (repairedJson.match(/\{/g) || []).length;
      const closeBraces = (repairedJson.match(/\}/g) || []).length;
      const openBrackets = (repairedJson.match(/\[/g) || []).length;
      const closeBrackets = (repairedJson.match(/\]/g) || []).length;

      // Add missing closing braces
      for (let i = 0; i < openBraces - closeBraces; i++) {
        repairedJson += '}';
      }

      // Add missing closing brackets
      for (let i = 0; i < openBrackets - closeBrackets; i++) {
        repairedJson += ']';
      }

      console.log("Attempting parse with bracket completion");
      return JSON.parse(repairedJson);
    } catch (bracketError) {
      console.log("Bracket completion failed, trying regex extraction...");
    }

    // Strategy 3: Extract summary and keyPoints using regex for chapter summaries
    try {
      console.log("Attempting regex extraction for summary and keyPoints");

      // Try multiple patterns for summary field (including the problematic mixed quote pattern)
      let summaryMatch = jsonContent.match(/"summary"\s*:\s*"([^"]*(?:\\.[^"]*)*)"/);
      if (!summaryMatch) {
        // Try with single quote after property name: "summary': 'content'
        summaryMatch = jsonContent.match(/"summary'\s*:\s*'([^']*(?:\\.[^']*)*)'/);
      }
      if (!summaryMatch) {
        // Try with mixed quotes: "summary': "content"
        summaryMatch = jsonContent.match(/"summary'\s*:\s*"([^"]*(?:\\.[^"]*)*)"/);
      }
      if (!summaryMatch) {
        // Try without quotes around content
        summaryMatch = jsonContent.match(/"summary['"]?\s*:\s*([^,}]+)/);
      }

      // Try multiple patterns for keyPoints
      let keyPointsMatch = jsonContent.match(/"keyPoints"\s*:\s*(\[[^\]]*\])/);
      if (!keyPointsMatch) {
        keyPointsMatch = jsonContent.match(/"keyPoints'\s*:\s*(\[[^\]]*\])/);
      }

      if (summaryMatch && summaryMatch[1]) {
        let summary = summaryMatch[1].replace(/\\"/g, '"').replace(/\\\\/g, '\\').trim();
        // Remove any trailing quotes or commas
        summary = summary.replace(/['"]*$/, '').replace(/,$/, '');

        let keyPoints: string[] = [];

        if (keyPointsMatch && keyPointsMatch[1]) {
          try {
            keyPoints = JSON.parse(keyPointsMatch[1]);
          } catch {
            // If keyPoints parsing fails, create default ones
            keyPoints = ['Key chapter content', 'Important developments'];
          }
        } else {
          keyPoints = ['Key chapter content', 'Important developments'];
        }

        console.log("Successfully extracted summary and keyPoints via regex");
        return { summary, keyPoints };
      }
    } catch (regexError) {
      console.log("Summary/keyPoints regex extraction failed");
    }

    // Strategy 4: Extract chapters array using regex
    try {
      console.log("Attempting regex extraction of chapters array");
      const chaptersMatch = jsonContent.match(/"chapters"\s*:\s*(\[[\s\S]*?\])/);
      if (chaptersMatch && chaptersMatch[1]) {
        const chaptersJson = `{"chapters":${chaptersMatch[1]}}`;
        return JSON.parse(chaptersJson);
      }

      // Try to extract individual chapter objects and reconstruct
      const chapterMatches = jsonContent.match(/\{[^{}]*"title"[^{}]*\}/g);
      if (chapterMatches && chapterMatches.length > 0) {
        const chapters = chapterMatches.map(match => {
          try {
            return JSON.parse(match);
          } catch {
            return null;
          }
        }).filter(Boolean);

        if (chapters.length > 0) {
          console.log(`Reconstructed ${chapters.length} chapters from individual matches`);
          return { chapters };
        }
      }
    } catch (regexError) {
      console.log("Regex extraction failed");
    }

    // Final strategy: Text-based extraction as last resort
    try {
      console.log("Attempting text-based extraction as final fallback");
      if (expectedFormat === 'chapters') {
        return this.extractChaptersFromText(jsonContent, contextData?.userChapters);
      } else {
        return this.extractSummaryFromText(jsonContent);
      }
    } catch (textError) {
      console.log("Text-based extraction also failed");
    }

    // All strategies failed
    console.error('All JSON recovery strategies failed');
    console.error('Original error:', originalError.message);
    console.error('Content that failed to parse (first 500 chars):', jsonContent.substring(0, 500));
    throw new Error(`Failed to parse JSON: ${originalError.message || 'Invalid format'}`);
  }

  /**
   * Extract chapters array from text when all JSON parsing fails
   * Creates minimal placeholder outlines to signal a need for regeneration
   */
  private extractChaptersFromText(content: string, userChapters?: any[]): any {
    console.log("Creating informative placeholder outlines after AI generation failure");

    // Try to determine how many chapters we need
    const numChapters = userChapters?.length || 1;

    // Create placeholder chapters to signal regeneration is needed
    const chapters = [];
    
    // Try to extract potential topics from the AI response
    const contentSample = content.substring(0, 1500); // Look at more content
    
    // Extract chapter titles from the AI response if possible
    const chapterTitleMatches = contentSample.match(/["']title["']\s*:\s*["']([^"']+)["']/g) || [];
    const extractedTitles = chapterTitleMatches.map(match => {
      const titleMatch = match.match(/["']title["']\s*:\s*["']([^"']+)["']/);
      return titleMatch ? titleMatch[1] : null;
    }).filter(Boolean);
    
    // Extract section titles from the AI response if possible
    const sectionTitleMatches = contentSample.match(/section.*?title["']\s*:\s*["']([^"']+)["']/g) || [];
    const extractedSections = sectionTitleMatches.map(match => {
      const sectionMatch = match.match(/title["']\s*:\s*["']([^"']+)["']/);
      return sectionMatch ? sectionMatch[1] : null;
    }).filter(Boolean);
    
    // Extract potential keywords for each chapter
    const potentialKeywords = contentSample.match(/\b[A-Z][a-z]{3,}\b/g) || [];
    const uniqueKeywords = [...new Set(potentialKeywords)].slice(0, 8);
    
    // Extract any complete sentences that might be useful for descriptions
    const sentenceMatches = contentSample.match(/["'][A-Z][^"'.]{20,100}\.["']/g) || [];
    const potentialDescriptions = sentenceMatches.map(match => 
      match.replace(/^["']|["']$/g, '')
    ).filter(Boolean);

    for (let i = 0; i < numChapters; i++) {
      const chapterNum = i + 1;
      const userChapter = userChapters?.[i];
      
      // Use extracted information where available
      const chapterTitle = userChapter?.title || userChapter?.outline?.title || 
                          extractedTitles[i] || `Chapter ${chapterNum}`;
                          
      const chapterDescription = userChapter?.description || userChapter?.outline?.description ||
                               potentialDescriptions[i] || 
                               `This chapter requires more specific topic information for quality outline generation.`;
      
      // Create default sections based on any extracted information
      const defaultSections = [];
      
      // Try to use extracted section titles if available
      const sectionsStartIdx = i * 5; // Allocate 5 potential sections per chapter
      const availableSections = extractedSections.slice(sectionsStartIdx, sectionsStartIdx + 5);
      
      if (availableSections.length >= 3) {
        // We have enough extracted sections to create a minimal outline
        availableSections.forEach((sectionTitle, idx) => {
          defaultSections.push({
            title: sectionTitle,
            description: "This section needs to be regenerated with more topic-specific details.",
            level: 1,
            order: idx + 1,
            estimatedWordCount: 500,
            keyPoints: [
              "Regenerate for more specific content",
              uniqueKeywords[idx % uniqueKeywords.length] || "Add topic-specific keywords"
            ]
          });
        });
      } else {
        // Create informative placeholder sections
        defaultSections.push({
          title: "AI Outline Generation Needs Topic-Specific Information",
          description: "For better results, provide more specific chapter topic details, key concepts, and themes you want covered.",
          level: 1,
          order: 1,
          estimatedWordCount: 1000,
          keyPoints: [
            "Click 'Regenerate' to try again", 
            "Add detailed topic information to your chapter description", 
            "Include specific keywords related to your chapter content"
          ]
        });
        
        // Add suggestion sections with any extracted keywords
        if (uniqueKeywords.length > 0) {
          defaultSections.push({
            title: "Potential Topics Detected",
            description: "The AI detected these potential topics, consider including more specific information about them:",
            level: 1,
            order: 2,
            estimatedWordCount: 800,
            keyPoints: uniqueKeywords.slice(0, 5)
          });
        }
        
        // Add a third section with structure recommendations
        defaultSections.push({
          title: "Improving Your Outline Generation",
          description: "For best results, consider adding content in these areas before regenerating:",
          level: 1,
          order: 3,
          estimatedWordCount: 1200,
          keyPoints: [
            "Specify the main topics this chapter should cover",
            "Add key terminology specific to this subject",
            "Mention any theories, frameworks, or methodologies to include",
            "Note any case studies or examples you want discussed"
          ]
        });
      }

      chapters.push({
        title: chapterTitle,
        description: chapterDescription,
        sections: defaultSections,
        estimatedWordCount: userChapter?.outline?.estimatedWordCount || 3000,
        keyPoints: ["Click 'Regenerate' for a better topic-specific outline", "Add more specific chapter details"]
      });
    }

    console.log(`Created ${chapters.length} semi-intelligent fallback outlines with available context`);
    return { chapters };
  }

  // These methods have been removed to prevent generation of generic templates

  /**
   * Extract summary and keyPoints from text when all JSON parsing fails
   */
  private extractSummaryFromText(content: string): any {
    console.log("Performing text-based extraction");

    // Look for summary content after any variation of "summary"
    const summaryPatterns = [
      /summary['"]?\s*[:=]\s*['"]([^'"]+)['"]/i,
      /summary['"]?\s*[:=]\s*([^,}]+)/i,
      /'([^']*introduces[^']*)/i, // Look for introductory text
      /"([^"]*introduces[^"]*)"/i,
      /([^{]*Chapter \d+[^}]*)/i // Look for chapter descriptions
    ];

    let summary = '';
    for (const pattern of summaryPatterns) {
      const match = content.match(pattern);
      if (match && match[1] && match[1].trim().length > 10) {
        summary = match[1].trim().replace(/['"]*$/, '').replace(/,$/, '');
        break;
      }
    }

    // If no summary found, create a basic one
    if (!summary) {
      summary = 'Chapter content covers key concepts and developments in the subject matter.';
    }

    // Create basic key points
    const keyPoints = [
      'Key chapter content',
      'Important concepts covered',
      'Builds upon previous material'
    ];

    console.log("Text-based extraction successful");
    return { summary, keyPoints };
  }

  /**
   * Generate a book chapter with context awareness
   * Automatically uses sectional generation for very long chapters
   */
  async generateChapter(
    chapterPrompt: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      // Check if API key is properly configured
      if (!this.hasValidApiKey()) {
        throw new Error('No valid API key configured. Please set up your OpenRouter API key in the .env file.');
      }

      // Extract target word count and determine if we need sectional generation
      const targetWordCount = parseInt(chapterPrompt.match(/(\d+)\s+words/)?.[1] || "6000");

      // For very long chapters, use the sectional generation method
      if (targetWordCount > 15000) {
        console.log(`Chapter target is ${targetWordCount} words - using sectional generation approach`);
        return this.generateVeryLongChapter(chapterPrompt, targetWordCount, options);
      }

      const optimalModel = getOptimalModel("content-generation", undefined, targetWordCount);

      // Determine appropriate token limit based on target word count
      let appropriateTokens = CONTEXT_SETTINGS.CHAPTER_GENERATION_TOKENS;
      if (targetWordCount > 12000) {
        appropriateTokens = CONTEXT_SETTINGS.VERY_LONG_CHAPTER_TOKENS;
      } else if (targetWordCount > 8000) {
        appropriateTokens = CONTEXT_SETTINGS.CHAPTER_GENERATION_TOKENS * 1.5;
      }

      const {
        model = optimalModel,
        maxTokens = appropriateTokens,
        context
      } = options;
      
      // Build context-aware system prompt
      const systemPrompt = this.buildSystemPrompt(context);
      
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: chapterPrompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.7 // Slightly creative for book writing
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '[No content generated]';

      console.log(`Chapter generation completed. Content length: ${content.length} characters`);
      console.log(`Chapter content preview: ${content.substring(0, 200)}...`);

      return content;
    } catch (error: any) {
      console.error('AI Chapter Generation Error:', error);
      throw new Error('Failed to generate chapter. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate very long chapters by breaking them into sections
   * This method is used for chapters that exceed normal token limits
   */
  async generateVeryLongChapter(
    chapterPrompt: string,
    targetWordCount: number,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<string> {
    try {
      // If the target word count is reasonable for single generation, use normal method
      if (targetWordCount <= 12000) {
        return this.generateChapter(chapterPrompt, options);
      }

      console.log(`Generating very long chapter with ${targetWordCount} words using sectional approach`);

      // Break the chapter into logical sections
      const sectionsPerChapter = Math.ceil(targetWordCount / 8000); // ~8000 words per section
      const wordsPerSection = Math.floor(targetWordCount / sectionsPerChapter);

      // Generate outline for sections first
      const sectionOutlinePrompt = `${chapterPrompt}

IMPORTANT: This chapter needs to be ${targetWordCount} words long, so it will be generated in ${sectionsPerChapter} sections of approximately ${wordsPerSection} words each.

First, create a detailed outline breaking this chapter into ${sectionsPerChapter} logical sections. For each section, provide:
1. Section title
2. Key topics to cover (3-5 bullet points)
3. Approximate word count target
4. How it connects to previous/next sections

Provide the outline in this format:
## Section 1: [Title]
- Key topic 1
- Key topic 2
- Key topic 3
Target: ${wordsPerSection} words

[Continue for all sections...]`;

      // Generate section outline
      const outlineModel = getOptimalModel("outline-generation");
      const outlineResponse = await this.generateChapter(sectionOutlinePrompt, {
        ...options,
        model: outlineModel,
        maxTokens: 4096
      });

      // Parse sections from outline
      const sections = this.parseSectionOutline(outlineResponse);

      // Generate each section
      const generatedSections: string[] = [];
      let previousContent = "";

      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];

        const sectionPrompt = `${chapterPrompt}

CONTEXT: This is section ${i + 1} of ${sections.length} in a ${targetWordCount}-word chapter.

PREVIOUS CONTENT SUMMARY: ${previousContent ? this.summarizeContent(previousContent) : "This is the first section."}

SECTION TO GENERATE:
${section.title}

Key topics to cover:
${section.topics.map(topic => `- ${topic}`).join('\n')}

TARGET: Write approximately ${section.wordCount} words for this section.

IMPORTANT INSTRUCTIONS:
- Write ONLY this section, not the entire chapter
- Ensure smooth transition from previous content
- Maintain consistent tone and style
- Include detailed explanations, examples, and insights
- End with a natural transition to the next section (unless this is the final section)
- Do not include section headers or titles in your response
- Focus on depth and comprehensive coverage of the topics`;

        console.log(`Generating section ${i + 1}/${sections.length}: ${section.title}`);

        const sectionContent = await this.generateChapter(sectionPrompt, {
          ...options,
          maxTokens: Math.min(CONTEXT_SETTINGS.VERY_LONG_CHAPTER_TOKENS, section.wordCount * 6) // ~6 tokens per word estimate
        });

        generatedSections.push(sectionContent);

        // Update previous content summary for next section
        if (i < sections.length - 1) {
          previousContent += sectionContent;
          // Keep only recent content to avoid token limits
          if (previousContent.length > 8000) {
            previousContent = previousContent.slice(-4000);
          }
        }
      }

      // Combine all sections
      const fullChapter = generatedSections.join('\n\n');

      console.log(`Very long chapter generation completed. Total length: ${fullChapter.length} characters`);

      return fullChapter;

    } catch (error: any) {
      console.error('Very Long Chapter Generation Error:', error);
      throw new Error('Failed to generate very long chapter. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Parse section outline into structured data
   */
  private parseSectionOutline(outline: string): Array<{title: string, topics: string[], wordCount: number}> {
    const sections: Array<{title: string, topics: string[], wordCount: number}> = [];
    const lines = outline.split('\n');

    let currentSection: {title: string, topics: string[], wordCount: number} | null = null;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Section header
      if (trimmedLine.startsWith('## Section')) {
        if (currentSection) {
          sections.push(currentSection);
        }
        const title = trimmedLine.replace(/^## Section \d+:\s*/, '');
        currentSection = { title, topics: [], wordCount: 6000 }; // Default word count
      }
      // Topic bullet point
      else if (trimmedLine.startsWith('-') && currentSection) {
        currentSection.topics.push(trimmedLine.replace(/^-\s*/, ''));
      }
      // Target word count
      else if (trimmedLine.startsWith('Target:') && currentSection) {
        const match = trimmedLine.match(/(\d+)/);
        if (match) {
          currentSection.wordCount = parseInt(match[1]);
        }
      }
    }

    // Add the last section
    if (currentSection) {
      sections.push(currentSection);
    }

    return sections;
  }

  /**
   * Summarize content for context in next sections
   */
  private summarizeContent(content: string): string {
    // Take the last 1000 characters and first 500 characters for context
    if (content.length <= 1500) return content;

    const start = content.substring(0, 500);
    const end = content.substring(content.length - 1000);

    return `${start}...[content continues]...${end}`;
  }

  /**
   * Generate comprehensive outlines for all chapters
   */
  async generateAllChapterOutlines(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    try {
      // For large numbers of chapters (6+), use chunking strategy
      if (userChapters.length >= 6) {
        return this.generateChapterOutlinesInChunks(metadata, userChapters, options);
      }

      const {
        model = "anthropic/claude-3-5-sonnet-20241022", // Using Claude Sonnet 3.5 for reliable structured output
        maxTokens = 12288 // Significantly increased token limit for better outline generation
      } = options;

      // Use Claude Sonnet 3.5 for outline generation as it produces reliable structured output
      const selectedModel = getOptimalModel("outline-generation", metadata) || "anthropic/claude-3-5-sonnet-20241022";
      
      // Log the model being used for debugging
      console.log(`Using AI model for outline generation: ${selectedModel}`);

      // Enhance metadata and chapters with more details if they're minimal
      const enhancedMetadata = this.enhanceMetadataForOutlineGeneration(metadata);
      const enhancedChapters = this.enhanceChaptersForOutlineGeneration(userChapters);
      
      console.log("Generating outlines using enhanced metadata and chapter information");
      const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(enhancedMetadata, enhancedChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            {
              role: "system",
              content: `You are an AI specialized in generating detailed book outlines in strict JSON format. 

YOUR RESPONSIBILITY: Create highly specific, topic-relevant outlines for each chapter that demonstrate expert knowledge.

CRITICAL INSTRUCTIONS:
1. CREATE SPECIFIC CONTENT ONLY: Every title, description, and key point must be SPECIFIC to the chapter's topic
2. NO GENERIC OUTLINES: Never use generic templates, patterns, or standard outline structures
3. DEMONSTRATE EXPERTISE: Write as if you're a subject matter expert in this exact field
4. USE SPECIALIZED TERMINOLOGY: Include field-specific terms throughout all sections
5. CREATE DETAILED OUTLINES: Include 8-15 substantial sections per chapter
6. COMPLETE ALL CHAPTERS: Generate complete outlines for ALL chapters requested

JSON FORMAT REQUIREMENTS:
1. STRICT JSON: Your entire response must be valid, parseable JSON - nothing else
2. NO CODE BLOCKS: Do not use markdown code blocks or any formatting
3. NO COMMENTS: Do not include explanations or comments in the JSON
4. PROPER QUOTES: Use double quotes (") for ALL properties and string values
5. NO TRAILING COMMAS: Ensure no trailing commas in arrays or objects
6. VALID NESTING: All objects and arrays must be properly nested and closed
7. JSON STRUCTURE ONLY: Your response must contain ONLY the JSON object, nothing else

CONTENT REQUIREMENTS:
1. TOPIC SPECIFICITY: Create sections tailored to each chapter's specific topic
2. AVOID GENERICS: Do not use generic section titles like "Introduction" or "Conclusion"
3. DOMAIN LANGUAGE: Use subject-specific terminology throughout
4. DESCRIPTIVE CONTENT: Each section must include topic-specific details
5. SPECIFIC KEY POINTS: Key points must directly reference aspects of the topic

EXPECTED JSON STRUCTURE:
{
  "chapters": [
    {
      "title": "Chapter Title",
      "description": "Chapter description",
      "sections": [
        {
          "title": "Topic-Specific Section Title",
          "description": "Section description with topic-specific details",
          "level": 1,
          "order": 1,
          "estimatedWordCount": 500,
          "keyPoints": ["Topic-specific key point 1", "Topic-specific key point 2", "Topic-specific key point 3"]
        }
      ],
      "estimatedWordCount": 3000,
      "keyPoints": ["Chapter-level key point 1", "Chapter-level key point 2"]
    }
  ]
}`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.8,  // Balanced temperature for creativity while maintaining reliability
          top_p: 0.95,  // Slightly increase nucleus sampling for more diverse outputs
          frequency_penalty: 0.3, // Reduce repetition of similar phrases
          presence_penalty: 0.3,  // Encourage the model to cover more topics
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for outline generation (first 200 chars):",
          content.length > 200 ? content.substring(0, 200) + "..." : content);
        console.log("Full AI Response for outline generation:", content);

        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content, 'chapters', { userChapters });
        
        // Validate the parsed JSON structure
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result');
          throw new Error('Failed to parse AI response as JSON. The AI returned an invalid response format.');
        }
        
        if (!parsed.chapters || !Array.isArray(parsed.chapters)) {
          console.error('Invalid outline format, missing chapters array:', parsed);
          console.error('Parsed result type:', typeof parsed);
          console.error('Parsed result keys:', Object.keys(parsed || {}));
          throw new Error('Invalid outline format returned by AI. Missing chapters array.');
        }

        console.log(`Successfully parsed ${parsed.chapters.length} chapters from AI response`);
        console.log('Chapter titles:', parsed.chapters.map((ch: any) => ch.title || 'Untitled'));

        const outlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => {
          // Validate chapter structure and provide defaults
          if (!chapter.title || !chapter.sections || !Array.isArray(chapter.sections)) {
            console.error('Invalid chapter structure:', chapter);
            console.error('Chapter keys:', Object.keys(chapter || {}));
            console.error('Sections type:', typeof chapter.sections);
            throw new Error(`Invalid structure for chapter ${index + 1}. Missing title or sections.`);
          }

          console.log(`Processing chapter ${index + 1}: "${chapter.title}" with ${chapter.sections.length} sections`);
          console.log(`Section titles:`, chapter.sections.map((s: any) => s.title || 'Untitled'));

          return {
            id: `outline-${userChapters[index]?.id || index}`,
            chapterId: userChapters[index]?.id || `chapter-${index}`,
            title: chapter.title || `Chapter ${index + 1}`,
            description: chapter.description || '',
            sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
              id: `section-${section.order || sectionIndex + 1}`,
              title: section.title || `Section ${sectionIndex + 1}`,
              description: section.description || '',
              level: section.level || 1,
              order: section.order || sectionIndex + 1,
              estimatedWordCount: section.estimatedWordCount || 500,
              keyPoints: section.keyPoints || []
            })),
            estimatedWordCount: chapter.estimatedWordCount || 3000,
            keyPoints: chapter.keyPoints || [],
            status: 'ready' as const
          };
        });

        return outlines;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outlines. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outlines. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate chapter outlines in chunks to handle large numbers of chapters
   */
  private async generateChapterOutlinesInChunks(
    metadata: BookMetadata,
    userChapters: UserChapter[],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline[]> {
    const chunkSize = 4; // Process 4 chapters at a time
    const allOutlines: GeneratedOutline[] = [];

    console.log(`Generating outlines for ${userChapters.length} chapters in chunks of ${chunkSize}`);

    for (let i = 0; i < userChapters.length; i += chunkSize) {
      const chunk = userChapters.slice(i, i + chunkSize);
      const chunkNumber = Math.floor(i / chunkSize) + 1;
      const totalChunks = Math.ceil(userChapters.length / chunkSize);

      console.log(`Processing chunk ${chunkNumber}/${totalChunks} (chapters ${i + 1}-${Math.min(i + chunkSize, userChapters.length)})`);

      try {
        const {
          model = "anthropic/claude-3-5-sonnet-20241022", // Using Claude Sonnet 3.5 for better JSON structure reliability
          maxTokens = 8192 // Higher token limit for chunk processing
        } = options;

        // Use Claude Sonnet 3.5 for outline generation
        const selectedModel = getOptimalModel("outline-generation", metadata) || "anthropic/claude-3-5-sonnet-20241022";
        
        console.log(`Processing chunk ${chunkNumber} using model: ${selectedModel}`);
        
        // Enhance metadata and chapters with more details if they're minimal
        const enhancedMetadata = this.enhanceMetadataForOutlineGeneration(metadata);
        const enhancedChunk = this.enhanceChaptersForOutlineGeneration(chunk);

        const prompt = OUTLINE_GENERATION_PROMPTS.generateAllOutlines(enhancedMetadata, enhancedChunk);

        const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: selectedModel,
            messages: [
              {
                role: "system",
                content: `You are an expert book outline generator specializing in creating HIGHLY SPECIFIC outlines customized to exact book topics.

CRITICAL INSTRUCTION: YOU MUST CREATE UNIQUE SECTIONS SPECIFICALLY FOR THE EXACT TOPIC - NEVER USE GENERIC TEMPLATES OR GENERIC SECTION NAMES.

TOPIC SPECIFICITY REQUIREMENTS:
- ANALYZE DEEPLY: Study the chapter titles, keywords, and user descriptions very carefully
- CREATE CUSTOM SECTIONS: Every section name must directly relate to the specific topic (not generic like "Introduction" or "Conclusion")
- SPECIALIZED CONTENT: Section descriptions must include topic-specific terminology and concepts
- TAILOR KEY POINTS: All key points must directly reference aspects of the specific topic
- NEVER USE GENERIC TEMPLATES: Do not use any predefined outline structures or common academic paper structures
- DOMAIN EXPERTISE: Write the outline as if you are an expert in the specific field or subject matter

STRUCTURE REQUIREMENTS:
- Create 8-15 main sections per chapter with meaningful, TOPIC-SPECIFIC titles
- Each section must have detailed descriptions explaining the specific content related to the topic
- Include realistic word count estimates for each section (300-800 words per section)
- Provide 3-5 key points for each section that highlight concepts specific to this exact topic
- Ensure logical flow that builds knowledge of the specific topic
- Make sections substantial with content directly relevant to the topic

FORMATTING REQUIREMENTS:
- You must respond with raw valid JSON only
- Do not wrap your response in code blocks, markdown, or any other formatting
- Do not use escaped quotes (\") in property names or values
- Use only standard double quotes (") for all JSON strings
- Do not include any control characters or special formatting
- Ensure all property names and string values are properly quoted
- Do not escape quotes unless absolutely necessary for content within strings
- Keep all content simple and JSON-safe
- Check that your JSON is valid before responding
- NEVER use single quotes (') in property names - always use double quotes and colons like "title":

The entire response should be a single valid JSON object that can be directly parsed.`
              },
              {
                role: "user",
                content: prompt
              }
            ],
            max_tokens: maxTokens,
            temperature: 0.8,
            top_p: 0.95, // Slightly increase nucleus sampling for more diverse outputs
            frequency_penalty: 0.3, // Reduce repetition of similar phrases
            presence_penalty: 0.3  // Encourage the model to cover more topics
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        const content = data.choices?.[0]?.message?.content || '';

        console.log(`AI Response for chunk ${chunkNumber} (first 200 chars):`,
          content.length > 200 ? content.substring(0, 200) + "..." : content);

        // Parse the JSON response with enhanced error handling
        const parsed = this.extractAndParseJSON(content, 'chapters', { userChapters: chunk });

        if (!parsed || !parsed.chapters || !Array.isArray(parsed.chapters)) {
          console.error(`Invalid response structure for chunk ${chunkNumber}`);
          throw new Error(`Invalid outline structure in chunk ${chunkNumber}`);
        }

        const chunkOutlines: GeneratedOutline[] = parsed.chapters.map((chapter: any, index: number) => {
          const originalIndex = i + index;
          return {
            id: `outline-${chunk[index]?.id || originalIndex}`,
            chapterId: chunk[index]?.id || `chapter-${originalIndex}`,
            title: chapter.title || `Chapter ${originalIndex + 1}`,
            description: chapter.description || '',
            sections: (chapter.sections || []).map((section: any, sectionIndex: number) => ({
              id: `section-${section.order || sectionIndex + 1}`,
              title: section.title || `Section ${sectionIndex + 1}`,
              description: section.description || '',
              level: section.level || 1,
              order: section.order || sectionIndex + 1,
              estimatedWordCount: section.estimatedWordCount || 500,
              keyPoints: section.keyPoints || []
            })),
            estimatedWordCount: chapter.estimatedWordCount || 3000,
            keyPoints: chapter.keyPoints || [],
            status: 'ready' as const
          };
        });

        allOutlines.push(...chunkOutlines);
        console.log(`Successfully processed chunk ${chunkNumber}, total outlines: ${allOutlines.length}`);

        // Add a small delay between chunks to avoid rate limiting
        if (i + chunkSize < userChapters.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (chunkError: any) {
        console.error(`Error processing chunk ${chunkNumber}:`, chunkError);

        // Try again with a simpler approach for this chunk
        try {
          console.log(`Retrying chunk ${chunkNumber} with a different approach`);
          
          // Process each chapter in the chunk individually with a more targeted approach
          const retryOutlines = [];
          
          for (const userChapter of chunk) {
            try {
              // Create a more targeted prompt for this specific chapter with enhanced topic-specific instructions
              const singleChapterPrompt = `
I need a HIGHLY SPECIALIZED outline for the chapter: "${userChapter.outline.title}" 

TOPIC EXPERTISE REQUIRED: Act as if you are a subject matter expert in this specific topic.

CRITICAL REQUIREMENTS:
1. This outline must be CUSTOM-CREATED for this EXACT topic - use NO generic templates whatsoever
2. Every section title must contain topic-specific terminology and concepts
3. Every section description must include specialized content about this specific topic
4. Include topic-specific key points that would only appear in a book about this subject

Chapter Topic Details:
- Title: ${userChapter.outline.title}
- Description: ${userChapter.outline.description || 'No description provided'}
- User Content: ${userChapter.items.map(item => item.content).join(' | ')}
- Chapter Word Count: ${userChapter.outline.estimatedWordCount || '3000-5000'} words

Book Context:
- Book Title: ${metadata.title}
- Genre: ${metadata.genre}
- Target Audience: ${metadata.targetAudience}
- Keywords: ${metadata.keywords?.join(', ') || 'Not provided'}
- Book Description: ${metadata.description}

The outline must include 8-10 HIGHLY SPECIALIZED sections with titles that would only make sense for this specific topic. 
Each section must have 3-5 key points that demonstrate deep knowledge of this subject matter.

REMEMBER: AVOID ALL GENERIC SECTION TITLES - make each one uniquely relevant to this topic only.
`;

              // Enhance the chapter with more details
              const enhancedUserChapter = this.enhanceChaptersForOutlineGeneration([userChapter])[0];
              const enhancedMetadata = this.enhanceMetadataForOutlineGeneration(metadata);
              
              // Create a more specific prompt using the enhanced data
              const enhancedSingleChapterPrompt = `
I need a HIGHLY SPECIALIZED outline for the chapter: "${enhancedUserChapter.outline.title}" 

TOPIC EXPERTISE REQUIRED: Act as if you are a subject matter expert in this specific topic.

CRITICAL REQUIREMENTS:
1. This outline must be CUSTOM-CREATED for this EXACT topic - use NO generic templates whatsoever
2. Every section title must contain topic-specific terminology and concepts
3. Every section description must include specialized content about this specific topic
4. Include topic-specific key points that would only appear in a book about this subject

Chapter Topic Details:
- Title: ${enhancedUserChapter.outline.title}
- Description: ${enhancedUserChapter.outline.description || 'A chapter covering important aspects of the topic'}
- User Content: ${enhancedUserChapter.items.map(item => item.content).join(' | ')}
- Chapter Word Count: ${enhancedUserChapter.outline.estimatedWordCount || '3000-5000'} words

Book Context:
- Book Title: ${enhancedMetadata.title}
- Genre: ${enhancedMetadata.genre}
- Target Audience: ${enhancedMetadata.targetAudience}
- Keywords: ${enhancedMetadata.keywords?.join(', ') || 'Not provided'}
- Book Description: ${enhancedMetadata.description}

The outline must include 8-10 HIGHLY SPECIALIZED sections with titles that would only make sense for this specific topic. 
Each section must have 3-5 key points that demonstrate deep knowledge of this subject matter.
`;

              const retryResponse = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "Authorization": `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                  model: "google/gemini-2.0-flash-001", // Use the most capable model for retries
                  messages: [
                    {
                      role: "system",
                      content: `You are a specialized book outline expert focused exclusively on creating HIGHLY TOPIC-SPECIFIC outlines. Your expertise is in developing unique, custom outlines tailored exactly to specialized topics.

CRITICAL REQUIREMENTS:
1. AVOID ALL GENERIC PATTERNS: Do not use standard outline templates or common section names
2. CREATE CUSTOM TITLES: Every section title must directly incorporate terminology from the specific topic
3. TOPIC IMMERSION: Write as if you are a recognized expert in this exact subject
4. SPECIALIZED LANGUAGE: Use field-specific terminology throughout all sections and descriptions
5. UNIQUE STRUCTURE: The outline structure itself should reflect the nature of this specific topic

WHAT TO AVOID COMPLETELY:
- Generic section names like "Introduction," "Background," or "Conclusion"
- Standard academic paper structures
- Generic key points that could apply to any topic
- Default templates you've used for other outlines
- General-purpose section descriptions`
                    },
                    {
                      role: "user",
                      content: enhancedSingleChapterPrompt
                    }
                  ],
                  max_tokens: 4096, // Double token limit for more detailed outlines
                  temperature: 0.8, // Slightly higher temperature for more creativity
                  frequency_penalty: 0.4, // Stronger repetition avoidance
                  presence_penalty: 0.4 // Stronger topic diversity
                })
              });

              if (retryResponse.ok) {
                const retryData = await retryResponse.json();
                const retryContent = retryData.choices?.[0]?.message?.content || '';
                const retryParsed = this.extractAndParseJSON(retryContent, 'chapters');
                
                if (retryParsed && retryParsed.sections && Array.isArray(retryParsed.sections)) {
                  retryOutlines.push({
                    id: `outline-${userChapter.id}`,
                    chapterId: userChapter.id,
                    title: userChapter.outline.title,
                    description: userChapter.outline.description || `Outline for ${userChapter.outline.title}`,
                    sections: retryParsed.sections.map((section: any, sectionIndex: number) => ({
                      id: `section-${section.order || sectionIndex + 1}`,
                      title: section.title || `Section ${sectionIndex + 1}`,
                      description: section.description || '',
                      level: section.level || 1,
                      order: section.order || sectionIndex + 1,
                      estimatedWordCount: section.estimatedWordCount || 500,
                      keyPoints: section.keyPoints || []
                    })),
                    estimatedWordCount: retryParsed.estimatedWordCount || userChapter.outline.estimatedWordCount || 3000,
                    keyPoints: retryParsed.keyPoints || [],
                    status: 'ready' as const
                  });
                  console.log(`Successfully created retry outline for "${userChapter.outline.title}"`);
                  continue; // Skip to next chapter
                }
              }
            } catch (retryError) {
              console.error(`Individual retry failed for chapter "${userChapter.outline.title}":`, retryError);
            }
            
            // If retry fails, create a minimal fallback outline
            retryOutlines.push({
              id: `outline-${userChapter.id}`,
              chapterId: userChapter.id,
              title: userChapter.outline.title,
              description: userChapter.outline.description || `Outline for ${userChapter.outline.title}`,
              sections: [{
                id: 'section-1',
                title: 'Content needs to be generated',
                description: 'Please use the regenerate button to create a detailed outline',
                level: 1,
                order: 1,
                estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
                keyPoints: ['Outline generation required']
              }],
              estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
              keyPoints: ['Please regenerate this outline'],
              status: 'ready' as const
            });
          }
          
          allOutlines.push(...retryOutlines);
          console.log(`Added retry/fallback outlines for chunk ${chunkNumber}`);
        } catch (retryChunkError) {
          console.error(`Retry approach also failed for chunk ${chunkNumber}:`, retryChunkError);
          
          // Create minimal fallback outlines as a last resort
          const minimalFallbacks = chunk.map((userChapter) => ({
            id: `outline-${userChapter.id}`,
            chapterId: userChapter.id,
            title: userChapter.outline.title,
            description: userChapter.outline.description || `Outline for ${userChapter.outline.title}`,
            sections: [{
              id: 'section-1',
              title: 'Content needs to be generated',
              description: 'Please use the regenerate button to create a detailed outline',
              level: 1,
              order: 1,
              estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
              keyPoints: ['Outline generation required']
            }],
            estimatedWordCount: userChapter.outline.estimatedWordCount || 3000,
            keyPoints: ['Please regenerate this outline'],
            status: 'ready' as const
          }));
          
          allOutlines.push(...minimalFallbacks);
          console.log(`Added minimal fallback outlines for chunk ${chunkNumber}`);
        }
      }
    }

    console.log(`Completed outline generation for all ${userChapters.length} chapters`);
    return allOutlines;
  }

  /**
   * Generate outline for a single chapter
   */
  async generateChapterOutline(
    metadata: BookMetadata,
    userChapter: UserChapter,
    previousChapters: string[] = [],
    options: Partial<AIGenerationOptions> = {}
  ): Promise<GeneratedOutline> {
    try {
      const {
        model = "anthropic/claude-3-5-sonnet-20241022", // Using Claude Sonnet 3.5 for better outline generation
        maxTokens = 8192 // Significantly increased token limit for more detailed outlines
      } = options;

      // Use Claude Sonnet 3.5 for best results
      const selectedModel = getOptimalModel("outline-generation", metadata) || "anthropic/claude-3-5-sonnet-20241022";
      
      console.log(`Generating single chapter outline using model: ${selectedModel}`);
      
      // Enhance metadata and chapter with more details if they're minimal
      const enhancedMetadata = this.enhanceMetadataForOutlineGeneration(metadata);
      const enhancedChapter = this.enhanceChaptersForOutlineGeneration([userChapter])[0];

      const prompt = OUTLINE_GENERATION_PROMPTS.generateChapterOutline(enhancedMetadata, enhancedChapter, previousChapters);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            {
              role: "system",
              content: `You are an expert book outline generator specializing in creating HIGHLY SPECIFIC outlines customized to exact book topics.

CRITICAL INSTRUCTION: YOU MUST CREATE UNIQUE SECTIONS SPECIFICALLY FOR THE EXACT TOPIC - NEVER USE GENERIC TEMPLATES OR GENERIC SECTION NAMES.

TOPIC SPECIFICITY REQUIREMENTS:
- ANALYZE DEEPLY: Study the chapter titles, keywords, and user descriptions very carefully
- CREATE CUSTOM SECTIONS: Every section name must directly relate to the specific topic (not generic like "Introduction" or "Conclusion")
- SPECIALIZED CONTENT: Section descriptions must include topic-specific terminology and concepts
- TAILOR KEY POINTS: All key points must directly reference aspects of the specific topic
- NEVER USE GENERIC TEMPLATES: Do not use any predefined outline structures or common academic paper structures
- DOMAIN EXPERTISE: Write the outline as if you are an expert in the specific field or subject matter

STRUCTURE REQUIREMENTS:
- Create 8-15 main sections with meaningful, TOPIC-SPECIFIC titles
- Each section must have detailed descriptions explaining the specific content related to the topic
- Include realistic word count estimates for each section (300-800 words per section)
- Provide 3-5 key points for each section that highlight concepts specific to this exact topic
- Ensure logical flow that builds knowledge of the specific topic
- Make sections substantial with content directly relevant to the topic

CRITICAL JSON FORMATTING REQUIREMENTS:
1) You MUST respond with raw valid JSON only
2) Do NOT use markdown code blocks or any other formatting
3) Do NOT include ANY explanation before or after the JSON
4) The ENTIRE response should be a single valid JSON object
5) Use ONLY double quotes for all properties and strings
6) NEVER use single quotes (') in property names - always use double quotes and colons like "title":
7) Check that your JSON is valid before responding
FAILURE TO FOLLOW THESE REQUIREMENTS WILL BREAK THE SYSTEM.`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.8,
          top_p: 0.95, // Use consistent settings for all outline generation
          frequency_penalty: 0.3, // Reduce repetition of similar phrases
          presence_penalty: 0.3  // Encourage the model to cover more topics
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      // Parse the JSON response
      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for chapter outline generation (first 200 chars):", 
          content.length > 200 ? content.substring(0, 200) + "..." : content);
        
        // Use the helper method to parse and extract JSON
        const parsed = this.extractAndParseJSON(content, 'chapters');
        
        // Validate the parsed result
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result');
          throw new Error('Failed to parse AI response as JSON. The AI returned an invalid response format.');
        }
        
        console.log("Successfully parsed chapter outline JSON");

        // Validate and build outline with defaults for missing fields
        const outline: GeneratedOutline = {
          id: `outline-${userChapter.id}`,
          chapterId: userChapter.id,
          title: parsed.title || userChapter.outline.title,
          description: parsed.description || userChapter.outline.description,
          sections: (parsed.sections || []).map((section: any, index: number) => ({
            id: `section-${section.order || index + 1}`,
            title: section.title || `Section ${index + 1}`,
            description: section.description || '',
            level: section.level || 1,
            order: section.order || index + 1,
            estimatedWordCount: section.estimatedWordCount || 500,
            keyPoints: section.keyPoints || []
          })),
          estimatedWordCount: parsed.estimatedWordCount || userChapter.outline.estimatedWordCount || 3000,
          keyPoints: parsed.keyPoints || [],
          status: 'ready' as const
        };

        return outline;
      } catch (parseError: any) {
        console.error('Failed to parse outline JSON:', parseError);
        throw new Error(`Failed to parse generated outline. ${parseError.message || 'Please try again.'}`);
      }

    } catch (error: any) {
      console.error('AI Chapter Outline Generation Error:', error);
      throw new Error('Failed to generate chapter outline. ' + (error?.message || 'Please try again.'));
    }
  }

  /**
   * Generate a summary of a chapter for context building
   */
  async generateChapterSummary(
    chapterTitle: string,
    chapterContent: string,
    options: Partial<AIGenerationOptions> = {}
  ): Promise<AIAnalysisResult> {
    try {
      const {
        model = "anthropic/claude-3-sonnet",
        maxTokens = CONTEXT_SETTINGS.SUMMARY_GENERATION_TOKENS
      } = options;

      // Truncate content intelligently to avoid token limits while preserving key information
      const truncatedContent = this.intelligentContentTruncation(chapterContent, 8000);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: "system",
              content: `You are an expert at creating concise chapter summaries for book context management.

              Create a summary that:
              - Is EXACTLY ${CONTEXT_SETTINGS.SUMMARY_MAX_WORDS} words or fewer
              - Captures the key narrative developments, character arcs, and plot points
              - Maintains continuity for subsequent chapters
              - Focuses on actionable information that affects future chapters
              - Includes important world-building or setting details if relevant

              CRITICAL JSON FORMATTING RULES:
              - NEVER use mathematical expressions with backslashes (\\, \\Delta, \\frac, etc.)
              - NEVER use LaTeX notation or dollar signs ($) for equations
              - NEVER use quotes within quoted strings - use single quotes or rephrase completely
              - NEVER use backslashes except for valid JSON escapes (\\n, \\t, \\", \\\\)
              - Replace any mathematical formulas with plain English descriptions
              - Use simple, clean text without special characters or symbols
              - Avoid control characters, smart quotes, or unusual Unicode
              - ALWAYS use double quotes (") for property names, NEVER single quotes (')
              - ALWAYS use colon (:) after property names, NEVER single quotes before colon
              - Property names must be: "summary": and "keyPoints": (with double quotes and colon)

              Return your response as JSON in this EXACT format (note the double quotes and colons):
              {
                "summary": "Your concise summary here",
                "keyPoints": ["Key point 1", "Key point 2", "Key point 3"]
              }

              CRITICAL: Property names MUST use double quotes and colons like "summary": NOT "summary':
              THE ENTIRE RESPONSE MUST BE VALID JSON WITH NO OTHER TEXT BEFORE OR AFTER.`
            },
            {
              role: "user",
              content: `Create a concise summary for the chapter "${chapterTitle}":\n\n${truncatedContent}`
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.3 // Lower temperature for consistent summaries
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';

      try {
        // Log a truncated version of the content for debugging
        console.log("AI Response for chapter summary (first 200 chars):",
          content.length > 200 ? content.substring(0, 200) + "..." : content);

        // Use the helper method to parse and extract JSON with additional fallback
        let parsed: any;
        let cleanContent = content.trim(); // Declare outside try-catch for error logging

        try {
          parsed = this.extractAndParseJSON(content, 'summary');
        } catch (parseError) {
          console.warn('Primary JSON parsing failed, attempting enhanced manual cleanup for chapter summary');

          // Enhanced manual cleanup specifically for chapter summaries
          try {
            // Remove any non-JSON text before and after
            const jsonStart = cleanContent.indexOf('{');
            const jsonEnd = cleanContent.lastIndexOf('}');
            if (jsonStart !== -1 && jsonEnd !== -1) {
              cleanContent = cleanContent.substring(jsonStart, jsonEnd + 1);
            }

            // Enhanced LaTeX and mathematical expression handling
            cleanContent = cleanContent.replace(/\$\\Delta\\phi[^$]*\$/g, '"phase difference equation"');
            cleanContent = cleanContent.replace(/\$\\frac\{[^}]*\}\{[^}]*\}[^$]*\$/g, '"mathematical formula"');
            cleanContent = cleanContent.replace(/\$[^$]*\$/g, '"equation"');

            // Handle backslash sequences that aren't valid JSON escapes
            cleanContent = cleanContent.replace(/\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/g, '\\\\');

            // CRITICAL: Fix mixed quote patterns in property names
            // "summary': → "summary":
            cleanContent = cleanContent.replace(/"([^"']*)':\s*/g, '"$1": ');
            // 'summary": → "summary":
            cleanContent = cleanContent.replace(/'([^"']*)":\s*/g, '"$1": ');
            // 'summary': → "summary":
            cleanContent = cleanContent.replace(/'([^"']*)':\s*/g, '"$1": ');

            // Fix unescaped quotes by replacing them with single quotes
            cleanContent = cleanContent.replace(/"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}\]])/g, '"$1\'$2\'$3"');

            // Remove problematic characters that commonly break JSON
            cleanContent = cleanContent.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

            // Fix trailing commas
            cleanContent = cleanContent.replace(/,(\s*[\]}])/g, '$1');

            // Ensure proper quote escaping
            cleanContent = cleanContent.replace(/\\"/g, '"');
            cleanContent = cleanContent.replace(/\\":/g, '":');

            // Try parsing the cleaned content
            parsed = JSON.parse(cleanContent);
            console.log('Enhanced manual cleanup successful for chapter summary');
          } catch (manualError) {
            console.error('Enhanced manual cleanup also failed:', manualError);
            console.error('Problematic content:', cleanContent?.substring(0, 200));
            return this.createFallbackSummaryResult(chapterTitle, chapterContent);
          }
        }

        // Validate the parsed result
        if (!parsed) {
          console.error('JSON parsing returned null/undefined result for chapter summary');
          return this.createFallbackSummaryResult(chapterTitle, chapterContent);
        }

        // Validate required fields and word count
        const summary = parsed.summary || `Summary of "${chapterTitle}" (missing summary)`;
        const keyPoints = Array.isArray(parsed.keyPoints) ? parsed.keyPoints : [];

        // Enforce word count limit strictly
        const validatedSummary = this.enforceWordLimit(summary, CONTEXT_SETTINGS.SUMMARY_MAX_WORDS);

        console.log(`Generated summary for "${chapterTitle}" (${summary.split(/\s+/).length} words, final: ${validatedSummary.split(/\s+/).length} words)`);

        return {
          summary: validatedSummary,
          keyPoints: keyPoints.slice(0, 5) // Limit key points to 5 max
        };
      } catch (parseError: any) {
        console.error('Failed to parse chapter summary JSON:', parseError);
        return this.createFallbackSummaryResult(chapterTitle, chapterContent);
      }
    } catch (error: any) {
      console.error('AI Chapter Summary Generation Error:', error);
      return this.createFallbackSummaryResult(chapterTitle, chapterContent);
    }
  }

  /**
   * Intelligent content truncation that preserves key information
   */
  private intelligentContentTruncation(content: string, maxChars: number): string {
    if (content.length <= maxChars) {
      return content;
    }

    // Try to find natural break points (paragraphs, sentences)
    const paragraphs = content.split('\n\n');
    let truncated = '';

    for (const paragraph of paragraphs) {
      if ((truncated + paragraph).length > maxChars) {
        // If adding this paragraph would exceed limit, try to add part of it
        const remainingChars = maxChars - truncated.length;
        if (remainingChars > 100) { // Only add if we have meaningful space left
          const sentences = paragraph.split('. ');
          for (const sentence of sentences) {
            if ((truncated + sentence + '. ').length <= maxChars) {
              truncated += sentence + '. ';
            } else {
              break;
            }
          }
        }
        break;
      }
      truncated += paragraph + '\n\n';
    }

    return truncated.trim() || content.slice(0, maxChars);
  }

  /**
   * Enforce word limit on summary text
   */
  private enforceWordLimit(text: string, maxWords: number): string {
    const words = text.split(/\s+/);
    if (words.length <= maxWords) {
      return text;
    }

    return words.slice(0, maxWords).join(' ') + '...';
  }

  /**
   * Create a basic fallback summary from chapter content
   */
  private createFallbackSummary(chapterTitle: string, chapterContent: string): string {
    // Extract first few sentences as a basic summary
    const sentences = chapterContent.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const firstSentences = sentences.slice(0, 3).join('. ').trim();

    if (firstSentences.length > 0) {
      return `Chapter "${chapterTitle}": ${firstSentences}${firstSentences.endsWith('.') ? '' : '.'}`;
    }

    // If no good sentences found, create a basic summary
    const wordCount = chapterContent.split(/\s+/).length;
    return `Chapter "${chapterTitle}" contains ${wordCount} words of content covering the chapter's main topics and developments.`;
  }

  /**
   * Create fallback summary result when AI generation fails
   */
  private createFallbackSummaryResult(chapterTitle: string, chapterContent: string): AIAnalysisResult {
    console.log('Creating fallback summary for chapter:', chapterTitle);

    // Create a simple, safe summary from the content
    const fallbackSummary = this.createFallbackSummary(chapterTitle, chapterContent);

    // Extract key topics from the chapter title and content
    const keyPoints = this.extractKeyPointsFromContent(chapterTitle, chapterContent);

    return {
      summary: this.enforceWordLimit(fallbackSummary, CONTEXT_SETTINGS.SUMMARY_MAX_WORDS),
      keyPoints: keyPoints.slice(0, 5) // Limit to 5 key points
    };
  }

  /**
   * Extract key points from chapter content when AI parsing fails
   */
  private extractKeyPointsFromContent(chapterTitle: string, chapterContent: string): string[] {
    const keyPoints: string[] = [`Chapter: ${chapterTitle}`];

    // Extract headings and important terms
    const headings = chapterContent.match(/^#+\s+(.+)$/gm);
    if (headings && headings.length > 0) {
      headings.slice(0, 3).forEach(heading => {
        const cleanHeading = heading.replace(/^#+\s+/, '').trim();
        if (cleanHeading.length > 0) {
          keyPoints.push(`Section: ${cleanHeading}`);
        }
      });
    }

    // If no headings found, add a generic point
    if (keyPoints.length === 1) {
      keyPoints.push('Content covers key concepts and developments');
      keyPoints.push('Builds upon previous chapters');
    }

    return keyPoints;
  }

  /**
   * Build a system prompt with book context for consistent generation
   */
  private buildSystemPrompt(context?: BookContext): string {
    const basePrompt = `You are an expert book author with deep knowledge across many subjects.
Write in a clear, engaging, and professional style appropriate for a published book.
Create substantial, high-quality content that incorporates creative elements, examples, and depth.
Maintain consistency with the book's voice, tone, and previous chapters.

CRITICAL CONTENT REQUIREMENTS:
- Generate COMPREHENSIVE, DETAILED chapters of 4,000-6,000 words minimum
- Use ALL available output tokens to create the longest possible content
- Include multiple detailed examples, case studies, and practical applications
- Expand on every concept with thorough explanations and context
- Add relevant anecdotes, historical context, and real-world connections
- Create detailed subsections with rich, substantive content
- Never summarize or abbreviate - provide full, complete explanations
- Write as if this is a premium, comprehensive book that readers paid for

CRITICAL FORMATTING REQUIREMENTS:
- Structure your content with clear headings and subheadings
- Use ## for major section headings within the chapter
- Use ### for subsection headings
- Start each major section with a clear, descriptive heading
- Organize content logically with proper paragraph breaks
- Use proper transitions between sections
- Include introductory and concluding paragraphs for each major section
- Format content for easy reading with appropriate spacing

CONTENT STRUCTURE GUIDELINES:
1. Begin with an engaging introduction that sets the context
2. Organize content into 3-5 major sections with clear headings
3. Each section should have 2-4 subsections with specific topics
4. Include practical examples and real-world applications in each section
5. End with a conclusion that ties everything together
6. Ensure smooth transitions between all sections

IMPORTANT: Your task is to generate a COMPLETE, FULL-LENGTH chapter for the book. Follow the outline structure carefully and create the most comprehensive, detailed content possible that builds upon any previous chapters provided in the context.`;
    
    if (!context) {
      return basePrompt;
    }
    
    const contextPrompt = `
You have access to the following context from the book:

BOOK OUTLINE:
${context.bookOutline}

${context.previousChapters.length > 0 ? `PREVIOUS CHAPTERS:
${context.previousChapters.map(ch => 
  `Chapter: ${ch.chapterId}
   Summary: ${ch.summary}
   Key Points: ${ch.keyPoints.join(', ')}`
).join('\n\n')}` : 'NOTE: This is the first chapter of the book.'}

CRITICAL INSTRUCTIONS:
1. Follow the chapter outline structure exactly
2. Build on previous chapters' content for narrative continuity
3. Maintain the book's voice and tone consistently
4. Generate MAXIMUM LENGTH content - aim for 4,000-6,000+ words per chapter
5. Use ALL available tokens to create the longest, most comprehensive content possible
6. Include detailed examples, case studies, step-by-step processes, and practical applications
7. Expand every concept with thorough explanations, context, and real-world connections
8. Add relevant anecdotes, historical background, and industry insights
9. Create detailed subsections with rich, substantive content - never summarize
10. Include proper transitions that reference previous chapters where appropriate
11. Write comprehensive, book-quality content that provides exceptional value to readers
12. Never cut content short - always provide complete, full explanations

FORMATTING REQUIREMENTS FOR THIS CHAPTER:
- Use ## for major section headings (e.g., ## Introduction, ## Core Concepts)
- Use ### for subsection headings (e.g., ### Key Principles, ### Implementation Steps)
- Structure content with clear paragraph breaks and logical flow
- Begin each section with a clear heading that describes what will be covered
- Include smooth transitions between sections
- End major sections with brief summaries or transitions to the next section
- Format the content for professional book publication standards`;
    
    return basePrompt + contextPrompt;
  }

  /**
   * Enhance metadata with more information when minimal data is provided
   * This helps the AI generate better outlines with limited user input
   */
  private enhanceMetadataForOutlineGeneration(metadata: BookMetadata): BookMetadata {
    // Clone the metadata to avoid modifying the original
    const enhanced = { ...metadata };
    
    // If keywords are missing or minimal, add some general ones based on title/genre
    if (!enhanced.keywords || enhanced.keywords.length < 3) {
      const titleWords = enhanced.title.split(/\s+/)
        .filter(word => word.length > 3)
        .map(word => word.replace(/[^\w]/g, ''));
      
      enhanced.keywords = [
        ...(enhanced.keywords || []),
        ...titleWords,
        enhanced.genre,
        enhanced.targetAudience
      ].filter(Boolean).filter((v, i, a) => a.indexOf(v) === i);
    }
    
    // Ensure description is substantial
    if (!enhanced.description || enhanced.description.length < 30) {
      enhanced.description = `${enhanced.title} is a ${enhanced.genre} book targeting ${enhanced.targetAudience} readers.` + 
        ` It explores important concepts and provides valuable insights into the subject matter.`;
    }
    
    // Ensure we have a tone defined
    if (!enhanced.tone) {
      enhanced.tone = "professional";
    }
    
    console.log("Enhanced metadata for outline generation");
    return enhanced;
  }
  
  /**
   * Enhance chapter information when minimal details are provided
   * This helps ensure the AI has enough context to generate topic-specific outlines
   */
  private enhanceChaptersForOutlineGeneration(userChapters: UserChapter[]): UserChapter[] {
    // Clone the chapters to avoid modifying the originals
    return userChapters.map((chapter, index) => {
      const enhanced = { ...chapter };
      
      // Ensure the chapter outline has a substantive title
      if (!enhanced.outline.title || enhanced.outline.title.trim().length < 3) {
        enhanced.outline.title = `Chapter ${index + 1}`;
      }
      
      // Add default chapter descriptions if missing
      if (!enhanced.outline.description || enhanced.outline.description.trim().length < 20) {
        const chapterDescriptions = [
          "Explores fundamental concepts and core principles related to the subject matter",
          "Analyzes key developments and important factors in the field",
          "Discusses advanced techniques and methodologies relevant to the topic",
          "Examines case studies and practical applications of theoretical concepts",
          "Investigates emerging trends and future directions in this domain"
        ];
        
        enhanced.outline.description = chapterDescriptions[index % chapterDescriptions.length];
      }
      
      // Ensure we have a reasonable word count estimate
      if (!enhanced.outline.estimatedWordCount || enhanced.outline.estimatedWordCount < 500) {
        enhanced.outline.estimatedWordCount = 3000;
      }
      
      return enhanced;
    });
  }

  /**
   * Check if the current API key is valid
   */
  private hasValidApiKey(): boolean {
    return !!this.apiKey && 
           !this.apiKey.includes('your_') && 
           this.apiKey.length > 20;
  }
}

// Export as singleton
const bookAIService = new BookAIService();
export default bookAIService;
