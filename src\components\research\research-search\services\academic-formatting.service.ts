/**
 * Enhanced Academic Formatting Service
 * Handles conversion from markdown to clean academic formatting and comprehensive document generation
 */

import { EnhancedResearchSession } from './enhanced-deep-research.service';
import { AcademicSection } from './enhanced-academic-writing.service';
import { EnhancedReference } from './enhanced-reference-management.service';

export interface FormattedDocument {
  title: string;
  content: string;
  htmlContent: string;
  wordContent: string;
  metadata: {
    wordCount: number;
    citationCount: number;
    referenceCount: number;
    pageCount: number;
    readingTime: number;
  };
  sections: FormattedSection[];
  bibliography: string;
  tableOfContents: string;
}

export interface FormattedSection {
  id: string;
  title: string;
  level: number;
  content: string;
  wordCount: number;
  citationCount: number;
  pageNumber?: number;
}

export class AcademicFormattingService {
  
  /**
   * Convert markdown content to clean academic HTML
   */
  formatForAcademicOutput(content: string): string {
    let formatted = content;

    // Remove markdown headers and replace with proper HTML
    formatted = this.convertHeaders(formatted);
    
    // Handle emphasis and strong text
    formatted = this.convertEmphasis(formatted);
    
    // Handle lists
    formatted = this.convertLists(formatted);
    
    // Handle citations
    formatted = this.formatCitations(formatted);

    // Format references section
    formatted = this.formatReferences(formatted);

    // Clean up extra whitespace
    formatted = this.cleanWhitespace(formatted);
    
    // Add proper academic styling
    formatted = this.addAcademicStyling(formatted);

    return formatted;
  }

  /**
   * Convert markdown content to clean text for Word export
   */
  formatForWordExport(content: string): string {
    let formatted = content;

    // Enhanced HTML tag removal - remove all HTML tags and classes
    formatted = this.removeAllHtmlTags(formatted);

    // Remove all markdown symbols
    formatted = this.removeMarkdownSymbols(formatted);

    // Convert headers to proper formatting (bold text, no HTML)
    formatted = this.convertHeadersToText(formatted);

    // Clean up citations
    formatted = this.cleanCitations(formatted);

    // Format paragraphs
    formatted = this.formatParagraphs(formatted);

    // Enhanced final cleanup
    formatted = this.enhancedFinalCleanup(formatted);

    return formatted;
  }

  /**
   * Convert markdown headers to HTML with proper spacing
   */
  private convertHeaders(content: string): string {
    return content
      .replace(/^### (.*$)/gm, '<h3 class="academic-h3" style="margin-top: 2rem; margin-bottom: 1rem; font-size: 1.125rem; font-weight: 600; color: #374151;">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="academic-h2" style="margin-top: 2.5rem; margin-bottom: 1.5rem; font-size: 1.25rem; font-weight: 700; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="academic-h1" style="margin-top: 3rem; margin-bottom: 2rem; font-size: 1.5rem; font-weight: 800; color: #111827; text-align: center;">$1</h1>');
  }

  /**
   * Convert emphasis and strong text
   */
  private convertEmphasis(content: string): string {
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong class="academic-strong">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="academic-em">$1</em>')
      .replace(/__(.*?)__/g, '<strong class="academic-strong">$1</strong>')
      .replace(/_(.*?)_/g, '<em class="academic-em">$1</em>');
  }

  /**
   * Convert markdown lists to HTML
   */
  private convertLists(content: string): string {
    // Handle unordered lists
    content = content.replace(/^[\s]*[-*+]\s+(.*)$/gm, '<li class="academic-li">$1</li>');
    
    // Wrap consecutive list items in ul tags
    content = content.replace(/(<li class="academic-li">.*<\/li>\s*)+/gs, (match) => {
      return `<ul class="academic-ul">${match}</ul>`;
    });

    // Handle ordered lists
    content = content.replace(/^[\s]*\d+\.\s+(.*)$/gm, '<li class="academic-oli">$1</li>');
    
    // Wrap consecutive ordered list items in ol tags
    content = content.replace(/(<li class="academic-oli">.*<\/li>\s*)+/gs, (match) => {
      return `<ol class="academic-ol">${match}</ol>`;
    });

    return content;
  }

  /**
   * Format citations for academic display
   */
  private formatCitations(content: string): string {
    // Format in-text citations with better styling
    content = content.replace(/\(([^)]+),\s*(\d{4})\)/g, '<span class="citation" style="color: #2563eb; font-weight: 500;">($1, $2)</span>');
    content = content.replace(/\[(\d+)\]/g, '<span class="citation-number" style="color: #2563eb; font-weight: 500;">[$1]</span>');
    content = content.replace(/\(([^)]+)\)/g, '<span class="citation" style="color: #2563eb; font-weight: 500;">($1)</span>');

    return content;
  }

  /**
   * Format references section with proper spacing
   */
  private formatReferences(content: string): string {
    // Add proper spacing between references
    content = content.replace(/^(\d+\.\s+.+)$/gm, '<div class="reference-item" style="margin-bottom: 1.5rem; padding-left: 2rem; text-indent: -2rem; line-height: 1.6;">$1</div>');

    // Format references header
    content = content.replace(/## References/g, '<h2 class="references-header" style="margin-top: 3rem; margin-bottom: 2rem; font-size: 1.25rem; font-weight: 700; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">References</h2><div class="references-container">');

    // Close references container at the end
    if (content.includes('references-container')) {
      content += '</div>';
    }

    return content;
  }

  /**
   * Clean up whitespace
   */
  private cleanWhitespace(content: string): string {
    return content
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove excessive line breaks
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces with single space
      .trim();
  }

  /**
   * Add academic styling wrapper with enhanced formatting
   */
  private addAcademicStyling(content: string): string {
    return `<div class="academic-content" style="
      font-family: 'Times New Roman', serif;
      line-height: 1.8;
      color: #374151;
      max-width: none;
      padding: 2rem;
      background: white;
    ">
      <style>
        .academic-content p {
          margin-bottom: 1.5rem;
          text-align: justify;
          text-indent: 2rem;
        }
        .academic-content ul, .academic-content ol {
          margin: 1.5rem 0;
          padding-left: 2rem;
        }
        .academic-content li {
          margin-bottom: 0.5rem;
        }
        .academic-content blockquote {
          margin: 2rem 0;
          padding: 1rem 2rem;
          border-left: 4px solid #e5e7eb;
          background: #f9fafb;
          font-style: italic;
        }
        .academic-content .references {
          margin-top: 3rem;
          padding-top: 2rem;
          border-top: 2px solid #e5e7eb;
        }
        .academic-content .reference-item {
          margin-bottom: 1rem;
          padding-left: 2rem;
          text-indent: -2rem;
          line-height: 1.6;
        }
      </style>
      ${content}
    </div>`;
  }

  /**
   * Remove all markdown symbols for clean text
   */
  private removeMarkdownSymbols(content: string): string {
    return content
      .replace(/#{1,6}\s+/g, '') // Remove header symbols
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/__(.*?)__/g, '$1') // Remove bold
      .replace(/_(.*?)_/g, '$1') // Remove italic
      .replace(/^[\s]*[-*+]\s+/gm, '• ') // Convert bullets to bullet points
      .replace(/^[\s]*\d+\.\s+/gm, '') // Remove numbered list markers
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
      .replace(/`([^`]+)`/g, '$1') // Remove code formatting
      .replace(/>\s+/gm, '') // Remove blockquote markers
      .replace(/\|.*\|/g, '') // Remove table formatting
      .replace(/[-=]{3,}/g, '') // Remove horizontal rules
      .trim();
  }

  /**
   * Convert headers to clean text with proper formatting
   */
  private convertHeadersToText(content: string): string {
    return content
      .replace(/^### (.*$)/gm, '\n\n$1\n')
      .replace(/^## (.*$)/gm, '\n\n$1\n')
      .replace(/^# (.*$)/gm, '\n\n$1\n\n');
  }

  /**
   * Clean citations for text output
   */
  private cleanCitations(content: string): string {
    // Keep citations as they are for academic text
    return content;
  }

  /**
   * Format paragraphs for better readability
   */
  private formatParagraphs(content: string): string {
    return content
      .split('\n\n')
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0)
      .join('\n\n');
  }

  /**
   * Generate academic CSS styles
   */
  getAcademicStyles(): string {
    return `
      .academic-content {
        font-family: 'Times New Roman', serif;
        line-height: 1.6;
        color: #333;
        max-width: none;
      }
      
      .academic-h1 {
        font-size: 1.5em;
        font-weight: bold;
        margin: 1.5em 0 1em 0;
        text-align: center;
        color: #2c3e50;
      }
      
      .academic-h2 {
        font-size: 1.3em;
        font-weight: bold;
        margin: 1.3em 0 0.8em 0;
        color: #34495e;
        border-bottom: 1px solid #eee;
        padding-bottom: 0.3em;
      }
      
      .academic-h3 {
        font-size: 1.1em;
        font-weight: bold;
        margin: 1.1em 0 0.6em 0;
        color: #34495e;
      }
      
      .academic-strong {
        font-weight: bold;
        color: #2c3e50;
      }
      
      .academic-em {
        font-style: italic;
      }
      
      .academic-ul, .academic-ol {
        margin: 1em 0;
        padding-left: 2em;
      }
      
      .academic-li, .academic-oli {
        margin: 0.5em 0;
        line-height: 1.6;
      }
      
      .citation {
        color: #3498db;
        font-weight: 500;
      }
      
      .citation-number {
        color: #e74c3c;
        font-weight: bold;
        font-size: 0.9em;
        vertical-align: super;
      }
      
      .academic-content p {
        margin: 1em 0;
        text-align: justify;
        text-indent: 1.5em;
      }
      
      .academic-content blockquote {
        margin: 1.5em 2em;
        padding: 1em;
        background: #f8f9fa;
        border-left: 4px solid #3498db;
        font-style: italic;
      }
    `;
  }

  /**
   * Create a complete formatted document
   */
  createFormattedDocument(title: string, content: string, references?: string): string {
    const formattedContent = this.formatForAcademicOutput(content);
    const formattedReferences = references ? this.formatForAcademicOutput(references) : '';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          ${this.getAcademicStyles()}
        </style>
      </head>
      <body>
        <div class="academic-content">
          <h1 class="academic-h1">${title}</h1>
          ${formattedContent}
          ${formattedReferences ? `<div class="references-section">${formattedReferences}</div>` : ''}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Extract plain text for word count
   */
  extractPlainText(content: string): string {
    return content
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Count words in content
   */
  countWords(content: string): number {
    const plainText = this.extractPlainText(content);
    return plainText.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Format content for editor integration
   */
  formatForEditor(content: string, title: string): { title: string; content: string } {
    const cleanContent = this.formatForWordExport(content);
    
    return {
      title,
      content: cleanContent
    };
  }

  /**
   * Create academic abstract
   */
  generateAbstract(content: string, maxWords: number = 250): string {
    const plainText = this.extractPlainText(content);
    const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let abstract = '';
    let wordCount = 0;
    
    for (const sentence of sentences) {
      const sentenceWords = sentence.trim().split(/\s+/).length;
      if (wordCount + sentenceWords <= maxWords) {
        abstract += sentence.trim() + '. ';
        wordCount += sentenceWords;
      } else {
        break;
      }
    }
    
    return abstract.trim();
  }

  /**
   * Extract keywords from content
   */
  extractKeywords(content: string, maxKeywords: number = 10): string[] {
    const plainText = this.extractPlainText(content).toLowerCase();
    const words = plainText.split(/\s+/);

    // Simple keyword extraction - count word frequency
    const wordCount = new Map<string, number>();
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);

    words.forEach(word => {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length > 3 && !stopWords.has(cleanWord)) {
        wordCount.set(cleanWord, (wordCount.get(cleanWord) || 0) + 1);
      }
    });

    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxKeywords)
      .map(([word]) => word);
  }

  /**
   * Remove HTML tags and artifacts
   */
  private removeHtmlTags(content: string): string {
    return content
      .replace(/<[^>]*>/g, '') // Remove all HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&hellip;/g, '...');
  }

  /**
   * Enhanced HTML tag removal for Word export
   */
  private removeAllHtmlTags(content: string): string {
    return content
      // Remove all HTML tags with classes
      .replace(/<[^>]*class="[^"]*"[^>]*>/g, '')
      // Remove all remaining HTML tags
      .replace(/<[^>]*>/g, '')
      // Remove HTML entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&hellip;/g, '...')
      // Remove any remaining HTML artifacts
      .replace(/class="[^"]*"/g, '')
      .replace(/academic-[a-z]+/g, '')
      .replace(/<\/[^>]*>/g, '');
  }

  /**
   * Enhanced final cleanup for Word export
   */
  private enhancedFinalCleanup(content: string): string {
    return content
      // Remove code block markers
      .replace(/```[a-z]*\n?/g, '')
      // Remove inline code formatting
      .replace(/`([^`]+)`/g, '$1')
      // Remove bold markdown but preserve content
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      // Remove italic markdown but preserve content
      .replace(/\*([^*]+)\*/g, '$1')
      // Remove header markdown but preserve content
      .replace(/#{1,6}\s*/g, '')
      // Convert list markers to bullets
      .replace(/^\s*[-*+]\s+/gm, '• ')
      // Remove numbered list markers
      .replace(/^\s*\d+\.\s+/gm, '')
      // Remove any remaining HTML class references
      .replace(/academic-[a-z]+/g, '')
      .replace(/class="[^"]*"/g, '')
      // Clean up spacing
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\s+$/gm, '')
      // Remove any remaining HTML artifacts
      .replace(/<[^>]*>/g, '')
      .trim();
  }

  /**
   * Final cleanup for Word export (legacy method)
   */
  private finalCleanup(content: string): string {
    return this.enhancedFinalCleanup(content);
  }

  /**
   * Format research session for comprehensive academic output
   */
  formatResearchSessionForAcademicOutput(session: EnhancedResearchSession): FormattedDocument {
    const sections = this.formatResearchSections(session);
    const bibliography = this.formatResearchBibliography(session);
    const tableOfContents = this.generateTableOfContents(sections);

    const content = this.assembleDocument(session, sections, bibliography, tableOfContents);
    const htmlContent = this.formatForAcademicOutput(content);
    const wordContent = this.formatForWordExport(content);

    const metadata = this.calculateDocumentMetadata(content, session);

    return {
      title: session.title,
      content,
      htmlContent,
      wordContent,
      metadata,
      sections,
      bibliography,
      tableOfContents
    };
  }

  /**
   * Format individual sections with proper academic structure
   */
  private formatResearchSections(session: EnhancedResearchSession): FormattedSection[] {
    const sections: FormattedSection[] = [];

    // Title page section
    sections.push({
      id: 'title',
      title: 'Title Page',
      level: 0,
      content: this.formatTitlePage(session),
      wordCount: 50,
      citationCount: 0
    });

    // Abstract section
    if (session.outline.abstract) {
      sections.push({
        id: 'abstract',
        title: 'Abstract',
        level: 1,
        content: this.formatAbstract(session.outline.abstract),
        wordCount: session.outline.abstract.split(' ').length,
        citationCount: 0
      });
    }

    // Table of contents
    sections.push({
      id: 'toc',
      title: 'Table of Contents',
      level: 1,
      content: '', // Will be populated later
      wordCount: 100,
      citationCount: 0
    });

    // Introduction
    sections.push({
      id: 'introduction',
      title: 'Introduction',
      level: 1,
      content: this.formatIntroduction(session),
      wordCount: 400,
      citationCount: 5
    });

    // Main content sections
    session.outline.points.forEach((point, index) => {
      const completedPoint = session.completedPoints.find(cp => cp.pointId === point.id);

      sections.push({
        id: point.id,
        title: point.title,
        level: 2,
        content: this.formatMainSection(point, completedPoint),
        wordCount: completedPoint?.content?.split(' ').length || 0,
        citationCount: completedPoint?.citations?.length || 0
      });

      // Add subsections if they exist
      if (point.subpoints && point.subpoints.length > 0) {
        point.subpoints.forEach((subpoint, subIndex) => {
          sections.push({
            id: `${point.id}_sub_${subIndex}`,
            title: subpoint.title,
            level: 3,
            content: this.formatSubsection(subpoint),
            wordCount: subpoint.description?.split(' ').length || 0,
            citationCount: 0
          });
        });
      }
    });

    // Conclusion
    sections.push({
      id: 'conclusion',
      title: 'Conclusion',
      level: 1,
      content: this.formatConclusion(session),
      wordCount: 500,
      citationCount: 8
    });

    // References
    sections.push({
      id: 'references',
      title: 'References',
      level: 1,
      content: '', // Will be populated with bibliography
      wordCount: 200,
      citationCount: 0
    });

    return sections;
  }

  /**
   * Format title page
   */
  private formatTitlePage(session: EnhancedResearchSession): string {
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `# ${session.title}

**Research Type:** ${session.researchType.name}

**Prepared by:** Enhanced Academic Research Platform

**Date:** ${currentDate}

**Research Duration:** ${session.researchMetadata.researchDuration} hours

**Total Word Count:** ${session.researchMetadata.totalWordCount.toLocaleString()} words

**Total Citations:** ${session.researchMetadata.totalCitations}

**Academic Sources:** ${session.researchMetadata.academicSources}

**Government Sources:** ${session.researchMetadata.governmentSources}

---

## Research Summary

This comprehensive research report presents an in-depth analysis of "${session.originalQuery}" through systematic academic investigation. The research employs rigorous methodology to examine multiple perspectives, synthesize current literature, and provide evidence-based insights.

### Key Research Metrics
- **Sections Completed:** ${session.researchMetadata.sectionsCompleted}/${session.totalPoints}
- **Average Source Quality:** ${session.researchMetadata.averageSourceQuality.toFixed(2)}/1.0
- **Confidence Score:** ${session.researchMetadata.confidenceScore.toFixed(2)}/1.0

---

*This document has been generated using enhanced academic research methodology with comprehensive source verification and quality assessment.*`;
  }

  /**
   * Format abstract section
   */
  private formatAbstract(abstract: string): string {
    return `## Abstract

${abstract}

**Keywords:** ${this.extractKeywords(abstract).join(', ')}`;
  }

  /**
   * Format introduction section
   */
  private formatIntroduction(session: EnhancedResearchSession): string {
    return `## Introduction

The field of ${this.extractDiscipline(session.originalQuery)} has witnessed significant developments in recent years, particularly regarding ${session.originalQuery.toLowerCase()}. This comprehensive research report examines the current state of knowledge, identifies key trends and patterns, and provides critical analysis of the available evidence.

### Research Objectives

This investigation aims to:

1. **Comprehensive Analysis**: Provide a thorough examination of ${session.originalQuery} through systematic review of current literature and evidence
2. **Critical Evaluation**: Assess the quality, reliability, and significance of existing research findings
3. **Synthesis and Integration**: Integrate diverse perspectives and methodological approaches to develop a cohesive understanding
4. **Practical Implications**: Identify practical applications and implications for theory, practice, and policy
5. **Future Directions**: Highlight gaps in current knowledge and suggest areas for future research

### Methodology

This research employs a systematic approach to literature review and analysis, incorporating:

- **Comprehensive Search Strategy**: Utilization of multiple academic databases and search engines to ensure comprehensive coverage
- **Quality Assessment**: Rigorous evaluation of source credibility, methodological rigor, and relevance
- **Multi-perspective Analysis**: Examination of theoretical, empirical, and practical perspectives
- **Evidence Synthesis**: Integration of findings from diverse sources to develop coherent conclusions

### Scope and Limitations

This research focuses on ${session.originalQuery} with particular attention to recent developments and high-quality academic sources. The analysis is limited to English-language publications and may not capture all cultural or regional perspectives on the topic.

### Document Structure

This report is organized into ${session.totalPoints} main sections, each addressing specific aspects of ${session.originalQuery}. The analysis progresses from foundational concepts to advanced applications, concluding with synthesis and implications for future research and practice.`;
  }

  /**
   * Format main content section
   */
  private formatMainSection(point: any, completedPoint?: any): string {
    let content = `## ${point.title}\n\n`;

    if (point.description) {
      content += `${point.description}\n\n`;
    }

    if (completedPoint?.content) {
      content += completedPoint.content;
    } else {
      content += `*This section is currently being researched and will be populated with comprehensive academic content including detailed analysis, multiple citations, and evidence-based conclusions.*`;
    }

    return content;
  }

  /**
   * Format subsection
   */
  private formatSubsection(subpoint: any): string {
    return `### ${subpoint.title}

${subpoint.description || '*Detailed content for this subsection will be developed through comprehensive research and analysis.*'}`;
  }

  /**
   * Format conclusion section
   */
  private formatConclusion(session: EnhancedResearchSession): string {
    return `## Conclusion

This comprehensive research investigation of ${session.originalQuery} has revealed significant insights across multiple dimensions of analysis. Through systematic examination of ${session.researchMetadata.totalCitations} citations from ${session.researchMetadata.academicSources} academic sources and ${session.researchMetadata.governmentSources} government sources, several key conclusions emerge.

### Key Findings

The research has identified several critical findings that advance our understanding of ${session.originalQuery}:

1. **Theoretical Contributions**: The analysis reveals important theoretical frameworks that enhance our conceptual understanding
2. **Empirical Evidence**: Substantial empirical evidence supports key propositions and hypotheses
3. **Practical Applications**: Clear practical implications emerge for implementation and application
4. **Methodological Insights**: The research highlights important methodological considerations for future studies

### Synthesis and Integration

The integration of diverse perspectives and methodological approaches provides a comprehensive understanding that transcends individual studies or theoretical frameworks. This synthesis reveals both convergent findings and areas of ongoing debate within the field.

### Implications for Theory and Practice

The findings have significant implications for both theoretical development and practical application. Theoretical implications include contributions to existing frameworks and the identification of new conceptual relationships. Practical implications encompass applications in policy, practice, and intervention development.

### Limitations and Future Research

While this research provides comprehensive coverage of ${session.originalQuery}, several limitations should be acknowledged. Future research should address these limitations and explore emerging areas of investigation identified through this analysis.

### Final Recommendations

Based on the comprehensive analysis presented in this report, several recommendations emerge for researchers, practitioners, and policymakers. These recommendations are grounded in the evidence presented and offer concrete directions for future action and investigation.

The research presented in this report represents a significant contribution to the understanding of ${session.originalQuery} and provides a foundation for continued investigation and application in this important area of study.`;
  }

  /**
   * Format bibliography with enhanced academic standards
   */
  private formatResearchBibliography(session: EnhancedResearchSession): string {
    if (!session.consolidatedReferences || session.consolidatedReferences.length === 0) {
      return `## References

*References will be automatically generated and formatted according to academic standards once the research is completed.*`;
    }

    let bibliography = `## References\n\n`;

    // Sort references alphabetically by first author
    const sortedRefs = session.consolidatedReferences.sort((a, b) => {
      const aAuthor = this.extractFirstAuthor(a.formattedCitation);
      const bAuthor = this.extractFirstAuthor(b.formattedCitation);
      return aAuthor.localeCompare(bAuthor);
    });

    sortedRefs.forEach((ref, index) => {
      bibliography += `${index + 1}. ${ref.formattedCitation}\n\n`;
    });

    return bibliography;
  }

  /**
   * Generate table of contents
   */
  private generateTableOfContents(sections: FormattedSection[]): string {
    let toc = `## Table of Contents\n\n`;

    sections.forEach((section, index) => {
      if (section.id === 'toc') return; // Skip TOC itself

      const indent = '  '.repeat(section.level);
      const pageNum = index + 1; // Simplified page numbering

      toc += `${indent}${section.level > 0 ? `${this.getSectionNumber(section, sections)}. ` : ''}${section.title} ... ${pageNum}\n`;
    });

    return toc;
  }

  /**
   * Assemble complete document
   */
  private assembleDocument(
    session: EnhancedResearchSession,
    sections: FormattedSection[],
    bibliography: string,
    tableOfContents: string
  ): string {
    let document = '';

    sections.forEach(section => {
      if (section.id === 'toc') {
        document += tableOfContents + '\n\n';
      } else if (section.id === 'references') {
        document += bibliography + '\n\n';
      } else {
        document += section.content + '\n\n';
      }
    });

    return document;
  }

  /**
   * Calculate document metadata
   */
  private calculateDocumentMetadata(content: string, session: EnhancedResearchSession): {
    wordCount: number;
    citationCount: number;
    referenceCount: number;
    pageCount: number;
    readingTime: number;
  } {
    const wordCount = content.split(/\s+/).length;
    const citationCount = session.researchMetadata.totalCitations;
    const referenceCount = session.consolidatedReferences?.length || 0;
    const pageCount = Math.ceil(wordCount / 250); // Approximate 250 words per page
    const readingTime = Math.ceil(wordCount / 200); // Approximate 200 words per minute

    return {
      wordCount,
      citationCount,
      referenceCount,
      pageCount,
      readingTime
    };
  }

  /**
   * Helper methods
   */

  private extractDiscipline(query: string): string {
    const disciplines = {
      'health': 'healthcare and medicine',
      'education': 'education and pedagogy',
      'technology': 'technology and computer science',
      'business': 'business and management',
      'psychology': 'psychology and behavioral science',
      'environment': 'environmental science',
      'social': 'social sciences',
      'economic': 'economics and finance'
    };

    const lowerQuery = query.toLowerCase();
    for (const [key, value] of Object.entries(disciplines)) {
      if (lowerQuery.includes(key)) {
        return value;
      }
    }

    return 'interdisciplinary studies';
  }

  private extractFirstAuthor(citation: string): string {
    const match = citation.match(/^([A-Z][a-z]+)/);
    return match ? match[1] : 'Unknown';
  }

  private getSectionNumber(section: FormattedSection, sections: FormattedSection[]): string {
    // Simple section numbering - could be enhanced for more complex hierarchies
    const sameLevel = sections.filter(s => s.level === section.level && sections.indexOf(s) <= sections.indexOf(section));
    return sameLevel.length.toString();
  }
}

// Export singleton instance
export const academicFormattingService = new AcademicFormattingService();
