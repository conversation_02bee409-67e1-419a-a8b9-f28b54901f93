<!DOCTYPE html>
<html>
<head>
    <title>Gemini Agent Debug Test</title>
</head>
<body>
    <h1>Debug Test for Gemini Agent</h1>
    
    <h2>Test Document Content</h2>
    <div id="test-content">
        <h1>Research Paper Title</h1>
        <p>This is the introduction section with some citations (<PERSON>, 2020) and references to other work (<PERSON> et al., 2019).</p>
        
        <h2>Literature Review</h2>
        <p>Previous studies have shown various results (Brown, 2021). The methodology used by these researchers provides a foundation for our work.</p>
        
        <h2>Methodology</h2>
        <p>Our approach builds on established methods from the literature. We used a quantitative approach similar to <PERSON> (2018).</p>
        
        <h2>Results</h2>
        <p>The findings indicate significant improvements over baseline methods. Statistical analysis was performed using standard techniques.</p>
        
        <h2>References</h2>
        <p><PERSON>, A. (2021). Advanced Research Methods. Journal of Science, 15(3), 45-67.</p>
        <p><PERSON>, M<PERSON> (2018). Quantitative Analysis Techniques. Research Quarterly, 22(1), 12-28.</p>
        <p><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2019). Comprehensive Study Results. Academic Review, 8(2), 89-102.</p>
        <p><PERSON>, <PERSON> (2020). Introduction to Modern Research. Science Today, 12(4), 234-245.</p>
    </div>
    
    <h2>Expected Changes</h2>
    <div id="expected-result">
        <h1>Research Paper Title</h1>
        <p>This is the introduction section.</p>
        
        <h2>Literature Review</h2>
        <p>Previous studies have shown various results. The methodology used by these researchers provides a foundation for our work.</p>
        
        <h2>Methodology</h2>
        <p>Our approach builds on established methods from the literature. We used a quantitative approach.</p>
        
        <h2>Results</h2>
        <p>The findings indicate significant improvements over baseline methods. Statistical analysis was performed using standard techniques.</p>
    </div>
    
    <script>
        // Test function to verify content replacement
        function testContentReplacement() {
            const originalContent = document.getElementById('test-content').innerHTML;
            const expectedContent = document.getElementById('expected-result').innerHTML;
            
            console.log('Original content length:', originalContent.length);
            console.log('Expected content length:', expectedContent.length);
            
            // Test the replacement logic
            const edits = [
                {
                    originalContent: '(Smith, 2020)',
                    editedContent: '',
                    sectionId: 'test1'
                },
                {
                    originalContent: '(Johnson et al., 2019)',
                    editedContent: '',
                    sectionId: 'test2'
                },
                {
                    originalContent: '<h2>References</h2>\n        <p>Brown, A. (2021). Advanced Research Methods. Journal of Science, 15(3), 45-67.</p>\n        <p>Davis, M. (2018). Quantitative Analysis Techniques. Research Quarterly, 22(1), 12-28.</p>\n        <p>Johnson, R., Smith, K., & Wilson, L. (2019). Comprehensive Study Results. Academic Review, 8(2), 89-102.</p>\n        <p>Smith, J. (2020). Introduction to Modern Research. Science Today, 12(4), 234-245.</p>',
                    editedContent: '',
                    sectionId: 'test3'
                }
            ];
            
            let updatedContent = originalContent;
            
            edits.forEach((edit, index) => {
                console.log(`Testing edit ${index + 1}:`, edit.originalContent.substring(0, 50) + '...');
                
                if (updatedContent.includes(edit.originalContent)) {
                    updatedContent = updatedContent.replace(edit.originalContent, edit.editedContent);
                    console.log(`✅ Edit ${index + 1} applied successfully`);
                } else {
                    console.log(`❌ Edit ${index + 1} failed - content not found`);
                }
            });
            
            console.log('Final content length:', updatedContent.length);
            console.log('Final content:', updatedContent);
            
            return updatedContent;
        }
        
        // Run test when page loads
        window.addEventListener('load', () => {
            console.log('🧪 Running content replacement test...');
            testContentReplacement();
        });
    </script>
</body>
</html>
